{"state":"Merged","mergedAt":"2023-09-21T19:52:05Z","number":8193,"mergeCommitSha":"d7c3186a78307a0dba73a475b92e3a79e4edfd49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8193","title":"Instrument grpc","createdAt":"2023-09-21T19:38:42Z"}
{"state":"Merged","mergedAt":"2023-09-21T20:53:46Z","number":8194,"mergeCommitSha":"78ffcc8668aaedc1f06b608e30b6c6208c1b5094","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8194","title":"Add more utilties","createdAt":"2023-09-21T20:38:58Z"}
{"state":"Merged","mergedAt":"2023-09-21T21:03:50Z","number":8195,"mergeCommitSha":"0ec0cdd0accac85cd0d9b0d7c2cd529984b3e27d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8195","title":"Add bm25 encoder to embedding-utils","createdAt":"2023-09-21T20:39:18Z"}
{"state":"Merged","mergedAt":"2023-09-21T21:04:16Z","number":8196,"body":"Was using the incorrect key for hvec video.\r\n\r\nFix key generation for sections.","mergeCommitSha":"a7995f8a75cff88bd566ff8818ae7a5092fbba39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8196","title":"Fix video and navigation issue","createdAt":"2023-09-21T20:53:42Z"}
{"state":"Merged","mergedAt":"2023-09-26T21:27:28Z","number":8197,"body":"This PR activates all per-user per-client rate limit rules in prod. These rules are set with a very high limit at the moment and we are not expecting any client under normal circumstances to come anywhere near these limits. \r\n\r\n- `PusherPerClientPerUserRateLimit for /api/channels/`, `LogsPerClientPerUserRateLimit for /api/logs` are set to 500 request per 5 minutes\r\n- `ApiPerClientPerUserRateLimit for /api/` are set to 1000 requests per 5 minutes \r\n- `ApiRateLimit` which rate limits based on IP is for now kept in non-enforcing mode\r\n\r\n","mergeCommitSha":"5d08adb059d30e39e15a1fe1010406462dca01cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8197","title":"Set prod rate limit rules to enforcing","createdAt":"2023-09-21T21:22:49Z"}
{"state":"Merged","mergedAt":"2023-09-21T21:31:29Z","number":8198,"mergeCommitSha":"763e063b3989bc0faf744fb224bd74b3776b681a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8198","title":"Fix binary","createdAt":"2023-09-21T21:31:24Z"}
{"state":"Merged","mergedAt":"2023-09-21T22:19:22Z","number":8199,"body":"These changes have been deployed locally via `eksctl`","mergeCommitSha":"2320fd98b6eea6ed0d26d3e6a2a558e0b425d1e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8199","title":"StepFunctions permissions for sourcecode service","createdAt":"2023-09-21T22:08:20Z"}
{"state":"Closed","mergedAt":null,"number":82,"body":"- Builds on https://github.com/Chapter2Inc/codeswell/pull/71\r\n- Implements https://www.notion.so/nextchaptersoftware/RFC-Model-User-Identities-51ca26f525774b33ae3a29ef94c96752\r\n\r\n### TODO\r\n\r\n- [ ] some rework to match Notion doc\r\n- [ ] complete testing\r\n- [ ] diagram","mergeCommitSha":"e0331410c79dba81883be319211582a3787af69b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/82","title":"[WIP] Introduce SourceMark models","createdAt":"2022-01-19T22:07:22Z"}
{"state":"Merged","mergedAt":"2022-04-07T17:39:30Z","number":820,"body":"Add `primaryEmail` property onto `Person` in our API.  `Person` is only used in `getPerson`, which returns the currently-logged-in user.\r\n\r\nI added a PersonStore as part of this, following the patterns in the other stores","mergeCommitSha":"08db74b1dd3e451ca239fbc9b8b9bdddad130634","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/820","title":"WIP: Add primary email property to Person API model","createdAt":"2022-04-07T17:13:20Z"}
{"state":"Merged","mergedAt":"2023-09-21T22:23:55Z","number":8200,"mergeCommitSha":"7867124a41b203990c94f1444432f6c2d30dc7fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8200","title":"Fix source code processor image","createdAt":"2023-09-21T22:16:56Z"}
{"state":"Merged","mergedAt":"2023-09-21T22:54:01Z","number":8201,"mergeCommitSha":"6b0c06c17a90e0dff5d8323ff5e0995615d2edad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8201","title":"Fix MessageModel.linearCommentId","createdAt":"2023-09-21T22:23:16Z"}
{"state":"Open","mergedAt":null,"number":8202,"mergeCommitSha":"3873ff68be55dc0ecaba62f71f55e34fc989e033","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8202","title":"Add Sidebar support bg","createdAt":"2023-09-21T22:27:16Z"}
{"state":"Merged","mergedAt":"2023-09-21T22:59:44Z","number":8203,"mergeCommitSha":"7b1c57462532578167ee8dd5b3453f4e84509daf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8203","title":"Add a team setting for sparse embeddings strategy","createdAt":"2023-09-21T22:37:03Z"}
{"state":"Merged","mergedAt":"2023-09-22T03:50:35Z","number":8204,"mergeCommitSha":"8a3cf52153f38a55b15813185e19e5921c5f8705","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8204","title":"Add step function env var for sparse embeddings strategy","createdAt":"2023-09-21T22:56:21Z"}
{"state":"Open","mergedAt":null,"number":8205,"body":"With Sonoma, using user defaults from the IDE will cause permissions popups.\r\n\r\nWe've partially moved away from that to use a Port file in tmp *but* there's a race where it may not exist if the Hub is opened *after* the IDE is opened.\r\n\r\nInstead of using user defaults as backup, only use the tmp file.","mergeCommitSha":"4238ebfd079c56a72f357daa77edfe8d6b9ca231","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8205","title":"Remove user defaults","createdAt":"2023-09-21T23:03:16Z"}
{"state":"Merged","mergedAt":"2023-09-22T03:55:53Z","number":8206,"mergeCommitSha":"ff753d5bd6a61388014225dbcfdd2349476b1455","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8206","title":"Add BM25 branching logic to embeddings generator","createdAt":"2023-09-21T23:31:47Z"}
{"state":"Merged","mergedAt":"2023-09-25T23:53:26Z","number":8207,"body":"* Adjust processing email (update text)\r\n* Send users to `/download` page on the dashboard, where the client calculates the following:\r\nA) Is Mac\r\n    1. Hub installed --> Open the hub\r\n    2. Hub not installed --> Prompt them to download the hub\r\n    \r\n    B) Is not Mac\r\n    * redirect to `/`","mergeCommitSha":"c66c182020c4b411d1e1ae761f4c2146a5573d53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8207","title":"Update processing email","createdAt":"2023-09-21T23:47:44Z"}
{"state":"Merged","mergedAt":"2023-09-22T04:04:09Z","number":8208,"mergeCommitSha":"3ae1ff6614da65a1c19d84df9263bb6c4091679a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8208","title":"Handle Linear issue webhooks","createdAt":"2023-09-21T23:48:37Z"}
{"state":"Merged","mergedAt":"2023-09-22T16:09:14Z","number":8209,"mergeCommitSha":"fe7265b062e8f34af001b8d7300fb142860a82c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8209","title":"Remove code","createdAt":"2023-09-21T23:55:25Z"}
{"state":"Merged","mergedAt":"2022-04-08T23:53:37Z","number":821,"body":"<img width=\"1233\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/162262959-16778bf3-987f-43bb-885b-e895d123f276.png\">\r\n<img width=\"654\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/162263003-8952c7b0-4962-4b41-b259-5cc0e1cb31f9.png\">\r\n<img width=\"1501\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/162263137-3eaf259c-f2c6-42b7-a7ca-013bbffee7c0.png\">\r\n<img width=\"469\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/162263270-042dc385-393d-4dcf-96d3-ce27ad2bb506.png\">\r\n\r\n\r\nNOTES:\r\n* Archived discussions are only listed on the web dashboard (i.e. users can archive threads on vscode but can only restore them from the dashboard)\r\n* Copy is placeholder/can be polished\r\n* Client-side filtering for now for archived threads, we should update the GET threads API to filter by archived / not archived but this should be okay for April\r\n* Archiving/restoring access is limited to the author of the thread/anchor message right now (similar to editing/deleting threads) -- all of this should eventually be replaced by some sort of role-based access control\r\n","mergeCommitSha":"7d29cfcd03a99481068fa4fbd62917c81588d76e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/821","title":"Add ability to archive threads","createdAt":"2022-04-07T17:37:34Z"}
{"state":"Merged","mergedAt":"2023-09-22T00:50:01Z","number":8210,"body":"Let's hope this fixes the issue.","mergeCommitSha":"1183d9eb50d9948762b1019bf96ad21199fb0d7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8210","title":"Fix dynamodb table","createdAt":"2023-09-22T00:45:48Z"}
{"state":"Merged","mergedAt":"2023-09-22T03:01:55Z","number":8211,"body":"- only access to specific table in this account\n- restrict permissions\n- adminweb service does not need access\n\nFor this error:\n```\ns.a.a.s.d.m.DynamoDbException: User: arn:aws:sts::************:assumed-role/eksctl-dev-addon-iamserviceaccount-default-s-Role1-1S48QHJNM1JIP/aws-sdk-java-************* is not authorized to perform: dynamodb:Scan on resource: arn:aws:dynamodb:us-west-2:************:table/documentPartitions because no identity-based policy allows the dynamodb:Scan action (Service: DynamoDb, Status Code: 400, Request ID: V9T7KQENB8C10EKRFTG6F94P2JVV4KQNSO5AEMVJF66Q9ASUAAJG)\n```","mergeCommitSha":"6c489087551e9ea23d0a17495882a20d163c3da3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8211","title":"Sourcecode service has access to DynamoDB table","createdAt":"2023-09-22T02:58:11Z"}
{"state":"Merged","mergedAt":"2023-09-22T19:53:38Z","number":8212,"body":"A few content tweaks from Dennis.","mergeCommitSha":"5fa597e1cfb9348b14a0533a642594ff010b4ea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8212","title":"Blog post content updates","createdAt":"2023-09-22T03:53:20Z"}
{"state":"Merged","mergedAt":"2023-09-22T19:53:23Z","number":8213,"body":"It was hidden in the smallest viewport previously. Showing it now. ","mergeCommitSha":"542dd95e83df646efc397fc1fed2a135890c2dd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8213","title":"Show hero video in mobile viewport","createdAt":"2023-09-22T04:07:22Z"}
{"state":"Merged","mergedAt":"2023-09-22T06:00:30Z","number":8214,"mergeCommitSha":"f66b2014a66b7e5b4444693f62f9602f2616f105","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8214","title":"Read and use embedding strategy in python images","createdAt":"2023-09-22T05:37:46Z"}
{"state":"Open","mergedAt":null,"number":8215,"body":"When IDEs open and the user launches the \"Connect\" page, we start polling very frequently (every 2 seconds) and this goes on forever.\r\n\r\nIntroduce the idea of long polling (every 30 seconds) for this passive polling and a shorter polling (every 2 seconds. times out after 60 seconds) for when an installation is triggered.\r\n\r\nAlso observing visibility so no polling occurs while the sidebar is hidden.\r\n\r\nThis should help resolve situations like this: \r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1695340760624209","mergeCommitSha":"5d9c4314cbfdbc27f570db514f7692629bb9f846","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8215","title":"Introduce long polling","createdAt":"2023-09-22T05:49:43Z"}
{"state":"Merged","mergedAt":"2023-09-22T07:37:13Z","number":8216,"mergeCommitSha":"16c6f993939c32bbba6f514a676d893b5dc24c72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8216","title":"Use team embedding strategy, default is SPLADE","createdAt":"2023-09-22T07:00:51Z"}
{"state":"Merged","mergedAt":"2023-09-22T08:14:33Z","number":8217,"mergeCommitSha":"1f6b83b62258a96deed06b7ff05a2456085da361","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8217","title":"Fix source code processor image","createdAt":"2023-09-22T08:11:16Z"}
{"state":"Merged","mergedAt":"2023-09-22T18:23:58Z","number":8218,"body":"In situations where a user goes back to an existing tab later in the video, the exact same filePath or URL is used as a key.\r\n\r\nThis cause some issues when deleting and rendering as we treat these filePaths / URLs as keys. Include the position to make each reference unique.\r\n\r\n","mergeCommitSha":"7702a70f70674584516d274d9dc49cde1c2539b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8218","title":"Handle identical files within createWalkthrough","createdAt":"2023-09-22T18:00:40Z"}
{"state":"Merged","mergedAt":"2023-09-22T19:18:01Z","number":8219,"body":"Shower time","mergeCommitSha":"e9d11c08a8fdcdb076004bb91d8351d8bfa642d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8219","title":"Preserve UInt32 encodings for sparse vectors","createdAt":"2023-09-22T18:15:47Z"}
{"state":"Merged","mergedAt":"2022-04-07T17:58:30Z","number":822,"body":"Updates web extension build for publishing.\r\n\r\nFix bug from merging multiple conflicting PRs.\r\nRename and fix CI.","mergeCommitSha":"a0141a48b815cef81f546a703fdfae90a3a09d9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/822","title":"Update web extension build for publish","createdAt":"2022-04-07T17:42:28Z"}
{"state":"Merged","mergedAt":"2023-09-22T20:19:13Z","number":8220,"body":"Added automation to create/update Kube IAM service accounts. We no longer need to do local `eksctl update iamserviceaccount....`","mergeCommitSha":"c0975af9655a1d990b216939b198b1cbae6b5fe7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8220","title":"Eksctl service account deploy via ci","createdAt":"2023-09-22T19:55:02Z"}
{"state":"Merged","mergedAt":"2023-09-22T20:33:16Z","number":8221,"body":"Changed the order of tasks so that we could re-use the SSH tunnel for Kube api endpoint in service account deployment tasks. ","mergeCommitSha":"00ed79329d267d54ac65edbcce76b85eccf2b2b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8221","title":"fix the task order to re-use ssh tunnel","createdAt":"2023-09-22T20:33:09Z"}
{"state":"Merged","mergedAt":"2023-09-22T21:04:48Z","number":8222,"mergeCommitSha":"f7edfa073cb767cff88a87ab1447dd6800f30a2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8222","title":"Log hash of confluence content","createdAt":"2023-09-22T20:44:52Z"}
{"state":"Merged","mergedAt":"2023-09-22T21:03:30Z","number":8223,"body":"- Add initial cluster skeleton\r\n- Update\r\n- Update files\r\n- Clustering\r\n- Update\r\n","mergeCommitSha":"9adaf8ae18bf7d7fad13d2c2ccb0cd1174c8e264","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8223","title":"ClusterV2","createdAt":"2023-09-22T21:03:20Z"}
{"state":"Merged","mergedAt":"2023-09-22T21:36:59Z","number":8224,"mergeCommitSha":"a95919437437a01dfb67303d24bd1972700eeb16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8224","title":"Add hybrid rank to admin console, clean some stuff up","createdAt":"2023-09-22T21:06:19Z"}
{"state":"Merged","mergedAt":"2023-09-22T21:16:53Z","number":8225,"mergeCommitSha":"ac0475e426d8021b16bef9ca4789c518552172aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8225","title":"pointing to the latest client assets","createdAt":"2023-09-22T21:07:02Z"}
{"state":"Open","mergedAt":null,"number":8226,"body":"Proposal to change the feedback buttons to a stroke/outline style for the following reasons:\r\n1) Outline state provides a better contrast to the filled in state (i.e. with feedback vs with feedback)\r\n2) Vscode lacks a theme color equivalent for all the button states (i.e. green/orange/red/yellow, vscode does not have an orange theme color) \r\n\r\ndashboard:\r\n<img width=\"784\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/2d31610b-961f-42e8-91ac-58a4b7781a46\">\r\n\r\nvscode:\r\n<img width=\"532\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/7aa61574-709e-4a35-aacd-548095fa20f0\">\r\n\r\njetbrains:\r\n<img width=\"450\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/8aaf8243-f644-4e82-b1cb-b2e1c56bd1e2\">","mergeCommitSha":"d75b8be9d344d78eaec6efe9efdd8633cb4879c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8226","title":"Feedback button colors","createdAt":"2023-09-22T21:42:13Z"}
{"state":"Merged","mergedAt":"2023-09-22T22:01:23Z","number":8227,"mergeCommitSha":"f11f064b9d8b4f834eece75a678aa302a91ab5ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8227","title":"Render data tables on HTMX settle","createdAt":"2023-09-22T21:52:50Z"}
{"state":"Merged","mergedAt":"2023-09-22T23:00:23Z","number":8228,"mergeCommitSha":"7c0a5932437f1bb0d4010dd8fb910ab90e5d913b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8228","title":"Log modifiedAt for confluence content","createdAt":"2023-09-22T22:40:57Z"}
{"state":"Merged","mergedAt":"2023-09-22T23:22:24Z","number":8229,"mergeCommitSha":"91fb8fe0dd0265227648c83feeca7ca459dc7771","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8229","title":"Display indexes and values separately for sparse vectors","createdAt":"2023-09-22T23:21:49Z"}
{"state":"Merged","mergedAt":"2022-04-07T18:38:10Z","number":823,"body":"Moving jobs to the end of list so they would catch failures? I am not sure if order matters but right now we are not getting alerts on failures. It's still worth a try.","mergeCommitSha":"e44fb2dee29e2677dbfac998ef908422c57fd2b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/823","title":"I think job orders matter","createdAt":"2022-04-07T18:15:31Z"}
{"state":"Merged","mergedAt":"2023-09-23T01:54:33Z","number":8230,"mergeCommitSha":"6180b0c8048522d70577e4ff2c280b21abf830ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8230","title":"Pass incremental config input params from sourcecode service to sourcecode pipeline","createdAt":"2023-09-22T23:25:51Z"}
{"state":"Merged","mergedAt":"2023-09-23T00:00:30Z","number":8231,"mergeCommitSha":"e0a7a92574536af02065f33aa17c3850841e5e52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8231","title":"Update submodule commit","createdAt":"2023-09-22T23:50:50Z"}
{"state":"Merged","mergedAt":"2023-09-23T00:23:02Z","number":8232,"mergeCommitSha":"fc779afc4730d301266893d7cb769c033a66b83d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8232","title":"Log dense vectors for queries","createdAt":"2023-09-23T00:20:28Z"}
{"state":"Closed","mergedAt":null,"number":8233,"mergeCommitSha":"4738db26808787491c3e7e7ce3a6ebde2895c720","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8233","title":"Broken sparse score example","createdAt":"2023-09-23T02:20:24Z"}
{"state":"Merged","mergedAt":"2023-09-23T03:29:22Z","number":8234,"mergeCommitSha":"0be306ce1aa2faaa2f579db77be0322e92de81b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8234","title":"Activmeq instrumentation","createdAt":"2023-09-23T02:56:06Z"}
{"state":"Merged","mergedAt":"2023-09-23T03:06:08Z","number":8235,"mergeCommitSha":"d368e1d6d24373eac8803b66e68b820eb2ef70f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8235","title":"Fix source code","createdAt":"2023-09-23T03:05:58Z"}
{"state":"Merged","mergedAt":"2023-09-23T04:24:54Z","number":8236,"body":"Revert \"Fix source code (#8235)\"\n\nThis reverts commit d368e1d6d24373eac8803b66e68b820eb2ef70f9.\n\nRevert \"Pass incremental config input params from sourcecode service to sourcecode pipeline (#8230)\"\n\nThis reverts commit 6180b0c8048522d70577e4ff2c280b21abf830ff.","mergeCommitSha":"97964124873bd6dcaed7a5bf57167d0c43c0ee92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8236","title":"Revert source code config","createdAt":"2023-09-23T04:24:39Z"}
{"state":"Merged","mergedAt":"2023-09-23T05:55:20Z","number":8237,"mergeCommitSha":"3f860c52a5a65626384ed09b9f7f4285ea0094b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8237","title":"Only embed confluence doc if changed","createdAt":"2023-09-23T05:35:19Z"}
{"state":"Merged","mergedAt":"2023-09-23T08:25:03Z","number":8238,"mergeCommitSha":"58c6a05aa0b3f5e90489b007f8da97ce80f793f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8238","title":"Activmeq improvements","createdAt":"2023-09-23T06:00:04Z"}
{"state":"Merged","mergedAt":"2023-09-23T09:02:59Z","number":8239,"mergeCommitSha":"78eca48efbb98ebf7c2b6e4b8de5e0237d218963","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8239","title":"Upudate call stack","createdAt":"2023-09-23T08:45:43Z"}
{"state":"Merged","mergedAt":"2022-04-07T20:19:28Z","number":824,"mergeCommitSha":"7f965686adbfe97ae7ba0160e6924dd549a0f3d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/824","title":"Use teamId from path for putSourcemarks","createdAt":"2022-04-07T18:23:27Z"}
{"state":"Merged","mergedAt":"2023-09-23T09:55:56Z","number":8240,"mergeCommitSha":"b6697ae7b2cdfaeccdbe04b2d192872ccfa0517c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8240","title":"Stack trace improvements","createdAt":"2023-09-23T09:54:58Z"}
{"state":"Merged","mergedAt":"2023-09-23T18:51:52Z","number":8241,"mergeCommitSha":"989e54cb833f25cc33e3ed373a4a56ae556dcc69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8241","title":"Use query specific embeddings methods for queries","createdAt":"2023-09-23T18:23:26Z"}
{"state":"Merged","mergedAt":"2023-09-23T20:29:10Z","number":8242,"mergeCommitSha":"22d95fa2c6ad1d5558885374cdb364e3f196d89e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8242","title":"Update source code processor to use updated embeddings-utils","createdAt":"2023-09-23T19:04:27Z"}
{"state":"Merged","mergedAt":"2023-09-23T20:47:49Z","number":8243,"mergeCommitSha":"1796ba459803c2ea23c55110164606c79114af2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8243","title":"Update the instructor endpoint to use query embeddings","createdAt":"2023-09-23T20:26:55Z"}
{"state":"Merged","mergedAt":"2023-09-25T17:56:38Z","number":8244,"mergeCommitSha":"5bc1e9d3a15db6e6612f770378724d38a85d06a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8244","title":"Use query embeddings for queries","createdAt":"2023-09-23T21:09:36Z"}
{"state":"Merged","mergedAt":"2023-09-24T05:09:31Z","number":8245,"body":"IDE config for python libs\n\nNo detekt","mergeCommitSha":"324d5d1eca9eec9596d71651bb5b108f621bfd5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8245","title":"IDE config for python libs","createdAt":"2023-09-24T04:38:19Z"}
{"state":"Merged","mergedAt":"2023-09-24T05:19:37Z","number":8246,"mergeCommitSha":"7132b7b2085c889679029492426eb0209e8f2b17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8246","title":"Code formatting in CI for python libs using black","createdAt":"2023-09-24T04:38:22Z"}
{"state":"Merged","mergedAt":"2023-09-25T21:30:32Z","number":8247,"body":"Pass incremental config input params from sourcecode service to sourcecode pipeline (#8230)\n\nUse KT enum","mergeCommitSha":"55409de6d0cf254bbab23136146e7957870cc0f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8247","title":"Pass incremental config input params from sourcecode service to sourcecode pipeline V2","createdAt":"2023-09-24T05:46:57Z"}
{"state":"Merged","mergedAt":"2023-09-25T21:30:07Z","number":8248,"mergeCommitSha":"5632072307c1a06dce66dd6fd65b7105a78172da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8248","title":"Git utils to support incremental code ingestion","createdAt":"2023-09-24T06:00:05Z"}
{"state":"Merged","mergedAt":"2023-09-24T17:51:44Z","number":8249,"mergeCommitSha":"8ba97051d81c5ffe4543167a30615f0cfc85ef93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8249","title":"Update Slight wording change","createdAt":"2023-09-24T17:51:16Z"}
{"state":"Merged","mergedAt":"2022-04-07T18:31:47Z","number":825,"body":"This includes the logback.xml that we need.","mergeCommitSha":"7357ac7b54e0058cdd960de024b969a8a0de0c59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/825","title":"Add lib log","createdAt":"2022-04-07T18:31:11Z"}
{"state":"Merged","mergedAt":"2023-09-25T16:08:29Z","number":8250,"mergeCommitSha":"02f7704453965977feeb5bd50e062e64cc0cb108","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8250","title":"App Direct customer quote and create walkthrough video asset to blog post","createdAt":"2023-09-25T15:57:35Z"}
{"state":"Merged","mergedAt":"2023-09-25T17:51:53Z","number":8251,"mergeCommitSha":"36253a9367b1c3d76012cb09b19d4891b5a8b5e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8251","title":"Downscale search service to avoid overloading Pinecone with 80 concurrent writers","createdAt":"2023-09-25T17:31:22Z"}
{"state":"Merged","mergedAt":"2023-09-25T18:15:33Z","number":8252,"body":"For whatever reason, iOS 17 (I think?) ignores scroll events on the video element.  I suspect this is a Safari bug.\r\n\r\nAdding an empty overlay window overtop of the video element fixes scrolling.  This is a pretty gross hack, but seems to work.","mergeCommitSha":"32b2b63e4db5c25e0e1bcdb38f0803ce46da914a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8252","title":"Fix mobile landing page scrolling","createdAt":"2023-09-25T18:00:11Z"}
{"state":"Merged","mergedAt":"2023-09-25T19:57:21Z","number":8253,"mergeCommitSha":"f870f18a4e4169393aedd6d3e7c143ade6e5c519","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8253","title":"Downscale code ingestion job concurrency from 15 to 10","createdAt":"2023-09-25T19:15:48Z"}
{"state":"Merged","mergedAt":"2023-09-25T21:02:26Z","number":8254,"mergeCommitSha":"e636e07b95acbe35425b390297b1d76852154280","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8254","title":"Update cdk dependencies","createdAt":"2023-09-25T19:19:33Z"}
{"state":"Merged","mergedAt":"2023-09-25T21:25:23Z","number":8255,"body":"We need to reference the recipient name in some of the email templates; this property needs to be funnelled through the sendgrid API to our templates ","mergeCommitSha":"1f5b6a146cf0b36cebd3ddba4579c04c57a8ad07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8255","title":"Add recipient name to sendgrid payload","createdAt":"2023-09-25T20:04:16Z"}
{"state":"Merged","mergedAt":"2023-09-26T04:10:04Z","number":8256,"body":"- About page: alphabetize team by last name\r\n- About page: update careers email address \r\n- New security section:\r\n![CleanShot 2023-09-25 at 13 28 53@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/01e4b1be-6562-4e4a-ae15-eaff9bacc2d9)\r\n- Fix 'Features' navigation page scroll\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13353189/a6a375c5-41c4-46b0-b28e-7e484fa4726e\r\n- Add paragraphs to tab switcher\r\n![CleanShot 2023-09-25 at 17 35 28@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/5de26985-79ad-4a30-8116-1ea2a4f3ee05)\r\n\r\n","mergeCommitSha":"b3de44ae4e3f73c3a2e99376c153ad982ee0b3f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8256","title":"Landing page fixes","createdAt":"2023-09-25T20:28:39Z"}
{"state":"Merged","mergedAt":"2023-09-26T16:25:12Z","number":8257,"mergeCommitSha":"ef5497f14ddebae24b05004d26dadb41cd5a1023","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8257","title":"Add Embedding Service","createdAt":"2023-09-25T20:33:02Z"}
{"state":"Merged","mergedAt":"2023-09-25T21:58:56Z","number":8258,"body":"- Max retries is 5 — this only affects the `UNAVAILABLE` status code.\n- Server side timeout is 20s, which is the default. Was 5m.","mergeCommitSha":"0acf696aecce15c18d53cf5d6bc8cff506e002cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8258","title":"Reasonble Pinecone retry config","createdAt":"2023-09-25T21:02:45Z"}
{"state":"Merged","mergedAt":"2023-09-25T21:45:13Z","number":8259,"mergeCommitSha":"ed8910490b788f757c3c667cc69c7a58067680b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8259","title":"Fx topic mpaping to take into account keywords","createdAt":"2023-09-25T21:12:19Z"}
{"state":"Merged","mergedAt":"2022-04-07T19:04:55Z","number":826,"mergeCommitSha":"f213bd8131d99f8c67810f64ef70ad47ad2cfba0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/826","title":"Try again","createdAt":"2022-04-07T19:04:48Z"}
{"state":"Merged","mergedAt":"2023-09-26T00:48:05Z","number":8260,"mergeCommitSha":"2371c0f411d8603551c301f3ced10d4451da3994","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8260","title":"Add gpt instructor and move topics extraction to gpt35 instructor","createdAt":"2023-09-26T00:10:15Z"}
{"state":"Merged","mergedAt":"2023-09-26T19:01:59Z","number":8261,"mergeCommitSha":"bdad78d531e6033751f91b52acb56abac209169c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8261","title":"Dead code removal for now","createdAt":"2023-09-26T17:28:14Z"}
{"state":"Merged","mergedAt":"2023-09-26T17:37:16Z","number":8262,"mergeCommitSha":"da98af39c1db6f621a8f75aca16cf9285c5af320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8262","title":"Rename infra files","createdAt":"2023-09-26T17:31:02Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:25:19Z","number":8263,"mergeCommitSha":"da4124fc4fec4af52bff1737340a6cd2b9032d68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8263","title":"Cp public subdir to public dist","createdAt":"2023-09-26T17:36:16Z"}
{"state":"Merged","mergedAt":"2023-09-26T17:41:51Z","number":8264,"mergeCommitSha":"06f5d70400d8101a37b8d757f8c151c22f99d540","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8264","title":"Files changed","createdAt":"2023-09-26T17:40:11Z"}
{"state":"Merged","mergedAt":"2023-09-26T19:58:08Z","number":8265,"body":"Adds a `BatchEventDequeueService` that consumes a batch of messages from a queue before passing the batch to a `BatchEventHandler` to process. \r\n\r\nTo start I've added a `delete_embedding_queue` that will be consumed using this batch service. For now it will just log the batches, but the next PR will call the `PineconeDeleteApi` to batch delete the documents.","mergeCommitSha":"baf0a39dd38706dddf1d1b3454075ecd337ae0a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8265","title":"Add batched event handling","createdAt":"2023-09-26T17:40:25Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:39:53Z","number":8266,"mergeCommitSha":"318fa5178e0a0fd75b84bf5b16f875aada316644","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8266","title":"Update About.tsx","createdAt":"2023-09-26T17:42:39Z"}
{"state":"Merged","mergedAt":"2023-09-26T17:43:45Z","number":8267,"mergeCommitSha":"61ad9e26e3496ce8d7fdd7f912db548adcb4c6df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8267","title":"Another bad infra file path","createdAt":"2023-09-26T17:43:39Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:10:55Z","number":8268,"mergeCommitSha":"3f81251e30e5280c8bbddc64ac9fd448a6a34cca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8268","title":"Deployment needs to be approved","createdAt":"2023-09-26T18:09:23Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:13:58Z","number":8269,"mergeCommitSha":"993bcb77e8c4e37f708e61b3d7a173a32819f19a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8269","title":"Try again","createdAt":"2023-09-26T18:13:49Z"}
{"state":"Merged","mergedAt":"2022-04-07T23:31:19Z","number":827,"body":"TODO in future PRs\r\n\r\n- [ ] support storing SourcePoints for uncommitted file changes\r\n- [ ] storing cache on disk so that we don't need to fetch all sourcemarks on start\r\n- [x] support push updates so that we can fetch new sourcemarks (https://github.com/NextChapterSoftware/unblocked/pull/827#discussion_r845524947)","mergeCommitSha":"acc9909186cfd87aaa0edc3adfe8e0aa10581ed5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/827","title":"First pass at implementing SourceMarkStore","createdAt":"2022-04-07T19:31:15Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:27:09Z","number":8270,"mergeCommitSha":"574e5d42c0db49ae6df1685538728f6207c10a43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8270","title":"Fix kube docs","createdAt":"2023-09-26T18:27:00Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:56:06Z","number":8271,"mergeCommitSha":"92907fa230b20cace5e8922817dc59b2cc576bf6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8271","title":"Maybe fix meta image","createdAt":"2023-09-26T18:45:37Z"}
{"state":"Merged","mergedAt":"2023-09-26T18:55:13Z","number":8272,"body":"```sh\nmake check release\n```","mergeCommitSha":"cb8658355d7f2a49e280d03e166d53dc87baee9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8272","title":"Improve Python lib release process","createdAt":"2023-09-26T18:52:09Z"}
{"state":"Merged","mergedAt":"2023-09-26T20:20:57Z","number":8273,"mergeCommitSha":"fcd6799750831dffb932fe91ce1782401c2cee73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8273","title":"Deprecate davinci","createdAt":"2023-09-26T19:57:02Z"}
{"state":"Merged","mergedAt":"2023-09-26T20:38:04Z","number":8274,"body":"- changed the blog route from `/article-001` to `get-unblocked`\r\n- moved the `Docs` link from the footer into the header","mergeCommitSha":"e4e19840ad6889d1cc9cbf14b48989704ac92ab8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8274","title":"Blog route change","createdAt":"2023-09-26T20:13:37Z"}
{"state":"Merged","mergedAt":"2023-09-26T21:19:08Z","number":8275,"mergeCommitSha":"1097506c59ca6a805b385eefd01bd952c9061a7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8275","title":"Fix DeleteEmbeddingEventHandler logging","createdAt":"2023-09-26T20:45:45Z"}
{"state":"Merged","mergedAt":"2023-09-26T21:28:05Z","number":8276,"mergeCommitSha":"fc3fb4505823a073013a6bbcf7480fa217f98c7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8276","title":"Add catch handlers for data pipeline","createdAt":"2023-09-26T21:19:54Z"}
{"state":"Merged","mergedAt":"2023-09-26T23:11:09Z","number":8277,"body":"- Update date to Oct 3rd\r\n- Clean up some text","mergeCommitSha":"be4e7d49f64099ee1219ddfb177601b288d453b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8277","title":"Update blog post","createdAt":"2023-09-26T21:51:17Z"}
{"state":"Merged","mergedAt":"2023-09-26T22:11:34Z","number":8278,"mergeCommitSha":"30d5150911368508a74241bb60b670362b6c73e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8278","title":"Add logs to check we're dequeueing multiple events","createdAt":"2023-09-26T21:55:07Z"}
{"state":"Merged","mergedAt":"2023-09-26T23:11:23Z","number":8279,"mergeCommitSha":"2eaab90887ced3267e1c4e33be0516c9ca04afea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8279","title":"Update Article-001.tsx","createdAt":"2023-09-26T21:59:15Z"}
{"state":"Merged","mergedAt":"2022-04-07T19:48:13Z","number":828,"mergeCommitSha":"10e02f6d99a57b769295cc09c9019fb95ef70c06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/828","title":"Rename action to services-build","createdAt":"2022-04-07T19:48:08Z"}
{"state":"Merged","mergedAt":"2023-09-27T00:34:53Z","number":8280,"body":"- Changed the integrations section based on feedback from Dennis. \r\n- Also added left and right quotation marks to the blog post title\r\n\r\n![CleanShot 2023-09-26 at 15 11 31@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/65ff2a88-2313-4c05-b4b0-900e0482af78)\r\n","mergeCommitSha":"96d169ec3501e11041e888a2cd5db3530912cef4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8280","title":"New integrations section","createdAt":"2023-09-26T22:13:07Z"}
{"state":"Merged","mergedAt":"2023-09-26T23:12:00Z","number":8281,"body":"The log line below this shows we're only dequeuing one message per call, whereas I'd expect multiple (up to batchSize). I'm not sure if this change will fix but lets give it a shot. ","mergeCommitSha":"ed8d056aebdb0b1b6451849b998cb0e67005a0ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8281","title":"Try sequentially receiving messages","createdAt":"2023-09-26T22:54:34Z"}
{"state":"Merged","mergedAt":"2023-09-27T00:48:30Z","number":8282,"mergeCommitSha":"397bf2824e8c5254a54a273dbc2542949fde1d59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8282","title":"Add responsive button","createdAt":"2023-09-26T23:31:34Z"}
{"state":"Merged","mergedAt":"2023-09-26T23:42:36Z","number":8283,"body":"This reverts commit ed8d056aebdb0b1b6451849b998cb0e67005a0ca.","mergeCommitSha":"0261c974f7092128a4e13b23310fe368a74a3512","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8283","title":"Revert \"Try sequentially receiving messages (#8281)\"","createdAt":"2023-09-26T23:42:17Z"}
{"state":"Merged","mergedAt":"2023-09-28T05:30:56Z","number":8284,"mergeCommitSha":"0e7a7cdd613e83723b104abe6f6e88e2b8772a41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8284","title":"Use custom word tokenizer for sparse vectors","createdAt":"2023-09-27T00:22:56Z"}
{"state":"Merged","mergedAt":"2023-09-27T01:36:44Z","number":8285,"body":"Lots of hacks because they have internal everything for their classes...","mergeCommitSha":"a3684242f5f9c096f9845396cb3b02cd95ea1ea8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8285","title":"Instrument openai","createdAt":"2023-09-27T00:33:23Z"}
{"state":"Merged","mergedAt":"2023-09-27T02:41:20Z","number":8286,"mergeCommitSha":"e39d4ee119571870d40b418c8c98f16b0932c355","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8286","title":"Fix activemq batch processing","createdAt":"2023-09-27T01:57:55Z"}
{"state":"Merged","mergedAt":"2023-09-27T02:42:44Z","number":8287,"mergeCommitSha":"38adc1f46d8e86be0b1eaa85cf26e99d7b474ce7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8287","title":"Fix diff routes","createdAt":"2023-09-27T02:39:15Z"}
{"state":"Merged","mergedAt":"2023-09-27T03:42:59Z","number":8288,"body":"In general, we should alwyas depend on the latest non-breaking \"unblocked-\" utils using this declaration, which means that we take all versions except breaking versions.\n\n```\nunblocked-foo-lib=\"^0\"\n```","mergeCommitSha":"c30ab2096097c3cce1ac83cb34a7218fcd2a3d7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8288","title":"Uplevel PY file-utils to depend on latest non-breaking git-utils","createdAt":"2023-09-27T03:27:52Z"}
{"state":"Merged","mergedAt":"2023-09-27T05:03:49Z","number":8289,"mergeCommitSha":"b4e0caf3c8ee0d97f487171869a8c91226760855","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8289","title":"Uplevel all python libs","createdAt":"2023-09-27T04:58:53Z"}
{"state":"Merged","mergedAt":"2022-04-07T22:54:13Z","number":829,"mergeCommitSha":"f5160c3359d9f15b358d0bfcf829dc4f5e2c99c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/829","title":"Refactor some utils","createdAt":"2022-04-07T20:58:16Z"}
{"state":"Merged","mergedAt":"2023-09-27T06:02:00Z","number":8290,"mergeCommitSha":"5b585af9d13cebb6afa36c7b3b0fad079a1d36f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8290","title":"Lambda exposes AWS_STEP_FUNCTIONS_EXECUTION_ARN to ActiveMQ","createdAt":"2023-09-27T05:12:02Z"}
{"state":"Merged","mergedAt":"2023-09-27T06:25:56Z","number":8291,"body":"This was responsible for large numbers of failed file partitions.\r\n\r\nPartitions were NoneType.\r\n\r\nAdded failing unit test to catch future regressions.","mergeCommitSha":"f0ab7ce012a8f043cac600d4b0962e03d5384ba1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8291","title":"Fix SentenceSplitter partitioning mode","createdAt":"2023-09-27T06:05:28Z"}
{"state":"Merged","mergedAt":"2023-09-27T07:19:09Z","number":8292,"body":"- cleanup unused tools in source-code-data-pipeline\n- uplevel all poetry dependency declaration to latest non-breaking\n- plumbing PY code ingestion output to S3 results.json\n- plumbing KT RepoId to StepFunctions input payload, so that the stage completion lambda for the code ingestion pipeline picks it up and sends it back to KT via ActiveMQ","mergeCommitSha":"6668cd33b064a08f36a56e3396bc04ba7422363f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8292","title":"Fix code ingest output payload","createdAt":"2023-09-27T06:25:32Z"}
{"state":"Merged","mergedAt":"2023-09-27T16:07:09Z","number":8293,"mergeCommitSha":"f56f6e31d7812fa0acdc8c7f5c58319edb98db9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8293","title":"Fix json path for code ingestion data pipeline","createdAt":"2023-09-27T16:02:56Z"}
{"state":"Merged","mergedAt":"2023-09-27T16:13:04Z","number":8294,"mergeCommitSha":"0ede83df505cd89beaa57b58376f6e470cbf09f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8294","title":"Fix from state machine rename","createdAt":"2023-09-27T16:12:06Z"}
{"state":"Merged","mergedAt":"2023-09-27T17:38:56Z","number":8295,"mergeCommitSha":"8ed673ed363f89c8f106ce42008e94842a628ac5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8295","title":"Update DeleteEmbeddingEventHandler to batch delete embeddings","createdAt":"2023-09-27T17:14:38Z"}
{"state":"Merged","mergedAt":"2023-09-27T17:55:45Z","number":8296,"body":"This rule is disabled in prod. We were testing it in Dev to see how would it behave. \r\n\r\nAs we expected it does not properly handle multiple users behind a corp network. ","mergeCommitSha":"878048ea541f8c7d5485e1128b0ef7483356981e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8296","title":"disable ip based rate limit","createdAt":"2023-09-27T17:39:13Z"}
{"state":"Merged","mergedAt":"2023-09-27T18:06:29Z","number":8297,"mergeCommitSha":"c597b3ea78044daf672f6f5ab3fdeea7c967e9c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8297","title":"Fixes openAI max token sizes for chat and completion","createdAt":"2023-09-27T17:42:34Z"}
{"state":"Merged","mergedAt":"2023-09-27T18:53:53Z","number":8298,"body":"This is now covered by `EmbeddingEvent.DeleteEmbedding`","mergeCommitSha":"48c70fe73430d6c7bfea9ae52531249d38d025b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8298","title":"Remove SearchEvent.RemoveEmbedding event","createdAt":"2023-09-27T18:03:26Z"}
{"state":"Merged","mergedAt":"2023-09-27T18:57:13Z","number":8299,"mergeCommitSha":"1d2e09a77f5bff9601f5eaa87f3acd3d35b67f99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8299","title":"Git util command must take repo root directory","createdAt":"2023-09-27T18:47:26Z"}
{"state":"Merged","mergedAt":"2022-01-19T22:57:54Z","number":83,"body":"- Created a new CDK stack to manage IAM resources. Also added two IAM roles for managing access to EKS \r\n- Added RBAC role/rolebinding definitions for Kubernetes. These roles are used by IODC when mapping IAM users (via IAM roles) to access levels on Kube\r\n- Updated EKS cluster setup instructions \r\n- Added a lot of documentation around EKS-IAM auth management \r\n\r\nThese changes have been deployed to Dev. I have tested Kube access with limited `deploy` role. ","mergeCommitSha":"e232f2188b4b00a411a682bbef148bfacb801988","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/83","title":"Cdk add iam eks roles and groups","createdAt":"2022-01-19T22:50:26Z"}
{"state":"Merged","mergedAt":"2022-04-07T22:47:29Z","number":830,"body":"Due to `.startWith({ initialized: false, value: undefined })`, the initial event for the stream may be incorrect if the stream's value was set before it's subscribed to.\r\n\r\nAlso fixed issue with unsubscribing in sync. Must be done async.","mergeCommitSha":"47a2df5d6a0fb69078a5808f907f0f79c917a330","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/830","title":"Fix issue with initial VCS update","createdAt":"2022-04-07T21:42:03Z"}
{"state":"Merged","mergedAt":"2023-09-27T19:25:11Z","number":8300,"mergeCommitSha":"ff7513be8a4dc028fe7f995aaac7ca914049c269","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8300","title":"Goodbye davinci","createdAt":"2023-09-27T18:58:15Z"}
{"state":"Merged","mergedAt":"2023-09-27T19:08:43Z","number":8301,"mergeCommitSha":"273b58e0c060e17f9dca15357c4fda4dc5d7fa54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8301","title":"Fix StepFunction stage task failed catch conditional","createdAt":"2023-09-27T19:07:14Z"}
{"state":"Merged","mergedAt":"2023-09-27T19:32:14Z","number":8302,"mergeCommitSha":"0308ae0d6174e9277736847e1e697bfae75c50d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8302","title":"Rename CodeIngestionDataPipeline","createdAt":"2023-09-27T19:20:30Z"}
{"state":"Merged","mergedAt":"2023-09-27T22:13:58Z","number":8303,"mergeCommitSha":"4dd4f14f2260d853f621991de8ef8790f818fb93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8303","title":"Update EmbeddingPersistence to allow for batched upserts","createdAt":"2023-09-27T20:02:51Z"}
{"state":"Merged","mergedAt":"2023-09-27T20:21:18Z","number":8304,"mergeCommitSha":"51813ac3b4fe121c71396cbd5ce9fe15e4b8a0ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8304","title":"Bump submodule","createdAt":"2023-09-27T20:08:55Z"}
{"state":"Merged","mergedAt":"2023-09-27T21:19:05Z","number":8305,"mergeCommitSha":"a9755561d44c45a8f3cebb77161c9faeccd3964a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8305","title":"Plumbing stepFunctionArn and repoId to code ingestion output","createdAt":"2023-09-27T21:09:16Z"}
{"state":"Merged","mergedAt":"2023-09-27T21:32:48Z","number":8306,"mergeCommitSha":"107870676d47d08d1a85a7aa67e666cc2fa885dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8306","title":"Event handler typing","createdAt":"2023-09-27T21:12:00Z"}
{"state":"Merged","mergedAt":"2023-09-27T23:52:22Z","number":8307,"body":"@matthewjamesadam - I took a stab at adding video posters. Let me know where I went wrong!","mergeCommitSha":"51d5f4e11352176140cac33f1d9d603ddc9e333f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8307","title":"add video posters","createdAt":"2023-09-27T21:31:04Z"}
{"state":"Merged","mergedAt":"2023-09-27T22:07:51Z","number":8308,"mergeCommitSha":"a1662c1feb15e428d1ab63f1b1d3a6fa79ef2f18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8308","title":"Rename stuff","createdAt":"2023-09-27T22:07:08Z"}
{"state":"Merged","mergedAt":"2023-09-28T00:09:56Z","number":8309,"body":"https://github.com/NextChapterSoftware/unblocked/assets/********/97e2c408-feae-400a-a473-f9ca82009d86\r\n\r\nNote: not currently hooked up to any routes","mergeCommitSha":"d487a98138fddcbdd5168c93c301fa147b622b35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8309","title":"Personal org error UI","createdAt":"2023-09-27T22:21:51Z"}
{"state":"Merged","mergedAt":"2022-04-08T17:24:26Z","number":831,"body":"Filter for SourcePoints for the current commit. \r\n\r\nAssumes each SourceMark has a source point for each commit since the source mark's creation...but maybe this is a bad assumption?","mergeCommitSha":"c0a53f6af32d36fc0c510996793a5a145b114a51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/831","title":"Filter SourcePoints by commit in addition to filePath","createdAt":"2022-04-08T00:07:52Z"}
{"state":"Closed","mergedAt":null,"number":8310,"body":"## Related\r\n- https://github.com/NextChapterSoftware/unblocked/pull/8309\r\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1695835809878959\r\n\r\n## Usage\r\n1. Call the new `getInstallationStatus` first\r\n2. If the status is `ok`, then start polling on refresh token until teams appear\r\n3. If the status is not `ok`, then show error message (do not poll in this case)","mergeCommitSha":"f60c1485f26754fde465448ae3f99032ab72dc1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8310","title":"Introduce SCM installation status for web 'install/redirect' route","createdAt":"2023-09-27T22:53:26Z"}
{"state":"Merged","mergedAt":"2023-09-27T23:34:01Z","number":8311,"mergeCommitSha":"99b0deb4f945d67a842dc3bcfd56b70bcce4457c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8311","title":"Rename","createdAt":"2023-09-27T23:09:13Z"}
{"state":"Merged","mergedAt":"2023-09-28T02:32:34Z","number":8312,"mergeCommitSha":"fb2e07d75f7962472eb1a67504462649179e920c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8312","title":"DocumentEmbeddingService emits an UpsertEmbeddingEvent","createdAt":"2023-09-27T23:55:47Z"}
{"state":"Merged","mergedAt":"2023-09-28T03:10:13Z","number":8313,"mergeCommitSha":"2b26532eb93089d5d07bed5b458141953da23786","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8313","title":"UpsertEmbeddingEventHandler upserts vectors to pinecone","createdAt":"2023-09-28T02:45:28Z"}
{"state":"Merged","mergedAt":"2023-09-28T06:17:02Z","number":8314,"mergeCommitSha":"04ed0ff12fe27aa031e913c8d015ad0955e7cb8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8314","title":"Portable named uuid generation from repo files","createdAt":"2023-09-28T05:06:42Z"}
{"state":"Merged","mergedAt":"2023-09-28T07:26:27Z","number":8315,"mergeCommitSha":"21f41b6adc0d47c592cf6ab9e60fdee1c3fbcaaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8315","title":"Update BM25 embeddings for source code","createdAt":"2023-09-28T06:47:10Z"}
{"state":"Closed","mergedAt":null,"number":8316,"mergeCommitSha":"6b37a114e0f651687fcdc87776a1a1a9fca36ff9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8316","title":"Dynamo PY utils","createdAt":"2023-09-28T07:15:25Z"}
{"state":"Merged","mergedAt":"2023-09-28T07:38:39Z","number":8317,"mergeCommitSha":"021661940514f331350d9f33aab901779c5a4e29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8317","title":"Update instructor to use new BM25","createdAt":"2023-09-28T07:22:00Z"}
{"state":"Merged","mergedAt":"2023-09-28T17:43:47Z","number":8318,"body":"Chrome was exhibiting some strange column gap differences in the features tab section on the landing page. This PR adjusts the `column-gap` to rectify that. It also adds a new XXL breakpoint and adjusts `top-margin` to accommodate for the longer content.","mergeCommitSha":"feb97c638fa24057ad3e806d31ba19f17afafdda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8318","title":"Fix responsive feature section on chrome","createdAt":"2023-09-28T16:55:31Z"}
{"state":"Merged","mergedAt":"2023-09-28T18:00:29Z","number":8319,"mergeCommitSha":"85a6aee30ed518ccea0ae68c3583e8b1daa222ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8319","title":"Plumbing more incremental code config from KT to PY","createdAt":"2023-09-28T17:48:36Z"}
{"state":"Merged","mergedAt":"2022-04-08T00:58:59Z","number":832,"mergeCommitSha":"3b33f1bcf05cb76fa31921193e07f631666339db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/832","title":"Port Git utils from KT to TS","createdAt":"2022-04-08T00:20:20Z"}
{"state":"Merged","mergedAt":"2023-09-28T18:20:46Z","number":8320,"mergeCommitSha":"862e4d80970dcbdf5967610ea14a991a51333f76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8320","title":"Update refinery configuration","createdAt":"2023-09-28T18:15:37Z"}
{"state":"Merged","mergedAt":"2023-09-28T18:37:33Z","number":8321,"body":"This matches the hub.\r\n\r\n![CleanShot 2023-09-28 at 11 26 11@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/9ba7b578-1038-4c39-83a3-d08dd9706f35)\r\n","mergeCommitSha":"462e6d5269fbbcf956c4d8875771ea91eea718ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8321","title":"submodule bump - new hero video animation with tighter letter spacing","createdAt":"2023-09-28T18:26:37Z"}
{"state":"Merged","mergedAt":"2023-09-28T19:47:42Z","number":8322,"mergeCommitSha":"b37190bb17688d8f4cd014d69b15c26fd4683ca3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8322","title":"Code ingestion does not bulk clear vectors from Pinecone when running incrementally","createdAt":"2023-09-28T18:35:16Z"}
{"state":"Merged","mergedAt":"2023-09-28T18:50:09Z","number":8323,"mergeCommitSha":"3a00b2b09fc6f7dceb0a39a97b70a7532726f96f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8323","title":"Remove auto scaling for now","createdAt":"2023-09-28T18:50:02Z"}
{"state":"Merged","mergedAt":"2023-09-28T18:59:21Z","number":8324,"mergeCommitSha":"2495cc4e37a1d674ae01942ff5f771dc633bbaa0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8324","title":"Add comments","createdAt":"2023-09-28T18:51:04Z"}
{"state":"Merged","mergedAt":"2023-09-28T19:23:45Z","number":8325,"mergeCommitSha":"49f89b9ee20ad3a4a62cd43e05020b56a572705c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8325","title":"Updat eopenai client","createdAt":"2023-09-28T19:11:59Z"}
{"state":"Merged","mergedAt":"2023-09-28T23:38:22Z","number":8326,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1695926834122459\r\n\r\nPartial fix. The regexp is still brittle because a user could put arbitrary characters in the label part, including '|', which would mess with this parser.\r\n\r\nOther commonly used suffixes in Atlassian products include:\r\n - ﻿`|height=300`: Adjusts the height of an embedded content, such as an image or video.\r\n - `|thumbnail`: Generates a thumbnail preview of an attached image or file.\r\n - `|panel`: Wraps content in a styled panel or box.\r\n - `|color=red`: Sets the color of the text or background in a specified color.\r\n - `|bordered`: Adds a border around the content.\r\n - `|scrollable`: Makes the content scrollable within a defined area.","mergeCommitSha":"beaa4f9aabd6f5e46ea77b5cb831e6c52911819d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8326","title":"Fix Atlassian smart links","createdAt":"2023-09-28T19:12:40Z"}
{"state":"Merged","mergedAt":"2023-09-28T23:06:28Z","number":8327,"mergeCommitSha":"38050372d79d2159140e64ac63de1b2640840192","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8327","title":"adding google analytics to head","createdAt":"2023-09-28T20:48:30Z"}
{"state":"Merged","mergedAt":"2023-09-28T22:52:04Z","number":8328,"body":"Allow horizontal scrolling in the landing page quote section.\r\n\r\nThis ended up being super complicated, because we want to snap to a single selected quote, but allow natural scrolling.  I solved this by setting up an IntersectionObserver to identify the central item, and as a different quote intersects with the centre of the screen, a new quote is \"selected\".","mergeCommitSha":"56111b3785601ad1dfc8cfddd168a4a51ad522ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8328","title":"Landing page quote section updates","createdAt":"2023-09-28T21:01:08Z"}
{"state":"Merged","mergedAt":"2023-09-29T19:45:36Z","number":8329,"mergeCommitSha":"f02a561f04cedb6d3b4d381b64deee3ef9b885bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8329","title":"Transform chat into retrieval query","createdAt":"2023-09-28T21:23:42Z"}
{"state":"Merged","mergedAt":"2022-04-08T20:47:40Z","number":833,"body":"This pr does two things:\r\n1. Adds an ednpoint for creating an asset (and returning the asset metadata with its upload url)\r\n2. Adds an endpoint for getting an asset (and return the asset metadata with its download url)\r\n\r\nTesting:\r\n1. Validated via unit tests.\r\n2. Validated via some integration tests I have yet to check in.\r\n","mergeCommitSha":"94db1f6b47b0b8eb5f51494b671cb451318a4206","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/833","title":"Add Assets Service Implementations","createdAt":"2022-04-08T01:52:32Z"}
{"state":"Merged","mergedAt":"2023-09-28T21:50:42Z","number":8330,"mergeCommitSha":"003e94e39201b6dc71334a18783fa66c794c66d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8330","title":"Lambda debugging","createdAt":"2023-09-28T21:50:08Z"}
{"state":"Merged","mergedAt":"2023-09-28T22:19:16Z","number":8331,"mergeCommitSha":"d1a7247fdc332aa51dfd16efbb7ed43747d3dbee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8331","title":"Source code service has access to S3 data pipeline bucket","createdAt":"2023-09-28T22:13:11Z"}
{"state":"Merged","mergedAt":"2023-09-28T23:34:26Z","number":8332,"body":"Need to do this in order to consistently persist vectors/partitions across the two pinecone/dynamo stores.\n\nLeads to more Pinecone requests.","mergeCommitSha":"51ba3937519ac764742ab384c017617763fceaee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8332","title":"Process code ingestion files one at a time","createdAt":"2023-09-28T23:04:51Z"}
{"state":"Merged","mergedAt":"2023-09-28T23:36:06Z","number":8333,"mergeCommitSha":"ebd4f010b8e914d3e3105381008bc5e8a8ea38ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8333","title":"Submodule bump - new email assets","createdAt":"2023-09-28T23:20:33Z"}
{"state":"Merged","mergedAt":"2023-09-29T00:20:10Z","number":8334,"body":"When running incrementally:\r\n\r\n- before partitioning\r\n   - check for existing cached item\r\n   - if cached item does not match content hash of file, then cleanup pinecone and dynamo\r\n\r\n- after partitioning\r\n   - cache partitions IDs in dynamo\r\n","mergeCommitSha":"caff0114279784dd96b8e048f613adbad602ac14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8334","title":"Check DynamoDB for partitions when processing files incrementally","createdAt":"2023-09-28T23:38:45Z"}
{"state":"Merged","mergedAt":"2023-09-29T00:05:33Z","number":8335,"mergeCommitSha":"10b35ef701e6c64616c0e0b15c672d6a4844ace0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8335","title":"Fix test","createdAt":"2023-09-29T00:05:05Z"}
{"state":"Merged","mergedAt":"2023-09-29T02:13:30Z","number":8336,"body":"- New SF Giants customer quote\r\n- Added paragraphs to break up spacing\r\n- Added title and alt tags for logos\r\n","mergeCommitSha":"6ac589886c862959bec764e6d0151158fd61ec0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8336","title":"Adding new customer quote","createdAt":"2023-09-29T00:09:58Z"}
{"state":"Merged","mergedAt":"2023-09-29T00:26:06Z","number":8337,"body":"Just removing the declaration, since we'll reuse the stack creation builders in the future.\n\nThe future plan is to create a generic catch lambda handler that sends an alarms to a slack channel.","mergeCommitSha":"e5ab955c6749c7e2cd87a09dd9480b930a936fd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8337","title":"Remove catch stage from code ingestion data pipeline","createdAt":"2023-09-29T00:20:04Z"}
{"state":"Merged","mergedAt":"2023-09-29T00:43:34Z","number":8338,"mergeCommitSha":"7dd4a3438df6415a9f7626be363793b76388c99a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8338","title":"Fix dynamo","createdAt":"2023-09-29T00:43:13Z"}
{"state":"Merged","mergedAt":"2023-09-29T02:10:21Z","number":8339,"body":"Chrome doesn't support `0` as it is supposed to...","mergeCommitSha":"a7b8d59ab4e8a079d0b8963d5f8c02fabe9e7be0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8339","title":"Fix Chrome bug in landing page","createdAt":"2023-09-29T01:59:40Z"}
{"state":"Merged","mergedAt":"2022-04-08T16:42:35Z","number":834,"body":"- Create auth-config.json to store pub keys for each environment. Lambda@edge does not support env-vars so this is recommended way for passing config.\r\n- Modified lambda function to load config, verify jwt signature, issuer and audience. It will then cross check requested teamID against claims\r\n- Added unit tests to help with local dev\r\n\r\nI know the auth function is not \"up to code\" with regards to its structure. The highest priority when writing this code was to ensure we don't accidentally allow access due to config failures. Hencethe nested structure code with authorization deeply nested in checks. We will iterate over this a few more times but for now it should do the job","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/834","title":"Add CloudFront@Edge auth for customer assets","createdAt":"2022-04-08T03:57:43Z"}
{"state":"Merged","mergedAt":"2023-09-29T02:51:28Z","number":8340,"mergeCommitSha":"f26a09815820bd83a4cde63fa8053561151c2626","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8340","title":"Grant data pipelines access to dynamoDB","createdAt":"2023-09-29T02:44:10Z"}
{"state":"Merged","mergedAt":"2023-09-29T03:15:13Z","number":8341,"mergeCommitSha":"9b5b1bbd2350acbd7bc170ed66183796212b7eab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8341","title":"Tighten up dynamo permissions a bit","createdAt":"2023-09-29T03:14:51Z"}
{"state":"Merged","mergedAt":"2023-09-29T03:32:21Z","number":8342,"mergeCommitSha":"b6b3ef4d243d0de145c2848cd107564c66a5a7df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8342","title":"Reduce code ingestion concurrency to 1, was 10, previously 24","createdAt":"2023-09-29T03:31:56Z"}
{"state":"Merged","mergedAt":"2023-09-29T04:02:16Z","number":8343,"mergeCommitSha":"338adbacc0b2a52627da5b8e04bd81df75f7d11f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8343","title":"Fix dynamo access from sagemaker","createdAt":"2023-09-29T04:01:15Z"}
{"state":"Merged","mergedAt":"2023-09-29T04:26:41Z","number":8344,"mergeCommitSha":"a394c5e2f37ed97f011e2f7f550ddc7fc999d372","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8344","title":"Code ingest off in PROD","createdAt":"2023-09-29T04:24:56Z"}
{"state":"Merged","mergedAt":"2023-09-29T06:48:51Z","number":8345,"mergeCommitSha":"d15a8253df711fb2adf9ca14a3e259f527311d51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8345","title":"Fixes for dynamo item access to match schema","createdAt":"2023-09-29T06:45:37Z"}
{"state":"Merged","mergedAt":"2023-09-29T07:10:31Z","number":8346,"mergeCommitSha":"b2342573a00a924482f3c8a4d4da24b9bbad4183","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8346","title":"Switch to a new 8-shard pinecone index","createdAt":"2023-09-29T07:10:12Z"}
{"state":"Merged","mergedAt":"2023-09-29T07:31:06Z","number":8347,"body":"fucking bullshit type-unsafe language","mergeCommitSha":"eca5deb1fd6679c039b3e9c7699dd34b8d711fd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8347","title":"Fix uuid type issue in dynamo upload","createdAt":"2023-09-29T07:28:06Z"}
{"state":"Merged","mergedAt":"2023-09-29T08:06:00Z","number":8348,"body":"This reverts commit a394c5e2f37ed97f011e2f7f550ddc7fc999d372.","mergeCommitSha":"4ef5691cff28a228e7495de7fd1ad6d550c24fab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8348","title":"Revert \"Code ingest off in PROD (#8344)\"","createdAt":"2023-09-29T08:05:28Z"}
{"state":"Merged","mergedAt":"2023-09-29T18:18:53Z","number":8349,"body":"Need to do the same in KT embedding service.","mergeCommitSha":"4e9d5c7c46df24b832f2e1d43e04ab0283d67497","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8349","title":"Code ingestion backoff when index overloaded","createdAt":"2023-09-29T17:54:48Z"}
{"state":"Merged","mergedAt":"2022-04-08T18:13:01Z","number":835,"body":"Needed to allow the SourceMark service to grab only modified sourcemarks and sourcepoints","mergeCommitSha":"e2e39dec7090f5e301fecb807bb2b54bff22437c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/835","title":"Add SourceMarks push channel","createdAt":"2022-04-08T06:50:05Z"}
{"state":"Merged","mergedAt":"2023-09-29T21:26:29Z","number":8350,"mergeCommitSha":"b9047f0ab0b923641b9e12a652402114eb8c11d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8350","title":"Add ability to re-embed team","createdAt":"2023-09-29T18:06:51Z"}
{"state":"Merged","mergedAt":"2023-09-29T19:29:08Z","number":8351,"body":"<img width=\"409\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/3f6e4a77-3e69-4286-8f7c-397f5749ea08\">\r\n\r\nthis button lacks a url right now, value needs to be passed through ","mergeCommitSha":"900055ebc71dbaad2988105003afd6ae75d96895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8351","title":"Add slack settings url to sendgrid contact attributes","createdAt":"2023-09-29T18:20:10Z"}
{"state":"Merged","mergedAt":"2023-09-29T18:25:09Z","number":8352,"body":"Same needs to be done in KT land.","mergeCommitSha":"9c2707b1f0c5a75172d51463321fc321479b750a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8352","title":"Do not attempt to upsert empty sparse vectors in code ingestion","createdAt":"2023-09-29T18:24:44Z"}
{"state":"Merged","mergedAt":"2023-09-29T20:09:22Z","number":8353,"body":"- Tweaked the standard responsive button styling\r\n- Tweaked the hero button styling, as the text in the hero is slightly larger than the rest of the page\r\n- Adjusted the responsive margins globally for h2 and h3s ","mergeCommitSha":"d0bb962447c98fd133f7ce4587b1da5585eb4996","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8353","title":"Adjust responsive buttons on landing page","createdAt":"2023-09-29T18:25:35Z"}
{"state":"Merged","mergedAt":"2023-09-29T20:01:57Z","number":8354,"mergeCommitSha":"ce6ecb79a98732a76febf75b75079ae007fc253e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8354","title":"Don't send empty sparse vectors to Pinecone, KT version","createdAt":"2023-09-29T18:30:34Z"}
{"state":"Merged","mergedAt":"2023-09-29T18:54:06Z","number":8355,"body":"```\n[2023-09-29 11:42:56] WARNING (node_utils) | include_metadata is set to True but language='typescript' chunk_lines=40 chunk_lines_overlap=15 max_chars=2300 callback_manager=<llama_index.callbacks.base.CallbackManager object at 0x34790e9d0> is not metadata-aware.Node content length may exceed expected chunk size.Try lowering the chunk size or using a metadata-aware text splitter if this is a problem.\n```","mergeCommitSha":"730d87614c16da0d1b18d010e710ae03c3900e3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8355","title":"Fix code partitioning metadata warning from llama_index","createdAt":"2023-09-29T18:43:49Z"}
{"state":"Merged","mergedAt":"2023-09-29T19:02:21Z","number":8356,"body":"This reverts commit b6b3ef4d243d0de145c2848cd107564c66a5a7df.\r\n\r\nKeep an eye on upsert latency/count:\r\nhttps://getunblocked.grafana.net/d/SzG_rhg4z/pinecone-dashboard?orgId=1&var-IndexName=prod-unblocked-instructor-dotproduct-n8&var-RequestType=upsert","mergeCommitSha":"5e52f67021315816d6384cd4b583f2637d85bfb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8356","title":"Revert \"Reduce code ingestion concurrency to 1, was 10, previously 24 (#8342)\"","createdAt":"2023-09-29T19:01:28Z"}
{"state":"Merged","mergedAt":"2023-09-29T19:47:10Z","number":8357,"mergeCommitSha":"1d7e3561be3fb33b9e756c6f8ee732d2f2cc59a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8357","title":"Add more pre-commit hooks","createdAt":"2023-09-29T19:20:03Z"}
{"state":"Merged","mergedAt":"2023-09-29T20:17:33Z","number":8358,"mergeCommitSha":"faa5fef8c6e8aded30f87229162246f393e410f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8358","title":"Turn off code ingestion","createdAt":"2023-09-29T20:15:13Z"}
{"state":"Merged","mergedAt":"2023-09-29T20:23:57Z","number":8359,"body":"The n8 index is dead.","mergeCommitSha":"c93393c66c3f3361a2fb60258dedb7052b44457e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8359","title":"Switch to n4 index","createdAt":"2023-09-29T20:23:28Z"}
{"state":"Merged","mergedAt":"2022-04-11T17:34:47Z","number":836,"mergeCommitSha":"f48660c9e7b15df20364b1e328ed58da10e878a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/836","title":"TextEditor SourceMarks get repos from RepoManager","createdAt":"2022-04-08T16:21:20Z"}
{"state":"Merged","mergedAt":"2023-09-29T20:30:19Z","number":8360,"mergeCommitSha":"7305ee8d0184d7b5e5903cda4a0c905739a8219f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8360","title":"Bump the auto-response relevancy threshold to 90%","createdAt":"2023-09-29T20:29:54Z"}
{"state":"Merged","mergedAt":"2023-09-30T02:33:25Z","number":8361,"mergeCommitSha":"6370757cb616526d7bdd00bf260aef1810cb7f44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8361","title":"Add Provider.Notion","createdAt":"2023-09-29T20:41:43Z"}
{"state":"Merged","mergedAt":"2023-09-29T21:51:32Z","number":8362,"mergeCommitSha":"f205d0982c1525cfdf3bb3640bfaf405a80ec4c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8362","title":"[SKIP TESTS] Try a change","createdAt":"2023-09-29T20:51:40Z"}
{"state":"Merged","mergedAt":"2023-09-29T21:10:27Z","number":8363,"mergeCommitSha":"9e23f14f6419058cb0f58686a3f547dcb37d619f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8363","title":"Adding csp allow for googlemanager","createdAt":"2023-09-29T21:09:12Z"}
{"state":"Merged","mergedAt":"2023-09-29T21:44:56Z","number":8364,"body":"Dynamically load google tag manager at runtime in the JS bundle, like we do for sentry, intercom, etc.\r\n\r\nThis lets us control the load point, the load configuration, and means we don't need to set up the script nonce.\r\n\r\nThis is only set up for the landing page in dev, so in case anything goes wrong it should not cause problems.","mergeCommitSha":"592b034ed05cd06ebb6ad58a0d317e390a13ff91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8364","title":"Dynamically load google tag manager","createdAt":"2023-09-29T21:21:09Z"}
{"state":"Merged","mergedAt":"2023-09-29T21:27:34Z","number":8365,"mergeCommitSha":"53a75662d3660a3fee5e18633ec0bb24e9c057d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8365","title":"Rip out Splade from embedding-utils","createdAt":"2023-09-29T21:23:31Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:03:32Z","number":8366,"mergeCommitSha":"036cc04d87414bc158fd7545e95f05117cdf4076","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8366","title":"Increment embedding utils version","createdAt":"2023-09-29T21:28:41Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:06:44Z","number":8367,"mergeCommitSha":"b9cef2b47fed8cabc4708f7bbab79b5d599a40d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8367","title":"Debugging dynamo bulk item cleanup by repo","createdAt":"2023-09-29T21:38:05Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:05:49Z","number":8368,"mergeCommitSha":"d02836eb1752064a1b2b30d96e6331b367fe49c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8368","title":"Add Notion OAuth client secrets","createdAt":"2023-09-29T21:42:18Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:04:20Z","number":8369,"mergeCommitSha":"ceed612b28feaade199f6e452be7b9244d0d457a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8369","title":"Remove splade option from source embeddings","createdAt":"2023-09-29T21:43:16Z"}
{"state":"Merged","mergedAt":"2022-04-09T00:44:47Z","number":837,"body":"- Changed the code to handle Bearer prefix in tokens\r\n- Cleanup comments and unused code\r\n- Updated test for positive case\r\n- Added test command to package.json","mergeCommitSha":"730c41120c0d48286f3788ad5e8797a364eef452","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/837","title":"Handler Bearer string in tokens","createdAt":"2022-04-08T17:28:56Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:05:05Z","number":8370,"mergeCommitSha":"72720c9742715c8706d416b72344b64a9cb4793d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8370","title":"Remove splade embeddings from instructor endpoint","createdAt":"2023-09-29T21:55:09Z"}
{"state":"Merged","mergedAt":"2023-09-29T21:59:27Z","number":8371,"mergeCommitSha":"179db0ce16dff7fe951d6215ea90c66c38c0925e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8371","title":"[SKIP TESTS] Try again","createdAt":"2023-09-29T21:59:19Z"}
{"state":"Merged","mergedAt":"2023-10-03T00:04:43Z","number":8372,"body":"Remove `/home` route. To be merged for launch.","mergeCommitSha":"ed6068637f4a3c68e35b09803ff724bbabfbb914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8372","title":"Make landing / live","createdAt":"2023-09-29T22:10:45Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:16:54Z","number":8373,"mergeCommitSha":"4d905f1e3006d11bec642a07c0197cda3165f1eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8373","title":"openai summaries","createdAt":"2023-09-29T22:16:49Z"}
{"state":"Merged","mergedAt":"2023-09-29T23:11:26Z","number":8374,"body":"Instructor endpoint needs to deploy before this is merged","mergeCommitSha":"2460d9910c0791eb4a4074ced1461fa54e0905d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8374","title":"Remove splade from KT land","createdAt":"2023-09-29T22:26:41Z"}
{"state":"Merged","mergedAt":"2023-09-29T22:39:14Z","number":8375,"mergeCommitSha":"31573cdf5acb5c21b97e9a0ac019cdd429a0afff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8375","title":"Remove uatoscale","createdAt":"2023-09-29T22:39:09Z"}
{"state":"Merged","mergedAt":"2023-09-29T23:53:04Z","number":8376,"body":"replaced the blog hero image with animations","mergeCommitSha":"db08d3510abdb83f1ecfb17b8d02e0e110004516","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8376","title":"add new blog hero video assets","createdAt":"2023-09-29T22:57:51Z"}
{"state":"Merged","mergedAt":"2023-09-29T23:57:40Z","number":8377,"mergeCommitSha":"f4e11de6008c62b696d9f660ad6eac5281544e7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8377","title":"[SKIP TESTS] Add aws traces","createdAt":"2023-09-29T23:10:54Z"}
{"state":"Merged","mergedAt":"2023-09-29T23:30:09Z","number":8378,"mergeCommitSha":"5453beb3dcba4aa70efca2b1ef21acce3889491d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8378","title":"Fix dynamo bulk delete items when clearing a repo","createdAt":"2023-09-29T23:11:30Z"}
{"state":"Merged","mergedAt":"2023-09-29T23:38:12Z","number":8379,"mergeCommitSha":"d21189079745d6a4208a4b174e99b77fb12023b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8379","title":"Google analytics on prod","createdAt":"2023-09-29T23:13:31Z"}
{"state":"Merged","mergedAt":"2022-04-11T18:31:25Z","number":838,"body":"Adds support for proper repo resolution in web extension.\r\n\r\nGiven the current URL and teams from TeamStore, tries to resolve repos.\r\nEach url within the browser will have its own RepoStore instance.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/162493778-2442ffdf-e4e1-4139-a7c8-7c273d2904d1.mp4\r\n\r\n\r\n","mergeCommitSha":"7ebda32f6ceec33905e0cac0c5980fd5297493e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/838","title":"Handle repo resolution in web extension","createdAt":"2022-04-08T17:40:00Z"}
{"state":"Merged","mergedAt":"2023-09-30T00:29:04Z","number":8380,"body":"The motivation for this is to track if the user has been added to the one-time onboarding email campaign. The trigger for this is either:\r\n- The first time they ask a real Q&A (not the canned ones from onboarding)\r\n- 3 days since signup\r\n\r\nWhatever comes first.","mergeCommitSha":"1d4e91d6baa30a1f7b2ec2c17f09fdf4737e483c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8380","title":"Add onboarding state for email campaign","createdAt":"2023-09-29T23:50:48Z"}
{"state":"Merged","mergedAt":"2023-09-30T01:24:57Z","number":8381,"body":"<img width=\"1240\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/f534a7a1-0a79-42ff-b223-1131ca63ad40\">\r\n\r\n\r\n* Add pricing (copy still needed)\r\n* Refactor landing header to simplify \r\n* Refactor a bit of the routing to cut down on repetition ","mergeCommitSha":"7a0bb4a6366601567881546de7c978bc26b68de2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8381","title":"Add pricing to landing ","createdAt":"2023-09-30T00:28:51Z"}
{"state":"Merged","mergedAt":"2023-09-30T00:53:41Z","number":8382,"body":"Content length as a proxy for relevancy / information entropy.\r\n\r\nWould like to do this before we bulk re-ingest.","mergeCommitSha":"e322ec32775f45c40166e0c8028569605e8dc2b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8382","title":"[SKIP TESTS] Increase the minimum input size of embedded text to improve answers","createdAt":"2023-09-30T00:29:32Z"}
{"state":"Merged","mergedAt":"2023-09-30T00:53:58Z","number":8383,"mergeCommitSha":"55f8633635266f49276c47f64de4247c787704cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8383","title":"Debug dynamo scan response","createdAt":"2023-09-30T00:37:41Z"}
{"state":"Merged","mergedAt":"2023-09-30T00:58:37Z","number":8384,"mergeCommitSha":"6ef068c20bd31159975f47a001624ed8d8fcdecd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8384","title":"[SKIP TESTS] Aws traces when endpoint not defined","createdAt":"2023-09-30T00:53:50Z"}
{"state":"Merged","mergedAt":"2023-09-30T23:42:25Z","number":8385,"mergeCommitSha":"c2013c54a92c64fc53d216c0c714db43bc2d8618","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8385","title":"Implement Notion OAuth","createdAt":"2023-09-30T02:39:47Z"}
{"state":"Merged","mergedAt":"2023-09-30T02:49:59Z","number":8386,"mergeCommitSha":"f2f0545a58d11c55a616cf086c3c6b3282512789","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8386","title":"Add text_utils","createdAt":"2023-09-30T02:45:55Z"}
{"state":"Merged","mergedAt":"2023-09-30T03:02:17Z","number":8387,"body":"Turns out pagination keys are compound keys.","mergeCommitSha":"caf0cd5af2ca02d6c778e595ebd18f0282f9abd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8387","title":"Fix dynamo scan pagination","createdAt":"2023-09-30T02:47:51Z"}
{"state":"Merged","mergedAt":"2023-09-30T03:39:01Z","number":8388,"body":"Come on google, what on earth.","mergeCommitSha":"282ed5083ef90f27e9cc42b73dfe8a522807a643","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8388","title":"Fix google analytics","createdAt":"2023-09-30T03:21:29Z"}
{"state":"Merged","mergedAt":"2023-09-30T03:52:23Z","number":8389,"mergeCommitSha":"8949272b7c996cfdade80a582b1e3e0a55512536","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8389","title":"Fix scan pagaination serialization","createdAt":"2023-09-30T03:50:54Z"}
{"state":"Merged","mergedAt":"2022-04-08T21:08:18Z","number":839,"body":"Updates the SourceMarkStore to be created per repo, and makes the SourceMarkStore listen and fetch updated/new sourcemarks","mergeCommitSha":"d19b62748bf7de8b18a649eab8b360a63c1e1206","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/839","title":"SourceMarkStore uses a stream","createdAt":"2022-04-08T20:21:50Z"}
{"state":"Merged","mergedAt":"2023-09-30T04:22:32Z","number":8390,"mergeCommitSha":"8ed81a02d7201c823ecb157d46ca4259df0793c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8390","title":"Fix css specificity","createdAt":"2023-09-30T04:12:27Z"}
{"state":"Merged","mergedAt":"2023-09-30T05:33:45Z","number":8392,"mergeCommitSha":"678f00f34ba29a0edbc0ead2debf8b23685321aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8392","title":"Update text utils","createdAt":"2023-09-30T04:26:31Z"}
{"state":"Merged","mergedAt":"2023-09-30T05:01:20Z","number":8393,"mergeCommitSha":"3d7924bc218cc8dac8518564314aed972e91ac9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8393","title":"Use dynamo query instead of scan","createdAt":"2023-09-30T04:57:21Z"}
{"state":"Merged","mergedAt":"2023-09-30T09:21:00Z","number":8394,"mergeCommitSha":"4b7ac4f6a71c3e6c67d08e92abb50c1e10c521b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8394","title":"Persist code ingestion completion results","createdAt":"2023-09-30T05:13:47Z"}
{"state":"Merged","mergedAt":"2023-09-30T06:02:25Z","number":8395,"body":"Query is only supported on partition key.","mergeCommitSha":"dea43718d025b6e694305f8fdcefc302115a5b90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8395","title":"[SKIP TESTS] Use scan not query","createdAt":"2023-09-30T06:01:20Z"}
{"state":"Merged","mergedAt":"2023-09-30T06:16:58Z","number":8396,"mergeCommitSha":"6f66a8427b6a547c60a5fef4712cec338b657abf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8396","title":"adding google-analytics.com to csp header","createdAt":"2023-09-30T06:16:33Z"}
{"state":"Merged","mergedAt":"2023-09-30T10:14:23Z","number":8397,"body":"Runs incrementally.","mergeCommitSha":"d549026eb3561b6ede0f65eddfe988d9c146003e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8397","title":"Do not clear all embedding for a repo in code ingestion","createdAt":"2023-09-30T10:13:07Z"}
{"state":"Merged","mergedAt":"2023-09-30T10:37:08Z","number":8398,"mergeCommitSha":"89323d67c441a7113b10a3778d74891f1f233f78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8398","title":"[SKIPS TESTS] New Pinecone index with 10 partitions","createdAt":"2023-09-30T10:36:41Z"}
{"state":"Merged","mergedAt":"2023-09-30T11:00:56Z","number":8399,"mergeCommitSha":"c4fcbc59772cae8c3aa853267f1beb6f586b69ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8399","title":"[SKIP TESTS] Start code ingestion in prod on new index with 4 nodes","createdAt":"2023-09-30T10:56:42Z"}
{"state":"Merged","mergedAt":"2022-01-20T17:25:02Z","number":84,"body":"The hocon model is by far the best because it's using lightbend, which allows for explicit loading of environment variables via config file.\r\n\r\nWe are moving away from implicitly using environment variables through hoplite mechanism.\r\n\r\nHopllite will handle reflection (which is nice and auto-populating fields)\r\nHoplite will invoke lightbend to load config files, which can reference environment variables explicilty.\r\n","mergeCommitSha":"38ed3fe885f87999d26c3a6c66c84b1cef2fcea0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/84","title":"Move to using Hocon for configuration","createdAt":"2022-01-19T22:53:10Z"}
{"state":"Merged","mergedAt":"2022-04-08T21:11:54Z","number":840,"body":"Want to be able to configure token expirys easily, especially when testing local environment.","mergeCommitSha":"1a9a0e407d48c34b0c0553fa340de4633669df18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/840","title":"Add ability to configure token expirys","createdAt":"2022-04-08T20:43:22Z"}
{"state":"Merged","mergedAt":"2023-09-30T17:22:23Z","number":8400,"mergeCommitSha":"3655180ca42149a41b8f613894e9846a1079de62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8400","title":"Code ingestion should skip Pods directories","createdAt":"2023-09-30T11:55:47Z"}
{"state":"Merged","mergedAt":"2023-09-30T15:38:57Z","number":8401,"mergeCommitSha":"f1d1527f4f87403f0b7b3c7f53663b4d53391545","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8401","title":"[SKIP TESTS] Double code ingest rate to 8 nodes","createdAt":"2023-09-30T15:38:25Z"}
{"state":"Merged","mergedAt":"2023-09-30T18:27:20Z","number":8402,"mergeCommitSha":"d727b042c717120e937e1c3d42e7f0d124fba0d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8402","title":"Fix broken lists and feedback","createdAt":"2023-09-30T18:11:49Z"}
{"state":"Merged","mergedAt":"2023-09-30T19:00:45Z","number":8403,"body":"Just the library changes needed to make this happen. The real work will come next","mergeCommitSha":"4a66b3813f6fe8f73eb8b3c1baab82f7ee095c98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8403","title":"Add document batching to embedding utils","createdAt":"2023-09-30T18:12:39Z"}
{"state":"Merged","mergedAt":"2023-09-30T18:39:57Z","number":8404,"body":"Code ingestion failed with.\n```sh\nfatal: your current branch 'main' does not have any commits yet\n```","mergeCommitSha":"bfabe7ebd43ec0a38954b2fc196576602befbf95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8404","title":"Handle empty git repos in code ingestion","createdAt":"2023-09-30T18:27:19Z"}
{"state":"Merged","mergedAt":"2023-09-30T18:44:12Z","number":8405,"body":"One more change for the cribl quote:\r\n\r\n\"very good\" to \"excellent\"","mergeCommitSha":"ef4d841577f5f3b2f13f96ea487b5969e3e85744","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8405","title":"change \"very good\" to \"excellent\"","createdAt":"2023-09-30T18:35:09Z"}
{"state":"Merged","mergedAt":"2023-09-30T19:09:21Z","number":8406,"mergeCommitSha":"5b9bd7424b6f0999a81debd6cbb55e6fb21d7ad9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8406","title":"Add source code processing flag for batching embeddings","createdAt":"2023-09-30T19:00:21Z"}
{"state":"Merged","mergedAt":"2023-09-30T19:28:08Z","number":8407,"body":"We’re currently processing 100s of tiny little repos from manifold (https://admin.prod.getunblocked.com/teams/3a7cff18-4e2c-460c-a512-af14284f244d/repos), which have very few files. The overhead of booting the SageMaker instance and cloning means that we are doing far less productive work; pinecone is not loaded at all.","mergeCommitSha":"2174c7db70bd2cfc9ba86eee7334bcb0291b6367","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8407","title":"[SKIP TESTS] Increase the code ingest concurrency","createdAt":"2023-09-30T19:23:49Z"}
{"state":"Merged","mergedAt":"2023-09-30T20:28:01Z","number":8408,"mergeCommitSha":"43e5fa47beb749108fdf9dea768e350a01623cc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8408","title":"Fix lambda for source code ingestion","createdAt":"2023-09-30T20:24:03Z"}
{"state":"Merged","mergedAt":"2023-09-30T20:55:37Z","number":8409,"body":"Removed Google analytics from script-src and added it to connect-src instead. ","mergeCommitSha":"cc30efd300604a20f6853e96cd264c1e8138bf66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8409","title":"adding google analytics to connect-src","createdAt":"2023-09-30T20:53:01Z"}
{"state":"Merged","mergedAt":"2022-04-08T21:20:26Z","number":841,"body":"from here:\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1649443792731959?thread_ts=**********.512949&cid=C02HEVCCJA3","mergeCommitSha":"faf2016e33d37fbd69f54db1b7981f0605bf0649","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/841","title":"fix alb health probes","createdAt":"2022-04-08T21:06:27Z"}
{"state":"Merged","mergedAt":"2023-09-30T21:42:09Z","number":8410,"body":"Only enabled in dev so we can run the experiment","mergeCommitSha":"7f26af295f7c2fdfb5254f998fa4898a1428b3a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8410","title":"Batch embeddings","createdAt":"2023-09-30T21:27:04Z"}
{"state":"Merged","mergedAt":"2023-09-30T21:49:10Z","number":8411,"body":"It looks like Chrome under the hood uses element `transform` properties to implement scroll-snapping -- so when we specify animations on the scale transform, the animation is double or triple-triggered, causing odd snapping and jarring effects.\r\n\r\nSo in this PR we instead use the `scale` property and animate on that.\r\n\r\nAlso, when you click/tap on an element, manually calculating the item's scroll position and using `scrollTo`, instead of `scrollIntoView`, is visually smoother in Safari.","mergeCommitSha":"382403b3f6651f2e8ecdd42c7f310b1db239a77d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8411","title":"Fix landing page quote scrolling","createdAt":"2023-09-30T21:39:30Z"}
{"state":"Merged","mergedAt":"2023-09-30T21:58:41Z","number":8412,"mergeCommitSha":"bd493395f762af009735c6d20ebad80627f5d519","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8412","title":"Enable Google tag manager in prod dashboard","createdAt":"2023-09-30T21:47:31Z"}
{"state":"Merged","mergedAt":"2023-09-30T21:52:29Z","number":8413,"body":"With this change and operator can immediately invoke the jobs, without clearing previous incremental state.","mergeCommitSha":"4b40b1266198af53f16ffc6064e1da19771224d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8413","title":"[SKIP TESTS] Safely rerun code ingestion and pr summarization","createdAt":"2023-09-30T21:49:03Z"}
{"state":"Merged","mergedAt":"2023-09-30T22:29:55Z","number":8414,"mergeCommitSha":"052e762b807508d62646dc91655035d4bcdc5948","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8414","title":"More clsuter fixes","createdAt":"2023-09-30T22:29:47Z"}
{"state":"Merged","mergedAt":"2023-09-30T22:51:45Z","number":8415,"mergeCommitSha":"19bed9d9eb86e64ce4078f1c8d31dc808eccab42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8415","title":"Add test for max length tokens","createdAt":"2023-09-30T22:29:48Z"}
{"state":"Merged","mergedAt":"2023-10-01T23:38:21Z","number":8416,"mergeCommitSha":"cc933ca5fd9130fce97b1dc713643e0eb887bd2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8416","title":"Omit sparse vectors with lengths > 1000","createdAt":"2023-09-30T22:51:06Z"}
{"state":"Merged","mergedAt":"2023-10-01T03:20:39Z","number":8417,"body":"This should prevent the IDE popup on startup on Sonoma.  We will store the current environment in a `/tmp` file and read that.\r\n\r\nNote that I didn't update the hub at all -- it will still get the value from user defaults.  Because the hub is sandboxed, reading directly from `/tmp` I think requires going through the helper app, and I didn't want to bother with that.","mergeCommitSha":"06a757e4cb50852acd750ec5348a3fd416a3413f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8417","title":"Use temporary file for IDE dev env","createdAt":"2023-09-30T23:09:21Z"}
{"state":"Merged","mergedAt":"2023-10-01T05:41:41Z","number":8418,"mergeCommitSha":"680660e2d6f578e0e278b3e8245cc72b443fef39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8418","title":"Add NotionSearchApi","createdAt":"2023-09-30T23:57:16Z"}
{"state":"Merged","mergedAt":"2023-10-01T02:02:26Z","number":8419,"mergeCommitSha":"3835438667fe6b8e3054e4cde517110643c8c395","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8419","title":"Add ability to query person models by onboardingState using bitwise ops","createdAt":"2023-10-01T01:09:55Z"}
{"state":"Merged","mergedAt":"2022-04-08T21:15:00Z","number":842,"body":"- Revert \"Test ZGC (#789)\"\r\n- Revert memory changes\r\n","mergeCommitSha":"7a706c3458d5312ab1a63c7a4ee390678ab3185b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/842","title":"RevertGcChange","createdAt":"2022-04-08T21:14:22Z"}
{"state":"Merged","mergedAt":"2023-10-01T02:16:55Z","number":8420,"mergeCommitSha":"9773f8f7c40a077faf220b7f5ab0d171d75ca031","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8420","title":"Remove llms","createdAt":"2023-10-01T02:14:35Z"}
{"state":"Merged","mergedAt":"2023-10-01T16:15:46Z","number":8421,"mergeCommitSha":"85382a50292d7fe433921f3d0efa38800128369e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8421","title":"Adding Expo customer quote including submodule bump","createdAt":"2023-10-01T05:35:06Z"}
{"state":"Merged","mergedAt":"2023-10-01T06:06:20Z","number":8422,"mergeCommitSha":"2d1059427175bcd48dcb277749a0e1de432cbc91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8422","title":"Fetch and render repo HEAD commit on RepoPage","createdAt":"2023-10-01T05:41:47Z"}
{"state":"Merged","mergedAt":"2023-10-01T06:54:44Z","number":8423,"body":"Far more efficient and cheaper, since this doesn't spin up a SageMaker job.","mergeCommitSha":"40bcf9286b289fb966e2acb65e08eb4c795aaacb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8423","title":"Skip repo code ingestion if the HEAD commit hasn't changed since the previous ingestion","createdAt":"2023-10-01T06:05:44Z"}
{"state":"Merged","mergedAt":"2023-10-01T20:12:00Z","number":8424,"mergeCommitSha":"365be6eef6e73749b0e1e080e627000cc7939641","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8424","title":"Skip more generated files","createdAt":"2023-10-01T20:04:08Z"}
{"state":"Merged","mergedAt":"2023-10-01T20:38:53Z","number":8425,"mergeCommitSha":"5d5a3621b5a5ca16c0921db2ad8522d0d8cae936","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8425","title":"Skip embedding and pinecone upsert when there are no partitions","createdAt":"2023-10-01T20:33:45Z"}
{"state":"Merged","mergedAt":"2023-10-02T01:10:25Z","number":8426,"body":"We are currently installing node into the sandboxed folder, and under Sonoma IntelliJ is unable to access that folder without triggering the warning popup.  With this PR we will install into the non-sandboxed folder.\r\n\r\nI will make an Installer build to test the end result out.","mergeCommitSha":"9367d5f480428030f502b17ba21cba42d1fa3ee9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8426","title":"Use non-sandboxed folder for IntelliJ node folder","createdAt":"2023-10-02T00:55:59Z"}
{"state":"Merged","mergedAt":"2023-10-02T01:31:04Z","number":8427,"mergeCommitSha":"6d3723bb8f702fc93a252cc3665689ccc8c8ed7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8427","title":"[SKIP TESTS] Set cursors to null for reingest","createdAt":"2023-10-02T01:30:40Z"}
{"state":"Merged","mergedAt":"2023-10-02T01:47:46Z","number":8428,"mergeCommitSha":"306d9be0eeba1cab9dc57ea44622af873f8b16cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8428","title":"Allow search service to auto up to 10 instances (80 queue consumers)","createdAt":"2023-10-02T01:38:20Z"}
{"state":"Merged","mergedAt":"2023-10-02T02:10:12Z","number":8429,"mergeCommitSha":"a558645a1e45a76d60a9c4f2f8000c4a4c90c4ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8429","title":"[SKIP TESTS] Scale up embedding service effectively 4x","createdAt":"2023-10-02T02:07:42Z"}
{"state":"Merged","mergedAt":"2022-04-09T01:33:47Z","number":843,"body":"Since the extension is getting polling for updates, it needs to get back deleted sourcemarks too so that it can clear them out of the local cache.","mergeCommitSha":"1c97444e32530b0376791526663a8d0a209f4ebd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/843","title":"Soft delete sourcemarks","createdAt":"2022-04-08T21:41:32Z"}
{"state":"Merged","mergedAt":"2023-10-02T02:53:08Z","number":8430,"mergeCommitSha":"4d333832a8a9c59675802a429e9c5ff0724e862a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8430","title":"Increase prefetch queue size","createdAt":"2023-10-02T02:52:51Z"}
{"state":"Merged","mergedAt":"2023-10-02T04:49:34Z","number":8431,"mergeCommitSha":"3571b71e84537d4738bf3bad541a9539011d8142","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8431","title":"Set max size limit for prompt input","createdAt":"2023-10-02T03:57:44Z"}
{"state":"Merged","mergedAt":"2023-10-02T04:41:33Z","number":8432,"body":"Stop gap until we have partitioning","mergeCommitSha":"9fe8d272f52782ca9388a4d0694994d4e1b35fef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8432","title":"Clip content from website scraper to 4k","createdAt":"2023-10-02T04:27:26Z"}
{"state":"Merged","mergedAt":"2023-10-02T04:48:37Z","number":8433,"mergeCommitSha":"ba45c651b938935135e1b7cd1f8d7783fcc7c4c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8433","title":"Clip StackOverflow content to 4k","createdAt":"2023-10-02T04:45:45Z"}
{"state":"Merged","mergedAt":"2023-10-02T09:34:13Z","number":8434,"mergeCommitSha":"1612a3a818a3a0afdb4346eef8f033eea47a81cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8434","title":"Enough wack a mole.","createdAt":"2023-10-02T05:14:13Z"}
{"state":"Merged","mergedAt":"2023-10-02T05:59:19Z","number":8435,"body":"Reverts NextChapterSoftware/unblocked#8323\r\n\r\nmotivation -- embedding service is starving, because search service can't generate vectors fast enough\r\nhttps://chapter2global.slack.com/archives/C02T1N1LK19/p1696226200687839","mergeCommitSha":"e69c4902e2eae313695ac971c291c078ded3f7fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8435","title":"Revert \"Remove auto scaling for now\"","createdAt":"2023-10-02T05:59:07Z"}
{"state":"Merged","mergedAt":"2023-10-02T06:53:14Z","number":8436,"mergeCommitSha":"3e8bdcf6d9c8fddf0ca9b68f1b84a7e5991b5d00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8436","title":"[SKIP TESTS] Lets make it rain","createdAt":"2023-10-02T06:52:05Z"}
{"state":"Merged","mergedAt":"2023-10-02T07:21:02Z","number":8437,"mergeCommitSha":"e00f4f74e8864645c213e9ffb150fdc4ce96d68a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8437","title":"Instructor endpoints","createdAt":"2023-10-02T07:20:09Z"}
{"state":"Merged","mergedAt":"2023-10-02T17:54:57Z","number":8438,"mergeCommitSha":"ef4684aee3bddfb6eef6a1b339197b44d37d6716","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8438","title":"Remove code symbol expansion","createdAt":"2023-10-02T16:06:16Z"}
{"state":"Merged","mergedAt":"2023-10-02T16:29:56Z","number":8439,"mergeCommitSha":"e25f35a8a2968888a7d9b256b9cd73a6de50429b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8439","title":"BitBucket.org/codeswell-software is internal","createdAt":"2023-10-02T16:14:04Z"}
{"state":"Merged","mergedAt":"2022-04-11T20:28:28Z","number":844,"body":"Add 'Get Help' button on the bottom-right of the web dashboard, and the bottom-right of the VSCode discussion thread webview.\r\n\r\nAdded a bunch of Intercom infrastructure to make this happen, hidden behind an `IntercomProvider` (which boots up Intercom) and a `useIntercom` hook, which allows calling into the intercom API.\r\n\r\nNext PR will set the logged-in user information.\r\n\r\n<img width=\"1545\" alt=\"Screen Shot 2022-04-08 at 3 00 57 PM\" src=\"https://user-images.githubusercontent.com/2133518/162537729-4b777f0a-dba5-4523-a4ec-47231600a425.png\">\r\n\r\n<img width=\"1283\" alt=\"Screen Shot 2022-04-08 at 2 59 30 PM\" src=\"https://user-images.githubusercontent.com/2133518/162537739-a3aaa404-c997-4a44-b944-23e35b62cd09.png\">\r\n\r\n","mergeCommitSha":"705f817c38eb3efb576dc08d919b98bca17336cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/844","title":"Basic Intercom integration","createdAt":"2022-04-08T22:02:41Z"}
{"state":"Merged","mergedAt":"2023-10-02T17:03:08Z","number":8440,"mergeCommitSha":"9b3b184fbb46698e0fbf476885b8ed09c3af6cc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8440","title":"Revert 9367d5f","createdAt":"2023-10-02T16:46:13Z"}
{"state":"Merged","mergedAt":"2023-10-11T22:45:01Z","number":8441,"mergeCommitSha":"4390215221d4a6dd9221652660c9e930521b80d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8441","title":"add new llama endpoint","createdAt":"2023-10-02T17:18:03Z"}
{"state":"Merged","mergedAt":"2023-10-02T18:29:59Z","number":8442,"mergeCommitSha":"8e21f539dc49a2af61b47a8ef4d0accf7362b023","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8442","title":"Add NotionBlocksApi","createdAt":"2023-10-02T17:24:17Z"}
{"state":"Merged","mergedAt":"2023-10-02T17:56:22Z","number":8443,"mergeCommitSha":"4e7d905512262aaa1d5167a2f63e7f5953b408c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8443","title":"Increment embedding utils version","createdAt":"2023-10-02T17:55:45Z"}
{"state":"Merged","mergedAt":"2023-10-02T18:20:32Z","number":8444,"mergeCommitSha":"f52e89da69dc329ebe85423d3e1d86bc60f15646","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8444","title":"Remove source code symbol expansion from code processor","createdAt":"2023-10-02T18:15:37Z"}
{"state":"Merged","mergedAt":"2023-10-02T18:38:52Z","number":8445,"mergeCommitSha":"aa2e1cfe71ec6b674f7da3259fa30d51e34190b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8445","title":"Remove code symbol expansion from instructor","createdAt":"2023-10-02T18:18:48Z"}
{"state":"Merged","mergedAt":"2023-10-02T18:36:25Z","number":8446,"mergeCommitSha":"95436faf636f3dd247c2dcb3f6263c680ff5ab1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8446","title":"Add metrics for sagemaker jobs","createdAt":"2023-10-02T18:36:21Z"}
{"state":"Merged","mergedAt":"2023-10-02T18:50:47Z","number":8447,"mergeCommitSha":"74d6b5de39efb68298bba535972cfa763da06702","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8447","title":"Fix logs","createdAt":"2023-10-02T18:50:03Z"}
{"state":"Merged","mergedAt":"2023-10-02T19:47:05Z","number":8448,"body":"<img width=\"1505\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/7d222e71-b68e-4ca4-b6cd-c36f43f6d941\">\r\n","mergeCommitSha":"e5d014c272a3083730a3bf6e0e714abfe20939b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8448","title":"Update pricing page","createdAt":"2023-10-02T18:51:08Z"}
{"state":"Merged","mergedAt":"2023-10-02T18:55:19Z","number":8449,"mergeCommitSha":"27b390b198eb96cb8d7ecb828f1e920f3cc74f4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8449","title":"Temporarily disable auto-scaling for deployment","createdAt":"2023-10-02T18:54:51Z"}
{"state":"Merged","mergedAt":"2022-04-09T01:24:50Z","number":845,"body":"changes:\r\n1. moved 3 unused files to `unused` directory\r\n2. calculator\r\n3. array utils","mergeCommitSha":"04485ae6329b537ff70bdcb5038fd23aaebb8397","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/845","title":"Flow control for sm calc","createdAt":"2022-04-08T22:12:07Z"}
{"state":"Merged","mergedAt":"2023-10-02T20:14:36Z","number":8450,"mergeCommitSha":"956c24aebc24bea56431e6d632ae134e884cd07c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8450","title":"Update sign in text","createdAt":"2023-10-02T19:54:05Z"}
{"state":"Merged","mergedAt":"2023-10-02T20:17:31Z","number":8451,"body":"This service has OOMing overnight like 30 mins past midnight and causing alarms to go off. \r\n","mergeCommitSha":"f391aeb2613bf60663ba502d435af25660c6a6b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8451","title":"Increased topic service memory to avoid OOMs","createdAt":"2023-10-02T20:04:34Z"}
{"state":"Merged","mergedAt":"2023-10-02T20:55:32Z","number":8452,"body":"After this migration is run, I will merge an (upcoming) PR that replaces the `useAccessAllowed` field with this new one.","mergeCommitSha":"d460694c90afdc79a7a10e11fe3a8e2dd400aa78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8452","title":"Backfill teamEnabledAt team setting","createdAt":"2023-10-02T20:27:51Z"}
{"state":"Merged","mergedAt":"2023-10-02T20:58:32Z","number":8453,"mergeCommitSha":"dd95e938579ef63e9a460a9088e87f087068c5b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8453","title":"quote changes from Dennis","createdAt":"2023-10-02T20:48:19Z"}
{"state":"Merged","mergedAt":"2023-10-03T00:05:52Z","number":8454,"body":"Please assume the `teamEnabledAt` backfill migration has been run","mergeCommitSha":"0a5a234f5e74b649df27d9b94b2da1a5f2a7322e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8454","title":"Replace userAccessAllowed with teamEnabledAt","createdAt":"2023-10-02T21:06:51Z"}
{"state":"Merged","mergedAt":"2023-10-02T21:21:26Z","number":8455,"mergeCommitSha":"45bf530a12615d2b38a80f8fcd093dab8b4dd839","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8455","title":"submodule bump","createdAt":"2023-10-02T21:11:59Z"}
{"state":"Merged","mergedAt":"2023-10-02T21:25:52Z","number":8456,"body":"Don't enable teams that aren't already enabled","mergeCommitSha":"5d3dd6340a7d75f1f8420aa1fdc1fb9a065c6743","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8456","title":"Small tweak to teamEnabledAt migrator","createdAt":"2023-10-02T21:18:14Z"}
{"state":"Merged","mergedAt":"2023-10-02T23:14:47Z","number":8457,"body":"Not used yet, but this stable generated UUID is the DynamoDB key that can be dereferenced to find all Pinecone partitions for this source code document, as well as the Git file content hash. Will be used for debugging.","mergeCommitSha":"ad37b3848d9c51a126c10cfb53af1e666492c3e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8457","title":"Code documents use a stable named UUID derived from repo and filepath","createdAt":"2023-10-02T21:29:19Z"}
{"state":"Merged","mergedAt":"2023-10-02T21:53:15Z","number":8458,"mergeCommitSha":"bab709ad06cebcb1b49bfe194bc1092ca9eab07f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8458","title":"Add NotionIngestionJob","createdAt":"2023-10-02T21:31:46Z"}
{"state":"Merged","mergedAt":"2023-10-02T21:47:00Z","number":8459,"mergeCommitSha":"7b36451bbff6a9a6d51e58d6e7ff9ff1efcc82c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8459","title":"Fix scroll issue","createdAt":"2023-10-02T21:37:07Z"}
{"state":"Merged","mergedAt":"2022-04-08T22:35:04Z","number":846,"mergeCommitSha":"af73f48da2675a984c67577dc7be0171c2d13191","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/846","title":"AssetService should not have redis access","createdAt":"2022-04-08T22:21:06Z"}
{"state":"Merged","mergedAt":"2023-10-02T22:35:40Z","number":8460,"mergeCommitSha":"979f930e515c2532e5bf9ac6f5e4e35adab60a49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8460","title":"Place items breaks container model in Safari","createdAt":"2023-10-02T22:08:38Z"}
{"state":"Merged","mergedAt":"2023-10-02T22:53:26Z","number":8461,"mergeCommitSha":"104dd31a71f63739be6edd55f16948c2944b674a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8461","title":"Rm grid","createdAt":"2023-10-02T22:40:27Z"}
{"state":"Merged","mergedAt":"2023-10-02T23:00:31Z","number":8462,"mergeCommitSha":"c31bbd9412a4a1b3670e173c13f3e25d358fba28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8462","title":"Fix Jira threads with no content","createdAt":"2023-10-02T22:46:07Z"}
{"state":"Merged","mergedAt":"2023-10-02T23:36:10Z","number":8463,"body":"This change will allow us to suspend Dashboard and Landing page deploys to prod if needed. ","mergeCommitSha":"c1df15cfc202c2c3f1369e7607b2ee6f1cea8c8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8463","title":"Add support for SUSPEND_PROD_DEPLOYS in web workflow","createdAt":"2023-10-02T23:25:29Z"}
{"state":"Open","mergedAt":null,"number":8464,"body":"https://chapter2global.slack.com/archives/C040JKLK5MG/p1696286337486399","mergeCommitSha":"fbacc680da6c81b7933e31e3146adb6bae4038ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8464","title":"Add repo externalId to findInstallationsAndRepos to fix IDE Bitbucket install another repo flow","createdAt":"2023-10-02T23:39:46Z"}
{"state":"Merged","mergedAt":"2023-10-02T23:54:04Z","number":8466,"mergeCommitSha":"f3d3bf036fc7c33e9ffb9ffca670158973bbb668","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8466","title":"Filter out Jira comments without authors","createdAt":"2023-10-02T23:42:26Z"}
{"state":"Closed","mergedAt":null,"number":8467,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1696290647708889?thread_ts=**********.584629&cid=C02HEVCCJA3","mergeCommitSha":"269a39d4a72f45d0e82efed2d6ba213bfc8be4d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8467","title":"Validate that the identity provider matches the team provider in connectScmInstallation","createdAt":"2023-10-02T23:54:18Z"}
{"state":"Merged","mergedAt":"2023-10-03T04:55:25Z","number":8468,"mergeCommitSha":"2a787d47b5f4ddb0dce004f90e974cc334f67bcf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8468","title":"Trigger the onboarding email campaign under certain conditions","createdAt":"2023-10-03T00:02:32Z"}
{"state":"Merged","mergedAt":"2023-10-03T00:23:41Z","number":8469,"body":"This reverts commit ed6068637f4a3c68e35b09803ff724bbabfbb914.","mergeCommitSha":"98ca9264df63bf021bceddce0b8ec0e84da34f21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8469","title":"Revert \"Clean up landing routes (#8372)\"","createdAt":"2023-10-03T00:08:06Z"}
{"state":"Merged","mergedAt":"2022-04-11T20:07:16Z","number":847,"body":"\r\n<img width=\"630\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/162547081-ff83ecbb-52ee-4d8e-80ce-4036002b9274.png\">\r\n\r\n\r\nModal styling: https://primer.style/css/components/box-overlay\r\nButton styling: https://primer.style/css/components/buttons\r\nColors: https://primer.style/primitives/colors","mergeCommitSha":"7335fa0ab981ed9fdd3af3c714acedcfc27ea747","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/847","title":"Style extension discussion thread dialog","createdAt":"2022-04-08T22:21:48Z"}
{"state":"Merged","mergedAt":"2023-10-03T00:14:48Z","number":8470,"body":"The problem was that we were using a dynamic name for the variantName for the endpoint.\r\nSagemaker allows model updates, but it does NOT allow variantName changes for an endpoint with auto-scaling enabled.\r\nMove to a static name for the variantName.\r\n\r\nTested image updates with this change and it works without issue.","mergeCommitSha":"802249e58abd34dc9ad0da8be5f926a1cb1b7596","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8470","title":"Fix auto-scaling deployments with sagemaker","createdAt":"2023-10-03T00:08:39Z"}
{"state":"Merged","mergedAt":"2023-10-03T06:15:35Z","number":8471,"body":"To be merge at 11:15pm Oct 2.","mergeCommitSha":"cab2a46d6a181ca4dfabfaeb5315fe50f9df7f82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8471","title":"Make landing page live","createdAt":"2023-10-03T00:24:59Z"}
{"state":"Closed","mergedAt":null,"number":8472,"mergeCommitSha":"43710a1f95dd34ad4a8ea9d6bdef4b5e11430ce8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8472","title":"Add MachineLearningInstructApi that will expose things like the codellama model","createdAt":"2023-10-03T00:25:45Z"}
{"state":"Merged","mergedAt":"2023-10-03T01:15:04Z","number":8473,"body":"1. We should not be using nullable for event fields (too error prone)\r\n2. Move WelcomeEmailEvent to base NotfiicationEvent type.\r\n3. Make sendGridEmailHandler non optional.\r\n","mergeCommitSha":"92f3f29c1569a0caf52d817d208e51c22668d579","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8473","title":"Fix teamId for notification events","createdAt":"2023-10-03T00:44:57Z"}
{"state":"Merged","mergedAt":"2023-10-03T02:14:53Z","number":8474,"mergeCommitSha":"aa4b2ce04e77a7b5e99c7e57e07ab6156376109b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8474","title":"Add missing open quote","createdAt":"2023-10-03T00:58:05Z"}
{"state":"Merged","mergedAt":"2023-10-03T03:41:11Z","number":8475,"body":"Supersedes https://github.com/NextChapterSoftware/unblocked/pull/8310","mergeCommitSha":"9d03c1b6d370f3105fd54583c51a10a75b298b9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8475","title":"Introduce createScmInstallation to get or create a team by externalInstallationId","createdAt":"2023-10-03T01:21:00Z"}
{"state":"Merged","mergedAt":"2023-10-03T02:32:28Z","number":8476,"body":"Also improve logging\r\n","mergeCommitSha":"5869eedae6982ae2d9536a11c784daca0779c7d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8476","title":"[SKIP TESTS] SendGrid is broken due to null contact attributes","createdAt":"2023-10-03T02:30:49Z"}
{"state":"Closed","mergedAt":null,"number":8477,"body":"If you merge this one, then close this one: https://github.com/NextChapterSoftware/unblocked/pull/8478","mergeCommitSha":"8c553661a25848cdf6e1fbca7376060ba3558a32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8477","title":"Don't show skip button on notification setup if team hasn't been enabled yet","createdAt":"2023-10-03T02:32:44Z"}
{"state":"Merged","mergedAt":"2023-10-03T04:21:43Z","number":8478,"body":"If you merge this one, then close this one: https://github.com/NextChapterSoftware/unblocked/pull/8477","mergeCommitSha":"4273df91f5870344968d5dba21e9ff59efcec454","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8478","title":"Allow users to skip notification step even if team is not enabled yet","createdAt":"2023-10-03T02:35:31Z"}
{"state":"Merged","mergedAt":"2023-10-03T02:57:16Z","number":8479,"mergeCommitSha":"0791d33df60c7dfeed0fb567a8ffc54448c9b63e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8479","title":"Enable sendgrid","createdAt":"2023-10-03T02:47:44Z"}
{"state":"Closed","mergedAt":null,"number":848,"body":"- upstream points (see SourceMarkCalculator)\n- some data typing changes","mergeCommitSha":"1d72d3ffeaafd7d1afda172e011fdb1164a923cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/848","title":"Upstream points from calculator","createdAt":"2022-04-08T22:55:51Z"}
{"state":"Merged","mergedAt":"2023-10-03T03:21:36Z","number":8480,"mergeCommitSha":"a480ffeb0a2b527d4b4742e1894b8364b6843f2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8480","title":"Remove old sendinblue teamplests","createdAt":"2023-10-03T03:18:56Z"}
{"state":"Merged","mergedAt":"2023-10-03T04:23:13Z","number":8481,"mergeCommitSha":"5a1cc8a5e720e03ceed9c6739cc18c548e9e30ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8481","title":"Fix ide thread deletion on message","createdAt":"2023-10-03T03:53:04Z"}
{"state":"Merged","mergedAt":"2023-10-03T05:56:17Z","number":8482,"mergeCommitSha":"f84c1feb0abfef85e1f49d79d999799b95e73c3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8482","title":"Check for personal orgs on installation","createdAt":"2023-10-03T03:56:04Z"}
{"state":"Merged","mergedAt":"2023-10-03T04:17:12Z","number":8483,"mergeCommitSha":"ed0f5a5ca14e3651ef48c49fb13a14acc3425f71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8483","title":"[SKIP TESTS] [BREAKS API ON MAIN] Fix query param in createScmInstallation","createdAt":"2023-10-03T04:15:11Z"}
{"state":"Merged","mergedAt":"2023-10-03T04:37:09Z","number":8484,"mergeCommitSha":"8f204cbebb201f39aeed8b8d3c332276690ad784","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8484","title":"[SKIP TESTS] reduce scale to not overload activemq connections","createdAt":"2023-10-03T04:36:59Z"}
{"state":"Merged","mergedAt":"2023-10-03T04:47:21Z","number":8485,"mergeCommitSha":"bb6413559f3cd283faf84f83680072a9ca5ee15c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8485","title":"Decrease activemq load","createdAt":"2023-10-03T04:46:37Z"}
{"state":"Merged","mergedAt":"2023-10-03T05:51:25Z","number":8486,"mergeCommitSha":"9ff6ca3a00aa6c916b3482f8f349b6fe6cba41cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8486","title":"Fix install url for personal org","createdAt":"2023-10-03T04:49:20Z"}
{"state":"Merged","mergedAt":"2023-10-03T05:51:41Z","number":8487,"mergeCommitSha":"da31609f759a26d00d982e41ce58efd51776448b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8487","title":"Increase calls tack","createdAt":"2023-10-03T05:48:34Z"}
{"state":"Merged","mergedAt":"2023-10-03T20:02:40Z","number":8488,"mergeCommitSha":"1891a59407877fa9b376f2189c9c3740250fd6d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8488","title":"Add NotionPageIngestionService","createdAt":"2023-10-03T05:59:35Z"}
{"state":"Merged","mergedAt":"2023-10-03T06:26:21Z","number":8489,"mergeCommitSha":"4b44450837b6c301c10ceb0e2229f1e0db2e37da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8489","title":"Fix admin web delete team","createdAt":"2023-10-03T06:25:40Z"}
{"state":"Merged","mergedAt":"2022-04-08T23:31:20Z","number":849,"body":"We were using the same group order as pusherservice which is not allowed.\r\nTEsted via manual helm deployment.\r\n\r\n<img width=\"1111\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/162546098-2557ee3a-355f-47b3-aa70-58530e77f0a2.png\">\r\n\r\nhttps://kubernetes-sigs.github.io/aws-load-balancer-controller/v2.1/guide/ingress/annotations/#group.order","mergeCommitSha":"fc5637c3352fb9cb2c7d402f6c4ded94662f28fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/849","title":"Multiple ingresses in same group CANNOT share group order","createdAt":"2022-04-08T23:26:32Z"}
{"state":"Merged","mergedAt":"2023-10-03T06:55:06Z","number":8490,"body":"<img width=\"1337\" alt=\"Screenshot 2023-10-02 at 11 45 27 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/0088ae3a-de5d-40e9-a51f-e235b34eecf5\">\r\n","mergeCommitSha":"b5c4c4e69edde381537ec7288915525006521709","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8490","title":"Fix personal org warning UI layout","createdAt":"2023-10-03T06:45:39Z"}
{"state":"Merged","mergedAt":"2023-10-03T06:55:53Z","number":8491,"mergeCommitSha":"8061b9d441bbefdb26819819f8c8450d6f7546c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8491","title":"Fix landing routes","createdAt":"2023-10-03T06:47:58Z"}
{"state":"Merged","mergedAt":"2023-10-03T07:39:10Z","number":8492,"body":"This service is still tormenting me at night with OOMs. This time the issue was CPU and Mem\r\n<img width=\"1422\" alt=\"Screenshot 2023-10-03 at 12 23 42 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/94733135/c7c09352-69ed-4a96-8375-b0c563bce6cc\">\r\n<img width=\"1406\" alt=\"Screenshot 2023-10-03 at 12 21 58 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/94733135/32a48369-055f-4a8f-b425-cefccbb9fae3\">\r\n","mergeCommitSha":"054f8db2495e43ebe8b19b81c1eafde25cfa9e0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8492","title":"Add more cpu to topicservice","createdAt":"2023-10-03T07:24:07Z"}
{"state":"Merged","mergedAt":"2023-10-03T08:06:42Z","number":8493,"mergeCommitSha":"c17e67450049414242a28abd371f91338e69401f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8493","title":"Delete dead queues","createdAt":"2023-10-03T07:25:59Z"}
{"state":"Merged","mergedAt":"2023-10-03T09:12:20Z","number":8494,"mergeCommitSha":"cd909f393c104267026f29c5de8961609b4448c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8494","title":"Reduce sizes more","createdAt":"2023-10-03T09:12:08Z"}
{"state":"Merged","mergedAt":"2023-10-03T16:55:28Z","number":8495,"body":"PR ingestion is running right now while onboarding, so throttle back a bit.","mergeCommitSha":"6299c8c22b41e85f173e394a5cec58841f4ec7ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8495","title":"Reduce code ingestion concurrency","createdAt":"2023-10-03T16:54:21Z"}
{"state":"Merged","mergedAt":"2023-10-03T17:31:08Z","number":8496,"mergeCommitSha":"97b87382f71bd6e84451e1f5efa9340b771e7e04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8496","title":"goodbye old pipelines","createdAt":"2023-10-03T17:31:02Z"}
{"state":"Merged","mergedAt":"2023-10-03T17:42:06Z","number":8497,"mergeCommitSha":"6df5202af24d2aee92e58fadeeb3fdf0e75ebe99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8497","title":"Reduce memroy limit","createdAt":"2023-10-03T17:39:52Z"}
{"state":"Merged","mergedAt":"2023-10-03T18:19:27Z","number":8498,"body":"- Increase the CPU limit on the pods that recently got throttled to allow for higher bursts \r\n- Increased the CPU request for Auth and API services (They directly impact user experience)\r\n- Increased max scale for Auth, API and Pusher\r\n- Added more API, Pusher and Auth instances \r\n\r\n```\r\nadminwebservice-8b485bd68-94lch        1/1     Running   0             36m\r\napiservice-688f4bc859-8tr8w            1/1     Running   1 (27m ago)   33m\r\napiservice-688f4bc859-jdd99            1/1     Running   1 (30m ago)   32m\r\napiservice-688f4bc859-pmq6k            1/1     Running   1 (28m ago)   36m\r\nassetservice-86d84f5c96-7v755          1/1     Running   0             36m\r\nassetservice-86d84f5c96-q464g          1/1     Running   0             35m\r\nauthservice-7bcc746b84-k87lz           1/1     Running   1 (30m ago)   35m\r\nauthservice-7bcc746b84-kl4pl           1/1     Running   2 (28m ago)   36m\r\ncampaignservice-9bb977c56-6g98l        1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-245sf           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-25lwk           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-4mtbz           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-5xxz2           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-6nmb4           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-7qgs4           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-8s8w6           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-9hkn2           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-dcptw           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-drm9v           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-dvwpb           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-f5jhv           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-fhzbq           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-g6d8z           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-jh82b           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-lkbr5           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-qtwvq           1/1     Running   0             35m\r\ndataservice-57ff7d8b5d-swclz           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-wtfdx           1/1     Running   0             36m\r\ndataservice-57ff7d8b5d-xbbb7           1/1     Running   0             35m\r\nembeddingservice-68b99d8454-rcpj4      1/1     Running   0             36m\r\nencryptionservice-796689bd7c-6hr94     1/1     Running   0             36m\r\nencryptionservice-796689bd7c-l6prh     1/1     Running   0             35m\r\nexpertservice-5777b5f898-htjtn         1/1     Running   0             36m\r\njiraservice-6d86b65d96-b7m6x           1/1     Running   0             36m\r\nlinearservice-7f7d8d7f64-wj66t         1/1     Running   0             36m\r\nnotificationservice-6949884c8c-5ghc7   1/1     Running   0             36m\r\npusherservice-5754994498-5s58c         1/1     Running   0             33m\r\npusherservice-5754994498-n2h8l         1/1     Running   0             36m\r\npusherservice-5754994498-qk52m         1/1     Running   0             35m\r\nscmservice-7cff49b66d-692r4            1/1     Running   2 (28m ago)   36m\r\nscmservice-7cff49b66d-6rvfm            1/1     Running   2 (28m ago)   36m\r\nscmservice-7cff49b66d-886v7            1/1     Running   2 (28m ago)   36m\r\nscmservice-7cff49b66d-9k2ml            1/1     Running   2 (28m ago)   36m\r\nscmservice-7cff49b66d-dqm4c            1/1     Running   2 (28m ago)   36m\r\nscmservice-7cff49b66d-rlkww            1/1     Running   2 (28m ago)   36m\r\nsearchservice-f6f4d6dd5-pfh7r          1/1     Running   0             29m\r\nsearchservice-f6f4d6dd5-rtnjr          1/1     Running   0             29m\r\nsecretservice-769b5db6df-6qnns         1/1     Running   0             36m\r\nsecretservice-769b5db6df-phrsk         1/1     Running   0             35m\r\nslackservice-5885d986cb-4cprn          1/1     Running   0             36m\r\nslackservice-5885d986cb-bhkf9          1/1     Running   0             36m\r\nslackservice-5885d986cb-n228z          1/1     Running   0             36m\r\nslackservice-5885d986cb-z5bml          1/1     Running   0             36m\r\nsourcecodeservice-5b977d74bd-hbt6w     1/1     Running   0             36m\r\ntelemetryservice-745b4545fc-8vghq      1/1     Running   0             36m\r\ntelemetryservice-745b4545fc-tw6d7      1/1     Running   0             35m\r\ntopicservice-75f6754fc5-29vnl          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-44lnz          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-49cc7          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-75dwc          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-7gh2g          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-cl4r9          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-cqzp9          1/1     Running   1 (30m ago)   35m\r\ntopicservice-75f6754fc5-drmwp          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-jfwmn          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-mlc9v          1/1     Running   0             35m\r\ntopicservice-75f6754fc5-mprpr          1/1     Running   2 (28m ago)   35m\r\ntopicservice-75f6754fc5-n45w9          1/1     Running   1 (30m ago)   36m\r\ntopicservice-75f6754fc5-nh4xb          1/1     Running   1 (30m ago)   35m\r\ntopicservice-75f6754fc5-xqb5j          1/1     Running   1 (28m ago)   35m\r\ntopicservice-75f6754fc5-zfl5l          1/1     Running   1 (30m ago)   35m\r\ntranscriptionservice-7ff4b45bb-q7f8h   1/1     Running   0             36m\r\nvideoservice-9c49555c9-htcbj           1/1     Running   0             36m\r\nvideoservice-9c49555c9-smmxn           1/1     Running   0             36m\r\nwebhookservice-6d9d646dbd-dbpvr        1/1     Running   0             34m\r\nwebhookservice-6d9d646dbd-nqnpf        1/1     Running   0             36m\r\nwebhookservice-6d9d646dbd-xqlmw        1/1     Running   0             35m\r\n```","mergeCommitSha":"19a28bb690d0279c8345faf0bee4a8fb4b72b842","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8498","title":"Increase CPU limit to allow for higher bursts","createdAt":"2023-10-03T17:52:28Z"}
{"state":"Merged","mergedAt":"2023-10-03T18:30:24Z","number":8499,"mergeCommitSha":"370a698b848bc080f4fc8eae66caf0c3c8ac8546","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8499","title":"Fix typo in blog post","createdAt":"2023-10-03T18:20:41Z"}
{"state":"Merged","mergedAt":"2022-01-20T17:51:43Z","number":85,"body":"Whenever an API in the spec requires `bearerAuth`, codegen APIs will now be provided a token from the `TokenProvider`\r\n\r\n`TokenProvider` currently stores JWT within session storage based on OWASP recommendation: https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/JSON_Web_Token_for_Java_Cheat_Sheet.md#token-storage-on-client-side\r\n\r\nAdditional work:\r\n- Refresh tokens\r\n \r\nNOTE: Zustand can be used without React: https://github.com/pmndrs/zustand#using-zustand-without-react\r\nCould potentially lead to stores for both web & vscode","mergeCommitSha":"0260b02b0849cfc5dcd5c697c941b83f2439f10e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/85","title":"Setup Auth on API requests","createdAt":"2022-01-19T23:27:54Z"}
{"state":"Merged","mergedAt":"2022-04-09T00:16:34Z","number":850,"mergeCommitSha":"4f348754f04d6d219814fcd1380a00544f5b04d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/850","title":"See if we can eliminate the kms bullshit","createdAt":"2022-04-09T00:14:06Z"}
{"state":"Merged","mergedAt":"2023-10-03T20:00:15Z","number":8500,"mergeCommitSha":"ff7857e2d7bdb024ee2b87a7d6ed8bebf3526353","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8500","title":"Try to optimize Bitbucket repo loading using API projection","createdAt":"2023-10-03T18:23:38Z"}
{"state":"Closed","mergedAt":null,"number":8501,"body":"We want to start embedding topic summaries so that the sample questions created during onboarding always have at least one document.\r\n\r\nThis PR adds new enums to allow embedding topic summaries.","mergeCommitSha":"d6b90c318ab6dad05a0c2fd1971021a37a2f95ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8501","title":"Add InsightType.TopicSummary and DocumentType.TopicSummary","createdAt":"2023-10-03T18:52:48Z"}
{"state":"Merged","mergedAt":"2023-10-03T20:00:38Z","number":8502,"mergeCommitSha":"d20166d4de74d37a0b3368a5d258fd1f5d6cdf9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8502","title":"Add my main BB account to internal users","createdAt":"2023-10-03T19:41:15Z"}
{"state":"Merged","mergedAt":"2023-10-03T20:22:14Z","number":8503,"mergeCommitSha":"62cc860749d02449af13afe3587a3ada8819fa48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8503","title":"REduce db sessions for services","createdAt":"2023-10-03T19:59:34Z"}
{"state":"Merged","mergedAt":"2023-10-03T20:34:22Z","number":8504,"mergeCommitSha":"80c0d598f70d113016d80dc5eef551e6ad849d06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8504","title":"Remove IntegrationInstallationModel.userOauthUrl for Notion","createdAt":"2023-10-03T20:22:44Z"}
{"state":"Merged","mergedAt":"2023-10-03T20:43:52Z","number":8505,"mergeCommitSha":"51ad9941e1a9bceaabedfa170d189392b152164d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8505","title":"Fix NotionOauthTokenProvider","createdAt":"2023-10-03T20:32:38Z"}
{"state":"Merged","mergedAt":"2023-10-03T22:55:22Z","number":8507,"mergeCommitSha":"ac3e5621e4cb22887389f7d6ddbc5d224d03d615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8507","title":"DLQ Scraper","createdAt":"2023-10-03T22:33:41Z"}
{"state":"Merged","mergedAt":"2023-10-03T23:10:27Z","number":8508,"mergeCommitSha":"b6ed858cc1bea215201d02baa80a3f25f78e59fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8508","title":"Remove dead code","createdAt":"2023-10-03T22:57:09Z"}
{"state":"Merged","mergedAt":"2023-10-03T23:33:58Z","number":8509,"mergeCommitSha":"f824a47487f68f16bf0dd609d33907faa0c25d44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8509","title":"Add getNotionConnectedTeamMembers operation","createdAt":"2023-10-03T22:57:41Z"}
{"state":"Merged","mergedAt":"2022-04-09T01:40:19Z","number":851,"mergeCommitSha":"25fb79acf9a601798fd8fe57e64442ea0977c565","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/851","title":"Try again","createdAt":"2022-04-09T01:40:11Z"}
{"state":"Merged","mergedAt":"2023-10-03T23:49:56Z","number":8510,"mergeCommitSha":"896407a4dc1afc7a1973285bb86ddf8da3e50aad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8510","title":"Fix broken next page pagination for bitbucket repo api","createdAt":"2023-10-03T23:13:47Z"}
{"state":"Merged","mergedAt":"2023-10-04T04:03:15Z","number":8511,"body":"When the viewport gets too small, the security icon container gets scrunched. See screenshot below. This PR fixes the issue.\r\n\r\n![CleanShot 2023-10-03 at 17 10 15@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/87d19788-d6d7-4163-9218-be610df22895)\r\n","mergeCommitSha":"a739c46548c3c7db50704f493ca46f38283fe0ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8511","title":"Fix bug with security icons on landing page","createdAt":"2023-10-04T00:11:06Z"}
{"state":"Merged","mergedAt":"2023-10-04T01:39:08Z","number":8512,"mergeCommitSha":"359a2c1fae491f84c8eb3339f6bc34d2ce256ee5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8512","title":"Increase web API timeout for ScmInstall APIs","createdAt":"2023-10-04T00:38:03Z"}
{"state":"Merged","mergedAt":"2023-10-04T01:02:10Z","number":8513,"mergeCommitSha":"78bf7cbffdd711425a10aa0cdf29091ad668a9c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8513","title":"[SKIP TESTS] Deploy scraper","createdAt":"2023-10-04T01:02:03Z"}
{"state":"Merged","mergedAt":"2023-10-04T01:35:53Z","number":8515,"mergeCommitSha":"18ed75783b2d6273a324644828bf3ce286a19909","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8515","title":"Move stuff around","createdAt":"2023-10-04T01:33:03Z"}
{"state":"Merged","mergedAt":"2023-10-04T01:56:34Z","number":8516,"mergeCommitSha":"8c7007bada25eb8ea8f213290acdd8d109487bbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8516","title":"Remove redis access","createdAt":"2023-10-04T01:55:07Z"}
{"state":"Merged","mergedAt":"2023-10-04T02:12:52Z","number":8517,"mergeCommitSha":"b1c5d67f90c87fd82e12288e2d5eee857e50ca19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8517","title":"Need to add timeout in two places","createdAt":"2023-10-04T02:12:14Z"}
{"state":"Closed","mergedAt":null,"number":8518,"body":"Moved the store to a stream with a 5-second throttle, prevents expensive double-calls of the `listInstallationRepos` method.  The problem is that the store fetches on construction, and the UI refreshes the store on every view, so we double-loaded.\r\n\r\nTBH I'm not even sure we need a cached store for this.  As far as I can tell we end up refreshing the data every time the UI loads anyways, so we could just make this call within the UI.  Something to investigate when Jeff is back.","mergeCommitSha":"1dcf1267d3711760006d5e5a9f027dffe72cce71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8518","title":"Reduce duplicate listInstallationRepos calls","createdAt":"2023-10-04T03:16:35Z"}
{"state":"Merged","mergedAt":"2023-10-04T04:22:52Z","number":8519,"body":"As per customer feedback.","mergeCommitSha":"a8eb2756cc57e05c77712284759e86a70d04192a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8519","title":"Remove extra space in email links","createdAt":"2023-10-04T04:06:49Z"}
{"state":"Merged","mergedAt":"2022-04-09T02:09:59Z","number":852,"body":"don’t ask me why it needs it, but it does.\r\nSue me…\r\n\r\nTesting:\r\n1. Validated against dev (upload and downloads)\r\n2. Updated both environments as well using eksctl","mergeCommitSha":"4e2180a5098e058cf92d0b2358783301eb6a0317","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/852","title":"Asset Service Role requires KMS access","createdAt":"2022-04-09T02:05:54Z"}
{"state":"Merged","mergedAt":"2023-10-04T05:13:16Z","number":8520,"body":"Reverts NextChapterSoftware/unblocked#8515\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1696395645471329","mergeCommitSha":"c0092c658a60d5aee38b1ca6944cc6c648cfb51c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8520","title":"Revert \"Move stuff around\"","createdAt":"2023-10-04T05:13:05Z"}
{"state":"Merged","mergedAt":"2023-10-04T23:15:09Z","number":8521,"mergeCommitSha":"4f23cb7343ef4538149357ed179005170b03d21e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8521","title":"Ignore pull requests opened by bots","createdAt":"2023-10-04T17:41:14Z"}
{"state":"Merged","mergedAt":"2023-10-04T18:11:57Z","number":8522,"mergeCommitSha":"a41cdfb0200f5b4ec1508e9dd84b184b4f9fe858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8522","title":"Fix DLQ retries","createdAt":"2023-10-04T18:04:44Z"}
{"state":"Closed","mergedAt":null,"number":8523,"mergeCommitSha":"772c2f0ddc1178a72bc2cfbf70aa66970ed35252","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8523","title":"getNotionConnectedTeamMembers returns primary member if set available","createdAt":"2023-10-04T18:13:39Z"}
{"state":"Merged","mergedAt":"2023-10-04T18:42:48Z","number":8524,"mergeCommitSha":"5a7d5121c5cc3dda2f8cb42ed1cb48b9a901cbe8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8524","title":"Reduce Pr ingestion concurrency","createdAt":"2023-10-04T18:42:06Z"}
{"state":"Merged","mergedAt":"2023-10-04T19:07:39Z","number":8525,"mergeCommitSha":"6778beab5b8f114b906f2c0751b7e5c6e6eb4d94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8525","title":"Only return notion in dev and local envs","createdAt":"2023-10-04T18:45:44Z"}
{"state":"Merged","mergedAt":"2023-10-04T18:50:29Z","number":8526,"mergeCommitSha":"a090db4a1525dbc0c9846f7ff69f16decc8c9107","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8526","title":"Revert revet","createdAt":"2023-10-04T18:47:13Z"}
{"state":"Merged","mergedAt":"2023-10-04T18:54:26Z","number":8527,"mergeCommitSha":"4df912602c8e30e06b938963e8600a3d89796e8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8527","title":"Try again","createdAt":"2023-10-04T18:54:13Z"}
{"state":"Merged","mergedAt":"2023-10-04T20:16:42Z","number":8528,"mergeCommitSha":"85b086cf2fbd9a43efb44ece058ab664316e1101","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8528","title":"Attempt db fix","createdAt":"2023-10-04T19:42:00Z"}
{"state":"Merged","mergedAt":"2023-10-04T23:19:38Z","number":8529,"body":"<img width=\"1503\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/9069c468-5aae-483c-b6b4-b073737f5c87\">\r\n<img width=\"1503\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/7b89004d-7126-45fa-8095-c02ea39672a7\">\r\n\r\nNotes: The Notion oauth API scopes pages access to every individual person, which creates possibly conflicting flows for different users, depending on their scope of access. To simplify, we've decided to only allow one person to auth and connect to Notion at a time.\r\n","mergeCommitSha":"6f9503ad2c8e0db14255d70cbff2e4ebb29e223a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8529","title":"Notion UI","createdAt":"2023-10-04T20:25:14Z"}
{"state":"Merged","mergedAt":"2022-04-09T05:15:33Z","number":853,"mergeCommitSha":"af66ffce912309b78af3175bb1bbc7a443297527","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/853","title":"update","createdAt":"2022-04-09T05:15:05Z"}
{"state":"Merged","mergedAt":"2023-10-04T23:50:02Z","number":8530,"mergeCommitSha":"f9189ee0bd07c420f0b4f8f7210da55edf571767","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8530","title":"Delete bot pull requests","createdAt":"2023-10-04T23:27:23Z"}
{"state":"Merged","mergedAt":"2023-10-05T04:19:17Z","number":8531,"body":"- Standard teams have limited access to features\r\n- Full teams have access to all features\r\n\r\nThese are not product terms.","mergeCommitSha":"2957aea614944811055cdb2dcfb9253a4303d2eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8531","title":"Introduce team mode","createdAt":"2023-10-04T23:38:07Z"}
{"state":"Merged","mergedAt":"2023-10-04T23:53:10Z","number":8532,"body":"Slow is better than full outage 5 times a day! \r\n\r\nThat's my new Moto\r\n","mergeCommitSha":"8c635dfe4e3ecb785c4c7ea4ca99ac5eb8e29695","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8532","title":"Fix scm scale to 2 for now until we figure this lock issue","createdAt":"2023-10-04T23:51:19Z"}
{"state":"Merged","mergedAt":"2023-10-05T00:35:02Z","number":8533,"mergeCommitSha":"f81b2ac0b97e238f431015d2b9bca378e19669ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8533","title":"limit search service to max scale of 5 pods","createdAt":"2023-10-05T00:34:51Z"}
{"state":"Merged","mergedAt":"2023-10-10T16:32:48Z","number":8534,"body":"This is gated by the `config.useTeamModes` environment config, which only take effect in local environments for now.","mergeCommitSha":"97acb6192e2e471ff270a595302752a2a153c733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8534","title":"Create teams from all account types personal/organization","createdAt":"2023-10-05T00:39:08Z"}
{"state":"Merged","mergedAt":"2023-10-05T05:22:55Z","number":8535,"body":"- by default new teams will 'Standard'\n- existing teams will be 'Full'","mergeCommitSha":"c7fc894726d2104fe9070b7433a2933ee06953a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8535","title":"Persist team mode and backill","createdAt":"2023-10-05T00:52:02Z"}
{"state":"Merged","mergedAt":"2023-10-05T02:08:23Z","number":8536,"mergeCommitSha":"adda2bdc24bdb29a9851f7c144e1cbb193e40d91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8536","title":"new azure endpoint","createdAt":"2023-10-05T01:45:34Z"}
{"state":"Merged","mergedAt":"2023-10-05T03:03:48Z","number":8537,"mergeCommitSha":"25cb5f6b0edf522ba6a34fa30c9b4e62c3371c6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8537","title":"Lower search service concurrency to 4","createdAt":"2023-10-05T03:00:47Z"}
{"state":"Merged","mergedAt":"2023-10-05T06:10:22Z","number":8538,"body":"Wait for backfill on all teams.","mergeCommitSha":"c06df8bef27e299e1aa2e3f3335683f6b3f4b672","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8538","title":"Remove nullable on TeamModel mode column and remove backfill migration","createdAt":"2023-10-05T05:01:21Z"}
{"state":"Merged","mergedAt":"2023-10-05T05:55:52Z","number":8539,"mergeCommitSha":"6ebfaaa6c95f5a2a8a6cb41c0479f5b3933bf93b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8539","title":"Try something","createdAt":"2023-10-05T05:03:54Z"}
{"state":"Merged","mergedAt":"2022-04-11T19:27:21Z","number":854,"body":"There's a particular magical incantation to get SwiftUI to layout buttons in the toolbar in the way that we need it to... and this is it. \r\n\r\nIncluded some state stubs as placeholders for real data and state bindings. Also - on macOS 12 the non-prominent border button style has a bug where you have to hover over the button to drop the it into the correct unfocused state, so I had to write a style wrapper \uD83E\uDD22","mergeCommitSha":"d145e3711019a1905e921ae56b0cdcfccbefde23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/854","title":"Land action buttons in the toolbar","createdAt":"2022-04-09T05:19:22Z"}
{"state":"Merged","mergedAt":"2023-10-07T16:24:38Z","number":8540,"mergeCommitSha":"67efe9e5f67ea45bbe60945f020dc1125c53a342","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8540","title":"Disable pull request ingestion for standard teams","createdAt":"2023-10-05T05:48:16Z"}
{"state":"Merged","mergedAt":"2023-10-05T06:58:23Z","number":8541,"mergeCommitSha":"5bf21dad760a3e2ef37650271e07625f3934e96a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8541","title":"[SKIP TESTS] Large messages are evil","createdAt":"2023-10-05T06:35:37Z"}
{"state":"Merged","mergedAt":"2023-10-05T07:43:59Z","number":8542,"mergeCommitSha":"d8ff4e970de4f50bc7de661e73aa25e224855f79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8542","title":"Logic to determine if code ingestion is done","createdAt":"2023-10-05T06:54:32Z"}
{"state":"Merged","mergedAt":"2023-10-05T18:40:07Z","number":8544,"body":"Needs tests but the basic idea here is to take as many blocks that fits under a specified limit when converting external datasources from markdown to our MessageBody format.","mergeCommitSha":"7b474f3807ef464886ecfa2b8a19db9d14d35ee8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8544","title":"Clip long messages","createdAt":"2023-10-05T16:01:15Z"}
{"state":"Merged","mergedAt":"2023-10-05T18:32:17Z","number":8545,"body":"Latency is too high","mergeCommitSha":"a4e25fc028b87ab048b4cd106fe451a779d69936","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8545","title":"[SKIP TESTS] Lower code ingest concurrency to 5","createdAt":"2023-10-05T18:31:43Z"}
{"state":"Merged","mergedAt":"2023-10-05T19:02:42Z","number":8546,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1696523449705469","mergeCommitSha":"cb30b6cd3e6c614ef3d45e5c9dbcb7cabda0596c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8546","title":"Ignore LinearTeam.icon when upsetting Linear Team","createdAt":"2023-10-05T18:40:24Z"}
{"state":"Merged","mergedAt":"2023-10-06T06:06:01Z","number":8547,"body":"- makes team enablement a one-way operation. since emails are sent, there is no way back.\n- adds confirmation dialog to manual team enablement, which should rarely be used anyway.","mergeCommitSha":"de3274a9278dd3bc5a43c84687a128936f79a335","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8547","title":"Automatically enable team when code ingestion is done","createdAt":"2023-10-05T19:20:24Z"}
{"state":"Merged","mergedAt":"2023-10-05T20:08:02Z","number":8548,"body":"Access the `teamModeService` like this: `Stores.teamModeService`","mergeCommitSha":"d418dceb6c4b6e8517d564b80173f83b7feb484d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8548","title":"Add team mode service to mac app","createdAt":"2023-10-05T19:35:01Z"}
{"state":"Merged","mergedAt":"2023-10-05T21:36:26Z","number":8549,"mergeCommitSha":"13a448bbfbb29d1ad024f4842f85dc545e08705e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8549","title":"Fix linear team ingestion","createdAt":"2023-10-05T20:54:07Z"}
{"state":"Merged","mergedAt":"2022-04-11T16:45:16Z","number":855,"body":"- Added necessary logic to lookup asset metadata and create a request to S3 for the asset using signed url provided by assets service\r\n- Added tests for asset lookup (using mocha)\r\n- Converted CloudFront Auth tests to use mocha\r\n- Minor touch ups and bug fixes here and there\r\n\r\nThis works locally (meaning tests pass). Next I'll work on ironing out the kinks when functions have been deployed","mergeCommitSha":"30c8f0d6a70b67c7a2571803126c441606d7063f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/855","title":"Added logic for CloudFront  asset lookup","createdAt":"2022-04-10T06:11:04Z"}
{"state":"Merged","mergedAt":"2023-10-06T23:06:15Z","number":8550,"body":"TODO need to apply this everywhere, not just for pull request ingestion","mergeCommitSha":"ec367a4c12877c24346c1dd5ec5fbaa93564fd6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8550","title":"Cap number of messages per thread when ingesting pull request threads","createdAt":"2023-10-05T21:04:05Z"}
{"state":"Merged","mergedAt":"2023-10-05T21:27:27Z","number":8551,"mergeCommitSha":"3aab6aa86a9dfd7d4504aac85387b62ab13de3be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8551","title":"Fix redirect for connected accounts","createdAt":"2023-10-05T21:17:23Z"}
{"state":"Merged","mergedAt":"2023-10-09T21:34:11Z","number":8552,"body":"* Hide 'Invite Team Members' when all team members `.hasAccount` (ie have made an account)\r\n* Remove 'Install IDE Plugins' context menu option in light mode\r\n* Remove 'Open in IDE' and 'Mark as read/unread' thread context menu options in light mode\r\n* Remove Thread group context menu completely in light mode (it has options for mark as read/unread)\r\n* Remove Settings -> View in IDE in light mode","mergeCommitSha":"a7540a8d4dda782cb40728cc7e60397c304a4c6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8552","title":"Hide various UI elements in hub light mode","createdAt":"2023-10-05T21:40:27Z"}
{"state":"Merged","mergedAt":"2023-10-05T22:24:33Z","number":8553,"mergeCommitSha":"7656b996b21a433958bb603476323bd3794372a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8553","title":"Make sure stores are initialized on startup","createdAt":"2023-10-05T21:44:58Z"}
{"state":"Merged","mergedAt":"2023-10-05T23:45:05Z","number":8554,"mergeCommitSha":"ffec17ea5ba1b6ae94e5e4168cfece379480cbfa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8554","title":"Consume team mode in dashboard","createdAt":"2023-10-05T22:12:08Z"}
{"state":"Merged","mergedAt":"2023-10-05T22:38:33Z","number":8555,"mergeCommitSha":"2924930293b801e7b108a211d9ee4c5d8d5065c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8555","title":"Boot stores to make extra sure they're up","createdAt":"2023-10-05T22:28:22Z"}
{"state":"Merged","mergedAt":"2023-10-06T18:25:31Z","number":8556,"mergeCommitSha":"1715724e9235fba65b0864362de1c92871eb3ef6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8556","title":"Dashboard: only show invite button as needed","createdAt":"2023-10-05T23:05:40Z"}
{"state":"Merged","mergedAt":"2023-10-05T23:38:16Z","number":8557,"mergeCommitSha":"9cdf375f428d26a4df09c0b5c7d77c24f1d83a06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8557","title":"Team APIs use real mode from the DB","createdAt":"2023-10-05T23:24:42Z"}
{"state":"Merged","mergedAt":"2023-10-05T23:57:09Z","number":8558,"body":"<img width=\"1499\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/0f94f29d-a0df-47df-9af0-f4cd28486c9b\">\r\n","mergeCommitSha":"03a94dc5ab1f6311f82ae4c5ee699f66214b439b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8558","title":"Update processing text for clarity","createdAt":"2023-10-05T23:35:29Z"}
{"state":"Merged","mergedAt":"2023-10-06T20:09:09Z","number":8559,"body":"To be used to determine the number of pull requests in a repo","mergeCommitSha":"d309122ec247f1acfaca49f66263a247d8353af9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8559","title":"Add ScmRepoApi.latestPullRequest","createdAt":"2023-10-05T23:59:15Z"}
{"state":"Merged","mergedAt":"2022-04-11T19:43:48Z","number":856,"body":"Ugly hacks galore. This is just laying down some basics to figure out what we can do with SwiftUI","mergeCommitSha":"af1e43587bfe113d0691eb76a4911b8dbd9eba74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/856","title":"Add local camera view","createdAt":"2022-04-11T05:10:48Z"}