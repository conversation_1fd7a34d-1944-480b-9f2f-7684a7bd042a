{"state":"Merged","mergedAt":"2022-08-21T07:50:35Z","number":2697,"body":"Lets try this again","mergeCommitSha":"5cc69e0afa663b710833d5c94eda273c5843530d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2697","title":"PullRequestInfo should include reviews without threads","createdAt":"2022-08-19T23:54:59Z"}
{"state":"Merged","mergedAt":"2022-08-22T19:15:00Z","number":2698,"body":"In this video I mimic the update path:\r\n- Start with an older build (`********`)\r\n- Install a newer build (`********`) on the command line, the same way the hub app does it\r\n- Reboot the hub app (as happens after an upgrade)\r\n- VSCode notes the new build, shows a popup offering to update\r\n- Click on `Reload` to reload the workspace\r\n\r\nhttps://user-images.githubusercontent.com/2133518/185721410-f3662b99-a0f9-4336-854f-76aba647faed.mp4\r\n\r\nHow this works:\r\n- Every CI build will now add the product number into the `package.json` that we ship with the extension\r\n- Whenever VSCode notices that the hub app has rebooted (ie when we reconnect to the hub app), we will check to see if an update is required:\r\n- Look through all installed (and non-obsoleted) extensions.  Find ours.  Parse they product number out of package.json.\r\n- If the product number is different then the running app, that means an update is pending.\r\n\r\nThis basically mimics how VSCode works internally.  The downside is that if VSCode changes how it manages extensions, we might break, but the code defaults to not warning, so in the worst case we will just miss popups.","mergeCommitSha":"2ef1e2c1df5f00d70b289f2d8c4105c89edf01c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2698","title":"Show warning in VSCode when update available","createdAt":"2022-08-20T00:13:57Z"}
{"state":"Merged","mergedAt":"2022-08-22T21:21:22Z","number":2699,"body":"https://app.logz.io/#/goto/ebf70201ea3590085e67ca1a2f566232?switchToAccountId=411850","mergeCommitSha":"ba597564347b16e3c18dbd112a044ffc1982d7c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2699","title":"[BREAKS API ON MAIN] Support DISMISSED PR State","createdAt":"2022-08-21T18:06:01Z"}
{"state":"Closed","mergedAt":null,"number":27,"body":"![image](https://user-images.githubusercontent.com/********/*********-4b18b3de-318c-469d-ac83-fedd7764bd46.png)\r\n![image](https://user-images.githubusercontent.com/********/*********-58a47321-0eff-44af-8ac0-57d4862759ef.png)\r\n![image](https://user-images.githubusercontent.com/********/*********-c4a67eae-eef1-454d-ae85-fba459f6f4d0.png)\r\n","mergeCommitSha":"baf96f24c9c2d822a878b89cb9a59654e0a80fe4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/27","title":"User Icon and icon stack with theme-ui","createdAt":"2022-01-10T20:34:59Z"}
{"state":"Merged","mergedAt":"2022-02-08T00:26:41Z","number":270,"body":"Add date mocking to resolve CI for stories that include relative dates.","mergeCommitSha":"086ee09382672e4d0db7cc7cac482872e1028e6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/270","title":"Setup mock date for vscode","createdAt":"2022-02-08T00:11:36Z"}
{"state":"Merged","mergedAt":"2022-08-21T18:40:15Z","number":2700,"body":"One liner. The issue was that auth state restoration wasn't happening when the token was expired, which is pretty much guaranteed every time the app starts after being asleep for a while. \r\n\r\nThere's no need to do this expiry check here because we're already looking for refresh token expiry. Token refresh will happen as soon as the auth store is initialized.","mergeCommitSha":"1c226740c4eb7aca149ae36ba0548e14583a8d39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2700","title":"Removes over-eager wait-for-auth state","createdAt":"2022-08-21T18:06:31Z"}
{"state":"Merged","mergedAt":"2022-08-22T16:00:52Z","number":2701,"body":"Not ideal, but can be merged temporarily while we figure out the correct solution so local envs don't get wrecked. ","mergeCommitSha":"3aebd33134424e400acaf395e2bbf6e078102abf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2701","title":"Fallback plan","createdAt":"2022-08-22T15:18:14Z"}
{"state":"Merged","mergedAt":"2022-08-22T18:53:38Z","number":2702,"body":"Generalize plugins across services and ensure PusherService has status pages plugin.","mergeCommitSha":"36f332f5fee3653b9f74fe1cab9e579ab5d173ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2702","title":"Add exception handler to pusher service","createdAt":"2022-08-22T18:17:54Z"}
{"state":"Merged","mergedAt":"2022-08-23T23:19:50Z","number":2703,"body":"On Extension installation, expand unblocked sidebars in the explorer sidebar.","mergeCommitSha":"34aedf9d6c4a216d0cd10eee69e2a41dbac7e638","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2703","title":"Open Unblocked explorer panels on extension installation","createdAt":"2022-08-22T18:25:37Z"}
{"state":"Merged","mergedAt":"2022-08-22T18:56:46Z","number":2704,"body":"Update to proper push channel","mergeCommitSha":"94dd53eea4462a4056581dd6fff5daa926d42264","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2704","title":"Add support for pusher channel","createdAt":"2022-08-22T18:46:02Z"}
{"state":"Merged","mergedAt":"2022-08-22T19:23:50Z","number":2705,"mergeCommitSha":"abfad89660344bff6f3703621f25af884e354321","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2705","title":"Cleanup gradle files","createdAt":"2022-08-22T19:00:18Z"}
{"state":"Merged","mergedAt":"2022-08-22T20:33:13Z","number":2706,"body":"<img width=\"172\" alt=\"CleanShot 2022-08-22 at 12 03 41@2x\" src=\"https://user-images.githubusercontent.com/1553313/185999055-8236803e-255e-4f2c-a0ec-3d8e63c425d3.png\">\r\n<img width=\"141\" alt=\"CleanShot 2022-08-22 at 12 03 44@2x\" src=\"https://user-images.githubusercontent.com/1553313/185999057-0238cba9-76bb-42e7-b1d6-9237b3d980c2.png\">\r\n\r\nAdd padding to of icon for alignment\r\n","mergeCommitSha":"dffe5c66bc9da847e4d7c68ba7ad3c9d082c53bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2706","title":"Update spacing in discussion count","createdAt":"2022-08-22T19:05:48Z"}
{"state":"Merged","mergedAt":"2022-08-22T20:30:08Z","number":2707,"body":"This changes the business logic for notification display.\r\n\r\n### Old Logic\r\n- Thread was previously 'read' OR thread is new OR last message createdAt timestamp has changed\r\n\r\n### New Logic\r\n- Last message in thread hasn't been seen before\r\n\r\nThe old logic allows for a scenario where a previously read thread that is marked as unread by the user will result in a notification. ","mergeCommitSha":"f51c3f0850b9bf0ddfa5a9a8620f28c1566cd269","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2707","title":"Only show notifications for messages not previously seen","createdAt":"2022-08-22T19:09:22Z"}
{"state":"Merged","mergedAt":"2022-08-22T19:44:08Z","number":2708,"mergeCommitSha":"016c14c2c4f27c7f4a049cb25f79c3d71a6e6c0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2708","title":"Setting minimum TTL for CloudFront assets endpoint","createdAt":"2022-08-22T19:40:43Z"}
{"state":"Merged","mergedAt":"2022-08-23T17:49:58Z","number":2709,"body":"Should fix the pusher issue Dennis was seeing","mergeCommitSha":"35c5f83f9b269d21db38c11d2ad55149ca405918","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2709","title":"Drop all pusher subscriptions on logout","createdAt":"2022-08-22T20:03:29Z"}
{"state":"Merged","mergedAt":"2022-02-08T00:56:19Z","number":271,"body":"This is a follow up to https://github.com/NextChapterSoftware/unblocked/pull/267. This query grabs the same data as the other query, except in batches of 100 PRs instead of just 1. \r\n\r\nThis batched query is more cost effective from a rate-limit perspective. However, if a PR has >20 review threads or has a review thread with >100 comments (both unlikely but possible), then we'll want to fall back to the single PR query to get the remaining review threads and/or comments.\r\n\r\nSee https://docs.github.com/en/graphql/overview/resource-limitations for an explanation of how much this query costs. In a nutshell, this query costs the equivalent of 21 api calls (compared to 100 if we were to run the single PR query for each of the 100 PRs).","mergeCommitSha":"6a9700516400de471fad76473c5887ee61311c86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/271","title":"Add batched PR review threads GraphQL query","createdAt":"2022-02-08T00:34:15Z"}
{"state":"Closed","mergedAt":null,"number":2710,"body":"Fixing an issue where null teams cause a poor intercom experience","mergeCommitSha":"16e34a81c8db3a2e839a09ec58ac01f9d63b71c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2710","title":"Intercom update","createdAt":"2022-08-22T20:15:46Z"}
{"state":"Merged","mergedAt":"2022-08-22T20:33:34Z","number":2711,"mergeCommitSha":"09a4f90ae4648e0e62fd7e42e190057e38a07eb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2711","title":"increase ec2 instance size limit","createdAt":"2022-08-22T20:17:45Z"}
{"state":"Merged","mergedAt":"2022-08-22T21:06:35Z","number":2712,"body":"https://chapter2global.slack.com/archives/C03T4CR3HBJ/p1661193188660189","mergeCommitSha":"7d9df1ebbb5e646bf4a186348cc2f58f940f66eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2712","title":"Create put viewPullRequest operation","createdAt":"2022-08-22T20:26:07Z"}
{"state":"Merged","mergedAt":"2022-08-22T20:54:00Z","number":2713,"body":"Reverts NextChapterSoftware/unblocked#2711","mergeCommitSha":"a631e30c7e3a58334671498458c787082c381b34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2713","title":"Revert \"increase ec2 instance size limit\"","createdAt":"2022-08-22T20:53:38Z"}
{"state":"Merged","mergedAt":"2022-08-23T01:00:50Z","number":2714,"body":"* Fix opening the proper view column for the Current File Explorer pane \r\n* Fix selected state of the thread row in the Current File Explorer pane","mergeCommitSha":"61e304f53313990db6c04d621e1bb4fc103c3418","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2714","title":"[TLC] Current file explorer fixes","createdAt":"2022-08-22T20:59:47Z"}
{"state":"Merged","mergedAt":"2022-08-22T21:40:54Z","number":2715,"mergeCommitSha":"de2b50e93adf4cdbbc28f9ccb4c6d882fee86d43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2715","title":"Fix banner padding issues","createdAt":"2022-08-22T21:02:27Z"}
{"state":"Merged","mergedAt":"2022-08-23T06:03:43Z","number":2716,"mergeCommitSha":"ed115e661b2fb252e7b7c9b0332ce3979fca076c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2716","title":"Record pull request view event","createdAt":"2022-08-22T21:09:45Z"}
{"state":"Merged","mergedAt":"2022-08-24T19:51:36Z","number":2717,"body":"Per revamped PR view design:\r\n<img width=\"596\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186019287-7c8e19fe-6ab1-4e24-8c7e-62a79c2c2cd5.png\">\r\n","mergeCommitSha":"0a5a9060ac5126da80f708f1763536508e76a56a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2717","title":"Add approvers to PrInfo model","createdAt":"2022-08-22T21:24:54Z"}
{"state":"Closed","mergedAt":null,"number":2718,"body":"`pullRequestId` is currently specified as a query parameter, but it's actually a path parameter for these operations:\r\n\r\n`/teams/{teamId}/pullRequests/{pullRequestId}/threads`\r\n`/teams/{teamId}/pullRequests/{pullRequestId}`\r\n`/teams/{teamId}/pullRequests/{pullRequestId}/info`\r\n\r\nThis change updates it to use the new `pullRequestId` path parameter:\r\n\r\n```\r\n    pullRequestId:\r\n      in: path\r\n      name: pullRequestId\r\n      required: true\r\n      schema:\r\n        $ref: '#/components/schemas/ApiResourceId'\r\n```\r\n\r\nBUT this breaks on main without the `[This breaks API compatibility on main]` commit message","mergeCommitSha":"b98a75e4f5d6a9a4552c9967469e31beee0c3aa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2718","title":"[BREAKS API ON MAIN but not really at least I think it doesnt] Use pullRequestId path parameter definition","createdAt":"2022-08-22T21:28:54Z"}
{"state":"Merged","mergedAt":"2022-08-22T23:13:40Z","number":2719,"body":"The PR view will show approvals now, so we need to make sure we're returning blocks with an approval status even if they have no message or threads.\r\n\r\nThese blocks exist but the API currently does not return blocks if it has no message or threads.","mergeCommitSha":"25a862cfcf0c5e8d9a9ed12995a8315179e87e83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2719","title":"Include PullRequestBlocks that are approvals even if they don't have a message or threads","createdAt":"2022-08-22T22:07:11Z"}
{"state":"Merged","mergedAt":"2022-02-08T00:48:20Z","number":272,"body":"This pr adds the ability for us to specify custom rules for ktlint.\r\n\r\nTo that end, I've added a rule forbidding the usage of Clock.System.now().\r\nWill add functionality to scope it to packages, but for now, this is fine.\r\n\r\nAlso cleaning up some plugin usage in our package, in particular plugin versioning.\r\n","mergeCommitSha":"bcd33348a9e381e4dacc9bed7ba5c125b55eca15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/272","title":"Add custom ktlint rules for next chapter","createdAt":"2022-02-08T00:41:16Z"}
{"state":"Merged","mergedAt":"2022-08-22T22:53:35Z","number":2720,"body":"We have been noticing cors failures complaining about \"No 'Access-Control-Allow-Origin\".\r\nThis is only when there are cache hits.\r\n\r\nI have a nuclear option fix for this if this doesn't work!","mergeCommitSha":"714fce1907f6f9d5c6c3738f4f369b256b4a146b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2720","title":"Ensure we cache keys based off headers and cors request headers","createdAt":"2022-08-22T22:44:50Z"}
{"state":"Merged","mergedAt":"2022-08-22T23:28:34Z","number":2721,"mergeCommitSha":"a09d94ee3aa80613c996823994cad72b2fe4b60e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2721","title":"lint ses stack","createdAt":"2022-08-22T23:23:45Z"}
{"state":"Merged","mergedAt":"2022-08-23T16:04:01Z","number":2722,"body":"Empty file states for sidebar panels\r\n<img width=\"1489\" alt=\"CleanShot 2022-08-22 at 16 39 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/186038250-72afc812-1094-4bb1-857b-2c186de283c2.png\">\r\n<img width=\"1501\" alt=\"CleanShot 2022-08-22 at 16 39 26@2x\" src=\"https://user-images.githubusercontent.com/1553313/186038252-e476da26-ff5d-492d-8cf7-1639c5d869c7.png\">\r\n\r\n","mergeCommitSha":"2ce85016ebb2f283df85ceed8c37d63574d076f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2722","title":"Add missing file states for active file manager","createdAt":"2022-08-22T23:40:21Z"}
{"state":"Merged","mergedAt":"2022-08-23T03:55:46Z","number":2723,"body":"As it turns out, the informational message popup disappears after a short while.  So, what we'll do is:\r\n\r\n* Whenever the workspace window gains focus, if there is a pending installation, show the popup.  Do this up to three times.\r\n* Whenever a new installation is detected, reset the popup count.  If the window has focus, show the popup\r\n* Tweak the wording a little bit \r\n\r\n<img width=\"1214\" alt=\"Screen Shot 2022-08-22 at 4 34 34 PM\" src=\"https://user-images.githubusercontent.com/2133518/186039537-5a8c2805-07cc-49e6-94cf-dce7512804d8.png\">\r\n\r\n","mergeCommitSha":"7b0034c59d891a55dc8de89bdf7418a883b4b337","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2723","title":"Show VSCode update popup up to 3 times on focus","createdAt":"2022-08-22T23:53:41Z"}
{"state":"Merged","mergedAt":"2022-08-23T17:29:41Z","number":2724,"body":"<img width=\"568\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186042365-5338589c-98ee-4d72-b44f-49545403e374.png\">\r\n\r\n![CleanShot 2022-08-22 at 17 26 33](https://user-images.githubusercontent.com/********/186042431-1da2d604-c0ab-4f37-a503-dbde674bd48e.gif)\r\n\r\nTODO: This is still missing the list of approvers - waiting on https://github.com/NextChapterSoftware/unblocked/pull/2717 (but shouldn't prevent this work from going in)\r\n\r\n","mergeCommitSha":"d566ceb5386edaff7becee5669bea8e078799426","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2724","title":"[TLC] Update PR header/description","createdAt":"2022-08-23T00:28:59Z"}
{"state":"Merged","mergedAt":"2022-08-23T19:58:23Z","number":2725,"body":"Adds logic to ingest just reviews for pull requests. Needed for the TLC work.\r\n\r\nWe could also just run the regular ingestion job for all PRs, but this is more efficient because we don't need to make requests to get comments or files (we've already ingested those).","mergeCommitSha":"6b28bf93a96591c3e73ba63ffccefed682e0af5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2725","title":"Add ability to trigger PR review ingestion for all repos in a team","createdAt":"2022-08-23T06:39:32Z"}
{"state":"Merged","mergedAt":"2022-08-23T22:47:21Z","number":2726,"body":"Affects both web extension when loading threads for many sourcemarks\r\n(eg: https://github.com/NextChapterSoftware/unblocked/blob/main/api/private.yml),\r\nand also the vscode extension when loading threads for many sourcemarks.\r\n\r\nWeb extension make 130 `getThread` requests. Latency for the final request is 1.1 seconds.\r\n<img width=\"800\" alt=\"Screen Shot 2022-08-23 at 09 18 42\" src=\"https://user-images.githubusercontent.com/1798345/186209919-6dcf21d5-3e78-4370-b83c-c5561fac28d6.png\">\r\n\r\nRelated:\r\nhttps://linear.app/unblocked/issue/UNB-431/source-mark-resolutionrendering-is-slow-to-load-for-file","mergeCommitSha":"675306dcafc1f37489ca4471a542b892ef502bb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2726","title":"Introduce fetchThreads to get threads in bulk","createdAt":"2022-08-23T16:09:05Z"}
{"state":"Merged","mergedAt":"2022-08-23T22:36:48Z","number":2727,"body":"Send PR event when opening PR view.","mergeCommitSha":"9cdf5b9efa473060900a192622ef2fe78a179ddc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2727","title":"Setup PR Metrics","createdAt":"2022-08-23T17:11:42Z"}
{"state":"Merged","mergedAt":"2022-08-23T19:29:27Z","number":2728,"body":"Will follow up with tests, and next steps.\r\n\r\nWant to get this in quick to stop the clients hammering the service.\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1661272228334169\r\n\r\nhttps://ui.honeycomb.io/unblocked/environments/production/result/x6Ugx7FakKc?hideMarkers&useStackedGraphs","mergeCommitSha":"bd715179bf5e5fc1b32f522d6a092a6d270d4feb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2728","title":"Quick fix for poller-api looping for getThread API","createdAt":"2022-08-23T18:55:53Z"}
{"state":"Merged","mergedAt":"2022-09-02T22:38:18Z","number":2729,"body":"Many of the classes were built as singletons, and the Hub app isn't using proper dependency injection. This refactor  arguably adds more tech debt because it allows for incomplete service singleton initialization. \r\n\r\nI'm taking this path now until we have a clearer view of the shared components.","mergeCommitSha":"32e44191cadaf55ddfa9290d76a83d06de8391cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2729","title":"Get video app building again","createdAt":"2022-08-23T19:00:06Z"}
{"state":"Merged","mergedAt":"2022-02-08T00:53:24Z","number":273,"mergeCommitSha":"c131814047494fe2722fba88a7d344149e2b0507","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/273","title":"Typo","createdAt":"2022-02-08T00:50:33Z"}
{"state":"Merged","mergedAt":"2022-08-23T19:46:14Z","number":2730,"body":"Image widths need to be capped.\r\n\r\n<img width=\"1388\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/186247162-61106f73-6007-4e66-a89f-be6232d8d27b.png\">\r\n","mergeCommitSha":"8a6e6ec085de5805782053a05864d5e69a08040b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2730","title":"Fix unauthed asset in emails","createdAt":"2022-08-23T19:18:42Z"}
{"state":"Merged","mergedAt":"2022-08-23T20:29:47Z","number":2731,"body":"This means when you iterate through items in the TLC view, we will scroll and hilight the code correctly.","mergeCommitSha":"ace018c419247c248f1534c24212a90da69ce470","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2731","title":"Reuse text editor in discussion view","createdAt":"2022-08-23T19:33:35Z"}
{"state":"Merged","mergedAt":"2022-08-23T20:30:33Z","number":2732,"mergeCommitSha":"e430fa1f7edfc7e8b6732429de6c507d428cdb01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2732","title":"Standardize monitoring for services","createdAt":"2022-08-23T19:45:52Z"}
{"state":"Merged","mergedAt":"2022-08-24T20:31:17Z","number":2733,"body":"We want to eventually return these in `PullRequestInfo.participantIds`","mergeCommitSha":"eb4a6d2449d623fb004c0d8921a3451bf69a0304","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2733","title":"Deserialize pull request assignees and reviewers","createdAt":"2022-08-23T20:22:27Z"}
{"state":"Merged","mergedAt":"2022-09-02T22:44:30Z","number":2734,"mergeCommitSha":"b34c8237ce877f6efce1ef0163b684aa99a844f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2734","title":"Update Agora frameworks to 4.0.0 beta 2","createdAt":"2022-08-23T20:40:51Z"}
{"state":"Merged","mergedAt":"2022-08-23T22:34:57Z","number":2735,"body":"* `ActiveFileManager` now publishes a simple `string | undefined` for the active file, as opposed to an object containing a bunch of other junk. I think the way I had it before was a mistake that made some code more complicated then it needed to be.\r\n* `ActiveFileManager.activeFile` is now updated with a longer (500ms) debounce.  This lets the system naturally handle rapid file changes, like can happen while you are switching between discussions.  Fixes bugs in the \"Insights\" views.\r\n","mergeCommitSha":"f7f85339fa11524a3b2e7a5fc0f009b76a5dac42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2735","title":"Fix ActiveFileManager bugs","createdAt":"2022-08-23T22:00:22Z"}
{"state":"Merged","mergedAt":"2022-08-23T22:50:02Z","number":2736,"body":"Going to do this in the bulk ingestion job instead","mergeCommitSha":"d71c031446c855f3df20150bf7cf2efab687b64f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2736","title":"Remove code","createdAt":"2022-08-23T22:20:20Z"}
{"state":"Merged","mergedAt":"2022-08-23T22:31:59Z","number":2737,"body":"This fixes a bug where the 'Insights' panel in the explorer tab would empty whenever you clicked on an insight. The problem is that we never stored the last state, so whenever the selected thread changed, we would re-render with an `uninitialized` state.","mergeCommitSha":"c6d57de21c78708bd05ed3426e12da306c197804","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2737","title":"Fix bug where 'Insights' panel would clear","createdAt":"2022-08-23T22:22:13Z"}
{"state":"Merged","mergedAt":"2022-08-23T22:45:17Z","number":2738,"body":"https://app.logz.io/#/goto/93e26d15f26b10483a3a9557b1e8ba8d?switchToAccountId=411850","mergeCommitSha":"8da70e7c239cc254c6ce01753892bc9afc70ccd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2738","title":"Handle milestoned event","createdAt":"2022-08-23T22:26:31Z"}
{"state":"Merged","mergedAt":"2022-08-23T23:16:14Z","number":2739,"body":"https://linear.app/unblocked/issue/UNB-567/[regression]-deleted-threads-are-resurfaced-when-editing-a-file","mergeCommitSha":"f5883bc289efa149a4402d27c0b0079dc2e0ed96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2739","title":"[Regression] Deleted threads are resurfaced when editing a file","createdAt":"2022-08-23T22:27:21Z"}
{"state":"Merged","mergedAt":"2022-02-08T18:21:04Z","number":274,"body":"## Client Interop\r\nKtor 2.0.0 is fundamentally backwards incompatible with its previous variants because of breaking DSL changes. I added a custom kotlin GQL client to make up for that.\r\n\r\n## Serialization\r\nThe gradle builder uses jackson by default, but the runtime loader loads the kotlinx serializer. So we end up with incompatible serialization. A quick classpath exclusion fixes that. Now the default class found by the ServiceLoader will be the jackson serializer. We might need to dump a custom object mapper to that thing at some point, but that's a problem for another day.\r\n","mergeCommitSha":"5cdabd432fcad55824e28347ced2bb21c2869ddd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/274","title":"Fixes GQL client incompatibility and serialization issues","createdAt":"2022-02-08T07:55:59Z"}
{"state":"Merged","mergedAt":"2022-08-23T23:39:58Z","number":2740,"body":"Will be reverted once ingestion is complete. This is only needed for the currently onboarded teams.","mergeCommitSha":"bb76cf73c586ea820d0401e7fffed1282143ada9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2740","title":"Trigger review ingestion in the bulk ingestion job","createdAt":"2022-08-23T22:44:59Z"}
{"state":"Merged","mergedAt":"2022-08-23T23:06:45Z","number":2741,"mergeCommitSha":"d942d5c1116410ea48e1f607e2a88b3f51c0351f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2741","title":"Get rid of extra padding","createdAt":"2022-08-23T22:50:49Z"}
{"state":"Merged","mergedAt":"2022-08-23T23:10:07Z","number":2742,"body":"Fixes the secondary text (the Timestamp labels here: https://chapter2global.slack.com/archives/C03T4CR3HBJ/p1661293765105189)\r\n\r\nThe base theming already applied an opacity to the foreground text, which looks nice in every theme.  Using the placeholder colour is unnecessary.","mergeCommitSha":"269dab9e410484e1268e41abbb7f5f3234fc1935","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2742","title":"Remove secondary text VSCode theming","createdAt":"2022-08-23T23:01:14Z"}
{"state":"Merged","mergedAt":"2022-08-24T01:52:08Z","number":2743,"body":"Enable TLC to world.","mergeCommitSha":"d46cdc671b221be878805ab4b2658f3ed7da19d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2743","title":"Enable TLC","createdAt":"2022-08-23T23:13:10Z"}
{"state":"Merged","mergedAt":"2022-08-29T18:36:14Z","number":2744,"body":"This reverts commit bb76cf73c586ea820d0401e7fffed1282143ada9.","mergeCommitSha":"e224f4838ac181e2b145cff3d3e4954bff344d73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2744","title":"Revert \"Trigger review ingestion in the bulk ingestion job (#2740)\"","createdAt":"2022-08-23T23:44:08Z"}
{"state":"Merged","mergedAt":"2022-08-24T00:38:56Z","number":2745,"body":"This style validator will catch things that zally doesn't during lint phase.\r\ni.e.\r\n1. Missing required fields in properties\r\n\r\ni.e.\r\n-------- -----\r\nSECTION COUNT\r\n-------- -----\r\nModels      1\r\n--------------\r\nModels\r\n--------------\r\n*ERROR* in Model 'LogContext', property 'message' -> This property should be present or removed from the list of required\r\n\r\nFAILURE: Build failed with an exception.\r\n","mergeCommitSha":"be69d36a13922a834baee54f306de030b1a939fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2745","title":"Add openapi style validator","createdAt":"2022-08-24T00:18:43Z"}
{"state":"Merged","mergedAt":"2022-08-24T01:36:56Z","number":2746,"body":"Makes no sense to compare main to main, or main to \"previous main\".\n\nInstead we need to run the test against publicly released versions only:\nhttps://www.notion.so/nextchaptersoftware/API-Version-Compatibility-Process-31d3bf9e50bf4f48814a7adbf0a45e7d#29e22487a0894f818fa0a1ad6dc47be6","mergeCommitSha":"986cf7b63641ccc1d726c1e6a34af05ec361873a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2746","title":"Skip APICompatTest on main CI","createdAt":"2022-08-24T00:35:03Z"}
{"state":"Merged","mergedAt":"2022-08-24T18:03:40Z","number":2747,"body":"ActiveTextEditor was not set until `window.onDidChangeActiveTextEditor` is called.\r\n\r\nThis meant our sidebars did not have an activeFile on load even if there was an active text editor.","mergeCommitSha":"a40f3415941e2a1193f21e86d1803b9e0994b829","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2747","title":"Set initial active editor","createdAt":"2022-08-24T17:54:22Z"}
{"state":"Merged","mergedAt":"2022-08-24T18:28:57Z","number":2748,"mergeCommitSha":"524837d877d5510f0e9c5ac5e064f45a7ece39ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2748","title":"Test java 18 upgrade","createdAt":"2022-08-24T18:18:15Z"}
{"state":"Merged","mergedAt":"2022-08-24T18:47:52Z","number":2749,"mergeCommitSha":"af115d122b555d8e30e68e684ddc1b38684a3914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2749","title":"Log number of commit hashes","createdAt":"2022-08-24T18:31:58Z"}
{"state":"Merged","mergedAt":"2022-02-08T18:55:57Z","number":275,"body":"Small PR to prep for setting up VSCode to utilize shared BaseAPI\r\n\r\nRemoves vscode API codegen and points references to shared instance.\r\nSeparate Web API models into same file to match vscode.","mergeCommitSha":"b45082c8a0d999af06696dc267f62cc9de470835","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/275","title":"Update API references to shared","createdAt":"2022-02-08T17:31:33Z"}
{"state":"Merged","mergedAt":"2022-08-24T22:48:29Z","number":2750,"body":"* Adjust font size, letter spacing, and word spacing of the Related PRs panel\r\n<img width=\"375\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186502950-3339259b-c4bb-4070-9363-8ec0e863a200.png\">\r\n<img width=\"365\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186503128-13b7e992-cafe-456e-81e6-232101f699ad.png\">\r\n<img width=\"374\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186503154-0628719e-9c71-42dc-ab47-7f907c9fd1f6.png\">\r\n\r\n\r\n* Fix missing selected state from panel\r\n<img width=\"364\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186503003-0f993e10-0afe-42ad-9032-0a72dba9732a.png\">\r\n","mergeCommitSha":"d0b8806dd41adf82ca00813b942dae9bd8cccc1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2750","title":"[TLC] Update related pull request panel styles","createdAt":"2022-08-24T19:10:24Z"}
{"state":"Merged","mergedAt":"2022-08-24T23:48:57Z","number":2751,"body":"Rendering PR Sidebar is currently blocked on loading *all* diff stats. \r\nIf a file has a lot of PRs (100s-1000s), loading diff stats can be quite slow ( > 10s).\r\n\r\nTo improve overall PR sidebar UX, we will show the first two tabs (comment count, merge date) without waiting on the diff stat tab. Diff stat tab will show a loader while git operations are running.\r\n\r\n<img width=\"619\" alt=\"CleanShot 2022-08-24 at 12 54 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/186512389-74aad93f-7dd4-4086-a6a8-f3400506b1fd.png\">\r\n\r\n","mergeCommitSha":"ea47961bee1f37bbd371d05d53ed150d3233b55b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2751","title":"[PR Sidebar]: Render comment & merge date tabs while diff stat is loading","createdAt":"2022-08-24T20:05:30Z"}
{"state":"Merged","mergedAt":"2022-08-24T21:00:12Z","number":2752,"mergeCommitSha":"46670467f7e57298ee24cab3a95854c048d9c122","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2752","title":"Cleanup code","createdAt":"2022-08-24T20:48:49Z"}
{"state":"Merged","mergedAt":"2022-09-07T16:28:45Z","number":2753,"mergeCommitSha":"081823b62e8dc6b74e975a7cd2246fc34ddf841e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2753","title":"Fix the video window toolbar layout","createdAt":"2022-08-24T20:52:42Z"}
{"state":"Merged","mergedAt":"2022-08-24T21:26:38Z","number":2754,"body":"No longer rate limiting metrics request on clients.\r\n\r\nRemoves dead unnecessary code.","mergeCommitSha":"a891607649f4de250023c69cd3383f848179e62f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2754","title":"Remove metrics cache","createdAt":"2022-08-24T21:01:18Z"}
{"state":"Merged","mergedAt":"2022-08-24T21:58:59Z","number":2755,"body":"<img width=\"517\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186525703-bef56f07-32c3-4788-88bf-bb20dcd1ff27.png\">\r\n","mergeCommitSha":"aa0ac784b5f62a57d71a304a9ae3615d811fe8c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2755","title":"[TLC] Add list of approvers to pr view","createdAt":"2022-08-24T21:20:10Z"}
{"state":"Merged","mergedAt":"2022-08-25T17:51:46Z","number":2756,"body":"Idea here is to re-use our existing logic for editing/creating/deleting top level comments in GitHub (`PullRequestCommentModel`). \r\n\r\nStep one is to migrate `UnblockedPRCommentMessagePayload.messageId` to  `UnblockedPRCommentMessagePayload.id` and add a type to this message, so that the message handler can handle those events differently.","mergeCommitSha":"beb74ea4122787f081259129a10adadd926eee20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2756","title":"Migrate UnblockedPRCommentMessagePayload.messageId to id","createdAt":"2022-08-24T21:59:20Z"}
{"state":"Merged","mergedAt":"2022-08-24T22:40:05Z","number":2757,"body":"We need to better handle when we spit this trace out, especially when the job is doing nothing because there are no messages etc.\r\n\r\nWill have to do that later, right now, our quota is being killed by this fucker.","mergeCommitSha":"7a68f0f3580a40462ad06de34e690a71173e9767","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2757","title":"Lots of spurious honeycomb traces killing us","createdAt":"2022-08-24T22:16:48Z"}
{"state":"Merged","mergedAt":"2022-08-25T03:51:10Z","number":2758,"body":"Needed for TLC CRUD operations","mergeCommitSha":"668dc0cc5af98dbbd72f4926b2c245d139dc1312","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2758","title":"Add create, update, and delete issue comment operations","createdAt":"2022-08-25T00:21:04Z"}
{"state":"Merged","mergedAt":"2022-08-25T02:30:13Z","number":2759,"body":"Going to eventually move to honeycomb metrics (requires a bit of work, but for now, just recording shit via spans)\r\n\r\nA bit of other crap related to network hostname.","mergeCommitSha":"4cd8cddb7fb91f263b7f95adfd38a831925059d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2759","title":"Log jvm metrics","createdAt":"2022-08-25T02:11:34Z"}
{"state":"Merged","mergedAt":"2022-02-08T20:32:07Z","number":276,"body":"## Problem\r\nIn some cases (app install flow for example), we need to pivot to a specific `TeamMember`. This is not possible if the root identity for a user is the `Person` object, because a person object can have many `Identities/Teammembers`.\r\n\r\nThe auth install flow needs to derive a specific `TeamMember` in order to determine whether the app is installed for a particular org. \r\n\r\n## What's in this PR\r\nChange the auth identity into the service to use the `Identity` instead of `Person`","mergeCommitSha":"121003b030ed000bb6815c5d4983f6a50b044088","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/276","title":"Use identity as auth identity rather than person","createdAt":"2022-02-08T18:04:33Z"}
{"state":"Merged","mergedAt":"2022-08-25T03:01:56Z","number":2760,"mergeCommitSha":"cb759fe5d8d51c4df7051f2e7d06ea5b2cab939f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2760","title":"Minor cleanup","createdAt":"2022-08-25T03:01:31Z"}
{"state":"Merged","mergedAt":"2022-08-25T06:39:36Z","number":2761,"body":"Fixed lack of wrapping on this page, in the Sourcepoints section:\r\nhttps://admin.prod.getunblocked.com/teams/5bf29d2c-23ae-4af8-8ccc-fe767cd2df79/members/5a69a1c3-58a0-48bb-a037-694aba71317a/threads/aef4eb73-cd97-4027-a603-6319d4313d73","mergeCommitSha":"208fe67d006764114e66dbaef30c91077b088e86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2761","title":"Fix snippet wrapping in admin when lines are long","createdAt":"2022-08-25T06:07:50Z"}
{"state":"Merged","mergedAt":"2022-08-25T15:18:29Z","number":2762,"body":"- use vararg instead of having to create maps\n- remove unused Marker functionality\n- no need to check if log level is enabled","mergeCommitSha":"b9fb50f2b7760dadcc4fd1815f0c453a3228f40d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2762","title":"More ergonomic logger extension functions","createdAt":"2022-08-25T06:21:21Z"}
{"state":"Merged","mergedAt":"2022-08-25T15:32:04Z","number":2763,"body":"In local testing discovered that while mark count is limited to 500 objects,\nthe point count goes as high as 12,000 objects.","mergeCommitSha":"66fd2de37278ab31c77a64c71105e6b3c6834505","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2763","title":"Adds logs to getSourceMarks API operation to count objects returned","createdAt":"2022-08-25T06:21:24Z"}
{"state":"Merged","mergedAt":"2022-08-26T01:41:14Z","number":2764,"body":"- Ability to focus the SM engine on a single sourcemark for debugging. This pretends\r\n  that there is only a single sourcemark in the repo. Invoke from comand line as:\r\n  ```\r\n  launchctl setenv SOURCEMARK_ID fdd2e9b9-fe73-4f00-9708-76d488140a60\r\n  ```\r\n- Generate a Git command report when performing source marks activity. This is\r\n  configured to generate a report for the SourceMarkProvider entry points.\r\n  ```\r\n  Command frequency for getSourceMarksForFile:\r\n  \r\n  count    sum    max    avg cmd\r\n     12     83      8    6.9 git ls-tree 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/build.gradle.kts\r\n     12     91     10    7.6 git rev-list -1 --objects 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/build.gradle.kts\r\n      6    165     30   27.5 git diff --diff-filter=DR --find-renames=50% --name-status be69d36a13922a834baee54f306de030b1a939fb --\r\n  ```\r\n","mergeCommitSha":"9e714d80dc25901fa38eeb0ca85eb94be789ebd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2764","title":"Source mark debug tooling","createdAt":"2022-08-25T06:41:54Z"}
{"state":"Merged","mergedAt":"2022-08-26T01:53:50Z","number":2765,"body":"Before (5,219 ms)\n```\n count    sum    max    avg cmd\n   166   1539    280    9.3 git ls-tree 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/private.yml\n   166   1328     12    8.0 git rev-list -1 --objects 2a0c8ed362c56692c321eb630e9b2c1833629d14 -- api/private.yml\n    83   2352     37   28.3 git diff --diff-filter=DR --find-renames=50% --name-status a150c3e4c7a008abb2212cec0f4eeb35570edf62 --\n```\n\nAfter (2,890 ms)\n```\n count    sum    max    avg cmd\n   166   1537    293    9.3 git ls-tree 01cb5b3f6e9b79c0bd00efb0d685031cec1bd431 -- api/private.yml\n   166   1325     14    8.0 git rev-list -1 --objects 01cb5b3f6e9b79c0bd00efb0d685031cec1bd431 -- api/private.yml\n     1     28     28   28.0 git diff --diff-filter=DR --find-renames=50% --name-status 01cb5b3f6e9b79c0bd00efb0d685031cec1bd431 --\n```","mergeCommitSha":"a712175b589cadec7ea15e6653ff3e10d7268cfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2765","title":"Optimize Git command usage during getSourceMarksForFile","createdAt":"2022-08-25T07:38:15Z"}
{"state":"Merged","mergedAt":"2022-08-25T15:59:41Z","number":2766,"body":"- also remove client side limit parameter\r\n\r\nThis will now require Clio to perform 1,129 sequential API calls to fetch all sourcemark for their repo, which will take ~4mins to download.\r\n\r\n<img width=\"1938\" alt=\"Screen Shot 2022-08-25 at 08 51 45\" src=\"https://user-images.githubusercontent.com/1798345/186712111-5170fea2-8b66-41c7-938b-1209e658de38.png\">\r\n","mergeCommitSha":"5d2869b00be7bc0cf22d4eb295d871e9fbd0f570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2766","title":"Reduce getSourceMark limit to 100 (was 500)","createdAt":"2022-08-25T15:49:20Z"}
{"state":"Merged","mergedAt":"2022-08-25T16:07:09Z","number":2767,"mergeCommitSha":"c3513d674f36edd9a8222ee8e17a66adbe34189d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2767","title":"increase api memory","createdAt":"2022-08-25T15:50:54Z"}
{"state":"Merged","mergedAt":"2022-08-25T16:04:14Z","number":2768,"body":"- Update memory ratio\r\n- Incease container memory ratio\r\n","mergeCommitSha":"465ee2f84f80ad48bd847a3efc65b59bde785d95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2768","title":"UpContainerMemoryUsage","createdAt":"2022-08-25T16:01:36Z"}
{"state":"Merged","mergedAt":"2022-08-25T16:18:39Z","number":2769,"mergeCommitSha":"a847c8da6874461a48c24b63c4071c5268647958","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2769","title":"increase API memory request to 1.5","createdAt":"2022-08-25T16:15:11Z"}
{"state":"Merged","mergedAt":"2022-02-08T18:27:30Z","number":277,"body":"No more !! in our code.\r\nit sucks and it's dangerous. \r\nAlso clean up tests.","mergeCommitSha":"3ce29344ea5ac326307812865cc74bec439b3a80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/277","title":"Add rule to forbid double bang dereferencing","createdAt":"2022-02-08T18:18:14Z"}
{"state":"Merged","mergedAt":"2022-08-26T15:55:54Z","number":2770,"body":"\r\nWhen any PR is ingesting and we have less PRs than commits, we *may* be missing PRs so show loading state.\r\n<img width=\"565\" alt=\"CleanShot 2022-08-25 at 08 52 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/186712981-666c2335-7ce0-452d-85b1-5fda8d85de2f.png\">\r\n\r\n\r\nOn PR view, show loading state if ingestion is still in progress.\r\n<img width=\"809\" alt=\"CleanShot 2022-08-25 at 09 19 11@2x\" src=\"https://user-images.githubusercontent.com/1553313/186717939-c8268045-d450-4a06-b4fc-bfddd33a1996.png\">\r\n\r\n","mergeCommitSha":"59f0f9df26611758f646ee743918e2fde09f25c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2770","title":"Handle PR Ingestion states","createdAt":"2022-08-25T16:20:43Z"}
{"state":"Merged","mergedAt":"2022-08-25T21:31:37Z","number":2771,"body":"https://user-images.githubusercontent.com/********/186719334-cdc58512-5466-44c4-b9be-ab2bd36f799d.mp4\r\n\r\nAlso:\r\n* Fix bug with capitalization of text\r\n* Fix build warning for flex property","mergeCommitSha":"1d94d408a88870125ae4b75dc6359e41c12e9c11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2771","title":"[TLC] Collapse thread views","createdAt":"2022-08-25T16:29:07Z"}
{"state":"Merged","mergedAt":"2022-08-25T17:52:19Z","number":2772,"body":"The Bresninator inspired me to do this.\r\nI thank the Bresninator.","mergeCommitSha":"aff76a3dc3df3a8194e06083e63ee1b52fbda7d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2772","title":"Clean up synchronous logging","createdAt":"2022-08-25T17:26:34Z"}
{"state":"Merged","mergedAt":"2022-08-25T20:52:43Z","number":2773,"mergeCommitSha":"908c27c3b87658e346be712d0b9949d839b4ffce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2773","title":"Wire up updating reviews","createdAt":"2022-08-25T18:49:19Z"}
{"state":"Merged","mergedAt":"2022-09-09T21:30:14Z","number":2774,"body":"This PR works on its own, but falls over after screen sharing due to lost window status. I'll fix that issue in a followup","mergeCommitSha":"92a051ba83b3fd05d7e6c359a41ec8478783f798","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2774","title":"Add toolbar menus","createdAt":"2022-08-25T18:49:53Z"}
{"state":"Merged","mergedAt":"2022-08-25T22:25:48Z","number":2775,"body":"Add debugging logs for open discussion bugs","mergeCommitSha":"7eb78f3df430cfcdaea9c170e64c2b107adce8d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2775","title":"Add debugging logs","createdAt":"2022-08-25T20:24:46Z"}
{"state":"Merged","mergedAt":"2022-09-09T21:46:01Z","number":2776,"mergeCommitSha":"f663f8e1e59d961242b11d7f0c55334dec73d3a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2776","title":"Fixes permissions dialog darkmode colours","createdAt":"2022-08-25T20:54:28Z"}
{"state":"Merged","mergedAt":"2022-09-01T21:16:59Z","number":2777,"mergeCommitSha":"e77f39941dc417861b48dad41594f0be7ddd56c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2777","title":"Endpoints for TLCs","createdAt":"2022-08-25T21:08:27Z"}
{"state":"Merged","mergedAt":"2022-08-25T22:53:04Z","number":2778,"body":"This pr allows us to intercept the ktor payload before the compression plugin fucks around with headers.\r\nWe can ascertain what the payload size is pre-compression.\r\n\r\nPost-compression, no go.\r\nhttps://github.com/ktorio/ktor/blob/fe91d81cb7b68fe4a1bb3b97eadbaa20ee0a5302/ktor-server/ktor-server-plugins/ktor-server-compression/jvm/src/io/ktor/server/plugins/compression/Compression.kt#L173","mergeCommitSha":"5fe93b3054797f261b4d2c3ce7abeb70450ea6f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2778","title":"Add ability to record precompressed response paypload size in honeycomb","createdAt":"2022-08-25T21:47:34Z"}
{"state":"Merged","mergedAt":"2022-08-26T17:52:10Z","number":2779,"mergeCommitSha":"9e407eb0be0f2a267ccd547b15cd23c51d27b4f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2779","title":"Wire up creating, editing, and deleting issue comments","createdAt":"2022-08-25T21:47:56Z"}
{"state":"Merged","mergedAt":"2022-02-08T23:07:14Z","number":278,"body":"Add refresh logic for auth token on vscode.\r\nWhen a request 401s, attempt to use refreshtoken to get a new base token.\r\n\r\n","mergeCommitSha":"699c838c5eaa6f26a6a0967d533a8988b9e95789","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/278","title":"Refresh Auth VSCode","createdAt":"2022-02-08T19:38:50Z"}
{"state":"Merged","mergedAt":"2022-08-26T02:54:36Z","number":2780,"body":"https://ui.honeycomb.io/unblocked/environments/local/datasets/service/result/oWptkkDhZEL?tab=metrics\r\n\r\nThis pr lets us now use various honeycomb meter types.\r\nCool thing is, you can combine metric board graphs with query graphs. (as in the above example)\r\nInterestingly, Honeycomb metrics are a good way of cost saving, as Honeycomb aggregates data.\r\n\r\nhttps://docs.honeycomb.io/getting-data-in/metrics/#:~:text=Honeycomb%20stores%20metrics%20in%20events,are%20included%20on%20the%20event.","mergeCommitSha":"54d9e29c0d82fa13d86d01777a557270c7934004","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2780","title":"Add metrics for honeycomb","createdAt":"2022-08-26T01:26:54Z"}
{"state":"Merged","mergedAt":"2022-09-01T03:16:00Z","number":2781,"body":"- Add a new service called Auth service \r\n- Add ALB paths to redirect all auth related calls to the new auth service\r\n- Added helm charts with updated value to suite the functions which will be handled by this service \r\n\r\nAuth service is a replica of API service. It uses the same artifact but handles a subset of API paths related to auth only. This way we can limit the impact of service crashes. \r\n\r\nMaking a whole new grade project and moving the code to it seemed like an overkill. This PR requires disabling prod deploys before merge so we could safely test it in Dev. ","mergeCommitSha":"ed2141f7c5ff73fe62d525543b92643e0a57ee45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2781","title":"add auth service as a replica of api service","createdAt":"2022-08-26T04:57:37Z"}
{"state":"Merged","mergedAt":"2022-08-26T22:44:45Z","number":2782,"body":"Changes:\r\n\r\n- found a more efficient way to get a file content hash\r\n- adds Git cache, and introduces LRU cache dependency\r\n\r\nResults:\r\n\r\n- full recalculation runtime on unblocked repo went from 3m 31s to 1m 42s\r\n- get source marks for file is almost instant even for large files with lots of marks;\r\n  however rendering (or joining on threadinfo) still takes a very long time.\r\n\r\n\r\nFixes: https://linear.app/unblocked/issue/UNB-588/optimize-source-mark-engine-point-lookup","mergeCommitSha":"ff26f1b9dbcef4d2550f8fd0b93511ebdf596b63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2782","title":"Optimize SM file lookup and full recalculation","createdAt":"2022-08-26T07:44:51Z"}
{"state":"Merged","mergedAt":"2022-08-31T18:08:30Z","number":2783,"body":"Add support for sequential promises that can be chunked.\r\n\r\nUsed primarily for calculating diff stats.\r\n\r\nStill seems to work?\r\n<img width=\"977\" alt=\"CleanShot 2022-08-26 at 11 22 55@2x\" src=\"https://user-images.githubusercontent.com/1553313/186967996-7a85911b-97e8-4f19-b110-a2d113cd1010.png\">\r\n\r\n","mergeCommitSha":"089b9f0c269bb22b90cca21fb08bbb2ce4d1280f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2783","title":"Sequential promises","createdAt":"2022-08-26T18:23:10Z"}
{"state":"Merged","mergedAt":"2022-08-26T19:10:10Z","number":2784,"mergeCommitSha":"ed57a07aec3cd41f0e9a567385c6f7b2424f7fa6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2784","title":"Fix vscode storybook tests","createdAt":"2022-08-26T19:01:24Z"}
{"state":"Merged","mergedAt":"2022-08-26T19:58:46Z","number":2785,"mergeCommitSha":"0fc3d9c466ebee8953179b105932cb815a408e73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2785","title":"Add per service jvm metrics","createdAt":"2022-08-26T19:28:36Z"}
{"state":"Merged","mergedAt":"2022-08-26T20:34:09Z","number":2786,"mergeCommitSha":"240ba8e64eda586e3e4be616e00b42754469f1e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2786","title":"Update","createdAt":"2022-08-26T20:17:19Z"}
{"state":"Closed","mergedAt":null,"number":2787,"body":"<img width=\"612\" alt=\"CleanShot 2022-08-26 at 13 32 33@2x\" src=\"https://user-images.githubusercontent.com/1553313/186988155-faff1302-a54b-46a2-9e08-a8daa003823c.png\">\r\n","mergeCommitSha":"a47acfc5143e98ef5683407e2fcb8f92e956c3b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2787","title":"Add mentioned to web extension","createdAt":"2022-08-26T20:45:53Z"}
{"state":"Closed","mergedAt":null,"number":2788,"body":"The main goal of this PR is to implement the screen picker. There's some crufty stuff that I will be cleaning up as the implementation evolves.\r\n\r\n![CleanShot 2022-08-29 at 10 45 15](https://user-images.githubusercontent.com/858772/187265153-e8f18549-f92a-482b-82ce-2c5a37e53721.gif)\r\n","mergeCommitSha":"8cee2396fe6a33754e436d90fd0580a600d6605e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2788","title":"Screen Share Picker","createdAt":"2022-08-26T21:14:35Z"}
{"state":"Merged","mergedAt":"2022-08-26T23:27:43Z","number":2789,"body":"When the last message in a thread is deleted, we want to suppress any notifications. Doing this on the client solves this in all deletion scenarios, but importantly it is the only way to solve the following scenario:\r\n\r\n1. `LastReadMessage == LastMessage - N`\r\n2. Delete `LastMessage`\r\n\r\nThe service will mark this thread as unread in this scenario and can't understand the client notification state.","mergeCommitSha":"51bc473fc002932298dfd2208aa793a8d1232f01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2789","title":"Suppress notifications for deletions","createdAt":"2022-08-26T22:16:24Z"}
{"state":"Merged","mergedAt":"2022-02-08T20:34:54Z","number":279,"body":"Updating the GraphQL queries to make it possible to page results.","mergeCommitSha":"9d7eaa9cda173e83b97400ba973f86fb219af076","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/279","title":"Allow setting page sizes and cursors on PR review thread queries","createdAt":"2022-02-08T20:29:04Z"}
{"state":"Merged","mergedAt":"2022-08-26T22:58:31Z","number":2790,"body":"<img width=\"279\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186998066-378ce78c-8fc9-4e7b-b1df-66d1d26a5fe1.png\">\r\n\r\n<img width=\"1479\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/186998103-c0126f22-67dc-4b65-ba02-bc189c5347d6.png\">\r\n\r\n* Also refactor teamMembers --> userIconProtocol logic into one place","mergeCommitSha":"3dc68ccbc791a0e188d559ddce678bb951cdf012","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2790","title":"UNB-561 Add missing unread mention indicators to dashboard/extension","createdAt":"2022-08-26T22:22:54Z"}
{"state":"Merged","mergedAt":"2022-08-29T22:21:07Z","number":2791,"mergeCommitSha":"c7657e73c4a18a98c32b2c3fd44957d4e6624608","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2791","title":"Fix bug where threads are being marked as read when the last message in a thread is deleted","createdAt":"2022-08-26T23:15:44Z"}
{"state":"Merged","mergedAt":"2022-08-26T23:36:30Z","number":2792,"mergeCommitSha":"1346bbe1ce4927014834a97688fa7433e87d5139","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2792","title":"add slack utilities","createdAt":"2022-08-26T23:18:11Z"}
{"state":"Merged","mergedAt":"2022-08-29T16:49:32Z","number":2793,"body":"<img width=\"1505\" alt=\"Pasted Graphic\" src=\"https://user-images.githubusercontent.com/********/187003024-ba0e9eaa-6668-4333-aae1-d56288a66e56.png\">\r\n\r\n<img width=\"1505\" alt=\"Pasted Graphic 1\" src=\"https://user-images.githubusercontent.com/********/187003029-46964506-befe-42f0-adbf-80a989820431.png\">\r\n\r\n","mergeCommitSha":"db175fdb8f6acbce55fa926d861a45fbe258abba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2793","title":"Add empty view for no discussions/no search","createdAt":"2022-08-26T23:36:05Z"}
{"state":"Merged","mergedAt":"2022-08-31T22:30:59Z","number":2794,"body":"https://linear.app/unblocked/issue/UNB-589/bulk-fetch-thread-info-api-implementation","mergeCommitSha":"b9c47a6c3e592a97e40b460a2f9ae04f400eadad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2794","title":"Bulk fetch threads","createdAt":"2022-08-26T23:44:35Z"}
{"state":"Merged","mergedAt":"2022-08-31T19:39:28Z","number":2795,"body":"API to support unread status in GitHub PR View.\r\n\r\n1. API to get PullRequest given teamID, repoID, PR number\r\n2. API + Channel to get list of unreadMessages. UnreadMessages match with IDs in GH's PR view.","mergeCommitSha":"24699eac41e59fe13f3181677b3d6d76dde43dad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2795","title":"API for web extension PR unread","createdAt":"2022-08-26T23:55:14Z"}
{"state":"Merged","mergedAt":"2022-09-02T22:48:23Z","number":2796,"body":"Problem:\n\n- A simple edit confused the source mark engine in ChannelPoller.ts\n  https://linear.app/unblocked/issue/UNB-597/a-simple-edit-confused-the-source-mark-engine-in-channelpollerts","mergeCommitSha":"11d7141f2664277a8954006f5e07af4c146385a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2796","title":"Support for diff hunk modifications","createdAt":"2022-08-28T02:19:50Z"}
{"state":"Merged","mergedAt":"2022-09-02T22:51:08Z","number":2797,"body":"https://linear.app/unblocked/issue/UNB-596/sourcemark-is-associated-with-an-unexpected-line-heavily-modified","mergeCommitSha":"3ad2cf6121edad7d8be66b8cc34ed3817c9439ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2797","title":"SourceMark is associated with an unexpected line. Heavily modified, should be removed.","createdAt":"2022-08-28T17:56:31Z"}
{"state":"Merged","mergedAt":"2022-08-29T17:45:57Z","number":2798,"body":"### Changes\r\nI cannot reproduce a problem with `fileContentHash` in my environment.\r\nHoping that more verbose logging will uncover the problem for people who can repro.\r\n\r\n### Problem\r\n```\r\n{\r\n  context: 'DiscussionThreadCommand',\r\n  service: 'vscode',\r\n  environment: 'local',\r\n  type: 'nodejs',\r\n  process: 'extension',\r\n  level: 'error',\r\n  message: 'Issue fetching anchor source point File does not exist at new commit; possibly renamed or moved.',\r\n  stack: 'Error: File does not exist at new commit; possibly renamed or moved.\\n' +\r\n    '\\tat nl.hunkReduce (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1162424)\\n' +\r\n    '\\tat async nl.lookupOrRecalculateForChanges (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1162159)\\n' +\r\n    '\\tat async nl.getPointForSourceMarkInternal (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1164865)\\n' +\r\n    '\\tat async nl.getPointForSourceMark (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1162923)\\n' +\r\n    '\\tat async dl.getSourceMarkLatestPoint (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1168991)\\n' +\r\n    '\\tat async gs.withChangedFiles (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1070282)\\n' +\r\n    '\\tat async wd (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1126107)\\n' +\r\n    '\\tat async Dd (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1141467)\\n' +\r\n    '\\tat async _ (/Users/<USER>/.vscode/extensions/nextchaptersoftware.unblocked-vscode-1.0.364/extension.js:2:1127963)',\r\n  timestamp: '2022-08-29T16:26:41.518Z'\r\n}\r\n```","mergeCommitSha":"7b448527bc4d8f387929e3fa821b85eb052c8fa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2798","title":"Attempt to track down error in fileContentHash","createdAt":"2022-08-29T17:37:29Z"}
{"state":"Merged","mergedAt":"2022-08-29T18:39:57Z","number":2799,"mergeCommitSha":"24f0aab13773888894867abeea8fa115044b60f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2799","title":"Add snippet","createdAt":"2022-08-29T17:40:08Z"}
{"state":"Merged","mergedAt":"2022-02-11T18:11:09Z","number":280,"body":"Initially had authstore exist for both web and vscode client.\r\n\r\nThis was necessary as there was client specific code within the AuthStore which made sharing the entire AuthStore difficult.\r\n\r\nBased on this document:\r\nhttps://github.com/pmndrs/zustand/wiki/Practice-with-no-store-actions\r\n\r\nWe have split up the Authstores state and actions. This then allowed me to refactor out the common aspects of the store for the two clients while allowing each client define their own specific actions.\r\n\r\nBuild off https://github.com/NextChapterSoftware/unblocked/pull/278","mergeCommitSha":"cf2b0d35b7c0cb9eb1284702a555d0f021d1520f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/280","title":"Shared Auth Store","createdAt":"2022-02-08T21:55:18Z"}
{"state":"Merged","mergedAt":"2022-08-29T18:34:07Z","number":2800,"mergeCommitSha":"d93eb1ef7a07a88ea984923e5ed3a91c1ba9160e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2800","title":"Deployments for snippet","createdAt":"2022-08-29T18:27:22Z"}
{"state":"Closed","mergedAt":null,"number":2801,"body":"Problem is that we introduced a new way to calculate the file content hash using\nan `ls-tree` option called `--object-only` which is not available in older Git versions.\n\nSee also:\n- breaking change in #2782\n- debugging to find problem in #2798","mergeCommitSha":"0ea6f669f9f1d6fd8686318f63bdb40fc25af4a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2801","title":"Fix for fileContentHash","createdAt":"2022-08-29T18:40:07Z"}
{"state":"Merged","mergedAt":"2022-08-29T19:06:33Z","number":2802,"mergeCommitSha":"f062e5272f606eae0e0e7c6af35dacc26fef293d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2802","title":"Build with java 18","createdAt":"2022-08-29T18:45:12Z"}
{"state":"Merged","mergedAt":"2022-08-29T19:12:09Z","number":2803,"body":"Problem is that we introduced a new way to calculate the file content hash using\nan ls-tree option called --object-only which is not available in older Git versions.\n\nSee also:\n\n- breaking change in Optimize SM file lookup and full recalculation #2782\n- debugging to find problem in Attempt to track down error in fileContentHash #2798\n- alternative fix in #2801","mergeCommitSha":"30cd967358e413ce3493b7f224e667ac7053aed7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2803","title":"Fix for ls-tree based fileContentHash function","createdAt":"2022-08-29T18:59:17Z"}
{"state":"Merged","mergedAt":"2022-08-29T19:37:31Z","number":2804,"mergeCommitSha":"73ef4711a7ed4179efff27edece0a45cb9a9017f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2804","title":"Wrong ecr repo (fuck)","createdAt":"2022-08-29T19:37:20Z"}
{"state":"Merged","mergedAt":"2022-08-30T03:40:15Z","number":2805,"body":"If you click on a thread in the hub, and it opens in VSCode, a second VSCode instance temporarily flickers open in the system dock.  This prevents it.  This thread has some more background: https://github.com/microsoft/vscode/issues/60579","mergeCommitSha":"cafb46a5095190fac4fcf1c888df51cc0e743e4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2805","title":"Prevent VSCode flickering on dock when opening a thread from the hub","createdAt":"2022-08-29T21:18:42Z"}
{"state":"Merged","mergedAt":"2022-08-29T21:38:38Z","number":2806,"body":"Revert \"Fix for ls-tree based fileContentHash function (#2803)\"\n\nThis reverts commit 30cd967358e413ce3493b7f224e667ac7053aed7.\n\nFix for fileContentHash\n\nProblem is that we introduced a new way to calculate the file content hash using\nan `ls-tree` option called `--object-only` which is not available in older Git versions.\n\nSee also:\n- breaking change in #2782\n- debugging to find problem in #2798","mergeCommitSha":"57caceddac79a84d440129c9d527d4ef1120e6a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2806","title":"Fix fileContentHash function again","createdAt":"2022-08-29T21:37:32Z"}
{"state":"Merged","mergedAt":"2022-08-29T22:18:31Z","number":2807,"body":"For older Git versions specifying an empty pretty format implies a default output;\nwhereas in more recent Git versions the behavious has changed (to what we want) to show no content.\nFix by putting in a newline (`%n`) which is already trimmed out anyway.\n\nhttps://linear.app/unblocked/issue/UNB-602/diffstatparser-exceptions-resulting-in-missing-changed-lines","mergeCommitSha":"0cba4ced823028d9453dc760e279dda50e57d10a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2807","title":"Fix DiffStatParser exceptions resulting in missing changed lines","createdAt":"2022-08-29T22:08:06Z"}
{"state":"Merged","mergedAt":"2022-08-31T18:45:23Z","number":2808,"body":"<img width=\"395\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/187302169-fca7ab1c-fa75-4292-a91d-caea4361880a.png\">\r\n\r\n<img width=\"1503\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/187302134-ea858a1e-fba5-45d2-a456-4d0c084d8065.png\">\r\n","mergeCommitSha":"a0e7325af7cf2968888054fd7536eee20b0cdbe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2808","title":"Add invite page to dashboard","createdAt":"2022-08-29T22:50:12Z"}
{"state":"Merged","mergedAt":"2022-08-30T01:01:32Z","number":2809,"mergeCommitSha":"49a98efa5f29328fa7e3445ac9d327fcb966bcee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2809","title":"Add basic functionality for snippet shit","createdAt":"2022-08-29T23:29:17Z"}
{"state":"Merged","mergedAt":"2022-02-09T01:00:39Z","number":281,"body":"![image](https://user-images.githubusercontent.com/********/153085234-ae84c8fc-5256-4ad6-aa16-0b83d962e1eb.png)\r\n\r\n* Also refactored StartDiscussion form to include MessageEditor\r\n![image](https://user-images.githubusercontent.com/********/153085533-f6934b3d-4010-4357-bfe0-423673b5339f.png)\r\n\r\n* Refactored StartDiscussionCommand to take a set of action commands \r\n\r\nNOTE: Is missing editor code theming, issues with the shiki library, will revisit (but shouldn't block this PR from going in)","mergeCommitSha":"f296e88998261115474fda8e849b07869d622bc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/281","title":"Add vscode codeblock component","createdAt":"2022-02-08T22:26:20Z"}
{"state":"Merged","mergedAt":"2022-08-30T02:05:39Z","number":2810,"mergeCommitSha":"0c3f0f669bc932e8766f2afccc2323482590078c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2810","title":"Split team member page into current/past so that it can render large teams","createdAt":"2022-08-30T01:06:30Z"}
{"state":"Merged","mergedAt":"2022-08-30T02:51:03Z","number":2811,"mergeCommitSha":"715d5fbc975c074982f4301a7805eda6e789cf47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2811","title":"[BREAKS API ON MAIN] Try loading snippets from index","createdAt":"2022-08-30T02:25:28Z"}
{"state":"Merged","mergedAt":"2022-09-07T20:01:04Z","number":2812,"body":"A first pass at pruning PR-ingested threads that are likely to not contain valuable information. \r\n\r\nThe approach here is to use heuristics to determine whether a thread contains valuable information, and to archive it if not.\r\n\r\nTo do this, I'm adding a new enum field on `ThreadModel` called `archivedReason` which will let us specify why a thread was archived, which will help track why a thread was archived.\r\n\r\nFor now we're using a single heuristic to identify low-value threads: threads with one message where the message contains only one word (examples: `LGTM!`, `cool`, `neat`, emojis). The intention is to follow-up this PR with others that add additional heuritics, but to start I'd like get this logic in with something simple and straightforward.\r\n\r\nTODO\r\n\r\n- [x] fill out stubbed tests\r\n- [x] add migration logic to Admin Console to archive low-value threads for already ingested repos\r\n- [x] only archive threads for closed/merged PRs \r\n- [x] backfill `ThreadModel.archivedReason` for user-archived threads\r\n ","mergeCommitSha":"c32eb6f36b30ebe24d66604f8e03f46de3f858ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2812","title":"Prune low-value threads","createdAt":"2022-08-30T06:19:10Z"}
{"state":"Merged","mergedAt":"2022-08-30T22:47:22Z","number":2813,"body":"follow up from #2810 ","mergeCommitSha":"0c1ae44e7e8073d92d31159ba6546e095c7c3b20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2813","title":"Stats page shoud link to current team members","createdAt":"2022-08-30T22:19:18Z"}
{"state":"Merged","mergedAt":"2022-08-30T22:47:36Z","number":2814,"body":"https://sentry.io/organizations/nextchaptersoftware/issues/","mergeCommitSha":"f2bfe1c0887c143aa89af80e714151ed78b07b74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2814","title":"Install Sentry","createdAt":"2022-08-30T22:20:10Z"}
{"state":"Merged","mergedAt":"2022-08-30T23:31:38Z","number":2815,"body":"Prototype and hidden behind feature flag.\r\nThis is the experimental UI I call Snippets n' Shit.\r\n\r\nNot meant for production.\r\nNot meant for review.\r\nNot meant for anything else but giggles and prototyping.\r\n\r\n-Rashin","mergeCommitSha":"da3252d2d1429e2e5484fbf40f57c0439bd3ef1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2815","title":"Snippet Webview Skeleton","createdAt":"2022-08-30T23:19:38Z"}
{"state":"Merged","mergedAt":"2022-08-31T01:23:11Z","number":2816,"mergeCommitSha":"b47d2232596277089f1b877de9362c11536e8b86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2816","title":"Retreive snippets from api","createdAt":"2022-08-31T00:36:55Z"}
{"state":"Merged","mergedAt":"2022-08-31T01:25:17Z","number":2817,"mergeCommitSha":"9885f7a0ed3c3d7ee59ae5786c4d0a6e5f0d3961","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2817","title":"Update","createdAt":"2022-08-31T01:08:51Z"}
{"state":"Merged","mergedAt":"2022-08-31T18:43:03Z","number":2818,"mergeCommitSha":"5fe7f5175adddf1aa9bc43e35a95a23ee212e036","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2818","title":"Trace for pollingBackgroundJobs","createdAt":"2022-08-31T17:08:14Z"}
{"state":"Merged","mergedAt":"2022-08-31T18:04:08Z","number":2819,"body":"Existing tests still work","mergeCommitSha":"897f18b7287ddfa5bae9c5e729c46ab58d659309","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2819","title":"Ease up on database queries","createdAt":"2022-08-31T17:38:56Z"}
{"state":"Merged","mergedAt":"2022-02-10T00:30:14Z","number":282,"body":"Setup CI deployments for our web service to `https://dashboard.dev.getunblocked.com/`\r\n\r\nIntroduces very simple env variable system for web.\r\nTODO: Introduce something similar for vscode.\r\n\r\nUpdates some CORS settings to allow requests from domain to `'https://apiservice.us-west-2.dev.getunblocked.com'`\r\n","mergeCommitSha":"3345544ae6869971c174097ca87f773fcb90dfa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/282","title":"Web deploy","createdAt":"2022-02-08T22:34:13Z"}
{"state":"Merged","mergedAt":"2022-08-31T18:31:00Z","number":2820,"body":"- Adding a 4 second timeout to deep check probes. Default value was 1 second. That causes frequent service restarts under heavy workload","mergeCommitSha":"2e9c517bc3b540d8af61748cb67f5064c0c2d628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2820","title":"add timeout to kube probes","createdAt":"2022-08-31T17:47:35Z"}
{"state":"Merged","mergedAt":"2022-08-31T20:10:32Z","number":2821,"mergeCommitSha":"5f7fdbdaa462a83b32ed4b1946b6f9904c4ae4fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2821","title":"Clear the invite form after submitting","createdAt":"2022-08-31T18:51:08Z"}
{"state":"Merged","mergedAt":"2022-08-31T19:41:55Z","number":2822,"mergeCommitSha":"a048bb6e1a086cf39921edd91fc0fddd9b694d6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2822","title":"Dont count messages in archived threads","createdAt":"2022-08-31T18:56:04Z"}
{"state":"Merged","mergedAt":"2022-08-31T21:44:22Z","number":2823,"mergeCommitSha":"eac6ccfa66eac975ef59d6653da656006ec53e1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2823","title":"[BREAKS API ON MAIN] Minor POC updates","createdAt":"2022-08-31T19:50:28Z"}
{"state":"Merged","mergedAt":"2022-08-31T22:07:08Z","number":2824,"body":"Running into situation where RefreshAuth successfully 200s but getPerson 401s within the same try catch block.\r\n\r\nThis could happen if getPerson is called *after* the authToken from refreshAuth expires (Mac goes to sleep?)\r\n\r\nFixes:\r\n* catch errors on getPerson\r\n* check for refreshAuth 401 specifically","mergeCommitSha":"7529c361d3f7ff1a70d0e39a3bc6451c31413e89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2824","title":"Retry on 401 from getPerson","createdAt":"2022-08-31T20:12:01Z"}
{"state":"Merged","mergedAt":"2022-09-01T15:35:31Z","number":2825,"body":"Follow up to https://github.com/NextChapterSoftware/unblocked/pull/2795\r\n\r\nAfter speaking with Ben, we do *not* want to have any UI for unread status in GH at this time.\r\nWe have the following options:\r\n\r\n~~1. Keep the planned APIs and add an `updatePullRequestMessageUnread` which acts similar to `updateThreadUnread`. This is what's in the current PR.~~\r\n\r\n2. Remove `getPullRequestThreadUnread`. `updatePullRequestMessageUnread` behaviour changes and is only used to mark as read. \r\n","mergeCommitSha":"e935c6135c74ba9fc6b838faf5b6909c1e05396d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2825","title":"[BREAKS API ON MAIN] Update PRMessageUnread with PUT operation","createdAt":"2022-08-31T20:45:23Z"}
{"state":"Merged","mergedAt":"2022-08-31T21:44:06Z","number":2826,"body":"https://hub.docker.com/_/openjdk\r\n\r\nRedhat has recommended to move away from their images to those provided by other providers.\r\nSince on github we're using Temurin jdks for building our packages, we should be using temurin for our runtime environments.","mergeCommitSha":"42e40972b670d756d517f4924f90435282f2d846","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2826","title":"Our current openjdk vendor had deprecated their images","createdAt":"2022-08-31T21:30:07Z"}
{"state":"Merged","mergedAt":"2022-09-02T20:01:54Z","number":2827,"body":"This is a refactoring that should not change client behaviour at all, hopefully.\r\n\r\nThe point of this refactoring is to change ChannelPoller so that each channel can return a series of last-modified values, one for each channel ID, not just a single last-modified value.  We will need this for bulk thread fetching.  This also makes sense structurally: the set of last-modified values (ie the channel IDs) is really determined by the values returned in the `GET` calls.\r\n\r\nMy next PR will refactor the thread stores to better use this, with bulk thread loading.","mergeCommitSha":"310260d465c1a4362889594ccb152b359a482d0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2827","title":"Refactor channel poller","createdAt":"2022-08-31T21:40:05Z"}
{"state":"Merged","mergedAt":"2022-09-01T23:25:31Z","number":2828,"body":"### problem\r\nProblem was that we were returning a collection with no `X-Unblocked-Last-Modified` header.\r\n\r\n### change\r\nNow we return a header with a cursor at the start of any possible collection.\r\n\r\n### result\r\nvscode client is now making poller requests every second for this channel with opaque cursor\r\n```json\r\n{\r\n  \"channel\": \"/sourceMarks?repoIds=7556968d-cfa6-4c01-bf24-d4b230947384\",\r\n  \"ifModifiedSince\": \"eyJtb2RpZmllZEF0IjoiMTk3MC0wMS0wMVQwMDowMDowMFoiLCJtYXJrSWQiOm51bGx9\"\r\n}\r\n```\r\n... which base64 decodes as:\r\n```json\r\n{\r\n  \"modifiedAt\": \"1970-01-01T00:00:00Z\",\r\n  \"markId\": null\r\n}\r\n```","mergeCommitSha":"378b5515259511cd2554d1d48debee23904416e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2828","title":"Handle getSourceMarks paging when there are zero sourcemarks in the repo","createdAt":"2022-08-31T22:34:03Z"}
{"state":"Merged","mergedAt":"2022-09-01T18:16:01Z","number":2829,"body":"<img width=\"1507\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/187802252-bd78d3f5-e7d2-4d44-b970-37de792db169.png\">\r\n\r\n<img width=\"1507\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/187802282-6dfc0071-da55-4aa5-9ae6-4d93e24d4e4f.png\">\r\n","mergeCommitSha":"db820a0f6b4e6da5f642bc62016a4e4863bdb0e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2829","title":"Add unread filter for dashboard","createdAt":"2022-08-31T23:28:26Z"}
{"state":"Merged","mergedAt":"2022-02-08T22:57:32Z","number":283,"body":"Not sure how these got past CI","mergeCommitSha":"d03368320cfb515006c7bf072c3aaf03e1ddab7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/283","title":"Fix Lint","createdAt":"2022-02-08T22:40:19Z"}
{"state":"Merged","mergedAt":"2022-09-01T01:38:39Z","number":2830,"mergeCommitSha":"0361b7b3e203882029a7869ee0e52c0f9be3228a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2830","title":"Add slack threads","createdAt":"2022-09-01T00:53:40Z"}
{"state":"Merged","mergedAt":"2022-09-01T02:11:23Z","number":2831,"mergeCommitSha":"85917b94b27c04b1d39e001c8d1daab79dff8407","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2831","title":"Improve lucene slack search","createdAt":"2022-09-01T01:41:55Z"}
{"state":"Merged","mergedAt":"2022-09-01T04:00:27Z","number":2832,"body":"Copy paste mistake in helm chart path. ","mergeCommitSha":"245683036bb8209ef0ffd2c7e09a151294d3dad6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2832","title":"fix helm chart path","createdAt":"2022-09-01T03:43:49Z"}
{"state":"Merged","mergedAt":"2022-09-01T05:57:50Z","number":2833,"mergeCommitSha":"2d28d945651edbed16c3d74043eb2cb6dbced45f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2833","title":"Fix alb paths","createdAt":"2022-09-01T05:39:45Z"}
{"state":"Merged","mergedAt":"2022-09-06T19:01:06Z","number":2834,"body":"Client bits with API request commented out.\r\n\r\nUses intersection observer to determine which discussions are visible on screen.\r\nAfter n (currently 2) seconds, we trigger API request to trigger read status.\r\n\r\nClient is somewhat smart in that it will try to send an event for the \"latest\" message in a discussion.\r\n\r\nFixes UNB-583","mergeCommitSha":"35ed90beab199b5781e4d0d16d3fc764c9f5cc26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2834","title":"Client bits for KaySync","createdAt":"2022-09-01T17:07:35Z"}
{"state":"Merged","mergedAt":"2022-09-01T17:29:24Z","number":2835,"body":"- Added service account for telemetry service\r\n- Added telemetry service as a replica of API service which handles /logs endpoint only \r\nSince logs are not mission critical this change does not require disabling prod deploys. ","mergeCommitSha":"bcd6d7958b5abc99a966e63a61b5dd114a419f3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2835","title":"adding telemetry service to handle /logs","createdAt":"2022-09-01T17:11:36Z"}
{"state":"Merged","mergedAt":"2022-09-01T17:41:47Z","number":2836,"mergeCommitSha":"df4acf59d5ccbd62808e6e158f4deb25ca8866fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2836","title":"add a read replica to dev rds","createdAt":"2022-09-01T17:31:26Z"}
{"state":"Merged","mergedAt":"2022-09-01T18:14:23Z","number":2837,"mergeCommitSha":"1f5dd2cfca618fa46685df0860a883f2ac46582f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2837","title":"Add ability to override environment configuration","createdAt":"2022-09-01T18:06:34Z"}
{"state":"Merged","mergedAt":"2022-09-01T18:56:28Z","number":2838,"body":"Regression introduced in #2764.","mergeCommitSha":"91693000bf0de33794e9c4bcfcdf105c9e809f01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2838","title":"Fix source mark store lookup interaction","createdAt":"2022-09-01T18:37:01Z"}
{"state":"Merged","mergedAt":"2022-09-01T22:31:28Z","number":2839,"body":"This also fixes UNB-542","mergeCommitSha":"64ce6f56fc92ea0e1f9e66df2a6b8e66842e09eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2839","title":"Server bits for KaySync","createdAt":"2022-09-01T18:57:17Z"}
{"state":"Merged","mergedAt":"2022-02-09T05:05:39Z","number":284,"body":"Refactored cross environment stacks (mainly ECR)\r\n\r\n    - Changed build configs to create standard ECR config\r\n    - Added a new stack for ECR replication config\r\n    - Moved existing replication policy from compute stack to the new stack for replicated repos\r\n    - Changed existing ECR registry stack to use new config class for ECR\r\n    - Added lambda funtion to cleanup old images in replicated ECR repos. (AWS does not reflect lifecycle policies in replicated repos)\r\n    - Deleted ComputeStack since we no longer need it\r\n\r\n    All changes have been deployed to Dev, Prod and SecOps account","mergeCommitSha":"1cdd6e2d3b1b976c72bfa1f8c465a5f19dc9fcd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/284","title":"Ckd refactor ECR stacks","createdAt":"2022-02-08T23:28:15Z"}
{"state":"Merged","mergedAt":"2022-09-01T19:22:45Z","number":2840,"mergeCommitSha":"3f2a856f0bd6349cc1f2f76e4024b7e4c045fdf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2840","title":"Do not update schemas for readonly replicas","createdAt":"2022-09-01T19:19:02Z"}
{"state":"Merged","mergedAt":"2022-09-01T19:27:45Z","number":2841,"body":"- suppress honeycomb\r\n- update\r\n","mergeCommitSha":"1e01c7da5816bface68b642d763278b45064df05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2841","title":"SuppressMoreHoneycomb","createdAt":"2022-09-01T19:27:28Z"}
{"state":"Merged","mergedAt":"2022-09-01T20:11:24Z","number":2842,"body":"bug reported https://chapter2global.slack.com/archives/C02HEVCCJA3/p1662060619206119","mergeCommitSha":"dd0ed41d54f2f62c6e18e11b4755b85065b4e107","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2842","title":"Dont use window location","createdAt":"2022-09-01T19:53:37Z"}
{"state":"Merged","mergedAt":"2022-09-01T19:58:27Z","number":2843,"body":"Tested in dev:\r\n\r\n<img width=\"1548\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/188000986-31a0e330-faa1-4df9-98f5-2bbcf2848ea4.png\">\r\n","mergeCommitSha":"9e26060260d34a914c5db4afc515de5153b56740","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2843","title":"Pusher now uses readonly rds instance in prod","createdAt":"2022-09-01T19:55:35Z"}
{"state":"Closed","mergedAt":null,"number":2844,"body":"This reverts commit 9e26060260d34a914c5db4afc515de5153b56740.\r\n","mergeCommitSha":"54a0d945444f8338d08ec08db464c970768e07c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2844","title":"Revert \"Pusher now uses readonly rds instance in prod (#2843)\"","createdAt":"2022-09-01T20:01:22Z"}
{"state":"Merged","mergedAt":"2022-09-01T22:01:45Z","number":2845,"mergeCommitSha":"23701455ef245855359d30e7101ac6b24f5ac4be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2845","title":"Add friendly signup names to sender email","createdAt":"2022-09-01T21:22:03Z"}
{"state":"Merged","mergedAt":"2022-09-01T22:21:42Z","number":2846,"mergeCommitSha":"6d7a4e41b26d3a75e97220934b1ca3181e5abb1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2846","title":"update welcome email","createdAt":"2022-09-01T22:19:07Z"}
{"state":"Merged","mergedAt":"2022-09-01T22:39:02Z","number":2847,"mergeCommitSha":"6774dfd0158595c389d18d78f697f41c5ad0ec1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2847","title":"Address other welcome email","createdAt":"2022-09-01T22:38:36Z"}
{"state":"Merged","mergedAt":"2022-09-02T23:29:19Z","number":2848,"body":"Basic code lens support\r\n\r\n<img width=\"591\" alt=\"CleanShot 2022-09-01 at 15 57 52@2x\" src=\"https://user-images.githubusercontent.com/1553313/188026349-3ee9bff5-b71d-4d25-a885-881f18ba0734.png\">\r\n<img width=\"741\" alt=\"CleanShot 2022-09-01 at 15 57 44@2x\" src=\"https://user-images.githubusercontent.com/1553313/188026350-ab754b27-dd0b-4890-af5d-c42380d9f4c1.png\">\r\n","mergeCommitSha":"bc4260734da06dc9eecbbd2de270f630fc445390","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2848","title":"Unblocked Code lens","createdAt":"2022-09-01T22:58:10Z"}
{"state":"Merged","mergedAt":"2022-09-02T00:11:28Z","number":2849,"mergeCommitSha":"511113b52b87a03eb5d7185b0bb2f86757ed7692","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2849","title":"update","createdAt":"2022-09-02T00:10:43Z"}
{"state":"Merged","mergedAt":"2022-02-09T00:24:28Z","number":285,"body":"This pr does the following:\r\n1. Update generators to using kotlinx serialization.\r\n2. Add custom serializers for all the data types we need.\r\n3. Update code and tests to use kotlinx serialization.","mergeCommitSha":"7e8df69e94f87d90e1ecbb6e5309cf0ed19d850a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/285","title":"Move to kotlinx serialization","createdAt":"2022-02-09T00:10:08Z"}
{"state":"Merged","mergedAt":"2022-09-02T01:12:11Z","number":2850,"mergeCommitSha":"13951448d0b963166ad143c4c4896a3893befc8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2850","title":"Reduce dev honeycomb sampling","createdAt":"2022-09-02T01:11:08Z"}
{"state":"Merged","mergedAt":"2022-09-02T01:46:45Z","number":2851,"mergeCommitSha":"83db7994993fc7a1c98aef0aec2f0a395155251d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2851","title":"more honeycomb reductions","createdAt":"2022-09-02T01:46:35Z"}
{"state":"Merged","mergedAt":"2022-09-02T04:16:06Z","number":2852,"mergeCommitSha":"b6a082b3233b20feec493408252723aad6c0cdca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2852","title":"more sampling","createdAt":"2022-09-02T04:15:55Z"}
{"state":"Merged","mergedAt":"2022-09-02T14:09:55Z","number":2853,"mergeCommitSha":"1af8036e6f060a1e99b6e4335da4e1157e9c84b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2853","title":"Update honeycomb sample rates per environment.","createdAt":"2022-09-02T14:09:46Z"}
{"state":"Merged","mergedAt":"2022-09-02T21:36:38Z","number":2854,"body":"changes\n1. wait for sourcemark store to download all marks before looking up marks for file.\n2. wait for partial-recalculation to complete before run full-recalculation. previously\n   they both ran concurrently and the event loop was filled with full-recalculation jobs first.\n3. free the onSourceMarksInitialized array which was misused because it was misleading.","mergeCommitSha":"be67b4a87dc94cce41f890155e06daebf6dec6bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2854","title":"Sourcemark engine startup ordering changes","createdAt":"2022-09-02T17:21:37Z"}
{"state":"Merged","mergedAt":"2022-09-02T19:13:30Z","number":2855,"body":"Confirmed deployed:\r\nhttps://github.com/NextChapterSoftware/unblocked/runs/8161902675?check_suite_focus=true","mergeCommitSha":"fa5d9e3a600ffa07c51d3e3aebcdaa36e2d28500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2855","title":"Move out telemetry code from apiservice","createdAt":"2022-09-02T17:26:30Z"}
{"state":"Merged","mergedAt":"2022-09-02T18:00:23Z","number":2856,"body":"### Background\r\n- https://docs.proxyman.io/troubleshooting/couldnt-see-any-request-from-localhost-server\r\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1662073948072599\r\n\r\n### Result\r\nRequests appear to target `localhost.proxyman.io`:\r\n<img width=\"1159\" alt=\"Screen Shot 2022-09-02 at 10 48 01\" src=\"https://user-images.githubusercontent.com/1798345/188209600-2c8c1f11-7a31-4965-a7b1-e6e7e77d32e6.png\">\r\n\r\nThis works because `localhost.proxyman.io` is an alias for loopback address:\r\n```sh\r\n$> host localhost.proxyman.io\r\nlocalhost.proxyman.io has address 127.0.0.1\r\n```\r\n","mergeCommitSha":"2571ab38900e066edad3e14e71a60aaa824c90ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2856","title":"Proxyman can track local service stack requests","createdAt":"2022-09-02T17:33:28Z"}
{"state":"Merged","mergedAt":"2022-09-02T21:52:56Z","number":2857,"mergeCommitSha":"4e54746ee6714bd03bcddf7555105ac840edfcb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2857","title":"Introduce more efficient way to get files changed since a commit","createdAt":"2022-09-02T18:25:47Z"}
{"state":"Merged","mergedAt":"2022-09-02T22:48:41Z","number":2858,"body":"### Changes\r\n- Throttles itself to limit concurrent IO to filesystem and Git\r\n- Optimize data-locality by processing according to file path.\r\n\r\n### Result\r\n- CPU never goes above 30%\r\n- looks to be using 2 out of 8 performance cores on my Mac","mergeCommitSha":"f7c50f56354481f68d8499d41612e2c5401ef241","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2858","title":"SourceMark full recalculation running in VSCode is kinder to the local system","createdAt":"2022-09-02T19:48:58Z"}
{"state":"Merged","mergedAt":"2022-09-03T00:21:49Z","number":2859,"mergeCommitSha":"e8897acec6dd6ee1a78e625add7b9d36dbfdced5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2859","title":"Fix telemetryservier crashing","createdAt":"2022-09-02T20:01:05Z"}
{"state":"Merged","mergedAt":"2022-02-09T00:37:46Z","number":286,"body":"Jackson is dead.\r\nI repeat, Jackson is dead.","mergeCommitSha":"7b55a6cd5375ebf8696c0a221b585b36eedda6de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/286","title":"Move to kotlinx serialization for graphql","createdAt":"2022-02-09T00:31:42Z"}
{"state":"Merged","mergedAt":"2022-09-06T21:21:57Z","number":2860,"body":"<img width=\"500\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/188243298-2d6a6ac5-249c-4f64-a592-ffa58a3bcff2.png\">\r\n\r\n<img width=\"483\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/188243319-b2045b09-2c0a-482d-aa08-b8f6b42b404a.png\">\r\n","mergeCommitSha":"bed2361fa688a600ccc9dcd398a08fb0d28f3b8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2860","title":"Add unreads filter to vscode","createdAt":"2022-09-02T22:47:31Z"}
{"state":"Merged","mergedAt":"2022-09-02T23:13:06Z","number":2861,"mergeCommitSha":"034f93c680c35394ab31253095bdb8b1528bac20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2861","title":"Use PromiseUtils wait","createdAt":"2022-09-02T23:04:36Z"}
{"state":"Merged","mergedAt":"2022-09-07T03:30:20Z","number":2862,"body":"Fixes UNB-590\r\n\r\nAdd a bulk-loading thread store (for now called `ThreadStore`).\r\n\r\nRight now this is only used by the TextEditorSourceMarks code (ie, the editor gutters and the \"insights in current file\" panels).  The idea is that all loading of groups of individual threads (in discussion view and sidebar views) will eventually use this, so that there is only one place that is polling and fetching individual threads.\r\n\r\nThe goal is that this store will be a singleton responsible for fetching, caching, and distributing thread data everywhere.\r\n\r\nThere are a few moving parts here:\r\n\r\n* `ThreadStore` is the main store.  It has a single public method: `getStream`, which returns an object (`ThreadListStream`) containing a stream of threads, plus some properties/methods for controlling the stream.  Right now the only thing you can control is the set of threads being streamed, but eventually this could have paging controls and such.\r\n* `ThreadStore` contains a series of `TeamThreadStore`s, one per team.  `TeamThreadStore` does all of the actual fetching and channel polling for thread data for a single team.  It tracks which threads have been requested by any stream, and fetches data as needed.\r\n* `ThreadStore` also contains a series of `ThreadListStream` objects, so that all fetched thread data can be delivered to the streams that need them.\r\n\r\nThis is only the first bit.  Upcoming PRs will add:\r\n* Unit tests -- this should be relatively easy to unit-test, given a mock channel poller and bulk-thread loading API\r\n* Caching -- right now, when a stream is shut down, all data loaded from that stream will be dropped.  Adding a cache to this should be relatively easy.\r\n* Using this in other contexts: Discussion view and sidebar.","mergeCommitSha":"9f52269f1b631355d992a71a82daffa14a8c793f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2862","title":"Add bulk-loading thread store","createdAt":"2022-09-02T23:28:43Z"}
{"state":"Merged","mergedAt":"2022-09-07T20:55:14Z","number":2863,"body":"### Motivation \r\nThese models are no longer needed.\r\n\r\nMinor improvement impact:\r\n- saves some space\r\n- saves a few DB queries and creates\r\n\r\nRelated to this:\r\nhttps://linear.app/unblocked/issue/UNB-441/refactor-threadmodel-to-include-repoid\r\n\r\n\r\n### Plan\r\n\r\n_Prev change:_ (#2879)\r\n1. change the `DELETE` cascade to `SET_NULL` for SourceMarkGroup references\r\n2. make references to SourceMarkGroup nullable, needed for above\r\n3. remove code that creates groups or queries groups\r\n4. add migration code to DROP SourceMarkGroup columns\r\n5. add migration code to DROP SourceMarkGroup table\r\n\r\n_This change:_\r\n1. remove SourceMarkGroup columns schema\r\n1. remove SourceMarkGroup table schema\r\n\r\n_Then, in this order:_\r\n\r\n- [x] Deploy & Run migration to drop columns and tables in LOCAL\r\n- [x] Backup PROD\r\n  - \"Mahdi: I just kicked off a manual snapshot which took a couple of mins to create\"\r\n- [x] pause prod deploy\r\n- [x] Deploy to DEV\r\n- [x] confirm that schema and data look ok in DEV\r\n- [x] Run migration to drop columns and tables in DEV\r\n- [x] confirm that schema and data look ok in DEV\r\n- [x] unpause prod deploy\r\n- [x] Deploy to PROD\r\n- [x] Run migration to drop columns and tables in PROD","mergeCommitSha":"c11095e10d6488dd716efd4bc73138230d99d733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2863","title":"DROP SourceMarkGroup and SourceMarkAccess models","createdAt":"2022-09-02T23:56:27Z"}