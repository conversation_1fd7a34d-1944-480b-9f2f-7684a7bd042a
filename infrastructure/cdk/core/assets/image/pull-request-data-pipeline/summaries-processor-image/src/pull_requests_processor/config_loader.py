import os

from datetime import datetime
from dateutil.relativedelta import relativedelta
from pytz import utc

from pull_requests_processor.pull_request_constants import DEFAULT_MONTHS_BACK


class ConfigLoader:
    def __init__(self):
        # required values
        self.repo_http_clone_url = os.environ.get("PROCESS_REPO_HTTP_CLONE_URL")
        self.repo_id = os.environ.get("PROCESS_REPO_ID")
        self.org_id = os.environ.get("PROCESS_ORG_ID")
        self.environment = os.environ.get("ENVIRONMENT", default="prod")
        self.llm_endpoint_url = os.environ.get(
            "PROCESS_LLM_ENDPOINT_URL",
            default=f"https://ml.alb.{self.environment}.gcp.getunblocked.com/api/ml/transformers/llama-31-8B",
        )
        self.oldest_pr_number = int(os.environ.get("PROCESS_OLDEST_PR_NUMBER", default=0))
        oldest_datetime_str = os.environ.get("PROCESS_OLDEST_DATETIME")
        self.oldest_datetime = (
            datetime.fromisoformat(oldest_datetime_str)
            if oldest_datetime_str
            else datetime.utcnow() - relativedelta(months=DEFAULT_MONTHS_BACK)
        ).astimezone(tz=utc)

        # verify required values
        self._verify_loaded_correctly()

        # optional values
        self.repo_clone_auth = self._resolve_repo_clone_auth_url()

    def _resolve_repo_clone_auth_url(self):
        """
        Hack: AWS SageMager has a hard limit of 256 chars per var, so in order to bypass this limitation kotlin will
                split the clone auth token into separate fragments so that we can re-join them back.
        """
        fragments = []
        for i in range(0, 5):
            fragment = os.environ.get(f"PROCESS_REPO_CLONE_AUTH_{i}")
            if fragment:
                fragments.append(fragment)
            else:
                break
        if fragments:
            return "".join(fragments)

    def _verify_loaded_correctly(self):
        missing_vars = [
            f"PROCESS_{key.upper()}" for key, value in self.__dict__.items() if value is None or value == ""
        ]
        if missing_vars:
            raise ValueError(f"Expected the following environment variables to be set: {', '.join(missing_vars)}")
