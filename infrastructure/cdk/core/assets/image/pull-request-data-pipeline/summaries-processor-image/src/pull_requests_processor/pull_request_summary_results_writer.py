import json
import os

from more_itertools import chunked
from typing import List
from dataclasses import asdict, dataclass

from pull_requests_processor.path_constants import PROCESS_OUTPUT_DIRECTORY
from pull_requests_processor.pull_request_types import PullRequestSummaryResult


@dataclass(order=True)
class PullRequestSummaryOutput:
    summary: str
    number: int
    html_url: str
    merge_commit_sha: str


class PullRequestSummaryResultsWriter:
    def write(
        self,
        pull_request_summary_results: List[PullRequestSummaryResult],
        output_path: str = PROCESS_OUTPUT_DIRECTORY,
        chunk_size=500,
    ):
        pull_request_summary_outputs = [
            asdict(
                PullRequestSummaryOutput(
                    summary=prs.summary,
                    number=prs.pull_request.number,
                    html_url=prs.pull_request.html_url,
                    merge_commit_sha=prs.pull_request.merge_commit_sha,
                )
            )
            for prs in pull_request_summary_results
        ]
        chunks = list(enumerate(chunked(pull_request_summary_outputs, chunk_size)))

        for chunk_id, chunk in chunks:
            filename = f"output_{chunk_id}.json"  # Create a unique filename for each chunk
            filepath = os.path.join(output_path, filename)
            with open(filepath, "w") as f:
                json.dump(chunk, f)
