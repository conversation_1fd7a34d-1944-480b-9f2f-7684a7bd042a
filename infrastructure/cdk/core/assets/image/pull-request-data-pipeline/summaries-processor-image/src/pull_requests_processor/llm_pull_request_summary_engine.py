from typing import Optional

from langchain.chains.base import Chain
from langchain.chains.summarize import load_summarize_chain
from langchain.llms.sagemaker_endpoint import LL<PERSON>ontentHandler
from langchain.schema import Document

from langchain_core.runnables import RunnableSerializable
from llm_prompt_utils.llm_inference_endpoint import LLMInferenceEndpoint
from llm_prompt_utils.llm_prompt import LL<PERSON>rompt

from pull_requests_processor.pull_request_types import PullRequest, PullRequestSummaryResult
from pull_requests_processor.git_diff_types import GitDiffPartitions


class LLMPullRequestSummaryEngine:
    _llm_chain: RunnableSerializable
    _llm_summary_chain: Chain
    _llm_combine_prompt: LLMPrompt
    _llm_map_prompt: LLMPrompt
    _token_max: int

    def __init__(
        self,
        llm_combine_prompt: LLMPrompt,
        llm_map_prompt: LLMPrompt,
        content_handler: LLMContentHandler,
        llm_endpoint_url: str,
        max_tokens: int,
        max_new_tokens: int,
        top_p: float = 0.7,
        temperature: float = 0.01,
        repetition_penalty: float = 1.0,
        return_full_text: bool = False,
        model_kwargs: Optional[dict] = None,
    ):
        self._llm_map_prompt = llm_map_prompt
        self._llm_combine_prompt = llm_combine_prompt

        model_kwargs = model_kwargs or {}
        model_kwargs = {
            "max_new_tokens": max_new_tokens,
            "top_p": top_p,
            "temperature": temperature,
            "repetition_penalty": repetition_penalty,
            "return_full_text": return_full_text,
            **model_kwargs,
        }

        self._llm_inference_endpoint = LLMInferenceEndpoint(
            llm_endpoint_url=llm_endpoint_url,
            model_kwargs=model_kwargs,
            content_handler=content_handler,
        )

        self._llm_summary_chain = load_summarize_chain(
            llm=self._llm_inference_endpoint,
            chain_type="map_reduce",
            map_prompt=self._llm_map_prompt.get_prompt_template(),
            combine_prompt=self._llm_combine_prompt.get_prompt_template(),
            token_max=max_tokens,
            verbose=False,
        )

        self._llm_chain = self._llm_map_prompt.get_prompt_template() | self._llm_inference_endpoint

        self._token_max = max_tokens

    def predict(self, pull_request: PullRequest, git_diff_partitions: GitDiffPartitions) -> PullRequestSummaryResult:
        docs = [
            Document(page_content=git_diff_partition.git_diff) for git_diff_partition in git_diff_partitions.partitions
        ]

        summary = ""
        if len(docs) > 1:
            results = self._llm_summary_chain.invoke(
                input={"input_documents": docs, "token_max": self._token_max}, return_only_outputs=True
            )
            summary = results["output_text"]
        elif len(docs) == 1:
            results = self._llm_chain.invoke(input={"text": docs[0].page_content, "token_max": self._token_max})
            summary = results

        return PullRequestSummaryResult(
            summary=summary,
            pull_request=pull_request,
        )

    async def async_predict(
        self, pull_request: PullRequest, git_diff_partitions: GitDiffPartitions
    ) -> PullRequestSummaryResult:
        docs = [
            Document(page_content=git_diff_partition.git_diff) for git_diff_partition in git_diff_partitions.partitions
        ]

        summary = ""
        if len(docs) > 1:
            results = await self._llm_summary_chain.ainvoke(
                input={"input_documents": docs, "token_max": self._token_max}, return_only_outputs=True
            )
            summary = results["output_text"]
        elif len(docs) == 1:
            results = await self._llm_chain.ainvoke(input={"text": docs[0].page_content, "token_max": self._token_max})
            summary = results

        return PullRequestSummaryResult(
            summary=summary,
            pull_request=pull_request,
        )
