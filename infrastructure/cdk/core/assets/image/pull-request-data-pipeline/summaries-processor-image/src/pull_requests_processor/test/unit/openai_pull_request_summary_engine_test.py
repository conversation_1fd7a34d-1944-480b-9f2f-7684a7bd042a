import os
import unittest
from datetime import datetime

from pull_requests_processor.pull_request_types import PullRequest, PullRequestState
from pull_requests_processor.openai_pull_request_summary_engine import OpenAIPullRequestSummaryEngine


class TestOpenAIPullRequestSummaryEngineTest(unittest.TestCase):
    _pull_request_summary_engine = OpenAIPullRequestSummaryEngine()
    _pull_request = PullRequest(
        number=1,
        merge_commit_sha="blah",
        html_url="blah",
        title="blah",
        body="blah",
        state=PullRequestState.MERGED,
        created_at=datetime.fromisoformat("2021-12-14T19:01:41Z"),
        merged_at=datetime.fromisoformat("2021-12-14T19:01:41Z"),
    )

    @staticmethod
    def fixture_path(name):
        return os.path.join(os.path.dirname(__file__), "fixtures", "git", name)

    def test_large_diff(self):
        file = self.fixture_path("large_git_diff_1")
        with open(file) as f:
            git_diff = f.read()
            results = self._pull_request_summary_engine.predict(pull_request=self._pull_request, git_diff=git_diff)
            self.assertTrue(results)


if __name__ == "__main__":
    unittest.main()
