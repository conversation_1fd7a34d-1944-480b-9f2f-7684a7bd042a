{"state":"Merged","mergedAt":"2024-01-25T20:36:56Z","number":10317,"mergeCommitSha":"b4e91db6b10dcb57e13447e3d5e08e8b2937c447","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10317","title":"Small tweaks to expert function to improve answers","createdAt":"2024-01-25T19:17:12Z"}
{"state":"Merged","mergedAt":"2024-01-25T21:00:30Z","number":10318,"body":"Makes state transitions smooth.  Add `AnimatingContentResizer`, which animates a component to its children's content size.\r\n\r\nNote that the width isn't animated, because the width is more complicated -- when there is a snippet, the max width is defined by the parent bounds, whereas when the tutorial is shown, the width is content-defined.  We can probably figure that out, but it'd be complicated, and didn't seem worth the effort.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/281bc84f-789d-4b24-874b-a827252ab9e7\r\n\r\n\r\n","mergeCommitSha":"ab74d2ed14541c0f6f9e3fdfc86857940c470740","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10318","title":"Add animations in 'Ask a Question' IDE UI","createdAt":"2024-01-25T20:02:15Z"}
{"state":"Merged","mergedAt":"2024-01-25T20:35:26Z","number":10319,"mergeCommitSha":"09c3dc837aadec9bdf401271e8aaea8425840cd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10319","title":"Handle null message returns","createdAt":"2024-01-25T20:13:32Z"}
{"state":"Merged","mergedAt":"2022-04-26T23:08:40Z","number":1032,"body":"Will have to move awy from this hack soon.\r\n","mergeCommitSha":"3953296b216b49edf97551ed5adf9e2748cc7e71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1032","title":"Hack fix","createdAt":"2022-04-26T23:02:26Z"}
{"state":"Merged","mergedAt":"2024-01-25T20:53:28Z","number":10320,"body":"Will experiment with similarity matching algorithms","mergeCommitSha":"dd304019eb9591f1fd001f7aa5c5d0e12e2841b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10320","title":"Limit the number of repos passed to the repo focus step (for now)","createdAt":"2024-01-25T20:39:23Z"}
{"state":"Merged","mergedAt":"2024-01-25T21:28:34Z","number":10321,"mergeCommitSha":"6f386a7dbcce5b746be55b2ab31ced0f1c6e1b03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10321","title":"Decrypt GitLab application secret for validation","createdAt":"2024-01-25T21:07:29Z"}
{"state":"Merged","mergedAt":"2024-01-25T21:48:01Z","number":10322,"body":"Went back to btoa and atob while figuring things out\n","mergeCommitSha":"853ef4468ea98a9d552772bd960f61fa6133d6eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10322","title":"Fix cryptoutils","createdAt":"2024-01-25T21:13:55Z"}
{"state":"Merged","mergedAt":"2024-01-25T21:42:12Z","number":10323,"mergeCommitSha":"055d7a1baf4abaf22cd11f8f64628fbe27e34615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10323","title":"Vend GitLab Application URL","createdAt":"2024-01-25T21:30:47Z"}
{"state":"Merged","mergedAt":"2024-01-25T22:44:39Z","number":10324,"mergeCommitSha":"11e3c036c06178a96aaaf81662743afdfc95ae27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10324","title":"Add reranker for expert functions + small tweaks","createdAt":"2024-01-25T22:21:38Z"}
{"state":"Merged","mergedAt":"2024-01-25T23:18:20Z","number":10325,"mergeCommitSha":"6b40bde9152a76eb70329487a8defb3c0bd0c077","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10325","title":"Add ability to list accessible shared google drives from admin console","createdAt":"2024-01-25T23:00:52Z"}
{"state":"Merged","mergedAt":"2024-01-25T23:21:13Z","number":10326,"body":"I wasn't paying attention and used vscode-specific colours in the base style.","mergeCommitSha":"958c873349d8c3ccd0c312f0de4a9d837c7a6c58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10326","title":"Fix 'Ask Question' theming in IntelliJ","createdAt":"2024-01-25T23:08:26Z"}
{"state":"Merged","mergedAt":"2024-01-25T23:28:44Z","number":10327,"body":"\r\n![CleanShot 2024-01-25 at 15 16 11@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/7df1408b-d3ff-427c-b1a6-13fe703d398c)\r\n","mergeCommitSha":"8d57ef1e2fde4bd4e9e54fef30d399ece92196f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10327","title":"Fix label alignment in explorer UI","createdAt":"2024-01-25T23:16:44Z"}
{"state":"Merged","mergedAt":"2024-01-26T00:28:19Z","number":10328,"mergeCommitSha":"f925b8ca9c98139cf1cb20f8c367ecfa914d2630","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10328","title":"Searching google shared drives by name should not be case sensitive","createdAt":"2024-01-25T23:42:44Z"}
{"state":"Merged","mergedAt":"2024-01-26T00:06:44Z","number":10329,"mergeCommitSha":"b68c7a20fdca756cf99d2368e4bedb3e2427cb2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10329","title":"[SKIP TESTS] Fix getSharedDrives route in admin console","createdAt":"2024-01-25T23:50:02Z"}
{"state":"Merged","mergedAt":"2022-04-26T23:28:12Z","number":1033,"body":"Removes `/api/...` prefix which is no longer necessary ","mergeCommitSha":"7420cc9167273b297b9785b2cb45ac4516f466d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1033","title":"Remove admin webroot prefix path","createdAt":"2022-04-26T23:05:04Z"}
{"state":"Merged","mergedAt":"2024-01-26T00:14:25Z","number":10330,"body":"Needed to facilitate chaining of more complex queries like \"what is the expert for this thing working on right now\"","mergeCommitSha":"ff6fe467bad06ef59b5402f5dfa4f4a45c2a156a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10330","title":"Add ML step for name extraction","createdAt":"2024-01-26T00:00:33Z"}
{"state":"Merged","mergedAt":"2024-01-26T00:43:22Z","number":10331,"mergeCommitSha":"0500ba8ac0cb96f83fd91c8e3bc71d4dc68b47e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10331","title":"[SKIP TESTS] Only print out names of the shared drives","createdAt":"2024-01-26T00:33:40Z"}
{"state":"Merged","mergedAt":"2024-01-26T04:04:27Z","number":10332,"body":"Same algorithm used in client and server:\r\n\r\n- RSA-OAEP\r\n- SHA-256\r\n\r\nAlso, rotate all the keys, and use default key size (4096).","mergeCommitSha":"bf186f16ae0d5b33ac87869c152a19d4a0425220","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10332","title":"Introduce RSAClientServerCryptoSystem","createdAt":"2024-01-26T00:51:35Z"}
{"state":"Merged","mergedAt":"2024-01-29T18:40:19Z","number":10333,"body":"Note that the first state here is currently disabled, because there is no great way to determine this state (when no document-based integrations are installed).  Will figure that out tomorrow.\r\n\r\n![CleanShot 2024-01-25 at 16 49 13@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/f5b9710a-d0be-41eb-98e3-222af8f9bd54)\r\n![CleanShot 2024-01-25 at 16 49 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/34589731-9b08-46a1-b729-6f07f8d6bbdb)\r\n![CleanShot 2024-01-25 at 16 49 50@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/0c2a70b8-d05c-49bc-9723-9039531fdf79)\r\n![CleanShot 2024-01-25 at 16 50 28@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/5855551d-7756-449e-bb01-917d62b5a994)\r\n","mergeCommitSha":"8c8eefd1f650dcbbb94fe74d535ea8ae07d7caa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10333","title":"Explorer UI 'No Data' status UIs","createdAt":"2024-01-26T00:54:29Z"}
{"state":"Merged","mergedAt":"2024-01-26T01:56:53Z","number":10334,"body":"Into spot No. 1!\r\n\r\n![CleanShot 2024-01-25 at 17 43 16@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/d668ba42-49df-456b-82bd-31a0bc16854e)\r\n","mergeCommitSha":"06857ad3ea6a71b485dfbae0b3e57234bba6bf4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10334","title":"Add drata quote to landing page","createdAt":"2024-01-26T01:44:44Z"}
{"state":"Merged","mergedAt":"2024-01-26T02:33:19Z","number":10335,"mergeCommitSha":"d74050925582c302df09532c2764abb968173195","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10335","title":"[SKIP TESTS] - small prompt tweak for expert function","createdAt":"2024-01-26T02:32:55Z"}
{"state":"Merged","mergedAt":"2024-01-26T04:40:32Z","number":10336,"mergeCommitSha":"c2239368a4df522755297624a072a943506dd066","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10336","title":"[SKIP TESTS] - swap take and distinct","createdAt":"2024-01-26T03:18:14Z"}
{"state":"Merged","mergedAt":"2024-01-26T21:53:37Z","number":10337,"mergeCommitSha":"9cf97bda25df26799852c3b3c0f12f6111478fd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10337","title":"add fine-tune export to filewriter","createdAt":"2024-01-26T04:44:11Z"}
{"state":"Merged","mergedAt":"2024-01-26T06:57:15Z","number":10338,"mergeCommitSha":"9532a114242d0c48680820f2df9d6a13ce6304ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10338","title":"Refactor enterprise host availability","createdAt":"2024-01-26T06:41:58Z"}
{"state":"Merged","mergedAt":"2024-01-26T21:44:35Z","number":10339,"mergeCommitSha":"d64ef2a1cd2f0cb6e4befa2997d84d8983a08cc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10339","title":"Introduce abstract EnterpriseAppConfig data model to replace GitHubAppConfig","createdAt":"2024-01-26T07:52:06Z"}
{"state":"Merged","mergedAt":"2022-04-26T23:28:48Z","number":1034,"mergeCommitSha":"1645b982baa09640f9ea56b9f24fc248ec24076a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1034","title":"Run adminweb service using docker locally","createdAt":"2022-04-26T23:07:49Z"}
{"state":"Merged","mergedAt":"2024-01-26T22:09:41Z","number":10340,"mergeCommitSha":"eb1bb9d8532d937f85b33e73553efc1f4b6bb1b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10340","title":"Hook up Enterprise App persistence to API","createdAt":"2024-01-26T08:16:21Z"}
{"state":"Merged","mergedAt":"2024-01-26T17:14:40Z","number":10341,"body":"Somewhat simplified code \uD83E\uDD37 and additional tests (moving to BouncyCastle implementation).\r\nThat said, as long we don't use forge or other libraries in js land, we're stuck with this bs, as OAEP padding is forced on us othewrise.\r\n\r\nFeel free to close, but thought I should run this by you.\r\nAlso, can we get rid of the AES.*Engines?\r\nAre we planning for any symmetric encryption?\r\n\r\n","mergeCommitSha":"f8f654ddc2ce7c1883146c355dd60b930c7bff93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10341","title":"Minor code cleanup for oaep","createdAt":"2024-01-26T11:00:16Z"}
{"state":"Merged","mergedAt":"2024-01-26T19:39:12Z","number":10342,"body":"For Unblocked slack questions, we should be notifying customer channels if validation is toggled on.","mergeCommitSha":"5ed8001b5d0899add7464b19dfb2d789df2522a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10342","title":"Fix up slack notifications for customers","createdAt":"2024-01-26T19:19:32Z"}
{"state":"Merged","mergedAt":"2024-01-31T21:05:20Z","number":10343,"body":"Support installing the VSCode plugin into the Cursor IDE.\r\n\r\nAs part of this, I removed all of the IDE icons -- we aren't actually using them at all.  We can re-add if necessary later.","mergeCommitSha":"8150d5217cd9c2a651bb863073a5ea460339e997","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10343","title":"Support Cursor IDE","createdAt":"2024-01-26T19:29:57Z"}
{"state":"Merged","mergedAt":"2024-01-29T22:30:59Z","number":10344,"body":"\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/5294cc99-e0d2-4e94-9625-b85ba9533a28\r\n\r\n","mergeCommitSha":"f0e748fbbbaa277483e003ba878af8aef7233fdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10344","title":"Remove full-height scrollbar from explorer panel","createdAt":"2024-01-26T19:42:19Z"}
{"state":"Merged","mergedAt":"2024-01-29T20:03:34Z","number":10345,"body":"The `listIntegrationInstallationsV2` operation","mergeCommitSha":"1dd17fae20d66d2674da1550613b7c49e5732bda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10345","title":"Push channel for `/teams/{teamId}/installationsV2`","createdAt":"2024-01-26T20:10:17Z"}
{"state":"Merged","mergedAt":"2024-01-26T22:21:17Z","number":10346,"mergeCommitSha":"ec461fa5f0f06e5564e963716203a71bf8e37cbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10346","title":"Show GitLab apps in admin web integrations page","createdAt":"2024-01-26T20:58:10Z"}
{"state":"Merged","mergedAt":"2024-01-26T22:23:40Z","number":10347,"mergeCommitSha":"b2c91af7c87621964640fd81856a839f47d9db44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10347","title":"Remove dead app affecting build times","createdAt":"2024-01-26T21:59:10Z"}
{"state":"Merged","mergedAt":"2024-01-26T22:47:21Z","number":10348,"mergeCommitSha":"a3806c1fbd17c8b27896ef57c7ba452a81706685","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10348","title":"GitLab Enterprise OAuth support","createdAt":"2024-01-26T22:23:00Z"}
{"state":"Merged","mergedAt":"2024-01-26T23:10:35Z","number":10349,"mergeCommitSha":"ea0b549b04342631b1814f97e44a9ed33bb2b31f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10349","title":"Fix GitLab Enterprise Admin Web","createdAt":"2024-01-26T22:23:04Z"}
{"state":"Closed","mergedAt":null,"number":1035,"mergeCommitSha":"28ab444c965c4b888812c45bf9540d3b1659351f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1035","title":"Add adminweb team pages","createdAt":"2022-04-26T23:15:59Z"}
{"state":"Merged","mergedAt":"2024-01-26T23:03:59Z","number":10350,"mergeCommitSha":"07e8cc3de1949225c77fdee345b7c0e65af07baf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10350","title":"Another stab at improving topic mapping","createdAt":"2024-01-26T22:49:19Z"}
{"state":"Merged","mergedAt":"2024-01-29T18:52:57Z","number":10351,"body":"Our existing `filterReady` operator only worked for Loading/Ready discriminating unions (derived from `LoadableDataStreamState`).  It couldn't be used if there were any other union cases.\r\n\r\nThis PR makes a more generic `filterCase`, which filters a set down to a particular case, and a `filterReady`, which filters a generic `ready` case.","mergeCommitSha":"0eb28159c87c5d62f3985e66af4d293dc8e341de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10351","title":"Better 'filterReady' stream operator","createdAt":"2024-01-26T23:38:24Z"}
{"state":"Merged","mergedAt":"2024-01-27T03:07:56Z","number":10352,"mergeCommitSha":"2e75c92a4ba6f55b4064be2f4e6b611403c8c23b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10352","title":"Enterprise validation handles temporarily unavailable","createdAt":"2024-01-26T23:51:45Z"}
{"state":"Merged","mergedAt":"2024-01-27T03:09:26Z","number":10353,"mergeCommitSha":"f7fefc9463127fce965d0982d78522d818f525b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10353","title":"Expose multiple validation error at once","createdAt":"2024-01-27T00:03:15Z"}
{"state":"Merged","mergedAt":"2024-01-27T03:10:29Z","number":10354,"body":"Fixes bug where entering a GitHub Enterprise host on the GitLab host connection page would log into the existing GitHub Enterprise instance. And visa-versa.","mergeCommitSha":"537ab5979b673dc5b303d47a252a85bd729844c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10354","title":"Only return requested enterprise apps in `findEnterpriseProviders`","createdAt":"2024-01-27T00:20:05Z"}
{"state":"Merged","mergedAt":"2024-01-31T06:21:42Z","number":10355,"mergeCommitSha":"7341a39d46174081dc97c2c56c8160bc6ecef3f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10355","title":"Increase max message size for events used for embedding documents","createdAt":"2024-01-27T00:39:46Z"}
{"state":"Merged","mergedAt":"2024-01-27T05:03:47Z","number":10356,"mergeCommitSha":"9647e8f950e1e29679ab80784d5e12b73f3e4465","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10356","title":"Decrypt GitLab Self-hosted OAuth secret when doing OAuth exchange","createdAt":"2024-01-27T04:44:58Z"}
{"state":"Merged","mergedAt":"2024-01-27T05:23:31Z","number":10357,"body":"Allow:\n```\ngitlab.secops.getunblocked.com\n```","mergeCommitSha":"d9e8a8afcf05724ce4adfa81196dee77d0815840","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10357","title":"Restricted access applies to GitLab, and allow our test instance","createdAt":"2024-01-27T04:45:02Z"}
{"state":"Merged","mergedAt":"2024-01-28T00:37:09Z","number":10358,"mergeCommitSha":"ccf1793d6263325339c8646c2f2d212d53d42917","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10358","title":"Identities are enterprise-provider specific","createdAt":"2024-01-27T05:46:14Z"}
{"state":"Merged","mergedAt":"2024-01-27T08:31:54Z","number":10359,"mergeCommitSha":"b2d3fbbf2f96934de0627c0274d55f31bf1bc174","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10359","title":"[SKIP TESTS] Add ability to trigger ingest of google shared drives from admin console","createdAt":"2024-01-27T08:08:36Z"}
{"state":"Merged","mergedAt":"2022-04-27T20:21:10Z","number":1036,"body":"<img width=\"1282\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165409272-901e6215-0a21-47c0-a34a-e5fa863443df.png\">\r\n<img width=\"933\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165409382-f87b5137-dbfa-49bb-9cca-0f3f0aca7d5f.png\">\r\n","mergeCommitSha":"1f4a40b15cd2423c85dac3fa054b3b93b513f56a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1036","title":"Add selected state to treeviews","createdAt":"2022-04-26T23:32:24Z"}
{"state":"Merged","mergedAt":"2024-01-29T21:20:47Z","number":10360,"body":"\r\nThis UI is only meant to be displayed when you have no \"long form doc\" integrations installed.  This PR hooks this up:\r\n\r\n* Add `IntegrationInstallationStore`, a store for getting and monitoring the installations on a team (updated via pusher)\r\n* Add `ActiveFileInstallationStateStream`, which determines the installation \"long form docs available\" state for the current file's team\r\n* Hook this state up via the explorer UI\r\n\r\n![CleanShot 2024-01-27 at 15 31 37@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/e308463e-3968-4373-ae4b-d5917efee943)\r\n\r\n","mergeCommitSha":"ed4ded1399c39d02c8e7ea07d3fb79264cc7c026","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10360","title":"Connect data to the UI for the 'Connect the Systems you Use' Explorer UI","createdAt":"2024-01-27T23:31:17Z"}
{"state":"Merged","mergedAt":"2024-01-28T06:57:58Z","number":10361,"body":"Final change to get GitLab Self-hosted onboarding fully working.\r\n\r\n<img width=\"800\" alt=\"Screenshot 2024-01-27 at 22 44 49\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/9616213f-aba7-4404-8d39-d20f91c3cc56\">\r\n<img width=\"800\" alt=\"Screenshot 2024-01-27 at 22 44 58\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/2dcf8f46-4296-4d0e-ac8b-e88cc3ed3198\">\r\n<img width=\"800\" alt=\"Screenshot 2024-01-27 at 22 47 19\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/3727c012-cd1e-4f49-8f61-79f2011a7a46\">\r\n","mergeCommitSha":"f981f789ed4a5040eee103c9b0eb33059f43c55c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10361","title":"Add ScmTeamApi support for GitLab","createdAt":"2024-01-28T04:24:40Z"}
{"state":"Merged","mergedAt":"2024-01-28T21:30:56Z","number":10362,"body":"These resource fields are always the same, so there's no value adding them to every event.\r\n\r\nhttps://ui.honeycomb.io/unblocked/environments/production/result/CzSufpRLCaD","mergeCommitSha":"2cbb3d2c31e2da3a7a085068fb59a61e0c30eee7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10362","title":"Remove Open Telemetry fields that are invariant","createdAt":"2024-01-28T21:00:45Z"}
{"state":"Merged","mergedAt":"2024-01-28T21:45:43Z","number":10363,"mergeCommitSha":"64881b68e2ed371046c7404fe65bb594ac3925f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10363","title":"Add ScmRepoApi support for GitLab","createdAt":"2024-01-28T21:30:29Z"}
{"state":"Merged","mergedAt":"2024-01-28T22:11:47Z","number":10364,"mergeCommitSha":"b9182e8f5bf0cd192316c5cca85bd55058ae87a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10364","title":"Cleanup TODOs in SCM client code","createdAt":"2024-01-28T22:09:55Z"}
{"state":"Merged","mergedAt":"2024-01-28T22:55:13Z","number":10365,"body":"We burn a lot of money on CI.\n\nThis change, which only applies to PRs within workflows that are PR triggered, will cancel previously running, queued, and pending jobs.\n\nSee:\nhttps://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value","mergeCommitSha":"728ff6958148f576993f00bc147b4caefb3449a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10365","title":"Auto-cancel old PR CI jobs in GitHub Actions","createdAt":"2024-01-28T22:40:31Z"}
{"state":"Merged","mergedAt":"2024-01-28T23:49:03Z","number":10366,"mergeCommitSha":"d3b6a7ada3b77bc686ea5fabdeb133a7bbae6a68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10366","title":"Fix references to GitLab source files in answers for sub-group repos","createdAt":"2024-01-28T23:15:51Z"}
{"state":"Merged","mergedAt":"2024-01-29T05:02:12Z","number":10367,"mergeCommitSha":"3d79021b0f71cfd484b1c55877137ec89bd5cc1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10367","title":"Support GitLab Self-hosted in IDE installation flow","createdAt":"2024-01-29T00:04:30Z"}
{"state":"Merged","mergedAt":"2024-01-29T05:44:57Z","number":10368,"mergeCommitSha":"7ec76a100f8b996996bf157dc81cfb0f6122f81e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10368","title":"Upgrade dorny/paths-filter to v3.0.0 to avoid GitHub NodeJS deprecation","createdAt":"2024-01-29T05:40:46Z"}
{"state":"Merged","mergedAt":"2024-01-31T20:07:42Z","number":10369,"body":"Winston.js has many issues, this PR fixes a few of them:\r\n\r\n* Any arguments to pass in after the second arg in `logger.error(first, second, third...)` would be ignored.  This properly flattens and logs them, the same as it does with the second arg.\r\n* If the second (or any subsequent) argument is a number, it would be dropped, if it was a string, it would get logged as a bunch of goofy indexed characters.  This will log all simple values correctly as `value`, `value2`, `value3` fields.\r\n* Exception stack traces were sometimes dropped.\r\n\r\nI'm wondering if we should drop Winston for something newer and smaller.  Winston seems like a dead project and has a lot of weird undefined behaviour.","mergeCommitSha":"cd03c9983e232eb03ba2429914b33d5cfeafb68f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10369","title":"Fix client logging issues","createdAt":"2024-01-29T06:06:48Z"}
{"state":"Merged","mergedAt":"2022-04-27T21:23:48Z","number":1037,"body":"* Whenever any edit occurs, re-render the sourcemark icons immediately, with a temporarily-calculated set of SM locations.  This causes SM icons to update immediately to a reasonable place, and re-renders the spacing on all lines, which fixes pretty much all of the editor jank.\r\n* As before, debounce a run of the SM engine -- this gets applied afterwards.  The end result is that the SM icons update quickly, then \"snap\" to a final location if the edit is complicated and the SM algorithm came to a different conclusion.","mergeCommitSha":"0230c1e79072871cf0ba0d9e108ff844f8afa732","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1037","title":"Improved SM icon behaviour in VSCode text editor","createdAt":"2022-04-27T00:16:33Z"}
{"state":"Merged","mergedAt":"2024-01-29T19:01:05Z","number":10370,"mergeCommitSha":"c91ab0ff3e9a0870aa04a9bb20aae9fdd7cd45f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10370","title":"Add badges","createdAt":"2024-01-29T18:52:22Z"}
{"state":"Merged","mergedAt":"2024-02-23T17:45:17Z","number":10371,"mergeCommitSha":"2b083b6eb20ea251596fba57094a3ccdd4343493","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10371","title":"Turn getAllByTeam into getIntegrationInstallations","createdAt":"2024-01-29T19:04:06Z"}
{"state":"Merged","mergedAt":"2024-01-29T20:51:31Z","number":10372,"body":"Removes topics from old general sidebar.\r\n\r\n![CleanShot 2024-01-29 at 11 09 14@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/6c5c4fb4-0216-4272-b2c7-7a630eb41e79)\r\n","mergeCommitSha":"8fcc8d3bc126e346598f82f2b04b765016a7e9f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10372","title":"Remove topics from old general sidebar and TopicStore","createdAt":"2024-01-29T19:19:13Z"}
{"state":"Merged","mergedAt":"2024-01-29T20:06:18Z","number":10373,"body":"When using the new UI, don't use the FileTopicStream (and its descendants) at all, so we don't use the `getRelatedTopics` API.  In the new UI we don't display this topic data at all.\r\n\r\nWe can remove all these streams after we ship.  Just leaving it here in case we need to back out to the old UI.","mergeCommitSha":"9699ebd48ece754026239c443c9d65ed9c27bd34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10373","title":"Don't call getRelatedTopics API in new Explorer UI","createdAt":"2024-01-29T19:53:50Z"}
{"state":"Merged","mergedAt":"2024-01-29T20:13:33Z","number":10374,"mergeCommitSha":"6aff9c6f4fef9aed024a6780db141c66c09ba518","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10374","title":"Migrate all existing standard teams to full mode","createdAt":"2024-01-29T20:02:13Z"}
{"state":"Merged","mergedAt":"2024-01-29T20:05:09Z","number":10375,"mergeCommitSha":"b6a8b0f907e9792863c1a1e89ed2e5a386476479","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10375","title":"Add logging","createdAt":"2024-01-29T20:04:59Z"}
{"state":"Merged","mergedAt":"2024-01-29T20:28:51Z","number":10376,"mergeCommitSha":"f0667d588d55910037dd3baa2c54d03bd3a42fd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10376","title":"Fix for `loginOptionsV3` API returning the wrong `registered` GitLab Self-hosted response","createdAt":"2024-01-29T20:09:59Z"}
{"state":"Merged","mergedAt":"2024-01-29T21:14:12Z","number":10377,"body":"Updating our JDK image (last one was 4 months old). This is to hopefully address a critical vulnerability founding perl. \r\n\r\n\r\nFYI, Rashin and I went through their docker tags. The versioning scheme is all over the place. This image is one of the builds from 4 days ago despite the version looking to be lower!","mergeCommitSha":"5a48694e1414bc79a867217d83aeecc17c86cda9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10377","title":"Update JDK docker image to address security findings","createdAt":"2024-01-29T20:31:08Z"}
{"state":"Merged","mergedAt":"2024-01-29T22:06:22Z","number":10378,"body":"Invite email will now include enterprise ID when navigating to login page.\r\nThis enterprise ID will be passed to `getLoginProviders` and prefill with targeted enterprise provider.","mergeCommitSha":"18bed60b19769f023c9da49efcfe61c061549cd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10378","title":"Invite enterprise","createdAt":"2024-01-29T21:13:47Z"}
{"state":"Merged","mergedAt":"2024-01-29T22:25:51Z","number":10379,"body":"Hides \"Show contexual insight bubbles\" subcommands in command palette ","mergeCommitSha":"d9c807bd8f33684f584919aa84db8cab9e1038ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10379","title":"Hide contextual commands in palette","createdAt":"2024-01-29T21:53:46Z"}
{"state":"Merged","mergedAt":"2022-04-27T18:31:54Z","number":1038,"body":"This:\r\n- Route cleanup\r\n- Search page\r\n- Repos and Repo pages\r\n- Members and Member pages\r\n- Users and User pages\r\n- Minimal Threads page\r\n\r\nRemaining:\r\n- Message pages\r\n- SourceMark page\r\n- Unreads page\r\n- Video pages\r\n\r\n---\r\n\r\n\r\n<img width=\"400\" alt=\"Repos\" src=\"https://user-images.githubusercontent.com/1798345/165552724-27b3f3f4-ede1-4f1c-8dfb-59ebd9d71783.png\">\r\n\r\n<img width=\"400\" alt=\"Members\" src=\"https://user-images.githubusercontent.com/1798345/165552822-24b8185f-b1c5-414f-ba53-47f5a1ffbb62.png\">\r\n\r\n<img width=\"400\" alt=\"Member\" src=\"https://user-images.githubusercontent.com/1798345/165552987-ae819e21-0ab5-4559-883a-374eb59547fd.png\">\r\n","mergeCommitSha":"93a5f7b3eeb5be852240954d168ee809a22cfda7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1038","title":"Add new admin web pages","createdAt":"2022-04-27T15:15:42Z"}
{"state":"Merged","mergedAt":"2024-01-29T22:18:45Z","number":10380,"body":"Proper implementation to come right after.","mergeCommitSha":"e79e6662e59678306c4aa77f201ea25437a3c6a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10380","title":"Add PullRequestIngestionStore.isInitialPullRequestIngestionCompleted","createdAt":"2024-01-29T21:55:35Z"}
{"state":"Merged","mergedAt":"2024-01-29T22:13:33Z","number":10381,"mergeCommitSha":"06dd6fbc67a81473c2066fba94c100c6a18b2422","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10381","title":"Remove excess logging from WebIngestionEventHandler","createdAt":"2024-01-29T22:02:32Z"}
{"state":"Merged","mergedAt":"2024-01-30T02:29:16Z","number":10382,"body":"Add basic framework for full team promotion.\r\n\r\nWill be removing code from other places if people are comfortable with this.","mergeCommitSha":"7bb724edc6cbc794ce5ecefdeb428a237a96d8af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10382","title":"Add full team promotion job","createdAt":"2024-01-29T22:36:08Z"}
{"state":"Merged","mergedAt":"2024-01-29T23:50:05Z","number":10383,"mergeCommitSha":"c9c96636d3f86f3eb4777c1520ab96de1c00a9ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10383","title":"Permit GitLab DEV login through locally running web client","createdAt":"2024-01-29T22:41:08Z"}
{"state":"Merged","mergedAt":"2024-01-29T23:24:38Z","number":10384,"mergeCommitSha":"1e05bd01dcbf6cf268750f38d1d24878a3a7bf48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10384","title":"Add IngestionStore.setInitialIngestionCompletedAtForAllIntegrations","createdAt":"2024-01-29T22:50:46Z"}
{"state":"Merged","mergedAt":"2024-01-29T23:36:36Z","number":10385,"body":"Equivalent to https://github.com/NextChapterSoftware/unblocked/blob/f8f654ddc2ce7c1883146c355dd60b930c7bff93/projects/libs/lib-crypto/src/test/kotlin/com/nextchaptersoftware/crypto/RSAClientServerCryptoSystemTest.kt#L17-L25\r\n","mergeCommitSha":"5478456f2db97374924537301e61cd4c2ab7f861","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10385","title":"Crypto utils test","createdAt":"2024-01-29T23:00:23Z"}
{"state":"Merged","mergedAt":"2024-01-30T00:17:36Z","number":10386,"mergeCommitSha":"5a978177ea0f1cf5dc6a284e73c372a91d0d58b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10386","title":"Backfill IngestionModel for Slack","createdAt":"2024-01-29T23:24:29Z"}
{"state":"Merged","mergedAt":"2024-01-30T00:31:28Z","number":10387,"mergeCommitSha":"631874258a489141f7204e0e7b6aa27058e28c53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10387","title":"Backfill IngestionModel for Web","createdAt":"2024-01-30T00:01:34Z"}
{"state":"Merged","mergedAt":"2024-01-30T00:54:27Z","number":10388,"mergeCommitSha":"08e09ecd69a581b089522de7f4b4628cfcb4a6bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10388","title":"Fix PullRequestIngestionStore.isInitialPullRequestIngestionCompleted","createdAt":"2024-01-30T00:20:04Z"}
{"state":"Merged","mergedAt":"2024-01-30T00:36:09Z","number":10389,"mergeCommitSha":"d7c527409f8bb0007b35c750907743bb6df6cad2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10389","title":"Submodule bump for new assets without components","createdAt":"2024-01-30T00:24:14Z"}
{"state":"Merged","mergedAt":"2022-04-27T18:40:28Z","number":1039,"body":"We need authorized asset support for web.\r\nTo that end;\r\n1. Add cors support for AssetService\r\n2. Add authorized context for base level web app.\r\n3. Generalize asset creation.","mergeCommitSha":"8a46a1e7c673c8499250a59ed4f97e5214c68bf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1039","title":"Add authorized asset support to web","createdAt":"2022-04-27T16:53:59Z"}
{"state":"Merged","mergedAt":"2024-01-30T00:54:59Z","number":10390,"body":"Supports a flow that allows a user to invite a collegue to a signin page that has the enterprise host pre-filled as a convenience.","mergeCommitSha":"c79d36ef14c668cd27dd807395c90bebb9ef75a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10390","title":"Supply dashboard invite URLs encoded with an enterprise provider ID","createdAt":"2024-01-30T00:28:56Z"}
{"state":"Merged","mergedAt":"2024-01-30T00:39:28Z","number":10391,"mergeCommitSha":"53e046c1836a14e796bbba442986048f8a8b071f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10391","title":"Fix GitLab Self-hosted override redirect URL","createdAt":"2024-01-30T00:39:02Z"}
{"state":"Merged","mergedAt":"2024-01-30T01:20:56Z","number":10392,"mergeCommitSha":"fb6fe69f984669897d019692ada74f6471ba1a40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10392","title":"Add offsets to enterprise flow","createdAt":"2024-01-30T01:07:15Z"}
{"state":"Merged","mergedAt":"2024-01-30T01:25:42Z","number":10393,"body":"- Add logging\r\n- openai\r\n","mergeCommitSha":"f6c689c7ad0868aa2b8059259b94fa3e5a68677a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10393","title":"MoreLogging","createdAt":"2024-01-30T01:25:24Z"}
{"state":"Merged","mergedAt":"2024-01-31T01:01:37Z","number":10394,"body":"Uses Statsig dynamic config to get a model id based on the team id and uses that to rerank.\r\n\r\nOnce we prove this works, the next step is a job that looks this up from the db, but haven't written the CDK to automatically kick off the fine-tuning process yet.","mergeCommitSha":"1bdd38ac43414ffc776490a1d611650f27db1f92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10394","title":"Adds Cohere fine-tuned inference","createdAt":"2024-01-30T01:40:51Z"}
{"state":"Merged","mergedAt":"2024-01-30T02:20:31Z","number":10395,"body":"- Revert \"MoreLogging (#10393)\"\r\n- Revert \"Add logging (#10375)\"\r\n- Revert \"Another stab at improving topic mapping (#10350)\"\r\n- Please work\r\n","mergeCommitSha":"536a319a49da0d5c9c5b27804619d4543e4651d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10395","title":"Trygain","createdAt":"2024-01-30T02:15:08Z"}
{"state":"Merged","mergedAt":"2024-01-30T03:18:22Z","number":10396,"body":"Was using DashboardUrls within landing page to redirect to `login`.\r\nIssue was that Landing bundle was treated as \"dashboard\" which meant `dashboard` prefix wasn't being added.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/ae8127116f09d674d00606c606f7ff25dc666b98/shared/webUtils/DashboardUrls.ts#L10\r\n\r\nUpdated webpack bundles to treat landing as a separate app type.","mergeCommitSha":"9ea8206eb7e6824730bbc3d0e465364a49e456ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10396","title":"Fix landing invite","createdAt":"2024-01-30T02:41:21Z"}
{"state":"Closed","mergedAt":null,"number":10397,"mergeCommitSha":"47282e8e5622f9e995c954925b044ac08170b232","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10397","title":"Remove unused code","createdAt":"2024-01-30T03:04:14Z"}
{"state":"Merged","mergedAt":"2024-01-30T16:42:31Z","number":10398,"body":"Modify prod.conf to add new teams if you want ot test stuff in prod\r\n","mergeCommitSha":"6539f8f4b5c8ec9908586708dde88721726c886b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10398","title":"Add selective teams for prod for team upgrade","createdAt":"2024-01-30T03:40:43Z"}
{"state":"Merged","mergedAt":"2024-01-30T05:47:48Z","number":10399,"body":"Changes:\r\n- add problem statement to _About Unblocked_ for context\r\n- add _About You_ section to emphasize that we are looking for developer first and foremost, with data engineering passion\r\n- level-up responsibilities to attract only senior engineers\r\n- concentrate on data processing, LLM knowledge is nice to have\r\n","mergeCommitSha":"f75ac35897c69e31038d446f1aaf82ce691ee233","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10399","title":"[RFC] Uplevel Data Engineer Requirements","createdAt":"2024-01-30T05:04:17Z"}
{"state":"Merged","mergedAt":"2022-01-21T23:36:41Z","number":104,"mergeCommitSha":"7f2ba6f00ffc8a616efe04b8679446071d080b9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/104","title":"Fix naming of tags to take into account runs...","createdAt":"2022-01-21T23:23:34Z"}
{"state":"Merged","mergedAt":"2022-04-27T19:27:51Z","number":1040,"body":"- Added AWS LocalStack with `S3`, `SQS` and `SES` services to docker compose templates \r\n- Added SES and SQS provider classes to AWS lib similar to what we have for S3. \r\n- Created lock provider for email notifications in Redis client lib \r\n- Created MockAWSProvider and a couple of helper classes for SES and SQS \r\n- Added the actual business logic to Email job to process messages from SQS and send emails using AWS SES \r\n- Reverted a few accidental changes pushed in from my local stash\r\n\r\nTests use localstack's SQS and SES services. SES service in localstack cannot send emails but we can inspect sent emails by looking at the filesystem inside the docker container. For the purpose of tests it is enough to check API response. \r\n\r\nThis is currently working but still needs some more work around email templates. I'll sync with Kay to figure out how we would like to approach email rendering. We might still stick to sending RAW emails and take care of html content generation in our code instead of using SES built-in functions. ","mergeCommitSha":"40f318b9ee93f2ffbf9839b45c7ab9dd81b8ac0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1040","title":"Add notification svc logic","createdAt":"2022-04-27T17:24:30Z"}
{"state":"Merged","mergedAt":"2024-01-30T17:39:24Z","number":10400,"mergeCommitSha":"02a97525aa69bc3b001f2d4813348e53c53c0c0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10400","title":"Enable GitLab Self-hosted in PROD","createdAt":"2024-01-30T05:22:20Z"}
{"state":"Merged","mergedAt":"2024-01-30T06:29:33Z","number":10401,"body":"The `/integrations` endpoint does not work outside of onboarding.  Use `/team-settings` for now, this will link to the connected SCM settings.","mergeCommitSha":"ab9b17c0e538b58762452fe338b484c2b5ef6ca4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10401","title":"Fix explorer UI link","createdAt":"2024-01-30T06:17:45Z"}
{"state":"Merged","mergedAt":"2024-01-30T09:36:36Z","number":10402,"mergeCommitSha":"36236acaf635377edc611b054f3f49c055a34de1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10402","title":"Allow Dev and Prod to approve their own peering requests under sec-ops","createdAt":"2024-01-30T09:35:51Z"}
{"state":"Merged","mergedAt":"2024-01-30T17:12:25Z","number":10403,"mergeCommitSha":"2b11a08e5b94168cdf242c73eeb817ce0d1eb65e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10403","title":"Fix up team promotion from ingeestion service","createdAt":"2024-01-30T16:53:11Z"}
{"state":"Merged","mergedAt":"2024-01-30T17:20:28Z","number":10404,"mergeCommitSha":"d97a0f5cff93cd5375ad97146524696026927c08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10404","title":"Fix exceptions in topic ingestion","createdAt":"2024-01-30T17:20:17Z"}
{"state":"Merged","mergedAt":"2024-01-30T18:25:09Z","number":10405,"mergeCommitSha":"e485f198b24d04282c4cf8bf48ac98cbd9528980","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10405","title":"Show ingested counts from repo page","createdAt":"2024-01-30T17:21:35Z"}
{"state":"Merged","mergedAt":"2024-01-30T18:25:01Z","number":10406,"mergeCommitSha":"56a7dd7094b1bd7158535025921aeb576459d803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10406","title":"Fix prompt","createdAt":"2024-01-30T18:24:55Z"}
{"state":"Merged","mergedAt":"2024-01-30T18:31:47Z","number":10407,"mergeCommitSha":"11315495e0bb794a22ec3371c14f01fa23626ade","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10407","title":"[SKIP TESTS] - Fix installer for phantom user ownership scenario","createdAt":"2024-01-30T18:31:03Z"}
{"state":"Merged","mergedAt":"2024-01-30T20:04:30Z","number":10408,"body":"Prefills manifest form using data from cache only when coming from IDE or Hub client. \r\nWe depend on preauth existing to determine if origin is client.\r\n\r\n![CleanShot 2024-01-30 at 10 25 19@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/b1f1973f-30f2-4389-8c79-76ba88c66b99)\r\n","mergeCommitSha":"0c03eabc008241f0ac160be6f9ab69b1b733d374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10408","title":"Prefill login manifest from Clients","createdAt":"2024-01-30T18:37:43Z"}
{"state":"Merged","mergedAt":"2024-01-30T19:27:05Z","number":10409,"body":"Remove experts tooltip in tutorial.\r\nWhen steps <= 1, do not show step count.\r\n\r\n![CleanShot 2024-01-30 at 11 08 25@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/5da358f4-6126-4d5f-98af-285bc9861728)\r\n\r\n","mergeCommitSha":"cb1abdb3a0b625e7bbcf54afbcd49895da535677","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10409","title":"Update onboarding tooltip","createdAt":"2024-01-30T19:10:11Z"}
{"state":"Merged","mergedAt":"2024-01-30T19:59:06Z","number":10410,"body":"We upgade anything that isn’t standard to full and ensure they’re enabled.\r\n","mergeCommitSha":"c482f7cc19355809c0ec28db5550de7a2826481b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10410","title":"[SKIP TESTS] Always full team mode","createdAt":"2024-01-30T19:17:24Z"}
{"state":"Merged","mergedAt":"2024-01-30T19:37:19Z","number":10411,"mergeCommitSha":"b66dacc67690ce5082e4cd0ce3320a3091d2b3b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10411","title":"Always show team bumper","createdAt":"2024-01-30T19:25:26Z"}
{"state":"Merged","mergedAt":"2024-01-30T19:56:05Z","number":10412,"mergeCommitSha":"c8c4586b3d2b822950f95d4e215b98aa1f53f0ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10412","title":"Increase interval","createdAt":"2024-01-30T19:54:46Z"}
{"state":"Merged","mergedAt":"2024-01-30T20:23:00Z","number":10413,"body":"All teams and users of an inside enterprise hosts are treated as insiders.","mergeCommitSha":"3a3cb0537119310316a2caf74341ea1f15201894","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10413","title":"Account for Enterprise hosts in metrics","createdAt":"2024-01-30T19:58:31Z"}
{"state":"Merged","mergedAt":"2024-01-30T20:15:05Z","number":10414,"mergeCommitSha":"469b0a88013513f5df99acb55abb92c273cd6b20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10414","title":"Run on 5 minute interval for team promotion","createdAt":"2024-01-30T20:07:01Z"}
{"state":"Merged","mergedAt":"2024-01-31T03:03:38Z","number":10415,"body":"## Changes\n\nClient UIs will now show \"hostname.com\" instead of \"hostname.com:433\", because it's unneccessary.\n\nClient UIs will still show \"hostname.com:678\", because 678 is a non-default secure port.","mergeCommitSha":"21573e838f93a3685695371e5a7d7a0df0ff087c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10415","title":"Drop default secure port (433) for SCM Enterprise hostname display","createdAt":"2024-01-30T20:08:11Z"}
{"state":"Merged","mergedAt":"2024-01-30T20:27:18Z","number":10416,"body":"- Removed hard dependency between network stack and EKS network stack using SSM\r\n- Fixed peering connection name tag\r\n- Re-deployed stacks locally to unwrap the dependency mess!\r\n- Added support for cross account peering and creation of routes in requester account\r\n\r\nNote: Routes in accepter account still require manual creation","mergeCommitSha":"13e600ad09e189ed08b91a044a4d7b9c23091320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10416","title":"Peer new eks vpc with vpn dev","createdAt":"2024-01-30T20:26:34Z"}
{"state":"Merged","mergedAt":"2024-01-30T21:00:24Z","number":10417,"body":"Previous PR only showed bumper in non-github situations.\r\nUpdated to always show bumper.","mergeCommitSha":"d0f770c00b781ff5e1669af92b1022c0de681d69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10417","title":"Always show team bumper","createdAt":"2024-01-30T20:48:04Z"}
{"state":"Merged","mergedAt":"2024-01-30T20:49:46Z","number":10418,"mergeCommitSha":"1abb900c998c2bdf62839e8063cb418976e34d57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10418","title":"[SKIP TESTS] Promotion only for teams tht exist","createdAt":"2024-01-30T20:49:36Z"}
{"state":"Merged","mergedAt":"2024-01-30T21:40:13Z","number":10419,"body":"The `download_installer_body` element needs to be there for the layout to be correct","mergeCommitSha":"94eb8a716ffe6232fde2ad98c190873006f411f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10419","title":"Fix download page \"downloading\" state","createdAt":"2024-01-30T21:27:57Z"}
{"state":"Merged","mergedAt":"2022-04-27T22:56:56Z","number":1042,"body":"If you try to render to a Webview while it is not visible, nothing happens.  The rendered state will be lost.  This was happening on startup when the sidebar view was loading, because we would force it to display, and then be hidden.\r\n\r\nWith this PR, when we have new data to render, if the webview is hidden, we will not render, but instead we will hold onto the props.  When the webview is made visible, we will render with those props we held onto.","mergeCommitSha":"228d45a2768715a04d2a5dd5e50250d6982f2a9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1042","title":"Fix sidebar data loading","createdAt":"2022-04-27T21:09:58Z"}
{"state":"Merged","mergedAt":"2024-01-30T22:06:11Z","number":10420,"mergeCommitSha":"9ca555e1f281a242acc32f36471ccf5231550ac7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10420","title":"[SKIP TESTS] Prod team promotion","createdAt":"2024-01-30T22:02:55Z"}
{"state":"Merged","mergedAt":"2024-01-30T22:37:05Z","number":10421,"mergeCommitSha":"53199fe6565c9b1ddc07cb4284d6e3aeeff00612","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10421","title":"Submodule bump - Remove audio track","createdAt":"2024-01-30T22:24:49Z"}
{"state":"Merged","mergedAt":"2024-01-30T23:12:24Z","number":10422,"mergeCommitSha":"ba4f9f46ae661da200b3f6e83602d448547e3b8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10422","title":"Getting rid of old code","createdAt":"2024-01-30T22:49:23Z"}
{"state":"Merged","mergedAt":"2024-02-05T22:23:45Z","number":10423,"mergeCommitSha":"4a0f827dd9d6d1c4c38507eb5e13acaa83dc59b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10423","title":"Mute AVPlayer audio for onboarding videos","createdAt":"2024-01-30T22:52:34Z"}
{"state":"Merged","mergedAt":"2024-01-31T01:23:15Z","number":10424,"body":"Proposal is to fix the correctness issue first, then address the performance glitch for very large teams.\r\n\r\n## Before: Personal teams\r\n<img width=\"1210\" alt=\"Screenshot 2024-01-30 at 14 57 56\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/5e270e40-67e0-49b3-a401-08b230a1ae8e\">\r\n\r\n## After: Personal teams\r\n<img width=\"1196\" alt=\"Screenshot 2024-01-30 at 14 58 09\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/148195df-9729-4938-9a26-28a126349674\">\r\n\r\n","mergeCommitSha":"07345536583b6a341d7751efe469aa255c6be45e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10424","title":"Fix several TS bugs that depended on Full mode","createdAt":"2024-01-30T22:56:37Z"}
{"state":"Merged","mergedAt":"2024-01-31T04:02:10Z","number":10425,"mergeCommitSha":"ffc3cfef99f722141cf5a37d9b4c0224752a71eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10425","title":"Remove toggles","createdAt":"2024-01-30T23:14:24Z"}
{"state":"Merged","mergedAt":"2024-01-30T23:39:43Z","number":10426,"body":"I missed one video that was still showing the old IDE designs. ","mergeCommitSha":"c9f3630734c3f8b80beadf96316c61f426892668","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10426","title":"Submodule bump - Update create walkthrough landing page video","createdAt":"2024-01-30T23:27:22Z"}
{"state":"Merged","mergedAt":"2024-01-31T02:13:31Z","number":10427,"mergeCommitSha":"7c817c72d622fbc82852b43ed667af58ea6c7f6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10427","title":"Efficient multi-account team member query","createdAt":"2024-01-31T00:25:22Z"}
{"state":"Merged","mergedAt":"2024-01-31T01:02:35Z","number":10428,"mergeCommitSha":"bf05f22303fdddd890d8379189fbff3fa7bcffeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10428","title":"List teams and people with release channel overrides","createdAt":"2024-01-31T00:25:58Z"}
{"state":"Merged","mergedAt":"2024-01-31T20:56:10Z","number":10429,"body":"Removes full/standard TeamMode from dashboard\nDemo is still kept around\n","mergeCommitSha":"058b68d1b45bbc619286e0d1796ecbe1077af570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10429","title":"Remove TeamModes from dashboard","createdAt":"2024-01-31T00:52:51Z"}
{"state":"Merged","mergedAt":"2022-04-30T22:05:54Z","number":1043,"body":"Currently, we only ingest threads from merged pull requests. We want to start ingesting threads from open pull requests.\r\n\r\nFor threads from open PRs, source points will be created with the hash of the original commit on the PR branch (vs threads from a merged PR, where we use the merge commit hash). \r\n\r\nAs open PRs are merged, we need to update the sourcemarks by adding a new original source point with the merge commit hash. We'll only do this for non-outdated threads (threads referring to code that hasn't changed).\r\n\r\nThis means that Sourcemarks can have multiple original source points (one for the pr branch, one for the main branch). This is blocked on the sourcemark agent being updated to handle this.","mergeCommitSha":"83d80102d91c45302d6236c76c7365c4292988b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1043","title":"Ingest threads from all pull requests","createdAt":"2022-04-27T22:24:36Z"}
{"state":"Merged","mergedAt":"2024-01-31T01:13:51Z","number":10430,"mergeCommitSha":"caf7fdf8af2c2675b5f9ed231d6b08378e369693","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10430","title":"Fix tab creation in IJ","createdAt":"2024-01-31T01:01:03Z"}
{"state":"Merged","mergedAt":"2024-01-31T16:19:20Z","number":10431,"body":"This flag will be set to true if bulk ingestion is complete for GitHub/GitHubEnterprise or if the number of PRs has been fully ingested is >80% for BitBucket and GitLab.","mergeCommitSha":"5ea0b33cc40f1fe314a907c75e039b5a90a9bea3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10431","title":"Add PullRequestIngestion.ingestionComplete flag","createdAt":"2024-01-31T01:11:37Z"}
{"state":"Merged","mergedAt":"2024-01-31T03:46:08Z","number":10432,"mergeCommitSha":"64bbfea629a626c18069f1408e59c0d86acf69a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10432","title":"Introduce Team Capabilities to programmatically control features","createdAt":"2024-01-31T01:20:49Z"}
{"state":"Merged","mergedAt":"2024-01-31T03:57:33Z","number":10433,"mergeCommitSha":"8c33939e34564ad6941bc1b2baab475e0881c472","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10433","title":"Web client uses team capabilities","createdAt":"2024-01-31T01:32:26Z"}
{"state":"Merged","mergedAt":"2024-01-31T19:15:30Z","number":10434,"body":"Remove dead types, views, and streams.","mergeCommitSha":"cf5057d70605ece816a09d5bcf6f7e7f3707e8a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10434","title":"Remove explorer insights old UI","createdAt":"2024-01-31T02:47:46Z"}
{"state":"Merged","mergedAt":"2024-01-31T03:04:24Z","number":10435,"mergeCommitSha":"f7b2b22c1e4aa7f7fea04c07673b4558fc4906c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10435","title":"Put post action at wrong path segment","createdAt":"2024-01-31T03:04:02Z"}
{"state":"Merged","mergedAt":"2024-01-31T03:15:47Z","number":10436,"mergeCommitSha":"409e457aa7868944725eb66eafa19bccb6f0359d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10436","title":"[SKIP TESTS] - remove release channel from teams page","createdAt":"2024-01-31T03:07:14Z"}
{"state":"Merged","mergedAt":"2024-01-31T20:48:30Z","number":10437,"mergeCommitSha":"bca9900f244214aa24c56fb506b6e6ea6c0f372d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10437","title":"Ingest shared google drives","createdAt":"2024-01-31T03:28:48Z"}
{"state":"Merged","mergedAt":"2024-01-31T03:53:36Z","number":10438,"mergeCommitSha":"ad95e09c1f236f5bc1698994539816bd05a61544","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10438","title":"Fix bad redirect","createdAt":"2024-01-31T03:42:14Z"}
{"state":"Merged","mergedAt":"2024-01-31T06:59:14Z","number":10439,"mergeCommitSha":"90b8f5401624a2236b0277c097f4df2aa825453a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10439","title":"Remove unused campaignservice","createdAt":"2024-01-31T05:13:08Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:14:29Z","number":1044,"body":"The webpack persistent build cache wasn't really working for VSCode builds, because we do two builds at the same time and apparently webpack's cache doesn't deal with that gracefully.  Giving each build a separate cache name fixes this.","mergeCommitSha":"dab45959f72a7a3f17c5585c8452b28831fedff9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1044","title":"Set up separate webpack caches for VSCode bundles","createdAt":"2022-04-27T22:53:42Z"}
{"state":"Merged","mergedAt":"2024-01-31T07:00:48Z","number":10440,"mergeCommitSha":"079946980b6e9845787f5a6dd25feba6b956b28d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10440","title":"Remove unused monkey-service","createdAt":"2024-01-31T05:20:10Z"}
{"state":"Merged","mergedAt":"2024-01-31T07:01:17Z","number":10441,"mergeCommitSha":"d345582b72a040b919bd45ab20eb6cae08b57707","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10441","title":"Remove unused secret-service","createdAt":"2024-01-31T05:25:35Z"}
{"state":"Merged","mergedAt":"2024-01-31T07:04:52Z","number":10442,"mergeCommitSha":"50e19d745beb45286a509918cb5f88171c5ac0d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10442","title":"Remove unused encryptionservice","createdAt":"2024-01-31T05:59:47Z"}
{"state":"Merged","mergedAt":"2024-01-31T07:24:55Z","number":10443,"mergeCommitSha":"e229b021d1ffe2a68e6ba06ee3d22c682f6614a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10443","title":"Deprecate features flags: FeatureSemanticSearchFile, FeatureNewIDESidebar","createdAt":"2024-01-31T07:24:24Z"}
{"state":"Merged","mergedAt":"2024-01-31T18:10:24Z","number":10444,"mergeCommitSha":"adca9efdaf046bc86c1b0883d7244acc2f2cb23f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10444","title":"Refactor Pinecone config and secrets to support dual write mode","createdAt":"2024-01-31T08:53:20Z"}
{"state":"Merged","mergedAt":"2024-01-31T17:20:53Z","number":10445,"body":"Unclear how much serverless apis are available in this release of java layere, but it is moving us in the right direction.\r\n\r\nValidated by running unit tests against pinecone namespace and testing against dev environment with Q&A.\r\n\r\n","mergeCommitSha":"a991a51b39a62bf8526adb13930058644dd4b0f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10445","title":"Upgrade pinecone (with some serverless support)","createdAt":"2024-01-31T10:20:33Z"}
{"state":"Merged","mergedAt":"2024-01-31T15:09:44Z","number":10446,"body":"Download button is broken because browser is sending unknown agent type","mergeCommitSha":"666387d4d28c4bf3fdcfa93d955ef39c120bf712","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10446","title":"Add 'landing' agent type to API","createdAt":"2024-01-31T14:31:52Z"}
{"state":"Merged","mergedAt":"2024-01-31T16:58:14Z","number":10447,"mergeCommitSha":"3d5dca4daf6d3a056f626047a2e8d93e9405d0e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10447","title":"[SKIP TESTS] - Remove dead services from build matrix","createdAt":"2024-01-31T16:55:55Z"}
{"state":"Merged","mergedAt":"2024-01-31T18:39:20Z","number":10448,"mergeCommitSha":"7c759c639177f1f77117543f9fbef462af455395","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10448","title":"Try new topics","createdAt":"2024-01-31T18:22:07Z"}
{"state":"Merged","mergedAt":"2024-01-31T18:34:54Z","number":10449,"mergeCommitSha":"0c288e048ecc8921577d0239d7776d1bc8b0f7aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10449","title":"Add padding","createdAt":"2024-01-31T18:23:46Z"}
{"state":"Merged","mergedAt":"2024-01-31T19:41:08Z","number":10450,"mergeCommitSha":"4ed71bf6ea1f6bfbfec099c4c5b0659d175b4edb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10450","title":"Cleanup team modes","createdAt":"2024-01-31T18:24:42Z"}
{"state":"Closed","mergedAt":null,"number":10451,"body":"Prevent adding a new `APP_TYPE` that is not compatible with `AgentType`.","mergeCommitSha":"54e233fd237518441b4bf817479f17193ccdcbf1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10451","title":"Small client app type tweak","createdAt":"2024-01-31T18:33:31Z"}
{"state":"Merged","mergedAt":"2024-01-31T18:55:43Z","number":10452,"mergeCommitSha":"a8a1cc647d5616018329b7f08acb6537599f078c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10452","title":"Fix prod secrets","createdAt":"2024-01-31T18:52:07Z"}
{"state":"Merged","mergedAt":"2024-01-31T19:13:11Z","number":10453,"mergeCommitSha":"d72add643c410b22958c2f4689b295bb368d5a4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10453","title":"Remove product agent enforcement for latestPublicVersion API","createdAt":"2024-01-31T19:02:35Z"}
{"state":"Merged","mergedAt":"2024-01-31T22:11:45Z","number":10454,"mergeCommitSha":"0758f55a7c5a603f3733f6aee8a544e369ec4787","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10454","title":"Remove all remaining 'TryUnblocked' flag checks","createdAt":"2024-01-31T21:08:50Z"}
{"state":"Merged","mergedAt":"2024-01-31T22:00:19Z","number":10455,"body":"Revives functionality deleted in #10439.","mergeCommitSha":"b95f4cdccf3c9cb22f52f98711663a0ea8427913","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10455","title":"Revive onboarding email job in the notification service","createdAt":"2024-01-31T21:35:39Z"}
{"state":"Merged","mergedAt":"2024-01-31T22:00:37Z","number":10456,"mergeCommitSha":"e924d3d3914ccce2f7e523e4d89f33d29224dee2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10456","title":"Fix permanent team deletion when team's Enterprise Config App no longer exists","createdAt":"2024-01-31T21:42:08Z"}
{"state":"Merged","mergedAt":"2024-01-31T22:19:56Z","number":10457,"body":"Fix issue where users were sent to connect route instead of processing route","mergeCommitSha":"cf60156e529baef9e28c891a3cfd9a327151f2b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10457","title":"Fix Processing State","createdAt":"2024-01-31T22:08:25Z"}
{"state":"Merged","mergedAt":"2024-01-31T22:59:09Z","number":10458,"mergeCommitSha":"35244743ff99ec99caf6db0baa49f43c5e8f2048","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10458","title":"Reactor TeamSettingsStore","createdAt":"2024-01-31T22:33:25Z"}
{"state":"Merged","mergedAt":"2024-02-01T00:33:13Z","number":10459,"mergeCommitSha":"326293aedfccead79499c9c3681413bca53e9b42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10459","title":"Removes \"General\" sidebar + FeatureNewIDESidebar on client","createdAt":"2024-01-31T22:41:26Z"}
{"state":"Merged","mergedAt":"2022-04-28T17:46:00Z","number":1046,"body":"* Message layout fixes\r\n<img width=\"809\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165649756-ffed1bd4-4c91-43ee-8f7c-87194d3ae1ef.png\">\r\n\r\n* Add icon badge for not a member (replace text) \r\n    * Involves refactoring the StatusIcon component into shared/\r\n![image](https://user-images.githubusercontent.com/13431372/165649826-0b3d3705-9b6b-465d-a1c8-9947e759f552.png)\r\n","mergeCommitSha":"61ce704f3fc822a104dfe6ae4467affd2f9845c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1046","title":"Styling fixes","createdAt":"2022-04-27T23:57:48Z"}
{"state":"Merged","mergedAt":"2024-01-31T22:57:53Z","number":10460,"body":"The repro case here is:\r\n1. Create a new Q&A by hi lighting some code and asking a question\r\n2. While the Q&A is processing, hover on the resulting sourcemark gutter icon, view the thread\r\n3. Thread opens up in an editor window, but has no content\r\n\r\nThis caused a bug where the discussion stream would sometimes not initialize correctly, so the discussion content wouldn't display.  `repoAndThreadStream` is split into two, then re-joined, which causes the diamond.","mergeCommitSha":"d8d01cda4e3091aaaac42dc4f6d9a39784ad7e9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10460","title":"Fix discussion stream bug that would cause new Q&As to not display in VSCode editor window correctly","createdAt":"2024-01-31T22:44:57Z"}
{"state":"Merged","mergedAt":"2024-02-12T19:01:35Z","number":10461,"mergeCommitSha":"43f2467dc8f80cb847719c2bf9fc6fc6befd2fcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10461","title":"Add EmbeddingDeleteModel","createdAt":"2024-01-31T23:13:32Z"}
{"state":"Merged","mergedAt":"2024-02-01T00:55:50Z","number":10462,"body":"changes\n- migration that copies all `TeamSettingsModel.teamEnabledAt` to `TeamModel.enabledAt`\n- logic to ensure that they are both in sync when writes occur","mergeCommitSha":"2a88bddfd9aab281ae0f99e3bf0d5529fd26a05a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10462","title":"Dual write teamEnabledAt to Team and TeamSettings models","createdAt":"2024-01-31T23:18:39Z"}
{"state":"Merged","mergedAt":"2024-01-31T23:31:09Z","number":10463,"body":"The 'Ask a Question...' action (used in the lightbulb menu and keyboard shortcut) would show the 'Ask a Question' view, and pop open the sidebar, but wouldn't focus on the text element.  This was normally not noticeable, but if you already had that UI open, you'd never get the focus in the right place.","mergeCommitSha":"a48b03f5662a252c54902dd6f91d20dfd3d86c86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10463","title":"Focus 'Ask a Question' text entry on VSCOde action","createdAt":"2024-01-31T23:19:00Z"}
{"state":"Merged","mergedAt":"2024-01-31T23:47:24Z","number":10464,"body":"Assume by default that we *won't* show the instructions, until we've loaded the data.  This prevents a bit of a flickery startup animation once the instructions are closed.","mergeCommitSha":"3a2a9d4f6428aad00bdf5be8a2f7408df5f27b1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10464","title":"Better 'Ask a Question' startup animation","createdAt":"2024-01-31T23:34:20Z"}
{"state":"Merged","mergedAt":"2024-02-01T00:07:52Z","number":10465,"mergeCommitSha":"e61404889c0f87859a71aa25f62f0f4549f59ab5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10465","title":"Drop priority for web reingest events","createdAt":"2024-01-31T23:57:06Z"}
{"state":"Merged","mergedAt":"2024-02-01T03:02:59Z","number":10466,"body":"changes\n- reads use TeamModel.enabledAt\n- remove all traces of TeamSettingsModel.teamEnabledAt","mergeCommitSha":"c64777546c250f8dd3f1da06cca3b28b55638a21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10466","title":"Transition to using TeamModel.enabledAt","createdAt":"2024-02-01T00:07:25Z"}
{"state":"Merged","mergedAt":"2024-02-01T06:01:44Z","number":10467,"mergeCommitSha":"4bc86572b8344588cdbd36f44c887c96958f0e78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10467","title":"Deeper backfill of metrics","createdAt":"2024-02-01T06:01:18Z"}
{"state":"Merged","mergedAt":"2024-02-02T19:10:29Z","number":10468,"body":"First cut of a responsive blog landing page, and the header link now routes here, as opposed to the first article.\r\n\r\nDesktop:\r\n![CleanShot 2024-01-31 at 22 37 57@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/a6353cec-d3b4-4e03-9a81-d89719ecc5d2)\r\n\r\niPad landscape:\r\n![CleanShot 2024-01-31 at 22 40 56@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/d533d114-1ad8-40be-b98a-aa356b372806)\r\n\r\niPad portrait:\r\n![CleanShot 2024-01-31 at 22 41 59@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/c68f731a-24e1-485c-bf56-d8b6150aa3ad)\r\n\r\nMobile:\r\n![CleanShot 2024-01-31 at 22 43 07@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/e0abf819-9976-4493-babb-6c850cd56734)\r\n","mergeCommitSha":"5db9f26aa3e95ffe4cd69ca19512230116d92f6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10468","title":"Blog landing page","createdAt":"2024-02-01T06:44:27Z"}
{"state":"Merged","mergedAt":"2024-02-01T17:26:24Z","number":10469,"mergeCommitSha":"8cf28341bd795dd1800465700a841c5b1429b5d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10469","title":"Handle when no PRs are in the repo","createdAt":"2024-02-01T17:16:29Z"}
{"state":"Merged","mergedAt":"2022-04-28T17:13:57Z","number":1047,"body":"- add thread details page\r\n- lot's of refactoring\r\n\r\n<img width=\"1705\" alt=\"thread detail page\" src=\"https://user-images.githubusercontent.com/1798345/165662396-a87b9408-840d-410f-9120-b99e60da5d00.png\">\r\n","mergeCommitSha":"b7906a833a5f9b902c7a24c6de7ff820205d840b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1047","title":"Admin thread detail page","createdAt":"2022-04-28T02:12:01Z"}
{"state":"Merged","mergedAt":"2024-02-01T18:26:03Z","number":10470,"mergeCommitSha":"3da129e734aa7970d75685f639d233dac959eeae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10470","title":"Remove experts from thread info response for now","createdAt":"2024-02-01T17:29:21Z"}
{"state":"Merged","mergedAt":"2024-02-01T18:17:51Z","number":10471,"body":"I can't make much sense of the metrics. It shows on average users going over their limit by 6-7 requests. I am gonna creep up these limits until all the rate limit stats go away. ","mergeCommitSha":"35fc3caef5581948c9a6939bfd3d82f8e8b2d2ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10471","title":"increase pusher rate limit","createdAt":"2024-02-01T18:11:23Z"}
{"state":"Merged","mergedAt":"2024-02-01T18:46:00Z","number":10472,"body":"For hover-triggered dropdowns:\r\n\r\n* Open and close the menu quicker (200ms)\r\n* Don't ever close the menu when clicking on the header","mergeCommitSha":"7b017b317fb0d5bb2d31052ab903487fa56750ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10472","title":"Fix team selector","createdAt":"2024-02-01T18:19:29Z"}
{"state":"Merged","mergedAt":"2024-02-01T20:19:57Z","number":10473,"mergeCommitSha":"32fcee11c6231cea9814aba7d537c316d7458cbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10473","title":"Remove pydantic requirement that is creating a personal hell for me.","createdAt":"2024-02-01T18:24:28Z"}
{"state":"Merged","mergedAt":"2024-02-01T19:27:32Z","number":10474,"mergeCommitSha":"9b1221bb16d19178039d911a76566eafe8db79b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10474","title":"Make Google OAuth team an insider team","createdAt":"2024-02-01T18:33:47Z"}
{"state":"Merged","mergedAt":"2024-02-01T19:27:19Z","number":10475,"body":"Required `membership_state` value is actually optional.\n\nTurns out we don't even consume this value, so just dropped it.","mergeCommitSha":"279b8252ff1032007c1ab0a430f1f04a86576b63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10475","title":"Fix GitLab members API","createdAt":"2024-02-01T19:04:11Z"}
{"state":"Merged","mergedAt":"2024-02-01T19:53:43Z","number":10476,"mergeCommitSha":"0f57e738088c68cead96fec1b44e1101c74ccda4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10476","title":"Temporarily disable web site re-ingestion for demo teams","createdAt":"2024-02-01T19:35:27Z"}
{"state":"Merged","mergedAt":"2024-02-01T20:31:18Z","number":10477,"mergeCommitSha":"fe87706ec504c47557b6d9a58ba1f981e0d92bf0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10477","title":"[SKIP TESTS] - Testing to see if we can exclude build VMs from Drata","createdAt":"2024-02-01T19:39:02Z"}
{"state":"Merged","mergedAt":"2024-02-01T21:02:02Z","number":10478,"mergeCommitSha":"9555c5e9505d71b75c69e25edfa3ae0076f68a08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10478","title":"Check whether website is enabled for ingestion sooner in the handler","createdAt":"2024-02-01T20:50:12Z"}
{"state":"Merged","mergedAt":"2024-02-01T23:31:33Z","number":10479,"body":"Adds a Code Block component fo the landing page\r\n\r\n```\r\n<CodeBlock\r\n    code={`\r\n<div className=\"media\">\r\n    <img src={ideDiscover}></img>\r\n    <caption>\r\n        Left: The Unblocked Panel in Visual Studio Code displaying file-level discussions from GitHub\r\n        and Slack. Right: Line-level historical Pull Request discussions as indicated by the message\r\n        icon.\r\n    </caption>\r\n</div>`}\r\n    language=\"javascript\"\r\n/>\r\n  ```","mergeCommitSha":"3fa4fa2ec154b0021fe74aa4ae63127223988c13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10479","title":"Landing page Code block","createdAt":"2024-02-01T20:58:17Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:08:25Z","number":1048,"body":"The problem can happen when:\r\n1. None of the points of the mark exist in the tree, because (i) the mark was added on commit ahead of the local Git repo, or (ii) the mark was added on a commit on a feature branch that was not merged.\r\n2. The latest point could not be calculated, because (i) the mark range was deleted, or (ii) the mark range was modified significantly, or (iii) due to an engine bug.\r\n\r\nThis change allows the client to get the original point, so that the original point can be displayed in the discussion view.\r\n\r\nAlso related to this https://github.com/NextChapterSoftware/unblocked/issues/1022","mergeCommitSha":"c023cbea642cfa92633aefdfe1063e717e1c425d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1048","title":"VSCode: Snippet not displayed for mark when a point cannot be computed at the latest commit","createdAt":"2022-04-28T02:56:18Z"}
{"state":"Merged","mergedAt":"2024-02-01T21:13:36Z","number":10480,"mergeCommitSha":"1cc4f9707d21b70f6e3e4497b3e2553ab4f5d287","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10480","title":"Update our image models to latest depenencies","createdAt":"2024-02-01T21:09:54Z"}
{"state":"Merged","mergedAt":"2024-02-01T21:47:07Z","number":10481,"body":"\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/0ffa5d28-35fe-4eac-9bde-3df5860dd7d3\r\n\r\n","mergeCommitSha":"f2c1a80bb259c18d872b22e06dff36b188932966","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10481","title":"Animate pending processing image","createdAt":"2024-02-01T21:13:44Z"}
{"state":"Merged","mergedAt":"2024-02-02T01:53:10Z","number":10482,"body":"Was doing this post-processing in the sandbox for the demo, but doing this in the filewriter for prod use.\r\n\r\nSee https://docs.cohere.com/docs/rerank-preparing-the-data for format details.","mergeCommitSha":"6121b84233d75241dbd67fe3d3aa5a84e067604c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10482","title":"Write fine-tuning file in cohere format","createdAt":"2024-02-01T23:23:57Z"}
{"state":"Merged","mergedAt":"2024-02-02T00:58:04Z","number":10483,"mergeCommitSha":"b8fbf231e99d4d7b431c67727dc53acc242b8830","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10483","title":"[SKIP TESTS] Handle null Jira bodies","createdAt":"2024-02-01T23:44:58Z"}
{"state":"Merged","mergedAt":"2024-02-02T19:17:41Z","number":10484,"body":"Opt-in scroll restoration. This (mostly) fixes scroll restoration in the Chats component.\r\n\r\nA few small changes:\r\n\r\n* `ScrollContext` now exposes two methods: `storeScroll` and `restoreScroll` -- these save and restore scroll locations, mapped by history key, in a small LRU map.\r\n* `ScrollRestoration` -- opts into scroll restoration for the given component.  I think Chats are really the only UI where this matters right now, but we can improve this in the future.  Note that we only use this on the `ready` state for the Chats UI, as this is the point where we know scroll restoration will work.\r\n\r\nNote that this works fine with the browser backwards and forwards UI (the toolbar buttons, Cmd+Left and Cmd+Right), but does *not* yet work when you click the Close `X` button on the thread or PR details view.  I will fix that in an upcoming PR.\r\n\r\nI used to kind of like react router, but now I don't.","mergeCommitSha":"41741688f5e4334f9f079c2d6e299a12474bb377","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10484","title":"Dashboard scroll restoration","createdAt":"2024-02-02T02:10:37Z"}
{"state":"Merged","mergedAt":"2024-02-02T05:08:52Z","number":10485,"mergeCommitSha":"de01db3caa1b79ab542d457385825c2dcd72c1db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10485","title":"Account for more internal teams, especially internal personal teams","createdAt":"2024-02-02T04:53:35Z"}
{"state":"Merged","mergedAt":"2024-02-05T19:07:19Z","number":10486,"body":"The only place where we actually need to understand the history stack is in the detail views.  Our desired behaviour:\r\n\r\n* In general the detail view `X` button navigates backwards in the stack\r\n* The exception to this is if the detail view is the first item in the history stack (for instance, they clicked on a thread link in Slack and were taken directly there).  If we navigate backwards from here, we will navigate back to whatever site the user came from, before unblocked.\r\n\r\nWe had some support for this, but it involved a custom version of navigate that maintained its own stack.  We only used this in a few places, and it's inconsistent and weird.  A lot of the time you would hit `X` and wouldn't go backwards at all.  So in this PR I've instead tied into react router's events to track the app stack ourselves:\r\n\r\n* `HistoryStack` holds a stack of navigation frames, and understands how to apply the events to mutate the stack\r\n* `HistoryContext` sends events to the stack, and publishes results\r\n* Replace all usages of custom navigate method with the regular react router navigate\r\n\r\nI still cannot believe that react router does not publish its own history stack in any useful way.","mergeCommitSha":"a6f684286105fbaa4fb9aaea1ff84b1c0fde4cb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10486","title":"Fix broken history tracking","createdAt":"2024-02-02T04:56:51Z"}
{"state":"Merged","mergedAt":"2024-02-02T05:36:59Z","number":10487,"mergeCommitSha":"75489e3a75896599bf3dcc591e8e224c391fcee2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10487","title":"Ability to manually enable/disable team","createdAt":"2024-02-02T05:26:24Z"}
{"state":"Merged","mergedAt":"2024-02-02T06:08:49Z","number":10488,"mergeCommitSha":"646e487743ad242ffbdd2ef7eb8de7e788a16341","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10488","title":"[SKIP TESTS] Handle null Jira bodies with a default value this time","createdAt":"2024-02-02T05:56:07Z"}
{"state":"Merged","mergedAt":"2024-02-02T07:02:37Z","number":10489,"mergeCommitSha":"29db7eee36c4551cca43ff49a3bcd14af0ae1a8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10489","title":"Fix typo in action button","createdAt":"2024-02-02T07:02:05Z"}
{"state":"Merged","mergedAt":"2022-04-28T17:00:28Z","number":1049,"body":"Seems to be working. Second time is cached.\r\n\r\n---\r\n\r\n\r\n<img width=\"877\" alt=\"Screen Shot 2022-04-27 at 22 08 57\" src=\"https://user-images.githubusercontent.com/1798345/165680548-8b85eb57-04aa-4193-b88f-eefe596a0310.png\">\r\n\r\n","mergeCommitSha":"efe6b8bc370b6ca48a78b222b884721897a671da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1049","title":"Gradle should build TS protos","createdAt":"2022-04-28T04:04:16Z"}
{"state":"Merged","mergedAt":"2024-02-02T16:42:20Z","number":10490,"body":"\uD83C\uDDE8\uD83C\uDDE6 ","mergeCommitSha":"0405e18f2a62a32d699d565708fe6802f135d0bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10490","title":"Update metrics page charts to start from Canada Day 2023","createdAt":"2024-02-02T16:29:53Z"}
{"state":"Closed","mergedAt":null,"number":10491,"mergeCommitSha":"4e1ac1a44fad40f082ad3541ac4741a28f184033","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10491","title":"Backend change","createdAt":"2024-02-02T18:43:35Z"}
{"state":"Closed","mergedAt":null,"number":10492,"body":"Front end change\n\nfoo","mergeCommitSha":"ef72c9ea720d0f265dfcd255b3890478e6bd3f7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10492","title":"Front end change","createdAt":"2024-02-02T18:43:38Z"}
{"state":"Closed","mergedAt":null,"number":10493,"mergeCommitSha":"1c4e54dd442832d9320a524d0287cc128754387c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10493","title":"bar","createdAt":"2024-02-02T18:43:42Z"}
{"state":"Merged","mergedAt":"2024-02-02T19:28:36Z","number":10494,"mergeCommitSha":"dc5d8696900cac0a6696708f4e0218423cc4e6aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10494","title":"Temporarily pause notion page validation","createdAt":"2024-02-02T19:15:57Z"}
{"state":"Merged","mergedAt":"2024-02-02T20:01:06Z","number":10495,"mergeCommitSha":"9489cad651c03dff4983b5273fb783991846a59d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10495","title":"Completely remove concept of standard","createdAt":"2024-02-02T19:36:34Z"}
{"state":"Merged","mergedAt":"2024-02-02T20:11:58Z","number":10496,"body":"Strong tags weren't working. Replaced with bold and h4 tags where appropriate.","mergeCommitSha":"a72b2806a854c2b56b0aa8420ec8b9ed31afcb40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10496","title":"Replace strong tags with bold and h4s","createdAt":"2024-02-02T19:59:32Z"}
{"state":"Merged","mergedAt":"2024-02-02T20:32:59Z","number":10497,"mergeCommitSha":"a277033c39eef50cca8562e9045b45dfb3feeaf0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10497","title":"Add equity to job description","createdAt":"2024-02-02T20:11:33Z"}
{"state":"Merged","mergedAt":"2024-02-02T20:33:06Z","number":10498,"mergeCommitSha":"ed7b2c4334fe758e853c0217da879b38f02ecdaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10498","title":"Add debugging for notion databases","createdAt":"2024-02-02T20:13:20Z"}
{"state":"Merged","mergedAt":"2024-02-02T20:13:54Z","number":10499,"mergeCommitSha":"8c271e9f3fcdf01a84a5c22280e6f10dd77779a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10499","title":"Add bertopic","createdAt":"2024-02-02T20:13:49Z"}
{"state":"Merged","mergedAt":"2022-01-22T00:57:55Z","number":105,"body":"### Changes\r\n- account for anonymous identities, those who have never signed in\r\n- add concept of a deactivated members, members removed from the team\r\n- rename User to Person to avoid ambiguity (eg: a GitHub user or a Codeswell user)\r\n- a team member no longer maps to a user, it maps to an identity\r\n\r\n### Hack\r\n- Added this hack https://github.com/Chapter2Inc/codeswell/commit/ba775cf55eeaf6bccabb4b50211fe5dfe2bb1ff2 to remove an old model. Without this hack all your tests will fail on merge. I can revert the hack commit after some time.\r\n\r\n### Diagram\r\n<img width=\"1102\" alt=\"Screen Shot 2022-01-21 at 16 03 01\" src=\"https://user-images.githubusercontent.com/1798345/*********-be2b5d3b-288b-4101-b3ba-178bab1e3398.png\">\r\n","mergeCommitSha":"10cd69071241ecf1ecda158ea36e8c4cf4be8ff6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/105","title":"Refactor identity models","createdAt":"2022-01-21T23:52:20Z"}
{"state":"Merged","mergedAt":"2022-04-28T17:17:45Z","number":1050,"mergeCommitSha":"916b3df89f68e1c0efe5b36fcea3ab5c426df110","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1050","title":"Legacy proto generation cleanup","createdAt":"2022-04-28T05:05:43Z"}
{"state":"Merged","mergedAt":"2024-02-02T21:42:07Z","number":10500,"mergeCommitSha":"6f535c3376fb209654c768b9cf0abf3f40d33b79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10500","title":"Fix debugging for notion databases","createdAt":"2024-02-02T21:29:12Z"}
{"state":"Merged","mergedAt":"2024-02-02T23:00:17Z","number":10501,"body":"* Remove repo-scoped thread list streams -- we aren't using these anymore\r\n* Remove SidebarThreadStore -- not used\r\n* Remove `pr` and `commit` thread list streams -- not used","mergeCommitSha":"82fc08921fae83412744ca9b7307a92350a63aa1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10501","title":"Remove dead thread list stream code","createdAt":"2024-02-02T21:35:12Z"}
{"state":"Merged","mergedAt":"2024-02-02T22:15:28Z","number":10502,"mergeCommitSha":"e73f0aff20dcc7b9d4f29c8423e68b2748d2f9f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10502","title":"Query notion database","createdAt":"2024-02-02T22:02:31Z"}
{"state":"Merged","mergedAt":"2024-02-03T00:05:49Z","number":10503,"body":"Never used on server.","mergeCommitSha":"bb4598380618bcd50acafc94a443c7687a3d1982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10503","title":"Remove `remote` and `rootCommitSha` from APIs","createdAt":"2024-02-02T23:43:48Z"}
{"state":"Merged","mergedAt":"2024-02-03T17:54:21Z","number":10504,"mergeCommitSha":"cb666852b8334a91c41e7ad07983c00e972cb47b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10504","title":"Add Notion page properties","createdAt":"2024-02-02T23:56:36Z"}
{"state":"Closed","mergedAt":null,"number":10505,"body":"The thread list streams (mine, team QA, recommended, archived) are implemented by two different implementations:\r\n\r\n* `MyDiscussionStore`: handles `mine`\r\n* `TeamThreadsValueClass`: handles `teamQa`, `recommended`, `archived`\r\n\r\nWe were using the same key type for all of these, which meant there was a bunch of broken type code and cases unimplemented.  Clean this up so that `MyDiscussionStore` uses a key of type `MyDiscussionKey`, and `TeamThreadsValueClass` uses a key of type `TeamThreadsDiscussionsKey`.\r\n\r\nThe `mine` keys were messed up as well -- we used to have different keys for `mine`, `mineUnblocked`, and `mineByScm`, except they also each had a `provider` property, which only made sense in some circumstances.  Clean all this up.","mergeCommitSha":"63962b6a85363ba181cf6739e5a299ab8446d6ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10505","title":"Clean up thread list store types","createdAt":"2024-02-03T00:02:06Z"}
{"state":"Merged","mergedAt":"2024-02-03T00:39:51Z","number":10506,"mergeCommitSha":"19c484fb451da28a49ec8ba24f49549fa9e78aa8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10506","title":"Dont emit document validation events from the API service running semantic search","createdAt":"2024-02-03T00:19:06Z"}
{"state":"Merged","mergedAt":"2024-02-03T03:24:38Z","number":10507,"mergeCommitSha":"5d5b57e8ad42ffffaa81e58edd0cf800af2a9bd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10507","title":"Handle heterogeneous results returned by Notion database query","createdAt":"2024-02-03T01:02:51Z"}
{"state":"Merged","mergedAt":"2024-02-04T20:43:15Z","number":10508,"body":"A company (UserTesting) has a GitLab Self-hosted instance that has a separate SSH endpoint:\n\n- HTTPS: `code.acquia.com`\n- SSH: `gitcode.acquia.com`\n\nIn order for our IDE extensions to correctly match against DB repos we record the alternative SSH hostname derived from the SCM API ahead of time, then match the user's clone URLs against either the HTTPS repo URL or the alternative SSH URL.","mergeCommitSha":"c16fe20ecd7337695aca99ae4e557d97c57cfeb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10508","title":"Support for alternative SSH SCM hostnames","createdAt":"2024-02-03T01:08:59Z"}
{"state":"Merged","mergedAt":"2024-02-03T04:16:16Z","number":10509,"mergeCommitSha":"703cda09f5098bd6293f59303bb1fbff2fa9fcf5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10509","title":"Fix identity retrieved for google folder and shared drive search","createdAt":"2024-02-03T03:53:12Z"}
{"state":"Merged","mergedAt":"2022-04-29T18:58:08Z","number":1051,"body":"The eventual goal here is to offload creating, updating, and deleting GitHub comments from unblocked to a queue consumer. \r\n\r\nThis PR sets up a skeleton consumer and producer just to test that this works. We'll fire a message to the queue when messages are created, updated, and deleted, and the consumer will just log it out. Next PR will all proper logic.\r\n\r\nTODO\r\n- [x] Create a `standard` SQS queue called `unblocked_pr_comments`\r\n- [x] Update global.conf and prod.conf with the queue url","mergeCommitSha":"61a87175b996465724785528ba7c978e48d62a2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1051","title":"First pass at creating a queue produce and consumer for posting PR comments","createdAt":"2022-04-28T17:20:16Z"}
{"state":"Merged","mergedAt":"2024-02-03T05:27:37Z","number":10510,"mergeCommitSha":"614f7839d87a243605a60a7d9c217938136d3046","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10510","title":"Submodule bump — update blog images to match share preview aspect ratios","createdAt":"2024-02-03T05:16:02Z"}
{"state":"Merged","mergedAt":"2024-02-03T06:15:54Z","number":10511,"mergeCommitSha":"f840cbb991cb463fe6c6bf1d8b43547318275a51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10511","title":"Blog article 2 preview meta","createdAt":"2024-02-03T05:44:24Z"}
{"state":"Merged","mergedAt":"2024-02-03T18:17:26Z","number":10512,"body":"Add a makefile target that only runs if the API spec, proto spec, or package lockfile changes -- this lets us skip the slow gradle bootup on every build.\r\n\r\nThis is enabled for developer builds on dashboard and VSCode.  There's no point in enabling this for IntelliJ because it has to fire up gradle anyways.","mergeCommitSha":"cb9b17be3c6de479f602fd62e107046d3e906b05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10512","title":"Faster local client dependency builds","createdAt":"2024-02-03T17:08:34Z"}
{"state":"Merged","mergedAt":"2024-02-03T23:11:14Z","number":10513,"body":"- Try new mistral\r\n- Fix mistral embedder\r\n","mergeCommitSha":"56f734aabcd9adc5b8351f7f0e9de24b3210a1f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10513","title":"Add new e5 mistral embedder for testing","createdAt":"2024-02-03T17:47:08Z"}
{"state":"Merged","mergedAt":"2024-02-03T18:41:37Z","number":10514,"mergeCommitSha":"852c325c13a227ee37dc11e8e76252139712c257","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10514","title":"Implement remaining page properties","createdAt":"2024-02-03T18:26:01Z"}
{"state":"Merged","mergedAt":"2024-02-04T05:58:29Z","number":10515,"mergeCommitSha":"e34595fe04566e2421e888b49b32e1cc5998073c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10515","title":"Convert Notion child database into a markdown table to be embedded","createdAt":"2024-02-04T01:21:54Z"}
{"state":"Merged","mergedAt":"2024-02-04T01:45:56Z","number":10516,"mergeCommitSha":"0e4b4cef938054a5b8612e7fafe8190771a5fdfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10516","title":"Add Google API Limited Use Disclosure to privacy page","createdAt":"2024-02-04T01:34:41Z"}
{"state":"Merged","mergedAt":"2024-02-04T03:57:30Z","number":10517,"mergeCommitSha":"ac343d1a44366e72f74fa62b7aaa43880c9b1e57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10517","title":"Fix 'My Questions' IDE title","createdAt":"2024-02-04T03:46:32Z"}
{"state":"Merged","mergedAt":"2024-02-04T06:03:18Z","number":10518,"mergeCommitSha":"3b8bc26f00558f16b1b66d6519c79d036fce796e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10518","title":"Remove quotes","createdAt":"2024-02-04T05:51:45Z"}
{"state":"Merged","mergedAt":"2024-02-26T21:24:29Z","number":10519,"body":"When we added \"load more\" to the dashboard thread list stores, the code got pretty messy.  We ended up with three different thread store types.  This class refactors everything to use consistent codebase that we can build on:\r\n\r\n* `ThreadListStoreBase`: This is the base for all thread list stores.  It has all the logic for loading threads, managing pages, overlays, etc.  All the code here was factored out from `MyDiscussionThreadStore`.\r\n* `MyDiscussionThreadStore`, `TeamQaThreadStore`, `ArchivedThreadStore`, `RecommendedThreadStore` all derive from `ThreadListStoreBase`.  They mostly define the API call to make for each store, plus any channel polling behaviour.\r\n\r\nThe motivation for doing this now is that I want the thread caching to be consistent so that things like scroll restoration can work consistently.  But it also fixes a bunch of bugs:\r\n\r\n* Archived Threads and Recommended will now load more when you scroll to the bottom\r\n* Archived Threads will now reload data correctly on navigation and when threads are deleted/restored\r\n","mergeCommitSha":"74a8e3819b94dd1290f06b9e642f1b2596c62ac1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10519","title":"Clean up thread stores","createdAt":"2024-02-04T06:07:49Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:06:52Z","number":1052,"body":"- Added new standard queue for PR comments \r\n- Added SQS SendMessage permissions to `apiservice` for both notifications queue and pr comments \r\n- Added ReceiveMessage permissions to `scmservice`\r\n- Modified existing perms to use wildcards in queue name postfixes \r\n\r\nEKS changes have already been deployed. Once CDK stuff are deployed I'll get you queue URLs. ","mergeCommitSha":"b474d564272e0bac09100035471a3429d8a8e945","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1052","title":"add new queue and servie account permissions","createdAt":"2022-04-28T18:03:35Z"}
{"state":"Merged","mergedAt":"2024-02-04T07:06:30Z","number":10520,"mergeCommitSha":"78bf0a0020598b5eecb086f6be0ffc9570fc7be9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10520","title":"Notion database formula number is an int","createdAt":"2024-02-04T06:53:57Z"}
{"state":"Merged","mergedAt":"2024-02-04T07:55:34Z","number":10521,"mergeCommitSha":"bc2f5532d1977be8e650da3d525900ca4f299f83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10521","title":"[SKIP TESTS] Fix notion installation ingest page","createdAt":"2024-02-04T07:46:40Z"}
{"state":"Merged","mergedAt":"2024-02-04T17:25:16Z","number":10522,"mergeCommitSha":"04bd55cd97d4ed7225f1bb29db682f3ed2d1f80d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10522","title":"[SKIP TESTS] Force notion installation ingest from admin console","createdAt":"2024-02-04T08:25:58Z"}
{"state":"Merged","mergedAt":"2024-02-04T11:03:09Z","number":10523,"body":"- Remove all unused helm configs\r\n- Remove all unused eks configs \r\n","mergeCommitSha":"79fd9d77527bfb656e16626f4d3af3ad09c53f6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10523","title":"Cleanup helm and eks configs","createdAt":"2024-02-04T10:55:34Z"}
{"state":"Merged","mergedAt":"2024-02-05T06:21:16Z","number":10524,"mergeCommitSha":"ea4cd09afc595643da2afc1e859455a1fd81c476","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10524","title":"Improve latency of admin members page","createdAt":"2024-02-05T05:56:45Z"}
{"state":"Merged","mergedAt":"2024-02-06T03:13:36Z","number":10525,"mergeCommitSha":"2f6c04aff68ff31ccf677d464e151bcfc9f3d861","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10525","title":"Solo vs team metrics cohort","createdAt":"2024-02-05T06:04:16Z"}
{"state":"Merged","mergedAt":"2024-02-05T06:16:27Z","number":10526,"mergeCommitSha":"3144d4a8dee3a9110bb0751d4448620f21d7aabe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10526","title":"Set priority to high when forcing a reingest of a page","createdAt":"2024-02-05T06:07:35Z"}
{"state":"Merged","mergedAt":"2024-02-05T07:51:42Z","number":10527,"mergeCommitSha":"41061ee2dddeabccd4dd89841c515952dad0c8ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10527","title":"Good god Notion why would you have users without names","createdAt":"2024-02-05T07:20:46Z"}
{"state":"Merged","mergedAt":"2024-02-05T09:15:42Z","number":10528,"mergeCommitSha":"58afb69e09e37da58ca955695dd4148a235b71b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10528","title":"[SKIP TESTS] Handle missing database","createdAt":"2024-02-05T08:38:08Z"}
{"state":"Merged","mergedAt":"2024-02-06T22:11:38Z","number":10529,"body":"Auth + Installation sidebars are now consolidated into the view router.\r\nThis should allow for some additional stability as we require less \"IDE\" logic state.\r\nAlso consolidates + dedupes logic across clients as we no longer need client specific \"command / webviewprovider\" files.","mergeCommitSha":"3c9d36baf26afb2c1538f5fcbb36cc7d98da6e0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10529","title":"Refactor sidebars into a single ViewRouter","createdAt":"2024-02-05T17:25:51Z"}
{"state":"Merged","mergedAt":"2022-04-28T19:52:14Z","number":1053,"body":"<img width=\"818\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165817054-d12ad22f-e07b-4e9a-8fd9-7df85a2486e8.png\">\r\n<img width=\"652\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165817101-fe63352d-26f5-407a-a03a-d9df9362bc52.png\">\r\n<img width=\"722\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165818740-4b13eb4a-dc38-43f4-b9a2-919e6939369a.png\">\r\n\r\n* Editing anchor message now also enables the ability to edit the title of the thread. Save button saves both\r\n* Refactor Input component into shared/ and style for each client\r\n* Remove EditableInput title from web and web-extension","mergeCommitSha":"281a6ea4f477fed12e304371f4e4c749077fbde3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1053","title":"Ability to edit title of anchor message","createdAt":"2022-04-28T18:09:32Z"}
{"state":"Merged","mergedAt":"2024-02-05T17:59:33Z","number":10530,"mergeCommitSha":"b14d271d8eb470a8a19ef7b004cb4b4d514f669a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10530","title":"[SKIP TESTS] Clear cursor when reingesting Notion","createdAt":"2024-02-05T17:50:28Z"}
{"state":"Merged","mergedAt":"2024-02-05T19:08:56Z","number":10531,"body":"We are going to be adding more embeddings to this pckage.\r\n","mergeCommitSha":"8027ea94aa78380501fe9e0a78b720f6562f4bb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10531","title":"Move instructor to opt-in rather than always in for embedding utils","createdAt":"2024-02-05T18:57:27Z"}
{"state":"Merged","mergedAt":"2024-02-05T19:08:17Z","number":10532,"mergeCommitSha":"1a3234238c32d75b0f209acf9c632c42537c3a6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10532","title":"Ignore 400s when getting a linked notion database","createdAt":"2024-02-05T18:59:41Z"}
{"state":"Merged","mergedAt":"2024-02-05T19:20:29Z","number":10533,"mergeCommitSha":"3a3041b53b7b721b3dcc230e21c2548b56bbbfd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10533","title":"Rename","createdAt":"2024-02-05T19:13:01Z"}
{"state":"Merged","mergedAt":"2024-02-05T20:43:54Z","number":10534,"mergeCommitSha":"301dd62127dffa9716373bf786fede1123f20fba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10534","title":"Update cdk dependencies","createdAt":"2024-02-05T19:19:52Z"}
{"state":"Merged","mergedAt":"2024-02-05T20:02:19Z","number":10535,"mergeCommitSha":"57deba02ba69c501ecc2534e856ccc65e4dcd1fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10535","title":"Skip jira issues without comment bodies","createdAt":"2024-02-05T19:51:41Z"}
{"state":"Merged","mergedAt":"2024-02-05T21:30:47Z","number":10536,"mergeCommitSha":"620fb559ba7f814acb39d4a90118fc8b1ac5bf3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10536","title":"Add e5 tests","createdAt":"2024-02-05T20:58:20Z"}
{"state":"Merged","mergedAt":"2024-02-05T21:10:24Z","number":10537,"mergeCommitSha":"98766559689ac2c36c18d75b6c02a93a7aab6749","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10537","title":"Fix JiraIssueIngestionService","createdAt":"2024-02-05T20:59:59Z"}
{"state":"Open","mergedAt":null,"number":10538,"body":"Proposal to add tracking to onboarding","mergeCommitSha":"26a97e83064a131fc1b67754ac7277b7466a2a12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10538","title":"Add APIs for tracking onboarding metrics","createdAt":"2024-02-05T21:13:03Z"}
{"state":"Merged","mergedAt":"2024-02-05T22:22:41Z","number":10539,"body":"Link to slack when possible, otherwise just post name.","mergeCommitSha":"88dbbe15652ca92c85e2e11987fa081192cbea48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10539","title":"Create slack links for users when possible","createdAt":"2024-02-05T21:29:11Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:33:20Z","number":1054,"body":"Tracked down the call that was resetting the Authentication parameters context.\r\n\r\nThe identityIdOrNull call in Monitoring.kt was resetting it.\r\nIt makes a call to application.principal(), which does some weird shit:\r\n\r\n```\r\npublic val ApplicationCall.authentication: AuthenticationContext\r\n    get() = AuthenticationContext.from(this)\r\n\r\npublic inline fun <reified P : Principal> ApplicationCall.principal(): P? = authentication.principal()\r\n```\r\n\r\nI’m going to file a bug with the Ktors team, until then, this resolves the issues with tests and application.\r\n","mergeCommitSha":"1e272ff03fc9505ad23bf457c4ffeb7a76f9c00a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1054","title":"Fix issues with authentication parameters context being reset","createdAt":"2022-04-28T18:17:28Z"}
{"state":"Merged","mergedAt":"2024-02-05T22:36:51Z","number":10540,"body":"Previously, we were using Amazon’s sagemaker endpoint libraries which is entirely overkill for what we are trying to do (they're designed for handling multiple trained llm models), and hard to test/debug.\r\n\r\nIt also had some serious performance issues when I was testing compared to this implementation.\r\n\r\nThis moves us to FastAPI, an easily testable and modern web framework used by Netflix etc. Also has proper deserialization/serialzation using Pydantic.\r\n\r\nWe now have a docs page as well (for whatever that's worth)\r\n<img width=\"1429\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/3806658/190fe9af-f85d-49ce-b22a-39eb51a4b6ab\">\r\n","mergeCommitSha":"b5c30a7f255f8ce2495036161b0d7182195ebf65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10540","title":"Move instructor endpoint to FastAPI","createdAt":"2024-02-05T22:17:54Z"}
{"state":"Merged","mergedAt":"2024-02-06T23:12:06Z","number":10541,"body":"When a user navigates back from the pending processing email, we navigate users to ask a sample question.\r\n\r\nSame UI as the demo suggested questions but the data source comes from the sample questions API (same as Hub tutorial)\r\n\r\n![CleanShot 2024-02-05 at 14 19 25@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/35467b39-db71-4fa2-adcf-41cc2de60a70)\r\n","mergeCommitSha":"5880f6527435580cdfbb8ad7e7e17a420f19c19b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10541","title":"Sample questions for dashboard","createdAt":"2024-02-05T22:21:24Z"}
{"state":"Merged","mergedAt":"2024-02-06T00:26:54Z","number":10542,"body":"This is satisfy the first requirement in the feedback we got from the verification team over the weekend:\r\n\r\n![CleanShot 2024-02-05 at 14 30 53@2x](https://github.com/NextChapterSoftware/unblocked/assets/1924615/44922b87-1481-4693-b58f-1de24b601e0b)\r\n","mergeCommitSha":"df6798eb628bf0f189b3dcb84d83e2227169b6b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10542","title":"Add description of data use for Google OAuth verification","createdAt":"2024-02-05T22:32:42Z"}
{"state":"Closed","mergedAt":null,"number":10543,"body":"Use GTM, alongside gtag (for now, while we're experimenting).","mergeCommitSha":"63aee16fc14f0b9843861367d5e972a2edf8a444","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10543","title":"Use google tag manager","createdAt":"2024-02-05T22:51:47Z"}
{"state":"Merged","mergedAt":"2024-02-06T04:23:19Z","number":10544,"mergeCommitSha":"8fdbbd4a8d48e6a43c81c2231c37d62aa92ae357","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10544","title":"Make cohort non-nullable on growth/user metrics","createdAt":"2024-02-05T23:13:24Z"}
{"state":"Merged","mergedAt":"2024-02-06T02:39:22Z","number":10545,"body":"Intended to be used for determining if a team is a \"solo\" account or not, for driving metrics.\n\nThe value may be out of date.","mergeCommitSha":"513b93851dda71caaa482534dc298ea3f84fac1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10545","title":"Memoize team member count on team model when refreshing team provider resources","createdAt":"2024-02-05T23:47:03Z"}
{"state":"Merged","mergedAt":"2024-02-06T01:01:58Z","number":10546,"mergeCommitSha":"753a4e1ba4b5cef524fa73f4a37f71216697a084","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10546","title":"Filter not nullOrBlank","createdAt":"2024-02-05T23:52:20Z"}
{"state":"Merged","mergedAt":"2024-02-06T00:24:47Z","number":10547,"body":"The reason the link shows up as `/<team>/thread/null` is a different issue and might be a client side bug","mergeCommitSha":"b1c7531c91b24572f7f30917bd9e81f19734cd69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10547","title":"Fixes markdown link generation for references","createdAt":"2024-02-06T00:06:48Z"}
{"state":"Merged","mergedAt":"2024-02-06T01:25:27Z","number":10548,"mergeCommitSha":"6dab8b9f3f7bec8e91e51c4020d7834527a231b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10548","title":"Fix slack links","createdAt":"2024-02-06T01:04:24Z"}
{"state":"Merged","mergedAt":"2024-02-06T01:06:25Z","number":10549,"mergeCommitSha":"42df32219e15dbe5f1af74dada4b29ee10cec115","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10549","title":"[SKIP TESTS] - include only active repos for inline reference generation","createdAt":"2024-02-06T01:05:45Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:38:45Z","number":1055,"body":"We were hitting a bug in CloudFormation. It didn't come up earlier because we only had one Fifo queue and no standard queues.  Added a workaround until they fix it. \r\n\r\nDeployed these changes manually and queues have been created. \r\n\r\nDev: https://sqs.us-west-2.amazonaws.com/129540529571/unblocked_pr_comments-standard\r\nProd: https://sqs.us-west-2.amazonaws.com/029574882031/unblocked_pr_comments-standard","mergeCommitSha":"86391ad59ab1170a622cf57eb1c249f73345c131","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1055","title":"temp fix for a bug in CloudFront and CDK","createdAt":"2022-04-28T18:34:19Z"}
{"state":"Merged","mergedAt":"2024-02-06T02:21:35Z","number":10550,"mergeCommitSha":"49013e491ed9910df73984070b1022c347fc4db5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10550","title":"Revert","createdAt":"2024-02-06T02:12:50Z"}
{"state":"Merged","mergedAt":"2024-02-06T05:40:04Z","number":10551,"mergeCommitSha":"3f60e243fe5773371197f44277698a430259f127","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10551","title":"Cleanup completed migrations","createdAt":"2024-02-06T05:30:31Z"}
{"state":"Open","mergedAt":null,"number":10552,"body":"Add new next.js-based landing page.  The code is largely the same, just moved to `/web-landing`, with minor changes for next.js compatibility.  Next.js lets us statically generate the page, which means:\r\n\r\n* Static content like preview/meta-information can be added to each page, instead of the ugly webpack hacks\r\n* Page has full rendered content, which means search engines will actually find the content for each page correctly.  Better SEO and page ranking.\r\n* Smaller bundles, quicker first render (might not matter given how resource-heavy the page is)\r\n\r\nI could not get this to work in `/web/src/landing`.  Next.js expects a certain folder layout, and has enough build differences that I couldn't mix this with the old landing page or web projects:\r\n\r\n* In next.js, importing an image via `import MyImage from 'myimg.svg'` does not give you a string back, it gives you back an Image object that contains a `.src` property.\r\n* No react router, the routing is implied by the folder structure\r\n* Use next's `Link` and `useRouter` instead of react-router's `Link` and `useNavigate`","mergeCommitSha":"3de95eac8c8bda7cc02552d2a96dd5da0225524e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10552","title":"New landing page POC","createdAt":"2024-02-06T07:01:51Z"}
{"state":"Merged","mergedAt":"2024-02-06T15:29:19Z","number":10553,"mergeCommitSha":"b076d95e0794fd52c60eb4ae7aa1d8306c803688","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10553","title":"Charts have shared legend handler","createdAt":"2024-02-06T15:18:06Z"}
{"state":"Merged","mergedAt":"2024-02-06T16:31:48Z","number":10554,"mergeCommitSha":"e85d8339c7676955b9fe739eacd2955b4df1bbf4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10554","title":"[SKIP TESTS] - Bump cohere timeout in dev","createdAt":"2024-02-06T16:31:16Z"}
{"state":"Merged","mergedAt":"2024-02-06T16:41:29Z","number":10555,"body":"- REmov etopic ingestion check\r\n- Bye Bye Topic ingestion\r\n","mergeCommitSha":"3d8addf4a93a10391363577a5dc746414cca76d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10555","title":"RemvoeTopic","createdAt":"2024-02-06T16:41:23Z"}
{"state":"Merged","mergedAt":"2024-02-06T17:45:27Z","number":10556,"mergeCommitSha":"2939608dccd1ec3c8c28770f589fe79cdc0227f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10556","title":"Only send content view metrics for inbox views for selected team","createdAt":"2024-02-06T17:44:42Z"}
{"state":"Merged","mergedAt":"2024-02-06T19:45:16Z","number":10557,"mergeCommitSha":"1ac2b8cc2a16db101767682014b7b1a7ddee3e7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10557","title":"Uninstall slack team","createdAt":"2024-02-06T19:14:04Z"}
{"state":"Merged","mergedAt":"2024-02-06T19:48:17Z","number":10558,"mergeCommitSha":"a2f1bb69344485f77f13a052cad387beb895eeac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10558","title":"Fix total team count in metrics page","createdAt":"2024-02-06T19:16:52Z"}
{"state":"Merged","mergedAt":"2024-02-06T19:52:44Z","number":10559,"body":"Add the required google drive warning UI to onboarding and settings\r\n\r\n![CleanShot 2024-02-06 at 11 19 43@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/57102fe4-1342-4b7e-8b9c-fb0308b34b70)\r\n","mergeCommitSha":"cb63d9cbfdffa14b173ef8b5279ae73a35c5e40a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10559","title":"Google drive data share warning UI","createdAt":"2024-02-06T19:21:59Z"}
{"state":"Merged","mergedAt":"2022-05-02T23:44:54Z","number":1056,"body":"Add thread metadata to source points.\r\n\r\nSupport multiple source points on a single row.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/165825912-c3386616-b12b-48b8-8775-c40a800a0675.mp4\r\n\r\n\r\n","mergeCommitSha":"03bc1427eeeb2d0d8bfa81956a374e7f649ef954","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1056","title":"Thread & Multiple source points for web extension","createdAt":"2022-04-28T19:04:41Z"}
{"state":"Merged","mergedAt":"2024-02-10T01:13:33Z","number":10560,"mergeCommitSha":"2e491c2af3aa6829c5a3f345c001aa64cbbf2bef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10560","title":"Add priority to IntegrationEvent","createdAt":"2024-02-06T19:33:48Z"}
{"state":"Merged","mergedAt":"2024-02-06T20:20:59Z","number":10561,"mergeCommitSha":"504d5cf463ef0e9c78fca13355f6524e89c356e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10561","title":"Remove DemoSocialCommentNetwork","createdAt":"2024-02-06T20:04:23Z"}
{"state":"Merged","mergedAt":"2024-02-07T16:48:55Z","number":10562,"mergeCommitSha":"0c7bae33c3e84dd3fa8f52af11bc0b9956c6356b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10562","title":"Add id and hasBeenAsked fields to FollowOnSuggestion API","createdAt":"2024-02-06T20:06:25Z"}
{"state":"Merged","mergedAt":"2024-02-06T21:54:25Z","number":10563,"mergeCommitSha":"fecbc4c4eecb331e8cc1b7af4b799ffa453ea335","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10563","title":"Generate social network during onboarding and regenerate once a day","createdAt":"2024-02-06T21:39:10Z"}
{"state":"Merged","mergedAt":"2024-02-06T22:12:20Z","number":10564,"body":"Added debug routes to dashboard to help testing and verifying UIs\r\n\r\n* `/new/team/<teamId>/debug/missingValidRepos` -- \"Team has no valid repos\" state\r\n* `/new/team/<teamId>/debug/uninstalled` -- \"GitHub app is uninstalled\" state\r\n* `/new/team/<teamId>/debug/suspended` -- \"GitHub app is suspended\" state\r\n* `/new/team/<teamId>/debug/repoSelectionNeeded` -- \"User needs to select repos\" state\r\n* `/new/team/<teamId>/debug/waitingForAdminInstall` -- \"Waiting for GitHub admin to install app\" state\r\n","mergeCommitSha":"4f99766227f9a4552812a1e272ad4e567026c930","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10564","title":"Debug routes for showing different team states","createdAt":"2024-02-06T21:44:19Z"}
{"state":"Merged","mergedAt":"2024-02-06T22:23:26Z","number":10565,"body":"- Changed ALB hostname and the ingress chart to support specifying cluster name. \r\n- Modified ingress yaml to render templated variables before inserting into final template\r\n- Added placeholder for `cluster` to values.yaml\r\n\r\nExisting ALB name pattern is `alb.us-west-2.prod.getunblocked.com`. The new naming patter would be `alb.{clusterName.us-west-2.prod.getunblocked.com`. Changes above default to the original naming scheme until we set the cluster variable in helm command!\r\n\r\nI still have a bunch of other changes before we could remove redundant helm configs. This is just a starting step to unblock the work for new Kube cluster. \r\n\r\n\r\n@rasharab & @richiebres : do you think we should use symlinks instead of putting a tar archive in every service directory? e.g generate it under the helm directory at root and symlink it in all other places ? I am worried about repo size growth long term. Do symlinks work under git for binary files ? ","mergeCommitSha":"4ffa9c909f1263a555a4afd085ef3ffe5dddde4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10565","title":"Prepare for deployment to new cluster","createdAt":"2024-02-06T22:04:44Z"}
{"state":"Merged","mergedAt":"2024-02-08T21:22:48Z","number":10566,"body":"Add Invite toast to QAInputView\r\n\r\n![CleanShot 2024-02-06 at 14 19 46@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/ea3c04af-ef21-41b1-b8dd-fd09fe08ea59)\r\n\r\n![CleanShot 2024-02-06 at 14 19 33@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/8c648400-bc13-4a8f-b07f-176652844e0d)\r\n","mergeCommitSha":"4dc9db04dc405e25f6e0f0b1c04cc738e5a69ee6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10566","title":"Add invite to QA Input","createdAt":"2024-02-06T22:25:21Z"}
{"state":"Merged","mergedAt":"2024-02-06T22:44:10Z","number":10567,"mergeCommitSha":"fec2e72024881487b69a07f7af75c5b6eda2bd55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10567","title":"Fix pinecone client","createdAt":"2024-02-06T22:44:01Z"}
{"state":"Merged","mergedAt":"2024-02-06T22:48:24Z","number":10568,"body":"Need to handle NaN correctly.","mergeCommitSha":"30889688e2e7de16a19d9df6a562f84907ea5851","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10568","title":"Fix cluster topics processing","createdAt":"2024-02-06T22:45:38Z"}
{"state":"Merged","mergedAt":"2024-02-06T22:54:28Z","number":10569,"mergeCommitSha":"34afbac1d0accdaaba749a62d7cb865ba3bdd7f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10569","title":"Pinecone version release upgrade","createdAt":"2024-02-06T22:53:49Z"}
{"state":"Closed","mergedAt":null,"number":1057,"body":"We're seeing some instances where the message content coming from clients can't be deserialized to our protobuf format. We need to deserialize before we can post the message back to GitHub (if the message is for a PR ingested thread)\r\n\r\nAdding some checks here to validate the content at the API layer before doing anything with it so that we fast fail.","mergeCommitSha":"f534a40d750be6591a438454c49dac36ef2b71c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1057","title":"[DO NOT MERGE] Validate message content","createdAt":"2022-04-28T19:10:16Z"}
{"state":"Merged","mergedAt":"2024-02-06T23:15:21Z","number":10570,"mergeCommitSha":"eb9050b1e24a56d7535b536d19089403fd73255e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10570","title":"Fix test","createdAt":"2024-02-06T23:04:30Z"}
{"state":"Merged","mergedAt":"2024-02-07T17:16:51Z","number":10571,"mergeCommitSha":"5c81aba327e3143b81b623f8bb5ddefd88016b8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10571","title":"Move followup suggestions to their own model","createdAt":"2024-02-06T23:14:22Z"}
{"state":"Merged","mergedAt":"2024-02-07T00:50:33Z","number":10572,"mergeCommitSha":"0bf6916f86aa1d83b9ea48023d16418d7bc3a073","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10572","title":"Refactor NotionPageIngestionService to help with debugging","createdAt":"2024-02-06T23:34:49Z"}
{"state":"Merged","mergedAt":"2024-02-07T00:25:12Z","number":10573,"mergeCommitSha":"a7c27208eb33b1bbdfe8837de00e0a832417867d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10573","title":"Update topic mapping code","createdAt":"2024-02-07T00:17:27Z"}
{"state":"Merged","mergedAt":"2024-02-07T17:17:38Z","number":10574,"body":"![CleanShot 2024-02-06 at 16 30 19@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b35045d4-a754-4ed1-9698-bafb735fdca2)\r\n![CleanShot 2024-02-06 at 16 29 51@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/c824ecd2-ca4e-48a8-8138-93ac087dff4f)\r\n","mergeCommitSha":"716eb2f59330f7586f9ab3e4dfbd55bc0a2f58e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10574","title":"Add 'Try Unblocked' bumpers to \"missing repos\" UI","createdAt":"2024-02-07T00:31:53Z"}
{"state":"Merged","mergedAt":"2024-02-07T01:24:49Z","number":10575,"body":"- Disable backfilled topics\r\n- Disable bulk topic ingestion mpping\r\n","mergeCommitSha":"b3ed792fcdfcc1927025c3ed07bce36db14bd320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10575","title":"DisableBackFilledTopics","createdAt":"2024-02-07T00:34:16Z"}
{"state":"Merged","mergedAt":"2024-02-07T04:13:26Z","number":10576,"body":"Fire an alarm when a new team has been processing for longer than expected.","mergeCommitSha":"920c2bb675cebe98537416244408fdd393c950c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10576","title":"Introduce onboarding alarm","createdAt":"2024-02-07T01:17:22Z"}
{"state":"Merged","mergedAt":"2024-02-07T02:02:48Z","number":10577,"mergeCommitSha":"42fea9eba039ffd925c801a5d6f5f662985f2e63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10577","title":"Swallow 400s except 429s when getting notion blocks","createdAt":"2024-02-07T01:53:38Z"}
{"state":"Merged","mergedAt":"2024-02-07T04:45:17Z","number":10578,"mergeCommitSha":"ae259f6eea1d576de03be1965305e7c4b916e173","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10578","title":"Smoother way to create ExclusiveBackgroundJobs","createdAt":"2024-02-07T04:28:04Z"}
{"state":"Merged","mergedAt":"2024-02-07T04:37:45Z","number":10579,"mergeCommitSha":"376974e8e25889f7c884013e78f3db5483d7041c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10579","title":"Increase expiration for redis notion cache","createdAt":"2024-02-07T04:29:40Z"}
{"state":"Merged","mergedAt":"2022-05-02T18:39:35Z","number":1058,"body":"- Removed retool user and public key \r\n- Removed bastion host access to prod db \r\n\r\nThis change will cause our bastion host to be recreated. I will merge it after hours to avoid breaking our CI/CD","mergeCommitSha":"192808baffc13be3abd9f6e7191486cb9cb6d9fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1058","title":"remove retool access","createdAt":"2022-04-28T19:42:23Z"}
{"state":"Merged","mergedAt":"2024-02-07T07:10:57Z","number":10580,"body":"## Problem\r\nAside from message event consumers — where parallelism is handled well by the ActiveMQ system — the remaining background jobs are DB-based and will almost certainly contend with each other when there are multiple distributed instances.\r\n\r\nTo illustrate, most of these DB-based jobs take this form of:\r\n 1. list teams (or repos, or whatever), which is a deterministic query\r\n 2. iterate over the collection\r\n 3. do work\r\n\r\n## Change\r\n- This change ensures that the parallel instances will not contend by using exclusive jobs more widely.\r\n- Also changed the job period, which was very frequent in some case (eg: 1 second)\r\n\r\n## Future work\r\nThis change does not fully address the problems:\r\n- in most cases the period of the background job is set to run once every _N_ hours. Authors may not realize that the job will actually run every _N/P_ times if there are _P_ instances, counter to their intention.\r\n- iterating over large collections, like teams, is still possible right now, but these jobs are very likely to be interrupted by deployments, and consume large amounts of memory as we scale.\r\n\r\nA better approach is to make the job fine-grained enough to be parallizable. See the `RepoIngestionService` pattern as an alternative, where _N_ is always 1.","mergeCommitSha":"cf802978ba2d35a63b910b9f96d1a9f7c25de012","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10580","title":"Use ExclusiveBackgroundJobs more widely to reduce distributed contention","createdAt":"2024-02-07T05:29:21Z"}
{"state":"Merged","mergedAt":"2024-02-07T06:07:33Z","number":10581,"mergeCommitSha":"f7f22a8be6b942aa6d1bf8d42836adb255626bd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10581","title":"Drop max depth for crawling websites to 25","createdAt":"2024-02-07T05:57:18Z"}
{"state":"Merged","mergedAt":"2024-02-08T19:12:29Z","number":10582,"mergeCommitSha":"634c7d11f9164159624aa2304f7a74efa330f858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10582","title":"Delete topic metrics stuff","createdAt":"2024-02-07T06:16:53Z"}
{"state":"Merged","mergedAt":"2024-02-07T22:58:14Z","number":10583,"mergeCommitSha":"7cce5876366ad169c892498c1ab667de19562713","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10583","title":"Delete search suggestion stuff","createdAt":"2024-02-07T06:24:19Z"}
{"state":"Merged","mergedAt":"2024-02-07T07:08:42Z","number":10584,"body":"Slack channels API uses 200 for page size. We currently have 206 channels, and do no paging, which breaks the ability to send messages to the last 6 channels.\r\n\r\nBetter pagiantion primitive needed for Slack. See for example our transparent `com.nextchaptersoftware.ktor.client.Pagination` classes.\r\n\r\nLogs:\r\n```\r\nj.l.IllegalStateException: Could not find slack channel by name\r\n\tat c.n.s.SlackNotifier$toChannelId$2$1.invokeSuspend(SlackNotifier.kt:95)\r\n\tat c.n.s.SlackNotifier$toChannelId$2$1.invoke(SlackNotifier.kt)\r\n\tat c.n.s.SlackNotifier$toChannelId$2$1.invoke(SlackNotifier.kt)\r\n\tat c.n.c.LocalOptionalReadThroughCache$getOrCompute$2.invokeSuspend(LocalOptionalReadThroughCache.kt:21)\r\n\tat c.n.c.LocalOptionalReadThroughCache$getOrCompute$2.invoke(LocalOptionalReadThroughCache.kt)\r\n\tat c.n.c.LocalOptionalReadThroughCache$getOrCompute$2.invoke(LocalOptionalReadThroughCache.kt)\r\n\tat i.g.r.c.RealCache$get$3.invokeSuspend(RealCache.kt:110)\r\n\tat i.g.r.c.RealCache$get$3.invoke(RealCache.kt)\r\n\tat i.g.r.c.RealCache$get$3.invoke(RealCache.kt)\r\n\tat i.g.r.c.KeyedSynchronizer.synchronizedFor(KeyedSynchronizer.kt:25)\r\n\tat i.g.r.c.RealCache.get(RealCache.kt:99)\r\n\tat c.n.c.LocalOptionalReadThroughCache.getOrCompute(LocalOptionalReadThroughCache.kt:20)\r\n\tat c.n.s.SlackNotifier.toChannelId(SlackNotifier.kt:79)\r\n\tat c.n.s.SlackNotifier.alertOnboarding(SlackNotifier.kt:155)\r\n\tat c.n.a.j.OnboardingAlarmJob.raiseAlarmConditionally(OnboardingAlarmJob.kt:80)\r\n\tat c.n.a.j.OnboardingAlarmJob.access$raiseAlarmConditionally(OnboardingAlarmJob.kt:24)\r\n\tat c.n.a.j.OnboardingAlarmJob$raiseAlarmConditionally$1.invokeSuspend(OnboardingAlarmJob.kt)\r\n```","mergeCommitSha":"678c69f73067c329caacb76d923c7d0e56596c32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10584","title":"Dirty fix for Slack channel pagination bug","createdAt":"2024-02-07T06:47:18Z"}
{"state":"Merged","mergedAt":"2024-02-07T08:08:28Z","number":10585,"mergeCommitSha":"742cc29f0910285053bbf7f7a85a596c0f15ab94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10585","title":"No exclusive background job in queue-service, because no redis access","createdAt":"2024-02-07T08:08:08Z"}
{"state":"Merged","mergedAt":"2024-02-07T12:02:22Z","number":10586,"mergeCommitSha":"b7c59e857a4f78580cb0e71edf91dddd519b0c0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10586","title":"Fix build","createdAt":"2024-02-07T12:02:11Z"}
{"state":"Merged","mergedAt":"2024-02-07T12:04:13Z","number":10587,"mergeCommitSha":"7ad90db41b907e26bf6a5a1b68a7536a9c6603fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10587","title":"Fix build","createdAt":"2024-02-07T12:04:01Z"}
{"state":"Merged","mergedAt":"2024-02-07T12:18:32Z","number":10588,"mergeCommitSha":"dea1876ff424eda3b07d7bd858a2a7a3129be911","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10588","title":"Just work please…","createdAt":"2024-02-07T12:18:25Z"}
{"state":"Merged","mergedAt":"2024-02-07T16:59:33Z","number":10589,"mergeCommitSha":"e21fb3fb42d4cf68b13c84bf1b2908708e14e66f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10589","title":"Fix null route","createdAt":"2024-02-07T16:58:45Z"}
{"state":"Merged","mergedAt":"2022-04-28T21:21:34Z","number":1059,"body":"Per new designs from Ben:\r\n<img width=\"310\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165834038-576b0ab3-39ad-4b45-8c75-95e6cb72ffca.png\">\r\n<img width=\"213\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165834112-83b9d6a7-ddf8-4f6b-b69f-74be01e92ea4.png\">\r\n","mergeCommitSha":"96a6d7c70892bfd4238f2b3ea034c0d678d358ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1059","title":"Update unread marker in sidebar","createdAt":"2022-04-28T19:49:45Z"}
{"state":"Merged","mergedAt":"2024-02-07T17:21:12Z","number":10591,"mergeCommitSha":"5f814d7aa26c826336f09fc2e94a8781020fee97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10591","title":"Redirect /careers to /careers/data-engineer","createdAt":"2024-02-07T17:07:58Z"}
{"state":"Merged","mergedAt":"2024-02-07T19:37:27Z","number":10592,"mergeCommitSha":"5ff5550f1722e5a72b9ba8c2b717e41022e0fc65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10592","title":"Modify data engineer JD to Analytics Engineer","createdAt":"2024-02-07T18:29:34Z"}
{"state":"Merged","mergedAt":"2024-02-07T19:49:50Z","number":10593,"mergeCommitSha":"6ff530fae55531f3616b5cb842d7ee0200f8154b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10593","title":"Fix analytics engineer urls","createdAt":"2024-02-07T18:56:27Z"}
{"state":"Closed","mergedAt":null,"number":10594,"body":"![CleanShot 2024-02-07 at 11 06 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/858772/9c04c2bb-7b6c-4073-89c0-a4b5cd241fbf)\r\n\r\n\r\n![CleanShot 2024-02-07 at 11 06 50@2x](https://github.com/NextChapterSoftware/unblocked/assets/858772/7d250dc6-b3be-4b8b-b1e7-868689da7ea3)\r\n\r\n","mergeCommitSha":"b46238ea6411b4562d4de287591dfd70bc1f7254","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10594","title":"Add careers button on landing page","createdAt":"2024-02-07T19:07:31Z"}
{"state":"Merged","mergedAt":"2024-02-07T19:37:00Z","number":10595,"body":"## problem\r\nThis page does not show non-PR threads:\r\nhttps://admin.prod.getunblocked.com/teams/e0191c86-fa9d-431b-9b96-b42472b69a71/threads\r\n\r\n## change\r\nProblem was that repoId was optional, however we we still INNER JOINing on that table, resulting in zero result rows.","mergeCommitSha":"e3f800c7c37cad4571be696bdceead15b9cd7e14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10595","title":"Fix admin web threads view","createdAt":"2024-02-07T19:27:01Z"}
{"state":"Merged","mergedAt":"2024-02-07T21:55:41Z","number":10596,"mergeCommitSha":"7729f5e4c4161623924163b5f46604cf0e6370e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10596","title":"Add button in admin console to delete thread unread","createdAt":"2024-02-07T19:34:29Z"}
{"state":"Merged","mergedAt":"2024-02-07T20:25:45Z","number":10597,"body":"See explanation:\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1707335898701699","mergeCommitSha":"c7ecc609b657c0cdd0cfb429f8343c394525b9ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10597","title":"Fix social network query, due to `column neq true` where column is nullable","createdAt":"2024-02-07T20:08:31Z"}
{"state":"Merged","mergedAt":"2024-02-07T21:56:52Z","number":10598,"body":"We have generally only shown suggestions for the last bot response.  This PR shows all suggestions, except those that have already been asked.\r\n\r\n* Propagate the question ID throughout the call chain, so when we ask a follow-on question, so we can pass it to the API\r\n* Don't show follow-on questions that have already been asked (where `FollowOnSuggestion.hasBeenAsked===true`)\r\n* Add overlay so that the asked follow-on question disappears immediately\r\n* For existing threads (where `FollowOnSuggestion.hasBeenAsked===undefined`), only display on the last bot response.  This keeps the previous behaviour for old threads.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/71e48822-2dcd-418e-b66f-ace674b93c90\r\n\r\n","mergeCommitSha":"01c332956953d1c04ea6ef0c0595cad0130fb2b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10598","title":"Show previous suggested questions","createdAt":"2024-02-07T20:11:30Z"}
{"state":"Merged","mergedAt":"2024-02-07T21:03:17Z","number":10599,"body":"See explanation:\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1707335898701699","mergeCommitSha":"250ccabc2c0ff5d0d9a1b0ec597416302edbbd97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10599","title":"Fix more instances of `column neq true` where column is nullable","createdAt":"2024-02-07T20:19:07Z"}
{"state":"Merged","mergedAt":"2022-01-22T01:19:46Z","number":106,"body":"This reverts commit b87ca28f9d5a3d93fd1d3d3171f40795ee3d1f95 from https://github.com/Chapter2Inc/codeswell/pull/105.\r\n\r\nDo not merge this right away. Once everyone has run `./gradlew check` locally then this can be safely merged.","mergeCommitSha":"fed0fc59cf35024d3d4f793f945115799e764389","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/106","title":"Revert \"hack\"","createdAt":"2022-01-22T01:05:24Z"}
{"state":"Merged","mergedAt":"2022-04-28T22:27:09Z","number":1060,"body":"Slight tweaks to sidebar styling.\r\n\r\nRemove subheader\r\nFixed header height to match GH header height\r\nIncrease sidebar width to 260\r\n<img width=\"735\" alt=\"CleanShot 2022-04-28 at 13 32 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/165840756-b85d329f-7d00-43b7-acd1-64e5f20e3970.png\">\r\n\r\n","mergeCommitSha":"0f5c9d2bde988f59c0c7fdc6e1e07aeb7d9d34e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1060","title":"Sidebar extension Styling","createdAt":"2022-04-28T20:32:52Z"}
{"state":"Merged","mergedAt":"2024-02-07T20:46:17Z","number":10600,"body":"We should not be sending slack Q&A to team QA validation channel outside of channels selected by team.\r\n","mergeCommitSha":"319165848599c136660fc267930b6585422d6325","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10600","title":"Do not send to shared validation channel if not in selected list","createdAt":"2024-02-07T20:22:53Z"}
{"state":"Merged","mergedAt":"2024-02-08T06:56:19Z","number":10601,"body":"This is controlled by repo level environment variable called `DEPLOY_TO_NEW_CLUSTER`","mergeCommitSha":"5045a2481577e62c85abaf052ea7d64f1f399ed7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10601","title":"[SKIP TESTS] Add deployment steps for new cluster.","createdAt":"2024-02-07T21:44:20Z"}
{"state":"Merged","mergedAt":"2024-02-08T00:47:12Z","number":10602,"body":"If the thread has a message that was created before the PersonModel was created, team was enabled, or the team member was added, don't create a thread unread.\r\n\r\nThis will prevent re-ingestion of a PR from backfilling the thread unread for an old thread. We were observing this issue today with a user on the TravelPerk team where the PR was updated on GitHub's side and we reingested as a result. \r\n\r\nhttps://app.intercom.com/a/inbox/crhakcyc/inbox/conversation/5230\r\nhttps://admin.prod.getunblocked.com/teams/e766bb41-ec08-4334-995b-bdf2915241ea/members/77370445-5cc8-4cb8-8b83-db903aa8acab/threads?Mine","mergeCommitSha":"c517d9631ccc3e624bde3f47ee0f633e079f9e35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10602","title":"Only create thread unread when the thread has a message that was created after the user was enabled in Unblocked","createdAt":"2024-02-07T21:54:17Z"}
{"state":"Merged","mergedAt":"2024-02-07T22:59:35Z","number":10603,"mergeCommitSha":"24b6bc41e7c46020e05706828c0872e391851a79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10603","title":"Slack Flow","createdAt":"2024-02-07T22:26:35Z"}
{"state":"Merged","mergedAt":"2024-02-07T23:44:34Z","number":10604,"body":"…handler (#10478)\"\r\n\r\nThis reverts commit 9555c5e9505d71b75c69e25edfa3ae0076f68a08.","mergeCommitSha":"4890a5afde4728a5f319b7b21dd06047d73dec96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10604","title":"Revert \"Check whether website is enabled for ingestion sooner in the …","createdAt":"2024-02-07T23:31:55Z"}
{"state":"Merged","mergedAt":"2024-02-08T00:28:44Z","number":10605,"body":"First stab at a \"try unblocked\" standalone endpoint.  This is accessible under `https://getunblocked.com/dashboard/try-unblocked`.  I extracted most of the demo template list into a separate reusable component, which both the popup and the standalone view use.\r\n\r\nThe login UI tweaks are not done yet, that will been the next PR.","mergeCommitSha":"94c58451468bf2f596854d34edfb3e0e545a46b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10605","title":"\"Try Unblocked\" endpoint","createdAt":"2024-02-08T00:09:33Z"}
{"state":"Merged","mergedAt":"2024-02-10T00:55:09Z","number":10606,"body":"Customer data is encrypted at rest in Pinecone, and in transit to Pinecone.\r\n\r\nOnly Unblocked services with the `unblocked-content-service-secrets-env` have access to read the encryption key:\r\n- embedding-service (encrypt new vector content)\r\n- search-service (decrypt vector content for answers)\r\n- api-service (decrypt vector content for related documents API)\r\n- adminweb-service (decrypt vector content for debugging)\r\n\r\n## Rollout plan\r\nEncryption is lazy; all **new** vectors will have encrypted content. Existing vectors with plaintext metadata content will not be modified. The new serverless indexes will be 100% encrpypted, since they will contain only new vectors. And we are retiring the Pod indexes in the next few weeks once all teams have transitioned to serverless.\r\n\r\nThis change will be staged as follows across several PRs:\r\n\r\n1. In this PR, decryption is implemented in KT and enabled. Encryption is implemented in KT but not enabled.\r\n2. Then, encryption and decryption will be implemented in PY\r\n3. Then, encryption will be enabled in DEV\r\n4. Then, encryption will be enabled in PROD","mergeCommitSha":"c3f0a49d9686673fa8cf672722578c259bc52975","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10606","title":"Vector metadata content encryption","createdAt":"2024-02-08T00:12:07Z"}
{"state":"Merged","mergedAt":"2024-02-08T00:34:49Z","number":10607,"mergeCommitSha":"8a46dd576c8eb565fbb5b2e0e2415b50f51c39ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10607","title":"[SKIP TESTS] Add logging to WebIngestionEventHandler","createdAt":"2024-02-08T00:23:11Z"}
{"state":"Merged","mergedAt":"2024-02-08T00:55:08Z","number":10608,"mergeCommitSha":"022a0d5e6971462cf574a75ff0b13aef2273dd88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10608","title":"Increase priority for initial ingest of web sites","createdAt":"2024-02-08T00:43:35Z"}
{"state":"Merged","mergedAt":"2024-02-08T01:30:48Z","number":10609,"mergeCommitSha":"5e042af1c10d7a9c4f68f94c0a07a5b691b4929d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10609","title":"Fix \"try unblocked\" search row background","createdAt":"2024-02-08T01:18:30Z"}
{"state":"Merged","mergedAt":"2022-04-28T21:05:06Z","number":1061,"body":"- https://chapter2global.slack.com/archives/C031MKRPZSQ/p1650387856844799?thread_ts=1650386063.467609&cid=C031MKRPZSQ\r\n- https://chapter2global.slack.com/archives/C031MKRPZSQ/p1651167837144859?thread_ts=1651166663.897499&cid=C031MKRPZSQ","mergeCommitSha":"af66d5e2cfc1cac26e7fa8eec89b88030f263f77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1061","title":"Suppress flaky BackgroundJobsTest","createdAt":"2022-04-28T20:52:16Z"}
{"state":"Merged","mergedAt":"2024-02-20T04:51:24Z","number":10610,"mergeCommitSha":"60a55e529b898bdd0c62e688701f008c5d678179","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10610","title":"Define embedding configuration structures","createdAt":"2024-02-08T01:31:57Z"}
{"state":"Merged","mergedAt":"2024-02-08T01:47:51Z","number":10611,"mergeCommitSha":"c377c9558fe206830cc6747fdcb20fe5075a1f49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10611","title":"[SKIP TESTS] Revert UnreadsPage","createdAt":"2024-02-08T01:38:42Z"}
{"state":"Merged","mergedAt":"2024-02-08T04:21:31Z","number":10612,"mergeCommitSha":"b386439aa0b65d9146e6bd9eace9827de1ab9155","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10612","title":"Only enqueue web event if it doenst exist","createdAt":"2024-02-08T04:03:41Z"}
{"state":"Merged","mergedAt":"2024-02-15T03:07:55Z","number":10613,"mergeCommitSha":"48988ecd242dc86cd343e6a719c180b73a80e622","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10613","title":"Add PineconeDocumentKey and tests","createdAt":"2024-02-08T07:20:12Z"}
{"state":"Merged","mergedAt":"2024-02-08T07:43:32Z","number":10614,"mergeCommitSha":"97424cae6d41132aabfd845438977eec4d73c7d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10614","title":"[SKIP TESTS] fix mistake in variable name","createdAt":"2024-02-08T07:35:05Z"}
{"state":"Merged","mergedAt":"2024-02-08T17:52:14Z","number":10615,"mergeCommitSha":"6f1c4475ca0760bdd960f03ac62ed7bab83d13e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10615","title":"Add tests for WebIngestionEventHandler","createdAt":"2024-02-08T15:57:31Z"}
{"state":"Merged","mergedAt":"2024-02-08T20:52:57Z","number":10616,"body":"ViewRouter has a default state of \"empty\" to prevent momentary flickers of UI on load.\r\n\r\nIn VSCode, we were not setting the sidebar state to \"ask\" once auth goes through.\r\n\r\nHere's where it is set in IntelliJ\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/main/jetbrains/src/sidebar/extension.sidebar.ts#L52C1-L53C1","mergeCommitSha":"7fc919a351f21921a7c6875a1cf2109982095f65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10616","title":"Fix VSCode intiial sidebar","createdAt":"2024-02-08T17:18:49Z"}
{"state":"Merged","mergedAt":"2024-02-08T18:25:33Z","number":10617,"mergeCommitSha":"043d4c8fe477fab93999b75445c9be522ffbe610","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10617","title":"[SKIP TESTS] Ignore errors for new cluster and only run new job for Dev","createdAt":"2024-02-08T18:13:17Z"}
{"state":"Merged","mergedAt":"2024-02-12T22:40:37Z","number":10618,"mergeCommitSha":"21fa3bf91680e9c0587faa32bd905c07da41459b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10618","title":"GroupId for google embeddings should be the parent shared drive or folder id but null if no parent","createdAt":"2024-02-08T18:47:39Z"}
{"state":"Merged","mergedAt":"2024-02-08T19:54:24Z","number":10619,"mergeCommitSha":"c343bd084851df249e7d0832f171da4d24a2d5a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10619","title":"Submodule bump and update analytics engineer share image","createdAt":"2024-02-08T19:42:47Z"}
{"state":"Merged","mergedAt":"2022-04-28T23:36:16Z","number":1062,"body":"Still need for `local` env, so not removing much here.\r\n\r\nNote: I do think we have some lingering dependencies from client code on repo and team IDs. I don't think we'll fully realize those dependencies until we nuke the DEV DB.","mergeCommitSha":"b1c573e0c908adeb59186dd14e66add1b277a086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1062","title":"Remove hardcoded fixtures from DEV","createdAt":"2022-04-28T21:25:24Z"}
{"state":"Merged","mergedAt":"2024-02-08T21:02:52Z","number":10620,"mergeCommitSha":"3ff18254b2daa99bedbcb031a3e181dd7cc33d17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10620","title":"Limit total number of pages crawled per site","createdAt":"2024-02-08T20:00:46Z"}
{"state":"Closed","mergedAt":null,"number":10621,"body":"…0620)\"\r\n\r\nThis reverts commit 3ff18254b2daa99bedbcb031a3e181dd7cc33d17.","mergeCommitSha":"de6c0abbb95f3e117c9add05cbcd64bdb2b5fdff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10621","title":"[SKIP TESTS] Revert \"Limit total number of pages crawled per site (#1…","createdAt":"2024-02-08T21:45:01Z"}
{"state":"Merged","mergedAt":"2024-02-08T22:02:16Z","number":10622,"mergeCommitSha":"1f2efddc275152e580c3999b272535fdfbd5a728","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10622","title":"[SKIP TESTS] Dont use expireArgs","createdAt":"2024-02-08T21:50:39Z"}
{"state":"Merged","mergedAt":"2024-02-09T00:33:02Z","number":10623,"mergeCommitSha":"e58d4af755f61e88b5de6a00f4c72df53e0136be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10623","title":"Allow following https links when scraping site","createdAt":"2024-02-09T00:18:43Z"}
{"state":"Merged","mergedAt":"2024-02-09T01:31:39Z","number":10624,"mergeCommitSha":"16dee25c71e6510b70b3c48af509f7cb20b5a83e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10624","title":"Allow toggling disablement of sites for ingestion","createdAt":"2024-02-09T01:08:16Z"}
{"state":"Merged","mergedAt":"2024-02-09T06:03:06Z","number":10625,"mergeCommitSha":"df7a22a82bdc94db476c84f3a73b1bb79e99fe28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10625","title":"[SKIP TESTS] Remove logging","createdAt":"2024-02-09T05:51:27Z"}
{"state":"Open","mergedAt":null,"number":10626,"mergeCommitSha":"6bf493b4465a2f35fc56da19f51905dc7cc1aaf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10626","title":"Fix pseudo-random warning from CASA scan","createdAt":"2024-02-09T06:24:35Z"}
{"state":"Merged","mergedAt":"2024-02-09T08:15:04Z","number":10627,"mergeCommitSha":"b0157b94f62d72cf248c2a0a8ca65c56f105d50a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10627","title":"Upgrade to Redis engine version 7.1","createdAt":"2024-02-09T08:14:20Z"}
{"state":"Merged","mergedAt":"2024-02-09T08:31:34Z","number":10628,"mergeCommitSha":"7d09622db741b5b70c42fd0f887514fa0504c7d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10628","title":"forgot to update a config param","createdAt":"2024-02-09T08:30:47Z"}
{"state":"Merged","mergedAt":"2024-02-09T17:32:10Z","number":10629,"body":"Fix stupid typo.","mergeCommitSha":"7a447418360010179e77964ac0ad07b57456c4b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10629","title":"Fix generateAndSaveSocialNetworkModel which only ran if there was a parent coroutine","createdAt":"2024-02-09T17:31:44Z"}
{"state":"Merged","mergedAt":"2022-04-28T23:02:26Z","number":1063,"body":"Based on threadID in #, open discussion.\r\nOnly works for thread IDs atm. Not sure how we're going to support messages with hash parameters...\r\n\r\nLogic currently owned by extension's sidebar as there's some state that needs to be modified there.\r\n\r\nTODO: better error state.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/165848493-ee1e5a69-f6dc-481c-aa6c-4b9ec116dba9.mp4\r\n\r\n\r\n","mergeCommitSha":"782fc253b2c5462032503488835de6dcab30b6c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1063","title":"Open discussion based on URL for web extension","createdAt":"2022-04-28T21:26:04Z"}
{"state":"Merged","mergedAt":"2024-02-09T23:18:33Z","number":10630,"mergeCommitSha":"1de77ba87f0640dc90ce39a78ebe9cfeae302d54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10630","title":"Set expiration on limit counters only if not set","createdAt":"2024-02-09T18:06:48Z"}
{"state":"Merged","mergedAt":"2024-02-13T00:04:36Z","number":10631,"body":"The description will be generated server side\r\n\r\n![image](https://github.com/NextChapterSoftware/unblocked/assets/1924615/a16365b6-b6ff-49cb-9d5b-50fa45c8fa4d)\r\n","mergeCommitSha":"4e675c3141d9495aba3b4be19c200a3589ff4327","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10631","title":"Add integration processing statuses to getTeamStatus response","createdAt":"2024-02-09T18:39:21Z"}
{"state":"Merged","mergedAt":"2024-02-09T19:05:19Z","number":10632,"body":"We weren't using the log object at all here.  Hard to tell if there's a risk of recursion here, ie if the logger itself throws an unhandled exception, but if that happens we are probably in a very bad state anyways...","mergeCommitSha":"a801992714814618c7800cf3be8900cda53b91f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10632","title":"Log to logger on agent unhandled exception handler","createdAt":"2024-02-09T18:53:58Z"}
{"state":"Merged","mergedAt":"2024-02-09T19:11:04Z","number":10633,"mergeCommitSha":"5e5a20c93b2f4bb3c8655816dfaa755fbe5d2795","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10633","title":"Add logging to WebIngestionUrlValidationService","createdAt":"2024-02-09T18:55:53Z"}
{"state":"Merged","mergedAt":"2024-02-09T21:34:53Z","number":10634,"body":"Doubt this'll fix Later's issue (https://chapter2global.slack.com/archives/C0634F92NCU/p1707503532588759) but we should be doing this anyways.","mergeCommitSha":"b2669f9e8e2cc1e7feeebf2a52a50300f2c2b35b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10634","title":"Set user agent and accept headers for web ingestion client","createdAt":"2024-02-09T20:06:08Z"}
{"state":"Merged","mergedAt":"2024-02-09T20:21:24Z","number":10635,"body":"Changed the content and assets from auto response to mentioning @ Unblocked.\r\n\r\n![CleanShot 2024-02-09 at 12 08 17@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/34fa5e30-c7fc-4f60-8f51-e5ccb6f12785)\r\n","mergeCommitSha":"b88ce8157cd8d20334f7462417b5f785d1fb14e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10635","title":"Landing page changes for Slack approval process","createdAt":"2024-02-09T20:09:43Z"}
{"state":"Merged","mergedAt":"2024-02-10T01:22:35Z","number":10636,"body":"The logging role is too broad-sweeping and we should validate that the kotlin logger is in the import list before applying.\r\n","mergeCommitSha":"a235b4ad9395192d5d299b79d6be68b29355a00d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10636","title":"Add import validator for logging rule","createdAt":"2024-02-09T21:30:04Z"}
{"state":"Merged","mergedAt":"2024-02-09T22:22:14Z","number":10637,"mergeCommitSha":"0d9a3129999f0f0bc3c4a13aec517848c8bb743a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10637","title":"Fix","createdAt":"2024-02-09T22:10:47Z"}
{"state":"Merged","mergedAt":"2024-02-09T22:18:50Z","number":10638,"mergeCommitSha":"6451b72dc61e7d5a2c7ccf887c5e2db1db1444fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10638","title":"Do not re-render previously rendered data tables after the DOM has been settled","createdAt":"2024-02-09T22:17:56Z"}
{"state":"Merged","mergedAt":"2024-02-10T00:25:28Z","number":10639,"body":"Add a \"Copy Link\" button to the admin UI template page -- this is how you will get the template link to send to other people.","mergeCommitSha":"8c5ee946e9bfa7ccd1429e832502d762c6d4f38c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10639","title":"Add copy template link in admin UI","createdAt":"2024-02-09T23:17:03Z"}