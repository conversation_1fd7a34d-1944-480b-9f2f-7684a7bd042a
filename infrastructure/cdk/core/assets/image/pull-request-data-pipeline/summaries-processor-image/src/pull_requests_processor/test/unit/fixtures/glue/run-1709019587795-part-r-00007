{"state":"Merged","mergedAt":"2022-01-28T23:45:40Z","number":171,"mergeCommitSha":"fa98ea14d8c08cfcde3ce10996366de356dc92cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/171","title":"Tests for Chat models","createdAt":"2022-01-28T23:32:02Z"}
{"state":"Merged","mergedAt":"2022-06-09T05:36:31Z","number":1710,"mergeCommitSha":"1956c8b4f20313bdb4a2b6f14f218306ccd1c3cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1710","title":"Update","createdAt":"2022-06-09T05:35:36Z"}
{"state":"Merged","mergedAt":"2022-06-09T06:51:49Z","number":1711,"body":"Dev verified ","mergeCommitSha":"3ec42e4ce66f6341ca3bec34ca37b2e9b0cc048d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1711","title":"Turn on db connection pools in prod","createdAt":"2022-06-09T05:37:27Z"}
{"state":"Merged","mergedAt":"2022-06-09T06:11:56Z","number":1712,"mergeCommitSha":"1381003b1b8ff2e64e57d9648d2c34aad8d20276","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1712","title":"Drop max db connection pool size to 5","createdAt":"2022-06-09T06:11:33Z"}
{"state":"Merged","mergedAt":"2022-06-09T16:12:16Z","number":1713,"mergeCommitSha":"c1d558de8cc2081b4ea5615adcb888af846bd730","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1713","title":"Remove hikari's db connection pool","createdAt":"2022-06-09T15:54:16Z"}
{"state":"Merged","mergedAt":"2022-06-09T16:28:01Z","number":1714,"body":"Use extension URLs for vscode fallback and extension preference\n","mergeCommitSha":"3e74e50f47572fb8a69cb91b75126ba77f049e5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1714","title":"Use correct extension urls from the hub","createdAt":"2022-06-09T16:14:01Z"}
{"state":"Merged","mergedAt":"2022-06-09T18:12:29Z","number":1715,"body":"A brilliant man by the name of @matthewjamesadam suggested I add this utility.\r\nHe was right...AGAIN.","mergeCommitSha":"9c4ec1b06473aaa40f89f9323fc640ea0cf81b4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1715","title":"Add compact map utility","createdAt":"2022-06-09T17:48:17Z"}
{"state":"Merged","mergedAt":"2022-06-09T18:30:10Z","number":1716,"mergeCommitSha":"b8ad30b8b5bdcf2d5a1ed54852ab8ee9651047e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1716","title":"Fix lint","createdAt":"2022-06-09T18:11:51Z"}
{"state":"Merged","mergedAt":"2022-06-09T19:07:30Z","number":1717,"body":"Some state from the main \"onboarding\" installation leaked into the sidebar when doing the refactor.\r\n\r\nRemoved unnecessary logic and added a command to trigger sidebar state.","mergeCommitSha":"c7483849c6d65ee70830fad810549705d26012b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1717","title":"Fix sidebar continue when tutorial has occurred","createdAt":"2022-06-09T18:26:32Z"}
{"state":"Merged","mergedAt":"2022-06-10T17:22:51Z","number":1718,"body":"Updates the `getThreadsForMe` operation to add a new `includeThreadsForMyOpenPrs` parameter. This will be parameter is optional and `true` by default so as to not break old clients. New clients will need to pass in true for this parameter in order to exclude threads from the user's open PRs.\r\n\r\n","mergeCommitSha":"3773c0a0925de9ab4b775285702b85f766a10b33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1718","title":"Update getThreadsForMe","createdAt":"2022-06-09T18:46:40Z"}
{"state":"Merged","mergedAt":"2022-06-09T19:31:54Z","number":1719,"mergeCommitSha":"5ccfad4a47a9307810e927ebe25ec951d40ceb70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1719","title":"remove dead field","createdAt":"2022-06-09T18:50:12Z"}
{"state":"Merged","mergedAt":"2022-01-29T00:16:00Z","number":172,"body":"Follow on from https://github.com/Chapter2Inc/codeswell/pull/171#discussion_r794953763","mergeCommitSha":"1a69f24ad8a5ab24baea222213500dade448542b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/172","title":"Add createdAt accessors","createdAt":"2022-01-28T23:41:54Z"}
{"state":"Merged","mergedAt":"2022-06-09T20:44:53Z","number":1720,"body":"Completes UNB-210","mergeCommitSha":"ae735ccb62e64cbcdc9ce80962e8339194d4e447","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1720","title":"Add open PR query param to hub request","createdAt":"2022-06-09T19:37:32Z"}
{"state":"Merged","mergedAt":"2022-06-09T19:50:26Z","number":1721,"body":"Listener needs to be cleaned up before onComplete callback is made and sidebar is updated.","mergeCommitSha":"d505b76b5ecb3d819a81e04d3e85aad7b235c9c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1721","title":"Cleanup listener before triggering complete","createdAt":"2022-06-09T19:42:20Z"}
{"state":"Merged","mergedAt":"2022-06-09T20:33:40Z","number":1722,"body":"This jdbc driver is far more comprehensive to HIkari.\r\nIt allows for much more granular control over connections and termination of connections.\r\n\r\nhttps://commons.apache.org/proper/commons-dbcp/configuration.html","mergeCommitSha":"ba550e6febaa013e5fce3b2e41011a0e58586538","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1722","title":"Move to apache jdbc driver","createdAt":"2022-06-09T19:48:35Z"}
{"state":"Closed","mergedAt":null,"number":1723,"mergeCommitSha":"0ff479f2e4cd94805c0000137d534188f814c285","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1723","title":"Dummy change, ignore","createdAt":"2022-06-09T19:51:17Z"}
{"state":"Merged","mergedAt":"2022-06-10T20:32:27Z","number":1724,"body":"We are currently polling the Installations API every two seconds. This is causing us to hit GH rate limits and breaking the system.\r\n\r\nIdeal situation would be to continue polling the installations API with modified-since or have a push model.\r\n\r\nIn the meantime, we will poll the repos instead of the installations and only fetch installations when the repo list changes. \r\nThis works since the list of repos updating is the product of a successful installation.","mergeCommitSha":"70288f8e1557bed86205c55f39ba9bb3c6d1bfb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1724","title":"Utilize repoStore polling in installation store","createdAt":"2022-06-09T20:34:50Z"}
{"state":"Merged","mergedAt":"2022-06-10T00:42:22Z","number":1725,"body":"Add intercepting layer command that redirects the text editor sourcemark tooltip click to the tutorial command when onboarding.\r\n\r\nAlso, add logic to try to set the interaction thread to the first thread with a resolved sourceMark.","mergeCommitSha":"2519cf328d430cc5d9118db3fae00c2a1d04c9b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1725","title":"[Onboarding] Enable sourcemark tooltip click within onboarding flow","createdAt":"2022-06-09T20:42:43Z"}
{"state":"Merged","mergedAt":"2022-06-09T22:56:00Z","number":1726,"body":"Ensure we do not send welcome email more than once.","mergeCommitSha":"06e5ba9e9df83b51c1f7f70874ebaa2a39b9aeba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1726","title":"For Kay","createdAt":"2022-06-09T21:15:26Z"}
{"state":"Closed","mergedAt":null,"number":1727,"mergeCommitSha":"077679dead0e521484160119350f2f22a78ad4d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1727","title":"More work for Rashin :)","createdAt":"2022-06-09T21:43:06Z"}
{"state":"Merged","mergedAt":"2022-06-10T04:51:21Z","number":1728,"body":"- Added a rule to disallow traffic to __deepcheck from public web except if the magic header is set\r\n- This rule will also enable traffic sampling to reduce the volume of our access logs\r\nTested in Dev and it works fine.","mergeCommitSha":"8058e83dc2fab366af251c645dfaff2d7cfda9c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1728","title":"- Added a rule to disallow traffic to __deepcheck from public web exc…","createdAt":"2022-06-09T21:53:22Z"}
{"state":"Merged","mergedAt":"2022-06-09T23:37:02Z","number":1729,"mergeCommitSha":"5d214b2e5dcedbf59c3088ac2eaa100c4663e488","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1729","title":"IMprove logging","createdAt":"2022-06-09T23:02:41Z"}
{"state":"Merged","mergedAt":"2022-01-29T00:03:27Z","number":173,"mergeCommitSha":"100275c16ac697f0e0363502931faeb90bdb4226","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/173","title":"Cleanup linter code so only one rules definition","createdAt":"2022-01-29T00:02:17Z"}
{"state":"Merged","mergedAt":"2022-06-10T06:03:17Z","number":1730,"mergeCommitSha":"193c1c6e250eedc0e2586afb8bb340d77c87126a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1730","title":"Ensure pooled passwords are updated on new connections","createdAt":"2022-06-09T23:35:13Z"}
{"state":"Merged","mergedAt":"2022-06-10T00:39:48Z","number":1731,"body":"PlistBuddy ninja hacks ftw\r\n\r\nResolves UNB-232","mergeCommitSha":"07fb1653b9ef3e6e6f2a0f413d99544a69935a16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1731","title":"Add client headers to hub","createdAt":"2022-06-09T23:35:28Z"}
{"state":"Merged","mergedAt":"2022-06-10T17:43:33Z","number":1732,"body":"Fixes UNB-211 -- https://linear.app/unblocked/issue/UNB-211/show-open-pr-groups-in-vscode-sidebar\r\n<img width=\"1177\" alt=\"Screen Shot 2022-06-09 at 4 49 21 PM\" src=\"https://user-images.githubusercontent.com/2133518/172963402-f067d1b7-65d6-4ff5-abd4-b513f1c2f653.png\">\r\n\r\nAlmost all the work here is in `PullRequestThreadStore`, which manages fetching pull requests, and joining the threads for each pull request.","mergeCommitSha":"019d07754abdf3b39af56d7a8363b0ffe04d796f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1732","title":"Show Open PR threads in VSCode sidebar","createdAt":"2022-06-09T23:55:54Z"}
{"state":"Merged","mergedAt":"2022-06-10T18:09:19Z","number":1733,"mergeCommitSha":"fd222574f3773e654fa2db8d111d6503eda4cc4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1733","title":"Add pull request pusher channels","createdAt":"2022-06-09T23:59:11Z"}
{"state":"Merged","mergedAt":"2022-06-10T00:43:42Z","number":1734,"body":"Matt Adam brought up that we should be associating traces with requestIds for api calls (these ids are sent to client so we can backtrace them to honeycomb)\r\nHe’s right as usual!\r\n\r\n<img width=\"350\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/172965388-d6c2ecdf-126d-4ca6-8e57-4c5093dcfcac.png\">\r\n\r\nhttps://ui.honeycomb.io/unblocked/environments/local/trace/2jiqCBcnF2g?fields[]=name&fields[]=service.name&span=70eb9cb882563d10","mergeCommitSha":"cf998fbc25a0a9b3f44a5e29c7c1760d012e9281","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1734","title":"Log request callIds to honeycomb","createdAt":"2022-06-10T00:11:04Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:24:28Z","number":1735,"body":"Using fetch retry package that should hopefully catch *more* errors.\r\n\r\nHopefully catches unexpected errors such as this which sometimes occurs randomly (e.g. 500) or with network issues\r\n<img width=\"438\" alt=\"image (1)\" src=\"https://user-images.githubusercontent.com/1553313/172965253-ce7df6d7-32a4-41c6-8cc7-60742fb4baba.png\">\r\n ","mergeCommitSha":"78ee9bf92d00148d6db746368dc6d0d3afb68a77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1735","title":"Retry more requests","createdAt":"2022-06-10T00:11:17Z"}
{"state":"Merged","mergedAt":"2022-06-10T00:21:42Z","number":1736,"body":"This assertion is no longer valid since we create multiple original source points;\r\nanother is created on PR merge, unless the merge is a trivial fast-forward rebase.\r\n\r\nThis assertion was missed from this PR #1084.\r\n\r\nResponsible for 1000s of client error messages:\r\n```\r\nFailed to push sourcepoints into local cache Expected single item in list, but got 2\r\n```\r\n\r\n<img width=\"252\" alt=\"Screen Shot 2022-06-09 at 17 16 13\" src=\"https://user-images.githubusercontent.com/1798345/172965755-90366256-75be-4bd8-b210-0daa54261704.png\">\r\n","mergeCommitSha":"f54cf08720331835bacbdfa8606de87b734acec9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1736","title":"Remove invalid assertion for single SourcePoint per mark","createdAt":"2022-06-10T00:12:53Z"}
{"state":"Merged","mergedAt":"2022-06-10T17:43:43Z","number":1737,"mergeCommitSha":"fa33808b2b708e04a45412e5a2643a9fb0624bb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1737","title":"Add pusher channel for getThreadsForPullRequests","createdAt":"2022-06-10T00:22:18Z"}
{"state":"Merged","mergedAt":"2022-06-10T01:35:21Z","number":1738,"body":"Quick and dirty stats. Might formalize down the line as an API, but for now this is useful enough as is.\r\n\r\n<img width=\"875\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/172968666-d30ca9b1-8e62-47e0-87da-63ad939de6e6.png\">\r\n","mergeCommitSha":"353df3ed06fcf33fc5bb5b2e3d2c2d6dd1b810df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1738","title":"SourceMark stats to assess where to invest","createdAt":"2022-06-10T00:47:27Z"}
{"state":"Merged","mergedAt":"2022-06-10T02:51:54Z","number":1739,"mergeCommitSha":"5cf3d8f0a83b59f0a664b2693e77e344b0f687dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1739","title":"Move away from deprecated code","createdAt":"2022-06-10T00:58:31Z"}
{"state":"Merged","mergedAt":"2022-01-29T00:08:07Z","number":174,"mergeCommitSha":"949b90decbc3ffbe77f937a503f6cc6a6475a8c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/174","title":"Fix worklfows","createdAt":"2022-01-29T00:06:41Z"}
{"state":"Merged","mergedAt":"2022-06-10T16:38:52Z","number":1740,"mergeCommitSha":"d1f741217c32508a1e3e8f83077090a67c721684","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1740","title":"Update SourceMarkCalculator.getPointForSourceMark() to accept unsavedFiles","createdAt":"2022-06-10T02:58:18Z"}
{"state":"Merged","mergedAt":"2022-06-10T03:33:29Z","number":1741,"body":"Don't re-throw otherwise the remaining messages won't be processed.","mergeCommitSha":"df2b736af7b92d1a99e4951036a2ffefc8f38d46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1741","title":"Try to unblock queue","createdAt":"2022-06-10T03:14:49Z"}
{"state":"Merged","mergedAt":"2022-06-10T05:39:28Z","number":1742,"body":"Throwing an exception here will prevent other messages from being processed\r\n\r\nAlso if posting a message update to GitHub fails due to 404, we should not keep retrying","mergeCommitSha":"bb33a652713ec5a40a32fdfbf8763135fd11ca3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1742","title":"Handle exceptions better","createdAt":"2022-06-10T04:16:12Z"}
{"state":"Merged","mergedAt":"2022-06-10T04:38:58Z","number":1743,"mergeCommitSha":"8a90fdb5132ff4b8faeb8f1c55f92328b581c2fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1743","title":"Make new message indicator blue","createdAt":"2022-06-10T04:22:30Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:30:32Z","number":1744,"body":"Fixes \r\n![CleanShot 2022-06-09 at 23 02 16](https://user-images.githubusercontent.com/1924615/173000708-6acfe4ec-1e7c-4db2-b797-9c308abf4b37.png)\r\n","mergeCommitSha":"3e1387a6720320aa9df1cf0ccd6b110fe3b4ebcc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1744","title":"Fix repos page when no PullRequestIngestionModel exists","createdAt":"2022-06-10T06:02:30Z"}
{"state":"Merged","mergedAt":"2022-06-10T14:59:53Z","number":1745,"mergeCommitSha":"1b9886cbb55ec4f97a7f598e18f77b4437d97faa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1745","title":"Fix initial configuration for dynamicDataSource","createdAt":"2022-06-10T14:48:10Z"}
{"state":"Merged","mergedAt":"2022-06-10T17:44:20Z","number":1746,"body":"Fixes UNB-164","mergeCommitSha":"b640258396298873160e58301b4c95b12b229e73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1746","title":"Use PR titles and message preview","createdAt":"2022-06-10T16:15:22Z"}
{"state":"Merged","mergedAt":"2022-06-10T17:19:35Z","number":1747,"body":"Fucking boolean man.","mergeCommitSha":"4c8409f53f11409dd752c90e8121bf33aeea81b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1747","title":"Do not send welcome emails after first time","createdAt":"2022-06-10T17:03:51Z"}
{"state":"Merged","mergedAt":"2022-06-10T17:59:53Z","number":1748,"mergeCommitSha":"eaa279cc5b8ccf704b6558eb1de5ca608beb4874","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1748","title":"Brings vscode foreward when minimized, hopefully fixes other focus bugs too","createdAt":"2022-06-10T17:55:11Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:50:58Z","number":1749,"body":"<img width=\"1013\" alt=\"Pull Request Insight Bubbles\" src=\"https://user-images.githubusercontent.com/13431372/173127307-c293136d-7551-4c63-87da-49a4a1112181.png\">\r\n\r\n<img width=\"1355\" alt=\"Pasted Graphic 3\" src=\"https://user-images.githubusercontent.com/13431372/173127317-ade464d1-d2f8-44f5-97a7-732d63fca821.png\">\r\n\r\n<img width=\"1362\" alt=\"Start a Discussion\" src=\"https://user-images.githubusercontent.com/13431372/173127324-ed43679a-671e-4a68-8929-29b73b9c6b0d.png\">\r\n\r\n<img width=\"844\" alt=\"Stay in Touch\" src=\"https://user-images.githubusercontent.com/13431372/173127342-985fa9c0-d9f2-424e-9902-48171075d7cf.png\">\r\n\r\n<img width=\"813\" alt=\"You're Onboarded!\" src=\"https://user-images.githubusercontent.com/13431372/173127354-b8294c19-1e56-4cf1-9142-a8a987f9479c.png\">\r\n","mergeCommitSha":"50aeef39d866f9ef8346f70cbab337fb4134bbc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1749","title":"[Onboarding] Add assets","createdAt":"2022-06-10T18:22:23Z"}
{"state":"Merged","mergedAt":"2022-01-29T00:09:40Z","number":175,"mergeCommitSha":"06b75b7b6bd689b19316f67f8b5c9ca2d1321fef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/175","title":"update","createdAt":"2022-01-29T00:09:29Z"}
{"state":"Merged","mergedAt":"2022-06-10T19:12:31Z","number":1750,"body":"Added a new step to service deployments to annotate all Grafana dashboards with the commit sha and deployment status.\r\n\r\nFor now I have the condition which limits annotations to only prod commented out. If it works as expected I'll make a separate PR to remove the comment.\r\n\r\nWe will also need to add the same step to landing page and dashboard deployments later.\r\n\r\nMore info about this action: https://github.com/marketplace/actions/add-grafana-annotation","mergeCommitSha":"3c5c0a232fc4924d0f23f038fa5bf91be298d6f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1750","title":"Adding annotations to Grafana dashboards for deployments","createdAt":"2022-06-10T18:36:20Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:13:21Z","number":1751,"body":"https://linear.app/unblocked/issue/UNB-194/tabbing-between-title-to-description-is-incorrect\r\n\r\n","mergeCommitSha":"e552be9abd4a4886588578d41a4ada23cb196efb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1751","title":"Don't tab into the message editor buttons UNB-194","createdAt":"2022-06-10T19:04:32Z"}
{"state":"Closed","mergedAt":null,"number":1753,"mergeCommitSha":"8a779081f40e0d37bcad3b08e646d281302b1782","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1753","title":"[DRAFT] [IGNORE] Test commit","createdAt":"2022-06-10T19:33:02Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:50:50Z","number":1754,"body":"https://linear.app/unblocked/issue/UNB-149/re-word-archived-throughout-product\r\n\r\n<img width=\"300\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173137723-3be86765-9eda-42b5-89cd-f7e4f73c7432.png\">\r\n<img width=\"742\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173137733-ef65f832-2c5f-4d33-ba4c-4b0cb08ac8c9.png\">\r\n<img width=\"730\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173137763-2b98ffc5-d379-4e29-9536-03b6658289a3.png\">\r\n\r\n<img width=\"684\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173137919-91ef2a02-98ce-4092-a5bc-4103ab5fdbf7.png\">\r\n<img width=\"341\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173137976-cbb2c3cf-bbbb-41ef-a334-a33c57e7fe23.png\">\r\n","mergeCommitSha":"39e68110fc01f5fbba55ab6d7f4241a3ea7eb536","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1754","title":"Rename archive -> delete UNB-149","createdAt":"2022-06-10T19:38:28Z"}
{"state":"Merged","mergedAt":"2022-06-10T19:59:54Z","number":1755,"mergeCommitSha":"dc6ec08a74d2cc1dbd7673ba9a35268033c765ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1755","title":"forgot to add the token to Dev deploy job","createdAt":"2022-06-10T19:42:04Z"}
{"state":"Merged","mergedAt":"2022-06-11T00:18:14Z","number":1756,"body":"* Dashboard and web extension display open PR threads in the `mine` section\r\n* Don't duplicate comments if you have multiple open PRs\r\n* When you open a new PR, add the new PR to the list (don't list-replace)","mergeCommitSha":"ae0ba797eb5d0258b117c5bce9511f53b3fe72c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1756","title":"Fix Open PR UI bugs","createdAt":"2022-06-10T19:48:03Z"}
{"state":"Merged","mergedAt":"2022-06-10T20:32:11Z","number":1757,"body":"Were displaying the \"installation\" ui in the sidebar unnecessarily.\r\n\r\nWe should skip if there's at least one installation already installed with valid repos.","mergeCommitSha":"0e44af12a6bdc3a22e4544201f0cd8a3e9489158","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1757","title":"Fix unexpected install sidebar","createdAt":"2022-06-10T20:02:45Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:31:44Z","number":1758,"body":"bug:\r\n<img width=\"599\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173145534-7d60e85c-3df0-45d5-a75c-d340b8415e33.png\">\r\n\r\nafter:\r\n<img width=\"516\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173145680-fe22198b-3f14-4f2c-bd6a-7c6b586f6495.png\">\r\n","mergeCommitSha":"3fbee1d6df9e82608781c954f0f5f2fb9e1a4e3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1758","title":"Fix width of imgs in message editor","createdAt":"2022-06-10T20:29:51Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:19:38Z","number":1759,"mergeCommitSha":"e1eba4ecd5e59f5168474815f00da433379726db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1759","title":"limit deployment annotation to prod only","createdAt":"2022-06-10T20:36:50Z"}
{"state":"Merged","mergedAt":"2022-01-29T00:46:42Z","number":176,"body":"## Recommended steps\r\n\r\n```\r\ngit remote remove origin\r\ngit remote add origin https://github.com/NextChapterSoftware/unblocked\r\n./gradlew clean\r\n```","mergeCommitSha":"02740ced73cc4296bb749d45c6c6c7c9d206f9cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/176","title":"Rebrand","createdAt":"2022-01-29T00:17:53Z"}
{"state":"Merged","mergedAt":"2022-06-10T21:15:12Z","number":1760,"mergeCommitSha":"fa566ab7f94a2b5abbd275b717384c796cc6c5ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1760","title":"Try something","createdAt":"2022-06-10T20:52:05Z"}
{"state":"Merged","mergedAt":"2022-06-14T16:14:28Z","number":1761,"body":"Filters out the current user in create discussion\r\n\r\n\r\n<img width=\"608\" alt=\"CleanShot 2022-06-10 at 14 52 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/173155798-b4bcd954-21a3-407c-a818-d50c3cadbf41.png\">\r\n\r\n<img width=\"725\" alt=\"CleanShot 2022-06-10 at 14 52 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/173155804-50bcc676-2700-4d6f-93a2-9773ad46418c.png\">\r\n\r\nNext steps: https://linear.app/unblocked/issue/UNB-219/add-recommended-team-members-to-creatediscussion-ui","mergeCommitSha":"9bcca7731aa854fc5bc6c5adc9897078e5cbe02e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1761","title":"Filter our current user in Create discussion","createdAt":"2022-06-10T21:52:35Z"}
{"state":"Merged","mergedAt":"2022-06-13T15:51:49Z","number":1762,"body":"Running into issues where the repo objects from Vscode Git were not fully initialized. This sometimes made it look as if a repo had no remotes and triggered the project selector state.\r\n\r\nBased on this: https://github.com/microsoft/vscode/issues/77787 we need to explicitly wait on repo.status() to finish loading","mergeCommitSha":"4ea05690d1df3fc8fa3a4519675900a6a42ac443","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1762","title":"Wait for repo status","createdAt":"2022-06-10T22:26:28Z"}
{"state":"Merged","mergedAt":"2022-06-10T22:48:30Z","number":1763,"mergeCommitSha":"8bf56de73bcd2a35c73215b511580186c51dcb09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1763","title":"do not do unnecessary sts token queries","createdAt":"2022-06-10T22:30:59Z"}
{"state":"Merged","mergedAt":"2022-06-11T03:31:49Z","number":1764,"body":"This reverts commit fa566ab7f94a2b5abbd275b717384c796cc6c5ff.\r\n\r\n# Conflicts:\r\n#\tgradle.properties\r\n#\tprojects/models/src/main/kotlin/com/nextchaptersoftware/db/common/tomcat/ConnectionJdbcInterceptor.kt\r\n","mergeCommitSha":"b4f6e0aa49ffb014b4e3e9f0adc58436164f6f55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1764","title":"Revert \"Try something (#1760)\"","createdAt":"2022-06-11T03:31:32Z"}
{"state":"Merged","mergedAt":"2022-06-11T03:35:07Z","number":1765,"mergeCommitSha":"caba262f4811451403184724781012d3a4ed411e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1765","title":"Update messaging","createdAt":"2022-06-11T03:34:50Z"}
{"state":"Merged","mergedAt":"2022-06-12T03:35:01Z","number":1766,"body":"- fix bug in SourcePoint stopPropagation persistence\r\n- always show snippets in admin web, for debug\r\n- improve sourcemark stats","mergeCommitSha":"35d478fe04cb3779302c7d7b1b41db11cd90c800","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1766","title":"Fix bug in SourcePoint stopPropagation persistence","createdAt":"2022-06-12T03:10:58Z"}
{"state":"Merged","mergedAt":"2022-06-12T04:15:43Z","number":1767,"body":"Useful when we improve the SM engine and want to clear and forcibly recalculate everything in a repo.","mergeCommitSha":"cd025c6f13625f580f9f391fbcb7ac3355c40870","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1767","title":"Ability to clear repo sourcepoints from admin web","createdAt":"2022-06-12T03:30:05Z"}
{"state":"Merged","mergedAt":"2022-06-12T16:36:37Z","number":1768,"mergeCommitSha":"8250107033d8a212a44b533209efe5141740f2b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1768","title":"Installations being returned without installURL","createdAt":"2022-06-12T04:51:12Z"}
{"state":"Merged","mergedAt":"2022-06-13T23:59:51Z","number":1769,"body":"Add AtMention support to MessageEditor.\r\nLike the ListEditor, we are using the canonical slatejs plugin pattern as described here:\r\nhttps://kitemaker.co/blog/building-a-rich-text-editor-in-react-with-slatejs\r\n\r\nThis pr adds the following:\r\n1. A React zustand store (as there are a few state-based items we need to manage)\r\n2. Keyboard handlers for the div that we plan to use for managing @mention options in dropdown.\r\n3. OnChange handlers for the div that we are using to determine what user is trying to search for.\r\n![CleanShot 2022-06-12 at 13 24 56](https://user-images.githubusercontent.com/3806658/173252340-a5c16ccc-f784-4f7d-84c8-842ab9693f7e.gif)\r\n\r\n**TODO**:\r\n1. Proper dropdown\r\n2. Plugging in team members\r\n3. Calling appropriates apis based off @mention selection.","mergeCommitSha":"09118afb72805d62ca06270b7975a2456a55dab0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1769","title":"Add AtMentionFramework to editor","createdAt":"2022-06-12T20:21:05Z"}
{"state":"Merged","mergedAt":"2022-01-31T17:10:38Z","number":177,"body":"- introduce tag groups for related resources. for example, the \"Conversations\" group\r\n   would contain the Chat, ChatMessage and ChatParticipants tags.\r\n - rename tags\r\n - add overview, with conventions.","mergeCommitSha":"c78bd8f44a7dfd37200277fbb6fb9e606ddb22f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/177","title":"Update spec documentation","createdAt":"2022-01-29T00:57:45Z"}
{"state":"Merged","mergedAt":"2022-06-13T04:35:43Z","number":1770,"mergeCommitSha":"d8004c8398e0833266617c4302ce068b183ed39e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1770","title":"SourceMark log supression and TODO assignments","createdAt":"2022-06-13T04:20:50Z"}
{"state":"Merged","mergedAt":"2022-06-13T17:00:12Z","number":1771,"body":"Slight amendment to #1770, where we still log all the things, but only when assertion mode is enabled.\r\n","mergeCommitSha":"0eac36f6dd78e2035dc3e01398e981537a4f811c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1771","title":"SourceMarks log debug/warn in assertion mode only","createdAt":"2022-06-13T05:17:57Z"}
{"state":"Merged","mergedAt":"2022-06-13T22:15:04Z","number":1772,"body":"There's a current theory that the logout issue may be caused by unexpected 401s.\r\nAdd additional logging and safeguards to debug situation.","mergeCommitSha":"70846c674a2fe5e34c4ffd6fb88563e38c40973e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1772","title":"add refresh logging issue","createdAt":"2022-06-13T16:14:55Z"}
{"state":"Merged","mergedAt":"2022-06-13T17:54:30Z","number":1773,"body":"Fixes UNB-245 https://linear.app/unblocked/issue/UNB-245/sourcemark-rendering-races\r\n\r\nThe key problem is that we were potentially triggering the async operation to look up the sourcemark, and display the resulting text editor (or error UI) multiple times.  While the operation should have been idempotent, and does seem to be idempotent when run in the VSCode extension host, when run in non-extension-host VSCode, it throws an error, which results in the wrong behaviour.\r\n\r\nThe solution here is to hold onto the sourcemark resolution promise, so that we only run it once.","mergeCommitSha":"320d8ab0ba85035b86fc7beab5d5c41097c05d79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1773","title":"Don't show misleading \"discussion references a source code location that doesn't exist at your checked-out commit\" UI","createdAt":"2022-06-13T17:24:45Z"}
{"state":"Merged","mergedAt":"2022-06-13T18:04:37Z","number":1774,"body":"The API endpoint defaults to true, so should this.","mergeCommitSha":"0517a34a70527cf339327921ea2a8c741fc7a7f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1774","title":"includeThreadsForMyOpenPrs for push channel should default to true if param not included","createdAt":"2022-06-13T17:37:36Z"}
{"state":"Merged","mergedAt":"2022-06-13T19:08:02Z","number":1775,"body":"Found some bugs with selections/transforms that were resolved when I updated to latest slatejs.","mergeCommitSha":"f56f9c16db9fc29a1b87fc1cef4bf32b2f0f54f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1775","title":"Update slate","createdAt":"2022-06-13T18:55:56Z"}
{"state":"Merged","mergedAt":"2022-06-15T22:20:21Z","number":1776,"body":"<img width=\"631\" alt=\"CleanShot 2022-06-13 at 12 51 20@2x\" src=\"https://user-images.githubusercontent.com/1553313/173436409-231eadcf-7c83-4aa0-b6f3-b98338a5ed95.png\">\r\n\r\nCurrently only supporting GH.","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1776","title":"Login GH UI for VSCode","createdAt":"2022-06-13T20:08:46Z"}
{"state":"Merged","mergedAt":"2022-06-13T20:56:58Z","number":1777,"body":"All this does is prevent an org from being installed that is not in the allow list. If an org is subsequently removed from the allow list, it will have no impact. If we want to drop orgs and have their requests be rejected, we'll have to do a little extra during authorization.\r\n\r\nFixes UNB-251","mergeCommitSha":"9813a3ce03f3db9fba0e48834ad14e25b6a8e226","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1777","title":"Swap user allow list for org allow list","createdAt":"2022-06-13T20:11:00Z"}
{"state":"Merged","mergedAt":"2022-06-13T21:03:37Z","number":1778,"body":"This PR just renames the existing headers. New headers will be introduced in another PR:\r\n```\r\nX-Unblocked-Product-Version\r\nX-Unblocked-Product-Number\r\n```","mergeCommitSha":"ddceeda995bccd829629f8f6729b6ef572638fd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1778","title":"Rename client headers to match versionInfo naming scheme","createdAt":"2022-06-13T20:45:10Z"}
{"state":"Merged","mergedAt":"2022-06-13T21:48:27Z","number":1779,"body":"Adding a new bucket for download assets. This will also create a CloudFront endpoint for it","mergeCommitSha":"737241db665b7a6d3be3018fe3a59b7a6959732e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1779","title":"add download assets bucket","createdAt":"2022-06-13T20:53:27Z"}
{"state":"Merged","mergedAt":"2022-01-29T14:34:14Z","number":178,"mergeCommitSha":"50230bf02fd29ee8e534dc3de62a84cec3476dea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/178","title":"update\\\\\\","createdAt":"2022-01-29T14:34:03Z"}
{"state":"Merged","mergedAt":"2022-06-13T21:23:14Z","number":1780,"body":"This is to prevent dashboard deploys from deleting the installer file in the temp location under landing-page bucket. ","mergeCommitSha":"107dfcc9ce009b78085e61fbf033a31b5140b817","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1780","title":"exclude the temp assets folder while I wire up a dedicated bucket","createdAt":"2022-06-13T21:04:51Z"}
{"state":"Merged","mergedAt":"2022-06-16T23:47:50Z","number":1781,"body":"Fixes [Thread pusher channel not raised when a message is deleted](https://linear.app/unblocked/issue/UNB-223/thread-pusher-channel-not-raised-when-a-message-is-deleted)\r\n\r\nAlso:\r\n- grooms ThreadUnreads on message deletion\r\n- checks that team member is the author of a thread before deleting it\r\n- does not allow deleting the first in a thread\r\n\r\nTODO \r\nhttps://linear.app/unblocked/issue/UNB-278/groom-thread-unreads-when-a-message-is-deleted-during-pr-ingestion","mergeCommitSha":"1d6cc0931d3576de399ccf5e12bf195b9d8cf0eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1781","title":"Touch thread when message is deleted","createdAt":"2022-06-13T21:19:43Z"}
{"state":"Merged","mergedAt":"2022-06-13T21:44:34Z","number":1782,"body":"Will be used in follow on PR.","mergeCommitSha":"38eba7652590a2d9b4653f0495c26be8dcd4ac1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1782","title":"Ability to parse file statuses needed for rename detection","createdAt":"2022-06-13T21:24:03Z"}
{"state":"Merged","mergedAt":"2022-06-13T22:38:00Z","number":1783,"mergeCommitSha":"45751d4c9fa6c612ef9b934a13f6c0cf24f97b46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1783","title":"Add X-Unblocked-Product-Number and X-Unblocked-Product-Version headers","createdAt":"2022-06-13T21:53:12Z"}
{"state":"Merged","mergedAt":"2022-06-14T16:27:50Z","number":1784,"body":"Sets up product numbers and versions for TS Clients.\r\n\r\n Product Version not populated yet as we figure out semver strategy. Infrastructure for version should be ready to go so it just needs to be populated\r\n\r\n<img width=\"1076\" alt=\"CleanShot 2022-06-13 at 15 10 27@2x\" src=\"https://user-images.githubusercontent.com/1553313/173453823-9d0eaf4f-7475-4ed2-beb3-93e5f8a64564.png\">\r\n\r\n","mergeCommitSha":"3e00658a3e64ff438d57d184e1cccc30deb94227","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1784","title":"Setup product number for TS clients","createdAt":"2022-06-13T22:11:50Z"}
{"state":"Merged","mergedAt":"2022-06-14T03:21:18Z","number":1785,"mergeCommitSha":"d53744b82c79615899af42bcc0b3935a9c27c395","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1785","title":"Add version info api stub","createdAt":"2022-06-13T22:43:11Z"}
{"state":"Merged","mergedAt":"2022-06-14T20:00:25Z","number":1786,"body":"Added simple loading states to web extension.\r\nMinimum height for dialog.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/173460266-831ace3d-2032-4c97-ac63-0737139ff6ad.mp4\r\n\r\n","mergeCommitSha":"6eb842c8eca7ba56c84695a39fe3cb1e2beace64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1786","title":"Loading states for web extension","createdAt":"2022-06-13T22:51:00Z"}
{"state":"Merged","mergedAt":"2022-06-13T23:43:38Z","number":1787,"body":"Prefix token providers in web and extension (already done in vscode) just in case this is the cause for invalid refresh auth bug.","mergeCommitSha":"0263ba58ba24040ed612f057ddf8dd8745e3dbe6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1787","title":"Prefix token providers","createdAt":"2022-06-13T23:20:51Z"}
{"state":"Merged","mergedAt":"2022-06-14T00:32:39Z","number":1788,"body":"Issuer is the same in all environments, which makes it hard to know when we've crossed wires. ","mergeCommitSha":"434f0b8c9f6b2613245b4deed11c2ab16fabdfdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1788","title":"Use environment hostname for token issuer","createdAt":"2022-06-13T23:47:13Z"}
{"state":"Merged","mergedAt":"2022-06-14T06:07:59Z","number":1789,"body":"Before | After\r\n--|--\r\n<img width=\"763\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/173502971-a7685f70-bdf4-4177-8e6a-cbfb30597bed.png\"> | <img width=\"971\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/173502917-13d33837-96bd-4d7e-8c66-b62ccfccb814.png\">\r\n","mergeCommitSha":"12b3433a96b794304f33dcb299562aa4ad4f6141","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1789","title":"SourceMarks should persist after file rename or move","createdAt":"2022-06-14T00:38:29Z"}
{"state":"Merged","mergedAt":"2022-01-30T21:24:35Z","number":179,"body":"My bad.","mergeCommitSha":"561d43b4e81906ad1d168826d444d86a90cd302c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/179","title":"suspendingTest block should not return anything","createdAt":"2022-01-30T05:03:26Z"}
{"state":"Merged","mergedAt":"2022-06-14T23:40:36Z","number":1790,"body":"<img width=\"374\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173471184-6c320a28-3dac-44aa-bea7-52d4ac3b7adc.png\">\r\n","mergeCommitSha":"8ba601177ed03c3b188a03fc3c7b5b672afdc91a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1790","title":"Add context menu to vscode sidebar ","createdAt":"2022-06-14T00:58:20Z"}
{"state":"Merged","mergedAt":"2022-06-14T04:54:16Z","number":1791,"body":"Follow on from #1777 ","mergeCommitSha":"65318f35e141429fb06a7b39b5a30ae366efa7cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1791","title":"Allow personal orgs","createdAt":"2022-06-14T04:10:07Z"}
{"state":"Merged","mergedAt":"2022-06-14T13:48:16Z","number":1792,"mergeCommitSha":"08abaa227217e3367ebfba805fd522fd131cbad3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1792","title":"Add VersionInfoStore","createdAt":"2022-06-14T04:49:00Z"}
{"state":"Merged","mergedAt":"2022-06-14T16:29:44Z","number":1793,"mergeCommitSha":"3d9f8a3fb0363b4513d0ba83d4598ceedcb2fa68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1793","title":"Fix asset auth after issuer changes","createdAt":"2022-06-14T15:16:33Z"}
{"state":"Merged","mergedAt":"2022-06-14T20:33:29Z","number":1794,"body":"Added a release step to do the following:\r\n- Executes if branch is main and event name starts with `release-`\r\n- Gets the version string from tag name \r\n- Downloads and renames asset to the final marketing name \r\n- Generates md5sum for installer asset \r\n- Generates a metadata file \r\n- Uploads installer to both Dev and Prod download-assets bucket along with metadata file \r\n\r\nSample metadata json\r\n```\r\n{\r\n\"build_number\": 144,\r\n\"version\": \"0.1.0\",\r\n\"commit_sha\": \"0e9d48b634fc681cc483da7568b41e0c4013f236\",\r\n\"checksum\": \"f27dd0181a646df87815b69de1a653ab\",\r\n\"file_name\": \"unblocked-installer-0.1.0-144.pkg\",\r\n\"platform\": \"macos\",\r\n\"release_date\": \"2022-06-14 18:22:17\"\r\n}\r\n```\r\n<img width=\"1122\" alt=\"Screen Shot 2022-06-14 at 11 31 18 AM\" src=\"https://user-images.githubusercontent.com/********/*********-bfc5cb17-3fef-4079-aaa3-7f90c5f35b1a.png\">\r\n\r\n","mergeCommitSha":"fb589ef497e4560b8bf8dd55a313ae492a1930eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1794","title":"Add publish step for macos installer","createdAt":"2022-06-14T17:03:36Z"}
{"state":"Merged","mergedAt":"2022-06-14T17:23:11Z","number":1795,"mergeCommitSha":"6d491e302b7b89f076029acf2e0d1f78dffc2628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1795","title":"Add Test Accounts","createdAt":"2022-06-14T17:03:49Z"}
{"state":"Merged","mergedAt":"2022-06-14T17:37:55Z","number":1796,"body":"Should revert this after we've figure it out.","mergeCommitSha":"2b01fdffc443dbef9310179450b81e606073084f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1796","title":"Debug org installation","createdAt":"2022-06-14T17:24:29Z"}
{"state":"Merged","mergedAt":"2022-06-14T17:54:57Z","number":1797,"mergeCommitSha":"204c9fb81a6e041f0adc4d743dc1a3a234a40e7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1797","title":"Hack to display sourcemark gutter icon during onboarding","createdAt":"2022-06-14T17:44:29Z"}
{"state":"Merged","mergedAt":"2022-06-15T00:27:52Z","number":1798,"body":"Ktor 2.0.2 has the fix to the bug I submitted, so we should be able to do identityId queries now in mdc. :)","mergeCommitSha":"f9f41c42bedd54fe159c3ed69928875e4a479d0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1798","title":"Add identityId back to mdc as we need it for diagnosing stuff","createdAt":"2022-06-14T17:46:20Z"}
{"state":"Merged","mergedAt":"2022-06-14T18:29:35Z","number":1799,"body":"<img width=\"611\" alt=\"CleanShot 2022-06-14 at 11 09 21@2x\" src=\"https://user-images.githubusercontent.com/1553313/173660150-19163647-d24c-4831-af1d-2d5163246187.png\">\r\n","mergeCommitSha":"3012fd7eaf2676248f15c46d1b39ff71a8fd7173","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1799","title":"Add PR Threads to web extension","createdAt":"2022-06-14T18:13:41Z"}
{"state":"Merged","mergedAt":"2022-01-07T21:59:35Z","number":18,"body":"Testing out different state management libraries.\r\n\r\nUpdated AuthStore to use [Zustand](https://github.com/pmndrs/zustand).","mergeCommitSha":"c9cc994c1e5ce77e367fd8678d8128d7ec66548a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/18","title":"Zustand Implementation","createdAt":"2021-12-29T22:16:18Z"}
{"state":"Closed","mergedAt":null,"number":180,"body":"Just creating the stubs here. Will wire up the database calls in the next PR.","mergeCommitSha":"47104e861f0111f6f19159f4b00f4fd56fd80615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/180","title":"Add putChat and putMessage API stubs","createdAt":"2022-01-31T05:13:23Z"}
{"state":"Merged","mergedAt":"2022-06-14T20:05:09Z","number":1800,"mergeCommitSha":"feaaf77b9a274e8bac2bed7f46c74dd019690066","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1800","title":"Supply correct before value","createdAt":"2022-06-14T19:03:33Z"}
{"state":"Merged","mergedAt":"2022-06-14T19:32:50Z","number":1801,"mergeCommitSha":"1b34ac7a9af8f2aca699610f4c65ce87925345f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1801","title":"clean up stuff","createdAt":"2022-06-14T19:06:54Z"}
{"state":"Merged","mergedAt":"2022-06-14T20:05:22Z","number":1802,"body":"This is what we use in the API request headers.","mergeCommitSha":"c315334a48654f2d343345c9849c7780af0e433f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1802","title":"Use full commit SHA in TS projects","createdAt":"2022-06-14T19:12:27Z"}
{"state":"Merged","mergedAt":"2022-06-14T19:43:35Z","number":1803,"body":"Adding a new behaviour to CloudFront for download-assets site bucket. This is the path we would use for Client installer downloads. ","mergeCommitSha":"d76307b67797d0718d4914caf8f7efbbe032d0bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1803","title":"Add download assets cf endpoint","createdAt":"2022-06-14T19:16:06Z"}
{"state":"Merged","mergedAt":"2022-06-14T20:18:00Z","number":1804,"mergeCommitSha":"20230bfc55b29aba9fec3ba866239d35ef6c8bbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1804","title":"Fix for personal org installation","createdAt":"2022-06-14T19:57:27Z"}
{"state":"Merged","mergedAt":"2022-07-04T17:47:07Z","number":1805,"body":"When someone @mentions a person in a pull request comment in GitHub, Unblocked should check to see if that person exists in Unblocked and upsert a ThreadParticipant and ThreadUnread model.","mergeCommitSha":"8f8cef726631bccdf766261248ec6ded98e7d42a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1805","title":"Upsert ThreadParticipant and ThreadUnread for @mentions in GitHub comment bodies","createdAt":"2022-06-14T20:18:18Z"}
{"state":"Merged","mergedAt":"2022-06-16T14:28:28Z","number":1806,"body":"Also in this PR:\r\n- change `productSha` db column type from `text` to `sha1`\r\n- pass `Hash` values around instead of strings for type verification","mergeCommitSha":"26ca4fa3a0df235b2d5a43e26144aa67f886b462","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1806","title":"Version service class implementation","createdAt":"2022-06-14T21:15:34Z"}
{"state":"Merged","mergedAt":"2022-06-14T21:36:08Z","number":1807,"mergeCommitSha":"0d3d83ce889832d4462c92fb88c03031b494d1af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1807","title":"changed the condition to only execute on tags","createdAt":"2022-06-14T21:17:46Z"}
{"state":"Merged","mergedAt":"2022-06-16T16:06:16Z","number":1808,"body":"## Summary\r\n\r\n- rename `ClientType` to `AgentType`\r\n- add `X-Unblocked-Product-Sha` and `X-Unblocked-Product-Agent` header definitions to spec\r\n- send back full git sha from clients\r\n- changed versions endpoint to be unauthed to support `getunblocked.com/download`\r\n- changed `versionInfo` to `versionInfo/latest` to support other types of queries (which will be authed)\r\n","mergeCommitSha":"0b51ad2d5db1495efede1265c931e467fdbc1b15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1808","title":"Rename ClientType to AgentType and add new types","createdAt":"2022-06-14T22:00:30Z"}
{"state":"Merged","mergedAt":"2022-06-14T23:09:11Z","number":1809,"mergeCommitSha":"811d6ef32920559cf5a7bae4d608fdbbfea6926f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1809","title":"Add Pulumi to allow list in prod.conf","createdAt":"2022-06-14T22:05:49Z"}
{"state":"Merged","mergedAt":"2022-01-31T16:27:20Z","number":181,"body":"- Added prod EKS cluster config\r\n- Added prod CDK config\r\n- Changed network route creation in network related stack to address a bug in CDK. The bug was causing public subnet list to include all subnets not just public ones.\r\n- Both EKS and CDK changes have been deployed\r\n- All VPN routes have been setup\r\n- Configured and deployed Postgres service account\r\n- Updated default deny rule in k8s to allow all egress by default (except the VPN range)\r\nAll changes have been deployed and prod environment is operational","mergeCommitSha":"daf7b7fad2c5ccc88ae9402360daad55b7409f00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/181","title":"Deploy prod environment infra ","createdAt":"2022-01-31T07:31:55Z"}
{"state":"Merged","mergedAt":"2022-06-15T05:31:59Z","number":1810,"body":"Fixes UNB-213 https://linear.app/unblocked/issue/UNB-213/vscode-reviewers-that-are-participating-in-the-open-pr-discussion-will\r\n\r\n* When we can't resolve a sourcemark location in VSCode, show an updated UI\r\n* Offer to jump to GitHub\r\n<img width=\"1598\" alt=\"Screen Shot 2022-06-14 at 3 51 35 PM\" src=\"https://user-images.githubusercontent.com/2133518/*********-98a850de-9c7e-4a13-8797-a50ec4ceda5e.png\">\r\n\r\n","mergeCommitSha":"a930dd4bc9d6af0e0bcc1fd99908fe6337126296","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1810","title":"Update unresolved sourcemark UI","createdAt":"2022-06-14T22:51:46Z"}
{"state":"Merged","mergedAt":"2022-06-15T19:02:16Z","number":1811,"body":"This pr plugs in team member information into message editor.\r\nIn order to do this: \r\n1. We modify vscode webview contexts such that they include TeamMembers.\r\n2. Add a TeamMember context store that we can reference (we will need this for web && web extension)\r\n\r\n\r\n![CleanShot 2022-06-14 at 16 08 09](https://user-images.githubusercontent.com/3806658/173703911-07c8689f-1ae7-4519-b219-27951a25f9c6.gif)\r\n","mergeCommitSha":"499a959f8f524473db6365a20d18532679a4033c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1811","title":"Plug-in team members","createdAt":"2022-06-14T23:10:36Z"}
{"state":"Merged","mergedAt":"2022-06-15T22:48:15Z","number":1812,"body":"This is mostly wording changes, but @kaych & I also eliminated one of the steps in the onboarding workflow (by combining similar messaging).","mergeCommitSha":"544d5bcd74f43408ccc949766f576018865596a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1812","title":"Word smithing onboarding","createdAt":"2022-06-14T23:21:01Z"}
{"state":"Merged","mergedAt":"2022-06-15T17:41:30Z","number":1813,"body":"Concurrently fetches source marks for both latest & current commit hash.\r\n\r\nAlso fixes some rendering issues with the popup when in the first 5 lines.\r\n\r\nTest case: https://github.com/NextChapterSoftware/unblocked/blob/5f56b14ae25973e8148f753a228788d553bfc3d2/vscode/src/components/Loading/Loading.tsx#L1\r\n<img width=\"1473\" alt=\"CleanShot 2022-06-14 at 17 05 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/173709282-6895dd2c-a352-40e2-9f7e-febada785e5a.png\">\r\n\r\n","mergeCommitSha":"69a3c1f61615938e75b689f835885f1a5f780d68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1813","title":"Fix issue with source mark rendering","createdAt":"2022-06-15T00:05:42Z"}
{"state":"Merged","mergedAt":"2022-06-16T00:47:15Z","number":1814,"body":"<img width=\"536\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173930009-d4a48032-f59f-4fac-90e2-a7c6eb16ad08.png\">\r\n\r\n<img width=\"537\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173930238-52b1ce27-3ca4-45d0-becb-343306252492.png\">\r\n","mergeCommitSha":"00b218ed03c2b2331c42859e3abdf5ef20592e55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1814","title":"Style mentions dropdown component","createdAt":"2022-06-15T02:33:21Z"}
{"state":"Merged","mergedAt":"2022-06-15T04:05:52Z","number":1815,"body":"Will figure out what all the valid special characters are in follow up...","mergeCommitSha":"91fa4088d27f760dc37e88f752b4d9cf4075423b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1815","title":"Fix for repo url with dot character","createdAt":"2022-06-15T03:00:03Z"}
{"state":"Merged","mergedAt":"2022-06-16T14:58:24Z","number":1816,"mergeCommitSha":"3e7dc0931fb1c31a2a18e3d33bb6f84791dce094","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1816","title":"Adds resiliency to team ingestion","createdAt":"2022-06-15T04:50:40Z"}
{"state":"Merged","mergedAt":"2022-06-15T05:55:28Z","number":1817,"body":"Follow on from https://github.com/NextChapterSoftware/unblocked/pull/1810#pullrequestreview-1006665695","mergeCommitSha":"07661687eb1c5d10f834a673a699907d249809ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1817","title":"Expose and use Repo web url","createdAt":"2022-06-15T05:41:33Z"}
{"state":"Closed","mergedAt":null,"number":1818,"body":"Fixes UNB-115 https://linear.app/unblocked/issue/UNB-115/vscode-scrollbar-behaviour\r\n\r\nRemove the minimum height on VSCode code blocks.  Code blocks render at full content height.\r\n\r\nTo be honest, I am not sure this is the right thing to do.  I will hold off on merging this until I've demoed it a bit.  The 'Start Discussion' UI in particular is pretty confusing with this.\r\n","mergeCommitSha":"8daed6a0197e09cff605fadb5c575039f98f9e0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1818","title":"Remove CodeBlock max-height","createdAt":"2022-06-15T05:51:48Z"}
{"state":"Merged","mergedAt":"2022-06-15T07:24:08Z","number":1819,"body":"We're burning through rate limits syncing PRs from repos when no PRs have updated because the eTag keeps changing and I don't know why","mergeCommitSha":"321f12b951b15b8594881c0ef72e83bd2c8cbb37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1819","title":"Add logging to try to understand why eTag is constantly changing","createdAt":"2022-06-15T07:03:28Z"}
{"state":"Merged","mergedAt":"2022-01-31T17:11:32Z","number":182,"mergeCommitSha":"db153f0ccc7e30149317469deddcfe1b8a88468c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/182","title":"Add encoded github key to config","createdAt":"2022-01-31T17:07:18Z"}
{"state":"Merged","mergedAt":"2022-06-16T17:55:28Z","number":1820,"body":"<img width=\"257\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/173883081-546496fb-cedd-4bed-97dc-44a9812e2962.png\">\r\n","mergeCommitSha":"aeb53d4c948cd3904bd693f95d4294f75f289273","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1820","title":"Fix border radius of unread marker on dashboard","createdAt":"2022-06-15T16:54:20Z"}
{"state":"Merged","mergedAt":"2022-06-15T17:12:31Z","number":1821,"mergeCommitSha":"5854945263bc75eb15399f1c4657bf3b0dcd49c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1821","title":"Whitelist Apple UB","createdAt":"2022-06-15T17:00:04Z"}
{"state":"Merged","mergedAt":"2022-06-15T17:03:42Z","number":1822,"body":"I will be force merging this one and won't wait for a whole ton of builds to finish","mergeCommitSha":"378b2ff0d36f4a0ca5c6b8829ac4e1e2851a8a27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1822","title":"trying to stop installer builds from running on every Pr","createdAt":"2022-06-15T17:01:13Z"}
{"state":"Merged","mergedAt":"2022-06-15T17:09:33Z","number":1823,"mergeCommitSha":"ed9c75e4badad6fca5f73572bfb812bdd335dc75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1823","title":"Allow list TheUnblockedDemo org and dennispiUB demo user","createdAt":"2022-06-15T17:09:20Z"}
{"state":"Merged","mergedAt":"2022-06-15T21:25:47Z","number":1824,"body":"Please forgive me for my sins.","mergeCommitSha":"d7c530dbc1cf0cde952e25995d3cafe6a8de8e02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1824","title":"Add in-memory eTag cache","createdAt":"2022-06-15T17:34:41Z"}
{"state":"Merged","mergedAt":"2022-06-15T19:19:33Z","number":1825,"body":"Reverts NextChapterSoftware/unblocked#1821\r\n\r\nNo need for this.","mergeCommitSha":"131381070178e5cb8db0025038280cb79001bf7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1825","title":"Revert \"Whitelist Apple UB\"","createdAt":"2022-06-15T17:47:47Z"}
{"state":"Merged","mergedAt":"2022-06-15T18:19:49Z","number":1826,"mergeCommitSha":"0e90bfea9735e407797e4c3b98da57622f919e0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1826","title":"Recommendations hack must include TheUnblockedDemo org","createdAt":"2022-06-15T18:15:50Z"}
{"state":"Merged","mergedAt":"2022-06-17T16:23:34Z","number":1827,"body":"<img width=\"598\" alt=\"CleanShot 2022-06-15 at 12 01 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/173905324-e299556d-e1c2-4dc8-91e7-913beb7ad0af.png\">\r\n","mergeCommitSha":"1aba6228752a7d12e2c81fe5cc8b2b079c9e710c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1827","title":"Login UI for web extension","createdAt":"2022-06-15T19:06:19Z"}
{"state":"Merged","mergedAt":"2022-06-15T23:53:56Z","number":1828,"mergeCommitSha":"5aa7bef53793b4c20ae087ab159ee155f7822bfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1828","title":"Convert inline html image to inline element","createdAt":"2022-06-15T19:11:29Z"}
{"state":"Merged","mergedAt":"2022-06-15T19:22:35Z","number":1829,"body":"Error case should be caught first.\r\n\r\nCheck if response model exists for safety. (e.g. could be undefined)","mergeCommitSha":"7eef45784e192c23bd1b2a1598b9e234a3d9e81b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1829","title":"Extra safety","createdAt":"2022-06-15T19:13:19Z"}
{"state":"Merged","mergedAt":"2022-01-31T17:37:05Z","number":183,"mergeCommitSha":"1749015076397a8235cb5e3e7c6687890f02a62e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/183","title":"Fix ErrorResponse typo","createdAt":"2022-01-31T17:31:11Z"}
{"state":"Merged","mergedAt":"2022-06-15T22:57:25Z","number":1830,"body":"Tried to get array inheritance working, but couldn't.\r\n\r\nRemoving the personal orgs for now, because it makes no sense to ingest personal orgs. There is only one user, they don't work anyway.","mergeCommitSha":"f76faab4e94e9edef127bda1b29cff10ee8695e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1830","title":"Cleanup Org Allowlist Config","createdAt":"2022-06-15T19:37:27Z"}
{"state":"Merged","mergedAt":"2022-06-16T23:47:18Z","number":1831,"body":"Goal here is to capture events from the clients so that we can answer from https://www.notion.so/nextchaptersoftware/User-Metrics-af26d39abbf24aca8269809eb27ef93c:\r\n\r\n```\r\n- What is an \"active user\"?\r\n    - Participated in a thread per day, week, month, for any of our clients\r\n        - Participation: Created, Replied to, or Viewed a thread\r\n```","mergeCommitSha":"12d24e6f542f644b4648a6c80b2dd80311b0d3c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1831","title":"Capture thread view events","createdAt":"2022-06-15T19:42:13Z"}
{"state":"Merged","mergedAt":"2022-06-16T16:38:06Z","number":1832,"mergeCommitSha":"d24afce8b03b1e999a6927465761561740e4d4e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1832","title":"Implements Versions API","createdAt":"2022-06-15T19:42:27Z"}
{"state":"Merged","mergedAt":"2022-06-15T20:42:23Z","number":1833,"body":"Each notification service instance will try to update email templates on startup. Following changes are to make sure they don't step on each other and also to allow us handle AWS rate limits better. \r\n\r\n- Added a redis lock to avoid having notification service instances stepping on each other during startup\r\n- Added rate limit to SES client with default backoff. Some SES API calls (e.g template CRUD) have 1 req per second limit\r\n- Updated configuration to provide a max timeout for startup template update lock (currently set to 30 seconds)\r\n- Updated tests\r\n\r\n\r\nException that was being thrown by notification service causing crashes on startup due to rate limit:\r\n```\r\nException in thread \"main\" software.amazon.awssdk.services.ses.model.SesException: Rate exceeded (Service: Ses, Status Code: 400, Request ID: 61e54777-1e2b-4095-94b1-dab799a367f4, Extended Request ID: null)\r\n\tat software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handleErrorResponse(CombinedResponseHandler.java:125)\r\n\tat software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handleResponse(CombinedResponseHandler.java:82)\r\n\tat software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handle(CombinedResponseHandler.java:60)\r\n\tat software.amazon.awssdk.core.internal.http.CombinedResponseHandler.handle(CombinedResponseHandler.java:41)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:40)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:30)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:73)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:42)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:78)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:40)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:50)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:36)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:81)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:36)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:56)\r\n\tat software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:36)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.executeWithTimer(ApiCallTimeoutTrackingStage.java:80)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:60)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:42)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:48)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:31)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:26)\r\n\tat software.amazon.awssdk.core.internal.http.AmazonSyncHttpClient$RequestExecutionBuilderImpl.execute(AmazonSyncHttpClient.java:193)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.invoke(BaseSyncClientHandler.java:103)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.doExecute(BaseSyncClientHandler.java:167)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:82)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:175)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)\r\n\tat software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)\r\n\tat software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)\r\n\tat software.amazon.awssdk.services.ses.DefaultSesClient.updateTemplate(DefaultSesClient.java:4895)\r\n\tat com.nextchaptersoftware.aws.ses.StandardSesProvider.updateTemplate(SesProvider.kt:150)\r\n\tat com.nextchaptersoftware.notification.email.templates.loader.EmailTemplatesLoader$load$1.invoke(EmailTemplatesLoader.kt:35)\r\n\tat com.nextchaptersoftware.notification.email.templates.loader.EmailTemplatesLoader$load$1.invoke(EmailTemplatesLoader.kt:22)\r\n\tat com.nextchaptersoftware.utils.ResourceWalker$walk$1$1.visitFile(ResourceWalker.kt:38)\r\n\tat com.nextchaptersoftware.utils.ResourceWalker$walk$1$1.visitFile(ResourceWalker.kt:29)\r\n\tat java.base/java.nio.file.Files.walkFileTree(Files.java:2811)\r\n\tat java.base/java.nio.file.Files.walkFileTree(Files.java:2882)\r\n\tat com.nextchaptersoftware.utils.ResourceWalker.walk(ResourceWalker.kt:27)\r\n\tat com.nextchaptersoftware.notification.email.templates.loader.EmailTemplatesLoader.load(EmailTemplatesLoader.kt:22)\r\n\tat com.nextchaptersoftware.notificationservice.plugins.EmailTemplatesKt.configureEmailTemplates(EmailTemplates.kt:9)\r\n\tat com.nextchaptersoftware.notificationservice.ModuleKt.module(Module.kt:77)\r\n\tat com.nextchaptersoftware.notificationservice.ModuleKt.module$default(Module.kt:38)\r\n\tat com.nextchaptersoftware.notificationservice.ServerKt$startServer$1.invoke(Server.kt:13)\r\n\tat com.nextchaptersoftware.notificationservice.ServerKt$startServer$1.invoke(Server.kt:8)\r\n\tat io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:318)\r\n\tat io.ktor.server.engine.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:307)\r\n\tat io.ktor.server.engine.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:335)\r\n\tat io.ktor.server.engine.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:307)\r\n\tat io.ktor.server.engine.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:144)\r\n\tat io.ktor.server.engine.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:274)\r\n\tat io.ktor.server.netty.NettyApplicationEngine.start(NettyApplicationEngine.kt:185)\r\n\tat com.nextchaptersoftware.notificationservice.ServerKt.startServer(Server.kt:14)\r\n\tat com.nextchaptersoftware.notificationservice.ApplicationKt.main(Application.kt:13)\r\n\tat com.nextchaptersoftware.notificationservice.ApplicationKt.main(Application.kt)\r\n```","mergeCommitSha":"563ea532dc0985c4eb27633a965f5fca182cac4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1833","title":"Adding Redis lock to coordinate notification service startup and template updates","createdAt":"2022-06-15T20:23:52Z"}
{"state":"Merged","mergedAt":"2022-06-16T16:34:08Z","number":1834,"body":"Two things that came up.\r\n\r\n1. The sidebar may be an \"installations\" state. We want to force it to installed if the user reaches the tutorial.\r\n2. If we're in a state where we have no unknownRepos but installation is not installed, we still want to show the install link (This only occurs during testing / deleting GH app)","mergeCommitSha":"c7c149aa8dd4af66370ea7d0c07f0f9eca1b6222","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1834","title":"Additional states for Installation QOL","createdAt":"2022-06-15T22:06:21Z"}
{"state":"Merged","mergedAt":"2022-06-15T22:41:47Z","number":1835,"body":"Catch errors from git blame to prevent create knowledge UI from crashing.","mergeCommitSha":"3c635613b769e967162b65f78779a0cf8f920223","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1835","title":"Catch git blame in create knowledge","createdAt":"2022-06-15T22:11:20Z"}
{"state":"Merged","mergedAt":"2022-06-15T22:32:37Z","number":1836,"body":"Need to understand why were getting\r\n\r\n```\r\nj.l.IllegalArgumentException: Requested element count -5 is less than zero.\r\n\tat k.c.CollectionsKt___CollectionsKt.takeLast(_Collections.kt:912)\r\n\tat c.n.p.GitHubPullRequestReviewCommentExtensionsKt.sourceSnippet(GitHubPullRequestReviewCommentExtensions.kt:35)\r\n```","mergeCommitSha":"0e56ed701e8f0a32a16081081fc8dff3da3a77bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1836","title":"Add logging to GitHubPullRequestReviewComment.sourceSnippet","createdAt":"2022-06-15T22:14:56Z"}
{"state":"Merged","mergedAt":"2022-06-17T03:38:45Z","number":1837,"body":"Fixes UNB-172 https://linear.app/unblocked/issue/UNB-172/[hub]-wire-up-intercom-links-from-hub\r\n\r\n* Clicking on 'Customer Support' in the Hub app now launches the dashboard with a special query flag (`https://getunblocked.com/dashboard?showIntercom=true`)\r\n* In the dashboard, if we are authed, show the default route (My discussions) and pop up intercom\r\n* If we are not authed, offer to log in, and once they do, pop up intercom:\r\n<img width=\"1294\" alt=\"Screen Shot 2022-06-15 at 3 55 27 PM\" src=\"https://user-images.githubusercontent.com/2133518/173955614-ca3ad04b-96ea-4bef-a6a2-805303d9b58c.png\">\r\n\r\nDashboard details:\r\n* I added a root route to the dashboard, that checks for this flag, and if it's set, stores a 5-minute window timestamp in session storage (this is the window where we will auto-popup intercom)\r\n* For all authed routes, if the flag is set in session storage (ie the user wanted customer support), pop up intercom and clear session storage\r\n* For the login route, if the flag is set, display a special message instead.  We want to encourage people to log in for support.\r\n\r\n","mergeCommitSha":"80079abb9fb8fa99a3c3ced9ace50c533ac0f5d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1837","title":"Open Intercom from Hub app","createdAt":"2022-06-15T22:57:09Z"}
{"state":"Closed","mergedAt":null,"number":1838,"body":"https://user-images.githubusercontent.com/1553313/173958818-6bfe8ba5-d6d2-4e0f-beba-1d7b54fc3c5e.mp4\r\n","mergeCommitSha":"5c4ea1ae68ed87da3aa0060732136dc073c5621b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1838","title":"Ability to remove added invitee","createdAt":"2022-06-15T23:29:12Z"}
{"state":"Merged","mergedAt":"2022-06-16T17:58:10Z","number":1839,"body":"We need to translate mention to protos and back again. :)\r\n\r\nI also want to give a big shout out to @dennispi as I know he looks at all these prs. :)","mergeCommitSha":"411a1e826e6aee439bb91c79979bf6ff39fa08c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1839","title":"Add mention to protos","createdAt":"2022-06-15T23:39:30Z"}
{"state":"Merged","mergedAt":"2022-01-31T18:19:55Z","number":184,"body":"Two things:\r\n1. Zally has a preference for configuring ignores via configuration file.\r\n2. We are now enforcing title case excluslively ont ag names.\r\n","mergeCommitSha":"c36a091ed01199bdfae3591e97b45857d24f68c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/184","title":"Enforce title case on tags for zally","createdAt":"2022-01-31T17:56:44Z"}
{"state":"Merged","mergedAt":"2022-06-16T02:35:56Z","number":1840,"mergeCommitSha":"851bed0da44ce9bd7b4447c3c372de00d47e92cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1840","title":"Shorten signature","createdAt":"2022-06-15T23:51:32Z"}
{"state":"Merged","mergedAt":"2022-06-16T07:14:32Z","number":1841,"mergeCommitSha":"faf669c9d27ef40ac1d5f1d343a7656a4ebcb40c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1841","title":"TeamMembers added during PR ingestion should not be active by default","createdAt":"2022-06-16T04:31:54Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:29:36Z","number":1842,"body":"- general HttpResponse pagination extension\r\n- applied to all list-based GitHub client APIs,\r\n  except for pull request APIs that currently depend on eTags.","mergeCommitSha":"7af433341393eb129723fdc7708e7fa534865e5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1842","title":"HTTP Response Pagination addresses Pulumi onboarding","createdAt":"2022-06-16T08:35:22Z"}
{"state":"Merged","mergedAt":"2022-06-17T00:55:12Z","number":1843,"mergeCommitSha":"2a4e7b720d83edd7873913de4b41493dabb90420","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1843","title":"Add version ingestion to admin console","createdAt":"2022-06-16T14:27:36Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:51:48Z","number":1844,"body":"Addresses this error https://app.logz.io/#/goto/6a36b1d3c617ad3bf6da8b76c6f23df7?switchToAccountId=411850","mergeCommitSha":"26bf17847df90820d7df23487bdeb5c951de9bac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1844","title":"Fallback to defaultContextLines if start line >= line","createdAt":"2022-06-16T17:24:34Z"}
{"state":"Merged","mergedAt":"2022-06-16T17:41:51Z","number":1845,"body":"Was known that Safari does not support declarative_net_request.redirect.\r\nLittle was known that it would crash at start initially... \r\n\r\nFrom testing and from chats with Safari Engineer during WWDC, declarative_net_request.redirect will be supported in the near future (he mentioned it's in safari tech preview but it doesn't work for me there...)\r\n\r\n","mergeCommitSha":"6b8df199c1d6abbc5a83eecb1e814a6c3299a691","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1845","title":"Remove declarative Net Request ","createdAt":"2022-06-16T17:27:07Z"}
{"state":"Merged","mergedAt":"2022-06-16T19:55:53Z","number":1846,"body":"- Added a step to extract semver value from release tag if this is a build triggered by a tag push. It will default to `1.0.0` otherwise. \r\nThe only way I can properly test this end to end is via a tag push once we merge this. ","mergeCommitSha":"db976294e1f5428fe50f0e8c9ca11ce05a885aff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1846","title":"Add app version from release tag to mac app and vscode","createdAt":"2022-06-16T17:50:03Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:11:50Z","number":1847,"body":"Fixes https://app.logz.io/#/goto/04775d79a8d0900459dd7a24ae62ed02?switchToAccountId=411850","mergeCommitSha":"d548263c794412a281b95bc6c3bfa5bffc5f6fb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1847","title":"Dont page pullRequestFiles beyond three","createdAt":"2022-06-16T18:34:35Z"}
{"state":"Merged","mergedAt":"2022-06-16T20:29:05Z","number":1848,"body":"<img width=\"709\" alt=\"CleanShot 2022-06-16 at 11 40 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-8246f7f4-eaa7-4774-a764-3ebb9453a1d2.png\">\r\n\r\nAfter login exchange (part of auth process for clients), we have been redirecting users directly to home.\r\nThis led to a subpar UX so swapping it out for a basic login success page. (Needs better design \uD83D\uDE4F @benedict-jw )\r\n\r\nAdded a `/login/success` route which should be closed by the clients. Currently VSCode + Extension will automatically close page. Hub should as well?\r\n","mergeCommitSha":"1e36972dfdc1d704f3fe2406fb96fa14384c2839","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1848","title":"Basic login success page","createdAt":"2022-06-16T18:45:20Z"}
{"state":"Merged","mergedAt":"2022-06-16T19:11:29Z","number":1849,"mergeCommitSha":"75f010eaa73d90878998ff95e8898498156a31d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1849","title":"update","createdAt":"2022-06-16T18:58:38Z"}
{"state":"Merged","mergedAt":"2022-01-31T22:37:26Z","number":185,"body":"This pr does the following:\r\n1. Add prod environment helm values\r\n2. Add prod environment apiservice config\r\n3. Add github actions for prod deployment\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/1775233123","mergeCommitSha":"cfca9a3a4b08946ab038046c60a1fa93b8013a51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/185","title":"Add prod environment deployment","createdAt":"2022-01-31T18:57:18Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:11:26Z","number":1850,"body":"Validate https://linear.app/unblocked/issue/UNB-132/snippet-in-open-pr-ingested-comment-blank-after-changing-code-based-on is no longer an issue","mergeCommitSha":"ba26cd33d84c7193afe3120ad43c8b51f4dfc872","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1850","title":"Add a test to validate source snippet parsing","createdAt":"2022-06-16T19:17:59Z"}
{"state":"Merged","mergedAt":"2022-06-16T20:53:36Z","number":1851,"body":"- Change how we get version values. Instead of env var we will be using the output of the `version` job \r\n- Changed services workflow to run only for changes to ci-services* workflow changes. It's annoying to wait for a whole service build when we are not making any changes to it. \r\n- Changed the service workflow to use latest version of EC2 runner. I forgot to update the branch name it was still using the old version with runner name conflict issue. ","mergeCommitSha":"bfda78dcc573c3896555bb93da28cddb35b95a9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1851","title":"use step output instead of env var","createdAt":"2022-06-16T20:12:34Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:15:05Z","number":1852,"body":"looks like Sofia Pro numbers have a different line height alignment than what we used before, needed some adjusting to the styling \r\n\r\nbefore\r\n<img width=\"265\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/174158434-c70285d3-955c-4c9e-9256-efa91efb27a2.png\">\r\n\r\nafter\r\n<img width=\"265\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/174158452-e4874d45-01a5-4ceb-bc8a-4dd9ce6e1a47.png\">\r\n","mergeCommitSha":"0f4fc97823ae70e7c5d85d99595b23502173a3a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1852","title":"Fix center alignment of dashboard unreads","createdAt":"2022-06-16T20:32:10Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:35:11Z","number":1853,"body":"Reverting VScode Extension version for now. We need to figure out how we want to version those releases on their own. ","mergeCommitSha":"be81e99659c53e1808837b8c8ed0810642e694aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1853","title":"revert vscode version","createdAt":"2022-06-16T21:04:16Z"}
{"state":"Closed","mergedAt":null,"number":1854,"mergeCommitSha":"11aa023d9e34fe93c719021b68ce9291ab141498","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1854","title":"Jeff/unb 285 broken recent workspaces in vscode","createdAt":"2022-06-16T21:08:24Z"}
{"state":"Merged","mergedAt":"2022-06-17T06:30:42Z","number":1855,"body":"Fixes UNB-285\r\n\r\nVSCode changed their recent workspace key.\r\n\r\nMake the JSON parser more generic to hopefully not break next time they rev versions.","mergeCommitSha":"2f5aa10fa7cfbad49e416adde6068b6c4e335cbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1855","title":"More generic submenu item fetcher","createdAt":"2022-06-16T21:09:52Z"}
{"state":"Merged","mergedAt":"2022-06-16T21:49:24Z","number":1856,"body":"before:\r\n<img width=\"701\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/174168154-375a4f2e-e528-44d4-bb38-30bfdd5f46a7.png\">\r\n<img width=\"1030\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/174168790-ab6c6104-a478-4dda-9f4a-df4ed81f5356.png\">\r\n\r\nafter:\r\n<img width=\"669\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/174168237-afd1edcf-2b16-4b4c-a48a-2e68fffa4d61.png\">\r\n<img width=\"920\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/174168840-45a92802-852b-48de-8f3a-8b6531495609.png\">\r\n\r\n\r\nNote: This looked fine in vscode because `img` elements were automatically given `width: 100%` within the vscode built-in styles but it ends up looking very broken in all our other clients","mergeCommitSha":"8c0da89efcf1e25c8776072d37056702dab16955","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1856","title":"All message block images need set width","createdAt":"2022-06-16T21:27:18Z"}
{"state":"Merged","mergedAt":"2022-06-17T00:37:24Z","number":1857,"mergeCommitSha":"6220f10b9b604536c66f3012a49458788fd6404b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1857","title":"fixing a bunch of stupid copy paste mistakes","createdAt":"2022-06-16T22:06:37Z"}
{"state":"Merged","mergedAt":"2022-06-28T23:19:38Z","number":1858,"body":"Timeouts for Auth Process.\r\n\r\nAfter 20 seconds, we will end polling and prompt user to try again.\r\n\r\nRequired a refactor of how the polling system works. VSCode & Web extension now share polling code in shared authstore.\r\nThis was necessary to add a \"timeout\" state\r\n\r\n<img width=\"492\" alt=\"CleanShot 2022-06-23 at 15 34 58@2x\" src=\"https://user-images.githubusercontent.com/1553313/175426237-868cfabb-5e28-4125-b3a3-355f11831601.png\">\r\n<img width=\"418\" alt=\"CleanShot 2022-06-23 at 15 32 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/175426242-0d750400-09e2-4e2d-821b-ec959e2b627e.png\">\r\n ","mergeCommitSha":"b0aa8a408380946190fb6dab8600e90dfd4155c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1858","title":"Jeff/unb 284 escape valve for auth in sidebar","createdAt":"2022-06-16T22:53:36Z"}
{"state":"Merged","mergedAt":"2022-06-17T22:24:34Z","number":1859,"body":"We want there to only be a single refreshAuth request occurring at a time.\r\nIf we end up in a situation where multiple requests require refreshing auth, they should all be blocked by a single refresh instance.\r\n\r\nFor context: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1655226872702149","mergeCommitSha":"f2146c08cd6b730a073592052ea907008cc0144b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1859","title":"Singleton refresh auth promise","createdAt":"2022-06-16T23:14:26Z"}
{"state":"Merged","mergedAt":"2022-01-31T21:08:10Z","number":186,"body":"As discussed, some renaming:\r\n\r\n1. `SourceMark` → `SourceMarkGroup` (this change)\r\n2. `SourceMark` → `SourceMarkGroup` in API\r\n3. `SourceVector` → `SourceMark`\r\n4. Relate `SourceMark` → `Message`","mergeCommitSha":"faad5db78caf378584359c55bbc4f9568b6b0de3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/186","title":"Rename [1 of 4]: SourceMark -> SourceMarkGroup","createdAt":"2022-01-31T20:58:59Z"}
{"state":"Merged","mergedAt":"2022-06-17T00:42:08Z","number":1860,"body":"Move some of the existing scm jobs to PollingBackgroundJobs so we can do a standardized honeycomb trace.","mergeCommitSha":"b54fb1ebaa678a2b02e2190f831b29a08e0e1d19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1860","title":"Add honeycomb traces for servcies","createdAt":"2022-06-16T23:56:53Z"}
{"state":"Merged","mergedAt":"2022-06-17T00:50:50Z","number":1861,"mergeCommitSha":"dcc21213bfbb80d1666857840ddaf2fa79566f8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1861","title":"Fix timing tets","createdAt":"2022-06-17T00:50:23Z"}
{"state":"Merged","mergedAt":"2022-06-17T00:53:24Z","number":1862,"body":"- Fix timing tets\r\n- updtae\r\n- update\r\n","mergeCommitSha":"1c389fd6dc8d2a5760e495b615e92d5a920d2942","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1862","title":"FixTimingTests2","createdAt":"2022-06-17T00:53:20Z"}
{"state":"Merged","mergedAt":"2022-06-17T01:24:27Z","number":1863,"mergeCommitSha":"caeef966dcf875a71f22d4e4423140d1dc2f1a5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1863","title":"update","createdAt":"2022-06-17T01:24:19Z"}
{"state":"Merged","mergedAt":"2022-06-17T02:02:35Z","number":1864,"mergeCommitSha":"9e162dc019bb0bee4b1af566d58b7838375590bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1864","title":"trying to fix a potential interpolation issue","createdAt":"2022-06-17T01:44:18Z"}
{"state":"Merged","mergedAt":"2022-06-17T03:05:20Z","number":1865,"mergeCommitSha":"03218ecf329412b23c608ac3b23d7739ab969937","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1865","title":"Ugly version hack","createdAt":"2022-06-17T02:55:55Z"}
{"state":"Merged","mergedAt":"2022-06-17T18:26:34Z","number":1866,"body":"Fixes UNB-191 https://linear.app/unblocked/issue/UNB-191/add-support-to-vscode-for-multiple-threads-per-line-in-text-editor\r\n\r\nSome notes:\r\n* Implementing the designs exactly is not possible with the restrictions in VSCode's Markdown/HTML.  In the designs the titles are bigger/bolder, and the spacing is slightly different.  This was as close as I could get it.\r\n* The \"multi\" thread icon (when there are multiple kinds of threads in a single row) is the Pink PR and Blue Discussion icon.  Open PRs don't appear as green.  I'm not sure how to handle all these mixed cases, I'll ask Ben next week.\r\n* The changes to the TextEditorSourceMark code was pretty involved, but I think the end result makes more sense, and it's more efficient FWIW.\r\n* I factored the tooltip creation code into another file, as it got pretty complicated as well\r\n\r\n<img width=\"649\" alt=\"Screen Shot 2022-06-16 at 8 00 59 PM\" src=\"https://user-images.githubusercontent.com/2133518/174216669-d14844a7-8c4c-4669-964f-c1ff3fb6ba30.png\">\r\n<img width=\"287\" alt=\"Screen Shot 2022-06-16 at 8 01 05 PM\" src=\"https://user-images.githubusercontent.com/2133518/174216671-a35d7745-44fc-488f-a960-d1d3141e6274.png\">\r\n\r\n","mergeCommitSha":"9f6b24ea65eb01da3adccf453e9f260595894845","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1866","title":"Render gutter icons and tooltips for multi-thread text rows","createdAt":"2022-06-17T03:04:20Z"}
{"state":"Merged","mergedAt":"2022-06-17T03:43:20Z","number":1867,"body":"We've 400 repos now so syncing them serially is causing delays in grabbing messages posted to GitHub.\r\n\r\nAnother optimization is to have two sync jobs, one for repos that have been active recently, and another for repos without any activity.\r\n\r\nBut really, we need a webhook service :).","mergeCommitSha":"0a7a912a628ad9af9e2c3d457159f9b18344874e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1867","title":"Concurrently process repos for PR sync job","createdAt":"2022-06-17T03:14:24Z"}
{"state":"Merged","mergedAt":"2022-06-17T04:25:49Z","number":1868,"mergeCommitSha":"d78d9d9d0e76d1e5db4182738c853ff4cede84dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1868","title":"Don't code on an empty brain","createdAt":"2022-06-17T04:07:10Z"}
{"state":"Merged","mergedAt":"2022-06-17T04:14:11Z","number":1869,"body":"Too many concurrent requests, we're getting throttled by GitHub","mergeCommitSha":"e7442f441b2d6923b732e124fe05e4007dfc7dc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1869","title":"Drop concurrency to 10","createdAt":"2022-06-17T04:11:01Z"}
{"state":"Closed","mergedAt":null,"number":187,"mergeCommitSha":"563a48aad1dd12ed8e38d8518389a30ecb4adff0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/187","title":"Test: ignore this","createdAt":"2022-01-31T21:17:00Z"}
{"state":"Merged","mergedAt":"2022-06-17T07:06:27Z","number":1870,"body":"The logic for parsing @mention ranges was a cluster fuck.\r\nWe've somewhat improved it as the prior logic was a pain to discern what was going on.\r\nWe are now showing @mention list when user types '@' symbol.\r\nAlso fixed bug with hasContent logic.\r\n\r\n\r\n![CleanShot 2022-06-16 at 21 22 21](https://user-images.githubusercontent.com/3806658/174224471-e8889003-1477-4fab-9da5-ee1265340fe4.gif)\r\n","mergeCommitSha":"b14607646b15b598b5947e1ba4bee0f13f45e8c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1870","title":"Fix showing at mentions","createdAt":"2022-06-17T04:17:03Z"}