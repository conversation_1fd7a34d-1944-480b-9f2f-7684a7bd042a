{"state":"Merged","mergedAt":"2022-09-19T20:58:17Z","number":3031,"body":"Also, follow up from #3026, no need to import crypto.","mergeCommitSha":"d260fab9442cca08a83ef302bf07cfbb77a94753","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3031","title":"Compress sourcemark LRU cache values","createdAt":"2022-09-19T18:56:59Z"}
{"state":"Merged","mergedAt":"2022-09-26T14:47:52Z","number":3032,"mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3032","title":"Implement VideoIPCClient","createdAt":"2022-09-19T19:28:02Z"}
{"state":"Merged","mergedAt":"2022-09-19T21:32:06Z","number":3033,"mergeCommitSha":"1f79a3f80c138232216692cda405d2e402b469d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3033","title":"Move common ingestion utilities","createdAt":"2022-09-19T19:39:51Z"}
{"state":"Merged","mergedAt":"2022-09-21T20:29:29Z","number":3034,"body":"First bit of work for displaying Slack threads, this extracts the stream of current commits and file data out from the PR stream and makes it reusable.","mergeCommitSha":"d546fafbccc44c463fd30c6374227b7ea8bf2fe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3034","title":"Streamify the VSCode commit/PR pipeline","createdAt":"2022-09-19T20:42:38Z"}
{"state":"Merged","mergedAt":"2022-09-19T21:21:39Z","number":3035,"body":"Behaviour: try to open in requested destination but still fall back if destination not available\r\n\r\n<img width=\"458\" alt=\"CleanShot 2022-09-19 at 14 06 51@2x\" src=\"https://user-images.githubusercontent.com/858772/191116808-af378e54-67b2-4b6b-a6f5-1fab12c8437a.png\">\r\n","mergeCommitSha":"d8b6c45f8171cabeed8822f5f95aaba9de2e6720","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3035","title":"Right-click options for threads","createdAt":"2022-09-19T21:07:38Z"}
{"state":"Merged","mergedAt":"2022-09-19T21:53:46Z","number":3036,"mergeCommitSha":"7e39f8d6c4bd8be466aab96576227a8e6bfa4821","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3036","title":"fix the region","createdAt":"2022-09-19T21:53:30Z"}
{"state":"Merged","mergedAt":"2022-09-19T22:25:50Z","number":3037,"mergeCommitSha":"1ac343fb8f5f9826b0cdca5fbfaa4b1f43483acb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3037","title":"remove concurrency","createdAt":"2022-09-19T22:25:37Z"}
{"state":"Merged","mergedAt":"2022-09-19T22:43:26Z","number":3038,"mergeCommitSha":"b8a5fb3cf02e9ebb5eb8762445fb28c3050ea910","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3038","title":"force a rerun","createdAt":"2022-09-19T22:43:19Z"}
{"state":"Merged","mergedAt":"2022-09-19T22:56:09Z","number":3039,"mergeCommitSha":"93131d60ca8181672c3a76f366c28331208acde0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3039","title":"renable concurrency and disable secret deployments","createdAt":"2022-09-19T22:55:50Z"}
{"state":"Merged","mergedAt":"2022-02-10T00:55:11Z","number":304,"body":"Released on Monday: https://fontawesome.com/docs/changelog/","mergeCommitSha":"5a200586ba81dd274c39174a3c044e5ac435655f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/304","title":"Update fontawesome packages to 6.0.0","createdAt":"2022-02-10T00:50:12Z"}
{"state":"Merged","mergedAt":"2022-09-19T23:21:15Z","number":3040,"mergeCommitSha":"3001616828d5970bdc99886daed121fb2ba02100","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3040","title":"passing kube api hosts directly","createdAt":"2022-09-19T23:21:10Z"}
{"state":"Merged","mergedAt":"2022-09-19T23:55:46Z","number":3041,"mergeCommitSha":"b7be1b3ff55eda1e47b6cc1ba12ae15bf6636201","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3041","title":"fixing my mistake in docker image creation","createdAt":"2022-09-19T23:51:57Z"}
{"state":"Merged","mergedAt":"2022-09-21T16:37:33Z","number":3042,"mergeCommitSha":"1894366acd4e1979e32b6c319bbdb5c6c378a4fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3042","title":"Basic slack importer","createdAt":"2022-09-20T01:30:07Z"}
{"state":"Merged","mergedAt":"2022-09-20T01:39:35Z","number":3043,"mergeCommitSha":"155ee0f5707484547f473cc708c2f594494b89fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3043","title":"Update files for matt","createdAt":"2022-09-20T01:39:17Z"}
{"state":"Merged","mergedAt":"2022-09-20T02:38:28Z","number":3044,"body":"Trying to track down the memory consumption problem, 1GB is evaporating somewhere.","mergeCommitSha":"5adefd7108e86df928d0949e7353ad83075a61d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3044","title":"Add resource usage for debugging and make sourcepoint upload more efficient","createdAt":"2022-09-20T02:28:33Z"}
{"state":"Merged","mergedAt":"2022-09-20T02:52:49Z","number":3045,"body":"- Broke the deploy step apart. It was really hard to see deployment steps for different environments on GH action UI\r\n- Re-enabled secret deployments ","mergeCommitSha":"2312c4f430f2c9a422108c27ff69945cac4f2cd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3045","title":"Splitting deploy step ","createdAt":"2022-09-20T02:37:07Z"}
{"state":"Merged","mergedAt":"2022-09-20T06:18:12Z","number":3046,"mergeCommitSha":"cddec8c2b67413960f2d8cd9c740654f30a8fdd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3046","title":"Fixing deployment","createdAt":"2022-09-20T06:17:43Z"}
{"state":"Merged","mergedAt":"2022-09-20T07:03:05Z","number":3047,"mergeCommitSha":"c4034f04f11e6c4959d62ee4378bd9db8c3b0e32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3047","title":"Test another deployment change","createdAt":"2022-09-20T06:30:18Z"}
{"state":"Merged","mergedAt":"2022-09-20T07:22:55Z","number":3048,"body":"I think the issue is caused by Kubectl not supporting concurrency checks like helm. ","mergeCommitSha":"f9d164fc085f52937e51d60ce2c78717e5d7a2a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3048","title":"cheap fix for secrets deployment","createdAt":"2022-09-20T07:03:26Z"}
{"state":"Merged","mergedAt":"2022-09-20T07:53:38Z","number":3049,"mergeCommitSha":"67e0c06e1de6fe3c4c5e179cea7d0b345700c1e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3049","title":"fix docker image tag","createdAt":"2022-09-20T07:53:14Z"}
{"state":"Merged","mergedAt":"2022-02-10T00:53:24Z","number":305,"body":"Was using jdk 8 and we're building jdk 17.","mergeCommitSha":"eea97ec7bff778dee02c41cca1ee2ed69357e277","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/305","title":"Move to jdk 17","createdAt":"2022-02-10T00:52:20Z"}
{"state":"Merged","mergedAt":"2022-09-20T17:15:40Z","number":3050,"body":"- Revert the condition for secrets deployments\r\n- Move the prod deployment suspension flag one level up","mergeCommitSha":"4e99813ed8de207ae4af919e0b90cf2a77e54ae7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3050","title":"Fix secret deployment","createdAt":"2022-09-20T16:52:22Z"}
{"state":"Merged","mergedAt":"2022-09-20T17:53:16Z","number":3051,"body":"Made changes to add service name to health probe path.\r\nOld path: `/api/__deepcheck`\r\nNew path: `/api/health/apiservice/__deepcheck`\r\n\r\nFixed the secret name used to control prod deployments in main workflow","mergeCommitSha":"f4a57074b71cbc4e865d88bd9df0e4c74bddf36b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3051","title":"fix kube probe paths","createdAt":"2022-09-20T17:43:36Z"}
{"state":"Merged","mergedAt":"2022-09-20T19:19:28Z","number":3052,"body":"* Add debugging logs to track down unexpected fault UI instances\r\n* Remove spammy badge logs, we don't need them","mergeCommitSha":"1bd541c2731702a69c94860c2fff350035002535","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3052","title":"Add some debugging logs","createdAt":"2022-09-20T18:02:06Z"}
{"state":"Merged","mergedAt":"2022-09-20T18:05:39Z","number":3053,"mergeCommitSha":"5fb7311b50b7481b4f5cbbc56d5fb04a22a2b8fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3053","title":"remove secret from global env vars","createdAt":"2022-09-20T18:05:23Z"}
{"state":"Merged","mergedAt":"2022-09-21T00:28:23Z","number":3054,"body":"Missed this from previous #3007","mergeCommitSha":"1ad94081d3118d180cb2be0056dcd7b497c65a0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3054","title":"Archive sourcemarks when PR archives threads","createdAt":"2022-09-20T18:21:27Z"}
{"state":"Merged","mergedAt":"2022-09-20T19:32:45Z","number":3055,"mergeCommitSha":"efaafc64e288045c8e9e27ca070c85f2dcecb0ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3055","title":"Add logging for sourcemark lookup failure","createdAt":"2022-09-20T18:57:34Z"}
{"state":"Merged","mergedAt":"2022-09-21T20:04:38Z","number":3056,"body":"Setup highlighting for web.\r\nRequired some refactoring of code block to support full line highlighting and the jumpiness of transitioning from unformatted -> formatted.\r\n\r\n<img width=\"963\" alt=\"CleanShot 2022-09-20 at 12 00 40@2x\" src=\"https://user-images.githubusercontent.com/1553313/191342348-7aeb6116-4b2c-4021-917a-c61fb7fe7f0b.png\">\r\n<img width=\"921\" alt=\"CleanShot 2022-09-20 at 12 00 37@2x\" src=\"https://user-images.githubusercontent.com/1553313/191342354-4251fd8d-2bc0-4a92-8d94-77ac27548c01.png\">\r\n","mergeCommitSha":"bf3fc4a3b7700a8fb237af759540962d41307c80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3056","title":"Highlighting for Web","createdAt":"2022-09-20T19:01:52Z"}
{"state":"Merged","mergedAt":"2022-09-26T15:32:08Z","number":3057,"body":"To activate teammember view: `defaults write com.nextchaptersoftware.UnblockedHub EnableTeamMemberView YES`\r\n\r\n<img width=\"522\" alt=\"CleanShot 2022-09-20 at 15 24 42@2x\" src=\"https://user-images.githubusercontent.com/858772/191375687-77dd5764-effa-441c-963c-d2b80381a1c2.png\">\r\n\r\n","mergeCommitSha":"2871d3b8364f4c28851181381428ed34cdd622be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3057","title":"Bring back teammember view","createdAt":"2022-09-20T20:38:22Z"}
{"state":"Closed","mergedAt":null,"number":3058,"body":"* Ignore files we don't care about in the file explorer\r\n* Enable default bundle splitting in the webpack build -- this makes debug bootup faster, and creates smaller output bundles, as the duplicated data in each bundle is factored out.  Unfortunately the odd breakpoint triggering on startup is still there.","mergeCommitSha":"d4668b7deceb38ffe0f533642e5fd82b4c1b363c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3058","title":"Tweak VSCode build settings","createdAt":"2022-09-20T21:27:07Z"}
{"state":"Merged","mergedAt":"2022-09-26T15:38:39Z","number":3059,"body":"This build hack is necessary because of an Xcode/Swift compiler bug that Ankit was tracking back in 2019, where an embedded app causes module map resolution to fail (still not fixed): https://github.com/apple/swift-nio/issues/1128\r\n\r\nThis PR introduces two new build configurations and a new scheme as a way to omit the embedded video app for our main customers builds (for now).\r\n\r\nClose your eyes \uD83D\uDE48","mergeCommitSha":"00bc28e7f23d1aaab1fce2e285675cab808cbc9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3059","title":"The most insane build hack I've ever done","createdAt":"2022-09-20T21:57:08Z"}
{"state":"Merged","mergedAt":"2022-02-10T17:10:01Z","number":306,"body":"Update to 6.0.0 needs this package updated as well for the typing.","mergeCommitSha":"354738100951fb2eafc81376064e79b80625afce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/306","title":"Update fontawesome svg core package","createdAt":"2022-02-10T01:17:29Z"}
{"state":"Merged","mergedAt":"2022-09-21T01:17:52Z","number":3060,"mergeCommitSha":"d7a033972cb552212c258fe63d11ee37be633c15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3060","title":"Upsert source points to prevent duplicates","createdAt":"2022-09-20T23:39:00Z"}
{"state":"Merged","mergedAt":"2022-09-21T00:28:38Z","number":3061,"body":"This partially reverts #3044","mergeCommitSha":"e5af7d873eff9c3abc7731290300c0154f86f092","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3061","title":"Fix duplicate sourcepoint upload","createdAt":"2022-09-20T23:39:32Z"}
{"state":"Merged","mergedAt":"2022-09-26T16:18:20Z","number":3062,"body":"Curiously when you embed one sandboxed app inside another the permissions requests need to exist at the top level. ","mergeCommitSha":"edbb55ecb28e45b5021630964894a81ef7b26e58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3062","title":"Boot app and create new channel on person click","createdAt":"2022-09-20T23:57:33Z"}
{"state":"Merged","mergedAt":"2022-09-21T01:40:52Z","number":3063,"mergeCommitSha":"4bf8a15710f3f776aa1c7db8f78442dc3a670800","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3063","title":"Fix naming","createdAt":"2022-09-21T01:40:48Z"}
{"state":"Merged","mergedAt":"2022-09-23T03:00:43Z","number":3064,"body":"Changes\r\n\r\n- Read only bearer token\r\n- Update mustache template\r\n- Install read-only authentication\r\n- Impersonation in auth service\r\n\r\nSee also\r\n\r\n- https://linear.app/unblocked/issue/UNB-662/login-as-vscode-user-for-open-source-repo","mergeCommitSha":"ea0392f7effd376138b0ed09a8f5c568a3e1ec95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3064","title":"Login as VSCode user for open source repo","createdAt":"2022-09-21T15:28:32Z"}
{"state":"Closed","mergedAt":null,"number":3065,"body":"This reverts commit 1894366acd4e1979e32b6c319bbdb5c6c378a4fd.\r\n","mergeCommitSha":"b5c6c91b8825f5478a7a64475541cea9399c28b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3065","title":"Revert \"Basic slack importer (#3042)\"","createdAt":"2022-09-21T16:40:48Z"}
{"state":"Merged","mergedAt":"2022-09-21T17:03:26Z","number":3066,"mergeCommitSha":"ae5d4cdc17220f13b434aadd6be059a0f66113ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3066","title":"Add logging","createdAt":"2022-09-21T17:03:21Z"}
{"state":"Merged","mergedAt":"2022-09-21T17:26:38Z","number":3067,"body":"Motivation\r\n- Customers have found these and are messing with them.\r\n- See https://chapter2global.slack.com/archives/C036YH3QF7T/p1663778433704259\r\n\r\nAlso lint cleanup.","mergeCommitSha":"a96bd240150ed2640ebeb9be9be9ffc678311798","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3067","title":"Rip out SM engine settings","createdAt":"2022-09-21T17:14:04Z"}
{"state":"Merged","mergedAt":"2022-09-21T17:26:45Z","number":3068,"body":"Not good forcing every service to be dependent on a queue.\r\nWill be changing the code up.\r\n","mergeCommitSha":"f4a0eac2a2ef9f367886bc401d92166fadc7a5fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3068","title":"Apparently we need pr comments queue access for creating messages","createdAt":"2022-09-21T17:26:35Z"}
{"state":"Merged","mergedAt":"2022-09-21T18:03:07Z","number":3069,"body":"Avoids a write SQL request sometimes.","mergeCommitSha":"aa95b61802ff70c216f996ed8be90d6cc4171c65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3069","title":"Minor DB perf improvement when upserting source points","createdAt":"2022-09-21T17:27:12Z"}
{"state":"Merged","mergedAt":"2022-02-10T01:27:00Z","number":307,"body":"Saving 400MB per image creation by change base image to a more streamline variant.\r\nYeah, I think that's worth it...","mergeCommitSha":"8c8e88c2b850d3cfe18230b370ebed2e3c0218a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/307","title":"Optimize docker base image","createdAt":"2022-02-10T01:21:04Z"}
{"state":"Merged","mergedAt":"2022-09-21T18:03:24Z","number":3070,"body":"Diff between the same pair of commits is always identical, so eligible for immutable caching.","mergeCommitSha":"092742104392bf9e65805663f10557ffc9734c69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3070","title":"Cache git diffs","createdAt":"2022-09-21T17:33:03Z"}
{"state":"Merged","mergedAt":"2022-09-21T18:03:46Z","number":3071,"mergeCommitSha":"3a172443db324940d3361e639648c0941f1f1350","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3071","title":"API spec lint","createdAt":"2022-09-21T17:50:11Z"}
{"state":"Merged","mergedAt":"2022-09-21T18:01:25Z","number":3072,"mergeCommitSha":"8bebaffdcd0d448c1470cc7bf8fd447a602b55c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3072","title":"Add team parsing logign","createdAt":"2022-09-21T18:01:19Z"}
{"state":"Merged","mergedAt":"2022-09-21T18:51:10Z","number":3073,"mergeCommitSha":"7732d79336ab8af27056584cf544ff55bb2bc7d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3073","title":"Updating logging","createdAt":"2022-09-21T18:50:59Z"}
{"state":"Closed","mergedAt":null,"number":3074,"body":"* Add optional `isPinned` property to the Message model as well as the UpdateMessageRequest body\r\n* Implement backend -- needs review on whether this is the best approach","mergeCommitSha":"4571a4bc76353e38cdec08d885f52369cc74d930","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3074","title":"[BREAKS API ON MAIN] Message pinning","createdAt":"2022-09-21T18:54:05Z"}
{"state":"Merged","mergedAt":"2022-09-21T19:19:04Z","number":3075,"mergeCommitSha":"fd3d3ec395ef4143487c06e8c70d7652ecf63faa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3075","title":"Make transacitons more granular","createdAt":"2022-09-21T19:18:11Z"}
{"state":"Closed","mergedAt":null,"number":3076,"mergeCommitSha":"119f51d28755998310cd4d6fb40be946aa9cad80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3076","title":"WIP: Saving a few more seconds (DO NOT MERGE)","createdAt":"2022-09-21T19:51:18Z"}
{"state":"Merged","mergedAt":"2022-09-21T19:54:22Z","number":3077,"body":"- Fix user attribution\r\n- Fi xuser attibution\r\n","mergeCommitSha":"9979bce22317e01f1b1c0415d43e255009352111","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3077","title":"FixUserAttribution","createdAt":"2022-09-21T19:54:17Z"}
{"state":"Merged","mergedAt":"2022-09-21T22:13:10Z","number":3078,"body":"This is a regression from [here](https://github.com/NextChapterSoftware/unblocked/commit/1f958796c52d4e724094deef30fadacc66a04250#diff-db6b08442f5dd2a37777c541206eb0c302f87d6ef3a73c43241a84f54289d648R68) -- not sure why the style was moved though.\r\n\r\nbefore:\r\n<img width=\"685\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/191602388-0124491b-74ea-4e1f-98ee-cd800873b9fa.png\">\r\n\r\nafter:\r\n<img width=\"508\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/191602418-99fb9287-a33b-4215-938e-f8c773ac5db7.png\">\r\n","mergeCommitSha":"5011a482c809698b827383e5292128afdb15da4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3078","title":"Move padding to header","createdAt":"2022-09-21T20:20:07Z"}
{"state":"Merged","mergedAt":"2022-09-22T18:49:36Z","number":3079,"body":"Allow users to hide source mark gutter in VSCode.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/191619502-5414e5e9-307e-4fbb-89c1-f712dd102b75.mp4\r\n\r\n\r\nThe package.json is really bloated due to limitations on VSCode command titles/icons.\r\nhttps://github.com/microsoft/vscode/issues/34048\r\nGitLens runs into the same issue which blows up their extension.package.json due to all the state permutations.","mergeCommitSha":"63d79b965a523a793aee6e156e91620bb35da0d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3079","title":"Jeff/unb 666 allow users to hide insight bubbles in","createdAt":"2022-09-21T22:13:18Z"}
{"state":"Merged","mergedAt":"2022-02-10T16:57:46Z","number":308,"body":"Just a tool to debug stuff ","mergeCommitSha":"6a1539491b1fc70bab7f1ebdf91c68305241aab9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/308","title":"Adds configurable SQL logging to transactions","createdAt":"2022-02-10T16:42:02Z"}
{"state":"Merged","mergedAt":"2022-09-23T20:42:52Z","number":3080,"body":"Show Slack threads in typescript clients:\r\n\r\n* Show slack icon for slack-related threads (as they can be shown in the `mine` listings now)\r\n* In VSCode `dev` builds, add a new `Unblocked: Current File Threads` panel, that displays all threads relevant to the current file. Right now this includes Slack threads and PR comments.  This is a temporary panel, I suspect we will be merging this data into one of the existing panels before enabling this in prod.\r\n\r\n<img width=\"1529\" alt=\"Screen Shot 2022-09-21 at 3 56 26 PM\" src=\"https://user-images.githubusercontent.com/2133518/191625112-bc78417c-f6ff-4c7a-8768-a5e89bffc2e2.png\">\r\n<img width=\"1485\" alt=\"Screen Shot 2022-09-21 at 3 57 10 PM\" src=\"https://user-images.githubusercontent.com/2133518/191625120-9c650d06-41ba-44b6-b8e5-2d8ce7d444cc.png\">\r\n","mergeCommitSha":"5906c2783aac4c84c7dd5d53735254c926aa1fa4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3080","title":"Show Slack threads in TS clients","createdAt":"2022-09-21T23:03:07Z"}
{"state":"Merged","mergedAt":"2022-09-23T05:43:44Z","number":3081,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1663359669022899","mergeCommitSha":"7e1058c273591b61e3fb7c9ed1c8dafca8cac921","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3081","title":"Jeff/unify build versions","createdAt":"2022-09-21T23:32:52Z"}
{"state":"Merged","mergedAt":"2022-09-22T00:04:01Z","number":3082,"mergeCommitSha":"231cd5cdee2158baa1ae93a2965a70c8a8091adc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3082","title":"Fix channel paging","createdAt":"2022-09-22T00:03:41Z"}
{"state":"Merged","mergedAt":"2022-09-22T02:19:12Z","number":3083,"mergeCommitSha":"65e7821d057c5ef772e14600b2bf07ae0e7196d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3083","title":"Ensure we consume threads with nested prs","createdAt":"2022-09-22T02:12:03Z"}
{"state":"Merged","mergedAt":"2022-09-22T17:35:03Z","number":3084,"mergeCommitSha":"cc0db4fe46c577cd06ccf8db377759ca9a0a9fb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3084","title":"Admin console slack stuff","createdAt":"2022-09-22T17:27:24Z"}
{"state":"Merged","mergedAt":"2022-09-22T18:29:18Z","number":3085,"body":"Temporary patch to disassociate refreshing auth token and refreshing person. This will no longer block requests when auth refresh is successful but getPersons fails.\r\n\r\nIssue was that getPersons was throwing a 401 (https://github.com/NextChapterSoftware/unblocked/pull/2824) which we are no longer manually retrying (https://github.com/NextChapterSoftware/unblocked/pull/2982).\r\n\r\nThere needs to be some refactoring done to authstore in general to lock this down some more.\r\n\r\n","mergeCommitSha":"60aae9761ae04a07a342ed94232ba17ab0b5a628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3085","title":"Separate Auth and Refresh Person","createdAt":"2022-09-22T17:52:02Z"}
{"state":"Merged","mergedAt":"2022-09-22T23:27:33Z","number":3086,"body":"We have special lists of:\r\n- internal users\r\n- internal teams\r\n- demo users\r\n- impersonators (NEW)\r\n\r\nWhich are used to provide special handling for:\r\n- user engagement metrics\r\n- client version tracking\r\n- intercom attribution\r\n- fake recommendation model generation\r\n- login as user (NEW)","mergeCommitSha":"b034fda48d5dce985eb0c2e918ac2da96ebfb22c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3086","title":"Introduce Insiders Service to centralize internal user/team behaviour","createdAt":"2022-09-22T20:20:10Z"}
{"state":"Merged","mergedAt":"2022-09-22T22:43:41Z","number":3087,"body":"Version catalogs are the recommended approach these days...","mergeCommitSha":"7702b990cfef972bcb03e0bdf94bfaace9af9efa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3087","title":"Move away from deprecated way of managing gradle versions","createdAt":"2022-09-22T22:30:03Z"}
{"state":"Merged","mergedAt":"2022-09-23T16:20:36Z","number":3088,"body":"In both code action and context editor.\r\n\r\nNote that we can't use the `category` setting for this, as that is *only* displayed in the command palette, not in context menus or elsewhere.  I think we should probably just remove the category elsewhere, and explicitly have then prefix everywhere?\r\n\r\n<img width=\"424\" alt=\"Screen Shot 2022-09-22 at 4 45 54 PM\" src=\"https://user-images.githubusercontent.com/2133518/191870003-df53b23e-7800-452a-b6a7-840b46ac2d24.png\">\r\n<img width=\"406\" alt=\"Screen Shot 2022-09-22 at 4 46 01 PM\" src=\"https://user-images.githubusercontent.com/2133518/191870007-5a7c3739-eec0-4fd7-af37-3066b4e7088a.png\">\r\n","mergeCommitSha":"e2453277070eb9ca5e6877544022747cb9568db1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3088","title":"Add 'Unblocked' prefix to Start Discussion option","createdAt":"2022-09-22T23:47:27Z"}
{"state":"Merged","mergedAt":"2022-09-23T02:48:53Z","number":3089,"mergeCommitSha":"b63e517e28b0b6da46f724b10d340f015f65a78a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3089","title":"Cleanup gradl efiles","createdAt":"2022-09-23T02:22:24Z"}
{"state":"Merged","mergedAt":"2022-02-10T18:17:50Z","number":309,"body":"PRs that only change dependencies should run CI too.","mergeCommitSha":"ee03b0f356e40692e2a1c379251d22fb2a6049b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/309","title":"Run CI on package.json / package-lock.json change","createdAt":"2022-02-10T17:06:53Z"}
{"state":"Merged","mergedAt":"2022-09-23T03:23:54Z","number":3090,"body":"https://linear.app/unblocked/issue/UNB-662/login-as-vscode-user-for-open-source-repo","mergeCommitSha":"bddfc38d366c5b094b26aecff5ecbcb5ab7c8971","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3090","title":"Impersonator should not affect client version metric","createdAt":"2022-09-23T03:13:42Z"}
{"state":"Merged","mergedAt":"2022-09-23T03:43:58Z","number":3091,"mergeCommitSha":"d9e23a4017433708929537346b72e4a497e0207d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3091","title":"Whoopsie, fixing prod","createdAt":"2022-09-23T03:43:46Z"}
{"state":"Merged","mergedAt":"2022-09-23T04:22:13Z","number":3092,"body":"This reverts commit 092742104392bf9e65805663f10557ffc9734c69.\n\nTurns out that massive branch merges occur in repos, like `expo/expo`\nwhere 26,075 files and 3,864,730 lines of diffs were cached.","mergeCommitSha":"2fdd64f6077227692328ce59ad699a240da857e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3092","title":"Revert \"Cache git diffs (#3070)\"","createdAt":"2022-09-23T04:21:50Z"}
{"state":"Merged","mergedAt":"2022-09-23T06:04:21Z","number":3093,"mergeCommitSha":"f2172d88294f46f7356b75b53c8b3cbc33ce4ab8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3093","title":"Sort lines","createdAt":"2022-09-23T05:58:32Z"}
{"state":"Merged","mergedAt":"2022-09-23T08:53:47Z","number":3094,"mergeCommitSha":"841cf9c1ccabe8da8889fbc038206a5df326a8d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3094","title":"Upgrade some of our dependencies","createdAt":"2022-09-23T06:23:29Z"}
{"state":"Merged","mergedAt":"2022-09-23T06:54:47Z","number":3095,"body":"Motivation is that full recalculation is unusably slow, maxing out mem and cpu.\nUntil we tune the algorithm, full recalculation is going to stay off.\n\nRely only on incremental recalculation so that we only recalculate marks necessary\nfor \"open\" files. In this approach, we still bulk fetch all mark data but we only\nrecalculate part of the entire dataset and typically deferred this until the file\nis opened.\n\nDownside risk is that points are not created for web extension consumption; so this\nbreaks the effectiveness of the web extension.","mergeCommitSha":"f2a2dfdb6f95c9a61e114a015f9c1732efc03cce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3095","title":"Turn off full recalculation","createdAt":"2022-09-23T06:46:18Z"}
{"state":"Merged","mergedAt":"2022-09-26T16:22:33Z","number":3096,"mergeCommitSha":"f99a2fa6eb922b6058076ffd0c39a5605ec1cb7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3096","title":"Update agora sdk to release version","createdAt":"2022-09-23T16:08:58Z"}
{"state":"Closed","mergedAt":null,"number":3097,"mergeCommitSha":"5263ed48cf7d73c3eb9fee2fb9feffdbfbf1847c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3097","title":"Adds identity override to hub","createdAt":"2022-09-23T18:09:14Z"}
{"state":"Merged","mergedAt":"2022-09-23T19:20:53Z","number":3098,"body":"Just adds a badge to admin web.","mergeCommitSha":"7f82ae7d6b42a6e400299eb7e58be58479c797cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3098","title":"Admin web shows impersonators","createdAt":"2022-09-23T18:53:22Z"}
{"state":"Merged","mergedAt":"2022-09-26T16:42:04Z","number":3099,"body":"(To be a part of the upcoming onboarding  revamp work)\r\n\r\nbasic implementation:\r\n![CleanShot 2022-09-23 at 13 44 48](https://user-images.githubusercontent.com/13431372/192053713-4bd29d2f-2bae-42c6-9ea6-cb0a8b06f8e7.gif)\r\n\r\nwith a custom template:\r\n<img width=\"475\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/192053444-17938871-d3a1-429e-a7fd-14aabaddef88.png\">\r\n\r\nNote: Had to update a lot of the css imports in shared component css files in order for storybook to load properly","mergeCommitSha":"0cb233efb51b50be464885f24729b20e2c433492","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3099","title":"Basic slider carousel component","createdAt":"2022-09-23T20:43:35Z"}
{"state":"Merged","mergedAt":"2022-01-12T23:17:50Z","number":31,"body":"Due to limitations in `eksctl` cli, EKS deployments are manual. Implementing CI/CD for EKS cluster deployments requires additional components to properly handle configuration changes in order to call appropriate sub-commands. Considering the low frequency of changes to core EKS configurations, full automation of this component is better left for future. \r\n\r\n- Added `eksctl` yaml config file for Dev Kubernetes cluster \r\n- Added README with deployment instructions\r\n- Deployed EKS cluster to Dev with latest configurations \r\n","mergeCommitSha":"4ab1afc2d4f1435c163e9eaaf056b75feca61fe1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/31","title":"Added Kubernetes deployment config","createdAt":"2022-01-12T20:45:45Z"}
{"state":"Merged","mergedAt":"2022-02-10T18:56:48Z","number":310,"body":"Serializer was bombing on GitHub requests because it was strict","mergeCommitSha":"3c336545b1541a53120c4d3086946faf58275749","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/310","title":"Relax json serialization for kotlinx","createdAt":"2022-02-10T18:42:07Z"}
{"state":"Merged","mergedAt":"2022-09-23T22:55:15Z","number":3100,"body":"before:\r\n<img width=\"744\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/192062060-95d01152-51d7-489f-bda7-0af10c2a2dbb.png\">\r\n\r\nafter:\r\n<img width=\"706\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/192062133-5f3c9724-2302-4ffd-b746-f069c882b2cc.png\">\r\n","mergeCommitSha":"da831aa233d186bca6e89c231e4ad546e39d8e6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3100","title":"Fix email formatting","createdAt":"2022-09-23T21:57:17Z"}
{"state":"Merged","mergedAt":"2022-09-23T23:34:25Z","number":3101,"mergeCommitSha":"3a024b82856b67d29872e2b2ea1f122518654867","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3101","title":"Minor slack webhook challenge handling","createdAt":"2022-09-23T23:15:05Z"}
{"state":"Merged","mergedAt":"2022-09-25T20:11:35Z","number":3102,"body":"#### Changes\r\n- Works consistently on Hub, WebExt, VSCode, Dashboard clients\r\n- No client changes needed to activate\r\n- Does not break API at all; optional headers were never used and now removed.\r\n\r\n#### How to Login-as-User\r\n- https://www.notion.so/nextchaptersoftware/How-to-Login-as-User-c455944aa6fa4be2a090ab21d436afb7","mergeCommitSha":"4b19e914b506d20ebff5468b42d687dcfdfa8a7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3102","title":"Login-As-User™ is controlled by server [BREAKS API ON MAIN]","createdAt":"2022-09-25T19:31:15Z"}
{"state":"Merged","mergedAt":"2022-09-27T06:38:10Z","number":3103,"body":"Problem\r\n\r\nWe compute diffs for all files in a commit to track sourcemarks that move across files\r\nin a commit. However, mostly sourcemarks are not moved between files.\r\n\r\nChanges\r\n- Compute diff for only the current file in the commit that the sourcemark lives in.\r\n- Only computing the diffs for the _other_ files in the commit when we know that\r\n  the sourcemark may have been moved.\r\n\r\nImpact\r\n- expected to bring down memory usage, since we will cache less Git content\r\n- expected to improve runtime perf because we do not parse diffs as frequently\r\n\r\nResults\r\n- full recalculation on `nextchaptersoftware/unblocked`:\r\n\r\nmemory (MB) | before | after\r\n--|--|--\r\nrss | 1,968 | 131\r\nmaxRSS | 1,968 | 131\r\nheapUsed | 1,711 | 81\r\nheapTotal | 1,856 | 91\r\n\r\n- full recalculation on `expo/expo`:\r\n\r\nmemory (MB) | before | after\r\n--|--|--\r\nrss | fail | 2,003\r\nmaxRSS | fail | 2,085\r\nheapUsed | fail | 646\r\nheapTotal | fail | 661\r\n","mergeCommitSha":"ea18b89a99e38134ee27eb54fd51c4b1e0ce0b3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3103","title":"Defer computing Git diffs unless necessary","createdAt":"2022-09-26T06:42:09Z"}
{"state":"Merged","mergedAt":"2022-09-26T20:40:15Z","number":3104,"body":"`No props to render elements` is logged a lot when no failure has actually occurred, so move it to a point where we do think a failure has happened.  `Setting authentication token` is not a useful log AFAICT.","mergeCommitSha":"684070b94eb1d24a3659f932fdd520289c58031e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3104","title":"Remove spammy webview logs","createdAt":"2022-09-26T16:29:41Z"}
{"state":"Merged","mergedAt":"2022-09-26T19:10:23Z","number":3105,"body":"Auth lookup fails for Dennis as a result.","mergeCommitSha":"d4a74d8583d3f7d6714ec95c62e7a1e23c84d0b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3105","title":"Can't spell Dennis","createdAt":"2022-09-26T16:56:32Z"}
{"state":"Merged","mergedAt":"2022-09-26T17:58:38Z","number":3106,"mergeCommitSha":"727a7392939aa6411879bc4e3c66ce479baaa2c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3106","title":"Flip all macOS builders to macOS 12 using Xcode 14.0.1","createdAt":"2022-09-26T17:43:18Z"}
{"state":"Merged","mergedAt":"2022-09-26T19:42:43Z","number":3107,"mergeCommitSha":"9a084ce7d9397e7a00ef7882c4d005bb1f857003","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3107","title":"Add slack webhook to infra","createdAt":"2022-09-26T19:38:36Z"}
{"state":"Merged","mergedAt":"2022-09-26T20:38:43Z","number":3108,"mergeCommitSha":"ba6ad39f9fb413f2557940cd058e2715128bec25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3108","title":"Fix LUIElement break and remove unncecessary entitlements from base app","createdAt":"2022-09-26T20:23:30Z"}
{"state":"Merged","mergedAt":"2022-09-26T20:38:59Z","number":3109,"mergeCommitSha":"9b7d52e2e1fd6dd9ca12e6b44ea14eba4a1f84ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3109","title":"Ignore case when sorting team members","createdAt":"2022-09-26T20:28:03Z"}
{"state":"Merged","mergedAt":"2022-02-10T19:39:03Z","number":311,"mergeCommitSha":"bc027f942bfd610750853535e2f33272304031b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/311","title":"Fix incorrect github user id type","createdAt":"2022-02-10T19:30:46Z"}
{"state":"Merged","mergedAt":"2022-09-26T23:51:24Z","number":3110,"body":"To align teammember view with threads:\r\n\r\n## To this\r\n<img width=\"484\" alt=\"CleanShot 2022-09-26 at 16 42 26@2x\" src=\"https://user-images.githubusercontent.com/858772/192399647-44afed8f-13a1-41a1-bda3-758d5df16fb8.png\">\r\n\r\n<img width=\"473\" alt=\"CleanShot 2022-09-26 at 16 43 15@2x\" src=\"https://user-images.githubusercontent.com/858772/192399672-9e9763b4-6d8f-4bc0-9316-bfd6b95ce1f8.png\">\r\n\r\n<img width=\"473\" alt=\"CleanShot 2022-09-26 at 16 43 51@2x\" src=\"https://user-images.githubusercontent.com/858772/192399730-4d105969-ee93-49a4-9d44-3abc845d4e55.png\">\r\n\r\n<img width=\"475\" alt=\"CleanShot 2022-09-26 at 16 43 39@2x\" src=\"https://user-images.githubusercontent.com/858772/192399740-2aa22a49-bb72-436b-b509-c154c139c631.png\">\r\n\r\n","mergeCommitSha":"49040a2d8b47b649dfb73fa60d6817740ef3eaa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3110","title":"Fix teammember spacing","createdAt":"2022-09-26T20:54:51Z"}
{"state":"Merged","mergedAt":"2022-09-28T20:06:11Z","number":3111,"body":"AuthStore was fragile due to architecture and lack of tests.\r\n\r\nEverything is now more stream based when possible.\r\n\r\nRefactored to move away from Zustand and make it entirely stream based.\r\nRefactored AuthState into multiple streams.\r\n\r\n\r\n","mergeCommitSha":"a0a67692c632bff9bf022402fe7344250ee736e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3111","title":"Refactored Auth Store","createdAt":"2022-09-26T21:39:24Z"}
{"state":"Merged","mergedAt":"2022-09-26T23:03:02Z","number":3112,"body":"Fixes UNB-671\r\n\r\nTo be reverted once run for the unblocked repo, since its the only one with LEFT side threads.","mergeCommitSha":"9efca1cb544fbc7283359a23a124368238a0d668","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3112","title":"Delete LEFT side threads during bulk ingestion","createdAt":"2022-09-26T22:48:17Z"}
{"state":"Merged","mergedAt":"2022-09-26T23:23:54Z","number":3113,"body":"This change moves all codegen to a single location. Specifically:\r\n`generatedApi` and `GRPC` to:\r\n- `generated/api`\r\n- `generated/grpc`","mergeCommitSha":"149a2b60c553af688401e90a0807e869348ebbc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3113","title":"Consolidate generated code to a single directory","createdAt":"2022-09-26T22:59:10Z"}
{"state":"Merged","mergedAt":"2022-09-27T02:35:15Z","number":3114,"body":"This reverts commit 9efca1cb544fbc7283359a23a124368238a0d668.","mergeCommitSha":"abb99006b8bec288503a983003d2975f3532814b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3114","title":"Revert \"Delete LEFT side threads during bulk ingestion (#3112)\"","createdAt":"2022-09-26T23:04:25Z"}
{"state":"Merged","mergedAt":"2022-09-26T23:12:06Z","number":3115,"mergeCommitSha":"fffbf2798f267d7f9142b6d018faa8c943c7f043","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3115","title":"Remove unused import","createdAt":"2022-09-26T23:11:55Z"}
{"state":"Closed","mergedAt":null,"number":3116,"body":"Ideally this should fail PR build...","mergeCommitSha":"98e98d70e083c558d6be238250380be781a36ce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3116","title":"[DO NOT MERGE] Add unused import","createdAt":"2022-09-26T23:15:20Z"}
{"state":"Merged","mergedAt":"2022-09-27T00:16:01Z","number":3117,"mergeCommitSha":"56eee3fd50434bc349eac379010d65ad36dbca82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3117","title":"Add slack webhook framework","createdAt":"2022-09-26T23:31:42Z"}
{"state":"Merged","mergedAt":"2022-09-26T23:56:28Z","number":3118,"mergeCommitSha":"e5370ae192a6d10153eef9a27ccafd2d9e0a460f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3118","title":"Fix teammember spacing (take 2)","createdAt":"2022-09-26T23:54:09Z"}
{"state":"Merged","mergedAt":"2022-09-27T00:09:36Z","number":3119,"body":"Used for validating snippets.","mergeCommitSha":"83f7ced916a1f09900a4235cf03eb77db21e95cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3119","title":"Add Git debugging for source points","createdAt":"2022-09-27T00:03:57Z"}
{"state":"Merged","mergedAt":"2022-02-10T21:21:22Z","number":312,"body":"It's already deployed Dev and prod and the config looks identical to the working hand configured setup in Dev. Waiting for final confirmation from Jeff but this should be good to go. \r\n","mergeCommitSha":"0df7e2df0d393d14121c6a0dc86f61c040270da2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/312","title":"fix cloudfront cors issue using new resource in CDK","createdAt":"2022-02-10T20:45:04Z"}
{"state":"Merged","mergedAt":"2022-09-27T07:11:16Z","number":3120,"body":"Immediately after construction the source mark calculator now processes the top-10 files ordered\r\nby thread count. Motivation is to ensure that there are several files with lots of content\r\navailable soon after Unblocked has been loaded in VSCode.\r\n\r\nhttps://chapter2global.slack.com/archives/C036YH3QF7T/p1663978621194009","mergeCommitSha":"c3ced5e1c16c8aee6fd326dbc4e3903311f9d0de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3120","title":"Partial recalculation processes top-N files","createdAt":"2022-09-27T00:45:13Z"}
{"state":"Merged","mergedAt":"2022-09-27T03:10:07Z","number":3121,"mergeCommitSha":"6a6501be14875e36e94561ca5e886976281756b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3121","title":"Add message event handlers","createdAt":"2022-09-27T03:09:50Z"}
{"state":"Merged","mergedAt":"2022-09-27T04:51:59Z","number":3122,"mergeCommitSha":"e542657bd14a5c2a5c8f821d16ebc2690e0b01c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3122","title":"update more dependencies","createdAt":"2022-09-27T03:45:14Z"}
{"state":"Merged","mergedAt":"2022-10-05T18:20:02Z","number":3123,"body":"This reverts commit f2a2dfdb6f95c9a61e114a015f9c1732efc03cce from #3095.\r\n\r\nMerge when memory is constrained.","mergeCommitSha":"701d2cc5ec41a42407d0c4f0b22d1bfc0a77b8aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3123","title":"Re-enable full recalculation","createdAt":"2022-09-27T03:52:08Z"}
{"state":"Merged","mergedAt":"2022-09-27T17:06:47Z","number":3124,"body":"Change the source map type for VSCode. This improves the debugging experience:\r\n\r\n* Startup time is a lot quicker\r\n* When breakpoints are set on startup, don't break execution in a random location","mergeCommitSha":"05a89fa3b8768c6e5dad5d344a84cadc80f3f1ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3124","title":"Improve VSCode debug startup time","createdAt":"2022-09-27T16:27:05Z"}
{"state":"Merged","mergedAt":"2022-09-27T18:32:43Z","number":3125,"body":"https://linear.app/unblocked/issue/UNB-672/source-mark-engine-doesnt-handle-file-paths-with-spaces\r\n\r\nMostly updating test fixtures as a result of changing the diff arguments. The fix is pretty small/simple.","mergeCommitSha":"4dd6702a88094d727671582dde645caf04620446","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3125","title":"Source mark engine doesn't handle file paths with spaces","createdAt":"2022-09-27T18:16:22Z"}
{"state":"Merged","mergedAt":"2022-09-27T19:59:00Z","number":3126,"body":"Because published web extensions are > than existing build numbers, add .1 minor version to update HWM.\r\n\r\nMainly needed for Safari which is currently at 1.0.7533 while our installer build numbers at 1.0.437","mergeCommitSha":"0d07f3f0e53545467a1dca73dc3fde9731fd1b18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3126","title":"Update Web extension HWM","createdAt":"2022-09-27T18:44:45Z"}
{"state":"Merged","mergedAt":"2022-09-27T20:11:45Z","number":3127,"mergeCommitSha":"f0bda53c9d99042c0f6f1d4cae36dccc7807ba4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3127","title":"Use universal release tag","createdAt":"2022-09-27T19:07:58Z"}
{"state":"Merged","mergedAt":"2022-09-27T20:47:36Z","number":3128,"mergeCommitSha":"b273f7b3fa426526969500fda1631d7521fc9dd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3128","title":"Fix admin web copy command which had broken shell escaping","createdAt":"2022-09-27T20:47:25Z"}
{"state":"Closed","mergedAt":null,"number":3129,"body":"Add API to fetch VCP for a given team / user.\r\n\r\nEach client will have a pusher channel for each team membership a person has. This channel updates whenever a there's an update to the Video Channel Participant list for a team membership.\r\n\r\nWhenever this channel sends an event, the client will call getVideoChannelParticipants to get an updated list.\r\n","mergeCommitSha":"977c1623d0dfc2861ad9c2920af76916d8ff2e18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3129","title":"Add VideoChannelParticipant request","createdAt":"2022-09-27T21:32:11Z"}
{"state":"Merged","mergedAt":"2022-02-10T21:08:36Z","number":313,"body":"Rest object `id`s map to `databaseId` in GraphQL objects, while rest object `node_id`s map to `id` in GraphQL objects:\r\n\r\nhttps://developer.github.com/changes/2017-12-19-graphql-node-id/\r\nhttps://docs.github.com/en/graphql/guides/using-global-node-ids\r\n\r\nOur GraphQL UserQuery uses `databaseId` as the identity external ID, while thus far the pull request GraphQL queries have used `id` as the external ID. Let's make sure we use the same field.","mergeCommitSha":"455b5b91d8d0b0fc5237de5849d262001ff403d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/313","title":"Use databaseId instead of id","createdAt":"2022-02-10T20:53:14Z"}
{"state":"Merged","mergedAt":"2022-09-28T01:24:45Z","number":3130,"body":"Problem\n\n- Commit propagation edges are constructed based on the original point's file, but\n  also taking into account when the file is renaemd or moved.\n\n- However, when the _point_ is moved from one file to another (which is not the same\n  as the file itself being renamed or moved), then the original propagation path is\n  invalid.\n\nChanges\n\n- Refactor the commit propagation so that it can start from an arbitrary commit.\n\n- Detect when the point has moved to another file. If it has moved then we re-generate\n  the remainder of the propagation path, clearing the current path and restarting\n  from the moved point instead.","mergeCommitSha":"9ad0f3fc52a5ebf3876081afece76441e18007e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3130","title":"Commit propagation changes course when the sourcemark moves to another file","createdAt":"2022-09-27T22:06:30Z"}
{"state":"Merged","mergedAt":"2022-09-28T21:51:14Z","number":3131,"body":"## Summary\r\nThis PR does two things:\r\n1. Modernizes the Video tests to operate against the local PG stack instead of mock stores. This caught a whole bunch of issues\r\n2. Adds the \"Declined\" participant status type, and changes the \"Pending\" type to \"Invited\" to reduce confusion\r\n\r\nThe `Status` type denotes a video channel participant's channel status. Clients will listen on a push channel that will vend video channel participant changes for that user, which drives the invite and realtime chat experiences","mergeCommitSha":"58dbb776e7e39a0182bf2d2f414516167e558425","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3131","title":"[BREAKS API ON MAIN] - Add \"Declined\" type to VidepChannelParticipant.Status enum","createdAt":"2022-09-27T22:41:22Z"}
{"state":"Merged","mergedAt":"2022-09-30T22:09:18Z","number":3132,"body":"Update getVideoChannels to return only for me. Setup for modifiedSince.\r\nAdd Invite API\r\nAdd date to VideoParticipants for notifications purposes","mergeCommitSha":"998c745a4a58e2c8a867314ae94486fdecfcdb2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3132","title":"[BREAKS API ON MAIN] Updated channels api","createdAt":"2022-09-27T23:56:51Z"}
{"state":"Merged","mergedAt":"2022-09-28T01:37:49Z","number":3133,"mergeCommitSha":"92225001321c09e28f8507e110469fceb8a9917b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3133","title":"Add webhook processing for slack","createdAt":"2022-09-28T00:14:21Z"}
{"state":"Merged","mergedAt":"2022-09-28T04:52:01Z","number":3134,"mergeCommitSha":"c5d133994e26e5edf61037c02eb08f2ba0930cd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3134","title":"Fix millisecond resolution","createdAt":"2022-09-28T04:51:45Z"}
{"state":"Merged","mergedAt":"2022-09-28T06:09:43Z","number":3135,"mergeCommitSha":"54fecd0795d9e11cacfb5d1b25c79f54b232f10f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3135","title":"update cdk deps","createdAt":"2022-09-28T06:06:07Z"}
{"state":"Merged","mergedAt":"2022-09-28T16:48:54Z","number":3136,"body":"# Before (CSS broken)\r\n\r\n<img width=\"805\" alt=\"Screen Shot 2022-09-28 at 09 03 07\" src=\"https://user-images.githubusercontent.com/1798345/192829185-bb3d4b68-228e-4362-bd3e-983316f1fa92.png\">\r\n\r\n(https://admin.prod.getunblocked.com/versions/4c7689e7-68c9-4299-8843-bb521b99225d)\r\n\r\n\r\n# After\r\n\r\n<img width=\"1229\" alt=\"Screen Shot 2022-09-28 at 09 04 44\" src=\"https://user-images.githubusercontent.com/1798345/192829534-072533ab-36ee-4df0-9f3e-6ee0072e6118.png\">\r\n\r\n","mergeCommitSha":"291dac929d80531675036f611fe8713daa4a8c0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3136","title":"Replace rawgit.com CDN as it has been EOL","createdAt":"2022-09-28T16:02:26Z"}
{"state":"Merged","mergedAt":"2022-09-28T18:38:39Z","number":3137,"body":"## Current shipping client\r\n\r\n<img width=\"467\" alt=\"CleanShot 2022-09-28 at 10 38 59@2x\" src=\"https://user-images.githubusercontent.com/858772/192851821-78b461a3-a6fd-4e4a-8f35-9a93ed52aa50.png\">\r\n<img width=\"470\" alt=\"CleanShot 2022-09-28 at 10 38 52@2x\" src=\"https://user-images.githubusercontent.com/858772/192851837-2e4b07a9-a9f0-419d-a583-bd39c061669d.png\">\r\n<img width=\"468\" alt=\"CleanShot 2022-09-28 at 10 42 25@2x\" src=\"https://user-images.githubusercontent.com/858772/192851971-4e8a1b64-40a4-4fd2-b09d-588e32a744e3.png\">\r\n<img width=\"475\" alt=\"CleanShot 2022-09-28 at 10 42 16@2x\" src=\"https://user-images.githubusercontent.com/858772/192851979-4af9497e-330f-4b38-96d7-95416f20d25f.png\">\r\n\r\n## With Tabs\r\n<img width=\"473\" alt=\"CleanShot 2022-09-28 at 10 43 11@2x\" src=\"https://user-images.githubusercontent.com/858772/192852226-f2d9ba7a-f2c6-4c32-a26e-775d4f281bfc.png\">\r\n\r\n<img width=\"477\" alt=\"CleanShot 2022-09-28 at 10 43 24@2x\" src=\"https://user-images.githubusercontent.com/858772/192852271-1013dd18-1e8b-4b25-b079-e4baf0ee6d5a.png\">\r\n\r\n<img width=\"467\" alt=\"CleanShot 2022-09-28 at 10 43 32@2x\" src=\"https://user-images.githubusercontent.com/858772/192852276-844ffe47-9259-4066-ab54-4bc55b5e67e7.png\">\r\n<img width=\"466\" alt=\"CleanShot 2022-09-28 at 10 43 39@2x\" src=\"https://user-images.githubusercontent.com/858772/192852285-3ce8c5c9-9182-4681-960f-f9523127a294.png\">\r\n<img width=\"470\" alt=\"CleanShot 2022-09-28 at 10 44 15@2x\" src=\"https://user-images.githubusercontent.com/858772/192852328-70613a38-8b7f-479d-b1f4-e8619bebcde9.png\">\r\n","mergeCommitSha":"28a5cb3600d36f6759367aa9fbb0dacca68ddb1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3137","title":"Hub touch ups","createdAt":"2022-09-28T17:39:38Z"}
{"state":"Merged","mergedAt":"2022-09-28T18:58:45Z","number":3138,"mergeCommitSha":"74e983b8a1f92dd32321145deb9c60ccc40d1054","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3138","title":"Minor rename","createdAt":"2022-09-28T17:58:11Z"}
{"state":"Merged","mergedAt":"2022-09-28T18:54:53Z","number":3139,"body":"When we display the read-only SM resolution view, re-resolve and display the view whenever VSCode's window is refocused, so if the user runs git in terminal we will update the display.\r\n\r\nNow that I'm playing with this, I'm not sure it 100% solves this.  The user may use the VSCode UI to change branches, or may run this command in the built-in VSCode terminal. Something to consider for the future.\r\n\r\nhttps://user-images.githubusercontent.com/2133518/192856697-672397a4-fc1c-42a2-8d1e-bdba51aa5aac.mp4\r\n\r\n","mergeCommitSha":"905e42d46feaf39892ff624a616496448c44392c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3139","title":"Re-resolve SMs for read-only view when focused","createdAt":"2022-09-28T18:10:01Z"}
{"state":"Merged","mergedAt":"2022-02-10T21:48:02Z","number":314,"mergeCommitSha":"e65cae417d17e1b2b88e36584dd970b6a6df7726","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/314","title":"Fix cdk pretty and lint","createdAt":"2022-02-10T21:34:32Z"}
{"state":"Merged","mergedAt":"2022-09-28T20:27:49Z","number":3140,"body":"This pr was inspired by a pr made long time ago in Skywagon days that linted out expressions in log statements.\r\n\r\nRichie once told me, why the hell are you doing variable evaluation in a log statement, why aren’t you indexing it?\r\nAnd he’s right.\r\n","mergeCommitSha":"81c54d4413506b84ece5c0e33be77208afa413e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3140","title":"Inspired by Richard Bresnan","createdAt":"2022-09-28T19:33:06Z"}
{"state":"Merged","mergedAt":"2022-09-28T20:18:29Z","number":3141,"body":"\r\n<img width=\"475\" alt=\"CleanShot 2022-09-28 at 13 11 24@2x\" src=\"https://user-images.githubusercontent.com/858772/192879169-dcb03cab-b45b-4206-b987-88edc3d226c1.png\">\r\n","mergeCommitSha":"7087c38ce86a944f0020a76a1ac831b70f659c2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3141","title":"Reduce unread dot indicator size by 2pt","createdAt":"2022-09-28T19:54:43Z"}
{"state":"Merged","mergedAt":"2022-09-28T23:00:55Z","number":3142,"mergeCommitSha":"e4ba1b83e1041fefdffcbadfd685191678c2884e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3142","title":"Ability to toggle full recalculation ON/OFF via environment","createdAt":"2022-09-28T20:13:23Z"}
{"state":"Merged","mergedAt":"2022-09-28T20:38:06Z","number":3143,"mergeCommitSha":"46c2581af890fbb487da0f12fd0875431539d570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3143","title":"Reduce disclosure size","createdAt":"2022-09-28T20:37:43Z"}
{"state":"Merged","mergedAt":"2022-09-28T23:05:38Z","number":3144,"body":"Changes\n\n- time values in seconds\n- memory values in MB\n- add wallclock time\n\nAlso\n\n- defer upstream of new points till after full recalculation\n- new env vars to control SM internal debugging:\n   - SOURCEMARK_DEBUG\n   - SOURCEMARK_VERBOSE","mergeCommitSha":"9eb276977415363bd2667d76d0d0b5ba831c1587","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3144","title":"Tweak sourcemark benchmark resource metrics","createdAt":"2022-09-28T21:20:35Z"}
{"state":"Merged","mergedAt":"2022-09-28T21:25:31Z","number":3145,"mergeCommitSha":"b2833f517d29e3412070d6b4d53da82c0fe0f09a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3145","title":"Disable test","createdAt":"2022-09-28T21:25:25Z"}
{"state":"Merged","mergedAt":"2022-09-28T22:00:25Z","number":3146,"body":"In the case the user has auth'd but the team has not auth's and the user does not have vscode, we do another invite flow such that we send an invitation to another person without team context.\r\n\r\nThis email will only ask to install unblocked.","mergeCommitSha":"1c93b02d97d9b2ffeca15e919d500d9fb3521f4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3146","title":"Fix invite email","createdAt":"2022-09-28T21:49:13Z"}
{"state":"Merged","mergedAt":"2022-09-28T22:27:09Z","number":3147,"mergeCommitSha":"607c0e59c0b0b1c7b3a12e76407363d2604fd658","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3147","title":"goodbye aws pinpoint, mahdi hates you","createdAt":"2022-09-28T22:09:29Z"}
{"state":"Merged","mergedAt":"2022-09-28T22:47:00Z","number":3148,"mergeCommitSha":"d59fa7556977f23bdae7496a571c46b434b1452f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3148","title":"cleanup db logs","createdAt":"2022-09-28T22:35:01Z"}
{"state":"Merged","mergedAt":"2022-09-29T04:19:02Z","number":3149,"mergeCommitSha":"1947e7edaa6577e56e3355fe91b968c14b3092d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3149","title":"Add inviteToChannel() to VideoChannelService","createdAt":"2022-09-28T23:36:20Z"}
{"state":"Merged","mergedAt":"2022-02-22T23:23:12Z","number":315,"body":"Super rough first pass at creating the required database entries for PR ingestion.","mergeCommitSha":"773287500d85e7a14672d94f2f880a83e32a7aeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/315","title":"Store ingested pull request review threads in the db","createdAt":"2022-02-10T23:03:38Z"}
{"state":"Merged","mergedAt":"2022-09-29T00:10:29Z","number":3150,"body":"Potentially fixes issue where VSCode contexts become out of sync with auth state. This manifests with incorrect sidebar states.\r\n<img width=\"678\" alt=\"CleanShot 2022-09-28 at 16 45 17@2x\" src=\"https://user-images.githubusercontent.com/1553313/192908961-fbed0de2-7b4c-40b9-8bc9-fa3b4e828dbd.png\">\r\n\r\n\r\nAs of right now, HubAuth stream will send an event whenever its auth state changes (minute or so.) This may trigger VSCode's setupAuth which will reinitialize install & auth context to false. \r\n\r\nFix is to move the initialization of install & auth context outside of setupAuth.","mergeCommitSha":"d4f445ed381600541b78ead0eb43df9c88d2b4dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3150","title":"Update context init","createdAt":"2022-09-29T00:01:12Z"}
{"state":"Merged","mergedAt":"2022-09-29T00:36:27Z","number":3151,"mergeCommitSha":"f01b91c999d855b5ca664f1aa12a1780e0392f22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3151","title":"Show client web extension versions in admin web","createdAt":"2022-09-29T00:16:30Z"}
{"state":"Merged","mergedAt":"2022-09-29T21:56:34Z","number":3152,"body":"Needed for optimized reads of channel activity for poller and `videoChannels/mine`","mergeCommitSha":"dd16e37e56cd1357ea621c7f2016a3ae8ac16d5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3152","title":"Update VideoChannel modifiedAt on participant updates","createdAt":"2022-09-29T19:14:52Z"}
{"state":"Merged","mergedAt":"2022-09-29T19:49:03Z","number":3153,"body":"- handle files with tabs, spaces, newlines, quotes\n- handle empty diffstat for binary files","mergeCommitSha":"735426d0cd1b55a50ece0f58b530eae12a4a9078","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3153","title":"Fix bug in DiffStat parser","createdAt":"2022-09-29T19:32:45Z"}
{"state":"Merged","mergedAt":"2022-09-30T22:34:55Z","number":3154,"body":"Setup basic grpc communication between vscode + video app.\r\nFeature flagged. \r\n\r\nhttps://user-images.githubusercontent.com/1553313/*********-ea136bd3-f31d-4634-8595-8c3dc811a9e1.mp4\r\n\r\n","mergeCommitSha":"7caae72a291d63c56e6eafaf4fb9b3aa2967f812","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3154","title":"Initial VSCode + Video app setup","createdAt":"2022-09-29T19:36:46Z"}
{"state":"Merged","mergedAt":"2022-09-29T20:49:45Z","number":3155,"mergeCommitSha":"991d4f63de8bebf47b1196657db60059270ceb69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3155","title":"Account for SM usage more accurately","createdAt":"2022-09-29T20:39:23Z"}
{"state":"Merged","mergedAt":"2022-10-03T18:36:46Z","number":3156,"body":"Refactor how we load per-file sourcemarks and associated files.\r\n\r\nPreviously, the loading logic was built into `TextEditorSourceMarks` -- that class managed SM/thread loading, rendering, and editor states.  WIth his PR:\r\n* `FileSourceMarkStreams`: given a file path, vends a stream that returns resolved sourcemark and thread data.  Under the hood this resolves the sourcemarks, loads the threads, and joins the data.  The class tracks created streams, so there is only one outstanding active stream per file (ie, when multiple UIs stream data for the same file, it is reused)\r\n* `TextEditorSourceMarks` uses this stream, and is now much simpler: its purpose is now, given a stream of SMs/threads for a file, render those SMs in the editor gutters.\r\n* `CurrentFileInsightsStream`: Builds on top of `FileSourceMarkStreams` -- for the currently-active file, returns the threads relevant to that file.  The variety of UIs that display \"insights for current file\" now use this stream.\r\n\r\nI am working on unit tests for this and will commit that in a subsequent PR","mergeCommitSha":"8e522b184742a435c95dc41000a5cabf0bcd5dd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3156","title":"Refactor sourcemark/thread loading","createdAt":"2022-09-29T20:52:17Z"}
{"state":"Closed","mergedAt":null,"number":3157,"body":"Update UserIcon to take in popover in preparation for Video work.\r\n<img width=\"519\" alt=\"CleanShot 2022-09-29 at 13 50 24@2x\" src=\"https://user-images.githubusercontent.com/1553313/193139360-6a17d60a-beb4-480a-9f51-0e5a60e816fd.png\">\r\n","mergeCommitSha":"de5416c50eca9002963e874338f064ec22a0de6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3157","title":"UserIcon Popover","createdAt":"2022-09-29T20:59:39Z"}
{"state":"Merged","mergedAt":"2022-09-30T15:46:05Z","number":3158,"mergeCommitSha":"90e6363def8bfdd43da920f4d5fe757d84db1b41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3158","title":"Implements videochannels/mine","createdAt":"2022-09-29T21:45:51Z"}
{"state":"Merged","mergedAt":"2022-09-29T21:55:28Z","number":3159,"mergeCommitSha":"535b5cc772f2d8c755df9277846547db2578b836","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3159","title":"Added debug message to figure out spawn EBADF issues","createdAt":"2022-09-29T21:54:37Z"}
{"state":"Merged","mergedAt":"2022-02-11T02:16:31Z","number":316,"body":"## Problem\r\nNeeded for Push and response caching. \r\n\r\n## Summary\r\nAdded a store procedure to update modifiedAt timestamps, and then we register a trigger for each table to fire at the proc whenever UPDATE is called\r\n\r\nTrigger operation syntax and examples here: https://www.postgresql.org/docs/current/plpgsql-trigger.html","mergeCommitSha":"f6412735297e07b7932b153776f3635baef37b24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/316","title":"Add modifiedAt field with update triggers","createdAt":"2022-02-10T23:12:55Z"}
{"state":"Merged","mergedAt":"2022-09-30T16:17:47Z","number":3160,"body":"Like it says on the tin","mergeCommitSha":"800637e1197fb6bae87f815eb2298dda34562a89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3160","title":"Push for video channel changes","createdAt":"2022-09-29T22:45:10Z"}
{"state":"Merged","mergedAt":"2022-09-30T01:41:29Z","number":3161,"body":"The ktlint-gradle guys are way behind in ktlint support (and they are refusing to upgrade), so I just forked and fixed it up here:\r\nhttps://github.com/NextChapterSoftware/ktlint-gradle\r\n\r\nWe now can move to latest ktlint which has a whole bunch of new useful rules.","mergeCommitSha":"fd40491e4932051e57c66f9f3230a5ab1af9d88c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3161","title":"move to latest ktlint","createdAt":"2022-09-30T01:19:36Z"}
{"state":"Merged","mergedAt":"2022-10-03T22:12:23Z","number":3162,"body":"Issue where unexpected 401s can occur if there's a large gap between a refresh and its API request within `tokenRefreshMiddleware`\r\n\r\nWe will now retry the refresh process if we get a 401 and the current status is unauthenticated.\r\n\r\nRequired some type hackery with node-fetch to allow for updating the stream buffer high water mark.","mergeCommitSha":"7d42a4ef928fc7b6d252fa71fc4e7ae3139071b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3162","title":"Setup Retries","createdAt":"2022-09-30T03:18:03Z"}
{"state":"Merged","mergedAt":"2022-09-30T16:27:47Z","number":3163,"body":"- Addresses correctess issue at merge commits, where changes to the file were completely\n  omitted along some incident branches. We were supposed to get the diff between the\n  non-treesame commits for that file; but we incorrectly got the diff between non-treesame\n  commits for _any_ file which rarely worked correctly.\n\n- Should significantly improve performance of diffs calculated for edges incident on merge\n  commits in the case where the sourcemark was refactored between files. We were supposed to\n  compute the other files diffs for changes in the same commit that the sourcemark file was\n  changed; but we incorrectly computed the other files diffs for all changes from the changed\n  commit to the merge commit, which could contain 100s of commits and 1000s of files.","mergeCommitSha":"7a38f7d8d4ee98c1eada744a73ffb0dfc17d6797","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3163","title":"Merge commit correctness and perf fixes","createdAt":"2022-09-30T06:20:50Z"}
{"state":"Merged","mergedAt":"2022-09-30T16:28:14Z","number":3164,"body":"More of a brute force approach, but will always be accurate because\nit uses exactly the same API used to fetch sourcemarks for a file.","mergeCommitSha":"ee96acdf2e9111c9594e6c6feec35580360d1dc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3164","title":"New approach to getting top-N sourcemarks per file","createdAt":"2022-09-30T07:04:37Z"}
{"state":"Merged","mergedAt":"2022-10-01T01:20:18Z","number":3165,"body":"fixes: https://linear.app/unblocked/issue/UNB-655/sm-engine-needs-better-handling-for-very-large-diffs\r\n\r\nfixes: https://linear.app/unblocked/issue/UNB-678/error-diffparser-could-not-parse-line-diff-cc-appsstandalone","mergeCommitSha":"d4ff236c79b20a7965ea667a9144b170bdc19586","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3165","title":"SM engine handles very large diffs better","createdAt":"2022-09-30T07:08:58Z"}
{"state":"Merged","mergedAt":"2022-09-30T16:32:53Z","number":3166,"body":"We no longer have to check for participant updates because we're updating the channel `modifiedAt` whenever there are participant table changes","mergeCommitSha":"fbb1e9fa5ffc698ac4b7b41180628321be17ebaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3166","title":"Simplify single video channel push query","createdAt":"2022-09-30T16:27:53Z"}
{"state":"Merged","mergedAt":"2022-10-03T23:27:15Z","number":3167,"body":"Note that this is a hard delete of the thread since it will no longer exist in GitHub","mergeCommitSha":"9136716b8753aa2e1c15556ebb91c57671d589bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3167","title":"Delete thread if entire PR thread is deleted in GitHub","createdAt":"2022-09-30T21:12:22Z"}
{"state":"Merged","mergedAt":"2022-09-30T22:31:10Z","number":3168,"body":"1. Only send threads back to the stream once we have data for *all* requested threads.  Before we would return partial cached results.\r\n2. Always send the full set of threads back to the stream.  Before, we would only send the updated threads\r\n3. Handle the empty ready state correctly (when some code requests no threads)","mergeCommitSha":"6db5b039a55f1040c569ddd7b0f248e71ed243a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3168","title":"Fix some ThreadStore bugs","createdAt":"2022-09-30T22:20:23Z"}
{"state":"Merged","mergedAt":"2022-09-30T22:46:16Z","number":3169,"mergeCommitSha":"198f924487784e64babffbfd18d09ff1cdccdbdf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3169","title":"Allow only a single window for chat app, and close app when window is closed","createdAt":"2022-09-30T22:39:18Z"}
{"state":"Merged","mergedAt":"2022-02-11T00:13:23Z","number":317,"mergeCommitSha":"f231e7ed34be094dc078f10db1e988dab01ed28a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/317","title":"Make docker use the same postgres version as our AWS instance","createdAt":"2022-02-11T00:11:11Z"}
{"state":"Merged","mergedAt":"2022-10-03T18:36:29Z","number":3170,"body":"...and restore if the PR is reopened.","mergeCommitSha":"19cbc47d6854657bb01ff842db0cda1ae19b6b2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3170","title":"Archive closed PR threads","createdAt":"2022-09-30T23:47:10Z"}
{"state":"Merged","mergedAt":"2022-10-01T02:39:03Z","number":3171,"body":"Reverts NextChapterSoftware/unblocked#3165\r\n\r\nBad results in terms of mem/runtime. Back to drawing board...","mergeCommitSha":"8f5798456fe4cf2ed8bc1573464bad09a28f94cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3171","title":"Revert \"SM engine handles very large diffs better\"","createdAt":"2022-10-01T02:38:56Z"}
{"state":"Merged","mergedAt":"2022-10-03T18:08:33Z","number":3172,"body":"- prefetch as soon as sourcemark download completes\n- just run on 3 file","mergeCommitSha":"6662a3819b5b93686305f06487ce25b55e1ed7ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3172","title":"Further optimize getTopFiles","createdAt":"2022-10-03T17:58:48Z"}
{"state":"Merged","mergedAt":"2022-10-03T19:10:49Z","number":3173,"mergeCommitSha":"59c5f51c78cbe26db508263f00adaab75bf3e754","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3173","title":"Add basic team information","createdAt":"2022-10-03T18:46:56Z"}
{"state":"Merged","mergedAt":"2022-10-03T20:08:33Z","number":3174,"body":"## Problem\r\n\r\nSometimes we encounter very large diffs caused by a developer merging a long lived side branch,\r\nrunning prettier across and entire code base, checking in generated code, or checking in large\r\nfiles for whatever reason.\r\n\r\n## Solution\r\n\r\nApply a bunch of heuristics, almost all of which sacrifice correctness for performance.\r\n\r\n1. Ignore generated files\r\n2. Assume that files can only be moved to other files matching the original file extension\r\n3. Exclude deleted files from the diff, since a mark cannot be moved to a deleted file.\r\n4. Exclude large file diffs completely if the diff is larger than 1000 lines.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-655/sm-engine-needs-better-handling-for-very-large-diffs","mergeCommitSha":"05b417438bb5e3464743332fbc33b65b5d66b282","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3174","title":"SM engine handles very large diffs better","createdAt":"2022-10-03T19:26:30Z"}
{"state":"Merged","mergedAt":"2022-10-03T20:08:19Z","number":3175,"body":"Prefetch was doing nothing useful. Now it's doing what it's supposed to.\r\n\r\n```\r\n{\r\n  context: 'SourceMarkCalculator',\r\n  service: 'vscode',\r\n  environment: 'local',\r\n  type: 'nodejs',\r\n  process: 'extension',\r\n  teamId: '9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361',\r\n  repoId: '7fce6ebb-5989-48e7-9af0-fff594769a8a',\r\n  repoRootPath: '/Users/<USER>/work/NextChapterSoftware/unblocked',\r\n  pid: 33014,\r\n  file: FilePath { value: 'api/private.yml' },\r\n  level: 'error',\r\n  message: 'getFileRelativeToRepo: expected file to be absolute',\r\n  timestamp: '2022-10-03T19:22:47.968Z'\r\n}\r\n```","mergeCommitSha":"1f81d32995a5597bd9a3cf88a132a52b24ce5cc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3175","title":"Fix bug in getTopFiles prefetch","createdAt":"2022-10-03T19:28:01Z"}
{"state":"Closed","mergedAt":null,"number":3176,"body":"This doesn't get us all the way there on the experience we're after, but these changes are a necessary starting point to set up the parent/child permissions relationship with the Hub","mergeCommitSha":"640118f59e8dd5fffbb491c8c008d55cfa03f3c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3176","title":"Updates the Video App sandbox relationship with the Hub","createdAt":"2022-10-03T19:59:50Z"}
{"state":"Closed","mergedAt":null,"number":3177,"mergeCommitSha":"e6e45285c72e930a32979c54fa23a5332608195c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3177","title":"Clean up function","createdAt":"2022-10-03T20:22:19Z"}
{"state":"Merged","mergedAt":"2022-10-03T21:02:42Z","number":3178,"mergeCommitSha":"d3e06fd40f36ee49528709b78700991176cbb3d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3178","title":"Fix getTopFiles prefetch again","createdAt":"2022-10-03T20:54:47Z"}
{"state":"Merged","mergedAt":"2022-10-04T03:58:15Z","number":3179,"mergeCommitSha":"b5d5dbe009fdf001d59805ba68e197b8f456118b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3179","title":"Add logging to debug why thread title isnt updating with message deletion","createdAt":"2022-10-04T00:13:59Z"}
{"state":"Merged","mergedAt":"2022-02-11T22:08:57Z","number":318,"body":"## Problem\r\nTo facilitate our fast-polling and caching strategy, we need a simple interface for clients to ask whether a particular entity or set of entities has been modified since a particular date. \r\n\r\n## Proposal\r\nAfter speaking with Matt, we concluded that this would be most easily implemented as a POST request with a request body. OpenAPI does support header definitions but the support is not great for kotlin server so we would have to write our own templates etc. \r\n\r\nRequest:\r\n```\r\n{\r\n    \"channels\": [\r\n        {\r\n            \"channel\": \"/threads\",\r\n            \"ifModifiedSince\": \"2022-02-03T06:45:48Z\"\r\n        }\r\n    ]\r\n}\r\n```\r\n\r\nResponse:\r\n```\r\n{\r\n    \"channels\":[ \"/threads\" ]\r\n}\r\n```\r\n            \r\n\r\n\r\n## Thoughts for the future\r\nIf we want true http response caching, we probably do need to implement support for OpenAPI header params","mergeCommitSha":"0179475de3b3ce202bba38f84f561984b8626560","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/318","title":"Propose modified since status API","createdAt":"2022-02-11T03:08:10Z"}
{"state":"Merged","mergedAt":"2022-10-04T02:33:14Z","number":3180,"body":"- Slack team ingestion\r\n- Add new enum\r\n","mergeCommitSha":"9025c4e51359d66a4308b4f6c9c60768f64d655f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3180","title":"[BREAKS API ON MAIN] AddSlackTeamIngestion2","createdAt":"2022-10-04T02:06:43Z"}
{"state":"Merged","mergedAt":"2022-10-04T06:50:03Z","number":3181,"mergeCommitSha":"deb54440f8904310b28e1b0f1349010e97a5e936","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3181","title":"Pass thread ID","createdAt":"2022-10-04T06:12:24Z"}
{"state":"Merged","mergedAt":"2022-10-04T07:05:59Z","number":3182,"body":"So confused why this isn't getting caught in CI","mergeCommitSha":"29f246317d1ff765e2805900313f4a705b189636","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3182","title":"Remove unused import","createdAt":"2022-10-04T07:05:42Z"}
{"state":"Merged","mergedAt":"2022-10-04T18:33:37Z","number":3183,"body":"- Sourcemark engine excludes file types by extension, fixing #3174.\r\n- Also skip pre-computed nodes","mergeCommitSha":"1deb501c04c2dd7f52fa8bdd87c67a571a11e4a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3183","title":"Sourcemark engine excludes file types by extension","createdAt":"2022-10-04T17:29:54Z"}
{"state":"Merged","mergedAt":"2022-10-12T00:31:37Z","number":3184,"body":"### Main changes\r\n\r\n- Leverage `git diff -G` to search for tokens from the original snippet when there\r\n  are a large number of candidate files to search. Git is several orders of\r\n  magnitude faster at searching for text than node application is.\r\n\r\n### Other changes\r\n\r\n- When searching for inserts across files, skip parsing _lines removed_ from the\r\n  diff since we never inspect these hunks.\r\n- Upstream newly computed sourcepoints on the fly, but ensure that we have a\r\n  significant batch to amortize the network overhead.\r\n- Full recalculation loop iterates by file, instead of by chunks-of-files. This\r\n  was leading to memory spikes.\r\n- Full recalculation no longer has a CPU throttle, because it was pretty useless.","mergeCommitSha":"6fefccdbf544096fd7e00c25a24563c8f6e7a076","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3184","title":"More efficient sourcemark token searching across files","createdAt":"2022-10-04T17:32:46Z"}
{"state":"Merged","mergedAt":"2022-10-04T18:33:00Z","number":3185,"body":"```\r\nhyperfine --warmup 3 'git rev-list -1 --objects HEAD -- README.md' 'git rev-parse HEAD:README.md'                                                                  5.6s\r\n```\r\n\r\n### Benchmark 1: git rev-list -1 --objects HEAD -- README.md\r\n  Time (mean ± σ):       7.0 ms ±   0.5 ms    [User: 5.3 ms, System: 1.2 ms]\r\n  Range (min … max):     6.4 ms …   9.7 ms    325 runs\r\n\r\n### Benchmark 2: git rev-parse HEAD:README.md\r\n  Time (mean ± σ):       2.4 ms ±   0.3 ms    [User: 1.4 ms, System: 0.7 ms]\r\n  Range (min … max):     2.0 ms …   4.0 ms    688 runs\r\n\r\n### Summary\r\n  'git rev-parse HEAD:README.md' ran\r\n    2.91 ± 0.38 times faster than 'git rev-list -1 --objects HEAD -- README.md'","mergeCommitSha":"be70cdc073c4b6865790558edbc850b0abc1eb80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3185","title":"Found a faster Git native way to compute content hashes","createdAt":"2022-10-04T17:59:09Z"}
{"state":"Merged","mergedAt":"2022-10-05T21:29:42Z","number":3186,"body":"Current plan is to reuse existing models as much as possible.\r\n\r\nA walkthrough is just another type of thread.\r\n<img width=\"714\" alt=\"CleanShot 2022-10-04 at 11 40 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/193899659-8b8a5fc4-f656-44cb-8948-440949a2d0ed.png\">\r\nThe main difference from a the view standpoint is a new VideoBlock within MessageContent. \r\n\r\nCreating a walkthrough from VSCode can use the existing `CreateThreadRequest` models\r\n<img width=\"752\" alt=\"CleanShot 2022-10-04 at 11 41 49@2x\" src=\"https://user-images.githubusercontent.com/1553313/193900212-d6031a60-e281-4dd7-af70-4300ea647bd0.png\">\r\n\r\nVideo will be uploaded and attached to MessageContent, similar to existing image assets.\r\nRelatedFiles / Anchor points maps to `CreateMessageRequest.sourcemarks`. Each file will be its own sourcemark. We will have n source marks -> 1 thread.\r\n\r\nTeamMembers = `CreateThreadRequest.threadParticipants`\r\n","mergeCommitSha":"993bb640dd64350f2203be9e35b8dd6cd3a73e55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3186","title":"[BREAKS API ON MAIN] Updated spec for VSCode Walkthrough","createdAt":"2022-10-04T18:44:32Z"}
{"state":"Merged","mergedAt":"2022-10-04T19:28:17Z","number":3187,"body":"No longer just scm providers...","mergeCommitSha":"1ef16274e9bd5cada60d86761888dd0f6cafde84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3187","title":"Minor provider changes","createdAt":"2022-10-04T19:14:38Z"}
{"state":"Merged","mergedAt":"2022-10-11T18:25:55Z","number":3188,"mergeCommitSha":"c376e83663529fb6cb154d11485525f55928e99c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3188","title":"FileSourceMarkStream tests","createdAt":"2022-10-04T19:44:30Z"}
{"state":"Merged","mergedAt":"2022-10-04T20:23:30Z","number":3189,"mergeCommitSha":"ab015200e31ef2ee3b3eecf623da0fc9cacff9ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3189","title":"Rename scmproviders","createdAt":"2022-10-04T20:10:52Z"}
{"state":"Merged","mergedAt":"2022-02-14T18:44:04Z","number":319,"body":"Update VSCode environments to support local & deployed dev environments.\r\n\r\n","mergeCommitSha":"cc8d46d1e4586dd70d8402a85e942b9256a61e58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/319","title":"Update VScode environments","createdAt":"2022-02-11T06:01:34Z"}
{"state":"Merged","mergedAt":"2022-10-04T21:45:47Z","number":3190,"body":"See the 'Pull Requests Discussions' section below:\r\n\r\n<img width=\"348\" alt=\"Screen Shot 2022-10-04 at 2 04 26 PM\" src=\"https://user-images.githubusercontent.com/2133518/193928309-cb0b3602-5d86-4eef-b5b9-d16eb33c57a9.png\">\r\n\r\nWe need a way to differentiate PRs that have slack threads from PRs that do not, so we can render a different icon.  I'm not sure if this is the best way to model this or not.","mergeCommitSha":"017f68f9bdc383e02bf902de0492fd57f8bfa1cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3190","title":"PullRequest API model indicates if it has slack threads or not","createdAt":"2022-10-04T21:05:41Z"}
{"state":"Merged","mergedAt":"2022-10-04T22:53:06Z","number":3191,"mergeCommitSha":"dd9f2c01ee6cb26de7f6a48627bb9e4ca0fa9d73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3191","title":"Add slack provider","createdAt":"2022-10-04T22:10:40Z"}
{"state":"Merged","mergedAt":"2022-10-04T22:52:41Z","number":3192,"mergeCommitSha":"8e7958beec68ba42e77c80db891a6a63126480c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3192","title":"Return hasSlackThreads in getPullRequestsForCommits operation","createdAt":"2022-10-04T22:29:31Z"}
{"state":"Merged","mergedAt":"2022-10-06T23:52:53Z","number":3193,"body":"Spec for communication between VSCode + Video Walkthrough App.","mergeCommitSha":"ccdc559c9ee9609a54e9fff540abe8ae22178a02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3193","title":"Video Walkthrough Proto","createdAt":"2022-10-04T22:58:42Z"}
{"state":"Merged","mergedAt":"2022-10-05T20:18:14Z","number":3194,"body":"The regex probably needs some work","mergeCommitSha":"09426a8455f09c498f24bd54056124a428f9bd6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3194","title":"Create SlackURL data class to extract slack urls from PR descriptions and comments","createdAt":"2022-10-05T00:19:18Z"}
{"state":"Merged","mergedAt":"2022-10-06T21:24:13Z","number":3195,"body":"Creates a new app that is a member of the shared container. There's some UI code for buttons I'm slipping in here too but it's pretty straightforward. \r\n\r\nIPC service is bootstrapped and (currently) uses the same default location and key for the IPC port as the VideoChatApp. \r\n\r\nFor launch purposes, the bundleId of this app is `com.nextchaptersoftware.UnblockedWalkthroughApp`","mergeCommitSha":"fcd55460360554d20a43088f6ef5e8846a83341a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3195","title":"Create a new walkthrough app","createdAt":"2022-10-05T04:24:45Z"}
{"state":"Merged","mergedAt":"2022-10-05T07:43:23Z","number":3196,"body":"Too many performance, memory and correctness issues with merge commits.\nInstead we linearize the commit traversal.\n\nLimitation is that only one of the paths inbound to a merge commit will be processed,\nmeaning that sourcemarks may not be visible in the web extension for a file at a\ncommit within the non-processed path. Probably not a big deal given that most file\nbrowsing occurs on the main line and at or near HEAD.","mergeCommitSha":"ba31d6b99f39c4433c11809595a07d1514f92d48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3196","title":"Linearize commit propagation","createdAt":"2022-10-05T07:40:06Z"}
{"state":"Merged","mergedAt":"2022-10-05T17:55:13Z","number":3197,"mergeCommitSha":"df46ae81f41b44370e158a329d3e4bbb543e1fd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3197","title":"Split into distinct slack apps for dev and prod","createdAt":"2022-10-05T17:44:41Z"}
{"state":"Merged","mergedAt":"2022-10-05T17:57:31Z","number":3198,"body":"When a refresh token is expired, `sharedSetupAuth` or `sharedRefreshAuth` will throw an error.\r\n\r\nThis *should* be caught and trigger a logout but since we were not waiting for it, logout was never triggered...","mergeCommitSha":"247c89034134233b69f4c7ab67592b7c932c7927","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3198","title":"Await auth check","createdAt":"2022-10-05T17:47:57Z"}
{"state":"Merged","mergedAt":"2022-10-05T18:06:01Z","number":3199,"body":"https://linear.app/unblocked/issue/UNB-437/source-mark-fails-to-track-moved-files\n\nAlso relax the text similarity difference within a file.","mergeCommitSha":"420ac871f7b57cf2a6e2b93add62fb3b73f719b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3199","title":"Source mark fails to track moved file move at boundary","createdAt":"2022-10-05T18:02:45Z"}
{"state":"Closed","mergedAt":null,"number":32,"mergeCommitSha":"7ebd26236241822771908749450dffa2336ed515","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/32","title":"Capitalize util files","createdAt":"2022-01-13T00:01:25Z"}
{"state":"Merged","mergedAt":"2022-02-15T21:37:23Z","number":320,"body":"Setup a basic version of GH Install starting from VSCode (Required to provide repo url)\r\nCurrently some caveats due to limited API. Will revisit when API is updated or onboarding is more defined.\r\n\r\nIntroduced VSCode Git interface as part of GH Install work. Will require some UI and thought around the experience to determine correct repositories and remotes.\r\n\r\nAlso introduced useAsyncOperation hook with result type.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/153631949-49dbfadc-b24a-4244-a218-027d80a51036.mp4\r\n\r\n\r\n\r\n","mergeCommitSha":"da8d6a2c19c8490b5d978068e6ad7763a4e1150c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/320","title":"Setup GH Install","createdAt":"2022-02-11T16:41:21Z"}