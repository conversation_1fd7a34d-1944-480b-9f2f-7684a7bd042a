commit a9e070290ca2fdde86cbd631d7ff63664dc68f83
Author: <PERSON> <<EMAIL>>
Date:   Wed Jun 14 16:36:40 2023 -0700

    Add documents to example admin page (#6581)

    * Add documents to example admin page

    * Add date and score to template objects

    * Add templated fields to compiler

diff --git a/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/Prompt.kt b/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/Prompt.kt
index b2d0527c5..5c6a3f0a1 100644
--- a/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/Prompt.kt
+++ b/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/Prompt.kt
@@ -35,6 +35,8 @@ data class TemplatedDocument(
     val source: String,
     val sourceId: String,
     val content: String,
+    val score: TemplatedField<Float>? = null,
+    val date: TemplatedField<String>? = null,
 ) : TemplatedPromptSection

 data class TemplatedPrefix(
@@ -45,6 +47,15 @@ data class TemplatedSuffix(
     override val template: String,
 ) : TemplatedPromptSection

+data class TemplatedField<T>(
+    val value: T,
+    val label: String? = null,
+) {
+    override fun toString(): String {
+        return value.toString()
+    }
+}
+
 data class ConstrainedTemplateSet<T>(
     val items: List<T>,
 )
diff --git a/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptSection.kt b/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptSection.kt
index 9f84b5d1b..1731853df 100644
--- a/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptSection.kt
+++ b/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptSection.kt
@@ -48,6 +48,7 @@ interface TemplatedPromptSection {
                 val compiledProp = compileProperty(
                     prop = prop,
                     template = result,
+                    fieldName = fieldName,
                     replacementFieldName = replacementFieldName,
                     availableForConstrained = localAvailableForConstrained,
                     skipConstrained = skipConstrained,
@@ -64,6 +65,7 @@ interface TemplatedPromptSection {
     private fun compileProperty(
         prop: Any?,
         template: String,
+        fieldName: String,
         replacementFieldName: String,
         availableForConstrained: Int,
         skipConstrained: Boolean,
@@ -100,6 +102,14 @@ interface TemplatedPromptSection {
                 )
             }

+            is TemplatedField<*> -> {
+                val label = prop.label ?: fieldName.replaceFirstChar { it.uppercaseChar() }
+                val replacementText = "$label: ${prop.value?.toString() ?: ""}"
+                val text = template.replace(replacementFieldName, replacementText)
+                val size = replacementText.length - replacementFieldSize
+                CompilationResult(text, size, 0)
+            }
+
             else -> {
                 val replacementText = prop?.toString() ?: ""
                 val text = template.replace(replacementFieldName, replacementText)
@@ -132,6 +142,7 @@ interface TemplatedPromptSection {
     ): CompilationResult {
         val dummyTemplate = "{dummy}"
         val dummyReplacement = "{dummy}"
+        val dummyFieldName = "dummy"
         val replacementFieldSize = replacementFieldName.length
         var localAvailableForConstrained = availableForConstrained

@@ -139,6 +150,7 @@ interface TemplatedPromptSection {
             compileProperty(
                 prop = it,
                 template = dummyTemplate,
+                fieldName = dummyFieldName,
                 replacementFieldName = dummyReplacement,
                 availableForConstrained = localAvailableForConstrained,
                 skipConstrained = skipConstrained,
@@ -165,6 +177,7 @@ interface TemplatedPromptSection {
             false -> {
                 val dummyTemplate = "{dummy}"
                 val dummyReplacement = "{dummy}"
+                val dummyFieldName = "dummy"
                 val replacementFieldSize = replacementFieldName.length
                 var localAvailableForConstrained = availableForConstrained

@@ -172,6 +185,7 @@ interface TemplatedPromptSection {
                     compileProperty(
                         prop = it,
                         template = dummyTemplate,
+                        fieldName = dummyFieldName,
                         replacementFieldName = dummyReplacement,
                         availableForConstrained = localAvailableForConstrained,
                         skipConstrained = false,
diff --git a/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt b/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt
index fe5d4516b..1d121bfde 100644
--- a/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt
+++ b/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt
@@ -6,6 +6,7 @@ import com.nextchaptersoftware.db.models.MLTypedDocument
 import com.nextchaptersoftware.ml.input.prompt.ConstrainedTemplateSet
 import com.nextchaptersoftware.ml.input.prompt.TemplatedDocument
 import com.nextchaptersoftware.ml.input.prompt.TemplatedDocumentSection
+import com.nextchaptersoftware.ml.input.prompt.TemplatedField
 import com.nextchaptersoftware.ml.input.prompt.TemplatedInferenceExample
 import com.nextchaptersoftware.ml.input.prompt.TemplatedInferencePrompt
 import com.nextchaptersoftware.ml.input.prompt.TemplatedPrefix
@@ -41,6 +42,12 @@ class PromptCompilerService {
                             source = it.source,
                             sourceId = it.sourceId.toString(),
                             content = it.content,
+                            score = it.score?.let { score ->
+                                TemplatedField(score)
+                            },
+                            date = it.date?.let { date ->
+                                TemplatedField(date.toString())
+                            },
                         )
                     },
                 ),
diff --git a/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptTest.kt b/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptTest.kt
index 73035e7c3..bb5c90fd6 100644
--- a/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptTest.kt
+++ b/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/input/prompt/TemplatedPromptTest.kt
@@ -86,11 +86,14 @@ class TemplatedPromptTest {
                                     {source}
                                     --- Content ---
                                     {content}
+                                    {score}
+                                    {date}
                                 """.trimIndent(),
                                 sourceType = "SourceCode",
                                 sourceId = "00000000-0000-0000-0000-000000000000",
                                 source = "src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt",
                                 content = "Test Source Code",
+                                score = TemplatedField(0.1f, "CustomScoreLabel"),
                             ),
                             TemplatedDocument(
                                 template = """
@@ -100,11 +103,15 @@ class TemplatedPromptTest {
                                     {source}
                                     --- Content ---
                                     {content}
+                                    {score}
+                                    {date}
                                 """.trimIndent(),
                                 sourceType = "SourceCode",
                                 sourceId = "00000000-0000-0000-0000-000000000000",
                                 source = "src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt",
                                 content = "Source Code",
+                                score = TemplatedField(0.1f),
+                                date = TemplatedField("2021-01-01T00:00:00.000Z"),
                             ),
                         ),
                     ),
@@ -153,7 +160,7 @@ class TemplatedPromptTest {
         assertThat(unbounded.text).isEqualTo(
             """
               This is a template
-
+
               Here is a prefix:
               I am a prefix

@@ -180,6 +187,8 @@ class TemplatedPromptTest {
               src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt
               --- Content ---
               Test Source Code
+              CustomScoreLabel: 0.1
+

               ### SourceCode Document
               Source ID: 00000000-0000-0000-0000-000000000000
@@ -187,6 +196,8 @@ class TemplatedPromptTest {
               src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt
               --- Content ---
               Source Code
+              Score: 0.1
+              Date: 2021-01-01T00:00:00.000Z
               ###

               ### The following documents originate from: Jira
diff --git a/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt b/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt
index dce02c7ca..9217e474a 100644
--- a/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt
+++ b/projects/libs/lib-ml/src/test/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt
@@ -13,6 +13,7 @@ import com.nextchaptersoftware.db.utils.DatabaseTestsBase
 import com.nextchaptersoftware.ml.services.template.MLInferenceTemplateService
 import com.nextchaptersoftware.types.Hash
 import java.util.UUID
+import kotlinx.datetime.Instant
 import org.assertj.core.api.Assertions.assertThat
 import org.junit.jupiter.api.Test
 import org.mockito.Mockito.mock
@@ -51,6 +52,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
                         {source}
                         --- Content ---
                         {content}
+                        {score}
+                        {date}
                         ###
             """.trimIndent(),
             promptTemplate = """
@@ -127,24 +130,30 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
                 sourceId = UUID.fromString("00000000-0000-0000-0000-000000000000"),
                 source = "src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt",
                 content = "Source Code",
+                score = 0.1f,
+                date = Instant.parse("2021-01-01T00:00:00.000Z"),
             ),
             MLTypedDocument(
                 sourceType = InsightType.SourceCode,
                 sourceId = UUID.fromString("00000000-0000-0000-0000-000000000000"),
                 source = "src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt",
                 content = "Test Source Code",
+                score = 0.1f,
+                date = Instant.parse("2021-01-01T00:00:00.000Z"),
             ),
             MLTypedDocument(
                 sourceType = InsightType.Slack,
                 sourceId = UUID.fromString("00000000-0000-0000-0000-000000000000"),
                 content = "Thread Content",
                 source = "Date: 2021-01-01T00:00:00.000Z",
+                score = 0.2f,
             ),
             MLTypedDocument(
                 sourceType = InsightType.PullRequest,
                 sourceId = UUID.fromString("00000000-0000-0000-0000-000000000000"),
                 content = "Thread Content",
                 source = "Date: 2021-01-01T00:00:00.000Z",
+                date = Instant.parse("2021-01-01T00:00:00.000Z"),
             ),
             MLTypedDocument(
                 sourceType = InsightType.PullRequest,
@@ -181,7 +190,7 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               Sentiment: 1
               Human Feedback: This is a great answer!
               ###
-
+
               ### Example
               Question: What is the number 42?
               Answer: The answer to the meaning of life
@@ -197,6 +206,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerService.kt
               --- Content ---
               Source Code
+              Score: 0.1
+              Date: 2021-01-01T00:00:00Z
               ###

               ### SourceCode Document
@@ -205,6 +216,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               src/main/kotlin/com/nextchaptersoftware/ml/services/prompt/PromptCompilerServiceTest.kt
               --- Content ---
               Test Source Code
+              Score: 0.1
+              Date: 2021-01-01T00:00:00Z
               ###
               ###

@@ -215,6 +228,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               Date: 2021-01-01T00:00:00.000Z
               --- Content ---
               Thread Content
+              Score: 0.2
+
               ###
               ###

@@ -225,6 +240,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               Date: 2021-01-01T00:00:00.000Z
               --- Content ---
               Thread Content
+
+              Date: 2021-01-01T00:00:00Z
               ###

               ### PullRequest Document
@@ -233,6 +250,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               Date: 2021-01-01T00:00:00.000Z
               --- Content ---
               Thread Content
+
+
               ###
               ###

@@ -243,6 +262,8 @@ class PromptCompilerServiceTest : DatabaseTestsBase() {
               Date: 2021-01-01T00:00:00.000Z
               --- Content ---
               Thread Content
+
+
               ###
               ###

diff --git a/projects/libs/lib-search-semantic/src/main/kotlin/com/nextchaptersoftware/search/semantic/services/SemanticSearchQueryService.kt b/projects/libs/lib-search-semantic/src/main/kotlin/com/nextchaptersoftware/search/semantic/services/SemanticSearchQueryService.kt
index 37b15e35a..acc93762b 100644
--- a/projects/libs/lib-search-semantic/src/main/kotlin/com/nextchaptersoftware/search/semantic/services/SemanticSearchQueryService.kt
+++ b/projects/libs/lib-search-semantic/src/main/kotlin/com/nextchaptersoftware/search/semantic/services/SemanticSearchQueryService.kt
@@ -71,6 +71,8 @@ class SemanticSearchQueryService(
                         source = checkNotNull(document.insightIndexContentModel).title,
                         sourceType = checkNotNull(document.insightIndexContentModel).insightType,
                         sourceId = checkNotNull(document.insightIndexContentModel).id,
+                        score = checkNotNull(document.score),
+                        date = checkNotNull(document.insightIndexContentModel).createdAt,
                     )

                     is PullRequestSearchResult -> MLTypedDocument(
@@ -78,6 +80,8 @@ class SemanticSearchQueryService(
                         source = checkNotNull(document.insightIndexContentModel).number.let { "pull request #$it" },
                         sourceType = checkNotNull(document.insightIndexContentModel).insightType,
                         sourceId = checkNotNull(document.insightIndexContentModel).id,
+                        score = checkNotNull(document.score),
+                        date = checkNotNull(document.insightIndexContentModel).createdAt,
                     )

                     is CodeSearchResult -> MLTypedDocument(
@@ -85,6 +89,7 @@ class SemanticSearchQueryService(
                         source = checkNotNull(document.insightIndexContentModel).filePath,
                         sourceType = checkNotNull(document.insightIndexContentModel).insightType,
                         sourceId = checkNotNull(document.insightIndexContentModel).id,
+                        score = checkNotNull(document.score),
                     )
                 }
             }.filterNot { document ->
diff --git a/projects/models/src/main/kotlin/com/nextchaptersoftware/db/models/MLTypedDocument.kt b/projects/models/src/main/kotlin/com/nextchaptersoftware/db/models/MLTypedDocument.kt
index 5ef64ecf9..6a7cea48d 100644
--- a/projects/models/src/main/kotlin/com/nextchaptersoftware/db/models/MLTypedDocument.kt
+++ b/projects/models/src/main/kotlin/com/nextchaptersoftware/db/models/MLTypedDocument.kt
@@ -1,6 +1,7 @@
 package com.nextchaptersoftware.db.models

 import java.util.UUID
+import kotlinx.datetime.Instant
 import kotlinx.serialization.Contextual
 import kotlinx.serialization.Serializable

@@ -20,4 +21,8 @@ data class MLTypedDocument(
     /** The database ID of the source of this document */
     @Contextual
     val sourceId: UUID,
+
+    val score: Float? = null,
+
+    val date: Instant? = null,
 )
diff --git a/projects/models/src/test/kotlin/com/nextchaptersoftware/db/models/MLTypedDocumentTest.kt b/projects/models/src/test/kotlin/com/nextchaptersoftware/db/models/MLTypedDocumentTest.kt
index 7d5b9a3f8..9e36296fa 100644
--- a/projects/models/src/test/kotlin/com/nextchaptersoftware/db/models/MLTypedDocumentTest.kt
+++ b/projects/models/src/test/kotlin/com/nextchaptersoftware/db/models/MLTypedDocumentTest.kt
@@ -15,6 +15,7 @@ class MLTypedDocumentTest {
             source = "UNB-1317",
             sourceType = InsightType.Linear,
             sourceId = UUID.fromString("*************-4c7c-8f32-924bd82cbdea"),
+            score = 0.1f,
         )

         val encoded = original.encode()
@@ -24,7 +25,8 @@ class MLTypedDocumentTest {
                 """"content":"ML: Visualize our embeddings",""",
                 """"source":"UNB-1317",""",
                 """"sourceType":6,""",
-                """"sourceId":"*************-4c7c-8f32-924bd82cbdea"""",
+                """"sourceId":"*************-4c7c-8f32-924bd82cbdea","""",
+                """score":0.1""",
                 "}",
             ).joinToString(separator = ""),
         )
diff --git a/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/component/Badge.kt b/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/component/Badge.kt
index 9294574ad..435229b34 100644
--- a/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/component/Badge.kt
+++ b/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/component/Badge.kt
@@ -4,6 +4,7 @@ import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
 import com.nextchaptersoftware.db.models.AgentType
 import com.nextchaptersoftware.db.models.ClientVersion
 import com.nextchaptersoftware.db.models.IngestionStatus
+import com.nextchaptersoftware.db.models.InsightType
 import com.nextchaptersoftware.db.models.LinearIngestionStatus
 import com.nextchaptersoftware.db.models.LinearTeamIngestionStatus
 import com.nextchaptersoftware.db.models.Provider
@@ -239,6 +240,43 @@ fun FlowContent.asBadge(provider: Provider, small: Boolean = false, withStyle: S
     }
 }

+fun FlowContent.asBadge(insightType: InsightType, small: Boolean = false) {
+    val size = when (small) {
+        true -> "fa-s"
+        else -> "fa-2xl"
+    }
+    when (insightType) {
+        InsightType.Jira -> span(classes = "fa-brands $size fa-jira") {
+            style = "color: #206AFE;"
+        }
+
+        InsightType.SourceCode,
+        InsightType.PullRequest,
+        InsightType.PullRequestComment,
+        -> span(classes = "fa-brands $size fa-github") {
+            style = "color: #FFFFFF; background-color: #303030; border-radius: 50%;"
+        }
+
+        InsightType.Linear -> img(src = "$WEB_ROOT/images/linear32.png") {
+            alt = "Linear"
+            title = "Linear"
+            width = if (small) "16px" else "34px"
+        }
+
+        InsightType.Slack -> span(classes = "fa-brands $size fa-slack") {
+            style = "color: #360C37;"
+        }
+
+        InsightType.Walkthrough,
+        InsightType.Note,
+        -> img(src = "$WEB_ROOT/images/unblocked-admin.svg") {
+            alt = "Unblocked"
+            title = "Unblocked"
+            width = if (small) "16px" else "34px"
+        }
+    }
+}
+
 fun FlowContent.asBadge(role: ProviderRole?) {
     attributes["data-sort"] = (role?.dbOrdinal ?: 0).toString()

diff --git a/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/InferenceExamplePage.kt b/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/InferenceExamplePage.kt
index c26ee315f..036e4daf4 100644
--- a/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/InferenceExamplePage.kt
+++ b/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/InferenceExamplePage.kt
@@ -6,8 +6,11 @@ import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
 import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils
 import com.nextchaptersoftware.adminwebservice.adminweb.MenuItem
 import com.nextchaptersoftware.adminwebservice.adminweb.component.DropDownOption
+import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
+import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
 import com.nextchaptersoftware.adminwebservice.adminweb.component.property
 import com.nextchaptersoftware.adminwebservice.adminweb.component.submittingDropDownList
+import com.nextchaptersoftware.adminwebservice.adminweb.component.truncate
 import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
 import com.nextchaptersoftware.adminwebservice.adminweb.page.InferenceExamplesPage.renderInferenceExamplesByType
 import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
@@ -16,12 +19,15 @@ import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTem
 import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
 import com.nextchaptersoftware.api.utils.CallExtensions.respondNotFound
 import com.nextchaptersoftware.db.common.Database.suspendedTransaction
+import com.nextchaptersoftware.db.models.InsightType
 import com.nextchaptersoftware.db.models.MLInferenceExample
 import com.nextchaptersoftware.db.models.MLInferenceExampleType
 import com.nextchaptersoftware.db.models.MLInferenceResult
+import com.nextchaptersoftware.db.models.MLTypedDocument
 import com.nextchaptersoftware.db.models.Message
 import com.nextchaptersoftware.db.models.MessageDAO
 import com.nextchaptersoftware.db.models.MessageModel
+import com.nextchaptersoftware.db.models.PullRequest
 import com.nextchaptersoftware.ml.services.examples.MLInferenceExampleService
 import com.nextchaptersoftware.ml.services.results.MLInferenceResultService
 import com.nextchaptersoftware.utils.asUUIDOrNull
@@ -34,22 +40,34 @@ import io.ktor.server.html.respondHtmlTemplate
 import io.ktor.server.request.receiveParameters
 import io.ktor.server.request.uri
 import io.ktor.util.pipeline.PipelineContext
+import java.util.UUID
 import kotlinx.html.ButtonType
 import kotlinx.html.FlowContent
 import kotlinx.html.FormMethod
 import kotlinx.html.ScriptType
 import kotlinx.html.TextAreaWrap
+import kotlinx.html.ThScope
+import kotlinx.html.a
 import kotlinx.html.button
 import kotlinx.html.code
 import kotlinx.html.details
 import kotlinx.html.form
 import kotlinx.html.h1
+import kotlinx.html.h2
 import kotlinx.html.h3
 import kotlinx.html.id
 import kotlinx.html.link
 import kotlinx.html.script
+import kotlinx.html.span
 import kotlinx.html.summary
+import kotlinx.html.table
+import kotlinx.html.tbody
+import kotlinx.html.td
 import kotlinx.html.textArea
+import kotlinx.html.th
+import kotlinx.html.thead
+import kotlinx.html.title
+import kotlinx.html.tr
 import kotlinx.html.unsafe

 object InferenceExamplePage {
@@ -62,6 +80,7 @@ object InferenceExamplePage {
         val breadcrumb = call.makeBreadcrumb()
         val example = AdminPathUtils.getInferenceExample(call.parameters)
         val result = resultService.findByExampleId(example.id)
+        val documents = result?.documents ?: emptyList()
         val goldenRecord = result?.let {
             exampleService.examples(it.goldenRecordExamples)
         } ?: emptyList()
@@ -109,6 +128,7 @@ object InferenceExamplePage {
                     result = result,
                     goldenRecord = goldenRecord,
                     goldenRecordMessages = goldenRecordMessages,
+                    documents = documents,
                     path = path,
                 )
             }
@@ -120,14 +140,17 @@ object InferenceExamplePage {
         result: MLInferenceResult?,
         goldenRecord: List<MLInferenceExample>,
         goldenRecordMessages: List<Message>,
+        documents: List<MLTypedDocument>,
         path: String,
     ) {
         renderExampleData(example, result, path)
         result?.let {
             renderResultData(
                 example = example,
+                prompt = it.prompt,
                 goldenRecord = goldenRecord,
                 goldenRecordMessages = goldenRecordMessages,
+                documents = documents,
                 path = path,
             )
         }
@@ -226,8 +249,10 @@ object InferenceExamplePage {

     private fun FlowContent.renderResultData(
         example: MLInferenceExample,
+        prompt: String,
         goldenRecord: List<MLInferenceExample>,
         goldenRecordMessages: List<Message>,
+        documents: List<MLTypedDocument>,
         path: String,
     ) {
         renderInferenceExamplesByType(
@@ -237,6 +262,90 @@ object InferenceExamplePage {
             actionPath = path,
             messages = goldenRecordMessages,
         )
+
+        h2 { +"Documents" }
+        renderDocuments(
+            teamId = example.teamId,
+            prompt = prompt,
+            documents = documents,
+        )
+    }
+
+    @Suppress("LongMethod")
+    private fun FlowContent.renderDocuments(
+        teamId: UUID,
+        prompt: String,
+        documents: List<MLTypedDocument>,
+    ) {
+        table(classes = "table table-hover align-middle") {
+            thead {
+                tr {
+                    th(scope = ThScope.col) { +"Source" }
+                    th(scope = ThScope.col) { +"Type" }
+                    th(scope = ThScope.col) { +"Title" }
+                    th(scope = ThScope.col) { +"Date" }
+                    th(scope = ThScope.col) { +"Score" }
+                    th(scope = ThScope.col) { +"Included" }
+                    th(scope = ThScope.col, classes = "noSort") { +"View" }
+                }
+            }
+            tbody(classes = "table-dark") {
+                documents.forEach { document ->
+                    tr {
+                        td { asBadge(document.sourceType) }
+                        td { +document.sourceType.name }
+                        td {
+                            span {
+                                title = document.source.ifEmpty { ThreadPage.EMPTY_TITLE }
+                                +(
+                                    document.source.truncate(ThreadsPage.MAX_TITLE_LENGTH)
+                                        .ifEmpty { ThreadPage.EMPTY_TITLE }
+                                    )
+                            }
+                        }
+                        td {
+                            document.date?.let {
+                                timeAgo(it)
+                            } ?: +"-"
+                        }
+                        td {
+                            document.score?.let {
+                                +it.toString()
+                            } ?: +"-"
+                        }
+                        td {
+                            asBadge(prompt.contains(document.sourceId.toString()))
+                        }
+                        td {
+                            when (document.sourceType) {
+                                InsightType.Note,
+                                InsightType.Slack,
+                                InsightType.Jira,
+                                InsightType.Linear,
+                                InsightType.Walkthrough,
+                                -> {
+                                    a(
+                                        classes = "btn btn-outline-info btn-sm mx-1",
+                                        href = "/teams/$teamId/threads/${document.sourceId}",
+                                    ) { +"Thread" }
+                                }
+
+                                InsightType.PullRequest,
+                                InsightType.PullRequestComment,
+                                -> {
+                                    a(
+                                        classes = "btn btn-outline-info btn-sm mx-1",
+                                        href = "/teams/$teamId/pullRequests/${document.sourceId}",
+                                    ) { +"PR" }
+                                }
+
+                                InsightType.SourceCode -> {}
+                            }
+                        }
+                    }
+                }
+            }
+        }
     }

     suspend fun PipelineContext<Unit, ApplicationCall>.updateInferenceResponse(
