{"state":"Closed","mergedAt":null,"number":6003,"mergeCommitSha":"242019f4c32d3e6486ccad868bbd4cd25bfb0119","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6003","title":"Identity Align: hook up alignment PG data to members API response","createdAt":"2023-05-03T00:36:53Z"}
{"state":"Closed","mergedAt":null,"number":6004,"body":"Only primary members have web URL pages.","mergeCommitSha":"6233b123aa2baea5d21f379f5d75b94941198fd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6004","title":"Populate team member dashboard url in TeamMembers API endpoints","createdAt":"2023-05-03T03:26:07Z"}
{"state":"Merged","mergedAt":"2023-05-03T18:25:32Z","number":6005,"body":"This service will create threads from each jira issue. The first message of the thread will have the description of the issue (if it exists) and reply messages will be created for each issue comment.","mergeCommitSha":"db8c74eb66a5c40d7475145b4c514e513ff167d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6005","title":"Create JiraIssueIngestionService","createdAt":"2023-05-03T05:46:26Z"}
{"state":"Merged","mergedAt":"2023-05-03T09:06:19Z","number":6006,"body":"Gradually move eerything to centralized model for oauth.","mergeCommitSha":"8c372d98aa1041a9dca5c93972457e07815612f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6006","title":"Cleanup auth across providers","createdAt":"2023-05-03T07:18:10Z"}
{"state":"Merged","mergedAt":"2023-05-03T07:27:12Z","number":6007,"mergeCommitSha":"2e2ce7da93c5bbd1fdd4863d12da7ef59e109d4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6007","title":"disable test parallelization","createdAt":"2023-05-03T07:27:02Z"}
{"state":"Merged","mergedAt":"2023-05-03T08:08:19Z","number":6008,"body":"- Revert \"disable test parallelization (#6007)\"\r\n- AND AGAIN\r\n","mergeCommitSha":"8d876acdec53a85bd30ddbdc6d00385a69e85820","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6008","title":"Revertchanges","createdAt":"2023-05-03T08:08:07Z"}
{"state":"Merged","mergedAt":"2023-05-03T18:53:21Z","number":6009,"mergeCommitSha":"b446efdf901017fb2edf589fb34ecadbd4132d1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6009","title":"[BREAKS API ON MAIN] This pr breaks api on main","createdAt":"2023-05-03T17:04:07Z"}
{"state":"Merged","mergedAt":"2022-03-17T17:20:29Z","number":601,"body":"I just realized `FormattedText` has an `is_code` field which covers the inline code case. Maybe let's back this change out from yesterday and just use `FormattedText` for inline code elements?","mergeCommitSha":"7e1dd15e1ed9651498b3a7ff04c5cd17f24def90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/601","title":"Revert \"Add code InlineElement (#593)\"","createdAt":"2022-03-17T17:09:35Z"}
{"state":"Merged","mergedAt":"2023-05-03T20:51:11Z","number":6010,"body":"## Summary\r\n\r\nThe `Person` type was recently modified in a way that broke AuthStore serialization. This meant that current `Stable` clients will log the user out after an upgrade.","mergeCommitSha":"9bfa7d8f0195b86cfdafd0dab639d704a032bd6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6010","title":"Legacy authstore migration","createdAt":"2023-05-03T17:26:26Z"}
{"state":"Merged","mergedAt":"2023-05-03T19:23:12Z","number":6011,"mergeCommitSha":"c8797d41a2ec63b089f2e6643220460e5dea6d34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6011","title":"Append additonal state to urls","createdAt":"2023-05-03T17:32:32Z"}
{"state":"Merged","mergedAt":"2023-05-03T20:13:55Z","number":6012,"mergeCommitSha":"170c16da7f8d036fddb96933772443554d4eb8fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6012","title":"More api fixes","createdAt":"2023-05-03T19:06:17Z"}
{"state":"Closed","mergedAt":null,"number":6013,"body":"The apis make reference to an integration installation id.\r\n\r\n  /teams/{teamId}/installations/{installationId}:\r\n\r\nDo we want this to be barebones and just indicate installation state for a provider?\r\nAnd can we have multiple installations for the same integration provider?\r\nNot sure what the thinking is here...\r\n\r\nAnd what fields besides the ones listed we should be putting in there.","mergeCommitSha":"88796fa84460c4ee5ec4c66fe46d82c66f4e109e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6013","title":"Is this what we want?","createdAt":"2023-05-03T19:33:42Z"}
{"state":"Merged","mergedAt":"2023-05-03T21:47:54Z","number":6014,"mergeCommitSha":"12660310a711392d4bf1b253d01885b62c0afffc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6014","title":"Factor out auth","createdAt":"2023-05-03T20:15:37Z"}
{"state":"Merged","mergedAt":"2023-05-03T21:37:52Z","number":6015,"mergeCommitSha":"22bf09d2b95cd4cd7056594b0e69a7081d3032cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6015","title":"If AuthState restore fails, restore AuthToken","createdAt":"2023-05-03T20:39:02Z"}
{"state":"Merged","mergedAt":"2023-05-03T21:42:45Z","number":6016,"mergeCommitSha":"dfed2dac69383731921c513494e5ad0e19f1de4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6016","title":"Doug is a demo user, so recommendations are fabricated","createdAt":"2023-05-03T21:36:56Z"}
{"state":"Merged","mergedAt":"2023-05-04T00:40:24Z","number":6017,"mergeCommitSha":"c978a1d3278362281ae5c0bd0dc4814d60155206","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6017","title":"Basic list integrations","createdAt":"2023-05-03T22:34:10Z"}
{"state":"Merged","mergedAt":"2023-05-03T22:53:28Z","number":6018,"mergeCommitSha":"2a01b617d346e87d9464f84052a7254367267d3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6018","title":"Restore all Person properties","createdAt":"2023-05-03T22:41:09Z"}
{"state":"Merged","mergedAt":"2023-05-04T01:53:08Z","number":6019,"body":"This disables Keda auto-scaler objects so I can remove and re-deploy the controllers without breaking Dev deployments","mergeCommitSha":"9a55157e9b7033285a3fad77a6d8d3dabf77c332","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6019","title":"Disable auto-scaler while troubleshooting","createdAt":"2023-05-04T01:25:19Z"}
{"state":"Merged","mergedAt":"2022-03-17T19:03:51Z","number":602,"body":"This is the final bit of logic that ensures we're dealing with the correct team","mergeCommitSha":"3c03aa26ea8747f68d8ad118cccbd6fe8a5d54fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/602","title":"Extract TeamMember from context","createdAt":"2022-03-17T17:23:58Z"}
{"state":"Merged","mergedAt":"2023-05-04T17:32:08Z","number":6020,"mergeCommitSha":"40dfa417c1752dbd78c5b02f34c380470ecb22be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6020","title":"Basic Installation models","createdAt":"2023-05-04T04:06:23Z"}
{"state":"Merged","mergedAt":"2023-05-04T05:17:38Z","number":6021,"body":"This is what we do for all other non-GitHub Enterprise providers.","mergeCommitSha":"a72a6e4040b2006737a0567e53a0fd9a7d52aaf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6021","title":"Use Provider.Jira.dbOrdinal for Identity.externalTeamId","createdAt":"2023-05-04T04:58:15Z"}
{"state":"Merged","mergedAt":"2023-05-04T08:36:42Z","number":6022,"mergeCommitSha":"be38f2e81672f269b9a8729c1506bed742aeb8a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6022","title":"Fix identity backfill migration","createdAt":"2023-05-04T08:35:16Z"}
{"state":"Merged","mergedAt":"2023-05-04T17:04:26Z","number":6023,"body":"Also populate team member dashboard url in TeamMembers API endpoints.\r\nOnly primary members have web URL pages.\r\n\r\nFixes: https://linear.app/unblocked/issue/UNB-1233/identity-align-hook-up-alignment-pg-data-to-members-api-response","mergeCommitSha":"2e8d4dd460f44ffe5b00260793b2b76207ac16ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6023","title":"Hook up primary team member info to members API response","createdAt":"2023-05-04T09:02:52Z"}
{"state":"Merged","mergedAt":"2023-05-07T22:45:36Z","number":6024,"body":"This will run immediately after the user Oauths, installs the marketplace app, and selects the projects to be ingested.","mergeCommitSha":"51f57dbc25a6ba817db9f60379d2bd3cb9b0f703","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6024","title":"Add JiraIssueBulkIngestionJob","createdAt":"2023-05-04T16:28:08Z"}
{"state":"Closed","mergedAt":null,"number":6025,"body":"- Basic Installation models\r\n- Fix test\r\n- REmove printf\r\n- Update\r\n- Use upsert\r\n","mergeCommitSha":"142aeeb89e265670e26fada57e2776175a19ef7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6025","title":"MoveToUpsert","createdAt":"2023-05-04T17:24:23Z"}
{"state":"Merged","mergedAt":"2023-05-04T18:13:13Z","number":6026,"mergeCommitSha":"bd01116cfc1692a92a5dc2a3a8bea0dfdbce1dc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6026","title":"Use upsert for database models","createdAt":"2023-05-04T17:25:07Z"}
{"state":"Merged","mergedAt":"2023-05-04T18:31:58Z","number":6027,"body":"Team member store interface was getting too big to understand.","mergeCommitSha":"729c992d4991c74ad28255dc298d956ee3581e54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6027","title":"Refactor association store","createdAt":"2023-05-04T18:06:41Z"}
{"state":"Merged","mergedAt":"2023-05-04T18:33:37Z","number":6028,"body":"Next step is to make it event based, triggering when team members change.","mergeCommitSha":"fee54b642ec774f5009b1890c1637c4056df8ae9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6028","title":"Run identity assocation manually from admin","createdAt":"2023-05-04T18:13:07Z"}
{"state":"Merged","mergedAt":"2023-05-04T19:09:15Z","number":6029,"body":"This will be used to get all projects for the Jira site. We'll call this during onboarding to list projects for the user to choose so that we only ingest issues from user-selected projects.","mergeCommitSha":"904e5c92a95ec9eed5e5a7dd1df4cd5c5af4a1a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6029","title":"Add JiraProjectsApi","createdAt":"2023-05-04T18:19:49Z"}
{"state":"Merged","mergedAt":"2022-03-17T22:38:40Z","number":603,"body":"![image](https://user-images.githubusercontent.com/13431372/158869851-5b75fe88-2b8b-4d9d-bc59-6b42bd49619f.png)\r\n\r\n* only show 'All Conversations' right now in Navigator\r\n* fetch and display the anchor sourcemark of the first message (anchor message)\r\n* stream in and display participant data\r\n* link from the conversation view to the thread view, fetch and display anchor sourcemark in the thread view\r\n![image](https://user-images.githubusercontent.com/13431372/158870020-38e5374c-4f22-4ef3-be0e-e82dd40d2063.png)\r\n\r\nNOTES:\r\n* I kept the mocked out Chat view for now\r\n* There needs to be ensuing work to fix the stories which only display the mocked out views (how do we mock out the data stores??) -- I think this can be done in the next PR?","mergeCommitSha":"930b13222b8605c961bccea874b03f5ebaea188a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/603","title":"Fetch and render threads on web","createdAt":"2022-03-17T18:22:18Z"}
{"state":"Merged","mergedAt":"2023-05-08T19:44:16Z","number":6030,"body":"Refactor parts of installer flow to work with IntelliJ.\r\n\r\nAlso fixes UNB-1082\r\n\r\n<img width=\"465\" alt=\"CleanShot 2023-05-04 at 11 49 37@2x\" src=\"https://user-images.githubusercontent.com/1553313/236301154-cddb3465-4c3d-43a9-a662-4ee5aece2ac4.png\">\r\n<img width=\"620\" alt=\"CleanShot 2023-05-04 at 11 51 18@2x\" src=\"https://user-images.githubusercontent.com/1553313/236301401-636ba807-5317-4a9e-a07e-f2f761ceb28c.png\">\r\n","mergeCommitSha":"cd56eac2125b4170126e6d32e1078aa62bf0b960","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6030","title":"[unb 1081] installer flow for IntelliJ","createdAt":"2023-05-04T18:54:19Z"}
{"state":"Merged","mergedAt":"2023-05-04T19:58:19Z","number":6031,"body":"This is to allow the handler code to call out to the Jira rest api in the jira service to get all projects after receiving a Jira marketplace app installation event.","mergeCommitSha":"96059f84e9c28ec9192897067c3b62df8f6899e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6031","title":"Move Jira webhook code to its own lib-jira-webhook module","createdAt":"2023-05-04T19:38:17Z"}
{"state":"Merged","mergedAt":"2023-05-04T21:43:17Z","number":6032,"body":"## Summary\r\nNeeds very careful review:\r\n\r\n- Brings Hub Install process in line with plugin install process. This is inherently more reliable because the installer will echo back installation success, which allows the Hub to try again if something failed.\r\n- Removes manual upgrades. We used to provide a fallback to the native installer, but if the automatic installer fails there's no reason to believe that the native installer will succeed. It's better to just let the hub try again.\r\n- Add additional upgrade success check at the tail end of the install to prevent any possibility of a \"false positive\" upgrade","mergeCommitSha":"6ce6e259ba57778b1bbea75c67180484b18a065f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6032","title":"Brings hub installation in line with plugin installation process","createdAt":"2023-05-04T19:40:02Z"}
{"state":"Merged","mergedAt":"2023-05-04T21:01:41Z","number":6033,"mergeCommitSha":"9616c76af41c0c4775aa6f8ff8ac16bba9e431b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6033","title":"Hook up identity alignment to manual run from admin web","createdAt":"2023-05-04T20:01:51Z"}
{"state":"Merged","mergedAt":"2023-05-04T22:20:03Z","number":6034,"body":"We still need to add the models for storing the projects in the database, but this logic will go out to the Jira rest API to grab all projects after we receive an installation webhook.","mergeCommitSha":"f8d73ab7ba7bfc6b35f8703f647ea12d82af415c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6034","title":"Add JiraProjectIngestionService","createdAt":"2023-05-04T21:24:10Z"}
{"state":"Merged","mergedAt":"2023-05-04T21:29:41Z","number":6035,"mergeCommitSha":"804b34ff39a7d3de6a75cdba318c6f32c43696dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6035","title":"Fix identity association bug","createdAt":"2023-05-04T21:25:48Z"}
{"state":"Merged","mergedAt":"2023-05-04T23:05:16Z","number":6036,"mergeCommitSha":"454b2af99510a413523aa4af8a7376899eb4c2b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6036","title":"Invoke alignment algorithm when team members are modified","createdAt":"2023-05-04T21:38:51Z"}
{"state":"Merged","mergedAt":"2023-05-04T22:19:12Z","number":6037,"mergeCommitSha":"6999ee9e6d40565aa871c8faee8bf76e8a9782ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6037","title":"Version Bump","createdAt":"2023-05-04T22:18:58Z"}
{"state":"Merged","mergedAt":"2023-05-05T01:26:49Z","number":6038,"body":"Fixes UNB-1032\r\n\r\nThis works because of the update refactor, which will run the update code every time the version info is refreshed (roughly every 30 seconds)","mergeCommitSha":"81050033d2c1a8d2e485428db60caf78e2448f81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6038","title":"Fix walkthrough crashing when an app update is available","createdAt":"2023-05-04T22:40:14Z"}
{"state":"Merged","mergedAt":"2023-05-04T23:08:44Z","number":6039,"mergeCommitSha":"547d02a46a38c701360612fefb87adeef19cb13f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6039","title":"Conditionally load Slack and Linear runtime fixtures","createdAt":"2023-05-04T22:50:05Z"}
{"state":"Merged","mergedAt":"2022-03-17T23:25:34Z","number":604,"body":"1. Scroll discussion thread in VSCode\r\n2. Scroll discussion thread in Web\r\n3. Maximum height for code blocks in ask a question\r\n\r\nhttps://user-images.githubusercontent.com/1553313/158886838-8386f43d-cc0b-4e05-84f5-f52335b687cd.mp4\r\n\r\n\r\n","mergeCommitSha":"73b23777eea5a27f4bfb36cc6b1d1fdee26b980d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/604","title":"Fix small styling issues","createdAt":"2022-03-17T20:08:45Z"}
{"state":"Merged","mergedAt":"2023-05-04T23:18:40Z","number":6040,"mergeCommitSha":"8563a364cb9b22b805782ae4271b2df3004e2f3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6040","title":"Fix update cycle","createdAt":"2023-05-04T23:17:55Z"}
{"state":"Merged","mergedAt":"2023-05-05T04:24:52Z","number":6041,"body":"Also upsert these to the database in the JiraProjectIngestionService so that we can list these during onboarding.","mergeCommitSha":"42185d65b6d3696505a5e1b34e9d1aacb0d48a21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6041","title":"Add JiraProjectModel","createdAt":"2023-05-05T00:10:44Z"}
{"state":"Merged","mergedAt":"2023-05-05T19:26:10Z","number":6042,"mergeCommitSha":"1f27c8d1240c17665bc08e8e4d110925612a19f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6042","title":"Identity alignment takes into account accounts explicitly linked by the user","createdAt":"2023-05-05T00:40:39Z"}
{"state":"Merged","mergedAt":"2023-05-05T01:25:37Z","number":6043,"body":"This will only relaunch the hub _after_ the installed version manifest is written to disk so that we don't get multiple updates. We also now check at runtime if the hub is on the latest version and short circuit the installer if so.","mergeCommitSha":"92a4d3d3bd4bad4e3a43009b9d9dc45bd003d490","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6043","title":"Fixes update/restart race","createdAt":"2023-05-05T01:04:39Z"}
{"state":"Merged","mergedAt":"2023-05-05T02:48:45Z","number":6044,"body":"- [BREAKS API ON MAIN] Slack user oauth\r\n- Update\r\n","mergeCommitSha":"a4a5b50d2fc1cfbdc1ce1d48e32ea9c882af2c10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6044","title":"[BREAKS API ON MAIN] Please build","createdAt":"2023-05-05T01:53:13Z"}
{"state":"Merged","mergedAt":"2023-05-05T02:49:36Z","number":6045,"body":"2 days into troubleshooting I finally figured out that Keda needs Kube metric service. The metric service shipping with Keda is more of a proxy and not a full metric service! It would have been nice if they good reflect this shit in the name!\r\n\r\n- Re-installed Keda\r\n- Installed Metric Service\r\n- Renabled auto-scalers in Dev","mergeCommitSha":"1b2434e85e610213ab6f7102f844723776913d5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6045","title":"Add Kube metric service needed for Keda to work","createdAt":"2023-05-05T02:09:30Z"}
{"state":"Merged","mergedAt":"2023-05-05T02:23:43Z","number":6046,"mergeCommitSha":"58323426ca540e47dd7400b31dbd7a0faa41bae2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6046","title":"Bump","createdAt":"2023-05-05T02:23:29Z"}
{"state":"Merged","mergedAt":"2023-05-05T05:36:02Z","number":6047,"body":"- Removed container name from scaler resource. HPA's feature to support container based metrics is still in Alpha. This would default to overall pod level resource utilization. Since we only have one container per pod it should be fine. \r\n- Regenerated all helm charts \r\n- Enabled scaler resource for 3 services in prod (api, auth and pusher)\r\n\r\nI have tested this scaler in Dev and worked as expected. It should help us deal with the auth service overload in prod for now while we fix the root cause.\r\n\r\nNext I will be looking into adding queue based pod scalers as well as adding CPU based scalers to our remaining services","mergeCommitSha":"682f34885a6e6474aab0943a501b566a4d8cb0be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6047","title":"Enable scaler for auth,api and pusher services in prod","createdAt":"2023-05-05T03:31:13Z"}
{"state":"Merged","mergedAt":"2023-05-05T17:30:22Z","number":6048,"body":"Add a custom banner matching our designs, for scenarios where we have source code.  The full-window editor, for when we don't have source,  is coming next.\r\n\r\n<img width=\"1512\" alt=\"Screenshot 2023-05-04 at 10 52 17 PM\" src=\"https://user-images.githubusercontent.com/2133518/236385919-6651eed7-f295-415a-a4c0-e586b9fd4046.png\">\r\n","mergeCommitSha":"3785d1f3d3581349f57be9decc1a4dd50dc69f35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6048","title":"JetBrains read-only view banner","createdAt":"2023-05-05T05:53:29Z"}
{"state":"Merged","mergedAt":"2023-05-11T22:24:35Z","number":6049,"body":"We are going to want to pull these prompts out into a A/B framework someday!","mergeCommitSha":"610e4b51c5d0f13f6fb6129d8bc6c509a33c4089","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6049","title":"Move default prompts into companion const + reference from adminweb","createdAt":"2023-05-05T18:00:35Z"}
{"state":"Merged","mergedAt":"2022-03-18T00:09:02Z","number":605,"body":"What the heck is this ? I have taken out individual packages from core and added them to new Gradle projects as described in this doc https://www.notion.so/nextchaptersoftware/Service-Architecture-Decomposition-f65d933de2f94c2487de73043efe33f5\r\n\r\nNew Gradle projects:\r\n**Clients Project**:\r\n  - Redis\r\n  - SCM\r\n\r\n**Models Project**: \r\n  - Db \r\n  - models \r\n\r\n**Libs Project**:\r\n  - Pringestion \r\n  - Sourcemarks\r\n\r\n**Utils Project**:\r\n - config \r\n - logs\r\n - http (some exception stuff that are global)\r\n - security \r\n - utils (date time stuff)\r\n \r\n \r\nTo do this I had to break the dependency between Sourcemarks -> db and scm-> db. Basically I had to change those packages so we could eliminate some circular dependencies (not entirely circular but it becomes circular when we separate projects). \r\n\r\n**Here's what I changed in the Kotlin code**:\r\n- I moved SCM `Provider` enum class to DB package in `RepoModel.kt` (like we have done for video stuff)\r\n- Moved `SourceMarkVisibility` and `SourceMarkLifecycle` to RepoModel.kt in db package \r\n- Moved the Hash class out of Hash class out of source marks and added it to utils package (it was used all over the place). \r\n- I also updated all imports related to changes above accordingly \r\n\r\n**Here's what changed in Gradle config files**:\r\n- Removed unused dependencies in each new project \r\n- Removed OpenAPI, GraphQL and Proto generations wherever they were not needed \r\n- Added necessary inter-project dependencies \r\n\r\nFinally I moved Graphql submodule to it's new and hopefully final location under `clients` Gradle project. \r\n```\r\n# On your next pull from master once this is merged \r\ngit submodule update --init --recursive\r\nrm -r projects/core\r\n```\r\n\r\nI have tested this locally and all integration tests passed but I would appreciate it if someone else could give it a go as well. ","mergeCommitSha":"c385ae7d39c6a9fced5fbbe80138d518bb24bb2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/605","title":"Create more subprojects","createdAt":"2022-03-17T23:12:45Z"}
{"state":"Merged","mergedAt":"2023-05-10T23:28:10Z","number":6050,"body":"These endpoints are modelled after the slack configuration endpoints to allow a user to pick the Jira projects for in a Jira site they want ingested. \r\n\r\n(Note: a Jira project is to a Jira site what a Slack channel is to a Slack org or what a repo is to a GitHub org)\r\n\r\nDuring onboarding, the Jira connection page on the Unblocked tab will look like this:\r\n\r\n<img width=\"1293\" alt=\"CleanShot 2023-05-05 at 11 06 36@2x\" src=\"https://user-images.githubusercontent.com/1924615/236535021-99e8bb8f-a39d-4946-a895-98ffc4b4cb35.png\">\r\n\r\nAfter they oauth and install the app from the marketplace (launched in a separate tab):\r\n\r\n<img width=\"1285\" alt=\"CleanShot 2023-05-05 at 11 08 08@2x\" src=\"https://user-images.githubusercontent.com/1924615/236535126-2eae2d69-aa73-42bd-92c2-ed3dfff93b11.png\">\r\n\r\n...they should return to the Unblocked tab and it should update to look like this:\r\n\r\n<img width=\"1291\" alt=\"CleanShot 2023-05-05 at 11 09 36@2x\" src=\"https://user-images.githubusercontent.com/1924615/236535388-5de8fd56-78c2-4cd3-aad3-16f1a5861ba8.png\">\r\n\r\nThis tab is what these endpoints are meant to serve. The idea here is to poll on `getJiraProjects` and show this tab once `getJiraProjects` returns with a response where `isIngestionComplete` is true, since at that point the response will contain all projects in the Jira site.","mergeCommitSha":"b576171527093ab1aeac143634d7e79b8b427bce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6050","title":"Add Jira project configuration endpoints","createdAt":"2023-05-05T18:13:42Z"}
{"state":"Merged","mergedAt":"2023-05-05T19:26:59Z","number":6051,"mergeCommitSha":"f14089e6a471aebbe1024e149e93ce81b1ad8918","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6051","title":"Admin refactor for identities","createdAt":"2023-05-05T18:56:24Z"}
{"state":"Merged","mergedAt":"2023-05-10T01:38:22Z","number":6052,"body":"Update TeamMemberStreams to ingest `isPrimary` property.\r\n\r\nThese are the updated Team Member streams:\r\n* `stream` - The data cache stream\r\n* `listStream` - The full list of *all* team members (no filters)\r\n* `activeListStream` - The above stream but filters for current team members and non-bots\r\n* `mapStream` - The default map stream - where each id maps to associated the primary member where possible; if not, id maps to the non-primary user \r\n* `mapStreamValues` - A deduped list of values of `mapStream` \r\n\r\nThe general result of this UI work is that we will always show the SCM display name/avatar where possible (topics, threads, etc). We will fall back on showing the secondary users if there is no primary user association. ","mergeCommitSha":"4590eae9aff448307034c5581e089e4a4e7ebccc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6052","title":"[UNB-1239] Ingest and output primary user associations","createdAt":"2023-05-05T20:28:30Z"}
{"state":"Merged","mergedAt":"2023-05-05T22:39:12Z","number":6053,"mergeCommitSha":"9853f002af54bb22640bff3fcc27d789b7acb3a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6053","title":"Identity Align: Searching with primary ID should return results for primary and associated members","createdAt":"2023-05-05T20:33:31Z"}
{"state":"Merged","mergedAt":"2023-05-17T14:59:54Z","number":6054,"mergeCommitSha":"e5445ea2a21604348267ffa091280639bcb33e87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6054","title":"Call IDE to finish onboarding","createdAt":"2023-05-05T21:48:33Z"}
{"state":"Merged","mergedAt":"2023-05-08T21:18:39Z","number":6055,"body":"Fixes UNB-1137\r\n\r\nEditor displayed when we cannot resolve any source code.  There are two panels: one displayed when there is a fault, one when we think the source code might be on the origin.\r\n\r\nI have adopted the MigLayout layout manager -- it has a super goofy API, but it at least seems to work and reliably lay out components, unlike the built-in layout managers.\r\n\r\n<img width=\"1512\" alt=\"Screenshot 2023-05-05 at 3 58 34 PM\" src=\"https://user-images.githubusercontent.com/2133518/236582740-83a512f8-84fd-490f-83a4-ce9236391208.png\">\r\n<img width=\"1512\" alt=\"Screenshot 2023-05-05 at 4 01 43 PM\" src=\"https://user-images.githubusercontent.com/2133518/236582743-eebecbd0-f6f1-4725-ab77-639939b1782e.png\">\r\n","mergeCommitSha":"7933435950d8bc8a19fee89fc6f11351e3fd4f29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6055","title":"JetBrains unresolved file editor","createdAt":"2023-05-05T23:08:01Z"}
{"state":"Merged","mergedAt":"2023-05-08T17:42:57Z","number":6056,"mergeCommitSha":"312ce47a94a6397bc142be71de0ddbfc83b56bec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6056","title":"Complete integrations onboarding","createdAt":"2023-05-05T23:13:46Z"}
{"state":"Merged","mergedAt":"2023-05-06T23:47:55Z","number":6057,"body":"- Fix team member invites view bug on team member page\r\n- Fix vertical profile wrapping bug\r\n- Turn off search autocompletion\r\n- Separate routes for current, past, and integration members","mergeCommitSha":"0ed1b40c112a65efc5c0a724d35f3919564a4911","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6057","title":"Team member page enhancements","createdAt":"2023-05-06T23:13:40Z"}
{"state":"Merged","mergedAt":"2023-05-07T06:05:03Z","number":6058,"mergeCommitSha":"520d563c2c3dc669316a300b8b556680dc34bcf4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6058","title":"Show associated members","createdAt":"2023-05-07T05:56:17Z"}
{"state":"Merged","mergedAt":"2023-05-07T19:54:34Z","number":6059,"mergeCommitSha":"ccedade7659107d69e60b14fee618677a35233fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6059","title":"Add linear icon","createdAt":"2023-05-07T19:15:52Z"}
{"state":"Closed","mergedAt":null,"number":606,"mergeCommitSha":"cf59c8686b2a430e8284a4bd5d3c292612cd246f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/606","title":"Add languageId to the SourceSnippet","createdAt":"2022-03-17T23:36:59Z"}
{"state":"Merged","mergedAt":"2023-05-07T23:15:31Z","number":6060,"body":"This will let users configure whether they want all projects in a site ingested or just selected channels. Next PR will add the ability to store channel preferences if the user wants just selected channels ingested.","mergeCommitSha":"1ce518cddb2b3be6ba3a8f71cdc86088f1d32d7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6060","title":"Add JiraSite.jiraProjectIngestionType property","createdAt":"2023-05-07T22:54:07Z"}
{"state":"Merged","mergedAt":"2023-05-08T06:01:30Z","number":6061,"body":"This will allow users to configure which projects to ingest if they choose not to ingest all projects in a Jira site.","mergeCommitSha":"b08c1821de8b9ab4312773809db131bb815b1f58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6061","title":"Add JiraProjectModel.shouldIngest","createdAt":"2023-05-07T23:27:18Z"}
{"state":"Merged","mergedAt":"2023-05-08T07:29:18Z","number":6062,"body":"This is to ensure that if a user chooses not to ingest all projects in a Jira site, we only ingest those they've selected.","mergeCommitSha":"c76bc496dd11bc92f47fc1036573b32bfd43e5ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6062","title":"Only ingest jira projects selected by the user","createdAt":"2023-05-08T06:20:14Z"}
{"state":"Merged","mergedAt":"2023-05-08T08:21:35Z","number":6063,"body":"To ensure these threads are searchable","mergeCommitSha":"e48d794dac322c43cb7271745b7f9821013f66b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6063","title":"Trigger thread events when creating a Jira thread","createdAt":"2023-05-08T07:50:51Z"}
{"state":"Merged","mergedAt":"2023-05-08T16:14:28Z","number":6064,"body":"When multiple primary members that have the same profile information each match\nthe profile information from another non-primary member, then the aligned primary\nmember was randomly assigned.\n\nThis change prioritizes the primary members that is a current member of the team.","mergeCommitSha":"d538d4db0e56ede07f659e6cd3dc7940269386dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6064","title":"Identity alignment should prioritize current members of the team on conflict","createdAt":"2023-05-08T16:01:28Z"}
{"state":"Merged","mergedAt":"2023-05-08T18:56:05Z","number":6065,"mergeCommitSha":"6a70bf0fe318890b434fd92262b7d48f59b61ee1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6065","title":"Implementation for hasCompletedIntegrationsOnboarding flag","createdAt":"2023-05-08T17:14:54Z"}
{"state":"Merged","mergedAt":"2023-05-08T19:42:31Z","number":6066,"body":"I set the wrong default layout for webviews.  Oops.  Fixes UNB-1249.","mergeCommitSha":"413cb3edb17d65c54aca50ba6ad5eaaed5139ca0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6066","title":"Fix default webview layout","createdAt":"2023-05-08T17:50:45Z"}
{"state":"Merged","mergedAt":"2023-05-08T19:40:53Z","number":6067,"body":"Create an endpoint for serving the marketplace app descriptor for our Jira integration\r\n\r\nhttps://developer.atlassian.com/cloud/jira/platform/connect-app-descriptor/","mergeCommitSha":"480378a2c17898c99301ef382c5137490b879a46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6067","title":"Create api service operation to serve our jira marketplace app descriptor","createdAt":"2023-05-08T18:08:27Z"}
{"state":"Merged","mergedAt":"2023-05-09T00:16:01Z","number":6068,"body":"When the agent dies, another agent session is created. When this occurs, we need the extension to restart its corresponding AgentAPI interface and streams.\r\n\r\nWhen the agent does restart, there is unfortunately some downtime as the entire plugin is reloaded.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/236900364-1835353a-5af5-4068-be08-c8551b048568.mp4\r\n\r\n","mergeCommitSha":"e801de29928425f15a8c23c5df965cebf533113c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6068","title":"[UNB-1217] Restart Extension on Agent Restart","createdAt":"2023-05-08T18:18:19Z"}
{"state":"Merged","mergedAt":"2023-05-08T22:29:10Z","number":6069,"mergeCommitSha":"5c194df4e3f42c624b7486cb25bc3ac3b16f6d2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6069","title":"[BREAKS API ON MAIN] This is a first pass hidden api","createdAt":"2023-05-08T18:37:30Z"}
{"state":"Merged","mergedAt":"2022-03-18T05:12:30Z","number":607,"body":"We should not be saving the full system path onto the sourcemark object - instead, save the relative path for display, and then find the repo root in vscode when needing to navigate.\r\n\r\nRelative path in the code block:\r\n![image](https://user-images.githubusercontent.com/13431372/158929786-f154cb78-0da9-4206-863d-24d4dc28422d.png)\r\n","mergeCommitSha":"aa75bdc0c94234bb75fe3b6d4d75307a89ee2167","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/607","title":"Save relative path and concatenate full path for navigation","createdAt":"2022-03-18T00:26:46Z"}
{"state":"Merged","mergedAt":"2023-05-08T21:15:21Z","number":6070,"body":"Hard to know what features have been overridden at a glance. This helps.","mergeCommitSha":"16289eb3ccea91d10fa7ab1b7fd16c26cee979d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6070","title":"Render feature overrides","createdAt":"2023-05-08T20:18:43Z"}
{"state":"Merged","mergedAt":"2023-05-08T20:59:52Z","number":6073,"body":"Not slack :)","mergeCommitSha":"5125dff9c84bdd6205c9d28965d838e18ba78918","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6073","title":"Use correct provider in JiraIntegrationFactory","createdAt":"2023-05-08T20:35:19Z"}
{"state":"Merged","mergedAt":"2023-05-09T00:19:09Z","number":6074,"body":"Fix issue where reopening the last opened issue doesn't show the tool window.\r\n","mergeCommitSha":"f7331a4abc90728fece8044d7f1d28cb5fd324ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6074","title":"Fix Reopening Issue","createdAt":"2023-05-08T20:37:59Z"}
{"state":"Merged","mergedAt":"2023-05-08T22:15:02Z","number":6075,"body":"<img width=\"356\" alt=\"CleanShot 2023-05-08 at 14 30 27@2x\" src=\"https://user-images.githubusercontent.com/1553313/236939675-e04f4d15-c866-4430-9322-1a95271e269b.png\">\r\n<img width=\"1020\" alt=\"CleanShot 2023-05-08 at 14 30 15@2x\" src=\"https://user-images.githubusercontent.com/1553313/236939681-79ee59e5-55a6-40dc-a0a5-69f15d024195.png\">\r\n\r\nMissing justify-content...\r\nI think it was due to a merge conflict between some of my PRs.","mergeCommitSha":"e65cbe00a87b873ba497f08123dedb847610687d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6075","title":"Fix Button Layout","createdAt":"2023-05-08T21:32:28Z"}
{"state":"Merged","mergedAt":"2023-05-08T22:48:50Z","number":6076,"body":"So that we have a fallback if the marketplace app isn't installed","mergeCommitSha":"6a68865951d3b48415803b5889b8b12889005c52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6076","title":"Create Jira identity after oauth and store tokens","createdAt":"2023-05-08T22:12:22Z"}
{"state":"Merged","mergedAt":"2023-05-15T20:25:51Z","number":6077,"body":"Only Insight ToolWindows should be shown and focused on load.\r\nOthers should remain hidden on launch.\r\n\r\nThis is subject to change as we introduce user actions that should launch and focus the extension sidebars.","mergeCommitSha":"fb094f3590a6c15dc25d4afc80a3dd235418ad68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6077","title":"Conditionally focus toolwindow","createdAt":"2023-05-08T22:27:40Z"}
{"state":"Merged","mergedAt":"2023-05-09T02:04:30Z","number":6078,"mergeCommitSha":"d099a90aa3db8994123413695c8a9083e28e65d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6078","title":"Check for an existing Jira site before creating one","createdAt":"2023-05-08T23:00:41Z"}
{"state":"Merged","mergedAt":"2023-05-08T23:21:36Z","number":6079,"body":"Fixes UNB-1212\r\n\r\nI haven't figured out the root cause of this yet, the embedded CEF browser isn't sending the correct `insertParagraph` event when enter is pressed, but I don't know why.  The actual key event *is* sent, so I'm basically overriding it here.","mergeCommitSha":"83868049726bf9d3a4d60acf96e3356a5090b298","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6079","title":"Hack to workaround missing break insertion event in IntelliJ","createdAt":"2023-05-08T23:00:41Z"}
{"state":"Merged","mergedAt":"2022-03-18T01:49:26Z","number":608,"mergeCommitSha":"68ffadc3e8fe09030943e359745b1d1125e5b702","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/608","title":"adding Kay to ip whitelist","createdAt":"2022-03-18T01:48:37Z"}
{"state":"Merged","mergedAt":"2023-05-08T23:55:39Z","number":6080,"body":"# Before\r\n<img width=\"1085\" alt=\"Screenshot 2023-05-08 at 16 13 13\" src=\"https://user-images.githubusercontent.com/1798345/236956801-abae777a-0d75-4b05-ac16-6dee7837f793.png\">\r\n\r\n\r\n# After\r\n<img width=\"1086\" alt=\"Screenshot 2023-05-08 at 16 13 37\" src=\"https://user-images.githubusercontent.com/1798345/236956854-8b94ca24-bffa-4a7e-a543-12c010ebb62e.png\">\r\n","mergeCommitSha":"31d1bd6c28078f99290ee1f80852b3a41d77cca5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6080","title":"Fix GitLab settings page","createdAt":"2023-05-08T23:12:27Z"}
{"state":"Merged","mergedAt":"2023-05-08T23:27:00Z","number":6081,"body":"Revert change to visibility controller.\r\n\r\nChangeTypes are necessary as we are unexpectedly sending false visibleFlow to controller.","mergeCommitSha":"6cded797b8a8af292abc4a76eff494f5c518aa35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6081","title":"Revert VisibilityControllerCHange","createdAt":"2023-05-08T23:16:41Z"}
{"state":"Merged","mergedAt":"2023-05-11T05:29:08Z","number":6082,"mergeCommitSha":"524609b5b2f01c0577ef04369bccf575e8b982b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6082","title":"Persist the all repos selection in patchInstallation API","createdAt":"2023-05-08T23:19:39Z"}
{"state":"Merged","mergedAt":"2023-05-09T00:06:26Z","number":6083,"body":"fixes: UNB-802","mergeCommitSha":"7af9cdcaa1e2e5cb6334334966ddd92d6cdb7b95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6083","title":"Git blame should not treat root commits as boundary commits","createdAt":"2023-05-08T23:50:10Z"}
{"state":"Merged","mergedAt":"2023-05-09T16:29:29Z","number":6084,"body":"Adding the necessary calls to suck down all issues from the GitHub api. Note that since all pull requests are issues, this new method will filter out issues that are pull requests.","mergeCommitSha":"3611872a5cf6dc8dc020a0c984762adffb6cbcc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6084","title":"Add GitHubAppApi.allIssues for retrieving all issues","createdAt":"2023-05-08T23:58:46Z"}
{"state":"Merged","mergedAt":"2023-05-09T02:21:16Z","number":6085,"mergeCommitSha":"fb6f85b4477a91b7afd8411ea4fc04effe59d16b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6085","title":"Add dummy semantic ui","createdAt":"2023-05-09T02:05:53Z"}
{"state":"Merged","mergedAt":"2023-05-09T18:24:05Z","number":6086,"mergeCommitSha":"3eee25b91a08062762c9b20d49383a39b014d724","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6086","title":"Optmiize document retrieval","createdAt":"2023-05-09T18:13:02Z"}
{"state":"Merged","mergedAt":"2023-05-09T18:54:55Z","number":6087,"mergeCommitSha":"009a43aa4d3c7e1fcdda2a0f8a7f0fc738a3db12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6087","title":"Do not show linear","createdAt":"2023-05-09T18:43:33Z"}
{"state":"Merged","mergedAt":"2023-05-09T19:19:47Z","number":6088,"body":"Fixes the issue where gutter icons would become doubled","mergeCommitSha":"abe4a37de5887f5bc46e99152291da8d20886380","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6088","title":"Fix inner/outer coroutine joining in JetBrains","createdAt":"2023-05-09T18:49:15Z"}
{"state":"Merged","mergedAt":"2023-05-09T20:55:13Z","number":6089,"body":"Lots of refactoring here because a GitHub issue comment is the same as a top-level PR comment, minus the pull request properties.","mergeCommitSha":"86bbc47bacd5c78eee888584c137d612e3482b4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6089","title":"Add ScmComment type for GitHub issue comments","createdAt":"2023-05-09T19:09:08Z"}
{"state":"Merged","mergedAt":"2022-03-18T21:53:32Z","number":609,"mergeCommitSha":"eb3a3ceeea1df7b875735c904301cfa0d2dd0b72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/609","title":"Add lastMessageCreatedAt to Thread API","createdAt":"2022-03-18T03:29:31Z"}
{"state":"Merged","mergedAt":"2023-05-09T20:22:42Z","number":6090,"mergeCommitSha":"2609259faef9f4fb3cd463ba07ed29137adfab4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6090","title":"Organize API","createdAt":"2023-05-09T19:16:00Z"}
{"state":"Merged","mergedAt":"2023-05-09T20:17:33Z","number":6091,"body":"When restarting the sidebar tool windows, make sure maps are clean and loadingView state is reset. Hopefully makes sure that the loading view is cleaned up.\r\n\r\nWhen new agent is instantiated, make sure its updated with editor events. Will help populate explorer insights panel on init","mergeCommitSha":"c6dfe5c1e1293c3115b4d39c313417318ed0a4ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6091","title":"Extra safety agent restart","createdAt":"2023-05-09T19:34:13Z"}
{"state":"Merged","mergedAt":"2023-05-09T20:57:42Z","number":6092,"mergeCommitSha":"f51ec51f6414c20bd45cfb61e927a709feb905c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6092","title":"Migrate installations from slackteams","createdAt":"2023-05-09T19:48:36Z"}
{"state":"Closed","mergedAt":null,"number":6093,"body":"Client must send the `rootCommitSha` in order to find the correct repo.","mergeCommitSha":"28bba9c278167dec5ea2b2cb0b4447a232cb4cc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6093","title":"Require rootCommitSha in findRepo","createdAt":"2023-05-09T20:33:38Z"}
{"state":"Merged","mergedAt":"2023-05-09T23:35:57Z","number":6094,"body":"No behaviour change. Just renaming.","mergeCommitSha":"a602ca948cd08838ddcf4a9af5d0fbed96160ded","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6094","title":"Re-organize installations","createdAt":"2023-05-09T20:33:41Z"}
{"state":"Merged","mergedAt":"2023-05-09T22:31:36Z","number":6095,"mergeCommitSha":"0f44f6055e4e27ded976f01c600ca25bb51fecd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6095","title":"ADd linear type","createdAt":"2023-05-09T21:41:30Z"}
{"state":"Merged","mergedAt":"2023-05-09T23:26:15Z","number":6096,"mergeCommitSha":"262ce7367f1566ab6b69c0a5c780759245ec827e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6096","title":"Make updates synchronous","createdAt":"2023-05-09T23:02:57Z"}
{"state":"Merged","mergedAt":"2023-05-10T15:57:17Z","number":6097,"body":"This will allow us to link this newly created Jira identity to the SCM identity of the person oauth-ing","mergeCommitSha":"a0957d16874f22e1be936110fb85a75a7cf9b10e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6097","title":"Set Identity.personId for Jira after oauth","createdAt":"2023-05-09T23:25:04Z"}
{"state":"Merged","mergedAt":"2023-05-10T00:58:35Z","number":6098,"body":"We should be using personId as the connection point for all integration identities\r\n\r\nThe extra associatedIdentityId was unnecessary...","mergeCommitSha":"d929470d8d6261db3e04bfc7312f2190f0abcd77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6098","title":"Richie exposed stupidity on the part of Rashin","createdAt":"2023-05-10T00:14:51Z"}
{"state":"Merged","mergedAt":"2023-05-10T00:23:04Z","number":6099,"mergeCommitSha":"528945d5e98c28f319f09210f382567a0a30fdfa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6099","title":"Remove short circuit from update code","createdAt":"2023-05-10T00:22:22Z"}
{"state":"Merged","mergedAt":"2022-01-18T19:48:48Z","number":61,"mergeCommitSha":"b8ef99dda96f258fa9dec4e6f04b8a2bf66f7533","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/61","title":"Remove yaml anchors","createdAt":"2022-01-18T19:23:30Z"}
{"state":"Merged","mergedAt":"2022-03-18T21:00:29Z","number":610,"body":"Includes some changes to the proto spec","mergeCommitSha":"1a47cdf98079c5d780f34fbb410033bb5df6e858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/610","title":"Convert GitHub pull request markdown to message proto ","createdAt":"2022-03-18T06:58:45Z"}
{"state":"Merged","mergedAt":"2023-05-16T16:38:59Z","number":6100,"body":"For our custom hooks that take in a dependency list, enforce that the dependency list is correct.\r\n\r\nThere is no way for eslint to automatically determine what is a React hook, so we have to declare them explicitly in the eslint config.  I added all functions using the `DependencyList` type.","mergeCommitSha":"1187446009619fe9a915ebec3b5203d9929b56b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6100","title":"Enforce dependencies in custom React hooks","createdAt":"2023-05-10T00:33:57Z"}
{"state":"Merged","mergedAt":"2023-05-10T02:21:44Z","number":6101,"mergeCommitSha":"b24c38a51e5d42a3e31cf3645ee7d6efe00934da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6101","title":"Update ui","createdAt":"2023-05-10T02:01:17Z"}
{"state":"Merged","mergedAt":"2023-05-10T03:51:41Z","number":6102,"mergeCommitSha":"b3cd45a6d82fe4a7dec64b78d2aca790580bd6c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6102","title":"Add references","createdAt":"2023-05-10T03:41:12Z"}
{"state":"Merged","mergedAt":"2023-05-11T04:41:46Z","number":6103,"mergeCommitSha":"06a5e14be481638e014c3749226c681ec1f7ba8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6103","title":"API docs in adminweb","createdAt":"2023-05-10T05:26:27Z"}
{"state":"Closed","mergedAt":null,"number":6104,"body":"This secret will be used by Keda scaling objects described here: https://keda.sh/docs/2.10/scalers/activemq/\r\n\r\nThe secret yaml looks like this:\r\n\r\n```\r\napiVersion: v1\r\nkind: Secret\r\n\r\nmetadata:\r\n  name: keda-scaler-secrets-env\r\n  namespace: default\r\n\r\ntype: Opaque\r\n\r\nstringData:\r\n  activemq-password: 'PASSWORD_GOES_HERE'\r\n  activemq-username: 'USERNAME_GOES_HERE'\r\n```","mergeCommitSha":"b58adf2325fc4b8e37f9990653044d5859367cb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6104","title":"Adding activemq secret for Keda autoscalers","createdAt":"2023-05-10T17:09:20Z"}
{"state":"Merged","mergedAt":"2023-05-10T20:17:28Z","number":6105,"body":"Also, the shared secret used to sign JWTs for making Jira API requests will be stored on the installation model encrypted.","mergeCommitSha":"98036625270e1d9bb5d1caf5fa04cb7626997ef3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6105","title":"Create Installation model for jira on installation webhook","createdAt":"2023-05-10T17:28:54Z"}
{"state":"Merged","mergedAt":"2023-05-10T22:29:50Z","number":6106,"mergeCommitSha":"28eac86dda0e8af78aa8f25446db1810b7e6b317","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6106","title":"Funnel in missing connection on installation","createdAt":"2023-05-10T18:12:41Z"}
{"state":"Merged","mergedAt":"2023-05-10T19:56:17Z","number":6107,"body":"Ran into situation where we had multiple \"teams\" but only a single one installed.\r\n\r\nThis meant the poller was still active even after we moved to the installed state.\r\nFix is to disable poller when user clicks continue on installer flow and moves to installed state.\r\n\r\nThis issue only manifests when you have multiple repos open in IntelliJ","mergeCommitSha":"35c4b0a768888d1b7ad17eb8c59aaf9ffed80e39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6107","title":"Stop Polling Installations on installed","createdAt":"2023-05-10T18:42:08Z"}
{"state":"Merged","mergedAt":"2023-05-10T22:22:38Z","number":6108,"body":"Add first cuts of onboarding integration routes and APIs\r\n* Note: these are orphaned routes that aren't connected to anything yet\r\n<img width=\"870\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/9828b150-4614-4376-9456-5a205bc9a4d0\">\r\n<img width=\"767\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/497d4287-59ac-465a-9734-81ef852522d2\">\r\n<img width=\"705\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/ad25e5e1-6b7d-4e9b-b5c9-7cd8bde5f709\">\r\n<img width=\"719\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/ec0a7a2f-e33e-45a3-8f36-c58abcf9a16a\">\r\n\r\n--- \r\n\r\nOutstanding work:\r\n- [ ] Ability to uninstall integration (not yet implemented on the backend)\r\n- [ ] JIRA configuration APIs (https://github.com/NextChapterSoftware/unblocked/pull/6050) -- for both onboarding and settings\r\n- [ ] Missing connection on the integration installation model (https://github.com/NextChapterSoftware/unblocked/pull/6106)\r\n","mergeCommitSha":"dfedc04379bc8b0814c912abe7982bb797748ce6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6108","title":"Connect integrations UI/first pass","createdAt":"2023-05-10T18:58:02Z"}
{"state":"Merged","mergedAt":"2023-05-10T19:25:00Z","number":6109,"body":"\"Unblocked helps developers find answers to questions while they implement new features, fix bugs or get up to speed on a new codebase.\"\r\n\r\n@dennispi @benedict-jw can edit this directly in this PR and it will show up on the page.","mergeCommitSha":"27b29ac14189282776907f2b86ef11619567c81c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6109","title":"Updating the download + invite pages to use the new language","createdAt":"2023-05-10T19:06:33Z"}
{"state":"Merged","mergedAt":"2022-03-18T18:08:21Z","number":611,"body":"- `TransactionVendorInterface` was duplicated so removed it \r\n- Moved the hashing stuff to security package under `utils` project\r\n\r\nRan tests locally and all passed ","mergeCommitSha":"8afa8a542609a1e4b4b7a50c2b47d851b308be64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/611","title":"Cleanup leftover shared core","createdAt":"2022-03-18T17:07:56Z"}
{"state":"Merged","mergedAt":"2023-05-10T19:34:58Z","number":6110,"body":"Similar to #6109.","mergeCommitSha":"94533f10d0e539e7916690189eaa6974de041a4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6110","title":"Update installer text","createdAt":"2023-05-10T19:32:35Z"}
{"state":"Merged","mergedAt":"2023-05-10T20:04:32Z","number":6111,"body":"This determines webhook destination.","mergeCommitSha":"a8a4c13eb85bd9b7ab621f73b48c4af215022f75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6111","title":"Set the correct baseUrl for our Atlassian Connect app descriptor","createdAt":"2023-05-10T19:57:30Z"}
{"state":"Merged","mergedAt":"2023-05-11T00:04:01Z","number":6112,"mergeCommitSha":"58f71e0ac00eb7f92166f3ef14ef6874ec9c5c93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6112","title":"Add grpc retries for pinecone","createdAt":"2023-05-10T20:23:27Z"}
{"state":"Merged","mergedAt":"2023-05-10T20:58:25Z","number":6113,"body":"…tor (#6111)\"\r\n\r\nThis reverts commit a8a4c13eb85bd9b7ab621f73b48c4af215022f75.","mergeCommitSha":"4ef573a5efaf515b65b4d6e09cfc18fc3cf7d927","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6113","title":"Revert \"Set the correct baseUrl for our Atlassian Connect app descrip…","createdAt":"2023-05-10T20:58:15Z"}
{"state":"Merged","mergedAt":"2023-05-10T21:15:14Z","number":6114,"body":"This determines webhook destination.","mergeCommitSha":"d8f6b0012a6bf385a51f8fb91d2a450836ddef86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6114","title":"Set the correct baseUrl for our Atlassian Connect app descriptor","createdAt":"2023-05-10T21:07:00Z"}
{"state":"Merged","mergedAt":"2023-05-10T21:45:49Z","number":6115,"body":"- https://admin.prod.getunblocked.com/teams/6e722471-e284-49ca-a005-f5b7a8d78c0f/members/Past\r\n- https://github.com/XRPLF","mergeCommitSha":"0fae12f4faee6f1b589d8feb03652d17d1b42bf5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6115","title":"Hack 2 GitHub members into the XRP org","createdAt":"2023-05-10T21:10:57Z"}
{"state":"Merged","mergedAt":"2023-05-10T22:11:58Z","number":6116,"body":"I missed a couple of mocks.","mergeCommitSha":"6bc1c521e54b475776e303749a5d7baf2cb899be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6116","title":"Fix JiraIssueBulkIngestionServiceTest","createdAt":"2023-05-10T21:57:44Z"}
{"state":"Merged","mergedAt":"2023-05-10T22:27:18Z","number":6117,"mergeCommitSha":"bdd5816131d03c5ce1b14421be17c8e447ab7fcb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6117","title":"Update ThreadIcon color for linear","createdAt":"2023-05-10T22:12:45Z"}
{"state":"Merged","mergedAt":"2023-05-15T20:25:34Z","number":6118,"body":"Before\r\n<img width=\"774\" alt=\"CleanShot 2023-05-10 at 15 52 22@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/7e72eb5a-4a95-4965-a9ae-ec985e254d46\">\r\n\r\nAfter\r\n<img width=\"647\" alt=\"CleanShot 2023-05-10 at 15 52 07@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/d272f165-b7a9-4481-9ccf-6f02645c5976\">\r\n","mergeCommitSha":"9d75e840fb6cecf68bf8227d3b928190f08160d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6118","title":"Fix tab video headers","createdAt":"2023-05-10T22:53:33Z"}
{"state":"Merged","mergedAt":"2023-05-11T16:23:06Z","number":6119,"body":"`defaults write com.nextchaptersoftware.UnblockedHub EnableSearch YES`\r\n\r\n<img width=\"544\" alt=\"CleanShot 2023-05-10 at 16 09 26@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/858772/2442d806-aa6d-413e-a293-f9afa6fa5a1f\">\r\n","mergeCommitSha":"4dbd2346a286aebc5bfe8929e95b1057e243df5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6119","title":"New search experience","createdAt":"2023-05-10T23:08:53Z"}
{"state":"Merged","mergedAt":"2022-03-24T16:35:25Z","number":612,"body":"<img width=\"1022\" alt=\"CleanShot 2022-03-18 at 12 00 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/159067804-f6d3b010-c9a5-471c-8007-e630be04592c.png\">\r\n\r\nMatch email from git to identity in team member using hashed emails.","mergeCommitSha":"4a11e707f96858341068e04ae002309c9a4e0daf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/612","title":"Setup Identity Matcher","createdAt":"2022-03-18T19:01:27Z"}
{"state":"Merged","mergedAt":"2023-05-11T00:41:47Z","number":6120,"body":"Was reduced temporarily to 1 hour in #6115.\n\nResetting back now.","mergeCommitSha":"2b04159f17869a3c93165b419d7fd5376553b6b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6120","title":"Reset GitHubInstallationMaintenanceJob frequency","createdAt":"2023-05-11T00:40:41Z"}
{"state":"Merged","mergedAt":"2023-05-11T01:55:34Z","number":6121,"mergeCommitSha":"16f464c2ca852867379565c898c1ca59b86b6c6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6121","title":"Add ml stack","createdAt":"2023-05-11T00:46:17Z"}
{"state":"Merged","mergedAt":"2023-05-11T01:48:01Z","number":6122,"mergeCommitSha":"38437a2fddda7d285198b3b44695381add1a964c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6122","title":"Update getInsightCounts to include counts for associated members","createdAt":"2023-05-11T01:35:28Z"}
{"state":"Merged","mergedAt":"2023-05-11T17:28:31Z","number":6123,"body":"Browsing insights requires that you hover over each insight to see its topics/tags.\r\nThis change fixes that when there are 3 or fewer tags, the typical case.\r\n\r\n\r\n## Example: Desktop showing tags list expanded when 3 or less.\r\n\r\n<img width=\"986\" alt=\"Screenshot 2023-05-10 at 18 51 20\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/6f8d2acd-4ff1-494f-8e32-94880b537a91\">\r\n\r\n\r\n## Example: Smallest viewport showing tags list expanded when 3 or less.\r\n\r\n<img width=\"618\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/7acccf66-f024-46f7-b48e-8cb454f7e13e\">","mergeCommitSha":"423f363d1fe4ce4a9e22b50f08b9dc66c85bc7e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6123","title":"Show up to three tags by default","createdAt":"2023-05-11T01:47:39Z"}
{"state":"Merged","mergedAt":"2023-05-11T03:34:30Z","number":6124,"mergeCommitSha":"5729f8d5fef5fbd8265c994586da2b5993f8a20b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6124","title":"listInstallationRepos correctly populates the allowAllRepos flag","createdAt":"2023-05-11T02:01:33Z"}
{"state":"Merged","mergedAt":"2023-05-11T04:37:22Z","number":6125,"body":"Fixes the search filter/count dropdown in search for linear.\r\n\r\nSee for example linear is missing in filter and counts:\r\n\r\n<img width=\"1145\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/112849e2-01a4-4bee-a587-8a603b329630\">\r\n","mergeCommitSha":"fcdad9b788f9c8b51a3f0ab6d23baa1d7b1a522d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6125","title":"Fix Linear search in web","createdAt":"2023-05-11T03:30:20Z"}
{"state":"Merged","mergedAt":"2023-05-11T07:32:51Z","number":6126,"mergeCommitSha":"234cfeec0a1ef7a9f26ebbaa25c596a98822d78b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6126","title":"Add sagemaker inference endpoint for ml","createdAt":"2023-05-11T07:32:30Z"}
{"state":"Merged","mergedAt":"2023-05-11T07:48:06Z","number":6127,"mergeCommitSha":"0516399187d431003c1c69ec05db8cd87b5c07a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6127","title":"Remvoe secret","createdAt":"2023-05-11T07:48:01Z"}
{"state":"Merged","mergedAt":"2023-05-11T07:59:35Z","number":6128,"mergeCommitSha":"6fa76b31ac01e2c4e2e3c9b026e58de40511f086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6128","title":"Increase deployment time","createdAt":"2023-05-11T07:59:28Z"}
{"state":"Merged","mergedAt":"2023-05-16T20:58:24Z","number":6129,"mergeCommitSha":"4cfa6d206d5c1fa815602a3454ae8d6d8d9a98d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6129","title":"JetBrains: Editor should not scroll when clicking on an insight bubble","createdAt":"2023-05-11T16:47:09Z"}
{"state":"Merged","mergedAt":"2022-03-21T23:00:25Z","number":613,"mergeCommitSha":"3944df15f715bfe873ea06b666ff33218b3add29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/613","title":"Pass file extension as language id on web","createdAt":"2022-03-18T20:35:40Z"}
{"state":"Merged","mergedAt":"2023-05-11T17:37:00Z","number":6130,"mergeCommitSha":"16da87f4aeb35132d72b04c51c68b1a9714af72c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6130","title":"Cleanup API doc tags","createdAt":"2023-05-11T17:36:37Z"}
{"state":"Merged","mergedAt":"2023-05-11T19:16:27Z","number":6131,"body":"Given a team, provider the ability to get the current SCM installation,\nand the current SCM installation's repos.","mergeCommitSha":"d44c4bb0664beefe02fd95273cf27503648250c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6131","title":"Add a team specific SCM installation API","createdAt":"2023-05-11T17:51:07Z"}
{"state":"Merged","mergedAt":"2023-05-11T19:22:33Z","number":6132,"mergeCommitSha":"cd6765415df06c4b02e58f66f7e0bf7b5b239e94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6132","title":"Remove disconnect installation implementation","createdAt":"2023-05-11T18:20:53Z"}
{"state":"Merged","mergedAt":"2023-05-15T17:11:53Z","number":6133,"body":"Behaviour matches VSCode now.  This is a workaround to allow enough time for the source mark to resolve correctly.\r\n\r\n<img width=\"1512\" alt=\"Screenshot 2023-05-11 at 11 27 28 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/ff35e8bb-de99-4f91-8b62-c54f42d51a1e\">\r\n","mergeCommitSha":"9505abf4d85cb155f3a18b8c16583a4dadfa2593","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6133","title":"JetBrains: show 'View' popup after creating note","createdAt":"2023-05-11T18:28:24Z"}
{"state":"Merged","mergedAt":"2023-05-11T20:51:33Z","number":6134,"mergeCommitSha":"384cbe44d7c6068e8d159bdd12a140d9e43e7e75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6134","title":"Implement team-specific installation APIs","createdAt":"2023-05-11T18:43:12Z"}
{"state":"Merged","mergedAt":"2023-05-11T18:51:37Z","number":6135,"mergeCommitSha":"795ef9e30082b01abe0ccd942d0bd068cb85f108","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6135","title":"Fixes the installer build because it uses Xcode 14.2","createdAt":"2023-05-11T18:49:08Z"}
{"state":"Merged","mergedAt":"2023-05-15T16:55:24Z","number":6136,"body":"This will let us construct a link to the issue in the customer's Jira site (it has the form https://<JIRA_SITE_NAME>.atlassian.net/browse/<JIRA_ISSUE_KEY>)","mergeCommitSha":"14a08652e14672df32c8769cfe068d3dc3575d54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6136","title":"Store Jira issue key on ThreadModel","createdAt":"2023-05-11T18:52:45Z"}
{"state":"Merged","mergedAt":"2023-05-11T19:57:46Z","number":6137,"body":"<img width=\"1171\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/96260b31-566b-43f7-af37-cd9a97bb88c4\">\r\n","mergeCommitSha":"7aca82b53af4a59bbcecde9eb2d70f3c32a7b179","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6137","title":"Fix unstyled unconnected state","createdAt":"2023-05-11T19:14:39Z"}
{"state":"Merged","mergedAt":"2023-05-11T20:41:35Z","number":6138,"body":"Becasue the client will poll this endpoint to know when to transition to the Jira project configuration page.","mergeCommitSha":"5a7d85bffa0fa05caac94d980c4741a138119538","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6138","title":"Fix the getJiraSites operation to not return disconnected sites","createdAt":"2023-05-11T20:30:40Z"}
{"state":"Merged","mergedAt":"2023-05-15T23:01:08Z","number":6139,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1683831304469149","mergeCommitSha":"bdd22072707998ec23ce2b7c1c53de9cc412922b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6139","title":"Add ThreadType.jira enum","createdAt":"2023-05-11T20:53:28Z"}
{"state":"Merged","mergedAt":"2022-03-18T22:40:12Z","number":614,"body":"If a channel doesn't support polling (ie, `getTeamMembers`), the client would fetch anyways.  With this change, if a service doesn't respond with a `lastModified` timestamp for a channel, we assume that channel doesn't support polling and we don't fetch.","mergeCommitSha":"65b745f3542ea3388a924173a8d8a89e7934cb89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/614","title":"Fix unnecessary channel polling","createdAt":"2022-03-18T20:47:35Z"}
{"state":"Merged","mergedAt":"2023-05-15T23:08:11Z","number":6140,"body":"followup for https://github.com/NextChapterSoftware/unblocked/pull/6123. It results in the same behaviour, just moved the logic out so that we can control how many to display via prop","mergeCommitSha":"a024996cc7f8d6c0a67e56aefd90f82bfdf0a80e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6140","title":"Refactor pill collapsing to prop","createdAt":"2023-05-11T20:57:32Z"}
{"state":"Merged","mergedAt":"2023-05-11T21:32:10Z","number":6141,"mergeCommitSha":"7545caa6e0a51ee380a8483630b0951c41cb4b5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6141","title":"Uninstall installation","createdAt":"2023-05-11T20:58:32Z"}
{"state":"Merged","mergedAt":"2023-05-11T21:56:35Z","number":6142,"mergeCommitSha":"84193d65a544d17e662abe0de65fdf5cc90846c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6142","title":"Send ClientType to hub","createdAt":"2023-05-11T21:00:35Z"}
{"state":"Merged","mergedAt":"2023-05-11T22:07:21Z","number":6143,"mergeCommitSha":"5c0bba03936e231da6d238a36eda05e466a76040","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6143","title":"Connect SCM installation returns an installation object","createdAt":"2023-05-11T21:22:58Z"}
{"state":"Merged","mergedAt":"2023-05-11T22:28:04Z","number":6144,"mergeCommitSha":"cff0c0e07bd55dc1d736d238da9686702e2f3077","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6144","title":"Configure semantic search completion type","createdAt":"2023-05-11T21:32:11Z"}
{"state":"Merged","mergedAt":"2023-05-11T22:34:45Z","number":6145,"mergeCommitSha":"15bbb52591faf940a976468a1b6070b4f8eae77e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6145","title":"Reinstall selected repos","createdAt":"2023-05-11T21:44:24Z"}
{"state":"Merged","mergedAt":"2023-05-15T17:45:03Z","number":6146,"body":"Remaining project items blocking this:\r\nhttps://linear.app/unblocked/project/bitbucket-53371e29e88e","mergeCommitSha":"e9805eb02982d1ea0cb9d871e6c41bd056bf8531","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6146","title":"Enable Bitbucket in PROD","createdAt":"2023-05-11T21:47:24Z"}
{"state":"Merged","mergedAt":"2023-05-12T01:23:05Z","number":6147,"mergeCommitSha":"9472e3bbade5dbba8274a2845b7415139754c3e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6147","title":"Announce web questions to slack","createdAt":"2023-05-11T22:24:43Z"}
{"state":"Merged","mergedAt":"2023-05-11T23:02:10Z","number":6148,"body":"Original PR ✅\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/6143\r\n\r\nStacked PR ✅\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/6145\r\n\r\nBut when each merged, created a conflict by duplicating functions.","mergeCommitSha":"eebdbb0d81f5a067d63227cd15deefdcf1daf3d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6148","title":"Fix merge conflict","createdAt":"2023-05-11T22:58:37Z"}
{"state":"Merged","mergedAt":"2023-05-11T23:54:13Z","number":6149,"mergeCommitSha":"df32aadcb8cdeec8f4112e7bc39790d09a65b58e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6149","title":"Fix iframe scroll on API docs page","createdAt":"2023-05-11T23:41:18Z"}
{"state":"Merged","mergedAt":"2022-03-18T21:23:14Z","number":615,"mergeCommitSha":"cf64de38601324a4ee5c15c91d14c1726ebeb159","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/615","title":"adding new docker repo for scm service","createdAt":"2022-03-18T21:01:38Z"}
{"state":"Closed","mergedAt":null,"number":6150,"mergeCommitSha":"e210c9224add07afe28c19e54ae385a76c2304b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6150","title":"This PR should fail","createdAt":"2023-05-11T23:47:51Z"}
{"state":"Merged","mergedAt":"2023-05-16T16:59:14Z","number":6151,"body":"* Add Jira configuration \r\n<img width=\"727\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/49172031-b541-4022-9a61-f113b1627424\">\r\n\r\n* Update slack settings to use the new installation APIs; remove `slackInstall` APIs from the client\r\n\r\n* Note: I haven't added Jira settings to the team settings dashboard yet; will do this once we surface Jira to external users\r\n","mergeCommitSha":"68bac103e7ab841966d186f942eab591186bdd28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6151","title":"[Onboarding] Integrate Jira APIs/configuration","createdAt":"2023-05-12T00:01:56Z"}
{"state":"Merged","mergedAt":"2023-05-12T15:53:02Z","number":6152,"body":"```\r\n1. remove first page — Use getTeamScmInstallation\r\n2. no Save response :white_check_mark:\r\n3. centering of dialog as above\r\n4. cannot remove all repos — allow it for now [LATER: put a banner/warning on the dashboard to deeplink to repo selection page]\r\n5. should disable Save button until changes to be made (dirty)\r\n6. transitioning from selecting a subset of repos to all repos, then back to select repos option does not re-fetch the entire repo list.\r\n7. Repo selection vertical scroll\r\n```\r\n\r\n<img width=\"937\" alt=\"CleanShot 2023-05-11 at 17 14 21@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/1f1aeec3-b424-48b1-98f4-d3252ceb9c52\">\r\n","mergeCommitSha":"a0d2155c0487c90da44d85371c9f24dbbb15f8e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6152","title":"Updates to Repo settings","createdAt":"2023-05-12T00:14:58Z"}
{"state":"Merged","mergedAt":"2023-05-12T05:04:56Z","number":6153,"mergeCommitSha":"6c8f56085aef8b19b636017aafb40947409cff20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6153","title":"Remove reference to GitHub App, should be SCM agnostic","createdAt":"2023-05-12T00:29:20Z"}
{"state":"Merged","mergedAt":"2023-05-12T05:26:41Z","number":6154,"body":"So that we know which identity to use if we need to fall back to using the oauth token for Jira ingestion.","mergeCommitSha":"6185c270a8817a6bf8321d8f7254adc0e8fdb10f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6154","title":"Create a team member for the Jira user after oauth","createdAt":"2023-05-12T04:25:20Z"}
{"state":"Merged","mergedAt":"2023-05-17T17:32:03Z","number":6155,"body":"When opening a thread from the Hub, we typically depend on the clients to update the unread status.\r\n\r\nSince we're moving away from the web extension, we need the hub to set unread on threads opened up in GH.","mergeCommitSha":"6b3797dbe3043da6181825bcac168b81189ef5c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6155","title":"Update unread status","createdAt":"2023-05-12T16:30:56Z"}
{"state":"Merged","mergedAt":"2023-05-12T17:45:46Z","number":6156,"mergeCommitSha":"8c8f9d82db68910f61beeb9553c9f09342072e0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6156","title":"Collapse the inbox view when search is focused","createdAt":"2023-05-12T17:43:55Z"}
{"state":"Merged","mergedAt":"2023-05-12T18:51:29Z","number":6157,"body":"This is part 1 of trying to reduce the GH cache cost. Gateway endpoint should be transparent to the service and not require any code changes. It does the magic using VPC DNS. ","mergeCommitSha":"0f8cfe50b015b138b2c6b5921ae4f620c67abc25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6157","title":"enable s3 vpc gateway endpoint in secops core vpn","createdAt":"2023-05-12T18:44:25Z"}
{"state":"Merged","mergedAt":"2023-05-17T17:22:29Z","number":6158,"body":"Updates Github Settings to use InstallURL instead of repo selector\r\n\r\n<img width=\"1365\" alt=\"CleanShot 2023-05-12 at 12 09 46@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/daf74bc1-40ce-40e7-8ecd-4bab0cd308eb\">\r\n","mergeCommitSha":"43bca467dbccf1663761d59e7209b7ec38adb187","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6158","title":"Update Github Repo to use InstallUrlContent","createdAt":"2023-05-12T19:11:39Z"}
{"state":"Merged","mergedAt":"2023-05-15T23:10:38Z","number":6159,"body":"<img width=\"1497\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/1b3d5a11-2504-403f-9628-d8f09d3be56e\">\r\n<img width=\"1490\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/d5611211-a5d3-4224-b3c9-ea9737b257bd\">\r\n","mergeCommitSha":"faf8e52b4e180eb94a82bd32140fd3146bbbdbd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6159","title":"[Onboarding] Add download mac app views","createdAt":"2023-05-12T19:20:42Z"}
{"state":"Merged","mergedAt":"2022-03-18T22:24:17Z","number":616,"body":"Some additional dependency refactoring can be done for common classes. See file diff for explanation about which files were copied as-is and which were modified from apiservice","mergeCommitSha":"5d1b8131d8fa886b64a999c2e40f75615722ebb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/616","title":"Spin up scm service","createdAt":"2022-03-18T21:11:17Z"}
{"state":"Merged","mergedAt":"2023-05-12T20:18:46Z","number":6160,"body":"Pre-commit is a great library for a whole collection of pre-commit checks...","mergeCommitSha":"80778bf66d021ef1b8ec5a6df83867b06959b7fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6160","title":"Add pre-commit checks","createdAt":"2023-05-12T19:29:02Z"}
{"state":"Merged","mergedAt":"2023-05-12T20:25:00Z","number":6161,"mergeCommitSha":"77ba643b8337a0a3620f95d3f56fde670ed45785","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6161","title":"Fix logo glitch","createdAt":"2023-05-12T20:21:08Z"}
{"state":"Merged","mergedAt":"2023-05-13T03:35:55Z","number":6162,"body":"Uses the new SemanticSearchAPI in UnblockedMobile.","mergeCommitSha":"b11244ec955002684042d90470af7b6067548735","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6162","title":"Replacing Doug-sandbox with a Rashin-machine","createdAt":"2023-05-12T20:33:37Z"}
{"state":"Merged","mergedAt":"2023-05-13T00:11:06Z","number":6163,"mergeCommitSha":"c91f218f240325fb67538d0e198bbd4740c05d9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6163","title":"this removes cache items older than 30 days","createdAt":"2023-05-12T20:54:44Z"}
{"state":"Merged","mergedAt":"2023-05-13T01:17:37Z","number":6164,"mergeCommitSha":"52a4588e95dd411363a40c76bfb4a4267cdf1abf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6164","title":"[BREAKS API ON MAIN] Add  redirect","createdAt":"2023-05-13T00:50:01Z"}
{"state":"Merged","mergedAt":"2023-05-13T03:35:01Z","number":6165,"body":"- Add redirecturl\r\n- Update\r\n- Trigger\r\n- Add git modules\r\n","mergeCommitSha":"f55405cf265af72db4d6035ab76e9619e080ca8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6165","title":"Upload source code submodule","createdAt":"2023-05-13T01:28:39Z"}
{"state":"Merged","mergedAt":"2023-05-14T15:46:26Z","number":6166,"mergeCommitSha":"15c088e62d0a115c916b612ff1c0f7ae4bfd1f53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6166","title":"Use a single queue for jira events","createdAt":"2023-05-14T06:31:49Z"}
{"state":"Merged","mergedAt":"2023-05-14T17:27:41Z","number":6167,"body":"To ingest Jira projects to show on the configuration page during onboarding.","mergeCommitSha":"d36ca4c5e364fc6d8ce07b21c27eca120b3e3fca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6167","title":"Emit JiraProjectIngestionEvent during oauth","createdAt":"2023-05-14T17:02:15Z"}
{"state":"Merged","mergedAt":"2023-05-14T18:31:41Z","number":6168,"body":"To allow for fallback to using user auth when the marketplace app isn't installed.","mergeCommitSha":"ff0a354d03f0ebaa3ededb31faa98f613d86a3f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6168","title":"Add ability to ingest projects using OAuth tokens","createdAt":"2023-05-14T18:01:39Z"}
{"state":"Merged","mergedAt":"2023-05-15T15:02:25Z","number":6169,"body":"Instead of the marketplace listing","mergeCommitSha":"429e7554d06227aab6c4179e008ec01add60d500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6169","title":"Redirect to client-provided url after jira oauth","createdAt":"2023-05-15T05:57:17Z"}
{"state":"Merged","mergedAt":"2022-03-18T21:40:11Z","number":617,"body":"Changes have been deployed to both Dev and Prod Kube clusters. ","mergeCommitSha":"4eff12e8f3e83fa622c2c1b62709bb3806f6a455","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/617","title":"added service account with access to Redis and Postgres for scm service","createdAt":"2022-03-18T21:14:46Z"}
{"state":"Merged","mergedAt":"2023-05-15T23:52:08Z","number":6170,"mergeCommitSha":"85748e1e879dac45e887dc6878b447c046cc0563","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6170","title":"Add team setting for enabling GitHub issues","createdAt":"2023-05-15T16:56:58Z"}
{"state":"Merged","mergedAt":"2023-05-15T17:09:07Z","number":6171,"mergeCommitSha":"c929e5375dc2ed68b3524895efbc5516c82544d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6171","title":"REmove unnecessary workflow","createdAt":"2023-05-15T17:08:57Z"}
{"state":"Merged","mergedAt":"2023-05-15T17:42:37Z","number":6172,"mergeCommitSha":"bad51fced9aa5f5b0a462c3d6edcde578c74a347","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6172","title":"Upsert installation when jira site is created","createdAt":"2023-05-15T17:23:39Z"}
{"state":"Merged","mergedAt":"2023-05-16T01:06:12Z","number":6173,"body":"Fix issue with empty explorer insights panel in IntelliJ.\r\n\r\nWas a slight race where initial editor state wasn't sent to the agent. This meant editor based streams weren't loaded and was blocking the ExplorerInsightsStream.\r\n\r\nFix by sending initial editor value from ProjectService to Agent.","mergeCommitSha":"913c7d85d6d363080d80f4996f66b8a551eafa8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6173","title":"Fix empty explorer insights","createdAt":"2023-05-15T17:30:33Z"}
{"state":"Merged","mergedAt":"2023-05-15T19:37:34Z","number":6174,"mergeCommitSha":"b342e2cd5715abb6c89bb6a3058ee5e232908f86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6174","title":"Use user oauth for issue ingestion","createdAt":"2023-05-15T18:30:41Z"}
{"state":"Merged","mergedAt":"2023-05-15T19:48:25Z","number":6175,"mergeCommitSha":"613a32bb70c17d2531a245a7f72787be1db4260a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6175","title":"Add search bar to unselected state","createdAt":"2023-05-15T18:36:19Z"}
{"state":"Merged","mergedAt":"2023-05-15T22:33:57Z","number":6176,"mergeCommitSha":"3d94c593ecacf10143563cd5746c31901857749d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6176","title":"Add JiraSite.avatarUrl","createdAt":"2023-05-15T19:13:22Z"}
{"state":"Merged","mergedAt":"2023-05-15T20:26:00Z","number":6177,"mergeCommitSha":"282311751b14d1d17524831c365af1832d3de16d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6177","title":"Slack installation avatarUrl","createdAt":"2023-05-15T19:52:07Z"}
{"state":"Merged","mergedAt":"2023-05-15T19:56:53Z","number":6178,"mergeCommitSha":"3970cd7ce5d1a7108d3aa6e78f4ac5603ad71c01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6178","title":"Small fixes","createdAt":"2023-05-15T19:56:01Z"}
{"state":"Merged","mergedAt":"2023-05-15T21:01:12Z","number":6179,"body":"Disabled states for repo selector differ based on whether or not we are onboarding.\r\n\r\nWhen onboarding, always allow saving as long as there is some data present.\r\nIn settings, only allow saving when data has changed from API provided data.","mergeCommitSha":"d1b445683389ae83da755d3795a8cb2e4924dce5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6179","title":"Fix repo onboarding state","createdAt":"2023-05-15T20:10:07Z"}
{"state":"Merged","mergedAt":"2022-03-19T03:30:46Z","number":618,"body":"The exposed framework just creates a WARN level log when schema migration fails \r\n\r\nhttps://app.logz.io/#/dashboard/kibana/discover?_a=(columns:!(message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'logzioCustomerIndex*',key:environment,negate:!f,params:(query:dev),type:phrase),query:(match_phrase:(environment:dev)))),index:'logzioCustomerIndex*',interval:auto,query:(language:lucene,query:schema),sort:!(!('@timestamp',desc)))&_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now%2Fd,to:now%2Fd))&discoverTab=logz-logs-tab&accountIds=411850&switchToAccountId=411850\r\n\r\n","mergeCommitSha":"ebb14db46801c2058ff7c9afb7a6a78b72d7c095","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/618","title":"Add error log on schema update failure","createdAt":"2022-03-18T22:33:28Z"}
{"state":"Merged","mergedAt":"2023-05-15T21:07:15Z","number":6180,"body":"…herwise states…","mergeCommitSha":"6f26f91ce666a59e296f30ba713a93a52574e916","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6180","title":"Service sshould be heavily throttled with honeycomb samples unless otherwise states…","createdAt":"2023-05-15T21:06:35Z"}
{"state":"Merged","mergedAt":"2023-05-15T22:55:34Z","number":6181,"mergeCommitSha":"5aeca724ea13493b10f829799d1a852370e0945b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6181","title":"Mark internal GitLab and Bitbucket teams","createdAt":"2023-05-15T22:18:24Z"}
{"state":"Merged","mergedAt":"2023-05-15T23:39:32Z","number":6182,"mergeCommitSha":"a7800a998d57238c70fd91cb8518dcefbb96d743","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6182","title":"Fix jira issue ingestion for user oauth","createdAt":"2023-05-15T22:37:32Z"}
{"state":"Merged","mergedAt":"2023-05-16T01:16:04Z","number":6183,"mergeCommitSha":"dbfc5655c81a3a7d7abd2e5735b380a28e67cad4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6183","title":"Eliminate unnecessary requests for idenity model information","createdAt":"2023-05-15T23:41:30Z"}
{"state":"Merged","mergedAt":"2023-05-16T16:15:06Z","number":6184,"mergeCommitSha":"012c745ecbfa8aa099d01d60760dc7380adf3bb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6184","title":"Show Jira Sites on admin console","createdAt":"2023-05-15T23:49:34Z"}
{"state":"Merged","mergedAt":"2023-05-16T20:09:58Z","number":6185,"mergeCommitSha":"fbd1b50154c2054f588ecbdc1a734b27ef896dd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6185","title":"Use a generic SCM label in global settings","createdAt":"2023-05-16T00:45:39Z"}
{"state":"Merged","mergedAt":"2023-05-17T21:03:53Z","number":6186,"body":"Tutorial checklist support in IntelliJ.\r\n<img width=\"1193\" alt=\"CleanShot 2023-05-15 at 21 55 01@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/52c63de3-b80c-4fd5-bd24-0d4cb3848d71\">\r\n","mergeCommitSha":"9eaaa325b2afd8db75c3a483f255121110c65439","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6186","title":"unb-1252 tutorialchecklist in intellij","createdAt":"2023-05-16T04:58:28Z"}
{"state":"Merged","mergedAt":"2023-05-16T16:54:55Z","number":6187,"mergeCommitSha":"e45357f278dfd0b96b1a65d5dde6129c2c822467","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6187","title":"Update scm sample rate","createdAt":"2023-05-16T16:54:47Z"}
{"state":"Merged","mergedAt":"2023-05-16T17:20:05Z","number":6188,"body":"If the user configures Jira ingestion to not ingest all projects then require them to choose at least one project.","mergeCommitSha":"3a3e0ab2ed90019a982757a2c374acc42b697099","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6188","title":"Require at least one project to be selected in the configuration","createdAt":"2023-05-16T17:00:53Z"}
{"state":"Merged","mergedAt":"2023-05-16T17:24:44Z","number":6189,"body":"IntelliJ's API for loading services is not very good.  You mark the service's scope (app or project) with a decorator, but AFAICT that doesn't do anything.  If you accidentally load an app-level scope with the project-level API, or visa-versa, it will load the service with the wrong scope.\r\n\r\nSo we were loading a couple app-level services at the project level.  This caused the upgrade bug:  BundleExtractorService is intended to be app-level, as the bundle set is the same for all projects.  The intention is to write the extracted resources out once into a temporary folder.  However when a new project was opened, a new BundleExtractorService was created.\r\n\r\nThis normally wouldn't cause a bug, but if you'd upgraded before this point, the originating jar is deleted, so this would fail.","mergeCommitSha":"d94088c4789a4f88f277ec8acc895bc2d79491cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6189","title":"Ensure app-level IntelliJ services are singletons","createdAt":"2023-05-16T17:14:26Z"}
{"state":"Merged","mergedAt":"2022-03-18T22:49:54Z","number":619,"body":"This logic needs refactoring so that it uses the stores, but just fixing this for now before we do that.","mergeCommitSha":"00799c95f638f87bcb65d93d16556e788d1b9901","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/619","title":"Update ThreadModel.lastMessageCreatedAt when ingesting PRs","createdAt":"2022-03-18T22:45:23Z"}
{"state":"Merged","mergedAt":"2023-05-16T18:20:10Z","number":6190,"body":"Theory that there may be a race condition which we tried closing a view *before* it was open.\r\nTherefore, the close fails and the \"loading\" view persists.\r\n\r\nThis could occur since invokeLater schedules the passed Runnable to be executed on the EDT at some point in the future and immediately returns.\r\n\r\nInvokeAndWait also schedules the passed Runnable to be executed on the EDT, but it blocks the current thread until the Runnable has finished executing.\r\n\r\nWe should be using InvokeAndWait as we want the closeView operations to completely finish before we try loading new views.","mergeCommitSha":"873c6c74e31ab2b2433047db489798fb87fcd87e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6190","title":"Update invokeLater to invokeAndWait","createdAt":"2023-05-16T18:08:45Z"}
{"state":"Closed","mergedAt":null,"number":6191,"body":"This reverts commit 026a8b6d\r\n\r\nKilling us in s3 costs ATM...","mergeCommitSha":"00f9aecebbfe9aa56e8299821455a1f82d0ff0a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6191","title":"Revert \"Gradle cache s3 (#5852)\"","createdAt":"2023-05-16T18:57:09Z"}
{"state":"Merged","mergedAt":"2023-05-16T19:14:06Z","number":6192,"body":"I am reverting the artifact upload and downlod to use Github cache. Since package step runs on Github Action runners they will download everything from S3 16 times per build using the global endpoint.\r\n\r\nGradle cache should still remain on S3 and since upload/downloads are done from Ec2 they should be free.","mergeCommitSha":"82147ef4d8a3fd96180471a62281cd4ff0c01ec1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6192","title":"Revert artifacts to use GH cache","createdAt":"2023-05-16T18:57:31Z"}
{"state":"Merged","mergedAt":"2023-05-16T20:30:31Z","number":6193,"mergeCommitSha":"533cf0e6cd570a1ba35828e26e49177f634e7a4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6193","title":"Re-enable type checking in JetBrains project","createdAt":"2023-05-16T19:08:50Z"}
{"state":"Merged","mergedAt":"2023-05-17T18:03:32Z","number":6194,"mergeCommitSha":"a8a359e7531ec650449494e85051fd95b8728e6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6194","title":"[Onboarding] Route connect repo to connect integrations","createdAt":"2023-05-16T19:30:49Z"}
{"state":"Merged","mergedAt":"2023-05-16T20:24:56Z","number":6195,"mergeCommitSha":"c7d4c83c38751700dd456975daafd54aa777f5ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6195","title":"Use correct provider name in open-with context menu","createdAt":"2023-05-16T20:07:29Z"}
{"state":"Merged","mergedAt":"2023-05-16T23:04:59Z","number":6196,"body":"- Add better prompt for semantic search\r\n- Add more dependendies\r\n- Update\r\n","mergeCommitSha":"60c593cf889670c9b80fb3010f637b3f87bdf3f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6196","title":"MoveToBetterPrompts","createdAt":"2023-05-16T20:36:21Z"}
{"state":"Merged","mergedAt":"2023-05-16T21:29:50Z","number":6197,"body":"Ideally this would be triggered by the gradle lint/test tasks, but I don't want to wade into gradle today","mergeCommitSha":"0ba0c1a884b904fec2243b60962cf93c80bc792f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6197","title":"Lint JetBrains TS code in CI","createdAt":"2023-05-16T20:36:23Z"}
{"state":"Merged","mergedAt":"2023-05-16T21:27:26Z","number":6198,"mergeCommitSha":"f978b91d0182d09349fa58ed01c24a74d772367e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6198","title":"Upsert installation even when jira site is not created","createdAt":"2023-05-16T21:06:17Z"}
{"state":"Merged","mergedAt":"2023-05-16T23:20:30Z","number":6199,"body":"Processing Complete email template.\r\n`openUnblockedUrl` needs to be hooked up\r\n\r\nAsset still needs to upload.\r\n\r\nLet me know if anything is missing... First time working on these emails.\r\n\r\n<img width=\"919\" alt=\"CleanShot 2023-05-16 at 14 24 24@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/d0696b59-8c15-4b33-8d18-7e46e091fcdf\">\r\n","mergeCommitSha":"d3426d72a79901a3b6b4fe42d85f62998c06e78b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6199","title":"Processing complete email template","createdAt":"2023-05-16T21:27:27Z"}
{"state":"Merged","mergedAt":"2022-01-21T17:30:12Z","number":62,"mergeCommitSha":"2ae5b1ae4a0e40df9c510335289639d3abc083c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/62","title":"Add readme for web","createdAt":"2022-01-18T19:30:07Z"}
{"state":"Merged","mergedAt":"2022-03-18T23:17:20Z","number":620,"body":"We moved most of the projects out of `apiservice`","mergeCommitSha":"08212470748f1ca2bda7f1b250bea27e68791211","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/620","title":"API service builds and deploys when changes are made to the projects folder","createdAt":"2022-03-18T23:06:33Z"}
{"state":"Merged","mergedAt":"2023-05-16T22:03:46Z","number":6200,"body":"Just DRYing up some code","mergeCommitSha":"ebefcad1b499261fbb32ded36cbd667e312ab382","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6200","title":"Create abstract IngestionLockProvider","createdAt":"2023-05-16T21:44:01Z"}
{"state":"Merged","mergedAt":"2023-05-16T22:03:54Z","number":6201,"body":"This will allow us to surface the issue number for such comments to help with ingestion of GitHub issues.","mergeCommitSha":"844f43fb422241f55a92b155de055afadbd12034","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6201","title":"Convert ScmComment to ScmIssueComment","createdAt":"2023-05-16T21:50:20Z"}
{"state":"Merged","mergedAt":"2023-05-17T16:58:43Z","number":6202,"body":"May be accessing project service once container is disposed.\r\nAdd additional safety and logging","mergeCommitSha":"216a87d98447854bb0b998a2775bf3d3f4b5a711","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6202","title":"Safer access to project service","createdAt":"2023-05-16T22:22:20Z"}
{"state":"Merged","mergedAt":"2023-05-16T22:41:06Z","number":6203,"mergeCommitSha":"ccf6b8c64ffdfc7f98fd3bec0707dd31280a7ec9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6203","title":"Set has installed hub flag after logging in","createdAt":"2023-05-16T22:32:19Z"}
{"state":"Merged","mergedAt":"2023-05-16T23:11:25Z","number":6204,"body":"- Add better prompt for semantic search\r\n- Update\r\n- Clean up\r\n- Update source\r\n- freaking prompts\r\n","mergeCommitSha":"9c9c956075d2106d002f9ed1843e0160baba3566","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6204","title":"MoveToBetterPrompts","createdAt":"2023-05-16T23:11:14Z"}
{"state":"Merged","mergedAt":"2023-05-16T23:31:31Z","number":6205,"body":"Update build range for upcoming EAP 2023.2","mergeCommitSha":"4d064a4e5ad7e636a8f992c5500794fbc2cd4add","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6205","title":"Update support for upcoming EAP","createdAt":"2023-05-16T23:21:25Z"}
{"state":"Merged","mergedAt":"2023-05-16T23:37:30Z","number":6206,"mergeCommitSha":"dc80aa5f123d8c3534ffdbe04c27728d5cbb976c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6206","title":"ADd timeout","createdAt":"2023-05-16T23:37:23Z"}
{"state":"Merged","mergedAt":"2023-05-16T23:50:18Z","number":6207,"mergeCommitSha":"fda908630000aa5af1f79a337428bcba7717ada1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6207","title":"Increase timeout","createdAt":"2023-05-16T23:50:12Z"}
{"state":"Merged","mergedAt":"2023-05-17T16:28:41Z","number":6208,"body":"Installation store should *not* update on auth refresh if it's already at its terminal state of \"installed\"\r\n\r\nShould help with bug where sidebar transitions from \"installed\" state to \"installation\" state.\r\n\r\n![image (5)](https://github.com/NextChapterSoftware/unblocked/assets/1553313/8020e604-e76b-4136-acf5-f3acd4bfabd1)\r\n![now](https://github.com/NextChapterSoftware/unblocked/assets/1553313/41a9c092-70c6-43e4-aa6e-87d6f1d8484e)\r\n\r\nFixes UNB-1282","mergeCommitSha":"b2d7c3e4a2b76e3214c785e7f42cf5c8de14e13b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6208","title":"Only update installation store when necessary","createdAt":"2023-05-16T23:59:32Z"}
{"state":"Merged","mergedAt":"2023-05-25T21:43:34Z","number":6209,"body":"<img width=\"1483\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/f6ae936d-71c5-40d1-85bd-acce4dee8f93\">\r\n\r\n* Fetch list of `pendingTeams` and propagate pending property down to views \r\n* client should redirect `/` to the pending view if `pending === true`\r\n* pending view should redirect to `/` if `!pending`","mergeCommitSha":"b3690707e37d6421bfd32d0a39b9a202811d756f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6209","title":"[Onboarding] Add pending state per team","createdAt":"2023-05-16T23:59:34Z"}
{"state":"Merged","mergedAt":"2022-03-22T00:13:53Z","number":621,"body":"When Ktor receives a SIGTERM event, it attempts a graceful shutdown by unloaded its modules and plugins. If we wire up the background job executor as a plugin, we get the benefit of Ktor's lifecycle machinery. ","mergeCommitSha":"e12f90e735729d247ef3a8dfb39c5b36f1f41213","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/621","title":"Adds ktor managed background job plugin","createdAt":"2022-03-21T05:00:07Z"}
{"state":"Merged","mergedAt":"2023-05-17T15:27:51Z","number":6210,"mergeCommitSha":"34821fa46da000e7a82eb3b6ce00db9761a8175c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6210","title":"Change default hot key to command shift u","createdAt":"2023-05-17T15:19:26Z"}
{"state":"Merged","mergedAt":"2023-05-17T17:31:46Z","number":6211,"mergeCommitSha":"6e0d1455839ec1a9401766d27a61c6bb13b94f43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6211","title":"Rip out the old onboarding flow and associated assets","createdAt":"2023-05-17T17:06:08Z"}
{"state":"Merged","mergedAt":"2023-05-17T17:53:12Z","number":6212,"body":"This sets up a way to enqueue processing complete email requests which will be sent out via AWS SES.\r\nThis follows the standard paradigm we use for other emails.","mergeCommitSha":"a8d559645a0ea8bb01567e23d09680f1f5fa78a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6212","title":"Add abiltiy to send processing complete emails via ses","createdAt":"2023-05-17T17:19:01Z"}
{"state":"Merged","mergedAt":"2023-05-17T18:22:42Z","number":6213,"body":"Will rip out the actual code in a separate PR","mergeCommitSha":"0ac620a6656d234fdefe71a6d9538c7020291146","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6213","title":"Rm tutorial wizard and command from vscode","createdAt":"2023-05-17T18:06:49Z"}
{"state":"Merged","mergedAt":"2023-05-17T21:34:08Z","number":6214,"mergeCommitSha":"fee22e341440b73631ed6f71c0ed1b696addfbe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6214","title":"Rip out VSCode from installer","createdAt":"2023-05-17T18:13:35Z"}
{"state":"Merged","mergedAt":"2023-05-17T18:20:31Z","number":6215,"mergeCommitSha":"9405ea9bda077e11337121d6be0a1cb62c8e989d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6215","title":"Make data. pipeline geneation generic","createdAt":"2023-05-17T18:17:48Z"}
{"state":"Merged","mergedAt":"2023-05-17T19:02:49Z","number":6216,"body":"Add additional onboarding loading state.\r\n\r\nThis command determines whether installation is necessary given the current workspace.\r\nIf yes, sends to installation flow. After installation, InstallationWizardCommand should send them to tutorial checklist with (https://github.com/NextChapterSoftware/unblocked/pull/6213)\r\nIf no, goes straight to tutorial checklist.\r\n\r\nHas Installations:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/720e84bf-f701-4ca5-a772-d2e72071e381\r\n\r\nNo Installations\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/3d9e0aba-4ee4-4c4c-a83f-58a4f8ab38a9\r\n\r\n","mergeCommitSha":"21558fee9c60c5fb50cbfa85f9db04c517b04a7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6216","title":"Onboarding Loading state","createdAt":"2023-05-17T18:28:25Z"}
{"state":"Merged","mergedAt":"2023-05-17T18:31:45Z","number":6217,"mergeCommitSha":"75ecc7b22518275fe20d52f4203190b7ca3793ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6217","title":"Update cdk dependencies","createdAt":"2023-05-17T18:29:06Z"}
{"state":"Merged","mergedAt":"2023-05-17T18:38:53Z","number":6218,"mergeCommitSha":"2b3847cd4f5ad779394eab3e47b486b92aed5020","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6218","title":"Capture and act on custom scheme url for Hub","createdAt":"2023-05-17T18:38:27Z"}
{"state":"Merged","mergedAt":"2023-05-17T19:18:28Z","number":6219,"mergeCommitSha":"3cf62ce68e0c0c9c0c20c8f1f9b1a6ab42086f6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6219","title":"Update source mebeddings","createdAt":"2023-05-17T19:17:23Z"}
{"state":"Merged","mergedAt":"2022-03-31T17:30:42Z","number":622,"body":"Updating Repo spec to support focusing clients (VSCode + WebExtension) to their current workspace while allowing dashboard remain non-repo specific.\r\n\r\nAdd repoIDs to `getThreads`, `getUnreads`\r\n\r\n\r\n","mergeCommitSha":"6432f516a65663d540080bdcb2bd4ff3a5b14747","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/622","title":"Repo spec update","createdAt":"2022-03-21T17:44:58Z"}
{"state":"Merged","mergedAt":"2023-05-17T21:13:04Z","number":6220,"body":"Boo","mergeCommitSha":"54d310913be455fd989ef6db6115d54d2ac86953","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6220","title":"Fix macOS compile issue","createdAt":"2023-05-17T21:12:26Z"}
{"state":"Merged","mergedAt":"2023-05-17T23:10:22Z","number":6221,"mergeCommitSha":"fadb70ec638d7233c6fab142bdf4f7d7c46408bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6221","title":"Add GitHub issues ingestion logic","createdAt":"2023-05-17T21:25:38Z"}
{"state":"Merged","mergedAt":"2023-05-18T04:37:03Z","number":6222,"mergeCommitSha":"ad05269a25b0c4014d6660455e6ae2bc07fa9f6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6222","title":"Send processing complete email","createdAt":"2023-05-17T21:35:59Z"}
{"state":"Merged","mergedAt":"2023-05-18T17:14:22Z","number":6223,"body":"Setup onboarding loading for IntelliJ\r\n","mergeCommitSha":"1fa6b019b932122a6ea87d575feca7221c72eed0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6223","title":"Setup onboarding loading for intellij","createdAt":"2023-05-17T22:51:54Z"}
{"state":"Merged","mergedAt":"2023-05-17T23:15:24Z","number":6224,"body":"And a connection might not be available with getinstallationConnection endpoint.\r\n","mergeCommitSha":"e1aed588e943e5e72a314252dc962a1d9d4e9441","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6224","title":"[BUG FIX] Bad assumption, an installation might not have connections.","createdAt":"2023-05-17T23:00:34Z"}
{"state":"Merged","mergedAt":"2023-05-18T00:10:15Z","number":6225,"mergeCommitSha":"43a3a677ee1ae85a4027d9327fb17a1c7d35b5f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6225","title":"Temporary hack to increase timeouts for search","createdAt":"2023-05-17T23:45:32Z"}
{"state":"Merged","mergedAt":"2023-05-26T22:16:04Z","number":6226,"mergeCommitSha":"ab81a27b6076f82bb77a4e65fc4c2fd750af5dd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6226","title":"Return GitHub Issue threads with Provider.GitHub","createdAt":"2023-05-17T23:46:48Z"}
{"state":"Merged","mergedAt":"2023-05-18T00:15:11Z","number":6227,"mergeCommitSha":"14d703706fbe0ec66a3b19904a744dee188f1499","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6227","title":"Onboarding cleanup","createdAt":"2023-05-17T23:55:18Z"}
{"state":"Merged","mergedAt":"2023-05-18T03:59:27Z","number":6228,"mergeCommitSha":"05e517dee99a00772c5c171260bd6c234a12c5c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6228","title":"Remove ability for Hub to downgrade","createdAt":"2023-05-18T03:58:53Z"}
{"state":"Merged","mergedAt":"2023-05-18T04:42:59Z","number":6229,"mergeCommitSha":"ea5dcbe26f54c9370efc72d5240f191c560d76af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6229","title":"Set clientAssets head to tip of main","createdAt":"2023-05-18T04:42:34Z"}
{"state":"Merged","mergedAt":"2022-03-22T05:39:09Z","number":623,"body":"Also correctly sets `Thread.threadType`","mergeCommitSha":"d928922577b0299a44ee623e4fc7d6e3f2d0f789","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/623","title":"Include PR details for threads ingested from PR comments","createdAt":"2022-03-21T19:43:08Z"}
{"state":"Merged","mergedAt":"2023-05-18T17:50:11Z","number":6230,"mergeCommitSha":"0b34da5825b122fa0baf0e66c9b58c6ef3070d37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6230","title":"Create GitHubIssuesBulkIngestionJob","createdAt":"2023-05-18T16:34:28Z"}
{"state":"Merged","mergedAt":"2023-05-18T17:10:33Z","number":6231,"body":"Part 1 of getting rid of inline icons: remove the inline brand icons.\r\n\r\nWe were only using the inline branded icons for the walkthrough UI -- this PR changes this so we use the SVG file ones instead.","mergeCommitSha":"10112cffad5a6331127fd1d41547c3acd330ad46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6231","title":"Remove inline brand icons","createdAt":"2023-05-18T16:39:47Z"}