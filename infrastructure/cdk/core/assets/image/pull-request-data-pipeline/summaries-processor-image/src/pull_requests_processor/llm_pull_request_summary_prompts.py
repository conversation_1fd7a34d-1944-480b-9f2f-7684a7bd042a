from langchain_core.prompts import Base<PERSON>romptTemplate, PromptTemplate
from llm_prompt_utils.llm_prompt import LL<PERSON>rompt


SUMMARY_OUTPUT = """
Display the summary in this format:
Author: <author>
Date: <date>

Summary:
<concise summary including themes and objectives>

Notable Changes:
<notable changes>

For the above format, keep this in mind;
1. If the author cannot be determined, then exclude the Author section.
"""


class PullRequestSummaryCombinePrompt(LLMPrompt):
    _prompt = (
        """
```{text}```

Given the above summaries of git diffs delimited by triple backquotes, extract intention and summarize the git diff summaries.
Make sure you take into account ALL the git diff summaries when performing summarization.
Make sure the summary is below 2500 characters and is concise.

%s
    """
        % SUMMARY_OUTPUT
    )
    _inputs = ["text"]

    def get_prompt_template(self) -> BasePromptTemplate:
        return PromptTemplate(template=self._prompt, input_variables=self._inputs)

    def get_inputs(self) -> list[str]:
        return self._inputs


class PullRequestSummaryMapPrompt(LLMPrompt):
    _prompt = (
        """
"{text}"

Summarize notable changes in the above git diff making sure overall themes and objectives are preserved in the summary as well as related code symbols.
Avoid unnecessary or colorful commentary, and provide a clear, objective overview of the modifications made in the codebase.
Make sure the summary contains no more than 2000 characters and is concise.

%s
    """
        % SUMMARY_OUTPUT
    )
    _inputs = ["text"]

    def get_prompt_template(self) -> BasePromptTemplate:
        return PromptTemplate(template=self._prompt, input_variables=self._inputs)

    def get_inputs(self) -> list[str]:
        return self._inputs


class PullRequestSummarySystemPrompt(LLMPrompt):
    _prompt = """You are a helpful assistant used to summarize a group of git diff changes for a pull request identifying objectives and themes."""

    _inputs = []

    def get_prompt_template(self) -> BasePromptTemplate:
        return PromptTemplate(template=self._prompt, input_variables=self._inputs)

    def get_inputs(self) -> list[str]:
        return self._inputs
