commit 6c5db30d7480a109b82f2dd078dc8d4f2460a68f
Author: mahdi-to<PERSON><PERSON> <<EMAIL>>
Date:   Mon Apr 25 15:59:59 2022 -0700

    fix a copy paste mistake in adminweb service deployment code (#1005)

diff --git a/.github/workflows/ci-services.yml b/.github/workflows/ci-services.yml
index e38846537e..a300905193 100644
--- a/.github/workflows/ci-services.yml
+++ b/.github/workflows/ci-services.yml
@@ -271,7 +271,7 @@ jobs:
       ecr-repo-name: adminweb
       service-name: adminwebservice
       service-dir: ./projects/services/adminwebservice
-      service-helm-dir: ./projects/services/notificationservice/.helm/adminwebservice
+      service-helm-dir: ./projects/services/adminwebservice/.helm/adminwebservice
       aws-region: ${{ needs.build.outputs.aws-region }}
       kube-api-host-dev: ${{ needs.build.outputs.kube-api-host-dev }}
       kube-api-host-prod: ${{ needs.build.outputs.kube-api-host-prod }}
