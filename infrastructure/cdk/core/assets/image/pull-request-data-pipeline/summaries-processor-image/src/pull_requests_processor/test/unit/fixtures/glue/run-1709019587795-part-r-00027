{"state":"Merged","mergedAt":"2023-04-17T19:11:15Z","number":5673,"body":"These entry points are restricted:\n- on-premise host creation\n- team creation\n- user creation","mergeCommitSha":"55f1a9b86f79963036e4c1591d854247d026b5dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5673","title":"Use restricted access service to deny access to DEV","createdAt":"2023-04-17T17:22:00Z"}
{"state":"Merged","mergedAt":"2023-04-17T18:05:37Z","number":5674,"body":"No logic change","mergeCommitSha":"6a587cb2b7e9362d26ed3c5c414e7fe2c65832f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5674","title":"Add support for providing a transaction to PullRequestIngestionService methods","createdAt":"2023-04-17T17:32:52Z"}
{"state":"Merged","mergedAt":"2023-04-17T23:31:35Z","number":5675,"body":"https://linear.app/unblocked/issue/UNB-1170/ml-improve-topic-summary-prompt","mergeCommitSha":"5e2b7c99f857179981e2df0cbbe36f46aab15728","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5675","title":"UNB-1170: ML: Improve topic summary prompt","createdAt":"2023-04-17T18:02:19Z"}
{"state":"Merged","mergedAt":"2023-04-17T21:54:33Z","number":5676,"body":"Adds additional logging to debug potential situations where errors are thrown in streams unexpectedly, freezing up the UI.","mergeCommitSha":"31a5c3d817c5e90ebe1f04fa36ad53ffec059ab6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5676","title":"Additional logging","createdAt":"2023-04-17T20:05:09Z"}
{"state":"Merged","mergedAt":"2023-04-20T23:01:57Z","number":5677,"body":"Merges explorer and insights sidebar into single toolwindow\r\n\r\n<img width=\"687\" alt=\"CleanShot 2023-04-17 at 13 39 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/232605593-f4db30ff-c6c5-435d-b666-c96b36b820e1.png\">\r\n\r\n","mergeCommitSha":"4688f6e2eeeffcdbb7e5f43d65429e7f624f07fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5677","title":"Consolidate IntelliJ sidebar","createdAt":"2023-04-17T20:25:36Z"}
{"state":"Merged","mergedAt":"2023-04-18T04:01:52Z","number":5678,"mergeCommitSha":"ee50d333314a5fa51b7d8f597683326f6f37674c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5678","title":"Install Bitbucket webhook during installation","createdAt":"2023-04-17T20:42:22Z"}
{"state":"Merged","mergedAt":"2023-04-18T17:11:10Z","number":5679,"mergeCommitSha":"83e14368aaef12ab4de35a9331c91bc8ae480bb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5679","title":"Allow direct asset import in VSCode webviews","createdAt":"2023-04-17T22:32:35Z"}
{"state":"Merged","mergedAt":"2022-03-11T23:07:40Z","number":568,"body":"To be merged after https://github.com/NextChapterSoftware/unblocked/pull/567","mergeCommitSha":"7f9eb4c070d8799f97e6f4acea522f4e27bb9c03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/568","title":"Use correct path for readines checks","createdAt":"2022-03-11T22:57:17Z"}
{"state":"Merged","mergedAt":"2023-04-17T23:36:34Z","number":5680,"body":"In theory we could do a left join and check that there is no sourcemark associated with the thread, but slack threads don't have an associated sourcemark. I could add an additional clause to ignore slack threads but if in the future we add another kind of thread that doesn't have a sourcemark (like issues) then we'd have to remember to update the clause here.\r\n\r\nIt's just less fragile to add a ThreadModel.isTopLevelCommentThread property.","mergeCommitSha":"888902e8f1d9baabaa4954adf249cc7dc5a9b21f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5680","title":"Add ThreadModel.isTopLevelCommentThread property","createdAt":"2023-04-17T22:54:54Z"}
{"state":"Merged","mergedAt":"2023-04-18T00:53:01Z","number":5681,"mergeCommitSha":"fe8d1f226f9b9663095a095d49c1d315b783d8ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5681","title":"Filter out pull request comments if theyve been threaded","createdAt":"2023-04-17T23:49:52Z"}
{"state":"Merged","mergedAt":"2023-04-18T00:47:44Z","number":5682,"body":"<img width=\"1158\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/232634752-e740c61b-fb93-4933-9df8-15141b64d766.png\">\r\n<img width=\"1180\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/232634768-4b40c883-bd22-4208-80ee-d3cd74909028.png\">\r\n<img width=\"1171\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/232634827-e49adb13-9f95-42bf-a30c-4f93a2075388.png\">\r\n<img width=\"1183\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/232634967-58ad28ce-2f6e-446b-9df0-b8b8a487323a.png\">\r\n","mergeCommitSha":"3151fcf4f652d832205bfb2aac2264072262e207","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5682","title":"UNB-1112 Update dashboard message layout","createdAt":"2023-04-17T23:55:22Z"}
{"state":"Merged","mergedAt":"2023-04-18T05:03:05Z","number":5683,"mergeCommitSha":"4bb803b6a9ee190fb422efb68e4604809a5f6a64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5683","title":"Add Bitbucket webhook route","createdAt":"2023-04-17T23:56:41Z"}
{"state":"Merged","mergedAt":"2023-04-18T02:34:29Z","number":5684,"mergeCommitSha":"de10df156ad7e23dfa82a788dbefa10c0e8cd9fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5684","title":"Unb 1170 ml improve topic summary prompt","createdAt":"2023-04-18T00:32:38Z"}
{"state":"Merged","mergedAt":"2023-04-18T02:36:53Z","number":5685,"mergeCommitSha":"62fa65b73b20a1182131d9ba216aa6630dda4b81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5685","title":"Add testbed for embeddings","createdAt":"2023-04-18T01:23:59Z"}
{"state":"Merged","mergedAt":"2023-04-18T03:07:17Z","number":5686,"mergeCommitSha":"71ada4a34e7b58185985dae5bb918a62ff6e500b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5686","title":"Add team setting for threaded top-level comments","createdAt":"2023-04-18T02:23:26Z"}
{"state":"Merged","mergedAt":"2023-04-18T05:28:11Z","number":5687,"body":"The check for a null file only applies for code-level comments.","mergeCommitSha":"0f056b3f3ca9075024458ae941a5c64ed272f72f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5687","title":"Fix PullRequestIngestionService to allow null files for top-level threads","createdAt":"2023-04-18T05:14:26Z"}
{"state":"Merged","mergedAt":"2023-04-18T21:43:46Z","number":5688,"body":"Add state to enable installation for all current and future repos in an installation.\r\n<img width=\"864\" alt=\"CleanShot 2023-04-17 at 22 15 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/232677750-e6da66ac-2b7c-4c35-b6b4-172d4aff07e5.png\">\r\n","mergeCommitSha":"8736b5a82e05798b1e9230b44d2bf35569e90f86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5688","title":"All repositories state in Repo selection","createdAt":"2023-04-18T05:15:08Z"}
{"state":"Merged","mergedAt":"2023-04-18T17:57:53Z","number":5689,"mergeCommitSha":"5d8698d0efef766ac283c433eeb32b22bbf780df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5689","title":"Enable TLC notifications for insider teams","createdAt":"2023-04-18T17:32:07Z"}
{"state":"Merged","mergedAt":"2022-03-11T23:49:10Z","number":569,"mergeCommitSha":"5521842e8e3f232f32c65169e6c1dc0c0fb374b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/569","title":"Add the platform version to the application","createdAt":"2022-03-11T23:00:34Z"}
{"state":"Merged","mergedAt":"2023-04-18T17:55:10Z","number":5690,"body":"This rule was noising and firing a ton of alarms in the middle of night. Added the exception for Calico containers. \r\n\r\nDeployed to Dev and Prod. ","mergeCommitSha":"733a9c6951286e0d4b8f206ccb6008af532054c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5690","title":"deal with another noisy rule","createdAt":"2023-04-18T17:53:58Z"}
{"state":"Merged","mergedAt":"2023-04-18T18:19:13Z","number":5691,"body":"There is a useEffect within CreateInsight that triggers `InsightCommand`'s Update.\r\nThis caused the postMessage to update (without a useCallback) causing a recursive rendering cycle.\r\n\r\n","mergeCommitSha":"ef3bddd0578188d978e6ecf511b40ec8f0f1dc7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5691","title":"Remove unnecessary render cycle","createdAt":"2023-04-18T17:59:58Z"}
{"state":"Closed","mergedAt":null,"number":5692,"mergeCommitSha":"8dc31fce9dad9ca4252148a6b9166d050984009c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5692","title":"[DO NOT MERGE] Enable TLC notifications for all teams","createdAt":"2023-04-18T18:00:06Z"}
{"state":"Merged","mergedAt":"2023-04-20T15:53:45Z","number":5693,"body":"It's a bit janky, but really the only way to get the experience we need for existing customers (which is not to show the onboarding flow).\r\n\r\nThe logic is as follows:\r\n- Existing customers will already be \"authed\", and their \"hasSeenTutorial\" flag will be true\r\n- This will load into memory on startup, so there is no race condition possible\r\n- We will set this flag to true at the tail end of the onboarding flow in the Hub, both in the service and on the client\r\n- The reason for having a client-side flag for this is to deal with what happens when users log out. We want to show the sign-in UX back in the hub instead of in the onboarding window. \r\n- One additional challenge remains, which is to deal with the case where they sign in with a different user that doesn't belong to an onboarded team","mergeCommitSha":"dd52a2fa7eb80bd799c07606fec5f0f347a5bbc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5693","title":"Introduce new defaults for OnboardingV2","createdAt":"2023-04-18T18:28:43Z"}
{"state":"Closed","mergedAt":null,"number":5694,"mergeCommitSha":"0ffbe16c64407700b13d34e772ce7734db71c812","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5694","title":"WIP: JetBrains custom editor","createdAt":"2023-04-18T21:00:32Z"}
{"state":"Merged","mergedAt":"2023-04-19T22:49:49Z","number":5695,"mergeCommitSha":"aa8b800c35f9e394e299881790e549fb2e88b406","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5695","title":"Include pull request reviews in the TLC thread","createdAt":"2023-04-18T21:26:27Z"}
{"state":"Merged","mergedAt":"2023-04-18T21:49:41Z","number":5696,"body":"Tests:\r\n- Topics\r\n- Machine Learning\r\n- Sourcemarks.\r\n\r\nDefinitely the best results so far.\r\n\r\nThere are a bunch of tools (prompt builders + dynamic loaders) that we may want to look at adopting too.\r\n\r\nWe can discuss those and may use one when we need to add expert descriptions?","mergeCommitSha":"a551103faebae1c8c5f7dea72cfce4e8dc98046a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5696","title":"Telling the LLM more about the usage seems to improve results","createdAt":"2023-04-18T21:31:57Z"}
{"state":"Merged","mergedAt":"2023-04-19T18:49:09Z","number":5697,"mergeCommitSha":"6c35d81aae31e95703fdc1437e1e96eccb4f54e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5697","title":"Record invites sent and invites dismissed","createdAt":"2023-04-18T21:58:40Z"}
{"state":"Merged","mergedAt":"2023-04-19T15:36:51Z","number":5698,"body":"We want the TLC thread to appear immediately under the PR description. This only applies to GitHub PRs since BitBucket and GitLab support threaded TLCs, so those will appear in order of creation date.","mergeCommitSha":"9f37c520121fd0371cd01f5fa89da61dfa9db707","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5698","title":"GitHub TLC thread comes first","createdAt":"2023-04-18T22:54:17Z"}
{"state":"Merged","mergedAt":"2023-04-21T19:57:15Z","number":5699,"mergeCommitSha":"40ea66819e1f7dcd421bccb127e6041ab14066f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5699","title":"Add onboarding state logic","createdAt":"2023-04-18T22:55:57Z"}
{"state":"Merged","mergedAt":"2022-01-18T18:14:48Z","number":57,"body":"Not really functional. Just ensures schema is up to date.","mergeCommitSha":"2d49f54403345769d71767c7d6942022159fafc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/57","title":"App connects to DB","createdAt":"2022-01-18T16:26:01Z"}
{"state":"Merged","mergedAt":"2022-03-12T01:40:06Z","number":570,"body":"This can be merged once you have renamed paths in API spec \r\n\r\nRelated to #571 ","mergeCommitSha":"4a584c3e8a0e0903f423e2cb997eb84ff694a56b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/570","title":"rename probe paths and regen charts","createdAt":"2022-03-11T23:40:00Z"}
{"state":"Merged","mergedAt":"2023-04-20T18:47:45Z","number":5700,"body":"Add SCM settings to dashboard. Feature-flagged off in dashboard.\r\n\r\nCurrently shows Org Selector -> Repo Selector. Will be removing Org Selector soon.\r\n<img width=\"1378\" alt=\"CleanShot 2023-04-18 at 16 42 15@2x\" src=\"https://user-images.githubusercontent.com/1553313/232927976-2ad4262f-36ca-4c08-9951-d96d5f444a1b.png\">\r\n<img width=\"1523\" alt=\"CleanShot 2023-04-18 at 16 42 22@2x\" src=\"https://user-images.githubusercontent.com/1553313/232927979-fba1a94f-2329-43f9-9638-b53173c87e56.png\">\r\n\r\n\r\n\r\nUpdates ConnectTeam to handle empty installation state (aka non org owner)\r\n<img width=\"696\" alt=\"CleanShot 2023-04-18 at 16 35 36@2x\" src=\"https://user-images.githubusercontent.com/1553313/232927859-b2721ecc-2046-4009-a1ca-b85c673c28f5.png\">\r\n<img width=\"927\" alt=\"CleanShot 2023-04-18 at 16 41 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/232927921-cb418e74-368f-466a-a6fa-94babad7c841.png\">\r\n\r\n\r\n","mergeCommitSha":"b6bec1410d36a1bcf60216ed3caef50a4395ee79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5700","title":"Repo selection UI in Settings","createdAt":"2023-04-18T23:42:41Z"}
{"state":"Merged","mergedAt":"2023-04-19T00:01:34Z","number":5701,"body":"Also, make sure that we don't emit 'Answer' in the response.","mergeCommitSha":"6194283500cf4f314081c087400d62cba961443b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5701","title":"5 sentences is just too long, going the other way:","createdAt":"2023-04-18T23:46:47Z"}
{"state":"Merged","mergedAt":"2023-04-19T15:37:16Z","number":5702,"body":"To be used when adding these to the TLC thread.","mergeCommitSha":"4f2ed897801d080066642260bc2da2f4d57c8583","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5702","title":"Add ScmPullRequestReview.asScmPrCommentTopLevel extension","createdAt":"2023-04-19T00:15:39Z"}
{"state":"Merged","mergedAt":"2023-04-19T16:54:19Z","number":5703,"body":"Required to run against newer versions of IntelliJ","mergeCommitSha":"393c23fa57dad5210d5830c5cf20866221398bba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5703","title":"Update to java 17","createdAt":"2023-04-19T00:45:05Z"}
{"state":"Merged","mergedAt":"2023-04-19T16:43:28Z","number":5704,"body":"Not just for GitHub.","mergeCommitSha":"b85b00242ecfb92194f695a3f4aa7bb1af628871","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5704","title":"List all TLC threads under the pull request description","createdAt":"2023-04-19T16:25:29Z"}
{"state":"Merged","mergedAt":"2023-04-19T18:17:44Z","number":5705,"body":"Prevents the job from running for 45 mins and then dying.\r\n\r\nSimilar to #5658.","mergeCommitSha":"d3b8493deda549fde47b2c8abbcd1bf10932d1e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5705","title":"Set a timeout for \"Set up SDK\" step in all workflows","createdAt":"2023-04-19T18:16:49Z"}
{"state":"Merged","mergedAt":"2023-04-19T19:14:15Z","number":5706,"body":"https://linear.app/unblocked/issue/UNB-1174/topics-adminweb-add-button-to-refresh-approved-topic-descriptions\r\n\r\nAdded `triggerTopicSummaryAllTopics` to TopicsPage + Routing","mergeCommitSha":"05223106ef60669ff558b3703504bcd267cc8d72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5706","title":"UNB-1174: Add button to refresh approved topic descriptions","createdAt":"2023-04-19T18:18:26Z"}
{"state":"Merged","mergedAt":"2023-04-19T21:11:54Z","number":5707,"body":"- 7 day\n- 30 day\n\nhttps://www.notion.so/nextchaptersoftware/Engagement-Score-bc769924dc4d4bd08c296d8c0bbd31a0","mergeCommitSha":"9573c0e575fdc62af85cc64422a55a777d20a20a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5707","title":"Calculate engagement metrics","createdAt":"2023-04-19T20:22:23Z"}
{"state":"Merged","mergedAt":"2023-04-19T21:46:06Z","number":5708,"mergeCommitSha":"01cdb81f0baba95543eebc7f3f740025faed2008","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5708","title":"Use user engagement to gate invite flows","createdAt":"2023-04-19T20:22:26Z"}
{"state":"Merged","mergedAt":"2023-04-19T22:25:10Z","number":5709,"body":"Also make sure we trigger the required events when creating a TLC thread.","mergeCommitSha":"5d0eb5a91a828acd8a239c58be2105fd402aa94e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5709","title":"Ensure updating a TLC message sets the correct title on the thread","createdAt":"2023-04-19T20:26:20Z"}
{"state":"Merged","mergedAt":"2022-03-12T01:36:26Z","number":571,"body":"We provide two health checks:\r\n\r\n- *deep check* — to be used for startup and liveness probes\r\n- *shallow check* — to be used for readiness probes\r\n\r\nRelated to https://github.com/NextChapterSoftware/unblocked/pull/570","mergeCommitSha":"f0947053e890150ff7315ed1c37f993e18e47111","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/571","title":"Refactor health checks","createdAt":"2022-03-12T00:42:49Z"}
{"state":"Merged","mergedAt":"2023-04-19T20:57:12Z","number":5710,"body":"Fixes missing provider which resulted in incorrect label in menu dropdown.\r\n\r\n<img width=\"790\" alt=\"CleanShot 2023-04-19 at 13 44 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/233195823-cd4e2529-94e6-4f3a-a770-9a8d2ee937bc.png\">\r\n","mergeCommitSha":"dda511794dedcab1df2469c87876159fcecbb47d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5710","title":"FIx missing Provider","createdAt":"2023-04-19T20:44:55Z"}
{"state":"Merged","mergedAt":"2023-04-19T23:23:34Z","number":5711,"body":"This is done in conjunction with the new TLC threads.\r\n\r\nvscode:\r\n<img width=\"764\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233196699-4afac5c5-e212-4c2f-a2a0-f855175df7d2.png\">\r\n<img width=\"555\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233196738-1087e813-4819-4a27-86c0-3dc3a18c6e29.png\">\r\n\r\njetbrains:\r\n<img width=\"653\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233196847-7bda343b-dda6-479c-9d2e-b3a55ff74731.png\">\r\n<img width=\"459\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233196883-97230e97-3717-495b-b30e-b4d8c027d6ca.png\">\r\n\r\ndashboard:\r\n<img width=\"1078\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233197125-13195d51-3153-41ed-966b-fc08ae238138.png\">\r\n<img width=\"716\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/233197188-7c86ebd7-c353-46bc-b2f3-95b07b85a49e.png\">\r\n\r\n","mergeCommitSha":"65d4cd8601cf20c44a32fd6335328e9f8c4722b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5711","title":"Reformat PR views in clients","createdAt":"2023-04-19T20:52:01Z"}
{"state":"Merged","mergedAt":"2023-04-19T21:42:11Z","number":5712,"body":"Part of the work to roll up review comments into the TLC thread.","mergeCommitSha":"d212f71c81c6bebd4aa03c327a31602a84a4b1f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5712","title":"Bulk ingestion of GitHub PRs triggers review-only ingestion","createdAt":"2023-04-19T21:20:33Z"}
{"state":"Merged","mergedAt":"2023-04-20T04:38:42Z","number":5713,"body":"Removes blocks like \r\n![CleanShot 2023-04-19 at 14 28 26@2x](https://user-images.githubusercontent.com/1924615/233204202-7c5b75a0-897b-4237-8cc8-12a099c25422.png)\r\n","mergeCommitSha":"16b574564a184150a8007e91a338b1f6b545d357","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5713","title":"Don't return review blocks without threads","createdAt":"2023-04-19T21:29:09Z"}
{"state":"Merged","mergedAt":"2023-04-20T15:53:14Z","number":5714,"body":"Everyone is approved for now until we add the implementation...","mergeCommitSha":"6df2d672c22f956b5699030ce23df45ef51fdbcc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5714","title":"Add pendingTeams API","createdAt":"2023-04-19T21:39:19Z"}
{"state":"Merged","mergedAt":"2023-04-19T22:42:45Z","number":5715,"body":"Not clear why this affects Kube health checks.\n\nThis causes Kube health check request to respond with 406:\n```kotlin\ncall.respond(status = HttpStatusCode.fromValue(200), message = response)\n```\n\nThis does not:\n```kotlin\ncontext.call.respond(HttpStatusCode.OK, \"Ok!\\n\")\n```","mergeCommitSha":"fe78d409969d82d82defde8ddca1a4b197eee097","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5715","title":"Fix health check","createdAt":"2023-04-19T22:41:34Z"}
{"state":"Merged","mergedAt":"2023-04-21T02:39:44Z","number":5716,"body":"The previous prompt tended to list all the PR titles and make up users.","mergeCommitSha":"2dd996eb870779ea651324ca7480a4e3b03bb8f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5716","title":"Unb 1170 topic summary prompt 3","createdAt":"2023-04-19T22:43:29Z"}
{"state":"Closed","mergedAt":null,"number":5717,"body":"Instead of waiting for doPoll to run before fetching, trigger an initial fetch when all channels have not run yet.","mergeCommitSha":"8fed450c147ab22ad47b67525abd4d3862f36119","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5717","title":"Initial fetch in Poller","createdAt":"2023-04-19T22:55:11Z"}
{"state":"Merged","mergedAt":"2023-04-19T23:11:09Z","number":5718,"mergeCommitSha":"ee3d6e4199342504292c04688334d1618ef4e459","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5718","title":"Ignore low relevance TLCs","createdAt":"2023-04-19T22:58:07Z"}
{"state":"Merged","mergedAt":"2023-04-19T23:33:17Z","number":5719,"mergeCommitSha":"a325531588ec32ba04325512bdbfa5315aa5f2b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5719","title":"Admin: rename activity events to make more human readable","createdAt":"2023-04-19T23:10:43Z"}
{"state":"Merged","mergedAt":"2022-03-12T01:11:17Z","number":572,"mergeCommitSha":"5d4b9e854ac30d300b16278ed862d8cdd0d73e88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/572","title":"Fix exception","createdAt":"2022-03-12T01:05:37Z"}
{"state":"Merged","mergedAt":"2023-04-19T23:36:02Z","number":5720,"mergeCommitSha":"8c062ae5195760ba1a29e5cc7a992b941d6d317b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5720","title":"Remove build cache lock","createdAt":"2023-04-19T23:28:54Z"}
{"state":"Merged","mergedAt":"2023-04-20T03:55:09Z","number":5721,"body":"1. Declare more intenral teams\n    -  https://github.com/Bishop-Fox-Unblocked-Pentest-2\n    -  https://github.com/ccerne-unblocked-pentest-3\n    -  https://github.com/get-unblocked\n2. Teams are only marked as internal in PROD. This is intended to best simulate\n   production conditions in non-PROD environments.","mergeCommitSha":"5c68751df6df1f6717446dfb161e6d9021207848","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5721","title":"Admin: Declare more internal teams","createdAt":"2023-04-19T23:30:45Z"}
{"state":"Merged","mergedAt":"2023-04-20T23:44:14Z","number":5722,"mergeCommitSha":"a209bfe1050b2eda368040b4d18dc155c9dad227","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5722","title":"Fix Retry logic","createdAt":"2023-04-19T23:41:28Z"}
{"state":"Merged","mergedAt":"2023-04-20T00:21:05Z","number":5723,"mergeCommitSha":"a8b02c5d5bec07bd6d593585c670aca1dd38c41e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5723","title":"Respect the event createThreadUnread property","createdAt":"2023-04-20T00:10:52Z"}
{"state":"Merged","mergedAt":"2023-04-20T01:03:36Z","number":5724,"body":"We need to be able to invoke hugging face instructor embedding from sagemaker.\r\nHugging face does not provide an inference endpoint for this model.","mergeCommitSha":"9770d77331da7573fcaf43a2ca9fadde35958f37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5724","title":"Add ability to invoke hugging face embeddings","createdAt":"2023-04-20T01:01:04Z"}
{"state":"Merged","mergedAt":"2023-04-20T02:38:17Z","number":5725,"mergeCommitSha":"ed0ce7ab89ddd8c4644e9072a5cff50192136fa7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5725","title":"Update hugging face endpoints","createdAt":"2023-04-20T02:38:12Z"}
{"state":"Merged","mergedAt":"2023-04-20T03:03:58Z","number":5726,"mergeCommitSha":"f02da6cf28f7798a6a3095f2b975f7df5001e2d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5726","title":"Add a catch block for top-level comment ingestion","createdAt":"2023-04-20T02:49:42Z"}
{"state":"Merged","mergedAt":"2023-04-20T04:23:03Z","number":5727,"mergeCommitSha":"4e2be24a0153f5045b842313f92733b22a61d045","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5727","title":"Admin: avatar style tweak","createdAt":"2023-04-20T04:22:54Z"}
{"state":"Merged","mergedAt":"2023-04-20T05:45:25Z","number":5728,"mergeCommitSha":"3f7e954a666e5d38929e23a6d5dd44ae84d72c93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5728","title":"Persist all repo selection preference in DB","createdAt":"2023-04-20T05:19:56Z"}
{"state":"Merged","mergedAt":"2023-04-20T05:44:52Z","number":5729,"mergeCommitSha":"56b0d99782dbdb5340e309b7ce12475754d39568","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5729","title":"Minimum GitLab level to install Unblocked is Maintainer","createdAt":"2023-04-20T05:22:05Z"}
{"state":"Merged","mergedAt":"2022-03-15T22:31:13Z","number":573,"body":"It's possible for someone to check in a model change that will fail schema migration; for example, adding a non-nullable column to an existing model where there already exists rows in the database.\r\n\r\nCurrently when this happens, `SchemaManager.updateSchema` will return and the application will proceed with starting up. This is not what we want, because the application will expect the database to be in a particular state. \r\n\r\nIt would be better for the application to hard fail and exit. This way deployment halts and we can be alerted so that we can revert the change.\r\n\r\nThis change updates `updateSchema` to check to see if schema migration succeeded and returns true if so. If it returns false, the application will exit.\r\n\r\nNote: when schema migration fails, the `busy` table in the database will have a row. In addition to reverting the offending change, we'll need to clear that table so that later schema changes can be attempted.","mergeCommitSha":"4b3e20eafc8ed7e67959e4d78eefaade4b0d6e5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/573","title":"API service exits if schema migration fails","createdAt":"2022-03-12T01:31:21Z"}
{"state":"Merged","mergedAt":"2023-04-20T15:14:09Z","number":5730,"body":"This reverts commit ee3d6e4199342504292c04688334d1618ef4e459.\r\n\r\nA TLC thread with a single message that doesn't have valuable content will be archived, so this change is unnecessary.","mergeCommitSha":"2058a4a0d1e037441d80c8bbc4deab7bc9436b8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5730","title":"Revert \"Ignore low relevance TLCs (#5718)\"","createdAt":"2023-04-20T05:33:48Z"}
{"state":"Merged","mergedAt":"2023-05-08T17:38:27Z","number":5731,"body":"Quick and dirty approach. Every class of PR webhook is handled in the same brute force way,\r\nwhere we just send an event to the SCM-service to re-process the entire PR.\r\n\r\nWe can be much more targetted and efficient with a bit more work.","mergeCommitSha":"27922c158a6a91c6ccc644e5a5a0c2a034fe61e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5731","title":"Bitbucket PR hook handler","createdAt":"2023-04-20T05:59:46Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:00:44Z","number":5732,"body":"By default, the cache download will continue on for 10 minutes doing nothing.\r\nTo get around that, it is recommended to set a smaller itmeout and force a cache miss when it does timeout.\r\nThe timeout does not cause the build to fail, it just makes it a cache miss build.\r\n","mergeCommitSha":"e5a6d2914ef37f233910d0789192dc8191981ef3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5732","title":"Add cache segment download timeout","createdAt":"2023-04-20T06:37:05Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:22:17Z","number":5733,"body":"If bulk ingestion gets interrupted, clearing the URLs means it will restart from the beginning.","mergeCommitSha":"33431c531bf2fabf09351825792cc672ca3aff49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5733","title":"Dont clear the next batch urls until bulk ingestion is complete","createdAt":"2023-04-20T15:56:56Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:29:53Z","number":5734,"mergeCommitSha":"7a9a403b3b5482b28b4431770f9226fa115ba7d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5734","title":"Admin: Show repo selection preference, and cleanup code","createdAt":"2023-04-20T16:29:22Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:37:42Z","number":5735,"mergeCommitSha":"362b8099a17cb092ef811b7c91a0f10345d6d7c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5735","title":"Standard upload timeout is too low","createdAt":"2023-04-20T16:37:36Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:41:20Z","number":5736,"body":"- Standard upload timeout is too low\r\n- Increase timeout v2\r\n","mergeCommitSha":"5e847fc7fddfed31a72e9753accc200ab9e39833","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5736","title":"IncreaseTimeout2","createdAt":"2023-04-20T16:40:51Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:42:53Z","number":5737,"mergeCommitSha":"9d7b9a3b04f16ee39b5ca9b72d1e0efdcff5b7e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5737","title":"Accidentally reduced a timeout I should not have","createdAt":"2023-04-20T16:42:47Z"}
{"state":"Merged","mergedAt":"2022-03-15T03:27:24Z","number":574,"body":"The `StatusPages` handler was effectively intercepting all exceptions and setting them to 500s. There are a class of exceptions, including some of Ktor's default exceptions, which need to be handled upstream so they can adopt the correct error responses. \r\n\r\nAdditionally, since Ktor 2.0.0, exceptions are logged and dealt with internally ahead of StatusPages handling, meaning we should not re-throw exceptions, and in fact they've removed re-throws from their sample code for 2.0.0. This is a much better model IMO. It makes no sense to have plugins get in the middle of the exception handling pipeline unless they are explicitly for that purpose. \r\n\r\nAdditionally, throwing exceptions in the status pages plugin creates problems for test applications. I've filed a bug report against Ktor for that issue here: https://youtrack.jetbrains.com/issue/KTOR-4009 \r\n\r\nDefault exception handling in Ktor is given by this function:\r\n```kotlin\r\npublic fun defaultExceptionStatusCode(cause: Throwable): HttpStatusCode? {\r\n    return when (cause) {\r\n        is BadRequestException -> HttpStatusCode.BadRequest\r\n        is NotFoundException -> HttpStatusCode.NotFound\r\n        is UnsupportedMediaTypeException -> HttpStatusCode.UnsupportedMediaType\r\n        is TimeoutException, is TimeoutCancellationException -> HttpStatusCode.GatewayTimeout\r\n        else -> null\r\n    }\r\n}\r\n```\r\nThis list is incomplete though, and even Ktor's samples demonstrate potential custom status handling of some other common cases. I've added `UnauthorizedException` and `ForbiddenException`, which are helpful in the pipeline to generalize common error cases. \r\n\r\nI'm looking for feedback on one specific question: should top level API delegates capture and handle all possible error states explicitly? Or should we allow exceptions to bubble up to status handlers?\r\n\r\nThe former feels more in line with a tight API definition, but it creates tight coupling with the underlying implementation...","mergeCommitSha":"206cc7acbe2d8688e6a83567e73c88bb682dbeea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/574","title":"Properly handle statuses for custom exceptions","createdAt":"2022-03-14T03:13:16Z"}
{"state":"Merged","mergedAt":"2023-04-20T17:05:55Z","number":5743,"mergeCommitSha":"f1b5810fb562b422541bfa588c2f2af2ac2e11ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5743","title":"Add submodules to macOS installer build","createdAt":"2023-04-20T16:57:37Z"}
{"state":"Merged","mergedAt":"2023-04-20T16:58:24Z","number":5744,"mergeCommitSha":"4565d68b2fb83b4e693d9d2458099c2499a89914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5744","title":"Byepass gradle cache for now","createdAt":"2023-04-20T16:58:18Z"}
{"state":"Merged","mergedAt":"2023-04-20T17:15:13Z","number":5745,"body":"We need to re-run bulk ingestion to generate TLC threads but we only need to ingest issue comments, not PR or review comments. I'll revert this later today.","mergeCommitSha":"03629d41e9e34a23c4cea38e04b8b643faef47a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5745","title":"Temporarily disable bulk ingestion paths","createdAt":"2023-04-20T17:03:44Z"}
{"state":"Merged","mergedAt":"2023-04-20T17:11:55Z","number":5746,"mergeCommitSha":"eeef03b5487d47d90bbcae093f0b112abb9f5dba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5746","title":"Push the GH PAT to the macOS builder","createdAt":"2023-04-20T17:11:33Z"}
{"state":"Merged","mergedAt":"2023-04-20T17:15:00Z","number":5747,"mergeCommitSha":"a7e00b20022cbedfe93886b24f4c7afa4d92d9f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5747","title":"Add GH PAT secret to macos build definition","createdAt":"2023-04-20T17:14:43Z"}
{"state":"Merged","mergedAt":"2023-04-24T05:30:25Z","number":5748,"body":"Only to be merged after we've run it for existing teams.","mergeCommitSha":"b26fbaaad6daf8d83b13ee5b4d5c048d55abad6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5748","title":"Remove temporary bulk ingestion changes used for TLC thread generation","createdAt":"2023-04-20T17:17:52Z"}
{"state":"Merged","mergedAt":"2023-04-20T17:19:07Z","number":5749,"body":"- Byepass gradle cache for now\r\n- Goodbye gradle cache\r\n","mergeCommitSha":"33578babd5cea28e86c47353a0c394c076c324bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5749","title":"DisableGradleCache","createdAt":"2023-04-20T17:19:01Z"}
{"state":"Merged","mergedAt":"2022-03-14T16:44:29Z","number":575,"body":"We recently renamed our health endpoints. My last PR modified the path in Deployment specs but it seems helm charts were not properly regenerated. \r\n\r\n\r\nMy last PR with the actual path change: https://github.com/NextChapterSoftware/unblocked/pull/570\r\n","mergeCommitSha":"3bcea52398d8fa9613c91d2a6b0a474aa1c9cba9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/575","title":"force helm charts to update","createdAt":"2022-03-14T16:29:51Z"}
{"state":"Merged","mergedAt":"2023-04-20T18:19:36Z","number":5750,"mergeCommitSha":"924a42872aa9cd6927d2d8faac8eb3249682f15b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5750","title":"Fix notification title in Hub for TLCs","createdAt":"2023-04-20T18:03:51Z"}
{"state":"Merged","mergedAt":"2023-04-20T18:25:42Z","number":5751,"mergeCommitSha":"2cf08dad8860802fd9b3b54ad442aa1598ec0fbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5751","title":"Fix message header icons","createdAt":"2023-04-20T18:07:00Z"}
{"state":"Merged","mergedAt":"2023-04-20T18:33:25Z","number":5752,"body":"Reverts NextChapterSoftware/unblocked#5750\r\n\r\nProblem with the change: Hub notifications for the _first_ message in a thread (code-level or top-level) will show duplicate content in the 1st and 3rd line.","mergeCommitSha":"a7b95baffbd2637e91adbe4bfd7901271758857a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5752","title":"Revert \"Fix notification title in Hub for TLCs\"","createdAt":"2023-04-20T18:32:57Z"}
{"state":"Merged","mergedAt":"2023-04-20T20:11:08Z","number":5756,"body":"The transactions here are unnecessary since we do the same checks in the ingestion logic.","mergeCommitSha":"dc4da2df7aed8872c739e25e847ca76a28e02ad7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5756","title":"Ensure that ThreadModel.lastMessageCreatedAt is correctly set for TLCs","createdAt":"2023-04-20T19:56:50Z"}
{"state":"Merged","mergedAt":"2023-04-20T21:25:17Z","number":5758,"body":"1. Move to ml instances.\r\n2. Move away from lambda to direct sagemaker invocations","mergeCommitSha":"121236639f561b6763736796e83a55a0ddc31eaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5758","title":"Improve performance of embedding","createdAt":"2023-04-20T20:44:52Z"}
{"state":"Merged","mergedAt":"2023-04-20T23:41:25Z","number":5759,"body":"When this is deployed I will manually move all existing teams to `Granted` state. Currently unused, but teams have to be set to the right state before we deploy the filters for JWT and getTeams","mergeCommitSha":"f22956b855762eed16b330bb52919f34e8fd6e76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5759","title":"Add team access state","createdAt":"2023-04-20T20:47:14Z"}
{"state":"Merged","mergedAt":"2022-03-17T16:12:24Z","number":576,"body":"Implements the Video Channel API Delegate. See inline for walkthrough","mergeCommitSha":"291fc1daaf2dd490e9ac3dd08a23bf9c45c566c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/576","title":"Video Channel API","createdAt":"2022-03-14T18:13:25Z"}
{"state":"Merged","mergedAt":"2023-04-24T16:09:49Z","number":5761,"body":"I'll be creating a follow up PR to remove the team setting since its no longer needed.","mergeCommitSha":"1b93c05fd4b8783fa2bd51bdbd002c808d4946e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5761","title":"Enable notifications for TLC threads for all teams","createdAt":"2023-04-20T21:41:57Z"}
{"state":"Merged","mergedAt":"2023-04-24T05:43:51Z","number":5762,"body":"We no longer need to return the individual comments, as they've been rolled up into a thread.","mergeCommitSha":"4ee6274f2e16081b766a5b413b7d9dcbb2364e6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5762","title":"Clean up PullRequestInfo","createdAt":"2023-04-20T22:25:32Z"}
{"state":"Merged","mergedAt":"2023-04-20T22:49:42Z","number":5763,"mergeCommitSha":"a979d443770df4e18dab7e8a31c53cb016043251","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5763","title":"Reduce indexing concurrency from 5 to 1","createdAt":"2023-04-20T22:33:52Z"}
{"state":"Merged","mergedAt":"2023-04-20T22:49:00Z","number":5764,"body":"This is temp until I roll out auto-scalers.","mergeCommitSha":"679a408f19db52054393dbbf3ed3de0a092ec26a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5764","title":"add one more search service instance","createdAt":"2023-04-20T22:37:28Z"}
{"state":"Merged","mergedAt":"2023-04-20T22:54:45Z","number":5765,"mergeCommitSha":"3076015130a32838da44ca41a223ba69b3d3289c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5765","title":"Clean up embedding endpoitns","createdAt":"2023-04-20T22:54:28Z"}
{"state":"Merged","mergedAt":"2023-04-20T23:44:29Z","number":5766,"body":"- Copy all JS bundle content in the jar, instead of doing it manually\r\n- Rebuild bundle content when webpack config changes\r\n- Fixes missing Unblocked image in search tab","mergeCommitSha":"91ada13aef1a367fcbcd2b31dc8b57c88dc6e04a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5766","title":"Better copying of JS content in JetBrains plugin","createdAt":"2023-04-20T23:05:08Z"}
{"state":"Merged","mergedAt":"2023-04-20T23:55:58Z","number":5767,"body":"Issue where creating an insight on \"strange\" file can cause empty tool windows in IntelliJ\r\n\r\nGit operations could occasionally fail. These types of failures should *not* prevent the user from creating insights. ","mergeCommitSha":"07d3273c11d9f08a3bbde463457634e3bd4083af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5767","title":"Catch error instead of throwing","createdAt":"2023-04-20T23:41:05Z"}
{"state":"Merged","mergedAt":"2023-04-23T20:22:37Z","number":5768,"mergeCommitSha":"22461d57b21dfe8f6267a92375ca33a6ca41db16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5768","title":"Implement deleteRepo and hide uninstalled repos","createdAt":"2023-04-21T00:13:43Z"}
{"state":"Merged","mergedAt":"2023-04-21T01:17:54Z","number":5769,"mergeCommitSha":"aa53bc6f945c4704643c61b42f9d79a10e9a10ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5769","title":"Show default settings for teams with no settings","createdAt":"2023-04-21T00:16:53Z"}
{"state":"Merged","mergedAt":"2022-03-14T22:32:43Z","number":577,"body":"![image](https://user-images.githubusercontent.com/13431372/158237586-a9901023-a1cf-48ca-a59d-3950c877f2b8.png)\r\n\r\n* Funnel through sourcemark data to the `createThread` request \r\n* Add MockSourceMark code and update story\r\n* There's additional UI work to move the title into the first message body but I'll do that in another PR\r\n* There are a couple of TODOs that will be addressed once the `anchorSourceMarkId` property is supported on the backend\r\n* NOTE: Not added to the web dashboard yet from lack of designs (chatted with Ben about this). We may need to rethink which parts of the thread UI we want to reuse between clients when need to implement this","mergeCommitSha":"d3064dfbac26385cf501dfa845cda7ae884454bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/577","title":"Style anchor sourcemark code block","createdAt":"2022-03-14T18:37:19Z"}
{"state":"Merged","mergedAt":"2023-04-21T05:01:42Z","number":5770,"body":"Motivation is to ensure that the DEV experience is as similar to product as possible. Engagement metrics are not recorded for internal users, which prevent us from testing functionality in DEV that depends on users being engaged.\r\n\r\nPart of this change cleanly separates **_admin web users_** from **_internal users_**. they are no longer the same thing.","mergeCommitSha":"4a2c90d882a3f19cfb4a0db4cf8219af2dafae4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5770","title":"In DEV treat all users as non-internal users","createdAt":"2023-04-21T00:36:24Z"}
{"state":"Merged","mergedAt":"2023-04-21T17:24:04Z","number":5771,"mergeCommitSha":"aaa9d8e58230b894c0579b6fc1364d544dcbd165","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5771","title":"Organize JetBrains into folders","createdAt":"2023-04-21T00:37:18Z"}
{"state":"Merged","mergedAt":"2023-04-21T01:36:25Z","number":5772,"body":"- Enable hugging face instructor embeddings\r\n- Update\r\n","mergeCommitSha":"ae20c5b462170810d8a19c2386ea1024f02e2f1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5772","title":"Enable Hugging Face Embeddings","createdAt":"2023-04-21T01:07:15Z"}
{"state":"Merged","mergedAt":"2023-04-22T20:00:07Z","number":5773,"mergeCommitSha":"c049af01b870cfc85f2210de8b69d7968257651f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5773","title":"Add TeamMember.description + TeamMember.userDefinedDescription","createdAt":"2023-04-21T01:21:00Z"}
{"state":"Merged","mergedAt":"2023-04-21T08:57:33Z","number":5774,"body":"It looks like the flag changed when going from java 11 - 17","mergeCommitSha":"dca1c39990ed9764cee39d94ed5ad15f9e55b847","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5774","title":"Update Max Heap Size","createdAt":"2023-04-21T02:33:23Z"}
{"state":"Merged","mergedAt":"2023-04-21T03:07:11Z","number":5778,"body":"Since pull request issue comments and review comments are in threads, those will be indexed as a thread. Thus we don't need to include them when indexing the pull request itself.","mergeCommitSha":"afb3389376080bcc622b894f3375d1382a062ec8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5778","title":"Update pull request indexing logic","createdAt":"2023-04-21T02:44:31Z"}
{"state":"Merged","mergedAt":"2023-04-21T03:12:15Z","number":5779,"mergeCommitSha":"3147af7403f0ebaf2f853c54b802cc9d2793bcde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5779","title":"Increase search sevice","createdAt":"2023-04-21T03:12:09Z"}
{"state":"Merged","mergedAt":"2023-04-21T04:01:08Z","number":5781,"mergeCommitSha":"e1180ac0cdf106279654168ebfb365e80c5df96a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5781","title":"GEneralize insight indexing content","createdAt":"2023-04-21T03:45:41Z"}
{"state":"Merged","mergedAt":"2023-04-21T03:59:34Z","number":5782,"body":"This reverts commit a979d443770df4e18dab7e8a31c53cb016043251.","mergeCommitSha":"d46472424d029c54460082f3f5afe97334c7eab3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5782","title":"Revert \"Reduce indexing concurrency from 5 to 1 (#5763)\"","createdAt":"2023-04-21T03:49:07Z"}
{"state":"Merged","mergedAt":"2023-04-21T21:44:29Z","number":5783,"body":"Logic to filter pending teams from the JWT and the `getTeams` response was reverted. Current plan is to have the Hub make a call to `pendingTeams` to determine which teams should be allowed through the onboarding Gate. Trivial to add filtering back in for future clients. ","mergeCommitSha":"bdeb2e0a6f00ed2b88b9f9a8374eb5f02104fee7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5783","title":"Implement pendingTeams endpoint","createdAt":"2023-04-21T04:42:28Z"}
{"state":"Merged","mergedAt":"2023-04-21T05:10:13Z","number":5784,"mergeCommitSha":"cdf4eec4cbd0502b3eb210f512be3b5c40709374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5784","title":"Add missing feature flag","createdAt":"2023-04-21T05:01:10Z"}
{"state":"Open","mergedAt":null,"number":5785,"body":"TODO tests","mergeCommitSha":"cdd14ace2219692b22be4f4339d987e79f255d27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5785","title":"Write-back for updating reviews","createdAt":"2023-04-21T05:05:35Z"}
{"state":"Merged","mergedAt":"2023-04-21T08:52:29Z","number":5786,"body":"Default queue prefect limit is 1000. That's per consumer.\r\nWe need to reduce this...","mergeCommitSha":"f47bdc1b065a708fa02ce2a8c3f16d9e8b1e0801","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5786","title":"Add activemq queue prefetch limit","createdAt":"2023-04-21T08:32:32Z"}
{"state":"Merged","mergedAt":"2023-04-21T14:54:57Z","number":5788,"mergeCommitSha":"9c47c25dde6e0a5469748a9a4c8e83088d57eca0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5788","title":"Fix admin console toggle","createdAt":"2023-04-21T14:54:27Z"}
{"state":"Merged","mergedAt":"2023-04-21T17:26:21Z","number":5789,"mergeCommitSha":"664cfbdc6852f3c07b7a89695547147ecb2aa421","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5789","title":"Remove deprecated scm queues","createdAt":"2023-04-21T16:54:13Z"}
{"state":"Merged","mergedAt":"2022-03-15T04:12:47Z","number":579,"body":"Precursor to module split","mergeCommitSha":"ed3aebd706fa2b8147b603bf9739cdc3368c3b89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/579","title":"Adds config DI to Jwt","createdAt":"2022-03-14T20:40:54Z"}
{"state":"Merged","mergedAt":"2023-04-21T21:30:53Z","number":5790,"body":"Give teams access to the platform by default (until we don't)","mergeCommitSha":"5eb1eed51ed9df9ab7d89b59b2d8fe6e3a3a2b07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5790","title":"Set userAccessAllowed to true by default","createdAt":"2023-04-21T17:12:51Z"}
{"state":"Merged","mergedAt":"2023-04-21T18:13:46Z","number":5796,"mergeCommitSha":"f8d54210c91752e8ab718741f4ea8db79da7ec2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5796","title":"Fix extension","createdAt":"2023-04-21T18:03:13Z"}
{"state":"Merged","mergedAt":"2023-04-21T18:30:19Z","number":5798,"mergeCommitSha":"6ddb0407ef56aeb9bd789d608bf77005224bede9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5798","title":"JetBrains New UI icons","createdAt":"2023-04-21T18:07:08Z"}
{"state":"Merged","mergedAt":"2023-04-21T18:48:46Z","number":5799,"body":"This reverts commit 713c4d4742a79929d3fd28781db634def422e2b3.\r\n","mergeCommitSha":"d2e09fa8e25870f7df6e799f205d5abf8bc22184","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5799","title":"Revert \"chore(deps): update dependency gradle to v8.1.1 (#5791)\"","createdAt":"2023-04-21T18:48:40Z"}
{"state":"Closed","mergedAt":null,"number":58,"mergeCommitSha":"67b7a805e623e3c0e28dbb639cde94fd5215410b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/58","title":"Add team models","createdAt":"2022-01-18T16:27:43Z"}
{"state":"Merged","mergedAt":"2022-03-14T22:17:34Z","number":580,"mergeCommitSha":"7f719d567043adc6bc3bac285961c797c28830b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/580","title":"Fix teamID in RuntimeFixtures","createdAt":"2022-03-14T22:07:30Z"}
{"state":"Merged","mergedAt":"2023-04-25T23:47:48Z","number":5800,"mergeCommitSha":"f47b5b3e8872b775ce56059bd318fbfb35c2141a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5800","title":"Pull in pending teams","createdAt":"2023-04-21T18:54:28Z"}
{"state":"Merged","mergedAt":"2023-04-22T18:56:08Z","number":5802,"mergeCommitSha":"2083abcc01acf18bf4d32c9b2f8bbfaa781b1802","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5802","title":"Fix embedded postgres stale cleanup","createdAt":"2023-04-21T20:50:53Z"}
{"state":"Merged","mergedAt":"2023-04-21T22:42:47Z","number":5803,"body":"It is impossible to atomically send an email and also record that the email was sent.\r\nWe can choose to send the email first, or record the invite first; both have drawbacks.\r\n\r\n- If we record the invite first then there is a risk that the email will never be sent.\r\n- If we send the email first then there is a risk that the invite will not be recorded\r\n  leading to duplicate emails being sent.\r\n\r\nThis change inverts the order so that we record the invite first. Motivation is because\r\nKay sent a bad team member ID which failed TeamInviteeModel referencial integrity,\r\nwhich resulted in a 500, which was retried automatically by the client, and it sent 7\r\nemails.","mergeCommitSha":"2f9ec3fcca8d7f9626b612afbd02b4d8051c8ba7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5803","title":"Ensure that we record the invite sent before sending the invite","createdAt":"2023-04-21T21:48:07Z"}
{"state":"Merged","mergedAt":"2023-04-24T17:35:13Z","number":5804,"body":"This funnels the window focus events through to the agent.\r\n\r\nWhile I was in this code I also changed the ChannelPoller to get its focus state from FocusStream, instead of having its own focus setting API.","mergeCommitSha":"072dbc3592a14fcc8e9fcdb075c6e8a90fa9038f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5804","title":"JetBrains: only poll when window has focus","createdAt":"2023-04-21T23:18:53Z"}
{"state":"Merged","mergedAt":"2023-04-22T00:09:24Z","number":5805,"body":"We relied on a standard gRPC request from IntelliJ to the Agent to transmit a list of repositories.\r\n\r\nThe projectService typically issued two requests:\r\n\r\n1. Upon loading, it sent an empty repository list.\r\n2. Following an update, it sent a list of repositories.\r\n\r\nUnfortunately, the sequence of these requests is not guaranteed, leading to instances where the Agent first received the second request and then the first, resulting in an empty repository state for the Agent.\r\n\r\nPR updates the communication method to utilize a stream that guarantees the correct order of requests.","mergeCommitSha":"9f1500d998ed84742f0dd02b0e41ff60330174d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5805","title":"UpdateRepos GRPC from request to stream","createdAt":"2023-04-21T23:48:25Z"}
{"state":"Merged","mergedAt":"2023-04-22T19:32:25Z","number":5806,"mergeCommitSha":"c6af6e77760ec46c802334acf4c85d141e1e4429","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5806","title":"Gradle cache propblems","createdAt":"2023-04-22T19:32:20Z"}
{"state":"Merged","mergedAt":"2023-04-22T19:37:10Z","number":5807,"mergeCommitSha":"a0a4dec430984163c1d469aeb4bb43be66f53133","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5807","title":"Make sure step doesn't cause build to fail","createdAt":"2023-04-22T19:37:04Z"}
{"state":"Merged","mergedAt":"2023-04-25T20:16:15Z","number":5808,"body":"Introduces ExpertSummaryService which generates TeamMember.description with an associated eventqueue + adminweb action.","mergeCommitSha":"2c9ae85ed10e42013c37ba1b4b0b297a9d6255ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5808","title":"Expert Summary Service","createdAt":"2023-04-23T05:40:19Z"}
{"state":"Merged","mergedAt":"2023-04-23T16:56:23Z","number":5809,"mergeCommitSha":"49297462d1678bd01ceb5fe330b4f1993a523b26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5809","title":"This action is consuming a lot of cache space","createdAt":"2023-04-23T16:56:16Z"}
{"state":"Merged","mergedAt":"2022-03-15T18:20:38Z","number":581,"body":"An anchor SourceMark is the one that appears at the top of a thread. Versus a regular SourceMark which can appear in a message body, an anchor SourceMark can never be removed/deleted from a thread and there can ever only be one per thread. \r\n\r\nMain changes in this PR:\r\n\r\n- adding an `isAnchor` property to `SourceMarkModel`\r\n- ensuring that the `isAnchor` field is updated if the thread creation request has a message with an `anchorSourceMarkId`\r\n- returning the correct value for `anchorSourceMarkId` when returning `Messages`\r\n\r\nBecause of what I mentioned above, `isAnchor` is only ever set for one SourceMark per thread and only at thread creation time.","mergeCommitSha":"da8ecc5d9ab8535f04f7d01a5160f42aa4c408ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/581","title":"Implement backend support for anchorSourceMark","createdAt":"2022-03-14T22:32:24Z"}
{"state":"Merged","mergedAt":"2023-04-23T18:32:06Z","number":5810,"mergeCommitSha":"0bf3f619e61599f03e49acb4165fd01a1488e895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5810","title":"comment out unused junit test config","createdAt":"2023-04-23T18:32:00Z"}
{"state":"Merged","mergedAt":"2023-04-23T18:59:38Z","number":5811,"mergeCommitSha":"a1c6e671d3c2fd507c3a4486adb3b2e1e28cd918","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5811","title":"Update gradle build cache node release version","createdAt":"2023-04-23T18:59:22Z"}
{"state":"Merged","mergedAt":"2023-04-23T19:09:26Z","number":5812,"body":"- Revert \"Revert \"chore(deps): update dependency gradle to v8.1.1 (#5791)\" (#5799)\"\r\n- Attempt to move to a version of build action with better caching?","mergeCommitSha":"339584b14443ca3ab440b452e269a69dd652adec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5812","title":"MoreGradleActionStepFixes","createdAt":"2023-04-23T19:07:51Z"}
{"state":"Merged","mergedAt":"2023-04-23T19:14:08Z","number":5813,"mergeCommitSha":"cbba16ecf28bf641d562e226293bdc896ccfd4ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5813","title":"Remove setup java cache","createdAt":"2023-04-23T19:14:02Z"}
{"state":"Merged","mergedAt":"2023-04-23T19:42:25Z","number":5815,"mergeCommitSha":"e36c34fa3c9976c06b41369ebcf9d452b745cc15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5815","title":"Attempt to fix gradle dying","createdAt":"2023-04-23T19:34:49Z"}
{"state":"Merged","mergedAt":"2023-04-23T20:18:44Z","number":5817,"mergeCommitSha":"3210eb40407867becf3e958614a5ef1481617be5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5817","title":"Admin: View and clear team member email invites","createdAt":"2023-04-23T19:53:53Z"}
{"state":"Merged","mergedAt":"2023-04-23T20:19:23Z","number":5818,"mergeCommitSha":"ed4384abaa5c3cfa516317b9d7660cf1dc22e486","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5818","title":"Refactor admin action routes","createdAt":"2023-04-23T19:53:56Z"}
{"state":"Merged","mergedAt":"2022-03-15T06:02:07Z","number":582,"body":"https://www.notion.so/nextchaptersoftware/Engineering-priorities-9cc1d3210b22441bb8f631d4a801f6cc#5510fc36d4474e43b8ea57994eb6eea8","mergeCommitSha":"4745cbde726a585fac8ea646c7756c3226c85059","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/582","title":"Add threadId to SourceMark","createdAt":"2022-03-14T22:58:35Z"}
{"state":"Merged","mergedAt":"2023-04-23T20:11:18Z","number":5820,"mergeCommitSha":"33df7233b10a17172274c472e2d1e5d51ad2dd7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5820","title":"Improve gradle documentation for jvm args","createdAt":"2023-04-23T19:55:14Z"}
{"state":"Merged","mergedAt":"2023-04-23T20:34:16Z","number":5821,"mergeCommitSha":"da9adc78f8c256943d4e25638f11a4fcdeddf88d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5821","title":"Limit cache directories","createdAt":"2023-04-23T20:34:05Z"}
{"state":"Closed","mergedAt":null,"number":5822,"mergeCommitSha":"b272b4e482b96a0ec70357035ed49e4d2a89622b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5822","title":"Reduce concurrency","createdAt":"2023-04-23T20:51:16Z"}
{"state":"Merged","mergedAt":"2023-04-23T20:54:42Z","number":5823,"body":"- Reduce concurrency\r\n- disable gradle flag\r\n","mergeCommitSha":"ec1addddaf94dc66c07acffc77b2aecc186f7262","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5823","title":"DisableGradleFlag","createdAt":"2023-04-23T20:54:21Z"}
{"state":"Merged","mergedAt":"2023-04-23T21:52:27Z","number":5825,"mergeCommitSha":"df963038815d6c0b2b57bc16a0f716e5baf48f3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5825","title":"Do gradle caching manually","createdAt":"2023-04-23T21:28:36Z"}
{"state":"Merged","mergedAt":"2023-04-23T22:10:18Z","number":5826,"mergeCommitSha":"1aab328b9afd82be91539793ab03449289dc12ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5826","title":"Make sure we hash gradle wrapepr propties file","createdAt":"2023-04-23T21:57:16Z"}
{"state":"Merged","mergedAt":"2023-04-23T22:09:09Z","number":5828,"body":"All non-default branch cache is essentially useless and should be wiped.\r\n","mergeCommitSha":"658a3ff0764c08b878df6584db0cdc9c317b015a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5828","title":"Clean pull request cache that cant be used on main","createdAt":"2023-04-23T22:08:45Z"}
{"state":"Merged","mergedAt":"2023-04-23T22:30:06Z","number":5829,"body":"- Clean pull request cache that cant be used on main\r\n- Wrong actions cache\r\n","mergeCommitSha":"21257310eed42dab196d10c4d6ad3ba2258e6bda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5829","title":"FixWrongActionsCache","createdAt":"2023-04-23T22:29:09Z"}
{"state":"Merged","mergedAt":"2022-03-16T20:11:24Z","number":583,"body":"* ~Should eventually implement a config file per environment~ (done)\r\n* Also simplified sidebar to hide the mocked sections for now:\r\n![image](https://user-images.githubusercontent.com/13431372/158275812-7af17eb4-3bac-4ede-b761-ddb56e2aaae3.png)","mergeCommitSha":"880db0067800dd5c3108fa08150866af4b6deee4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/583","title":"Add shallow feature flag config","createdAt":"2022-03-14T23:16:04Z"}
{"state":"Merged","mergedAt":"2023-04-23T23:09:41Z","number":5830,"mergeCommitSha":"b53f1ba1532265ac93956388a3ad919b1ea7448d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5830","title":"Admin: cleanup profiles","createdAt":"2023-04-23T23:08:52Z"}
{"state":"Merged","mergedAt":"2023-04-24T01:21:16Z","number":5831,"mergeCommitSha":"1436d91b7f5ce38a55fbd2a58bb3a21a35105631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5831","title":"Try dyanmic parallelization","createdAt":"2023-04-23T23:18:44Z"}
{"state":"Merged","mergedAt":"2023-04-24T01:43:02Z","number":5837,"mergeCommitSha":"5d8a4cb6fc7f25c94fd69b714d471e0e01f52c3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5837","title":"timeout cache restore","createdAt":"2023-04-24T01:42:52Z"}
{"state":"Merged","mergedAt":"2023-04-24T01:58:55Z","number":5838,"mergeCommitSha":"140991c7528c8225b3e6624b54be72dd1e9a41bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5838","title":"Keep on playing with these test parameters","createdAt":"2023-04-24T01:58:44Z"}
{"state":"Merged","mergedAt":"2023-04-24T02:23:57Z","number":5839,"mergeCommitSha":"f6230a56f7315ec73f316ce11bd781e4e6f24b1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5839","title":"More Refactor","createdAt":"2023-04-24T02:22:33Z"}
{"state":"Merged","mergedAt":"2022-03-16T17:24:55Z","number":584,"body":"PR walkthrough in files","mergeCommitSha":"b863a1fb7f527fca6af4f47193c90e193993ab00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/584","title":"Team scope restrictions","createdAt":"2022-03-15T02:58:27Z"}
{"state":"Merged","mergedAt":"2023-04-24T02:49:25Z","number":5840,"mergeCommitSha":"a7141bbfc358ad7a084caeda40407ebfd082110d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5840","title":"Segregate save from restore","createdAt":"2023-04-24T02:33:23Z"}
{"state":"Merged","mergedAt":"2023-04-24T03:18:01Z","number":5841,"mergeCommitSha":"6108a4933bd302598c14c7c8b8200262a2feab63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5841","title":"Fix typos in admin","createdAt":"2023-04-24T03:15:30Z"}
{"state":"Merged","mergedAt":"2023-04-24T03:27:15Z","number":5842,"mergeCommitSha":"90e1d456e901c6a62c7e73fcb504c67de68af2db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5842","title":"Fix issue where build image artifacts was not being downloaded","createdAt":"2023-04-24T03:22:46Z"}
{"state":"Merged","mergedAt":"2023-04-24T03:48:18Z","number":5843,"body":"- Revert gradle upgrade for now\r\n- Revet gradle\r\n","mergeCommitSha":"99689bf4af8e20c722ead6e9034c191707082923","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5843","title":"RevertGradle2","createdAt":"2023-04-24T03:47:44Z"}
{"state":"Merged","mergedAt":"2023-04-24T04:43:04Z","number":5844,"body":"We are definitely moving to s3…\r\nThis is ridiculous and amateur hour not having a timeout work on composite actions like action/cache.\r\n","mergeCommitSha":"c48e429a6dad3bb3b9127f12fb0180618c558408","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5844","title":"Github action cache is a composite step and step timeouts do not work","createdAt":"2023-04-24T04:42:54Z"}
{"state":"Closed","mergedAt":null,"number":5845,"mergeCommitSha":"c818e0da946befdc6dd8164f7578ca038bf6e5d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5845","title":"[WIP] Update minimum threshold for determining thread relevancy","createdAt":"2023-04-24T05:24:51Z"}
{"state":"Merged","mergedAt":"2023-04-24T16:32:49Z","number":5849,"body":"- Add `unblocked-gh-actions-s3-cache-sec-ops-us-west-2` bucket for caching\r\n- Added new IAM roles to be used for reading/writing/deleting objects to the bucket\r\n- Created IAM groups under root account and assigned the roles to them\r\n- Added deploybot user to the new IAM groups.\r\n\r\nNext step is to validate this by updating our source code backup GitHub action job and see if it works. If that job passes it means the new roles are working as expected.\r\n\r\n\r\nBucket Name: `unblocked-gh-actions-s3-cache-sec-ops-us-west-2`\r\nRole to assume: `arn:aws:iam::************:role/IAMS3AccessRole-unblocked-gh-actions-s3-cache`\r\n\r\nI have already deployed these changes locally. We need this PR to be merged before anything causes the S3 bucket to be deleted by another workflow run. ","mergeCommitSha":"c8f924adfff1abf0fd7a296631a0a2846cac9d22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5849","title":"add s3 bucket and roles for caching","createdAt":"2023-04-24T15:46:32Z"}
{"state":"Closed","mergedAt":null,"number":585,"mergeCommitSha":"f1fae23d3648f6546ca66fa6234946bc78fa6e37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/585","title":"Attempt to use separate fetch libraries","createdAt":"2022-03-15T19:54:48Z"}
{"state":"Merged","mergedAt":"2023-04-25T20:36:01Z","number":5850,"body":"No longer needed since we've enabled it for all teams.","mergeCommitSha":"065007dcb92cb753c7f3837fbfaa0e4018259692","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5850","title":"Drop TeamSettingsModel.topLevelCommentsThreadsSupported column","createdAt":"2023-04-24T16:18:03Z"}
{"state":"Merged","mergedAt":"2023-04-24T16:48:42Z","number":5851,"body":"Updated this workflow to use the deploybot user with scoped permissions. I used this to test the new S3 bucket role as well. ","mergeCommitSha":"2ef67ece40ecf2b830ef997bd7fe78c492d346e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5851","title":"Update source backup job","createdAt":"2023-04-24T16:32:37Z"}
{"state":"Merged","mergedAt":"2023-04-25T03:32:34Z","number":5852,"body":"We are moving away from using github cache which has proven to be unreliable.\r\nWe are now going to be using a custom s3 cache implementation.\r\n\r\nhttps://github.com/NextChapterSoftware/action-s3-cache","mergeCommitSha":"026a8b6d893866f2eea3b4aafc13d1192ae20f18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5852","title":"Gradle cache s3","createdAt":"2023-04-24T17:23:25Z"}
{"state":"Merged","mergedAt":"2023-04-24T20:04:54Z","number":5853,"body":"This will happen as long as the query used by the PushChannelService is not the same as the query used by the operation.  This gap exists for a number of operations/channels so we should fix that.","mergeCommitSha":"779cf7a4e0becff38dae8ccfdc86d76f9aab26b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5853","title":"Fix /repos push channel","createdAt":"2023-04-24T18:48:23Z"}
{"state":"Merged","mergedAt":"2023-04-26T20:02:23Z","number":5854,"mergeCommitSha":"f0f10ef60d756c68beb890937623ddb3a0c566d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5854","title":"Add node distribution info to version manifest","createdAt":"2023-04-24T20:09:47Z"}
{"state":"Merged","mergedAt":"2023-04-24T22:32:39Z","number":5855,"body":"Not clear what the benefit of having these is?\r\n\r\nWe filled logz quota on Friday.\r\n\r\n<img width=\"256\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/234110632-120ad62d-ae58-4225-9515-85e985254693.png\">\r\n","mergeCommitSha":"5e123271888a773f1cd7fb95b099f356a3c6df43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5855","title":"Remove trace logging","createdAt":"2023-04-24T20:35:20Z"}
{"state":"Merged","mergedAt":"2023-04-26T21:15:03Z","number":5856,"body":"Setup webview visibility trackers in IntelliJ.\r\n\r\nExtension WebviewController will pass webview events to corresponding Agent WebviewContentController, making it available to commands.\r\n\r\nEnables metrics polling.","mergeCommitSha":"ebcc078d3d4b8b4d922a46602be35617f0eee473","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5856","title":"IntelliJ webview visibility + Metrics","createdAt":"2023-04-24T21:45:03Z"}
{"state":"Merged","mergedAt":"2023-04-24T22:51:04Z","number":5857,"mergeCommitSha":"dfd036dabb509ff23c27a07e864212552053b24a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5857","title":"UNB-1189 Fix slack container width","createdAt":"2023-04-24T22:16:53Z"}
{"state":"Merged","mergedAt":"2023-04-25T23:21:49Z","number":5858,"body":"The hub is now going to set its own tutorial flag after tutorial has been completed, which should impact the global `Person` tutorial status","mergeCommitSha":"8182f0d958e28527f3a9177f98855b1ed8aa5d39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5858","title":"Allow hub to set its own tutorial state","createdAt":"2023-04-24T22:55:35Z"}
{"state":"Merged","mergedAt":"2023-04-24T23:51:32Z","number":5859,"body":"\r\n<img width=\"651\" alt=\"CleanShot 2023-04-24 at 15 51 38@2x\" src=\"https://user-images.githubusercontent.com/1553313/234133690-226ef7a7-6aa9-43b7-afe7-f99f981d7c2e.png\">\r\n<img width=\"674\" alt=\"CleanShot 2023-04-24 at 15 51 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/234133759-06c64150-fb44-438b-bf20-a52a84f8498d.png\">\r\n<img width=\"631\" alt=\"CleanShot 2023-04-24 at 15 51 36@2x\" src=\"https://user-images.githubusercontent.com/1553313/234133771-59f84b23-795d-42ba-9ea4-b576e213a1ef.png\">\r\n","mergeCommitSha":"31f7895ad21b7595f59c994084580ca797390917","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5859","title":"Update text editor styling","createdAt":"2023-04-24T22:58:26Z"}
{"state":"Closed","mergedAt":null,"number":586,"body":"API to find team member by git email.\r\n\r\nWill be used for this UI.\r\n<img width=\"457\" alt=\"CleanShot 2022-03-15 at 13 59 21@2x\" src=\"https://user-images.githubusercontent.com/1553313/158471578-1248553b-fa82-40d5-bce3-4856b05d9cd5.png\">\r\n ","mergeCommitSha":"1997e8d853a3379356028d0308045308aac33d1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/586","title":"Find TeamMember by Git Email","createdAt":"2022-03-15T20:59:36Z"}
{"state":"Merged","mergedAt":"2023-04-25T19:49:55Z","number":5860,"body":"Update quoteblock to render proper vertical line.\r\n\r\nUpdates spacing between threads & PRs.\r\n\r\n<img width=\"762\" alt=\"CleanShot 2023-04-24 at 16 30 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/234137389-3983b3b6-1cbf-4a60-a69a-871ee9811784.png\">\r\n<img width=\"816\" alt=\"CleanShot 2023-04-24 at 16 30 35@2x\" src=\"https://user-images.githubusercontent.com/1553313/234137405-2afc7a24-8f5f-4f77-a966-af0dbad5d56b.png\">\r\n","mergeCommitSha":"f6d5c49a3bca1cdb4bc37813bdf9153380c0cef7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5860","title":"Update spacing and quoteblock","createdAt":"2023-04-24T23:32:48Z"}
{"state":"Merged","mergedAt":"2023-04-27T21:29:22Z","number":5861,"body":"The idea here is to use the existing `authExchange` operation (currently used just by Slack) to drive oauth token exchange. We'd then use the access token to list the Jira sites that we've been granted access to, save that to the database, then ask the user to install our marketplace app. When they install our marketplace app, we'll then get a web hook which gives us creds to generate JWTs to access the API. \r\n\r\nThe reason to ask for OAuth is because we need to know the unblocked team to which a Jira site should be linked. Relying solely on the marketplace app installation web hook does not provide enough information.\r\n\r\nTechnically, we could use the user's Oauth access token received via the exchange to ingest Jira issues (and we could fall back to that) but its ultimately better to have them install the marketplace app since the app creds are not user-bound.\r\n\r\nTODO before merging\r\n✅ create Unblocked Jira OAuth integrations (local, dev, and prod) in https://developer.atlassian.com/console/myapps/ (<NAME_EMAIL>) and add the secrets to our vault\r\n\r\nTODO in later PRs\r\n- create a database model to persist the Jira site details and (encrypted) creds (a `JiraSiteModel` similar to `SlackTeamModel`)\r\n- upsert Jira provider identities for each team member","mergeCommitSha":"cf367b82116a2280befb54779cca8c5194fe32e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5861","title":"Implement Jira OAuth token exchange","createdAt":"2023-04-25T00:06:45Z"}
{"state":"Closed","mergedAt":null,"number":5862,"body":"- remove IDs that should no be exposed to clients:\r\n   - Identity.id\r\n   - Person.id\r\n\r\n- remove fields that are unnecessary\r\n   - drop Identity.username is only applicable for some providers. Makes no sense for Bitbucket, or Slack for example.\r\n   - drop Identity.isBot. We should just drop bots, unless we label messages from these differently in the UI. Can always add it back later.\r\n   - drop Message.authorTeamMemberId. This is replaced with an Identity struture.\r\n\r\n- add Identity structure as `profile` to Message, replacing TeamMember.id\r\n   - the consequence is that the GET /teamMembers response can be limited to only real team members (no non-SCM users, no bot users).\r\n\r\n- add linkedProfiles (`Identity[]`) to TeamMember model in order to render linked profiles in Expert pages.\r\n","mergeCommitSha":"ab38f652325e38460d800532015575f155251574","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5862","title":"[RFC] Refactor identity related API models","createdAt":"2023-04-25T00:16:34Z"}
{"state":"Merged","mergedAt":"2023-04-25T00:33:05Z","number":5863,"body":"We need the resource arn to do list buckets.","mergeCommitSha":"f4e91ca116dbc6287bb80e9946c6f3c23fa4d7b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5863","title":"Add resource arn","createdAt":"2023-04-25T00:20:36Z"}
{"state":"Merged","mergedAt":"2023-04-25T14:33:26Z","number":5864,"body":"Added a script which checks for the latest node version and uploads it to the proper directories in S3 if we don't have a copy of it. I also added a Github action job to run the ingestion every night. \r\n\r\nThis script can be ran for a local machine as well. Later I will add an extra optional flag to specify explicit versions just in case if we ever have to ingest a specific binary. For more info check the comments at the top of the script. \r\n\r\n\r\nTested it and works as expected. `node-v20.0.0-darwin-*.tar.gz` was ingested by this script.","mergeCommitSha":"6999536e035fc25dc87acb84535b6d6aa9eac171","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5864","title":"Node downloader gh actions job","createdAt":"2023-04-25T00:46:21Z"}
{"state":"Merged","mergedAt":"2023-04-25T21:59:53Z","number":5865,"body":"All clients can call this, and vscode/intellij/hub/dashboard get get the status flag","mergeCommitSha":"3a8327b9c413bdf26cb792e55bcc019bc1dfa1dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5865","title":"Add Hub Installed onboarding status flag","createdAt":"2023-04-25T01:19:06Z"}
{"state":"Merged","mergedAt":"2023-04-25T03:40:55Z","number":5866,"mergeCommitSha":"018cc0eeac6d76bb7195d9c87eb6adf8064073f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5866","title":"Set gradle cache","createdAt":"2023-04-25T03:40:32Z"}
{"state":"Merged","mergedAt":"2023-04-25T19:39:43Z","number":5867,"body":"<img width=\"945\" alt=\"CleanShot 2023-04-24 at 20 42 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/234169269-b5b635e3-2dc1-49dd-8654-8215c7fad197.png\">\r\n<img width=\"464\" alt=\"CleanShot 2023-04-24 at 20 41 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/234169271-2715c575-6bda-42c2-82ad-f8f59d118e30.png\">\r\n","mergeCommitSha":"58a5459e36f41498467b3a521cbecb9aa5f1d77c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5867","title":"Update separator colour","createdAt":"2023-04-25T03:42:27Z"}
{"state":"Merged","mergedAt":"2023-04-25T05:43:34Z","number":5868,"body":"Better caching performance","mergeCommitSha":"ff31f157402b00255bc1ef3c3d41469ea4cbc3c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5868","title":"Move to a variant of caching using zstd","createdAt":"2023-04-25T05:25:53Z"}
{"state":"Merged","mergedAt":"2023-04-25T17:27:36Z","number":5869,"body":"We record these events for the _sidebar_ and _insights_ panels:\n- IDE panel open\n- IDE panel remain open\n- IDE panel close\n\nHowever, only the open event is an indicator of engagement or activity.","mergeCommitSha":"3e77da934955491259a2789a619d2f9be5287df6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5869","title":"Record IDE panel open/close events","createdAt":"2023-04-25T14:17:55Z"}
{"state":"Closed","mergedAt":null,"number":587,"mergeCommitSha":"c4ea6d54bd9bda100f8ea4679055ccd9ed85b690","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/587","title":"Client mapped data streams","createdAt":"2022-03-15T21:14:21Z"}
{"state":"Merged","mergedAt":"2023-04-25T18:01:01Z","number":5870,"body":"Motivation is to test engagement for our users.","mergeCommitSha":"8b6b301dbc61c1576d1e88b1776ec0f14f252be7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5870","title":"Record metrics for internal users, but filter out for graphs","createdAt":"2023-04-25T14:18:37Z"}
{"state":"Merged","mergedAt":"2023-04-25T16:11:00Z","number":5871,"body":"Admin: Toggle descriptions are optional\n\nAdmin: Remove isBot toggle because this is managed by SCM sync","mergeCommitSha":"9beb091d8e55d9f44c0d8b54eac45b1eaf7ed7d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5871","title":"Admin: Improvements","createdAt":"2023-04-25T14:19:14Z"}
{"state":"Merged","mergedAt":"2023-04-25T17:01:27Z","number":5872,"body":"Some developer opened unblocked with an enterprise SCM repo.\n\nNot enabled yet, so it's failing for 501.","mergeCommitSha":"1a0b0905d0110f522bce65b104b16ee589dc1074","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5872","title":"Fix findInstallationsAndRepos API 500s","createdAt":"2023-04-25T14:19:33Z"}
{"state":"Merged","mergedAt":"2023-04-25T18:06:15Z","number":5873,"mergeCommitSha":"47d484e80995cf295c30c60d0993e651527cd65b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5873","title":"Add ability to cleanup cache","createdAt":"2023-04-25T16:56:47Z"}
{"state":"Merged","mergedAt":"2023-04-25T17:27:14Z","number":5874,"mergeCommitSha":"67cdb934052044872e5b530c18a8734ae5572656","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5874","title":"Fix test from #5872","createdAt":"2023-04-25T17:26:55Z"}
{"state":"Merged","mergedAt":"2023-04-25T19:34:49Z","number":5875,"body":"This is used to toggle mutation operations on pull request messages,\nlike edit or delete message, gated by the author of the message.\n\nThe bug was that the PR would never be treated as being owned by the\nauthorized user, and therefore mutation operation would always be hidden.","mergeCommitSha":"94bfa3bb0b28455133ad1a22da09bbcf8364c547","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5875","title":"Fix person ID bug affecting PR mutation operations","createdAt":"2023-04-25T17:51:12Z"}
{"state":"Merged","mergedAt":"2023-05-01T17:47:57Z","number":5876,"body":"Update Tab styling in IntelliJ\r\n\r\nUpdated context to inject webview focus state into webviews.\r\n\r\n<img width=\"711\" alt=\"CleanShot 2023-04-25 at 11 20 40@2x\" src=\"https://user-images.githubusercontent.com/1553313/234367114-72d54ccc-7003-4c1f-a745-43124c185966.png\">\r\n","mergeCommitSha":"5f249f567785ba3a69fa156105eaddfef17828a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5876","title":"[UNB-1199] update tab styling to match native","createdAt":"2023-04-25T18:21:52Z"}
{"state":"Merged","mergedAt":"2023-04-25T20:37:02Z","number":5877,"mergeCommitSha":"0d8a83ba817b37bca016046a23a7a925998c89a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5877","title":"Get all uninvited team members when sociallyConnected == false","createdAt":"2023-04-25T18:32:28Z"}
{"state":"Merged","mergedAt":"2023-04-25T19:47:32Z","number":5878,"body":"IntelliJ will now open topics in dashboard.\r\n\r\nDid some cleanup to remove unused code.","mergeCommitSha":"538ba90865705a082ec9502b3cb1e637ae56c91c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5878","title":"Implement Integrate dashboard topic","createdAt":"2023-04-25T19:36:38Z"}
{"state":"Merged","mergedAt":"2023-04-25T20:33:50Z","number":5879,"body":"We were throttling _all_ metrics so that we would record at most one event\nof a specific type per user per 3 hours period. This should've been just\nfor these continuously sampled metrics:\n\n- IdeSidebarViewed\n- IdeInsightsViewed","mergeCommitSha":"78894adc8b9f4f7428afc7e97987535112f20bba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5879","title":"Don't throttle all metrics","createdAt":"2023-04-25T19:59:55Z"}
{"state":"Merged","mergedAt":"2022-03-17T17:12:21Z","number":588,"body":"Update Sidebar's state management and include authstore stream.\r\n\r\n","mergeCommitSha":"b7161fcf0d3bcdb56141c662ed696c722499d7b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/588","title":"Update sidebar data","createdAt":"2022-03-15T21:19:56Z"}
{"state":"Merged","mergedAt":"2023-04-27T18:06:38Z","number":5880,"body":"<img width=\"557\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/234390509-46dd1d68-10c6-4c5b-b22f-937ac90f5020.png\">\r\n","mergeCommitSha":"fdd19ceb7372f01d493f354ac5363b43ed27ba89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5880","title":"Surface user descriptions in the dashboard ","createdAt":"2023-04-25T20:05:34Z"}
{"state":"Merged","mergedAt":"2023-04-25T21:09:03Z","number":5881,"body":"Adds linear graphql and generalizes some graphql common functionality.\r\n","mergeCommitSha":"f76e32c58e7e4d8a9a88a487f2ed2b07e349c63c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5881","title":"Add linear graphql","createdAt":"2023-04-25T20:28:30Z"}
{"state":"Merged","mergedAt":"2023-04-26T20:15:10Z","number":5882,"body":"Updates main sidebar to be declaratively setup. This allows us to use the \"secondary\" property which places the sidebar into the bottom left.\r\n\r\n<img width=\"599\" alt=\"CleanShot 2023-04-25 at 13 34 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/234398037-6ee2b09d-74a6-4622-a7e6-16b6d001aeff.png\">\r\n","mergeCommitSha":"ddb6ed24cf32f6a679f7d4ed673a694a0080314e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5882","title":"Sidebar should load in bottom left tool window section","createdAt":"2023-04-25T20:38:53Z"}
{"state":"Merged","mergedAt":"2023-04-25T21:32:22Z","number":5883,"mergeCommitSha":"a2f86461d3bfd1693006b14deb90358d7c7544c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5883","title":"Ci dependabot fixes","createdAt":"2023-04-25T21:31:14Z"}
{"state":"Merged","mergedAt":"2023-04-25T21:42:40Z","number":5884,"mergeCommitSha":"91492f255ae2518513fb369b46861fd917c97b8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5884","title":"Temporarily log engagement scores","createdAt":"2023-04-25T21:41:18Z"}
{"state":"Merged","mergedAt":"2023-04-25T21:53:15Z","number":5885,"mergeCommitSha":"9d315a2ea7e5c96709a420d8a84e3c70b01779fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5885","title":"Update java docs","createdAt":"2023-04-25T21:53:08Z"}
{"state":"Merged","mergedAt":"2023-04-25T22:06:08Z","number":5886,"mergeCommitSha":"3c910874a410a437646de0546cc98a55302ffbd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5886","title":"Increase java heap memory","createdAt":"2023-04-25T22:06:02Z"}
{"state":"Merged","mergedAt":"2023-04-25T23:02:28Z","number":5887,"body":"Add changes to com.nextchaptersoftware.api.models.converters for the API.","mergeCommitSha":"fbc5928a2cc9d099a2b572bafe81488fbbc92534","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5887","title":"UNB-1203: Experts: Display TeamMember.description","createdAt":"2023-04-25T22:11:42Z"}
{"state":"Merged","mergedAt":"2023-04-25T22:58:16Z","number":5888,"mergeCommitSha":"2d95291dde826625de46f5ab6101261aa358cab2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5888","title":"Improve logging and stuff","createdAt":"2023-04-25T22:49:30Z"}
{"state":"Merged","mergedAt":"2023-04-26T00:38:41Z","number":5889,"mergeCommitSha":"f512d07e7e8341e6b24c2a61e5456b86f8ce0153","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5889","title":"Remove logging","createdAt":"2023-04-25T23:33:14Z"}
{"state":"Merged","mergedAt":"2022-03-16T19:03:59Z","number":589,"body":"Changes:\r\n- Moved all the shared code (except `plugins` package` to a new Gradle project at `/projects/core`\r\n- Added a new `build.gradle.kts` file for core project and moved necessary dependency and build logic to it \r\n- Added the new project as a subproject to Gradle settings \r\n- Imported `core` project in `apiservice` as a dependency \r\n- Moved resources + submodules form `apiservice` to new core project \r\nOur plan is to slowly start taking packages out of core and implement the structure described here: https://www.notion.so/nextchaptersoftware/Service-Architecture-Decomposition-f65d933de2f94c2487de73043efe33f5\r\n\r\nNotes: \r\n- I wasn't able to move the tests to new core project. All test packages work fine but we end up stalling on DB operator extension tests \r\n- My subsequent PR(s) will move APIservice to `/project/services` \r\n\r\nI did a local run of `./gradlew check` and all tests passed ","mergeCommitSha":"7910ab21661d17800e60ee4f9c326bc0c92dbd09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/589","title":"Move shared libs to temporary core project","createdAt":"2022-03-15T21:22:17Z"}
{"state":"Merged","mergedAt":"2023-04-26T23:30:09Z","number":5890,"body":"Update styling with GetStarted Toast in IntelliJ\r\n\r\n<img width=\"604\" alt=\"CleanShot 2023-04-25 at 17 07 40@2x\" src=\"https://user-images.githubusercontent.com/1553313/234433983-32c9814e-daaf-44ac-8f9f-8c51829562f8.png\">\r\n\r\n\r\n","mergeCommitSha":"fbfd34bdbb2f81b2b740c457ca13654cefb9ba43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5890","title":"[unb-1193] style issues with get started unblocked","createdAt":"2023-04-26T00:04:31Z"}
{"state":"Merged","mergedAt":"2023-04-26T01:04:48Z","number":5891,"mergeCommitSha":"0132ae218550075ccf5ef61ee62b32d0fc97836b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5891","title":"Send welcome emails again","createdAt":"2023-04-26T00:15:16Z"}
{"state":"Merged","mergedAt":"2023-04-27T16:59:17Z","number":5892,"body":"and used the results in the ExpertSummaryService","mergeCommitSha":"08b6465ad0569581498e0835abcb87af333c12db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5892","title":"Added TopicExpertStore().findTopicExpertise","createdAt":"2023-04-26T00:20:31Z"}
{"state":"Merged","mergedAt":"2023-04-26T02:25:40Z","number":5893,"body":"- More efficient query\n- No need to check that is SCM provider, we just need isCurrentMember","mergeCommitSha":"d49ee58417833720e09ba080e46e23e94249ee37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5893","title":"Invitees must be members of the team","createdAt":"2023-04-26T00:28:25Z"}
{"state":"Merged","mergedAt":"2023-04-26T02:55:32Z","number":5894,"mergeCommitSha":"419496a23616141e82e37984922eeba0360429e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5894","title":"Revert inadvertent HealthCheck change in #5610","createdAt":"2023-04-26T02:54:17Z"}
{"state":"Merged","mergedAt":"2023-04-26T03:44:45Z","number":5895,"mergeCommitSha":"7c2a57113eb15c018a490f90423a33ccfe3e268a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5895","title":"Try more heap changes","createdAt":"2023-04-26T03:44:38Z"}
{"state":"Merged","mergedAt":"2023-04-26T05:33:56Z","number":5896,"body":"- Revert \"Try more heap changes (#5895)\"\r\n- Try again\r\n","mergeCommitSha":"30b99d601d3daeaf7713bb7567507884b84007cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5896","title":"Revert","createdAt":"2023-04-26T05:33:48Z"}
{"state":"Merged","mergedAt":"2023-04-26T06:53:49Z","number":5897,"body":"The lack of a return type means that ktor is unable to determine the response\r\ncontent type, and the response length. This manifests as a 406, because the\r\n\"unknown\" response type cannot possibly match any acceptable content specified\r\nby a client.\r\n\r\nThis addresses API operations that have no return type API, typically those are\r\noperations that return 202, 204, and sometimes 200.\r\n\r\nWith this fix it doesn’t matter what Accept header is passed in the request.","mergeCommitSha":"fbceed28a59d6b7ec0ace64d686108a02dc4e1a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5897","title":"Fix code-generated responses that have no return type","createdAt":"2023-04-26T06:03:44Z"}
{"state":"Merged","mergedAt":"2023-04-28T18:45:02Z","number":5898,"body":"A primary member is a direct member of a team.\r\n\r\n## Key concepts\r\n\r\n- **Primary member**\r\n  A direct member of the team. If the team is a GitHub team, then these will be the GitHub members of that team only. Confusingly, Slack or JIRA identities are also represented as team member API models for legacy reasons; these non-primary team members may have an optional primary member.\r\n\r\n- **Current member**\r\n  A primary member that is currently part of the team, as opposed to a _past member_.\r\n\r\n- **Past member**\r\n  A primary member that is not currently part of the team, as opposed to a _current member_.\r\n\r\n- **Has Account**\r\n  A primary member that has an Unblocked account. This means that they have signed into the service at least once.\r\n\r\n## Motivation and alternatives\r\n- https://www.notion.so/nextchaptersoftware/Identity-Resolution-Behaviour-c960af33fbab4056bc79f5b0e71cabb9\r\n- https://github.com/NextChapterSoftware/unblocked/pull/5862\r\n\r\n## Refactoring\r\n\r\nRefactor identity and team member models to cleanup:\r\n\r\n- remove `Identity.id`, which is not needed by clients\r\n- make `Identity.username` optional because only some providers have it\r\n- make `Identity.htmlUrl` optional because only some providers have it","mergeCommitSha":"e02de16e4003c05175b3dba6432844dc292b79d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5898","title":"Introduce primary member","createdAt":"2023-04-26T16:14:19Z"}
{"state":"Merged","mergedAt":"2023-04-26T18:55:29Z","number":5899,"body":"Bumped up padding by 2px in each direction to better match existing rows in IntelliJ\r\n<img width=\"632\" alt=\"CleanShot 2023-04-26 at 09 28 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/234647225-32f7eb61-ab54-482a-99c1-db5cea22aab2.png\">\r\n","mergeCommitSha":"b34e9fa8224fcd82b6d884046678678a8153e857","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5899","title":"Update spacing in tree height","createdAt":"2023-04-26T16:52:08Z"}
{"state":"Merged","mergedAt":"2022-01-19T17:33:16Z","number":59,"body":"Sass implementation of theming. Essentially a color theme map in theme.scss is defined which then gets used in the scss code when using `themed` fn within the mixin:\r\n\r\n```\r\n@include themeColors() {\r\n    color: themed($colorAlias);\r\n}\r\n```\r\nThe idea would be that this code would replace most (all?) instances of where we reference color values. Note that the fn has to reference the exact key from the theme map. There are ways to write more mixins to abstract this concept but I wasn't sure if necessary.\r\n\r\nThis is more of a proof of concept for how this could work. There's no dark theme defined yet because there's no lib for it. Storybook seems to have some support for custom theming (https://storybook.js.org/docs/react/configure/theming). Some details still need to be hashed out.","mergeCommitSha":"8a4b94e1c653750dd32f2a015735e3d378b800f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/59","title":"Add theming prototype","createdAt":"2022-01-18T18:04:06Z"}
{"state":"Merged","mergedAt":"2022-03-15T22:49:35Z","number":590,"body":"Next up - implementing SHA236 hash function to transform the emails for the API response","mergeCommitSha":"8a5fbe924e8d5ff54f9428ec1553c31f37ebd4c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/590","title":"Adds scm verified email list to IdentityModel","createdAt":"2022-03-15T21:40:38Z"}
{"state":"Merged","mergedAt":"2023-04-26T16:54:45Z","number":5900,"mergeCommitSha":"9e403c6e028841d3ab0065fc443fab976bb1e1d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5900","title":"Reduce parallelization","createdAt":"2023-04-26T16:54:38Z"}
{"state":"Merged","mergedAt":"2023-04-26T18:15:10Z","number":5901,"body":"We'll be creating identities for team members as part of the work to ingest Jira issues.","mergeCommitSha":"29aad3d542f2954e0c39b305d31e0ab37f9be4f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5901","title":"Add Jira as a new provider type","createdAt":"2023-04-26T17:49:05Z"}
{"state":"Merged","mergedAt":"2023-04-27T19:41:58Z","number":5902,"body":"<img width=\"523\" alt=\"CleanShot 2023-04-26 at 13 12 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/234691608-044b9a24-3b62-4876-8559-c1a9258cd9f6.png\">\r\n","mergeCommitSha":"af604ec6fa0efbba859d66f2b290d14e7781e7c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5902","title":"Update spacing in explorer insights header","createdAt":"2023-04-26T20:13:26Z"}
{"state":"Merged","mergedAt":"2023-04-26T21:02:19Z","number":5903,"mergeCommitSha":"ab9f0b08aa89461399366dfc540c0eeb914fdd1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5903","title":"Software is hard. Quotes are hard. FML","createdAt":"2023-04-26T20:59:53Z"}
{"state":"Merged","mergedAt":"2023-04-26T22:14:19Z","number":5904,"body":"The OAuth 2.0 Integrations can be found at https://developer.atlassian.com/console/myapps/ (login <EMAIL> in shared 1Password vault).","mergeCommitSha":"e35208ff56772b041cb4c9d885b122c45a089b6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5904","title":"Add JiraConfig and OAuth 2.0 integration secrets","createdAt":"2023-04-26T21:32:20Z"}
{"state":"Merged","mergedAt":"2023-05-03T06:35:28Z","number":5905,"body":"Add API to get the installation states for 3rd party integrations (i.e. Slack, JIRA). \r\n\r\nThis would be used to populate this onboarding UI as well as the settings UIs:\r\n<img width=\"384\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/234692155-ea3552e0-3252-4433-bbef-9b51b8d797e2.png\">\r\n\r\nProperties:\r\n* `isInstalled` - whether the integration has been installed with Unblocked\r\n* `installUrl` - url to install integration with Unblocked\r\n* `isUserAuthed` - whether the user has authed with this integration \r\n* `authUrl` - url to auth with the integration for identity resolution\r\n* `provider` - which Provider this is applicable to\r\n\r\nWe would also need an additional flag on the Person model to determine whether or not to show this connection UI at all. The designs show an affordance for the user the ability to skip this step, at which point we should not show the user this UI again, even if they haven't connected anything. (Presumably we would have a separate but similar settings route for the user to auth).","mergeCommitSha":"502ec3590d3a95239b6eb6609ad004d21e5f2c11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5905","title":"[BREAKS API ON MAIN] Get integrations","createdAt":"2023-04-26T21:58:10Z"}
{"state":"Merged","mergedAt":"2023-04-26T21:59:05Z","number":5906,"mergeCommitSha":"53434405a2fd1d97e2fb7a49f8c5e83a4feb6d61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5906","title":"Remove trailing comma from manifest json","createdAt":"2023-04-26T21:58:46Z"}
{"state":"Merged","mergedAt":"2023-05-01T17:15:24Z","number":5907,"body":"Update SidebarToolWindowService to only show filter icon on explorer insights tab.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/234717993-a351134b-abc7-476d-81df-49325765e772.mp4\r\n\r\nAlso Fixes UNB-1118\r\n\r\nhttps://user-images.githubusercontent.com/1553313/234719191-33a91698-8fa0-40d5-9d12-d08f7bc2e706.mp4\r\n\r\n","mergeCommitSha":"98d784077999c4129c27ec40b51f8e70ec622667","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5907","title":"Filter icon only in explorer insights tab","createdAt":"2023-04-26T22:40:55Z"}
{"state":"Merged","mergedAt":"2023-04-26T22:42:19Z","number":5908,"mergeCommitSha":"9518dcdb07f20da42cd709c886415d91b21ac204","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5908","title":"Add node info to VersionInfo API response","createdAt":"2023-04-26T22:42:00Z"}
{"state":"Merged","mergedAt":"2023-04-26T23:02:14Z","number":5909,"body":"Should be JIRA_CLIENT_SECRET","mergeCommitSha":"2f584dc0b9edd5b38ff2f9a8333602f01d4ac59c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5909","title":"Fix jira secret","createdAt":"2023-04-26T22:59:38Z"}
{"state":"Merged","mergedAt":"2022-03-15T22:38:11Z","number":591,"mergeCommitSha":"4fa112134bd2d5eefb67272e5f55be0940970801","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/591","title":"Adds SHA 256 hashing function","createdAt":"2022-03-15T22:28:51Z"}
{"state":"Merged","mergedAt":"2023-04-27T02:50:15Z","number":5910,"mergeCommitSha":"a2963230fafd2b74da78f0f9d22e97495b7d72b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5910","title":"Test some changes","createdAt":"2023-04-26T23:11:30Z"}
{"state":"Merged","mergedAt":"2023-04-27T17:33:56Z","number":5911,"mergeCommitSha":"424287a00e05c636d3f53568bc1e3490e09dfad0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5911","title":"IntelliJ tries to launch node from Hub path, falls back to system node if fails","createdAt":"2023-04-26T23:26:01Z"}
{"state":"Merged","mergedAt":"2023-04-27T22:51:43Z","number":5912,"body":"## Summary\r\n\r\nThere are two parts to this PR, Node download and install, and a slight refactor of how the download and install mechanism works generally for all plugins\r\n\r\n### Node Download and Install\r\n1. Node is downloaded if it doesn't exist to `~/Library/Containers/com.nextchatpersoftware.UnblockedHub/Data/Documents`\r\n2. The installer is run, which unpacks node to `~/Library/Containers/com.nextchatpersoftware.UnblockedHub/Data/Library/Application Support/Node`\r\n3. We shell out to `xattr` to remove quarantine flags on node binary\r\n\r\nStep (3) will require some validation via IDE debugging (it works on my machine, but unsure if it will work for others)\r\n\r\n\r\n### Refactor\r\nThis PR also slightly re-works the plugin update mechanism in the following ways:\r\n1. Plugin install now only happens if you have completed onboarding. Downloading still happens either way. This is a necessary component of the new onboarding flow, where plugin installation doesn't happen until much later in the flow.\r\n2. The logic that determines whether a plugin download is needed now looks to the download version file instead of the installed version file. \r\n3. Launching the installer now depends on whether there is a download/installed version mismatch. In hindsight this is what we always should have done.","mergeCommitSha":"816a97aae8183d68ded214fdf63fc6b8cffe7c3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5912","title":"Download and install node if needed","createdAt":"2023-04-27T01:08:18Z"}
{"state":"Merged","mergedAt":"2023-04-27T02:51:37Z","number":5913,"mergeCommitSha":"13cfd6f86b790da1d5ae2c23f6c97b796132d945","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5913","title":"Helm charts","createdAt":"2023-04-27T02:04:17Z"}
{"state":"Merged","mergedAt":"2023-04-27T03:58:33Z","number":5914,"mergeCommitSha":"8f753e29d395a4b4e0cf2ccc2a6a3231303d54bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5914","title":"Add admin console pages","createdAt":"2023-04-27T03:46:43Z"}
{"state":"Merged","mergedAt":"2023-04-27T04:52:55Z","number":5915,"mergeCommitSha":"bf312d59bdc48b66dbbecbc8cac7441546a252b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5915","title":"Fix linear issue paging","createdAt":"2023-04-27T04:52:26Z"}
{"state":"Merged","mergedAt":"2023-04-27T07:23:54Z","number":5916,"mergeCommitSha":"9a810508be24532d1d8a7f9e296f0f8532876aa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5916","title":"Fix indexing","createdAt":"2023-04-27T06:41:02Z"}
{"state":"Merged","mergedAt":"2023-04-27T16:23:38Z","number":5917,"body":"- Data service\r\n- data\r\n","mergeCommitSha":"9ae53880607cd093b9588ef771ce0955f1949664","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5917","title":"DataService","createdAt":"2023-04-27T15:57:16Z"}
{"state":"Merged","mergedAt":"2023-04-27T16:46:14Z","number":5918,"body":"I don't think there is any reason not to?","mergeCommitSha":"65fc71c2bab696c4a3f6053e33103a8390813821","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5918","title":"Format ktlint on save","createdAt":"2023-04-27T16:13:43Z"}
{"state":"Merged","mergedAt":"2023-04-27T16:25:20Z","number":5919,"mergeCommitSha":"ec1eafe20b92b2bae8734af6040b3cbd32fb8ac6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5919","title":"Add service deployment","createdAt":"2023-04-27T16:25:14Z"}
{"state":"Merged","mergedAt":"2022-03-16T21:33:18Z","number":592,"mergeCommitSha":"2654fc4c018aec115c93761e2818cc77728a8011","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/592","title":"Add hashed emails to teammember api","createdAt":"2022-03-16T00:20:05Z"}
{"state":"Merged","mergedAt":"2023-04-27T17:27:15Z","number":5920,"mergeCommitSha":"f8938f378ddfe5a733b7ebe734f94294ebc18d95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5920","title":"Update glue etl processing","createdAt":"2023-04-27T17:04:50Z"}
{"state":"Merged","mergedAt":"2023-04-27T19:14:09Z","number":5921,"body":"GRPC from Kotlin -> TS was having issues with boolean values === false.\r\nThey would be stripped out and TS client stream dies due to missing stream...\r\n\r\nAlso fixes issue with missing tool window event type.","mergeCommitSha":"d3b258be6a4f9637080c7da98ef0e53b21777b82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5921","title":"Fix issue with webview visibility in IntelliJ","createdAt":"2023-04-27T17:39:21Z"}
{"state":"Merged","mergedAt":"2023-04-27T18:17:36Z","number":5922,"mergeCommitSha":"43039386a79f7074085f05618b00e14798f80cc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5922","title":"Topic processing","createdAt":"2023-04-27T18:16:42Z"}
{"state":"Merged","mergedAt":"2023-04-27T19:06:45Z","number":5923,"mergeCommitSha":"4a4a0cfdc43a7416d149071a5a89c8e3ae4e3e45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5923","title":"Return linear results","createdAt":"2023-04-27T18:50:36Z"}
{"state":"Merged","mergedAt":"2023-04-27T20:02:10Z","number":5924,"mergeCommitSha":"9d9ed7d678282af9411fc4116a49b292ea6f0cbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5924","title":"Add author id support to semantic search provider","createdAt":"2023-04-27T18:51:54Z"}
{"state":"Closed","mergedAt":null,"number":5925,"body":"For the purposes of onboarding/populating this step of onboarding:\r\n<img width=\"778\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/234967818-362778ff-23d6-4326-93a7-5ab07a6a35fa.png\">\r\n\r\n* The goal is to repurpose the existing `listInstallations` API to return all provider installations (this requires updating the backend implementation)\r\n    * This involves updating the `InstallationV2` model to remove `allowAllRepos` as a required field. I don't think this requires a rev since it's only referenced in GETs but will rev this model if necessary\r\n* Add ability to `deleteInstallation`\r\n* Rev and update existing scm-specific installation endpoints to be `ScmInstallation` tag-based for clarity","mergeCommitSha":"81a3c2bdbfbf8c79d97a106199c31d941feef103","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5925","title":"Repurpose installation APIs","createdAt":"2023-04-27T19:15:12Z"}
{"state":"Merged","mergedAt":"2023-04-27T23:08:55Z","number":5926,"mergeCommitSha":"8a581234a17cdb2f45eb2fe3a00e389899f8c8b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5926","title":"Synchronize access to UpdateService related operations to prevent races","createdAt":"2023-04-27T20:19:03Z"}
{"state":"Merged","mergedAt":"2023-04-27T23:09:34Z","number":5927,"mergeCommitSha":"58283dcdf67e6459f2f8dca6a6c62a9ab61b6a63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5927","title":"Remove long-time deprecated caches","createdAt":"2023-04-27T20:24:23Z"}
{"state":"Merged","mergedAt":"2023-04-27T23:20:32Z","number":5928,"body":"Previous PR converting the `UpdateService` to an actor will guarantee that the download will finish before this installation call is made. \r\n\r\nSubsequent PR will wire up an `ObservableObject` so that the Onboarding UX can observe the installation state and show a progress spinner at the end of onboarding until the download/install is complete","mergeCommitSha":"458377df4a3ff743c47ed6f7ce180990f5e06773","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5928","title":"Attempt to install plugins at tour step during onboarding","createdAt":"2023-04-27T20:45:43Z"}
{"state":"Merged","mergedAt":"2023-04-27T21:11:16Z","number":5929,"body":"This will be used for the Jira OAuth flow. I'll be wiring up the Auth Service with these libraries in a separate PR.","mergeCommitSha":"e0b53f72a2e46c9075f86ddc7227e9bcbc9b0b42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5929","title":"Add client-jira and lib-jira-ingestion libs","createdAt":"2023-04-27T20:53:28Z"}
{"state":"Merged","mergedAt":"2022-03-16T16:56:07Z","number":593,"body":"Adds support for `code InlineElements`","mergeCommitSha":"6e7a291ee61e47bab41ffcc13ddcf547b991cdfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/593","title":"Add code InlineElement","createdAt":"2022-03-16T16:18:35Z"}
{"state":"Merged","mergedAt":"2023-04-27T23:31:23Z","number":5930,"mergeCommitSha":"524c90c7db61771b25de132b5fd569b199ff0a36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5930","title":"Only download plugins for installed IDEs","createdAt":"2023-04-27T21:32:38Z"}
{"state":"Merged","mergedAt":"2023-04-27T23:19:39Z","number":5931,"body":"I think this might solve an issue both Dennis and Ben have observed where their hub shuts itself down. The theory I have is as follows:\r\n- New update is downloaded\r\n- Installer is launched\r\n- Installer sends termination signal to hub, but hub doesn’t terminate (for a reason I don’t fully understand)\r\n- Installer successfully drops the bits for the hub\r\n- Sometime later, the hub is terminated, but doesn't come back up because the installer has already exited\r\n\r\nInstead of playing nice, we should probably force kill the hub to ensure its lifecycle is fully under the installer's control","mergeCommitSha":"03f65bfdcdd88186343635bc4c4e45ccc485939f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5931","title":"Force kill the hub from the installer. It seems that in some cases the Hub is not terminating gracefully","createdAt":"2023-04-27T22:13:03Z"}
{"state":"Merged","mergedAt":"2023-04-27T23:17:56Z","number":5932,"body":"Add an explict 280 character limit on summaries, also removed some language that seemed to cause some crazy overfitting on partner data.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-1214/topic-summaries-tune-prompt-queries","mergeCommitSha":"e101311f516e234785a526cf52ce3abfa8afd2a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5932","title":"UNB-1214: Topic summaries: Tune Prompt + Queries","createdAt":"2023-04-27T23:05:48Z"}
{"state":"Merged","mergedAt":"2023-05-01T17:48:34Z","number":5933,"body":" \r\n<img width=\"435\" alt=\"CleanShot 2023-04-27 at 16 28 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/235011873-ecba2ba1-f5e8-44e3-8c30-ad7547a01636.png\">\r\n","mergeCommitSha":"8dfc217806ffb86b190b81a02881561ec1dd44a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5933","title":"Update button size of toast","createdAt":"2023-04-27T23:16:52Z"}
{"state":"Merged","mergedAt":"2023-04-28T04:58:18Z","number":5934,"body":"- thread create\n- message create\n- search","mergeCommitSha":"4c7ad7f138d0ada43bc0c6642642d607f93ad397","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5934","title":"Capture new user activities","createdAt":"2023-04-28T00:14:35Z"}
{"state":"Closed","mergedAt":null,"number":5935,"body":"Update clients to no longer \"poll\" metrics on sidebars.\r\nWill send an open event and close event based on visibility.\r\n","mergeCommitSha":"840870ff6f5c6d3b2eb3a2160f2c0e6fc32bb1a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5935","title":"[unb-1182] handle viewed metrics","createdAt":"2023-04-28T00:31:26Z"}
{"state":"Merged","mergedAt":"2023-04-28T00:35:21Z","number":5936,"mergeCommitSha":"5ad4440b6e9a7f9c5889cd157fa51fa161f39a91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5936","title":"Use placeholder","createdAt":"2023-04-28T00:35:08Z"}
{"state":"Merged","mergedAt":"2023-04-28T01:14:01Z","number":5937,"mergeCommitSha":"a05cda0646eb68862d4797ad93d1975f8f0e5e11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5937","title":"Fix log","createdAt":"2023-04-28T01:13:55Z"}
{"state":"Merged","mergedAt":"2023-04-28T02:06:09Z","number":5938,"mergeCommitSha":"e6e9916feac29460d9fbd003d66f7677d78237ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5938","title":"Fixes not saving version info on first time install","createdAt":"2023-04-28T02:01:21Z"}
{"state":"Merged","mergedAt":"2023-04-28T05:48:47Z","number":5939,"mergeCommitSha":"f6efc131887abc097517058024c1dafcf3b5a020","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5939","title":"Prevent notifications in the hub until team access is enabled","createdAt":"2023-04-28T03:49:47Z"}
{"state":"Merged","mergedAt":"2022-03-16T22:29:05Z","number":594,"body":"![image](https://user-images.githubusercontent.com/13431372/158639226-42c8c27f-275c-46c8-abb5-cf899430bfe2.png)\r\n* Currently using our own API to fetch the anchor SM -- this will be replaced by the local SM db once that is set up\r\n* Will do web in next PR\r\n* Removed ability to createThread from the temp view on the web since we can do it on vscode now and the args didn't make sense anymore (I'll remove this view altogether in the web PR)\r\n* READ: There's additional API work needed to save the language id of the code snippet via our API (I'll add this in a separate PR) so that other clients know how to style/syntax highlight the code snippets\r\n    * Note: we're inferring from the file extension for now and should probably still do this as a backup\r\n\r\n","mergeCommitSha":"829b7db20f1553d78d9266a77a11cdaac4de16f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/594","title":"Fetch and display anchor sourcemark on vscode","createdAt":"2022-03-16T16:46:39Z"}
{"state":"Merged","mergedAt":"2023-04-28T05:47:58Z","number":5940,"mergeCommitSha":"f1a63c8f65b5e3c83c67877f8bbc917712e2c4f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5940","title":"Show IDE restart dialog if jetbrains is running","createdAt":"2023-04-28T05:37:37Z"}
{"state":"Merged","mergedAt":"2023-04-28T06:46:16Z","number":5941,"mergeCommitSha":"d746b068243fdf24837f7008799ea35691f0e970","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5941","title":"Bump","createdAt":"2023-04-28T06:45:39Z"}
{"state":"Merged","mergedAt":"2023-04-28T17:42:33Z","number":5942,"body":"- \"Today\" was actually last 24 hours. Now fixed.\n- Sometimes we get duplicates. Use debounce to fix.","mergeCommitSha":"686fee10fdc732a08b28687e7151556058878873","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5942","title":"Activity metric fixes","createdAt":"2023-04-28T16:59:22Z"}
{"state":"Merged","mergedAt":"2023-04-28T19:23:07Z","number":5943,"mergeCommitSha":"3a992c4c6c7ab26dfc5be79581dfcebf37a3913d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5943","title":"Fix service template","createdAt":"2023-04-28T19:22:42Z"}
{"state":"Merged","mergedAt":"2023-04-28T19:26:38Z","number":5944,"mergeCommitSha":"8c340518b129b809dd4df0efd04dd77b9117c9f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5944","title":"Cleanup imports","createdAt":"2023-04-28T19:26:24Z"}
{"state":"Merged","mergedAt":"2023-05-11T22:49:01Z","number":5945,"mergeCommitSha":"4d0961366c9697f7c347d7a4004cef847708727b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5945","title":"added cancelself to the set of admin identities","createdAt":"2023-04-28T20:26:50Z"}
{"state":"Merged","mergedAt":"2023-04-28T20:34:12Z","number":5946,"mergeCommitSha":"aa6821d4ee31808fe4b88bc3b2f1eeb01548ce10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5946","title":"Use Jetbrains undocumented CLI command to gracefully restart IDE","createdAt":"2023-04-28T20:33:14Z"}
{"state":"Merged","mergedAt":"2023-05-08T18:00:24Z","number":5947,"body":"Fix logout. Had an issue where API refresh middleware was instantly re-authenticating after logout.\r\n\r\nUpdate Insight toolwindow to close on logout.","mergeCommitSha":"38f999ec16e11af3b240e5111ef90fd1669a0087","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5947","title":"[unb-1083] logged out state for sidebars","createdAt":"2023-04-28T21:07:12Z"}
{"state":"Merged","mergedAt":"2023-04-28T22:59:28Z","number":5948,"body":"https://linear.app/unblocked/issue/UNB-1205/expert-summaries-tune-prompts-queries\r\n\r\nNext week we are going to be plumping datetime, authors, topics through the search metadata and embeddings, which should improve these results even more without changing the prompts. In short, we are working on training the LLM what time is, what are approved topics and related experts. \r\n\r\nAt the same time, we are going to pull the prompts out of the source, so we can drive them along side search in the adminweb, which means anyone can change the prompts or run them for a specific team.\r\n\r\nFollow along @ https://linear.app/unblocked/project/ml-8c936278fc03","mergeCommitSha":"093c4446668b6b031340dd710fc6700d7d41b553","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5948","title":"Improved topic + expert prompts","createdAt":"2023-04-28T22:18:56Z"}
{"state":"Merged","mergedAt":"2023-04-28T22:48:42Z","number":5949,"mergeCommitSha":"1a9448a54a32df11293b06270edef72128a626f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5949","title":"Fix booleans","createdAt":"2023-04-28T22:39:17Z"}
{"state":"Merged","mergedAt":"2022-03-16T18:10:14Z","number":595,"mergeCommitSha":"378fdd8c409bc7fbd0650d184049ab814f04fee9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/595","title":"Client mapped data streams","createdAt":"2022-03-16T17:06:18Z"}
{"state":"Merged","mergedAt":"2023-05-01T15:09:27Z","number":5950,"body":"When we get an installation lifecycle event, we'll encrypt the shared secret before placing onto the queue.\r\n\r\nNext PR will add queue event handler where we'll save the encrypted shared secret in the database.","mergeCommitSha":"1ca7de67453255dc09d4ee45d4743ad662245b62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5950","title":"Enqueue Jira webhooks","createdAt":"2023-04-28T22:50:47Z"}
{"state":"Merged","mergedAt":"2023-04-29T00:03:13Z","number":5951,"mergeCommitSha":"c33678bcca0d4c0ed394c893d9f84bdc70bdd864","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5951","title":"Add bot","createdAt":"2023-04-28T23:17:12Z"}
{"state":"Merged","mergedAt":"2023-04-29T00:12:17Z","number":5952,"mergeCommitSha":"241836590eaa0b0852e08ae637f6e5931bc6c3e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5952","title":"Enable bot","createdAt":"2023-04-29T00:05:18Z"}
{"state":"Merged","mergedAt":"2023-04-29T23:08:53Z","number":5953,"body":"- GitHub and Slack identity fixtures from real teams.\r\n- Basic algorithm, which is rubbish right now, but regression tests will track improvement.\r\n\r\nSee \"Identity Resolution Behaviour\" doc:\r\nhttps://www.notion.so/nextchaptersoftware/Identity-Resolution-Behaviour-c960af33fbab4056bc79f5b0e71cabb9?pvs=4","mergeCommitSha":"177ea2479998ad46ef7faecafe012ff6f8ff8e35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5953","title":"Introduce test fixtures for identity alignment","createdAt":"2023-04-29T06:57:34Z"}
{"state":"Merged","mergedAt":"2023-04-30T05:50:58Z","number":5954,"body":"Uses Damerau-Levenshtein distance to achieve better results, more matches.","mergeCommitSha":"668689d41571866b6e531b0b9d0a877e9a6fb3f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5954","title":"Use text similarity for identity alignment","createdAt":"2023-04-30T03:08:35Z"}
{"state":"Merged","mergedAt":"2023-04-30T16:35:14Z","number":5955,"body":"Non-English names often contain accents and diacritics, which when normalized can result in more matches.","mergeCommitSha":"1c8da175267d3473d97cdae6ada5639cf88c8074","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5955","title":"Normalize for accents to improve identity alignment","createdAt":"2023-04-30T16:07:00Z"}
{"state":"Merged","mergedAt":"2023-04-30T17:22:40Z","number":5956,"mergeCommitSha":"94b0ffa537cd02f5dc251d706f9dc6e15ba22abe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5956","title":"Inspired by Richard Bresnan, linear","createdAt":"2023-04-30T16:17:48Z"}
{"state":"Merged","mergedAt":"2023-04-30T17:28:23Z","number":5957,"mergeCommitSha":"f3605ed05d50ea2d39e9b188d59046aea6e10853","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5957","title":"Inspired by Richard Bresnan -> Integration emails","createdAt":"2023-04-30T16:43:03Z"}
{"state":"Merged","mergedAt":"2023-04-30T21:02:21Z","number":5958,"mergeCommitSha":"4f4998034d84dadf1e26e8c9cc134c015a15418e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5958","title":"Attempt to clean orphaned processs","createdAt":"2023-04-30T19:13:38Z"}
{"state":"Closed","mergedAt":null,"number":5959,"body":"Lowercase emails before hashing to ensure that the same email is always hashed to the same value.","mergeCommitSha":"493faf76f01dc9378f169cb449f48569270858f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5959","title":"Normalize hashed emails by lowercasing","createdAt":"2023-04-30T20:25:04Z"}
{"state":"Merged","mergedAt":"2022-03-17T17:12:44Z","number":596,"body":"<img width=\"734\" alt=\"CleanShot 2022-03-16 at 10 07 51@2x\" src=\"https://user-images.githubusercontent.com/1553313/158647500-78124567-24e8-44a5-87b2-ad1499596eb0.png\">\r\n\r\nPulled in Git code from OctoberDemo to fetch log & blame.\r\n\r\nCurrently parsing Git data and generating GitContributors for UI.\r\n\r\n**TODO**\r\nMap git emails to unblocked identities. \r\n","mergeCommitSha":"6b7befc1cb929ea1fbb097c8899d9e2f08c1bf8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/596","title":"Setup Contributors in KnowledgeCreation","createdAt":"2022-03-16T17:09:25Z"}
{"state":"Merged","mergedAt":"2023-04-30T21:03:10Z","number":5960,"mergeCommitSha":"dbb6f074d2affd5d72c6e0c648c52a4f4adbb218","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5960","title":"Only record search metrics if not in read-only mode","createdAt":"2023-04-30T20:34:35Z"}
{"state":"Closed","mergedAt":null,"number":5961,"mergeCommitSha":"261999ff732d2b82cf08082d1b60b742123ff17d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5961","title":"Attempt to fix stale tests","createdAt":"2023-04-30T21:17:19Z"}
{"state":"Merged","mergedAt":"2023-04-30T22:58:58Z","number":5962,"mergeCommitSha":"70f2c172fed13614fa32487f675f6d7c4fe7988e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5962","title":"Latency test","createdAt":"2023-04-30T22:58:45Z"}
{"state":"Merged","mergedAt":"2023-05-02T14:44:37Z","number":5963,"mergeCommitSha":"e0474a966fca9eceac5b3a5b7444e9d28abf2212","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5963","title":"Add dark mode notification swirl image for onboarding","createdAt":"2023-05-01T16:22:27Z"}
{"state":"Closed","mergedAt":null,"number":5964,"body":"Putting this in the SCM service for now. I think it makes sense to spin up a new service for Jira though, and to move this there.","mergeCommitSha":"84bb6f6b8245c4d7e475c75b3bdd0d0bbe586992","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5964","title":"Add Jira webhook event handlers","createdAt":"2023-05-01T16:31:35Z"}
{"state":"Merged","mergedAt":"2023-05-01T17:23:46Z","number":5965,"body":"- jira service\r\n- Update reqs\r\n","mergeCommitSha":"23eedf0e51e89c6998346203ec77b255d3797051","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5965","title":"JiraService","createdAt":"2023-05-01T17:05:20Z"}
{"state":"Merged","mergedAt":"2023-05-08T19:33:27Z","number":5966,"body":"Setup IntelliJ client to properly authenticate and fetch video files.\r\n\r\n*ISSUE* \r\nWith this, the client will now pull down h265. \r\nUnfortuantely, IntelliJ JCEF browser does not support the codec. Will require setting up pipeline to go from h265 to \r\n```\r\nTheora\r\nVP8\r\nVP9\r\n```","mergeCommitSha":"55e01650eccd1a8a31f5fafbeb4d7ce94915f234","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5966","title":"Setup Video support in IntelliJ","createdAt":"2023-05-01T17:21:18Z"}
{"state":"Merged","mergedAt":"2023-05-01T19:35:14Z","number":5967,"mergeCommitSha":"69e314a0bc0759f85a94f92c8be22173375e27ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5967","title":"Identity alignment completion progress","createdAt":"2023-05-01T18:17:32Z"}
{"state":"Merged","mergedAt":"2023-05-01T21:55:53Z","number":5968,"mergeCommitSha":"ec2141a63d11d1b48efb38d2cfb4c4ffd0ec273a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5968","title":"[BREAKS API ON MAIN] Fix team member primary ID type and add web links","createdAt":"2023-05-01T19:33:29Z"}
{"state":"Merged","mergedAt":"2023-05-01T19:44:08Z","number":5969,"body":"- Manually provisioned KMS multi-region keys\r\n- Added IAM service account permissions to allow secret service to access this key\r\n\r\nAll details can be found here: https://www.notion.so/nextchaptersoftware/Global-Multi-Region-Encryption-Keys-8d5cb2d4387a4a24b887e67598117a9c?pvs=4","mergeCommitSha":"9c47b7a7ca2e2552168bc3b1138dae4895d726e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5969","title":"added multi-region key IAM permissions to secretservice account","createdAt":"2023-05-01T19:35:15Z"}
{"state":"Merged","mergedAt":"2022-03-17T21:15:16Z","number":597,"body":"This makes it more inline with how Markdown handles images https://spec.commonmark.org/0.30/","mergeCommitSha":"1c8aa99b7ca83cd538e69c947b6a8d576a557d96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/597","title":"Make image an InlineElement","createdAt":"2022-03-16T21:21:01Z"}
{"state":"Merged","mergedAt":"2023-05-01T20:01:49Z","number":5970,"mergeCommitSha":"3d798bfa6a6d14a16a8bc7dd6284952a667f1d92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5970","title":"Need to handle multiple video transcoder pipelines.","createdAt":"2023-05-01T19:50:44Z"}
{"state":"Merged","mergedAt":"2023-05-05T20:13:34Z","number":5971,"mergeCommitSha":"62dad2a1793b30325a70b609e3d59a52a302e0a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5971","title":"Adds a 'No IDEs installed onboarding step'","createdAt":"2023-05-01T19:59:10Z"}
{"state":"Merged","mergedAt":"2023-05-03T18:52:00Z","number":5972,"body":"![CleanShot 2023-05-01 at 11 39 46@2x](https://user-images.githubusercontent.com/1553313/235521436-d2e8caf8-e493-4552-bf79-b1df6a49cd77.png)\r\n","mergeCommitSha":"94fba2987bba5d7c19948aa9527237f0270c4fd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5972","title":"Update Slack header colour","createdAt":"2023-05-01T20:03:29Z"}
{"state":"Merged","mergedAt":"2023-05-01T20:32:10Z","number":5973,"mergeCommitSha":"df0afb74913486206afa02c4a74d70294d9c3204","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5973","title":"Add Jira webhook event handlers","createdAt":"2023-05-01T20:21:41Z"}
{"state":"Merged","mergedAt":"2023-05-01T20:36:53Z","number":5974,"mergeCommitSha":"2621f9251d414ead2973236c1912ad0bd4deade9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5974","title":"Trigger vp9 pipeline","createdAt":"2023-05-01T20:36:03Z"}
{"state":"Closed","mergedAt":null,"number":5975,"body":"- Trigger vp9 pipeline\r\n- We need to restore vp9 video for intellij\r\n","mergeCommitSha":"c4dbcef32f9c364961db21d0c7b8f5e1016304fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5975","title":"EnsureWeReturnCorrectVideoAsset","createdAt":"2023-05-01T21:11:36Z"}
{"state":"Merged","mergedAt":"2023-05-01T21:13:13Z","number":5976,"mergeCommitSha":"bce199746e4b141196ccab3d47b8c2f47d077bcb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5976","title":"We need to restore vp9 video for intellij","createdAt":"2023-05-01T21:13:04Z"}
{"state":"Merged","mergedAt":"2023-05-02T04:47:03Z","number":5977,"body":"The shared secret will be stored in *unencrypted* form for now (aim is to fix this before launch)","mergeCommitSha":"861e7c004322afe2cdded718fb496ab015a95bae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5977","title":"Add JiraSiteModel","createdAt":"2023-05-01T23:02:03Z"}
{"state":"Merged","mergedAt":"2023-05-02T00:41:23Z","number":5978,"body":"fixes https://linear.app/unblocked/issue/UNB-1238/identity-align-use-github-graphql-api-to-get-member-display-names\r\n\r\nNow we collect display names for users who have not created an unblocked account.\r\n<img width=\"741\" alt=\"Screenshot 2023-05-01 at 16 37 04\" src=\"https://user-images.githubusercontent.com/1798345/*********-c8d9a6be-d56a-487e-a10f-96a743436fe1.png\">\r\n","mergeCommitSha":"35d45143259b727b72815da3d2be6403a447480f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5978","title":"Use GitHub GraphQL API to get member display names","createdAt":"2023-05-01T23:23:00Z"}
{"state":"Merged","mergedAt":"2023-05-02T22:12:20Z","number":5979,"body":"A first cut at an Unresolved Source Mark UIs in JetBrains.  Here is the workflow:\r\n\r\n* When we'd like to view a source mark location but the mark can't be resolved, the agent requests the \"Unresolved File\" call via the promise proxy.  This hasn't changed.\r\n* Plugin creates an `UnresolvedFileSession` instance.  This is a new class that represents a single \"session\" for an unresolved mark.  It is responsible for displaying the correct UI for the resolution state, providing git actions to help resolve the SM, etc.  This class exists until the SM is correctly resolved, or the editor is closed.\r\n* Whenever the user attempts a resolution action (git pull, checkout, etc), `UnresolvedFileSession` runs that locally in the plugin, then calls to the agent to re-resolve the mark (new API call: `resolveSourceMark`)\r\n\r\nNote that this PR has placeholder UIs for the banner and editor.  I will finalize those in the next PR.","mergeCommitSha":"edaa1ce75118001f5e6ea29555de9b7885f5fdf3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5979","title":"JetBrains unresolved source mark UI","createdAt":"2023-05-01T23:53:47Z"}
{"state":"Merged","mergedAt":"2022-03-16T21:42:22Z","number":598,"body":"- Moved `nowWithMicrosecondPrecision` from db.common to `utils` package ( a metric ton of imports had to be changed)\r\n- Moved security, http and utils packages to a new project\r\n- Added a new trimmed down version of build.gradle.kts for the new project\r\n- Imported the project in core build.gradle and settings.gradle\r\n\r\nVerified locally and all tests passed.","mergeCommitSha":"925bff63773be5df0d00ee18b6b5a6b472f21b83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/598","title":"Creating Utils gradle project","createdAt":"2022-03-16T21:32:39Z"}
{"state":"Merged","mergedAt":"2023-05-02T02:45:46Z","number":5980,"body":"Much better results on local. upstreaming to dev + prod.\r\nhttps://linear.app/unblocked/issue/UNB-1222/ml-pull-the-prompts-out-of-inference-endpoints","mergeCommitSha":"f5f6b8e2fab9deb302507bbc59292f8e1e0899e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5980","title":"Unb 1222 removed prompts from inference endpoints","createdAt":"2023-05-02T02:13:40Z"}
{"state":"Merged","mergedAt":"2023-05-02T06:10:48Z","number":5981,"body":"Lowercase emails before hashing to ensure that the same email is always hashed to the same value.\r\n\r\nRan into client issues in previous attempt (#5959). This is a super simpler alternative.","mergeCommitSha":"689ed5652c43881811c698801ef89d4fdaf4b26e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5981","title":"Normalize hashed emails by lowercasing","createdAt":"2023-05-02T04:46:22Z"}
{"state":"Merged","mergedAt":"2023-05-02T20:24:14Z","number":5982,"body":"Open PRs & thread messages directly in GH, bypassing web extension.\r\n\r\nWhen opening a \"thread\", opens to latest message in thread.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/235586594-9fbb20b1-bec4-4c3f-b830-ed4190266b8c.mp4\r\n\r\n","mergeCommitSha":"d3471dd7a3d6af5c2aa85a3e6cc2864ef154f52c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5982","title":"[unb-1243] open prs directly in gh","createdAt":"2023-05-02T05:31:31Z"}
{"state":"Merged","mergedAt":"2023-05-02T07:27:55Z","number":5983,"body":"In linear a \"display name\" means \"nickname\",\r\nand \"name\" means \"full name\".\r\n\r\nSo the mapping to our concept was inverted.","mergeCommitSha":"3088a14d7874ea8674ce7e191603c0134e50c25f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5983","title":"Linear display name and username were permuted","createdAt":"2023-05-02T06:27:16Z"}
{"state":"Merged","mergedAt":"2023-05-02T07:28:12Z","number":5984,"body":"We now collect:\n- Slack emails (thanks to Rashin!)\n- GitHub display names of user without accounts\n\nRerunning the alignment tests with the new data leads to significant improvement,\nfrom 42% of team matched to 62% of team matched.","mergeCommitSha":"e7c9dbc4630541cea3192cab1a0ac743661cd5b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5984","title":"Update tests after slack email ingest and github display name ingest","createdAt":"2023-05-02T06:57:55Z"}
{"state":"Merged","mergedAt":"2023-05-02T07:49:33Z","number":5985,"mergeCommitSha":"dbc5a2330209d01757cccac21fc10a6996cde5a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5985","title":"Identity align: compare usernames with display names gets better results","createdAt":"2023-05-02T07:27:33Z"}
{"state":"Merged","mergedAt":"2023-05-02T16:53:06Z","number":5986,"body":"    - Added installation instruction for Keda pod autoscaler\r\n    - Modified global network policy to allow traffic to and from keda namespace\r\n    - Added Keda auto scaler resource to helm charts with a cpu based trigger\r\n    - Modified apiservice, authservice and pusherservice to enable auto-scaler in Dev. Prod config is commented out for now\r\n    - Updated Falco to exclude Keda from alerts when contacting Kube API\r\n    - Regenerated all helm charts\r\n\r\n\r\n\r\nThis change will enable autoscaler for the 3 services mentioned above in Dev. Once we have validated the behavior and are confident we can enable prod. I would like to start with the CPU based trigger before adding support for queue based triggers.","mergeCommitSha":"a268aa612db6b7bfb4ef25e080fbed3ca91aa935","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5986","title":"Add keda scaler","createdAt":"2023-05-02T16:45:04Z"}
{"state":"Merged","mergedAt":"2023-05-02T17:56:01Z","number":5987,"mergeCommitSha":"ee5d903f9b6d73a973787e424f07dfd90c618b75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5987","title":"Add user information to bot","createdAt":"2023-05-02T17:55:54Z"}
{"state":"Merged","mergedAt":"2023-05-02T18:50:07Z","number":5988,"body":"This is the JWT we'll use to hit the Jira API for issue ingestion. This is signed by the shared secret we receive as part of the marketplace app install.\r\n\r\nI had to add \"https://packages.atlassian.com/maven-public\" to the list of maven repositories to look up for package resolution. If we don't like this, I can look into checking in the jar instead.\r\n\r\nhttps://developer.atlassian.com/cloud/jira/platform/understanding-jwt-for-connect-apps/","mergeCommitSha":"99fe7a872e84b0ba0579fdc6031aeac146b0c678","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5988","title":"Replace manual JWT generation with atlassian-jwt lib","createdAt":"2023-05-02T17:57:57Z"}
{"state":"Merged","mergedAt":"2023-05-02T19:25:10Z","number":5989,"body":"Jsut housekeeping.","mergeCommitSha":"d9bdea306a42f01aae68c64c762a15221176a126","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5989","title":"Consolidate auth code (phase 1)","createdAt":"2023-05-02T18:59:44Z"}
{"state":"Merged","mergedAt":"2022-03-17T00:13:53Z","number":599,"body":"Temp disable to unblock.\r\n\r\nDue to https://github.com/NextChapterSoftware/unblocked/issues/578","mergeCommitSha":"82683a043c0c0913b6b4814f5260fcac640b5c14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/599","title":"Temporarily disable throwAPIError","createdAt":"2022-03-16T23:26:37Z"}
{"state":"Merged","mergedAt":"2023-05-02T20:03:19Z","number":5990,"mergeCommitSha":"ca1e991ce9665ee0ea47de9fe14b49f63fd0e1cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5990","title":"Standardize oauth api","createdAt":"2023-05-02T19:50:54Z"}
{"state":"Merged","mergedAt":"2023-05-02T20:57:39Z","number":5991,"mergeCommitSha":"688dc2d1a3211cf96441e616bde3e7310eadac8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5991","title":"Might have to change this more as we go forward","createdAt":"2023-05-02T20:17:14Z"}
{"state":"Merged","mergedAt":"2023-05-02T20:44:49Z","number":5992,"body":"Extracts the header containing the jwt (\"Authorization: JWT ...\") and includes it in `JiraWebhookEventPayload` to be validated in the `JiraWebhookEventHandler`. The idea here is to use the shared secret received as part of the Marketplace app install event to validated the JWT included with the incoming webhook. By doing this, we'll know the team that maps to the webhook.\r\n\r\nThis is just a skeleton. We'll add the actual verification in a separate PR.","mergeCommitSha":"8b848132b660e7af8d62d39abff38c4510974d51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5992","title":"Add skeleton for validating Jira webhooks","createdAt":"2023-05-02T20:33:48Z"}
{"state":"Merged","mergedAt":"2023-05-02T21:42:46Z","number":5993,"mergeCommitSha":"7d350d79e0078bcec5fbff21d37f597ea736d15c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5993","title":"Standardize client factories","createdAt":"2023-05-02T20:59:57Z"}
{"state":"Merged","mergedAt":"2023-05-02T21:13:08Z","number":5994,"body":"Also changes issue search to use the V2 rest api since that returns issue and comment bodies in markdown instead of [Atlassian Document Format](https://developer.atlassian.com/cloud/jira/platform/apis/document/structure/), which makes it easier to convert to our MessageBody format since we already have converters for markdown. Also, the v3 rest api is still in beta.","mergeCommitSha":"502e2a37d1c32e146d3f7f8a964e989b7a1436f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5994","title":"Add more Jira models","createdAt":"2023-05-02T21:05:50Z"}
{"state":"Merged","mergedAt":"2023-05-04T07:59:00Z","number":5995,"mergeCommitSha":"e4d08732c176b1cd03882cf3f2104ad1b451e5b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5995","title":"Identity Align: persist alignment in PG with the confidence value","createdAt":"2023-05-02T21:05:59Z"}
{"state":"Merged","mergedAt":"2023-05-08T17:19:26Z","number":5996,"body":"Setup source mark generation for IntelliJ.\r\n\r\nRefactored WalkthroughAppApi into shared ide. IntelliJ Agent now uses same functionality and streams to push source marks to video app.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/235794658-a7e2318a-c0cf-4a4a-82db-f71b366ed771.mp4\r\n\r\n","mergeCommitSha":"d13610f65552dd8de150ca272350ab86fc140dae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5996","title":"[unb-1244] provide sourcemarks from intellij to","createdAt":"2023-05-02T21:56:29Z"}
{"state":"Merged","mergedAt":"2023-05-02T23:01:23Z","number":5997,"body":"Added required rbac permission for deployer user to create keda crds\r\n\r\nI have already deployed this change and deployments worked fine. ","mergeCommitSha":"565a50886fa80c0994aebaff37adc52c31032219","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5997","title":"Added required rbac permission for deployer user to create keda crds","createdAt":"2023-05-02T22:42:56Z"}
{"state":"Merged","mergedAt":"2023-05-02T23:20:25Z","number":5998,"body":"We are using `systems` to talk about the 3rd party apps/systems that developers use for authz/n, so are going to remove that language from the prompts and therefore topic + expert summaries.","mergeCommitSha":"7faa5efb576532c1e5c4cc89a9cf76108eff6ffb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5998","title":"Removed the notion of `system` from the prompts","createdAt":"2023-05-02T23:02:53Z"}
{"state":"Merged","mergedAt":"2023-05-02T23:49:24Z","number":5999,"mergeCommitSha":"f205bbefba509b88a795d60c2b99163500a0f0ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5999","title":"Map new provider types to icon assets","createdAt":"2023-05-02T23:26:39Z"}
{"state":"Merged","mergedAt":"2021-12-14T18:17:53Z","number":6,"mergeCommitSha":"35e8b5f2465a59bd648b67ba53d6b3c60db67f8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6","title":"Vscode integrations","createdAt":"2021-12-10T23:15:17Z"}
{"state":"Merged","mergedAt":"2022-01-18T18:50:12Z","number":60,"body":"So that we can test #56 ","mergeCommitSha":"1a218ef61f25ce760263cc9bc95e46ef05355545","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/60","title":"Workflows run when workflow definitions change","createdAt":"2022-01-18T18:36:53Z"}
{"state":"Merged","mergedAt":"2022-03-17T04:20:06Z","number":600,"body":"The web dashboard client needs to be able to fetch the latest sourcepoint for an anchorSourceMarkId, but it doesn't have any information about commit hashes or file hashes. This change adds a new arg to the request body to request the original source point, which is what the web dashboard needs.","mergeCommitSha":"3b7558b1b785278ce5ceacaebc3ed17b328461b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/600","title":"Update LatestSourcePointReq to include isOriginal bool","createdAt":"2022-03-16T23:33:56Z"}
{"state":"Merged","mergedAt":"2023-05-04T08:14:30Z","number":6000,"mergeCommitSha":"8393feb09e403dadf9c7fb0d33a24d7b272f759b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6000","title":"Admin: Identity Alignment shown in admin console","createdAt":"2023-05-02T23:41:32Z"}
{"state":"Merged","mergedAt":"2023-05-03T00:18:16Z","number":6001,"mergeCommitSha":"d26548b0eac36c393cc5d8a9c319ec3c577e0ddd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6001","title":"More refactoring for what I need","createdAt":"2023-05-03T00:00:15Z"}
{"state":"Merged","mergedAt":"2023-05-03T03:35:35Z","number":6002,"body":"This will be used for validating incoming webhooks. When we receive a Jira webhook, we'll decode the JWT and extract the client key (`iss`), look up the jira site for with that client key, then validate the signature of the JWT using the jira site shared secret. If it's correct, then we'll know the team to which that webhook belongs.","mergeCommitSha":"2047f859b9b75f09d30fb6090bab53c524cecc31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6002","title":"Add clientKey property for JiraSiteModel","createdAt":"2023-05-03T00:03:49Z"}