from dataclasses import dataclass
from enum import Enum
from datetime import datetime
from typing import Optional


class PullRequestState(Enum):
    UNKNOWN = 0
    OPEN = 1
    CLOSED = 2
    MERGED = 3

    @classmethod
    def from_str(cls, label: str):
        if label.lower() in "open":
            return cls.OPEN
        elif label.lower() in "closed":
            return cls.CLOSED
        elif label.lower() in "merged":
            return cls.MERGED
        else:
            return PullRequestState.UNKNOWN


@dataclass(order=True)
class PullRequest:
    number: int
    merge_commit_sha: str
    html_url: str
    title: str
    body: str
    created_at: datetime
    merged_at: Optional[datetime]
    state: PullRequestState


@dataclass(order=True)
class PullRequestSummaryResult:
    """Stores result of pull request summary."""

    summary: str
    pull_request: PullRequest
