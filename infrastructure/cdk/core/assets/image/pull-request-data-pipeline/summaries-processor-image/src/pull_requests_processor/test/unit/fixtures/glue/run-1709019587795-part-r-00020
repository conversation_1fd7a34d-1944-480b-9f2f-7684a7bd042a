{"state":"Merged","mergedAt":"2022-02-23T04:22:33Z","number":392,"body":"Adds ability to grab PR comments for a repo from the GitHub API and create the necessary DB models. This is to unblock sourcemark development.\r\n\r\nRuns in the background on application start up (fire and forget). Once we have an admin console, we can migrate this logic to there.\r\n\r\nNotes\r\n- message bodies have *not* been serialized to our protobuf form\r\n- outdated comments are *not* imported","mergeCommitSha":"9a7d4723df39832cc34ec8d48fd1921c18ec24ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/392","title":"Add PR ingestion runner","createdAt":"2022-02-23T01:16:54Z"}
{"state":"Merged","mergedAt":"2022-12-02T07:39:43Z","number":3920,"mergeCommitSha":"ac5331c9dcb5b96de8a3f5cb73f7da9a0855e894","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3920","title":"Show the correct topics","createdAt":"2022-12-02T07:28:31Z"}
{"state":"Merged","mergedAt":"2022-12-02T16:59:24Z","number":3921,"body":"A first attempt at identifying experts for a topic. \r\n\r\nThis approach looks at the pull request creators, reviewers, and message authors and counts how often they appear, with a bias toward PR creators and reviewers. Outliers that contribute the most are surfaced experts.\r\n\r\nThis is very much a work in progress.","mergeCommitSha":"9cd5ed0bfd9785faf8538b4885a8910538481694","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3921","title":"Create ExpertsRecommendationService","createdAt":"2022-12-02T08:24:15Z"}
{"state":"Merged","mergedAt":"2022-12-02T17:24:54Z","number":3922,"mergeCommitSha":"6fdf1fd3cdfc6a89dec1652cab586c661639994c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3922","title":"Fix counts","createdAt":"2022-12-02T17:17:13Z"}
{"state":"Merged","mergedAt":"2022-12-02T17:42:47Z","number":3923,"mergeCommitSha":"2b4f3e4c4ca2d7d3402324d0fb481412498ccbec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3923","title":"Add transaction","createdAt":"2022-12-02T17:42:20Z"}
{"state":"Merged","mergedAt":"2022-12-02T19:15:50Z","number":3924,"body":"https://linear.app/unblocked/issue/UNB-794/dashboard-re-fetches-slack-installation-state-on-every-navigation","mergeCommitSha":"478920d46d214a71ae8b7ca5c4039978b20a903b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3924","title":"UNB-794 / Dont always fetch slack installation","createdAt":"2022-12-02T18:55:28Z"}
{"state":"Merged","mergedAt":"2022-12-02T19:53:54Z","number":3925,"body":"Since our removalPolicy was set to retain I still need to delete those queues manually from AWS console. ","mergeCommitSha":"6dc117a80a8d3909f6b0525ba964dc95c92a6c84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3925","title":"remove sqs queues from dev","createdAt":"2022-12-02T19:31:41Z"}
{"state":"Merged","mergedAt":"2022-12-02T19:58:33Z","number":3926,"body":"At the moment there is no way to redirect back to the original landing url following a 401 response. The change will allow us to implement a passthrough url embedded in the OAuth flow's /login/exchange redirect, which the dashboard can then extract and redirect to after the exchange is completed. ","mergeCommitSha":"b320a4eff39bee863e84183ac1a314f41433e245","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3926","title":"Add completionUrl parameter to login endpoints","createdAt":"2022-12-02T19:39:52Z"}
{"state":"Merged","mergedAt":"2022-12-02T20:06:39Z","number":3927,"mergeCommitSha":"5f8b30daea758d6114ea7367087226786f5f60c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3927","title":"Add ability to trigger search reindexing for all teams","createdAt":"2022-12-02T19:51:11Z"}
{"state":"Merged","mergedAt":"2022-12-05T19:07:01Z","number":3928,"mergeCommitSha":"167413353cf8bfde42846a7f6a6d09baec5ba596","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3928","title":"Implements completionUrl for login","createdAt":"2022-12-02T20:20:32Z"}
{"state":"Merged","mergedAt":"2022-12-02T22:17:26Z","number":3929,"mergeCommitSha":"ae04c28be5f23b018fbd7efb6c73c651cc57c843","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3929","title":"Source agent exit status 1 when something catastrophic goes wrong","createdAt":"2022-12-02T21:32:58Z"}
{"state":"Merged","mergedAt":"2022-02-23T18:03:38Z","number":393,"body":"Note.\r\nAll of this is boilerplate.","mergeCommitSha":"7aae5fc4807330dd7d97485b63c82f7dbc2a228b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/393","title":"Adding skeleton for thread sidebar in vscode","createdAt":"2022-02-23T01:30:24Z"}
{"state":"Merged","mergedAt":"2022-12-02T21:57:23Z","number":3930,"mergeCommitSha":"75f1751ad2d1f081fd38e96432559377b1202eb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3930","title":"Add reindexing button","createdAt":"2022-12-02T21:50:49Z"}
{"state":"Merged","mergedAt":"2022-12-02T22:19:49Z","number":3931,"mergeCommitSha":"04e4e549266e6050b00237b663270431b97582e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3931","title":"Gracefully handle termination signals","createdAt":"2022-12-02T22:06:36Z"}
{"state":"Merged","mergedAt":"2022-12-02T23:05:28Z","number":3932,"body":"Next PR will add a migration to drop the column.","mergeCommitSha":"35a09892cb1f3f2416da38bfa1b270e47224a070","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3932","title":"Remove TopicModel.repo","createdAt":"2022-12-02T22:36:19Z"}
{"state":"Merged","mergedAt":"2022-12-02T23:29:53Z","number":3933,"mergeCommitSha":"a59ad52bc98a739344508cfbeaf8dcea824f1123","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3933","title":"Drop TopicModel.repoId column","createdAt":"2022-12-02T23:20:24Z"}
{"state":"Merged","mergedAt":"2022-12-02T23:51:54Z","number":3934,"mergeCommitSha":"b4902ea633477be2a6c8225e2252d61091142075","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3934","title":"David/add button","createdAt":"2022-12-02T23:44:01Z"}
{"state":"Merged","mergedAt":"2022-12-03T00:17:29Z","number":3935,"body":"<img width=\"1115\" alt=\"CleanShot 2022-12-02 at 16 07 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/205411435-febf8a8c-819c-4fe1-9a9e-e9668ec1e636.png\">\r\n","mergeCommitSha":"3a9be1eba32ee810cbcb6e2c4094084175854d72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3935","title":"Fix missing titles","createdAt":"2022-12-03T00:08:01Z"}
{"state":"Merged","mergedAt":"2022-12-06T21:00:22Z","number":3936,"body":"Upload before opening browser. Does not deal with failure states (needs some design work)\r\n\r\n<img width=\"393\" alt=\"CleanShot 2022-12-05 at 16 22 43@2x\" src=\"https://user-images.githubusercontent.com/858772/205777578-db0d3a1f-521f-4585-825e-5e553716550f.png\">\r\n","mergeCommitSha":"3f193be664bcc089a88ac6503ff2fc9d55edcee3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3936","title":"Upload the video before bouncing to the browser","createdAt":"2022-12-03T00:19:45Z"}
{"state":"Merged","mergedAt":"2022-12-04T03:20:30Z","number":3937,"mergeCommitSha":"5fafc0d2892b25e665b32d53859426e4f07e9622","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3937","title":"reduce prod cluster size by removing one of the read-only instances","createdAt":"2022-12-04T03:17:58Z"}
{"state":"Merged","mergedAt":"2022-12-04T07:26:33Z","number":3938,"body":"- Resized Dev RDS \r\n- Disabled datapipeline stack. I need to sync with Rashin to figure out what's the story there. \r\n- Removed all SQS queues from prod","mergeCommitSha":"fe0e9e5f24a3d3b4c6cfca5d9572d336bd279220","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3938","title":"disabled datapipeline stack, resized dev RDS and removed prod sqs queues","createdAt":"2022-12-04T07:20:33Z"}
{"state":"Merged","mergedAt":"2022-12-05T17:41:25Z","number":3939,"body":"Default to applications path to launch walkthrough app instead of mdfind & open -b with bundle ID.\r\n\r\n","mergeCommitSha":"65bef618fcd4c24bc6e761c8fd12815e4a4b8af0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3939","title":"Update launch approach","createdAt":"2022-12-05T17:17:38Z"}
{"state":"Merged","mergedAt":"2022-02-23T18:24:52Z","number":394,"body":"This will let us reset and rebuild SourceMarks","mergeCommitSha":"b05dc9158469f6dfa792a3f4e5bdf9dcce264b81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/394","title":"Add isOriginal property to SourcePointModel","createdAt":"2022-02-23T07:40:29Z"}
{"state":"Merged","mergedAt":"2022-12-05T18:49:50Z","number":3940,"body":"Based on feedback here: https://github.com/NextChapterSoftware/unblocked/pull/3939\r\n\r\nAdded utility helper for Walkthrough App","mergeCommitSha":"6c3053250789f1cef6f00f1667c32a5d0b4d55eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3940","title":"Add Walkthrough Path Helper","createdAt":"2022-12-05T18:03:52Z"}
{"state":"Merged","mergedAt":"2022-12-05T19:14:13Z","number":3941,"body":"Blends difference in pixels (poor-man's vibrancy). \r\n\r\nwidth = 10% screen size\r\nalpha = 40% + difference blending\r\n\r\n\r\nhttps://user-images.githubusercontent.com/858772/205720611-50c2c49e-3ac0-474a-a1b4-c939bce50dca.mp4\r\n\r\n","mergeCommitSha":"b900132c80fc6e46649c771a14331219bc147712","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3941","title":"Adds unblocked watermark to video composition","createdAt":"2022-12-05T18:56:52Z"}
{"state":"Merged","mergedAt":"2022-12-05T21:10:47Z","number":3942,"body":"This will allow us to create system recommended topic experts and let users to manually remove/add team members as topic experts.","mergeCommitSha":"3622b702d759c39fff7b0c8882ad1f72c4efce8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3942","title":"Add TopicExpertModel","createdAt":"2022-12-05T19:34:38Z"}
{"state":"Merged","mergedAt":"2022-12-05T20:15:51Z","number":3943,"mergeCommitSha":"7b2755dc55aee2d2a0552148840bb250d4837bc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3943","title":"Fix main break","createdAt":"2022-12-05T20:15:10Z"}
{"state":"Merged","mergedAt":"2022-12-05T22:34:22Z","number":3944,"mergeCommitSha":"5aa8680a267d77060c6a11e60804ef891f8fec14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3944","title":"Return topics experts","createdAt":"2022-12-05T21:52:42Z"}
{"state":"Merged","mergedAt":"2022-12-09T21:10:23Z","number":3945,"body":"* Search UI implies search tokens that are scoped under a team\r\n* Refactor add adshboard routes and UIs to be under the `/team/:teamId` base route\r\n* Add `TeamContext` to hold state of currently selected team\r\n* Update UI to include marker for user's team, as well as a team selector:\r\n<img width=\"360\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/205758919-41aba046-336d-413b-8fe8-85be02b42c63.png\">\r\nNB: this inner dropdown only shows when there is >1 team \r\n\r\n\r\n#### NOTES\r\nIgnore the `HomeSearch` component and all the changes to the SearchContext.tsx file for now. I'm not using that component anywhere, more important to get the team refactor reviewed first \r\n","mergeCommitSha":"140a04c99a6d74ff424cebf03f21c4e5956e1eb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3945","title":"Refactor dashboard under team context","createdAt":"2022-12-05T22:46:01Z"}
{"state":"Merged","mergedAt":"2022-12-06T18:39:47Z","number":3946,"body":"Update styling and functionality for contributors list a bit closer to design.\r\n\r\n<img width=\"1240\" alt=\"CleanShot 2022-12-05 at 14 56 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/205761008-1cf279bc-9ac2-43d4-ac38-daf8c7710ba7.png\">\r\n\r\n\r\nTODO: Still some work in regards to the \"add\" button.\r\nNeeds refactoring of TeamMemberDropdown which will be a separate PR.\r\n\r\n","mergeCommitSha":"d8af178c872ea20affbb5a0d68ebcc3540d18034","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3946","title":"Update web contributors","createdAt":"2022-12-05T22:58:16Z"}
{"state":"Merged","mergedAt":"2022-12-05T23:49:28Z","number":3947,"body":"This was causing bulk ingestion of pull request TLCs to fail.\r\n\r\nhttps://app.logz.io/#/goto/6d03ec6a5cd54a3269c9068f64356040?switchToAccountId=411850","mergeCommitSha":"306e78b68fda0cec31916dd4c88f530aae8e512f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3947","title":"Filter pull request comments in GitHubAppClient.V3Org.pullRequestComments","createdAt":"2022-12-05T23:10:05Z"}
{"state":"Merged","mergedAt":"2022-12-05T23:40:05Z","number":3948,"mergeCommitSha":"9972d369a3d71c12092403d40864b7f0c645e88a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3948","title":"Add topic pipeline","createdAt":"2022-12-05T23:24:12Z"}
{"state":"Merged","mergedAt":"2022-12-06T01:02:37Z","number":3949,"body":"Model to add login state. Used to spec out state object being passed around in login urls.\r\n\r\nOne note, completion url will be base 64 encoded on the client side when passed to service.\r\nThis is necessary as the url is being passed as part of the query parameter (thought we were doing body at first)","mergeCommitSha":"61d76ad24da7d2d6d00e9d5d00846599345c8d5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3949","title":"Add model for login state","createdAt":"2022-12-06T00:03:03Z"}
{"state":"Merged","mergedAt":"2022-02-24T08:56:35Z","number":395,"body":"Part of the plan here:\r\nhttps://www.notion.so/nextchaptersoftware/SourceMark-Demo-Plan-5bd323e961b046119756078145e874f8","mergeCommitSha":"33b14cfe42ffebe0642a522639522b090f40409d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/395","title":"Add getSourceMarks operation and add SourcePoints to SourceMark","createdAt":"2022-02-23T07:49:41Z"}
{"state":"Merged","mergedAt":"2022-12-06T01:16:37Z","number":3950,"mergeCommitSha":"76e1a9b57dcb79b7193d371a917cfa5336053fe2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3950","title":"Wrong quueu","createdAt":"2022-12-06T01:16:32Z"}
{"state":"Merged","mergedAt":"2022-12-06T22:38:12Z","number":3951,"body":"Update ActiveFileManager to correctly handle interactions between text editors and webviews.  The general rule is: when a non-text-editor (ie a webview) is foregrounded, continue to display the last-focused text editor, as long as that text editor is visible.  This ensures that when our webviews are focused, we continue to display the insights from the last-focused editor.  If *only* a webview is visible, we will display all insights for the file associated with that webview.  I think this gives the best balance of behaviours, at least for now.\r\n\r\nThe ActiveFileManager has some subtle behaviour that could easily regress, so I decided to write unit tests.  Unfortunately this required mocking out a lot of VSCode interfaces (`window`, `TextEditor`, `TextDocument`, and others).  I made interfaces (like `ITextEditor`, `ITextDocument`, etc) for the various VSCode APIs, and associated mock classes (`MockWindow`, `MockTextEditor`), which we can use to mock these out in tests.  We can expand these interfaces and mocks and reuse them to write tests elsewhere.\r\n","mergeCommitSha":"f1f878de102335c66c03a3067785fedf977a9be7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3951","title":"Update ActiveFileManager to correctly handle webviews","createdAt":"2022-12-06T02:26:49Z"}
{"state":"Merged","mergedAt":"2022-12-06T17:12:42Z","number":3952,"mergeCommitSha":"30aeb443acc9f86fd53f579e252dcef6bf22f478","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3952","title":"[BREAKS API ON MAIN] Pass the client token through the state parameter","createdAt":"2022-12-06T06:13:35Z"}
{"state":"Merged","mergedAt":"2022-12-06T13:34:37Z","number":3953,"mergeCommitSha":"c2c71ae5a59513e2abe79d09946c35928a8795ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3953","title":"Add debugging","createdAt":"2022-12-06T13:34:09Z"}
{"state":"Merged","mergedAt":"2022-12-06T13:36:53Z","number":3954,"mergeCommitSha":"fbcd13d0855ae1b784bd6e099617dfc796fdd440","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3954","title":"Debugging","createdAt":"2022-12-06T13:36:46Z"}
{"state":"Merged","mergedAt":"2022-12-06T14:01:33Z","number":3955,"mergeCommitSha":"9d5f216a7f44bf0676e3f22d745dcb57249292fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3955","title":"Fix s3 processing","createdAt":"2022-12-06T14:01:15Z"}
{"state":"Merged","mergedAt":"2022-12-06T18:31:22Z","number":3956,"body":"- notebook\r\n- Basic sagemaker step function pipeline\r\n- Add addtional processing jobs\r\n- Add docker images\r\n- Add model check\r\n- Fix bugs with glue s3 buckets\r\n- Add python scripts\r\n- Improve stat machine\r\n- Typescript code\r\n- Update\r\n- Move files around\r\n- Add trainer image\r\n- Bertopic part 1\r\n- Comment\r\n- Topic output\r\n- Add processing job sagemaker step function\r\n- Update processing script\r\n- Add ssl\r\n- Add lambda support\r\n- More fixes\r\n- Fix queue name\r\n- Lint\r\n- Add docker image construct\r\n- Update script\r\n- Fix payload\r\n- Try agian\r\n- Crap never ends\r\n- Test deployment\r\n","mergeCommitSha":"6c2a7496ab324254cbc58155dadd74933d29d494","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3956","title":"Add Topic Pipeline CDK Infra","createdAt":"2022-12-06T14:07:04Z"}
{"state":"Merged","mergedAt":"2022-12-06T14:20:10Z","number":3957,"mergeCommitSha":"4c1e8a65fce61ac202c88dd018946f8ba7798904","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3957","title":"Fix test","createdAt":"2022-12-06T14:20:00Z"}
{"state":"Merged","mergedAt":"2022-12-06T14:36:56Z","number":3958,"mergeCommitSha":"194e8e67c4cbf1c3a104b64e2ab47b7aa6063e30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3958","title":"Redeploy","createdAt":"2022-12-06T14:36:49Z"}
{"state":"Merged","mergedAt":"2022-12-06T14:39:36Z","number":3959,"mergeCommitSha":"a57ebd503e027ccc4b28829c75390bd8dee15ebb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3959","title":"redeploy","createdAt":"2022-12-06T14:39:28Z"}
{"state":"Merged","mergedAt":"2022-02-24T16:55:29Z","number":396,"body":"Updates message editor API so that editor <-> Block translation is localized to the MessageEditor.\r\n\r\nIntroduced hooks for getContent & multiple callbacks to get `hasContent` state and caching purposes.\r\n\r\nNOTE: Removes JsonValue constraint for `useWebviewState`","mergeCommitSha":"e95e239cbb93bd41c3137391ee9851c9f3802988","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/396","title":"Refactor Message Editor and MessageList ","createdAt":"2022-02-23T17:23:23Z"}
{"state":"Merged","mergedAt":"2022-12-06T14:59:48Z","number":3960,"mergeCommitSha":"2135a72f63dbbbc7e63c75bc7459c514748371b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3960","title":"Fix json parsing","createdAt":"2022-12-06T14:59:43Z"}
{"state":"Closed","mergedAt":null,"number":3961,"mergeCommitSha":"e6ddad36dd52e2112a8b6ef9f9ed707540deccb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3961","title":"Fix action","createdAt":"2022-12-06T15:02:09Z"}
{"state":"Merged","mergedAt":"2022-12-06T15:15:01Z","number":3962,"mergeCommitSha":"2935991568ed3f06317e54c35bce556eec0d34ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3962","title":"Redeploy agian","createdAt":"2022-12-06T15:14:55Z"}
{"state":"Merged","mergedAt":"2022-12-06T15:30:10Z","number":3963,"mergeCommitSha":"b22bdf3f4712c9604d17ff8d22e67b1f2d47dc90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3963","title":"Do not use null check","createdAt":"2022-12-06T15:30:00Z"}
{"state":"Merged","mergedAt":"2022-12-06T16:32:11Z","number":3964,"mergeCommitSha":"68b08310e49ff2721801aa214d234a7948b1a01f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3964","title":"increase entries","createdAt":"2022-12-06T16:31:59Z"}
{"state":"Merged","mergedAt":"2022-12-06T18:18:55Z","number":3965,"mergeCommitSha":"a8d5dad22301fa2443ec78d0c99db7d0ef2a776f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3965","title":"Remove no longer needed code","createdAt":"2022-12-06T17:57:57Z"}
{"state":"Merged","mergedAt":"2022-12-06T18:03:42Z","number":3966,"mergeCommitSha":"29ba560a61d05c0b2d875d3a2e7cf84c5387b2ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3966","title":"Capitalize titles","createdAt":"2022-12-06T17:58:16Z"}
{"state":"Merged","mergedAt":"2022-12-06T20:56:35Z","number":3967,"body":"If one tries to navigate to a page but is rejected due to RequireAuth, we will now redirect user to page post auth.\r\n\r\nWe have introduced a \"clientState\" option to loginOptions that allows the client to push stateful data within the \"state\" query parameter that is passed from unblocked to Github and back.\r\n\r\n1. Encode `currentRoute` when redirected from `RequireAuth` to `Login`\r\n2. Shuttle route through auth flow\r\n3. At `LoginExchange`, decode state and redirect to desired route.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/205993000-c6fda384-c8ad-4d19-8749-d75900039cf8.mp4\r\n\r\n","mergeCommitSha":"83457b3102c2c1390becc7dbea8256c457385870","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3967","title":"Redirect user to page after auth","createdAt":"2022-12-06T18:35:43Z"}
{"state":"Merged","mergedAt":"2022-12-06T21:00:30Z","number":3968,"body":"Slight polish\r\n\r\nMoved add button.\r\n<img width=\"1190\" alt=\"CleanShot 2022-12-06 at 11 27 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/206004046-8db301b1-b83b-4978-ab83-c8b8003947ac.png\">\r\n\r\nUnsaved changes dialog: \r\n<img width=\"1134\" alt=\"CleanShot 2022-12-06 at 11 26 22@2x\" src=\"https://user-images.githubusercontent.com/1553313/206004095-5e7dc614-83c4-4b9c-a96f-d8d0196490d9.png\">\r\n","mergeCommitSha":"af6b383455af0013d518a52d7860a8e50b96a57d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3968","title":"Web compose polish","createdAt":"2022-12-06T19:27:35Z"}
{"state":"Merged","mergedAt":"2022-12-06T21:51:58Z","number":3969,"mergeCommitSha":"35159637ff9260bdc9db6e15e24d0a17377ff96f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3969","title":"Generate topic experts","createdAt":"2022-12-06T19:49:23Z"}
{"state":"Merged","mergedAt":"2022-02-23T18:52:14Z","number":397,"mergeCommitSha":"34a6abcf504122b2e8d7c00f0f9bba6afa6e463c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/397","title":"No bother in naming","createdAt":"2022-02-23T18:10:19Z"}
{"state":"Merged","mergedAt":"2022-12-06T21:24:50Z","number":3970,"mergeCommitSha":"68b99b3a1798a24fa87cef768800bfe1266edee6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3970","title":"Add countdown timer before recording starts","createdAt":"2022-12-06T20:43:46Z"}
{"state":"Merged","mergedAt":"2022-12-06T21:40:49Z","number":3971,"mergeCommitSha":"57d0fbd0fe2c4252f487f5010f4425ce797e4c7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3971","title":"Don't use bundleId to launch walkthrough app","createdAt":"2022-12-06T21:32:34Z"}
{"state":"Merged","mergedAt":"2022-12-06T22:28:10Z","number":3972,"body":"This is purely changing the UI, not any of the data handling -- so we are still loading and processing the `mine` list even though we don't really need to anymore.  Once we've verified this is the UI we want we can cut out more.\r\n<img width=\"1096\" alt=\"Screen Shot 2022-12-06 at 1 38 58 PM\" src=\"https://user-images.githubusercontent.com/2133518/206028796-3aff25d1-2330-40f9-9d8e-9540529e40fb.png\">\r\n\r\n<img width=\"1096\" alt=\"Screen Shot 2022-12-06 at 1 38 38 PM\" src=\"https://user-images.githubusercontent.com/2133518/206028781-5711e3aa-2db8-4eb2-af53-40fc620e86e3.png\">\r\n","mergeCommitSha":"d4a08653217d9e19175902297c4b0e37504a817d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3972","title":"Simplify VSCode sidebar","createdAt":"2022-12-06T21:39:28Z"}
{"state":"Merged","mergedAt":"2022-12-06T22:17:02Z","number":3973,"body":"Increase pull request creator and reviewer weighting, and take the top three contributors instead of just looking at outliers.","mergeCommitSha":"2ecad37cfe9443a55d7698e9e753181659bc2bd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3973","title":"Simplify topic expert recommendation algorithm","createdAt":"2022-12-06T22:02:44Z"}
{"state":"Merged","mergedAt":"2022-12-06T23:01:48Z","number":3974,"mergeCommitSha":"fc6a6e3ca5a378f339faf0645a14e4e983e8c559","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3974","title":"...","createdAt":"2022-12-06T23:01:14Z"}
{"state":"Merged","mergedAt":"2022-12-06T23:43:42Z","number":3975,"body":"Fix bert training to create nested trees ","mergeCommitSha":"b014ebc0f90b2f5013f1754a6b4fa8880410a607","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3975","title":"Fix bert","createdAt":"2022-12-06T23:11:12Z"}
{"state":"Merged","mergedAt":"2022-12-06T23:34:06Z","number":3976,"mergeCommitSha":"95ebb898fc07f33052ad5326570c489a54f39d99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3976","title":"Add walkthrough launcher gif","createdAt":"2022-12-06T23:33:34Z"}
{"state":"Merged","mergedAt":"2022-12-07T05:41:59Z","number":3977,"body":"Generate assets url instead of depending on asset download url","mergeCommitSha":"522f42b978bb00eb45326776d5f6ed06e8b97de2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3977","title":"Generate Asset Urls","createdAt":"2022-12-07T00:46:14Z"}
{"state":"Merged","mergedAt":"2022-12-07T03:23:13Z","number":3978,"body":"There's been a regression where fetching the insights for `private.yml` can be quite slow, up to 10 seconds.  This fixes it.\r\n\r\n`GitRunner.commitExists` is called a lot by the sourcemark engine.  There are two levels of caching that it uses:\r\n1. We fetch all commits in one operation and store them in a set (TreeCache)\r\n2. If we can't find a commit in the TreeCache, we query using `git cat-file`, this occurs when commits don't exist in the tree (like PR commits that get dropped on merge and whatnot).  We intend to cache the output of these as well, using the general git command cache, but this never actually worked, because `git cat-file` works different then most git commands, in that we don't care about (or want to cache) the output of the command, we care about (and want to cache) whether the command succeeded or not.  The git operation would throw an exception, which bypassed caching.\r\n\r\nI wrote a separate set of `GitRunner.run*` methods that run \"boolean success\" commands, ie, those that disregard output, but only return true/false based on whether the command succeeds, and cache appropriately.\r\n\r\nOne outcome of this is that once the cache is warmed up, there is a lot of work being done synchronously when you flip between files, which causes the UI to block up.  I added a hacky chunked wait so large mark lists would behave a little better but this is not a great long-term solution.  We need to profile what is happening in the SM engine here and optimize.\r\n\r\nAlso a separate issue I found is that a unique `GitRunner` instance is created on every invocation of the SM engine.  This means the cache is less effective, though practically it doesn't seem to make a big difference on my machine.  This is pretty easy to fix, but I wasn't sure if it was intentional or not for some reason so I've left it alone for now.","mergeCommitSha":"3496656b57ae7e9ec930aabf25b041102e5dfdc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3978","title":"Fix data caching for git commit existence check","createdAt":"2022-12-07T01:10:10Z"}
{"state":"Merged","mergedAt":"2022-12-07T01:42:05Z","number":3979,"mergeCommitSha":"c05bb4d1e727b69b178e4b9818656494c738e9c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3979","title":"Add slack webhook filtering","createdAt":"2022-12-07T01:12:47Z"}
{"state":"Merged","mergedAt":"2022-02-23T20:01:15Z","number":398,"body":"- Added a new stack to create bastion host under sec-ops account\r\n- Added necessary configs for bastion host to sec-ops config object\r\n- Updated sec-ops json env config file to list of retool public IPs and SSK key name for bastion host\r\n- Updated prod RDS to allow traffic from ***********/16 (sec-ops CIDR) so bastion host can talk to RDS (needed for retool)\r\n- Added public keys for both our management user as well as retool user\r\n- Created `retool` user on postgres (creds in 1p) and verified it on retool\r\n- Not directly related - Created 'admin` user on postgres Dev and Prod. Added creds to 1Password\r\n\r\nall changes have been deployed to secops account. There were no changes to Dev. The only remaining change is IP address whitelist for prod which should be added by CI/CD.","mergeCommitSha":"d39cb3a6a62d30552fb123403f5f7c49774c7d90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/398","title":"Added Bastion Host","createdAt":"2022-02-23T19:46:23Z"}
{"state":"Merged","mergedAt":"2022-12-07T06:04:39Z","number":3980,"body":"Web Compose needs to follow a similar path to DiscussionThread and use authed urls for video assets.","mergeCommitSha":"196d97d16afb4f466c32bdee0715f042a95936ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3980","title":"Web compose to use authed asset urls","createdAt":"2022-12-07T01:23:10Z"}
{"state":"Merged","mergedAt":"2022-12-07T18:00:19Z","number":3981,"mergeCommitSha":"29ff67de66e81f9136226fa801d1a00f5d5fd5a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3981","title":"Upgrade trainer instance types","createdAt":"2022-12-07T02:51:48Z"}
{"state":"Merged","mergedAt":"2022-12-07T18:15:19Z","number":3982,"mergeCommitSha":"7afb7769f84162171b95c19147e763ff655e66b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3982","title":"Show the pull request at the top of the list of the \"Thread\" topics page","createdAt":"2022-12-07T18:05:37Z"}
{"state":"Merged","mergedAt":"2022-12-07T19:02:02Z","number":3983,"body":"Ensure internal thread titles are kosher during demos. :)","mergeCommitSha":"bfbedfaf7580f1de868f203eb9a369acb51aeebb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3983","title":"Make sure we clean up thread titles","createdAt":"2022-12-07T18:31:57Z"}
{"state":"Merged","mergedAt":"2022-12-07T19:23:20Z","number":3984,"mergeCommitSha":"be3880d824506f50b4082629da791c9c890ccdd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3984","title":"Add client download url to get asset response","createdAt":"2022-12-07T18:40:28Z"}
{"state":"Merged","mergedAt":"2022-12-07T19:12:07Z","number":3985,"body":"This makes them show up blank in the Activity Monitor. This PR also bumps the minimum version for the installer to macOS 12.3","mergeCommitSha":"ac6d858cfe5a5e742935292c0dc93c9437814f0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3985","title":"Apps were missing bundle display name","createdAt":"2022-12-07T18:54:51Z"}
{"state":"Merged","mergedAt":"2022-12-07T19:03:42Z","number":3986,"mergeCommitSha":"dbbd64dff715b2315c6599526f807002789b2a22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3986","title":"Fix","createdAt":"2022-12-07T19:00:45Z"}
{"state":"Merged","mergedAt":"2022-12-07T23:37:58Z","number":3987,"body":"If you have file A open in VSCode before walkthrough app initialization, VSCode will try sending the file reference but nothing will be captured. Once walkthrough app initializes and starts running, VSCode will not resend file A since technically, the file never changed in VSCode.\r\n\r\nThis is not a problem within VSCode as we resend file A when VSCode initializes the walkthrough. \r\n\r\nThe fix here is to always send the latest file reference event to the walkthrough app whenever vscode is focused.\r\n\r\nThis may send duplicate events but too much data is better than too little data. Added dedupe logic to the walkthrough app which should remove unnecessary references.\r\n\r\nBased on Dennis' feedback.\r\nhttps://chapter2global.slack.com/archives/C043QV89MHD/p1670430566871249","mergeCommitSha":"09638562e00ed86971ca8bc10077cfaddec57212","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3987","title":"Capture initial code reference from hub walkthroughs.","createdAt":"2022-12-07T19:08:19Z"}
{"state":"Merged","mergedAt":"2022-12-07T19:20:55Z","number":3988,"mergeCommitSha":"cbfa730db67d31a85a97e8e51b6df5828b84b566","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3988","title":"Really fix it this time","createdAt":"2022-12-07T19:20:49Z"}
{"state":"Merged","mergedAt":"2022-12-07T20:17:04Z","number":3989,"mergeCommitSha":"7fa4317ac1a2223aca45da9cfdcb45b674f4e74d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3989","title":"Remove ssl scripts for processing","createdAt":"2022-12-07T20:16:51Z"}
{"state":"Merged","mergedAt":"2022-02-23T23:29:09Z","number":399,"body":"This pr does the following:\r\n1. Creates a webview provider for thread sidebar.\r\n2. Add a viewContainer to activityBar on left handside of vscode that loades thread sidebar webview.\r\n3. Fix up webpack to add assets to dist folder for vscode. This is necessary as we’re running the extension environment from the dist folder:\r\n            \"args\": [\"--extensionDevelopmentPath=${workspaceFolder}/dist\"],\r\n\r\n\r\n<img width=\"378\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/155418159-253a12cd-a3d1-4886-902a-ad0edf2568cd.png\">\r\n","mergeCommitSha":"4f65808552964c50c0763963f53d54c4afccb348","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/399","title":"Add a discussion thread sidebar to vscode","createdAt":"2022-02-23T22:12:38Z"}
{"state":"Merged","mergedAt":"2022-12-07T20:19:53Z","number":3990,"body":"- Remove ssl scripts for processing\r\n- Comment\r\n","mergeCommitSha":"438e60fea3cc7676cf27d0c62b849f4b9f7431f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3990","title":"RemoveSSLFromProcessing","createdAt":"2022-12-07T20:19:33Z"}
{"state":"Merged","mergedAt":"2022-12-07T21:05:19Z","number":3991,"mergeCommitSha":"b99f55bc17cb1833780304f2f7fa66f9843c4238","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3991","title":"Run main things on main","createdAt":"2022-12-07T21:04:48Z"}
{"state":"Merged","mergedAt":"2022-12-07T23:37:30Z","number":3992,"mergeCommitSha":"b983ac5eecd28a4118528705b2e83a204603fdc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3992","title":"Hub should use clientDownloadUrl instead of constructing its own","createdAt":"2022-12-07T21:36:24Z"}
{"state":"Merged","mergedAt":"2022-12-07T21:58:34Z","number":3993,"mergeCommitSha":"fcba142737f15ad13db351709915490cc9cf4039","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3993","title":"Return topic experts in getRelatedTopics","createdAt":"2022-12-07T21:50:15Z"}
{"state":"Merged","mergedAt":"2022-12-07T23:36:22Z","number":3994,"body":"VSCode is currently calling the `getRelatedTopics` API call every time a new file is focused.  There's no reason to do this, because the file -> topic mappings change very infrequently.  Introduce a cache that maps files to topics with a 10 minute ttl.","mergeCommitSha":"5a5e25b2ca6fd27fea292bfbf8137fa47f99fc2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3994","title":"Cache file topics to reduce API calls","createdAt":"2022-12-07T21:58:15Z"}
{"state":"Merged","mergedAt":"2022-12-07T22:10:28Z","number":3995,"mergeCommitSha":"23bdd46ac55c6fe6aaf99f6e5dda4c45fa24f20c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3995","title":"Add new data processing service","createdAt":"2022-12-07T22:03:34Z"}
{"state":"Merged","mergedAt":"2022-12-07T22:23:03Z","number":3996,"body":"- Updated EKS node groups but I haven't pushed them. I will be pushing them as part of our upcoming EKS cluster upgrade\r\n- Added cert-manager installation steps \r\n- Installed cert-manager in dev and prod \r\n- Updated EKS logging to reduce cost of CloudWatch logs\r\n\r\n\r\nFYI: The following are files exposed to container via cert-manager\r\n`ca.crt` the root ca\r\n `tls.crt` actual cert \r\n `tls.key` private key","mergeCommitSha":"325af13078a7f3b0f2721a8dcfffd6a5f097b33e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3996","title":"added cert-manager and resized nodes","createdAt":"2022-12-07T22:17:52Z"}
{"state":"Merged","mergedAt":"2022-12-07T22:40:31Z","number":3997,"mergeCommitSha":"f769cc94a63763c53ffdeaa609b673f62468804f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3997","title":"Pass all events through the countdown window","createdAt":"2022-12-07T22:32:39Z"}
{"state":"Merged","mergedAt":"2022-12-07T23:36:31Z","number":3998,"body":"We were filtering out any file-scoped (ie, video) threads here.  Instead we want to *always* show them.","mergeCommitSha":"05a356ae6464682ee5f2108f99e590a590183b74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3998","title":"Show file-scoped threads in explorer insights","createdAt":"2022-12-07T22:46:29Z"}
{"state":"Merged","mergedAt":"2022-12-09T01:26:07Z","number":3999,"body":"Updated backgrounds and borders.\r\n\r\n<img width=\"1195\" alt=\"CleanShot 2022-12-07 at 15 02 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/206316910-08834d01-139f-4190-8ddb-7100375d6a60.png\">\r\n","mergeCommitSha":"b4b6d5100c479c470ad1dae2caf853d3b9dd9984","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3999","title":"Update contributors list style","createdAt":"2022-12-07T23:08:58Z"}
{"state":"Closed","mergedAt":null,"number":4,"mergeCommitSha":"7e8f0207e956125d1052a4d550e00a0019d3ff71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4","title":"Break","createdAt":"2021-12-10T01:00:35Z"}
{"state":"Merged","mergedAt":"2022-01-17T19:21:14Z","number":40,"body":"This is a basic pr for moving private spec over.\r\nlet me know if this is good enough. :)\r\nAnd feel free to do whatever you want afterwards...","mergeCommitSha":"f8489451b2af871cc49731671067342b14c2767b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/40","title":"Start private spec","createdAt":"2022-01-17T17:59:02Z"}
{"state":"Merged","mergedAt":"2022-02-24T02:17:59Z","number":400,"body":"- Added a new stack to create the following resources\r\n  - IAM user within each env account. It's used only for uploads to an S3 bucket\r\n  - Secret AccessKey/secret for the user. We also add it to ASM for later mounting on EKS\r\n  - Created an S3 bucket for streaming assets\r\n  - Enabled automatic tiering on S3 bucket\r\n  - Created IAM policy to grant user access to S3 bucket\r\n\r\nOur apporach with IAM user in each app account is temporary. We would like to move towards STS tokens or signed URLs if Agora folks help us. For now this should unblock Peter.","mergeCommitSha":"198f28c62f3a4f221de2ef1de48e7f379b634f22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/400","title":"Create resources for streaming service","createdAt":"2022-02-23T23:41:57Z"}
{"state":"Merged","mergedAt":"2022-12-07T23:20:22Z","number":4000,"body":"- Adding /certs to all pods. \r\n- Updating all helm charts \r\n\r\nCerts volume will contain the following files:\r\n`ca.crt` the root ca\r\n`tls.crt` actual cert\r\n`tls.key` private key\r\n\r\n\r\nCurrently we set the following fields in the cert:\r\n`SAN` set to `{SERVICENAME}.{NAMESPACE}.{AWS_REGION}.{ENV}.getunblocked.com`\r\n`Common name` set to `{AWS_REGION}.{ENV}.getunblocked.com`\r\n\r\n\r\nWe can set additional attributes as needed. You can find a full list of attributes here: https://cert-manager.io/docs/projects/csi-driver/#supported-volume-attributes\r\n\r\n\r\nExample cert: \r\n```\r\n-----BEGIN CERTIFICATE-----\r\nMIIDLTCCAhWgAwIBAgIRALGrGpoRnkeacQTToSZvYOMwDQYJKoZIhvcNAQELBQAw\r\nKjEoMCYGA1UEAxMfdXMtd2VzdC0yLnByb2QuZ2V0dW5ibG9ja2VkLmNvbTAeFw0y\r\nMjEyMDcyMTU1NTBaFw0yMzAzMDcyMTU1NTBaMAAwggEiMA0GCSqGSIb3DQEBAQUA\r\nA4IBDwAwggEKAoIBAQDRSR3sk78+tjPk11ZYNWombWAA3M6JHqM3igbEA+qQR1am\r\nGCw/NVQZqGRepwhMnw3DHuSESDvw28n7e4gPBU/0oj3yrIl3eUHxJfQqeLCDHwtC\r\nWkUxRIli27JFmOobWnO6qPdnxdwV0XKcZpVLf3BSatLGh3GNg+GXk7nFFEZgvi1Y\r\n3jTvtGhWXH8INetQDFDnV1YJ3FjUWLZw1zcD9HOHDYd2xMT/W0tENmCuYb5jeXgZ\r\nmveM/DbS6cd/zTrrDB+adzLBbuC4aaOga79TGkUifx5sKMp+Uwv/7MpaAvpgAeQc\r\nGuPtRlRk2e8ZJE4Ld7nQ3+DQ/r4Tu5ChFSClhKbTAgMBAAGjeDB2MA4GA1UdDwEB\r\n/wQEAwIFoDAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFMUluJbWuuavvB11zO43\r\nkl/rOGxrMDUGA1UdEQEB/wQrMCmCJ29uZS50d28udXMtd2VzdC0yLnByb2QuZ2V0\r\ndW5ibG9ja2VkLmNvbTANBgkqhkiG9w0BAQsFAAOCAQEAzSKjVxDMPaeufD91Zy8d\r\nz4tFAWUmw6oGjhwEUZr4t/wVfRgCgL0waxaB5gyiZuEHlcjKG6y5701vbKXZs/vm\r\nsZtLwV5qolSXW1pENQYBZgSBgqxEZlbWF+E5+a6POcqJwojEw1ntl4JOqw3KERfq\r\n0ZLXjRVYOgl71s/nBEIx/eHzGyWD0vSzvn5EdhdOUOEybIPzq/KlxH+q0olOcCXv\r\nG/+ASz+RWfDGmBF5nslmNwmwaHOPe5noKMfN5sR698woWXchip9sA4Hg22HpPAm5\r\nRKqcpO+L8rfjgReaitbv8jpEwk1Ma2o7I7Q56NYI5/cCSU0I1CmSDOgar3+jWhn8\r\npA==\r\n-----END CERTIFICATE-----\r\n```","mergeCommitSha":"d82e72a986cebdfd49b8b9b5ec5e84365efc2e29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4000","title":"update all helm charts to include the cert volume","createdAt":"2022-12-07T23:09:29Z"}
{"state":"Merged","mergedAt":"2022-12-15T18:32:07Z","number":4001,"body":"My understanding is when this field is populated, we want to limit the search to insights authored by team members in that list. These team members may or may not be experts, so I think calling it `teamMemberIds` is more accurate. This API isn't being used (yet).","mergeCommitSha":"f57332757b55e95bd76f4c79f75650389158bbdf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4001","title":"[BREAKS API ON MAIN] Rename expertIds to teamMemberIds","createdAt":"2022-12-07T23:54:07Z"}
{"state":"Merged","mergedAt":"2022-12-08T05:05:31Z","number":4002,"mergeCommitSha":"95691e16db31dc9cf754b8928d45ba476631a01b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4002","title":"Always render the watermark","createdAt":"2022-12-08T05:04:37Z"}
{"state":"Merged","mergedAt":"2022-12-08T06:35:03Z","number":4003,"mergeCommitSha":"e1483a360288b8f323ef4044af9a8101f8f97aea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4003","title":"Wheeeeee late night coding is fun","createdAt":"2022-12-08T06:34:22Z"}
{"state":"Merged","mergedAt":"2022-12-08T18:25:29Z","number":4004,"mergeCommitSha":"284310dfeaa35dd17e75eb56b68df2c793e93c0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4004","title":"Try detaching camera from preview","createdAt":"2022-12-08T18:25:07Z"}
{"state":"Merged","mergedAt":"2022-12-08T18:46:21Z","number":4005,"body":"- persist data\r\n- I htink this should work :)\r\n- Update\r\n","mergeCommitSha":"7e07820ae0310228122c0fe13df28b1b1fb685a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4005","title":"PersistData","createdAt":"2022-12-08T18:26:55Z"}
{"state":"Merged","mergedAt":"2022-12-08T18:28:18Z","number":4006,"mergeCommitSha":"bfda3349dccf500c5e8d00e55184bb794946bb47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4006","title":"Update walkthrough gif","createdAt":"2022-12-08T18:27:32Z"}
{"state":"Merged","mergedAt":"2022-12-08T18:35:23Z","number":4007,"mergeCommitSha":"0aca02d851e0397c4cea3893d297559c584eed33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4007","title":"Use Xcode 14.1","createdAt":"2022-12-08T18:35:00Z"}
{"state":"Merged","mergedAt":"2022-12-08T18:58:18Z","number":4008,"mergeCommitSha":"900433afe88b7c58432af26c56dc38cfa3d7ffd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4008","title":"Deploy more changes for data","createdAt":"2022-12-08T18:53:45Z"}
{"state":"Merged","mergedAt":"2022-12-08T18:55:20Z","number":4009,"mergeCommitSha":"cb3016484980921c7fd11ef71513dd36d02838cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4009","title":"Revert detached preview","createdAt":"2022-12-08T18:55:03Z"}
{"state":"Merged","mergedAt":"2022-02-23T23:57:51Z","number":401,"mergeCommitSha":"e6dc5142dea8022eb998d1ad97e40fc33fd2499a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/401","title":"Rename from threadSidebar","createdAt":"2022-02-23T23:44:16Z"}
{"state":"Merged","mergedAt":"2022-12-08T19:07:27Z","number":4010,"mergeCommitSha":"b4e5c52d8a7c34f67c85a4115b45d17a8adedc99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4010","title":"Explicitly copy sample buffers","createdAt":"2022-12-08T19:07:09Z"}
{"state":"Merged","mergedAt":"2022-12-08T19:27:10Z","number":4011,"mergeCommitSha":"466d8d2c2925ccce491372a661a36e083f6fc456","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4011","title":"Fix deployments","createdAt":"2022-12-08T19:16:57Z"}
{"state":"Merged","mergedAt":"2022-12-08T19:49:49Z","number":4012,"mergeCommitSha":"fc829249eb282037490c001794eedfc47bf4c6bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4012","title":"Fix content type","createdAt":"2022-12-08T19:47:01Z"}
{"state":"Merged","mergedAt":"2022-12-08T19:58:37Z","number":4013,"mergeCommitSha":"ef83c35e3379e5361529ddf4e80899d670595310","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4013","title":"Add scm buckets","createdAt":"2022-12-08T19:58:31Z"}
{"state":"Merged","mergedAt":"2022-12-08T21:21:33Z","number":4014,"mergeCommitSha":"7ae0decdaf0a07828328a897fbf6dddabb15737a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4014","title":"Add scm data inputs","createdAt":"2022-12-08T21:04:12Z"}
{"state":"Merged","mergedAt":"2022-12-12T23:10:18Z","number":4015,"body":"Ability to add and remove related links for web.\r\n\r\n<img width=\"479\" alt=\"CleanShot 2022-12-08 at 15 17 06@2x\" src=\"https://user-images.githubusercontent.com/1553313/206587023-302ca4bd-903f-4faa-bbc4-97b0221ccb58.png\">\r\n","mergeCommitSha":"b28011bd53a590012bf00ab5a0665a0599998491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4015","title":"Modify web file references in web composer","createdAt":"2022-12-08T23:17:45Z"}
{"state":"Merged","mergedAt":"2022-12-09T00:09:26Z","number":4016,"body":"This checkin cleans up the new folder that @rasharab made for the ml python work and then adds in a notebook where we are doing our labeling work that pulls from S3 and then creates a simple example -> labels map.","mergeCommitSha":"d4c42f30d852451b3064c5954a4c3bac958466d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4016","title":"Adding labeling notebook","createdAt":"2022-12-08T23:40:40Z"}
{"state":"Merged","mergedAt":"2022-12-08T23:59:47Z","number":4017,"mergeCommitSha":"961a1049d8bf592fc5bbad65997d65e0e89d9ab1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4017","title":"Use a single CIContext to avoid pixel buffer leaks","createdAt":"2022-12-08T23:48:36Z"}
{"state":"Merged","mergedAt":"2022-12-09T00:07:59Z","number":4018,"mergeCommitSha":"673e174e7510d614068f6af87d33c5499b8d9124","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4018","title":"Immediately slurp the active foreground app","createdAt":"2022-12-09T00:07:41Z"}
{"state":"Merged","mergedAt":"2022-12-09T01:21:01Z","number":4019,"body":"- Add scm and slcka processing\n- update\n","mergeCommitSha":"40cdc396d20e902a1af4b9f4d95395e2e6003fa8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4019","title":"TakeScmDataProcessing","createdAt":"2022-12-09T01:20:53Z"}
{"state":"Merged","mergedAt":"2022-02-24T00:29:00Z","number":402,"body":"We should be linting the cdk codebase.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/1890423583","mergeCommitSha":"e08c40be179ddab0d4b5762ef0a87a8876e12ecf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/402","title":"Lint cdk source code","createdAt":"2022-02-24T00:01:30Z"}
{"state":"Merged","mergedAt":"2022-12-09T01:49:12Z","number":4020,"mergeCommitSha":"b5e06ba7d41cd13783884fe7ea3e024e790c7a28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4020","title":"Fix test","createdAt":"2022-12-09T01:49:08Z"}
{"state":"Merged","mergedAt":"2022-12-09T02:27:57Z","number":4021,"mergeCommitSha":"60c4c1d3b6fdf38be86fab633a07fa09749fca61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4021","title":"Clean up lambda","createdAt":"2022-12-09T02:27:46Z"}
{"state":"Merged","mergedAt":"2022-12-09T17:29:46Z","number":4022,"mergeCommitSha":"321a753378f69720c4ebaa301d2d8ebfb17e4c2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4022","title":"Suppress topiic service honeycomb","createdAt":"2022-12-09T17:28:43Z"}
{"state":"Merged","mergedAt":"2022-12-09T18:23:52Z","number":4023,"mergeCommitSha":"be2b1fade4023bbca59d89ec38623c1d8a5e97c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4023","title":"search service sampling increase","createdAt":"2022-12-09T18:23:46Z"}
{"state":"Merged","mergedAt":"2022-12-09T18:34:47Z","number":4024,"mergeCommitSha":"e2bc76612439539719bd0f57310de432fa19db43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4024","title":"Move image assets","createdAt":"2022-12-09T18:31:40Z"}
{"state":"Merged","mergedAt":"2022-12-09T22:30:44Z","number":4025,"mergeCommitSha":"173ee061f67985ea7df8162b66c9e89da5b7083e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4025","title":"Add sanitization jobs","createdAt":"2022-12-09T21:58:19Z"}
{"state":"Merged","mergedAt":"2022-12-09T22:34:41Z","number":4026,"mergeCommitSha":"1ecbf42e606260814d23dbbc0914818ac7b8242e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4026","title":"update","createdAt":"2022-12-09T22:34:25Z"}
{"state":"Merged","mergedAt":"2022-12-12T21:00:09Z","number":4027,"mergeCommitSha":"969217e3ad8fd51b63121bad825f9dd54bf0c020","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4027","title":"[BREAKS API ON MAIN] VideoDraft API V2","createdAt":"2022-12-09T23:01:49Z"}
{"state":"Merged","mergedAt":"2022-12-09T23:27:31Z","number":4028,"mergeCommitSha":"7025521c1c1943f058537edc289e1e98fac424f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4028","title":"Fix search navigation","createdAt":"2022-12-09T23:12:52Z"}
{"state":"Merged","mergedAt":"2022-12-12T22:04:27Z","number":4029,"body":"Also add optional list of participants to PullRequest ","mergeCommitSha":"148d5d44f9ad122ed8f6d07a5b684a4f6b0b76b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4029","title":"Add associated topics to ThreadInfo and PullRequest","createdAt":"2022-12-12T17:26:29Z"}
{"state":"Merged","mergedAt":"2022-02-25T00:19:00Z","number":403,"mergeCommitSha":"2dbcf48c629084e9c5d38291025006adec6a2ab9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/403","title":"Paralellize calling poll get fns","createdAt":"2022-02-24T00:16:41Z"}
{"state":"Merged","mergedAt":"2022-12-15T17:20:28Z","number":4030,"body":"Added Video Draft List route and their corresponding rows.\r\n\r\n<img width=\"998\" alt=\"CleanShot 2022-12-12 at 10 28 26@2x\" src=\"https://user-images.githubusercontent.com/1553313/207127336-48b86a54-9be1-483d-b082-40e20682a8e7.png\">\r\n\r\nTODO: Add entry point to sidebar. This may require rejigging fetching / updating of draft lists in order to populate the sidebar inbox number.","mergeCommitSha":"1dac9594ef92a8741e6bcfd97dafeefa39d853bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4030","title":"Video Draft List","createdAt":"2022-12-12T18:38:35Z"}
{"state":"Merged","mergedAt":"2022-12-12T19:34:02Z","number":4031,"body":"Missing ref... Most likely from bad merge.\r\n\r\nWithout this, blocks within message editor were not saved in walkthrough creation.","mergeCommitSha":"bec266fe79abc786cfc8fdeaaff055681b68aef2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4031","title":"Missing ref for create walkthrough","createdAt":"2022-12-12T19:23:06Z"}
{"state":"Merged","mergedAt":"2022-12-12T19:51:39Z","number":4032,"body":"This log is no longer necessary as failed connections to walkthrough app are expected.\r\n\r\nVSCode used to only connect with the walkthrough app on demand. aka whenever VSCode wanted to start a video walkthrough.\r\n\r\nThis was changed so that VSCode will consistently try connecting to the walkthrough app on launch, regardless of video walkthrough intent. \r\n\r\n\r\n","mergeCommitSha":"fb976a55003e56e744ed5af87d307ef1fa94af22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4032","title":"Remove noisy log","createdAt":"2022-12-12T19:28:42Z"}
{"state":"Merged","mergedAt":"2022-12-12T20:58:23Z","number":4033,"body":"From here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/3978#issuecomment-1347223103\r\n\r\nAvoid resolving a Git repo each time. (Has no effect on the cache)","mergeCommitSha":"d6e0e322b9d719498200d4c1b60f8a6949041dd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4033","title":"Avoid creating and discarding a GitRunner during lookup","createdAt":"2022-12-12T20:05:02Z"}
{"state":"Merged","mergedAt":"2022-12-13T00:59:50Z","number":4034,"body":"Per new designs:\r\n\r\n<img width=\"901\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/207148685-21cb21e2-640b-4ab9-a1eb-6e7f6722941b.png\">\r\n\r\n\r\nStill missing:\r\n* Associated topics (https://github.com/NextChapterSoftware/unblocked/pull/4029)\r\n* PR participants \r\n    * Right now the upper section only shows the author (instead of all of the participants)\r\n","mergeCommitSha":"690948e6fea3f5c4aa6c32095970f9913ae52051","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4034","title":"Revamp insight cards ","createdAt":"2022-12-12T20:18:46Z"}
{"state":"Merged","mergedAt":"2022-12-12T21:12:23Z","number":4035,"mergeCommitSha":"4c826c6eb94d22ad27907c906cf15664cd6e4205","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4035","title":"Add parallel pipelines","createdAt":"2022-12-12T20:41:49Z"}
{"state":"Merged","mergedAt":"2022-12-12T22:10:16Z","number":4036,"mergeCommitSha":"1845a65efe774cede19f99b1be0f3641d4957408","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4036","title":"move to bert topics ingestion","createdAt":"2022-12-12T21:53:26Z"}
{"state":"Merged","mergedAt":"2022-12-13T00:08:56Z","number":4037,"body":"This PR does two things:\r\n\r\n1. We were \"completing\" `_videoWalkthroughStateStream`. This would mean that subsequent connections / walkthroughs would *not* attach file references correctly as this stream not longer received / published events.\r\n\r\n2. VSCode is currently publishing events, regardless of video walkthrough state. It will now only publish events if there is an active walkthrough based on the Video Walkthrough app's running state.","mergeCommitSha":"054d6d1d16366a7219d851e0fa06dce181aa6309","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4037","title":"Update statefulness of VSCode walkthrough","createdAt":"2022-12-12T22:41:03Z"}
{"state":"Merged","mergedAt":"2022-12-13T00:15:39Z","number":4038,"body":"Show the top three experts for a file, in the VSCode editor's tooltip.\r\n\r\nI made a new class (`FileTopicStream`), whose purpose is to return the set of topics relevant to a file.  This merges a lot of upstream data and fetches the topic when needed.  This is now used by the explorer insights panel and the TextEditorSourceMarks code.\r\n\r\n<img width=\"596\" alt=\"Screen Shot 2022-12-12 at 3 04 03 PM\" src=\"https://user-images.githubusercontent.com/2133518/207175400-4b9d47f1-7f4f-4f14-9dcd-3d9ca7fc3727.png\">\r\n","mergeCommitSha":"cbeaca4871aeb2674715d9559d67d73a482d9c21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4038","title":"Show topic experts in VSCode editor tooltip","createdAt":"2022-12-12T23:05:51Z"}
{"state":"Merged","mergedAt":"2022-12-13T00:44:52Z","number":4039,"body":"<img width=\"919\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/207197611-388ce65a-fe44-4720-8a8e-38ac5c95252f.png\">\r\n","mergeCommitSha":"6c3656e7fc5446a252f01e1c92845af494e8a4e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4039","title":"Add histogram sub pipeline","createdAt":"2022-12-12T23:47:29Z"}
{"state":"Merged","mergedAt":"2022-02-25T23:29:04Z","number":404,"body":"NOTE: Lots of file changes but this is primarily due to me moving things around and deleting older files. Bulk of the work is in `vscode/src/knowledge/` and `shared/webComponents/Button/`\r\n\r\n* Refactored the ability to create discussions/notes into a single command/webview/form component\r\n    * Essentially the idea is the abstract them into a common 'Knowledge' type concept since the UI is largely similar \r\n![image](https://user-images.githubusercontent.com/13431372/155434884-f0d0a1bd-626c-43fb-bdcd-c3d99f8b5e01.png)\r\n![image](https://user-images.githubusercontent.com/13431372/155434930-f477e41c-8ecd-49ea-a4a3-b1e96752b904.png)\r\n![image](https://user-images.githubusercontent.com/13431372/155434964-49b74dca-7abe-410f-9a16-bf91626997cc.png)\r\n* Simplified the file structure of the top level folders in the vscode/ directory (this is open to feedback)\r\n```\r\nvscode/src/\r\n    discussion/  // @discussion alias\r\n        discussionThread/ // all thread view related UIs and commands\r\n        startDiscussion/ // all start discussion specific UIs and commands\r\n        DiscussionCommands.ts // file that can be shared\r\n    notes/ // @notes alias\r\n        addNote/ // all add note specific UIs and commands \r\n    knowledge/ // @knowledge alias\r\n        CreateKnowledge/ // Common UI used in for startDiscussion and addNote, component is referenced in the extension.startDiscussion.ts and extension.addNote.ts \r\n    ... // other directories, etc\r\n```\r\n\r\n* Refactored Button component to shared/ directory - similar to the MessageEditor component, it then gets imported to each client directory and reexported with the client-specific styling\r\n    * Done mostly in part to support the dropdown logic which will may be required to be shared across clients\r\n    * ![image](https://user-images.githubusercontent.com/13431372/155435163-844be657-0214-4231-b999-ed10d9ddaf56.png)\r\n","mergeCommitSha":"1d49a4560ecbf101849dd1a5bed5a3eb0c73a536","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/404","title":"Consolidate creating discussions/notes into one component","createdAt":"2022-02-24T00:44:21Z"}
{"state":"Merged","mergedAt":"2022-12-15T00:08:51Z","number":4040,"body":"per https://github.com/NextChapterSoftware/unblocked/pull/4029, topicIds are now returned with threadInfo's and pullRequest's. \r\n\r\nThis PR aggregates the topicIds to topics.\r\n\r\n<img width=\"948\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/207184917-c4b27e8b-2a15-4205-a6bb-db680fddd571.png\">\r\n\r\n~Note: This PR doesn't add topics to the UI yet, just handles the data aggregating to the insights.~","mergeCommitSha":"26c4a1261a07c3650e25afc98011bf5e1c92ee3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4040","title":"Attach topics to insights","createdAt":"2022-12-12T23:56:57Z"}
{"state":"Merged","mergedAt":"2022-12-13T19:27:12Z","number":4041,"body":"### Changes\r\n\r\n0. Reverts #3978, as no longer necessary.\r\n1. Fix bug in CommandLog that prevented visibility into git commands that had non-zero exit status.\r\n2. Bulk cache all commits in the repo, not just commits in the tree.\r\n3. Invalidate caches when the user fetches or pulls via our \"read-only\" UX.\r\n4. Optimize cache storage a bit to offset the extra objects stored.\r\n\r\n### Result\r\n- We were making 10,000s of calls to `git cat-file -e SHA^{commit}` that had non-zero exit status.\r\n  Now there are zero.","mergeCommitSha":"45de81682500617ad3f7a90fd6809bfb6597999f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4041","title":"Cache all commits in the repo, not just the commits in the tree","createdAt":"2022-12-13T02:30:34Z"}
{"state":"Merged","mergedAt":"2022-12-13T04:00:21Z","number":4042,"mergeCommitSha":"cf4c0ac313c7284d364e180306c085164d0a94b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4042","title":"Trigger topics ingestion","createdAt":"2022-12-13T03:26:33Z"}
{"state":"Merged","mergedAt":"2022-12-13T04:36:26Z","number":4043,"mergeCommitSha":"e45395895c27797cae694e0080aee5f5c43fe558","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4043","title":"Fix step function creds","createdAt":"2022-12-13T04:36:17Z"}
{"state":"Merged","mergedAt":"2022-12-13T06:22:26Z","number":4044,"mergeCommitSha":"bc38c6b453dc981d99c01148cf9b8dbc9637c9ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4044","title":"Update stack names","createdAt":"2022-12-13T06:05:33Z"}
{"state":"Merged","mergedAt":"2022-12-13T06:30:42Z","number":4045,"mergeCommitSha":"1002b9db27153e815a9a4d76f47110c1a0b8aab3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4045","title":"Fix topic ingestion id","createdAt":"2022-12-13T06:30:26Z"}
{"state":"Merged","mergedAt":"2022-12-13T06:47:20Z","number":4046,"mergeCommitSha":"d0116f736582f0549afbcf1d3aef31d1774974a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4046","title":"update","createdAt":"2022-12-13T06:47:14Z"}
{"state":"Merged","mergedAt":"2022-12-13T07:12:21Z","number":4047,"mergeCommitSha":"2e58ab5fcd4684c8611ddc9869d2f84a72d0ab9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4047","title":"Fix IAM permission for s3 for topic service","createdAt":"2022-12-13T07:08:21Z"}
{"state":"Merged","mergedAt":"2022-12-13T07:20:27Z","number":4048,"mergeCommitSha":"13707b801b38a65f0ea8abe743333ebcf62ea77c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4048","title":"update docs","createdAt":"2022-12-13T07:20:16Z"}
{"state":"Merged","mergedAt":"2022-12-13T16:01:00Z","number":4049,"mergeCommitSha":"c13bf8910e255589846b389eed9b60b20f00e3e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4049","title":"Add retries top ipelines","createdAt":"2022-12-13T15:58:36Z"}
{"state":"Merged","mergedAt":"2022-12-13T18:25:00Z","number":4050,"body":"Ensure we retry only on specific subset of errors.","mergeCommitSha":"0eb125b69840be438caf882bab6145f8ffdeb2d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4050","title":"Improve stage retries","createdAt":"2022-12-13T18:23:21Z"}
{"state":"Merged","mergedAt":"2022-12-13T22:40:49Z","number":4051,"body":"API changes only. Web clients were modified to use the `LegacyVideoMetadata` model in place of the `VideoMetadata` model in all instances.","mergeCommitSha":"0f87110f39238c30b352344875b2640ce67d1d5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4051","title":"Rev API for VideoMetadata changes","createdAt":"2022-12-13T18:50:19Z"}
{"state":"Merged","mergedAt":"2022-12-14T19:42:10Z","number":4052,"mergeCommitSha":"2480c9cfd2e13d8ff1d492ee10979ac8d34e99a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4052","title":"Add API for clients to provide folders for topic generation","createdAt":"2022-12-13T19:19:22Z"}
{"state":"Merged","mergedAt":"2022-12-13T19:36:59Z","number":4053,"mergeCommitSha":"1505813508b62ba8c68d7452d6e412d31cdc8b74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4053","title":"Clean up topics page","createdAt":"2022-12-13T19:23:05Z"}
{"state":"Merged","mergedAt":"2022-12-15T21:09:24Z","number":4054,"body":"Adds support to adding &\r\n<img width=\"797\" alt=\"CleanShot 2022-12-13 at 11 41 37@2x\" src=\"https://user-images.githubusercontent.com/1553313/207429599-766352c2-af36-4abe-921f-e6c4c3d58c16.png\">\r\n removing web references in VSCode.\r\n\r\n<img width=\"844\" alt=\"CleanShot 2022-12-13 at 11 41 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/207429609-b4cfe466-be69-4814-834b-bb5d52b20fb9.png\">\r\n<img width=\"424\" alt=\"CleanShot 2022-12-13 at 11 41 49@2x\" src=\"https://user-images.githubusercontent.com/1553313/207429620-9c68cabf-cd89-43db-9e44-f0cc4e6726e8.png\">\r\n\r\n\r\nAlso added logic so negative positions, which are set for manually added references, will appear at the end of the references list without a timestamp\r\n<img width=\"673\" alt=\"CleanShot 2022-12-13 at 11 44 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/207429563-87841fa4-2212-4a20-acaf-bca237d33c52.png\">\r\n\r\n","mergeCommitSha":"6352e29d72def4103a0c6ee03d721c41301785b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4054","title":"Modify web references in VScode","createdAt":"2022-12-13T19:45:30Z"}
{"state":"Closed","mergedAt":null,"number":4055,"body":"https://linear.app/unblocked/issue/UNB-799/sourcemark-incorrectly-treated-a-change-as-modified-unrecognizably","mergeCommitSha":"8163c043ddc1bd54de8d568c3bd99f0ec7283d84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4055","title":"Sourcemark incorrectly treated a change as Modified Unrecognizably","createdAt":"2022-12-13T20:07:06Z"}
{"state":"Merged","mergedAt":"2022-12-13T21:38:07Z","number":4056,"mergeCommitSha":"8029d6bdabc6577dc2fecc5f531e2d5bfa032300","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4056","title":"Add lemmatization","createdAt":"2022-12-13T21:37:24Z"}
{"state":"Merged","mergedAt":"2022-12-13T22:50:41Z","number":4057,"mergeCommitSha":"c37ef6a1125bafe8d606d775945eae5899183f72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4057","title":"Update histogram script","createdAt":"2022-12-13T21:59:03Z"}
{"state":"Merged","mergedAt":"2022-12-14T17:22:47Z","number":4058,"body":"There is a potential bug where VSCode would throw an error before sending the disconnect event to the walkthrough app.\r\n\r\nIn the event there is a failure on VSCode, we should still try to send the disconnect event to the walkthrough app so that it closes.","mergeCommitSha":"90c61f4d6baffa4a5e2a9cc4f94f72ea14691437","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4058","title":"Add error handling to walkthrough complete","createdAt":"2022-12-13T22:24:38Z"}
{"state":"Merged","mergedAt":"2022-12-13T23:20:38Z","number":4059,"mergeCommitSha":"883088257dc64c0db04939f7deef58d00351015f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4059","title":"Add createdAt to VideoMetadata","createdAt":"2022-12-13T22:53:30Z"}
{"state":"Merged","mergedAt":"2022-02-24T18:40:14Z","number":406,"body":"- separate SourceMark app to demonstrate concept\r\n- uses polling strategy to monitor repos\r\n\r\nPlan: https://www.notion.so/nextchaptersoftware/SourceMark-Demo-Plan-5bd323e961b046119756078145e874f8#abe9f888c8a44eb19d2e233ec888b707\r\n\r\nNext PRs:\r\n- [ ] Git primitives to support SourceMark calculation\r\n- [ ] SourceMark calculation","mergeCommitSha":"acb0ba2a093478ade9e2d0ea3fb094b8f53afa0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/406","title":"SourceMark Application Runner","createdAt":"2022-02-24T18:34:31Z"}
{"state":"Merged","mergedAt":"2022-12-13T23:05:06Z","number":4060,"mergeCommitSha":"c5f4e81e34062eed3606da52dd664e8d8ae56fe7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4060","title":"Write out json","createdAt":"2022-12-13T22:58:19Z"}
{"state":"Merged","mergedAt":"2022-12-13T23:04:26Z","number":4061,"body":"Walked @matthewjamesadam through this live earlier. No impact on data pipeline or app.","mergeCommitSha":"4d45242422488147584595adab4f86e6eaef94ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4061","title":"Scoring BERT + Slack","createdAt":"2022-12-13T23:00:41Z"}
{"state":"Merged","mergedAt":"2022-12-13T23:24:13Z","number":4062,"mergeCommitSha":"c03767f97ab9cb1dfa2330094534b33900de05fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4062","title":"Update topics page in admin console","createdAt":"2022-12-13T23:15:27Z"}
{"state":"Merged","mergedAt":"2022-12-13T23:21:21Z","number":4063,"mergeCommitSha":"ffa6b82f35ed31abd3a9dbca70b3605d9c14c4ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4063","title":"Move markdown processing","createdAt":"2022-12-13T23:19:25Z"}
{"state":"Merged","mergedAt":"2022-12-13T23:39:34Z","number":4064,"body":"Need to inclue additional phython modules in docker image\r\n\r\nhttps://us-west-2.console.aws.amazon.com/cloudwatch/home?region=us-west-2#logsV2:log-groups/log-group/$252Faws$252Fsagemaker$252FProcessingJobs/log-events/PreProcessTrainData-2c3c6e44-eac9-45d8-9f80-3bc9b9d15f21$252Falgo-1-1670974596\r\n\r\n\r\n\r\nModuleNotFoundError: No module named 'text_sanitizer'\r\n--\r\n\r\n\r\n<br class=\"Apple-interchange-newline\">","mergeCommitSha":"03ea9c7b04cedbdb8c24249fc49c8eba0accf179","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4064","title":"Fix docker file","createdAt":"2022-12-13T23:38:55Z"}
{"state":"Merged","mergedAt":"2022-12-14T00:50:19Z","number":4065,"body":"Sorry for the code duplication","mergeCommitSha":"94baa664ae4478a1904b0f0122e11c96d059cae7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4065","title":"Wire up histogram topic event handling","createdAt":"2022-12-14T00:11:55Z"}
{"state":"Merged","mergedAt":"2022-12-14T00:33:16Z","number":4066,"mergeCommitSha":"4dfd28d25eea1428f0035111bc326d7f3a053cbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4066","title":"Fix pre-processing","createdAt":"2022-12-14T00:33:10Z"}
{"state":"Merged","mergedAt":"2022-12-16T07:03:16Z","number":4067,"body":"Throw Slack + Folders + Notion at Bertopic. No pipeline or app changes.","mergeCommitSha":"6e62ce199091ecf14afea40784bd1fa8c0fe46fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4067","title":"Kitchen sink","createdAt":"2022-12-14T01:44:55Z"}
{"state":"Closed","mergedAt":null,"number":4068,"mergeCommitSha":"8455f53ceb56ec24c9a370eb158da32349ef0c06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4068","title":"[BREAKS API ON MAIN] Remove unnecessary message id from request model","createdAt":"2022-12-14T02:07:41Z"}
{"state":"Merged","mergedAt":"2022-12-14T03:33:02Z","number":4069,"body":"There is no explicit binding between a draft and video metadata. The service will check that the assets between the metadata and the draft match before executing the delete operation","mergeCommitSha":"667a00ac277fd47081f8fe92d5c8d2d279a7f3fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4069","title":"[BREAKS API ON MAIN] - Add draftId to CreateUpdateVideoMetadata","createdAt":"2022-12-14T03:15:27Z"}
{"state":"Merged","mergedAt":"2022-02-24T19:34:37Z","number":407,"body":"Initial utils:\r\n- check if git repo is shallow (we cannot support shallow repos, this is a user error)\r\n- get the canonical git repo root (takes account of symlinks, relative paths, etc)","mergeCommitSha":"42a2705b9cafecf181dfd6bcf54509a8d5e6e9b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/407","title":"Introduce Git service primitives to support SourceMark calculator","createdAt":"2022-02-24T18:55:27Z"}
{"state":"Closed","mergedAt":null,"number":4070,"mergeCommitSha":"b505c5affe673aa6a156a5c9b27138205ad77b21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4070","title":"Batch inserts into smaller transactions to avoid deadlocks","createdAt":"2022-12-14T05:36:17Z"}
{"state":"Merged","mergedAt":"2022-12-14T17:18:00Z","number":4071,"mergeCommitSha":"8bac367c160d2e83beb7ef9674a0b586b41d9efe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4071","title":"Count words in titles and slack bodies","createdAt":"2022-12-14T05:43:51Z"}
{"state":"Merged","mergedAt":"2022-12-15T00:07:28Z","number":4072,"body":"Implements the backend changes necessary for clients to adopt the MessageV3 API. Notably:\r\n1. Separate Create/Update and Get request/response objects\r\n2. Sending `draftId` to create requests triggers transactional delete of video drafts on creation of new threads and messages","mergeCommitSha":"6bff91865dca80f74fde2822d8fbe38378e00204","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4072","title":"Implement video metadata changes","createdAt":"2022-12-14T05:58:53Z"}
{"state":"Merged","mergedAt":"2022-12-15T00:47:40Z","number":4073,"body":"Added support for new VideoMetadata properties.\r\n\r\nDuration + PreviewImage.","mergeCommitSha":"2a5aae0a573518affb038f803966845b08847b3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4073","title":"Update support for new VideoMetadata models","createdAt":"2022-12-14T17:24:23Z"}
{"state":"Merged","mergedAt":"2022-12-14T18:36:12Z","number":4074,"body":"Also removing the `stem_words` function because I don't think we want that (though maybe we do?). \r\n\r\nStemming is useful for full text search but does not produce natural language words. We'd have to convert stems back to the word form, but lemmatization I think covers this:\r\n\r\nhttps://www.nltk.org/_modules/nltk/stem/lancaster.html","mergeCommitSha":"c3d0b999e46303bb2c26f6f3018afc5c17cc12ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4074","title":"Lemmatize words in sanitizer phase","createdAt":"2022-12-14T18:18:07Z"}
{"state":"Merged","mergedAt":"2022-12-14T18:36:32Z","number":4075,"mergeCommitSha":"ad51cd4974ec43de20357d9e473930fa62bffb52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4075","title":"Use real createdAt time","createdAt":"2022-12-14T18:36:02Z"}
{"state":"Merged","mergedAt":"2022-12-14T18:39:18Z","number":4076,"mergeCommitSha":"8d2fd30384ac9d523b247a39f007b5691c4b7b0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4076","title":"Lint fix","createdAt":"2022-12-14T18:38:53Z"}
{"state":"Merged","mergedAt":"2022-12-14T19:44:54Z","number":4077,"body":"Api service should not readily have access to pushing to s3 buckets.\r\n\r\nWe are using event based system to flush to s3.","mergeCommitSha":"791d9e62756e031bfba15d6ceec0aacf7a7bd16b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4077","title":"queue based system for folder data to s3","createdAt":"2022-12-14T19:26:07Z"}
{"state":"Merged","mergedAt":"2022-12-14T22:24:57Z","number":4078,"mergeCommitSha":"384c483778154a3fd890d58a05c33467de0be2f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4078","title":"Add topics impleemntation","createdAt":"2022-12-14T21:14:20Z"}
{"state":"Merged","mergedAt":"2022-12-14T21:23:30Z","number":4079,"mergeCommitSha":"c32ec0cf6ec314b15901afbcc2625857106a6d05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4079","title":"Fix slack","createdAt":"2022-12-14T21:23:24Z"}
{"state":"Merged","mergedAt":"2022-02-25T21:48:33Z","number":408,"body":"Refresh token expiration date was not properly parsed leading to refreshing with an expired refresh token.\r\nThis caused infinite recursion on client.\r\n\r\nUpdated parsing and date comparison of tokens.\r\nRenamed signout to logout to be consistent.","mergeCommitSha":"03f4a20ca8e1fb9eabc65b90a49c8c86460f6c94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/408","title":"Fix Client Refresh","createdAt":"2022-02-24T19:09:44Z"}
{"state":"Merged","mergedAt":"2022-12-15T21:34:46Z","number":4080,"body":"This is very simplistic -- on login (or when the repo set changes), periodically we will check to see if nobody has uploaded folder lists in the last 20 hours.  If nobody has, we will generate, encode, and upload the folders.","mergeCommitSha":"7bfa1b2882477e2f8c8faf94b742af6c9fa12078","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4080","title":"VSCode sends folder updates to service every now and then","createdAt":"2022-12-14T21:50:35Z"}
{"state":"Merged","mergedAt":"2022-12-14T21:51:58Z","number":4081,"mergeCommitSha":"54a581ee13bb266d1a7b753959e34ca0c147b98a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4081","title":"More sack fixes","createdAt":"2022-12-14T21:51:52Z"}
{"state":"Merged","mergedAt":"2022-12-14T22:44:37Z","number":4082,"mergeCommitSha":"894c5ebaee13b760074bd4b11ce079bb957b8204","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4082","title":"Make folder data in s3 proeprly formed","createdAt":"2022-12-14T22:43:21Z"}
{"state":"Merged","mergedAt":"2022-12-15T18:53:47Z","number":4083,"body":"<img width=\"893\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/207742640-05113280-60c4-4a02-9f21-837913d1d609.png\">\r\n","mergeCommitSha":"3961f140958f14917b65d3816394a54922dba8ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4083","title":"Add filter by experts to TopicView","createdAt":"2022-12-15T00:14:50Z"}
{"state":"Merged","mergedAt":"2022-12-15T03:39:39Z","number":4084,"body":"AVADAKADABRA! ","mergeCommitSha":"1ad8aa32ee5a9cd0750d619e4c882fd2d59295da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4084","title":"Notify Unblocked Slack when new walkthroughs are created","createdAt":"2022-12-15T00:44:46Z"}
{"state":"Merged","mergedAt":"2022-12-16T17:18:46Z","number":4085,"body":"Generates Preview/Poster images for walkthroughs.\r\n\r\nThese will be used to hopefully help with the blank initial frames in Safari.\r\nWill also reduce data costs on list pages where we can load just the preview image instead of the video.","mergeCommitSha":"78de827790b10b6c359373adfeda5cc4ae0d5ce4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4085","title":"Generate Walkthrough preview images","createdAt":"2022-12-15T00:50:48Z"}
{"state":"Merged","mergedAt":"2022-12-15T01:23:50Z","number":4086,"mergeCommitSha":"81ceeca6752524c3d88b78747bed969394d4185c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4086","title":"Add folder data to pipelines","createdAt":"2022-12-15T01:13:40Z"}
{"state":"Merged","mergedAt":"2022-12-15T19:32:22Z","number":4087,"body":"Creating the models to support Search V2. Next PRs will wire up indexing and implement the `searchInsights` API operation.","mergeCommitSha":"a328cb81ad07a53454182dfe274b555121343163","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4087","title":"Search V2","createdAt":"2022-12-15T01:15:43Z"}
{"state":"Merged","mergedAt":"2022-12-15T04:30:37Z","number":4088,"body":"- AddHistgoramFilter\r\n- Update\r\n","mergeCommitSha":"09444f046e780636002e3a84afb0d321ec2605a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4088","title":"Enable folder filtering for bert and histogram","createdAt":"2022-12-15T04:28:10Z"}
{"state":"Merged","mergedAt":"2022-12-15T04:56:39Z","number":4089,"mergeCommitSha":"00fe2d7f922550871883c295575ed64883ca974b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4089","title":"update documentation","createdAt":"2022-12-15T04:55:02Z"}
{"state":"Merged","mergedAt":"2022-02-24T19:41:16Z","number":409,"body":"Going to immediately follow up this PR to clean up this hacky approach, but wanted to get this in so that PR ingestion uses the merge commit SHA when creating SourcePoints to unblock richie","mergeCommitSha":"e89dd06c6c03b43f01957d98fbc1fc8c69ef3cf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/409","title":"Commit hash should be merge commit sha","createdAt":"2022-02-24T19:18:01Z"}
{"state":"Merged","mergedAt":"2022-12-15T05:44:41Z","number":4090,"mergeCommitSha":"7750ed0841737b50dd55f551ff2e5941694006e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4090","title":"Improve folder parsing","createdAt":"2022-12-15T05:43:42Z"}
{"state":"Merged","mergedAt":"2022-12-15T17:26:05Z","number":4091,"mergeCommitSha":"d00643c47f11339369c2af47a28860b429e7baa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4091","title":"Map insights to topics from all sources (including BERT and histogram)","createdAt":"2022-12-15T06:06:25Z"}
{"state":"Merged","mergedAt":"2022-12-15T06:07:12Z","number":4092,"mergeCommitSha":"15d59e064fb1810e0cfb706e7e7b235332a53cdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4092","title":"Optimize state machine executions","createdAt":"2022-12-15T06:06:57Z"}
{"state":"Merged","mergedAt":"2022-12-15T06:11:17Z","number":4093,"mergeCommitSha":"4ccd2992959b494fed2bb750fdedd87d258a61ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4093","title":"ReduceQuery","createdAt":"2022-12-15T06:11:12Z"}
{"state":"Merged","mergedAt":"2022-12-15T07:02:14Z","number":4094,"mergeCommitSha":"e3e92578524b87da8984496bee6a74eea395aaad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4094","title":"Increase timeouts and fix inflect code","createdAt":"2022-12-15T07:00:00Z"}
{"state":"Merged","mergedAt":"2022-12-15T07:54:53Z","number":4096,"mergeCommitSha":"099cb41f532b2bc8343fb29a6c0a225bb3dddbd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4096","title":"Try to speed up lemmatization","createdAt":"2022-12-15T07:54:46Z"}
{"state":"Merged","mergedAt":"2022-12-15T15:24:59Z","number":4097,"mergeCommitSha":"cd6b7fba603f3e18b3dc0803d48b883bf4702077","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4097","title":"optimize sanitization","createdAt":"2022-12-15T15:24:53Z"}
{"state":"Merged","mergedAt":"2022-12-15T17:49:02Z","number":4098,"mergeCommitSha":"82d1b89c0b09999bfb8668c0273b93f42d8663b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4098","title":"Parallelize sanitization","createdAt":"2022-12-15T17:20:16Z"}
{"state":"Merged","mergedAt":"2022-12-15T19:09:29Z","number":4099,"mergeCommitSha":"0c2943d0b6d01b467a218e1f88355e80c5335ba0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4099","title":"Remotely enable sourcemark debugging on customer accounts","createdAt":"2022-12-15T17:31:12Z"}
{"state":"Merged","mergedAt":"2022-01-17T19:13:59Z","number":41,"mergeCommitSha":"45cdd60b75eb7c8d9d5dd13d2945a9a05712bdb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/41","title":"Change Service Service to Identity Service","createdAt":"2022-01-17T19:11:08Z"}
{"state":"Merged","mergedAt":"2022-02-25T02:10:36Z","number":410,"body":"No logic changes, just cleanup from https://github.com/NextChapterSoftware/unblocked/pull/409","mergeCommitSha":"d3597281d14745c29d26035cb6fc8173658da37f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/410","title":"Refactor GitHub API client logic","createdAt":"2022-02-24T21:39:56Z"}
{"state":"Merged","mergedAt":"2022-12-20T21:14:56Z","number":4100,"mergeCommitSha":"b274f8b480d3bd8795d33ecd1e8eb0b7a3922bc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4100","title":"GitHub App manifest for On-premise (GitHub Enterprise Server) [BREAKS API ON MAIN]","createdAt":"2022-12-15T17:31:42Z"}
{"state":"Merged","mergedAt":"2022-12-15T17:45:00Z","number":4101,"mergeCommitSha":"114a5ee53fda7f2badc9ea2787a968ea9f84e381","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4101","title":"Chunk inserts","createdAt":"2022-12-15T17:31:53Z"}
{"state":"Merged","mergedAt":"2022-12-15T19:02:50Z","number":4102,"body":"I missed this UI when we switched the hilighter over to be entirely file-extension-based.","mergeCommitSha":"14c3e3f3a79e9ff8026e80825caeb01face1dadf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4102","title":"Fix read-only view syntax hilighting","createdAt":"2022-12-15T18:50:33Z"}
{"state":"Merged","mergedAt":"2022-12-15T19:37:37Z","number":4103,"body":"Fixes a where navigating directly to a thread caused a runtime exception.\r\nWas due to prevRoute not existing if navigating directly to a page using a url.","mergeCommitSha":"b21939afa443d52b4b40fc7b23abcecf56f2c6a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4103","title":"Fix crash bug","createdAt":"2022-12-15T19:14:12Z"}
{"state":"Merged","mergedAt":"2022-12-15T20:04:45Z","number":4104,"mergeCommitSha":"39e61e6dfcfacdeee3c0f29dacdbe2559b165c42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4104","title":"Wire up Search V2 indexing","createdAt":"2022-12-15T19:41:01Z"}
{"state":"Merged","mergedAt":"2022-12-15T20:18:01Z","number":4105,"body":"1. PowerML changes.\r\n2. Glue ETL can now handle no folders for customer.\r\n3. Clean up stuff\r\n","mergeCommitSha":"793da0765e7f8817e7d952bb98deb6bb20b76e42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4105","title":"Optimizations to glue code with dsl updates with powerml","createdAt":"2022-12-15T20:14:37Z"}
{"state":"Merged","mergedAt":"2022-12-15T21:32:00Z","number":4106,"body":"Mistake on my part, we didn't handle git failures here correctly.  The end result is that if you clicked on a file that hasn't been added to the git repository, the stream would go into the error state and the insights panel would go into the 'loading' state forever.","mergeCommitSha":"8e625943ec852f7e83507348706bbf5ba53733be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4106","title":"Fix explorer insights bug that caused spinner","createdAt":"2022-12-15T21:05:06Z"}
{"state":"Merged","mergedAt":"2022-12-16T16:42:21Z","number":4107,"body":"Update RelatedFilesSection to delete file references along web file references","mergeCommitSha":"29a7ed94d1578d7838cbe8bef03de03290781a68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4107","title":"Add ability to delete file references","createdAt":"2022-12-15T21:30:27Z"}
{"state":"Merged","mergedAt":"2022-12-15T22:31:58Z","number":4108,"body":"Add a UI for when there are insights in the file, but not in the part of the view you are looking at, when 'Visible range' is enabled.\r\n\r\n<img width=\"453\" alt=\"Screen Shot 2022-12-15 at 1 32 59 PM\" src=\"https://user-images.githubusercontent.com/2133518/207971476-750609b7-31e1-4a75-b58d-e7119a45adb6.png\">\r\n","mergeCommitSha":"dabdd0b3c23c9c2c0812e266890a7cdef1479396","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4108","title":"Add 'No insights in view area' UI to VSCode explorer insights panel","createdAt":"2022-12-15T21:33:48Z"}
{"state":"Merged","mergedAt":"2022-12-15T21:57:21Z","number":4109,"body":"- Added readme with info about our GHE deployment\r\n- Added the modify CloudFormation template used to deploy this service \r\n- Added code used for turning it on/off using a hacky lambda function ","mergeCommitSha":"31d58da642b0d536048ce3447c995e06db8d26ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4109","title":"document ghe instance","createdAt":"2022-12-15T21:34:00Z"}
{"state":"Merged","mergedAt":"2022-02-25T00:55:27Z","number":411,"body":"- add git diff parser: fundamental to SourceMark tracking\r\n- and utils like: headCommitSha, remoteCloneUrl, rootCommit which will be used in follow up PRs","mergeCommitSha":"1e306a93fabe910ee4cdfc88e48f5041ebd15342","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/411","title":"Git Diff and Friends","createdAt":"2022-02-25T00:50:24Z"}
{"state":"Merged","mergedAt":"2022-12-15T22:00:34Z","number":4110,"body":"Oops, I had this code sitting locally and didn't push it to the PR for this feature.","mergeCommitSha":"fea098c9f382bf3b38e650e9bc0d3af9ee5090ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4110","title":"Add API error handler in FolderUploader","createdAt":"2022-12-15T21:37:41Z"}
{"state":"Merged","mergedAt":"2022-12-15T21:40:07Z","number":4111,"mergeCommitSha":"31736e5907018b6a1103d87f843c4abf1f489950","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4111","title":"optimize histogram","createdAt":"2022-12-15T21:40:01Z"}
{"state":"Merged","mergedAt":"2022-12-15T21:47:22Z","number":4112,"mergeCommitSha":"0f105dc116eeec8d0d507de92f3b49231924e777","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4112","title":"Fix prod activemq","createdAt":"2022-12-15T21:47:14Z"}
{"state":"Merged","mergedAt":"2022-12-15T22:08:21Z","number":4113,"mergeCommitSha":"5bc47e58a205cf927acd740f2abc20fe7c0d9d39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4113","title":"Increase glue worker count","createdAt":"2022-12-15T22:08:14Z"}
{"state":"Merged","mergedAt":"2022-12-15T22:36:10Z","number":4114,"body":"<img width=\"911\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/207980981-b7e7b758-8e38-4d88-8ecb-5499388dc12a.png\">\r\n","mergeCommitSha":"e8303eafdd8ee38bc571268ad87a7b20a7452895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4114","title":"Bold the topic name","createdAt":"2022-12-15T22:30:17Z"}
{"state":"Merged","mergedAt":"2022-12-16T00:27:45Z","number":4115,"mergeCommitSha":"f53e818c3c806c257ada09bc3b1b6123962eec87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4115","title":"Enable new VSCode Explorer insights panel globally","createdAt":"2022-12-15T22:34:52Z"}
{"state":"Merged","mergedAt":"2022-12-15T23:39:52Z","number":4116,"mergeCommitSha":"b7f71ba8347224f5398cb0bd39163fa08889afc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4116","title":"Search V2 indexing fixes","createdAt":"2022-12-15T22:56:44Z"}
{"state":"Merged","mergedAt":"2022-12-16T00:50:18Z","number":4117,"body":"Pending reviews don't have a submittedAt, so this PR fixes:\r\n\r\nuse:kotlinx.serialization.json.internal.JsonDecodingException: Unexpected JSON token at offset 1077: Expected string literal but 'null' literal was found at path: $.review.submitted_at Use 'coerceInputValues = true' in 'Json {}` builder to coerce nulls to default values. JSON input: .....032b0cad0b49e\",\"submitted_at\":null,\"state\":\"pending\",\"html_u.....","mergeCommitSha":"23df7b1587613c54cdb277b92a39bf54600f3d2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4117","title":"Ignore pending reviews","createdAt":"2022-12-15T23:35:31Z"}
{"state":"Merged","mergedAt":"2022-12-16T16:54:46Z","number":4118,"mergeCommitSha":"41fefdd7b571d6714ab1fc70605be5643d6a2d58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4118","title":"Fix text vibrancy issues in onboarding panels on Ventura","createdAt":"2022-12-16T00:23:08Z"}
{"state":"Merged","mergedAt":"2022-12-16T00:47:00Z","number":4119,"mergeCommitSha":"65d82c1f1c547cbba5b6e0f9ddce07324472b294","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4119","title":"Implement searchInsights operation","createdAt":"2022-12-16T00:36:08Z"}
{"state":"Merged","mergedAt":"2022-03-03T18:25:48Z","number":412,"mergeCommitSha":"4dc42f2bd182c379bae84d4c8ba879e7cdad488c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/412","title":"Add ThreadType to Thread model to support Notes","createdAt":"2022-02-25T01:09:01Z"}
{"state":"Merged","mergedAt":"2022-12-16T01:12:41Z","number":4120,"mergeCommitSha":"4c74b34a640267b4b8dff63dec50c69d838b0bc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4120","title":"Send Preview url back with the VideoMetadata API response","createdAt":"2022-12-16T01:04:19Z"}
{"state":"Merged","mergedAt":"2022-12-16T05:20:56Z","number":4121,"mergeCommitSha":"18fd067feab4b997c47ffb57f9c195f67df6810a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4121","title":"Include title in SearchInsightAuthor.searchVector","createdAt":"2022-12-16T05:03:00Z"}
{"state":"Closed","mergedAt":null,"number":4122,"mergeCommitSha":"53a8843cebf59747b4e7cadad24813c99266d664","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4122","title":"Add TopicExpertModel.sourceType to note the recommendation engine source","createdAt":"2022-12-16T05:57:54Z"}
{"state":"Merged","mergedAt":"2022-12-16T14:32:03Z","number":4123,"mergeCommitSha":"5b2ced68d501ff8c172d10ae03b5fd97b9f681f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4123","title":"Add docs","createdAt":"2022-12-16T14:31:44Z"}
{"state":"Merged","mergedAt":"2022-12-16T19:59:17Z","number":4124,"mergeCommitSha":"33b8dccee7e99af2ca203fed1bf299f3e93690c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4124","title":"Lint fix","createdAt":"2022-12-16T19:59:01Z"}
{"state":"Merged","mergedAt":"2022-12-16T20:26:06Z","number":4125,"mergeCommitSha":"715eb9129b231eaddb3099d0bcb013298e2cd28f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4125","title":"Force the walkthrough app to close if the initializer is unresponsive","createdAt":"2022-12-16T20:19:08Z"}
{"state":"Merged","mergedAt":"2022-12-16T22:04:16Z","number":4126,"mergeCommitSha":"2c974ae77ea20a863c84c3c350b525b684a96ffb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4126","title":"Fix topic mapping","createdAt":"2022-12-16T20:28:12Z"}
{"state":"Merged","mergedAt":"2022-12-16T21:36:56Z","number":4127,"body":"Adds entry point to video drafts.\r\n\r\nThe number is not fully responsive as there's no pusher channel. It will react to current user interaction but if one pushes a new draft while viewing the page, a refresh is necessary.\r\n\r\nRow will disappear if there are no drafts.\r\n\r\n<img width=\"1403\" alt=\"CleanShot 2022-12-16 at 12 28 58@2x\" src=\"https://user-images.githubusercontent.com/1553313/208183836-b35f392a-8dc6-4523-a987-6412bc05302d.png\">\r\n","mergeCommitSha":"bcd5454d09ff9e2af336554eb196a707aa7fad8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4127","title":"Add web entry point to video drafts","createdAt":"2022-12-16T20:29:25Z"}
{"state":"Merged","mergedAt":"2022-12-16T23:06:02Z","number":4128,"body":"Addresses race in https://github.com/NextChapterSoftware/unblocked/pull/4099. I did it wrong originally.","mergeCommitSha":"2f1d9abc4126c2436887e8a44aea928c0381d1d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4128","title":"Use capability stream for DebugSourcemarkEngine","createdAt":"2022-12-16T21:06:33Z"}
{"state":"Merged","mergedAt":"2022-12-16T23:05:37Z","number":4129,"body":"From every hour to every 12 hours.\n\nMotivation\n- https://getunblocked.grafana.net/d/eIvzXO_7k/service-pod-resources-overview?orgId=1&var-datasource=grafanacloud-getunblocked-prom&var-cluster=prod-us-west-2&var-namespace=default&var-service=scmservice&from=now-24h&to=now-1m\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1671226123355469?thread_ts=1671148019.856799&cid=C02HEVCCJA3","mergeCommitSha":"d36726ccd49da08f831c1772706b1e484e3d83ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4129","title":"Periodic pull request ingestion runs less frequently","createdAt":"2022-12-16T21:57:35Z"}
{"state":"Merged","mergedAt":"2022-02-25T17:20:50Z","number":413,"body":"Also adds logic to skip ingestion if the repo already has SourceMarks. This is a quick and dirty way to prevent continually ingesting PR comments every time the application loads. \r\n\r\nIf you wish to re-ingest, just drop your database or run\r\n\r\n`DELETE FROM sourcemarkmodel where repo='fb06ca32-cdf0-4bcc-9e08-c8c675768235'`\r\n\r\nUltimately we want to make ingestion idempotent and be able to pick up where it left off if it's interrupted mid-ingestion. We'll do that later.","mergeCommitSha":"097337906a0b09c5dbf278e58a3bba779dc10f76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/413","title":"Run PR ingestion in DEV","createdAt":"2022-02-25T02:36:48Z"}
{"state":"Merged","mergedAt":"2022-12-20T22:55:10Z","number":4130,"body":"To allow the server to return the result counts","mergeCommitSha":"4429d5b9b219d2a4226c4778aaa12bb2f45eb8a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4130","title":"[BREAKS API ON MAIN] Update searchInsights response shape","createdAt":"2022-12-16T22:07:40Z"}
{"state":"Merged","mergedAt":"2022-12-16T22:45:58Z","number":4131,"body":"Need to pass draftID when creating a thread from a Video Draft.\r\n\r\nAlso notifies our VideoDraftStore to refresh after thread creation.","mergeCommitSha":"439547170f84085e5c3d1e14a5de24ee26de0cd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4131","title":"Delete VideoDraft on thread creation","createdAt":"2022-12-16T22:28:38Z"}
{"state":"Closed","mergedAt":null,"number":4132,"mergeCommitSha":"a3b4c73cf996d21669bf2a0cd533266b9bc2a26d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4132","title":"Limit topics returned from the API to 50","createdAt":"2022-12-16T22:41:40Z"}
{"state":"Merged","mergedAt":"2022-12-17T00:11:10Z","number":4133,"mergeCommitSha":"92c338045d4a17358c8946cb50bfa6469ffba1a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4133","title":"Dont limit the number topics used for mapping insights","createdAt":"2022-12-16T22:47:41Z"}
{"state":"Closed","mergedAt":null,"number":4134,"body":"Ability to delete threads at the message context level.\r\n\r\nThis is currently necessary for VSCode as standard thread operations are done in the header which is tightly coupled with the CodeBlock. This CodeBlock is not rendered for video walkthroughs :)\r\n\r\n<img width=\"834\" alt=\"CleanShot 2022-12-16 at 14 47 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/208201322-77df6bb7-0647-4023-83ca-7710162bcbae.png\">\r\n","mergeCommitSha":"bfdcf70a04ffb0487f7ed436e47bbf08fb9587cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4134","title":"Delete video in VSCode","createdAt":"2022-12-16T22:48:00Z"}
{"state":"Merged","mergedAt":"2022-12-16T23:21:45Z","number":4135,"body":"Updated the readme with email setup info and automatic shutdown stuff.","mergeCommitSha":"0597ff083e5087528a46abb3145987a2ebbb5b08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4135","title":"updated lambda and readme","createdAt":"2022-12-16T22:52:21Z"}
{"state":"Merged","mergedAt":"2022-12-20T19:34:54Z","number":4136,"body":"VSCode was sending a file reference event on walkthrough starting even if it's not in focus.\r\n\r\nThis was causing unexpected initial repo file ref events showing when starting a video walkthrough from the hub.\r\n","mergeCommitSha":"9871e595e89eda62f26b44f59656ebdf455e5ab8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4136","title":"Ignore unexpected repo file event during hub video walkthrough","createdAt":"2022-12-17T00:14:41Z"}
{"state":"Merged","mergedAt":"2022-12-17T01:01:41Z","number":4137,"mergeCommitSha":"5c9178ab62fb60a0ba2f5f1a101740f3d62ae9ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4137","title":"Upsert topic experts periodically","createdAt":"2022-12-17T00:40:01Z"}
{"state":"Merged","mergedAt":"2022-12-19T17:53:36Z","number":4138,"body":"Limit the topics for a file to the top 3 topics that have at least 50% of the score of the top topic.","mergeCommitSha":"b3ab15f17b48e5e421773775e6dcef6a3a350444","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4138","title":"Limit topics per file in VSCode","createdAt":"2022-12-17T00:46:20Z"}
{"state":"Merged","mergedAt":"2022-12-19T19:13:09Z","number":4139,"mergeCommitSha":"c548eda38fd159c62b1e9e3e786bb76d8638a858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4139","title":"Clean up InsightsApiDelegateImpl and SearchInsightService","createdAt":"2022-12-19T18:56:27Z"}
{"state":"Merged","mergedAt":"2022-03-01T14:58:12Z","number":414,"body":"This pr does the following:\r\n1. Adds styling for TreeView for VScode.\r\n2. Removed TreeNode and we are now using a flattened TreeViewState for webview and extension.\r\n3. Add specialized TreeDataProviders for each of the specific panes we have in sidebar. Somewhat mimics how vscode data providers work.\r\n4. Adds sidebar logic and populating with dummy data.\r\n\r\nTODO:\r\n1. Fine tune how state management works.\r\n2. Add event handlers for a bunch of things.\r\n3. Figure out refresh logic.\r\n4. Output live data.\r\n\r\n<img width=\"602\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/155663170-0ee91f22-84f3-4a9f-88df-0eeebaab3a0e.png\">\r\n\r\n","mergeCommitSha":"e720292968313c2e651e198f1eaa19661d386c4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/414","title":"Add Sidebar Panes for VSCode Extension","createdAt":"2022-02-25T05:08:38Z"}
{"state":"Merged","mergedAt":"2022-12-20T19:07:38Z","number":4140,"body":"Add shared `InsightHeader` component for all vscode thread views. It will render a header, a subheader (includes topics and any other optional metadata) and the menu component in the same format.\r\n\r\nthreads:\r\n<img width=\"542\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/208501207-5169fd12-18ff-4128-a91b-82e34425e0f6.png\">\r\n<img width=\"544\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/208502291-804dce29-009d-4ab6-8d36-270c14359773.png\">\r\n<img width=\"545\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/208502376-82d5ff33-1f66-48ed-86c8-0dbde9671edd.png\">\r\n\r\n\r\nPRs:\r\n<img width=\"547\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/208501263-029d8eba-f5cd-4265-8e4d-4a96317cfa69.png\">\r\n","mergeCommitSha":"26f4f84a2d089e56d95e38b296562e7a8a33554a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4140","title":"Add unified headers for vscode insights","createdAt":"2022-12-19T19:17:13Z"}
{"state":"Merged","mergedAt":"2022-12-20T17:19:13Z","number":4141,"body":"Some members on the team were noticing issues with random logouts on client.\r\n\r\nBased on Matt's logs, it looks like errors were being thrown on RefreshAuth which was causing logout operation.\r\n\r\nUpdated logic to *only* logout on 401 or other expected unauthed situations.","mergeCommitSha":"8f269577c73c3819dc296fd525c19270dcb571b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4141","title":"Conditional logout on refresh auth","createdAt":"2022-12-19T19:25:03Z"}