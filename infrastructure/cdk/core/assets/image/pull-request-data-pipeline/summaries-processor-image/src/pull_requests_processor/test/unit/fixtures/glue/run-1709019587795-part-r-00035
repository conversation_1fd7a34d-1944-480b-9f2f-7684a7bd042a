{"state":"Merged","mergedAt":"2023-08-28T20:59:23Z","number":7794,"mergeCommitSha":"5bc0d7201a21a7d59575cc90e17ce44b302f9383","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7794","title":"Enable StackOverflowForTeams in dev and local for now","createdAt":"2023-08-28T20:06:55Z"}
{"state":"Merged","mergedAt":"2023-08-28T20:49:15Z","number":7795,"mergeCommitSha":"595784c6351acf03d9b9b0e32a97be9dc97e9449","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7795","title":"Version.createdAt can be null","createdAt":"2023-08-28T20:19:20Z"}
{"state":"Merged","mergedAt":"2023-08-28T21:06:31Z","number":7796,"mergeCommitSha":"e98cda58e712da9d03e8e3bb43d9baf742f6ac4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7796","title":"Add StackOverflowTeamsIngestionService logging","createdAt":"2023-08-28T20:33:38Z"}
{"state":"Merged","mergedAt":"2023-08-29T16:52:09Z","number":7798,"body":"This reverts commit 5bc0d7201a21a7d59575cc90e17ce44b302f9383.","mergeCommitSha":"4dd22efa8bdb707dc07c8e7d7c26207392098fad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7798","title":"Enable StackOverflowForTeams in prod","createdAt":"2023-08-28T21:02:12Z"}
{"state":"Merged","mergedAt":"2023-08-28T21:38:03Z","number":7799,"mergeCommitSha":"ff7c45b55001c08fa9c0dfe0387dae83decb94c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7799","title":"Only hit the API for answers if the questions have answers","createdAt":"2023-08-28T21:28:34Z"}
{"state":"Merged","mergedAt":"2022-01-19T18:34:13Z","number":78,"body":"https://github.com/Chapter2Inc/codeswell/pull/77#issuecomment-1016743345","mergeCommitSha":"979d6b71a8388c79702e8f04e7e7c055e071c112","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/78","title":"Treat warnings as errors","createdAt":"2022-01-19T18:27:21Z"}
{"state":"Merged","mergedAt":"2022-04-05T05:27:02Z","number":780,"body":"PR ingested comments currently show the diff hunk, but really want it to show the lines from the current version of the file. We can get this from the diff hunk if we do some processing.\r\n\r\nIf we did this right, then the source snippet lines will match exactly the lines in that file at that commit.","mergeCommitSha":"7f9c18678b334ce9cab8c05b0e2cdaf86d0141a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/780","title":"Get correct source snippet for a PR ingested comment","createdAt":"2022-04-05T03:55:10Z"}
{"state":"Merged","mergedAt":"2023-08-28T22:04:51Z","number":7800,"mergeCommitSha":"ce78e91b802507a6ec03e2f54395d0b4b3234266","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7800","title":"Confluence pages and blog posts can have null createdAts","createdAt":"2023-08-28T21:44:17Z"}
{"state":"Merged","mergedAt":"2023-08-28T22:56:49Z","number":7801,"mergeCommitSha":"c69eee7a704f0834bd0f6c3cf601f89b6185baf5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7801","title":"Add 1 second to filter to skip previously ingested Stack Overflow questions","createdAt":"2023-08-28T22:46:50Z"}
{"state":"Merged","mergedAt":"2023-08-29T17:23:53Z","number":7802,"body":"NOTE: only merge this when the feature is ready for prod.","mergeCommitSha":"caa374e779e7ba5fbfce819e54abe2f20aef0d7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7802","title":"Show stack overflow in nav","createdAt":"2023-08-28T22:53:10Z"}
{"state":"Merged","mergedAt":"2023-08-29T02:33:41Z","number":7803,"body":"- Cleanup\r\n- Update LLM code for mosaic and pull request summaries\r\n","mergeCommitSha":"97e69d7d626f1e0702bc3e2b9dd282c414ae49a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7803","title":"MosaicCleanup","createdAt":"2023-08-29T00:08:34Z"}
{"state":"Merged","mergedAt":"2023-08-29T03:30:05Z","number":7804,"mergeCommitSha":"65f9c61eacb108f7d759fe4c5045bb789c5559f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7804","title":"Add more logging to search event handlers","createdAt":"2023-08-29T01:37:53Z"}
{"state":"Merged","mergedAt":"2023-08-29T16:22:06Z","number":7805,"body":"I added `PersonStore.allIds()`, but turns out it doesn't work.  Fix and add test.","mergeCommitSha":"e35097902200e3a21bb6209d1d6cb64c227f2a67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7805","title":"Fix bad method in PersonStore","createdAt":"2023-08-29T03:44:17Z"}
{"state":"Merged","mergedAt":"2023-08-29T07:03:34Z","number":7806,"mergeCommitSha":"35f4c5e745e5a00f29e3b91944829e31bc5fbcd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7806","title":"Add more logging and some tests to StackOverflowTeamsEmbeddingService","createdAt":"2023-08-29T06:41:32Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:17:20Z","number":7808,"mergeCommitSha":"9d3c51d720b0f4ca9157abc6126637740a67db27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7808","title":"Remove duplicate thread display call","createdAt":"2023-08-29T17:57:58Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:24:26Z","number":7809,"body":"We were not doing auto answer for message file shared events\r\n","mergeCommitSha":"ad8b89592c8ad661ba8c81103613d736951bf6d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7809","title":"Fix statsig problem","createdAt":"2023-08-29T18:11:14Z"}
{"state":"Merged","mergedAt":"2022-04-05T12:28:39Z","number":781,"mergeCommitSha":"a36d63b5372e4e9d5809744f11ee8476481cd38b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/781","title":"Add asset service ecr","createdAt":"2022-04-05T12:23:35Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:40:10Z","number":7810,"mergeCommitSha":"a2f9f8b35272f692a5e57c24bebd079b0881ab51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7810","title":"Ignore Stack Overflow bot questions","createdAt":"2023-08-29T18:16:11Z"}
{"state":"Merged","mergedAt":"2023-08-29T23:19:06Z","number":7811,"body":"- Lambda function generates a random value and uses it for nonce\r\n- For every .html file or requests for site root (\"/\"), Lambda will retrieve the object content from S3 and modify all script tags before returning the response\r\n- Caching is set to 5mins and it is configurable using config.json file\r\n\r\nFull conversation can be found here: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1693004625770079","mergeCommitSha":"23b2c791463a8fef9c413cbcc5828d39032a2f3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7811","title":"Adding origin response lambda for docs sites","createdAt":"2023-08-29T18:26:34Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:48:32Z","number":7812,"mergeCommitSha":"0bac07426014ef43b0bc2eecdd23f317fdc893e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7812","title":"Increase Confluence lock expiration","createdAt":"2023-08-29T18:32:00Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:59:32Z","number":7813,"body":"Update email assets","mergeCommitSha":"ab3621bdae56e953cc7197c4aee12b8d5239a117","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7813","title":"Update email assets","createdAt":"2023-08-29T18:37:21Z"}
{"state":"Merged","mergedAt":"2023-08-29T23:48:49Z","number":7814,"mergeCommitSha":"5047957a8d8d3cff4c19fa1b00cef5c8428e2195","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7814","title":"Update admin console metrics charts to start from 2023 Jan 1","createdAt":"2023-08-29T18:56:37Z"}
{"state":"Merged","mergedAt":"2023-08-29T19:07:12Z","number":7815,"mergeCommitSha":"9afe676168b140dd7a7e9cae1bc2a6da3ee6817f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7815","title":"Add more source ingestion logging","createdAt":"2023-08-29T18:58:42Z"}
{"state":"Merged","mergedAt":"2023-08-29T20:00:19Z","number":7816,"body":"Fixes https://app.logz.io/#/dashboard/osd/?_a=(columns%3A!(message)%2Cfilters%3A!(('%24state'%3A(store%3AappState)%2Cbool%3A(minimum_should_match%3A1%2Cshould%3A!((match_phrase%3A(_logzio_logceptions%3A'367bc0d1f7b167f403a706c690304783'))))%2Cmeta%3A(alias%3Ai.k.c.p.ClientRequestException%0A%2Cdisabled%3A!f%2Ckey%3Abool%2ClogzInsights%3A!t%2Cnegate%3A!f%2Ctype%3Acustom%2Cvalue%3A'%7B%22minimum_should_match%22%3A1%2C%22should%22%3A%5B%7B%22match_phrase%22%3A%7B%22_logzio_logceptions%22%3A%22367bc0d1f7b167f403a706c690304783%22%7D%7D%5D%7D')))%2Cindex%3A'logzioCustomerIndex*'%2Cinterval%3Aauto%2Cquery%3A(language%3Alucene%2Cquery%3A'')%2Csort%3A!())&_g=(filters%3A!()%2CrefreshInterval%3A(pause%3A!t%2Cvalue%3A0)%2Ctime%3A(from%3Anow-15m%2Cto%3Anow))&accountIds=411850&switchToAccountId=411850&discoverTab=logz-exceptions-tab","mergeCommitSha":"89bd8a2d1281622fe41397c2b9f1f3b726b6a591","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7816","title":"Chunk Stack Overflow answers retrieval to max 50 questions","createdAt":"2023-08-29T19:20:28Z"}
{"state":"Merged","mergedAt":"2023-08-29T20:45:49Z","number":7817,"mergeCommitSha":"e13fd88b0426dadd0762ea628da2dd59b4170236","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7817","title":"Add asset","createdAt":"2023-08-29T20:06:49Z"}
{"state":"Closed","mergedAt":null,"number":7818,"mergeCommitSha":"e1d8a4333f8a7d9e02f8db31dd374234d093f2d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7818","title":"Update ConfluenceSite.lastSynced only when done ingesting everything","createdAt":"2023-08-29T20:37:40Z"}
{"state":"Merged","mergedAt":"2023-08-29T21:40:06Z","number":7819,"mergeCommitSha":"a87e9dced5ae2ad81be9b20dd67819872c465a5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7819","title":"Only ingest Stack Overflow questions with accepted answers","createdAt":"2023-08-29T20:38:13Z"}
{"state":"Merged","mergedAt":"2022-04-05T12:57:04Z","number":782,"mergeCommitSha":"7416daecb602c46b71289bb322685f727ad83fad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/782","title":"update","createdAt":"2022-04-05T12:56:59Z"}
{"state":"Merged","mergedAt":"2023-08-30T04:35:48Z","number":7820,"body":"First part of updating keyboard handling -- the new list library has some things we need.  Factored this PR out.\r\n\r\nI removed all the various custom editor types -- everything can just use `Editor`.  They were causing build problems.  The sub-types are nice if we want to have parts of our editor be able to be factored out of our editor, but we don't need that and I doubt we ever will.","mergeCommitSha":"7c676dbfd996ec66e1e3b314959a88365a90c0c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7820","title":"Update slate editor and list library","createdAt":"2023-08-29T20:54:59Z"}
{"state":"Merged","mergedAt":"2023-08-30T18:00:59Z","number":7821,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8e26b2ff-2886-43f2-86be-bf125db0f238\r\n\r\n","mergeCommitSha":"b51628c4d048111e0f7d1a5137dfd568bc949673","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7821","title":"Update editing state to stack overflow","createdAt":"2023-08-29T21:15:34Z"}
{"state":"Merged","mergedAt":"2023-08-29T22:15:55Z","number":7822,"mergeCommitSha":"84d5dcc586bf833b238a9ee4ee441d90e7762659","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7822","title":"Add stack overflow admin console pages","createdAt":"2023-08-29T21:53:29Z"}
{"state":"Merged","mergedAt":"2023-08-29T23:13:21Z","number":7823,"mergeCommitSha":"93b29796ff33b0412dd5e6077052f5abd18d286d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7823","title":"Show stack overflow integration icon on admin console teams page","createdAt":"2023-08-29T22:29:04Z"}
{"state":"Merged","mergedAt":"2023-08-29T23:34:33Z","number":7824,"mergeCommitSha":"39de316f4b24b10ab03a2ea7d0223e734d8c1e3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7824","title":"Update async processing","createdAt":"2023-08-29T22:57:57Z"}
{"state":"Merged","mergedAt":"2023-08-30T00:43:47Z","number":7825,"mergeCommitSha":"b4d1b505a6c15f85bf09f3f123ebfefa987d3ae5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7825","title":"Post slack messaging when channels cannot be referenced","createdAt":"2023-08-30T00:15:09Z"}
{"state":"Merged","mergedAt":"2023-08-30T03:47:41Z","number":7826,"body":"This is a bit of an odd gap in Slate -- any element that has leaf-node children allows formatting on those children.  This means that the code block would allow formatting.  I've asked the Slate devs if there's an elegant way of fixing this but for now we're taking the manual approach.\r\n\r\nThe fixes here are:\r\n* Add an Element trait that defines if the block allows inline content.  Right now only Paragraph and Quote will allow this\r\n* The node normalization method clears formatting on nodes that don't support formatting.  This ensures that no formatting is applied, and handles cases like selecting the whole document and bolding it\r\n* The `*stuff*` formatting keyboard shortcuts no longer format on these blocks\r\n\r\nThis doesn't fix inline links in code blocks, I'll fix that later\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/4454e1d6-424b-436d-98e4-11dfe3675ff2\r\n\r\n","mergeCommitSha":"44fd792eb0533cbd530d7437b17a24d40386c47d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7826","title":"Fix bug where inline content was allowed on all editor blocks","createdAt":"2023-08-30T01:03:29Z"}
{"state":"Merged","mergedAt":"2023-08-30T05:13:46Z","number":7827,"mergeCommitSha":"7b629dede041540838ac6548c1e22d1b0f8e49b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7827","title":"Chart should start from the beginning of this year not 2022","createdAt":"2023-08-30T03:22:17Z"}
{"state":"Merged","mergedAt":"2023-08-30T04:27:39Z","number":7828,"mergeCommitSha":"81a7729f7bd3594802de9154600ac90794a845b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7828","title":"Optimize async","createdAt":"2023-08-30T04:27:16Z"}
{"state":"Merged","mergedAt":"2023-09-01T16:30:19Z","number":7829,"body":"Improved list keyboard handling:\r\n\r\n* Starting a line with `- ` or `* ` starts an unordered list\r\n* Starting a line with `1. ` starts an ordered list\r\n* Tab now works as expected: increases list depth, only when already in a list\r\n* Shift+tab now works as expected: decreases list depth\r\n* Delete potentially clears list\r\n* Enter potentially clears the last list item\r\n* Allow formatted text in list items\r\n\r\nMost of the logic here was pulled from the prezly list library.  Unfortunately their library is very opinionated, in that you have to use their keyboard shortcuts to include their logic.\r\n\r\nNote that I did some significant refactoring of keyboard handling -- I made a base interface for all keyboard handlers, which can then be implemented and composed in a variety of ways.","mergeCommitSha":"4c3b2c2a5e1d8a8392fe305dba38c4a94248ddff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7829","title":"MessageEditor improved list keyboard handling","createdAt":"2023-08-30T05:43:21Z"}
{"state":"Merged","mergedAt":"2022-04-05T13:57:04Z","number":783,"mergeCommitSha":"56a7b9308c657d8ef99a608d7386c0403d73ed1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/783","title":"Fix incorrect resource arn for postgres in prod for assetservice","createdAt":"2022-04-05T13:46:29Z"}
{"state":"Merged","mergedAt":"2023-08-30T18:18:54Z","number":7831,"body":"Where a slight chance that old onboarding flow would appear if config flags weren't set properly.","mergeCommitSha":"4f9bb78cff67548e11b75475f64a3caedcc0df7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7831","title":"Remove onboarding entry","createdAt":"2023-08-30T18:08:58Z"}
{"state":"Merged","mergedAt":"2023-08-30T21:40:24Z","number":7832,"mergeCommitSha":"6b68f8f0a0674e53c03a25da7e367245726f0221","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7832","title":"Store next url during confluence ingestion to allow killed jobs to continue","createdAt":"2023-08-30T18:20:29Z"}
{"state":"Merged","mergedAt":"2023-08-30T18:59:46Z","number":7833,"mergeCommitSha":"f04da0b6c14e3f5b487c0f94be764639570ed70c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7833","title":"UNB-1436 Fix invite header alignment","createdAt":"2023-08-30T18:42:53Z"}
{"state":"Merged","mergedAt":"2023-08-30T21:08:11Z","number":7834,"mergeCommitSha":"11b0e5694b9c5a0112d3dc0e432746e81fe6541c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7834","title":"Update patchStackOverflowTeam operation to support updating PAT","createdAt":"2023-08-30T20:11:59Z"}
{"state":"Merged","mergedAt":"2023-08-30T20:46:27Z","number":7835,"body":"Old versions of Slate used to explicitly disable focus outlines in the editable area, the newest version of Slate removed this (https://github.com/ianstormtaylor/slate/issues/5146) -- this introduces a bug where in the IDEs there is a weird focus outline within the editor.  This removes it.","mergeCommitSha":"4f27fe7585c57ab2df24ebf92a9c8424d69fd35a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7835","title":"Fix slate outline regression","createdAt":"2023-08-30T20:32:32Z"}
{"state":"Merged","mergedAt":"2023-09-05T18:02:46Z","number":7836,"body":"Add support for inline code, superscript, subscript, and strikethrough elements in the message editor.\r\n\r\nThe only one of these that can actually be triggered by the user is code.  Surrounding text in backticks wraps it as a code element.","mergeCommitSha":"4b62afd4ec789b09118bef11b34c76a440e68c9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7836","title":"Add inline code element support to message editor","createdAt":"2023-08-30T20:41:20Z"}
{"state":"Merged","mergedAt":"2023-08-30T21:03:20Z","number":7837,"mergeCommitSha":"8ea39df58453ebaec07638bdf7172b29609b85db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7837","title":"Fix pr summarization prompts","createdAt":"2023-08-30T20:59:31Z"}
{"state":"Merged","mergedAt":"2023-08-30T22:25:09Z","number":7838,"body":"Group hub insights by date\r\n\r\n![CleanShot 2023-08-30 at 14 34 20@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/591a0949-5563-41a7-b5a0-73becebf2e23)\r\n","mergeCommitSha":"8216ace40cbc0ec145d851aec39c8ce42db996ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7838","title":"Group hub insights by date","createdAt":"2023-08-30T21:34:09Z"}
{"state":"Merged","mergedAt":"2023-08-31T18:38:14Z","number":7839,"body":"<img width=\"1496\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/f33d0a2b-4bfa-4ac2-ac04-84d673c46352\">\r\n<img width=\"1499\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/2ed5e20f-1e03-4489-b0a4-c4b16c856883\">\r\n","mergeCommitSha":"1a9518f3aec5fb6f3493efd40a2c0e526772cf62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7839","title":"Center align onboarding connect views and add footer","createdAt":"2023-08-30T21:54:56Z"}
{"state":"Merged","mergedAt":"2022-04-05T15:58:38Z","number":784,"body":"This class was floating around everywhere","mergeCommitSha":"ebe82131c1d2aced4f3f31e1c7c385f191514693","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/784","title":"Consolidate TestUtils class to a single module","createdAt":"2022-04-05T15:42:56Z"}
{"state":"Merged","mergedAt":"2023-08-30T22:47:47Z","number":7840,"mergeCommitSha":"5d54ea90f35b82cecd8c46248dc88617c2d53750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7840","title":"Update pull request summary ingestion input payload","createdAt":"2023-08-30T22:37:49Z"}
{"state":"Merged","mergedAt":"2023-08-30T23:37:41Z","number":7841,"mergeCommitSha":"044b0c2a687bafb2a8f02b4b15c893795a5c4f43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7841","title":"Clear Confluence next urls when ingestion is done","createdAt":"2023-08-30T23:14:28Z"}
{"state":"Merged","mergedAt":"2023-08-31T00:27:43Z","number":7842,"mergeCommitSha":"81cc66481c652d9799b14ecd3a49d9798c4514a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7842","title":"Fix poetry install","createdAt":"2023-08-31T00:25:35Z"}
{"state":"Merged","mergedAt":"2023-08-31T00:55:34Z","number":7843,"mergeCommitSha":"c1579bc9644c8857d1bb226faba342bedffde94b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7843","title":"Add dockerignore","createdAt":"2023-08-31T00:55:28Z"}
{"state":"Merged","mergedAt":"2023-08-31T01:18:04Z","number":7844,"body":"- Large runner test\r\n- Update\r\n","mergeCommitSha":"0a7e814ee8925b5b620134a720bba0a9906c26c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7844","title":"LargerRunners","createdAt":"2023-08-31T01:05:33Z"}
{"state":"Merged","mergedAt":"2023-08-31T01:36:52Z","number":7845,"mergeCommitSha":"593111e07898baf49f01a885d0b2fc723d510212","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7845","title":"Add transformers dependency","createdAt":"2023-08-31T01:36:46Z"}
{"state":"Merged","mergedAt":"2023-08-31T04:29:08Z","number":7847,"body":"- Up scm service count\r\n- Add auto scaling\r\n","mergeCommitSha":"6fb17e9fa6d0653516048a19c7152322b7ea98fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7847","title":"IncreaseConsumerscount","createdAt":"2023-08-31T04:28:38Z"}
{"state":"Merged","mergedAt":"2023-08-31T04:32:35Z","number":7848,"mergeCommitSha":"e371569f73cb83c38bacc838fa86375c985b65af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7848","title":"Increase consumer count","createdAt":"2023-08-31T04:32:29Z"}
{"state":"Merged","mergedAt":"2023-08-31T05:50:27Z","number":7849,"body":"- Fix withTimeouts\r\n- Update\r\n- update\r\n","mergeCommitSha":"bccab81c5a283db64049b455a01195452cba6648","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7849","title":"FixTimeouts","createdAt":"2023-08-31T05:29:22Z"}
{"state":"Merged","mergedAt":"2022-04-05T19:37:03Z","number":785,"body":"This pr does the following:\r\n1. Add a new custom gradle task (buildMatrix) that derives all the build tasks that we can run in project.\r\n2. We output that result as a json, and we consume that json to create a matrix of tasks to run. These matrix of tasks effectively parallelize across ALL our build targets, and auto-adds service jars to an artifact that we deploy.\r\n\r\ni.e.\r\nNEW: (7M)\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/2098256577\r\n\r\nOLD: (12M) \r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/2098035260\r\n\r\nMore work has to be done to share stuff between jobs, but this is a start.","mergeCommitSha":"3527fc4331dc829cf9732596ab3a4b86220463b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/785","title":"Improve Deployment Times","createdAt":"2022-04-05T17:21:07Z"}
{"state":"Merged","mergedAt":"2023-08-31T06:26:29Z","number":7851,"mergeCommitSha":"1c0a1047c6d477947aaf2234085d813cfab38cef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7851","title":"Move stuff around","createdAt":"2023-08-31T06:07:04Z"}
{"state":"Merged","mergedAt":"2023-08-31T17:16:53Z","number":7852,"mergeCommitSha":"5cff6a9d3cd65e07c0c7aac643e1c9929635926f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7852","title":"Reverse boolean flags to clear next URLs","createdAt":"2023-08-31T17:03:28Z"}
{"state":"Merged","mergedAt":"2023-08-31T17:48:12Z","number":7853,"mergeCommitSha":"d167f60a28a158534c657e5b3819ecc745cc6e33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7853","title":"Missing error","createdAt":"2023-08-31T17:27:15Z"}
{"state":"Merged","mergedAt":"2023-09-01T04:50:33Z","number":7854,"mergeCommitSha":"d8b550a34e7c7af9bbdb72ecec29cf5c23860c7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7854","title":"Drop no longer used columns","createdAt":"2023-08-31T19:00:21Z"}
{"state":"Merged","mergedAt":"2023-08-31T20:41:55Z","number":7855,"mergeCommitSha":"b1e898ec75218d2d47a6da1e5af85cf8e16269cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7855","title":"Add pull request ingestion piepline","createdAt":"2023-08-31T20:21:26Z"}
{"state":"Merged","mergedAt":"2023-08-31T20:49:02Z","number":7856,"mergeCommitSha":"ce004f7c2f87fea50f8c0077d76ca9f636064385","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7856","title":"adding two new pieces of important information to the @unblocked corpus","createdAt":"2023-08-31T20:34:42Z"}
{"state":"Merged","mergedAt":"2023-08-31T22:36:27Z","number":7858,"mergeCommitSha":"4df56e53eb892dd423b773712e6b68677d31534d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7858","title":"Fix pr ingestion","createdAt":"2023-08-31T22:14:19Z"}
{"state":"Merged","mergedAt":"2023-09-05T19:54:40Z","number":7859,"body":"Running into an issue with the latest Sonoma Build `14.0 Beta (23A5337a)` where spaces weren't being registered within the search input. There were also issues with occasional focus issues.\r\n\r\n1. DummyTextField was causing an additional text caret to render when text field was closed. Not sure why this exists...?\r\n2. Originally had a wrapper Button to enable search focused state on click. Behaviour changed in this beta where clicking \"space\" while having the text input focused actually triggered the Button's click handler instead of inputting a space. Removed the wrapper button and added a tap gesture to mimic the existing behaviour.","mergeCommitSha":"b3cfe74d28d3d5ba31ca0911f25d019a5595fe67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7859","title":"Potential SOnoma Fix","createdAt":"2023-08-31T22:37:03Z"}
{"state":"Merged","mergedAt":"2022-04-05T18:17:19Z","number":786,"body":"- Added Slack alarms for main branch builds and deploys of service and infra workflows\r\n- Build failure alarms are sent to infra-alarms-dev\r\n- Service/infra  deploy failure alarms are sent to each corresponding env","mergeCommitSha":"78f4f750f86cb971b2c8bd8b8310c86622429463","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/786","title":"Add slack notification for GitHub actions (WIP)","createdAt":"2022-04-05T17:52:31Z"}
{"state":"Merged","mergedAt":"2023-08-31T22:56:00Z","number":7860,"mergeCommitSha":"863e9d82bd5bf5a660dab77357583c50a6167b05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7860","title":"Fix setting last strated at for code ingestion","createdAt":"2023-08-31T22:55:40Z"}
{"state":"Merged","mergedAt":"2023-09-01T18:29:03Z","number":7861,"mergeCommitSha":"47766eed16ee05e78d704241ef79ca60eb9fb8ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7861","title":"Landing page refresh","createdAt":"2023-08-31T23:58:56Z"}
{"state":"Merged","mergedAt":"2023-09-01T00:47:09Z","number":7862,"mergeCommitSha":"bcfb4d2e59437ef18d2bf4e21392ffd607f5b4fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7862","title":"Add continuations","createdAt":"2023-09-01T00:19:48Z"}
{"state":"Merged","mergedAt":"2023-09-01T01:56:18Z","number":7863,"mergeCommitSha":"94a9799a7669900f7cdb99bcd46f559eedc949f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7863","title":"Fix badges","createdAt":"2023-09-01T01:18:32Z"}
{"state":"Merged","mergedAt":"2023-09-01T02:03:26Z","number":7864,"mergeCommitSha":"803c1ff994f568b10924efd9d2cd061d9fa4549b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7864","title":"Clean up code","createdAt":"2023-09-01T01:58:41Z"}
{"state":"Closed","mergedAt":null,"number":7865,"body":"- Clean up code\r\n- update init scripts\r\n- disable\r\n- Increase max results\r\n- Increase concurrentLimit\r\n","mergeCommitSha":"0983e878c650052a3f97edd76447a7649dd38ef6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7865","title":"IncreaseMaxResults","createdAt":"2023-09-01T02:10:06Z"}
{"state":"Merged","mergedAt":"2023-09-01T02:24:14Z","number":7866,"body":"- Increase max results\r\n- Increase concurrentLimit\r\n","mergeCommitSha":"3d138b1d276741792ba395fbf283ad745f7a9b52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7866","title":"INcreaseMaxResults2","createdAt":"2023-09-01T02:11:07Z"}
{"state":"Merged","mergedAt":"2023-09-01T02:44:42Z","number":7867,"mergeCommitSha":"b28a9ff5a36bd4dd5b58f431fed2d5a65387007d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7867","title":"Improve repos admin page to show number of prs","createdAt":"2023-09-01T02:37:36Z"}
{"state":"Merged","mergedAt":"2023-09-01T02:47:46Z","number":7869,"mergeCommitSha":"c6e7b89df74496b8c9c26f1482c0e44cfbc4e97a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7869","title":"Increase number of workers for glue job","createdAt":"2023-09-01T02:47:41Z"}
{"state":"Merged","mergedAt":"2022-04-05T22:09:12Z","number":787,"body":"No longer using these","mergeCommitSha":"470adeca57ce30d581d4600f0042d84253c1a803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/787","title":"Remove node dependencies","createdAt":"2022-04-05T18:33:07Z"}
{"state":"Merged","mergedAt":"2023-09-07T17:50:17Z","number":7870,"body":"1. Adds a first class `Recommended` topic source.\r\n2. Adds create approved + recommend topics actions.\r\n3. Various other editing fixes.\r\n\r\nPutting into a table soon -- I hope.","mergeCommitSha":"087e7e27ba55ad2b9fcb86341ccd67798c9a2ea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7870","title":"Minimal improvement to existing topics curation page","createdAt":"2023-09-01T04:20:39Z"}
{"state":"Merged","mergedAt":"2023-09-01T06:27:59Z","number":7871,"mergeCommitSha":"07952f7ae42b9b8ea193f262bce6d854de878019","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7871","title":"Add site ingestion","createdAt":"2023-09-01T05:51:56Z"}
{"state":"Merged","mergedAt":"2023-09-01T17:41:59Z","number":7872,"mergeCommitSha":"2d6fcd0adc274896fac9a26f2f32a63544c700bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7872","title":"IMprove team settings for slack stuff","createdAt":"2023-09-01T17:41:48Z"}
{"state":"Merged","mergedAt":"2023-09-05T21:00:15Z","number":7873,"mergeCommitSha":"7bc779ace19d3d4f239e4f3a5bac447ab7cd94bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7873","title":"Remove sites from SiteIngestionConfig","createdAt":"2023-09-01T17:57:24Z"}
{"state":"Merged","mergedAt":"2023-09-01T20:14:37Z","number":7874,"mergeCommitSha":"3ec29f5cff6dbfd0b9f77340d31c106464d6b5d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7874","title":"Add property on DocumentReference to show a globe icon for web pages","createdAt":"2023-09-01T18:25:10Z"}
{"state":"Merged","mergedAt":"2023-09-01T20:40:48Z","number":7875,"mergeCommitSha":"7cc7e326ec53a1a907c930e16c4e68b95ce12236","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7875","title":"Fix app direct git parsing","createdAt":"2023-09-01T20:26:52Z"}
{"state":"Merged","mergedAt":"2023-09-01T21:03:19Z","number":7876,"body":"\r\n<img width=\"308\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/3cc975ae-d5fd-4f36-83d3-621f94bb5705\">\r\n\r\n","mergeCommitSha":"96c4cb9f8d36e7f55db767d85ba35493f7f36e16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7876","title":"Render provider icons based on property","createdAt":"2023-09-01T20:40:02Z"}
{"state":"Merged","mergedAt":"2023-09-01T21:54:26Z","number":7877,"mergeCommitSha":"4d2d97df4bd6fe090adfe8c27206c377c8b49098","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7877","title":"Remove unnecessary elements from html","createdAt":"2023-09-01T21:14:32Z"}
{"state":"Merged","mergedAt":"2023-09-01T21:17:35Z","number":7878,"mergeCommitSha":"df8a5c8f43666a77066bcd9e96b32bd8e6152fd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7878","title":"Only do pr summary for merged","createdAt":"2023-09-01T21:17:30Z"}
{"state":"Merged","mergedAt":"2023-09-01T21:29:07Z","number":7879,"mergeCommitSha":"6fcb786052e754921cd307ca21df61ddb3757b5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7879","title":"Fix admin console typos","createdAt":"2023-09-01T21:28:57Z"}
{"state":"Merged","mergedAt":"2022-04-06T22:52:54Z","number":788,"body":"<img width=\"1003\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/161841203-124801ed-0d71-4d45-8944-11c89ab7228a.png\">\r\n","mergeCommitSha":"11065718a1dadd2b96ef4ad52376537c02377ac8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/788","title":"Fix code block line range","createdAt":"2022-04-05T20:14:32Z"}
{"state":"Merged","mergedAt":"2023-09-01T21:40:02Z","number":7880,"mergeCommitSha":"bcdd6c006e0ddd78bed7fb074e7b0e09e1adcb7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7880","title":"Add throttling exceptino handlers for sagemaker","createdAt":"2023-09-01T21:39:40Z"}
{"state":"Merged","mergedAt":"2023-09-01T21:42:46Z","number":7881,"mergeCommitSha":"3933ae55b5947a6389ebe89d2d7610d7ffc57562","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7881","title":"Auto scale prod to 30 instances","createdAt":"2023-09-01T21:41:51Z"}
{"state":"Merged","mergedAt":"2023-09-01T23:38:31Z","number":7882,"mergeCommitSha":"e43a1440b09841fe08456ff448ca945cb1a2bfc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7882","title":"Autofocus edit input","createdAt":"2023-09-01T22:01:58Z"}
{"state":"Merged","mergedAt":"2023-09-01T22:13:06Z","number":7883,"mergeCommitSha":"a5e808681f29ba827a3a50bd3ddc5a1f36df4dcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7883","title":"Update tests","createdAt":"2023-09-01T22:12:43Z"}
{"state":"Merged","mergedAt":"2023-09-01T22:29:06Z","number":7884,"mergeCommitSha":"ee8239df1d104a888d845cd88d082dfd4809b9bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7884","title":"Reduce db timeouts","createdAt":"2023-09-01T22:15:26Z"}
{"state":"Merged","mergedAt":"2023-09-01T22:51:46Z","number":7885,"mergeCommitSha":"62e3a6acfff1f9e2b8657de51da115fb87c32935","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7885","title":"Move away from custom map prompt","createdAt":"2023-09-01T22:51:40Z"}
{"state":"Merged","mergedAt":"2023-09-05T20:22:06Z","number":7886,"body":"Add two new APIs to modify a topic within a thread.","mergeCommitSha":"4da81e7be533275c87c4dd644c9cb1a4517b137b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7886","title":"Add API to modify topic insight model","createdAt":"2023-09-01T23:16:08Z"}
{"state":"Merged","mergedAt":"2023-09-01T23:44:06Z","number":7887,"mergeCommitSha":"8d6eba785b98b08acdadcfe812c1c9f4481893b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7887","title":"Fix titles for web pages","createdAt":"2023-09-01T23:35:34Z"}
{"state":"Merged","mergedAt":"2023-09-04T04:38:31Z","number":7888,"body":"* Fixed a bug where steps would occasionally get skipped when re-viewing\r\n* Fix content sizing bugs in Chrome","mergeCommitSha":"f920fc1bb4649d0c0c32bbaaea790a978a0919b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7888","title":"Fix some landing page bugs","createdAt":"2023-09-01T23:37:26Z"}
{"state":"Merged","mergedAt":"2023-09-20T16:51:18Z","number":7889,"body":"<img width=\"1491\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/1dc38369-79aa-4f44-9107-86506efb4988\">\r\n<img width=\"1503\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/d9022cc3-cd64-4b39-bf2c-6a068afa92a9\">\r\n","mergeCommitSha":"77315dc5365e0fda8613416525b39903546963d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7889","title":"Repo connector should use search list setting","createdAt":"2023-09-01T23:38:35Z"}
{"state":"Merged","mergedAt":"2022-04-05T20:57:12Z","number":789,"mergeCommitSha":"b1dfc2b70d57c0635e095b7af28fa4f619e1efe3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/789","title":"Test ZGC","createdAt":"2022-04-05T20:57:00Z"}
{"state":"Merged","mergedAt":"2023-09-05T15:35:38Z","number":7890,"body":"- updated h265 videos from client assets repo\r\n- removed shadows from assets themselves, and added it to the video via CSS (so cool!)\r\n","mergeCommitSha":"1a47acc7892d54d0d9a3c681eff0558258716320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7890","title":"updated videos and styling","createdAt":"2023-09-02T00:02:24Z"}
{"state":"Merged","mergedAt":"2023-09-02T02:07:46Z","number":7891,"mergeCommitSha":"689a91c1bc7a14d8bd3de13df6740d473e8bdf5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7891","title":"Fix token calculations for pprompts","createdAt":"2023-09-02T02:03:30Z"}
{"state":"Merged","mergedAt":"2023-09-05T17:34:10Z","number":7892,"mergeCommitSha":"f60b870f78df4b38e2a583aa5c7ceb71f4ebbc5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7892","title":"Clean up pr summarization token calculations","createdAt":"2023-09-02T03:13:43Z"}
{"state":"Merged","mergedAt":"2023-09-05T17:08:48Z","number":7893,"body":"- Change hero movie to an auto playback movie\r\n- Add codecs to hero movie\r\n- Always mute auto-play movies, as our movies have no sound, and un-muted movies never auto-play in Chrome","mergeCommitSha":"9928bea827038b9bdc9ee0415887e0e8beb5af00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7893","title":"Fix hero movie playback in Chrome","createdAt":"2023-09-05T03:25:38Z"}
{"state":"Merged","mergedAt":"2023-09-05T05:31:59Z","number":7894,"body":"I'm guessing its because we don't embed team member expertise","mergeCommitSha":"39cb5f2aa9bd920b45f29039319d888003191894","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7894","title":"Remove \"Who are the experts of the X component?\" while we figure out why it doesn't work","createdAt":"2023-09-05T04:22:51Z"}
{"state":"Merged","mergedAt":"2023-09-05T16:13:36Z","number":7895,"mergeCommitSha":"d9e5d08ecda4feea0d061f5a2bd30e6f6572416d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7895","title":"Migration to backfill the NULL providerInstallationValid rows to FALSE for all teams","createdAt":"2023-09-05T15:49:43Z"}
{"state":"Merged","mergedAt":"2023-09-05T18:21:30Z","number":7896,"body":"When IDE client repos do not match any installed repos on the server, then return a hardcoded error message.\r\n\r\n\r\n## Before\r\n\r\ntitle: `Connect Repositories to Unblocked`\r\ndescription: `Connect the following repositories to see insights from  and code conversations for this project.`\r\n\r\n\r\n## After\r\n\r\ntitle: `Connect Repositories to Unblocked`\r\ndescription: `No valid repositories found.`","mergeCommitSha":"9424f1e77d51bcefa6465af69744b01b2e16d6aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7896","title":"Hardcoded error message when findInstallationsAndRepos cannot find any repos","createdAt":"2023-09-05T17:11:56Z"}
{"state":"Merged","mergedAt":"2023-09-05T17:26:44Z","number":7897,"mergeCommitSha":"19282138925c5a8521b584acb4f9b07d6b4808e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7897","title":"Allow multi-codec landing section steps","createdAt":"2023-09-05T17:17:36Z"}
{"state":"Merged","mergedAt":"2023-09-05T19:27:21Z","number":7898,"body":"This was bothering me a lot. I added a webhook for a channel called #customer-topic-update in slack. ","mergeCommitSha":"78fbf466241130def070b3c2ef067a5fbb10d004","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7898","title":"Move topic update notifications to #customer-topic-update","createdAt":"2023-09-05T17:37:01Z"}
{"state":"Merged","mergedAt":"2023-09-05T17:39:23Z","number":7899,"mergeCommitSha":"b9a181104d2a4eea446d2b46a5a494bb775868f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7899","title":"Enable pr summarization for prod","createdAt":"2023-09-05T17:39:18Z"}
{"state":"Closed","mergedAt":null,"number":79,"body":"Writing down some research and assumptions I'm going to be taking before jumping into the auth code.","mergeCommitSha":"6307d8b7b302036a2896aa5f0106a5468c1b065d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/79","title":"Web JWT Doc","createdAt":"2022-01-19T18:49:26Z"}
{"state":"Merged","mergedAt":"2022-04-05T22:17:38Z","number":790,"body":"This has the side effect of preventing requests for repos in other teams. There is still a wider effort that needs to happen to to lock down resources to team (https://www.notion.so/nextchaptersoftware/Team-Resource-Authorization-0126454fb1c74d9ab2df9ff12935e243#c897bdce92bc478da86b7523cc461659) but at least this covers threads for now.","mergeCommitSha":"6254a47a83cd87c96939b287107dac45a89dc5fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/790","title":"Filter threads by team when repo IDs are not supplied","createdAt":"2022-04-05T20:58:38Z"}
{"state":"Merged","mergedAt":"2023-09-05T18:21:14Z","number":7900,"mergeCommitSha":"c78e9db92074fd0508c6277eefcc2a597c3aa0d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7900","title":"Fix the \"stale\" pipeline indicator for PRs/source ingestion","createdAt":"2023-09-05T17:43:47Z"}
{"state":"Merged","mergedAt":"2023-09-05T18:22:42Z","number":7901,"body":"Fixes https://chapter2global.slack.com/archives/C045VGYML95/p1693927003734189","mergeCommitSha":"7e6f7f559724d4424725e7526bdddf14280d520c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7901","title":"Update inference example for sample question","createdAt":"2023-09-05T17:53:06Z"}
{"state":"Merged","mergedAt":"2023-09-05T18:27:11Z","number":7902,"body":"Per this post, it looks like ScreenCaptureKit output references need to be strongly held or they get dealloc'd before the underlying media engine sends the frames. Very weird.\r\n\r\nhttps://developer.apple.com/forums/thread/733077","mergeCommitSha":"efdf29151a9ce4cec7242cc1ff12edb808b6a285","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7902","title":"Fix missing screen capture on Sonoma","createdAt":"2023-09-05T18:12:24Z"}
{"state":"Merged","mergedAt":"2023-09-05T21:09:44Z","number":7903,"body":"So that we know if a question came from onboarding, Slack, or just Unblocked (dashboard/hub).","mergeCommitSha":"0eedf37f47bf6a8de1e376b6e90dd1a75f8380ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7903","title":"Show the source of an new unblocked question or vote","createdAt":"2023-09-05T18:17:17Z"}
{"state":"Merged","mergedAt":"2023-09-05T19:25:06Z","number":7904,"mergeCommitSha":"9efe45a7886aa5f04e3768a0663fd0ea1f3ffaf6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7904","title":"Ask unblocked question","createdAt":"2023-09-05T18:36:09Z"}
{"state":"Merged","mergedAt":"2023-09-05T20:40:32Z","number":7905,"mergeCommitSha":"95cb4f363061de3b49d3fd43b1651cf81a14344b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7905","title":"Update font sizing","createdAt":"2023-09-05T18:48:27Z"}
{"state":"Merged","mergedAt":"2023-09-05T21:02:54Z","number":7906,"body":"The `disableCommentSignatures` setting is ON by default now.","mergeCommitSha":"128007d3c935856528ffc71ab33f94b5f5cda98b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7906","title":"Disable comment signatures by default for new teams","createdAt":"2023-09-05T19:34:18Z"}
{"state":"Merged","mergedAt":"2023-09-05T22:50:36Z","number":7907,"mergeCommitSha":"7f7a974f2c3b2264988309d6188d5135b3aa6e22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7907","title":"Prevent the file_traversal.py file from being recognised as a generated file","createdAt":"2023-09-05T19:35:36Z"}
{"state":"Merged","mergedAt":"2023-09-05T21:15:49Z","number":7908,"body":"* Upgrade stylelint to 15 -- the project has by default removed most of the rules that have to do with code style (ie, those that conflict with prettier), and is now focusing on correctness/standardization\r\n* Remove stylelint prettier integration -- no need for it now\r\n* Remove `empty-line` rules, as they conflict with prettier and are obnoxious\r\n\r\nNote that the end result is that the IDE auto-formatting gives the correct format that the linter checks against, which is what we want.","mergeCommitSha":"d9aa4d35239eb84c2342ffa0b07468a05e434148","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7908","title":"Fix css/scss linting rules","createdAt":"2023-09-05T19:59:17Z"}
{"state":"Merged","mergedAt":"2023-09-05T22:11:40Z","number":7909,"body":"We gave 5 seconds to scan for all git repos in the workspace folders -- increasing to 15s to see if this fixes the customer's problem.  Will look into a better solution after we've verified this fixes it.\r\n\r\nThe theory is that on startup there is a lot of work happening, and in a large repo with flat folder structures this may take awhile.","mergeCommitSha":"3eed46cc3bfaae5b58eb4ff3c2ca7348cbd0b591","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7909","title":"Increase IDE git repo scanning timeout","createdAt":"2023-09-05T20:50:23Z"}
{"state":"Merged","mergedAt":"2022-04-05T21:06:09Z","number":791,"mergeCommitSha":"f5200e32f6fa0a79e664a8d61c421099c3003aea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/791","title":"Fixworkflow","createdAt":"2022-04-05T20:59:09Z"}
{"state":"Merged","mergedAt":"2023-09-05T23:47:29Z","number":7910,"mergeCommitSha":"289332e44e56b82934ef130d30d2ae3693c1f567","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7910","title":"Create threads for all Jira issues, not just those that reference pull requests","createdAt":"2023-09-05T21:07:24Z"}
{"state":"Closed","mergedAt":null,"number":7911,"body":"This reverts commit 0eedf37f47bf6a8de1e376b6e90dd1a75f8380ea.","mergeCommitSha":"ef5e83d3f6cacc6ae5bdf8838954e4e8db89e629","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7911","title":"Revert \"Show the source of an new unblocked question or vote (#7903)\"","createdAt":"2023-09-05T21:11:06Z"}
{"state":"Merged","mergedAt":"2023-09-05T22:06:29Z","number":7912,"mergeCommitSha":"b3b1d5d74a0ad0bc1d8bc3a64a285685f227f9bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7912","title":"Invert boolean on TeamsPage","createdAt":"2023-09-05T21:21:14Z"}
{"state":"Merged","mergedAt":"2023-09-05T22:07:34Z","number":7913,"body":"After updating ThreadInsightModels for a thread, update thread modifiedAt for push event.","mergeCommitSha":"6230290cdbaed88e880f5728470da635c20e4cc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7913","title":"Update thread modifiedAt on topic change","createdAt":"2023-09-05T21:52:31Z"}
{"state":"Merged","mergedAt":"2023-09-05T23:56:55Z","number":7914,"body":"Ability to remove topic from thread. Adding create next.","mergeCommitSha":"918f5d6a09122d8562e3e2939d9635198d6cdf6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7914","title":"Delete topic from thread","createdAt":"2023-09-05T22:40:21Z"}
{"state":"Merged","mergedAt":"2023-09-05T23:42:51Z","number":7915,"mergeCommitSha":"f2011a04ce49c2dd0e38a781251c92f851a11e9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7915","title":"Reduce db timeouts","createdAt":"2023-09-05T23:42:32Z"}
{"state":"Merged","mergedAt":"2023-09-06T00:01:24Z","number":7916,"mergeCommitSha":"5f5e30a7c70d3345524c2c7cebba8ecfd05183ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7916","title":"Expose authenticated repo clone url","createdAt":"2023-09-05T23:50:51Z"}
{"state":"Merged","mergedAt":"2023-09-06T01:45:17Z","number":7917,"mergeCommitSha":"3cdca32096cfa760de586ff0ef044e9482d6e9a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7917","title":"Add customerIntegrationsWebhookUrl","createdAt":"2023-09-06T00:12:01Z"}
{"state":"Merged","mergedAt":"2023-09-06T00:14:53Z","number":7918,"mergeCommitSha":"c8d8b541a39146c13c407aa29b2d7962f0059ef2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7918","title":"Fix legend formatting","createdAt":"2023-09-06T00:14:11Z"}
{"state":"Merged","mergedAt":"2023-09-06T01:47:50Z","number":7919,"mergeCommitSha":"53b2d5dc786fe0fd81d1b5b91e20633a85c91152","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7919","title":"Add email","createdAt":"2023-09-06T01:20:09Z"}
{"state":"Merged","mergedAt":"2022-04-05T21:12:49Z","number":792,"body":"- Scaled out both dev and prod clusters\r\n- Changed Kubernetes deployments to bring up 100% of new pods during deployment (kinda blue/green -ish)\r\n- Right sized a few of the services (cpu/mem)\r\n- Regenerated helm charts","mergeCommitSha":"e2d753e3ff678a7f5e2c51e878c81d31147887c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/792","title":"faster kube deployments","createdAt":"2022-04-05T21:01:34Z"}
{"state":"Merged","mergedAt":"2023-09-06T21:13:22Z","number":7920,"body":"This is a component that optionally renders React content based on the viewport size.  The props are modelled after how we use breakpoints in CSS:\r\n\r\n```\r\n<Breakpoints md={ <content>} lg={ <content> } >\r\n  <default/smallest content>\r\n</Breakpoints>\r\n```\r\n\r\nThe idea being that the children content is rendered, and as progressively larger breakpoints are hit, we render that content instead.  This uses the same mechanism of `react-responsive` but is customized to how we tend to use breakpoints.","mergeCommitSha":"30234364050ef7bba35aa007c566e78c7e5a825c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7920","title":"Add breakpoint component","createdAt":"2023-09-06T03:51:58Z"}
{"state":"Merged","mergedAt":"2023-09-06T17:58:19Z","number":7921,"mergeCommitSha":"6fd9fa042c1ba8d5037bf86c7f3dc0372e13b2d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7921","title":"Notify in #customer-integrations when an integration is added or removed","createdAt":"2023-09-06T05:20:56Z"}
{"state":"Merged","mergedAt":"2023-09-06T08:34:17Z","number":7922,"body":"- Separated Reader and Writer instance configs\r\n- Modified build config to allow for different sizes for Reader and Writer instances\r\n- Resized prod Writer instance from xlarge to 2xlarge\r\n- Resized prod Reader from r6g.xlarge to burstable t4g.large instance to save cost. \r\n- Updated configs for both Dev and prod with latest changes. \r\nAll these changes have been deployed to both Dev and Prod. ","mergeCommitSha":"b1f5505995dbf205749666aa3a7bfcd3b39a052c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7922","title":"Split rds reader writer configs","createdAt":"2023-09-06T08:24:49Z"}
{"state":"Merged","mergedAt":"2023-09-06T17:02:29Z","number":7923,"body":"In my last update I reduced the size of reader instance in prod. That seems to have caused some instability. \r\n\r\nIncrease the size of the reader instance","mergeCommitSha":"d03c2ba088548c9c907f6ebd936ad9ace6b29b4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7923","title":"Resize reader instance prod","createdAt":"2023-09-06T17:01:04Z"}
{"state":"Merged","mergedAt":"2023-09-06T18:31:38Z","number":7924,"body":"Increase pusher CPU to delay throttling ","mergeCommitSha":"8725fac66dcd1f8c5043eabe54dbd6f6329bd473","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7924","title":"add more CPU to delay throttling for pusher","createdAt":"2023-09-06T18:15:20Z"}
{"state":"Merged","mergedAt":"2023-09-06T18:50:36Z","number":7925,"body":"Client currently only displays the first 4 topics on an insight. \r\nMove this filtering logic to backend which will only limit 4 auto-generated topics. User selected topics will always be returned.","mergeCommitSha":"d02a02076534d5d0aa5b16fd9630ce0fecff7afe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7925","title":"Add topic limiting to backend","createdAt":"2023-09-06T18:23:25Z"}
{"state":"Merged","mergedAt":"2023-09-15T20:27:26Z","number":7928,"body":"Add ability to add topics to threads.\r\nRemoves limit to number of topics displayed on client.\r\n\r\nRefactored TeamMemberDropdown into a SearchDropdown.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/4cc492d9-72bc-46f5-9a40-5b3640053378","mergeCommitSha":"4d1c2b541c588aabc64cbe37f9bf8cfe265c7e63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7928","title":"Add / remove topic to thread","createdAt":"2023-09-06T19:59:40Z"}
{"state":"Merged","mergedAt":"2023-09-07T23:17:10Z","number":7929,"body":"The tests are just there to ensure we catch anything that causes changes to how the vectors are generated. We ultimately need higher level tests that check for retrieval ranking from a known set of documents and a known query.\r\n\r\nIn the next PR I'll replace BERT with Splade in the actual embeddings implementation","mergeCommitSha":"f2aebdd30bd0a7bddc98bc5d20ce96ea2f9b8b01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7929","title":"Add Splade Vectorizer","createdAt":"2023-09-06T20:10:51Z"}
{"state":"Merged","mergedAt":"2022-04-05T22:21:52Z","number":793,"body":"Pusher service is crashlooping https://chapter2global.slack.com/archives/C031MKRPZSQ/p1649195205232259\r\nIt's due to memory pressure. Increased its memory allocation back to the original value. ","mergeCommitSha":"ccc319382e088dcf40bf8c1ad5b34cd3ec5f76d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/793","title":"scaling pusher resources back up","createdAt":"2022-04-05T21:50:56Z"}
{"state":"Merged","mergedAt":"2023-09-07T06:36:41Z","number":7930,"body":"AWS is deprecating support for Node 14. We are moving all Lambda edge functions to Node 18.","mergeCommitSha":"4f2970a19407f2a6f018fe90caf244ca68e27c14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7930","title":"Update lambda nodejs runtime from 14 to 18","createdAt":"2023-09-06T20:21:01Z"}
{"state":"Merged","mergedAt":"2023-09-06T20:40:50Z","number":7931,"body":"PR Ingest Pipeline was a little confusing haha","mergeCommitSha":"020c647c10c3d3e35b11a5c818ffb6ef906d02a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7931","title":"Rename to PR Summarization Pipeline","createdAt":"2023-09-06T20:23:27Z"}
{"state":"Merged","mergedAt":"2023-09-06T21:09:20Z","number":7932,"mergeCommitSha":"d15f541bff270673b9c9370e4cd74ff33339e93e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7932","title":"Ingest Unblocked docs site for the NCS team","createdAt":"2023-09-06T20:48:51Z"}
{"state":"Merged","mergedAt":"2023-09-12T19:54:36Z","number":7933,"body":"Issue where closing borderless window was causing the entire app to be hidden (including hub icon)\r\n\r\nNarrowed it down to `hidesOnDeactivate`. Not sure what's different now in comparison to 13.0 but this is causing the issue.\r\nRemoved it entirely and now manually closing popover when clicking outside of hub app.","mergeCommitSha":"84be775f87ba1612cd8e6e3545bcd4a146cdf957","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7933","title":"Potential hub fix for closing popover","createdAt":"2023-09-06T20:50:43Z"}
{"state":"Merged","mergedAt":"2023-09-06T22:16:12Z","number":7934,"body":"* Factor most of the existing (large-dashboard) landing page out into `StepsLarge`\r\n* Add new `StepsMobile`, representing the mobile steps UI\r\n* `InfoSection` now does little more then switching the steps based on breakpoint\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/4f4f606f-7042-46ed-83c4-0b1419e7e622\r\n\r\n","mergeCommitSha":"db35cd5816cf46f6298394e57fbe158c5de541e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7934","title":"Mobile landing page","createdAt":"2023-09-06T21:19:09Z"}
{"state":"Merged","mergedAt":"2023-09-06T23:44:08Z","number":7935,"body":"Add API to update pull request topic","mergeCommitSha":"9ca501eb27f355c696885e96057891e58b7178e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7935","title":"Update pull request topic","createdAt":"2023-09-06T21:22:31Z"}
{"state":"Merged","mergedAt":"2023-09-06T22:01:09Z","number":7936,"mergeCommitSha":"abde1ecd0f67c78f0717c3cfeab8f0b805178858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7936","title":"Fix body for customer integration slack notifications","createdAt":"2023-09-06T21:36:43Z"}
{"state":"Merged","mergedAt":"2023-09-06T23:08:10Z","number":7937,"body":"<img width=\"764\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/3806658/10962eb8-b733-4886-b5ef-7ec90ee114e6\">\r\n","mergeCommitSha":"7b99080e6b2680d93edac71167b36aa87e623e8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7937","title":"Add slack references","createdAt":"2023-09-06T21:51:53Z"}
{"state":"Merged","mergedAt":"2023-09-06T23:45:26Z","number":7938,"mergeCommitSha":"5f23c2164af49dcad1d4a123c72a43e6e729fb4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7938","title":"Embed the plain text of a web page instead of html","createdAt":"2023-09-06T23:23:52Z"}
{"state":"Merged","mergedAt":"2023-09-07T17:38:05Z","number":7939,"body":"This component handles the `md` and `lg` breakpoints.\r\n\r\n<img width=\"1195\" alt=\"Screenshot 2023-09-06 at 4 59 44 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/d113b888-6277-409a-b68c-28080b29b39a\">\r\n<img width=\"896\" alt=\"Screenshot 2023-09-06 at 4 59 51 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/364990dd-7285-4d13-aaf6-e35b7f20beb5\">\r\n","mergeCommitSha":"29349e8810e8197a274451edb8911bff3ae55647","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7939","title":"Landing page medium-size layout","createdAt":"2023-09-07T00:00:37Z"}
{"state":"Closed","mergedAt":null,"number":794,"mergeCommitSha":"820b335be6bfbb9f7fefffb20357f4c80c9c7d10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/794","title":"Attempt faster build","createdAt":"2022-04-05T22:04:18Z"}
{"state":"Merged","mergedAt":"2023-09-07T01:02:25Z","number":7940,"body":"- Add source\r\n- Add python makefile etc.\r\n- Test jfrog artifactory\r\n","mergeCommitSha":"43492341d92bac895d45d002663a5ba5c1a15c37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7940","title":"MoveToPythonLibraries","createdAt":"2023-09-07T00:10:49Z"}
{"state":"Merged","mergedAt":"2023-09-07T00:45:28Z","number":7941,"mergeCommitSha":"19add995bf5ee04f737101c31b0ef25cbac7bd46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7941","title":"Fix build","createdAt":"2023-09-07T00:33:15Z"}
{"state":"Merged","mergedAt":"2023-09-07T01:24:50Z","number":7943,"body":"Ability to search for documents from a team based on a search query.\r\n\r\nOptions for tuning the mix of semantic (dense vector) and lexical (sparse vector) retrieval results.\r\n\r\nAlso fixes input placeholder rendering.\r\n\r\n<img width=\"800\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/0fd0a29e-3778-40fe-9a9e-3ee28a19ec68\">\r\n","mergeCommitSha":"320df74eb42ab8c159468acce3979b8340500267","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7943","title":"Introduce document retrieval tool","createdAt":"2023-09-07T01:11:41Z"}
{"state":"Merged","mergedAt":"2023-09-07T01:18:02Z","number":7944,"body":"- Fix prod\r\n- Fix lint\r\n","mergeCommitSha":"db3889a4801e407cafd1dc5cd07309650e683996","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7944","title":"FixProd","createdAt":"2023-09-07T01:17:55Z"}
{"state":"Merged","mergedAt":"2023-09-07T17:48:47Z","number":7945,"mergeCommitSha":"1db14804e3664e890de675c9203743c157c957c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7945","title":"Remove unused BM25 sparse embedder","createdAt":"2023-09-07T17:02:22Z"}
{"state":"Merged","mergedAt":"2023-09-07T18:04:30Z","number":7946,"body":"TeamStore.listTeams() only returns non-deleted teams.","mergeCommitSha":"1dbb1e3495a40b797c70671fa5420ec47cef258e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7946","title":"Check that a team hasnt been soft deleted before ingesting data","createdAt":"2023-09-07T17:44:00Z"}
{"state":"Merged","mergedAt":"2023-09-07T18:22:36Z","number":7947,"mergeCommitSha":"dc3b0efd6a6db2ac6b5c84477b50625487326613","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7947","title":"Update doc site url for ingestion","createdAt":"2023-09-07T17:49:57Z"}
{"state":"Merged","mergedAt":"2023-09-07T18:59:09Z","number":7948,"body":"This reverts commit d02a02076534d5d0aa5b16fd9630ce0fecff7afe.","mergeCommitSha":"c7c514d90e3bec78a85f98e933d6e75a5209e8b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7948","title":"Revert \"Add topic limiting to backend (#7925)\"","createdAt":"2023-09-07T18:36:42Z"}
{"state":"Merged","mergedAt":"2023-09-07T19:00:17Z","number":7949,"mergeCommitSha":"76840b1b494841350e517a76f7c37b8af5a1a9bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7949","title":"update poetry docs","createdAt":"2023-09-07T18:59:56Z"}
{"state":"Merged","mergedAt":"2022-04-06T22:47:10Z","number":795,"mergeCommitSha":"4766c1f09baf817820546d1350d1dee8f50d29bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/795","title":"Change addListener() to subscribe() to unsubscribe","createdAt":"2022-04-05T22:20:11Z"}
{"state":"Merged","mergedAt":"2023-09-07T19:40:17Z","number":7950,"body":"Redirect to custom error html doc on 403 status code for docs site.","mergeCommitSha":"32afad520ab4da5adb9bb502b4e830f367a30ec5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7950","title":"Add http redirect for 403 errors","createdAt":"2023-09-07T19:18:32Z"}
{"state":"Closed","mergedAt":null,"number":7951,"body":"- update poetry docs\r\n- Add jfrog build args\r\n","mergeCommitSha":"7fc98d82a97f1a5f9e0003238d06b4b3670f9e94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7951","title":"UpdateStuff","createdAt":"2023-09-07T19:19:51Z"}
{"state":"Merged","mergedAt":"2023-09-07T19:23:34Z","number":7952,"mergeCommitSha":"a48f3435a4142fe5dc65f95375c8db0895dd3c4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7952","title":"Add jfrog build args to pipelines","createdAt":"2023-09-07T19:23:22Z"}
{"state":"Merged","mergedAt":"2023-09-11T19:50:08Z","number":7953,"body":"Unrevert topic limiting.\r\n\r\nUpdated so by default, we do not limit.\r\nLimiting is only done on thread and PR decorators.","mergeCommitSha":"664daa24521e53b82b5a88c86740007822455cb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7953","title":"Updated limit topic","createdAt":"2023-09-07T19:48:47Z"}
{"state":"Closed","mergedAt":null,"number":7954,"body":"Adding last active date so hub can determine which IDE to open threads / grab context from.\r\n\r\nThis is a push model where IDE will notify the hub whenever its active state is modified. \r\nWe could do a pull model (aka hub asks IDE when it was last active) but requires a more complicated IPC stream.\r\n\r\nThis current push approach could be a little noisy","mergeCommitSha":"a738fdddc1288a269f8c1388e921fda4a3296ad3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7954","title":"Add Last Active Date","createdAt":"2023-09-07T20:17:46Z"}
{"state":"Merged","mergedAt":"2023-09-07T22:09:01Z","number":7955,"mergeCommitSha":"7038eecbf6843999eeafcb966e771333e7057a31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7955","title":"changes the ordering on the topics page","createdAt":"2023-09-07T21:48:43Z"}
{"state":"Merged","mergedAt":"2023-09-07T22:42:05Z","number":7956,"mergeCommitSha":"8956547c5b09a190cb8af60451b532ece4ef1461","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7956","title":"First stab at admin auth impersonate","createdAt":"2023-09-07T22:21:25Z"}
{"state":"Merged","mergedAt":"2023-09-07T23:00:22Z","number":7957,"mergeCommitSha":"aa44f234bed5c674ed6303a5f39004702fa28d6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7957","title":"Fix document page links","createdAt":"2023-09-07T22:37:27Z"}
{"state":"Merged","mergedAt":"2023-09-07T23:25:14Z","number":7959,"mergeCommitSha":"cecaa9bd996614a187bf6947b6a6c91a9f17e3d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7959","title":"Impersonate as user for slack notifications","createdAt":"2023-09-07T23:23:43Z"}
{"state":"Merged","mergedAt":"2022-04-06T04:36:03Z","number":796,"body":"This is almost guaranteed to break when real clients are hitting it, but we'll have to just go through that pain...","mergeCommitSha":"95780999ec38bb8e7192adcacc8f5c7b9d9307d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/796","title":"Integrate agora client","createdAt":"2022-04-05T22:26:29Z"}
{"state":"Merged","mergedAt":"2023-09-08T00:37:51Z","number":7960,"mergeCommitSha":"74aac67b01626d3921d89a56265a3b48fffe47ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7960","title":"Show document content. Fix doc links. Embed thread titles.","createdAt":"2023-09-08T00:17:44Z"}
{"state":"Merged","mergedAt":"2023-09-08T01:01:47Z","number":7961,"mergeCommitSha":"ecda2f8ee0ade81e8fe535510eece0625e8aa414","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7961","title":"Update docs","createdAt":"2023-09-08T00:59:55Z"}
{"state":"Merged","mergedAt":"2023-09-08T01:38:21Z","number":7962,"mergeCommitSha":"77af2359505089d7b9480b68eae382bb4c471fac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7962","title":"Reduce pr ingestion","createdAt":"2023-09-08T01:34:49Z"}
{"state":"Merged","mergedAt":"2023-09-08T19:02:14Z","number":7963,"mergeCommitSha":"807104f69adbb3cf35f01e7b8902d771b3a11907","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7963","title":"Topic curation fixup","createdAt":"2023-09-08T01:34:50Z"}
{"state":"Merged","mergedAt":"2023-09-08T12:00:54Z","number":7964,"body":"I searched for \"OpenAI\" in #ask-unblocked and #ask-unblocked-internal and the response was 100% consistent with \"as an AI developed by OpenAI\", so a simple string replace seemed most appropriate here. Turns out it makes perfect sense (in english at least) to just delete the string. \r\n\r\nOnce we need to tailor to non-english audiences we'll need to do this type of thing with a transformer.","mergeCommitSha":"7e66a6820cea930520cff92a01160be656238600","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7964","title":"Add developed by OpenAI response sanitizer","createdAt":"2023-09-08T03:49:08Z"}
{"state":"Merged","mergedAt":"2023-09-08T09:23:25Z","number":7965,"body":"- Add new certificates to be used by a CloudFront distribution dedicated to redirects \r\n- Added the CloudFront distribution called redirects.{dev, prod}.getunblocked.com with wild card certs for the domains \r\n- Added lambda function to perform the redirect on View Request lambda edge events\r\n\r\nTo redirect any subdomain simply make a CNAME record pointing at `redirect.prod.getunblocked.com` or `redirect.dev.getunblocked.com` depending on the environment. \r\n\r\n\r\nThe redirect function extracts the subdomain and use it as a path from domain apex. e.g https://dashboard.getunblocked.com becomes https://getunblocked.com/dashboard","mergeCommitSha":"bd1ef22c1a46d3a34eff7db0f15c16d11e13c6ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7965","title":"Subdomain redirect cloudfront","createdAt":"2023-09-08T05:54:27Z"}
{"state":"Merged","mergedAt":"2023-09-08T14:21:18Z","number":7966,"mergeCommitSha":"d1f2d6c2dbca1e5297d074fa776e62d64c819b99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7966","title":"Fix pydantic implicit dependency that breaks the code partitioner","createdAt":"2023-09-08T12:42:37Z"}
{"state":"Merged","mergedAt":"2023-09-08T15:19:05Z","number":7967,"mergeCommitSha":"5ccaab1c1ac6474efca7054277e159358721bb21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7967","title":"Standardize intervals for ingestion pieplines","createdAt":"2023-09-08T15:05:54Z"}
{"state":"Merged","mergedAt":"2023-09-08T17:55:27Z","number":7968,"body":"- Clean up readmes for jfrog\r\n- Update\r\n","mergeCommitSha":"ff86a6cf71e36c6029918b736f35aa268a187e95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7968","title":"ReadMeCleanup","createdAt":"2023-09-08T17:54:43Z"}
{"state":"Merged","mergedAt":"2023-09-08T18:37:24Z","number":7969,"body":"To debug https://app.logz.io/#/goto/d19b1f860f82ce654ece886b6744c12b?switchToAccountId=411850","mergeCommitSha":"510ed97483ac2066deb49ae36c1fd7fef17dde70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7969","title":"Add logging context for ThreadService.updateLastMessageCreatedAt","createdAt":"2023-09-08T18:21:11Z"}
{"state":"Merged","mergedAt":"2022-04-06T20:51:40Z","number":797,"body":"This is a work in progress, so these models and apis will ikely change.\r\n\r\nThis should hopefully unblock Mahdi a bit as the apis are now sparsely implemented.\r\n","mergeCommitSha":"0524595cfd962d247e31eb917022303c7bd0fbca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/797","title":"Add basic asset models and apis","createdAt":"2022-04-05T23:57:06Z"}
{"state":"Merged","mergedAt":"2023-09-08T19:18:43Z","number":7970,"body":"Testing out a longer auth token expiry window in Dev.","mergeCommitSha":"c8eec1fd330c61ec6235ad1962c686e238845440","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7970","title":"Update dev auth token expiry","createdAt":"2023-09-08T18:50:19Z"}
{"state":"Merged","mergedAt":"2023-09-08T19:13:21Z","number":7971,"mergeCommitSha":"372496ea3ada2bf76c412c9b78ab32fd5697a5d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7971","title":"Remove thread incognito","createdAt":"2023-09-08T19:12:31Z"}
{"state":"Closed","mergedAt":null,"number":7972,"mergeCommitSha":"5e0ef6e1b370732cc4dbecccafd3b15c7531a43a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7972","title":"Use dot-product Pinecone index for PROD","createdAt":"2023-09-08T19:43:34Z"}
{"state":"Closed","mergedAt":null,"number":7973,"mergeCommitSha":"22d425a810a6136aa7c8f1729f970dc9797ea45d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7973","title":"Ignore me","createdAt":"2023-09-08T19:51:11Z"}
{"state":"Merged","mergedAt":"2023-09-11T18:16:38Z","number":7974,"body":"Added new preferences in the hub that determines *where* QA results are opened in:\r\n![CleanShot 2023-09-08 at 13 03 23@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/28c71b98-3d79-4855-9ac1-564f9e77f57b)\r\n\r\nQA from the hub will now take context from the IDE. Contexts will only be included if the IDEs were active < 10 minutes ago (we can / should adjust this)\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/8251de32-3b41-4cd3-aed0-9a689b0235fe\r\n\r\nEDIT: New version does not have separate settings.\r\n\r\n","mergeCommitSha":"705c60184ea5a92088ef48b4406b9b107a81a1e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7974","title":"Hub + IDE QA Integration","createdAt":"2023-09-08T20:24:08Z"}
{"state":"Merged","mergedAt":"2023-09-08T20:39:51Z","number":7975,"mergeCommitSha":"df4d0599636d7addac3935f44570e52ea59cd4df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7975","title":"Decrease activemq gc","createdAt":"2023-09-08T20:39:42Z"}
{"state":"Merged","mergedAt":"2023-09-08T21:03:56Z","number":7976,"mergeCommitSha":"8a2cb925942d623c8531f0978a86ae004a7d9938","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7976","title":"Log error when ingesting Jira issue","createdAt":"2023-09-08T20:49:32Z"}
{"state":"Merged","mergedAt":"2023-09-08T22:50:51Z","number":7977,"body":"Inject JWT Token Subject & client ID into header for rate limiting purposes\r\n\r\nVSCode (TS Clients):\r\n![CleanShot 2023-09-08 at 14 20 19@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/40de73a0-8d66-4a38-b350-8ff91384f361)\r\n\r\nHub:\r\n![CleanShot 2023-09-08 at 14 15 22@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/1e6fe531-938b-40a3-a22b-2a9a00e1b56e)\r\n","mergeCommitSha":"37ec72bfe3a9cea401b01e8c4d10251bc3b1e736","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7977","title":" Inject subject + Client ID into header","createdAt":"2023-09-08T21:23:05Z"}
{"state":"Merged","mergedAt":"2023-09-08T21:25:47Z","number":7978,"mergeCommitSha":"7fae1a998c8e524fb2808d88f83d31ff39bbb628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7978","title":"Cleanup Stomp connections","createdAt":"2023-09-08T21:23:51Z"}
{"state":"Merged","mergedAt":"2023-09-08T21:56:36Z","number":7979,"body":"* Remove `mine` API usage in IDEs, as we have no UIs displaying it\r\n* Remove PullRequestStore (we weren't using it) and associated API usage","mergeCommitSha":"ccefcfe5f7f71b25b9f223dd9c3e25322b9cb19a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7979","title":"Remove mine thread usage in IDEs","createdAt":"2023-09-08T21:42:56Z"}
{"state":"Merged","mergedAt":"2022-04-06T16:44:09Z","number":798,"mergeCommitSha":"a350b65a74780857b900e248c9ec56be175c9036","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/798","title":"Refactor clients projects","createdAt":"2022-04-06T00:06:43Z"}
{"state":"Merged","mergedAt":"2023-09-10T13:50:12Z","number":7980,"body":"I want to get this out so that Dennis can take a look at the content and order of investors. \r\n\r\nWhat's left to do:\r\n- Responsive layouts\r\n- Refactor some of the reusable sections (ie: hero)\r\n- Refactor the type styles\r\n- Hook up hiring email button","mergeCommitSha":"119527a838ecd92bf3382317fafe5a1523d51a41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7980","title":"First cut of the About Us page","createdAt":"2023-09-08T21:50:05Z"}
{"state":"Merged","mergedAt":"2023-09-12T00:28:36Z","number":7981,"body":"### Changes\r\n- supports multiple Pinecone indexes: dense and hybrid\r\n- always generate dense+sparse vectors for document embedding, if there is a hybrid index\r\n- continue to generate dense-only vectors for document embedding, if there is a dense index\r\n- add index type to ML template, defaulting to DENSE\r\n- removes team setting generateSparseVectors; this now only depends on the presence of the index, and the template index property\r\n- document query tool can toggle between dense and hybrid indexes.\r\n\r\n### TODO\r\n- [ ] post merge: DROP TeamSettingsModel generateSparseVectors column in admin web","mergeCommitSha":"651c8986c14a9830dff2325228418f71b515314d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7981","title":"Support dual Pinecone indexes","createdAt":"2023-09-08T22:08:24Z"}
{"state":"Merged","mergedAt":"2023-09-08T22:10:33Z","number":7982,"mergeCommitSha":"9b2876363ef24b6097377e0ccc99e0987750eaa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7982","title":"Update secetes","createdAt":"2023-09-08T22:10:27Z"}
{"state":"Merged","mergedAt":"2023-09-08T22:38:57Z","number":7983,"body":"In cases where we are *not* opening up a PR model but a thread that's part of a PR (yes... it's different), we don't actually get the scmURL.\r\n\r\nWe do get the latestMessage from the threads though which may hold a reference to the prCommentUrl which is actually a more appropriate url to use.","mergeCommitSha":"4cad3d10745ca86542efbce8ca7282565ebe36d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7983","title":"Use latestMessageUrl as backup to SCMUrl in Hub","createdAt":"2023-09-08T22:25:57Z"}
{"state":"Merged","mergedAt":"2023-09-08T22:37:29Z","number":7984,"mergeCommitSha":"37430f01c379a96c077f460d13f06f9cd6f3f187","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7984","title":"Disable code unil i fix it","createdAt":"2023-09-08T22:37:11Z"}
{"state":"Merged","mergedAt":"2023-09-08T22:53:35Z","number":7985,"body":"Added the ability to change the primary email for a person to one of their verified emails from any of their connected SCM identities.\r\n\r\nPerson email is a unique table constraint, so the update will fail if there is another existing person that has the same email.\r\n\r\nThis should be a feature that the user can perform themselves from Hub / web.\r\n\r\n<img width=\"726\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/77e488fb-42b2-4479-b766-7022eaf4a7a9\">\r\n","mergeCommitSha":"2f2f43e3989caaffcd631b55fb89159335f41c43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7985","title":"Ability to update person email","createdAt":"2023-09-08T22:48:57Z"}
{"state":"Merged","mergedAt":"2023-09-11T05:27:12Z","number":7986,"body":"Fixes this error https://app.logz.io/#/goto/8d090b0d74cf2c9f411387fc24610e67?switchToAccountId=411850","mergeCommitSha":"d26228f1840c25107e2c6067765da8e06a364823","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7986","title":"Skip ingesting Jira issues with no content","createdAt":"2023-09-08T23:19:55Z"}
{"state":"Merged","mergedAt":"2023-09-12T18:13:21Z","number":7987,"body":"Both TypeScript and Swift code now handle 429 requests with a special case -- if the `Retry-After` header is supplied, the retry will only happen after the specified time.\r\n\r\nNote that the TS ChannelPoller code has a special case for this -- it handles 429 directly, instead of relying on the BaseAPI code, so that old channel lists aren't re-queried.  The hub only ever subscribes to a single channel (mine) so this isn't necessary there.","mergeCommitSha":"1d2e0419d1d74f451e664747529624e2243037a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7987","title":"Clients handle 429 responses correctly","createdAt":"2023-09-08T23:47:44Z"}
{"state":"Merged","mergedAt":"2023-09-09T00:53:40Z","number":7988,"mergeCommitSha":"be5c6ecb85e7ee8846d5afa9432a497e0010bcaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7988","title":"Update conf to include new headers","createdAt":"2023-09-09T00:46:26Z"}
{"state":"Merged","mergedAt":"2023-09-09T00:58:03Z","number":7989,"mergeCommitSha":"5c2ddd175ccb7ef17cc6830abe24f592eb630a94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7989","title":"Improve logging for ktors","createdAt":"2023-09-09T00:56:22Z"}
{"state":"Merged","mergedAt":"2022-04-06T02:51:55Z","number":799,"mergeCommitSha":"2e2a99ad2177a794f920c7ba6fac18ab29bcbaa0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/799","title":"Skeleton for source mark integration in VSCode","createdAt":"2022-04-06T00:30:00Z"}
{"state":"Merged","mergedAt":"2023-09-09T01:31:41Z","number":7990,"body":"- Add ktor server parsing\r\n- Ktor server parsing\r\n","mergeCommitSha":"69fb870c45e347379019469550940601c0f36138","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7990","title":"AddKtorServerTracingV2","createdAt":"2023-09-09T01:25:14Z"}
{"state":"Merged","mergedAt":"2023-09-09T02:26:43Z","number":7991,"body":"- Revert \"AddKtorServerTracingV2 (#7990)\"\r\n- Revert \"Disable code unil i fix it (#7984)\"\r\n","mergeCommitSha":"9d572bba20d01973c4aebe78e9a4b27f2f0e2488","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7991","title":"RevertChanges","createdAt":"2023-09-09T02:26:21Z"}
{"state":"Merged","mergedAt":"2023-09-09T07:12:17Z","number":7992,"body":"Team delete is very slow, and these slow cascading queries have locked up the DB in the past.\n\nTo prevent DB downtime, and allow for faster team deletes, we index the team foreign key on all referencing tables. This is especially important for referencing tables with very large numbers of rows, since without the foreign key index a large table scan is required.\n\nFor more information on this approach, see:\n - https://dba.stackexchange.com/questions/37034/very-slow-delete-in-postgresql-workaround\n - https://stackoverflow.com/questions/71719249/delete-cascade-in-postgresql-extremely-slow","mergeCommitSha":"9f34a1a646714da983affc97f6d79480f7743826","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7992","title":"Faciliate fast cascade deletes by indexing team foreign keys","createdAt":"2023-09-09T05:54:34Z"}
{"state":"Merged","mergedAt":"2023-09-11T05:02:02Z","number":7993,"body":"This will also upsert the experts after the topic has been mapped to insights","mergeCommitSha":"ad344bb0da88ec33976d25936a25d567a1c249d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7993","title":"Add ability to trigger insight mapping for a topic","createdAt":"2023-09-11T04:43:23Z"}
{"state":"Merged","mergedAt":"2023-09-11T21:42:16Z","number":7994,"mergeCommitSha":"9e36235017e2d08c973e7bb36778961b6474a489","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7994","title":"Show last synced at timestamp for jira site","createdAt":"2023-09-11T16:14:26Z"}
{"state":"Merged","mergedAt":"2023-09-11T21:17:53Z","number":7995,"mergeCommitSha":"0ee832445b4fed9e1915f69775d227728be1788d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7995","title":"Use /unreads instead of /threads/mine for Hub notifications polling","createdAt":"2023-09-11T17:59:43Z"}
{"state":"Merged","mergedAt":"2023-09-11T19:11:23Z","number":7996,"body":"Changes the default from OpenAI to Recommended.","mergeCommitSha":"5a991b73697db6d8cc5610980cbdb9427bf817b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7996","title":"use recommended as the default topic source","createdAt":"2023-09-11T18:14:19Z"}
{"state":"Merged","mergedAt":"2023-09-11T19:23:55Z","number":7997,"body":"- Added support for defining RateLimit based WAF2 rules\r\n- Modified build config to support configuring multiple rules\r\n- Added support for providing custom responses when rate limiting (might be required for pusher)\r\n- Modifed Dev config to add a set of rules in Count mode.\r\n\r\nCurrently the rules are in Count mode (permissive). Once we make sure they don't break traffic in Dev, I'll publish the same rules to prod.\r\n\r\nWe are using the metrics generated by these rules to figure out a suitable limit before going to blocking mode.\r\n\r\n\r\nPS: I am going to be refactoring all these codes into more organized modules. Things are growing too complex and these files are getting longer every day. I'll deal with that separately. ","mergeCommitSha":"d4c724ceb4f9d6159713e6353b3bf40cb61c7233","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7997","title":"Adding rate limiting WAF2 rules (dev)","createdAt":"2023-09-11T18:21:50Z"}
{"state":"Open","mergedAt":null,"number":7998,"mergeCommitSha":"699eca3f150d7edbe01f79f65889078bbbcc9ef9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7998","title":"Update authTokenExpiry for global","createdAt":"2023-09-11T18:35:43Z"}
{"state":"Merged","mergedAt":"2023-09-11T20:48:37Z","number":7999,"body":"minor cleanup for consistency","mergeCommitSha":"f20c570db2efbe0dd6a6e9e204838bcdddb434c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7999","title":"Refactor thread update logic","createdAt":"2023-09-11T18:46:34Z"}
{"state":"Merged","mergedAt":"2021-12-14T18:39:51Z","number":8,"mergeCommitSha":"9f7a9e6e000e16c0f22c1805d4bc1fb96f81794c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8","title":"Update baselines of main","createdAt":"2021-12-14T18:39:31Z"}
{"state":"Merged","mergedAt":"2022-01-19T19:17:38Z","number":80,"body":"This driver has some addiitonal layers to deal with Aurora clusters (we decided to use aurora clusters for other reasons Mahdi can talk about)\r\n\r\nIt is in preview mode, but the mysql driver for aurora is also the recommended jdbc driver for users using mysql aurora.\r\nWe will eventually move to this once this matures if we don't take this now.\r\n\r\nhttps://awslabs.github.io/aws-postgresql-jdbc/\r\n\r\nAnd then we have this: :)\r\nhttps://github.com/awslabs/aws-postgresql-jdbc#aws-iam-database-authentication\r\n","mergeCommitSha":"ab8e744dc7a2792b625dcc4d236378974bbd60a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/80","title":"Move to aws postgres jdbc driver","createdAt":"2022-01-19T19:00:54Z"}
{"state":"Merged","mergedAt":"2022-04-06T02:40:09Z","number":800,"mergeCommitSha":"534a50e3233f537d284e6eb0913512368fefc08c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/800","title":"update","createdAt":"2022-04-06T02:16:02Z"}
{"state":"Merged","mergedAt":"2023-09-11T19:07:51Z","number":8000,"mergeCommitSha":"cf4f496ad3a778328a7aed41019f385b5b041673","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8000","title":"Add reference truncation","createdAt":"2023-09-11T18:47:47Z"}
{"state":"Merged","mergedAt":"2023-09-11T19:53:55Z","number":8001,"body":"I broke CDK deployments. This is a quick and dirty fix.","mergeCommitSha":"840731f0bfddfe4ca736f1bc82d7529ed46bb2cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8001","title":"remove the custom response. We don't need it for now","createdAt":"2023-09-11T19:38:56Z"}
{"state":"Merged","mergedAt":"2023-09-11T20:59:50Z","number":8002,"mergeCommitSha":"fe7f14c86dd5cbc6474badbcca7b645905cd2310","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8002","title":"Ingest https://docs.cribl.io for Cribl","createdAt":"2023-09-11T19:56:11Z"}
{"state":"Merged","mergedAt":"2023-09-11T21:01:29Z","number":8003,"body":"This PR is a followup on https://github.com/NextChapterSoftware/unblocked/pull/7997\r\n\r\nThese changes were deployed to Dev in my last PR. We are still not in enforcing mode and need to watch request rates to get a better idea of our traffic patterns and request frequencies. \r\n\r\nI will be using metrics published by these rules to create a Grafana Dashboard showing request rates for each endpoint. ","mergeCommitSha":"1888fab72bdac8a7611a990647ffb6e4eb261944","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8003","title":"Adding rate limit rules (counting mode) to prod","createdAt":"2023-09-11T20:33:20Z"}
{"state":"Merged","mergedAt":"2023-09-11T21:00:35Z","number":8004,"body":"Adding logs to debug issue where hub sometimes fails to refresh auth.","mergeCommitSha":"6e4d940dc80a70736a8f83f8eea823e5742aed81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8004","title":"Add verbose logs for hub auth","createdAt":"2023-09-11T20:48:10Z"}
{"state":"Merged","mergedAt":"2023-09-11T21:28:42Z","number":8005,"mergeCommitSha":"73885c65d565acf20e03b9cb7fff422a0714464b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8005","title":"Add date to logs","createdAt":"2023-09-11T21:19:32Z"}
{"state":"Merged","mergedAt":"2023-09-11T23:31:55Z","number":8006,"body":"Unfortunately with the way the user feedback is currently grouped, we can't easily add the descriptions, as we simply have a list of user IDs.  With this change, we will provide all feedback as a flattened list, so we can render the feedback inline.","mergeCommitSha":"edc112be04b5945db630c9a462e7ae6b2568c284","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8006","title":"API changes for inline QA feedback","createdAt":"2023-09-11T21:21:07Z"}
{"state":"Merged","mergedAt":"2023-09-11T21:26:57Z","number":8007,"mergeCommitSha":"2a04581a7d8a89f941aacbe4baafe314a1c2df24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8007","title":"move rate limit rules to be evaluated first","createdAt":"2023-09-11T21:25:21Z"}
{"state":"Merged","mergedAt":"2023-09-12T00:06:35Z","number":8008,"mergeCommitSha":"ce46aefcad067779986f448717dcfe94c6f15261","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8008","title":"Source code ingestion supports dual indexes","createdAt":"2023-09-11T22:06:44Z"}
{"state":"Merged","mergedAt":"2023-09-11T22:30:45Z","number":8009,"mergeCommitSha":"6376618bc3e438f8960501604d03e1b6851697b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8009","title":"Update openai client","createdAt":"2023-09-11T22:16:32Z"}
{"state":"Merged","mergedAt":"2022-04-06T04:54:37Z","number":801,"mergeCommitSha":"e1db7ba4a88b99ba6db3af6b4f7bb48277c7611f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/801","title":"Remove Unused  Dependencies","createdAt":"2022-04-06T04:11:08Z"}
{"state":"Merged","mergedAt":"2023-09-11T22:40:26Z","number":8010,"mergeCommitSha":"8a0b599c29aa9ecb6631d9df49430fcff9450d29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8010","title":"Check for duplicate urls when crawling a site","createdAt":"2023-09-11T22:29:12Z"}
{"state":"Merged","mergedAt":"2023-09-11T23:21:40Z","number":8011,"body":"* Remove IDE `/ExplorerInsights` folder -- this is the old UI, which I never removed.  I will rename `/NewExplorerInsights` once this is in.\r\n* Remove the filtered file streams, as we don't use these any more.","mergeCommitSha":"f6b8581ec3a7c6cc16d8c713015993faa090394e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8011","title":"Remove a bunch of dead IDE code","createdAt":"2023-09-11T22:39:12Z"}
{"state":"Merged","mergedAt":"2023-09-11T23:55:27Z","number":8012,"mergeCommitSha":"a6d6aa75a394e96b4b0cf27b5abfce07b23bb60d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8012","title":"Update isQuestionCompletionService to look for new JSON response","createdAt":"2023-09-11T22:57:52Z"}
{"state":"Merged","mergedAt":"2023-09-12T00:30:16Z","number":8013,"mergeCommitSha":"d4dc7143b785aefb1de1e6c14fca66bb4455c9b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8013","title":"Add linear oauth client secrets","createdAt":"2023-09-11T23:52:52Z"}
{"state":"Merged","mergedAt":"2023-09-12T03:05:48Z","number":8014,"mergeCommitSha":"81caf55cb55664d264a3a97b04c94629b28ec2fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8014","title":"Fix admin web","createdAt":"2023-09-12T03:05:12Z"}
{"state":"Merged","mergedAt":"2023-09-12T05:06:37Z","number":8015,"mergeCommitSha":"e3129fdf780088dd783b0b3ad0d4a7734a6cba38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8015","title":"Fix embeddingsIndexType saving in ML templates in admin web","createdAt":"2023-09-12T04:41:26Z"}
{"state":"Merged","mergedAt":"2023-09-12T17:35:30Z","number":8017,"body":"Changed the following:\r\n- Added responsive handling for about page\r\n- Added venture and angel investors\r\n- Factored out responsive typography which also impacts home page\r\n- Updated responsive tabs\r\n- Updated footer with links to about us and docs","mergeCommitSha":"7ecdfe665d50756fcc43212bd9418e9c30928aad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8017","title":"Landing page and about us responsive handling","createdAt":"2023-09-12T17:08:14Z"}
{"state":"Merged","mergedAt":"2023-09-12T18:01:15Z","number":8018,"body":"We're removing the stylelint formatting rules, since prettier does this for us and they don't provide value.  Remove one more.","mergeCommitSha":"c0f44c3f4bf648a09058d556293dca280eec9464","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8018","title":"Disable one more stylelint rule","createdAt":"2023-09-12T17:24:35Z"}
{"state":"Merged","mergedAt":"2023-09-12T19:29:59Z","number":8019,"body":"Update PR View to mark threads as unread if there's a target threadID.","mergeCommitSha":"c75c4b5821a23851fd75522118f41fd2d96ad268","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8019","title":"Update thread unread for targeted threads in PR","createdAt":"2023-09-12T17:52:25Z"}
{"state":"Merged","mergedAt":"2022-04-06T06:39:32Z","number":802,"mergeCommitSha":"bc9437cc326abaf1e0252491df1fa60d29efdf37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/802","title":"Update gradle wrapper","createdAt":"2022-04-06T05:38:37Z"}
{"state":"Merged","mergedAt":"2023-09-12T18:05:47Z","number":8020,"mergeCommitSha":"9da030b8ec28cd691419d3c892aca05df1f3450e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8020","title":"Auto-scale sagemaker endpoint based off request","createdAt":"2023-09-12T17:59:47Z"}
{"state":"Merged","mergedAt":"2023-09-12T18:47:31Z","number":8021,"body":"TODO\r\n\r\n- add webhook handler\r\n- convert all API endpoints to take the token received through Oauth\r\n- add Linear configuration endpoints to allow selecting the teams for ingestion\r\n- fix linear IdentityModel creation to take the linear organization ID instead of the linear team ID as the external ID","mergeCommitSha":"0f0467e6255fbbc873a6d2129b9b6a85e72ffe3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8021","title":"Add Linear OAuth support","createdAt":"2023-09-12T18:17:55Z"}
{"state":"Merged","mergedAt":"2023-09-13T20:36:15Z","number":8022,"body":"Join operator which is similar to Combine but \"Joined\" streams do not trigger a new event when a new value is emitted.","mergeCommitSha":"b45b954cbb050afbaa242df1c7b148cb664a4af3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8022","title":"Joined Operator for Streams","createdAt":"2023-09-12T18:25:22Z"}
{"state":"Merged","mergedAt":"2023-09-12T19:18:40Z","number":8023,"body":"Implementation to come later","mergeCommitSha":"5eabf6b37bd9dd4de81d08b298bf8f844ed269b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8023","title":"Add Linear webhook stub","createdAt":"2023-09-12T18:37:20Z"}
{"state":"Merged","mergedAt":"2023-09-12T21:17:34Z","number":8024,"mergeCommitSha":"d8ad4629d64d4e806a8bed76575e512cc3a6136a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8024","title":"Add test for Linear auth install","createdAt":"2023-09-12T20:21:25Z"}
{"state":"Merged","mergedAt":"2023-09-14T20:06:28Z","number":8025,"body":"For majority of requests, if we receive a 401, refresh the auth token and retry the original request.\r\nExceptions to this are auth refresh & exchange requests.\r\n\r\n","mergeCommitSha":"2d3a631061dab69e74b43f93cedd28ac060ecf14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8025","title":"Refresh Auth on 401 in Hub","createdAt":"2023-09-12T20:48:57Z"}
{"state":"Merged","mergedAt":"2023-09-12T21:34:24Z","number":8026,"mergeCommitSha":"626f3ce2463a659f3a0c082c739b74dfa2202937","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8026","title":"Add Linear integration and installatino factories","createdAt":"2023-09-12T21:19:19Z"}
{"state":"Merged","mergedAt":"2023-09-12T22:07:03Z","number":8027,"body":"Running into \"Cycle Inside\" issue in new xcodes. Issue with build scripts but who knows why...\r\nhttps://developer.apple.com/forums/thread/730974","mergeCommitSha":"44137e8eb1d44a10fa76b523cb8a548ae62f9ea2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8027","title":"Fix hub build issue","createdAt":"2023-09-12T21:45:55Z"}
{"state":"Merged","mergedAt":"2023-09-13T05:56:34Z","number":8028,"body":"- Reduced the number of rules\r\n- Added IP based rule as the global catch all\r\n- Change Pusher rule in Dev to enforcing\r\n- All other rules remain in Counting mode until we have a better idea around suitable limits based on the metrics\r\n- A few weird TS related code changes to get around optional values","mergeCommitSha":"f21e714246af0b1102d225b153f2cb530edb5625","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8028","title":"Reworked Dev and Prod rate limit rules","createdAt":"2023-09-12T21:50:34Z"}
{"state":"Merged","mergedAt":"2023-09-14T20:48:33Z","number":8029,"body":"* Remove `Show Feedback in Thread` checkbox in feedback tooltip\r\n* Render any feedback with text description in an inline list below the response\r\n* Update the client Aggregate models so they store a flattened list of feedbacks, instead of grouped feedbacks\r\n* When the user changes their feedback such that it would delete any previous text feedback they gave, show a warning modal\r\n\r\n<img width=\"1249\" alt=\"Screenshot 2023-09-12 at 2 58 15 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/c50f962e-193f-4f5f-a326-4a65ec799abc\">\r\n<img width=\"1249\" alt=\"Screenshot 2023-09-12 at 2 58 19 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/6ce36596-61e3-4943-890c-631e88340d25\">\r\n<img width=\"1582\" alt=\"Screenshot 2023-09-12 at 3 01 12 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/7e77ebde-2948-415d-b324-7a16a8f97e23\">\r\n/bf63b34f-30ec-4b8e-b0df-681adc513ed8\">\r\n<img width=\"1270\" alt=\"Screenshot 2023-09-12 at 3 02 30 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/e901314a-6177-4781-94ac-9816c6f9269b\">\r\n","mergeCommitSha":"12046d5b7f62b5cc786d1b535f78e2442f8d4fd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8029","title":"Add inline message feedback UI","createdAt":"2023-09-12T22:03:03Z"}
{"state":"Merged","mergedAt":"2022-04-06T07:36:59Z","number":803,"mergeCommitSha":"fad08e5051eb6093d0c3a4c015b193ec63f0e4c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/803","title":"Fix logger","createdAt":"2022-04-06T06:58:28Z"}
{"state":"Merged","mergedAt":"2023-09-12T23:08:53Z","number":8030,"mergeCommitSha":"95bfcd9f35ec7cf54dca5a4a2dd7010f89e43b55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8030","title":"Update ProviderAuthClientFactory with Linear Oauth client","createdAt":"2023-09-12T22:17:44Z"}
{"state":"Merged","mergedAt":"2023-09-12T22:49:22Z","number":8031,"mergeCommitSha":"69315560017e6b5be30de912e17c87ea799b1343","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8031","title":"gpt4 in azure","createdAt":"2023-09-12T22:20:59Z"}
{"state":"Merged","mergedAt":"2023-09-13T17:54:27Z","number":8032,"body":"For the Unblocked logo on the dashboard, always redirect to the index of the current team. \r\n<img width=\"1164\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/75b2d36e-8bc0-49c4-8ea1-6dd4635198ed\">\r\n\r\nAlso block the logo linking if within an onboarding UI (i.e. don't let users navigate away from onboarding pages).","mergeCommitSha":"0ef299332877590632241671ba08314e686a3589","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8032","title":"Unblocked nav link redirect","createdAt":"2023-09-12T22:51:32Z"}
{"state":"Merged","mergedAt":"2023-09-13T00:17:00Z","number":8033,"mergeCommitSha":"cd2aab75f48c58cc6dc6f9e875bf7465c953cc76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8033","title":"Round robin gpt4","createdAt":"2023-09-12T23:51:32Z"}
{"state":"Merged","mergedAt":"2023-09-13T17:54:06Z","number":8034,"body":"* Fix off by one inline code\r\n* Remove leaf styling when there is no editor content\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13431372/e77ccdb3-a86c-4e5c-a0b9-cba99e2ebfd2\r\n\r\n","mergeCommitSha":"1e989632a71348922caa063c7de795f244f59ede","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8034","title":"Message editor bug fixes","createdAt":"2023-09-13T00:15:23Z"}
{"state":"Merged","mergedAt":"2023-09-13T15:40:45Z","number":8035,"mergeCommitSha":"b3f5b5ebf66c940c47b61ff0cdbb1bb60a643551","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8035","title":"Add statsig.com/pricing to ingested pages","createdAt":"2023-09-13T04:19:33Z"}
{"state":"Merged","mergedAt":"2023-09-13T05:07:57Z","number":8036,"mergeCommitSha":"603c23905b0fddff60cd97279aa62fe3a5e78865","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8036","title":"Add GitHubIssue insight type and enable in document retrieval for semantic search","createdAt":"2023-09-13T04:31:34Z"}
{"state":"Merged","mergedAt":"2023-09-13T15:18:19Z","number":8037,"body":"When a threads unread is associated with an archived or deleted thread, then the thread is no longer considered to be unread.\n\nFor the purposes of pusher synchronization the last modified unread for a team member reflects the modified timestamp of any of the user' thread unreads, regardless of archived state.","mergeCommitSha":"3dd5a3935ba5fdaf63a3825793b3d1961c1d9ef6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8037","title":"Thread unread API accounts for archived threads","createdAt":"2023-09-13T04:51:44Z"}
{"state":"Merged","mergedAt":"2023-09-13T06:38:20Z","number":8038,"body":"See for example, but here:\r\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/54b597fc-4e65-4b5c-9d94-0f30e0bf53c6","mergeCommitSha":"3c1ef1f83399589241475e8f537cd2ea0135bfa5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8038","title":"Deduplicate multiple reference partitions of the same document","createdAt":"2023-09-13T06:06:14Z"}
{"state":"Merged","mergedAt":"2023-09-14T15:55:56Z","number":8039,"body":"A team in Linear is analogous to a project in Jira.","mergeCommitSha":"344ed0f2691dd6f80867e34a1a681923c82e023b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8039","title":"Update Linear embeddings to include group ID","createdAt":"2023-09-13T16:28:53Z"}
{"state":"Merged","mergedAt":"2022-04-06T20:48:20Z","number":804,"body":"Render SourceMarks in the gutter.  In this PR the SourceMarks for an editor are pulled when the editor opens.  A follow-on PR will pull updated values once file content changes.\r\n\r\nThe testing story for this stuff is not simple, as it's mostly tying different VSCode APIs together.  I'll do some research on how we can start writing tests for things like this, but I'm not sure how easy it will be.\r\n\r\n<img width=\"1508\" alt=\"Screen Shot 2022-04-06 at 9 50 58 AM\" src=\"https://user-images.githubusercontent.com/2133518/162026711-422d25d5-4892-4479-9b29-b257240abd7c.png\">\r\n\r\n","mergeCommitSha":"8d3a17234a1e81403533cd3196330f56c713633e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/804","title":"Render sourcemarks in the gutter","createdAt":"2022-04-06T16:47:11Z"}
{"state":"Merged","mergedAt":"2023-09-13T17:31:29Z","number":8040,"mergeCommitSha":"7dc9d56f8e5fe45383d80685e0817dafbcdf5b8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8040","title":"Temporarily disable Linear ingestion","createdAt":"2023-09-13T17:01:30Z"}
{"state":"Merged","mergedAt":"2023-09-13T19:25:48Z","number":8041,"body":"Follow on from #8037","mergeCommitSha":"aa1e8baa02cb75f5fbbcc4f74043222627969fff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8041","title":"Touch thread unread record when a thread is archived or restored","createdAt":"2023-09-13T17:05:16Z"}
{"state":"Merged","mergedAt":"2023-09-13T18:25:03Z","number":8042,"body":"We are still working on ensuring 429 codes are being handled correctly. ","mergeCommitSha":"cf12e25d6285cef34baec88047a11a9c7426fa96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8042","title":"Increasing pusher WAF rate limit in Dev","createdAt":"2023-09-13T18:11:19Z"}
{"state":"Merged","mergedAt":"2023-09-13T18:44:28Z","number":8043,"body":"This can result in a lot of logger API spam","mergeCommitSha":"5a8fb00fe1dfb6cf2e01bcfbcb07f67725d8fb1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8043","title":"Don't error log 429s without RetryAfter header","createdAt":"2023-09-13T18:23:58Z"}
{"state":"Merged","mergedAt":"2023-09-13T23:13:49Z","number":8044,"body":"Sonoma introduced new limitations where accessing data from a sandboxed app will now trigger a \"permissions\" dialog.\r\n\r\nWork around this by using our non-sandboxed helper to extract the hub port and copy it over to `tmp` where our non-sandboxed IDE plugins can read.","mergeCommitSha":"bf8000045f7edf6f0fb659db07e5630b3ae91416","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8044","title":"Exchange Hub Port without User Defaults","createdAt":"2023-09-13T18:26:05Z"}
{"state":"Merged","mergedAt":"2023-09-13T19:29:43Z","number":8045,"mergeCommitSha":"9a6eb97c24ea368b455759717d0f57765ef7c47f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8045","title":"Add ability to drop linear for a team","createdAt":"2023-09-13T18:35:32Z"}
{"state":"Merged","mergedAt":"2023-09-13T19:27:21Z","number":8046,"mergeCommitSha":"90b337cbfc64383d8f7fb2afdbd27b7b23fa9564","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8046","title":"Round robin completion service","createdAt":"2023-09-13T19:03:26Z"}
{"state":"Merged","mergedAt":"2023-09-13T19:49:05Z","number":8047,"mergeCommitSha":"664741cc0fa777d2fe831200c46e28147424bef8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8047","title":"Increase instructor number","createdAt":"2023-09-13T19:48:55Z"}
{"state":"Merged","mergedAt":"2023-09-13T20:44:22Z","number":8048,"body":"Don't re-show the VSCode update notification if it's already being shown.  Prevents flickering in the notification when the VSCode window gets focus.","mergeCommitSha":"2862a137e4c564ab8cdd741e8b96091615db34a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8048","title":"Fix flicker in VSCode update notification","createdAt":"2023-09-13T20:17:39Z"}
{"state":"Merged","mergedAt":"2023-09-13T22:02:16Z","number":8049,"body":"Send in blue should no longer be used.\r\nSendgrid has replaced it completely at this point.","mergeCommitSha":"e7a17de0040e9901b2b0a61a917bd97476a93e8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8049","title":"Remove send in blue","createdAt":"2023-09-13T20:35:21Z"}
{"state":"Merged","mergedAt":"2022-04-07T00:09:39Z","number":805,"body":"Setup Repo resolution for VSCode.\r\nRefactors general setup of RepoStore and streams.\r\n\r\nRemoves all mock repo IDs from VSCode + Web.\r\nRemoved all mock teamIDs from VSCode. Web extension next. Web will require some product decisions.\r\n\r\n","mergeCommitSha":"f8c640a14ee5c279d4f01ff4256345e100e61a6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/805","title":"Update RepoStore setup for VSCode","createdAt":"2022-04-06T16:59:54Z"}
{"state":"Merged","mergedAt":"2023-09-15T22:02:17Z","number":8050,"body":"The padding part of the MessageEditor has never worked quite right -- the mouse cursor for this area would be the default arrow cursor, and clicking would not give focus to the editor.  Fix both these issues.","mergeCommitSha":"ae08397cb3dfeac18b4a28c0426a3f5ac81ce0cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8050","title":"Fix editor focus issues","createdAt":"2023-09-13T20:49:56Z"}
{"state":"Merged","mergedAt":"2023-09-13T22:01:22Z","number":8051,"mergeCommitSha":"862e904e8c66bb1ae15703f820ec7e6e6070830c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8051","title":"Linear APIs user oauth token","createdAt":"2023-09-13T21:02:50Z"}
{"state":"Merged","mergedAt":"2023-09-13T21:30:51Z","number":8052,"mergeCommitSha":"586fbf8c7f6b1c7c079b6539ca21a7b1444bd8de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8052","title":"Fix hub notifications","createdAt":"2023-09-13T21:13:08Z"}
{"state":"Merged","mergedAt":"2023-09-13T22:09:47Z","number":8053,"mergeCommitSha":"2c81de7087c999dfccf04e3ab4e14aec2ea8bf19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8053","title":"Fix site scraper","createdAt":"2023-09-13T21:57:07Z"}
{"state":"Merged","mergedAt":"2023-09-13T22:31:18Z","number":8054,"mergeCommitSha":"eb9b6b230f45977bbdc50d944b02a653e1b52d6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8054","title":"Remove LinearRuntimeFixture","createdAt":"2023-09-13T22:14:41Z"}
{"state":"Merged","mergedAt":"2023-09-14T16:51:27Z","number":8055,"mergeCommitSha":"646e51ad27d37bd73f77116eb79d3bc962c7e8fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8055","title":"Cohere client","createdAt":"2023-09-13T22:39:09Z"}
{"state":"Merged","mergedAt":"2023-09-13T23:15:44Z","number":8056,"body":"I'll re-embed after this is deployed","mergeCommitSha":"c99f82d2033d4a0a3861af22fcca57687726ac38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8056","title":"Document ID for a web page should be just generated from the url","createdAt":"2023-09-13T22:52:16Z"}
{"state":"Merged","mergedAt":"2023-09-14T18:47:50Z","number":8057,"mergeCommitSha":"c94243ea86008535ed4f3d0b6529324af7e9963a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8057","title":"Fix product agent header","createdAt":"2023-09-13T23:20:46Z"}
{"state":"Merged","mergedAt":"2023-09-22T21:54:28Z","number":8058,"body":"<img width=\"799\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/d15c297c-e0fd-4993-966a-fa97beaed503\">\r\n\r\n* Make threads under PRs deletable\r\n* Add support to delete TLCs\r\n    * this is semi-blocked by the client 500ing right now (see: https://linear.app/unblocked/issue/UNB-1479/deleting-the-first-tlc-of-a-github-tlc-thread-throws-500s) so the Delete button is currently hidden for the first message of a TLC thread\r\n\r\n\r\n*Note that this is only applicable to Unblocked/SCM provider threads at the moment; we have yet to implement writeback to Slack/Jira/other integrations.","mergeCommitSha":"7d6192df772176b20243de19e4a9f4fa7354a99a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8058","title":"Allow deleting PR threads ","createdAt":"2023-09-13T23:30:00Z"}
{"state":"Merged","mergedAt":"2023-09-14T21:06:38Z","number":8059,"mergeCommitSha":"df0c5320c083dcdac6a8842149ef4aca311fe869","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8059","title":"Skip hub download UI when not on mac","createdAt":"2023-09-13T23:34:55Z"}
{"state":"Merged","mergedAt":"2022-04-06T20:11:47Z","number":806,"body":"* In order to properly sort (and eventually auto-delete archived threads), there needs to be a date property indicating when a thread has been archived. Instead of just adding another property onto the model, the proposed change is to change the `isArchived` boolean property into an `archivedAt` Date property (and its presence would double as a boolean for the client to understand whether a thread has been archived or not). \r\n* Added a new endpoint to update this property \r\n    * We should allow users to both archive threads and also restore archived threads, hence the request endpoint having a boolean property to indicate which direction to go ","mergeCommitSha":"eee01755b3aa09afae8ee1e610319e9acbfdb27c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/806","title":"Thread achived marker should be a date","createdAt":"2022-04-06T18:48:21Z"}
{"state":"Merged","mergedAt":"2023-09-14T20:57:37Z","number":8060,"body":"Only does re-ranking, does not reduce document set based on rank score. That's next. We will still get the benefit of document re-ordering with this change.","mergeCommitSha":"73b8a1d4ef8864fa380ce60ef2819e6e7b0615a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8060","title":"Add rerank to semantic search after retrieval","createdAt":"2023-09-13T23:37:38Z"}
{"state":"Merged","mergedAt":"2023-09-14T18:09:00Z","number":8062,"mergeCommitSha":"02470e67422dbada01f0fb43daf9662ec3d7cd6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8062","title":"LinearIngestionService only runs for installed Linear integrations","createdAt":"2023-09-14T16:52:11Z"}
{"state":"Merged","mergedAt":"2023-09-14T17:40:11Z","number":8063,"body":"This will allow for configuring specific teams for ingestion after Oauth.","mergeCommitSha":"d9901a3c8984416743c06fb71d421a1dbdcead91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8063","title":"Get Linear teams during auth install","createdAt":"2023-09-14T17:16:37Z"}
{"state":"Merged","mergedAt":"2023-09-14T20:40:59Z","number":8064,"body":"This impacts the Create Note (IDE) and Create Walkthrough (dashboard) UIs.  Both these UI show longer fix-height editors, without inline submit buttons.","mergeCommitSha":"14701d7249d57c63828ead830433ea3a162f54a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8064","title":"Hide submit button on large-height editors","createdAt":"2023-09-14T17:51:22Z"}
{"state":"Merged","mergedAt":"2023-09-14T18:18:46Z","number":8065,"body":"I just added 0.5GB more request memory and also increased the limit to allow bursting up to 3GB (instead of the old 2GB)","mergeCommitSha":"20e6396ab32ace3af0538f2c7a3655d46da97a2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8065","title":"temporary increase of API service memory to address pagination issue","createdAt":"2023-09-14T18:05:48Z"}
{"state":"Merged","mergedAt":"2023-09-14T18:40:33Z","number":8066,"mergeCommitSha":"8fd4045de1646d621f78379698a5bc5dcdb7a63c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8066","title":"Cleanup unnecessary PROCESS_PINECONE_INDEX","createdAt":"2023-09-14T18:06:59Z"}
{"state":"Merged","mergedAt":"2023-09-15T22:13:53Z","number":8067,"body":"Currently when a thread is archived we don't create an embedding, but we don't clean up a previously embedded thread after it's archived. \r\n\r\nWe often archive threads for low relevance, so this will prevent these documents from being returned by a document search.","mergeCommitSha":"48a8fad18024acb5dac268933b1abf6e15d1d0c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8067","title":"Delete embedding when a thread is archived","createdAt":"2023-09-14T18:14:11Z"}
{"state":"Merged","mergedAt":"2023-09-14T19:32:02Z","number":8068,"mergeCommitSha":"dddea6530b698799b87c7b759ef61588e4c73fb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8068","title":"Update hc metrics","createdAt":"2023-09-14T18:28:32Z"}
{"state":"Merged","mergedAt":"2023-09-14T20:05:36Z","number":8069,"body":"Backup method to grab port from user defaults.\r\nPrimarily used with older IDEs which are unable to grab port from tmp file","mergeCommitSha":"665e60b67ceb2e55d2eee32dc85a8cbb9063e0e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8069","title":"Backup port with user defaults","createdAt":"2023-09-14T18:44:49Z"}
{"state":"Merged","mergedAt":"2022-04-07T16:07:24Z","number":807,"body":"Setup some CI for web extension.\r\nModify manifest to support chrome store publishing.\r\n\r\n**Fixed some regressions in build due to missing CI**","mergeCommitSha":"732591e53df7c2ce986ca1447d0358c6cef55fc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/807","title":"Setup CI & Chrome store Publishing","createdAt":"2022-04-06T20:13:32Z"}
{"state":"Merged","mergedAt":"2023-09-15T02:19:23Z","number":8070,"mergeCommitSha":"6d240dfe1aef006434f109dbb2dd2d84a5901191","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8070","title":"Update open telemetry","createdAt":"2023-09-14T19:33:01Z"}
{"state":"Merged","mergedAt":"2023-09-14T21:57:46Z","number":8071,"mergeCommitSha":"33f8e05711923da42d81f20ed08852b671076b94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8071","title":"IdentityModel.externalTeamId for linear users should be organization ID","createdAt":"2023-09-14T20:15:32Z"}
{"state":"Merged","mergedAt":"2023-09-14T23:40:55Z","number":8072,"body":"Also does auto-responder min threshold","mergeCommitSha":"969b7d44e87a03879252735314b199eb11601969","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8072","title":"Add minimum rank score for documents before they're excluded from inference","createdAt":"2023-09-14T20:33:19Z"}
{"state":"Merged","mergedAt":"2023-09-15T02:19:13Z","number":8074,"body":"We are going to take advantage of this in the DynamoDB partition map, where each partition ID is a binary UUID (16 Bytes).\r\n- https://www.notion.so/nextchaptersoftware/Incremental-Ingestion-with-Partitions-55e1acda7cf241e984c6a10673baad76?pvs=4\r\n\r\nAlso remove unused `fileSha`.","mergeCommitSha":"069954dc3b058eaf660c56c3c05cad9d9857f9d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8074","title":"Always use UUIDs as Pinecone document IDs","createdAt":"2023-09-14T20:57:34Z"}
{"state":"Merged","mergedAt":"2023-09-18T22:14:19Z","number":8075,"mergeCommitSha":"8ba6ab5b6872dedc57823d6a7601fd073a04b52e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8075","title":"Add Linear configuration API operations","createdAt":"2023-09-14T21:55:10Z"}
{"state":"Merged","mergedAt":"2023-09-15T00:06:36Z","number":8076,"mergeCommitSha":"bc0221ef1e610aa0ec119049fa4ae78a7d608156","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8076","title":"Add LinearTeamIngestionType to allow selecting teams for ingestion","createdAt":"2023-09-14T22:46:40Z"}
{"state":"Merged","mergedAt":"2023-09-15T15:57:43Z","number":8077,"mergeCommitSha":"a0ecc0a16f8c34f57e019d2b00f7bd8b3f7b1650","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8077","title":"Only show weekdays on metrics page","createdAt":"2023-09-14T23:15:22Z"}
{"state":"Merged","mergedAt":"2023-09-15T18:01:18Z","number":8078,"body":"Noticed some issues where streams would go offline when mapStreamAsync throws an error.\r\n\r\nWe don't handle failed streams so ignore error instead.","mergeCommitSha":"54b6f8fb2b45da7f293a79732db89572ccb7082f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8078","title":"Filter errors map stream async","createdAt":"2023-09-15T00:54:37Z"}
{"state":"Merged","mergedAt":"2023-09-15T17:43:54Z","number":8079,"body":"Added the first table based on schema specs provided here: https://www.notion.so/nextchaptersoftware/Incremental-Ingestion-with-Partitions-55e1acda7cf241e984c6a10673baad76?pvs=4\r\n\r\nThere are few assumptions around encryption, performance class and billins. We will add configuration params for those whenver we need to change them. Most of them should remain unless we hit major cost or performance issues\r\n\r\nPS: This stack was added slightly differently than other existing stacks. I have started the refactoring work and this is the new structure we will be using for CDK stacks moving forward. I'll migrate other stacks one at a time.","mergeCommitSha":"6d4506d61b70dcc42bdb0a24bd7b3a6261eaa3ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8079","title":"Added support for creating dynamodb tables","createdAt":"2023-09-15T01:04:29Z"}
{"state":"Merged","mergedAt":"2022-04-06T20:24:57Z","number":808,"body":"- Created a separate ansible vault for prod secrets\r\n- Updated Make targets  and ansible playbooks to handle two environments\r\n- Added a new RSA keypair to Prod vault (also stored in 1p)\r\n- Updated Agora appID and Cert in both Dev and Prod","mergeCommitSha":"73d2a904e742c2e907758bcb10e0e8eeff294263","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/808","title":"Split dev and prod secret configs","createdAt":"2022-04-06T20:17:44Z"}
{"state":"Merged","mergedAt":"2023-09-15T02:42:36Z","number":8080,"mergeCommitSha":"42ff9dbfcbbb388063d51c919836b2ed17c3fed9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8080","title":"Improve ktor tracing","createdAt":"2023-09-15T02:22:30Z"}
{"state":"Merged","mergedAt":"2023-09-15T03:41:05Z","number":8081,"body":"Plan:\r\n1. remove from DEV\r\n2. if all well, remove from PROD\r\n3. if all well, remove dual index code\r\n\r\nThis change will prevent all interactions with the DEV dense index. No inserts, deletes, or queries are possible.","mergeCommitSha":"032e328937152245ba1fc742ed4fb8aadb110a28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8081","title":"Remove dense Pinecone index from DEV","createdAt":"2023-09-15T03:30:42Z"}
{"state":"Merged","mergedAt":"2023-09-15T05:15:09Z","number":8082,"body":"TODO\r\n - [ ] post-merge drop column `MLInferenceTemplateModel.embeddingIndexType` @richiebres ","mergeCommitSha":"75339954dea265c4cd8cf0e567d5f524432d5357","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8082","title":"Remove dual Pinecone index from all environments","createdAt":"2023-09-15T04:24:46Z"}
{"state":"Merged","mergedAt":"2023-09-15T07:59:47Z","number":8083,"mergeCommitSha":"e877d11377454a7a322cc3501b7a7772346dcc84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8083","title":"Add RRF service","createdAt":"2023-09-15T05:40:54Z"}
{"state":"Merged","mergedAt":"2023-09-15T16:23:19Z","number":8084,"mergeCommitSha":"fdfef777e8f8fd2d54a434fbcb658c31ba50a7ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8084","title":"Apply RRF to search","createdAt":"2023-09-15T06:24:00Z"}
{"state":"Merged","mergedAt":"2023-09-15T07:26:51Z","number":8085,"mergeCommitSha":"1f4b40f28420cb051544ec1176c565c63afd3b0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8085","title":"Another possible coroutine fix","createdAt":"2023-09-15T07:26:44Z"}
{"state":"Merged","mergedAt":"2023-09-15T07:42:11Z","number":8086,"mergeCommitSha":"123ce4f8a114d82c337a61b5edeefc32b82adf18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8086","title":"Another fix 2","createdAt":"2023-09-15T07:42:02Z"}
{"state":"Merged","mergedAt":"2023-09-15T17:13:35Z","number":8087,"mergeCommitSha":"b06b2ac7a997411bb400086aede78d494060fe8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8087","title":"Use relaxed boolean conversion","createdAt":"2023-09-15T17:11:17Z"}
{"state":"Merged","mergedAt":"2023-09-15T17:43:41Z","number":8088,"mergeCommitSha":"ae6a41db2ec7df2b243edb7c7cbc02563464f4cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8088","title":"FIX FIX","createdAt":"2023-09-15T17:43:19Z"}
{"state":"Merged","mergedAt":"2023-09-15T18:07:21Z","number":8089,"mergeCommitSha":"201b11121713baac800d7b23d18dced10c620d84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8089","title":"Fix use RRF checkbox","createdAt":"2023-09-15T18:01:19Z"}
{"state":"Merged","mergedAt":"2022-04-06T21:08:13Z","number":809,"body":"This PR changes the logger system to always use a single logger (`logger` from `@shared-utils`).\r\n\r\n* Each app initializes the logger with its own logging properties and targets on startup\r\n* Anyone logging should now always `import { logger } from '@shared-utils'` -- it makes logging more consistent across all projects\r\n* Fixes circular dependencies where logger initialization and API initialization depended on each other\r\n","mergeCommitSha":"d128d88bccde5995931f2d3fd4203a2e13a80d9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/809","title":"Reorganize logger initialization","createdAt":"2022-04-06T20:41:56Z"}
{"state":"Merged","mergedAt":"2023-09-15T18:39:43Z","number":8090,"body":"Since the data in this table can always be reproduced we don't need replication. I need to manually disable delete protection on the table in us-east-2 before merging this. ","mergeCommitSha":"ec203b4a72562f918ee41da3e4aed5d0240dc102","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8090","title":"remove table replication","createdAt":"2023-09-15T18:12:26Z"}
{"state":"Merged","mergedAt":"2023-09-15T19:52:57Z","number":8091,"mergeCommitSha":"7cdf590e1639eeb8c1003d0d2498f9bfdab9b787","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8091","title":"Optimize Linear GraphQL queries to handle API limits","createdAt":"2023-09-15T18:38:40Z"}
{"state":"Merged","mergedAt":"2023-09-15T18:41:13Z","number":8092,"mergeCommitSha":"c2575b489d33d97fc434012ecfb77a9663a2d4f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8092","title":"Add process completion lambda for source code embeddings","createdAt":"2023-09-15T18:40:10Z"}
{"state":"Merged","mergedAt":"2023-09-15T18:53:24Z","number":8093,"body":"I have deployed these changes to Dev and Prod. If you hit any permission issues please let me know. \r\n\r\nIAM and DB permissions can act weird because of a missing action. ","mergeCommitSha":"6a3c66e9d2f74c318ad939c5fc07d45b3fc47600","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8093","title":"Grant admin service access to documentPartitions table","createdAt":"2023-09-15T18:51:22Z"}
{"state":"Merged","mergedAt":"2023-09-15T19:36:37Z","number":8094,"body":"This is part 1 of many many PRs to restructure things. I will also improve and simplify the code as I go along. \r\n\r\nACM stack doesn't really have much room for improvement. ","mergeCommitSha":"127600960542f08cd1f6d04b00fb0a43b8597a33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8094","title":"CDK refactor: restructuring ACM stack","createdAt":"2023-09-15T19:20:17Z"}
{"state":"Merged","mergedAt":"2023-09-15T22:01:45Z","number":8095,"body":"* `_underscore_` should _italicize_\r\n* `~tilde~` should ~strikethrough~\r\n* `Cmd+Z` should undo\r\n* `Cmd+Shift+Z` should redo\r\n\r\nNote: This change leverages the built-in undo/redo functionality of the slate library. It's not completely perfect but is better than nothing. We can look into further customizing this functionality as we see fit.","mergeCommitSha":"ec77f9cdbd1ee9a90f7143713bf2007eb8690ac6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8095","title":"Update message editor shortcut/text formatting","createdAt":"2023-09-15T19:21:39Z"}
{"state":"Merged","mergedAt":"2023-09-15T20:48:33Z","number":8096,"body":"Moving CloudFront stuff to their own directory. Should be a noop change.","mergeCommitSha":"54a112471f8983345b2a1196c92f9fbca85fd1a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8096","title":"Move cloudfront stacks out of the way","createdAt":"2023-09-15T19:56:04Z"}
{"state":"Merged","mergedAt":"2023-09-15T20:15:45Z","number":8097,"body":"Were not cleaning up services properly. Could cause some issues with closing the project? ","mergeCommitSha":"37d250f64be50b69127a77f85d406ea48e1d096d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8097","title":"Dispose of services in IntelliJ","createdAt":"2023-09-15T19:56:50Z"}
{"state":"Merged","mergedAt":"2023-09-15T21:00:48Z","number":8098,"mergeCommitSha":"80b6c6b736c523ec5a785ed538aa6e622f6bda44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8098","title":"Removing hc kotlin extensions","createdAt":"2023-09-15T20:15:45Z"}
{"state":"Merged","mergedAt":"2023-09-15T20:46:54Z","number":8099,"mergeCommitSha":"720112ddd5b75da87fddb885196fa8f3c27d5816","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8099","title":"Re-do the reference ids for documents after re-rank","createdAt":"2023-09-15T20:20:06Z"}
{"state":"Merged","mergedAt":"2022-01-19T21:06:33Z","number":81,"body":"They're behind in releasing this to maven, but I need IAM authentication and it's provided ehre.","mergeCommitSha":"87cda3fa0316a93f84de8a31783dce28006075d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/81","title":"Move to second release of postgres","createdAt":"2022-01-19T20:59:56Z"}
{"state":"Merged","mergedAt":"2022-04-06T21:16:13Z","number":810,"mergeCommitSha":"d0d35df2e143613c45799d10c8e648915a718914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/810","title":"Fix default","createdAt":"2022-04-06T21:00:06Z"}
{"state":"Merged","mergedAt":"2023-09-15T20:49:54Z","number":8100,"mergeCommitSha":"d1b981775e062e38d0eaa9a3e8ea9c714301d1f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8100","title":"Fix small ref id bug","createdAt":"2023-09-15T20:49:38Z"}
{"state":"Merged","mergedAt":"2023-09-15T21:44:24Z","number":8101,"mergeCommitSha":"734604205165f87101075320a64f7d7fa2348c72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8101","title":"Add logging to LinearIssueThreadModelService","createdAt":"2023-09-15T21:28:32Z"}
{"state":"Merged","mergedAt":"2023-09-15T21:43:43Z","number":8102,"mergeCommitSha":"e4e09f55ebb4c95bfc46fcdebb54adab8f7c3e12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8102","title":"Update refinery configuration","createdAt":"2023-09-15T21:43:11Z"}
{"state":"Merged","mergedAt":"2023-09-15T22:29:20Z","number":8103,"body":"Problem\r\nhttps://www.notion.so/nextchaptersoftware/Team-Member-Loading-Problem-422e67d77af44cd9a4c89912cbd1d18a?pvs=4\r\n\r\nIf this works, then we should see far fewer partial requests (where response size < 8MB)\r\nhttps://ui.honeycomb.io/unblocked/environments/production/result/wrJyDX92vJo?useStackedGraphs","mergeCommitSha":"f5eb6ca3b15da72a558507d3b9510362a01a522e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8103","title":"Avoid upserting bot member unless necessary","createdAt":"2023-09-15T22:15:19Z"}
{"state":"Merged","mergedAt":"2023-09-15T22:32:54Z","number":8104,"body":"https://www.notion.so/nextchaptersoftware/Team-Member-Loading-Problem-422e67d77af44cd9a4c89912cbd1d18a?pvs=4","mergeCommitSha":"f49027bfed883f15d6a20b837286219f3f9571b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8104","title":"Show team members modified record timestamp to debug polling bug","createdAt":"2023-09-15T22:32:05Z"}
{"state":"Merged","mergedAt":"2023-09-15T23:02:38Z","number":8105,"body":"Updated TeamMemberFetcher to be stateful and keep track of lastModifiedAt\r\n\r\nOn every request:\r\n\r\nBefore:\r\n![CleanShot 2023-09-15 at 15 21 27@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/9f8e11ad-674c-4c8e-8c0e-8bbbec86a01d)\r\n\r\nAfter (Bot User?)\r\n\r\n![CleanShot 2023-09-15 at 15 22 37@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/8934cb51-3769-44c5-b6ba-f805e80192c5)\r\n","mergeCommitSha":"3d3795229c07c676ed09ef6c0b2f11e197127c3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8105","title":"TeamMemberStore with LastModifiedAt","createdAt":"2023-09-15T22:33:34Z"}
{"state":"Merged","mergedAt":"2023-09-16T05:09:01Z","number":8106,"body":"Unused, and likely to have resulted in team member model churn.","mergeCommitSha":"890034c958b3994f463177317e19a141c3b15852","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8106","title":"Remove team member association confidence value","createdAt":"2023-09-15T23:03:10Z"}
{"state":"Merged","mergedAt":"2023-09-18T18:52:30Z","number":8107,"body":"Currently this variable is set to BestEffort which would use Spot instances only when the prices are good. Otherwise it falls back to on-demand instances.\r\n\r\nOnce this change has been verified for this workflow I will add it to other workflows","mergeCommitSha":"09846fb46c053f93d89b48b5a156f82c9982bb60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8107","title":"Use ec2 builder instance strategy","createdAt":"2023-09-16T01:31:34Z"}
{"state":"Merged","mergedAt":"2023-09-16T03:58:46Z","number":8108,"mergeCommitSha":"768c2b3141b07465ad94b0e72aa75d3c36384360","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8108","title":"Propagate document rank scores at each rank step to admin console pages","createdAt":"2023-09-16T03:04:29Z"}
{"state":"Merged","mergedAt":"2023-09-16T04:29:42Z","number":8109,"mergeCommitSha":"e2733e5d656c3f9fde2ec4583331eb24431ffe07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8109","title":"Fix label","createdAt":"2023-09-16T04:29:25Z"}
{"state":"Merged","mergedAt":"2022-04-06T22:00:53Z","number":811,"body":"- Moved the static error assets bucket to StaticSites stack (still in us-east-1 because it needs integration with CF)\r\n- Moved all client assets buckets to us-west-2\r\n- Enabled transfer acceleration on all assets buckets \r\nDeployed it to Dev from my local machine and works fine. ","mergeCommitSha":"a876def8f68bdeb982cb09d9ff1ea2a547618c3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/811","title":"Move assets buckets to us west","createdAt":"2022-04-06T21:35:30Z"}
{"state":"Merged","mergedAt":"2023-09-16T07:02:43Z","number":8110,"mergeCommitSha":"b5dec30ee1ae033e6e947480a18761754e954bf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8110","title":"Ensure interface works for set math","createdAt":"2023-09-16T05:17:00Z"}
{"state":"Merged","mergedAt":"2023-09-17T04:43:21Z","number":8111,"body":"Was 30 seconds. The intention of this change is to spread the API load caused by releasing a new version.\r\n\r\nhttps://www.notion.so/nextchaptersoftware/Team-Member-Loading-Problem-422e67d77af44cd9a4c89912cbd1d18a?pvs=4","mergeCommitSha":"1ec028b62daf1f843ec93fb31477c772403ee3f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8111","title":"Increase mac version info polling frequency to 5 min","createdAt":"2023-09-16T05:33:07Z"}
{"state":"Merged","mergedAt":"2023-09-17T04:50:39Z","number":8112,"mergeCommitSha":"50efaf76ff54e206e71a57f469f410a1fb8a349a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8112","title":"Cleanup mechanism to disable template","createdAt":"2023-09-16T07:15:00Z"}
{"state":"Merged","mergedAt":"2023-09-17T05:02:16Z","number":8113,"mergeCommitSha":"2d37744c7bfacac4e52dc47e7e1a875518402b93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8113","title":"Refactor template page to group items","createdAt":"2023-09-16T07:15:03Z"}
{"state":"Merged","mergedAt":"2023-09-18T05:15:49Z","number":8114,"mergeCommitSha":"46eef19f2d936450e6a4e697b73fddb21fb0e0ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8114","title":"Document type selection on search template page","createdAt":"2023-09-16T07:15:06Z"}
{"state":"Merged","mergedAt":"2023-09-16T11:56:51Z","number":8115,"mergeCommitSha":"d7b23b20822536c717e4937df449a2ec49c894ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8115","title":"More HC fixes","createdAt":"2023-09-16T11:29:01Z"}
{"state":"Merged","mergedAt":"2023-09-18T18:00:10Z","number":8116,"body":"Fetching team members with lastModified returns a subset.\r\nNeed to upsert existing values instead of a replacement.","mergeCommitSha":"105af1f24904f6c0e1849825ce3f03797ddae176","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8116","title":"Upsert team members","createdAt":"2023-09-16T16:29:05Z"}
{"state":"Merged","mergedAt":"2023-09-17T19:50:14Z","number":8117,"body":"This should be a noop change. We are just reorganizing the code.","mergeCommitSha":"850a37cc3b136e39ea95d8b8f799bc5d2f056a3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8117","title":"Moving RDS, DNS, WAF2 and SQS stacks around","createdAt":"2023-09-16T20:08:46Z"}
{"state":"Merged","mergedAt":"2023-09-17T20:53:14Z","number":8118,"body":"This is another noop PR. We are just moving the code around to reorganize CDK codebase. ","mergeCommitSha":"c4ca54d41633b467c3ff1bb22413627772712a39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8118","title":"More stack reorganization (EC2, ECR, MQ, IAM, Redis)","createdAt":"2023-09-17T20:22:09Z"}
{"state":"Merged","mergedAt":"2023-09-17T22:31:33Z","number":8119,"mergeCommitSha":"b80c3f64f248a0c7678cd5cdc9e3f60228fd778d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8119","title":"Temporarily log sparse vector data for pinecone queries","createdAt":"2023-09-17T21:38:52Z"}
{"state":"Merged","mergedAt":"2022-04-08T20:41:47Z","number":812,"body":"No code, just the project structure and the Agora, GRPC, OpenAPI, and codegen dependencies.\r\n\r\nAgora frameworks are linked directly, and the openAPI + GRPC dependencies are managed via XPM","mergeCommitSha":"c7e642b96b38b729302acafcb323823488041311","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/812","title":"Video app belts and braces","createdAt":"2022-04-06T21:53:56Z"}
{"state":"Merged","mergedAt":"2023-09-18T05:56:18Z","number":8120,"mergeCommitSha":"6428f1ca4db330e3a498a727c8a50cd5a607fbf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8120","title":"Fix doc selection rendering","createdAt":"2023-09-18T05:55:34Z"}
{"state":"Merged","mergedAt":"2023-09-18T17:47:57Z","number":8121,"body":"Motivation: we use 'document' to describe anything in Pinecone, so hopefully this change makes it less confusing.\n\nNo user facing impact.","mergeCommitSha":"aa05a642be5616aabac808a211b31f3e02844a57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8121","title":"Rename InsightType Document to Documentation","createdAt":"2023-09-18T16:49:56Z"}
{"state":"Merged","mergedAt":"2023-09-18T19:10:56Z","number":8122,"body":"Another noop PR to move code around","mergeCommitSha":"87cba896930d57a6a83cbf4799510cd6e6696c28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8122","title":"Move s3, cloudwatch, lambda, transcoder stacks","createdAt":"2023-09-18T18:51:43Z"}
{"state":"Merged","mergedAt":"2023-09-18T21:18:24Z","number":8123,"mergeCommitSha":"34e05e7ccb99b45621b44e658e9846d16c757447","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8123","title":"Add template flat for re-ranker","createdAt":"2023-09-18T19:48:31Z"}
{"state":"Merged","mergedAt":"2023-09-18T20:26:59Z","number":8124,"body":"This is still a noop PR. I have moved all remaining stacks except ML related ones. The reason this change touched almsot every file is the network stack. VPC configs are used in all major stacks.\r\n\r\nNext step is to figure out how we want to split the ML stuff so I can strack cracking on refactoring the code.","mergeCommitSha":"300eea3c4a827256b68eed54a08a4f3da60b1b41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8124","title":"Moving all remaining stacks except ML ones","createdAt":"2023-09-18T20:15:18Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:39:09Z","number":8125,"body":"Welcome Email:\r\nSent when a user connects to Unblocked for t he first time (aka new PersonModel is created)\r\n\r\nOnboarded Drip Campaign\r\n* When a team transitions to UserAccessAllowed, add all team memberse with Unblocked accounts onto drip campaign.\r\n* Any new team members added to this team should be added to drip campaign on login.\r\n\r\n\r\n**Currently not enabled on sendgrid side**","mergeCommitSha":"d945db396eb5e5c98c68d630d94199d4980e2395","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8125","title":"Send welcome email on new person","createdAt":"2023-09-18T21:03:39Z"}
{"state":"Merged","mergedAt":"2023-09-18T21:52:44Z","number":8126,"mergeCommitSha":"6855403448a059628bcb8d0d6186b4f1cc38416b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8126","title":"Add Linear webhook signing secrets","createdAt":"2023-09-18T21:23:36Z"}
{"state":"Merged","mergedAt":"2023-09-19T18:27:40Z","number":8127,"body":"* Consolidate repetitive settings code into a single reusable component. Currently applies to Jira and Confluence only (Slack and Stack Overflow have slightly different UIs)\r\n* Should be no-op on the UI \r\n* Done in anticipation of upcoming integrations ","mergeCommitSha":"9cd31da4ac15e14f18cf2eab4ca5c6f0f7f1b0de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8127","title":"Refactor settings UIs into single component","createdAt":"2023-09-18T21:46:06Z"}
{"state":"Merged","mergedAt":"2023-09-18T22:21:53Z","number":8128,"body":"This change will ensure we're only sending the notification if the message was updated.","mergeCommitSha":"c249a6d00dd6352ed9da3f6440cf37f094681ec5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8128","title":"Fix duplicate slack notifications for onboarding questions","createdAt":"2023-09-18T21:52:35Z"}
{"state":"Merged","mergedAt":"2023-09-18T23:00:24Z","number":8129,"mergeCommitSha":"f92d6f87ae0fbe8f28e961fa1119ab94d7b0f0d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8129","title":"Validate Linear webhook signature","createdAt":"2023-09-18T22:10:09Z"}
{"state":"Merged","mergedAt":"2022-04-06T23:48:40Z","number":813,"mergeCommitSha":"f9d98f2382488eabb47d95e530049fd5410fe9a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/813","title":"Add utils for sourcemarks","createdAt":"2022-04-06T23:28:31Z"}
{"state":"Merged","mergedAt":"2023-09-18T23:31:21Z","number":8130,"mergeCommitSha":"6fb7b95d97daba790e9c809df951a2f42d9072cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8130","title":"Move to smart model for upserts","createdAt":"2023-09-18T22:11:24Z"}
{"state":"Merged","mergedAt":"2023-09-18T22:33:28Z","number":8131,"mergeCommitSha":"f37834cc6bf8a1810c5df7700a37ba4f147d2f13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8131","title":"Defensive strategy to kill the installer if it's hung","createdAt":"2023-09-18T22:19:11Z"}
{"state":"Merged","mergedAt":"2023-09-18T22:28:48Z","number":8132,"body":"Another noop change. This is the last PR for moving files around. I am about to start cleaning up the actual code now.","mergeCommitSha":"4782aa56d642a3c241953bc0dbe1e363a3bacf28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8132","title":"Reorganizing datapipeline and machinelearning stacks","createdAt":"2023-09-18T22:21:37Z"}
{"state":"Merged","mergedAt":"2023-09-19T00:04:56Z","number":8133,"mergeCommitSha":"28c06e53b8008c3da9568199495d3e77db477bba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8133","title":"Move to common aws library","createdAt":"2023-09-18T23:48:59Z"}
{"state":"Merged","mergedAt":"2023-09-19T01:23:42Z","number":8134,"mergeCommitSha":"cd1680c75192d60940d55396c38544adc7c6ec92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8134","title":"Move git utils to own library","createdAt":"2023-09-19T01:06:02Z"}
{"state":"Merged","mergedAt":"2023-09-19T02:34:02Z","number":8135,"mergeCommitSha":"e93bac5cdaa23a8201b7d2b7f051bb6c0eabf18c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8135","title":"Fixing infro builds","createdAt":"2023-09-19T02:33:49Z"}
{"state":"Merged","mergedAt":"2023-09-19T02:36:36Z","number":8136,"mergeCommitSha":"211915c9aee5e0cd328eb5906460d66fddb0166e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8136","title":"Fix docker files","createdAt":"2023-09-19T02:36:21Z"}
{"state":"Merged","mergedAt":"2023-09-21T18:52:41Z","number":8137,"body":"Added overrides for important ec2 builder attributes that need frequent updates.\r\n- EC2_BUILDER_GH_ACTION_RUNNER_VERSION as a repo env var to specify action runner version\r\n- EC2_BUILDER_AMI_ID as a repo env var to override AMI ID for ec2 builders\r\n- EC2_BUILDER_INSTANCE_STRATEGY to override instance strategy (used when we experience issues with ec2 spot instances)\r\n\r\nCurrent values: \r\nEC2_BUILDER_GH_ACTION_RUNNER_VERSION: `v2.300.2`\r\nEC2_BUILDER_AMI_ID: `ami-031e253f85d4296a8`\r\nEC2_BUILDER_INSTANCE_STRATEGY: `MaxPerformance`","mergeCommitSha":"211cd8410197b3dbf9d5c7a63db25ee2d4f3352f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8137","title":"Add overrides for important ec2 builder configs","createdAt":"2023-09-19T08:48:42Z"}
{"state":"Merged","mergedAt":"2023-09-20T07:28:43Z","number":8138,"body":"If this change works we can finally disable prod cdk deploys along with k8s deploys. Another issue with our current approach is how skipped jobs are shown as Passing. This would prevent those jobs from running so users get a clear indication as to what jobs have been skipped.","mergeCommitSha":"b6fdfc41f701b84f0cea009d0f406994c189dc79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8138","title":"Fixing suspend deploy flag","createdAt":"2023-09-19T09:10:42Z"}
{"state":"Merged","mergedAt":"2023-09-19T21:36:05Z","number":8139,"mergeCommitSha":"90e7f25efb3fe6e0e0b2f180e4a1d4f362670295","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8139","title":"Clean up transactions for upsert","createdAt":"2023-09-19T17:03:24Z"}
{"state":"Merged","mergedAt":"2022-04-07T01:50:42Z","number":814,"body":"Move utils to libs (delete utils)\r\nLibs folder has a hierarchy now:\r\nSegment libs into more granular modules (more work to be done).\r\nFix all dependencies across projects.\r\nEventually, most of libs:common will be in more granular dependencies.\r\n\r\n<img width=\"477\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/162100563-f17bb87b-971a-4d13-bc23-6ccb168cad4b.png\">\r\n\r\n","mergeCommitSha":"57c17d4c31931a37805f0d78e575e7f21b005724","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/814","title":"[BRESNINATOR ALERT] Move away from utils to libs","createdAt":"2022-04-07T01:11:39Z"}
{"state":"Merged","mergedAt":"2023-09-19T17:19:21Z","number":8140,"mergeCommitSha":"fe0b587efbcb0ba3c93548c338900af6ab87a2a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8140","title":"Ensure exceptions are propagated from Search code","createdAt":"2023-09-19T17:05:06Z"}
{"state":"Merged","mergedAt":"2023-09-19T19:08:56Z","number":8141,"body":"Remove the risk of TeamMembers cache + modifiedAt cache going out of sync.","mergeCommitSha":"765b5d66266ab5cdf3ba5b07f536aa03e627d876","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8141","title":"Merge TeamMembers & Modified At","createdAt":"2023-09-19T17:43:36Z"}
{"state":"Merged","mergedAt":"2023-09-19T19:54:34Z","number":8142,"mergeCommitSha":"1498bce917289292c31570ba450aae7a918844da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8142","title":"Deserialize Linear webhooks","createdAt":"2023-09-19T18:30:26Z"}
{"state":"Merged","mergedAt":"2023-09-19T19:12:39Z","number":8143,"mergeCommitSha":"2d7659d1333d7509c397e97af2539093938160ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8143","title":"Move out partitioning","createdAt":"2023-09-19T18:49:35Z"}
{"state":"Merged","mergedAt":"2023-09-19T21:05:29Z","number":8144,"mergeCommitSha":"b55d8d713055129662f573c2f1857f14e2673e78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8144","title":"Test python","createdAt":"2023-09-19T19:32:01Z"}
{"state":"Merged","mergedAt":"2023-09-19T20:54:43Z","number":8145,"mergeCommitSha":"d17ee2b803aad662620fbb6608196866d9355300","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8145","title":"Set Provider.Linear.isIntegration to true","createdAt":"2023-09-19T19:59:16Z"}
{"state":"Merged","mergedAt":"2023-09-19T21:05:02Z","number":8146,"body":"Take new lastModifiedAt instead of current lastModifiedAt\r\n\r\n","mergeCommitSha":"6efbe861abed0b0ae6e7c991e9f260b663938e51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8146","title":"Fix lastModifiedAt","createdAt":"2023-09-19T20:49:05Z"}
{"state":"Merged","mergedAt":"2023-09-19T21:39:14Z","number":8147,"mergeCommitSha":"4531931b2cf4d598f2cd185a27dfc76c180ebdab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8147","title":"Install pip3 only","createdAt":"2023-09-19T21:21:13Z"}
{"state":"Merged","mergedAt":"2023-09-19T23:03:51Z","number":8148,"body":"Missing from blog post content:\r\n- Video of creating an Unblocked walkthrough (Dennis to complete)\r\n- List of customers (currently App Direct and Cribl)\r\n\r\nLet's get this in so Dennis can share it, and work on cleaning it up later.\r\n\r\n<img width=\"1279\" alt=\"CleanShot 2023-09-19 at 14 49 47@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/aaf59b8b-9171-47bb-a06c-902c8c9fce82\">\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13353189/31846d03-5d6a-4d95-ae9f-61bd5a727557\r\n\r\n\r\n\r\n","mergeCommitSha":"c183e6459ce892011d7446bbdd292557256e2694","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8148","title":"Blog post and related assets, among other small landing page tweaks","createdAt":"2023-09-19T21:49:35Z"}
{"state":"Merged","mergedAt":"2023-09-19T21:58:44Z","number":8149,"mergeCommitSha":"b2b3d740b9da3415feacf21ada5993accc37471b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8149","title":"Rename packages","createdAt":"2023-09-19T21:55:05Z"}
{"state":"Merged","mergedAt":"2022-04-07T02:25:18Z","number":815,"body":"To avoid naming conflicts in the future with gradle modules.\r\nJust hit it today when we were splitting up libs...\r\nAs discussed with Richie...","mergeCommitSha":"3fda70b9f6e6c71f21f6d8b330264c957f05dda3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/815","title":"Fix up naming of dependent modules in clients and libs folders","createdAt":"2022-04-07T02:15:03Z"}
{"state":"Merged","mergedAt":"2023-09-19T22:24:10Z","number":8150,"mergeCommitSha":"1cc133f5a3ec5a38dd7816b165fa838dcd004612","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8150","title":"Include redirect_uri in linear oauth link","createdAt":"2023-09-19T22:10:17Z"}
{"state":"Merged","mergedAt":"2023-09-19T23:50:42Z","number":8151,"mergeCommitSha":"583db2c151f0c5f154367942d4fad6ba65dac532","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8151","title":"Dont return avatar URL for linear installation","createdAt":"2023-09-19T22:51:44Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:40:03Z","number":8152,"body":"Added component for landing page quotes\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/116e1b2a-d477-4984-b705-738b1ce5461d\r\n\r\nUpdated mobile navigation dots.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/6c9d9a11-474d-4eb4-a4af-4f291afd408d\r\n\r\n","mergeCommitSha":"c8082b36112a3af032a9bf55d6b4ea3ee7d6c95d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8152","title":"Landing page quotes section","createdAt":"2023-09-19T22:59:22Z"}
{"state":"Merged","mergedAt":"2023-09-19T23:15:12Z","number":8153,"mergeCommitSha":"e7ebb752b46feb8b93cb5abddc67736e6e858491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8153","title":"Wrong version","createdAt":"2023-09-19T23:15:01Z"}
{"state":"Merged","mergedAt":"2023-09-20T06:15:36Z","number":8154,"mergeCommitSha":"1adb7a356430a95c4844867cf8ccf873d8da62e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8154","title":"DynamoDB KT client to cleanup document to partition map generated during incremental ingestion","createdAt":"2023-09-19T23:18:08Z"}
{"state":"Merged","mergedAt":"2023-09-20T17:01:18Z","number":8155,"body":"Mostly renaming refactoring.","mergeCommitSha":"03cb5fa9ae767e38e447436954bd790a25b87694","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8155","title":"Move code ingestion background job to source code service","createdAt":"2023-09-19T23:18:13Z"}
{"state":"Merged","mergedAt":"2023-09-20T17:02:29Z","number":8156,"body":"Incremental code ingestion generates a map from a source code file path to a set of Pinecone partition documents. Adding the ability to clear this incremental map in order to reset state.","mergeCommitSha":"40fa907e96c49ae52ce9f57a39eddc4f650a6e8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8156","title":"Incremental code ingestion can cleared from adminweb","createdAt":"2023-09-19T23:18:16Z"}
{"state":"Merged","mergedAt":"2023-09-19T23:43:40Z","number":8157,"mergeCommitSha":"fe55e56a09fb51d227d330f6aaa762ea74449606","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8157","title":"Add span errors","createdAt":"2023-09-19T23:31:56Z"}
{"state":"Merged","mergedAt":"2023-09-20T00:08:40Z","number":8158,"mergeCommitSha":"64177d8ad36a92f309fbd6d4d74b76fef1656dcf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8158","title":"Associate person ID with Oauthing user","createdAt":"2023-09-19T23:49:14Z"}
{"state":"Merged","mergedAt":"2023-09-30T10:04:17Z","number":8159,"mergeCommitSha":"4d3c1ce1a195eb371b4adab2690c1676b91e4fed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8159","title":"[SKIP TESTS] Clear incremental code ingestion maps when deleting or uninstalling team or repo","createdAt":"2023-09-19T23:54:14Z"}
{"state":"Merged","mergedAt":"2022-04-07T04:01:52Z","number":816,"body":"- Split out logging\r\n- update\r\n","mergeCommitSha":"dbd3f3613da0f5d04c0a01fcb71728d4af475c80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/816","title":"CleanupLogging","createdAt":"2022-04-07T03:00:33Z"}
{"state":"Merged","mergedAt":"2023-09-20T17:49:17Z","number":8160,"mergeCommitSha":"ac884ea617e6020872420b4958723edb12dab23d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8160","title":"Disable deleting embeddings for archived threads","createdAt":"2023-09-20T04:40:57Z"}
{"state":"Merged","mergedAt":"2023-09-20T07:53:14Z","number":8161,"body":"Second attempt to get SUSPEND_PROD_DEPLOYS flag working for CDK.","mergeCommitSha":"ca0928ea7b1d04d96f360c03a2070bb7a92d6e43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8161","title":"second attempt to get this working","createdAt":"2023-09-20T07:53:05Z"}
{"state":"Merged","mergedAt":"2023-09-20T16:01:29Z","number":8162,"mergeCommitSha":"857ad5e772754866b0e0e1f60193ebe15517f982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8162","title":"Reduce source code ingestion concurrency","createdAt":"2023-09-20T15:40:48Z"}
{"state":"Merged","mergedAt":"2023-09-20T16:57:10Z","number":8163,"mergeCommitSha":"49f43ca45865f214f1eaa750a4485dadd14940be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8163","title":"Dont return avatar URL for linear org","createdAt":"2023-09-20T16:44:50Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:12:36Z","number":8164,"body":"onboarding:\r\n<img width=\"1479\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/20a42111-8ae5-431a-bd7c-0627f701c6d2\">\r\n<img width=\"1479\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/fa0b77c4-2671-43ba-a4f4-c5779a3bd7f6\">\r\n<img width=\"1473\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/83604dab-2e2d-4940-ae96-477e41d68821\">\r\n\r\nsettings:\r\n<img width=\"1479\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/996f3b56-5d34-4d8a-8a09-874c9ad74f63\">\r\n<img width=\"1480\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/f125ae98-311c-4991-a600-cd43a208ac76\">\r\n","mergeCommitSha":"6558f84a0a60dec00d8b21ca96e8f237aecb5ab2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8164","title":"Linear config UI ","createdAt":"2023-09-20T16:57:59Z"}
{"state":"Merged","mergedAt":"2023-09-20T20:08:15Z","number":8165,"mergeCommitSha":"8c19ec84cad9d40bae6ed6dca94247ca34ccc43c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8165","title":"Embed confluence pages as markdown with title","createdAt":"2023-09-20T17:17:58Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:30:21Z","number":8166,"body":"Should not be doing this.\n\nCorrect solution is to access SCM secrets indirectly through gRPC to secret service.","mergeCommitSha":"174ea540f8c7c2b6234f59ead9d4f5b82f7f23ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8166","title":"SourceCode service now has direct access to SCM secrets","createdAt":"2023-09-20T18:25:47Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:56:51Z","number":8167,"mergeCommitSha":"345e0ac8400add3d0a7baebe6d41216d916ba43b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8167","title":"Fix Linear OAuth client ID for prod","createdAt":"2023-09-20T18:37:35Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:55:46Z","number":8168,"mergeCommitSha":"830c0e31abfa54b31d5cd9084e92048a30f31f6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8168","title":"Landing blog grammar nits","createdAt":"2023-09-20T18:45:50Z"}
{"state":"Merged","mergedAt":"2023-09-20T18:49:41Z","number":8169,"mergeCommitSha":"539ea4ac040fa4748050b255ca2195e085706540","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8169","title":"Source code service has access to Redis","createdAt":"2023-09-20T18:48:50Z"}
{"state":"Merged","mergedAt":"2022-04-07T05:06:41Z","number":817,"body":"ported from KT","mergeCommitSha":"1be69e9f61d932d5fa689b488f8e94b8568d4727","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/817","title":"Add text utils to vscode SM","createdAt":"2022-04-07T03:54:37Z"}
{"state":"Merged","mergedAt":"2023-09-21T16:54:10Z","number":8170,"body":"The new flag using Repo level environment variables is working. Here's an example workflow:\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/6245380789/job/16954322432\r\n\r\n\r\nWe now can see a clear indication when a deployment has been skipped. Before it would just show a green check and the only way to know a deployment has been skipped was via reviewing the steps. \r\n\r\nDoc describing how to use the new flag: https://www.notion.so/nextchaptersoftware/Disable-Prod-Deployments-9aa0dda13e7c4ffe91f05e055e41b8d2?pvs=4","mergeCommitSha":"2b3228515496dbaaf51d969dea2fdd645ed6de29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8170","title":"Remove all references to old SUSPEND_PROD_DEPLOYS flag","createdAt":"2023-09-20T19:38:54Z"}
{"state":"Merged","mergedAt":"2023-09-20T20:00:41Z","number":8171,"mergeCommitSha":"780e6cf730ba039e6c723b61c956067e04004ecb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8171","title":"Fix Redis access from sourcecodeservice","createdAt":"2023-09-20T19:59:34Z"}
{"state":"Merged","mergedAt":"2023-09-20T21:16:07Z","number":8172,"mergeCommitSha":"d256efde5b3c1db1d762de81c00c51865fc243b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8172","title":"Rotate LINEAR_CLIENT_SECRET for prod","createdAt":"2023-09-20T20:20:50Z"}
{"state":"Merged","mergedAt":"2023-09-20T23:37:56Z","number":8173,"body":"Update landing to use mobile assets in smaller breakpoints.\r\n\r\nUpdate components to actually use source instead of \"src\"","mergeCommitSha":"02e9c242872b39e7aedcf433b1876a9c6e994ab5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8173","title":"Update landing page assets based on breakpoint","createdAt":"2023-09-20T20:53:10Z"}
{"state":"Merged","mergedAt":"2023-09-20T20:56:15Z","number":8174,"mergeCommitSha":"8281003a33f6c6e4b06061225b9dbbac0801f2a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8174","title":"Another Redis secret fix","createdAt":"2023-09-20T20:55:08Z"}
{"state":"Merged","mergedAt":"2023-09-20T23:02:04Z","number":8175,"body":"Fixes https://app.logz.io/#/goto/9a5f7609e8ef364d173f8177f2a9387c?switchToAccountId=411850","mergeCommitSha":"0a477b84800c75381bf6f440aa8db2551a0d51a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8175","title":"Handle text inline elements that are missing a text property","createdAt":"2023-09-20T22:17:54Z"}
{"state":"Merged","mergedAt":"2023-09-21T00:53:25Z","number":8176,"mergeCommitSha":"378fd39a1f5e26b58d66602537c277598b348689","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8176","title":"Add initial support for clients","createdAt":"2023-09-20T22:29:39Z"}
{"state":"Merged","mergedAt":"2023-09-20T23:22:09Z","number":8177,"mergeCommitSha":"9d3beda9fe9be6f19c41a0f9968ae4ed779a67a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8177","title":"Fix landing index spacing and quotation marks","createdAt":"2023-09-20T22:41:39Z"}
{"state":"Merged","mergedAt":"2023-09-20T22:54:31Z","number":8178,"mergeCommitSha":"0702d29209f4f3ac7fc412279bce5c7e8438e0bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8178","title":"this is a change to test the new suspend prod deploy flag","createdAt":"2023-09-20T22:54:19Z"}
{"state":"Merged","mergedAt":"2023-09-20T23:30:25Z","number":8179,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/cc68612b-e2a7-44f5-b380-2b186401f70b\r\n\r\n","mergeCommitSha":"1e03acf4e95d26aeb4195d66c6922885ec73083e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8179","title":"Clear bot mention iff no other content","createdAt":"2023-09-20T22:58:32Z"}
{"state":"Merged","mergedAt":"2022-04-07T17:03:11Z","number":818,"mergeCommitSha":"99b88a02f0ae34f61efad943f8079d2ca8e73303","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/818","title":"Revert build fanning changes as it is way too expensive for now","createdAt":"2022-04-07T16:29:05Z"}
{"state":"Merged","mergedAt":"2023-09-21T01:16:51Z","number":8180,"mergeCommitSha":"f7101117d93350106a8a77a4cc6d28563274bc3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8180","title":"Add ktor client tracing","createdAt":"2023-09-21T00:54:36Z"}
{"state":"Merged","mergedAt":"2023-09-21T02:06:03Z","number":8181,"mergeCommitSha":"1fa07621bf9787cb52f4b3786aee1e61116d8f4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8181","title":"Canoncial route","createdAt":"2023-09-21T02:05:39Z"}
{"state":"Merged","mergedAt":"2023-09-21T02:57:13Z","number":8182,"body":"- Handle intercom paths\r\n- Lint\r\n","mergeCommitSha":"e41c515f5be90e6b367efc093217163c5715219d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8182","title":"MoreCanonical","createdAt":"2023-09-21T02:57:08Z"}
{"state":"Merged","mergedAt":"2023-09-21T03:17:07Z","number":8183,"mergeCommitSha":"f8595683916a2d2cd7909cfcd82160f31af4492a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8183","title":"Fix client schema","createdAt":"2023-09-21T03:15:51Z"}
{"state":"Merged","mergedAt":"2023-09-21T03:38:55Z","number":8184,"mergeCommitSha":"29a44594f753bbdf0e25f012f3442706caf55523","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8184","title":"Fix transactions","createdAt":"2023-09-21T03:38:15Z"}
{"state":"Merged","mergedAt":"2023-09-21T05:32:02Z","number":8185,"body":"For `span.kind=client`, make `name` something that we can easily group on.\n\nThis is so that we can easily detect an issue with a single downsteam client.","mergeCommitSha":"487749e9af121b75ea57be92ecdbff1f0457d8d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8185","title":"Client span tweaks","createdAt":"2023-09-21T05:02:23Z"}
{"state":"Merged","mergedAt":"2023-09-21T05:32:21Z","number":8186,"mergeCommitSha":"201586b495edc3c4828d587a56956c254e3c94f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8186","title":"Remove superfluous Pinecone describe-index-stats API call","createdAt":"2023-09-21T05:15:30Z"}
{"state":"Merged","mergedAt":"2023-09-21T16:43:13Z","number":8187,"mergeCommitSha":"0db0a2267c65a6ed326ce75cc9af74b9c3934d95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8187","title":"Remove LinearRepoResolutionService","createdAt":"2023-09-21T16:06:54Z"}
{"state":"Merged","mergedAt":"2023-09-21T16:24:27Z","number":8188,"body":"This reverts commit ac884ea617e6020872420b4958723edb12dab23d.","mergeCommitSha":"f43f82644d438e779710c293b701ce8c833a14ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8188","title":"Revert \"Disable deleting embeddings for archived threads (#8160)\"","createdAt":"2023-09-21T16:08:59Z"}
{"state":"Merged","mergedAt":"2023-09-21T18:45:47Z","number":8189,"body":"LinesAdded was incorrectly determined. This issue was exasperated by its interactions with the VIM plugin.\r\n\r\nWhen new text is inserted `change.text` will *not* include a new line character `\\n`. e.g. (\"a\")\r\n`change.text.split('\\n').length` on \"a\" will return a length of 1, not 0.\r\n\r\nBecause `linesAdded` is > 0, we will rerender the TextEditorSourceMarks.\r\n\r\nTLDR, on every character, we are triggering a rerender of TextEditorSourceMarks.\r\n\r\n\r\n","mergeCommitSha":"ea5e1b1fc0ce5df5398e4d808aaf2990bb9e00a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8189","title":"Fix lag in VSCode for large files","createdAt":"2023-09-21T18:20:21Z"}
{"state":"Merged","mergedAt":"2022-04-07T17:36:48Z","number":819,"mergeCommitSha":"ac77deae44707329a3a843c0ad533b6a31012216","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/819","title":"Split up more modules","createdAt":"2022-04-07T17:12:33Z"}
{"state":"Merged","mergedAt":"2023-09-21T19:34:48Z","number":8191,"mergeCommitSha":"374ca1967e5eeac0400d270878eeaddb5ef2cc3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8191","title":"Handle Linear comment webhooks","createdAt":"2023-09-21T18:59:37Z"}
{"state":"Merged","mergedAt":"2023-09-21T20:31:13Z","number":8192,"body":"- gRPC is optimized for multi-pod indexes\n- we expect better write throughput and lower write latency\n- plan to switch over as soon as index is up\n\nIndex:\nhttps://app.pinecone.io/organizations/-NShhmpLr6lQoYJjAKHm/projects/us-west1-gcp:df1432b/indexes/prod-unblocked-instructor-dotproduct-n4/browser","mergeCommitSha":"2027baf35ba7f9d2f8abff4fee8f1bf0603efb37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8192","title":"Switch to horizontally scaled Pinecone index","createdAt":"2023-09-21T19:14:24Z"}