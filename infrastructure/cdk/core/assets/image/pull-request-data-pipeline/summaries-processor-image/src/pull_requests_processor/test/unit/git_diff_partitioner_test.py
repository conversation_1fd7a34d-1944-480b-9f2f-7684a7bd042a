import os
import unittest

from pull_requests_processor.git_diff_partitioner import GitDiffPartitioner
from pull_requests_processor.llm_constants import LLAMA_MAX_INPUT_TOKENS


class TestGitDiffPartitioner(unittest.TestCase):
    _git_diff_partitioner = GitDiffPartitioner(max_tokens=LLAMA_MAX_INPUT_TOKENS)

    @staticmethod
    def fixture_path(name):
        return os.path.join(os.path.dirname(__file__), "fixtures", "git", name)

    def test_large_diff(self):
        file = self.fixture_path("large_git_diff_1")
        with open(file) as f:
            git_diff = f.read()
            results = self._git_diff_partitioner.split_git_diff(git_diff=git_diff)
            self.assertTrue(len(results.partitions) > 0)


if __name__ == "__main__":
    unittest.main()
