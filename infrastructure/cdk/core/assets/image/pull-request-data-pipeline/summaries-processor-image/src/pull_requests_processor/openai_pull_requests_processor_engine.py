import logging
import asyncio

from dataclasses import dataclass
from tqdm import tqdm
from typing import List, Optional

import git_utils.git_utils as git
from pull_requests_processor.openai_pull_request_summary_engine import OpenAIPullRequestSummaryEngine
from pull_requests_processor.pull_requests_processor_engine import (
    ThreadedPullRequestsProcessorEngine,
    PullRequestsProcessorEngine,
)
from pull_requests_processor.pull_request_types import PullRequest, PullRequestSummaryResult
from pull_requests_processor.pull_request_constants import DEFAULT_BATCH_SIZE


@dataclass(order=True)
class WorkerRequest:
    pull_request: PullRequest
    repo_dir: str


class OpenAIPullRequestsProcessorEngine:
    _pull_request_summary_engine: OpenAIPullRequestSummaryEngine = OpenAIPullRequestSummaryEngine()
    _pull_requests_processor_engine: PullRequestsProcessorEngine = ThreadedPullRequestsProcessorEngine()

    def async_process_pull_requests(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> List[PullRequestSummaryResult]:
        task = self._pull_requests_processor_engine.async_get_pull_request_summary_results(
            generator=self.__process_pull_request,
            repo_dir=repo_dir,
            pull_requests=pull_requests,
            batch_size=batch_size,
        )
        loop = asyncio.get_event_loop()
        pull_request_summary_results = loop.run_until_complete(task)
        loop.close()

        return pull_request_summary_results

    async def __process_pull_request(
        self, repo_dir: str, pull_request: PullRequest
    ) -> Optional[PullRequestSummaryResult]:
        try:
            git_diff = git.show(repo_dir=repo_dir, sha=pull_request.merge_commit_sha)
            pull_request_summary_result = await self._pull_request_summary_engine.async_predict(
                pull_request=pull_request,
                git_diff=git_diff,
            )
            print(pull_request_summary_result)
            return pull_request_summary_result
        except Exception as e:
            logging.error(e)

        return None
