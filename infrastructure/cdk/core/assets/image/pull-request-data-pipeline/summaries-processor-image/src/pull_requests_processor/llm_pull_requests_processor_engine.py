import logging
import asyncio

from dataclasses import dataclass

from langchain.llms.sagemaker_endpoint import LL<PERSON>ontent<PERSON>and<PERSON>
from typing import List, Optional

import git_utils.git_utils as git
from llm_prompt_utils.llm_prompt import <PERSON><PERSON>rompt

from pull_requests_processor.git_diff_partitioner import GitDiffPartitioner
from pull_requests_processor.llm_pull_request_summary_engine import LLMPullRequestSummaryEngine
from pull_requests_processor.pull_request_types import PullRequest, PullRequestSummaryResult
from pull_requests_processor.pull_requests_processor_engine import (
    ThreadedPullRequestsProcessorEngine,
)
from pull_requests_processor.pull_request_constants import DEFAULT_BATCH_SIZE


@dataclass(order=True)
class WorkerRequest:
    pull_request: PullRequest
    repo_dir: str


class LLMPullRequestsProcessorEngine:
    _pull_request_summary_engine: LLMPullRequestSummaryEngine
    _git_diff_partitioner: GitDiffPartitioner
    _pull_requests_processor_engine: ThreadedPullRequestsProcessorEngine

    def __init__(
        self,
        llm_combine_prompt: <PERSON><PERSON>rom<PERSON>,
        llm_map_prompt: LL<PERSON>rompt,
        content_handler: LLMContentHandler,
        llm_endpoint_url: str,
        max_tokens: int,
        max_new_tokens: int,
    ):
        self._pull_request_summary_engine = LLMPullRequestSummaryEngine(
            llm_map_prompt=llm_map_prompt,
            llm_combine_prompt=llm_combine_prompt,
            content_handler=content_handler,
            max_tokens=max_tokens,
            max_new_tokens=max_new_tokens,
            llm_endpoint_url=llm_endpoint_url,
        )
        self._git_diff_partitioner = GitDiffPartitioner(
            max_tokens=max_tokens,
        )
        self._pull_requests_processor_engine = ThreadedPullRequestsProcessorEngine()

    def process_pull_requests(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> List[PullRequestSummaryResult]:
        return self._pull_requests_processor_engine.get_pull_request_summary_results(
            generator=self.__process_pull_request,
            repo_dir=repo_dir,
            pull_requests=pull_requests,
            batch_size=batch_size,
        )

    def __process_pull_request(self, repo_dir: str, pull_request: PullRequest) -> Optional[PullRequestSummaryResult]:
        try:
            git_diff = git.show(repo_dir=repo_dir, sha=pull_request.merge_commit_sha)
            git_diff_partitions = self._git_diff_partitioner.split_git_diff(git_diff=git_diff)
            pull_request_summary_result = self._pull_request_summary_engine.predict(
                pull_request=pull_request,
                git_diff_partitions=git_diff_partitions,
            )
            print(pull_request_summary_result)
            return pull_request_summary_result
        except Exception as e:
            logging.exception(e)

        return None

    def async_process_pull_requests(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> List[PullRequestSummaryResult]:
        task = self._pull_requests_processor_engine.async_get_pull_request_summary_results(
            generator=self.__async_process_pull_request,
            repo_dir=repo_dir,
            pull_requests=pull_requests,
            batch_size=batch_size,
        )

        loop = asyncio.get_event_loop()
        pull_request_summary_results = loop.run_until_complete(task)
        loop.close()

        return pull_request_summary_results

    async def __async_process_pull_request(
        self, repo_dir: str, pull_request: PullRequest
    ) -> Optional[PullRequestSummaryResult]:
        try:
            git_diff = git.show(repo_dir=repo_dir, sha=pull_request.merge_commit_sha)
            git_diff_partitions = self._git_diff_partitioner.split_git_diff(git_diff=git_diff)
            pull_request_summary_result = await self._pull_request_summary_engine.async_predict(
                pull_request=pull_request,
                git_diff_partitions=git_diff_partitions,
            )
            print(pull_request_summary_result)
            return pull_request_summary_result
        except Exception as e:
            logging.exception(e)

        return None
