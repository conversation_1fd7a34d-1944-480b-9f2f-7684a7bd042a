{"state":"Merged","mergedAt":"2023-01-18T18:54:49Z","number":4330,"body":"Client needs to send two redirectURLs.\r\n\r\n1. RedirectURL for oauth exchange (exists)\r\n2. RedirectURL for manifest exchange (NEW)\r\n\r\nWeb client is currently currently setting the redirectURL as `login/manifest/exchange` instead of `login/exchange` in findEnterpriseRepositories which is causing issues with the oauth flow.\r\n","mergeCommitSha":"ae42509c0c0544c2a63e17ce6553401b00f70594","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4330","title":"Update manifest url for exchange","createdAt":"2023-01-18T17:03:55Z"}
{"state":"Merged","mergedAt":"2023-01-19T00:36:05Z","number":4331,"mergeCommitSha":"14a347ef32866fcd2e8abd60d363180ca9429d08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4331","title":"Respect limit passed by client","createdAt":"2023-01-18T19:31:06Z"}
{"state":"Merged","mergedAt":"2023-01-18T20:52:13Z","number":4332,"body":"We are using product agent to determine which video asset we should be using.\r\nFor vscode we need to use h264 with mp3 due to licensing issues.\r\n\r\nThis is horrible, but it is what it is.","mergeCommitSha":"7cab68c1b5b1ad16419d0a30bbf208e2bf2373ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4332","title":"route to specific video asset for vscode","createdAt":"2023-01-18T19:54:43Z"}
{"state":"Merged","mergedAt":"2023-01-19T00:14:40Z","number":4333,"body":"Triggering reindexing for a repo from the admin console was hitting timeouts. Instead lets send a single event for a repo, and have the search service be responsible for creating the indexing events for each insight.","mergeCommitSha":"916f538f7afdc7bf851f17bbe184ab769254dc48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4333","title":"Drive repo indexing with an event","createdAt":"2023-01-18T21:07:25Z"}
{"state":"Merged","mergedAt":"2023-01-18T21:34:53Z","number":4334,"mergeCommitSha":"9484dfe3de18c728e846c39e3be2da5ace09533e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4334","title":"increase slack service and slack data service cpu limits","createdAt":"2023-01-18T21:28:49Z"}
{"state":"Merged","mergedAt":"2023-01-19T02:36:26Z","number":4335,"mergeCommitSha":"80ed35153fbc91eb1df3d47e99c045835ac8e109","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4335","title":"Upgrade exposed/aws","createdAt":"2023-01-18T22:05:08Z"}
{"state":"Merged","mergedAt":"2023-01-20T00:32:15Z","number":4336,"body":"Update spec to allow hub to open PR in VSCode","mergeCommitSha":"9343c12e5d5474c5470e9d351823fafb18ebe336","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4336","title":"Open PR from Hub","createdAt":"2023-01-18T22:16:42Z"}
{"state":"Merged","mergedAt":"2023-01-19T00:07:25Z","number":4337,"mergeCommitSha":"490dc96a1b98f7c59956328812a45978b472d69c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4337","title":"Identity provider was wrong for GHE","createdAt":"2023-01-18T23:39:32Z"}
{"state":"Merged","mergedAt":"2023-01-20T18:31:27Z","number":4338,"body":"Also cleans up some styling for breakpoints ","mergeCommitSha":"874535c9fd3c3506ec1fcca2d623b41ea4d1fdb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4338","title":"Refactor menu properties","createdAt":"2023-01-19T00:22:08Z"}
{"state":"Merged","mergedAt":"2023-01-19T00:44:06Z","number":4339,"body":"No behaviour change.","mergeCommitSha":"244dd33aa81da77e9dc0a0401cd4cd7e40180c6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4339","title":"Rename GitHub API classes","createdAt":"2023-01-19T00:25:00Z"}
{"state":"Merged","mergedAt":"2022-02-28T23:47:30Z","number":434,"body":"I mixed up the key/values","mergeCommitSha":"9d581421ee885cd31a142fbbbb1afc4ccd50ef7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/434","title":"Logging bug fix","createdAt":"2022-02-28T23:41:39Z"}
{"state":"Merged","mergedAt":"2023-01-19T00:49:04Z","number":4340,"mergeCommitSha":"c468615f1a8f1450a48116b82d06296d2b624401","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4340","title":"Fix topic routing","createdAt":"2023-01-19T00:31:50Z"}
{"state":"Merged","mergedAt":"2023-01-19T00:53:49Z","number":4341,"mergeCommitSha":"64216740bab5a6b665012e0d547bdcfeebd8ef4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4341","title":"Set ThreadInfo.pullRequests","createdAt":"2023-01-19T00:33:08Z"}
{"state":"Merged","mergedAt":"2023-03-14T20:18:21Z","number":4342,"mergeCommitSha":"7505e17764fd25dce6de0024b1444a1fd9098074","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4342","title":"Support offsets for search result pagination","createdAt":"2023-01-19T01:08:19Z"}
{"state":"Merged","mergedAt":"2023-01-19T04:17:35Z","number":4343,"body":"No behaviour change, just renames.","mergeCommitSha":"7dfbe43b7962527cf401194eaa82eb273a0394ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4343","title":"Refactor GitHub classes","createdAt":"2023-01-19T01:14:22Z"}
{"state":"Merged","mergedAt":"2023-01-19T18:42:16Z","number":4344,"body":"Reverting https://github.com/NextChapterSoftware/unblocked/pull/4287 for deployment purposes","mergeCommitSha":"283570bf4da173ca56e4379eeaaa5b4c75cb1170","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4344","title":"Revert VSCode","createdAt":"2023-01-19T18:32:57Z"}
{"state":"Merged","mergedAt":"2023-01-19T19:01:39Z","number":4345,"body":"Issue with shared styles between web + VSCode.\r\n\r\nTemporary fix for VSCode. Does make web icons smaller but will address that independently once this is in.\r\n\r\n<img width=\"376\" alt=\"CleanShot 2023-01-19 at 10 40 03@2x\" src=\"https://user-images.githubusercontent.com/1553313/213532013-166c6c8d-df1a-4a24-b984-c7b2e5094264.png\">\r\n\r\n<img width=\"893\" alt=\"CleanShot 2023-01-19 at 10 39 27@2x\" src=\"https://user-images.githubusercontent.com/1553313/213532030-7c4bc1fd-177b-457c-98fc-b8dde662f2f7.png\">\r\n\r\n","mergeCommitSha":"ee497e2fbfa18b25803ee32775750ba76c47a074","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4345","title":"Fix icon size vs code","createdAt":"2023-01-19T18:41:24Z"}
{"state":"Merged","mergedAt":"2023-01-20T18:41:53Z","number":4346,"body":"Add slack urls to insight summary views\r\n<img width=\"352\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213538909-7320b3b0-49b4-4bd6-8cae-e5f0fb668ffd.png\">\r\n<img width=\"344\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213539005-38448410-e982-44cc-90ab-41639fcbf335.png\">\r\n\r\nAdd missing context menu to insight explorer pr rows:\r\n<img width=\"364\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213539094-0e1f1ca9-e3ab-4f87-b6cd-f42ccd495484.png\">\r\n","mergeCommitSha":"61301c1fe6ce9c5940ccfb7d3f2ab2018775b371","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4346","title":"Add missing slack urls","createdAt":"2023-01-19T19:19:34Z"}
{"state":"Merged","mergedAt":"2023-01-19T19:21:02Z","number":4347,"mergeCommitSha":"d0dcd1b3aca842c1f113d569a2ca812d21cc236f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4347","title":"Add transcoder access to video service","createdAt":"2023-01-19T19:20:40Z"}
{"state":"Merged","mergedAt":"2023-01-20T18:17:13Z","number":4348,"mergeCommitSha":"383a0de67ba074466bb02967e4a6b42151e0cf96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4348","title":"Add links to PullRequest","createdAt":"2023-01-19T19:32:37Z"}
{"state":"Merged","mergedAt":"2023-02-21T19:15:23Z","number":4349,"body":"<img width=\"999\" alt=\"CleanShot 2023-01-19 at 11 42 00@2x\" src=\"https://user-images.githubusercontent.com/1553313/213544740-7e699b95-1873-4100-94f9-7f94ec23de09.png\">\r\n\r\nBack to proper size\r\n\r\n","mergeCommitSha":"801931fb9324286d791227dd8d70f2affa866585","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4349","title":"Web References Icon Override","createdAt":"2023-01-19T19:47:06Z"}
{"state":"Merged","mergedAt":"2022-03-01T17:02:22Z","number":435,"body":"Second log entry below has `identityId`\r\n```\r\n16:06:15 | DEBUG | c.n.p.Monitoring: 200 OK: GET - /godmode/60933ef2-1a65-41e9-9265-190fc6f37d87\r\n{ http.req.query=, http.req.path=/godmode/60933ef2-1a65-41e9-9265-190fc6f37d87, http.req.method=GET, http.req.header.User-Agent=curl/7.77.0, http.res.status=200 }\r\n\r\n16:06:48 | DEBUG | c.n.p.Monitoring: 200 OK: GET - /teams\r\n{ http.req.query=, identityId=60933ef2-1a65-41e9-9265-190fc6f37d87, http.req.path=/teams, http.req.method=GET, http.req.header.User-Agent=curl/7.77.0, http.res.status=200 }\r\n```","mergeCommitSha":"a300812e9c50c0c9697b25b4cf52d8773168bd84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/435","title":"Plumb identity ID into API call logger","createdAt":"2022-03-01T00:07:34Z"}
{"state":"Merged","mergedAt":"2023-01-19T21:01:07Z","number":4350,"mergeCommitSha":"3a1e008d7c89e99235c9a6eb4d1324bb53884731","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4350","title":"break it or make it. Remove all capabilities from service pods","createdAt":"2023-01-19T20:39:19Z"}
{"state":"Merged","mergedAt":"2023-01-19T21:21:18Z","number":4351,"mergeCommitSha":"db141ce11b4be33dbe9aa845a1a7eba68abb5ffc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4351","title":"Add transcoder functionality","createdAt":"2023-01-19T20:54:38Z"}
{"state":"Merged","mergedAt":"2023-01-20T17:08:10Z","number":4352,"body":"Revert the VSCodeGHE revert\r\n\r\nAdded fix to RepoStore for `'Could not find repo from service'`","mergeCommitSha":"f67afea0f4065820b513c51643aaf43afbe0a9c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4352","title":"Reintroduce GHE in VSCode","createdAt":"2023-01-19T21:23:53Z"}
{"state":"Merged","mergedAt":"2023-01-19T22:20:56Z","number":4353,"body":"Our logging library is throwing exceptions because it cannot write to /tmp directory after recent permission changes. This adds a temporary empty dir volume with writable permissions to `/tmp` to get around that without adding any security risks\r\n\r\n\r\n```\r\nailed to instantiate [ch.qos.logback.classic.LoggerContext]\r\nReported exception:\r\nch.qos.logback.core.LogbackException: Failed to initialize Configurator: ch.qos.logback.classic.util.DefaultJoranConfigurator using ServiceLoader\r\n\tat ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:99)\r\n\tat ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:77)\r\n\tat ch.qos.logback.classic.spi.LogbackServiceProvider.initializeLoggerContext(LogbackServiceProvider.java:50)\r\n\tat ch.qos.logback.classic.spi.LogbackServiceProvider.initialize(LogbackServiceProvider.java:41)\r\n\tat org.slf4j.LoggerFactory.bind(LoggerFactory.java:152)\r\n\tat org.slf4j.LoggerFactory.performInitialization(LoggerFactory.java:139)\r\n\tat org.slf4j.LoggerFactory.getProvider(LoggerFactory.java:422)\r\n\tat org.slf4j.LoggerFactory.getILoggerFactory(LoggerFactory.java:408)\r\n\tat org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:357)\r\n\tat org.slf4j.LoggerFactory.getLogger(LoggerFactory.java:383)\r\n\tat software.amazon.awssdk.utils.Logger.loggerFor(Logger.java:221)\r\n\tat software.amazon.awssdk.auth.signer.internal.AbstractAws4Signer.<clinit>(AbstractAws4Signer.java:65)\r\n\tat software.amazon.awssdk.services.rds.DefaultRdsUtilities.<init>(DefaultRdsUtilities.java:40)\r\n\tat software.amazon.awssdk.services.rds.DefaultRdsUtilities.<init>(DefaultRdsUtilities.java:46)\r\n\tat software.amazon.awssdk.services.rds.DefaultRdsUtilities$DefaultBuilder.build(DefaultRdsUtilities.java:153)\r\n\tat com.nextchaptersoftware.db.common.config.AwsStsPasswordDelegate.getValue(AwsStsPasswordDelegate.kt:20)\r\n\tat com.nextchaptersoftware.db.common.config.AwsDbConfigProvider.getPassword(AwsDbConfigProvider.kt:16)\r\n\tat com.nextchaptersoftware.db.common.config.AwsDbConfigProvider.<init>(AwsDbConfigProvider.kt:31)\r\n\tat com.nextchaptersoftware.db.common.config.DbConfigProviderDelegate.getValue(DbConfigProviderDelegate.kt:30)\r\n\tat com.nextchaptersoftware.db.common.Database.getDbConfigProvider(Database.kt:34)\r\n\tat com.nextchaptersoftware.db.common.Database.connect(Database.kt:78)\r\n\tat com.nextchaptersoftware.db.common.Database.init(Database.kt:52)\r\n\tat com.nextchaptersoftware.apiservice.ApplicationKt.main(Application.kt:32)\r\n\tat com.nextchaptersoftware.apiservice.ApplicationKt.main(Application.kt)\r\nCaused by: io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigQueueException: java.io.FileNotFoundException: /tmp/logzio-logback-queue/java/logzio-logback-appender/meta_data/page-0.dat (No such file or directory)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.MappedPageFactory.acquirePage(MappedPageFactory.java:94)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigArray.initArrayIndex(BigArray.java:241)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigArray.commonInit(BigArray.java:157)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigArray.<init>(BigArray.java:141)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigQueue.<init>(BigQueue.java:83)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.BigQueue.<init>(BigQueue.java:67)\r\n\tat io.logz.logback-appender.sender.DiskQueue.<init>(DiskQueue.java:32)\r\n\tat io.logz.logback-appender.sender.DiskQueue.<init>(DiskQueue.java:10)\r\n\tat io.logz.logback-appender.sender.DiskQueue$Builder.build(DiskQueue.java:154)\r\n\tat io.logz.logback-appender.sender.LogzioSender$Builder.getLogsQueue(LogzioSender.java:272)\r\n\tat io.logz.logback-appender.sender.LogzioSender$Builder.build(LogzioSender.java:264)\r\n\tat io.logz.logback.LogzioLogbackAppender.start(LogzioLogbackAppender.java:252)\r\n\tat ch.qos.logback.core.model.processor.AppenderModelHandler.postHandle(AppenderModelHandler.java:84)\r\n\tat ch.qos.logback.core.model.processor.DefaultProcessor.secondPhaseTraverse(DefaultProcessor.java:248)\r\n\tat ch.qos.logback.core.model.processor.DefaultProcessor.secondPhaseTraverse(DefaultProcessor.java:244)\r\n\tat ch.qos.logback.core.model.processor.DefaultProcessor.traversalLoop(DefaultProcessor.java:90)\r\n\tat ch.qos.logback.core.model.processor.DefaultProcessor.process(DefaultProcessor.java:106)\r\n\tat ch.qos.logback.core.joran.GenericXMLConfigurator.processModel(GenericXMLConfigurator.java:199)\r\n\tat ch.qos.logback.core.joran.GenericXMLConfigurator.doConfigure(GenericXMLConfigurator.java:165)\r\n\tat ch.qos.logback.core.joran.GenericXMLConfigurator.doConfigure(GenericXMLConfigurator.java:122)\r\n\tat ch.qos.logback.core.joran.GenericXMLConfigurator.doConfigure(GenericXMLConfigurator.java:65)\r\n\tat ch.qos.logback.classic.util.DefaultJoranConfigurator.configureByResource(DefaultJoranConfigurator.java:53)\r\n\tat ch.qos.logback.classic.util.DefaultJoranConfigurator.configure(DefaultJoranConfigurator.java:34)\r\n\tat ch.qos.logback.classic.util.ContextInitializer.autoConfig(ContextInitializer.java:93)\r\n\t... 23 more\r\nCaused by: java.io.FileNotFoundException: /tmp/logzio-logback-queue/java/logzio-logback-appender/meta_data/page-0.dat (No such file or directory)\r\n\tat java.base/java.io.RandomAccessFile.open0(Native Method)\r\n\tat java.base/java.io.RandomAccessFile.open(RandomAccessFile.java:344)\r\n\tat java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:259)\r\n\tat java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:213)\r\n\tat java.base/java.io.RandomAccessFile.<init>(RandomAccessFile.java:127)\r\n\tat io.logz.logback-appender.sender.com.bluejeans.common.bigqueue.MappedPageFactory.acquirePage(MappedPageFactory.java:85)\r\n\t... 46 more\r\n```","mergeCommitSha":"84c7798d7f63d656c0907d169b20923a9b6b3d1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4353","title":"add a temp writable volume and fsGroup security context","createdAt":"2023-01-19T21:48:17Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:24:53Z","number":4354,"body":"- Reduce padding to match designs\r\n- Animate the filter input bar open/close.  I added an animation that collapses/expands the UI.","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4354","title":"Cleanup VSCode explorer insight filter UX","createdAt":"2023-01-19T22:39:29Z"}
{"state":"Merged","mergedAt":"2023-01-19T22:51:08Z","number":4355,"mergeCommitSha":"16e99699993df4eeca9c65794837c55b4a3c4feb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4355","title":"Move transcoding to event based system","createdAt":"2023-01-19T22:47:40Z"}
{"state":"Merged","mergedAt":"2023-01-24T23:50:11Z","number":4356,"body":"To handle the autocomplete UI in the search experience:\r\n<img width=\"428\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213579720-c8b9e21c-9796-4ed7-ac63-732adef4476f.png\">\r\n<img width=\"415\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213579760-655daf89-dbb6-4fb8-9403-1de142dc041f.png\">\r\n","mergeCommitSha":"ec4e0f9782cdddd41000891f13fdffb23d6df10a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4356","title":"Search suggestions API","createdAt":"2023-01-19T22:49:43Z"}
{"state":"Merged","mergedAt":"2023-01-20T18:26:52Z","number":4357,"body":"This calls a GCP service that I am still figuring out where to check into the tree. There are 3 todos in the client, but think this includes your feedback @pwerry.","mergeCommitSha":"bee9c47b8f53b81c1f0d3d076ce50d665beb80bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4357","title":"Mobile chat","createdAt":"2023-01-19T22:57:28Z"}
{"state":"Merged","mergedAt":"2023-01-19T23:20:01Z","number":4358,"body":"```\r\nThread 0 Crashed::  Dispatch queue: com.apple.main-thread\r\n0   libswiftCore.dylib            \t       0x1b6c45fa0 closure #1 in closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:) + 380\r\n1   libswiftCore.dylib            \t       0x1b6c45fa0 closure #1 in closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:) + 380\r\n2   libswiftCore.dylib            \t       0x1b6c45ce4 closure #1 in closure #1 in _assertionFailure(_:_:file:line:flags:) + 200\r\n3   libswiftCore.dylib            \t       0x1b6c45adc closure #1 in _assertionFailure(_:_:file:line:flags:) + 212\r\n4   libswiftCore.dylib            \t       0x1b6c4563c _assertionFailure(_:_:file:line:flags:) + 236\r\n5   Unblocked Video               \t       0x1041e5104 internalInfoCallback(_:axElement:notification:cfInfo:userData:) + 732\r\n6   Unblocked Video               \t       0x1041e4dfc @objc internalInfoCallback(_:axElement:notification:cfInfo:userData:) + 84\r\n```\r\n\r\nAXSwift is at fault here. The crash only seems to happen when using the more advanced observer callback method, so I removed it.","mergeCommitSha":"81947f8ba7db4953397b6539007b32f64e0cb3d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4358","title":"Fix potential recording crash","createdAt":"2023-01-19T23:05:53Z"}
{"state":"Merged","mergedAt":"2023-01-20T00:12:58Z","number":4359,"mergeCommitSha":"164f032c36b338ac38ced52efe87d59a13a2ae8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4359","title":"Fix bug where shortcut key needs to be pressed twice to open hub","createdAt":"2023-01-20T00:10:48Z"}
{"state":"Merged","mergedAt":"2022-03-02T20:45:41Z","number":436,"body":"API completely re-written.\r\n\r\nThe key idea behind this is that the service is now maintaining channel state, including the list of participants and their roles. To create a channel, the client calls `/videoChannels/{channelID}/join`, where `channelID` is a UUID the client generates. The response contains the Agora identity and token that the client should use to join the channel. The next participant that comes along will already have the `channelID`, and when they join they are added to that VideoChannel's participant list.\r\n\r\nWhen updates to the video channel state occur, either by adding participants or by putting it in \"recording\" mode, the push channel will receive an update.\r\n\r\nRecordings are a top level entity (rather than a child of VideoChannel) because they are referenced in other parts of the system, like Messages","mergeCommitSha":"3bb75684367c76c7858859c190b8c0498cadbdb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/436","title":"Video chat recording API","createdAt":"2022-03-01T00:21:18Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:25:01Z","number":4360,"body":"We currently re-focus a tree view whenever a selected item is re-rendered in that view.  This was added here: https://github.com/NextChapterSoftware/unblocked/pull/1036.\r\n\r\nThis seems wrong, and is causing bugs.  Scrolling around on views can cause the view focus to change.  I can't see why we need this feature, I think we should remove it.","mergeCommitSha":"bfdb52178722c85b76fac5abcec89a02474f8600","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4360","title":"Remove auto-focus of VSCode tree items","createdAt":"2023-01-20T00:11:59Z"}
{"state":"Merged","mergedAt":"2023-01-20T01:24:44Z","number":4361,"body":"before:\r\n<img width=\"790\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213594602-fc754f57-0d73-4aed-932d-ce92957bdd2b.png\">\r\n\r\n\r\nafter:\r\n<img width=\"790\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/213594648-bd81a0d3-e298-4fd8-8090-9e77dc30a0a5.png\">\r\n\r\n","mergeCommitSha":"96b32a62b9ef4a9898dba3a59bd52fc4cf41dfd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4361","title":"Resize insight summary for unread dot spacing","createdAt":"2023-01-20T00:48:42Z"}
{"state":"Merged","mergedAt":"2023-01-25T21:02:13Z","number":4362,"body":"fixes https://linear.app/unblocked/issue/UNB-845/consume-github-app-client-provider-in-pr-ingestion\r\nfixes https://linear.app/unblocked/issue/UNB-844/introduce-enterprise-github-app-api-factory\r\nfixes https://linear.app/unblocked/issue/UNB-843/introduce-enterprise-github-app-provider-and-get-pr-ingestion-working","mergeCommitSha":"ce25e221e29cdfa700b61249b88f2b3a1426222e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4362","title":"Introduce Enterprise GitHub App factory","createdAt":"2023-01-20T03:13:38Z"}
{"state":"Closed","mergedAt":null,"number":4363,"mergeCommitSha":"d1c0074f32cd9df46832ed10323c2066eefb2d51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4363","title":"Enable GHE in PROD","createdAt":"2023-01-20T06:09:32Z"}
{"state":"Merged","mergedAt":"2023-01-20T14:10:59Z","number":4364,"mergeCommitSha":"83b29ab9f575bd7ed82f2d8ca49bfb6e7b22ed9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4364","title":"Fix multipart upload","createdAt":"2023-01-20T14:10:24Z"}
{"state":"Merged","mergedAt":"2023-01-20T17:59:03Z","number":4365,"mergeCommitSha":"5bfc7139a28d81cc1fb3faa41c31447b585f9695","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4365","title":"Cleanup cdk code","createdAt":"2023-01-20T17:58:35Z"}
{"state":"Merged","mergedAt":"2023-01-20T20:01:41Z","number":4366,"mergeCommitSha":"0ae147f35de6da36dd7710ae8be7de1928071a7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4366","title":"Mobile chat","createdAt":"2023-01-20T18:42:17Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:37:33Z","number":4367,"body":"Follow up to https://github.com/NextChapterSoftware/unblocked/pull/4336\r\n\r\nUpdates OpenPullRequestInfo to allow for panel metadata to be updated after PR is loaded.","mergeCommitSha":"8af9e1ef79df60a9915710b2f44c3b862ded61e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4367","title":"VSCode support for opening PR from Hub","createdAt":"2023-01-20T18:57:37Z"}
{"state":"Open","mergedAt":null,"number":4368,"body":"For navigation amongst clients. Not sure if there are other links that are relevant for these models(?)","mergeCommitSha":"a19b1b100399218301a33ca55d61f5db57b249f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4368","title":"Add links to Topic and TeamMember","createdAt":"2023-01-20T21:48:20Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:18:34Z","number":4369,"mergeCommitSha":"92ee5e90ef596bea982888c980dce575e56780c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4369","title":"Add vscode and dashboard capabilities to PR insights","createdAt":"2023-01-20T22:01:49Z"}
{"state":"Merged","mergedAt":"2022-03-01T17:25:33Z","number":437,"body":"Implements a locking mechanism for PR ingestion so that we're not ingesting dupes when multiple API service instances are running.","mergeCommitSha":"e494b2bb82ff948ad3cc061fa707271bc961c28b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/437","title":"Implement locks for PR ingestion and enable ingestion in DEV","createdAt":"2022-03-01T00:34:05Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:12:40Z","number":4370,"mergeCommitSha":"4e6854482c5a8301ae49d6cea9052a9007d97c30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4370","title":"Add cloudfront support for product agent","createdAt":"2023-01-20T22:09:36Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:53:47Z","number":4371,"mergeCommitSha":"29c42a72e33e237fcc42c101c424f9cec710cccb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4371","title":"Asset url requests should include product agent","createdAt":"2023-01-20T22:34:36Z"}
{"state":"Merged","mergedAt":"2023-01-20T23:56:10Z","number":4372,"body":"* Refactor PullRequestContextMenu for reuse across clients\r\n* Pass through `PullRequest.links.dashboardUrl` into clients\r\n* Rename `@chat` to `@threads` (large diff but no file changes except deleting `ChatSummaryView` files)\r\n* Cleanup spacing between unread/mention indicator and the title in the dashboard\r\n","mergeCommitSha":"370adad9a0ee6814589f07d4313a39c3174c5ae9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4372","title":"Refactor PullRequestContextMenu for reuse","createdAt":"2023-01-20T23:08:00Z"}
{"state":"Merged","mergedAt":"2023-01-21T03:01:57Z","number":4373,"body":"We're replacing it with the SearchInsight versions.","mergeCommitSha":"b4d21e0176ef9768a5eb95b04ac292fbb913f742","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4373","title":"Remove SearchService, SearchStore, and SearchModel","createdAt":"2023-01-20T23:17:31Z"}
{"state":"Merged","mergedAt":"2023-02-01T01:11:39Z","number":4374,"body":"Requires https://github.com/NextChapterSoftware/unblocked/pull/4373 to be merged first","mergeCommitSha":"0fec19509e1d17ad5b6ac49fae79002267c10c37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4374","title":"Delete SearchModel table","createdAt":"2023-01-20T23:24:02Z"}
{"state":"Merged","mergedAt":"2023-01-21T00:46:38Z","number":4375,"mergeCommitSha":"b9b996d8b16179e2a29c2aa4621bdcac6d3ca767","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4375","title":"Include PullRequestModel.updatedAt when indexing recency","createdAt":"2023-01-21T00:15:39Z"}
{"state":"Merged","mergedAt":"2023-01-25T21:08:34Z","number":4376,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1674169039214029","mergeCommitSha":"f8f5ff3d1dc19715b440b3cf8c3444a0a26efa65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4376","title":"GitHubInstallationMaintenanceJob runs every 12h instead of every 10s","createdAt":"2023-01-21T01:19:26Z"}
{"state":"Merged","mergedAt":"2023-01-23T18:10:22Z","number":4377,"mergeCommitSha":"65b9a0334e95bec15595282353a6b543b30acf4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4377","title":"Fix image max-width in pr layout","createdAt":"2023-01-23T17:57:17Z"}
{"state":"Merged","mergedAt":"2023-01-23T18:25:16Z","number":4378,"mergeCommitSha":"03b9c00e1402d21cef7ff4761fbd3f02c63be23d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4378","title":"update powerml code","createdAt":"2023-01-23T18:14:34Z"}
{"state":"Merged","mergedAt":"2023-01-23T19:18:51Z","number":4379,"mergeCommitSha":"202769cce6b56d136c818d8c6f359f871e312b18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4379","title":"update docs","createdAt":"2023-01-23T19:18:30Z"}
{"state":"Merged","mergedAt":"2022-03-03T19:29:34Z","number":438,"body":"Removing concept of ThreadParticipants. \r\n\r\nClients will always request ThreadParticipants / TeamMembers with the context of a thread.\r\n\r\nAdd list of TeamMember IDs to Thread.","mergeCommitSha":"dbbdb895cf29728c7ff2ffb826c263d75b932832","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/438","title":"Move ThreadParticipants to TeamMembers within Thread","createdAt":"2022-03-01T00:38:14Z"}
{"state":"Merged","mergedAt":"2023-01-23T20:05:17Z","number":4380,"body":"- Fix a tiny bug in CI (variable was being set for the wrong action)\r\n- Added a new Global Network policy to enforce stronger deny rules. (Deny traffic between pods, pod->kubeAPI)\r\n- Modified pod policy to use calico and only allow traffic from the service created as part of the same chart\r\n\r\n\r\nThis would probably break dev. I have tested as much as I could but the rest has to be done live. ","mergeCommitSha":"90a6dd622742d7d5c955efd27b0b2d0c1193c731","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4380","title":"Restrict pod network access dev","createdAt":"2023-01-23T19:55:08Z"}
{"state":"Merged","mergedAt":"2023-01-23T21:17:40Z","number":4381,"body":"- retry\r\n- Try retries\r\n","mergeCommitSha":"f9f763b4ea01b7e9fdcf84a7cfa102422e86c801","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4381","title":"AttemptRetries","createdAt":"2023-01-23T19:56:01Z"}
{"state":"Merged","mergedAt":"2023-01-25T21:42:33Z","number":4382,"body":"Webpack ts-loader is really slow.\r\n\r\nSpeed up build times by moving ts-build to esbuild-loader.\r\nThis required updating our exports to explicitly export types. https://github.com/webpack/webpack/issues/7378#issuecomment-683894656\r\n\r\nVSCode for DEV builds:\r\nBefore - 35659 ms for extension. 42207ms for webviews\r\nAfter - 5884ms for extension. 7862ms for webviews\r\n\r\nWeb for DEV builds:\r\nBefore - 27258 ms \r\nAfter - 8156 ms","mergeCommitSha":"286c4c1ec021e7e5f0046da6c2d1315bec01a868","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4382","title":"Add support for esbuild loader","createdAt":"2023-01-23T20:02:12Z"}
{"state":"Merged","mergedAt":"2023-01-24T09:40:25Z","number":4383,"body":"Our deployer user was missing the permission to create calico network resources. This has been applied to both Dev and prod. \r\n\r\nThe procedure to apply it is documented in the cluster setup README. ","mergeCommitSha":"a499b5e127804d42789a600cfef5c65bc6a3c2b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4383","title":"grant access to calico network policies to deployer user","createdAt":"2023-01-23T21:44:38Z"}
{"state":"Merged","mergedAt":"2023-01-23T21:50:46Z","number":4384,"mergeCommitSha":"72327fb082e192ecadf9299ab66ba815965e0279","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4384","title":"Update slack avatar","createdAt":"2023-01-23T21:50:34Z"}
{"state":"Merged","mergedAt":"2023-01-26T17:05:58Z","number":4385,"mergeCommitSha":"2c5a82cd6faeb3f3963f5b94872429e68d623a4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4385","title":"Increase the avatar cache size to accommodate larger images","createdAt":"2023-01-23T21:50:34Z"}
{"state":"Merged","mergedAt":"2023-01-23T23:33:08Z","number":4386,"body":"Per new designs:\r\n<img width=\"483\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214160124-e51ff832-2725-490e-97e9-35e262f4190f.png\">\r\n\r\nAdd ability to clear topics:\r\n<img width=\"337\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214160201-593ddc4b-a188-48e9-9bf7-68843137fab1.png\">\r\n\r\n<img width=\"1201\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214160249-f81ef734-b5c3-4fbb-a632-50fdf2ae76a0.png\">\r\n\r\nAdd ability to invite non-UB experts:\r\n<img width=\"430\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214160338-1800bda5-c1e1-4d74-9616-dc2bbb4a6603.png\">\r\n","mergeCommitSha":"44b328478e8214b11812b175c96dd0e71a094bdb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4386","title":"Update create topic flow ","createdAt":"2023-01-23T22:05:25Z"}
{"state":"Merged","mergedAt":"2023-01-23T22:30:49Z","number":4387,"mergeCommitSha":"4918fab9ed1bf0fe3530f2bb6f969c2448de064c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4387","title":"increase pusher dev cpu resources","createdAt":"2023-01-23T22:30:42Z"}
{"state":"Closed","mergedAt":null,"number":4388,"body":"see app.py. didn't check in the embedding as it is big and we don't have lfs turned on. no changes to production.","mergeCommitSha":"4db0430665b8cfd571c05c50ea393e738aad5427","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4388","title":"Creates a unblocked-bot GCP App Engine app","createdAt":"2023-01-24T04:23:14Z"}
{"state":"Merged","mergedAt":"2023-01-25T00:15:57Z","number":4389,"body":"Should fix a bug where we're not re-indexing when an archived thread is restored.","mergeCommitSha":"32f17aa1ff4bf1eeaf3351a491c4079ec5c216df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4389","title":"Move reindexing event to restoreThreadIfNeeded","createdAt":"2023-01-24T18:50:19Z"}
{"state":"Merged","mergedAt":"2022-03-01T01:06:44Z","number":439,"body":"My bad.","mergeCommitSha":"eb96a9c56b67c7365853758afea49995191bcdbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/439","title":"Sourcemarks API is authed","createdAt":"2022-03-01T00:47:44Z"}
{"state":"Merged","mergedAt":"2023-01-24T19:08:27Z","number":4390,"body":"Under Mine, the view will retrigger via pusher channel (existing behaviour, no-op)\r\nIn the Insights view, manually refetch insights\r\nIn the Thread view, don't redirect after deleting/restoring","mergeCommitSha":"53ec431d8f10e3afed927c1bb6f7b93e82e3dab2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4390","title":"Fix dashboard redirects after deleting threads","createdAt":"2023-01-24T18:56:03Z"}
{"state":"Merged","mergedAt":"2023-01-24T19:08:36Z","number":4391,"body":"<img width=\"390\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214383991-c6d9e6a0-ed0b-4aa4-baf8-ecfa1285e6a6.png\">\r\n","mergeCommitSha":"cc25007a6593087ae739ef714fa96f11ab4e3670","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4391","title":"Goodnight sweet badges","createdAt":"2023-01-24T18:59:25Z"}
{"state":"Merged","mergedAt":"2023-01-24T21:05:17Z","number":4392,"mergeCommitSha":"d92d5d037052a9109eb489b41e136f0aa4f68d4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4392","title":"Don't return team members unmarked as experts","createdAt":"2023-01-24T19:21:16Z"}
{"state":"Merged","mergedAt":"2023-03-13T21:55:18Z","number":4393,"body":"Fixes UNB-863","mergeCommitSha":"4d503361356f19b9e9013c10c960015049c67b4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4393","title":"UNB-863 Search query parameter needs size limit","createdAt":"2023-01-24T19:34:07Z"}
{"state":"Merged","mergedAt":"2023-01-24T22:35:21Z","number":4394,"mergeCommitSha":"53ecc413b2267855e3bdf7d92e5c6bdf6e263491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4394","title":"Encrypt s3 buckets","createdAt":"2023-01-24T22:34:14Z"}
{"state":"Merged","mergedAt":"2023-01-26T22:45:49Z","number":4395,"body":"Add experts to topic:\r\n<img width=\"315\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214445548-ed139e52-24e3-4d4f-ab55-b88e9960ea5e.png\">\r\n\r\nRemove experts from topic:\r\n<img width=\"384\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214445620-a62a9bfa-1c92-40e4-88ff-c7d269cad0e5.png\">\r\n<img width=\"484\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214445632-0b72d1a3-2dee-4723-b8c0-49dbd5af2d8f.png\">\r\n\r\nUpdate UI for new designs:\r\n<img width=\"1495\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/214446392-01ec9ec8-f7c3-4826-8873-55d5780f2a5c.png\">\r\n","mergeCommitSha":"1c358c6d2724e354631ab60ec67490ff12f428dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4395","title":"More topic management polish","createdAt":"2023-01-24T23:48:35Z"}
{"state":"Merged","mergedAt":"2023-01-25T18:59:44Z","number":4396,"body":"To enable quick look ups for search suggestions","mergeCommitSha":"fb7d27ea338e78eac419613cdc10310173754733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4396","title":"First pass at creating a search suggestion cache","createdAt":"2023-01-25T07:51:09Z"}
{"state":"Merged","mergedAt":"2023-01-25T09:55:19Z","number":4397,"body":"- Added a deny policy to block pods from accessing EC2 metadata endpoint (Deployed to dev and prod)\r\n- Added a new stack which creates a security group to allow traffic from CloudFront origin facing IPs using AWS managed prefix list \r\n\r\nOnce this has been deployed we need to set the annotation to use the new security group in helm charts. I will be making a separate PR for that. ","mergeCommitSha":"b74583c827f74d3cc5d0cc0a6a9384187aba13ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4397","title":"Network security for ALB and EC2 Kube nodes","createdAt":"2023-01-25T09:52:37Z"}
{"state":"Merged","mergedAt":"2023-01-25T18:22:30Z","number":4398,"body":"Related PR: https://github.com/NextChapterSoftware/unblocked/pull/4397\r\n\r\n- Added the security group created as part of the PR above to pod alb annotations. This should modify the ALB to only accept traffic coming from CloudFront origin facing IP addresses\r\n- Added pod security context to complement the container security context configuration that we added previously.","mergeCommitSha":"1abc6d24198bce5dce5e0e06894ae9b411107e4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4398","title":"Add new security group annotation to all external facing services. ","createdAt":"2023-01-25T10:48:19Z"}
{"state":"Merged","mergedAt":"2022-01-17T20:41:09Z","number":44,"body":"This is a PR to create our core CDK app which is used to deploy basic infrastructure components like VPC networks, databases etc to AWS. \r\n\r\n- Added class definition for environment JSON config files (`lib/build-config.ts`)\r\n- Created JSON config file for Dev in us-west-2 (`config/dev-us-west-2.json`)\r\n- Added Network Stack to create all required VPC/network configuration\r\n- Added Database Stack to deploy an RDS instance and allow access to it from EKS cluster\r\n- Updated README to include environment setup instructions and some useful resources\r\n\r\nAll components included in this PR have been deployed to `dev`. \r\n\r\nNext:\r\n- PR to add ECR Repo and IAM roles \r\n- Setup GitHub actions to CI/CD CDK changes to Dev","mergeCommitSha":"4b4d9357bf930a68635898d9370c4ddfbddaac84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/44","title":"AWS CDK project to deploy core infra components","createdAt":"2022-01-17T19:37:39Z"}
{"state":"Merged","mergedAt":"2022-03-01T17:07:49Z","number":440,"body":"#### Next\r\n- integrate `Unread` inserts/mutations with message creates/deletes\r\n- integrate `Unread` inserts/mutations with message reads/unreads\r\n- integrate `Unread` inserts/mutations with thread deletes\r\n- API model for fetching unreads → https://github.com/NextChapterSoftware/unblocked/pull/442\r\n- API model for marking threads as read/unread → https://github.com/NextChapterSoftware/unblocked/pull/442\r\n\r\n#### Motivation\r\nhttps://www.notion.so/nextchaptersoftware/Unread-Message-Synchronization-50af28a5fa734f59a670e0f04e15e938","mergeCommitSha":"ba5d29b79b7c2bfd294d4f559f8128d137143b87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/440","title":"Introduce ThreadUnread DB model","createdAt":"2022-03-01T01:26:40Z"}
{"state":"Merged","mergedAt":"2023-01-25T18:48:16Z","number":4404,"mergeCommitSha":"54ab6bfd10aacc7fb148cb207b04fd95a18c6b47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4404","title":"limit common","createdAt":"2023-01-25T18:48:10Z"}
{"state":"Merged","mergedAt":"2023-01-25T18:59:54Z","number":4405,"mergeCommitSha":"c135e6354026744fc7ffcb12f377db73dfd45189","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4405","title":"trying to fix alb security group in Dev.","createdAt":"2023-01-25T18:59:28Z"}
{"state":"Closed","mergedAt":null,"number":4406,"body":"Basic model for company.\r\n\r\nAdded migration to backfill companies but no entry point from admin console yet.\r\n\r\nCompany creation still needs to occur on Team creation. Requires refactoring `TeamDAO.new` call sites which may conflict with @richiebres changes? ","mergeCommitSha":"6165ab6f87e6eeafed3b6bb7c7724d50fd144743","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4406","title":"Backfill company","createdAt":"2023-01-25T19:04:18Z"}
{"state":"Merged","mergedAt":"2022-03-02T00:21:34Z","number":441,"body":"This allows making local changes to DataCacheStore safely.  The local data is treated as an overlay on the remove data.  There are two APIs added to DataCacheStore:\r\n\r\n`store.overlayValues(values) => unwindFn` -- this adds `values` to the local overlay values.  This returns a function that can be called to safely \"unwind\" the overlay.  The intention is that the unwind function can be called when a related API function fails, as the overlay values will no longer be relevant:\r\n\r\n```\r\nconst unwindFn = dogStore.overlayValues([newDog]);\r\ntry {\r\n   API.dogs.createDog(newDog);\r\n}\r\ncatch (error) {\r\n  unwindFn();\r\n}\r\n```\r\n\r\n`store.withOverlayValues(values, asyncFn)` -- this adds 'values' to the local overlay values while the asyncFn is operating.  If asyncFn throws an error, the values are unwound.  This is a terser form of the above:\r\n\r\n```\r\ndogStore.withOverlayValues([newDog], API.dogs.createDog(newDog))\r\n```","mergeCommitSha":"8535b740c00899ab4a2f7951a3d208876334cc9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/441","title":"Allow local changes to DataCacheStore","createdAt":"2022-03-01T05:04:30Z"}
{"state":"Merged","mergedAt":"2023-01-25T19:34:13Z","number":4412,"mergeCommitSha":"5b5d8ba451594ba8feba4483357e59856e703276","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4412","title":"Give search service access to redis","createdAt":"2023-01-25T19:19:54Z"}
{"state":"Closed","mergedAt":null,"number":4418,"mergeCommitSha":"23a41f094ac004b2e677acd6b69cd9cd2ff9aeb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4418","title":"test gradle dependnecies","createdAt":"2023-01-25T19:45:00Z"}
{"state":"Merged","mergedAt":"2022-03-01T17:54:20Z","number":442,"body":"New APIs\r\n- Get the unread status for all threads.\r\n- Get the unread status of one thread.\r\n- Update the unread status of one thread.\r\n","mergeCommitSha":"f6692c7461723dbc8513d538a06d8f03e8ac2221","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/442","title":"Introduce Unread API","createdAt":"2022-03-01T15:42:35Z"}
{"state":"Merged","mergedAt":"2023-01-25T20:55:49Z","number":4420,"mergeCommitSha":"56d68ef24b2dd4ee4de4f14326a6c0c676f58fd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4420","title":"Cache team members for search autocomplete","createdAt":"2023-01-25T20:14:55Z"}
{"state":"Merged","mergedAt":"2023-01-25T21:16:43Z","number":4422,"body":"I want to use these aliases to limit permissions granted to service accounts. Right now any service account with KSM access can read any key.\r\n\r\nAlready deployed to Dev and works as expected. ","mergeCommitSha":"a322ebc5bd350d8032e4e94377067e895b3ab4e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4422","title":"Add aliases for all S3 keys","createdAt":"2023-01-25T21:14:32Z"}
{"state":"Merged","mergedAt":"2023-01-25T21:18:22Z","number":4423,"body":"Matches https://github.com/organizations/NextChapterSoftware/settings/apps/dev-un-blocked","mergeCommitSha":"aa43178cb3c6c098dd9de9f17988cc4557f0ed77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4423","title":"Update GitHub App description","createdAt":"2023-01-25T21:15:26Z"}
{"state":"Merged","mergedAt":"2023-01-25T22:01:34Z","number":4424,"mergeCommitSha":"f9e8f168d1d8474a2889a80a05b673ab85396169","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4424","title":"Skip slack users for search autocomplete","createdAt":"2023-01-25T21:48:26Z"}
{"state":"Merged","mergedAt":"2023-01-25T23:36:11Z","number":4425,"mergeCommitSha":"d4f9c041154f62e7584119c6766810ec1d2ed4e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4425","title":"Skip bot users for search autocomplete","createdAt":"2023-01-25T22:06:54Z"}
{"state":"Merged","mergedAt":"2023-01-26T16:52:48Z","number":4426,"body":"Dennis was running into issues where VSCode was completely unresponsive.\r\nThere were no logs so difficult to reproduce.\r\n\r\nAdding logs at the API layer to narrow things down.","mergeCommitSha":"72c18d8f61f8b13602edb4cbf06d49451195823e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4426","title":"Add logs on fetch failures","createdAt":"2023-01-25T22:10:03Z"}
{"state":"Merged","mergedAt":"2022-03-01T18:03:25Z","number":443,"body":"cleanup\r\n- fix all Zally API lint rules\r\n- refactor ifModifiedSince params\r\n- refactor default responses\r\n- rename Annotation to Content","mergeCommitSha":"f1c5cb81fc6b3a74242b4c14ff89cbb95d0fa2de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/443","title":"API spec refactor and fixes","createdAt":"2022-03-01T15:48:42Z"}
{"state":"Merged","mergedAt":"2022-03-01T18:19:47Z","number":444,"body":"- Introduce `getThreadMessages`, to get messages for a single thread.\r\n- Fix bug in `getMessages` operation wasn't getting message for all threads.\r\n\r\nSee also:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/420#discussion_r816909600","mergeCommitSha":"4e05391c73849aba3fbc28d24209e6b8cc17b774","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/444","title":"Fix getMessages, introduce getThreadMessages","createdAt":"2022-03-01T16:38:42Z"}
{"state":"Merged","mergedAt":"2023-01-26T00:53:49Z","number":4440,"mergeCommitSha":"4b92da64783fa7a2828f1c3e1c015a9eba9f394b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4440","title":"Green checks for approved topics","createdAt":"2023-01-25T23:27:14Z"}
{"state":"Merged","mergedAt":"2023-01-25T23:31:52Z","number":4441,"mergeCommitSha":"575540d40c69fb536ae65a80574344daff3ccea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4441","title":"ignore video app","createdAt":"2023-01-25T23:31:28Z"}
{"state":"Merged","mergedAt":"2023-01-31T00:00:27Z","number":4442,"body":"This removes the code for the old, separated Insights and Pull Request explorer panels in VSCode.  This was dead code -- we were no longer using these webviews or providers, but we still built and shipped them.","mergeCommitSha":"3afce9d7ec6939924bad4da5ef284878a6cb51b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4442","title":"Remove old explorer insights / PR VSCode panels","createdAt":"2023-01-25T23:34:01Z"}
{"state":"Merged","mergedAt":"2023-01-25T23:44:11Z","number":4448,"body":"- Migrated the agora stack to S3 construct \r\n- Disabled it for now until we need it again.","mergeCommitSha":"da6b449dc74af74ca80d2d83965b4f8bf18b0803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4448","title":"disabling agora stack","createdAt":"2023-01-25T23:42:44Z"}
{"state":"Merged","mergedAt":"2022-03-01T19:50:02Z","number":445,"body":"This is the first component to implement the new design for traffic routing.\r\nSlack Thread: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1645825865398289\r\n\r\nHere's what's included in This PR\r\n- Added a new config object for the main CloudFront distro\r\n- Updated Dev and prod config JSONs with proper values\r\n- Added config to generate a certificate for domain/subdomain apex in each environment (prod has a subject Alt for getunblocked.com as well)\r\n- Added a new stack to create the CloudFront distro to be used for all of our web traffic\r\n- Added the stack to Dev and Prod in standard (us-east-1) region. This is to future proof our setup in case we need WAF. WAF for Cloudfront is only available in us-east-1\r\n\r\nI have deployed this change to dev and it works as expected.","mergeCommitSha":"f7222ce6584b13ed87b7ca494f6609fc825821b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/445","title":"Adding a new Main CloudFront Distro for all traffic","createdAt":"2022-03-01T18:39:19Z"}
{"state":"Merged","mergedAt":"2023-01-31T02:43:49Z","number":4456,"body":"- Identities are upserted, not ignored on insert\r\n\r\nFollow up changes:\r\n- [ ] migration to backfill identities\r\n- [ ] make `IdentityModel.externalTeamId` required\r\n- [ ] use `IdentityModel.upsert` whihc is safer/faster","mergeCommitSha":"1e6cb8e3d125eda6866a8eadb91551f033930906","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4456","title":"Duplicate team member created for GHE team","createdAt":"2023-01-26T01:11:50Z"}
{"state":"Merged","mergedAt":"2023-01-26T03:50:50Z","number":4459,"mergeCommitSha":"30141acb6529ef1f10e3dba2a5cb45be2645cf4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4459","title":"Dependency upgrades","createdAt":"2023-01-26T03:28:16Z"}
{"state":"Merged","mergedAt":"2022-03-01T22:17:39Z","number":446,"body":"Setup abstract data cache store for Keyed data.\r\nA general pattern where we want cached stores given an ID. E.g. messageStore for a threadID.\r\n\r\n","mergeCommitSha":"293cb1dc1c1d3d611c7ba65d7d7161465b70742a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/446","title":"Abstract Data Cache Store","createdAt":"2022-03-01T19:23:38Z"}
{"state":"Merged","mergedAt":"2022-03-03T00:49:47Z","number":447,"body":"Setup pusher channel to subscribe to messages for a threadID\r\n\r\nPlease rip this apart :)","mergeCommitSha":"13f07115e36b99a44cec4551934de71247931370","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/447","title":"Setup Message PusherService","createdAt":"2022-03-01T20:05:42Z"}
{"state":"Merged","mergedAt":"2023-01-26T20:17:11Z","number":4470,"body":"- Removed unused generic service accounts like `postgres-access` etc \r\n- Changed KMS permissions for all service accounts used by our core application to only access keys related to S3 buckets on which they have been authorized \r\n\r\n~Note: I wasn't able to lock down the kms-issuer account. It doesn't use a key with aliases which we could use in our policy. Good news is that cert-manager components run in their own isolated namespace. They do not have any publicly exposed services so we basically treat them as Kube control plane.~\r\n\r\nI have deployed these changes to Dev. I like to keep it in Dev for now and if all is good I will apply them to Prod. We can go ahead and merge this PR since EKS changes have to be applied locally and don't (can't) have CI/CD\r\n\r\nUpdate:\r\nChanged how we do the permissions based on this doc: https://docs.aws.amazon.com/kms/latest/developerguide/alias-authorization.html","mergeCommitSha":"8b5fd9bcc2bc2541f86ed15591110a2ad196d547","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4470","title":"Cleanup and lock down service accounts","createdAt":"2023-01-26T05:19:03Z"}
{"state":"Merged","mergedAt":"2023-01-26T05:56:32Z","number":4473,"body":"- chore(deps): update plugin io.gitlab.arturbosch.detekt to v1.22.0\r\n- update\r\n","mergeCommitSha":"6dac2a6fd324bf45550587df0a71ba7885b70d42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4473","title":"TryAnotherFix","createdAt":"2023-01-26T05:45:44Z"}
{"state":"Merged","mergedAt":"2023-01-26T19:13:57Z","number":4479,"body":"Needs a follow up PR to check the contents of the PR description so that we also ignore PRs with short descriptions.","mergeCommitSha":"d8c8f31f07a70dcf13ac22fc5d6829916a1bd7ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4479","title":"Dont map low signal insights to topics","createdAt":"2023-01-26T17:20:15Z"}
{"state":"Closed","mergedAt":null,"number":448,"mergeCommitSha":"3ace965b6dff8f5669e710796a4f8141c33879a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/448","title":"[WIP] Try to get message photos into api service","createdAt":"2022-03-01T20:11:11Z"}
{"state":"Merged","mergedAt":"2023-01-26T20:12:04Z","number":4480,"body":"Deleting walkthroughs from draft view was not hooked up.","mergeCommitSha":"7048d3f457e6a4dd0f0895255a1945dc7f67e65f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4480","title":"Delete WalkthroughDraft from compose","createdAt":"2023-01-26T17:21:53Z"}
{"state":"Merged","mergedAt":"2023-01-26T18:20:39Z","number":4481,"mergeCommitSha":"f88df6596cca484576e7e6ed0e50c2bda21283ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4481","title":"Restore video settings on next launch of video app","createdAt":"2023-01-26T18:00:20Z"}
{"state":"Merged","mergedAt":"2023-01-26T18:17:03Z","number":4482,"mergeCommitSha":"9ddf2cc7e5159fa666d373f6c7575601f35f5dec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4482","title":"Resilient glue job","createdAt":"2023-01-26T18:06:22Z"}
{"state":"Merged","mergedAt":"2023-01-26T18:25:41Z","number":4483,"body":"- Bert docker image\r\n- update\r\n","mergeCommitSha":"841e75b6fbd957a0f3b933cb0395eb929f6cd200","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4483","title":"FixdockerVersion","createdAt":"2023-01-26T18:25:34Z"}
{"state":"Merged","mergedAt":"2023-01-26T18:54:57Z","number":4484,"mergeCommitSha":"5f53103b0152bc4b9a977f64dd41e24632efc692","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4484","title":"update","createdAt":"2023-01-26T18:54:52Z"}
{"state":"Merged","mergedAt":"2023-01-26T20:07:19Z","number":4486,"body":"- Move away from deprecated package\r\n","mergeCommitSha":"716b8a0ea3cf770a41d6e820fc79e5a4226fa67b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4486","title":"react-dom-interactions is deprecated, move to recommended package","createdAt":"2023-01-26T19:13:36Z"}
{"state":"Merged","mergedAt":"2023-01-26T19:19:32Z","number":4487,"mergeCommitSha":"5a7b24163beb2bcc162b5cf1c87b5203d186e2f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4487","title":"do not update cdk image assets","createdAt":"2023-01-26T19:19:08Z"}
{"state":"Merged","mergedAt":"2023-01-26T19:27:39Z","number":4488,"mergeCommitSha":"1d612eb9ef94767a79aa889bb9809d0f3a1f9523","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4488","title":"update","createdAt":"2023-01-26T19:27:33Z"}
{"state":"Merged","mergedAt":"2022-03-01T22:13:55Z","number":449,"mergeCommitSha":"e5f0d7804092eff897bd3eb8fc4e5e3c5282a438","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/449","title":"No lettuce debug logging","createdAt":"2022-03-01T22:08:47Z"}
{"state":"Merged","mergedAt":"2023-01-26T20:11:49Z","number":4491,"body":"When active file manager changes to undefined, we should keep a reference.\r\n\r\nIf not, subsequent video walkthroughs may receive an old reference on launch.","mergeCommitSha":"2c25f31243d9744cad82b41de64478322388f38c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4491","title":"Keep reference to undefined file","createdAt":"2023-01-26T19:52:44Z"}
{"state":"Merged","mergedAt":"2023-01-26T22:43:26Z","number":4496,"body":"Being able to mark a pull request as archived lets us hide these insights when we detect they are low signal (or created by a bot if we decide to go that route).\r\n\r\nThis makes the pull request model similar to the thread model. I can see us eventually migrating pull requests to become threads so we can just copy these properties over when the time comes.","mergeCommitSha":"28b9803ecb25692f6cad70108ee71bf9374858cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4496","title":"Add ability to archive a pull request","createdAt":"2023-01-26T21:05:35Z"}
{"state":"Merged","mergedAt":"2023-01-26T22:39:09Z","number":4497,"body":"Cleaning up my workarea, sorry for the spam. This will bump the min version to 12.3 across all our build targets include the Mac build of the iOS/iPadOS **_experimential_** client.","mergeCommitSha":"7d8cae021115c2b943a3ea194ec3f003820f2353","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4497","title":"Removing a build error","createdAt":"2023-01-26T21:24:09Z"}
{"state":"Merged","mergedAt":"2023-01-26T22:13:50Z","number":4498,"mergeCommitSha":"4444909d57642b484e8beb643f0bee4a349308da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4498","title":"Don't set Topic.relevant or Topic.description if not provided by the client","createdAt":"2023-01-26T21:56:09Z"}
{"state":"Merged","mergedAt":"2023-01-30T18:08:55Z","number":4499,"body":"Pulls down all the PRs from the main Parse team repos and generates PowerML topics.","mergeCommitSha":"e9ddafc3f1e87906b4016b7b00810f0db679be21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4499","title":"Topic/System/ML Open Source Tests","createdAt":"2023-01-26T21:59:23Z"}
{"state":"Merged","mergedAt":"2022-01-17T21:35:58Z","number":45,"body":"- Added a stack for compute related resources. Added ECR repo to the new stack. \r\n- According to EKS docs we don't need to create any extra IAM roles. Our EKS cluster should be able to pull images from this repo out of the box. \r\n\r\nI have deployed this to Dev. We should now have all basic components needed to build and deploy images to ECR-EKS.","mergeCommitSha":"9619e4d9f05f4ca72dba0ef49d47c7cd38348f47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/45","title":"Added compute stack to create ecr repo","createdAt":"2022-01-17T21:05:07Z"}
{"state":"Merged","mergedAt":"2022-03-02T19:40:01Z","number":450,"body":"* Add shared/ reusable components for drag and drop functionality\r\n    * Leverages the `react-dnd` library https://react-dnd.github.io/react-dnd/docs/overview\r\n    * See examples in storybook for how they could be used (the Resortable List story is what we'll be using for the PR creation UI)\r\n\r\n### Key components\r\n* `Draggable` - wrapper component to make an element draggable\r\n* `Droppable` - wrapper component to make an element be capable of having a draggable element dropped into it\r\n* `DragAndDropList` - list of items that are both draggable and droppable, i.e. to support resorting functionality \r\n    * takes a generically typed list as well as a template function for each item ","mergeCommitSha":"e57ebdb49bf1fe30a30ec47a554da1b1ae5bd6f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/450","title":"Add library/components for drag and drop","createdAt":"2022-03-01T22:34:36Z"}
{"state":"Merged","mergedAt":"2023-01-27T18:29:26Z","number":4500,"body":"Fixes https://linear.app/unblocked/issue/UNB-876/add-x-content-type-options=nosniff-to-all-responses\r\n\r\nOWASP item 3.3.1","mergeCommitSha":"20c06f2cc99a31b8efb922742681a8bed8af2b47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4500","title":"Add X-Content-Type-Options:nosniff header to all responses","createdAt":"2023-01-26T22:02:07Z"}
{"state":"Merged","mergedAt":"2023-01-26T22:30:36Z","number":4501,"body":"I was wondering why my original change did not suppress the issue. It was a bad input name. I'll do the proper fix on the action runner side. \r\n\r\n```\r\n[Start self-hosted EC2 runner](https://github.com/NextChapterSoftware/unblocked/actions/runs/4019054784/jobs/6905431088#step:5:1)\r\nUnexpected input(s) 'github-action-runner-version', valid inputs are ['aws_access_key_id', 'aws_secret_access_key', 'aws_region', 'aws_iam_role_arn', 'github_token', 'github_action_runner_version', 'ec2_instance_type', 'ec2_ami_id', 'ec2_instance_iam_role', 'ec2_instance_tags', 'ec2_instance_ttl', 'ec2_security_group_id', 'ec2_subnet_id', 'ec2_spot_instance_strategy']\r\n```","mergeCommitSha":"3204b5c42671f96eaae88046e25ba55ef5ca91e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4501","title":"temp fix for the annoying ec2 runner failures","createdAt":"2023-01-26T22:04:08Z"}
{"state":"Merged","mergedAt":"2023-01-27T23:35:18Z","number":4502,"body":"Fixes https://linear.app/unblocked/issue/UNB-877/add-strict-transport-security-max-age=15724800-includesubdomains-to\r\n\r\nOWASP item 14.4.5","mergeCommitSha":"7693a4c199e1df1551aa3dae95d227cf3f0b5a44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4502","title":"Add Strict-Transport-Security header to all responses","createdAt":"2023-01-26T22:07:36Z"}
{"state":"Merged","mergedAt":"2023-01-30T19:11:03Z","number":4504,"body":"This will help us keep track of which team members are bots. \r\n\r\nNOTE: This PR doesn't check usernames for identities that are not labelled as bots but are, in fact, bots. I think we can add those later? Unless someone can suggest a heuristic to apply (ex names ending in `bot`)","mergeCommitSha":"b98dee5e22330120bed07b1cf6e54d7869b94bd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4504","title":"Add IdentityModel.isBot property","createdAt":"2023-01-26T23:40:41Z"}
{"state":"Merged","mergedAt":"2023-01-30T22:45:52Z","number":4505,"mergeCommitSha":"31334f63fd9e8c72504705e9050a2c673a4c97ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4505","title":"Dont map topic to pull request if archived","createdAt":"2023-01-26T23:53:36Z"}
{"state":"Merged","mergedAt":"2023-01-27T23:36:15Z","number":4507,"body":"Adapted from https://github.com/NextChapterSoftware/unblocked/pull/4507\r\n\r\nFixes https://linear.app/unblocked/issue/UNB-890/enforce-openapi-max-size-constraints-in-ktor-codegen\r\n\r\nOWASP 5.4.3","mergeCommitSha":"292353a8651394298357c1076e3eb40b1c6699a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4507","title":"Add string length validation to codegen","createdAt":"2023-01-27T00:06:13Z"}
{"state":"Closed","mergedAt":null,"number":4508,"body":"Fine to archive now since we no longer map archived threads to topics. This way its possible to have these insights appear in search results but not topic pages.","mergeCommitSha":"fe385a7bdc81b895c166bd70e799070b6368328b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4508","title":"Add back indexing of archived threads","createdAt":"2023-01-27T00:15:06Z"}
{"state":"Merged","mergedAt":"2022-03-03T23:13:13Z","number":451,"body":"I moved the proto generation to api service's `build.gradle.kts` so that we don't need to specify the output folder in both the api service and common `build.gradle.kts`.\r\n\r\nNext PR will update `GitHubFlavouredMarkdownTransformer` to parse the markdown and generate the correct message blocks.","mergeCommitSha":"1e2edf5a54869bc59a03ad8b1dbe025905040da6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/451","title":"Add MessageBody proto to API service","createdAt":"2022-03-01T23:35:00Z"}
{"state":"Merged","mergedAt":"2023-01-27T05:39:59Z","number":4514,"body":"- chore(deps): update plugin com.google.protobuf to v0.9.2\r\n- Fix protobuf\r\n","mergeCommitSha":"b6da31e9efa98ef61192c279c5f6391165f444be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4514","title":"FixProtobuf","createdAt":"2023-01-27T04:51:52Z"}
{"state":"Merged","mergedAt":"2023-01-27T05:08:13Z","number":4515,"mergeCommitSha":"2fd77e95c7957bd9cc38217a86f0ccca21740ef3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4515","title":"Renovate should never rebase unless manually asked","createdAt":"2023-01-27T05:07:08Z"}
{"state":"Merged","mergedAt":"2023-01-27T19:50:56Z","number":4516,"mergeCommitSha":"e202e83ac4b03b8530ec07df8f66f92186174611","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4516","title":"Make search button clickable in whitespace","createdAt":"2023-01-27T05:27:15Z"}
{"state":"Merged","mergedAt":"2022-03-02T04:50:08Z","number":452,"body":"This will be for SourceMarks.","mergeCommitSha":"2ed2050684c42551bea08e4939929bb97e21a72b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/452","title":"Add SourceMarkBlock to Message.proto","createdAt":"2022-03-02T00:06:06Z"}
{"state":"Merged","mergedAt":"2023-01-28T00:04:23Z","number":4521,"mergeCommitSha":"3bfef7574c41f24fdddb71e646f8f9c72875e0e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4521","title":"[BREAKS API ON MAIN] Remove unused API","createdAt":"2023-01-27T18:12:13Z"}
{"state":"Merged","mergedAt":"2023-01-27T22:36:42Z","number":4523,"body":"- fix(deps): update opentelemetryversion\r\n- Fix opentelemetry\r\n","mergeCommitSha":"0e2fb49ae338e57d59879c6e8246d7a2e1c3a2ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4523","title":"FixOpenTelemetry","createdAt":"2023-01-27T20:37:16Z"}
{"state":"Closed","mergedAt":null,"number":4524,"mergeCommitSha":"dde95002cbb47ac52c534b6943025c9653401c03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4524","title":"WIP(do NOT merge): trying the new per environment secrets","createdAt":"2023-01-27T20:37:24Z"}
{"state":"Merged","mergedAt":"2023-01-29T04:38:22Z","number":4525,"mergeCommitSha":"0a7f2d246fa395913555f5adc5ec63b608cf6290","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4525","title":"Remove the mac build of the mobile app.","createdAt":"2023-01-27T21:04:50Z"}
{"state":"Merged","mergedAt":"2023-01-28T00:00:12Z","number":4526,"mergeCommitSha":"30e96aa3f1ff0ef2dbe293c122ac8d13098c342c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4526","title":"Return search suggestions when topic and or team members are in the request","createdAt":"2023-01-27T21:36:15Z"}
{"state":"Merged","mergedAt":"2023-01-28T00:34:05Z","number":4527,"body":"Basic support for themes","mergeCommitSha":"a3a203bbf04b51575ca464e4dd4870236dc23be4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4527","title":"Pluugin webview theme","createdAt":"2023-01-27T23:01:59Z"}
{"state":"Merged","mergedAt":"2023-01-28T00:31:24Z","number":4528,"body":"OWASP 14.2.5\r\n\r\nFixes https://linear.app/unblocked/issue/UNB-875/generate-an-security-bill-of-materials-sbom-for-3rd-party-libs","mergeCommitSha":"e19cb445efe8cbde71d040b0547d0476aedb9e8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4528","title":"Add SBOM generator","createdAt":"2023-01-27T23:31:29Z"}
{"state":"Merged","mergedAt":"2023-01-27T23:44:34Z","number":4529,"mergeCommitSha":"b5da977e20865cb17940fdf54b506fcfdbb595b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4529","title":"Add vulenrability alerts","createdAt":"2023-01-27T23:44:29Z"}
{"state":"Merged","mergedAt":"2022-03-02T01:07:47Z","number":453,"body":"Need to diagnose why PR ingestion isn't happening in DEV","mergeCommitSha":"5e592ca6c144415efaafea23ec2325f4fb489c1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/453","title":"Add logging","createdAt":"2022-03-02T00:32:56Z"}
{"state":"Merged","mergedAt":"2023-01-28T00:01:28Z","number":4532,"body":"Reverts NextChapterSoftware/unblocked#4507\r\n\r\nSomething is broken in prod. We need a dev only deployment to debug this properly","mergeCommitSha":"8c6c2c2e298edc974133848f1c820c9135acb69d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4532","title":"Revert \"Add string length validation to codegen\"","createdAt":"2023-01-28T00:01:19Z"}
{"state":"Merged","mergedAt":"2023-01-30T23:09:51Z","number":4534,"body":"Going to queue this up again but remove the constraint checks and just log instead until we track down all the violations","mergeCommitSha":"67b85635c4da48745ef49c83b4645af52ef1fef1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4534","title":"Add string and int validation to OpenAPI codegen. Log instead of throw until we find all the gremlins","createdAt":"2023-01-28T00:10:04Z"}
{"state":"Merged","mergedAt":"2023-01-28T00:36:49Z","number":4535,"mergeCommitSha":"ea3cd5459e6a592d861eb28cb17938df3ad1f5f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4535","title":"Fix lint failure in main","createdAt":"2023-01-28T00:21:40Z"}
{"state":"Merged","mergedAt":"2023-01-30T22:29:26Z","number":4537,"body":"A skeleton providing the basics for a JetBrains plugin:\r\n\r\n* Plugin app skeleton\r\n* Build infrastructure\r\n* Webview rendering helpers\r\n\r\nThe gradle command to build the project runs a child npm process to create webview bundles, and then includes the bundles as resources in the plugin's jarfile.  At runtime those resources are loaded and rendered.","mergeCommitSha":"bd4acb7356e1e8453a47c2f5d8810a34b97b5ce1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4537","title":"JetBrains plugin skeleton","createdAt":"2023-01-28T00:35:21Z"}
{"state":"Merged","mergedAt":"2023-01-28T01:10:34Z","number":4538,"mergeCommitSha":"ef48119a1e3a10b653a8c95ee97cd3b1bc7a458d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4538","title":"Update slate","createdAt":"2023-01-28T00:44:43Z"}
{"state":"Merged","mergedAt":"2022-03-02T17:06:20Z","number":454,"body":"Cleanup","mergeCommitSha":"262cd3c9ddd3ad4933605fadc9eb1319494cd92e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/454","title":"Use consistent syntax in api definition file","createdAt":"2022-03-02T16:25:02Z"}
{"state":"Merged","mergedAt":"2023-01-28T02:28:52Z","number":4544,"body":"- chore(deps): update dependency babel-loader to v9\r\n- update babel\r\n","mergeCommitSha":"8eb0e5df8b29d21e3992691572f6d60bc38e2cd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4544","title":"FixBabel","createdAt":"2023-01-28T02:19:28Z"}
{"state":"Merged","mergedAt":"2022-03-02T19:40:27Z","number":455,"body":"This will ensure we're not creating duplicate records for a (`Thread`,  `TeamMember`)","mergeCommitSha":"d4dfd25d9920f56945fedb22ba4d85128314f7c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/455","title":"Add unique index for ThreadUnreadModel","createdAt":"2022-03-02T19:32:15Z"}
{"state":"Merged","mergedAt":"2023-01-28T04:04:57Z","number":4557,"mergeCommitSha":"d3b68bca94bfa3e9d6f3bb153b61b24d98f4ad91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4557","title":"update ktlint","createdAt":"2023-01-28T03:46:13Z"}
{"state":"Merged","mergedAt":"2022-03-02T22:47:30Z","number":456,"body":"Ignore everything except the change to rules-config.conf\r\n\r\nThe rest is because the irony of linter classes not being linted was too much for me to bear","mergeCommitSha":"bec434f4b5f148c0e0fd63e566b84a6779e71945","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/456","title":"Add Id case rule to zally config","createdAt":"2022-03-02T21:07:36Z"}
{"state":"Merged","mergedAt":"2023-01-28T20:16:20Z","number":4562,"body":"Github's dependabot is largely designed to work with typescript and node (beyond me, why).\r\nThey have consistently staved off attempts at adding recent support for gradle version catalogs etc.\r\n\r\nThis is to address that.","mergeCommitSha":"6239f1c1ce18c41b7f584af762b6e39fa7b499d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4562","title":"Store gradle dependency graph to Dependabot.","createdAt":"2023-01-28T20:16:02Z"}
{"state":"Merged","mergedAt":"2023-01-28T20:18:27Z","number":4563,"mergeCommitSha":"dfb3fec060ca12c280f09a95d8732c690ea118ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4563","title":"update","createdAt":"2023-01-28T20:18:20Z"}
{"state":"Merged","mergedAt":"2023-01-28T20:44:38Z","number":4564,"mergeCommitSha":"c248b158a6fdfd607a646fb6abadf8a592bfd6af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4564","title":"Github dependency","createdAt":"2023-01-28T20:30:51Z"}
{"state":"Merged","mergedAt":"2023-01-30T19:32:37Z","number":4568,"body":"Adding `fsgroup` annotation so applications running as non-root could read cert files","mergeCommitSha":"c8012654f7f06326a01fe6c7f10a9b6a2aa3bf12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4568","title":"Fix cert file permissions","createdAt":"2023-01-30T19:25:56Z"}
{"state":"Merged","mergedAt":"2023-01-30T21:17:34Z","number":4569,"mergeCommitSha":"13acc4dfe3c16507e2455391b8062af277def4d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4569","title":"Add isBot to IdentityStore.insertUnique arg list","createdAt":"2023-01-30T20:50:47Z"}
{"state":"Merged","mergedAt":"2022-03-03T17:20:39Z","number":457,"body":"- Added each static site bucket to cloudfront with a pre-defined path pattern\r\n- Added a lambda function to replace our WAF IP filters. This is for Dev phase only and will be removed later\r\n- Added a lambda edge function to correct request paths before sending them to S3 origin\r\n- Modified config object and environment json files to add configs needed for above changes\r\n\r\n**Note 1:** existing CloudFront distros defined in `static-site` stack will be removed once we make sure everything is working as expected. We will only have one active CloudFront disto afterwards\r\n**Note 2:** Edge IP filter is more of a hack. We will add WAF to our primary CloudFront in each environment once the existing old ones have been removed. Scoping IP filters to sub-paths is tricky and lambda edge method is much easier.\r\n\r\nIn my next PR I'll be adding ALB configs. Right now the root of our site is pointing at API service without an ALB. https://dev.getunblocked.com/__health\r\nAlso you can try dashboard (Jeff needs to fix a few things in it) at https://dev.getunblocked.com/dashboard/\r\n\r\nThis has been deployed to Dev and works as expected.","mergeCommitSha":"05036e8a7c6371a65b7ef041cdb9655f7bf710eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/457","title":"Add each static site as a path on CloudFront","createdAt":"2022-03-02T21:57:10Z"}
{"state":"Merged","mergedAt":"2023-02-01T22:01:22Z","number":4570,"body":"<img width=\"1495\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/215614568-2546a09b-476f-4c0f-8334-12cadf9bee09.png\">\r\n<img width=\"1485\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/215614630-2ae52f3d-bbb1-48f9-89e9-b219726813f5.png\">\r\n<img width=\"1486\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/215614705-5edb5292-967d-4fc0-b17a-717c8085dd51.png\">\r\n<img width=\"1489\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/215614757-9eb12f93-57d0-4483-ad46-162e8ec39199.png\">\r\n\r\n\r\nTODO:\r\n* Text autocomplete (i.e. text suggestions for the input query) -- missing backend impl, should be no-op on the client\r\n* Slack-like keyboard behaviour \r\n* Add search to mobile viewports\r\n* Add new search API to vscode\r\n* Redirect if there is 1:1 search query to topic/team member view\r\n","mergeCommitSha":"7959ccad3dca57c94000adc82c2a2b04262a1f20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4570","title":"Integrate searchV2 into dashboard ","createdAt":"2023-01-30T22:53:55Z"}
{"state":"Merged","mergedAt":"2023-01-30T23:46:52Z","number":4571,"mergeCommitSha":"eaba5b14562aa0e399050a922e7b24b11fc9b644","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4571","title":"Migration should list bots from database","createdAt":"2023-01-30T23:17:51Z"}
{"state":"Merged","mergedAt":"2023-01-31T22:05:09Z","number":4572,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1673303588628259","mergeCommitSha":"4ce1b7954ab016755500e1667988b9ed16fdf443","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4572","title":"[API] Add Identity.isBot","createdAt":"2023-01-30T23:28:49Z"}
{"state":"Merged","mergedAt":"2023-01-31T17:04:04Z","number":4573,"body":"Remove the client feature flag for video","mergeCommitSha":"bb7e8c889897062a503a278267d34fe61397e2e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4573","title":"Video for all","createdAt":"2023-01-30T23:33:17Z"}
{"state":"Merged","mergedAt":"2023-01-31T00:00:09Z","number":4574,"body":"Pete was the only person that voted, so he wins","mergeCommitSha":"19d0b5d3a638e8196d24f3ab0cd94ebf2f789ba5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4574","title":"Rename jetbrains plugin package","createdAt":"2023-01-30T23:53:04Z"}
{"state":"Merged","mergedAt":"2023-01-31T01:01:58Z","number":4575,"mergeCommitSha":"0dd0e272c50c753071455c60a95377545d8f6ba3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4575","title":"Fix title editing","createdAt":"2023-01-31T00:54:31Z"}
{"state":"Merged","mergedAt":"2023-01-31T01:13:45Z","number":4576,"mergeCommitSha":"5cac287424875366ef31342cc0cfc66d64afd2a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4576","title":"Log number of pull requests found for bots","createdAt":"2023-01-31T00:59:34Z"}
{"state":"Merged","mergedAt":"2023-01-31T17:16:24Z","number":4577,"body":"Removes client-side references to `FeatureSlackIntegration` and `FeatureVideoWalkthrough` -- meaning this UI will be enabled for all users once this code is merged.\r\n\r\n* note that both of these flags were only referred to in the dashboard ","mergeCommitSha":"6898b6ee1ab24468a625e700d3673a3a75ceea8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4577","title":"Remove video and slack feature flags","createdAt":"2023-01-31T01:21:13Z"}
{"state":"Merged","mergedAt":"2023-01-31T03:03:16Z","number":4578,"body":"Just hides the config from admin web to cleanup.\r\n\r\nClients will still receive the deprecated config value.\r\n\r\nRelated to https://github.com/NextChapterSoftware/unblocked/pull/4577","mergeCommitSha":"6c8632a5bd1d06509074db5ce079764a2ec8780b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4578","title":"Ability to deprecate client config","createdAt":"2023-01-31T01:55:22Z"}
{"state":"Merged","mergedAt":"2023-01-31T03:59:13Z","number":4579,"mergeCommitSha":"ed915a08b5fee2c227d6f1768abf3095dfbef775","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4579","title":"Minor SCM / provider refactor","createdAt":"2023-01-31T03:16:32Z"}
{"state":"Merged","mergedAt":"2022-03-03T21:01:13Z","number":458,"body":"Update VSCode to support threads and send messages to API service.\r\n\r\nIntegrates Login flow into sidebar when unauthenticated.\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/156472871-a16f4e44-abbc-45ee-9467-6f128e21c265.mp4\r\n\r\n\r\n","mergeCommitSha":"9c91c7aba0022242d56dd4578fafca33206076dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/458","title":"VSCode Thread Sidebar & MessageView","createdAt":"2022-03-02T22:51:02Z"}
{"state":"Merged","mergedAt":"2023-01-31T05:19:54Z","number":4580,"body":"GHE PR ingestion failing because rate limit endpoint 404:\n```\ni.k.c.p.ClientRequestException: Client request(GET https://ghe.secops.getunblocked.com/api/v3/rate_limit) invalid: 404 Not Found.\n```\n\nAs GHE response says, this is because rate limiting may not be configured at all.\n```json\n{\n  \"message\": \"Rate limiting is not enabled.\",\n  \"documentation_url\": \"https://docs.github.com/enterprise-server@3.7/rest/reference/rate-limit#get-rate-limit-status-for-the-authenticated-user\"\n}\n```","mergeCommitSha":"fa9759353bce92364d85fd413fd08bfc99440780","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4580","title":"PR ingestion works when no rate limit has been configured by SCM","createdAt":"2023-01-31T05:02:53Z"}
{"state":"Merged","mergedAt":"2023-01-31T05:39:46Z","number":4582,"mergeCommitSha":"5c10753b637cc35a68e9f3aedf6dc08a3b300f98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4582","title":"Add some tests for migrating bot pull requests","createdAt":"2023-01-31T05:08:35Z"}
{"state":"Merged","mergedAt":"2023-01-31T07:05:14Z","number":4584,"mergeCommitSha":"9ba17dacf6a66aec2aa43275d0629897c17e11d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4584","title":"Convert sized iterable to list","createdAt":"2023-01-31T06:31:46Z"}
{"state":"Merged","mergedAt":"2023-01-31T06:40:55Z","number":4585,"body":"Falco is complaining about privileged containers even though they are not. This change should hopefully suppress the false positive. ","mergeCommitSha":"80826698f32cb83ebe31b5dcc7f405a503837562","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4585","title":"trying to supress a false positive in falco","createdAt":"2023-01-31T06:33:29Z"}
{"state":"Merged","mergedAt":"2023-01-31T07:40:51Z","number":4586,"body":"Add per team model endpoints","mergeCommitSha":"e1660c1a4d5621855be8c235dc68b0e60865b14d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4586","title":"Add model endpoints","createdAt":"2023-01-31T07:30:45Z"}
{"state":"Merged","mergedAt":"2023-01-31T09:08:53Z","number":4588,"mergeCommitSha":"c414c35b5263c3b0caf97ae2e4ba4ac890385378","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4588","title":"change instancer type","createdAt":"2023-01-31T09:08:48Z"}
{"state":"Merged","mergedAt":"2022-03-03T00:34:45Z","number":459,"body":"While we're at it...","mergeCommitSha":"b5b9730a1424a09de25bba0383245da4d98b9296","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/459","title":"No more Ids with capital 'D'","createdAt":"2022-03-02T23:49:57Z"}
{"state":"Merged","mergedAt":"2023-01-31T17:21:16Z","number":4590,"body":"Not working due to unstable comparator function.\nhttps://stackoverflow.com/questions/8327514/comparison-method-violates-its-general-contract\n\nUnclear what's happening. Seems to be affecting new teams, or newly backfilled team members.","mergeCommitSha":"6b57e72b1544c0514734bc0cb6f248135293c3e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4590","title":"Temporarily disable team member sort","createdAt":"2023-01-31T17:13:21Z"}
{"state":"Merged","mergedAt":"2023-01-31T18:24:40Z","number":4591,"mergeCommitSha":"03dee3880c184581e6951442b2d80d36d83c6f69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4591","title":"Add encryption service","createdAt":"2023-01-31T17:59:59Z"}
{"state":"Merged","mergedAt":"2023-01-31T18:16:03Z","number":4592,"body":"Hides it.","mergeCommitSha":"8f64fffae6813dd4d12f3e0c6f7ea3051f49280f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4592","title":"Deprecate FeatureNewExplorerInsightsUI feature flag","createdAt":"2023-01-31T18:15:19Z"}
{"state":"Merged","mergedAt":"2023-01-31T18:27:07Z","number":4593,"mergeCommitSha":"edc39264ae3fc72cd5109082a22d29510a6553e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4593","title":"Dependabot worklfows","createdAt":"2023-01-31T18:21:11Z"}
{"state":"Closed","mergedAt":null,"number":4594,"body":"Put the test data/results in the tree. No prod changes.","mergeCommitSha":"6dc2a4adf99c28ea6f35c37312ae76d201f947a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4594","title":"Open source test data + results","createdAt":"2023-01-31T18:32:36Z"}
{"state":"Merged","mergedAt":"2023-01-31T20:07:20Z","number":4596,"mergeCommitSha":"4be661f733163d6a6edbbd42694933bfd43df73e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4596","title":"Dont hide TeamMemberView and PullRequestView in dashboard","createdAt":"2023-01-31T20:02:24Z"}
{"state":"Merged","mergedAt":"2023-01-31T21:06:15Z","number":4597,"body":"- Topic ingestion endpoint parsing\r\n- Update\r\n","mergeCommitSha":"8598ed59973bad9e86ba346816c2dd344db3aa25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4597","title":"TopicIngestionEndpoint","createdAt":"2023-01-31T20:52:38Z"}
{"state":"Merged","mergedAt":"2023-01-31T21:43:00Z","number":4598,"body":"Annoyingly the GitHub `GET /orgs/:id/members` API does not return user display names.\n\nInstead we need to use *GitHub App User Auth* with the `GET /user` or `GET /user/:id` APIs\nfor each _authenticated_ Unblocked GitHub user. We could schedule this via a low frequency\nbackground poll, or trigger an event to do this on login so that it's only done for active\nusers.","mergeCommitSha":"f565e545143730218fe1777c3484207d95334160","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4598","title":"Do not clear GitHub user display names on sync","createdAt":"2023-01-31T21:03:40Z"}
{"state":"Merged","mergedAt":"2023-01-31T21:15:31Z","number":4599,"mergeCommitSha":"908e2f51ddc771993d6013cb10ed0339728e8b61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4599","title":"use casing","createdAt":"2023-01-31T21:15:25Z"}
{"state":"Merged","mergedAt":"2022-01-17T23:25:53Z","number":46,"body":"This pr adds the following;\r\n\r\n1. Migrating openapi generator template for api service (overrides and additions based off https://github.com/OpenAPITools/openapi-generator/tree/c305c71715652fc2075122534fa049a28f750ff3/modules/openapi-generator/src/main/resources/kotlin-server) \r\n2. Tests for api code\r\n\r\nWe're using 2.0.0-beta version of ktors, so I had to update some imports here and there on the template overrides to reference changes.\r\n\r\nhttps://medium.com/backyard-programmers/whats-new-in-ktor-2-0-b3783adb50c9","mergeCommitSha":"4ee3248ba9cb8e7b5dedfa540e60dbcb0b9ea96a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/46","title":"Add basic openapi generator code for kotlin service","createdAt":"2022-01-17T21:56:08Z"}
{"state":"Merged","mergedAt":"2022-03-03T00:34:54Z","number":460,"mergeCommitSha":"fe1237af75f6b1748133f7e5e07754b55abea3bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/460","title":"Disable LongParameterList rule","createdAt":"2022-03-03T00:28:15Z"}
{"state":"Merged","mergedAt":"2023-01-31T22:01:22Z","number":4600,"mergeCommitSha":"301a01fdc6a71269b8574ef50f4fd9c1171692d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4600","title":"Use impersonation context to render name","createdAt":"2023-01-31T21:43:28Z"}
{"state":"Merged","mergedAt":"2023-01-31T22:30:58Z","number":4601,"body":"- use casing\r\n- Fix build\r\n","mergeCommitSha":"36f1611a76d28ece7533a73d62a0d8ce6fca2127","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4601","title":"Fixbuild","createdAt":"2023-01-31T22:30:46Z"}
{"state":"Closed","mergedAt":null,"number":4602,"body":"TODO: test full text search lookup of insights when one of the elements in `TopicModel.keywords` has a space (i.e. might need to wrap it in quotes)","mergeCommitSha":"2b1dd8e923d55c1f83d8ed27c1bfeca5cf483d1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4602","title":"[WIP] Add TopicModel.keywords property","createdAt":"2023-01-31T23:05:21Z"}
{"state":"Merged","mergedAt":"2023-02-01T18:55:35Z","number":4603,"body":"cc @jeffrey-ng, `esbuild-loader` breaks this.  I don't know why.  I'm disabling it on the dashboard for now.\r\n\r\nFrom comments like this:\r\nhttps://github.com/privatenumber/esbuild-loader/issues/267\r\nI think that `esbuild-loader` should generally support web workers, but I can't get it to work as expected.","mergeCommitSha":"e22d09ddc07002d06c92321619d5d9837d353b90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4603","title":"Temp fix for dashboard syntax hilighting","createdAt":"2023-01-31T23:36:25Z"}
{"state":"Merged","mergedAt":"2023-01-31T23:50:22Z","number":4604,"body":"- Add sagemaker triggers\r\n- Update\r\n","mergeCommitSha":"819bf1c7aec85df1022d7205ad68b8002aab872c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4604","title":"AddSageMakerTrigger","createdAt":"2023-01-31T23:37:44Z"}
{"state":"Merged","mergedAt":"2023-02-01T00:13:36Z","number":4605,"mergeCommitSha":"0120af8488cd81646b2a69a976e5d3cd346bdf07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4605","title":"Dont recommend bots as topic experts","createdAt":"2023-02-01T00:02:23Z"}
{"state":"Merged","mergedAt":"2023-02-01T00:21:58Z","number":4606,"mergeCommitSha":"16b904aaa0b5af0a40b61a558cb4d12e8fad4345","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4606","title":"Fix for repo installation upsert failure due to host mismatch","createdAt":"2023-02-01T00:02:54Z"}
{"state":"Merged","mergedAt":"2023-02-01T00:25:32Z","number":4607,"body":"Fixes CPU drain. The GIF rendering was happening regardless of the view state even when the hub was closed. CPU pinned between 20-30%...","mergeCommitSha":"ec6a216d779fce2d118caa5f40a9516f7a22b1ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4607","title":"This should do it...","createdAt":"2023-02-01T00:14:44Z"}
{"state":"Merged","mergedAt":"2023-02-01T00:37:53Z","number":4608,"mergeCommitSha":"509cd362601f033074aabe6007a94cb50d4277fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4608","title":"Update sagemaker resources","createdAt":"2023-02-01T00:37:47Z"}
{"state":"Merged","mergedAt":"2023-02-01T02:10:40Z","number":4609,"mergeCommitSha":"345aed6153534e42a58a30e229f2576cecd43ca2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4609","title":"Update","createdAt":"2023-02-01T02:10:27Z"}
{"state":"Merged","mergedAt":"2022-03-03T08:56:12Z","number":461,"mergeCommitSha":"1a2c53bc45bd7a8ae72367b98bb24e35b8e166ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/461","title":"Git diff unit tests","createdAt":"2022-03-03T08:03:19Z"}
{"state":"Merged","mergedAt":"2023-02-01T03:32:55Z","number":4610,"body":"Fucking hard-coded `GitHub`.","mergeCommitSha":"52ed98702c7a1f56090148f00a72cfb4b7723e00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4610","title":"Fix for GHE team members being deleted randomly","createdAt":"2023-02-01T02:37:34Z"}
{"state":"Merged","mergedAt":"2023-02-01T02:43:56Z","number":4611,"mergeCommitSha":"c18f481fdc1608aaba22bbc6585573659bcca986","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4611","title":"powerml array","createdAt":"2023-02-01T02:42:57Z"}
{"state":"Merged","mergedAt":"2023-02-01T03:29:26Z","number":4612,"mergeCommitSha":"c98bb562b157d16b0851c519e6fc844638a829e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4612","title":"make indexable","createdAt":"2023-02-01T03:29:09Z"}
{"state":"Merged","mergedAt":"2023-02-01T03:31:50Z","number":4613,"mergeCommitSha":"dc06e7ec80415a7b582b19944a28bd26a52a5151","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4613","title":"indexable","createdAt":"2023-02-01T03:31:42Z"}
{"state":"Merged","mergedAt":"2023-02-01T04:00:45Z","number":4614,"body":"Tested in local stack, works fine.\r\n\r\nWill run in DEV first.\r\n\r\nThen run in PROD.\r\n\r\nThis migration is super low risk, no deletion occurs.","mergeCommitSha":"63971280b81ec6292b2281a2de06e64e33895404","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4614","title":"Backfill externalTeamId on GitHub provider Identities","createdAt":"2023-02-01T03:50:45Z"}
{"state":"Merged","mergedAt":"2023-02-01T05:12:05Z","number":4615,"mergeCommitSha":"ed75b848de199dbc99e2cf665b1ccdc65a7330dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4615","title":"Remove duplicate identities","createdAt":"2023-02-01T05:02:21Z"}
{"state":"Closed","mergedAt":null,"number":4616,"mergeCommitSha":"fc9f8605157e47be81605c0863b3c8b11f20287a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4616","title":"Cleanup identity migration no longer needed","createdAt":"2023-02-01T05:17:31Z"}
{"state":"Merged","mergedAt":"2023-02-01T07:16:16Z","number":4617,"mergeCommitSha":"d97b50c3020448a304350ae13d117cebda59a7b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4617","title":"Remap team member references before identity delete","createdAt":"2023-02-01T06:45:14Z"}
{"state":"Merged","mergedAt":"2023-02-01T17:47:53Z","number":4618,"body":"Falco core and Sidekick have been deployed to both environments.\r\n    - Added readme with installation steps as well as links to docs about how to manage Falco rules\r\n    - Connected Falco sidekick to Slack (security-alarms-dev and security-alarms channels)\r\n    - Created separate Webhook integration endpoints on Grafana to receive alarms from Falco and page the oncall\r\n    - Updated Global network policy to allow API accesss from `falco` namespace.\r\n    - Deleted the old unused network policies\r\n    - Added Falco dashboard to Grafana (needs a bit of work to support multiple envs)\r\n\r\nI tried using Alertmanager, Grafana and Loki integrations but non worked as expected!\r\n\r\nThis should wrap up our Falco work for now.\r\n\r\n\r\n- [Falco Overview](https://getunblocked.grafana.net/d/FvUFlfuZz/falco-dashboard?orgId=1)\r\n- [Dev: #security-alarms-dev](https://chapter2global.slack.com/archives/C04M9AH4GA0)\r\n- [Prod: #security-alarms](https://chapter2global.slack.com/archives/C04MAEXF1GE)\r\n","mergeCommitSha":"60e0066a12ca745eae701673837395626cc91056","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4618","title":"Falco security setup","createdAt":"2023-02-01T10:28:43Z"}
{"state":"Merged","mergedAt":"2023-02-01T16:13:10Z","number":4619,"body":"This change removes 79% of log messages, based on last 2 days of logz history.","mergeCommitSha":"45ba5a11ec84e6fc143ee01ff65abc121bd585e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4619","title":"Reduce very spammy messages","createdAt":"2023-02-01T16:02:03Z"}
{"state":"Merged","mergedAt":"2022-03-04T02:20:37Z","number":462,"body":"- implements Unread api endpoints\r\n- creates/updates `ThreadUnread`s whenever a message is created\r\n- attempts to start separating the database layer from the api layer, at least for ThreadUnread (https://github.com/NextChapterSoftware/unblocked/issues/220)\r\n\r\n**Unread Message Synchronization**\r\nhttps://www.notion.so/nextchaptersoftware/Unread-Message-Synchronization-50af28a5fa734f59a670e0f04e15e938","mergeCommitSha":"ca9699c1faa089b9c6604cac6cd18ab341a6414f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/462","title":"Implement ThreadUnread","createdAt":"2022-03-03T08:34:42Z"}
{"state":"Merged","mergedAt":"2023-02-01T16:30:47Z","number":4620,"body":"- Run synchronously\n- Add logs","mergeCommitSha":"2a57aaaba7248ba7ad634655a5ca19a5aff97275","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4620","title":"Attempt to debug migration","createdAt":"2023-02-01T16:29:21Z"}
{"state":"Merged","mergedAt":"2023-02-01T17:10:30Z","number":4621,"mergeCommitSha":"7d1ac5ed2edd77c0e4b02e3c0d9563922a1992e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4621","title":"Migration debugging","createdAt":"2023-02-01T16:56:19Z"}
{"state":"Closed","mergedAt":null,"number":4622,"body":"Seems like ESBuild doesn't support importing web workers\r\n\r\nhttps://github.com/evanw/esbuild/issues/2866","mergeCommitSha":"17dc2048e47fd67d877a8d028e907cd3a8baac9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4622","title":"Move back to TSLoader for web workers","createdAt":"2023-02-01T17:20:42Z"}
{"state":"Merged","mergedAt":"2023-02-02T21:44:37Z","number":4623,"body":"Add a new stream with bot users filtered out, using the new `teamMember.identity.isBot` property.\r\n\r\nAreas where we filter out bot accounts:\r\n* Mentions/git collaborators list (we also manually filter out `!teamMember.isCurrentMember` here) \r\n* Adding experts to topic (in NewTopic flow as well as the topic management in the sidebar) \r\n\r\nThis PR only leverages the filtered map on the clients, not the stores. If there is a bot id in the topic experts list or a discussion participants list, we still want to show their user information. ","mergeCommitSha":"c3b0623765f39c9b7e71f604c87a4e658789b03a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4623","title":"Add activeMembersMapStream to TeamMemberStore","createdAt":"2023-02-01T17:46:54Z"}