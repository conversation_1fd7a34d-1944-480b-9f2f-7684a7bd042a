{"state":"Merged","mergedAt":"2022-07-06T19:20:42Z","number":2191,"body":"- All PRBs will be using caches as read-only\r\n- Main branch builds will update incremental caches\r\n\r\nTested it and on clean builds with no cache we spend about 3 more minutes. Once caches have been populated, build times drop from 9+ mins to under 3 minutes. ","mergeCommitSha":"f568956a0dfd890acdff9f66cb970af32c1e502e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2191","title":"Enable Gradle caching","createdAt":"2022-07-06T17:10:39Z"}
{"state":"Merged","mergedAt":"2022-07-06T18:03:22Z","number":2192,"mergeCommitSha":"fe6e6cfd954557ab04b6a5b08e3fd564ca33a89c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2192","title":"Fix section header padding","createdAt":"2022-07-06T17:24:56Z"}
{"state":"Merged","mergedAt":"2022-07-06T17:30:17Z","number":2193,"mergeCommitSha":"35c6ea027394c0e8f8b66fcbc9e7586984762f30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2193","title":"update","createdAt":"2022-07-06T17:30:13Z"}
{"state":"Merged","mergedAt":"2022-07-06T18:31:13Z","number":2194,"body":"Bad unique index","mergeCommitSha":"966fe8a047598a66fbe414259fb152a7d4f022ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2194","title":"Update","createdAt":"2022-07-06T18:03:26Z"}
{"state":"Merged","mergedAt":"2022-07-06T18:29:27Z","number":2195,"body":"<img width=\"640\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/177614326-73deb8dd-c72a-424b-944e-11dbfc7a69a2.png\">\r\n<img width=\"663\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/177614364-c7a2e387-edeb-45cf-850f-e4cbe8252e5e.png\">\r\n","mergeCommitSha":"5705e1e2e116c9be2bf4af20df4ae6f7e6c2b705","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2195","title":"Make descriptions optional on web extension thread creation","createdAt":"2022-07-06T18:03:50Z"}
{"state":"Merged","mergedAt":"2022-07-06T20:22:51Z","number":2196,"body":"Fixes UNB-397. This looks like it regressed as people changed the CI scripts.\r\n\r\n* Update CI scripts to pass down product number/version to build scripts\r\n* Update prod build scripts to fail if product number/version are not supplied\r\n* Add buildCfg logs back so we get some debugging help, but make them debug logs\r\n* Disable writing out debug logs on the console for jest tests","mergeCommitSha":"a61f4562c5f852ab4829896b60812f2f6892ca04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2196","title":"Provide product version/number headers in TS clients","createdAt":"2022-07-06T18:22:03Z"}
{"state":"Merged","mergedAt":"2022-07-07T03:19:14Z","number":2197,"body":"I *think* we said we wouldn't do this, so not sure why I overlooked this.\r\n\r\nThis is an example of a thread created from a left side comment:\r\n\r\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/85adeaa7-80b8-4084-9c75-71f02011aaa2\r\n\r\nThis is the comment:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/2188/files#r915117239","mergeCommitSha":"e44e278f49d5d6c915224b22e79cc98fa58de047","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2197","title":"Don't ingest threads on the left side","createdAt":"2022-07-06T18:23:19Z"}
{"state":"Merged","mergedAt":"2022-07-07T16:23:20Z","number":2198,"body":"Utilize promise forward proxy to setup storage transport layer.\r\n\r\n","mergeCommitSha":"618b85c46cb979944e952b8db7f841af3a1315f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2198","title":"Web Extension Proxy for storage","createdAt":"2022-07-06T19:00:43Z"}
{"state":"Merged","mergedAt":"2022-07-06T20:16:12Z","number":2199,"body":"Doesn't include web client implementation (will add in separate PR)","mergeCommitSha":"159cf32723121a32dcf5679f8e93cc4192d3f1d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2199","title":"Adds intercom hash to person","createdAt":"2022-07-06T19:04:55Z"}
{"state":"Merged","mergedAt":"2022-01-06T00:15:38Z","number":22,"body":"Add an tailwind eslint plugin that helps with both sorting & enforces *only* using tailwind classnames (can remove this rule)\r\n\r\nWill check eslint during CI.\r\n\r\nVSCode should also automatically handle the sort with eslint on file save.","mergeCommitSha":"4b8cf4ca9fa567b512a18e2e485aacfb1c09680d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/22","title":"Add Tailwind listing & sorting","createdAt":"2022-01-04T23:44:28Z"}
{"state":"Merged","mergedAt":"2022-07-06T21:09:56Z","number":2200,"body":"* Fix error in VSCode package step\r\n* Propagate product version/number values through to dashboard build","mergeCommitSha":"6d61d2df3b3650e77f3ae981dfe330134335a663","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2200","title":"Fix VSCode package step","createdAt":"2022-07-06T20:45:52Z"}
{"state":"Merged","mergedAt":"2022-08-12T21:48:05Z","number":2201,"body":"The scm-service was failing to recommended newly created threads because the\r\nthread did not exist yet. This occurred because the recommendation was run from\r\nwithin the same transaction as the thread creation.\r\n\r\nThis change allows the thread to be fully created first, then applies a\r\npotential recommendation.\r\n\r\n```\r\nERROR: insert or update on table \"threadrankmodel\" violates foreign key constraint \"fk_threadrankmodel_thread__id\"\r\nDetail: Key (thread)=(7c70a1e4-ae35-467e-a9cd-fd1ab7db07bf) is not present in table \"threadmodel\".\r\n```\r\n\r\nAlso, we run recommendation rebuild only for members who have Unblocked accounts.\r\n\r\nFixes:\r\n- https://linear.app/unblocked/issue/UNB-342/thread-recommendations-crashing\r\n- https://linear.app/unblocked/issue/UNB-540/recommendations-backfill-timing-out-after-3-mins-for-clio","mergeCommitSha":"af6bcff1eaa508d0847f37596c254d42a880a565","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2201","title":"Fix thread recommendation stability and performance","createdAt":"2022-07-06T21:11:21Z"}
{"state":"Merged","mergedAt":"2022-07-06T21:43:39Z","number":2202,"mergeCommitSha":"0b8e81ea2323cf175c21f2e54e4672cb9c29e2dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2202","title":"Update email templates","createdAt":"2022-07-06T21:31:47Z"}
{"state":"Merged","mergedAt":"2022-07-06T21:42:29Z","number":2203,"body":"Reverts NextChapterSoftware/unblocked#2191","mergeCommitSha":"c3cb0e647e24be5a2e1449978d1f046f2ed2ec10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2203","title":"Revert \"Enable Gradle caching\"","createdAt":"2022-07-06T21:41:51Z"}
{"state":"Merged","mergedAt":"2022-07-06T22:31:29Z","number":2204,"body":"The error checking was done at the root, which meant importing the file would immediately fail.","mergeCommitSha":"57cf8d5559ba4f75e786884c6eb8f1e94553919f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2204","title":"Fix web extension dev builds","createdAt":"2022-07-06T21:45:51Z"}
{"state":"Merged","mergedAt":"2022-07-06T21:59:11Z","number":2205,"body":"Reviewing requires checking out this branch and decrypting","mergeCommitSha":"7706690042c33632894e6a313790e17434e187cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2205","title":"Reformat secrets","createdAt":"2022-07-06T21:47:29Z"}
{"state":"Merged","mergedAt":"2022-07-07T04:54:37Z","number":2206,"body":"Fixes UNB-116","mergeCommitSha":"ff1654459df51e8ea0fd365db8c725e2c03dccec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2206","title":"Strip markdown from titles for PR ingested threads","createdAt":"2022-07-06T21:49:48Z"}
{"state":"Merged","mergedAt":"2022-07-06T22:09:17Z","number":2207,"mergeCommitSha":"eaf4e299dcc333a4a4f5753457df5f0c552c96cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2207","title":"update","createdAt":"2022-07-06T21:54:44Z"}
{"state":"Merged","mergedAt":"2022-07-06T22:07:43Z","number":2208,"body":"This reverts commit 0b8e81ea2323cf175c21f2e54e4672cb9c29e2dc.\r\n","mergeCommitSha":"3a4bd049c55c5386075faa3cd925ed6b1e80e4df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2208","title":"Revert \"Update email templates (#2202)\"","createdAt":"2022-07-06T22:07:38Z"}
{"state":"Merged","mergedAt":"2022-07-06T23:10:37Z","number":2209,"body":"Resolves UNB-259","mergeCommitSha":"1dadd6cd2ea86563b2a58a0245d80deca42d6d4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2209","title":"Add identity hash to intercom client","createdAt":"2022-07-06T22:27:40Z"}
{"state":"Merged","mergedAt":"2022-07-06T23:17:03Z","number":2210,"body":"Implementation to follow","mergeCommitSha":"9097671523360db9665e7ef83b1f126d869cc631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2210","title":"Add basic api for email preferences","createdAt":"2022-07-06T22:39:10Z"}
{"state":"Merged","mergedAt":"2022-07-07T04:54:47Z","number":2211,"body":"Handles creating threads on renamed files","mergeCommitSha":"5835a7e95c73115fd74859e7aeb8c8a816fbcd7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2211","title":"Fallback to searching files by previous_filename","createdAt":"2022-07-06T23:10:48Z"}
{"state":"Merged","mergedAt":"2022-07-22T17:20:30Z","number":2212,"body":"Fixes UNB-319.\r\n\r\nThis is a mess, details are in the Linear issue: https://linear.app/unblocked/issue/UNB-319/unreads-badge-doesnt-show-on-load-in-vscode\r\n\r\nThe solution I have here is the least intrusive one that seemed to work:\r\n* On extension startup, determine which sidebar state we should start up in (unauthed, uninstalled, installed), and go to that state directly, instead of relying on the chain of sidebar provider logic.\r\n* If we are starting directly in the installed state, open up that sidebar so the badge is displayed.\r\n","mergeCommitSha":"3d5d0aabe989e9bfcf005a47db8b5abad5d298ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2212","title":"Show VSCode badges on startup","createdAt":"2022-07-06T23:22:23Z"}
{"state":"Merged","mergedAt":"2022-07-06T23:23:48Z","number":2213,"mergeCommitSha":"b4ecdc870f0528c9d4fea453232458b99cad668a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2213","title":"update","createdAt":"2022-07-06T23:23:38Z"}
{"state":"Merged","mergedAt":"2022-07-07T03:18:28Z","number":2214,"body":"There are a couple of old sourcemarks that have empty snippets. Just adding them as unit tests to show that the bug has been fixed.","mergeCommitSha":"e40ac041d2af2c9ee4dca12205d6341d1d4b1f6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2214","title":"Add some unit tests to validate bug was fixed","createdAt":"2022-07-06T23:41:13Z"}
{"state":"Merged","mergedAt":"2022-07-07T17:17:28Z","number":2215,"mergeCommitSha":"91914c20d9e70b24d975249686bb907f27a1b16e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2215","title":"Add tests for MessageUtils","createdAt":"2022-07-06T23:42:57Z"}
{"state":"Merged","mergedAt":"2022-07-07T03:18:13Z","number":2216,"body":"To help with debugging PR ingestion","mergeCommitSha":"dfe0e6e1931fa463f2edd518b88858517140c7ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2216","title":"Add links to show GitHub API responses","createdAt":"2022-07-07T00:05:20Z"}
{"state":"Merged","mergedAt":"2022-07-07T03:22:01Z","number":2217,"mergeCommitSha":"4694c0f5226cfc2b4243d9ef85e05b4f95922baa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2217","title":"Remove legacy sourcemark kotlin native app","createdAt":"2022-07-07T01:22:12Z"}
{"state":"Closed","mergedAt":null,"number":2218,"body":"highly performant and it has direct bindings into git c libraries!\r\n\r\nNot necessary to do process calls for quite a few things\r\n\r\nhttps://docs.rs/git2/latest/git2/\r\n\r\nAlso, good support for grpc.\r\n\r\nOtherwise, consider golang as it has similar support for direct bindings to git libraries. (not sure how well this will work on windows...)","mergeCommitSha":"88708927b64f06ae5b1c526e6a8532f5b06f6f26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2218","title":"[EXPERIMENT] Experiment with Rust for sourcemarkapp git queries","createdAt":"2022-07-07T02:08:52Z"}
{"state":"Merged","mergedAt":"2022-07-11T06:35:47Z","number":2219,"body":"- API service component of this change.\n  https://linear.app/unblocked/issue/UNB-175/perf-paginate-sourcemark-and-sourcepoint-api\n\n- Client changes (vscode) will follow.\n\n","mergeCommitSha":"7677e94c400e789b0e4d489d5d7243b16a329212","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2219","title":"Paginate getSourceMarks API operation","createdAt":"2022-07-07T02:25:51Z"}
{"state":"Merged","mergedAt":"2022-02-02T19:39:21Z","number":222,"body":"- Added support for VPN access to RDS from VPN per env. Currently we only allow it for DEV\r\n- A whole bunch of code cleanup and pretty lint + indent stuff\r\n- Added a new stack to deploy an S3 hosted static site with CloudFront and WAF\r\n- Created a new config object for things that need to be deployed in AWS Standard region (us-east-1) life WAF and CF\r\n- Added config json for standard region in Dev\r\n\r\nA few more notes around Static Site:\r\n- `deploybot` has access  S3 static site bucket for `get`, `write`, `delete` and `list` object operations\r\n- `deploybot` has access  CloudFront  to list all distributions (needed to get Distro ID) and also to create, list invalidations for file paths on S3 bucket\r\n- IAM role pattern for static site actions `arn:aws:iam::AWS_ACCOUNT_ID_HERE:role/S3StaticSiteDeployerRole`\r\n- We will add the prod configuration json in the next PR. \r\n- To simplify CI/CD story we can provide CloudFront ID and bucket names as config parameters instead of lookup (they won't change for the lifetime of an env)\r\n- WAF is currently allowing access only from our office network IP address.\r\n\r\nAll these changes have been deployed to Dev","mergeCommitSha":"e38e33b1367d92d423f13bfa4c7daef21c1898a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/222","title":"Static Site and RDS access over VPN ","createdAt":"2022-02-02T19:31:45Z"}
{"state":"Closed","mergedAt":null,"number":2220,"body":"Do not merge until all (vscode) clients are using the limit and cursor.","mergeCommitSha":"581a09075e1a17ebdd0f954c9190bdd1a05b38ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2220","title":"Enforce getSourceMarks API limit","createdAt":"2022-07-07T02:25:54Z"}
{"state":"Merged","mergedAt":"2022-07-07T18:20:33Z","number":2221,"body":"- Single call returns _latest_ and _original_ points for a sourcemark\r\n- Also returns a reason when the latest point could not be found -- but this bit is rubbish right now. Can be improved in follow up.","mergeCommitSha":"290ae86f2bceae7bb3a9264ad578f5d70a0c8567","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2221","title":"Consolidate original/latest point lookup","createdAt":"2022-07-07T05:26:11Z"}
{"state":"Merged","mergedAt":"2022-07-07T17:48:15Z","number":2222,"body":"- Dev DB is upgraded to 14.3\r\n- Prod DB is kept back on 13.4\r\n- Added configs so we could specify versions which are not yet added to built-in enums provided by CDK \r\n- Deployed to Dev \r\n- Did a diff with prod and there was no difference. ","mergeCommitSha":"f899fca8f75c9804bfb5bd6971a380c4f78b8e19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2222","title":"fix the difference between CDK and Cloudformation","createdAt":"2022-07-07T17:27:44Z"}
{"state":"Merged","mergedAt":"2022-07-07T17:30:49Z","number":2223,"mergeCommitSha":"91045fa8e7b11164de143a94971777e976d49513","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2223","title":"update","createdAt":"2022-07-07T17:30:43Z"}
{"state":"Merged","mergedAt":"2022-07-07T18:05:36Z","number":2224,"mergeCommitSha":"85d6913e2873d69c9089b73a7ebabdc01277c22e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2224","title":"update postgres","createdAt":"2022-07-07T18:05:08Z"}
{"state":"Merged","mergedAt":"2022-07-07T18:41:40Z","number":2225,"mergeCommitSha":"9132e7ca66d4a878825bce3673b60e613eb5547e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2225","title":"Move tests around","createdAt":"2022-07-07T18:25:42Z"}
{"state":"Merged","mergedAt":"2022-07-13T20:34:22Z","number":2226,"body":"Moves all logic & statefulness out of background scripts into content scripts.\r\n\r\nLogic *should* not have changed much. Tried to keep a similar interface for now but there's room to simplify things in the future.\r\n\r\nAlso fixes unb-130","mergeCommitSha":"e94a8cb7ccdf02364de20fbbb536293f5636fe5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2226","title":"Web Extension Refactor","createdAt":"2022-07-07T18:52:26Z"}
{"state":"Merged","mergedAt":"2022-07-07T21:21:39Z","number":2227,"body":"Definitely a least (most?) proud moment.\r\n\r\nBug is that `LazyVStack` incorrectly sizes itself for 1 frame during a multi-layout pass. This throws the `contentHeight` hack for a loop because it has to ignore the incorrect size. Only way to do this without introducing a ton more complexity is to take the latest value within some minimum timing threshold. ","mergeCommitSha":"664c010c5f2edc36a5888e20a62bcdf87befeb3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2227","title":"Workaround LazyVStack height bug","createdAt":"2022-07-07T20:31:52Z"}
{"state":"Merged","mergedAt":"2022-07-07T22:48:13Z","number":2228,"mergeCommitSha":"39564d3878ee2285efd1ff1726c51cd563883cd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2228","title":"AddPersonEmails","createdAt":"2022-07-07T21:51:29Z"}
{"state":"Merged","mergedAt":"2022-07-07T23:17:10Z","number":2229,"body":"I know we said we wouldn't do this but the numbers are wonky:\r\n\r\n<img width=\"1436\" alt=\"CleanShot 2022-07-07 at 14 43 38@2x\" src=\"https://user-images.githubusercontent.com/1924615/177880519-069da735-e37c-4c1c-9f66-c5cee8e6f82a.png\">\r\n\r\nSome days have total users less than MAU and/or WAU because Rashin recreated his person object.\r\n\r\nSome days have total teams less than MAT and WAT because of the Unblocked demo team that is recreated.","mergeCommitSha":"29bc662e1957cfe56c8af125765e467c316a1c2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2229","title":"Don't track metrics for internal teams","createdAt":"2022-07-07T22:18:00Z"}
{"state":"Closed","mergedAt":null,"number":223,"body":"For a lot of the models we create in ModelBuilders, we can grab the team through the referenced objects. For example, if a message object needs the team object, it can grab it through the thread it references. This way there is no chance that the thread and message can refer to different teams when setting up tests.","mergeCommitSha":"498654e4bf2c2a1794a9799dbd91c7c6b2937c61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/223","title":"Get team through referenced objects in ModelBuilders","createdAt":"2022-02-02T19:52:33Z"}
{"state":"Merged","mergedAt":"2022-07-07T23:04:46Z","number":2230,"body":"https://linear.app/unblocked/issue/UNB-122/ui-with-help-textactions-when-sourcepoints-are-detached-or-unresolved","mergeCommitSha":"54c17ef9b8e70aeee708c9d3bfe20f8e45553f61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2230","title":"Characterize source point resolution scenarios","createdAt":"2022-07-07T22:41:30Z"}
{"state":"Merged","mergedAt":"2022-07-07T23:41:59Z","number":2231,"body":"Spent hours digging into what was causing the invalid JS (yet valid since it worked outside Storybook???) but wasn't able to figure it out.\r\n\r\nGoing around the problem and adding a custom template for runtime.\r\n\r\nVSCode storybook with message editor (which would definitely fail as it pulls in base api)\r\n<img width=\"1451\" alt=\"CleanShot 2022-07-07 at 16 11 49@2x\" src=\"https://user-images.githubusercontent.com/1553313/177886371-b4702552-2715-4972-9369-6ac932819002.png\">\r\n\r\n","mergeCommitSha":"88e371b223f48484108cc6cceccc63df123414c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2231","title":"Add custom template to fix storybook","createdAt":"2022-07-07T23:12:24Z"}
{"state":"Merged","mergedAt":"2022-07-08T00:56:28Z","number":2232,"body":"Additional logging in places that could cause spinner issues","mergeCommitSha":"ed06d99605c2f6acf8adf8990d5dd031c526c8a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2232","title":"Additional logging","createdAt":"2022-07-07T23:52:58Z"}
{"state":"Merged","mergedAt":"2022-07-08T08:45:47Z","number":2233,"body":"The typings here are getting out of control, this was an effort to get this UI in as quickly as possible.  I will clean this up at some point later.","mergeCommitSha":"9e2a26bfcb72c108fd1db65247c818fe04854252","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2233","title":"SourceMark resolution failure UIs","createdAt":"2022-07-08T00:07:48Z"}
{"state":"Merged","mergedAt":"2022-07-08T02:24:53Z","number":2234,"mergeCommitSha":"ad116dc2534de6a91eb100d733b4a9cb73643f1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2234","title":"UseEmailSettings","createdAt":"2022-07-08T02:11:39Z"}
{"state":"Merged","mergedAt":"2022-07-08T06:40:28Z","number":2235,"mergeCommitSha":"5ca3ac5cc02a9836efb46efeed09c475500ac1d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2235","title":"cleanup queries","createdAt":"2022-07-08T06:04:00Z"}
{"state":"Merged","mergedAt":"2022-07-08T06:46:41Z","number":2236,"body":"More scenarios covered.\n\nhttps://linear.app/unblocked/issue/UNB-122/ui-with-help-textactions-when-sourcepoints-are-detached-or-unresolved","mergeCommitSha":"f481e6816cc3f6f45343475a6da71e2d89229738","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2236","title":"Improved sourcemark characterization","createdAt":"2022-07-08T06:34:22Z"}
{"state":"Merged","mergedAt":"2022-07-08T07:01:14Z","number":2237,"body":"DB upgrade was done without any problems. Updated CDK version to match current DB version and deployed it locally to make sure everything is working as expected. This should be a noop change.","mergeCommitSha":"b59037285867201b987ca02ad203cc903bdfb25b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2237","title":"Update cdk to match DB versions deployed to RDS","createdAt":"2022-07-08T06:57:51Z"}
{"state":"Closed","mergedAt":null,"number":2238,"mergeCommitSha":"6b17a9a20a14bd5e3351ba3b2a6b88d96ed03639","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2238","title":"WIP: Adding secondary nodegroups to allow for zero downtime upgrades","createdAt":"2022-07-08T07:36:36Z"}
{"state":"Merged","mergedAt":"2022-07-11T18:39:59Z","number":2239,"body":"Styling needs improvement and \"logout\" button is missing. Logout button just needs to clear the \"admin-session\" cookie.\r\n\r\nAlso no strict origin policy is applied to the server cookie so it might be possible to hijack it. Will add policy in a follow-up.\r\n\r\nIt might also be nice to yank out the user email and display it to show the user which account they're logged in as. Trivial to add","mergeCommitSha":"067806db7ffa52b15fb921dc0a48ed208ae5fa16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2239","title":"Admin auth","createdAt":"2022-07-08T16:14:49Z"}
{"state":"Merged","mergedAt":"2022-02-03T18:29:08Z","number":224,"body":"Added config json for prod static site. Standard region in prod account is bootstrapped and all changes for static site have been deployed. \r\n\r\nhttps://prod.getunblocked.com\r\nhttps://www.prod.getunblocked.com","mergeCommitSha":"408a1f98afd0e1cc512d21594737d663797a41a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/224","title":"Deploy static site to prod","createdAt":"2022-02-02T20:40:48Z"}
{"state":"Merged","mergedAt":"2022-07-08T20:29:06Z","number":2240,"body":"<img width=\"1577\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/*********-30b52d08-2185-46cd-a69d-8d97fe10737b.png\">\r\n","mergeCommitSha":"690935d0811772d711d1d8613f751a864b2521d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2240","title":"Email prefs in admin console","createdAt":"2022-07-08T20:07:56Z"}
{"state":"Merged","mergedAt":"2022-07-08T22:55:35Z","number":2241,"body":"From:\r\n<img width=\"508\" alt=\"CleanShot 2022-07-08 at 14 43 05@2x\" src=\"https://user-images.githubusercontent.com/858772/*********-ae95c65f-2c7b-4ac7-820b-d6d6468c43b8.png\">\r\n\r\n\r\nTo:\r\n<img width=\"527\" alt=\"CleanShot 2022-07-08 at 14 42 41@2x\" src=\"https://user-images.githubusercontent.com/858772/178074174-dc801b16-9e5b-4153-8d2c-10b0f2459ce1.png\">\r\n\r\n","mergeCommitSha":"b58d085c4411517913d85663d02edc0568048c51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2241","title":"Contact support styling change","createdAt":"2022-07-08T21:42:25Z"}
{"state":"Merged","mergedAt":"2022-07-08T22:00:16Z","number":2242,"mergeCommitSha":"9d1af6b63182682558f14b6cee7740a0c194c513","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2242","title":"Update templates","createdAt":"2022-07-08T21:51:15Z"}
{"state":"Merged","mergedAt":"2022-07-11T18:37:46Z","number":2243,"body":"Scroll page so that item is at 30% of vh whenever possible.\r\n\r\nIgnoring GH's line navigation with `#L15`\r\nGH's basic behaviour jumped directly to the line at the top of page.\r\n\r\nIf we utilized GH's line hash state, and scrolled subsequently, we would have the following:\r\n![CleanShot 2022-07-08 at 13 23 27](https://user-images.githubusercontent.com/1553313/178078621-a2b7e299-1f3a-4ace-bc30-3329ec0c5262.gif)\r\n\r\nTherefore, wrote our own scrolling system for line numbers. \r\n![CleanShot 2022-07-08 at 15 34 43](https://user-images.githubusercontent.com/1553313/178078675-be6a9853-fecf-44a0-83be-a15783cf4722.gif)\r\n\r\nURL is cleaned up and should look identical to standard GH hash URL\r\n\r\nNew URL: `https://github.com/NextChapterSoftware/unblocked/blob/abc90a29b09febf50bae312f4b756091b65503a0/web/src/landing/TeamInvite/TeamInvite.tsx?ub-navigating-line=L6-L6`\r\n\r\nStandard GH URL:\r\n`https://github.com/NextChapterSoftware/unblocked/blob/abc90a29b09febf50bae312f4b756091b65503a0/web/src/landing/TeamInvite/TeamInvite.tsx#L6-L6`","mergeCommitSha":"df231cd4e39ca6cc80cd933791a4440bdd9ef333","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2243","title":"Scroll to line with padding","createdAt":"2022-07-08T22:39:43Z"}
{"state":"Merged","mergedAt":"2022-07-09T01:54:20Z","number":2244,"body":"<img width=\"1556\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/178086889-e7d48dc8-1fd3-4f4b-9613-c1bed9b151b7.png\">\r\n","mergeCommitSha":"b058ab794c5878ee0f5636dd8427f03b83aa94a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2244","title":"add opentelemetry tracing for redis","createdAt":"2022-07-09T01:37:33Z"}
{"state":"Merged","mergedAt":"2022-07-09T18:33:14Z","number":2245,"mergeCommitSha":"68be8970b07e8213a251d4eca92c2672723ab4e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2245","title":"update","createdAt":"2022-07-09T17:37:48Z"}
{"state":"Merged","mergedAt":"2022-07-11T15:01:27Z","number":2246,"body":"Ktors team have introduced a breaking change that we have to take care of otherwise next major upgrade we're going to have problems..\r\nAlso, there's some neat things we can do with resources (i.e. nested paths <-> nested classes etc.)\r\n\r\nAnyways, here you go.\r\n","mergeCommitSha":"16c4fb1792633dfc6f5a7dc108d65072010b85b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2246","title":"Move away from deprecated Locations API in Ktors","createdAt":"2022-07-09T19:40:36Z"}
{"state":"Merged","mergedAt":"2022-07-11T07:04:13Z","number":2247,"body":"Add paged API request helper, and hook up to VSCode' SourceMark store.\r\n\r\n* VSCode's SourceMark store was using the old DataAPIStream, I moved it over to the new APIStream code\r\n* The pager can set a limit on the number of pages to fetch at once, so that we don't starve the other stores of the ability to fetch, if we're fetching a ton of SourceMarks.  Right now the SM store is fetching 5 pages per channel poll, which means it will do 5 requests, then wait a second.  We can change this if needed.","mergeCommitSha":"9a8525f4d9570b772c3711c24bd89ccfcef679e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2247","title":"VScode fetch sourcemarks using paging","createdAt":"2022-07-09T22:13:58Z"}
{"state":"Merged","mergedAt":"2022-07-11T22:52:46Z","number":2248,"body":"Recommendation backfill is timing out, because there is far too much work\nfor a single call.  Reduce the total number of threads to backill to just the\nmost recent 2000 and batch to kep the transations smaller.","mergeCommitSha":"e23e745e39e398d0203aa8c1aba01007dfa6fdb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2248","title":"Attempt to address timeouts in recommendation backfill","createdAt":"2022-07-11T06:41:35Z"}
{"state":"Merged","mergedAt":"2022-07-11T18:29:35Z","number":2249,"body":"limit for applying to source-points instead of source-marks","mergeCommitSha":"6cbdca7fa1497ea5e82f09d50be5970f78eac4ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2249","title":"Fix source mark pagination","createdAt":"2022-07-11T08:08:52Z"}
{"state":"Merged","mergedAt":"2022-02-02T23:01:02Z","number":225,"body":"- Add ability to crate service helm charts automatically using ansible playbook.\r\n- Fix up github actions\r\n\r\nThe framework now is such:\r\n1. Create initial service helm skeleton via \"make create-service-helm-charts\"\r\n2. Any changes to base helm charts will be taking place in \"unblocked/helm/baseservice\"\r\n","mergeCommitSha":"6a0a94bc03201239b87ec4899efa4d960c19456a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/225","title":"AddHelmChartUtilities","createdAt":"2022-02-02T21:00:18Z"}
{"state":"Closed","mergedAt":null,"number":2250,"body":"The clients need to know if PR ingestion is occurring as there is a spinner shown during the tutorial while there are no threads to display. \r\n\r\nThere is a new property on Repo called `isPullRequestIngestionComplete`. The idea here is to use Redis to keep track of how many PRs are left to be ingested during the initial onboarding ingest. For repos with no PR threads, we'll know pretty quickly and can set `isPullRequestIngestionComplete` to true immediately so that clients can progress forward from the spinner without waiting on the threads API for results that will never come.\r\n\r\nFor repos that do have PR threads, we'll increment a counter in Redis every time we put a job on the queue during onboarding, and decrement when that job has been complete. When that counter hits 0, then we'll set `isPullRequestIngestionComplete` to true. ","mergeCommitSha":"0737e3a241e565a5f0f4488c5cd8a6ccbaedd4f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2250","title":"Get pull request ingestion progress","createdAt":"2022-07-11T08:47:32Z"}
{"state":"Closed","mergedAt":null,"number":2251,"body":"InstallationsStore in VSCode had to poll RepoStore instead of the Installations API directly due to some early limitations.\r\n\r\nUpdated to poll Installations to reduce complexity.","mergeCommitSha":"45c13a5abc2de8185d2f83e16d92a8ec5c80b7b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2251","title":"Poll Installations API directly","createdAt":"2022-07-11T16:16:24Z"}
{"state":"Merged","mergedAt":"2022-07-11T16:58:44Z","number":2252,"body":"Updated admin web service account to grant S3 list bucket and GetObject access on download-assets (public bucket). The reason for this approach is so that we won't need to provide a directory listing page for download assets. \r\n\r\nChanges have been deployed to both Dev and Prod. Next I'll create the S3 Kotlin client provider class. ","mergeCommitSha":"5db960110ed31bf4dd7ea57702ea658134a0a32c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2252","title":"update adminweb service account with s3 ro perms","createdAt":"2022-07-11T16:56:11Z"}
{"state":"Merged","mergedAt":"2022-07-11T17:35:44Z","number":2253,"mergeCommitSha":"75e3375cdceb74cfcf18f05bb035e24dc8242484","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2253","title":"Remove superfluous settings","createdAt":"2022-07-11T17:09:59Z"}
{"state":"Merged","mergedAt":"2022-07-11T17:51:15Z","number":2254,"body":"Should have been catching ResponseError instead of just Response.","mergeCommitSha":"c1f7275282ce268d20411fdcbc64c8f33adb5948","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2254","title":"Catch correct error type","createdAt":"2022-07-11T17:24:15Z"}
{"state":"Merged","mergedAt":"2022-07-11T18:57:07Z","number":2255,"mergeCommitSha":"a2c990843bb010a11a0721a843a72023ba0c03e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2255","title":"add converters","createdAt":"2022-07-11T18:11:17Z"}
{"state":"Merged","mergedAt":"2022-07-11T22:31:35Z","number":2256,"body":"Cleanup from https://github.com/NextChapterSoftware/unblocked/pull/2233\r\n\r\nRemove the wrapping `AnchorSourcePointData` type, use `SourceMarkResolution` everywhere instead.\r\n\r\nDiscussionThreadCommand still stores both a SourceMarkResolution (the resolved SM/SP), plus the SourceMarkData (the renderable sourcemark data, like the HTML rendered clip).\r\n\r\nAs part of this, moved the HTML rendering into DiscussionThreadCommand, as that was the only place that actually used it -- I think this makes the code dependencies a little bit clearer.","mergeCommitSha":"5ae32a32fcc9122a82e8c906d0721d069219c5e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2256","title":"Clean up SourceMark rendering types","createdAt":"2022-07-11T18:49:27Z"}
{"state":"Merged","mergedAt":"2022-07-11T18:59:12Z","number":2257,"mergeCommitSha":"500a06b0a597b778aadb73f36dace06d1c3cee07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2257","title":"stop","createdAt":"2022-07-11T18:58:57Z"}
{"state":"Merged","mergedAt":"2022-07-11T19:07:32Z","number":2258,"mergeCommitSha":"97ebc557995bcd5af6004ce6dd3b84a834eaa78f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2258","title":"update","createdAt":"2022-07-11T19:03:54Z"}
{"state":"Merged","mergedAt":"2022-07-11T19:15:38Z","number":2259,"body":"## Enable admin auth in dev\r\n","mergeCommitSha":"90deaff50a98571b2d430a2e9f33ab5d9f45bc69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2259","title":"Enable admin auth in dev","createdAt":"2022-07-11T19:14:09Z"}
{"state":"Merged","mergedAt":"2022-02-02T22:11:06Z","number":226,"body":"for convenience. faster than `./gradlew check` which runs tests","mergeCommitSha":"873fe5e2ca2455d3f9d2317de951083c601cb8a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/226","title":"lint make target","createdAt":"2022-02-02T21:52:30Z"}
{"state":"Merged","mergedAt":"2022-07-11T19:35:13Z","number":2260,"mergeCommitSha":"4af0513dabc176101c3920abccf0e23a2da95ebd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2260","title":"Fix admin prod config property name","createdAt":"2022-07-11T19:31:06Z"}
{"state":"Merged","mergedAt":"2022-07-12T00:15:37Z","number":2261,"body":"<img width=\"1508\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/178342128-d214ed02-f7c6-4467-870c-713f2a9f490f.png\">\r\n<img width=\"1507\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/178328227-4a80c6b6-485b-4049-affe-25109017401d.png\">\r\n<img width=\"655\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/178328297-bd663b01-397a-4e9a-bd43-c58fdf6087c7.png\">\r\n\r\nNOTES:\r\n* This only ports over the existing UI and functionality (i.e. will need to layer on the 'new' features like the modal navigation) \r\n* There are quite a lot of files changed but it's 99% CSS changes for the new designs (i.e. functionality is all the same)\r\n* Small viewports are still in progress but in its current form it's functional/doesn't look broken so I think it's enough to get in\r\n* Will need to do a separate pass at revamping all the theme variables to match Ben's style guide as well as a pass at the typography.scss file ","mergeCommitSha":"d35e518a9d264671d9b5dd6b3197636b68e4c894","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2261","title":"[Dashboard] Update (first pass)","createdAt":"2022-07-11T19:32:27Z"}
{"state":"Merged","mergedAt":"2022-07-11T20:10:02Z","number":2262,"mergeCommitSha":"4d25fe06a4dceb58c5770c1bd09647e8c5cacffa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2262","title":"update","createdAt":"2022-07-11T19:45:05Z"}
{"state":"Merged","mergedAt":"2022-07-11T20:19:22Z","number":2263,"body":"Doesn't break anything, just keeping things in parity with latest brew version","mergeCommitSha":"6b15ca35110a228d8da9cb9908b83d3ac6c8705e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2263","title":"Bump Swift GRPC version for diff job","createdAt":"2022-07-11T20:11:23Z"}
{"state":"Merged","mergedAt":"2022-07-11T20:32:04Z","number":2264,"mergeCommitSha":"c3f69c810cfa19bcea09efe25610539376c5f157","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2264","title":"UpgradeVersiong","createdAt":"2022-07-11T20:17:37Z"}
{"state":"Merged","mergedAt":"2022-07-11T21:50:20Z","number":2265,"mergeCommitSha":"a9d6fa9f8d0d847feb069aee897d2ed04d75d434","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2265","title":"update","createdAt":"2022-07-11T21:32:01Z"}
{"state":"Merged","mergedAt":"2022-07-11T23:23:23Z","number":2266,"mergeCommitSha":"d798a03e95395c3990498db849db6174eb2c51fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2266","title":"update","createdAt":"2022-07-11T22:56:33Z"}
{"state":"Merged","mergedAt":"2022-07-12T18:28:59Z","number":2267,"body":"Querying for both mark and point modifiedAt was error prone.\n\nSimplify modifiedAt queries so that only the sourcemark modifiedAt is queried.\n\nThis change touches the source mark modifiedAt whenever a new point is created for\nthe mark in order to achieve the simpler query.","mergeCommitSha":"38d38bd9af783d44f2458398aea6a835c019b9fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2267","title":"Fix another bug in sourcemark paging","createdAt":"2022-07-12T05:56:23Z"}
{"state":"Merged","mergedAt":"2022-07-12T16:28:28Z","number":2268,"body":"Not sure if this is the right approach but throwing it out there for feedback.","mergeCommitSha":"89e39186279c781bd6bff633044ec34f3b0cc234","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2268","title":"Create a helper function to get a trimmed snippet","createdAt":"2022-07-12T06:34:43Z"}
{"state":"Merged","mergedAt":"2022-07-12T18:46:26Z","number":2269,"body":"- Added S3 client provider along with functionality needed to implement polling for release artifacts \r\n- Added tests for S3 client. \r\n\r\nMy next PR will wire up Version ingestion and obsolete actions to S3 and introduce polling job. ","mergeCommitSha":"44edd61b8cf3488cea3403ac9c50d2d056f65eb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2269","title":"Add S3 client to aws-lib","createdAt":"2022-07-12T09:24:42Z"}
{"state":"Merged","mergedAt":"2022-02-02T23:36:56Z","number":227,"mergeCommitSha":"e2749a5ae45c1a0bee7c2aa1a553a0a1c6bcd104","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/227","title":"Upload test report if build fails","createdAt":"2022-02-02T23:04:35Z"}
{"state":"Merged","mergedAt":"2022-07-12T22:23:43Z","number":2270,"body":"Fix VSCode discussion text editor closing bugs from user feedback:\r\n* If you open a discussion, and then edit the text in the editor, do *not* close the editor when we close the discussion, as long as the editor is in a dirty state\r\n* If we open a discussion and display a SM resolution UI, close that when we close the discussion","mergeCommitSha":"fed0d3c71454234f3d35e72daebbaeea0367d9b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2270","title":"Fix VSCode discussion thread text editor closing behaviour","createdAt":"2022-07-12T18:24:40Z"}
{"state":"Merged","mergedAt":"2022-07-13T18:40:08Z","number":2271,"body":"Fixes UNB-412\r\n\r\nMight be missing some test cases","mergeCommitSha":"c89de8ad2b57744175ddda3082763b46c97b2b43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2271","title":"Trim source snippet lines","createdAt":"2022-07-12T18:57:11Z"}
{"state":"Merged","mergedAt":"2022-07-12T22:24:45Z","number":2272,"body":"This still needs the secret for Dev Refresh Token to be updated before we can merge it. ","mergeCommitSha":"b9c0de80aad0f43ff845c4296a19d2a0c61e0a73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2272","title":"update the Dev teamID in smoke tests","createdAt":"2022-07-12T19:44:24Z"}
{"state":"Merged","mergedAt":"2022-07-13T17:49:56Z","number":2273,"body":"* Update fonts to match styleguide \r\n* Style the dashboard modal \r\n* Add a new `destructive` button type \r\n* Refactor the Layout hooks into a wrapper component for explicit use and to avoid unintentional layouts\r\n* Refactor theming into its own provider\r\n* Add max-width to the sidebar \r\n* Small layout bug fixes","mergeCommitSha":"a8a3520fc7cd9001de6dcd436bf20530451a5e04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2273","title":"Dashboard cleanup","createdAt":"2022-07-12T21:01:46Z"}
{"state":"Merged","mergedAt":"2022-07-13T21:50:54Z","number":2274,"body":"## Part 1 (this PR)\r\n\r\nImplements the release channel concept. Current channels are:\r\n- Stable\r\n- Beta\r\n- Internal\r\n\r\nEach team is subscribed to a single release channel. The default is Stable. This PR does _not_ include changes to the admin console - that's coming in Part 2, along with the ability to change the release channel a team is subscribed on.\r\n\r\nDesign:\r\n- A version can belong to multiple release channels. This gives us maximum flexibility to assign special builds to certain teams, or to define unique release set for a particular build\r\n- Some channels are supersets of others. Ex: *Internal* is a super set of *Beta* and *Stable*. This means that builds released to *Beta* will also be released to *Internal*. Set relationships are computed at runtime and aren't part of the model\r\n\r\n\r\n## Part 2 (next PR)\r\n- Wire up release channel assignment and team subscriptions in admin console","mergeCommitSha":"0379d8f0b021bafc7e095c3bda022c83ed1d0392","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2274","title":"Adds release channels","createdAt":"2022-07-12T22:28:44Z"}
{"state":"Merged","mergedAt":"2022-07-12T23:28:20Z","number":2275,"mergeCommitSha":"74194ff15f8529d23935f60cc11c8ea2924183ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2275","title":"Update","createdAt":"2022-07-12T23:24:53Z"}
{"state":"Merged","mergedAt":"2022-07-13T03:51:25Z","number":2276,"body":"Part one of https://linear.app/unblocked/issue/UNB-408/pr-ingestion-triggered-by-the-sync-job-is-marking-comments-as-edited\r\n\r\nNext PR will run a migration to set this property (we'll pull from the https://docs.github.com/en/rest/pulls/comments#list-review-comments-in-a-repository to minimize the number of API calls needed) then use this new property instead of `prCommentUpdatedAt`.","mergeCommitSha":"cb0909986b2b82b4fd7581ddffd43a54a2446668","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2276","title":"Set prCommentBodyHash on MessageModel","createdAt":"2022-07-12T23:31:34Z"}
{"state":"Merged","mergedAt":"2022-07-13T00:35:49Z","number":2277,"body":"Needed for sourcemark pagination to work properly.","mergeCommitSha":"11fc93dc4a4e1afe956aeab64ed04ee8fab606e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2277","title":"Backfill sourcemark modifiedAt timestamps","createdAt":"2022-07-12T23:34:55Z"}
{"state":"Merged","mergedAt":"2022-07-13T01:13:43Z","number":2278,"mergeCommitSha":"14dc71d1f1e6f838ffcf3e904e0f319b029104ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2278","title":"add pinpoint apis","createdAt":"2022-07-13T00:22:52Z"}
{"state":"Merged","mergedAt":"2022-07-13T23:59:48Z","number":2279,"mergeCommitSha":"c1d368de6cdc84fc72f1a5452590d27f2e71d835","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2279","title":"Use prCommentBodyHash to determine if a message has been updated","createdAt":"2022-07-13T05:08:07Z"}
{"state":"Closed","mergedAt":null,"number":228,"mergeCommitSha":"5ef10e52e5212de6b4835f4c8a6feaac04665b8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/228","title":"Run build in CI for vscode/web","createdAt":"2022-02-02T23:05:05Z"}
{"state":"Merged","mergedAt":"2022-07-13T06:30:53Z","number":2280,"body":"### Problem\r\n\r\nStruggling to get some long-running tasks in admin web to complete synchronously (within\r\nan admin web POST request, which is 30 seconds) because each of these tasks performs DB\r\noperations on the entire thread or source mark collections.\r\n\r\n### Workaround\r\n\r\nApparently, I'm committing all sorts of sins by launching `GlobalScope` jobs,\r\nso I am at least timing out these jobs after a few minutes to prevent the service from\r\nfalling over due to resource leaks.\r\n\r\n### Alternatives (future)\r\n\r\n- use an SQS queue for each item in the DB\r\n- use DB streaming rather than fetching an entire table worth of IDs\r\n- invoke periodically automatically and post-ingestion (for the recommendation task) instead of triggering manually\r\n  https://linear.app/unblocked/issue/UNB-157/recommendations-are-generated-during-onboarding-and-rerun-periodically\r\n\r\n<img width=\"200\" alt=\"Screen Shot 2022-07-12 at 22 55 17\" src=\"https://user-images.githubusercontent.com/1798345/178660878-6fff3cef-86c1-456d-b1ff-767f274961b9.png\">","mergeCommitSha":"2fc52135fa8b0058f904216b531b47d53e22ee7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2280","title":"Possibly address long-running admin web task completion","createdAt":"2022-07-13T05:52:15Z"}
{"state":"Merged","mergedAt":"2022-07-13T16:05:27Z","number":2281,"body":"<img width=\"1133\" alt=\"Screen Shot 2022-07-13 at 08 13 23\" src=\"https://user-images.githubusercontent.com/1798345/178768775-2b2ed8db-906f-4142-a865-2bf96b9d44ad.png\">\r\n\r\n\r\n<img width=\"1119\" alt=\"Screen Shot 2022-07-13 at 08 14 43\" src=\"https://user-images.githubusercontent.com/1798345/178769075-f0cab0e4-0ae8-484c-9abf-fb98295d1d5f.png\">\r\n\r\n","mergeCommitSha":"26c2bf48454533438bdfc5b5e8519366b7c10019","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2281","title":"Login page button style","createdAt":"2022-07-13T15:11:28Z"}
{"state":"Merged","mergedAt":"2022-07-13T17:47:13Z","number":2282,"body":"- `/build` will be the new destination for publishing all build when we switch to new artifact ingestion code\r\n- `/releases` will hold artifacts that are downloadable by end users (public)\r\n- Added the new /releases* CloudFront endpoint to prepare for transition \r\n- Modified CI/CD to double write artifacts to help to but the old destination as well as the new `/builds` prefix ","mergeCommitSha":"d4237e6aa60cf3d7bddd43d6497ae4db64e75a29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2282","title":"double write installer and add new /releases CF endpoint","createdAt":"2022-07-13T16:19:58Z"}
{"state":"Merged","mergedAt":"2022-07-13T20:00:23Z","number":2283,"mergeCommitSha":"4836c846f3bd22185ff5b2eb585c6f4079a3f29c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2283","title":"[Admin Console] Replace \"Modified\" with \"Last Active\" on the People page","createdAt":"2022-07-13T17:46:51Z"}
{"state":"Merged","mergedAt":"2022-07-14T01:20:48Z","number":2284,"body":"## Part 2\r\n\r\nTeams now have a dropdown selector to assign a release channel. \r\n\r\nVersions have release channel toggles. The UX is a bit funky here because some channels are supersets of others and the toggles don't currently reflect those relationships. We can create these relationships but it could cause some confusion - for example turning off \"Internal\" implies turning off everything, turning on \"Stable\" implies turning on everything, etc","mergeCommitSha":"a9acd4da9d87379bf3be8f8059f1f5c79df3be48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2284","title":"Add release channels to admin console","createdAt":"2022-07-13T17:53:03Z"}
{"state":"Merged","mergedAt":"2022-07-13T18:23:19Z","number":2285,"body":"- Update\r\n- Update\r\n","mergeCommitSha":"aa9d70063e24fcbca1ecedc526834da9bba068e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2285","title":"UpdateEmailChannel","createdAt":"2022-07-13T18:19:56Z"}
{"state":"Merged","mergedAt":"2022-07-13T18:47:21Z","number":2286,"body":"My last attempt to create a new CF failed because of a misconfiguration. It's much cleaner to create a new bucket for our release management stuff. \r\n\r\n- Added a new `releases` bucket \r\n- Added a `/releases*` CloudFront behaviour. The old `/download-assets` will be removed once we have finished cutting over to the new release mechanism.\r\n- Added publish permission for the new bucket to deploybot user \r\n\r\nI'll make a separate PR to update the ci-installer.yaml workflow (which is broken right now) once these changes have been deployed. ","mergeCommitSha":"5a7700b4ba97b16385acbaccc5681c203d92ac94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2286","title":"added a new bucket and separate CF behavior for releases","createdAt":"2022-07-13T18:29:31Z"}
{"state":"Merged","mergedAt":"2022-07-13T19:27:14Z","number":2287,"body":"Part 2 of fixing my mess:\r\nDouble writes all installer build artifacts to both buckets. \r\n\r\nRequires https://github.com/NextChapterSoftware/unblocked/pull/2286\r\n\r\n","mergeCommitSha":"c20b671cda8a9aa34906b1fe3bbff34a67153197","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2287","title":"double write to new releases buckets","createdAt":"2022-07-13T18:35:53Z"}
{"state":"Merged","mergedAt":"2022-07-13T21:14:51Z","number":2288,"body":"Easier to read side-by-side with separate tables I think.\r\n\r\n<img width=\"1417\" alt=\"Screen Shot 2022-07-13 at 11 51 12\" src=\"https://user-images.githubusercontent.com/1798345/*********-b120253b-4c13-4e46-b922-2afca3786aef.png\">\r\n","mergeCommitSha":"ddbbd965d85dcae23464286188c9dd5ed0a68fab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2288","title":"[RFC] User and Team graphs are side-by-side","createdAt":"2022-07-13T18:50:30Z"}
{"state":"Merged","mergedAt":"2022-07-13T21:06:45Z","number":2289,"body":"Fix code highlighting due to moving from background script to content script.\r\n\r\nUtilizes PromiseProxyClientFn","mergeCommitSha":"40a344be9b6109eaacaa6cc6a47d0dc2ad9e3270","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2289","title":"Jeff/unb 428 fix code highlighting in web extension","createdAt":"2022-07-13T20:37:50Z"}
{"state":"Merged","mergedAt":"2022-02-02T23:09:48Z","number":229,"body":"We need to modify annotations of subchart.\r\n\r\nhttps://helm.sh/docs/chart_template_guide/subcharts_and_globals/","mergeCommitSha":"5ff1fed0c1f5e8695e462e4c7458148d517b8c8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/229","title":"Fix annotations","createdAt":"2022-02-02T23:07:47Z"}
{"state":"Merged","mergedAt":"2022-07-15T17:00:02Z","number":2290,"body":"<img width=\"1787\" alt=\"CleanShot 2022-07-13 at 14 06 16@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-73447da4-5c60-43b5-8751-c6e71d6b9d97.png\">\r\n<img width=\"1455\" alt=\"CleanShot 2022-07-13 at 14 05 57@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-eb2b1c80-563e-4894-b1b8-109d446826ae.png\">\r\n<img width=\"1577\" alt=\"CleanShot 2022-07-13 at 13 54 00@2x\" src=\"https://user-images.githubusercontent.com/1553313/178835105-838c675a-f1e5-47ae-a516-c849c8446bb7.png\">\r\n<img width=\"1526\" alt=\"CleanShot 2022-07-13 at 13 53 56@2x\" src=\"https://user-images.githubusercontent.com/1553313/178835124-4560f6c3-b31a-4b5e-ba81-d6b70ac388c5.png\">\r\n\r\n","mergeCommitSha":"7637fafc515ce4c6f54becfaeb8f23c1972eca7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2290","title":"Jeff/unb 413 show bubbles in the GitHub diff view","createdAt":"2022-07-13T20:40:25Z"}
{"state":"Merged","mergedAt":"2022-07-13T21:18:38Z","number":2291,"mergeCommitSha":"6dd49150779dc9cb451e17aa68933fe95d763462","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2291","title":"Admin web sorts users by last viewed","createdAt":"2022-07-13T21:05:46Z"}
{"state":"Merged","mergedAt":"2022-07-13T22:17:18Z","number":2292,"body":"Since sendgrid is being a fucking PITA, fuck it, I'll just use amazon pinpoint.\r\n\r\nFuck you sendgrid.","mergeCommitSha":"8ea42480f97b4fc47ebcfbdbae85fd2c1edd0f50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2292","title":"Follow up emails for welcome","createdAt":"2022-07-13T21:15:11Z"}
{"state":"Merged","mergedAt":"2022-07-14T15:59:10Z","number":2293,"body":"<img width=\"1019\" alt=\"CleanShot 2022-07-13 at 15 32 36@2x\" src=\"https://user-images.githubusercontent.com/1553313/178848329-b87848c3-61a9-4b0b-9f1a-9ee82073f1a0.png\">\r\n","mergeCommitSha":"c7a843d91f7e06e47393ee4d7d1fb6ae08f7a037","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2293","title":"Jeff/unb 433 sourcemark popover hidden under fold","createdAt":"2022-07-13T22:33:10Z"}
{"state":"Merged","mergedAt":"2022-07-13T22:50:29Z","number":2294,"body":"per https://chapter2global.slack.com/archives/C02VCS8L4R5/p1657749201326489","mergeCommitSha":"d85b4d950f2b2214530f0f22c85962c8798c8520","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2294","title":"Git blame on createDiscussion should filter out noreply emails","createdAt":"2022-07-13T22:34:11Z"}
{"state":"Merged","mergedAt":"2022-07-13T23:38:59Z","number":2295,"body":"For dashboard, we do *not* want to pass repoIDs as part of mine/recommended/archived at requests.\r\n\r\nLarge orgs === lots of repos === long query parameter === 494 error \r\n<img width=\"898\" alt=\"CleanShot 2022-07-13 at 16 17 26@2x\" src=\"https://user-images.githubusercontent.com/1553313/178853565-fe413b56-cd08-46bd-8190-8e084fd19cd8.png\">\r\n\r\n<img width=\"1155\" alt=\"CleanShot 2022-07-13 at 16 25 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/178853589-637c692d-aa11-4e4c-8362-5af054a9c70c.png\">\r\n\r\n","mergeCommitSha":"8abca50d019cbec1f81e0b52dbd52dc51d845f11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2295","title":"Fetch all threads for web regardless of repoIDs","createdAt":"2022-07-13T23:25:38Z"}
{"state":"Merged","mergedAt":"2022-07-14T00:17:44Z","number":2296,"mergeCommitSha":"28e79030a3e81de76490f03204c0e0eb24e190f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2296","title":"Add lambda tests","createdAt":"2022-07-13T23:59:38Z"}
{"state":"Merged","mergedAt":"2022-07-14T17:00:26Z","number":2297,"body":"I finally cracked this thing, what a mess.\r\n\r\nThis code tries to restrict Intercom to only being active in a single webview at at time:\r\n* WebviewContentController now tracks a set of webviews that are willing to display intercom, and tracks a single \"primary\" intercom target.  This primary target is the only one that actually has Intercom enabled, so if a message is received, it only appears in one webview.\r\n* When webviews are created, destroyed, and go through visibility state changes, WebviewContentController recalculates who is the primary intercom target.\r\n* Attempting to open Intercom on a secondary intercom window forces that window to claim primary intercom target status.\r\n* As part of this, the individual flags where we specify which webviews use intercom or not has been moved from the root webview code (where we render each webview), into the extension, where we create the WebviewContentController.\r\n\r\nIntercom in web extensions and dashboard should not change at all.\r\n\r\nThis is probably quite fragile, and AFAICT it's pretty much impossible to write any tests for this, since it's largely based on built-in VSCode behaviour.  So, we'll have to be careful if this stuff is changed.\r\n\r\nLong term we may want to consider ditching Intercom, if any of the alternatives are better.","mergeCommitSha":"670bf37404d7eadc4a71b7c1d905f5fc2f9bafbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2297","title":"Ensure intercom only displays on one webview at a time","createdAt":"2022-07-14T00:05:24Z"}
{"state":"Merged","mergedAt":"2022-07-14T04:41:04Z","number":2298,"body":"- Unread count nulls\r\n- Version breadcrumb\r\n- Release badge colours\r\n- Mobile responsiveness\r\n- Add teams to People page\r\n- Remove sourcemark backfill migration\r\n- Move sourcemarks to thread page\r\n- Many style tweaks\r\n- Search for identities\r\n- More PR info (state, commit id, comment link)\r\n\r\n<img width=\"700\" alt=\"Screen Shot 2022-07-13 at 21 24 57\" src=\"https://user-images.githubusercontent.com/1798345/178898301-c5dd45f1-5794-4dd8-9893-8e09cb14186b.png\">\r\n\r\n","mergeCommitSha":"0dfdfbdf04070a2dc26e4f59624125d701554c36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2298","title":"Admin web improvements","createdAt":"2022-07-14T04:23:56Z"}
{"state":"Merged","mergedAt":"2022-07-14T20:00:06Z","number":2299,"body":"Cleanup https://github.com/NextChapterSoftware/unblocked/commit/8abca50d019cbec1f81e0b52dbd52dc51d845f11","mergeCommitSha":"9b667dcc4c937a7a8548e7da6f563291ea3792c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2299","title":"Refactor threads with better types","createdAt":"2022-07-14T05:41:34Z"}
{"state":"Merged","mergedAt":"2022-01-07T21:34:07Z","number":23,"body":"Setup shared configs between VSCode & Web.\r\n\r\nMoved majority of config settings into a root `SharedConfigs` directory.\r\nThe equivalent Web & VSCode configs then import or reference their corresponding parent from the `SharedConfigs` directory.\r\n\r\nHope is to keep the two clients consistent in regards to styling and code conventions.\r\n\r\nMajority of the configs only allow you to reference file paths with the CLI. What they typically do is recursively move up the directories until it files a config file. aka we would need to place these shared config files at the root of the parent folder, not within a sharedConfigs folder. Therefore, when possible, updated the configs to JS and imported shared configs into individual config files.\r\n\r\n","mergeCommitSha":"098d74e166033436722765856c74012e12307590","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/23","title":"Setup shared configs","createdAt":"2022-01-07T00:00:57Z"}
{"state":"Merged","mergedAt":"2022-02-02T23:15:44Z","number":230,"mergeCommitSha":"680d814a958a2b3c3011db370744888ca4f4e7ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/230","title":"Fixes failed displayName parse issue","createdAt":"2022-02-02T23:11:34Z"}
{"state":"Merged","mergedAt":"2022-07-14T16:45:50Z","number":2300,"mergeCommitSha":"8387ab42c4a7e9afc996c2e0287cc3048e917f58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2300","title":"Keeping dlam out of prod db","createdAt":"2022-07-14T16:24:15Z"}
{"state":"Merged","mergedAt":"2022-07-14T17:03:47Z","number":2301,"mergeCommitSha":"3b8c07e033da021b6d58a1754cbe6711fe235040","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2301","title":"Only show metrics from 2022-07-07","createdAt":"2022-07-14T16:50:34Z"}
{"state":"Merged","mergedAt":"2022-07-15T07:08:52Z","number":2302,"body":"Currently the total count includes internal teams. This PR fixes that.\r\n\r\n![CleanShot 2022-07-14 at 10 06 45@2x](https://user-images.githubusercontent.com/1924615/179042034-872f4aec-5e8e-4507-93f7-585e3ba7b06d.png)\r\n\r\nTotal users still includes internal users, so I'll need to fix that in a separate PR.","mergeCommitSha":"c6efab13796cd755890a24e3a060bce90e4a572d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2302","title":"Only count external teams","createdAt":"2022-07-14T17:09:30Z"}
{"state":"Merged","mergedAt":"2022-07-15T22:50:23Z","number":2303,"body":"Email settings:\r\n<img width=\"398\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/179045508-bf1d8dfd-a75f-49ff-8cfe-8aa8c1c4cb7d.png\">\r\n<img width=\"1507\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/179044678-9fb6827a-d081-4ab7-893e-6ea36c9c7339.png\">\r\n\r\n* Required a bit of refactoring from the DiscussionThread UI to share the same styles\r\n* Added a Toggle component that leverages the [headlessui/Switch component ](https://headlessui.com/react/switch)\r\n* A bit of refactoring to the LayoutContext for the new layout and breakpoints requested by Ben\r\n* Other small layout nits","mergeCommitSha":"71ae2df609906da29e0c28ebe30a88de3a4f8d88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2303","title":"UNB-405 [Dashboard] Add user email settings","createdAt":"2022-07-14T17:31:53Z"}
{"state":"Merged","mergedAt":"2022-07-14T22:27:15Z","number":2304,"mergeCommitSha":"d6c40ec6683dcf07a0b9b7e92c5618bc71c14ccb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2304","title":"Adds VersionInfoStore upsert","createdAt":"2022-07-14T18:07:15Z"}
{"state":"Merged","mergedAt":"2022-07-14T19:16:37Z","number":2305,"body":"Move Auth window creation back to background.\r\n\r\nHad been moved to Content script during refactor. Looks like creating a window from the content script triggers Safari's native Popup blocker. Not an issue in chrome.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/179055022-e718937b-4e94-4959-921c-c69017ce3644.mp4\r\n\r\n\r\n","mergeCommitSha":"679386741d3eed53db5520efe5f2b456559e9087","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2305","title":"Move window creation to background","createdAt":"2022-07-14T18:24:16Z"}
{"state":"Merged","mergedAt":"2022-07-14T19:05:20Z","number":2306,"body":"![CleanShot 2022-07-14 at 11 51 13](https://user-images.githubusercontent.com/13431372/179060575-1e1c8684-59fd-4428-b5b0-3b88d524e424.gif)\r\n\r\n* Issue was that the title was being updated on every key press causing the view to reload and remount, moving the cursor to the end \r\n* Chatted with Ben, can move the actual title updating to the blur event of the input so avoid any unnecessary behaviour while the user is typing ","mergeCommitSha":"2cd114beae7426a4905b6c995f9167580247dfb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2306","title":"UNB-439 Move title saving to input blur","createdAt":"2022-07-14T18:51:47Z"}
{"state":"Merged","mergedAt":"2022-07-14T20:57:49Z","number":2307,"body":"![image](https://user-images.githubusercontent.com/13431372/179081753-dc562679-455e-4fd5-bf39-62e295d33000.png)\r\n","mergeCommitSha":"2c62c54552f0b74cd46dd075dcb02f60cb193ba3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2307","title":"Style the download error dialog","createdAt":"2022-07-14T20:49:00Z"}
{"state":"Merged","mergedAt":"2022-07-15T16:29:44Z","number":2308,"body":"Update timeout to better support slow networks.","mergeCommitSha":"84a9045ffdb7e9c3c6469c5861f90e1abfcf6da2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2308","title":"Update timeout","createdAt":"2022-07-14T21:05:43Z"}
{"state":"Merged","mergedAt":"2022-07-14T23:01:10Z","number":2309,"body":"## Summary\r\n- Add installer support to drop bits for _both_ vscode and vscodium\r\n- Add hub support for app launch and foreground support for vscodium\r\n\r\nResolves UNB-436","mergeCommitSha":"bf41d0434dc320d1e220ccd0e6d9e15853942712","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2309","title":"VSCodium Support","createdAt":"2022-07-14T21:28:21Z"}
{"state":"Merged","mergedAt":"2022-02-02T23:15:03Z","number":231,"mergeCommitSha":"9647b9f635b858059dcb7053ad41e1fcb9552ee6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/231","title":"fixbranches","createdAt":"2022-02-02T23:14:58Z"}
{"state":"Merged","mergedAt":"2022-07-14T23:07:50Z","number":2310,"body":"![CleanShot 2022-07-14 at 14 51 27](https://user-images.githubusercontent.com/13431372/179093301-fa06d5a7-f9c3-48e4-906c-0c4985ab742d.gif)\r\n","mergeCommitSha":"d2c9e524ac3a2802b4a521fcce00db92578a0cf3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2310","title":"Add ability to copy filepath from dashboard","createdAt":"2022-07-14T21:52:52Z"}
{"state":"Merged","mergedAt":"2022-07-25T16:59:53Z","number":2311,"body":"Navigate directly to thread comment in PR instead of PR itself.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/179097483-b64186e8-09d7-4515-9a98-8640a070245c.mp4\r\n\r\nFixes: unb-485","mergeCommitSha":"3fe1f0d8bdce89c3229ebdc108bb2223ba2877bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2311","title":"Navigate to PR Comment instead of top","createdAt":"2022-07-14T22:14:20Z"}
{"state":"Merged","mergedAt":"2022-07-14T22:58:49Z","number":2312,"body":"https://linear.app/unblocked/issue/UNB-444/new-repos-added-from-github-are-not-getting-created\r\n\r\nI think this is the general lesson to learn from this:\r\n1. for updates: it's better to use as small a transaction as possible while ensuring data integrity\r\n2. for inserts: it still makes sense to batch","mergeCommitSha":"8b5172d1ddb272f03f7154d47ba8077a3eababbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2312","title":"Fix: New repos added from GitHub are not getting created","createdAt":"2022-07-14T22:44:59Z"}
{"state":"Merged","mergedAt":"2022-07-15T19:28:45Z","number":2313,"body":"- Introduced more config params for version management \r\n- Added function to S3 provider to help with reading manifest files from private paths e.g /builds \r\n- Modified `makeObsolete` and `releaseToChannel` method so they would take care of artifact moves before state changes \r\n- Added function to scan `/builds` directory and call ingestion on each existing artifact\r\n- Added `pruneBuilds` function to remove old artifacts based on cutoff specified in config. Currently we have this disabled in the background job until we add `delete` capability to `versionStore`\r\n- Added background job to run auto ingestion every 1 minute \r\n- Added tests to fully verify artifact release process locally using awslocalstack ","mergeCommitSha":"9fdb524d72d265b4ee552283c4cf18a635d4652a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2313","title":"Add support for release artifact polling","createdAt":"2022-07-14T22:55:04Z"}
{"state":"Merged","mergedAt":"2022-07-15T04:39:57Z","number":2314,"body":"- fixes:\n  https://linear.app/unblocked/issue/UNB-445/failed-to-process-installation-created-hook\n\n- adds debugging to help with this:\n  https://linear.app/unblocked/issue/UNB-444/new-repos-added-from-github-are-not-getting-created","mergeCommitSha":"c4eeb8d82c2a081e14e9b0860ac94b4c49e55dda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2314","title":"Add webhook handler logging and fix org lookup bug","createdAt":"2022-07-15T04:26:40Z"}
{"state":"Merged","mergedAt":"2022-07-15T05:04:57Z","number":2315,"mergeCommitSha":"091b7beb7ab43072e007019251e59b6d67be05f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2315","title":"Basic sendInBlue implementation","createdAt":"2022-07-15T04:42:19Z"}
{"state":"Merged","mergedAt":"2022-07-15T06:21:12Z","number":2316,"body":"We were installing incoming repo hook requests on the first repo found in the DB,\r\nregardless of the team. Turns out that the first repo happened to be a deleted team.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-444/new-repos-added-from-github-are-not-getting-created","mergeCommitSha":"2a2cdcbc5bb991fc423e21a6c8665a7978ed2143","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2316","title":"Install repo on the right team","createdAt":"2022-07-15T05:47:35Z"}
{"state":"Merged","mergedAt":"2022-07-15T15:35:20Z","number":2317,"body":"Do not try to PR ingest when:\n- a repo is disconnected\n- a team is disconnected\n- a team is deleted","mergeCommitSha":"e1b394ea10bac421ead070437f421f672622d982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2317","title":"Do not attempt to PR ingest disconnected repos","createdAt":"2022-07-15T07:04:31Z"}
{"state":"Merged","mergedAt":"2022-07-16T00:59:02Z","number":2318,"body":"Fixes these duplicates:\r\n\r\n<table>\r\n<tr><td>\r\n<img width=\"821\" alt=\"Screen Shot 2022-07-15 at 15 46 42\" src=\"https://user-images.githubusercontent.com/1798345/179321349-4fdb907f-22b3-4322-84f5-b0593270edfb.png\">\r\n</td></tr>\r\n</table>\r\n\r\nhttps://linear.app/unblocked/issue/UNB-430/web-extension-shows-duplicate-identical-source-marks","mergeCommitSha":"c43ec74c14ff4b636eb28850af7c028f16311303","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2318","title":"Fix: Web extension shows duplicate identical source marks","createdAt":"2022-07-15T16:00:34Z"}
{"state":"Merged","mergedAt":"2022-07-15T16:09:06Z","number":2319,"body":"Forgot about the goofy bash array expansion rules","mergeCommitSha":"dd127d4e3a11460ae8501f9ce99d8d38d94d83e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2319","title":"Fix installer script","createdAt":"2022-07-15T16:07:14Z"}
{"state":"Merged","mergedAt":"2022-02-03T22:28:46Z","number":232,"body":"Web was a pretty straight forward change.\r\nUpdated login page to render login button based on login providers.\r\n\r\nVSCode required larger changes as we only wanted to show relevant items within command palette.\r\nVSCode now has state aware commands related to auth.\r\n\r\nRemoved the need for custom codegen templates.","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/232","title":"Update Web & VSCode for LoginOptions API change","createdAt":"2022-02-02T23:38:05Z"}
{"state":"Merged","mergedAt":"2022-07-15T17:43:19Z","number":2320,"body":"Just getting the ball rolling on syncing GitHub reactions.\r\n\r\nThis will help determine whether a GitHub comment has reactions. Next step is to call the reactions API for a comment with reactions, so that we can tell which user reacted (next PR).","mergeCommitSha":"ca2f3d6f20675e33d0589be5a205522b49ef9e81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2320","title":"Expose reactions property on GitHub comments","createdAt":"2022-07-15T17:15:32Z"}
{"state":"Merged","mergedAt":"2022-07-15T19:28:03Z","number":2321,"body":"Adding logic here to suck down reactions+reactors for a comment. The intention here is to only call this for comments with reactions.\r\n\r\nNot used for now. We should only hit this API once we have everything in place to show reactions, since this will cost an API request. ","mergeCommitSha":"4c012794f58b6e56421eeb1c46dbcf23ab2148f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2321","title":"Get comment reactions from the GitHub API","createdAt":"2022-07-15T17:46:13Z"}
{"state":"Merged","mergedAt":"2022-07-15T19:52:39Z","number":2322,"body":"![Screen Shot 2022-07-15 at 12.41.09.png](https://graphite-user-uploaded-assets.s3.amazonaws.com/YvgTLHS5ceSd78zAcJxj/4a9397f4-8c49-4c2d-ad15-aa6ca3809e18/Screen%20Shot%202022-07-15%20at%2012.41.09.png)","mergeCommitSha":"e87eba1b869e061ebc46b1427d705e7d1668f623","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2322","title":"Inline version ingest button","createdAt":"2022-07-15T19:40:15Z"}
{"state":"Merged","mergedAt":"2022-07-15T20:37:31Z","number":2323,"body":"- Deployed changes to grant AdminWeb permissions on the new releases S3 bucket\r\n- Added two lines of log to know when ingestion jobs start and finish ","mergeCommitSha":"7c3238d4d371a576296cf896f27f75d2971fbe9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2323","title":"add more logging and s3 permissions","createdAt":"2022-07-15T19:59:22Z"}
{"state":"Merged","mergedAt":"2022-07-15T20:24:53Z","number":2324,"body":"<img width=\"659\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/179304666-a746f831-b5d2-4e63-bbb4-3b38148bf4c5.png\">\r\n","mergeCommitSha":"a1d07188fda424c5a78bfc6a76b6e9d0359aadd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2324","title":"Email template","createdAt":"2022-07-15T20:19:29Z"}
{"state":"Merged","mergedAt":"2022-07-15T21:13:21Z","number":2325,"body":"Releases bucket is created in standard region and cli doesn't like it when we call it via us-west-2. Even though S3 is global!!\r\n\r\nException message:\r\n```\r\nsoftware.amazon.awssdk.services.s3.model.S3Exception: The bucket you are attempting to access must be addressed using the specified endpoint. Please send all future requests to this endpoint. (Service: S3, Status Code: 301, Request ID: XJ8CJ5M1DFM4EKBG, Extended Request ID: igjjFyfCEyKlzDQMMA6KvDo8waU7rZZAzQK+SuFXAeOkMlmZYgGz0IT6oZHDJjaUIeVf6ZI/mHY=)\r\n\tat software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handleErrorResponse(AwsXmlPredicatedResponseHandler.java:156)\r\n\tat software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handleResponse(AwsXmlPredicatedResponseHandler.java:108)\r\n\tat software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handle(AwsXmlPredicatedResponseHandler.java:85)\r\n\tat software.amazon.awssdk.protocols.xml.internal.unmarshall.AwsXmlPredicatedResponseHandler.handle(AwsXmlPredicatedResponseHandler.java:43)\r\n\tat software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler$Crc32ValidationResponseHandler.handle(AwsSyncClientHandler.java:95)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseClientHandler.lambda$successTransformationResponseHandler$7(BaseClientHandler.java:245)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:40)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.HandleResponseStage.execute(HandleResponseStage.java:30)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:73)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptTimeoutTrackingStage.execute(ApiCallAttemptTimeoutTrackingStage.java:42)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:78)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.TimeoutExceptionHandlingStage.execute(TimeoutExceptionHandlingStage.java:40)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:50)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallAttemptMetricCollectionStage.execute(ApiCallAttemptMetricCollectionStage.java:36)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:81)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.RetryableStage.execute(RetryableStage.java:36)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:56)\r\n\tat software.amazon.awssdk.core.internal.http.StreamManagingStage.execute(StreamManagingStage.java:36)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.executeWithTimer(ApiCallTimeoutTrackingStage.java:80)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:60)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallTimeoutTrackingStage.execute(ApiCallTimeoutTrackingStage.java:42)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:48)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ApiCallMetricCollectionStage.execute(ApiCallMetricCollectionStage.java:31)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.RequestPipelineBuilder$ComposingRequestPipelineStage.execute(RequestPipelineBuilder.java:206)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:37)\r\n\tat software.amazon.awssdk.core.internal.http.pipeline.stages.ExecutionFailureExceptionReportingStage.execute(ExecutionFailureExceptionReportingStage.java:26)\r\n\tat software.amazon.awssdk.core.internal.http.AmazonSyncHttpClient$RequestExecutionBuilderImpl.execute(AmazonSyncHttpClient.java:193)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.invoke(BaseSyncClientHandler.java:103)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.doExecute(BaseSyncClientHandler.java:167)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.lambda$execute$1(BaseSyncClientHandler.java:82)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.measureApiCallSuccess(BaseSyncClientHandler.java:175)\r\n\tat software.amazon.awssdk.core.internal.handler.BaseSyncClientHandler.execute(BaseSyncClientHandler.java:76)\r\n\tat software.amazon.awssdk.core.client.handler.SdkSyncClientHandler.execute(SdkSyncClientHandler.java:45)\r\n\tat software.amazon.awssdk.awscore.client.handler.AwsSyncClientHandler.execute(AwsSyncClientHandler.java:56)\r\n\tat software.amazon.awssdk.services.s3.DefaultS3Client.listObjectsV2(DefaultS3Client.java:6430)\r\n\tat com.nextchaptersoftware.aws.s3.StandardS3Provider.listObjects(S3Provider.kt:38)\r\n\tat com.nextchaptersoftware.aws.s3.S3Provider$DefaultImpls.listObjects$default(S3Provider.kt:18)\r\n\tat com.nextchaptersoftware.version.VersionService.scanForBuilds(VersionService.kt:167)\r\n\tat com.nextchaptersoftware.adminwebservice.jobs.BuildIngestionJob.run(BuildIngestionJob.kt:16)\r\n\tat com.nextchaptersoftware.service.PollingBackgroundJob$run$2$2$1$1.invokeSuspend(PollingBackgroundJob.kt:46)\r\n\tat com.nextchaptersoftware.service.PollingBackgroundJob$run$2$2$1$1.invoke(PollingBackgroundJob.kt)\r\n\tat com.nextchaptersoftware.service.PollingBackgroundJob$run$2$2$1$1.invoke(PollingBackgroundJob.kt)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt$withSpan$2.invokeSuspend(SpanCoroutine.kt:27)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt$withSpan$2.invoke(SpanCoroutine.kt)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt$withSpan$2.invoke(SpanCoroutine.kt)\r\n\tat kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:89)\r\n\tat kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:169)\r\n\tat kotlinx.coroutines.BuildersKt.withContext(Unknown Source)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan(SpanCoroutine.kt:26)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan(SpanCoroutine.kt:47)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan(SpanCoroutine.kt:75)\r\n\tat com.nextchaptersoftware.trace.coroutine.SpanCoroutineKt.withSpan$default(SpanCoroutine.kt:69)\r\n\tat com.nextchaptersoftware.service.PollingBackgroundJob$run$2.invokeSuspend(PollingBackgroundJob.kt:42)\r\n\tat kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\r\n\tat kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)\r\n\tat kotlinx.coroutines.internal.LimitedDispatcher.run(LimitedDispatcher.kt:42)\r\n\tat kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:95)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:749)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)\r\n```","mergeCommitSha":"1fcc62fd1f2d40204acfe4dc0e2be7aa851a350e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2325","title":"fix bucket regions. Almost there","createdAt":"2022-07-15T20:57:59Z"}
{"state":"Merged","mergedAt":"2022-07-15T22:31:55Z","number":2326,"body":"Looks like path prefixes on real S3 might be different than localstack when it comes to leading slash. Right now scans run but they don't pick up on any artifacts. This might fix it","mergeCommitSha":"b36c551afcf49bde41b553f8bb016850f8871793","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2326","title":"Fixing path prefixes for real s3","createdAt":"2022-07-15T22:15:08Z"}
{"state":"Merged","mergedAt":"2022-07-15T23:10:45Z","number":2327,"mergeCommitSha":"89d5ed85a2cf7b92a2613d56c5cc45aa9fc8ac92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2327","title":"UserId for emails","createdAt":"2022-07-15T22:59:39Z"}
{"state":"Merged","mergedAt":"2022-07-15T23:24:38Z","number":2328,"mergeCommitSha":"af2296092c6155f69bd5500a63e1ee7f821aa216","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2328","title":"Add s3 bucket to local stack","createdAt":"2022-07-15T23:18:24Z"}
{"state":"Merged","mergedAt":"2022-07-16T04:23:25Z","number":2329,"body":"- All tables are now sortable.\r\n\r\n- Future: Can do much more with DataTables, like pagination and search; but it didn't work well so I disabled for now until I can get it working properly.\r\n\r\n<img width=\"942\" alt=\"Screen Shot 2022-07-15 at 21 09 44\" src=\"https://user-images.githubusercontent.com/1798345/179338703-b22d41f7-a1bd-4d96-92cf-b19266194754.png\">\r\n","mergeCommitSha":"55eb28ce6c67b273dc265b7c09c47517cc17e7b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2329","title":"Admin web data tables","createdAt":"2022-07-16T04:06:55Z"}
{"state":"Merged","mergedAt":"2022-02-03T04:14:05Z","number":233,"mergeCommitSha":"0f4c2c226d2766aa974236f045672820b2c604ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/233","title":"Create auth tokens for GitHub app S2S auth","createdAt":"2022-02-02T23:38:51Z"}
{"state":"Merged","mergedAt":"2022-07-18T05:42:07Z","number":2330,"body":"Fixes this number so that it doesn't include persons that are a member of an internal team\r\n\r\n![CleanShot 2022-07-16 at 21 45 08@2x](https://user-images.githubusercontent.com/1924615/179384300-9310e0ad-a365-41ed-a89e-43f1434be048.png)\r\n","mergeCommitSha":"eb977567277019bfe0576a06b41d4945eb2e29c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2330","title":"Don't include internal users in total users count","createdAt":"2022-07-17T04:46:22Z"}
{"state":"Closed","mergedAt":null,"number":2331,"body":"Immediate motivation for doing this is to reduce Honeycomb EPM.\n\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1658165614087419","mergeCommitSha":"26ea59b33431880bb6bf58744c366316a2fe5960","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2331","title":"Tear down video service as it is not used currently","createdAt":"2022-07-18T17:47:38Z"}
{"state":"Merged","mergedAt":"2022-07-18T18:39:07Z","number":2332,"mergeCommitSha":"4590a7d8ffda83e60f7e33cbcf7ac405e85e8062","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2332","title":"update","createdAt":"2022-07-18T18:39:01Z"}
{"state":"Merged","mergedAt":"2022-07-18T20:32:28Z","number":2333,"body":"Rather than polluting the production intercom app, I'm using a sandboxed test app nested underneath our main app.\r\nScrewing around with intercom gives me the heepie jeepies when it's with production.\r\n\r\nhttps://www.intercom.com/help/en/articles/188-create-a-test-workspace-in-intercom","mergeCommitSha":"f68119423a7b8cc6508c38742a19408517cb48b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2333","title":"UseSandboxedIntercomeEnvironment","createdAt":"2022-07-18T20:16:42Z"}
{"state":"Merged","mergedAt":"2022-07-18T21:50:52Z","number":2334,"body":"![CleanShot 2022-07-18 at 13 53 40](https://user-images.githubusercontent.com/13431372/179615628-f12ee65a-3244-42dc-ae23-58982d451689.gif)\r\n\r\nextension:\r\n<img width=\"679\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/179619333-6a68c16f-3c6b-4799-9aa2-54d7a70a66f1.png\">\r\n\r\nAlso fix color of the invite team member icon\r\n","mergeCommitSha":"41df032b55b4fad0f08d8056b0fce16c18954537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2334","title":"Add checkbox to select/unselect all in create thread","createdAt":"2022-07-18T21:22:12Z"}
{"state":"Merged","mergedAt":"2022-07-18T22:03:54Z","number":2335,"mergeCommitSha":"233549c5173c7483380eaac60b2cc1d95a38d00d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2335","title":"Extend auth token expiry to 7 days","createdAt":"2022-07-18T21:49:52Z"}
{"state":"Merged","mergedAt":"2022-07-18T22:14:59Z","number":2336,"body":"Reason we have not encountered this before is because we have always _pre-installed_ the org,\nand org maintenance would have kicked in eventually.","mergeCommitSha":"82818956cffa77089541801b19a5d45f1a0f59c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2336","title":"Create PR ingestion model as part of org installation","createdAt":"2022-07-18T22:02:04Z"}
{"state":"Merged","mergedAt":"2022-07-18T22:21:09Z","number":2337,"mergeCommitSha":"e65882c4ead4162898ddb15a522cb8ec09aa8ae5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2337","title":"Ingest prioritized repos first during onboarding","createdAt":"2022-07-18T22:02:29Z"}
{"state":"Merged","mergedAt":"2022-07-18T22:29:03Z","number":2338,"mergeCommitSha":"8c05396e9733bffe079c466df59112fa1e5f27af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2338","title":"Temporarily disable dev smoke tests","createdAt":"2022-07-18T22:28:28Z"}
{"state":"Merged","mergedAt":"2022-07-18T23:27:59Z","number":2339,"mergeCommitSha":"37b8b77e3224174e6a54706dc4289555629b2a89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2339","title":"Skip for deleted teams","createdAt":"2022-07-18T23:26:14Z"}
{"state":"Merged","mergedAt":"2022-02-03T06:39:15Z","number":234,"body":"- Role for Grafana IAM User to consume CloudWatch metrics across accounts\r\n- Network policy to allow Grafana agent running on Kubernetes to scrape Kube System Metrics pod\r\n- Minor readme update ","mergeCommitSha":"fa3393653b3fdeadeae1a7c6c4f118e113c2ea4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/234","title":"IAM role setup for monitoring","createdAt":"2022-02-02T23:58:40Z"}
{"state":"Merged","mergedAt":"2022-07-19T00:44:33Z","number":2340,"body":"## Summary\r\n- Remove ability for VSCode to log the hub out (this was not the intended behaviour, the Hub should rule the roost)\r\n- Adds stack print when logout is invoked to help investigate unwanted logout events","mergeCommitSha":"54120649717dbf8b6ab4c59c33ed1bdb08041759","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2340","title":"Collect some logout debug info","createdAt":"2022-07-18T23:51:46Z"}
{"state":"Merged","mergedAt":"2022-07-20T22:39:55Z","number":2341,"body":"First cut for the 'Current File' sidebar in VSCode:\r\n\r\n* `TextEditorSourceMark` now publishes a stream of the threads it has loaded\r\n* `TextEditorSourceMarkManager` publishes the stream of loaded threads for whichever file has focus\r\n* `SidebarWebviewProvider` subscribes to this stream, pushes the data to the webview\r\n* `Sidebar` renders the new 'current file' data.\r\n\r\nNote:\r\n* This doesn't add anything to the main file explorer UI\r\n* The webview doesn't display 'loading' or 'no threads' state.  Will add in a follow-on PR.\r\n* There is no resizing in the bar above the view.  Will add in a follow-on PR.\r\n* Because of this, the UI is disabled for now.\r\n\r\n<img width=\"1580\" alt=\"Screen Shot 2022-07-18 at 5 19 11 PM\" src=\"https://user-images.githubusercontent.com/2133518/179638132-7800ccae-7c94-488e-9b38-fcd5a5dfff7e.png\">\r\n","mergeCommitSha":"026939085edc4ca200662622c83a57a7a139771c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2341","title":"VSCode 'Current File' sidebar panel v1","createdAt":"2022-07-19T00:22:09Z"}
{"state":"Merged","mergedAt":"2022-07-19T00:46:02Z","number":2342,"body":"This basically causes onboarding to hang because poller will fail to update threads when a new org is onboarding (pull request ingestion)","mergeCommitSha":"7c24970a16c796bc87b9db70721f9694be7bb5ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2342","title":"Fix thread mine url from web clients and vscode","createdAt":"2022-07-19T00:41:50Z"}
{"state":"Merged","mergedAt":"2022-07-20T00:34:10Z","number":2343,"body":"We're currently shunting pull requests to the priority queue as we page over the pull requests for a repo. In the worst case, the onboarding user may only have pull requests on the later pages.\r\n\r\nWe can do better: use the search API to ask for the onboarding customer's PRs and add those to the priority queue, then page over all pull requests to put onto the regular queue.\r\n\r\nWe need to use the user-to-server token here because this search query requires repository access (search query containing `repo:OWNER/REPONAME`) and our regular token does not have that.","mergeCommitSha":"1b3e5bd94480475eb7eb5f4eb3c409e3f583be8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2343","title":"Use search API to get priority pull requests","createdAt":"2022-07-19T08:41:54Z"}
{"state":"Merged","mergedAt":"2022-07-19T15:51:20Z","number":2344,"body":"Ingestion is borked. Dev and Prod appear to be racing each other somehow","mergeCommitSha":"0fe69d98c5e620344cccaddfe200747456d1c166","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2344","title":"Disable auto-ingestion in admin console","createdAt":"2022-07-19T15:49:54Z"}
{"state":"Merged","mergedAt":"2022-07-19T17:23:46Z","number":2345,"body":"```\nj.l.IllegalStateException: You can't change tag attribute because it was already passed to the downstream\n\tat k.h.c.DelayedConsumer.onTagAttributeChange(delayed-consumer.kt:16)\n\tat k.h.i.DelegatingMap.put(delegating-map.kt:27)\n\tat k.h.i.DelegatingMap.put(delegating-map.kt:5)\n\tat c.n.a.a.c.AvatarKt.avatar(Avatar.kt:16)\n\tat c.n.a.a.p.ThreadPageKt.renderMessages(ThreadPage.kt:401)\n\tat c.n.a.a.p.ThreadPageKt$renderThreadPage$2$2.invoke(ThreadPage.kt:113)\n\tat c.n.a.a.p.ThreadPageKt$renderThreadPage$2$2.invoke(ThreadPage.kt:98)\n\tat i.k.s.h.Placeholder.apply(Template.kt:29)\n\tat i.k.s.h.TemplateKt.insert(Template.kt:82)\n\tat c.n.a.a.t.ContentTemplate.apply(ContentTemplate.kt:37)\n\tat c.n.a.a.t.ContentTemplate.apply(ContentTemplate.kt:18)\n\tat i.k.s.h.RespondHtmlTemplateKt$respondHtmlTemplate$2.invoke(RespondHtmlTemplate.kt:21)\n\tat i.k.s.h.RespondHtmlTemplateKt$respondHtmlTemplate$2.invoke(RespondHtmlTemplate.kt:21)\n\tat i.k.s.h.HtmlContent.writeTo(RespondHtml.kt:60)\n\tat i.k.s.p.c.CompressedWriteResponse$writeTo$2.invokeSuspend(Compression.kt:181)\n\tat i.k.s.p.c.CompressedWriteResponse$writeTo$2.invoke(Compression.kt)\n\tat i.k.s.p.c.CompressedWriteResponse$writeTo$2.invoke(Compression.kt)\n\tat k.c.i.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:89)\n\tat k.c.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:264)\n\tat i.k.s.p.c.CompressedWriteResponse.writeTo(Compression.kt:179)\n\tat i.k.s.e.BaseApplicationResponse$respondWriteChannelContent$2$1.invokeSuspend(BaseApplicationResponseJvm.kt:173)\n\tat k.c.j.i.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\n\tat k.c.DispatchedTask.run(DispatchedTask.kt:106)\n\tat k.c.i.LimitedDispatcher.run(LimitedDispatcher.kt:42)\n\tat k.c.s.TaskImpl.run(Tasks.kt:95)\n\tat k.c.s.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\n\tat k.c.s.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:749)\n\tat k.c.s.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\n\tat k.c.s.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)\n```","mergeCommitSha":"fc1b93ea80a21676d9286970357b8b0c7370a538","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2345","title":"Fix crash (502) in unreads section of thread page","createdAt":"2022-07-19T17:10:31Z"}
{"state":"Merged","mergedAt":"2022-07-19T18:34:47Z","number":2346,"body":"Revert VersionInfo upsert to insert","mergeCommitSha":"21c8c5935b78423abd4c029056f364b5fef7c2a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2346","title":"Fix build ingestion","createdAt":"2022-07-19T17:58:29Z"}
{"state":"Merged","mergedAt":"2022-07-19T23:23:15Z","number":2347,"body":"Only use before cursor for paging through thread info collection when we know that there are more pages.\r\n\r\nMost of the changes are benign, just lifting the limit coersion to API layer.\r\n\r\nhttps://chapter2global.slack.com/archives/C03Q1EPV3EY/p1658246319662709","mergeCommitSha":"87b996e58aa98288bd6e3d30a573ace31ed5a8c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2347","title":"ThreadInfo paging is cursor based only when there are more items","createdAt":"2022-07-19T18:55:22Z"}
{"state":"Merged","mergedAt":"2022-07-19T21:00:51Z","number":2348,"mergeCommitSha":"bc73477a586e5edb8bf4fcec99ecca865b4f1b73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2348","title":"Add intercom client","createdAt":"2022-07-19T19:37:26Z"}
{"state":"Merged","mergedAt":"2022-07-19T22:35:53Z","number":2349,"body":"<img width=\"638\" alt=\"Screen Shot 2022-07-19 at 13 19 51\" src=\"https://user-images.githubusercontent.com/1798345/179840902-5eadd9ba-cb4d-432d-9099-8aa9c2bc2106.png\">\r\n\r\n- Incoming webhooks are listed here:\r\n  https://chapter2global.slack.com/apps/A0F7XDUAZ-incoming-webhooks?tab=settings\r\n\r\n- https://linear.app/unblocked/issue/UNB-409/slack-message-when-people-and-teams-sign-up\r\n\r\n- Need to deploy secrets.","mergeCommitSha":"4eb9adedf2db7087b5faeb64acf0e182f9a22934","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2349","title":"Send slack message when new user/team signs up","createdAt":"2022-07-19T19:37:46Z"}
{"state":"Merged","mergedAt":"2022-02-03T00:16:54Z","number":235,"mergeCommitSha":"1abba221182386b9c351c803dc10a4367e076bc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/235","title":"Add ability to update all service sub charts","createdAt":"2022-02-03T00:13:46Z"}
{"state":"Merged","mergedAt":"2022-07-20T22:15:43Z","number":2350,"body":"chrome:\r\n<img width=\"1312\" alt=\"Pasted Graphic 3\" src=\"https://user-images.githubusercontent.com/13431372/179827009-218c764a-287c-401c-9dc4-e01f9d481fcc.png\">\r\n\r\nsafari:\r\n<img width=\"1313\" alt=\"Pasted Graphic 4\" src=\"https://user-images.githubusercontent.com/13431372/179827037-8b394270-0fe3-47cb-a823-5b6841de26ca.png\">\r\n\r\n* Banner falls back to the Chrome content as a default \r\n* Add Banner component\r\n* Revamp Button styles per Ben's styleguide:\r\n<img width=\"756\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/179826527-f29dccf2-4498-47fc-aa19-20a875da50df.png\">\r\n* Add top level context provider for local storage -- this was necessary for the banner as we need the banner to show immediately after setting the token \r\n* Add helper to parse the useragent browser string (note that MDN recommends not doing this since the logic is brittle and the browsers can change its strings and break the logic pretty easily)\r\n* Fix some layout bugs\r\n","mergeCommitSha":"b5c4d41b263d66de6c608dbb85a06b357491d6ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2350","title":"Add banner for installing extension","createdAt":"2022-07-19T19:56:11Z"}
{"state":"Merged","mergedAt":"2022-07-22T22:32:20Z","number":2351,"body":"- Changed the obsolete step to avoid moving files from `releases` to `archived` directory \r\n- Removed everything related to `archived` directory. Moving forward we would just keep things in release directory \r\n- Added support for adding and updating tags on S3 object. Mainly used to mark an artifact as obsolete \r\n- Modified the release step to copy artifacts from `builds` directory to `releases` instead of a move \r\n- Updated tests  ","mergeCommitSha":"d06ad73c6f52c3b28513a45742130bbc26febb85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2351","title":"Switch to copy and tagging for version releases","createdAt":"2022-07-19T20:23:13Z"}
{"state":"Merged","mergedAt":"2022-07-19T21:37:51Z","number":2352,"mergeCommitSha":"07eb064c395d68856f92b8f417f03a6c372e990b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2352","title":"Disable","createdAt":"2022-07-19T21:37:44Z"}
{"state":"Merged","mergedAt":"2022-07-20T16:33:45Z","number":2353,"mergeCommitSha":"76a191f2e8a7e5e706f37f9f801c677857df497f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2353","title":"Add client bits","createdAt":"2022-07-19T21:38:30Z"}
{"state":"Merged","mergedAt":"2022-07-19T22:36:43Z","number":2354,"body":"Fix issues with source marks only rendering on initial load.\r\n\r\n","mergeCommitSha":"c749921bd5a8eb8990b471f8726baba9bbce6cfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2354","title":"Potential fix to turbo issues","createdAt":"2022-07-19T21:50:32Z"}
{"state":"Merged","mergedAt":"2022-07-19T22:31:02Z","number":2355,"body":"<img width=\"467\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/179854699-40badbe7-54ad-4659-a0e0-cb0e7ec205f6.png\">\r\n<img width=\"590\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/179854721-1ad9d350-6579-4ded-a490-be101f9c78a5.png\">\r\n","mergeCommitSha":"02c880d38eac30dc244014d809c2e19c6496aec7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2355","title":"Update select/unselect all checkbox styling","createdAt":"2022-07-19T21:51:41Z"}
{"state":"Merged","mergedAt":"2022-07-19T22:29:14Z","number":2356,"mergeCommitSha":"d9878cbf381c7c4474c6b74ce15a517d546552bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2356","title":"Reduce parallel","createdAt":"2022-07-19T21:55:34Z"}
{"state":"Closed","mergedAt":null,"number":2357,"mergeCommitSha":"68aec121ceee55c4782ea2f9f965ee53ae5d2bb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2357","title":"sendinblue secrets","createdAt":"2022-07-19T22:12:21Z"}
{"state":"Merged","mergedAt":"2022-07-20T05:12:13Z","number":2358,"body":"This reverts commit d9878cbf381c7c4474c6b74ce15a517d546552bd.\r\n","mergeCommitSha":"044d6e67fd619b370c4c5b958b9ce90b3ca244c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2358","title":"Revert \"Reduce parallel (#2356)\"","createdAt":"2022-07-19T22:30:37Z"}
{"state":"Merged","mergedAt":"2022-07-19T23:42:52Z","number":2359,"mergeCommitSha":"2da2f93744578c5b269b72562bae894908fc1879","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2359","title":"Fix flake","createdAt":"2022-07-19T23:42:40Z"}
{"state":"Merged","mergedAt":"2022-02-03T00:27:32Z","number":236,"body":"- Fix scaffolding\r\n- Update\r\n","mergeCommitSha":"fb31856c2a6ad46985b453c8af608dbfa372e2f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/236","title":"FixScaffoldFiles","createdAt":"2022-02-03T00:27:08Z"}
{"state":"Merged","mergedAt":"2022-07-22T21:16:28Z","number":2360,"body":"Setup data source and injec button for insight button.\r\n\r\n*EDIT* Images and video below.\r\n","mergeCommitSha":"4552ed9148d7c2a59e8cf128594411ea663658b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2360","title":"Insight Bubble Button","createdAt":"2022-07-20T00:27:19Z"}
{"state":"Merged","mergedAt":"2022-07-20T01:12:26Z","number":2361,"mergeCommitSha":"5b30a48414e9b698fd7dfae57d355b9d0fa70b0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2361","title":"WIP: trying to check ulimit values on builder machines","createdAt":"2022-07-20T01:11:47Z"}
{"state":"Merged","mergedAt":"2022-07-20T04:37:57Z","number":2362,"mergeCommitSha":"5f530a83aa78a7f909643a38b4822fa8f222e08a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2362","title":"Limit priority jobs for each priority user","createdAt":"2022-07-20T04:11:09Z"}