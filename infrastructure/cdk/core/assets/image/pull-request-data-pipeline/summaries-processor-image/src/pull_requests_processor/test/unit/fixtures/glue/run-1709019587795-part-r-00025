{"state":"Merged","mergedAt":"2022-03-10T03:10:17Z","number":520,"body":"next:\r\n- server implementation\r\n- possibly consolidate with `installState` operation","mergeCommitSha":"84a2b5266cbc4ef5dd17e94f74ca4ddf1e4850fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/520","title":"Introduce Repos API endpoint","createdAt":"2022-03-09T23:04:00Z"}
{"state":"Merged","mergedAt":"2023-03-09T05:25:13Z","number":5200,"mergeCommitSha":"26b891013e739763d9852cd716485a9251d59f47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5200","title":"Add retry logging","createdAt":"2023-03-09T05:24:51Z"}
{"state":"Merged","mergedAt":"2023-03-09T06:01:16Z","number":5201,"mergeCommitSha":"8249f263052c645b85712c01de8d8dd9b13ebb7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5201","title":"reduce sampling","createdAt":"2023-03-09T06:00:58Z"}
{"state":"Merged","mergedAt":"2023-03-09T07:31:15Z","number":5202,"mergeCommitSha":"bb623b2f6f3a5e5a3c945ce721df5e4a4bc5dbef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5202","title":"Fix issues with pagination and role access","createdAt":"2023-03-09T06:54:42Z"}
{"state":"Merged","mergedAt":"2023-03-09T15:30:08Z","number":5203,"mergeCommitSha":"312ca293fe64c54e8ac0fcec91132e78ec5aa2e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5203","title":"Change ml descriptions","createdAt":"2023-03-09T15:29:51Z"}
{"state":"Merged","mergedAt":"2023-03-09T15:34:46Z","number":5204,"body":"- Increase number of topics generated\r\n- increase topics generated\r\n","mergeCommitSha":"d27f95d0780f5cd996774c05355eae5b50b9635e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5204","title":"IncreaseTopicGeneration2","createdAt":"2023-03-09T15:34:32Z"}
{"state":"Merged","mergedAt":"2023-03-09T19:38:01Z","number":5205,"body":"- Increase number of topics generated\r\n- increase topics generated\r\n- remove hardcoded regions\r\n","mergeCommitSha":"4321cd85d36875c28e69309493b480a328fc4b74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5205","title":"RemoveHardcodedREgions","createdAt":"2023-03-09T19:37:11Z"}
{"state":"Merged","mergedAt":"2023-03-10T00:13:34Z","number":5206,"body":"Setup generic web team installation which only loads when user has *no* teams.\r\n\r\nEnumerates all the installations for the user (based on identity in JWT).\r\n\r\nTODO: Setup unique GH flow that doesn't enumerate teams. Dependant on new API.\r\n\r\n<img width=\"905\" alt=\"CleanShot 2023-03-09 at 11 30 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/224134039-3cf9f0ff-1d09-43e8-b557-477c9e4e9e88.png\">\r\n<img width=\"908\" alt=\"CleanShot 2023-03-09 at 11 28 22@2x\" src=\"https://user-images.githubusercontent.com/1553313/224134046-5ca35494-cece-4c2e-9abe-ce0cd5e80eb9.png\">\r\n","mergeCommitSha":"ced05b05676aca6f550cbeb2f05bd03e05dc9b43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5206","title":"Web team installation","createdAt":"2023-03-09T19:37:37Z"}
{"state":"Merged","mergedAt":"2023-03-09T19:59:30Z","number":5207,"mergeCommitSha":"7f93ebd643291b1b82f1fb37f9cd1763343fcc45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5207","title":"move away from deprecated apis","createdAt":"2023-03-09T19:57:50Z"}
{"state":"Merged","mergedAt":"2023-03-09T20:07:10Z","number":5208,"mergeCommitSha":"ebcde8928fc7afb3dc912a58f1e176f71cc038ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5208","title":"Up rev cdk deps","createdAt":"2023-03-09T20:00:08Z"}
{"state":"Merged","mergedAt":"2023-03-09T20:10:23Z","number":5209,"mergeCommitSha":"576da5d34964352baf6c32613b2529e14f5e5ba1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5209","title":"Fix service bootstrap template","createdAt":"2023-03-09T20:10:12Z"}
{"state":"Merged","mergedAt":"2022-03-10T00:36:17Z","number":521,"body":"Update localhost endpoints to include api prefix. (already done for dev & prod)\r\n\r\nCertain places in auth manually generate redirect paths to the api service. Needed to include /api/ prefix here as well. cc @pwerry @richiebres \r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/517","mergeCommitSha":"2916b01522d86c102beda3df68517576203f2036","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/521","title":"Fix issues with api prefix","createdAt":"2022-03-09T23:49:32Z"}
{"state":"Merged","mergedAt":"2023-03-09T20:47:58Z","number":5210,"body":"- Now returns an object:\n```\n{\n  provider: Provider,\n  installations: [InstallationV2]\n}\n```\n\n- Includes HTML url of each installation\n\n- Slight behaviour change, returning installations matching the specified optional `provider` query parameter.\n  If the `provider` argument is not specified, then it infers the provider from the currently logged-in identity.","mergeCommitSha":"b1eb7b590159131d157526f65d5a33254c84ec6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5210","title":"New listInstallations API for web-based team installation","createdAt":"2023-03-09T20:19:16Z"}
{"state":"Merged","mergedAt":"2023-03-10T00:58:50Z","number":5211,"body":"Kept this split to vscode for now.\r\n\r\nNote that this is just rearranging existing data (i.e. haven't added the Recommended Topics & Expoerts/Trending Topics sections yet)\r\n\r\nhttps://user-images.githubusercontent.com/13431372/224156262-46a7ef29-320d-4e9b-b4f0-481585993151.mp4\r\n\r\n","mergeCommitSha":"10bac3d64dd176343e1d1dcb279e84e076dc942b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5211","title":"Split vscode sidebar into tabs","createdAt":"2023-03-09T20:58:57Z"}
{"state":"Merged","mergedAt":"2023-03-10T00:43:11Z","number":5212,"body":"Data URI is _not_ a URL. Ktor was encoding it as such, which broke the image.\r\n\r\n<img width=\"765\" alt=\"Screenshot 2023-03-09 at 14 41 37\" src=\"https://user-images.githubusercontent.com/1798345/224176727-e957902e-7cf0-4b1e-a169-f49be6f97817.png\">\r\n","mergeCommitSha":"4a0370dd557919e577123b648b183e99e3c0971f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5212","title":"Fix GitLab empty group SVG data-uri placeholder","createdAt":"2023-03-09T22:41:18Z"}
{"state":"Merged","mergedAt":"2023-03-10T00:47:31Z","number":5213,"body":"We have many instances where we do this:\n```kotlin\nval url: Url? = null\n\n// ❌ silently transforms to \"null\" using the `Any?.toString()` function, instead of the intended `Url.toString()`.\n//   This \"null\" result is often exposed in the API.\nurl.toString()\n```\n\nInstead move to `Url.asString` extension property:\n```kotlin\nval url: Url? = null\n\nurl.asString // ✅ fails to not compile\n```","mergeCommitSha":"159c36f2f24a3fcb83dbe008fb121c4c4a6163b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5213","title":"Replace dangerous Url.toString usage","createdAt":"2023-03-09T22:41:20Z"}
{"state":"Merged","mergedAt":"2023-03-10T04:52:28Z","number":5214,"body":"### Changes\r\n- added `fullPath`\r\n\r\n### Example\r\n```json\r\n    \"installations\": [\r\n        {\r\n            \"avatarUrl\": \"https://gitlab.com/uploads/-/system/group/avatar/63439188/icon-192.png\",\r\n            \"displayName\": \"Unblocked\",\r\n            \"fullPath\": \"getunblocked\",\r\n            \"htmlUrl\": \"https://gitlab.com/groups/getunblocked\",\r\n            \"installationId\": \"63439188\",\r\n            \"isInstalled\": false,\r\n            \"provider\": \"gitlab\"\r\n        },\r\n        {\r\n            \"avatarUrl\": \"data:image/svg...\",\r\n            \"displayName\": \"aa\",\r\n            \"fullPath\": \"getunblocked/aa\",\r\n            \"htmlUrl\": \"https://gitlab.com/groups/getunblocked/aa\",\r\n            \"installationId\": \"64306716\",\r\n            \"isInstalled\": false,\r\n            \"provider\": \"gitlab\"\r\n        },\r\n]\r\n```\r\n\r\n### Intention\r\nUse `fullPath` + `htmlUrl` to render secondary text.\r\n\r\n<img width=\"540\" alt=\"Screenshot 2023-03-09 at 16 39 39\" src=\"https://user-images.githubusercontent.com/1798345/224193511-e066bab4-1c8e-4cb7-b170-8a6556e18241.png\">\r\n","mergeCommitSha":"6b4feafbffd492206d62daa04ad40cacdc17262c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5214","title":"Expose fully qualified GitLab subgroup name","createdAt":"2023-03-09T23:25:29Z"}
{"state":"Merged","mergedAt":"2023-03-10T01:28:10Z","number":5215,"body":"This passes the current editor's view range through to the active file stream.","mergeCommitSha":"1928c9c11828688fae7da124b092053a5ff10238","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5215","title":"View-based explorer insight stream works in Jetbrains","createdAt":"2023-03-10T01:20:09Z"}
{"state":"Merged","mergedAt":"2023-03-10T02:11:57Z","number":5216,"body":"Fix scrolling for:\r\n* onboarding UIs (installation, slideshow, checklist)\r\n* sidebar when there is a toast ","mergeCommitSha":"6230645e71256bd9293f056ef905ea35b2e64997","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5216","title":"Fix more scrolling instances","createdAt":"2023-03-10T01:48:46Z"}
{"state":"Merged","mergedAt":"2023-03-11T00:25:18Z","number":5217,"mergeCommitSha":"b2cad47c617f3393d7ec86a7814022ae95150d49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5217","title":"Remove Tool window","createdAt":"2023-03-10T02:03:21Z"}
{"state":"Closed","mergedAt":null,"number":5218,"body":"Is it time? Cmd+Shift+K \uD83E\uDD73\r\n\r\n<img width=\"585\" alt=\"CleanShot 2023-03-09 at 20 34 41@2x\" src=\"https://user-images.githubusercontent.com/858772/224224381-76cec386-124b-422e-8f3c-4b487c97c6e2.png\">\r\n","mergeCommitSha":"54655d8936f72429dbe4797c0637f2888e794085","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5218","title":"Enable search in hub for customers","createdAt":"2023-03-10T04:34:27Z"}
{"state":"Merged","mergedAt":"2023-03-10T04:36:58Z","number":5219,"body":"Fixes https://github.com/NextChapterSoftware/unblocked/pull/5215","mergeCommitSha":"1b71f55ee0da24249f41cb81388fcf95b68e4e89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5219","title":"Fix Jetbrains lint","createdAt":"2023-03-10T04:35:23Z"}
{"state":"Merged","mergedAt":"2022-03-10T00:18:21Z","number":522,"mergeCommitSha":"fb443c6c58b683130f59d0fcb26dc3c2077593ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/522","title":"DEBUG level for Exposed","createdAt":"2022-03-10T00:18:03Z"}
{"state":"Merged","mergedAt":"2023-03-10T06:56:32Z","number":5220,"body":"- GitHub -> Org\n- Bitbucket -> Workspace\n- GitLab -> Group","mergeCommitSha":"19edeb8c0f7eec26551a5f1b54a55f7ed4f42b9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5220","title":"Install copy should be SCM specific","createdAt":"2023-03-10T06:38:48Z"}
{"state":"Merged","mergedAt":"2023-03-10T08:33:43Z","number":5221,"body":"- Installations show when they are connected\r\n\r\n  <img width=\"400\" src=\"https://user-images.githubusercontent.com/1798345/224257640-6f656d72-7f4c-4437-841a-cbe642f13e02.png\">\r\n\r\n- connect installation succeeds\r\n\r\n  <table>\r\n<tr>\r\n<td>\r\n<img width=\"400\" alt=\"Screenshot 2023-03-09 at 23 59 32\" src=\"https://user-images.githubusercontent.com/1798345/224257646-9739603c-3cc9-4190-a8ba-d81ea9cc0eeb.png\">\r\n</td>\r\n<td>\r\n<img width=\"400\" alt=\"Screenshot 2023-03-09 at 23 59 40\" src=\"https://user-images.githubusercontent.com/1798345/224257648-95f3350b-cf82-48f9-a228-229d61ebcd36.png\">\r\n</td>\r\n</tr>\r\n</table>","mergeCommitSha":"13c1de11341ebe62c11e102be21405ac1914d0fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5221","title":"Connect installation API and enterprise provider plumbing","createdAt":"2023-03-10T07:58:52Z"}
{"state":"Merged","mergedAt":"2023-03-10T19:27:42Z","number":5222,"body":"- Added grpc type health probes\r\n- Added flag to chose between http, grpc or none\r\n- Modified secret service to disable all health probes. This is to prevent breaking our CI/CD pipeline while we test the new service","mergeCommitSha":"d7b5dd358b7102b0dfa09d4aff4ba5bf0347937d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5222","title":"Add support for grpc checks and override flag","createdAt":"2023-03-10T19:22:27Z"}
{"state":"Merged","mergedAt":"2023-03-10T19:53:37Z","number":5223,"body":"<img width=\"713\" alt=\"Screenshot 2023-03-10 at 11 53 18\" src=\"https://user-images.githubusercontent.com/1798345/224414711-2e7d9924-e0a5-4ddc-a0c1-eb046952e5e2.png\">\r\n","mergeCommitSha":"bd615c5c6d45b0a9bfe9c2ceb98f1e8a7b9aeee0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5223","title":"Fix display name in SCM installation response","createdAt":"2023-03-10T19:49:49Z"}
{"state":"Merged","mergedAt":"2023-03-10T21:00:48Z","number":5224,"body":"<img width=\"610\" alt=\"CleanShot 2023-03-10 at 12 16 44@2x\" src=\"https://user-images.githubusercontent.com/1553313/224420404-492da62d-2f66-4170-93bb-75637b0b7870.png\">\r\n","mergeCommitSha":"e7a8694b0d8bab0b3558a7a4b9237c49f5f65fc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5224","title":"Add External link to ConnectTeams","createdAt":"2023-03-10T20:18:52Z"}
{"state":"Merged","mergedAt":"2023-03-10T20:32:09Z","number":5225,"body":"Forgot to quote the number. ","mergeCommitSha":"5eb8c0d2b94d21a541480f4130eb6b2aa63fd19f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5225","title":"fix the port","createdAt":"2023-03-10T20:26:07Z"}
{"state":"Merged","mergedAt":"2023-03-10T21:29:58Z","number":5226,"body":"https://github.com/jeremymailen/kotlinter-gradle\r\n\r\nKotliner is using the new Gradler Worker apis to speed up execution and to hopefully deal with incremental builds more appropriately.","mergeCommitSha":"7e9baa3474923dc4f1ce7f3dc3c436d1118422c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5226","title":"Move to kotlinter to run ktlint via gradle","createdAt":"2023-03-10T21:14:10Z"}
{"state":"Merged","mergedAt":"2023-03-10T21:34:42Z","number":5227,"mergeCommitSha":"46d3d623627773dc74bd1d47259395e71acf2c8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5227","title":"Add SCM repo web url to ScmRepo","createdAt":"2023-03-10T21:16:18Z"}
{"state":"Merged","mergedAt":"2023-03-10T21:58:29Z","number":5228,"body":"The intention was to upload points in batches as the full recalculation takes place.\n\nHowever, due to a logic error we actually only uploaded them in batches once\nfull recalculation fully completed.","mergeCommitSha":"01b77fe67e41f180aefe2aa05ffa0860b16278b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5228","title":"Upstream sourcemark points incrementally, instead of all at once","createdAt":"2023-03-10T21:47:43Z"}
{"state":"Merged","mergedAt":"2023-03-10T22:08:13Z","number":5229,"mergeCommitSha":"1bcfc6dcf5b3dc688c964195d1d10ae6efa8e0c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5229","title":"Do not build custom-ktlint-rules automatically","createdAt":"2023-03-10T21:59:16Z"}
{"state":"Merged","mergedAt":"2022-03-10T00:52:01Z","number":523,"body":"Test order is non-deterministic. If system props haven't loaded when a test runs that doesn't call `ServiceInitializer.init()`, tests will fail. I can't see a good reason why we would _not_ want to call this on startup...","mergeCommitSha":"c92f0233ab2e2fbe8114b5a5dc3882bdb44dbb72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/523","title":"Fix property loading","createdAt":"2022-03-10T00:44:47Z"}
{"state":"Merged","mergedAt":"2023-03-10T22:25:25Z","number":5230,"mergeCommitSha":"735602898bfe23028e4e5cc60d5b17ceb92c9517","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5230","title":"Add link to repo","createdAt":"2023-03-10T21:59:25Z"}
{"state":"Merged","mergedAt":"2023-03-13T19:48:18Z","number":5231,"body":"* Heartbeat API is now a bidi stream\r\n* Add deterministic shutdown when IDE is shut down in a healthy state -- agent closes immediately\r\n* Add connection tracking to handle when IDE dies unexpected.  The agent tracks connections and disconnections on the stream, and whenever there have been no connections for > 1 minute, it shuts down","mergeCommitSha":"391d0c0bd70ed3fff613b352d97c361447c3d170","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5231","title":"Reliable IntelliJ agent shutdown","createdAt":"2023-03-10T23:30:59Z"}
{"state":"Merged","mergedAt":"2023-03-11T00:24:39Z","number":5232,"body":"### Now this UI actually works\r\n\r\n<img width=\"1066\" alt=\"Screenshot 2023-03-10 at 16 05 18\" src=\"https://user-images.githubusercontent.com/1798345/224451449-45288f63-c2dd-4c80-a5cf-3cfb9b27144b.png\">\r\n\r\n### Teams are created in admin web\r\n\r\n<img width=\"1468\" alt=\"Screenshot 2023-03-10 at 16 05 31\" src=\"https://user-images.githubusercontent.com/1798345/224451476-dc259470-9ed1-46d3-b79e-96cf3849669f.png\">\r\n","mergeCommitSha":"584633d87d7b67a56869a1dbf0aacc712cb6c251","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5232","title":"Connect installation API upserts teams","createdAt":"2023-03-11T00:04:27Z"}
{"state":"Merged","mergedAt":"2023-03-11T00:13:27Z","number":5233,"body":"\"unset\" in this case means use whatever the default VSCode behaviour is -- the next PR will change the default to use CustomGitProvider.  This way we can force behaviour in either direction if we want.","mergeCommitSha":"7cffff83b11de87f3ca7bf2229b7498127cde453","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5233","title":"Add VSCode git provider flag","createdAt":"2023-03-11T00:04:49Z"}
{"state":"Merged","mergedAt":"2023-03-13T21:04:10Z","number":5234,"body":"Refactor Code Block & Discussion Thread into shared/ide\r\n\r\n<img width=\"670\" alt=\"CleanShot 2023-03-10 at 16 18 15@2x\" src=\"https://user-images.githubusercontent.com/1553313/224452541-fc91d749-870a-4b53-8766-fa74f0aa669e.png\">\r\n<img width=\"522\" alt=\"CleanShot 2023-03-10 at 16 18 10@2x\" src=\"https://user-images.githubusercontent.com/1553313/224452542-92921803-f92c-4fc5-b12d-bf0222725061.png\">\r\n\r\nNext step is to update DiscussionThreadCommand in VSCode to use the shared stream.","mergeCommitSha":"8f6e2dc9efc1af812bad4ba09aa734084be3c1e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5234","title":"Shared Discussion Thread","createdAt":"2023-03-11T00:20:16Z"}
{"state":"Merged","mergedAt":"2023-03-11T06:00:26Z","number":5235,"body":"From https://github.com/NextChapterSoftware/unblocked/pull/5220#issuecomment-**********.","mergeCommitSha":"62ff8e47aadb5656b7cc2538f228e88f73bc969a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5235","title":"Use GitHub Organization not GitHub Org","createdAt":"2023-03-11T05:59:50Z"}
{"state":"Merged","mergedAt":"2023-03-14T16:56:54Z","number":5236,"body":"This effectively blocks repo resolution and git services until login is completed.  We only resolve this value once, so subsequent logout/login has no effect.\r\n\r\nNote that this also changes the default git provider to `CustomGitProvider`","mergeCommitSha":"dea3ce025718ec0f8adb0545593df96274f49a2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5236","title":"VSCode uses UseVSCodeGitProvider flag","createdAt":"2023-03-13T17:26:26Z"}
{"state":"Merged","mergedAt":"2023-03-13T20:50:24Z","number":5237,"body":"https://user-images.githubusercontent.com/13431372/224779896-2b07c88d-5424-4fa9-a01e-b4decc13822e.mp4\r\n\r\n","mergeCommitSha":"0c334e4ab59e7d2290a70be86a6271b771b4b0d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5237","title":"Update onboarding button text responsiveness","createdAt":"2023-03-13T17:26:54Z"}
{"state":"Merged","mergedAt":"2023-03-13T18:39:09Z","number":5238,"mergeCommitSha":"02ec5e574e2107427f47852f3c5da76977c402ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5238","title":"Add TopicMetricsJob and service skeleton","createdAt":"2023-03-13T17:37:04Z"}
{"state":"Merged","mergedAt":"2023-03-13T22:32:30Z","number":5239,"body":"Updated Connect flow for installations to have special UI based on installURL\r\n\r\n<img width=\"1097\" alt=\"CleanShot 2023-03-13 at 11 31 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/224796842-47bf3a84-c19d-4fea-b08e-e322cb6ae84a.png\">\r\n","mergeCommitSha":"2064395d96b732203924937b0f5a5b4c11644c7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5239","title":"Updated Connect flow for GH","createdAt":"2023-03-13T18:32:50Z"}
{"state":"Merged","mergedAt":"2022-03-12T00:45:19Z","number":524,"body":"Setup the currentPerson into AuthStore.\r\n\r\nUse this to make Messages locally for overlay.","mergeCommitSha":"1d4a69d1961b2bde2745a12d71a6e3798989d039","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/524","title":"Setup \"Me\" and message overlay","createdAt":"2022-03-10T00:52:38Z"}
{"state":"Merged","mergedAt":"2023-03-13T20:24:47Z","number":5240,"body":"Our old approach for exporting RDS was not working out. The export format had to be processed before being imported back into an RDS cluster. \r\n\r\nThis new approach uses our nightly snapshots and exports them to S3. S3 replication then takes care of copying them to us-east-2.\r\n\r\nEach time this lambda runs we export only one snapshot. This is to ensure we stay under the 5 concurrent job limit. The lambda function now runs once every hour. \r\n\r\nTested it in Dev and works as expected. Lambda's are an absolute source of pain","mergeCommitSha":"fa21a0e79067815c3ed8304d39b9b4f602427687","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5240","title":"changing how we export RDS","createdAt":"2023-03-13T20:01:05Z"}
{"state":"Merged","mergedAt":"2023-03-13T23:14:46Z","number":5241,"body":"To be used for trending topics","mergeCommitSha":"a9c335479165aca398e166c977f01fd4d2a8fdfa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5241","title":"Add ability to count messages and pull requests","createdAt":"2023-03-13T22:40:53Z"}
{"state":"Merged","mergedAt":"2023-03-13T23:54:04Z","number":5242,"body":"Fixes issue with oversized Insight header.","mergeCommitSha":"7bb7f1be7786290e1eb6b099024efc9ee47a6e08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5242","title":"Fix PR Header","createdAt":"2023-03-13T23:44:53Z"}
{"state":"Merged","mergedAt":"2023-03-14T16:59:41Z","number":5243,"mergeCommitSha":"b45133e784879c208a50e706a2a308b3f51b948d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5243","title":"scm service sample rate increae for honeycomb","createdAt":"2023-03-14T16:59:34Z"}
{"state":"Merged","mergedAt":"2023-03-14T17:47:03Z","number":5244,"body":"Note that this doesn't really work yet -- this doesn't re-connect all the relevant grpc flows.  I'll figure that out some other time.","mergeCommitSha":"dfbef1082743280b473f3ce7114803c172056c50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5244","title":"Restart agent process if it dies","createdAt":"2023-03-14T17:09:40Z"}
{"state":"Merged","mergedAt":"2023-03-14T23:38:38Z","number":5245,"body":"Refactored PR Views out of VSCode into shared.\r\nSTYLING STILL TBD. Lots of work needs to be done to figure out the themes...\r\n\r\nUpdated common stream to populate PR in IntelliJ\r\n\r\nNext PR will be to setup commands so we can open up the sections within IntelliJ.\r\n\r\nINTELLIJ:\r\n<img width=\"611\" alt=\"CleanShot 2023-03-14 at 10 45 11@2x\" src=\"https://user-images.githubusercontent.com/1553313/225094438-5c31be2a-c0e8-4d46-8444-e545ad0624da.png\">\r\n\r\n\r\nVSCODE:\r\n<img width=\"798\" alt=\"CleanShot 2023-03-14 at 10 51 26@2x\" src=\"https://user-images.githubusercontent.com/1553313/225094326-6e5d5aeb-3a08-478a-9ab6-b89aef3179f0.png\">\r\n","mergeCommitSha":"7dd38a6b02019fac1f08418a447cb591eaab285e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5245","title":"IntelliJ PR","createdAt":"2023-03-14T17:53:12Z"}
{"state":"Merged","mergedAt":"2023-03-17T22:07:51Z","number":5246,"body":"https://user-images.githubusercontent.com/13431372/225117980-2818dee3-3a39-4558-a44a-8e6ce6e27c31.mp4\r\n\r\n","mergeCommitSha":"ca2c4908848145adbff7bbb627a05b8b3865da24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5246","title":"Add view title menu to unblocked sidebar","createdAt":"2023-03-14T19:24:12Z"}
{"state":"Merged","mergedAt":"2023-03-14T20:27:34Z","number":5247,"body":"- Modified the python copy to do both snapshot copy and S3 exports\r\n- Changed IAM roles to allow more RDS actions\r\n\r\nI will make a spearate PR with a lambda function which cleans up snapshots beyond the retension age.\r\n\r\nPS: I know a whole bunch of print statements are littered all over the python code...I simply don't care! Sorry! ","mergeCommitSha":"13fff3648337eac2397d90e813c8bdd9a007ffc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5247","title":"Add support for snapshot copy","createdAt":"2023-03-14T19:40:57Z"}
{"state":"Merged","mergedAt":"2023-03-14T20:25:02Z","number":5248,"mergeCommitSha":"6dd924588384f4615fb7b4be877a6a886fe0f425","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5248","title":"Create queue and skeleton handlers for calculating topic metrics","createdAt":"2023-03-14T19:58:05Z"}
{"state":"Merged","mergedAt":"2023-03-20T16:12:28Z","number":5249,"body":"Before:\r\n<img width=\"596\" alt=\"CleanShot 2023-03-14 at 13 40 10@2x\" src=\"https://user-images.githubusercontent.com/1553313/225135929-09fe95e0-f19c-4b23-ab25-cb03281be366.png\">\r\n\r\nAfter:\r\n<img width=\"563\" alt=\"CleanShot 2023-03-14 at 13 58 24@2x\" src=\"https://user-images.githubusercontent.com/1553313/225135936-f7f421ad-fdf8-49a3-8c05-66e7253fb87b.png\">\r\n","mergeCommitSha":"efd3e2f5d452d75e6e463e195413da2fb8dee429","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5249","title":"Encode Unicode","createdAt":"2023-03-14T21:04:54Z"}
{"state":"Merged","mergedAt":"2022-03-10T16:56:07Z","number":525,"body":"Hopefully fix react router in deployed environment due to `dashboard` prefix.","mergeCommitSha":"e68bcb5e71156638554a187ac58ea71abc92b401","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/525","title":"Setup dashboard basename","createdAt":"2022-03-10T01:08:01Z"}
{"state":"Merged","mergedAt":"2023-03-16T16:23:34Z","number":5250,"body":"Sets logging context and headers for API requests\r\n\r\nAlso, remove an unused file (we don't have a generic sidebar tool window)","mergeCommitSha":"1cba83e2beebcd2274e6913a016ff5e3ec10d004","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5250","title":"Use correct jetbrains build cfg","createdAt":"2023-03-14T22:23:02Z"}
{"state":"Merged","mergedAt":"2023-03-14T23:51:56Z","number":5251,"body":"Next PR will update ingestion to create threads with file points instead of source points for file-level comments.","mergeCommitSha":"96ad072e830a3db0acb72320a830c4e7d4c4b89a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5251","title":"Refactor ScmPullRequestComment data classes to support GitHub file-level comments","createdAt":"2023-03-14T22:47:43Z"}
{"state":"Merged","mergedAt":"2023-03-15T20:38:52Z","number":5252,"body":"https://chapter2global.slack.com/archives/C02GEN8LFGT/p1678751404044799","mergeCommitSha":"cd6c5b486a00315afd692cc40522f4219daa0df6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5252","title":"Update pull request ingestion to support GitHub file-level comments","createdAt":"2023-03-15T00:04:13Z"}
{"state":"Merged","mergedAt":"2023-03-15T05:08:31Z","number":5253,"body":"I need this change so that services in us-east-2 could start","mergeCommitSha":"a38fc59169650777154b2ac1677e23e28be027a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5253","title":"update us-east-2 config for DR","createdAt":"2023-03-15T04:38:37Z"}
{"state":"Merged","mergedAt":"2023-03-15T16:27:27Z","number":5256,"mergeCommitSha":"b28043b1739c6eb01c700a4fe35ed46dcd4c3424","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5256","title":"Add more region specific config","createdAt":"2023-03-15T16:25:46Z"}
{"state":"Merged","mergedAt":"2023-03-15T17:08:07Z","number":5257,"mergeCommitSha":"cc21fef4e631ba1e2dd3890138503bc86aa5442a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5257","title":"Rename MockScmObjects function","createdAt":"2023-03-15T16:56:03Z"}
{"state":"Merged","mergedAt":"2023-03-15T18:37:38Z","number":5258,"body":"This will allow creating FilePoints (in addition to SourcePoints). Fixes a bug where we're creating duplicate file points.","mergeCommitSha":"0fb852626cb56d2549cc574910cf3e5f4d230128","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5258","title":"SourcePointStore.createSourcePoints should take a list of Points","createdAt":"2023-03-15T17:13:22Z"}
{"state":"Merged","mergedAt":"2023-03-15T18:45:01Z","number":5259,"mergeCommitSha":"75157b22fc57258ebd0b6797fdf65258f02cd4a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5259","title":"Database specific region for sts auth","createdAt":"2023-03-15T18:23:21Z"}
{"state":"Merged","mergedAt":"2022-03-10T07:00:19Z","number":526,"body":"This is a step towards removing possibly removing token minting from the api-service. The correct architecture for this would be a non-public facing `IdentityService` that handles the final step of the OAuth dance to GitHub to mint the refresh token. \r\n\r\nThat would prevent a confused deputy attack, because refreshing is trusted. Also prevents token theft if the api-service is popped.","mergeCommitSha":"1547e824e720f1bf585cc22ba674e214da5043ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/526","title":"Use asymmetric key for auth","createdAt":"2022-03-10T02:33:41Z"}
{"state":"Merged","mergedAt":"2023-03-15T19:06:17Z","number":5260,"body":"This is a hack but this operation is deprecated","mergeCommitSha":"829bde92f7f40cf082c61429f2789aa6bb30025c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5260","title":"Set setHasSeenTutorial for VSCode if updatePersonTutorial sent from hub","createdAt":"2023-03-15T18:48:20Z"}
{"state":"Merged","mergedAt":"2023-03-17T17:53:37Z","number":5261,"body":"Add contextual title and team name to page titles on the dashboard.\r\n<img width=\"291\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/225431547-0a43ee3e-0c16-433e-88f4-643ac992f8fb.png\">\r\n<img width=\"441\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/225434421-4ff835c0-4f24-47c4-b727-096945b6f25a.png\">\r\n\r\n-----------\r\n\r\n**Insights**\r\n* My Insights\r\n* Recommended Insights\r\n* Recently Deleted Insights\r\n* [Thread title]\r\n* [PR title] #[PR number]\r\n\r\n**Topics**\r\n* Topics\r\n* [Topic Name]\r\n* Add Topic\r\n\r\n**Experts**\r\n* [Team Member Name]\r\n\r\n**Search**\r\n* Search Results for [search queries]\r\n\r\n**Settings**\r\n* Email Preferences\r\n* Slack (unconnected)\r\n* [Slack workspace] (connected)\r\n* Invite Team Members","mergeCommitSha":"d086487a1a3d4999fe1e0b0a2e3a413afbc04c68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5261","title":"UNB-1008 Add page titles for all dashboard routes","createdAt":"2023-03-15T20:12:44Z"}
{"state":"Merged","mergedAt":"2023-03-15T23:55:45Z","number":5262,"mergeCommitSha":"f3e7511428e1666501b8fb8fba92f816c13e6062","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5262","title":"Increase llogging for services temporarily","createdAt":"2023-03-15T23:54:21Z"}
{"state":"Merged","mergedAt":"2023-03-16T03:38:28Z","number":5263,"body":"To allow reuse for trending topics","mergeCommitSha":"3b19f6c5c2f0b2107d6c3e60c20ce149458755d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5263","title":"Move Instant.startOfDayPST to lib-common","createdAt":"2023-03-16T00:10:19Z"}
{"state":"Merged","mergedAt":"2023-03-16T06:16:56Z","number":5264,"mergeCommitSha":"81db3dc5b6b808468f1f288d0fc7c76c2322de36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5264","title":"Create TopicMetricsModel and store","createdAt":"2023-03-16T05:32:08Z"}
{"state":"Merged","mergedAt":"2023-03-20T18:12:49Z","number":5265,"body":"This globs SourcePoint upload requests together, so that only one upload request is pending at a time.  Might help with this issue: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1678928835693709?thread_ts=1678915431.761619&cid=C02HEVCCJA3 -- if IDE operations run while source points are uploading, we can run multiple uploads at the same time.\r\n\r\nWrote a `GlobRunningPromise` helper for this.  This globs promise executions together while one is outstanding.","mergeCommitSha":"9119d9a2da827117b9293af3b607666a4996cdc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5265","title":"Glob SourcePoint uploading requests together","createdAt":"2023-03-16T17:57:05Z"}
{"state":"Merged","mergedAt":"2023-03-16T19:44:11Z","number":5266,"body":"- Renamed IAM roles created by lambda construct. IAM roles are global and we were getting conflicts\r\n- Added a new parameter to Redis stack to support deploying new versions while also avoiding recreating the existing clusters. The old version is no longer supported as an active config for new clusters.\r\n- Updated CDK us-easst-2 config to mark it as an active DR site coldSite=False\r\n- Updated subnet information for cold site\r\n- Added EKS cluster config yaml\r\n- Added config file for us-east-2 dev KMS issuer\r\n- Updated database stack to support creating DB clusters from snapshots","mergeCommitSha":"58b7d683645327840743d09b81957269b88e3f90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5266","title":"Changes needed to deploy cold site","createdAt":"2023-03-16T18:11:54Z"}
{"state":"Merged","mergedAt":"2023-03-16T19:11:37Z","number":5267,"body":"Node 17 is EOL","mergeCommitSha":"c745eaabd91b6c0b3b4d00faab75e8955eba5068","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5267","title":"Use current node version in CI","createdAt":"2023-03-16T18:24:56Z"}
{"state":"Merged","mergedAt":"2023-03-16T21:08:08Z","number":5268,"body":" Create kube config for us-east-2 and deployment script\r\n\r\n**Note:** Deployment script provided here is a very basic boilerplate for DR procedures. I will be reworking this script and move some of the logic in GH Actions to here. The reconfigure GH action to use this script for regular deployments. This way we won't need to maintain separate scripts","mergeCommitSha":"62aebedcd198be64a62a2d51adce227ea87f632d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5268","title":" Create kube config for us-east-2 and deployment script","createdAt":"2023-03-16T18:35:30Z"}
{"state":"Merged","mergedAt":"2023-03-16T19:53:13Z","number":5269,"mergeCommitSha":"c2313185a4a68c548f18971b71602be171122eff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5269","title":"Implement metrics calculation for trending topics","createdAt":"2023-03-16T18:50:24Z"}
{"state":"Merged","mergedAt":"2022-03-10T02:43:01Z","number":527,"body":"We figured out why the `BUSY` table in DEV was permanently locked in Exposed:\r\n```\r\ntrx {\r\n    1. acquire lock\r\n    2. upgrade schema\r\n    3. delete lock\r\n}\r\n```\r\n\r\nIf step 2 fails (in this case failing because of invalid `SourcePointModel` schema), then the can never be deleted and can never be re-acquired. This is an Exposed fail safe, and is actually desired behaviour to prevent data loss.\r\n\r\nNext:\r\n- we need to ensure that the application fails if the schema cannot be upgraded\r\n","mergeCommitSha":"2a83085b1ba952ba451d8878530f06f457e6d5d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/527","title":"Schema fix","createdAt":"2022-03-10T02:35:51Z"}
{"state":"Merged","mergedAt":"2023-03-16T20:28:58Z","number":5270,"body":"This is just a small readme update. I will no wait for a review. ","mergeCommitSha":"36038a693528cc464246f353ceb04d31fb180e25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5270","title":"Update CSI secrets driver installation procedure","createdAt":"2023-03-16T20:28:50Z"}
{"state":"Merged","mergedAt":"2023-03-16T21:53:06Z","number":5271,"body":"From the topic specific page\r\n\r\n<img width=\"1905\" alt=\"CleanShot 2023-03-16 at 14 38 45@2x\" src=\"https://user-images.githubusercontent.com/1924615/225758441-58d6fd74-bf28-4c0f-8a93-9832d8ab2729.png\">\r\n","mergeCommitSha":"cf309caac20b9cbf8836a8e8ed8e4591a3c72811","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5271","title":"Enable triggering topic metrics calculation from the admin console","createdAt":"2023-03-16T21:38:28Z"}
{"state":"Merged","mergedAt":"2023-03-24T21:43:00Z","number":5272,"mergeCommitSha":"dbd87f1c4cb0931a5e513b1e83b5eee256f2b394","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5272","title":"Clean up MacOS folder","createdAt":"2023-03-16T22:13:03Z"}
{"state":"Merged","mergedAt":"2023-03-16T22:36:05Z","number":5273,"body":"For now this is safe to trigger multiple times since we don't recalculate if it was previously done (though I may update it to do recalculations).","mergeCommitSha":"0acd8aa9cc513777fe7e35f46976313eab085db0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5273","title":"Enable triggering topic metrics calculation for the last 90 days from the admin console","createdAt":"2023-03-16T22:21:31Z"}
{"state":"Merged","mergedAt":"2023-03-17T22:16:34Z","number":5274,"body":"web:\r\n<img width=\"1512\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/225754579-60078c57-889a-4870-bebc-877e5fc45adc.png\">\r\n\r\nvscode:\r\n![image](https://user-images.githubusercontent.com/13431372/225754640-75fb93a9-b764-4f8a-bfb2-85fcbf83e956.png)\r\n\r\nintellij:\r\n![image](https://user-images.githubusercontent.com/13431372/225754679-8c0c2243-7baa-4df0-8b26-51c34fe0a730.png)\r\n\r\n* Added `FileHeader` component (separate component for web vs ide clients) -- this is rendered in both the CodeBlock component and also can be rendered individually (i.e. when there is no code block)\r\n* Added `MarkReference` component which renders either a `CodeBlock` (if there are sourcepoints) or a `FileHeader` (if there are no sourcepoints but there are filepoints)\r\n","mergeCommitSha":"8916aa9a50b2dadf403037c9419a6208f29d4d90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5274","title":"Support file threads on the clients","createdAt":"2023-03-16T23:01:58Z"}
{"state":"Merged","mergedAt":"2023-03-17T20:53:31Z","number":5275,"body":"* Add a button to the explorer insights title bar that toggles the filter bar\r\n* Display the current file in the top-right zone of the explorer insights panel, where the filter toggle is in VSCode\r\n\r\n<img width=\"371\" alt=\"Screen Shot 2023-03-16 at 5 42 42 PM\" src=\"https://user-images.githubusercontent.com/2133518/225782960-d4727aa3-c809-4bea-ac80-eea1f0a8c763.png\">\r\n\r\nSome notes:\r\n* I had to add a way for the plugin to communicate with the agent directly, back and forth.  I added a flag to the tunnel API so that commands can be sent to and from the agent to and from either the view or the plugin.","mergeCommitSha":"c2338ff7814e4cfa64337f6a65c572cc309f1731","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5275","title":"Add filter button to Jetbrains explorer insights tool window","createdAt":"2023-03-16T23:53:42Z"}
{"state":"Merged","mergedAt":"2023-03-17T19:06:41Z","number":5276,"body":"Went down a huge (almost entire day...) rabbit hole refactoring the architect to handle tool windows & webview controllers that are created and destroyed.\r\n\r\nThis was a ultimately much more complicated and UX wasn't great as there is a lot of overhead in tearing down and creating webviews.\r\n\r\nTook another approach and decided to consolidate Threads & PRs into a single webview. With this, tool windows are never unregistered and webviews never destroyed. \r\n\r\nhttps://user-images.githubusercontent.com/1553313/225838710-bd77e9d0-0d42-4ec2-a2df-ece27b1fa385.mp4\r\n\r\n\r\n","mergeCommitSha":"779f4ab41b5c531ab8a43cddee1f8f115baf8411","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5276","title":"Consolidate thread and pr commands in IntelliJ","createdAt":"2023-03-17T07:20:34Z"}
{"state":"Merged","mergedAt":"2023-03-17T19:30:04Z","number":5277,"body":"\r\nWhen copying snapshots to a secondary region, they show up as manual snapshots. Even if the original was an automated snapshot! This new function helps us avoid having snapshots staying around beyond our data rentention policy.\r\n\r\nWe are still able to manually exclude a particular snapshot from this cleanup procedure by tagging it (tag name: 'retain', tag value: 'true')","mergeCommitSha":"79ba9db0874e8b61ce58b68931acd0f81d393968","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5277","title":"Added lambda function to cleanup manual snapshots ","createdAt":"2023-03-17T08:06:29Z"}
{"state":"Merged","mergedAt":"2023-03-17T08:07:14Z","number":5278,"body":"More cleanup of EKS install instructions. This is purely documentation change so I am not going to request a review.","mergeCommitSha":"69586e0cf50353590991d2cff0ff7b3a53c23127","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5278","title":"More cleanup of EKS install instructions","createdAt":"2023-03-17T08:07:07Z"}
{"state":"Merged","mergedAt":"2023-03-17T16:21:35Z","number":5279,"mergeCommitSha":"dc75b30a205d63a83cfbbf4c4ed46e8f8d458d9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5279","title":"Drop batch size to 1 for metrics calculation","createdAt":"2023-03-17T16:08:49Z"}
{"state":"Merged","mergedAt":"2022-03-10T06:46:35Z","number":528,"body":"Less error-prone UUID typing.","mergeCommitSha":"52f1787b5ebd6719fde6ac3d7de06e9d016f1acf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/528","title":"refactor openAPI resource IDs","createdAt":"2022-03-10T05:25:24Z"}
{"state":"Merged","mergedAt":"2023-03-17T20:50:13Z","number":5280,"mergeCommitSha":"59af051ba455baca2e3b273ded5bbd3817b33c97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5280","title":"Refactor useDocumentTitle hook","createdAt":"2023-03-17T20:42:14Z"}
{"state":"Merged","mergedAt":"2023-03-17T21:16:51Z","number":5281,"body":"JeffML is the future!!!","mergeCommitSha":"c805da425bd04159c24cb3b186a1511ff42de7a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5281","title":"JeffML v1","createdAt":"2023-03-17T21:16:11Z"}
{"state":"Merged","mergedAt":"2023-03-17T21:41:06Z","number":5282,"mergeCommitSha":"53bcecf4cc427bf5c0e653cadf734af3161cbd03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5282","title":"Add graph to view topic metrics","createdAt":"2023-03-17T21:33:05Z"}
{"state":"Merged","mergedAt":"2023-03-17T22:22:15Z","number":5283,"body":"Fix issue where messages created in PR view had duplicate values due to overlay.\r\n\r\n","mergeCommitSha":"475f9ce53108930adb87729df57ca2a476d1cc09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5283","title":"Fix PR Message","createdAt":"2023-03-17T21:53:08Z"}
{"state":"Merged","mergedAt":"2023-03-17T22:21:58Z","number":5284,"body":"Connect PR Actions in IntelliJ","mergeCommitSha":"68c72d9229cc732a42dd144ae29a4be2ef34270e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5284","title":"Setup PR Actions in IntelliJ","createdAt":"2023-03-17T22:02:39Z"}
{"state":"Merged","mergedAt":"2023-03-23T22:58:53Z","number":5285,"body":"We need to add a trending score property to the `Topic` API model but unfortunately its being used by an existing PUT endpoint. This PR revs those endpoints, renaming the existing `Topic` to `LegacyTopic` and creating a new `Topic` that has the `trendingScore` property.","mergeCommitSha":"e7d881b035e604e06aa2629ceb80a74c14261bd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5285","title":"Add trending score to topics","createdAt":"2023-03-17T22:26:02Z"}
{"state":"Merged","mergedAt":"2023-03-17T22:54:06Z","number":5286,"mergeCommitSha":"c9e5e48f9ea26a29d01f0daf27326cf280bcbcfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5286","title":"Allow generating topic activity metrics for the previous 365 days","createdAt":"2023-03-17T22:39:29Z"}
{"state":"Merged","mergedAt":"2023-03-21T16:11:00Z","number":5287,"body":"Setup majority of commands for DiscussionThreads.\r\n\r\nStill some TODOs, mainly in regards to navigation & dialogs","mergeCommitSha":"5ae41d7a59246afcde4d6b29f2ffef5bcbdbc20b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5287","title":"Setup DiscussionThread Commands in IntelliJ","createdAt":"2023-03-17T23:13:46Z"}
{"state":"Merged","mergedAt":"2023-03-19T02:22:53Z","number":5288,"body":"This right here is a really painful reminder that ruined my Saturday......Do NOT let your environment configs drift apart!  ","mergeCommitSha":"f5479b7c8746d2a5c06538fe807f958e21ea8f90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5288","title":"this is a paiful reminder right here..","createdAt":"2023-03-19T02:16:39Z"}
{"state":"Merged","mergedAt":"2023-03-19T20:55:09Z","number":5289,"body":"### Background\r\nSM engine generates 100,000s of points. The intention is that those point are buffered\r\nfor upload in chunks of 2000 points in order to reduce the impact of the network\r\nrequest overhead.\r\n\r\n\r\n### Problem\r\nProblem was that we were flushing the buffer whenever the user navigated to a file in\r\nthe editor.\r\n\r\n\r\n### Change\r\nThis change will flush the buffer as intended:\r\n1. when the buffer reaches 2000 points, or\r\n2.  when the buffer is explicitly flushed at the end of full recalculation.\r\n\r\n\r\n### Results\r\nWith this change there are approximately 4X fewer upload requests, which means that the buffering is working as expected.\r\n\r\nResults | Before | After\r\n--|--|--\r\nPoints uploaded | 48,279 | 49,603\r\nNumber of upload requests | 211 | 55\r\n","mergeCommitSha":"2d466dae02a46faef50344d358cab7c3fa7ca09e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5289","title":"Address SM issue where we flushed the point buffer too frequently","createdAt":"2023-03-19T02:48:41Z"}
{"state":"Merged","mergedAt":"2022-03-10T08:02:32Z","number":529,"body":"Quick little token generator for Agora. Suffers the same injection issue as `Jwt`. I will refactor both in a separate PR\r\n\r\nStacked on: https://github.com/NextChapterSoftware/unblocked/pull/510","mergeCommitSha":"8168d7bccff770dbd78554d81315160c2496fa4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/529","title":"Agora channel auth","createdAt":"2022-03-10T07:05:18Z"}
{"state":"Merged","mergedAt":"2023-03-19T20:17:52Z","number":5290,"body":"This is just a minor documentation change. I won't ask for reviews and just force merge it.","mergeCommitSha":"a525d0f95a2f586f793ac3150096be66877d17b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5290","title":"Update eks doc calico subnets","createdAt":"2023-03-19T20:17:31Z"}
{"state":"Merged","mergedAt":"2023-03-19T22:57:37Z","number":5291,"mergeCommitSha":"8be44d4df81bd8a4d0a021701bdc78f4535d5d54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5291","title":"Annotate more internal teams and users","createdAt":"2023-03-19T21:15:50Z"}
{"state":"Merged","mergedAt":"2023-03-20T16:25:40Z","number":5292,"body":"I accidentally deleted Redis and ActiveMQ in us-west-2 while cleaning DR stuff. AWS console decided to all of a sudden change the region because another tab in another browser was still pointing at west region","mergeCommitSha":"7a112ef61387f8d6014de36a5cc3c0ffe1918a48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5292","title":"I messed up dev. This should fix it","createdAt":"2023-03-19T22:34:04Z"}
{"state":"Merged","mergedAt":"2023-03-20T18:31:47Z","number":5293,"body":"Changes have been deployed to both Dev and Prod. This was required to get Grafana agent working again.","mergeCommitSha":"6521d6d3ff34b8bde8289ca27b570747e1f047b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5293","title":"fixing ebs persistent volume driver which was needed for grafana agent","createdAt":"2023-03-20T08:35:22Z"}
{"state":"Merged","mergedAt":"2023-03-20T16:28:30Z","number":5294,"mergeCommitSha":"dada99a9d992d65418850b3e7c3d5cea4f27440d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5294","title":"Fix activemq host","createdAt":"2023-03-20T16:28:23Z"}
{"state":"Merged","mergedAt":"2023-03-20T17:02:22Z","number":5295,"mergeCommitSha":"eb287fb1d0c4c8b5816c6772bb1819f7d6750fcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5295","title":"Fix prompt counting","createdAt":"2023-03-20T17:02:07Z"}
{"state":"Merged","mergedAt":"2023-03-20T17:35:04Z","number":5296,"mergeCommitSha":"39f177443c981693f5e49f3b7abe73667faa6876","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5296","title":"Fix openai error handling","createdAt":"2023-03-20T17:33:51Z"}
{"state":"Merged","mergedAt":"2023-03-20T18:58:13Z","number":5297,"body":"Slightly more efficient","mergeCommitSha":"709a3db549524a4f1e1f80a9e0fc253a9f9538df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5297","title":"Use Flow for GitHubInstallationMaintenanceJob","createdAt":"2023-03-20T18:27:58Z"}
{"state":"Merged","mergedAt":"2023-03-20T19:41:27Z","number":5298,"body":"We see a large increase in 4xx errors in prod ALB metrics followed by very frequent failures in HTTP probes. Looking at ALB logs I see a bunch of 460 errors which could be due to target services not responding in time. Looking at Grafana I do see some CPU throttling.","mergeCommitSha":"96a9deaf0346b1c94deec7e90ebe6570f416ae85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5298","title":"add more cpu to public services that are being throttled","createdAt":"2023-03-20T18:37:17Z"}
{"state":"Merged","mergedAt":"2023-03-20T19:15:51Z","number":5299,"mergeCommitSha":"8619b7095374b34571f05721421225d97598c87d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5299","title":"Fix shutdown semantics","createdAt":"2023-03-20T19:13:33Z"}
{"state":"Merged","mergedAt":"2022-01-18T05:53:38Z","number":53,"body":"- `CODECOV_TOKEN` secret added here: https://github.com/Chapter2Inc/codeswell/settings/secrets/actions","mergeCommitSha":"3a12987793957881a83b19104422cdf872959823","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/53","title":"Upload jacoco result to CodeCov","createdAt":"2022-01-18T05:36:20Z"}
{"state":"Merged","mergedAt":"2022-03-10T18:05:37Z","number":530,"body":"Processing pairwise is better than processing commits in aggregate because:\r\n\r\n1. we believe the SourceMark tracking accuracy is improved since the solution space\r\n   for the number of possible destinations for any tracked code segment is much\r\n   smaller when restricted to the diffs from a _single_ commit; and\r\n2. we need to track SourcePoints for all possible commits in order to render source\r\n   points with high fidelity in the web client.","mergeCommitSha":"4846e3de69e297a483b4ff6479de98bd32f1174b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/530","title":"Recalculate SourceMarks for each successive commit pair","createdAt":"2022-03-10T15:53:44Z"}
{"state":"Merged","mergedAt":"2023-03-22T00:03:25Z","number":5300,"body":"Add basic File Opening functionality to PR Command\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/226460885-fcb69a20-f453-4d83-9ca2-a64f25ab5d99.mp4\r\n\r\n","mergeCommitSha":"5194fa72fa9091293c9845c8da44ad07d0e76109","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5300","title":"Basic PR File Opening","createdAt":"2023-03-20T20:40:46Z"}
{"state":"Merged","mergedAt":"2023-03-20T21:41:55Z","number":5301,"body":"Just removes unnecessary boilerplate.","mergeCommitSha":"2eac059e3b28a5f092505023d04c104930b1d452","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5301","title":"Refactor teardown for background job","createdAt":"2023-03-20T21:31:20Z"}
{"state":"Merged","mergedAt":"2023-03-20T22:03:01Z","number":5302,"mergeCommitSha":"cdb87f7f43d06ebad80b4f568344a7107a59e33a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5302","title":"Add logging to debug chart","createdAt":"2023-03-20T21:55:37Z"}
{"state":"Merged","mergedAt":"2023-03-21T17:20:00Z","number":5303,"body":"Moving general sidebar from VSCode to Shared in preparation to adding it to IntelliJ\r\n![Uploading CleanShot 2023-03-20 at <EMAIL>…]()\r\n","mergeCommitSha":"bd3bed732da1229332e52025d2ad2429e3efd7c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5303","title":"Refactor general sidebar from VSCode to Shared","createdAt":"2023-03-20T22:09:16Z"}
{"state":"Merged","mergedAt":"2023-03-21T18:29:06Z","number":5304,"body":"Reverts NextChapterSoftware/unblocked#5298.\r\n\r\nNot the cause of the issue.","mergeCommitSha":"f7d0dcd27b1385f27b77c68c2409856599197131","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5304","title":"Revert \"add more cpu to public services that are being throttled\"","createdAt":"2023-03-20T22:37:52Z"}
{"state":"Merged","mergedAt":"2023-03-20T23:08:09Z","number":5305,"mergeCommitSha":"b34d6b806493d249a91604ba395e186ebf65c5ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5305","title":"Sort topic metrics before charting","createdAt":"2023-03-20T23:00:45Z"}
{"state":"Merged","mergedAt":"2023-03-23T18:18:16Z","number":5306,"body":"Refactor CreateKnowledge code into CreateInsight in shared.\r\n\r\nPrep for CreateInsight flow in IntelliJ\r\n\r\n<img width=\"654\" alt=\"CleanShot 2023-03-20 at 16 15 10@2x\" src=\"https://user-images.githubusercontent.com/1553313/226485892-a5673952-e4b9-43c8-9402-d87fc84aede6.png\">\r\n","mergeCommitSha":"408d053cb1ba8e64a86799d224d43252329374a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5306","title":"Refactor Create Insight","createdAt":"2023-03-20T23:16:19Z"}
{"state":"Merged","mergedAt":"2023-03-21T18:17:18Z","number":5307,"mergeCommitSha":"a32c43e5ea9dbd7502f9e1a7f03b5db66017f211","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5307","title":"Add jetbrains build to installer job","createdAt":"2023-03-21T02:30:30Z"}
{"state":"Merged","mergedAt":"2023-03-21T18:25:31Z","number":5308,"body":"Somehow this package-lock file got modified in my PR: a32c43e5ea9dbd7502f9e1a7f03b5db66017f211\r\n\r\nThis reverts...","mergeCommitSha":"79eaa8afc7eeb2a3d4101c9c2dbc8307d5f87708","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5308","title":"Very odd...","createdAt":"2023-03-21T18:23:42Z"}
{"state":"Merged","mergedAt":"2023-03-21T19:32:53Z","number":5309,"body":"Reproduced by sucking down DEV DB\nhttps://www.notion.so/nextchaptersoftware/How-to-restore-DEV-DB-locally-258298353e514b8db32df33c268cbbbf","mergeCommitSha":"8b95978436bc312aa3696c4649ca1d51d8fb5c53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5309","title":"Fix team member page crashing due to sort order stability violation","createdAt":"2023-03-21T18:27:59Z"}
{"state":"Merged","mergedAt":"2022-03-10T17:18:34Z","number":531,"body":"Current backend APIS are generating incorrect redirect urls.\r\n\r\nFrom dev, getLoginOptions:\r\n```\r\n{\"providers\":[{\"provider\":\"github\",\"oauthUrl\":\"https://apiservice.us-west-2.dev.getunblocked.com/api/login/github?clientType=web\"}]}\r\n```","mergeCommitSha":"e86a93df450aeaa62e42c10ff29bbc2743c9be47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/531","title":"Update deployment configs","createdAt":"2022-03-10T17:10:18Z"}
{"state":"Merged","mergedAt":"2023-03-22T05:19:54Z","number":5310,"mergeCommitSha":"98f15fa0df8dd7ce66d37f681a4e0bef4ffff5a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5310","title":"Upload jetbrains bundle to s3","createdAt":"2023-03-21T18:34:18Z"}
{"state":"Merged","mergedAt":"2023-03-21T19:13:37Z","number":5311,"body":"Oh, it's getting lit \uD83D\uDD25 ","mergeCommitSha":"c87a713c54cfb51c7a48a7cdeb2b255c125214df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5311","title":"Add TopicModel.trendingScore property","createdAt":"2023-03-21T18:53:26Z"}
{"state":"Closed","mergedAt":null,"number":5312,"mergeCommitSha":"f0a55872bda70b558c6042696445ac0818df312f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5312","title":"Flesh out openai implementation","createdAt":"2023-03-21T20:38:44Z"}
{"state":"Merged","mergedAt":"2023-03-21T20:44:36Z","number":5313,"mergeCommitSha":"803414eddb23ada52ab980a9d69a8287ed57e6e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5313","title":"Flesh out openai implemetnation","createdAt":"2023-03-21T20:41:14Z"}
{"state":"Merged","mergedAt":"2023-03-22T17:06:32Z","number":5314,"body":"Add gutter icons to the jetbrains editors.  This gets the general mechanism going, the actual gutter content is very basic right now (just display the title).  Also note that if there are multiple SMs on a single line, there will be multiple icons.  I will fix these issues in the next PR.\r\n\r\n* Add tracking of which files are being edited, and which visible editors are editing each file\r\n* Add SM stream from agent -> plugin\r\n* When an editor is open for a file, begin streaming SMs.  Create gutter icons for the resulting SMs\r\n* Add an agent API for \"agent commands\" -- commands called by the plugin and executed on the agent.  Add a \"view thread\" command.\r\n\r\n<img width=\"1512\" alt=\"Screenshot 2023-03-21 at 2 06 18 PM\" src=\"https://user-images.githubusercontent.com/2133518/226740963-33a03934-d3db-4660-a6e4-545cd84abf01.png\">\r\n","mergeCommitSha":"55d4fd10fc31ff8ddd8c2f68538a62849fe15625","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5314","title":"Jetbrains gutter icons pt1","createdAt":"2023-03-21T20:59:25Z"}
{"state":"Merged","mergedAt":"2023-03-21T22:16:18Z","number":5315,"mergeCommitSha":"da3b670352f450163a3785b3356913981032af1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5315","title":"Pin SwiftChat iOS dependency","createdAt":"2023-03-21T21:36:26Z"}
{"state":"Merged","mergedAt":"2023-03-21T21:55:18Z","number":5316,"body":"- Added explicit handling of `coldSite` flag to each stack. This way the stack could exist as an empty stack while all resourced get deleted. \r\n- set us-east-2 back to a cold site \r\nI have deployed these changes locally. ","mergeCommitSha":"6aface5fab6eb2694684bf9517c4080b12e4ef1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5316","title":"cleanup after dr exercise","createdAt":"2023-03-21T21:44:22Z"}
{"state":"Merged","mergedAt":"2023-03-21T22:43:17Z","number":5317,"mergeCommitSha":"66916273b6e21181e2b8663fffdabd7e7e357b07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5317","title":"Add extract topics engine","createdAt":"2023-03-21T22:39:03Z"}
{"state":"Merged","mergedAt":"2023-03-22T21:01:06Z","number":5318,"body":"Refactors the \"General Sidebar\" logic into a shared stream.\r\n\r\nCreate a SidebarContainer which renders AuthSidebar & GeneralSidebar.\r\n\r\nAdd Logout Functionality\r\n\r\n<img width=\"774\" alt=\"CleanShot 2023-03-21 at 16 08 58@2x\" src=\"https://user-images.githubusercontent.com/1553313/226761923-cd4c7c8a-6977-4fd5-8d77-b3f01606c6b2.png\">\r\n\r\n\r\nTODO: \r\n* Styling\r\n* Some actions in GeneralSidebar","mergeCommitSha":"b1068f6bfef9b80397b014b150b57c6570cc0ddf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5318","title":"Sidebar Container","createdAt":"2023-03-21T23:09:07Z"}
{"state":"Merged","mergedAt":"2023-03-21T23:24:44Z","number":5319,"mergeCommitSha":"66c7cd4d20ab0aea63e6ae63d0ea542dd17f8a24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5319","title":"Optimize api usage for openai","createdAt":"2023-03-21T23:22:24Z"}
{"state":"Merged","mergedAt":"2022-03-10T18:29:37Z","number":532,"body":"OAuth redirect was sending to incorrect URL\r\n\r\nRemove extra `/api` path segment.  ","mergeCommitSha":"4f93553cef7aecc930130f3ea05b1be82014ab27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/532","title":"Update deployment configs","createdAt":"2022-03-10T18:13:22Z"}
{"state":"Merged","mergedAt":"2023-03-21T23:48:57Z","number":5320,"body":"Two problems:\n1. we were looping through GitHub Org owners in the GitHub API twice\n2. we were misrecording those \"owners\" as \"admins\"","mergeCommitSha":"8cd5f00719e48c853336a4b4fecb18f52a0ccf43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5320","title":"Fix issue where we were looping through GitHub Org owners twice","createdAt":"2023-03-21T23:39:58Z"}
{"state":"Merged","mergedAt":"2023-03-24T14:56:58Z","number":5321,"mergeCommitSha":"2809a33512ae23172dff29a68506164fd620bf16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5321","title":"Add util classes to calculate 30-day rolling count","createdAt":"2023-03-21T23:58:08Z"}
{"state":"Merged","mergedAt":"2023-03-23T22:58:35Z","number":5322,"body":"* Reorganize TopicView; moves description to the sidebar\r\n<img width=\"1485\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/226768403-901b6551-0a02-4e78-8b41-42260d54e316.png\">\r\n<img width=\"676\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/226768665-1c824a1c-ff8a-4117-8729-686c2594afa2.png\">\r\n\r\n* Add affordance to invite expert that isn't on Unblocked\r\n<img width=\"319\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/226768484-34c04e8b-bdcc-4ed2-b0ba-679282d5b860.png\">\r\n<img width=\"1482\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/226768518-0923474d-626e-4db7-863c-0ab5c43a6e06.png\">\r\n\r\n* Add hover state with description to Topics view table\r\n<img width=\"754\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/226768797-661d88b1-7e44-4ae9-9007-ea87de29a4be.png\">\r\n\r\n* Add Slack banner to Topics view\r\n<img width=\"1068\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/226768742-8e32ff30-ddce-41c3-b723-6bc33fe13f10.png\">\r\n\r\n\r\n","mergeCommitSha":"1d10347eb7b90b4b42bf8d92500cf9d9e5d842df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5322","title":"Update Topic views","createdAt":"2023-03-22T00:06:36Z"}
{"state":"Merged","mergedAt":"2023-03-22T06:03:21Z","number":5323,"body":"classes will be used in follow up PRs","mergeCommitSha":"311a2b23b6beda1c69568cd2938ffa01a48985a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5323","title":"Adds GitLab and Bitbucket member models","createdAt":"2023-03-22T05:43:59Z"}
{"state":"Merged","mergedAt":"2023-03-22T06:08:10Z","number":5324,"mergeCommitSha":"db67497728e4ec54cb910e1703bc0d201cd1ccb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5324","title":"Fixes S3 upload issue for installer","createdAt":"2023-03-22T06:07:42Z"}
{"state":"Merged","mergedAt":"2023-03-22T17:07:11Z","number":5325,"body":"And announcements","mergeCommitSha":"954228d8fef381990d2642335768363f62b3ca7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5325","title":"Add Bishop Fox team and users to insiders to omit from stats","createdAt":"2023-03-22T16:25:00Z"}
{"state":"Merged","mergedAt":"2023-03-22T17:23:17Z","number":5326,"mergeCommitSha":"a29bb1a479a9b9716e16438085b63ec886ab2c2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5326","title":"Fix build ingestion","createdAt":"2023-03-22T16:48:37Z"}
{"state":"Merged","mergedAt":"2023-03-22T17:55:25Z","number":5327,"mergeCommitSha":"44969243da777abeaa52f4f6734277844ce46b65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5327","title":"Add model images","createdAt":"2023-03-22T17:47:01Z"}
{"state":"Merged","mergedAt":"2023-03-22T18:21:11Z","number":5328,"body":"As discussed.  Any class can implement `AgentCoroutineScope`, which requires a `project` property (which all our services already do).  Such classes will now get a `launchWithIDEAgent` method which will maintain the class context when running.","mergeCommitSha":"ebc20a80bde91a243e0ba31717f581c9bcf1ffb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5328","title":"Jetbrains each service implements coroutine scope","createdAt":"2023-03-22T18:04:56Z"}
{"state":"Merged","mergedAt":"2023-03-22T20:47:01Z","number":5329,"body":"- Added a new stack to contain all of our housekeeping ec2 related lambda functions\r\n- Added a lambda function to run every 5 minutes and disable src/dsk check on any k8s nodes that have it enabled\r\n- Added the coldSite check to S3 stack. I forgot this one in my previous cleanup PR\r\n\r\nThis is temporary while we work to move away from Calico.","mergeCommitSha":"eb7e03ff1e3e4151cfa59550931e067c68c84ca3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5329","title":"Lambda function to disable src/dst check on k8s nodes","createdAt":"2023-03-22T20:38:39Z"}
{"state":"Merged","mergedAt":"2022-03-10T19:39:57Z","number":533,"mergeCommitSha":"db18ab68326c909a8e7a93ff37ded5e92b8e9a69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/533","title":"Use fileHash to retrieve a SourcePoint that's tree-same to a calculated SourcePoint","createdAt":"2022-03-10T18:42:19Z"}
{"state":"Merged","mergedAt":"2023-03-22T21:26:42Z","number":5330,"body":"Re-add request webview","mergeCommitSha":"4decd6ff7a5e6af42a8d0f53ef38565934333831","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5330","title":"Add webview launch","createdAt":"2023-03-22T21:05:57Z"}
{"state":"Merged","mergedAt":"2023-03-22T22:29:59Z","number":5331,"mergeCommitSha":"d06597d2a79e4111ca3bab3d61ea7d81d92f4f02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5331","title":"add openai topics","createdAt":"2023-03-22T21:28:00Z"}
{"state":"Merged","mergedAt":"2023-03-22T22:28:23Z","number":5332,"mergeCommitSha":"796366f49ad1814606904a3ad357e15e681707f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5332","title":"Add request timeouts","createdAt":"2023-03-22T22:28:06Z"}
{"state":"Merged","mergedAt":"2023-03-22T22:39:31Z","number":5333,"mergeCommitSha":"5762c0a25dc6d6d8a1038a625e1e207b56598e98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5333","title":"Increase powerml topic count","createdAt":"2023-03-22T22:39:27Z"}
{"state":"Merged","mergedAt":"2023-03-23T16:55:38Z","number":5334,"mergeCommitSha":"db8a7733586f15152e926a84280f38d6e62ac9ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5334","title":"Make sure recommended topics return with relevant value of 0","createdAt":"2023-03-22T22:54:20Z"}
{"state":"Merged","mergedAt":"2023-03-23T15:26:52Z","number":5335,"body":"Responsible for refreshing existing teams from SCM source of truth,\nand — for each of those teams — refreshing team members and repos.\n\nRuns periodically.\n\nPhases\n- refresh team (this change)\n- refresh members (next)\n- refresh repos (next)","mergeCommitSha":"b83b17e088e59774d713d19b32728c79f7df6b97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5335","title":"Introduce ScmTeamMaintenanceJob for team refresh","createdAt":"2023-03-22T23:45:14Z"}
{"state":"Merged","mergedAt":"2023-03-23T00:57:12Z","number":5336,"body":"https://chapter2global.slack.com/archives/C04FC045M1A/p1679530469129899","mergeCommitSha":"928834510b7b1c86723d66fcc2d6ac7398e8fa81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5336","title":"Fix \"manage your {getProviderDisplayName(provider)} Repositories\"","createdAt":"2023-03-23T00:17:16Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:13:02Z","number":5337,"body":"Add proper icons for different thread types","mergeCommitSha":"7524a238030709f531d061410187e82e06cf94ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5337","title":"Updated jetbrains gutter icons","createdAt":"2023-03-23T00:17:22Z"}
{"state":"Merged","mergedAt":"2023-03-23T22:01:21Z","number":5338,"mergeCommitSha":"85c99df78d88efbcddacc63b2b2f170efc5b2324","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5338","title":"Implements Hub installer for jetbrains","createdAt":"2023-03-23T03:11:13Z"}
{"state":"Merged","mergedAt":"2023-03-23T20:35:29Z","number":5339,"body":"Tested the upgrade and it doesn't trash the existing auth state because the new associated value is optional. Test shows the new config is successfully downloaded and stored (and restored). Not currently used but will be in subsequent PRs.","mergeCommitSha":"54db4e2d51377d9d70eedf7290703fc20bba67ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5339","title":"Adds client config to hub auth state","createdAt":"2023-03-23T03:45:54Z"}
{"state":"Merged","mergedAt":"2022-03-10T19:24:27Z","number":534,"body":"This should publish secrets for each environment as part of the same job deploying service(s)\r\nLimitation: Currently this deploys on each service deploy. It's not a big deal and doesn't cost us anything extra","mergeCommitSha":"4ef3e8b12be9394ac00d1a55a97072e57675f5b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/534","title":"Deploy env secrets as part of CI/CD","createdAt":"2022-03-10T18:51:30Z"}
{"state":"Merged","mergedAt":"2023-03-23T16:19:51Z","number":5340,"mergeCommitSha":"4943ad920a493a318e15269d8da628b3e61a1634","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5340","title":"Hookup Connect Button","createdAt":"2023-03-23T15:46:56Z"}
{"state":"Merged","mergedAt":"2023-03-23T17:20:44Z","number":5341,"body":"Fixes a bug where a user couldn't add a topic from the recommended list.","mergeCommitSha":"1b6d8067a8d585e685a9fdc69b0dd5d1feef8c38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5341","title":"Move topic to approved list when relevance is set to 1.0","createdAt":"2023-03-23T17:00:07Z"}
{"state":"Merged","mergedAt":"2023-03-23T20:16:26Z","number":5342,"body":"Integrates majority of Dropdown functionality.\r\n\r\nOpening URLs was failing as we need to add event handlers for the CefBrowser to intercept url requests.\r\n\r\nCopy URL was not working in IntelliJ as clipboard API is not supported within CefBrowser. (https://magpcss.org/ceforum/viewtopic.php?f=6&t=19097). Implemented backup mechanism when clipboard fails.","mergeCommitSha":"f63c618aff3d6a854cf9f74f3603282b2da965ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5342","title":"Fix issues with dropdown operations in IntelliJ","createdAt":"2023-03-23T17:25:34Z"}
{"state":"Merged","mergedAt":"2023-03-23T17:35:17Z","number":5343,"body":"This should suppress the excessive noise in Security Alarms channel. \r\n\r\nI have already deployed this change to Dev and Prod","mergeCommitSha":"72c401551c38d6279bc464e6681e675262e4ed22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5343","title":"Add falco rule for new calico docker image","createdAt":"2023-03-23T17:27:14Z"}
{"state":"Merged","mergedAt":"2023-03-23T18:09:50Z","number":5344,"body":"Assumption was that every person would have at least one identity.","mergeCommitSha":"420c96326a1ab6950669fe098678d14e70622fc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5344","title":"AdminWeb: Profile page crashes due to missing identities","createdAt":"2023-03-23T18:01:26Z"}
{"state":"Merged","mergedAt":"2023-03-23T18:34:23Z","number":5345,"mergeCommitSha":"1fa17e9a9ddfeebd8b83c3d18f00b28310a7aefa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5345","title":"Add more jeffml tests","createdAt":"2023-03-23T18:34:16Z"}
{"state":"Merged","mergedAt":"2023-03-23T18:54:02Z","number":5346,"mergeCommitSha":"4bcad2c778d0f4a81f2daa3bf025fc12a30d1a55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5346","title":"enable jeffml for prod","createdAt":"2023-03-23T18:53:23Z"}
{"state":"Merged","mergedAt":"2023-03-23T19:55:01Z","number":5347,"body":"Hack fix, until Ktor address this:\nhttps://youtrack.jetbrains.com/issue/KTOR-5708/Ktor-Url-mangles-data-Urls","mergeCommitSha":"a7d7fba01d76530be1ffca36f5ea69a9b24974c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5347","title":"GitLab connect page has missing avatars","createdAt":"2023-03-23T19:14:14Z"}
{"state":"Merged","mergedAt":"2023-03-23T19:44:24Z","number":5348,"body":"Topics should not be moved over as it affects correctness and some relevancy testing we're adding to console pages.\r\nIn any event, it's negated whenever we do a new topic ingetstionl","mergeCommitSha":"abbcbeb6007066112dffccb7505bb4d6f8851c44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5348","title":"super confusing moving topics over","createdAt":"2023-03-23T19:30:01Z"}
{"state":"Merged","mergedAt":"2023-03-23T19:57:15Z","number":5349,"mergeCommitSha":"dcb3cdfaa9c1de586a91099a7e2d23f9f3269b82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5349","title":"Improve task logging","createdAt":"2023-03-23T19:55:13Z"}
{"state":"Merged","mergedAt":"2022-03-10T19:07:19Z","number":535,"mergeCommitSha":"1ac8ae8b36d045a14323264e8abeb7e799f10e80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/535","title":"adding matt's ip to waf","createdAt":"2022-03-10T19:03:30Z"}
{"state":"Merged","mergedAt":"2023-03-23T20:49:08Z","number":5350,"body":"SCM service correctly decrypted a refresh token, correctly refreshed it over the GitLab API,\nbut failed to persist it because it did not have an encryption system loaded.\n\nThis caused downstream chaos in API service during installation.","mergeCommitSha":"1b3167d812d8d76754ad331988184b76ab4d33eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5350","title":"Encryption system must be specified in SCM-service so that it can refresh tokens","createdAt":"2023-03-23T20:42:48Z"}
{"state":"Merged","mergedAt":"2023-03-23T21:03:50Z","number":5351,"mergeCommitSha":"bc49e39e9f7582714e3c8b68b45c77c4547023df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5351","title":"Add install jetbrains client capability","createdAt":"2023-03-23T20:44:03Z"}
{"state":"Merged","mergedAt":"2023-04-03T17:44:17Z","number":5352,"body":"On discussion thread open, open file if resolved.","mergeCommitSha":"07ff963144470e914f43306b714945644a68791d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5352","title":"Open File on discussion open in IntelliJ","createdAt":"2023-03-23T20:52:57Z"}
{"state":"Merged","mergedAt":"2023-03-23T21:40:15Z","number":5353,"body":"How does this not fail CI builds?","mergeCommitSha":"7f4540d77e28e2859736eae94830f96628abff7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5353","title":"JetBrains lint","createdAt":"2023-03-23T21:39:11Z"}
{"state":"Merged","mergedAt":"2023-03-23T22:11:41Z","number":5354,"mergeCommitSha":"39f8a5d21a101ea9ec1596e8ff87e74bb2c36644","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5354","title":"Admin web provider brand icons","createdAt":"2023-03-23T22:10:56Z"}
{"state":"Merged","mergedAt":"2023-03-24T01:03:45Z","number":5355,"body":"<img width=\"1193\" alt=\"Screenshot 2023-03-23 at 17 47 17\" src=\"https://user-images.githubusercontent.com/1798345/227396190-b0efbfe9-4f7d-4bc9-843e-cd585e69b13b.png\">\r\n","mergeCommitSha":"2a08570f0c16c4e259bd6ef083b16ccac1ecd181","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5355","title":"Cleanup admin web person page","createdAt":"2023-03-24T00:47:05Z"}
{"state":"Merged","mergedAt":"2023-03-26T18:19:56Z","number":5356,"body":"Runs periodically.","mergeCommitSha":"7595085fe8d1e1363dd48a4ae77b9f1b4f1ef5ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5356","title":"Introduce TeamMemberMaintenance to manage team members","createdAt":"2023-03-24T00:47:08Z"}
{"state":"Merged","mergedAt":"2023-03-24T02:45:54Z","number":5357,"mergeCommitSha":"c3e8503c45f3c99de435e272ff20f042626aabb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5357","title":"increase timeouts","createdAt":"2023-03-24T02:45:39Z"}
{"state":"Merged","mergedAt":"2023-03-24T03:39:51Z","number":5358,"body":"Fix installer build","mergeCommitSha":"dd143260c4bad0f19060dd19935fa0e09e811607","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5358","title":"Add token","createdAt":"2023-03-24T03:30:24Z"}
{"state":"Closed","mergedAt":null,"number":5359,"body":"Add missing token setting","mergeCommitSha":"28908bf4bfe0778158c71616480570ab5a51ca21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5359","title":"Fix installer","createdAt":"2023-03-24T04:00:30Z"}
{"state":"Merged","mergedAt":"2022-03-10T20:19:52Z","number":536,"body":"SourceMarksProvider is going to end up hitting the API service so we should just use those models instead","mergeCommitSha":"fd3c176f30f314c12c3fa0d2f5cdb186ffa4b5ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/536","title":"Use API models","createdAt":"2022-03-10T19:17:28Z"}
{"state":"Merged","mergedAt":"2023-03-24T04:11:27Z","number":5360,"mergeCommitSha":"cc5a04ccf36d3977749dc1387b73cdb57550b922","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5360","title":"Add missing token","createdAt":"2023-03-24T04:01:45Z"}
{"state":"Merged","mergedAt":"2023-03-24T06:15:38Z","number":5361,"mergeCommitSha":"1c1b2058a59207a09b2f20dc9c83d0925653f051","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5361","title":"Fix task id generator","createdAt":"2023-03-24T06:15:31Z"}
{"state":"Merged","mergedAt":"2023-03-24T08:29:00Z","number":5362,"mergeCommitSha":"626ec83dc771ee03263cfaf8d94e6324526c0f46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5362","title":"Optimize prompts token usage","createdAt":"2023-03-24T08:28:06Z"}
{"state":"Merged","mergedAt":"2023-03-24T10:12:35Z","number":5363,"mergeCommitSha":"e1d0d229d19822154af7df444f35d85445f5d7d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5363","title":"Add debiggom","createdAt":"2023-03-24T10:12:29Z"}
{"state":"Merged","mergedAt":"2023-03-24T10:14:34Z","number":5364,"mergeCommitSha":"6452ef01d02fde7c159467d5540c564ff5974e8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5364","title":"update model files","createdAt":"2023-03-24T10:14:27Z"}
{"state":"Merged","mergedAt":"2023-03-27T16:44:16Z","number":5365,"body":"Sets up auth for the ScmRepoApi, which will be used for the majority of SCM operations on repo (eg: PR ingest)","mergeCommitSha":"4377bab4ff441abc2379575c22bab013e6fe17e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5365","title":"SCM api for repo operations","createdAt":"2023-03-24T15:33:58Z"}
{"state":"Merged","mergedAt":"2023-03-24T16:25:04Z","number":5366,"mergeCommitSha":"2cee0f5856701c392b3d555604c9ab5c72fba9e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5366","title":"Fix installer","createdAt":"2023-03-24T16:22:44Z"}
{"state":"Merged","mergedAt":"2023-03-24T17:34:57Z","number":5367,"mergeCommitSha":"d242df171ba91a4cb1c12a843fdd2c2a4f65c338","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5367","title":"Fix sending emails","createdAt":"2023-03-24T17:26:02Z"}
{"state":"Merged","mergedAt":"2023-03-24T19:10:54Z","number":5368,"body":"Next PR will wire these up","mergeCommitSha":"debbaf529cb141cc25dd84e6aa4e6b596366f6ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5368","title":"Add helper functions to calculate trending score","createdAt":"2023-03-24T18:30:37Z"}
{"state":"Merged","mergedAt":"2023-03-24T19:36:29Z","number":5369,"body":"The Copy operation will leave the old plugin jar in the plugin directory alongside the new one. Which plugin gets loaded is non-deterministic. To be safe, we need to nuke the plugin directory before the copy operation.","mergeCommitSha":"fd2c746ff9bd58ecd84583df96a4a50a75eac15d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5369","title":"Remove plugin directory before update","createdAt":"2023-03-24T18:46:14Z"}
{"state":"Merged","mergedAt":"2022-03-10T19:38:12Z","number":537,"body":"Setup environment specific tokens for vscode.\r\nWas running into issues where dev was trying to use local credentials in VSCode as they share common workspace data.\r\n\r\nUnnecessary for web as localstorage is per domain/subdomain.","mergeCommitSha":"bbb9c091ffcf3955c7c6550c792596200fc81cb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/537","title":"Setup environment specific tokens","createdAt":"2022-03-10T19:24:25Z"}
{"state":"Merged","mergedAt":"2023-03-24T18:51:39Z","number":5370,"mergeCommitSha":"63128bb81518c95c530aba0bc66145feff4b1342","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5370","title":"Add stop words","createdAt":"2023-03-24T18:50:53Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:32:13Z","number":5371,"body":"We were dependant on font family from IntelliJ Settings.\r\nThis font *may* not exist in system so we need some sensible fallbacks.\r\nBefore:\r\n<img width=\"531\" alt=\"CleanShot 2023-03-24 at 11 56 29@2x\" src=\"https://user-images.githubusercontent.com/1553313/227615417-48bb2400-a2f4-40d9-ad14-57795727daab.png\">\r\n\r\nAfter\r\n<img width=\"417\" alt=\"CleanShot 2023-03-24 at 11 56 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/227615434-eae9a207-93d2-4629-8f3e-e5de2ec97f75.png\">\r\n","mergeCommitSha":"3f93ed68600766c90bd1ea23ac949d974f3c3b58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5371","title":"[UNB-1078] Add backup fonts when UI font is unavailable in system","createdAt":"2023-03-24T18:57:47Z"}
{"state":"Merged","mergedAt":"2023-03-24T19:50:16Z","number":5372,"body":"Controller action stream is publishing \"cached\" events when new subscription is setup.\r\n\r\nSubscription now filters out mismatch insight ids.","mergeCommitSha":"981a0119a2a5e5432b5e8bd2156e7f00cd2f381b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5372","title":"Scope Insight commands to their respective model","createdAt":"2023-03-24T19:11:20Z"}
{"state":"Merged","mergedAt":"2023-03-24T20:26:23Z","number":5373,"mergeCommitSha":"e44448c4a0c5981dfd40009a8362ac3324607f3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5373","title":"Calculate trending score every night","createdAt":"2023-03-24T19:36:53Z"}
{"state":"Merged","mergedAt":"2023-03-28T19:51:05Z","number":5374,"body":"Currently sharing the same tokens for all environments.\r\n\r\nThis is an issue if one uses unblocked on their main IntelliJ & builds IntelliJ locally in Dev","mergeCommitSha":"4321f9fff2b0ebc822499dc8f00beda800743a81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5374","title":"Setup environment specific tokens","createdAt":"2023-03-24T20:38:09Z"}
{"state":"Merged","mergedAt":"2023-03-24T21:16:26Z","number":5375,"mergeCommitSha":"062885b0f16dd22858b297b66a031a67679db0e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5375","title":"Allow updating trending score from admin console","createdAt":"2023-03-24T20:49:27Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:21:59Z","number":5376,"body":"<img width=\"529\" alt=\"CleanShot 2023-03-24 at 14 30 16@2x\" src=\"https://user-images.githubusercontent.com/1553313/227645280-b74af71a-c106-4b16-8453-532b96a8924d.png\">\r\n","mergeCommitSha":"53ff0ca4910c2a6d44db3d68eb25599c6f73bd73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5376","title":"Update tab styling","createdAt":"2023-03-24T21:31:10Z"}
{"state":"Merged","mergedAt":"2023-03-24T21:56:59Z","number":5377,"mergeCommitSha":"d19cdba33036d3df1d7f416a23f5dcf8f3c3fbe5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5377","title":"Make calculateTrendingScore is less generous","createdAt":"2023-03-24T21:40:50Z"}
{"state":"Open","mergedAt":null,"number":5378,"body":"<img width=\"1410\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/227653344-de0c3568-c41c-4168-9bcd-33c2fe48f5b0.png\">\r\n","mergeCommitSha":"5d82ac24f6e8ebabac034c0a55ed7dae45f9aeae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5378","title":"Add topics intro dialog","createdAt":"2023-03-24T22:16:36Z"}
{"state":"Merged","mergedAt":"2023-03-27T21:49:57Z","number":5379,"body":"<img width=\"1423\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/227653457-a767f4db-cfde-4e2e-ab6f-cab81dda9969.png\">\r\n\r\n<img width=\"388\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/227653555-a1c486a6-d48b-4849-92ca-8d1e614c3321.png\">\r\n\r\n<img width=\"462\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/227656984-7575bef4-b678-4e1d-a5a9-a1e045b95421.png\">\r\n\r\n","mergeCommitSha":"4d044a030e1040656769c14115a8e90de5f7cac2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5379","title":"Add trending property to topics ","createdAt":"2023-03-24T22:18:15Z"}
{"state":"Merged","mergedAt":"2022-03-10T19:48:43Z","number":538,"mergeCommitSha":"2213e5413a4bb09e0d57649a79be1aae51e156a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/538","title":"Point cache must be write-through backfilled during recalculation","createdAt":"2022-03-10T19:33:29Z"}
{"state":"Merged","mergedAt":"2023-03-25T15:47:35Z","number":5380,"body":"This will enable the nightly job that calculates trending score for topics.","mergeCommitSha":"7e3b172d2d27853aaf93f4f5b990dcd392105851","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5380","title":"getTopicsForMetrics returns approved topics","createdAt":"2023-03-24T22:52:44Z"}
{"state":"Merged","mergedAt":"2023-03-24T23:39:37Z","number":5381,"mergeCommitSha":"9f4d4438bcf304cfc28ddb088c04888c770e6e74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5381","title":"Trigger topic metrics calculation for all approved topics","createdAt":"2023-03-24T23:22:38Z"}
{"state":"Merged","mergedAt":"2023-03-27T22:02:22Z","number":5382,"body":"I tried to do this without restarting and had it working under some conditions, but it ultimately proved impossible to guarantee an effective reload without a restart. Internal APIs also had to be used, and that yielded a runtime compatibility issue while experimenting, so ultimately that approach had to be abandoned.\r\n\r\nSince \"installation\" is already handled by dropping bits manually, the \"reload\" operation is all that's left. Plugin \"reload\" in IntelliJ requires the creation of a plugin \"descriptor\" for the updated plugin, which is an internal API. \r\n\r\nI also tried coercing IntelliJ's public plugin related classes to execute reload related code-paths to no avail. They really went out of their way to prevent manual plugin manipulation \uD83D\uDE2D.\r\n\r\nAt any rate, this works.\r\n\r\n\r\nRight after the bits are dropped, user will see this:\r\n<img width=\"415\" alt=\"CleanShot 2023-03-27 at 10 37 04@2x\" src=\"https://user-images.githubusercontent.com/858772/228021893-4edf4aa7-6ef2-407b-b317-08765a541b72.png\">\r\n\r\n\r\nAnd then when they click \"Reload\", they will see this:\r\n<img width=\"418\" alt=\"CleanShot 2023-03-27 at 10 37 13@2x\" src=\"https://user-images.githubusercontent.com/858772/228021951-8ab613ce-71af-4afa-8d56-dffee4263f42.png\">\r\n","mergeCommitSha":"d6529fd071e7369da502069418944a010fda5faa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5382","title":"Detect plugin updates and restart","createdAt":"2023-03-25T19:50:45Z"}
{"state":"Merged","mergedAt":"2023-03-27T14:54:35Z","number":5383,"mergeCommitSha":"6ccb5b60925ae8767396df7f6fa2629822f8b9dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5383","title":"Trigger topic trending score recalculation from the admin console","createdAt":"2023-03-27T06:38:55Z"}
{"state":"Merged","mergedAt":"2023-03-27T17:36:49Z","number":5384,"mergeCommitSha":"4c908cd7f9f4cd0dacecd4648088fdbdf6b130ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5384","title":"Increase ratio for the first trending level","createdAt":"2023-03-27T16:11:56Z"}
{"state":"Merged","mergedAt":"2023-03-27T18:26:27Z","number":5385,"mergeCommitSha":"4b535514a5745b64d08522fdbc2de8211ff14e83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5385","title":"Refactor HTTP SCM clients as ScmHttpClientFactory","createdAt":"2023-03-27T17:46:26Z"}
{"state":"Merged","mergedAt":"2023-03-27T18:35:53Z","number":5386,"body":"Vends generic ScmRepoApi interface, mainly for use in PR ingest.","mergeCommitSha":"f716fcbc3124f613dbd020e75b0117874a2f1578","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5386","title":"Add generic SCM Repo API Factory","createdAt":"2023-03-27T17:46:29Z"}
{"state":"Merged","mergedAt":"2023-03-27T18:36:34Z","number":5387,"mergeCommitSha":"00ec141e37ffc0deba17d5437f30c388542a4c8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5387","title":"Clean ununsed SCM classes","createdAt":"2023-03-27T17:46:32Z"}
{"state":"Merged","mergedAt":"2023-03-27T19:16:46Z","number":5388,"body":"<img width=\"1580\" alt=\"CleanShot 2023-03-27 at 11 45 27@2x\" src=\"https://user-images.githubusercontent.com/1924615/228037211-e297aafe-6fbc-4436-8fc6-fee744dbbe2b.png\">\r\n","mergeCommitSha":"67f828c5ebbc74ca24738c6e8634ea679e6ee301","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5388","title":"Add ability to update topic description from admin console","createdAt":"2023-03-27T18:45:58Z"}
{"state":"Merged","mergedAt":"2023-03-27T19:32:11Z","number":5389,"body":"This is an expected scenario should a user install on their personal org.","mergeCommitSha":"acb599b0d4851b4cd2252ceb7ff403ef22d58cbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5389","title":"Downgrade \"Not processing personal installation\" warning to info","createdAt":"2023-03-27T19:28:40Z"}
{"state":"Merged","mergedAt":"2022-03-10T19:48:55Z","number":539,"mergeCommitSha":"9beeadcbde680bb697b3cb3779d6c778854f7b0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/539","title":"forgot to add vault password to dev deploy job","createdAt":"2022-03-10T19:40:35Z"}
{"state":"Merged","mergedAt":"2023-03-27T20:38:03Z","number":5390,"body":"```json\n{\n  \"level\": \"ERROR\",\n  \"messageId\": \"ID:webhookservice-58b96fbdb8-mmv5j-42025-1679867683776-1:3:1:1:302\",\n  \"message\": \"Failed to process event message\",\n  \"platform\": {\n    \"version\": \"7595085fe8d1e1363dd48a4ae77b9f1b4f1ef5ff\",\n    \"buildNumber\": \"19750\"\n  },\n  \"environment\": \"prod\",\n  \"@timestamp\": \"2023-03-27T10:15:32.434+0000\",\n  \"LogSize\": 2812,\n  \"service\": \"scmservice\",\n  \"thread_name\": \"DefaultDispatcher-worker-339\",\n  \"teamId\": \"29cbd453-4a25-4792-9424-6f172abfab70\",\n  \"logger_name\": \"com.nextchaptersoftware.event.queue.dequeue.EventDequeueService\",\n  \"stack_trace\": \"k.s.SerializationException: com.nextchaptersoftware.scm.github.models.GitHubPullRequestEvent.Action does not contain element with name 'dequeued' at path $.action\n\tat k.s.j.i.JsonNamesMapKt.getJsonNameIndexOrThrow(JsonNamesMap.kt:63)\n\tat k.s.j.i.StreamingJsonDecoder.decodeEnum(StreamingJsonDecoder.kt:351)\n\tat c.n.s.g.m.GitHubPullRequestEvent$Action$$serializer.deserialize(GitHubPullRequestEvent.kt:15)\n\tat c.n.s.g.m.GitHubPullRequestEvent$Action$$serializer.deserialize(GitHubPullRequestEvent.kt:15)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\tat k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\n\tat k.s.e.AbstractDecoder.decodeSerializableElement(AbstractDecoder.kt:70)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableElement(StreamingJsonDecoder.kt:162)\n\tat c.n.s.g.m.GitHubPullRequestEvent$$serializer.deserialize(GitHubPullRequestEvent.kt:9)\n\tat c.n.s.g.m.GitHubPullRequestEvent$$serializer.deserialize(GitHubPullRequestEvent.kt:9)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\tat k.s.json.Json.decodeFromString(Json.kt:95)\n\tat c.n.s.h.g.GitHubPullRequestHandler.handle(GitHubPullRequestHandler.kt:19)\n\tat c.n.s.h.ScmEventWebhookHandler.handleGitHubWebhook(ScmEventWebhookHandler.kt:127)\n\tat c.n.s.h.ScmEventWebhookHandler.access$handleGitHubWebhook(ScmEventWebhookHandler.kt:38)\n\tat c.n.s.h.ScmEventWebhookHandler$process$2.invokeSuspend(ScmEventWebhookHandler.kt:87)\n\tat c.n.s.h.ScmEventWebhookHandler$process$2.invoke(ScmEventWebhookHandler.kt)\n\tat c.n.s.h.ScmEventWebhookHandler$process$2.invoke(ScmEventWebhookHandler.kt)\n\tat k.c.i.UndispatchedKt.startUndispatchedOrReturnIgnoreTimeout(Undispatched.kt:100)\n\tat k.c.TimeoutKt.setupTimeout(Timeout.kt:146)\n\tat k.c.TimeoutKt.withTimeout(Timeout.kt:44)\n\tat k.c.TimeoutKt.withTimeout-KLykuaI(Timeout.kt:71)\n\tat c.n.s.h.ScmEventWebhookHandler.process(ScmEventWebhookHandler.kt:82)\n\tat c.n.s.h.ScmEventWebhookHandler.access$process(ScmEventWebhookHandler.kt:38)\"\n}\n```","mergeCommitSha":"5c3c52c390641c6f20f97f8303e3e160865d7247","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5390","title":"Add missing GitHubPullRequestEvent types","createdAt":"2023-03-27T20:23:48Z"}
{"state":"Merged","mergedAt":"2023-03-27T22:35:38Z","number":5391,"body":"## Before\r\n<img width=\"1318\" alt=\"Screenshot 2023-03-27 at 15 19 06\" src=\"https://user-images.githubusercontent.com/1798345/228079623-b579d660-4adb-45e2-8458-c0a4cc266162.png\">\r\n\r\n## After\r\n<img width=\"1324\" alt=\"Screenshot 2023-03-27 at 15 18 33\" src=\"https://user-images.githubusercontent.com/1798345/228079617-fa8f1d04-bba3-474e-be74-53aeb94a3876.png\">\r\n","mergeCommitSha":"c5e4c2a472f237e76852ca3d25f956ddd49ffc9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5391","title":"Admin: searching for identities shows team memberships more clearly","createdAt":"2023-03-27T22:16:59Z"}
{"state":"Merged","mergedAt":"2023-03-27T22:36:29Z","number":5392,"body":"For Dennis.\r\n\r\n## Before\r\n<img width=\"1656\" alt=\"Screenshot 2023-03-27 at 15 16 43\" src=\"https://user-images.githubusercontent.com/1798345/228079321-3af561a0-657e-4153-a7a4-a35924eb4366.png\">\r\n\r\n\r\n## After\r\n\r\n<img width=\"1652\" alt=\"Screenshot 2023-03-27 at 15 16 48\" src=\"https://user-images.githubusercontent.com/1798345/228079326-d57ec145-c9d6-426d-a6ae-81e18a088385.png\">\r\n","mergeCommitSha":"955748367d7d80dfd00b25143a991a3ddb2db906","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5392","title":"Admin: People page shows smaller badge icons for SCMs","createdAt":"2023-03-27T22:17:02Z"}
{"state":"Merged","mergedAt":"2023-03-27T22:51:01Z","number":5393,"body":"* Rename `AgentCoroutineScope` to `ProjectCoroutineScope`, as the scope is tied to the project, not the agent\r\n* `ProjectCoroutineScope` uses `ProjectCoroutineService` to get a singleton coroutine scope for each project\r\n* This is done similarly everywhere now","mergeCommitSha":"5244696eb4d4be55ed01d816d32f8eecc628cc47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5393","title":"Jetbrains fix confusing coroutine setup","createdAt":"2023-03-27T22:21:24Z"}
{"state":"Merged","mergedAt":"2023-03-27T23:03:14Z","number":5394,"body":"Just adding a couple more admin console visualizations to see if we can improve on the definition of topic trendiness.","mergeCommitSha":"36d70433182ef4267bc15d0224a62116aafa6c90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5394","title":"Visualize average of daily message counts for a topic","createdAt":"2023-03-27T22:48:12Z"}
{"state":"Merged","mergedAt":"2023-03-28T17:18:33Z","number":5395,"body":"As part of the new install and update process we need to lift the vscode extension out of the installer. This PR doesn't remove the extension from the installer though - that will come in a follow up after we test the alternative installation path internally for a few builds. ","mergeCommitSha":"44b76cb942549354b7307b2c15596952e1a5f9e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5395","title":"Upload VSCode bundle to S3 on installer builds","createdAt":"2023-03-27T22:49:46Z"}
{"state":"Merged","mergedAt":"2023-03-28T00:16:32Z","number":5396,"body":"Slack webhook validation requires taking into account request timestamp and signature.\r\nSlacks sdk is fully formed and has a signature validator for this...\r\n\r\nTested against local stack with local webhooks.","mergeCommitSha":"65423b7053521793cbd5f5e662da4cbf3817f010","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5396","title":"[BREAKS API ON MAIN] Validate slack webhook secrets","createdAt":"2023-03-27T23:11:31Z"}
{"state":"Merged","mergedAt":"2023-03-28T00:34:31Z","number":5397,"body":"This reverts commit 65423b7053521793cbd5f5e662da4cbf3817f010.\r\n","mergeCommitSha":"66f32e9bf6a99a7f4e18487237f28a83fb7cd2eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5397","title":"Revert \"[BREAKS API ON MAIN] Validate slack webhook secrets (#5396)\"","createdAt":"2023-03-28T00:34:26Z"}
{"state":"Merged","mergedAt":"2023-03-28T01:39:59Z","number":5398,"mergeCommitSha":"a8fdd9f4d96b98f00c8a3db9e8bf2d8ff212d4f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5398","title":"Add ability to override operation earliest releases so that server side operations are not used in compat checks","createdAt":"2023-03-28T01:17:09Z"}
{"state":"Merged","mergedAt":"2023-03-28T01:59:15Z","number":5399,"mergeCommitSha":"ec23885abafda4c3d4b0cdba32557eb2ecccc317","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5399","title":"Admin: refactoring","createdAt":"2023-03-28T01:18:10Z"}
{"state":"Merged","mergedAt":"2022-01-18T06:24:40Z","number":54,"body":"### ktlint IntelliJ Setup\r\n\r\n1. Install **ktlint** IntelliJ plugin\r\n2. Configure ktlint\r\n    1. Open IntelliJ **Preferences**\r\n    2. Go to **Tools** » **ktlint** \r\n    2. Toggle only these checkboxes on:\r\n        - [x] Enable ktlint\r\n        - [x] Lint after Reformat\r\n    3. Set **Annotate ktlint errors as** to `Error`","mergeCommitSha":"8a2f27ffa31a4fb8bb99604355a5876ac150fb89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/54","title":"Add ktlint check","createdAt":"2022-01-18T06:18:23Z"}
{"state":"Merged","mergedAt":"2022-03-10T20:27:34Z","number":540,"body":"also make ansible deploys more verbose so I can troubleshoot things","mergeCommitSha":"6efb5e984c2458d8f1e74f26a8994edfa0ee4f28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/540","title":"Disable prod deploys for now","createdAt":"2022-03-10T20:19:37Z"}
{"state":"Merged","mergedAt":"2023-03-28T01:51:30Z","number":5400,"body":"This reverts commit 66f32e9bf6a99a7f4e18487237f28a83fb7cd2eb.\r\n","mergeCommitSha":"c2bd996768e02d59520315f92f8b05d47b8bc2fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5400","title":"Revert \"Revert \"[BREAKS API ON MAIN] Validate slack webhook secrets (#5396)\" (#5397)\"","createdAt":"2023-03-28T01:41:05Z"}
{"state":"Merged","mergedAt":"2023-03-28T05:40:42Z","number":5401,"mergeCommitSha":"2c820315b19e25f8f4ec612d9cb5a706491bb7b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5401","title":"fix bugs related to insight topic mapping","createdAt":"2023-03-28T05:23:20Z"}
{"state":"Merged","mergedAt":"2023-03-28T06:19:07Z","number":5402,"body":"- update code\r\n- Fix prompt\r\n","mergeCommitSha":"b4ebdfa5ac7c6b1cf066aaf880908279ebc8d8f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5402","title":"UpdateOpenAICode","createdAt":"2023-03-28T06:18:44Z"}
{"state":"Merged","mergedAt":"2023-03-28T17:26:24Z","number":5403,"body":"On refresh of the Topics view, the red banner to prompt users to connect to Slack is consistently flash on render:\r\n\r\nhttps://user-images.githubusercontent.com/13431372/228311632-132d2935-b2d3-45c7-8dfa-45b5db9ddbec.mp4\r\n\r\n\r\n\r\nTo mitigate this behaviour, we should default on setting the show boolean to false, and opt in to showing the banner only if the conditions apply (i.e. they don't have slack installed).","mergeCommitSha":"2eb8c7f47ad2ca3d0e469c91a26ce6dd9bdb168f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5403","title":"Fix banner flashing","createdAt":"2023-03-28T16:50:05Z"}
{"state":"Merged","mergedAt":"2023-03-28T17:17:31Z","number":5404,"mergeCommitSha":"56539973de060e48aa793e8134445904ab1878b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5404","title":"Only seek spotonly instances for jetbrains","createdAt":"2023-03-28T17:17:20Z"}
{"state":"Merged","mergedAt":"2023-03-28T17:43:00Z","number":5405,"mergeCommitSha":"cd8914dee62f724a2b98e1e823c36800b7093c8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5405","title":"Deprecate the getMessages API","createdAt":"2023-03-28T17:18:11Z"}
{"state":"Merged","mergedAt":"2023-03-30T21:10:37Z","number":5406,"body":"re: https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/7b6fcc9d-c312-48ac-8234-7634d979f51c\r\n\r\nThe regular `ScrollToTop` top level component is only triggered on route change. In the case of the New Topic creation flow, the route doesn't actually change, but the views do. Add a hook to manually scroll to the top given a list of dependencies.\r\n\r\n","mergeCommitSha":"d017eb2e0ac112fdcbf293eaf953cd6514da39ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5406","title":"Add scrollToTop hook for manual scrolling","createdAt":"2023-03-28T17:19:59Z"}
{"state":"Merged","mergedAt":"2023-03-28T17:27:48Z","number":5407,"body":"- Only seek spotonly instances for jetbrains\r\n- increase renovate parallel count\r\n","mergeCommitSha":"d678554d2fce920fe61489f2e3a1a96e31a6e8ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5407","title":"IncreaseRenovateParallelCount","createdAt":"2023-03-28T17:27:41Z"}
{"state":"Merged","mergedAt":"2022-03-10T21:40:49Z","number":541,"body":"Allows the sourcemark agent to add a SourcePoint to an existing SourceMark","mergeCommitSha":"bb2828dd3a5b3f419b515faacfe66253781d71b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/541","title":"Add putSourcePoint operation","createdAt":"2022-03-10T20:28:47Z"}
{"state":"Merged","mergedAt":"2023-03-28T18:18:14Z","number":5413,"mergeCommitSha":"8459c1d5c78ccad7f2c1d45e7671262a6254445f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5413","title":"Add client flag for VSCode download during install/update","createdAt":"2023-03-28T17:50:11Z"}
{"state":"Merged","mergedAt":"2022-03-10T21:07:53Z","number":542,"body":"Existing password in Action secrets was for local. Added a new secret for k8s vault password.","mergeCommitSha":"6f255c49d87daf9b2fbc8b8428f7a188da547e2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/542","title":"Added k8s vault secret","createdAt":"2022-03-10T21:02:10Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:09:49Z","number":5421,"body":"Update topics view table to align with designs:\r\n<img width=\"848\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/228342251-f4e2fd67-317e-402f-a399-b8e4775c8dd2.png\">\r\n\r\n\r\n","mergeCommitSha":"761656f8baa4d27569b9129dae64b49978e7dc85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5421","title":"Refactor TopicsView table","createdAt":"2023-03-28T18:22:10Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:04:19Z","number":5422,"mergeCommitSha":"980d4c649ad17c5e0310b9a7c1246cbb3ecb391b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5422","title":"Allow updating existing topic trending calculations when run from the admin console","createdAt":"2023-03-28T18:34:28Z"}
{"state":"Merged","mergedAt":"2023-03-28T22:03:13Z","number":5423,"mergeCommitSha":"8413e3f1f93f84386c43dd9b661e806b3a3322e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5423","title":"Download VSCode plugin from S3 instead of unpacking from installer","createdAt":"2023-03-28T18:40:08Z"}
{"state":"Merged","mergedAt":"2023-03-28T19:36:11Z","number":5424,"body":"Fix potential issue with missing topics in explorer sidebar.\r\n\r\nIf sourcemarks / threads stream emits an empty list, we would try to get topics for the file. API service would return an empty list of topics which would get cached.\r\n\r\nThe next time the threads stream comes around and *is* populated, we will grab from the fetched result instead of retrying the request.","mergeCommitSha":"4025a187e24c837fff8a2220e2e5f15616666b53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5424","title":"Fix missing topics","createdAt":"2023-03-28T18:49:21Z"}
{"state":"Merged","mergedAt":"2023-03-28T22:02:37Z","number":5425,"body":"- Approved topics should be only approve topics source\r\n- Lint\r\n","mergeCommitSha":"816161556a5556d15818b663e6de519a2142302c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5425","title":"FixApprovedTopics","createdAt":"2023-03-28T18:59:47Z"}
{"state":"Merged","mergedAt":"2022-03-10T21:38:19Z","number":543,"body":"```\r\nTASK [deploy k8s secrets] ******************************************************\r\n[371](https://github.com/NextChapterSoftware/unblocked/runs/5502771592?check_suite_focus=true#step:3:371)\r\nAn exception occurred during task execution. To see the full traceback, use -vvv. The error was: ModuleNotFoundError: No module named 'kubernetes'\r\n[372](https://github.com/NextChapterSoftware/unblocked/runs/5502771592?check_suite_focus=true#step:3:372)\r\nfailed: [localhost] (item=/home/<USER>/work/unblocked/unblocked/secrets/k8s/assets/secrets.env.yaml) => {\"ansible_loop_var\": \"item\", \"changed\": false, \"error\": \"No module named 'kubernetes'\", \"item\": \"/home/<USER>/work/unblocked/unblocked/secrets/k8s/assets/secrets.env.yaml\", \"msg\": \"Failed to import the required Python library (kubernetes) on fv-az200-776's Python /opt/pipx/venvs/ansible-core/bin/python. Please read the module documentation and install it in the appropriate location. If the required library is installed, but Ansible is using the wrong Python interpreter, please consult the documentation on ansible_python_interpreter\"}\r\n```\r\n\r\nAdded pip package for Kubernetes. This is my last attempt. If this doesn't work I'll disable secrets deployments for now. ","mergeCommitSha":"3cdcc19110f854ad50492eeff54c11fa669e5bb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/543","title":"add kubernetes pip package required by ansible","createdAt":"2022-03-10T21:36:48Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:26:54Z","number":5432,"body":"Not the right approach, but this quick hack fixes the problem for now.\n\nShould hook up pagination to data tables instead.","mergeCommitSha":"16ca4bb2035a6bb5324dc30f9ee3fbac6014d204","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5432","title":"Bump people page limit to 150 because we've exceeded 100 users","createdAt":"2023-03-28T20:20:27Z"}
{"state":"Merged","mergedAt":"2023-03-29T00:26:35Z","number":5433,"body":"This in turn allows us to rip out all the GitHub-specific code paths.","mergeCommitSha":"b2dce22a6f1c8e821503dc70815a456657af4506","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5433","title":"GitHub Team API now uses GitHub App Auth","createdAt":"2023-03-28T20:21:06Z"}
{"state":"Merged","mergedAt":"2023-03-28T20:45:40Z","number":5434,"body":"Default prod value","mergeCommitSha":"012df6004ba07dfc0e6e8e014b98978b88528e62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5434","title":"Set default env value","createdAt":"2023-03-28T20:27:50Z"}
{"state":"Merged","mergedAt":"2023-03-28T22:32:33Z","number":5435,"body":"In dark mode, using pure white can cause issues with the color rendering:\r\n<img width=\"607\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/228354471-56d150e6-bf2e-4562-a67d-49403cce6fa8.png\">\r\n\r\n* also update other templates to remove thread title and actions image\r\n![image](https://user-images.githubusercontent.com/13431372/228370217-c51adb48-d27c-4801-a168-13331f740d61.png)\r\n\r\n\r\n","mergeCommitSha":"0b40b67afaa80e280e4150f78077cd6302019a6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5435","title":"Update email templates","createdAt":"2023-03-28T20:28:41Z"}
{"state":"Merged","mergedAt":"2023-04-03T16:29:10Z","number":5436,"body":"Don't allow any deprecated APIs in typescript code.\r\n\r\n* Add a new OpenAPI template file for the `typescript-fetch` API, this allows us to generate `@deprecated` markers for all APIs correctly\r\n* Add eslint rules to disallow using deprecated APIs\r\n* Fix all the places we were using deprecated APIs\r\n\r\nI will look into submitting the `typescript-fetch` template change upstream to the openAPI generator project.","mergeCommitSha":"13875ac4bf3fa2a5eb19dfbe683bb00ff60f5839","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5436","title":"Don't allow deprecated APIs in TS code","createdAt":"2023-03-28T21:51:03Z"}
{"state":"Merged","mergedAt":"2023-03-28T22:10:08Z","number":5439,"body":"Remove old tutorial wizard code.","mergeCommitSha":"ebefd8ae0b6ea635f5b23cc28419491960d6cadb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5439","title":"Remove old onboarding code","createdAt":"2023-03-28T21:58:32Z"}
{"state":"Merged","mergedAt":"2022-03-10T21:53:54Z","number":544,"mergeCommitSha":"486d2feaf17f6fa582425b565df8a8c46356a5b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/544","title":"Screw k8s secrets","createdAt":"2022-03-10T21:51:25Z"}
{"state":"Merged","mergedAt":"2023-04-03T23:49:10Z","number":5440,"body":"Setup basic notification service for customer logging purposes.\r\n\r\n<img width=\"468\" alt=\"CleanShot 2023-03-28 at 14 47 51@2x\" src=\"https://user-images.githubusercontent.com/1553313/228377254-d470aa91-21ed-45f7-9fb0-0209d953c3d3.png\">\r\n","mergeCommitSha":"db4baa6a52e60d4f4b7d465836e0dab64aadd461","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5440","title":"Setup IntelliJ Notification Service","createdAt":"2023-03-28T22:05:54Z"}
{"state":"Merged","mergedAt":"2023-03-28T22:55:03Z","number":5441,"mergeCommitSha":"e2adb09e2aeaa54150c042a8ea02d39f20919c10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5441","title":"More JetBrains lint","createdAt":"2023-03-28T22:34:12Z"}
{"state":"Merged","mergedAt":"2023-03-28T23:01:33Z","number":5442,"body":"https://linear.app/unblocked/issue/UNB-1090/topic-feed-icon-for-experts-is-clipped and others","mergeCommitSha":"a55baef50375a84b1835a0dfa7d5f91480eca6ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5442","title":"Styling nits","createdAt":"2023-03-28T22:37:20Z"}
{"state":"Merged","mergedAt":"2023-03-28T22:52:33Z","number":5443,"body":"Add default to prod for env service.","mergeCommitSha":"f5b06401e21634f89f628fd33206c39c9f0c9647","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5443","title":"Fix missing default for env service","createdAt":"2023-03-28T22:51:34Z"}
{"state":"Closed","mergedAt":null,"number":5444,"mergeCommitSha":"91f8a4df5e942785d5f9028d55dbd8052577e448","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5444","title":"Test why JetBrains project isn't linting properly","createdAt":"2023-03-28T23:09:19Z"}
{"state":"Merged","mergedAt":"2023-03-30T20:47:50Z","number":5445,"body":"Adds a new property called `userDefinedDescription` which will be used to store any description that is created or updated by the user. `Topic.description` will continue to contain the generated description, and the client will show this in the UI if `userDefinedDescription` is null.\r\n\r\nThe reason for having two fields is to support the designs where generated descriptions are highlighted with an `ML` tag. If a user edits a topic description, we'll save that to `userDefinedDescription` (leaving `description` untouched) and the expectation is that the UI will not show the `ML` tag.","mergeCommitSha":"a59a358d85f63b1452a8425bbb9eb50b05a2b2d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5445","title":"Add user-defined description to topic model","createdAt":"2023-03-28T23:56:54Z"}
{"state":"Merged","mergedAt":"2023-03-29T00:09:57Z","number":5446,"body":"I was not aware that an insight can point to either a thread or a pullrequest (two different tables), and we have to handle it properly.","mergeCommitSha":"dcbe7fce7d2aae096e6810fe889583331cfee04b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5446","title":"Pull Request insights topic mapping","createdAt":"2023-03-28T23:57:15Z"}
{"state":"Merged","mergedAt":"2023-03-29T00:06:43Z","number":5447,"body":"> **Warning**\r\n> Plugin 'Unblocked' (version '1.0.832') is not compatible with the current version of the IDE, because it requires build 223.* or older but the current build is IU-231.8109.175","mergeCommitSha":"c5a9866e7734e64a6e5888866ee002ca9f91896f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5447","title":"Remove the Jetbrains plugin version upper bound","createdAt":"2023-03-29T00:04:31Z"}
{"state":"Merged","mergedAt":"2023-03-30T18:38:48Z","number":5448,"body":"https://linear.app/unblocked/issue/UNB-1101/dashboard-deleting-a-topic-goes","mergeCommitSha":"923abe1c8f275fe0eadd2242eea39522b83c806e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5448","title":"Redirect to topics view after delete","createdAt":"2023-03-29T00:20:35Z"}
{"state":"Merged","mergedAt":"2023-03-29T00:35:45Z","number":5449,"body":"This breaks CI and this sort of implicit dependency is a bad idea.\r\nOn CI, we do not want linting to format the files as those changes will not be submitted to main.","mergeCommitSha":"fc558be2dddb85560f91c303ece353641ca90773","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5449","title":"Break dependency on LintTask to FromatTask","createdAt":"2023-03-29T00:29:30Z"}
{"state":"Merged","mergedAt":"2022-03-10T22:39:58Z","number":545,"body":"Yeah super hacky but would like to get this in so that Richie and start running the demo. \r\n\r\nNext PR will fix auth try to consolidate `SourceMarkApiClient` with `UnblockedApiClient`","mergeCommitSha":"28763f7ab61e05b77f0165e008e61fed1356bcd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/545","title":"Hit API for downloading and updating SourceMarks","createdAt":"2022-03-10T22:16:10Z"}
{"state":"Merged","mergedAt":"2023-03-29T00:38:50Z","number":5450,"body":"The defualt “build” task that gradle provides calls the “check” task.\r\nOur kotlinter plugin adds a lint dependency on the “check” task so, in effect, we do not need to call lint task explicitly.\r\n","mergeCommitSha":"9aa0b84b9b8500267208bf32168d4382f1212b0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5450","title":"Cleanup github actions for jetbrains","createdAt":"2023-03-29T00:36:50Z"}
{"state":"Merged","mergedAt":"2023-03-29T00:46:54Z","number":5451,"mergeCommitSha":"cb752276585f4c999de54a971a324f3f7964cd39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5451","title":"Refactor GitHubInstallationHandler to remove more code","createdAt":"2023-03-29T00:38:42Z"}
{"state":"Merged","mergedAt":"2023-03-29T05:43:47Z","number":5452,"body":"# With both `since` and `until` constraints\r\n\r\n> **Warning**\r\n> Plugin 'Unblocked' (version '1.0.832') is not compatible with the current version of the IDE, because it requires build 223.* or older but the current build is IU-231.8109.175\r\n\r\n# With just the `since` constraint\r\nIDE seems to be treating the since constraint as an until constraint. Makes no sense.\r\n\r\n> **Warning**\r\n> Plugin 'Unblocked' (version '1.0.833') is not compatible with the current version of the IDE, because it requires build 221.* or older but the current build is IU-231.8109.175\r\n\r\n## This change\r\n\r\nRemove all constrains. Let's see what happens.\r\nhttps://plugins.jetbrains.com/docs/intellij/build-number-ranges.html","mergeCommitSha":"3c86ce93542793573c85565d1265201eb01085e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5452","title":"Remove all JetBrains ext version constraints","createdAt":"2023-03-29T05:29:08Z"}
{"state":"Merged","mergedAt":"2023-03-29T06:33:11Z","number":5453,"body":"```json\n{\n  \"level\": \"ERROR\",\n  \"message\": \"Failed to refresh team members\",\n  \"platform\": {\n    \"version\": \"cb752276585f4c999de54a971a324f3f7964cd39\",\n    \"buildNumber\": \"19947\"\n  },\n  \"environment\": \"dev\",\n  \"@timestamp\": \"2023-03-29T05:22:47.991+0000\",\n  \"service\": \"scmservice\",\n  \"thread_name\": \"DefaultDispatcher-worker-129\",\n  \"teamId\": \"2ba92041-cb22-4070-aa5c-d82d4098e7c4\",\n  \"logger_name\": \"com.nextchaptersoftware.scmservice.jobs.ScmTeamMaintenanceJob\",\n  \"stack_trace\": \"j.l.UnsupportedOperationException: Empty collection can't be reduced.\n        at c.n.m.MembershipMaintenance$createTeamMembers$2.invokeSuspend(MembershipMaintenance.kt:165)\n        at c.n.m.MembershipMaintenance$createTeamMembers$2.invoke(MembershipMaintenance.kt)\n        at c.n.m.MembershipMaintenance$createTeamMembers$2.invoke(MembershipMaintenance.kt)\n        at c.n.d.c.Database$suspendedTransaction$2$1.invokeSuspend(Database.kt:170)\n        at c.n.d.c.Database$suspendedTransaction$2$1.invoke(Database.kt)\n        at c.n.d.c.Database$suspendedTransaction$2$1.invoke(Database.kt)\n        at o.j.e.s.t.e.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:129)\n        at k.c.j.i.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\n        at k.c.DispatchedTask.run(DispatchedTask.kt:106)\n        at k.c.i.LimitedDispatcher.run(LimitedDispatcher.kt:42)\n        at k.c.s.TaskImpl.run(Tasks.kt:95)\n        at k.c.s.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\n        at k.c.s.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:750)\n        at k.c.s.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\n        at k.c.s.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)\\n\"\n}\n```","mergeCommitSha":"8c9de01ffbbca5e43e4f7f4a08b1ba6fb95ecc32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5453","title":"Debugging to figure out why this is happening in DEV","createdAt":"2023-03-29T06:29:35Z"}
{"state":"Merged","mergedAt":"2023-03-29T16:12:21Z","number":5454,"mergeCommitSha":"2dfa7527463c48c1a5ed499fbe95586f2c2125dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5454","title":"Fix lib-maintenence test build","createdAt":"2023-03-29T15:55:23Z"}
{"state":"Merged","mergedAt":"2023-03-29T16:20:55Z","number":5455,"body":"Turns out that the _default_ version is set based on the intellij.version, which is set to \"2022.1.4\".\n```xml\n<idea-version since-build=\"221.6008\" until-build=\"221.*\" />\n```\n\nSo we need to set an explicit version that is much more lenient.\n\n## Follow up\n\nWe should be running the plugin verifier task in CI to determine the correct bounds.\nhttps://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html#tasks-runpluginverifier","mergeCommitSha":"3ed0d06f965012f3575b748a93f1665a6160b6d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5455","title":"Fix jetbrains version","createdAt":"2023-03-29T16:15:08Z"}
{"state":"Merged","mergedAt":"2023-03-29T16:51:06Z","number":5456,"body":"## Motivation\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1680108066102089\r\n\r\n## Result\r\nYou'll now see this banner in Actions tab.\r\n\r\n<img width=\"1160\" alt=\"Screenshot 2023-03-29 at 09 51 47\" src=\"https://user-images.githubusercontent.com/1798345/228611419-8e45b865-cb08-40aa-90a8-c7535d12b992.png\">\r\n","mergeCommitSha":"222717ae0e3a30f94356534b5919ff9fa7dcaf6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5456","title":"Allow service build to run manually","createdAt":"2023-03-29T16:48:35Z"}
{"state":"Merged","mergedAt":"2023-03-29T23:21:06Z","number":5457,"mergeCommitSha":"e2909453cc0c7a247b3dc0b05287bf099b6081c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5457","title":"Add iOS client type","createdAt":"2023-03-29T17:03:27Z"}
{"state":"Merged","mergedAt":"2023-03-29T19:49:06Z","number":5458,"body":"…requests","mergeCommitSha":"fd71b4627ed9076d6bd3df5b7c947128ddd27ea8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5458","title":"Add ability to view all pull requests for team/repo and reindex pull requests","createdAt":"2023-03-29T19:28:10Z"}
{"state":"Merged","mergedAt":"2023-03-29T20:04:35Z","number":5459,"body":"No change in behaviour, just uses streams instead of pages.\r\n\r\nStopping criteria is now a limit of the cumulative items, rather than page-specific.","mergeCommitSha":"1f91cf499c6fbbd1b9f9f068887d054b20ee6290","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5459","title":"Use stream for pullRequestFile pagination","createdAt":"2023-03-29T19:43:02Z"}
{"state":"Merged","mergedAt":"2022-03-10T23:23:11Z","number":546,"mergeCommitSha":"ee8f9bb9ec832d753d12ae9a07d26afb1829d2a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/546","title":"Fix SourceMarkProvider auth","createdAt":"2022-03-10T23:16:22Z"}
{"state":"Closed","mergedAt":null,"number":5460,"mergeCommitSha":"3451a47d52f587281992120789800f2533e66dd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5460","title":"Add installer background image","createdAt":"2023-03-29T20:00:29Z"}
{"state":"Merged","mergedAt":"2023-03-29T20:07:46Z","number":5461,"mergeCommitSha":"a0e98c6ad5225389e87c796603330872d2582547","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5461","title":"Add more links for pull requets","createdAt":"2023-03-29T20:06:23Z"}
{"state":"Merged","mergedAt":"2023-03-29T20:12:01Z","number":5462,"mergeCommitSha":"7dd529b1bcfff0564ec0d1f7a907eb0712de7ff0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5462","title":"Add links to repos page","createdAt":"2023-03-29T20:11:42Z"}
{"state":"Merged","mergedAt":"2023-03-31T23:16:56Z","number":5463,"body":"* Set up logging for the agent (`log.*`) -- the implementation is shared with VSCode as we want the same behaviour\r\n* Set up Sentry for the agent\r\n* Set up node global error handlers.  This allows the agent to continue running when exceptions aren't caught, etc.  This is the same implementation that VSCode's extension process has.","mergeCommitSha":"09a07dfdce85b15ed771b965115cf13441c7b021","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5463","title":"Jetbrains agent logging and error handling","createdAt":"2023-03-29T20:39:31Z"}
{"state":"Merged","mergedAt":"2023-03-29T22:01:41Z","number":5464,"body":"https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/789aff37-86df-484e-a74c-2519511abf18","mergeCommitSha":"c9b42a09471a4afbb8fc6cc1e848222d3ae970e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5464","title":"Set max_line_length attribute in .editorconfig","createdAt":"2023-03-29T21:30:20Z"}
{"state":"Merged","mergedAt":"2023-03-30T23:44:08Z","number":5465,"body":"### Plan\r\n\r\n1. Replace GitHub-specific pagination with something SCM-generic (this change)\r\n2. Define `ScmRepoApi` interface (#5469)\r\n3. Replace hardcoded usage of `GitHubAppApi.V3Org` in `lib-pringestion` with `ScmRepoApi`\r\n4. Implement `ScmRepoApi` interface for Bitbucket and GitLab\r\n\r\n\r\n### Dependencies\r\n\r\n- [x] #5476\r\n- [x] #5477\r\n- [x] GitHub APIs used for PR ingest use batched-stream pagination (acdda92bfcd19cf958f3c03913fc06d1fb36ba28)\r\n- [x] PR ingest uses batched-stream-based GitHub APIs (e9e09f0ac138dc162e6aa47df362343ee0fe35fe)","mergeCommitSha":"b79f6b7429d4fa76f7c6d0c71841a1836a45baa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5465","title":"Replace persistence of GitHub page numbers in PR ingestion","createdAt":"2023-03-29T21:32:13Z"}
{"state":"Merged","mergedAt":"2023-03-29T22:46:17Z","number":5466,"body":"Will point to HEAD of the repo on their configured default branch.","mergeCommitSha":"c3cda1a2bd318210e0dc825af49fc25e51fb772e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5466","title":"Fix video file SCM HTML link","createdAt":"2023-03-29T22:35:36Z"}
{"state":"Closed","mergedAt":null,"number":5467,"mergeCommitSha":"c35853e15468a3c7340157a22ec467e1f0fa1e71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5467","title":"Was supposed to be 140","createdAt":"2023-03-29T23:48:41Z"}
{"state":"Merged","mergedAt":"2023-03-29T23:57:32Z","number":5468,"body":"Reverts NextChapterSoftware/unblocked#5464\r\n\r\nThis is fucking everything up. ","mergeCommitSha":"600bbd6529f18b3e7e2e41144fe325063bbf1891","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5468","title":"Revert \"Set max_line_length attribute in .editorconfig\"","createdAt":"2023-03-29T23:57:17Z"}
{"state":"Merged","mergedAt":"2023-03-30T00:55:33Z","number":5469,"mergeCommitSha":"a96362a3f2c07b34cbdae30b2bba4136830552ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5469","title":"Add SCM repo API interface methods","createdAt":"2023-03-30T00:31:16Z"}
{"state":"Merged","mergedAt":"2022-03-11T01:39:10Z","number":547,"body":"I'm not sure if the refresh token logic here works, but using the token from the API works.","mergeCommitSha":"edc5173dd544ce0b6e5946a128268597e12faf6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/547","title":"Use admin auth ","createdAt":"2022-03-10T23:49:14Z"}
{"state":"Merged","mergedAt":"2023-03-30T01:05:28Z","number":5470,"mergeCommitSha":"801c402e549d1bd754d8bb9d8cf91c5ccc27f9c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5470","title":"Show raw paginated pull requests response from GitHub","createdAt":"2023-03-30T00:57:23Z"}
{"state":"Merged","mergedAt":"2023-03-30T03:57:53Z","number":5471,"mergeCommitSha":"05305e78bee1622ae49a7fe5e0dcfd1022cc9d92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5471","title":"Order pull requests by created asc","createdAt":"2023-03-30T01:08:52Z"}
{"state":"Merged","mergedAt":"2023-03-30T17:14:09Z","number":5472,"body":"Goal is to keep project settings consistent:\n - linter settings\n - code formatting settings\n - JDK language level\n - modules\n\nPersonal preferences are not persisted.\n\nUses this to seed our ignore settings:\nhttps://www.toptal.com/developers/gitignore?templates=intellij","mergeCommitSha":"47d9c741a2ab7eb4632f33febf6cc7c8e829d4f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5472","title":"[RFC] Persist IDE project settings for consistency","createdAt":"2023-03-30T05:03:59Z"}
{"state":"Merged","mergedAt":"2023-03-30T07:16:43Z","number":5473,"body":"Blocked on #5465 from completing the GitHubRepoApi implementation.","mergeCommitSha":"eed19b89294f5c43d0b53d8afad01d61deb45957","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5473","title":"Populate SCM API repo factory and implementation","createdAt":"2023-03-30T06:39:37Z"}
{"state":"Merged","mergedAt":"2023-03-30T14:06:19Z","number":5474,"body":"Batched streaming returns a list of items and the next page URL, to be used\r\nby calling clients in cases where it is cost advantageous to cache the next\r\npage API in the event that the long-running stream is interrupted.\r\n\r\nTo use safely: the caller must process **all** of the items in the batch\r\nbefore persisting the next page, otherwise items will be lost if there is an\r\ninterruption mid-batch.","mergeCommitSha":"614a15b04fb375656b12a32b153c0cdcb19e3a85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5474","title":"Introduce batched streaming","createdAt":"2023-03-30T08:08:47Z"}
{"state":"Merged","mergedAt":"2023-03-30T18:27:55Z","number":5475,"body":"https://linear.app/unblocked/issue/UNB-1103/view-on-github-in-dashboard-listing-doesnt-go-to-gh","mergeCommitSha":"8e0b3677ad57a5461c17b4f86047b0f0a9b086be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5475","title":"Fix link events in summary views","createdAt":"2023-03-30T17:53:13Z"}
{"state":"Merged","mergedAt":"2023-03-30T20:39:07Z","number":5476,"mergeCommitSha":"2f1bdeb46c76d658e72134553a1fe17d10a3248c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5476","title":"DB model change to declare nullable next batch fields (next API page URLs)","createdAt":"2023-03-30T18:57:59Z"}
{"state":"Merged","mergedAt":"2023-03-30T20:40:53Z","number":5477,"body":"<img width=\"1235\" alt=\"Screenshot 2023-03-30 at 13 17 21\" src=\"https://user-images.githubusercontent.com/1798345/228954326-cebac153-5145-42a7-ad78-63cbef01de97.png\">\r\n","mergeCommitSha":"8aef2349421ee3fa13dc74087a0e67773df26acb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5477","title":"Admin: PR Ingestion page shows next batch of items","createdAt":"2023-03-30T20:16:42Z"}
{"state":"Merged","mergedAt":"2023-04-03T18:30:57Z","number":5478,"body":"## Summary\r\n\r\nPer the PR title, the biggest change is the order of installation. The complexity is in passing information back and forth from the Hub to the installer app. Since the Hub is sandboxed, it can't do anything cool like launch the installer with args or read the result. So instead we just echo the result on disk through two \"version\" files:\r\n- <pluginType>.version\r\n- <pluginType>.version.installed\r\n\r\nTo simplify, I made the installer as dumb as possible. If it finds installer files, it will install them, delete them, and mark the latest version.\r\n\r\nAll the business logic for deciding whether to perform a plugin upgrade is in the Hub, and it works as follows:\r\n\r\n1. Check to see if current version of the hub is equal to the lates version from the versions API. If so then\r\n2. Look for the <pluginType>.version.installed file. If it's >= the current hub version, so need to do anything. If not then\r\n3. Look for the existence of a new plugin file already downloaded. If one exists then inspect the <pluginType>.version file to see if it's a new plugin version. If so, then launch installer, if not then\r\n4. Download plugin and write new version to <pluginType>.version file\r\n5. Launch installer\r\n\r\nTested under all different conditions - seems to be working as expected.\r\n\r\n\r\n### First time upgrade/install\r\nThere is a bit of added complexity here. \r\n\r\nFirstly, on first time install we need to add an additional \"flag\" for \"processing complete\". That will be fairly trivial to add. In the mean time, plugin installation is protected through client feature flags.\r\n\r\nSecond, the initial upgrade will result in a double download and install. There are ways to avoid this, but it seems like more work that it's worth. Current customers won't see this at all because vscode plugin download is disabled for them. When we turn it on, they will already have the updated install code and it will just work.\r\n\r\n\r\n### Follow up\r\nThere's one more task to do after this, which is to remove the vscode plugin from the installer. We can't do this until the new onboarding flow is released, but it will be totally painless for customers when we do.","mergeCommitSha":"64067e8601b0e2351f896cc2ca05322fc479c620","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5478","title":"Install the plugins after the hub restarts","createdAt":"2023-03-30T21:25:44Z"}
{"state":"Merged","mergedAt":"2023-04-04T17:45:58Z","number":5479,"body":"Adds a `displayName` property to topic. The idea here is that `Topic.name` is assigned at creation and immutable, so that when insights are mapped to topics, that name (which should be generated) can be used as the unique key for looking up the topic. \r\n\r\n`Topic.displayName` will be for users to allow them to rename a topic without touching `Topic.name`.","mergeCommitSha":"d832ee9161babcc1744144ca6856c142ca66cb83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5479","title":"Add Topic.displayName","createdAt":"2023-03-30T23:08:11Z"}
{"state":"Merged","mergedAt":"2022-03-10T23:57:30Z","number":548,"mergeCommitSha":"cd54622f4fa85d502ce787ec8a5d7e949667c508","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/548","title":"Crash fix: populate point cache with tree-same commit points","createdAt":"2022-03-10T23:51:52Z"}
{"state":"Merged","mergedAt":"2023-04-13T16:34:38Z","number":5480,"body":"Setup create insight flow in IntelliJ\r\n<img width=\"518\" alt=\"CleanShot 2023-03-30 at 16 13 36@2x\" src=\"https://user-images.githubusercontent.com/1553313/228985150-862258ac-5739-494b-8493-cd30eb2f1200.png\">\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/228985680-b7783357-490d-49be-96e3-c6cde135e8e5.mp4\r\n\r\n\r\n\r\n","mergeCommitSha":"74e979390807497a580a665d23a6b6dedba489f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5480","title":"[UNB-1067] Setup Create Insight Flow in IntelliJ","createdAt":"2023-03-30T23:14:41Z"}