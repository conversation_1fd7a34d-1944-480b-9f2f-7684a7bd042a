{"state":"Merged","mergedAt":"2023-07-07T18:41:41Z","number":6956,"body":"* also updates the styling of the references dropdwon","mergeCommitSha":"db1f7e4322ac3a156ca9d3e02dfe00ed2a5c7322","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6956","title":"Fix dupe submission in dashboard QA input","createdAt":"2023-07-07T18:19:30Z"}
{"state":"Merged","mergedAt":"2023-07-07T19:02:04Z","number":6957,"body":"Fix merge issue where we always launch onboarding","mergeCommitSha":"69c632a4bd024b56389febec7a05cb4a66ad5b65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6957","title":"Fix bad bug from merge","createdAt":"2023-07-07T18:53:33Z"}
{"state":"Merged","mergedAt":"2023-07-07T19:37:52Z","number":6958,"mergeCommitSha":"897bbec4068fa030c9e0b1d8dafa759d7374638b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6958","title":"Display (Human) (Assistant) roles in chat history instead of team Ids, which are currently unused","createdAt":"2023-07-07T19:08:12Z"}
{"state":"Merged","mergedAt":"2023-07-07T19:43:24Z","number":6959,"body":"Also disable browser autocomplete for every input box, it's annoying.","mergeCommitSha":"b09b2fb08b4227591281100f3ab34701b5ab3fac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6959","title":"Show references in admin inference page","createdAt":"2023-07-07T19:21:13Z"}
{"state":"Merged","mergedAt":"2023-07-10T16:20:09Z","number":6960,"body":"<img width=\"769\" alt=\"CleanShot 2023-07-07 at 14 04 21@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/02b8297d-9417-4ae6-ba9f-0c69a4fee291\">\r\n\r\nUpdate Hub header to new designs.\r\n\r\nRemoved A/B Testing feature flag","mergeCommitSha":"d088523013e292655d76090d065f698a89c2b7ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6960","title":"Update Hub Insight Views","createdAt":"2023-07-07T21:04:44Z"}
{"state":"Merged","mergedAt":"2023-07-07T21:10:11Z","number":6961,"body":"```\r\nk.s.MissingFieldException: Field 'score' is required for type with serial name 'com.nextchaptersoftware.db.models.ReferenceArtifact.PullRequest', but it was missing\r\n\tat k.s.i.PluginExceptionsKt.throwMissingFieldException(PluginExceptions.kt:20)\r\n\tat c.n.d.m.ReferenceArtifact$PullRequest.<init>(ReferenceArtifact.kt:41)\r\n\tat c.n.d.m.ReferenceArtifact$PullRequest.<init>(ReferenceArtifact.kt)\r\n\tat c.n.d.m.ReferenceArtifact$PullRequest$$serializer.deserialize(ReferenceArtifact.kt:41)\r\n\tat c.n.d.m.ReferenceArtifact$PullRequest$$serializer.deserialize(ReferenceArtifact.kt:41)\r\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:86)\r\n\t... 42 common frames omitted\r\nWrapped by: k.s.MissingFieldException: Field 'score' is required for type with serial name 'com.nextchaptersoftware.db.models.ReferenceArtifact.PullRequest', but it was missing at path: $[0]\r\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:90)\r\n\tat k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\r\n\tat k.s.e.AbstractDecoder.decodeSerializableElement(AbstractDecoder.kt:70)\r\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableElement(StreamingJsonDecoder.kt:162)\r\n\tat k.s.e.CompositeDecoder$DefaultImpls.decodeSerializableElement$default(Decoding.kt:533)\r\n\tat k.s.i.CollectionLikeSerializer.readElement(CollectionSerializers.kt:80)\r\n\tat k.s.i.AbstractCollectionSerializer.readElement$default(CollectionSerializers.kt:51)\r\n\tat k.s.i.AbstractCollectionSerializer.merge(CollectionSerializers.kt:36)\r\n\tat k.s.i.AbstractCollectionSerializer.deserialize(CollectionSerializers.kt:43)\r\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\r\n\t... 33 common frames omitted\r\nWrapped by: k.s.MissingFieldException: Field 'score' is required for type with serial name 'com.nextchaptersoftware.db.models.ReferenceArtifact.PullRequest', but it was missing at path: $[0] at path: $[0]\r\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:90)\r\n\tat k.s.i.NullableSerializer.de...\r\n```","mergeCommitSha":"3db63082b9a7776214b2c05e0dec1dfeb76ddf6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6961","title":"Fix legacy data serialization issue with ReferenceArtifact","createdAt":"2023-07-07T21:04:46Z"}
{"state":"Merged","mergedAt":"2023-07-07T21:48:45Z","number":6962,"body":"Add flag for onboarding v2","mergeCommitSha":"5f08155475b4cba450c4eff70db0c7315d559a66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6962","title":"Add onboarding flow flag","createdAt":"2023-07-07T21:19:56Z"}
{"state":"Merged","mergedAt":"2023-07-07T21:47:18Z","number":6963,"mergeCommitSha":"b17161470da2b7afe2be837769ac49008d6c3af5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6963","title":"Use codebase consistently","createdAt":"2023-07-07T21:46:40Z"}
{"state":"Merged","mergedAt":"2023-07-07T21:57:13Z","number":6964,"mergeCommitSha":"3f43f0acc3e1ecf0585e48a573c0686de253a150","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6964","title":"Add a guard for deserializing reference artifacts","createdAt":"2023-07-07T21:56:19Z"}
{"state":"Merged","mergedAt":"2023-07-07T22:58:49Z","number":6966,"mergeCommitSha":"f83e027f9578fa50c9584c275b4bf93a18f34095","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6966","title":"Prompt crash fix for OpenAI","createdAt":"2023-07-07T22:27:41Z"}
{"state":"Merged","mergedAt":"2023-07-07T22:54:48Z","number":6967,"mergeCommitSha":"c29806514f14d22325c94058a00725650c3af699","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6967","title":"Better fix for message reference deserialization","createdAt":"2023-07-07T22:51:52Z"}
{"state":"Merged","mergedAt":"2023-07-10T22:04:10Z","number":6968,"mergeCommitSha":"accea6028a822776cad29efee661452ad0a7dfaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6968","title":"Add urls to SourceFileReference","createdAt":"2023-07-07T23:37:29Z"}
{"state":"Merged","mergedAt":"2023-07-08T00:38:26Z","number":6969,"body":"- at least 50th percentile score\n- and at least 0.1 score","mergeCommitSha":"763dfd0b2a1e078bb21acedb19d450ce68c948c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6969","title":"Apply minimum threshold to TF-IDF reference engine","createdAt":"2023-07-07T23:41:11Z"}
{"state":"Merged","mergedAt":"2022-03-29T03:57:04Z","number":697,"body":"This pr addresses the following:\r\n1. For redis clusters, we should be using RedisClusterClients.\r\n2. Because memory/cpu resources are currently limited for services, the boot up time is slow and the probes fail. We need to increase probe delays and up the limits of some of the services.\r\n\r\nTesting:\r\nhttps://github.com/NextChapterSoftware/unblocked/runs/5731081140?check_suite_focus=true","mergeCommitSha":"fb34b1fbaa009d0b5b63cae72c1c68f1628c0c46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/697","title":"[BUG FIX] Fix redis cluster handling and increase probe durations and initial check times","createdAt":"2022-03-29T00:20:04Z"}
{"state":"Merged","mergedAt":"2023-07-07T23:45:36Z","number":6970,"mergeCommitSha":"0ebd22dbff4e1a096f3fa1b04835ce3d1eafd14e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6970","title":"Fix search insight model cleanup","createdAt":"2023-07-07T23:44:59Z"}
{"state":"Merged","mergedAt":"2023-07-10T05:33:32Z","number":6971,"body":"This is necessary for accurate reference generation. Turns out that the relevance\nengine was considering _all_ input documents when generating references. It must\nonly consider the input documents that were included in the prompt. Easiest\nsolution is to just ensure that we never save omitted input documents.","mergeCommitSha":"11e8727608b81907c7c611616c28980892f8b6c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6971","title":"Only persist input documents with result if they were included in prompt","createdAt":"2023-07-10T04:34:40Z"}
{"state":"Merged","mergedAt":"2023-07-10T17:15:43Z","number":6975,"mergeCommitSha":"9cfa86167220943df762a512b43f16adc1a0105f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6975","title":"Show relevance engine type in admin web","createdAt":"2023-07-10T17:13:39Z"}
{"state":"Merged","mergedAt":"2023-07-10T18:06:21Z","number":6977,"mergeCommitSha":"e00a8298f8b6d507b89f46b07efe3799d805a2ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6977","title":"Update dependency","createdAt":"2023-07-10T18:06:15Z"}
{"state":"Merged","mergedAt":"2023-07-10T18:22:39Z","number":6978,"body":"I am enabling this (it was already enabled for replica region) in Dev only. I'd like to make sure the rule doesn't prematurely remove assets before enabling it in prod. \r\n\r\nFYI: The rule does two things,\r\n1- Removed objects from replica buckets if the original has been removed in primary region\r\n2- Remove older versions of the object once they are older than 7 days ","mergeCommitSha":"c4656769cc911c6ae30d9e0870a8b5f42f16daf3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6978","title":"enabled auto-removal of old versions in Dev S3","createdAt":"2023-07-10T18:18:23Z"}
{"state":"Merged","mergedAt":"2023-07-10T20:11:24Z","number":6979,"mergeCommitSha":"82a8393e96a8a5e4d28b8d2cd2401d60df00cb3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6979","title":"All the completion services were essentially doing the same thing","createdAt":"2023-07-10T19:46:24Z"}
{"state":"Merged","mergedAt":"2022-03-29T02:35:46Z","number":698,"mergeCommitSha":"827cae7144847ba469eb60e47f4acb43220c4a26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/698","title":"Fix redis health check warning","createdAt":"2022-03-29T00:21:07Z"}
{"state":"Merged","mergedAt":"2023-07-10T20:57:13Z","number":6980,"body":"Also changed `SampleQuestionsResponse` to return objects instead of strings so that we can add metadata for each question if needed.","mergeCommitSha":"3d7c53f8f3950d6d8bc50b90bfdb427eb052fda6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6980","title":"[BREAKS API ON MAIN] getSampleQuestions operation should return SampleQuestionsResponse","createdAt":"2023-07-10T19:50:55Z"}
{"state":"Merged","mergedAt":"2023-07-10T22:46:42Z","number":6981,"body":"https://www.notion.so/nextchaptersoftware/Reference-Generation-29e2b1db30994470aaebda30317290a3","mergeCommitSha":"3125b14d9a073b272fd5f6cc77e85bd0f6d3eb01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6981","title":"Embedding relevance engine","createdAt":"2023-07-10T20:19:58Z"}
{"state":"Merged","mergedAt":"2023-07-10T21:08:20Z","number":6982,"mergeCommitSha":"1eaf1e65cc2cfec6df929619a50f7ed62025fd72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6982","title":"add interactive slack bot events","createdAt":"2023-07-10T20:51:44Z"}
{"state":"Merged","mergedAt":"2023-07-13T16:39:05Z","number":6983,"body":"Add UI curves to welcome page\r\n<img width=\"796\" alt=\"CleanShot 2023-07-10 at 15 01 05@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/6274f7ea-2a80-4a0e-8033-72d226322b7e\">\r\n","mergeCommitSha":"98cf86b4c8a714825613900ad3296155f3f1dc95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6983","title":"Add bezier curves to design","createdAt":"2023-07-10T22:02:53Z"}
{"state":"Merged","mergedAt":"2023-07-10T23:43:30Z","number":6984,"body":"Poor man's streaming completion. Avoid's a complete refactor of completion by accepting a `flowCompletionAction` handler. Will tickle pusher with each received completion chunk.","mergeCommitSha":"2e05c8a93b8f7311f2f150a6893118b14365269f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6984","title":"Use flow completions for bot questions","createdAt":"2023-07-10T22:19:51Z"}
{"state":"Merged","mergedAt":"2023-07-10T22:53:50Z","number":6985,"mergeCommitSha":"3acbbafacf00078b150671e6dabd95848302921f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6985","title":"Add basic modal view implementaiton for slack","createdAt":"2023-07-10T22:37:24Z"}
{"state":"Merged","mergedAt":"2023-07-11T20:40:54Z","number":6986,"body":"Allow setting granular feedback on messages.  The behaviour is:\r\n\r\n* Giving *positive* feedback, or *removing* previous feedback you've set, applies instantly.  The feedback is sent to the API and the MessageStore overlay immediately updates the stream so the action displays instantly.\r\n* Giving *neutral* or *negative* feedback displays a tooltip to prompt for a reason.  We display the new feedback in the button state, but we only send the feedback to the service once the tooltip closes, either because the user clicked/closed the tooltip, or because they entered in feedback.\r\n\r\n<img width=\"1474\" alt=\"Screenshot 2023-07-10 at 4 18 37 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/53e06c1c-53ab-43ed-8fc9-1f25590811f9\">\r\n\r\n<img width=\"1474\" alt=\"Screenshot 2023-07-10 at 4 18 46 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/176a4913-48f4-4cc6-a398-665c186302d1\">\r\n\r\nSome implementation notes:\r\n\r\n* `FeedbackButton` is now solely used to display the feedback states, not to invite experts","mergeCommitSha":"f3514dea57d931b32bee1d3de215db01234efb27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6986","title":"Granular feedback UI","createdAt":"2023-07-10T23:25:45Z"}
{"state":"Merged","mergedAt":"2023-07-10T23:43:07Z","number":6987,"body":"Sadly, rate limiting is preventing us from using gpt4 for automation.","mergeCommitSha":"67cbb79d2c65bfb88c8a69591eb5107a3112fd26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6987","title":"Change pull request summary model gpt 35","createdAt":"2023-07-10T23:42:56Z"}
{"state":"Merged","mergedAt":"2023-07-11T00:52:22Z","number":6989,"mergeCommitSha":"3ba33f5c151afa85af9260e1267c687b5d9a58fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6989","title":"Revert content version change. Misintepreted the meaning of this property","createdAt":"2023-07-11T00:47:12Z"}
{"state":"Closed","mergedAt":null,"number":699,"body":"Reverts NextChapterSoftware/unblocked#692\r\n\r\nDidn't work: https://app.logz.io/#/goto/cef6898a29fa3787764b6ae08cc074eb?switchToAccountId=411850\r\n\r\n```\r\ni.l.c.RedisException: Master is currently unknown: [RedisMasterReplicaNode [redisURI=rediss://unblocked:********************@clustercfg.rer1l154blnqa1l2.eokkoa.usw2.cache.amazonaws.com:6379, role=REPLICA]]\r\n```\r\n\r\nWill try cluster config","mergeCommitSha":"e00bb104e10001e630cda8a360c70dfb199e10e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/699","title":"Revert \"Use elasticache master/replica client\"","createdAt":"2022-03-29T02:02:24Z"}
{"state":"Merged","mergedAt":"2023-07-11T02:36:57Z","number":6990,"mergeCommitSha":"98c54986dcd4b8706db110577edc9554ed806f93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6990","title":"Temporary until client is fixed","createdAt":"2023-07-11T02:36:30Z"}
{"state":"Merged","mergedAt":"2023-07-11T03:40:01Z","number":6991,"body":"This change effectively clusters documents by score, then only returns the highest scoring cluster as references.","mergeCommitSha":"397d2e0e6d1a95c57abc79ceb6c14c032341dab7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6991","title":"Relevance score clustering","createdAt":"2023-07-11T03:15:13Z"}
{"state":"Merged","mergedAt":"2023-07-11T20:52:36Z","number":6992,"body":"If you're in a 1-on-1 discussion with the bot, we should always suggest mentioning the bot","mergeCommitSha":"f20de1cac3a2d38490e1147c419d9f7aa1228189","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6992","title":"Don't skip bot mention after send","createdAt":"2023-07-11T03:18:20Z"}
{"state":"Merged","mergedAt":"2023-07-11T05:46:17Z","number":6993,"mergeCommitSha":"abf8481edcc37934d4fae718f1501ab5da3fca34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6993","title":"Bug was in MessageStore. This should fix it and flow should work as expected","createdAt":"2023-07-11T05:11:25Z"}
{"state":"Merged","mergedAt":"2023-07-11T18:22:31Z","number":6994,"mergeCommitSha":"b539292dd1edaba60db2bff718765167b6419a35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6994","title":"Bring Claude 2 online","createdAt":"2023-07-11T17:44:37Z"}
{"state":"Merged","mergedAt":"2023-07-11T19:46:06Z","number":6995,"mergeCommitSha":"3ecfceebddd3d803460dc5bdc55aa5d9ff95e525","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6995","title":"Quick link to semantic search for a team","createdAt":"2023-07-11T18:10:15Z"}
{"state":"Merged","mergedAt":"2023-07-11T19:58:02Z","number":6996,"body":"Introduces [HTMX](https://htmx.org) pattern for dynamic loading and HTML interactivity.","mergeCommitSha":"26de86c0b838d450311fdf257777a6e4b3b6323d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6996","title":"Fast team page load by deferred async rate limit","createdAt":"2023-07-11T19:21:36Z"}
{"state":"Merged","mergedAt":"2023-07-11T20:24:23Z","number":6997,"mergeCommitSha":"3da551272fa8ae973e310e0884bb77dd8b6c9884","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6997","title":"Render response time for examples","createdAt":"2023-07-11T19:41:04Z"}
{"state":"Merged","mergedAt":"2023-07-12T23:10:51Z","number":6998,"body":"During onboarding, the user will be presented with three questions they can ask Unblocked. These will be generated from topics, some which the user should be an expert of.\r\n\r\nTo allow a better onboarding experience, we want to \"pre-fetch\" the answers to these questions without creating threads. Only after the user clicks on one of these questions should a thread be created and the user be redirected to it in the dashboard.\r\n\r\nTo do this \"pre-fetch\" we'll emit search events for each sample question when providing the client these questions. These search events will each trigger a bot search without touching threads (only storing their results in an `MLInferenceExampleModel`).  \r\n\r\nWhen the user clicks on one of the three sample question in the UI, the client will make a get request to a new operation (`getSampleQuestionAnswer`) which is responsible for:\r\n- creating a thread for the question\r\n- emitting another search event to update this thread with the answer stored in the corresponding `MLInferenceExampleModel`\r\n- redirecting the user to the newly created thread\r\n\r\nNote each `SampleQuestion` is now returned with an `id`. This is the `id` that should be passed to `getSampleQuestionAnswer` by the client.\r\n\r\nTODO\r\n- [x] add tests for `SampleQuestionsService`\r\n- [x] add tests for `SampleQuestionsStore`\r\n- [x] add tests for `BotQuestionEventService`\r\n- [ ] refine question generation logic (probably in a follow up PR)","mergeCommitSha":"0e5dc461ad915f687b75e930b05cb01b398a6baf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6998","title":"Implement getSampleQuestions","createdAt":"2023-07-11T20:09:44Z"}
{"state":"Merged","mergedAt":"2023-07-11T22:05:52Z","number":6999,"mergeCommitSha":"0326568535e1eff34f573e7ec222d8bd5967b3dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6999","title":"Don't use up space from dropped documents during prompt compilation","createdAt":"2023-07-11T20:42:39Z"}
{"state":"Closed","mergedAt":null,"number":7,"mergeCommitSha":"44725b0ecb792c6315f1bca59071822bdd29580e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7","title":"vscode Green","createdAt":"2021-12-14T18:18:58Z"}
{"state":"Merged","mergedAt":"2022-01-18T23:35:32Z","number":70,"body":"(just breaking up a larger PR. nothing interesting here.)","mergeCommitSha":"eb74194c3521aa1f8d6c3305740d8078d4ad8c36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/70","title":"refactor tests","createdAt":"2022-01-18T23:33:49Z"}
{"state":"Merged","mergedAt":"2022-03-29T05:57:48Z","number":700,"mergeCommitSha":"cf0d26f924987372b332cc7a4e9810cc4dbef7ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/700","title":"resize pods","createdAt":"2022-03-29T05:25:00Z"}
{"state":"Merged","mergedAt":"2023-07-12T17:34:17Z","number":7000,"body":"For QA threads, we show a variant of the expert sidebar, containing proposed experts, with a link/button style.\r\n\r\n* Removed `threadExperts` up and down the message/thread view chain, as we don't actually use it anymore\r\n* Make a new component to render this sidebar (LoopInExpertsList)\r\n* Set message content to @mention when you click on the sidebar item\r\n\r\n<img width=\"1237\" alt=\"Screenshot 2023-07-11 at 2 01 32 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/c5442afe-1f14-44aa-8f75-6f511da1c075\">\r\n","mergeCommitSha":"7853d4542dc33462f0e00520be694aeab33a0bbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7000","title":"Add 'loop in expert' to dashboard sidebar","createdAt":"2023-07-11T21:03:13Z"}
{"state":"Merged","mergedAt":"2023-07-11T22:06:21Z","number":7001,"mergeCommitSha":"15be61c34bc0edf3335ce6a9c7d279f13b5ed474","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7001","title":"Inference Example page rendering fix","createdAt":"2023-07-11T21:22:03Z"}
{"state":"Merged","mergedAt":"2023-07-12T18:06:58Z","number":7002,"body":"Remove the custom invite sheet from the hub.  The `Invite Team Members...` menu option now goes to the dashboard.","mergeCommitSha":"59d58636ac464420aabb12b7bc6d381df76307c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7002","title":"Remove hub invite sheet","createdAt":"2023-07-11T21:36:32Z"}
{"state":"Merged","mergedAt":"2023-07-11T22:22:24Z","number":7003,"body":"Often the LLM response is structured into paragraphs, lists, etc.\n\nLeverage this information to generate higher fidelity relevance matching on a sentence-by-sentence basis.\n\nThen aggregate the results, reapplying filtering rules to get the most relevant references.","mergeCommitSha":"820f10fd016121d836a58f1e06d799f87e6db5da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7003","title":"Generate references by sentence","createdAt":"2023-07-11T21:53:01Z"}
{"state":"Merged","mergedAt":"2023-07-11T22:44:21Z","number":7004,"mergeCommitSha":"e9d2383356bcd95dca3c9714f6da569e19daebb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7004","title":"Add duration delta to regression test pass/fail","createdAt":"2023-07-11T22:05:51Z"}
{"state":"Merged","mergedAt":"2023-07-11T22:30:24Z","number":7005,"mergeCommitSha":"82016cebae5dcb5cd1dae790cb1b7948e5aab851","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7005","title":"Add slack bot types","createdAt":"2023-07-11T22:14:33Z"}
{"state":"Merged","mergedAt":"2023-07-11T23:23:20Z","number":7006,"mergeCommitSha":"608f75bc55cdeabe67645a9ebc7d1a7244fad7d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7006","title":"Tweak QA feedback border","createdAt":"2023-07-11T22:19:36Z"}
{"state":"Merged","mergedAt":"2023-07-12T17:02:55Z","number":7007,"body":"Oops \uD83E\uDD2A ","mergeCommitSha":"7c4b36b5d24124d80f205c648296e254da57eac8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7007","title":"Fix admin UI feedback counts","createdAt":"2023-07-11T23:20:51Z"}
{"state":"Merged","mergedAt":"2023-07-11T23:43:27Z","number":7008,"mergeCommitSha":"6c53f5d0b9987ac9b83ebc43f586156eeb24bb65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7008","title":"Fix an admin console woopsie in the regression testing page","createdAt":"2023-07-11T23:41:50Z"}
{"state":"Merged","mergedAt":"2023-07-27T21:57:36Z","number":7009,"body":"First draft of the landing page. It's not done but I'm merging at the request of @matthewjamesadam so he can continue to work on other aspects of it.","mergeCommitSha":"a10bd80ef3b133c0b323cca4f943bc4b7b0bbbac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7009","title":"Landing Page","createdAt":"2023-07-12T00:24:20Z"}
{"state":"Merged","mergedAt":"2022-03-29T20:26:27Z","number":701,"body":"Not the full implementation here, just getting the working bits added first while we work on the proto spec.","mergeCommitSha":"119f6170904e0fdeca3d5cc54df4f617e4bd62a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/701","title":"Add SourceMark agent gRPC server","createdAt":"2022-03-29T16:04:12Z"}
{"state":"Merged","mergedAt":"2023-07-12T04:27:38Z","number":7010,"mergeCommitSha":"25a51aba0af31efa30495148d9c05cd5a4e86bcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7010","title":"More slack bot changes","createdAt":"2023-07-12T03:57:23Z"}
{"state":"Merged","mergedAt":"2023-07-12T16:30:39Z","number":7011,"mergeCommitSha":"53a9a23a015662b93a3d5d976981f425ca09878d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7011","title":"Properly render test time deltas","createdAt":"2023-07-12T16:29:24Z"}
{"state":"Merged","mergedAt":"2023-07-12T16:35:02Z","number":7012,"mergeCommitSha":"cf264a3c7cc09b41703218f33978226100efae1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7012","title":"Fix body height","createdAt":"2023-07-12T16:34:15Z"}
{"state":"Merged","mergedAt":"2023-07-12T17:23:09Z","number":7013,"body":"Hitting limit of people we can render. Bump up as temp fix.","mergeCommitSha":"e02cd67207b7b06e90fcf9cd768ce698f173ac93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7013","title":"Bump people page count","createdAt":"2023-07-12T17:11:21Z"}
{"state":"Merged","mergedAt":"2023-07-12T22:50:47Z","number":7014,"body":"Setup Tutorial Tooltips specifically for discussions.\r\nCurrently does not implement overlay.\r\n\r\nTODO: Integrate with references in sidebar when implemented.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/d4bcb717-13e6-4ea0-b0d0-9ac47557deec\r\n\r\n\r\n\r\n","mergeCommitSha":"08440b8c6768b8e12e27b479a727d460c7a25e42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7014","title":"Basic Tutorial Tooltips","createdAt":"2023-07-12T18:02:32Z"}
{"state":"Merged","mergedAt":"2023-07-12T22:10:50Z","number":7015,"body":"Randomize the tile layout to add visual variance\r\n![CleanShot 2023-07-12 at 11 41 20](https://github.com/NextChapterSoftware/unblocked/assets/********/148c5313-7c18-4a9d-85ff-76ddcdfbb5c5)\r\n\r\n","mergeCommitSha":"839b7e1ba263a38bb83f88801deabec6036f1313","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7015","title":"Add tile variant styling to recommended view","createdAt":"2023-07-12T18:42:35Z"}
{"state":"Merged","mergedAt":"2023-07-12T20:25:01Z","number":7016,"body":"- Added IAM role with read-only access to CloudWatch to keda-operator service account\r\n- Modified the helm chart to support queue based triggers\r\n- Added queue based scaling config for searchservice and topicservice as test candidates\r\n- Fixed EKS readme with the new steps to correctly deploy Keda operator\r\n\r\nNext, we need to add scaling configuration for other services. I am working on figuring out suitable parameters for each service's trigger. \r\n\r\nThis change will enable auto-scaling for topic and search services in Dev only","mergeCommitSha":"75f7ed85301230f7891ad01a60cc3061ac3b81a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7016","title":"Setting up queue based scaling","createdAt":"2023-07-12T20:14:28Z"}
{"state":"Merged","mergedAt":"2023-07-12T21:02:03Z","number":7017,"mergeCommitSha":"30dfa6741a08277eea599a85b9838517de6dce74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7017","title":"More slack bot changes","createdAt":"2023-07-12T20:35:18Z"}
{"state":"Merged","mergedAt":"2023-07-12T20:46:30Z","number":7018,"mergeCommitSha":"9c1a0dfdb88594f000ccf4754a528c60fb839ce3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7018","title":"Adds Pinecone document page for debug","createdAt":"2023-07-12T20:42:37Z"}
{"state":"Merged","mergedAt":"2023-07-12T22:56:57Z","number":7019,"body":"* Set up VSCode tooltip styling\r\n* Implement ClientWorkspace local storage API for webviews -- this means `useLocalStorageState()` will work in webviews.  This allows the tooltip \"don't show again\" state to be synched, and lets us store any view state properly.\r\n* To do this, I had to implement PromiseProxy between webviews and IDE extensions -- this means we can now set up any async webview -> extension request easily.\r\n\r\n<img width=\"893\" alt=\"Screenshot 2023-07-12 at 1 55 08 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/90e67994-3822-4a7a-86d0-46e8096622aa\">\r\n","mergeCommitSha":"d89272c320aa93179d7dea3159b1e57f448f6890","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7019","title":"Add VSCode bot instruction tooltip","createdAt":"2023-07-12T20:57:18Z"}
{"state":"Merged","mergedAt":"2022-03-29T17:42:50Z","number":702,"body":"The health checks for all the services was essentially shared but duped.\r\n\r\nTo avoid any further code dupe, we are moving to a shared health checker across all services.","mergeCommitSha":"3f8b90a0c8871ec10c45cfe7bf45fa035aa58899","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/702","title":"Cleanup code dupe for service health checks","createdAt":"2022-03-29T17:21:30Z"}
{"state":"Merged","mergedAt":"2023-07-12T21:29:58Z","number":7021,"body":"This broke search service deploy in prod. ","mergeCommitSha":"c12ff36cca18a6e3e159366a3f5374d83c4b169f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7021","title":"fixing values file","createdAt":"2023-07-12T21:06:31Z"}
{"state":"Merged","mergedAt":"2023-07-17T23:25:55Z","number":7022,"mergeCommitSha":"407db90a8314c5b7e8939cbf3825042def716473","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7022","title":"Strip @Unblocked mentions when searching for documents","createdAt":"2023-07-12T21:23:27Z"}
{"state":"Merged","mergedAt":"2023-07-12T22:52:45Z","number":7023,"mergeCommitSha":"412309cb94a3e99a596ca767b9bde9eb01d6c576","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7023","title":"Change topics table row nav to edit","createdAt":"2023-07-12T22:17:48Z"}
{"state":"Merged","mergedAt":"2023-07-12T22:38:20Z","number":7024,"mergeCommitSha":"fdb9f73957a3e3b4c191132179db861273f6fe04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7024","title":"Clean up webhook events","createdAt":"2023-07-12T22:21:25Z"}
{"state":"Merged","mergedAt":"2023-07-12T23:46:39Z","number":7025,"mergeCommitSha":"c9ac5377b24f72c5aa637ca261eb9c70449501c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7025","title":"Track documents included in the prompt","createdAt":"2023-07-12T22:42:36Z"}
{"state":"Merged","mergedAt":"2023-07-12T23:17:46Z","number":7026,"mergeCommitSha":"9c4aa6f78bb859892f83bc81202e3e7e6fd03d7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7026","title":"Adjust colours","createdAt":"2023-07-12T23:01:25Z"}
{"state":"Merged","mergedAt":"2023-07-12T23:29:08Z","number":7027,"mergeCommitSha":"9f8489f1fa20a5b6d17ef4c6cc1e84b3fb5fa501","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7027","title":"Tweak reference debugging","createdAt":"2023-07-12T23:19:00Z"}
{"state":"Merged","mergedAt":"2023-07-14T19:41:41Z","number":7028,"body":"* Collapse the topic sections in the Overview tab by default\r\n* Store topic section expansion state in local storage\r\n* Render panel \"chrome\" in the loading state, to reduce UI flicker","mergeCommitSha":"5be0b640a28f03487133f0f983e861ef9fa83ae5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7028","title":"Fix some explorer UI bugs","createdAt":"2023-07-13T01:12:46Z"}
{"state":"Merged","mergedAt":"2023-07-13T12:10:23Z","number":7029,"mergeCommitSha":"870c98a3fd39d6ab20667b7346e0a88e197a3616","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7029","title":"Fix feedback messages","createdAt":"2023-07-13T02:25:29Z"}
{"state":"Merged","mergedAt":"2022-03-29T22:05:09Z","number":703,"body":"- Changed volume names in local stack docker compose file to avoid conflicts with the ones created and used for Gradle tests\r\n- Renamed/added make targets to support both full (with tests) and fast (no tests) builds\r\n- Fast builds retain db content (meant for front-end engineering)\r\n- Full builds cleans db (meant for local full stack engineering)\r\n- Added names to docker images built by compose\r\n- Updated README to reflect these new changes","mergeCommitSha":"7772e0da4eb293b3df8322a6ba0c10d25906e4c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/703","title":"Improve local development story","createdAt":"2022-03-29T17:22:10Z"}
{"state":"Merged","mergedAt":"2023-07-13T20:07:53Z","number":7030,"body":"Update hub to use sample questions from API instead of static questions.\r\n\r\nThis no longer depends on standard semantic search API.\r\nWill also send users to dashboard in \"tutorial\" mode.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f8c4621c-03c0-4f24-a366-183e6945becb\r\n\r\n","mergeCommitSha":"c57e633e56b1125bb46ddc9a11bd704e4a35ab77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7030","title":"Integrate sample questions into Hub","createdAt":"2023-07-13T16:47:30Z"}
{"state":"Merged","mergedAt":"2023-07-13T17:38:58Z","number":7031,"mergeCommitSha":"6a0f217fd6ac090f89595ad9256efab02c0bcd4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7031","title":"Include bot in mentions when creating a thread from a sample question","createdAt":"2023-07-13T17:16:54Z"}
{"state":"Merged","mergedAt":"2023-07-13T18:03:31Z","number":7032,"body":"See `TokenCalculator` which transforms 512 tokens to 3 x 512 characters.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/53976d00a5f238b183d99305af766fcbbe16c5aa/projects/libs/lib-ml/src/main/kotlin/com/nextchaptersoftware/ml/input/token/TokenCalculator.kt#L3-L10","mergeCommitSha":"338b16a6d27476d1633401186f52381a88e03e43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7032","title":"Update instructor max token size to 512","createdAt":"2023-07-13T17:22:09Z"}
{"state":"Merged","mergedAt":"2023-07-13T17:49:15Z","number":7033,"mergeCommitSha":"53976d00a5f238b183d99305af766fcbbe16c5aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7033","title":"Enable references for insider teams in prod","createdAt":"2023-07-13T17:34:09Z"}
{"state":"Merged","mergedAt":"2023-07-13T18:05:23Z","number":7035,"mergeCommitSha":"ca9c5caf2df6dbe4837d8d063e5b0b1dbd85297a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7035","title":"Get or create bot member before @-mentioning","createdAt":"2023-07-13T17:48:34Z"}
{"state":"Merged","mergedAt":"2023-07-13T18:38:03Z","number":7036,"mergeCommitSha":"ae2d90ea3f2345342566290a13377ee231141d22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7036","title":"Threads created from sample questions should be public","createdAt":"2023-07-13T18:22:59Z"}
{"state":"Merged","mergedAt":"2023-07-13T18:49:41Z","number":7037,"mergeCommitSha":"2f7c73850b1e9516ca90ef7c7f34f547400481dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7037","title":"Implement LLM relevance engine","createdAt":"2023-07-13T18:31:23Z"}
{"state":"Merged","mergedAt":"2023-07-13T19:19:53Z","number":7038,"mergeCommitSha":"36d07cfdbc3b7996b85ec16b73f0eeb64374732b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7038","title":"Slack auto answer","createdAt":"2023-07-13T18:52:57Z"}
{"state":"Merged","mergedAt":"2023-07-14T06:41:21Z","number":7039,"body":"- PR sync upserts DB PRs that are modified since last sync.\r\n\r\n- PR handler upserts DB PRs when necessary.","mergeCommitSha":"39aee2708a95188937d6c2c7b9b9d5d77fef9ad6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7039","title":"Fix PR synchronization","createdAt":"2023-07-13T19:06:24Z"}
{"state":"Merged","mergedAt":"2022-03-29T19:27:43Z","number":704,"body":"- Changed prod node size to allow for more pods\r\n- Increased  pod replica counts so we could see multi-pod in action\r\n- Modified github actions workflows to enable prod deploys\r\n\r\nNOTE: I have already dropped prod DB so services would hopefully come up clean\r\n\r\n ","mergeCommitSha":"d0e9c13ba8893e5312c9fd48c304173c12737e5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/704","title":"Deploy to prod again!","createdAt":"2022-03-29T18:13:24Z"}
{"state":"Merged","mergedAt":"2023-07-13T19:20:41Z","number":7040,"mergeCommitSha":"348be0c77bf1f1e53080aaf7c2a4f2623d77a769","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7040","title":"Fix small reference resolution bug","createdAt":"2023-07-13T19:20:12Z"}
{"state":"Merged","mergedAt":"2023-07-13T23:03:48Z","number":7041,"body":"New confetti using library since the generated scss was huge. like .5mb\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/9b16e4e9-73f2-4ae9-bf7d-d855aa604334\r\n\r\n","mergeCommitSha":"d249fe1c0bf536714314d5ea7b90d50d41d15588","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7041","title":"New confetti","createdAt":"2023-07-13T20:05:44Z"}
{"state":"Merged","mergedAt":"2023-07-13T20:43:20Z","number":7042,"body":"To allow re-run the onboarding flow and and regenerate sample questions as long as you give it 1 minute in between tries (otherwise it’ll return the previously generated questions)","mergeCommitSha":"8dd3d5f1144aa6fbb4cbac0b641e1538d5736a3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7042","title":"Regenerate sample questions if previously created ones age-out","createdAt":"2023-07-13T20:07:20Z"}
{"state":"Merged","mergedAt":"2023-07-13T20:47:46Z","number":7043,"body":"This is the \"lazy\" approach. If this doesn't work, then I will try to get it to output something in structured json that includes the score for each document.","mergeCommitSha":"c0f0bc0d4ebb2b8c5af61b1f35679bb713df398e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7043","title":"LLM relevance engine prompt tweaks based on first round of testing","createdAt":"2023-07-13T20:37:14Z"}
{"state":"Merged","mergedAt":"2023-07-18T20:32:13Z","number":7044,"body":"Include an enum to go along with `ThreadRank.reason` to allow the client to group recommended threads.","mergeCommitSha":"3f15883b0c7a98e8528148006762c43f0b09218b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7044","title":"Include an enum for the reason on ThreadRank","createdAt":"2023-07-13T20:51:32Z"}
{"state":"Merged","mergedAt":"2023-07-13T21:46:24Z","number":7045,"mergeCommitSha":"2f39ba026aa8e70d842b6995bee7ffa2e1382543","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7045","title":"Control slack auto answer","createdAt":"2023-07-13T21:09:18Z"}
{"state":"Merged","mergedAt":"2023-07-17T23:27:02Z","number":7046,"body":"Clients need a way to edit description/expertise as part of a user's settings, per designs:\r\n<img width=\"628\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/811c9de3-b36a-420b-815c-7f3f277fa1ff\">\r\n","mergeCommitSha":"a534a05295bafb53de279aaf886c8fba5b99a59f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7046","title":"Update team member description/expertise","createdAt":"2023-07-13T21:50:35Z"}
{"state":"Merged","mergedAt":"2023-07-13T22:24:24Z","number":7047,"body":"Filter PDFs as these are always binary.\n\nCatch pinecone vector upsert exceptions.","mergeCommitSha":"ea9d067cb1a70da8c567d42726088ed2a197e448","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7047","title":"Fix source ingestion","createdAt":"2023-07-13T22:21:42Z"}
{"state":"Merged","mergedAt":"2023-07-13T23:20:04Z","number":7048,"mergeCommitSha":"0d19c2e92e8cc432cb29dc945b35078bb02fd256","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7048","title":"Fix question classification","createdAt":"2023-07-13T23:05:03Z"}
{"state":"Merged","mergedAt":"2023-07-13T23:42:15Z","number":7049,"body":"Strange issue with Tooltip Options.\r\n\r\n`crossAxis === undefined != crossAxis not set`\r\n\r\n<img width=\"1045\" alt=\"CleanShot 2023-07-13 at 16 21 00@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/c6d1a06d-6fe3-47c2-9a9f-31d5d80bc1df\">\r\n","mergeCommitSha":"e018973ffd0d1d16c0abf5f74c820e73f4e6e967","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7049","title":"Fix tooltip","createdAt":"2023-07-13T23:22:03Z"}
{"state":"Merged","mergedAt":"2022-03-29T19:38:59Z","number":705,"body":"This pr addresses the following:\r\n1. We now have the ability to specify how we want to log per environment.\r\n2. We add a local logback config that does not log to logz.io\r\n3. We add a local logback-test config that does not log to logz.io.\r\n\r\nTESTING:\r\n1. Validated that logback-local was being loaded.\r\n\r\n12:31:34,359 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-local.xml] at [file:/Users/<USER>/chapter2/unblocked/projects/config/build/resources/main/logback-local.xml]\r\n\r\n2. Validated that for tests logback-test was being loaded.\r\n\r\n12:33:06,151 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [jar:file:/Users/<USER>/chapter2/unblocked/projects/config/build/libs/config-test-1.0.0.jar!/logback-test.xml]\r\n","mergeCommitSha":"4435b95cd5ea144a30a08656f664a12787f74adb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/705","title":"Add ability to have per-environment log configuration","createdAt":"2022-03-29T19:29:00Z"}
{"state":"Merged","mergedAt":"2023-07-14T17:41:11Z","number":7050,"body":"This will allow us to reuse histogram/bert topics by changing the stable name. Will add a \"new topic\" with this next.","mergeCommitSha":"ce5aa4a3e3b1654235aa9eb25e4bdc8c16b416a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7050","title":"Add the ability to change the stable name","createdAt":"2023-07-13T23:49:06Z"}
{"state":"Merged","mergedAt":"2023-07-14T00:21:10Z","number":7051,"mergeCommitSha":"9c5176caf67b7cb9563fa82a87f768cf764df576","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7051","title":"modified the powerml storage to 1TB","createdAt":"2023-07-14T00:20:58Z"}
{"state":"Merged","mergedAt":"2023-07-14T18:47:43Z","number":7052,"body":"Add `My Profile` and `Connected Accounts` routes to the personal settings UI\r\n<img width=\"1490\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/40c2b78d-3e13-4da7-9e4f-e8c53af57143\">\r\n<img width=\"1485\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/1c828ff0-cd7b-4cba-9707-37d64c39126e\">\r\n\r\n* Note: The personal profile page is hidden from the navigator for now because it's awaiting an [API](https://github.com/NextChapterSoftware/unblocked/pull/7046) to edit the fields","mergeCommitSha":"c39094096e2f2887a72f6a4845fe4de11fb37c8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7052","title":"Add to personal settings","createdAt":"2023-07-14T00:30:54Z"}
{"state":"Merged","mergedAt":"2023-07-14T02:09:05Z","number":7053,"mergeCommitSha":"3dcccc783c5526d7c85d0b4ca919d4dd0090b723","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7053","title":"Show message refrences","createdAt":"2023-07-14T01:12:41Z"}
{"state":"Merged","mergedAt":"2023-07-14T05:43:37Z","number":7054,"mergeCommitSha":"f21d797f05bd02661e8ad06361e9e44d213c6d20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7054","title":"Add reference resolver type to documents","createdAt":"2023-07-14T04:16:26Z"}
{"state":"Merged","mergedAt":"2023-07-14T07:35:02Z","number":7055,"mergeCommitSha":"0f042861ca96d7826b0af203022c995f580d8d0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7055","title":"Group references by RelevanceEngine type","createdAt":"2023-07-14T05:40:17Z"}
{"state":"Merged","mergedAt":"2023-07-14T17:32:36Z","number":7056,"body":"One nice consequence of this change is that we now generate dry-run references for all teams in PROD.","mergeCommitSha":"9b1986ac880705123033d510eff0e4315b35d55b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7056","title":"Trigger multiple resolvers in parallel (with a dryRun flag for all but the chosen engine)","createdAt":"2023-07-14T06:23:01Z"}
{"state":"Merged","mergedAt":"2023-07-14T19:35:43Z","number":7057,"mergeCommitSha":"ae67af26a93c9abb7dd1fa170e4ddcc9bf299d62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7057","title":"Extract references in-prompt","createdAt":"2023-07-14T06:58:32Z"}
{"state":"Merged","mergedAt":"2023-07-14T16:58:07Z","number":7058,"mergeCommitSha":"d8bb3ccd61977a9de0f91209971e1f5bc729e0d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7058","title":"Add logging to createMissingSourcePoints","createdAt":"2023-07-14T16:25:43Z"}
{"state":"Merged","mergedAt":"2023-07-14T17:38:42Z","number":7059,"body":"We're looking for a repoId but it's not in the path. Anyways, it'll be in the pull request object we get from the database.","mergeCommitSha":"29e1584b13dd5072bef1cf616f7c75707f7cbd3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7059","title":"[Admin Console] Fix reingestForPullRequest","createdAt":"2023-07-14T17:22:32Z"}
{"state":"Merged","mergedAt":"2022-03-29T20:18:19Z","number":706,"body":"- Modified all instances of `/apm/SERVICE_NAME` to `/api/health/SERVICE_NAME`. This is because Cloudfront would return an error when asking for `/apm`. We only have 3 CF origins namely `/` for landing page, `/dashboard` for static dashboard site and `/api` for all traffic that needs to be forwarded to ALB\r\n- Reflected the change above in ALB configuration","mergeCommitSha":"17063c732790619af338a53be2ff9cb43c80acac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/706","title":"Rename APM health path and moved it under /api","createdAt":"2022-03-29T20:07:10Z"}
{"state":"Merged","mergedAt":"2023-07-14T20:42:29Z","number":7060,"body":"Add tooltip that appears the first time one launches the hub after onboarding.\r\n\r\nHad to try replicating the existing notifications UI. Don't think it's 100% there so may continue tweaking... Main issues with border / shadow.\r\n\r\n<img width=\"669\" alt=\"CleanShot 2023-07-14 at 09 47 29@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/d901ce35-31bd-4be2-a809-b31eabade949\">\r\n","mergeCommitSha":"4b8045f95171320496b75eb4943affa32c92dedc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7060","title":"Specific queries helper tooltip UI","createdAt":"2023-07-14T17:41:04Z"}
{"state":"Merged","mergedAt":"2023-07-14T18:24:19Z","number":7061,"mergeCommitSha":"e66dad40f36bda2a2fe70ac1d21f4368e47d1a90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7061","title":"Fix document formatting","createdAt":"2023-07-14T18:08:59Z"}
{"state":"Merged","mergedAt":"2023-07-14T20:16:31Z","number":7062,"mergeCommitSha":"aefcb650067330c7b9369651b96a0d13da482e29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7062","title":"Update bot message if it exists in resolve(SearchEvent.ResolvePrefetchBotQuestion)","createdAt":"2023-07-14T18:15:51Z"}
{"state":"Merged","mergedAt":"2023-07-14T19:02:05Z","number":7063,"body":"createMissingSourcePoints will bail if either is null","mergeCommitSha":"c511e06046b26e91df7a32ddeaab7940b2e036ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7063","title":"Add logging to check that there is a file or file sha","createdAt":"2023-07-14T18:39:37Z"}
{"state":"Merged","mergedAt":"2023-07-14T21:32:12Z","number":7064,"body":"This pr was heavily influenced by the ruminations of one Richard bresnan.\r\n\r\nNot much different than what we talked about...\r\n\r\nValidated this worked on local stack:\r\n<img width=\"1705\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/3806658/4a89880e-9c79-4c0e-9277-5ce945a78205\">\r\n","mergeCommitSha":"b1b98e234c1214f2f02e20958a877191293eff43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7064","title":"Richie bot association","createdAt":"2023-07-14T20:15:05Z"}
{"state":"Closed","mergedAt":null,"number":7065,"mergeCommitSha":"5decfafbec4f8bd3edaf178cff9fe39e0066054b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7065","title":"Add debug logging for references","createdAt":"2023-07-14T20:32:42Z"}
{"state":"Merged","mergedAt":"2023-07-17T22:18:03Z","number":7066,"body":"When looping in an expert, we were replacing the entire input. We should be appending user instead.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/55ba425f-6976-41c9-a2ac-b0950026a7d3\r\n\r\n\r\nhttps://chapter2global.slack.com/archives/C02US6PHTHR/p1689360514176939","mergeCommitSha":"320e6dc203f752c0cd4c6f8cfc2e0f8d8b4c8465","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7066","title":"Append mention on same line","createdAt":"2023-07-14T20:36:37Z"}
{"state":"Merged","mergedAt":"2023-07-14T21:02:27Z","number":7067,"mergeCommitSha":"04d98748a06bc1c85e11d5911c80eafcfc04a3f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7067","title":"Fix ref bugs","createdAt":"2023-07-14T20:47:32Z"}
{"state":"Merged","mergedAt":"2023-07-14T22:01:12Z","number":7069,"body":"Latest fix in this series:\r\n- https://github.com/NextChapterSoftware/unblocked/pull/7063\r\n- https://github.com/NextChapterSoftware/unblocked/pull/7059\r\n- https://github.com/NextChapterSoftware/unblocked/pull/7058\r\n- https://github.com/NextChapterSoftware/unblocked/pull/7039","mergeCommitSha":"64afc864f35abd24839d6d695b536732546aa51f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7069","title":"Incremental PR ingestion should upsert threads leniently for Bitbucket","createdAt":"2023-07-14T21:40:26Z"}
{"state":"Merged","mergedAt":"2022-03-29T20:49:33Z","number":707,"mergeCommitSha":"d9a3fcba57b8c4f3e4a25b6198b3a2f39ab6b0bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/707","title":"Cleanup config","createdAt":"2022-03-29T20:38:50Z"}
{"state":"Merged","mergedAt":"2023-07-17T20:58:02Z","number":7070,"body":"* Group file entries with duplicate file names together into a single list entry, and when you click on that entry, display a modal UI showing all the files with that name\r\n* Add tooltips\r\n\r\n<img width=\"1565\" alt=\"Screenshot 2023-07-14 at 3 34 25 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/cd2547b0-1343-471e-b736-b6c8abc50b00\">\r\n<img width=\"1565\" alt=\"Screenshot 2023-07-14 at 3 34 33 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/1796c254-5425-4318-9d7d-d09be726cc1a\">\r\n\r\nDetails:\r\n\r\n* This required a pretty significant incision into `ModalDialog`'s layout.  Specifically, `ModalDialog` used to scroll on its entire body, instead of only on the content, which I don't think makes sense -- you never want to scroll header and footer content.  However, the blast radius of this is quite high.  Do we have any modals where the entire modal content (header, body, and footer) should scroll together?  If so, this might break it.\r\n* Added a `compact` variant to `ModalDialog`, which reduces padding.\r\n* Adding a generic `Row` component, as per the design library.  This is a simple row with title/secondary text, an icon, and right-hand-side icon","mergeCommitSha":"f895f5dc70c544d9c33390d53ba0cbfad40ba48e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7070","title":"Update QA references UI","createdAt":"2023-07-14T22:38:22Z"}
{"state":"Merged","mergedAt":"2023-07-14T23:27:30Z","number":7071,"body":"It's less ambiguous to say:\r\n```sh\r\ngit rev-parse commit --\r\n```\r\n\r\nThan what we were doing previously:\r\n```sh\r\ngit rev-parse commit\r\n```\r\n\r\nResolves this failure:\r\n```json\r\n{\r\n  \"client.error\": \"{\\\"name\\\":\\\"RunnerError\\\",\\\"stdout\\\":\\\"50c5e6f448bd\\\\n\\\",\\\"stderr\\\":\\\"fatal: ambiguous argument '50c5e6f448bd': unknown revision or path not in the working tree.\\\\nUse '--' to separate paths from revisions, like this:\\\\n'git <command> [<revision>...] -- [<file>...]'\\\\n\\\"}\",\r\n  \"client.environment\": \"local\",\r\n  \"logzio-signature\": -1014378248,\r\n  \"http.req.query\": \"\",\r\n  \"client.cause\": \"RunnerError: Command failed: /usr/bin/git rev-parse 50c5e6f448bd\\nstdout:\\n50c5e6f448bd\\n\\nstderr:\\nfatal: ambiguous argument '50c5e6f448bd': unknown revision or path not in the working tree.\\nUse '--' to separate paths from revisions, like this:\\n'git <command> [<revision>...] -- [<file>...]'\\n\",\r\n  \"http.req.header.User-Agent\": \"node-fetch\",\r\n  \"client.pid\": 13295,\r\n  \"type\": \"java\",\r\n  \"platform\": {\r\n    \"version\": \"64afc864f35abd24839d6d695b536732546aa51f\",\r\n    \"buildNumber\": \"25553\"\r\n  },\r\n  \"client.type\": \"nodejs\",\r\n  \"client.repoId\": \"6399820c-d768-4487-b1c7-3acafbebb9c2\",\r\n  \"LogSize\": 2355,\r\n  \"requestId\": \"dc1c369ef2a0c7f3\",\r\n  \"http.req.method\": \"PUT\",\r\n  \"_logzio_insights\": [\r\n    \"665fdaa0b768be40fa1396db795af45d1672e9b5\"\r\n  ],\r\n  \"logger_name\": \"com.nextchaptersoftware.telemetryservice.api.LogsApiDelegateImpl\",\r\n  \"client.level\": \"error\",\r\n  \"level\": \"ERROR\",\r\n  \"client.target\": \"6229e27c6d30c047d12c4581435f6677cef34b28\",\r\n  \"hostInfo\": {\r\n    \"hostname\": \"telemetryservice-859479b5dd-8rcs2\",\r\n    \"user\": \"?\"\r\n  },\r\n  \"client.timestamp\": \"2023-07-14T22:55:14.961Z\",\r\n  \"http.req.path\": \"/api/logs\",\r\n  \"message\": \"recalculateForCommit: failed mark recalculation\",\r\n  \"client.context\": \"SourceMarkScheduler\",\r\n  \"tags\": [\r\n    \"_logz_http_bulk_json_8070\"\r\n  ],\r\n  \"http.req.route\": \"/api/logs\",\r\n  \"client.message\": \"recalculateForCommit: failed mark recalculation\",\r\n  \"environment\": \"prod\",\r\n  \"@timestamp\": \"2023-07-14T22:55:16.666+0000\",\r\n  \"service\": \"telemetryservice\",\r\n  \"identityId\": \"dc0257f7-de7d-4478-adb5-93f9c9b444c9\",\r\n  \"isInsider\": \"false\",\r\n  \"thread_name\": \"eventLoopGroupProxy-4-2\",\r\n  \"client.repoRootPath\": \"/Users/<USER>/src/cribl\",\r\n  \"client.teamId\": \"e0c510e2-6e19-4947-a19a-98f45478d171\",\r\n  \"client.process\": \"extension\",\r\n  \"client.service\": \"vscode\",\r\n  \"client.mark\": \"{\\\"id\\\":\\\"8deaec0d-b276-4534-97c8-8933b10f2020\\\",\\\"threadId\\\":\\\"2b6a04bd-fd53-4a0a-9bee-1a141ce2c231\\\",\\\"originalFilePath\\\":\\\"src/sluice/js/search/input/SearchIn.ts\\\",\\\"originalSnippetEncoded\\\":\\\"H4sIAAAAAAAA/6tWSsssKi5RsjI2NtZRysnMSy1WsopWUoCD+PjkjMS8vNQcK4WkotTE7NQiTxcdJR0kFSmJJYnFqSWognp6evlFmemZeYk5wfmlRcmpvqkliSCVGMpygRL2epl5yTmlKameeUGpxaU5JcU6SrG1ANYpa/qcAAAA\\\"}\"\r\n}\r\n```","mergeCommitSha":"d2b7ff4b257afaa311c39eabd5c07eac3b080014","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7071","title":"Swallow hash lookup errors when API vends a short hash","createdAt":"2023-07-14T23:17:05Z"}
{"state":"Merged","mergedAt":"2023-07-17T19:12:13Z","number":7072,"body":"When within a form, don't show the chin message and don't submit on enter. Right now, this condition only applies to the CreateInsight form and the CreateWalkthrough form.\r\n\r\nI think there's room for discussion about whether we should fully align the behaviours (ie always submit on enter/never submit on enter) but I think this logic makes sense until that discussion happens. ","mergeCommitSha":"2f847c1f82f2a39151e9a37e38fe8bf123c9a52e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7072","title":"Don't submit on enter within forms","createdAt":"2023-07-14T23:50:44Z"}
{"state":"Merged","mergedAt":"2023-07-15T17:16:41Z","number":7073,"mergeCommitSha":"6691424c0be44787221b3227cf6797a9cad9cf26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7073","title":"Fix hardcoded ids","createdAt":"2023-07-15T00:04:36Z"}
{"state":"Merged","mergedAt":"2023-07-15T18:52:51Z","number":7074,"mergeCommitSha":"50b137313d6e37067776bb147f41af88e612a8b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7074","title":"Index stuff","createdAt":"2023-07-15T18:24:28Z"}
{"state":"Merged","mergedAt":"2023-07-15T19:23:38Z","number":7075,"mergeCommitSha":"5fa99afb3ec9b6d778b31bbacbd0dc5ca76d22e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7075","title":"Add cache","createdAt":"2023-07-15T18:57:41Z"}
{"state":"Merged","mergedAt":"2023-07-15T22:29:49Z","number":7076,"mergeCommitSha":"9b96f9da6438ec290170962a492e4a6b4fabd912","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7076","title":"Another optimization related to bot member id","createdAt":"2023-07-15T22:12:19Z"}
{"state":"Merged","mergedAt":"2023-07-17T17:18:10Z","number":7077,"mergeCommitSha":"5450cad364f088129b6dde2850db3a1ac9b89e8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7077","title":"INcrease test paralllelization","createdAt":"2023-07-15T22:50:50Z"}
{"state":"Closed","mergedAt":null,"number":7078,"body":"@mahdi-torabi : could you help me/us be able to run this script from the dev tier (api-service specifically)?\r\n\r\nWant to wire it up to the admin console, @rasharab willing. \r\n\r\nWe are going to want to be able to call https://runtime.sagemaker.us-west-2.amazonaws.com/endpoints/hf-endpoint-mosaicml--mpt-7b-chat/invocations from the dev tier (api-service specifically), too.","mergeCommitSha":"fd69267367ecdf8e6816ad0d00b21fc8fec6243a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7078","title":"call unblocked-fine-tuned-model on http://powerml.secops.getunblocked…","createdAt":"2023-07-15T23:26:42Z"}
{"state":"Merged","mergedAt":"2023-07-16T21:12:18Z","number":7079,"mergeCommitSha":"7388c2e0bd578a2890e8bd9cb5ef940af07c751f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7079","title":"open ports 8000 and 80 on powerml machine","createdAt":"2023-07-16T21:11:26Z"}
{"state":"Merged","mergedAt":"2022-03-29T20:57:07Z","number":708,"mergeCommitSha":"2b5c4a3a5d7a1b43cc18ecaa4b5aa5237edc0aee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/708","title":"Get port from command line","createdAt":"2022-03-29T20:45:11Z"}
{"state":"Merged","mergedAt":"2023-07-17T04:32:22Z","number":7080,"mergeCommitSha":"4e79ced61bac89829647b14182943424f267c3f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7080","title":"Remove repo metrics that slow down repo page load","createdAt":"2023-07-17T04:27:36Z"}
{"state":"Merged","mergedAt":"2023-07-19T17:47:44Z","number":7081,"body":"This PR adds the new IsTopicExpert enum and adds an approach to generating the rank for these recommendations. This is related to https://github.com/NextChapterSoftware/unblocked/pull/7044 so there is some overlap in the code.\r\n\r\nThe core of the ranking approach here is to sum the activity count of the topics for a thread where a user is an expert. So if a thread is related to `TopicA`, `TopicB`, and `TopicC`, and an member has anActivity count of 15 for `TopicA` and 10 for `TopicC` then a recommendation generated for this member will have rank `25` for reason `RecommendationReason.IsTopicExpert`. If a member is not an expert of any of the topics for a thread, no recommendation will be generated. ","mergeCommitSha":"1c297b73cd7db1c913d95a25c9e2ae28306aa29c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7081","title":"Add IsTopicExpert ranking","createdAt":"2023-07-17T16:28:50Z"}
{"state":"Merged","mergedAt":"2023-07-17T17:59:58Z","number":7082,"body":"We need to audit this boolean usage...","mergeCommitSha":"f1afd05f2d7002ec0fa3614031b4169975685382","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7082","title":"Temporary fix for is slack bot","createdAt":"2023-07-17T17:58:28Z"}
{"state":"Merged","mergedAt":"2023-07-17T19:42:55Z","number":7083,"body":"Adds a column to `TopicExpertModel` to let us keep track of the expert score of the team member, where the score is calculated from the number of messages, pull requests, and pull request reviews that member has authored for that topic.\r\n\r\nWill be used for https://github.com/NextChapterSoftware/unblocked/pull/7081 in calculating rank for recommendation.\r\n\r\nTODO:\r\n- [ ] Add a nightly job that recalculates these scores","mergeCommitSha":"917a105b1a98b35c10837190eb1801cc5fcc9621","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7083","title":"Add TopicExpertModel.activityCount","createdAt":"2023-07-17T18:26:28Z"}
{"state":"Merged","mergedAt":"2023-07-18T00:35:47Z","number":7084,"mergeCommitSha":"a52fd88327887de813ef1646571571627f223385","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7084","title":"More engagement metrics","createdAt":"2023-07-17T18:43:30Z"}
{"state":"Merged","mergedAt":"2023-07-17T20:23:16Z","number":7085,"body":"When the user creates a QA thread, in the hub or dashboard, only ask the bot where `isCurrentMember=true`.  This will avoid asking the slack variant of the bot.","mergeCommitSha":"95f20dbafa023bad1bc473325c471d20ca1d0719","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7085","title":"QA thread should only ask the current user bot","createdAt":"2023-07-17T19:58:05Z"}
{"state":"Merged","mergedAt":"2023-07-17T20:36:10Z","number":7086,"mergeCommitSha":"aaa84d07a6f5d304eb03f8f5c2aedfc1128b698b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7086","title":"Fix local refinery as they are forcing people to v2 configuration files","createdAt":"2023-07-17T20:14:17Z"}
{"state":"Merged","mergedAt":"2023-07-17T21:35:39Z","number":7087,"body":"- Minimum scale for all services except SlackService, VideoService, NotificatioinService and SearchService has been set to 1\r\n- SlackService, VideoService, NotificatioinService and SearchService was kept at the current scale which is 2\r\n- All services are allowed to burst up to 6 instances depending on queue length \r\n- Notification service needs a bit more work to test the multi-queue setup. For now I just added the main queue. \r\n","mergeCommitSha":"1686d807dcd7bd7bc1693be882e2e2166dd2b7e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7087","title":"enabling queue based scaler for background service pods","createdAt":"2023-07-17T21:16:48Z"}
{"state":"Merged","mergedAt":"2023-07-19T22:42:10Z","number":7088,"body":"Update video on onboarding-ask page\r\n\r\n<img width=\"794\" alt=\"CleanShot 2023-07-17 at 14 17 23@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/fe92682b-4334-41ed-81f8-13a0e4188919\">\r\n\r\n","mergeCommitSha":"7136df831d4885acc3413aa0133e29ef525d1c04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7088","title":"Update onboarding ask video","createdAt":"2023-07-17T21:17:14Z"}
{"state":"Merged","mergedAt":"2023-07-18T17:28:05Z","number":7089,"mergeCommitSha":"dad52f1c4e0d2c14dbfe23f834c8ca6687bc6241","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7089","title":"Calculate expert activity count nightly","createdAt":"2023-07-17T21:22:38Z"}
{"state":"Merged","mergedAt":"2022-03-29T21:15:26Z","number":709,"mergeCommitSha":"e868d51c74f8a480b2499665266adb1859a37f3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/709","title":"prod has more pods and needs more time to rollover","createdAt":"2022-03-29T21:03:52Z"}
{"state":"Merged","mergedAt":"2023-07-18T22:34:14Z","number":7090,"body":"`Message.references` has an unsual API that is not consistent with the rest of our message/thread properties.  For other properties (`ThreadInfo.pr`, `ThreadInfo.slack`, `Message.feedback`, etc), null properties mean \"this value is not relevant for this message/thread\", whereas for `Message.references`, null means \"loading\".  I do think we should change this to be consistent, but for now we will fix a bug:\r\n\r\nIf a user replies after the bot, we will display the references for that message (which will always be empty), instead of the last bot message.","mergeCommitSha":"5a84e4d40ec232e1a7d6c84120e65bc089387c13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7090","title":"Use last bot reply when displaying references in right column","createdAt":"2023-07-17T22:05:36Z"}
{"state":"Merged","mergedAt":"2023-07-17T22:09:23Z","number":7091,"body":"https://github.com/honeycombio/helm-charts/tree/main/charts/refinery\r\n\r\nV2 of refinery requires a significant amount of changes in configuration.\r\nI used their conversion tool to do this:\r\nhttps://docs.honeycomb.io/manage-data-volume/refinery/migration/\r\n","mergeCommitSha":"411d74c84d4510d98d3187701782f953f44b31b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7091","title":"Fix honeycomb refinery deployment","createdAt":"2023-07-17T22:07:42Z"}
{"state":"Merged","mergedAt":"2023-07-17T23:25:37Z","number":7092,"body":"Plumb from source code job creation to the source code ingestion runner.","mergeCommitSha":"71bdd2788e163a910f12d921adba9d152b1f950d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7092","title":"Encode repo full name in embedding space","createdAt":"2023-07-17T22:47:38Z"}
{"state":"Merged","mergedAt":"2023-07-18T00:08:52Z","number":7093,"body":"Hopefully this extra metadata will be used by the LLM inference to answer questions like these:\n\n- https://chapter2global.slack.com/archives/C05D7H25PV5/p1689615508120939\n- https://chapter2global.slack.com/archives/C05AJRKMQF3/p1689109594306629\n- https://chapter2global.slack.com/archives/C05D7H25PV5/p1689109166342169","mergeCommitSha":"07ff8b5e541aeba6aa70b279150ffbadcf76592d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7093","title":"Ability to add nullable repo full name to prompt template","createdAt":"2023-07-17T22:47:41Z"}
{"state":"Merged","mergedAt":"2023-07-18T00:26:00Z","number":7094,"mergeCommitSha":"f97ce923b78fc8d5213a3fc7df0cea9ea7718495","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7094","title":"Pull references from tail of response, in structured format","createdAt":"2023-07-17T23:04:46Z"}
{"state":"Merged","mergedAt":"2023-07-17T23:37:32Z","number":7096,"mergeCommitSha":"fdff915a5b34be41e1d7276a9572b953e95ef916","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7096","title":"Update hc readme","createdAt":"2023-07-17T23:37:14Z"}
{"state":"Merged","mergedAt":"2023-07-18T01:53:16Z","number":7097,"mergeCommitSha":"84d1adf9018a33ce1342d73ea6bbcf970424a156","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7097","title":"Fix repo name prompt","createdAt":"2023-07-18T01:19:39Z"}
{"state":"Merged","mergedAt":"2023-07-18T16:30:43Z","number":7098,"body":"We have a `TextInput`, which is `<input type=\"text\">`, with nice features like leading and trailing views, keyboard submit handling, etc.  We never had any equivalent for multi-line `textarea`, we were using a few inconsistent things in a few places. \r\n\r\nSo this adds `TextArea`, the API is a simplified version of `TextInput`:\r\n\r\n* You can specify trailing content.  No leading content, as for multi-line text I don't think that will ever make sense.\r\n* You can specify enter/escape handlers\r\n* You can specify a particular number of rows (`rows=2` like text area), or auto height (`autosize=true`, with an optional `rows=n` to set a minimum row height)\r\n\r\nI needed this for the IDE explorer panel, so semantic search can go multi-line.","mergeCommitSha":"7bf9868500c9eadc047fd835011f7448f786ad33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7098","title":"Add TextArea component","createdAt":"2023-07-18T03:20:02Z"}
{"state":"Merged","mergedAt":"2023-07-18T06:34:01Z","number":7099,"body":"TL;DR, we're storing the raw response now, compressed. Basically only used for display purposes in the admin console. I'm going to YOLO this unless anyone objects before it passes CI","mergeCommitSha":"25fb5766db5b6043abe942836d64a99aed24b109","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7099","title":"Store the raw response so we can debug what went wrong after structured response parsing","createdAt":"2023-07-18T05:26:45Z"}
{"state":"Merged","mergedAt":"2022-01-19T17:40:12Z","number":71,"body":"- new model with tests\r\n\r\n<img width=\"1092\" alt=\"Screen Shot 2022-01-19 at 09 39 25\" src=\"https://user-images.githubusercontent.com/1798345/150184654-73ad72a9-9b18-43bb-8461-c4c6a015e379.png\">\r\n\r\n","mergeCommitSha":"dacb44b8aecd56cbf25724138398d5819077bb22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/71","title":"Introduce team membership models","createdAt":"2022-01-18T23:47:05Z"}
{"state":"Merged","mergedAt":"2022-03-29T21:14:34Z","number":710,"mergeCommitSha":"87047814ac0523835bc8837562ecd01a13a570b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/710","title":"Make sure service name is included in logs","createdAt":"2022-03-29T21:04:26Z"}
{"state":"Merged","mergedAt":"2023-07-18T17:27:49Z","number":7100,"body":"Oops -- this would sometimes set the initial list length to zero, depending on the order of events.","mergeCommitSha":"9f48947460288abac2b28c6f3d11ae3e5aebb6a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7100","title":"Fix bug in initial reference list length","createdAt":"2023-07-18T17:15:54Z"}
{"state":"Merged","mergedAt":"2023-07-18T18:39:31Z","number":7101,"mergeCommitSha":"f818e3a81d6fc02e9682b3ce7231966c785c34d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7101","title":"Move to custom configuration for honeycomb endpoints","createdAt":"2023-07-18T17:57:09Z"}
{"state":"Merged","mergedAt":"2023-07-18T22:16:12Z","number":7102,"body":"## Motivating use case\n\nGitHub API requests are constrained such that _parallel_ API requests for resources within the\nsame GitHub installation (instance of our GitHub App installed on a GitHub Org) are forbidden.\nViolations of this _secondary_ rate limit can lead to temporary App suspension for breaking the\nterms of service.","mergeCommitSha":"cc87208066edbe18dd1c9329d16817a1fad48722","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7102","title":"Introduce message groups for ActiveMQ messages that must be processed serially","createdAt":"2023-07-18T18:34:11Z"}
{"state":"Merged","mergedAt":"2023-07-18T18:58:48Z","number":7103,"mergeCommitSha":"28aa7fe8083409391200675cd7eb2eea8f136ae7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7103","title":"Update user defined description","createdAt":"2023-07-18T18:36:52Z"}
{"state":"Merged","mergedAt":"2023-07-18T19:17:17Z","number":7104,"mergeCommitSha":"eb9151b31a425ae7b039c9f57ac9940a81748f3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7104","title":"Update opentelemetry","createdAt":"2023-07-18T18:40:18Z"}
{"state":"Merged","mergedAt":"2023-07-20T17:55:16Z","number":7105,"body":"Prune recommendations that are older than 30 days to clear out old recommendations.","mergeCommitSha":"1fe30147fed3068978313f8c4add28070d611627","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7105","title":"Prune old ThreadRanks so that recommendations just contain recent threads","createdAt":"2023-07-18T18:44:25Z"}
{"state":"Merged","mergedAt":"2023-07-18T19:38:37Z","number":7106,"body":"Refinery currently does not support passing through opentelemetry metrics.\r\nWe have to use old endpoint.\r\n","mergeCommitSha":"41293fc51d735029dc405a0743df71880e2c31f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7106","title":"Fix opentelemetry metrics with refinery","createdAt":"2023-07-18T19:19:37Z"}
{"state":"Merged","mergedAt":"2023-07-24T23:59:46Z","number":7107,"body":"https://github.com/NextChapterSoftware/unblocked/assets/********/1c8f804a-057a-4b35-8639-88b8e8da9e0b\r\n\r\nIntegrates ability to edit the user's own description and topic expertise.","mergeCommitSha":"5f6bda2013ca278752c28a7e3267fa4e37103b26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7107","title":"Add user profile to dashboard","createdAt":"2023-07-18T19:36:03Z"}
{"state":"Merged","mergedAt":"2023-07-23T02:19:17Z","number":7108,"body":"This uses a new library to do import/unused-var linting.  This library has the advantage of auto-fixing unused imports by clearing them.  No more manual import pruning.","mergeCommitSha":"4c29f052001891cf2af1f76acc7ad6fea848bb06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7108","title":"Clear unused TS imports on save","createdAt":"2023-07-18T19:36:55Z"}
{"state":"Merged","mergedAt":"2023-07-18T19:47:02Z","number":7109,"mergeCommitSha":"9f4237c4c296ca9053651e48b289f25ddf2e0091","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7109","title":"Move local environment to refinery","createdAt":"2023-07-18T19:41:44Z"}
{"state":"Merged","mergedAt":"2022-03-29T21:47:48Z","number":711,"mergeCommitSha":"a35bd9e393ab4dff4bc53dda74e2c93c9beca001","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/711","title":"Fix platform.version log field","createdAt":"2022-03-29T21:28:11Z"}
{"state":"Merged","mergedAt":"2023-07-18T20:20:29Z","number":7110,"mergeCommitSha":"5cee7a63313bb9fcf8e47757e45e16422937f1d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7110","title":"Move dev to refinery","createdAt":"2023-07-18T20:20:23Z"}
{"state":"Merged","mergedAt":"2023-07-18T21:41:49Z","number":7111,"body":"Merge whenever ready","mergeCommitSha":"6dfdca808032a5d2fe4db53cd6771ddd924916ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7111","title":"Enable references for all teams in prod","createdAt":"2023-07-18T20:43:33Z"}
{"state":"Merged","mergedAt":"2023-07-18T21:05:46Z","number":7112,"mergeCommitSha":"613a5f6b2a37ad16e6e6988bb72c21f4a60479d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7112","title":"Move all environments to refinery","createdAt":"2023-07-18T20:52:58Z"}
{"state":"Merged","mergedAt":"2023-07-18T21:21:18Z","number":7113,"mergeCommitSha":"21490df4b107b9d980ab2d1a3e15287cf316e90c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7113","title":"Move healht check dropping to refinery","createdAt":"2023-07-18T21:17:09Z"}
{"state":"Merged","mergedAt":"2023-07-18T22:06:21Z","number":7117,"body":"Lot's of model transformation code duplication cleanup.","mergeCommitSha":"ea6b97aee19adcc8ff09c199cf473c3ade37f80d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7117","title":"Show input document size in admin web","createdAt":"2023-07-18T21:47:44Z"}
{"state":"Merged","mergedAt":"2023-07-18T21:48:13Z","number":7118,"body":"Enable refinery otel metrics\r\n","mergeCommitSha":"471e28fb66c64964d9c12b13ce4b377a97b9c7a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7118","title":"Enable refinery otel metrics","createdAt":"2023-07-18T21:47:45Z"}
{"state":"Merged","mergedAt":"2023-07-20T22:41:12Z","number":7119,"body":"Creates a thread and views it, same as the dashboard.  IntelliJ is next.","mergeCommitSha":"6d80fafc140f98e584f2ab5bdc26b60525198c01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7119","title":"Hook up semantic search in VSCode","createdAt":"2023-07-18T22:03:29Z"}
{"state":"Merged","mergedAt":"2022-03-29T21:56:20Z","number":712,"mergeCommitSha":"28cca0bb249380a8b6cb817cfdaebc56740cebce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/712","title":"Implements video participant heartbeats to support video cleaner service","createdAt":"2022-03-29T21:32:24Z"}
{"state":"Merged","mergedAt":"2023-07-18T23:20:58Z","number":7120,"mergeCommitSha":"8ff52df17104ca5ceea7bbdf12819b4af521c3bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7120","title":"dynamically sample refinery rules","createdAt":"2023-07-18T23:09:33Z"}
{"state":"Merged","mergedAt":"2023-07-18T23:45:15Z","number":7121,"body":"Lets us process GitHub issue web hooks","mergeCommitSha":"18f4f6186dc61241ae085e076c2ddf1c6f650f2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7121","title":"Add GitHubIssueEventService","createdAt":"2023-07-18T23:21:51Z"}
{"state":"Merged","mergedAt":"2023-07-18T23:52:44Z","number":7122,"body":"Suspect this is because the repo is no longer installed, but we have found a document for it.\r\n\r\nWhen deployed, run this:\r\nhttps://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/semanticSearch?q=list+all+of+the+backend+kotlin+services&templateId=b607dee8-b7f7-4690-974c-31ba1b308c5f","mergeCommitSha":"d3a4f19303be34097c0ed7961431a5c37df80cc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7122","title":"Debug ML result failures caused by PR repo lookup fail","createdAt":"2023-07-18T23:39:16Z"}
{"state":"Merged","mergedAt":"2023-07-19T00:00:25Z","number":7123,"mergeCommitSha":"3349880fa9edeba4160af1550fbf0305d931996b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7123","title":"Refinery take 2","createdAt":"2023-07-19T00:00:20Z"}
{"state":"Closed","mergedAt":null,"number":7124,"body":"- Refinery take 2\r\n- Try again\r\n","mergeCommitSha":"1ae7de323736a5bef805254bbb7af4fc6a8b2925","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7124","title":"RefineryV2","createdAt":"2023-07-19T00:36:01Z"}
{"state":"Merged","mergedAt":"2023-07-19T00:40:25Z","number":7125,"mergeCommitSha":"7a51aa61aff1227f1ace58692556dca621ba35a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7125","title":"Refineryv3","createdAt":"2023-07-19T00:40:10Z"}
{"state":"Merged","mergedAt":"2023-07-19T04:43:40Z","number":7126,"mergeCommitSha":"86d302aa837089985f1e753f48ea892ac9bf2ca1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7126","title":"Fix onboarding outlet logic","createdAt":"2023-07-19T02:19:28Z"}
{"state":"Merged","mergedAt":"2023-07-19T05:09:01Z","number":7127,"body":"Minor issues\r\n- \"in your repository\" was not accurate since there could be multiple.\r\n- mising dot at end of second sentence.\r\n\r\nMaybe just reword in one sentence as?\r\n- Please choose the specific \"Application.kt\" file you'd like to open.\r\n\r\n\r\n---\r\n\r\nOld\r\n<img width=\"600\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/739122b4-ccc8-4750-ba87-8e433a6268d0\">\r\n\r\nNew\r\n<img width=\"600\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/a844129b-0e12-4e63-a4a6-6cbdaae3756d\">\r\n","mergeCommitSha":"604a6731ca1eea72c68a2a9940d4a3c1c82d8502","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7127","title":"Select a file to open wording","createdAt":"2023-07-19T04:32:45Z"}
{"state":"Closed","mergedAt":null,"number":7128,"body":"Reverts NextChapterSoftware/unblocked#7126\r\n\r\nIf needed?","mergeCommitSha":"58e65ddbd9ea4eb8e154caf84083543e0eaf94ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7128","title":"Revert \"Fix onboarding outlet logic\"","createdAt":"2023-07-19T15:17:40Z"}
{"state":"Merged","mergedAt":"2023-07-19T15:48:57Z","number":7129,"mergeCommitSha":"acebcde0fdd2f1ffa5acd4b227e32dbbface2f6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7129","title":"Revert second person onboarding code","createdAt":"2023-07-19T15:40:57Z"}
{"state":"Merged","mergedAt":"2022-03-29T22:40:54Z","number":713,"mergeCommitSha":"af9ac15cc3c18e691321beceb59ddd954c845fb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/713","title":"minor doc changes","createdAt":"2022-03-29T22:29:39Z"}
{"state":"Merged","mergedAt":"2023-07-19T17:36:39Z","number":7130,"mergeCommitSha":"4bde3de3fdde0e3f5d892d1c952971451e49a491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7130","title":"Fix Clear Member Associations","createdAt":"2023-07-19T17:29:44Z"}
{"state":"Merged","mergedAt":"2023-07-19T20:09:31Z","number":7131,"mergeCommitSha":"42066ac09ac299cc9a3733bc68b5fde59b91371f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7131","title":"PR document timestamp is the merged at date","createdAt":"2023-07-19T18:18:38Z"}
{"state":"Merged","mergedAt":"2023-07-19T18:55:38Z","number":7132,"mergeCommitSha":"bce120e9788a061a4d3f9dea2d70e17740d726ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7132","title":"Kill dead team settings","createdAt":"2023-07-19T18:33:50Z"}
{"state":"Merged","mergedAt":"2023-07-19T18:55:15Z","number":7133,"mergeCommitSha":"fc0001408e6e8206e4bac43f905b3c5fb59c4631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7133","title":"Increase test parallelization","createdAt":"2023-07-19T18:37:22Z"}
{"state":"Merged","mergedAt":"2023-07-19T19:03:19Z","number":7134,"mergeCommitSha":"a76eedf9959bd015ba04572d8a0fad5a419acb2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7134","title":"Remove stale configs","createdAt":"2023-07-19T19:03:03Z"}
{"state":"Merged","mergedAt":"2023-07-20T06:12:07Z","number":7135,"body":"This API is called with a null query when populating topic insights from IDEs.\nNot an explicit search, so not considered an engagement signal.","mergeCommitSha":"26fa9de43e7873b54085edc6c6ff7b89fddc27a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7135","title":"Exclude searchInsight from metrics if there is no query","createdAt":"2023-07-19T19:50:14Z"}
{"state":"Merged","mergedAt":"2023-07-19T20:40:28Z","number":7136,"mergeCommitSha":"a3f3d76fb7892674509c5f6d071e3be7383c61f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7136","title":"Add metadata field to document prompt","createdAt":"2023-07-19T20:01:41Z"}
{"state":"Merged","mergedAt":"2023-07-19T22:09:22Z","number":7137,"mergeCommitSha":"6f8a83cea2cb941f604ab47eb2d446cc56210fbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7137","title":"Update kotlinter","createdAt":"2023-07-19T21:03:34Z"}
{"state":"Merged","mergedAt":"2023-07-20T01:53:13Z","number":7138,"mergeCommitSha":"050889f90a50be4cb84178ded09459aa2fead835","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7138","title":"Impersonate button restyle to make easier to find","createdAt":"2023-07-19T21:18:48Z"}
{"state":"Merged","mergedAt":"2023-07-20T17:06:34Z","number":7139,"mergeCommitSha":"d3c0fea1eb5610403e5a2f27dc03a207d829d7d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7139","title":"Unrevert 2nd person onboarding code","createdAt":"2023-07-19T21:24:19Z"}
{"state":"Merged","mergedAt":"2022-03-29T23:14:47Z","number":714,"body":"<img width=\"1057\" alt=\"Screen Shot 2022-03-29 at 16 06 19\" src=\"https://user-images.githubusercontent.com/1798345/160720831-7f2b6b06-6846-40e2-bafd-82f813b7ef3a.png\">\r\n","mergeCommitSha":"d60bf8c83180762a94238b8e0b473e4c4813499c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/714","title":"Local logging shows metadata in yellow","createdAt":"2022-03-29T23:04:13Z"}
{"state":"Merged","mergedAt":"2023-07-19T23:40:38Z","number":7140,"mergeCommitSha":"7414efdb02890dd97772ff97696445fd5f9f277f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7140","title":"Add fallback template, but not used yet","createdAt":"2023-07-19T21:36:48Z"}
{"state":"Merged","mergedAt":"2023-07-27T16:38:32Z","number":7142,"body":"Setup lifecycle of campaigns.\r\nAdd view within admin console to view campaign status.\r\n\r\nWhen user finishes the tutorial, an person onboarding campaign instance is created for the user\r\nEvery 8 hours, CampaignService checks for all uncompleted campaign instances and checks if the next milestone of the campaign is fulfilled. If so, it will trigger the milestone's action.\r\n\r\n<img width=\"1462\" alt=\"CleanShot 2023-07-19 at 15 25 53@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/9b34e4ae-4b25-4c67-bdad-344ee7084e62\">\r\n\r\nOnce this is in, emails will be implemented.\r\nCampaigns are explained here: https://www.notion.so/nextchaptersoftware/RFC-Campaign-Service-71caa7a90e3e43199b673263b5a84abf?pvs=4\r\n","mergeCommitSha":"48725b26977f00c2e2311ae76d3ff36abbc1628d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7142","title":"Basic campaign setup","createdAt":"2023-07-19T22:31:14Z"}
{"state":"Merged","mergedAt":"2023-07-24T18:47:26Z","number":7143,"mergeCommitSha":"89e52f00fd9010d831be4da6f037a00ff6dabaaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7143","title":"Retry with fallback template when rate limited","createdAt":"2023-07-19T23:06:40Z"}
{"state":"Merged","mergedAt":"2023-07-21T00:36:44Z","number":7144,"body":"Add tooltip for references. Currently third in the order of tooltips.\r\n\r\n<img width=\"1136\" alt=\"CleanShot 2023-07-19 at 16 20 19@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/dd39e26f-8e78-4fcb-b369-0f5f7c72527e\">\r\n","mergeCommitSha":"18632ac34788b4a257e90adc625f5eb00cb2ee22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7144","title":"Add references tooltip","createdAt":"2023-07-19T23:21:27Z"}
{"state":"Merged","mergedAt":"2023-07-21T00:40:35Z","number":7146,"body":"<img width=\"854\" alt=\"CleanShot 2023-07-19 at 16 28 41@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/418f677f-eea7-47ee-855d-395088c0eb7d\">\r\n<img width=\"954\" alt=\"CleanShot 2023-07-19 at 16 28 44@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/8a0566f8-e8f5-4d09-83ca-749e16512932\">\r\n<img width=\"1007\" alt=\"CleanShot 2023-07-19 at 16 28 47@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/dfe71214-74b3-4c48-adbc-b8a7a0bb8b20\">\r\n","mergeCommitSha":"bba16a03e74bec52b26d979d7389bc2328ab4061","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7146","title":"Update tutorial text","createdAt":"2023-07-19T23:29:35Z"}
{"state":"Merged","mergedAt":"2023-07-21T22:15:50Z","number":7147,"body":"So that if webhooks fail we can still ingest updated issues","mergeCommitSha":"6d04c8f62f417dedf704b42ef09afb94bbeca147","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7147","title":"Add GitHubIssuesIngestionSyncJob","createdAt":"2023-07-20T00:04:55Z"}
{"state":"Merged","mergedAt":"2023-07-20T00:36:42Z","number":7148,"mergeCommitSha":"b9b43a8b763db7cedcdfd285fb657b71c444e67f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7148","title":"Make it easier for me to refactor bot stuff","createdAt":"2023-07-20T00:21:52Z"}
{"state":"Merged","mergedAt":"2023-07-20T17:06:08Z","number":7149,"mergeCommitSha":"9f91543b3338db813f766b78098044d6cf685ba7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7149","title":"Fix feedback input color","createdAt":"2023-07-20T00:27:51Z"}
{"state":"Merged","mergedAt":"2022-03-30T16:56:50Z","number":715,"mergeCommitSha":"fa63ffe94f115ddaf72e1f4b34948e6f2e8b97eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/715","title":"Add a heartbeat mechanism to the SourceMark agent","createdAt":"2022-03-29T23:44:02Z"}
{"state":"Merged","mergedAt":"2023-07-24T16:47:15Z","number":7151,"body":"Does not ingest these messages.","mergeCommitSha":"6f08363f3f1f05502747ce4e26253324c60e4a46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7151","title":"Filter out slash command messages that start with /commandString","createdAt":"2023-07-20T01:55:13Z"}
{"state":"Merged","mergedAt":"2023-07-20T04:12:11Z","number":7152,"mergeCommitSha":"c864a4e2acd68cf99d2206a8dd6d87657feba7b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7152","title":"Slack bot query service continuations","createdAt":"2023-07-20T03:57:25Z"}
{"state":"Merged","mergedAt":"2023-07-20T05:33:11Z","number":7153,"body":"* All right-column groups (experts, references, loop in an expert, components) use link colours\r\n* Show \"external link\" icon after external references.  PR and thread references are not considered external, source references are.\r\n\r\n<img width=\"1564\" alt=\"Screenshot 2023-07-19 at 9 40 10 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/a2f07dd8-9509-4411-8dc9-b9f973561130\">\r\n","mergeCommitSha":"6f3cd270a4420b8573ba1a5280fb0e3bbfdb8ce7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7153","title":"Tweak references UI","createdAt":"2023-07-20T04:42:13Z"}
{"state":"Merged","mergedAt":"2023-07-20T17:49:30Z","number":7154,"body":"Problem is that the title is clipped to the point that it's hard to read what the thread is about without having to click on each thread.\r\n\r\nNo need to clip thread titles by character (which was fragile due to dynamic character width), since the container will naturally clip where needed anyway. Now consistent with PR titles in `PullRequestSummaryView.tsx`.\r\n\r\n---\r\n\r\n### Before\r\n<img  src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/83dcc4d1-e524-4226-9c1d-78e44ee7a635\">\r\n\r\n### After\r\n<img  src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/5144791b-0d85-44ce-95eb-43809572c7ff\">\r\n","mergeCommitSha":"b48eab54259021ad0af0f6c5c4ec1bc5fae58039","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7154","title":"Fix unnecessary thread title clipping","createdAt":"2023-07-20T05:17:25Z"}
{"state":"Merged","mergedAt":"2023-07-20T06:18:50Z","number":7155,"mergeCommitSha":"0a4477596a362660cd240bf561b03179caaf3709","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7155","title":"Fix merge state in PR page in admin","createdAt":"2023-07-20T05:57:38Z"}
{"state":"Open","mergedAt":null,"number":7156,"body":"(For backup only)\r\n\r\nThis reverts commit d3c0fea1eb5610403e5a2f27dc03a207d829d7d5.","mergeCommitSha":"06cff93d3a94bf6aaab982c3bab5a1f1e0b65514","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7156","title":"(DO NOT MERGE) Revert \"Unrevert 2nd person onboarding code (#7139)\"","createdAt":"2023-07-20T17:07:36Z"}
{"state":"Merged","mergedAt":"2023-07-20T18:21:00Z","number":7157,"mergeCommitSha":"53a227daa1168ba399d765afea60fb5d1e3b0774","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7157","title":"Small template page bug","createdAt":"2023-07-20T18:20:37Z"}
{"state":"Merged","mergedAt":"2023-07-20T20:23:05Z","number":7158,"body":"We currently are not correclty parsing bot resopnses and including a lot of cruft in the message on webhooks.\r\nWe should select only the speicifc block and take its contents.","mergeCommitSha":"cc917dcea3296357767f810989be90dfd48f4610","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7158","title":"Fix slack bot webhooks","createdAt":"2023-07-20T20:10:23Z"}
{"state":"Merged","mergedAt":"2022-03-30T00:25:47Z","number":716,"body":"Get the API client working again","mergeCommitSha":"b7db2f1ff63ed1e94a68672a99c672a51c479da9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/716","title":"SourceMark agent passes teamId to API service","createdAt":"2022-03-29T23:52:33Z"}
{"state":"Merged","mergedAt":"2023-07-21T17:17:51Z","number":7161,"body":"Currently there are 2 recommendation reasons. If the client asks for a limit of 50, we'll return 25 of each type. \r\n\r\nThis change also deprecates the push channel and removes the paging logic for the `getThreadsRecommended` operation since we're not using these features.","mergeCommitSha":"6a9891999a1ca81e7e69f3eef057fdbd309d1416","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7161","title":"ThreadBundleStore().getThreadBundlesRecommended returns results for each RecommendationReason","createdAt":"2023-07-20T22:27:55Z"}
{"state":"Merged","mergedAt":"2023-07-20T23:48:00Z","number":7162,"body":"Webhooks in general should take into account if thread already exists and update the thread regardless of verification failures.\r\n","mergeCommitSha":"3accb54d99e22323c50bb5fdf50ab3d8987efcaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7162","title":"Fix up slack bot webhooks","createdAt":"2023-07-20T23:03:35Z"}
{"state":"Merged","mergedAt":"2023-07-21T17:03:33Z","number":7163,"mergeCommitSha":"08751e5b24cb73cfcc50d6aa47cf63fd348ad20e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7163","title":"Sort some lists","createdAt":"2023-07-20T23:15:16Z"}
{"state":"Merged","mergedAt":"2023-07-20T23:19:45Z","number":7164,"body":"- reduce pusher/telemetry service events, except errors\n- separate the 2xx GET and MUTATION events\n- reduce 2xx GET events\n- keep errors, not just DB errors\n- ditto for DEV environment","mergeCommitSha":"89b15f72158b1f45cd5ce07718910d3e4f97ea8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7164","title":"Tweak refinery config","createdAt":"2023-07-20T23:15:23Z"}
{"state":"Merged","mergedAt":"2023-07-20T23:20:48Z","number":7165,"body":"Was 256 kB now 20 kB. Still ridiculous, especially since only the first 4 kB (512 tokens) are embedded.\n\nAlso, slightly more efficient Git clone.","mergeCommitSha":"a5449d35d47eb5d7a10c431b75bc93685134dded","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7165","title":"Smaller file embeddings","createdAt":"2023-07-20T23:16:41Z"}
{"state":"Merged","mergedAt":"2023-07-21T15:27:13Z","number":7166,"body":"Where `X` should be a topic where the onboarding user is *not* an expert.\r\n\r\n<img width=\"796\" alt=\"CleanShot 2023-07-20 at 16 53 39@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/796a4414-2158-4a3b-98a1-6eb78f6f72c7\">\r\n","mergeCommitSha":"bba33497644c35b27cecfaee17b1be5b38c55a0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7166","title":"Add \"Who are the experts for the X component?\" sample question","createdAt":"2023-07-20T23:54:44Z"}
{"state":"Merged","mergedAt":"2023-07-21T01:09:16Z","number":7167,"mergeCommitSha":"cd56fb4eb1b4d155a1ffb0d998cb4f4fec89cefa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7167","title":"Do not embed slack qa threads","createdAt":"2023-07-21T00:15:14Z"}
{"state":"Merged","mergedAt":"2022-03-30T03:50:14Z","number":717,"body":"Jeff this will do what you need. Use with care...","mergeCommitSha":"2471a27396677c573a9e72604d08f5a56ae3caa6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/717","title":"Allow wildcard hostnames for given schemes","createdAt":"2022-03-30T03:16:31Z"}
{"state":"Merged","mergedAt":"2023-07-23T02:19:06Z","number":7170,"body":"Supersedes https://github.com/NextChapterSoftware/unblocked/pull/6715 (I stole some code from it -- thanks Jeff)\r\n\r\n<img width=\"543\" alt=\"Screenshot 2023-07-20 at 8 50 23 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/cb3378b0-7d15-4321-98ff-df6b9babca92\">\r\n","mergeCommitSha":"a26e2a7646feabd7c10f4e0574a4bade43a08928","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7170","title":"VSCode feedback buttons","createdAt":"2023-07-21T03:50:33Z"}
{"state":"Merged","mergedAt":"2023-07-21T16:07:57Z","number":7171,"body":"Onboarding text changes","mergeCommitSha":"4604807e7ff5d4c3b1db91479abda68a273ebcc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7171","title":"Update SlackStep.swift","createdAt":"2023-07-21T14:42:21Z"}
{"state":"Merged","mergedAt":"2023-07-21T16:08:15Z","number":7172,"body":"Updating text for new onboarding","mergeCommitSha":"40548e3157152c9c041cd3f8fdb5b62f1d58818e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7172","title":"Update ExpertAnswersStep.swift","createdAt":"2023-07-21T14:47:55Z"}
{"state":"Merged","mergedAt":"2023-07-21T16:39:10Z","number":7173,"body":"Fetch relevant topics for user instead of client side filtering.","mergeCommitSha":"4a029d77ef069b1c665062a977a746188b3c27ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7173","title":"Fetch relevant topics","createdAt":"2023-07-21T16:29:36Z"}
{"state":"Merged","mergedAt":"2023-07-21T18:15:21Z","number":7174,"body":"Update font sizes and spacing \r\n<img width=\"860\" alt=\"CleanShot 2023-07-21 at 10 27 58@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/57516d72-f82e-4354-a323-095f7a6fa5d2\">\r\n\r\nUpdate Questions text.\r\n<img width=\"850\" alt=\"CleanShot 2023-07-21 at 10 28 08@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/8c31efdb-71e0-4341-921a-f692382b9e2c\">\r\n\r\nFix issue where \"Ask better questions\" popover flashes after asking sample question","mergeCommitSha":"be5a167c7562b90539b9ef45d148faa13ff07e0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7174","title":"Hub Onboarding Feedback","createdAt":"2023-07-21T17:34:42Z"}
{"state":"Merged","mergedAt":"2023-07-21T19:01:04Z","number":7175,"body":"Update tooltip order during onboarding.\r\nHide references tooltip if no references.","mergeCommitSha":"8925b09545405207d047a49badb0d83b2c6ea6b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7175","title":"Update tooltip order","createdAt":"2023-07-21T17:44:17Z"}
{"state":"Merged","mergedAt":"2023-07-21T17:54:45Z","number":7176,"mergeCommitSha":"75f71cd4d2258d9211ab085498212e4e7662a1e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7176","title":"Fix logs for amazonmq","createdAt":"2023-07-21T17:52:12Z"}
{"state":"Merged","mergedAt":"2023-07-21T18:00:34Z","number":7177,"body":"Still way too high.\n\nChanges:\n- keep all error events - they are tiny\n- keep all API mutation events - they are tiny\n- reduce slow query events by 2X\n- reduce default throughput by 4X","mergeCommitSha":"e9bc36a6dab342f2369ac311eaabbb89ab0ad735","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7177","title":"Tweak refinery to reduce Honeycomb EPM","createdAt":"2023-07-21T17:56:13Z"}
{"state":"Merged","mergedAt":"2023-07-21T18:53:17Z","number":7178,"body":"Update download page size and text.\r\n<img width=\"1056\" alt=\"CleanShot 2023-07-21 at 11 06 03@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/78e69c07-b28d-4d90-9c88-611924935350\">\r\n","mergeCommitSha":"d70ee53edef80529d8a69c97f6b68b3932d9a477","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7178","title":"Update download page size","createdAt":"2023-07-21T18:21:57Z"}
{"state":"Merged","mergedAt":"2023-07-21T19:26:25Z","number":7179,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1689959291308209","mergeCommitSha":"2c24aa0a2940b1c3b6531e05093a483356c7a9d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7179","title":"Prefetching questions should update the message references","createdAt":"2023-07-21T18:23:54Z"}
{"state":"Merged","mergedAt":"2022-04-05T00:09:06Z","number":718,"body":"## Summary\r\n\r\nImplements recording service DB interactions _without_ Agora integration. Agora API calls will come in a follow-up PR.\r\n\r\nThe plan is to get the db hooks and background clean tasks in place and then layer on the Agora bits. There is not upload task here - Agora handles that for us. \r\n","mergeCommitSha":"00d6f71382678b3f688adac66f9bfd71a1804759","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/718","title":"Recording service","createdAt":"2022-03-30T04:38:59Z"}