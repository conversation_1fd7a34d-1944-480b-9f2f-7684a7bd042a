{"state":"Merged","mergedAt":"2023-03-31T01:18:28Z","number":5482,"body":"Ran schema migration successfully locally","mergeCommitSha":"c84b0921758863e24229e471007f20e06f2127e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5482","title":"Remove PR ingestion page cursors and add migration for schema","createdAt":"2023-03-31T00:22:06Z"}
{"state":"Merged","mergedAt":"2023-03-31T01:39:46Z","number":5483,"body":"<img width=\"1260\" alt=\"Screenshot 2023-03-30 at 17 33 50\" src=\"https://user-images.githubusercontent.com/1798345/228993780-db360a0c-f78f-4f3a-b75f-4115627d30de.png\">\r\n","mergeCommitSha":"8b802afd64ff3fd7fc6574819c62a9de8f9fa612","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5483","title":"Update repo ingest progress on repos page","createdAt":"2023-03-31T00:22:09Z"}
{"state":"Merged","mergedAt":"2023-03-31T01:44:48Z","number":5484,"mergeCommitSha":"f4df5b80849cd71d6cda9e15660b1697ea4a11ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5484","title":"Remove completed migration","createdAt":"2023-03-31T01:43:57Z"}
{"state":"Merged","mergedAt":"2023-03-31T06:16:32Z","number":5485,"mergeCommitSha":"444687bb4f691ed355a24affa1c068e5c84d3360","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5485","title":"PR ingest uses abstract ScmRepoApi instead of GitHub classes directly","createdAt":"2023-03-31T01:44:35Z"}
{"state":"Merged","mergedAt":"2023-03-31T04:34:06Z","number":5486,"body":"More stable to use invariant repo IDs that do not change when the repo is renamed.","mergeCommitSha":"3a3c9f14997354732053ca4ad8ebd8864a999a8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5486","title":"Use repoExternalId instead of owner/repoName for GitHub","createdAt":"2023-03-31T02:10:58Z"}
{"state":"Merged","mergedAt":"2023-03-31T18:28:02Z","number":5487,"mergeCommitSha":"d4ef7384829afd534d613e9168353147a8f0e6f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5487","title":"Add scm serialzation","createdAt":"2023-03-31T17:19:17Z"}
{"state":"Merged","mergedAt":"2023-03-31T21:39:50Z","number":5488,"body":"The pull request API is enough to at least pull in the top-level PR object.\r\n\r\nComments and review will come later.","mergeCommitSha":"90e7ecffafc04965d278017308fcb180da6f20a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5488","title":"Hook up Bitbucket to PR ingestion","createdAt":"2023-03-31T17:44:31Z"}
{"state":"Merged","mergedAt":"2023-04-03T18:30:36Z","number":5489,"mergeCommitSha":"ac70c6f870901977d47b458a50b3f73832b16abb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5489","title":"Fix jetbrains build configuration issues","createdAt":"2023-03-31T18:13:51Z"}
{"state":"Merged","mergedAt":"2022-03-11T02:06:42Z","number":549,"body":"Will need to replace `upstreamPoint` with `upstreamPoints` in `SourceMarksCalculator`","mergeCommitSha":"a8352a5e011f9b2a83ae29e60e7ea9ba93ade3f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/549","title":"Add batch put sourcepoints","createdAt":"2022-03-11T00:55:02Z"}
{"state":"Merged","mergedAt":"2023-03-31T18:53:38Z","number":5490,"body":"We no longer need this code as it's being done via pipeline.\r\n","mergeCommitSha":"ee4d372112f2f09a066fd902af95222fa328b9ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5490","title":"Remove old histogram code","createdAt":"2023-03-31T18:34:18Z"}
{"state":"Merged","mergedAt":"2023-03-31T20:00:20Z","number":5496,"body":"Also, `clean-docker` should not prune volumes (postgres). This is probably unexpected\nmost of the time, as it will lead to local data loss.","mergeCommitSha":"8bb1f13a3bba9e9c0afd8ea2f65ad604be74b6b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5496","title":"Make targets for \"base\" stack and \"frontend\" stack","createdAt":"2023-03-31T19:58:44Z"}
{"state":"Merged","mergedAt":"2023-03-31T20:43:54Z","number":5497,"body":"This normally doesn't have any effect on the behaviour on the system because once\r\nbulk ingestion has completed then the next batch URLs are ignored anyway. However,\r\nit's good to clear these out to avoid confusion.","mergeCommitSha":"05cc63f30b34516e3ad42c918cfe6c98ec664d95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5497","title":"PR ingestion clears next batch URLs once the collection has been entirely drained","createdAt":"2023-03-31T19:59:13Z"}
{"state":"Merged","mergedAt":"2023-03-31T21:15:25Z","number":5498,"body":"Problem\n- ktlint orders all imports lexicographical\n- by default the IDE orders alias imports before non-alias imports\n\nResolve dispute by forcing IDE to align with ktlint rules and lexicographically order.","mergeCommitSha":"60149a8aab8aaa46bf4a7403b6d7a1c9f4307757","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5498","title":"Prevent IDE fighting with ktlint over alias import order","createdAt":"2023-03-31T21:04:32Z"}
{"state":"Merged","mergedAt":"2023-04-04T16:44:42Z","number":5499,"body":"~This is a quick and dirty fix to address our security issue. I will rework IP filter rules to make them easier to read/update and manage.~\r\n\r\nReworked this PR based on review feedback. \r\n- Changed the config to allow specifying new rules for custom endpoints\r\n- Split the deepcheck and custom endpoint rules.\r\n\r\nI have tested it locally against dev. Works as expected. For Webhook endpoints I tested it by adding my own IP address to IP whitelist and then tested against the endpoint. IP filtering is working as expected.\r\n\r\nLinear task: https://linear.app/unblocked/issue/UNB-1107/add-ip-filter-for-apihookssendinblue","mergeCommitSha":"1ce5b00dcfa0ab535b71b7d27e765f45f9f80adc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5499","title":"Extend the IP filter rule to include SendInBlue","createdAt":"2023-03-31T21:37:07Z"}
{"state":"Merged","mergedAt":"2022-01-18T07:22:46Z","number":55,"mergeCommitSha":"4774d506ea83768f8aef0d07c77afe12652a4ccb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/55","title":"Fix more lint and upgrade deps","createdAt":"2022-01-18T07:01:57Z"}
{"state":"Merged","mergedAt":"2022-03-11T01:46:30Z","number":550,"body":"This is intended to be called for debugging purposes only, where it is useful to remove calculated source points while we are still iterating on the algorithm. Will probably remove after a few weeks.","mergeCommitSha":"d1a9a2d78574f63371ebd12fe581321507a65485","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/550","title":"Delete calculated source points","createdAt":"2022-03-11T01:39:31Z"}
{"state":"Merged","mergedAt":"2023-03-31T21:47:53Z","number":5500,"body":"Implementation will follow.","mergeCommitSha":"03b2a97cf408a0c3066c0ea1c6dead45aa4cd377","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5500","title":"Adds more PR SCM APIs","createdAt":"2023-03-31T21:46:40Z"}
{"state":"Merged","mergedAt":"2023-03-31T22:13:52Z","number":5501,"body":"<img width=\"1468\" alt=\"Screenshot 2023-03-31 at 15 12 07\" src=\"https://user-images.githubusercontent.com/1798345/229241979-dc7e3260-c9ee-46cb-b5ab-16f167cf4350.png\">\r\n","mergeCommitSha":"0f0b4f75faf8595c25c49e1a1ab1f66ae537f1ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5501","title":"Hook up GitLab to PR ingestion","createdAt":"2023-03-31T22:04:32Z"}
{"state":"Merged","mergedAt":"2023-03-31T22:38:18Z","number":5502,"body":"This updates the topic page to better reflect what a customer sees to help with curation of topics during onboarding new customers. Follow up PR to allow removing insights from a topic.","mergeCommitSha":"b00393b17fc7039788c1f381d4f6c43cfce47358","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5502","title":"Update topic page for Doug ❤️","createdAt":"2023-03-31T22:09:07Z"}
{"state":"Merged","mergedAt":"2023-03-31T23:33:48Z","number":5504,"body":"I'll add implementation `pullRequestAllComments` for GitLab and Bitbucket next.","mergeCommitSha":"250590b91d62c3a09b79ef706ea8275fae2d4719","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5504","title":"Refactor SCM comment APIs","createdAt":"2023-03-31T22:50:42Z"}
{"state":"Merged","mergedAt":"2023-04-02T19:29:34Z","number":5505,"body":"`useScrollToTop` was taking in a function that returned a ref instead of the ref itself, and this function was passed in as a dependency in `useEffect`.  The function was being recreated on every render, so the effect was triggering on every render.\r\n\r\nRefs are stable (that's the whole point of a ref), so we can just pass them in directly.","mergeCommitSha":"638a1cf27b6a37ea81146de0ec9da0308e830bac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5505","title":"Fix incorrect scroll to top behaviour","createdAt":"2023-03-31T22:58:00Z"}
{"state":"Closed","mergedAt":null,"number":5506,"body":"Our client kotlin API libraries were generating date properties as Strings.  I'm not sure why this was happening, as the generator docs say it should default to `java8` dates \uD83E\uDD37 ","mergeCommitSha":"26fc6705f08dc69c6fa4a15ffcd16a0ccbfad4ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5506","title":"OpenAPI kotlin client models should generate proper dates","createdAt":"2023-03-31T23:37:34Z"}
{"state":"Merged","mergedAt":"2023-04-01T04:10:02Z","number":5507,"mergeCommitSha":"2c2b0f75b8b2ab56896ac828f60a5931c0e6bebe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5507","title":"Send pull request ingestion event for GitLab and Bitbucket during bulk ingestion","createdAt":"2023-03-31T23:42:13Z"}
{"state":"Merged","mergedAt":"2023-04-01T00:57:04Z","number":5508,"body":"Newest PRs are ingested first.\n\nTurns out Bitbucket Cloud APIs have pretty flexible sorting:\nhttps://developer.atlassian.com/cloud/bitbucket/rest/intro/#sorting-query-results","mergeCommitSha":"01818f6fdb934b395bd8c2d3f2af4e7bb4400060","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5508","title":"Process Bitbucket PRs in descending order for bulk ingestion","createdAt":"2023-03-31T23:48:36Z"}
{"state":"Closed","mergedAt":null,"number":5509,"mergeCommitSha":"3877fe587860832f211e1c8906765a52e1eb4ef8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5509","title":"Fix deps in scroll fn","createdAt":"2023-04-01T03:42:53Z"}
{"state":"Merged","mergedAt":"2022-03-11T01:57:34Z","number":551,"mergeCommitSha":"0eb141c4ea607bbbb9d54de94bc9b347c9403c04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/551","title":"Fix logging","createdAt":"2022-03-11T01:50:44Z"}
{"state":"Merged","mergedAt":"2023-04-03T15:41:49Z","number":5510,"mergeCommitSha":"f4260f112950dd35cd96f216b2e3a5b36a551c8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5510","title":"Fix emoji in titles","createdAt":"2023-04-03T15:36:05Z"}
{"state":"Merged","mergedAt":"2023-04-03T16:54:03Z","number":5511,"mergeCommitSha":"d31741760efb9f6e232130666339ab42b50be5d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5511","title":"Don't reload topic page on topic deletion","createdAt":"2023-04-03T16:36:15Z"}
{"state":"Merged","mergedAt":"2023-04-03T16:59:28Z","number":5512,"mergeCommitSha":"565e83288d705c1ef7274ab8be766efedb7a7ed1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5512","title":"Disable descriptions for non-demo teams","createdAt":"2023-04-03T16:37:46Z"}
{"state":"Merged","mergedAt":"2023-04-03T17:39:09Z","number":5513,"mergeCommitSha":"78240b7fe6d2ec1674f117560aaa8c850d2ec679","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5513","title":"Add manual flag to show topic descriptions","createdAt":"2023-04-03T16:56:12Z"}
{"state":"Merged","mergedAt":"2023-04-03T17:59:53Z","number":5514,"body":"This reverts commit 565e83288d705c1ef7274ab8be766efedb7a7ed1.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-1111/re-enable-descriptions-for-non-demo-teams","mergeCommitSha":"5ea6478b704422a944fc878d244649c6e7bd8b9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5514","title":"Revert \"Disable descriptions for non-demo teams (#5512)\"","createdAt":"2023-04-03T17:14:39Z"}
{"state":"Merged","mergedAt":"2023-04-03T18:26:01Z","number":5515,"body":"Fix slack emoji in title","mergeCommitSha":"806c7dc8a4c0367aff8de502a52f0c2387540d88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5515","title":"Fi xslack emoji","createdAt":"2023-04-03T18:13:04Z"}
{"state":"Merged","mergedAt":"2023-04-03T18:26:42Z","number":5516,"body":"This helps when opening the root unblocked folder in VSCode, it ensures eslint parses the root folder locations correctly.","mergeCommitSha":"b458a6690422c5aac8bf6c9faaa86ee3045377d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5516","title":"Fix eslint monorepo setup","createdAt":"2023-04-03T18:18:41Z"}
{"state":"Merged","mergedAt":"2023-04-03T20:56:04Z","number":5518,"mergeCommitSha":"89f77122373bf189631b6f3f95eac256ad84e90d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5518","title":"Show archive state for pull request in admin console","createdAt":"2023-04-03T20:42:41Z"}
{"state":"Merged","mergedAt":"2023-04-05T17:59:08Z","number":5519,"mergeCommitSha":"850c030665cd20e5e92e8f62e1997a8fa4f26830","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5519","title":"Set correct defaults for explorer insights panel in Jetbrains","createdAt":"2023-04-03T21:15:22Z"}
{"state":"Merged","mergedAt":"2022-03-11T02:41:36Z","number":552,"mergeCommitSha":"3167a8516cb71eaf2705f3bd26a13baa113f064b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/552","title":"Run in DEV stack","createdAt":"2022-03-11T02:10:32Z"}
{"state":"Merged","mergedAt":"2023-04-03T22:11:33Z","number":5520,"mergeCommitSha":"65e06031fb4fba6886f86db565bb88c5ce374123","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5520","title":"Topic scm ingestion","createdAt":"2023-04-03T21:29:36Z"}
{"state":"Merged","mergedAt":"2023-04-03T22:44:31Z","number":5521,"mergeCommitSha":"427af567c197163e81675302ed7d09bd72b52709","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5521","title":"getPullRequestsForCommits does not return archived pull requests","createdAt":"2023-04-03T22:01:14Z"}
{"state":"Merged","mergedAt":"2023-05-11T19:46:08Z","number":5522,"body":"Fixes issue where PR Insight view would \"refresh\" and navigate to the first tab at the top of the page.\r\n\r\nSource of issue is within PullRequestInfoStream. Whenever it was updated upstream, its `mapStreamAsync` function would trigger. This would momentarily change the stream's result to \"loading\", causing the PRView within the webview to unmount, losing all its local state. Once the stream changes back to loaded, the PRView remounts.\r\n\r\nThis PR includes multiple improvements:\r\n1. Lift selected tab state out of webview and into extension. State shouldn't be held within the webview\r\n2. Whenever auth refreshes, person updates as well. Dedupe the PersonStream results as there are many downstream subscribers that shouldn't be triggered on a no-op person event.\r\n3. Debounce `SourceMarkSnippetStream`","mergeCommitSha":"4a294dfdc6890b7a5eea3f73473567f1a4b3c3ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5522","title":"Fix issue with Insights panel refreshing","createdAt":"2023-04-03T22:50:39Z"}
{"state":"Merged","mergedAt":"2023-04-04T00:25:55Z","number":5523,"mergeCommitSha":"4fc59aa1c61aac0dfcbd294643341c7b9b96decb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5523","title":"Fix up glue etl","createdAt":"2023-04-04T00:23:02Z"}
{"state":"Merged","mergedAt":"2023-04-04T00:52:39Z","number":5524,"body":"*Before:* \r\nWe were getting the same results for `dev.getunblocked.com` but I forgot to save the output!\r\n```\r\nnmap --script ssl-enum-ciphers -p 443 getunblocked.com\r\nStarting Nmap 7.93 ( https://nmap.org/ ) at 2023-04-03 17:16 PDT\r\nNmap scan report for getunblocked.com (*************)\r\nHost is up (0.038s latency).\r\nOther addresses for getunblocked.com (not scanned): ************** ************* **************\r\nrDNS record for *************: server-108-139-79-74.dxb53.r.cloudfront.net\r\n\r\nPORT    STATE SERVICE\r\n443/tcp open  https\r\n| ssl-enum-ciphers:\r\n|   TLSv1.2:\r\n|     ciphers:\r\n|       TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\r\n|       TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\r\n|       TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\r\n|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384 (ecdh_x25519) - A\r\n|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256 (ecdh_x25519) - A\r\n|     compressors:\r\n|       NULL\r\n|     cipher preference: server\r\n|   TLSv1.3:\r\n|     ciphers:\r\n|       TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\r\n|       TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\r\n|       TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\r\n|     cipher preference: server\r\n|_  least strength: A\r\n\r\nNmap done: 1 IP address (1 host up) scanned in 3.29 seconds\r\n```\r\n\r\n*After:*\r\n```\r\nnmap --script ssl-enum-ciphers -p 443 dev.getunblocked.com\r\nStarting Nmap 7.93 ( https://nmap.org ) at 2023-04-03 17:39 PDT\r\nNmap scan report for dev.getunblocked.com (*************)\r\nHost is up (0.036s latency).\r\nOther addresses for dev.getunblocked.com (not scanned): ************ ************* ************\r\nrDNS record for *************: server-13-35-169-127.fjr50.r.cloudfront.net\r\n\r\nPORT    STATE SERVICE\r\n443/tcp open  https\r\n| ssl-enum-ciphers:\r\n|   TLSv1.2:\r\n|     ciphers:\r\n|       TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\r\n|       TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\r\n|       TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\r\n|     compressors:\r\n|       NULL\r\n|     cipher preference: server\r\n|   TLSv1.3:\r\n|     ciphers:\r\n|       TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A\r\n|       TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A\r\n|       TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A\r\n|     cipher preference: server\r\n|_  least strength: A\r\n\r\nNmap done: 1 IP address (1 host up) scanned in 2.11 seconds\r\n```","mergeCommitSha":"db63ca92bf650a35c7833549653ea530091b95c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5524","title":"disabled weak ciphers","createdAt":"2023-04-04T00:44:37Z"}
{"state":"Merged","mergedAt":"2023-04-04T16:23:05Z","number":5525,"mergeCommitSha":"9b91ab69752a6cc6c6610963a235f1cc1eb52adf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5525","title":"Only display a single update toast by first dismissing the first one","createdAt":"2023-04-04T02:31:50Z"}
{"state":"Merged","mergedAt":"2023-04-05T00:22:37Z","number":5526,"body":"- GitLab uses discussion API, and use it to fabricate thread\r\n- Create and use `ScmPrComment` sealed class to represent top-, file-, and line-level comments","mergeCommitSha":"a851d35aea7bb1a998e1c84510afc188b3b695b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5526","title":"Implement comment APIs for GitLab and Bitbucket","createdAt":"2023-04-04T03:36:07Z"}
{"state":"Merged","mergedAt":"2023-04-04T17:27:49Z","number":5527,"body":"So that we can clean up topic mappings when we onboard a new customer","mergeCommitSha":"ecc5734a3db7314916bd6c0a267fa07df1676cda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5527","title":"Allow removing an insight from a topic from the admin console","createdAt":"2023-04-04T05:36:12Z"}
{"state":"Merged","mergedAt":"2023-04-04T18:38:21Z","number":5528,"body":"- Added ip filter rule for Gitlab https://docs.gitlab.com/ee/user/gitlab_com/#ip-range\r\n- Added ip filter rule for Bitbucket https://support.atlassian.com/organization-administration/docs/ip-addresses-and-domains-for-atlassian-cloud-products/#Outgoing-Connections\r\n- Added IP filter rule for AssemblyAI https://www.assemblyai.com/docs/walkthroughs#using-webhooks\r\n- Slack uses signature validation so we don't need to whitelist IP ranges for it.","mergeCommitSha":"d73a56bdf40182b1d59b29f26a094ee7f9f9f8f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5528","title":"adding more IP filters for gitlab, bitbucket and transcription service","createdAt":"2023-04-04T17:44:39Z"}
{"state":"Merged","mergedAt":"2023-04-04T18:29:47Z","number":5529,"mergeCommitSha":"832442b7e400c3c6d04e188c2c9acb60016cbf67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5529","title":"Fix missing TranscriptionHooks in API docs","createdAt":"2023-04-04T18:01:45Z"}
{"state":"Merged","mergedAt":"2022-03-11T04:10:39Z","number":553,"mergeCommitSha":"e08fbb19cf014af50c388942386e59efde66418d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/553","title":"Fix Git runner race","createdAt":"2022-03-11T04:03:17Z"}
{"state":"Merged","mergedAt":"2023-04-04T18:47:29Z","number":5530,"mergeCommitSha":"335b678ee0ecd22cb6d62719206b7de43c079d8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5530","title":"Improve oai prompt","createdAt":"2023-04-04T18:47:22Z"}
{"state":"Merged","mergedAt":"2023-04-04T20:07:05Z","number":5531,"body":"First step before I add the ability to add to edit keywords in the admin console.","mergeCommitSha":"a2fef83c53753320feac8388707bfb211c3ea938","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5531","title":"Add migration to backfill topic keywords","createdAt":"2023-04-04T19:01:16Z"}
{"state":"Merged","mergedAt":"2023-04-04T20:04:05Z","number":5532,"body":"Fixes auth issue where the cached token was not updating properly.\r\nThis caused unexpected 401s which were never refreshed.","mergeCommitSha":"5eb93fcdba67ade607d147d0723590621165ea7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5532","title":"Fix Auth Issue","createdAt":"2023-04-04T19:09:20Z"}
{"state":"Merged","mergedAt":"2023-04-04T19:35:06Z","number":5533,"mergeCommitSha":"ccc1f159acdef95f376b36e224451b025a9dbddd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5533","title":"Rotate sendinblue","createdAt":"2023-04-04T19:34:33Z"}
{"state":"Merged","mergedAt":"2023-04-04T20:02:45Z","number":5534,"mergeCommitSha":"f86b3497632d587e08cf15609926878dbd035a61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5534","title":"Add openai client support from services","createdAt":"2023-04-04T19:51:52Z"}
{"state":"Merged","mergedAt":"2023-04-05T18:21:47Z","number":5535,"body":"Add eslint rule that disallows unsafe method capture.  This would have prevented https://github.com/NextChapterSoftware/unblocked/pull/5532#event-8928345474\r\n\r\nRule is described here: https://typescript-eslint.io/rules/unbound-method\r\n\r\nWe should generally be using arrow functions when making a lambda with class method calls.\r\n","mergeCommitSha":"3be7a43fccd710597b3f3b0a05f142eb1b91f757","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5535","title":"Disallow unsafe method capture","createdAt":"2023-04-04T20:33:53Z"}
{"state":"Merged","mergedAt":"2023-04-04T23:01:31Z","number":5536,"body":"<img width=\"1218\" alt=\"CleanShot 2023-04-04 at 13 37 53@2x\" src=\"https://user-images.githubusercontent.com/1924615/229914869-e79af26c-2814-4359-ba18-eb6def6969f0.png\">\r\n","mergeCommitSha":"c817b3d3b97e68966e81b5ae63fea82b51f47471","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5536","title":"Add ability to update topic keywords from admin console","createdAt":"2023-04-04T20:38:41Z"}
{"state":"Merged","mergedAt":"2023-04-04T21:09:28Z","number":5537,"mergeCommitSha":"cf2c4fafd8c5dda4ba8bb70c87cdf7cd6e586f9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5537","title":"File is not required for getRecommendedTeamMembers","createdAt":"2023-04-04T20:48:51Z"}
{"state":"Merged","mergedAt":"2023-04-10T17:32:14Z","number":5538,"mergeCommitSha":"b3db1b63d34d9cdf57b02725db3f4bf2a7468c57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5538","title":"Add topics push channel","createdAt":"2023-04-04T21:27:36Z"}
{"state":"Closed","mergedAt":null,"number":5539,"body":"The clients need the ability to get the list of topics where a team member is an expert. This PR proposes modifying the `getTopics` operation to take an optional `expert` query parameter, which is the UUID of a team member. When present the topics returned will be just those where `expert` is an expert.\r\n\r\nIf this is not the ideal approach, we can come up with another endpoint.","mergeCommitSha":"6b98127a99099880d5027ef63a307e012c3bb602","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5539","title":"[RFC] Add ability to filter getTopics results by expert","createdAt":"2023-04-04T22:12:01Z"}
{"state":"Closed","mergedAt":null,"number":554,"body":"Interestingly, this made absolutely zero different. Not even 1 second faster on a 3 min baseline run.","mergeCommitSha":"2b23fff1d4aa218313991bf49a43e1e580863c8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/554","title":"[DO NOT MERGE] Introduce Git cache for performance","createdAt":"2022-03-11T04:09:04Z"}
{"state":"Merged","mergedAt":"2023-04-06T20:01:39Z","number":5540,"body":"<img width=\"896\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/229931084-d4d2e5f1-ba07-4aa8-be72-5fd9f644d353.png\">\r\n\r\n<img width=\"1181\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/229931205-3457f1ec-60ab-45a9-8300-9d661e7b5852.png\">\r\n\r\n<img width=\"1203\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/229931381-873277f6-8f2d-467f-aaba-df9346af5294.png\">\r\n\r\nsmall viewports:\r\n<img width=\"856\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/229940376-70851b1b-0d56-49e5-a387-78161afe0a1c.png\">\r\n<img width=\"850\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/229940307-0a0da6cf-ff07-45cd-9bbd-8b45a586f2ca.png\">\r\n\r\n\r\n\r\n\r\n","mergeCommitSha":"30f5801a016a5a92e944e5324ee7b5a5cfe046e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5540","title":"Topic editing UI","createdAt":"2023-04-04T22:56:39Z"}
{"state":"Merged","mergedAt":"2023-04-05T00:23:23Z","number":5541,"body":"There are two alternative approaches:\r\n\r\nIngestion Job Type | GitHub | All other SCMs\r\n-- | -- | --\r\nLightweight onboarding | runs for each PR that has a comment, triggers a PR ingestion event for each PR, with priority. | does not run at all (this change)\r\nBulk onboarding | collects all PRs, and all comments. does not trigger PR ingestion events. TODO reverse PR list direction DAVE. | collects all PRs, but no comments. triggers a PR ingestion event for each PR, without priority.\r\nSync | runs periodically | TODO does not currently work RICHIE\r\nEvent | runs on each PR event, collecting PRs, review, comments; or just reviews | TODO does not currently work DAVE\r\n","mergeCommitSha":"86b62945e2036069c7beb4c8f9fb6a12e7bf1f91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5541","title":"Only run lightweight PR ingestion for GitHub and GHE","createdAt":"2023-04-04T23:10:34Z"}
{"state":"Merged","mergedAt":"2023-04-04T23:47:21Z","number":5542,"mergeCommitSha":"02fb303e9b7b9a48d506a114b6a58f076ca1a095","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5542","title":"Add secrets","createdAt":"2023-04-04T23:38:09Z"}
{"state":"Merged","mergedAt":"2023-04-05T00:15:00Z","number":5543,"mergeCommitSha":"b72636c39ab30b54964cfb9558b07b9bf5620cde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5543","title":"allPullRequests retrieves in desc order of created","createdAt":"2023-04-05T00:01:26Z"}
{"state":"Merged","mergedAt":"2023-04-05T00:25:05Z","number":5544,"mergeCommitSha":"a868b1cd03c0932874794c22a9d6dedf3cf938a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5544","title":"Admin: Remove team dups from search results","createdAt":"2023-04-05T00:21:47Z"}
{"state":"Merged","mergedAt":"2023-04-05T01:12:33Z","number":5545,"body":"Not necessary.","mergeCommitSha":"e928381306dd3a202120c5ead3f34816e8a764aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5545","title":"Remove repoId from getRecommendedTeamMembers","createdAt":"2023-04-05T00:28:23Z"}
{"state":"Merged","mergedAt":"2023-04-05T01:43:13Z","number":5546,"mergeCommitSha":"f76cf7500b26e6c6d50e3450b547ecafd41a110b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5546","title":"Moving away from hardcoded oepnai tokens","createdAt":"2023-04-05T01:42:42Z"}
{"state":"Merged","mergedAt":"2023-04-05T21:12:24Z","number":5547,"body":"Allows the PR ingest **sync** and **event** jobs to work with an abstract SCM.","mergeCommitSha":"a5ac0069b825e29cba8acc5f9873bf46c1630729","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5547","title":"Abstract the ScmPullRequestService","createdAt":"2023-04-05T02:12:25Z"}
{"state":"Merged","mergedAt":"2023-04-05T17:48:32Z","number":5548,"mergeCommitSha":"8cee12c1526d6ffc309a9eeddfaeaeb8c2fd9c65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5548","title":"Remove deprecated class","createdAt":"2023-04-05T17:24:33Z"}
{"state":"Merged","mergedAt":"2023-04-05T22:01:30Z","number":5549,"body":"When you click on a gutter icon, we now immediately open the thread (if there is one thread on the line), or display a popover (if there are multiple threads on the line).  Most of the PR is for the popover.  Lots of issues for very little code:\r\n\r\n* The popover had to be written in Swing.  I tried building it using HTML but the popover never sized to content correctly.  This might be something to investigate in the future.\r\n* There's no built in component that async loads images, so I made one.  Ended up adding ktor client for this, might not be worth it if it brings in a lot of dependencies?\r\n* Build some date utils mimicking our TS helpers\r\n* The Swing layout components are horribly broken.  I settled on the GridBagLayout as it seemed to be the best-behaved.  If we build anything more complicated then this we should look at alternatives to using raw Swing, as this took ages to get working reliably, and it's still kinda broken.\r\n\r\nAlso fixes https://linear.app/unblocked/issue/UNB-1068/populate-insight-window-and-gutter-icons-on-load\r\n\r\n<img width=\"658\" alt=\"Screenshot 2023-04-05 at 10 23 16 AM\" src=\"https://user-images.githubusercontent.com/2133518/230156679-45b33d7b-3efc-4d91-9ede-b59433fa64c1.png\">\r\n","mergeCommitSha":"6f6d907f2f04826629029cded57f1bcecf2d9d10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5549","title":"JetBrains gutter icon popup","createdAt":"2023-04-05T17:27:38Z"}
{"state":"Merged","mergedAt":"2022-03-11T05:47:57Z","number":555,"mergeCommitSha":"fee3c3f1ab2e6f8e96f4eba42c8c9e31992fcf8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/555","title":"Add canonical route to ktor app logging context","createdAt":"2022-03-11T05:23:08Z"}
{"state":"Merged","mergedAt":"2023-04-05T18:54:31Z","number":5550,"body":"Uses GitGuardian to scan for secrets. This is a free service for teams < 25 engineers. ","mergeCommitSha":"7340bb66ecd582157af7a8dfcda7e321456a3068","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5550","title":"Scan for secrets on pull requests","createdAt":"2023-04-05T17:44:14Z"}
{"state":"Open","mergedAt":null,"number":5551,"body":"<img width=\"1486\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/230154616-30694d12-ea31-49be-bd25-bebd209091cd.png\">\r\n","mergeCommitSha":"f2126b49a37e91cbbcb9c774aca7d6eb97569341","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5551","title":"Add recommended team members invite toast","createdAt":"2023-04-05T17:45:16Z"}
{"state":"Merged","mergedAt":"2023-04-05T20:11:25Z","number":5552,"mergeCommitSha":"05dec7c2afaccecb85a5833dd0924fab28a8d2f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5552","title":"Removes cmd-shift-k install from customer builds","createdAt":"2023-04-05T20:01:49Z"}
{"state":"Merged","mergedAt":"2023-04-06T18:38:29Z","number":5553,"body":"Setup a promise proxy service from Agent -> IntelliJ\r\n\r\nDue to limitations with how our grpc is setup, we are unable to have promises that originate from the Agent to the Extension.\r\nUsing a two way directional stream, create a Promise Proxy which allows the agent to make promise-like requests to the agent.\r\n\r\nThe first use-case is to introduce a StorageProxy which allows the client to store arbitrary string values in IntelliJ. Moved token storage from keychain to this new storage system.","mergeCommitSha":"87b7c02ecd3e9041ae2a2c34ad24d89338ac0265","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5553","title":"IntelliJ Promise Proxy Service","createdAt":"2023-04-05T20:37:25Z"}
{"state":"Merged","mergedAt":"2023-04-05T22:00:32Z","number":5554,"mergeCommitSha":"db28550984641c1e30a9fdd543cfa6bdfe8e8081","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5554","title":"Move libraries around","createdAt":"2023-04-05T20:49:25Z"}
{"state":"Merged","mergedAt":"2023-04-05T22:04:51Z","number":5555,"mergeCommitSha":"a073c2dc7aed8f82e474b03d058ef7b6832f69d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5555","title":"Try again","createdAt":"2023-04-05T22:04:37Z"}
{"state":"Merged","mergedAt":"2023-04-05T22:41:57Z","number":5556,"mergeCommitSha":"49cc9837ee424abd21e2d61ba3588eee3e5a5538","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5556","title":"Cleanup etag column","createdAt":"2023-04-05T22:05:17Z"}
{"state":"Merged","mergedAt":"2023-04-05T22:52:12Z","number":5557,"mergeCommitSha":"8d2d772c2cec452fd408eda0f15d3aeb92b07f36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5557","title":"Fix min OS version check in installer","createdAt":"2023-04-05T22:50:20Z"}
{"state":"Merged","mergedAt":"2023-04-05T23:37:36Z","number":5558,"body":"This was preventing bot threads from being archived (since the logic was running before the thread was actually committed). I've added unit tests to show this is working now.","mergeCommitSha":"a7bb3178e4635ffe9944aa95acba91c62866bb25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5558","title":"[Bug Fix] Move bot archiving logic to outside of the transaction","createdAt":"2023-04-05T23:16:04Z"}
{"state":"Merged","mergedAt":"2023-04-06T01:57:22Z","number":5559,"mergeCommitSha":"f3b2dd31cdced61d7ee9cf871d4c58cf8677b77b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5559","title":"Add ability to invoke openai from services","createdAt":"2023-04-06T00:43:01Z"}
{"state":"Merged","mergedAt":"2022-03-11T05:41:56Z","number":556,"body":"Much faster in DEV:\r\n```\r\n21:27:25 | INFO  | c.n.s.SourceMarkCalculator: recalculated 331 of 331 SourceMarks in this batch\r\n21:27:25 | INFO  | c.n.s.SourceMarkCalculator: elapsed time: 1m 14.292s\r\n```","mergeCommitSha":"8ddeacd871e5dd302a14737e9ece39e07363516d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/556","title":"Background upstream API requests","createdAt":"2022-03-11T05:25:20Z"}
{"state":"Closed","mergedAt":null,"number":5560,"mergeCommitSha":"bc8000ab665c01b1e664e9f867ebf886d9ec7020","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5560","title":"Add apple-app-site-assocation file to https://getunblocked.com/.well-known/apple-app-site-association","createdAt":"2023-04-06T02:01:07Z"}
{"state":"Merged","mergedAt":"2023-04-06T02:43:53Z","number":5561,"mergeCommitSha":"f47ee9d0e17ba3b61b09be3b98fc0996ea64a103","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5561","title":"Update","createdAt":"2023-04-06T02:13:07Z"}
{"state":"Closed","mergedAt":null,"number":5562,"mergeCommitSha":"a0b880e5b43cb8bd2b34d0b8a664e59c1f5b95a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5562","title":"Add associated domains entitlement to app","createdAt":"2023-04-06T02:13:27Z"}
{"state":"Closed","mergedAt":null,"number":5563,"mergeCommitSha":"24b6b79893aab06f7809a1d71e79d01ff0ee8ae6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5563","title":"Capture universal link callback","createdAt":"2023-04-06T02:45:58Z"}
{"state":"Merged","mergedAt":"2023-04-06T03:22:26Z","number":5564,"mergeCommitSha":"6920eeebc571c6bde4a80ca482660d910775b911","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5564","title":"Reenable tests","createdAt":"2023-04-06T03:04:36Z"}
{"state":"Merged","mergedAt":"2023-04-06T04:23:16Z","number":5565,"mergeCommitSha":"d4056b5fbad47917b06624bee5410e5b3394200f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5565","title":"Add CSP rule for intercomcnd","createdAt":"2023-04-06T03:24:01Z"}
{"state":"Merged","mergedAt":"2023-04-06T05:18:59Z","number":5566,"mergeCommitSha":"3a8ad04344215e2c450eaa6cc9f7be8c20c9777a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5566","title":"Archive low relevance threads for team member","createdAt":"2023-04-06T05:04:27Z"}
{"state":"Merged","mergedAt":"2023-04-06T06:36:28Z","number":5567,"body":"Move to location recommended for local stack hooks\r\n\r\nhttps://docs.localstack.cloud/references/init-hooks/","mergeCommitSha":"41a4b711b0d22244e20e03e40ac0d6b4eb43e573","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5567","title":"Fix local stack init","createdAt":"2023-04-06T05:38:40Z"}
{"state":"Merged","mergedAt":"2023-04-06T06:24:50Z","number":5568,"body":"Before we we're only doing it for merged PRs, but really we should do for all.","mergeCommitSha":"b59518ab26d839a3d202056b30fdb211b3be7194","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5568","title":"Archive low relevance threads for all PRs","createdAt":"2023-04-06T05:51:25Z"}
{"state":"Merged","mergedAt":"2023-04-06T06:19:10Z","number":5569,"body":"- remove github APIs\n- use abstract SCM APIs","mergeCommitSha":"fab3c5dcd4c710db3e0c8dffd6e0d30bec075acc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5569","title":"Admin: Raw SCM API responses are abstract","createdAt":"2023-04-06T05:58:34Z"}
{"state":"Merged","mergedAt":"2022-03-11T06:07:42Z","number":557,"body":"Way fewer API requests.\r\n```\r\n22:01:58 | INFO  | c.n.s.SourceMarkCalculator: recalculated 331 of 331 SourceMarks in this batch\r\n22:01:58 | INFO  | c.n.s.SourceMarkCalculator: elapsed time: 1m 7.687s\r\n```","mergeCommitSha":"d68c621eb24453fc198120b9f809f7297678385b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/557","title":"SourcePoints now use batch upload","createdAt":"2022-03-11T05:58:19Z"}
{"state":"Merged","mergedAt":"2023-04-06T20:26:08Z","number":5570,"body":"Now have PR code-level threads for GitLab and Bitbucket.\r\n\r\n<img width=\"1070\" alt=\"Screenshot 2023-04-05 at 23 58 54\" src=\"https://user-images.githubusercontent.com/1798345/230296035-bab4b98f-82a9-4d1c-9977-3e5b5c7e48e9.png\">\r\n","mergeCommitSha":"cc5f0b471cb86eb2c1ef4dbc24fd910abc61dce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5570","title":"Hack abstract SCM threads into existance by ignoring SourceMark fields","createdAt":"2023-04-06T06:57:53Z"}
{"state":"Merged","mergedAt":"2023-04-06T18:29:03Z","number":5571,"body":"- make deployment resilient\r\n- Deply\r\n","mergeCommitSha":"afdb98c86a2e98fe8776d56478ca21c9eb781e28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5571","title":"MakeDeploymentResilient","createdAt":"2023-04-06T17:42:33Z"}
{"state":"Merged","mergedAt":"2023-04-14T05:41:22Z","number":5572,"body":"When IntelliJ changes appearance, update webview theme.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/230457516-da3c1075-d6a3-49c5-99bf-abc424ac62d0.mp4\r\n\r\n","mergeCommitSha":"76ef37deaeffccd942e2fb31aa46b4f51bf7a8f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5572","title":"Update theme on IDE change","createdAt":"2023-04-06T17:53:28Z"}
{"state":"Merged","mergedAt":"2023-04-06T20:56:57Z","number":5573,"body":"When pusher is having trouble dashboard doesn't load. This makes it a high impact service. We just an issue with the pusher deployment and it ended up taking both instances down. \r\n\r\nAdding more pods should make the rollover more gradual.","mergeCommitSha":"66998b94e5add6ca36042c4848214a7a5ec2686a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5573","title":"scale up pusher because it's a high impact service","createdAt":"2023-04-06T20:49:46Z"}
{"state":"Merged","mergedAt":"2023-04-10T16:12:54Z","number":5574,"mergeCommitSha":"f47677a9c060708e94cf5c26dd1b1eae694d93e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5574","title":"Check recommended topics for equality","createdAt":"2023-04-06T20:54:00Z"}
{"state":"Merged","mergedAt":"2023-04-06T21:43:32Z","number":5575,"body":"Scheme looks like this:\r\n\r\n`unblocked://exchange?code=<single-use-code>`","mergeCommitSha":"19206dc176194ac481250f9c665a1c72ecf8c586","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5575","title":"Add custom URL scheme for hub","createdAt":"2023-04-06T21:27:41Z"}
{"state":"Merged","mergedAt":"2023-04-06T21:29:34Z","number":5576,"body":"This reverts commit afdb98c86a2e98fe8776d56478ca21c9eb781e28.\r\n","mergeCommitSha":"b0add7a4dc0621aebb61910681312dc7a256d03d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5576","title":"Revert \"Make helm resilient (#5571)\"","createdAt":"2023-04-06T21:29:28Z"}
{"state":"Merged","mergedAt":"2023-04-10T17:11:03Z","number":5577,"body":"Don't retry channel poll requests.  The channel poller naturally retries every second, and blocking `/modifiedSince` will cause other calls participating in channel polling.\r\n\r\nThis is a very quick fix.  I don't love how we centralize the logic here in BaseAPI, it means testing and mocking this is very challenging.  I think this is an area we should refactor at some point.\r\n\r\nBackground: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1680813844776139","mergeCommitSha":"6ac9acd50731e699d0879cce478d1b87bec5938b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5577","title":"Don't retry channel poll requests","createdAt":"2023-04-06T21:38:30Z"}
{"state":"Merged","mergedAt":"2023-04-21T20:01:52Z","number":5578,"body":"Implementation to follow","mergeCommitSha":"68a9536ea4e25a33fe4b08e0b9cf1061673766b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5578","title":"Add single use code generate/exchange endpoint","createdAt":"2023-04-06T22:05:43Z"}
{"state":"Merged","mergedAt":"2023-04-10T23:51:26Z","number":5579,"body":"* Add filter by topics dropdown to Team Members view\r\n<img width=\"486\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/230510049-afa152cf-935b-4317-ac2b-243c9970f7a2.png\">\r\n\r\n* Add team member side menu with expertise (note: this will eventually include the user ML description)\r\n<img width=\"767\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/230505351-75f6e99c-1a33-453b-b8d9-b550a3e32c76.png\">\r\n","mergeCommitSha":"0996d0fe2b6a7c01a055390e9f772df8b6672a7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5579","title":"Add topics to TeamMemberView","createdAt":"2023-04-06T22:36:20Z"}
{"state":"Merged","mergedAt":"2022-03-12T00:32:00Z","number":558,"body":"Based on conversations from https://www.notion.so/nextchaptersoftware/Resource-Authorization-0126454fb1c74d9ab2df9ff12935e243\r\n\r\nPreviously, we were refreshing token *after* a 401 occurred.\r\n\r\nUpdated to refresh token *before* a 401 with some wiggle room. Currently 5 minute wiggle room.\r\n","mergeCommitSha":"0ccf673941cfb1de91af2d3696b1a8baf79a877e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/558","title":"Update Refresh Logic","createdAt":"2022-03-11T06:45:42Z"}
{"state":"Merged","mergedAt":"2023-04-09T06:13:09Z","number":5580,"body":"Every time a commit is pushed to a GitLab merge request a new version of the\ncommits and diffs in the MR are recorded. The latest version is most interesting\nto us, since the commits in the latest version are the only commits that will be\npersisted in Git, on merge.\n\nSee also:\n - https://docs.gitlab.com/ee/api/merge_requests.html#get-merge-request-diff-versions\n - https://docs.gitlab.com/ee/api/merge_requests.html#get-a-single-merge-request-diff-version","mergeCommitSha":"83ee1654f22f1d0fc01eae07c96f1e01dccab6d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5580","title":"Derive original sourcepoints from GitLab version diffs","createdAt":"2023-04-06T22:50:42Z"}
{"state":"Merged","mergedAt":"2023-04-21T21:48:41Z","number":5581,"mergeCommitSha":"65233f15746aae910ace75deee4500dd9d75d8c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5581","title":"Exchange code implementation","createdAt":"2023-04-06T23:29:10Z"}
{"state":"Merged","mergedAt":"2023-04-10T16:44:03Z","number":5582,"mergeCommitSha":"32e8b011a9eda705d363a4383b7fe970c8832caf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5582","title":"Pinecone and Milvus Vector DB clients","createdAt":"2023-04-07T03:24:57Z"}
{"state":"Merged","mergedAt":"2023-04-08T06:04:53Z","number":5583,"body":"https://github.com/advisories/GHSA-rgv9-q543-rqg4","mergeCommitSha":"835001ca88c79fe8c2a95bbd2c8d36501ea408ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5583","title":"Upgrade Jackson to fix https://github.com/advisories/GHSA-rgv9-q543-rqg4","createdAt":"2023-04-07T18:44:56Z"}
{"state":"Merged","mergedAt":"2023-04-11T20:37:57Z","number":5584,"body":"This only enables creating top-level comment threads for Bitbucket and GitLab. \r\n\r\nFor GitHub, we'll need to convert the existing top-level comments stored as `PullRequestCommentModel` into threads (i.e. a migration) before creating threads from GitHub top-level comments in the `PullRequestIngestionService`. That will come in another PR.","mergeCommitSha":"ba49be912ef7d281c9a607b12e9ec979aed6fc0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5584","title":"Update pull request ingestion to support creating top-level comment threads","createdAt":"2023-04-09T20:30:29Z"}
{"state":"Merged","mergedAt":"2023-04-10T18:10:01Z","number":5585,"mergeCommitSha":"6d0ae5e7f011d86f20893398375b757847487f17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5585","title":"Try refactoring","createdAt":"2023-04-10T17:55:35Z"}
{"state":"Closed","mergedAt":null,"number":5586,"body":"The shape of the stream output changed slightly, so a lot of files had to change trivially.","mergeCommitSha":"cc658cda1e27567388512c9c1abf4584b63f8352","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5586","title":"Use topic push channel in TS clients","createdAt":"2023-04-10T18:32:39Z"}
{"state":"Merged","mergedAt":"2023-04-10T19:47:25Z","number":5587,"mergeCommitSha":"5b92b2623f3e87ef8bd0a14617784f00045b5f6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5587","title":"Add S3 image asset buckets to CSP for upload and connect-src data loading","createdAt":"2023-04-10T19:32:58Z"}
{"state":"Merged","mergedAt":"2023-04-10T20:36:37Z","number":5588,"body":"For the new topics push channel","mergeCommitSha":"a10da5305225b1d577ee179a108ddea5cd31e318","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5588","title":"Return last modified header for getTopics operation","createdAt":"2023-04-10T20:09:56Z"}
{"state":"Merged","mergedAt":"2023-04-10T21:03:48Z","number":5589,"mergeCommitSha":"f0e291c7be50ef1a5bf2b3ff9ebe9b8c4dfb1e49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5589","title":"Diff hunk parser on server to process raw diffs as snippets from SCM","createdAt":"2023-04-10T20:22:56Z"}
{"state":"Merged","mergedAt":"2022-03-11T07:05:01Z","number":559,"mergeCommitSha":"28d90d36c22775e3f4534bb34731a9d05d0542ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/559","title":"Wire up more CLI options","createdAt":"2022-03-11T06:46:21Z"}
{"state":"Merged","mergedAt":"2023-04-11T21:30:58Z","number":5590,"mergeCommitSha":"55cb8470df734db9cde82dcda7cb7974f7b537c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5590","title":"Extract code snippets for Bitbucket code comments","createdAt":"2023-04-10T20:25:50Z"}
{"state":"Closed","mergedAt":null,"number":5591,"mergeCommitSha":"e96b421f6c4759e827c4ccbc06bf8916d0e313f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5591","title":"Add topic summaries","createdAt":"2023-04-10T20:51:56Z"}
{"state":"Merged","mergedAt":"2023-04-12T18:16:38Z","number":5592,"body":"Capture edit and save events in Jetbrains IDEs, so that as users edit and save files, the sourcemarks update.\r\n\r\n* Add a new API to the agent so the IDE can notify the agent when edits and saves happen\r\n* The existing agent machinery causes sourcemarks to update\r\n\r\nThere is a problem with in-memory updates where the sourcemarks are not reliably updating -- as far as I can tell the correct file content is being written.  Might need Richie to look into this.","mergeCommitSha":"7a31065f595213b343bc5bcda2f2e5dbe5c2cdec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5592","title":"Jetbrains: capture editor events for SM processing","createdAt":"2023-04-10T20:57:47Z"}
{"state":"Merged","mergedAt":"2023-04-11T21:26:06Z","number":5593,"body":"Related to https://github.com/NextChapterSoftware/unblocked/pull/5589#issuecomment-1502320689\r\n\r\nLot's of changes here, but I have a good understanding now of what GitHub is doing, and added some tests to validate. There are some result differences relative to the previous implementation, but in all cases that I could find these are improvements in accuracy.","mergeCommitSha":"f5a189a92c4a82c285efe9cb6a4f1caf97e11c02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5593","title":"Use DiffHunkParser for GitHub snippets","createdAt":"2023-04-10T21:19:15Z"}
{"state":"Merged","mergedAt":"2023-04-10T21:52:05Z","number":5594,"body":"We should be using standardized models for search / embeddings for text.","mergeCommitSha":"d7987491e837fd53e677b88bbb662f2b09443a91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5594","title":"Standardize search indexing context for threads / pull requests","createdAt":"2023-04-10T21:31:47Z"}
{"state":"Merged","mergedAt":"2023-04-11T18:29:10Z","number":5595,"body":"Add general support for opening unresolved git file.\r\n<img width=\"1237\" alt=\"CleanShot 2023-04-10 at 16 33 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/231018845-480cfbba-d5f4-426c-aada-0abc844727f5.png\">\r\n\r\n\r\n","mergeCommitSha":"037c1928d832383c5c0b5c4e8e5dbcaf7d9d2a6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5595","title":"[unb-1136] Open unresolved file","createdAt":"2023-04-10T21:32:25Z"}
{"state":"Closed","mergedAt":null,"number":5596,"body":"So that the response is symmetric","mergeCommitSha":"0054758a28213d1e580e9cd8eadc5da7325e8412","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5596","title":"[VERY WIP] Update the topics push channel to take the same query parameters as the getTopics operation","createdAt":"2023-04-10T22:56:43Z"}
{"state":"Merged","mergedAt":"2023-04-11T20:52:25Z","number":5597,"body":"In the IDE, we were showing all the participants of a given PR, resulting in possible dupes (re: identity problem). Instead, filter the participants for the given tab type (we already do this in dashboard).\r\n\r\n","mergeCommitSha":"b8d0d2f1ab2ad2e9fad8ba83c8175b55662709a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5597","title":"Filter discussion participants by tab","createdAt":"2023-04-11T00:22:00Z"}
{"state":"Merged","mergedAt":"2023-04-11T01:47:12Z","number":5598,"body":"This now allows us for to store all of our embeddings in our vector database.\r\nWill have to figure out how we want to namespace the data.\r\n\r\nWill enable this in dev/prod soon.","mergeCommitSha":"9e73377610c9baf15bb8f6a1d247479dc3daa91a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5598","title":"Add search insight embeddings","createdAt":"2023-04-11T01:05:59Z"}
{"state":"Merged","mergedAt":"2023-04-11T01:24:31Z","number":5599,"mergeCommitSha":"e872c54791c78f025c3e06b3d897cbd075f8ce19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5599","title":"Disable powerml","createdAt":"2023-04-11T01:24:13Z"}
{"state":"Merged","mergedAt":"2022-01-18T19:20:24Z","number":56,"body":"### Changes\r\n- splits the Web and VSCode workflows, which are different\r\n- unsplits the main and PR workflows, which should be near identical\r\n\r\n\r\n### Motivation\r\n- lots of CI failures on main: https://github.com/Chapter2Inc/codeswell/actions?query=branch%3Amain+is%3Afailure\r\n- Confusingly named duplicate workflows: https://github.com/Chapter2Inc/codeswell/actions\r\n\r\n<img width=\"312\" alt=\"Screen Shot 2022-01-18 at 09 05 22\" src=\"https://user-images.githubusercontent.com/1798345/149984339-42cbebb2-6ceb-49cc-8643-8d05cf06b1a3.png\">\r\n","mergeCommitSha":"2e1b8d48432b9598c1e8fe9f0c883860dcbe78fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/56","title":"Refactor GitHub workflows","createdAt":"2022-01-18T07:53:39Z"}
{"state":"Merged","mergedAt":"2022-03-11T17:20:58Z","number":560,"body":"No longer used. We can use the `putSourcePoints` operation to do the same thing.","mergeCommitSha":"daa12ef27abfab8fb0b4e4851d3b578abfb6b4b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/560","title":"Remove putSourcePoint operation","createdAt":"2022-03-11T16:31:59Z"}
{"state":"Merged","mergedAt":"2023-04-11T03:03:24Z","number":5600,"body":"Add fewshot case examples\r\n","mergeCommitSha":"b206aa96546adc4de305b44a5eda2672cd77fd5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5600","title":"Fix openai prompts for casing","createdAt":"2023-04-11T02:59:57Z"}
{"state":"Merged","mergedAt":"2023-04-11T03:32:56Z","number":5601,"mergeCommitSha":"a4e7fd25d728dcdc618dbc9d807db0e614f15570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5601","title":"Admin: Button to reingest a PR","createdAt":"2023-04-11T03:24:49Z"}
{"state":"Merged","mergedAt":"2023-04-11T03:33:07Z","number":5602,"mergeCommitSha":"0e3360786b3da071ec2f583cb4a9e8de5913fb22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5602","title":"GitHub unsuspend event should trigger an idempotent installation handler","createdAt":"2023-04-11T03:25:47Z"}
{"state":"Merged","mergedAt":"2023-04-11T16:14:16Z","number":5603,"mergeCommitSha":"403e2e4056c545c63b614e4ed636f9f5b8383a49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5603","title":"Sourcemark engine handles short SHAs","createdAt":"2023-04-11T06:41:52Z"}
{"state":"Merged","mergedAt":"2023-04-11T21:32:46Z","number":5604,"body":"We were showing all workspaces for a user, whether they were an `owner`,\n`collaborator`, or `member`. The problem is that only owners can list\nworkspace members and list repos and install webhooks, so everything breaks\nunless the user is an owner.\n\nThis change makes it impossible to onboard a Bitbucket Workspace without the\nfirst user being an owner.","mergeCommitSha":"2f97a5f64f8dc5ae2dc6956b3ad4753b989562ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5604","title":"Bitbucket connect page shows Workspaces where user is an Owner","createdAt":"2023-04-11T07:14:15Z"}
{"state":"Merged","mergedAt":"2023-04-11T20:15:22Z","number":5605,"body":"Modified the rule to deal with this noisy false positive alarm:\r\nhttps://chapter2global.slack.com/archives/C04M9AH4GA0/p1681215490589829","mergeCommitSha":"80d499dcc1e362e42939a49915871c971f507843","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5605","title":"this should take care of setuid false positives in dev","createdAt":"2023-04-11T12:37:17Z"}
{"state":"Merged","mergedAt":"2023-04-11T17:06:10Z","number":5606,"body":"Now comparing DEV and LOCAL:\n```\ndiff vscode/webpack.{dev,local}.js\n```\n\nShows that they differ only where expected:\n```\n16c16\n<                 ENVIRONMENT: JSON.stringify('dev'),\n---\n>                 ENVIRONMENT: JSON.stringify('local'),\n20d19\n<\n29c28\n<                 ENVIRONMENT: JSON.stringify('dev'),\n---\n>                 ENVIRONMENT: JSON.stringify('local'),\n```","mergeCommitSha":"43a5f7e16b5ce423b33c2938c07cb50cde0bb305","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5606","title":"Fix VSCode debugging breakpoints","createdAt":"2023-04-11T16:29:43Z"}
{"state":"Merged","mergedAt":"2023-04-11T20:40:23Z","number":5607,"mergeCommitSha":"6e7f353d7b8c1a3f7524b362b75af30782ee5f1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5607","title":"Add Hub restart resilience to installer in case macOS freaks out","createdAt":"2023-04-11T19:32:04Z"}
{"state":"Merged","mergedAt":"2023-04-11T20:53:15Z","number":5608,"body":"Allows these types to be used in kotlin.","mergeCommitSha":"f6ff7bc3718b9170bebf5af85f5ae0e7642f6795","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5608","title":"Move PR aggregate types into OpenAPI YML extras","createdAt":"2023-04-11T20:01:06Z"}
{"state":"Merged","mergedAt":"2023-04-11T20:06:25Z","number":5609,"body":"We can't inordinately sample 60000 documents.\r\nWe need to have a max cap of documents to process.","mergeCommitSha":"22cda67e85e4dc85f1d9ea8b83ece4d84c387085","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5609","title":"Sample topic data better","createdAt":"2023-04-11T20:06:15Z"}
{"state":"Merged","mergedAt":"2022-03-11T23:16:48Z","number":561,"body":"Fixes DiscussionThread Story. Cyclical import with the index.ts\r\n\r\nSlight updates to thread view UI. Mainly moving send button to right.","mergeCommitSha":"40835d3e8fa590174b2ac8f761b701f67cba764d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/561","title":"Fix DiscussionThread Story","createdAt":"2022-03-11T17:13:36Z"}
{"state":"Merged","mergedAt":"2023-04-25T23:58:16Z","number":5610,"body":"If the discussion has participants that are `!hasAccount`, show banner to allow affordance to invite them to Unblocked.\r\n\r\n<img width=\"556\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-782687b4-ec9c-45bb-92a2-87f41af7999b.png\">\r\n\r\n<img width=\"850\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-21b48367-6b4f-4083-9ae8-23bfb40898f0.png\">\r\n","mergeCommitSha":"3065367fae24b1be51b62c68489c13be7bd34533","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5610","title":"Add discussion level invite to Unblocked banner ","createdAt":"2023-04-11T20:36:12Z"}
{"state":"Merged","mergedAt":"2023-04-19T18:38:06Z","number":5611,"body":"Part of product invite flows:\r\n\r\nWe want to add an affordance for users to invite team members in their recommended network (if they aren't on Unblocked). \r\n<img width=\"357\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-5f7cbf0e-eadd-427b-b265-a2bed1e09e9a.png\">\r\n<img width=\"460\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/231285754-02b7232b-5586-4731-a83e-490939830c89.png\">\r\n\r\nWhen the user has sent the invites, we want the UI to disappear so there needs to be a flag to make this possible. \r\n\r\n~The end goal is for the flag to reset itself (after some indeterminate amount of time) given that the TeamMember still has users in their network that require inviting. Then the clients will just read off of this flag and render the UI as normal.~\r\n\r\nDashboard: https://github.com/NextChapterSoftware/unblocked/pull/5551","mergeCommitSha":"d74597e9050af35f44292b871a485f484b154555","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5611","title":"Get recommended team member invites based on your network connections","createdAt":"2023-04-11T20:51:11Z"}
{"state":"Merged","mergedAt":"2023-04-11T23:35:03Z","number":5612,"mergeCommitSha":"a98e62006b26ebc77648ca35fcc923637db3ff84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5612","title":"Admin: Add PR search and fix PR bugs","createdAt":"2023-04-11T23:23:30Z"}
{"state":"Merged","mergedAt":"2023-04-17T17:35:00Z","number":5613,"body":"Setup basic dialog infrastructure from Agent -> IntelliJ\r\n\r\nImplemented Archive & Restore dialogs\r\n\r\n<img width=\"1058\" alt=\"CleanShot 2023-04-11 at 16 24 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/231309737-28a0ad53-d4b5-470b-8e70-889d3d16e7b7.png\">\r\n","mergeCommitSha":"f1937ae1e90f997e77b88c57c41cbbe9b4061a26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5613","title":"[UNB-1071] Setup IntelliJ dialog infrastructure","createdAt":"2023-04-11T23:25:50Z"}
{"state":"Merged","mergedAt":"2023-04-12T04:39:20Z","number":5614,"body":"Ensures that the connected team's resources are refreshed immediately.","mergeCommitSha":"eb48ee9d57885c385dde0ed98a6c00dfa04cd95b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5614","title":"Trigger SCM Team Maintenance job after SCM installation connection","createdAt":"2023-04-11T23:44:27Z"}
{"state":"Merged","mergedAt":"2023-04-12T16:15:44Z","number":5615,"body":"Noticed because when I logged in as GitLab we displayed my Bitbucket profile.\r\nWe were choosing the profile information for the user at random.\r\n\r\nThis is an example of a terrible general pattern that we should develop a\r\nlint rule to avoid use:\r\n```js\r\nperson?.memberships[0]\r\n```\r\n\r\nIn general, do not use:\r\n```js\r\narray[0]\r\n```","mergeCommitSha":"c972a78a65e1a5fd47d1ab83de29374903e36fdf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5615","title":"Fix bug in home profile when user has multiple SCM memberships","createdAt":"2023-04-12T04:37:33Z"}
{"state":"Merged","mergedAt":"2023-04-12T21:21:50Z","number":5616,"mergeCommitSha":"9884fb73b954c8abd55a5cd522d7c22906327b30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5616","title":"Address cases where we choose a random member for a person","createdAt":"2023-04-12T06:32:31Z"}
{"state":"Closed","mergedAt":null,"number":5617,"body":"Update some links to include label. Primarily used for SCM links that differ based on provider.","mergeCommitSha":"be740750eef8f62f3690077b1c68d2b72f5dd74f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5617","title":"Update message links","createdAt":"2023-04-12T16:29:37Z"}
{"state":"Merged","mergedAt":"2023-04-12T18:13:42Z","number":5618,"mergeCommitSha":"940bcee9694e8d0ca0a4891377b7efb604c359b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5618","title":"Add topic summary descriptions","createdAt":"2023-04-12T17:45:49Z"}
{"state":"Merged","mergedAt":"2023-04-13T16:40:02Z","number":5619,"body":"Update hard-coded context menus to be dynamic based on provider.\r\n\r\nRequired injecting provider into aggregate models.","mergeCommitSha":"6cc643c3e4369ca04c8d09a6ebe09923fbe9feea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5619","title":"[unb 1145] Dynamic name by injecting provider","createdAt":"2023-04-12T18:06:50Z"}
{"state":"Merged","mergedAt":"2022-03-12T00:29:39Z","number":562,"body":"The way we were doing it before meant that we weren't correctly updating the refresh token","mergeCommitSha":"1d0a06fced06e10ce02bf886df1f07f84aa3edb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/562","title":"Refresh the refresh token","createdAt":"2022-03-11T17:30:16Z"}
{"state":"Merged","mergedAt":"2023-04-12T18:40:51Z","number":5620,"mergeCommitSha":"2e2c548f060a8c1e2ee0eb5fe8a0fb683a7e5798","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5620","title":"Minor rename","createdAt":"2023-04-12T18:21:03Z"}
{"state":"Merged","mergedAt":"2023-04-12T19:32:55Z","number":5621,"body":"Make consistent with server.\nhttps://github.com/NextChapterSoftware/unblocked/blob/7a31065f595213b343bc5bcda2f2e5dbe5c2cdec/projects/clients/client-scm/src/main/kotlin/com/nextchaptersoftware/scm/Scm.kt#L57","mergeCommitSha":"c35ec1c2d93a3a313da2c9aaf82328de3e6378a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5621","title":"Rename GitLab Enterprise","createdAt":"2023-04-12T18:21:05Z"}
{"state":"Merged","mergedAt":"2023-04-12T21:33:45Z","number":5622,"mergeCommitSha":"7e8bdfbc72a68440c1215ff27b6e42e3fb681f95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5622","title":"Abstract search phase 1","createdAt":"2023-04-12T19:25:21Z"}
{"state":"Closed","mergedAt":null,"number":5623,"mergeCommitSha":"fa4d2cd22fd71299b10854180b618a4d83806488","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5623","title":"[WIP] Add lib-jira","createdAt":"2023-04-12T20:05:52Z"}
{"state":"Merged","mergedAt":"2023-04-13T06:55:20Z","number":5624,"body":"Not used yet, but will be in subsequent PRs.","mergeCommitSha":"f12fbfba44b30098cf31f1eee806c65bd3d52255","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5624","title":"Expose installation ID allowing API to talk about unique installations","createdAt":"2023-04-12T20:51:50Z"}
{"state":"Merged","mergedAt":"2023-04-13T19:10:00Z","number":5625,"body":"Add Search Everywhere provider for JetBrains IDEs.  Results show up under an 'Unblocked' tab, and under 'All'.  Fixes https://linear.app/unblocked/issue/UNB-1121/search-ui-→-integrate-with-intellij-search-provider\r\n\r\n- Implement SearchEverywhereContributor\r\n- Add agent API for searching.  This returns a stream of values, right now only a single value is returned, but if we wanted to do additional searching we could return additional streamed results\r\n- Add PR icons to jetbrains project\r\n\r\n<img width=\"956\" alt=\"Screenshot 2023-04-12 at 2 58 08 PM\" src=\"https://user-images.githubusercontent.com/2133518/231594938-fb08cc0e-314e-4b43-985e-e4b30f08779c.png\">\r\n","mergeCommitSha":"0b4b59dcb68df8aca1979bdeb8fc8049949097c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5625","title":"JetBrains search","createdAt":"2023-04-12T21:59:42Z"}
{"state":"Merged","mergedAt":"2023-04-13T06:56:25Z","number":5626,"body":"Flow still works with new APIs, we're just passing a null list of repos to `connectInstallationV3`.\r\n\r\nFixes: UNB-1155\r\nFixes: UNB-1156","mergeCommitSha":"7cc293fd4e90eea1aa18c1c207fe8cd3a59be0a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5626","title":"Bitbucket Repo Selection: API to list available repos and connect","createdAt":"2023-04-12T22:17:04Z"}
{"state":"Merged","mergedAt":"2023-04-12T23:10:06Z","number":5627,"body":"<img width=\"551\" alt=\"CleanShot 2023-04-12 at 15 56 20@2x\" src=\"https://user-images.githubusercontent.com/858772/231603978-aa865c48-b5f6-490a-a584-5ca4496a895a.png\">\r\n","mergeCommitSha":"490c04c173319b510db1d7287f9cda57a70216aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5627","title":"Fixes Update Markdown layout when the width of the Markdown is small","createdAt":"2023-04-12T22:55:48Z"}
{"state":"Merged","mergedAt":"2023-04-13T00:31:11Z","number":5628,"mergeCommitSha":"b0464c12100322562d6cbf945e470482a58f8295","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5628","title":"Add serach indexer for openai","createdAt":"2023-04-12T23:05:57Z"}
{"state":"Merged","mergedAt":"2023-04-13T17:05:05Z","number":5629,"body":"Have `ProviderIcon` handle all scm icons. ","mergeCommitSha":"f228d4ed4b0fb70a8d083c931ad18bb9a461bb9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5629","title":"Update ProviderIcon for broad use","createdAt":"2023-04-12T23:13:52Z"}
{"state":"Merged","mergedAt":"2022-04-05T19:40:30Z","number":563,"mergeCommitSha":"ca2f13496c390a015a16ce29c16157b43813d696","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/563","title":"Merge server sourcepoints with local sourcepoints on each round","createdAt":"2022-03-11T17:30:54Z"}
{"state":"Merged","mergedAt":"2023-04-13T02:43:35Z","number":5630,"mergeCommitSha":"52670b95784e679ea158debc28b9eb3874e993a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5630","title":"Generalize openai prompts","createdAt":"2023-04-13T02:08:36Z"}
{"state":"Merged","mergedAt":"2023-04-13T18:12:29Z","number":5631,"mergeCommitSha":"d3cd117e0c1d56c90776fee5b5d3d34926a2afb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5631","title":"Add ability to toggle search embedding","createdAt":"2023-04-13T17:24:41Z"}
{"state":"Merged","mergedAt":"2023-04-13T23:00:35Z","number":5632,"mergeCommitSha":"d400a1862ee7d071d9a6dd668fbfe6a99a40eea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5632","title":"Semantic search part 2","createdAt":"2023-04-13T18:25:16Z"}
{"state":"Merged","mergedAt":"2023-04-13T20:39:05Z","number":5633,"body":"Operation changes:\n- modifies `listInstallationRepos` to return `InstallationAndRepos` object to drive stateless install flow\n- adds `patchInstallation` to install additional repos for an installation\n- adds `deleteRepo` operation to uninstall a repo","mergeCommitSha":"b9d1fa5d4b84f96de529bd1d316140248fc5ab6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5633","title":"[BREAKS API ON MAIN] Ability to install/uninstall repos and support stateless connect install flow","createdAt":"2023-04-13T18:42:11Z"}
{"state":"Merged","mergedAt":"2023-04-13T20:48:07Z","number":5634,"body":"Preliminary PR for integrating hub with JetBrains IDEs","mergeCommitSha":"4ab5653491a32805db3238ffbc2b93be532e037d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5634","title":"Move Hub API into shared","createdAt":"2023-04-13T18:55:26Z"}
{"state":"Merged","mergedAt":"2023-04-13T20:11:47Z","number":5635,"body":"Was changed in #5631\r\n","mergeCommitSha":"5842ccd6c3fd587a96e8f7636db83a2925db30be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5635","title":"Update client assets to main","createdAt":"2023-04-13T19:35:16Z"}
{"state":"Merged","mergedAt":"2023-04-13T20:00:00Z","number":5636,"mergeCommitSha":"a070af41293f15247b3776ef7117d69ee07216c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5636","title":"Remove refresh team error popup","createdAt":"2023-04-13T19:48:24Z"}
{"state":"Merged","mergedAt":"2023-04-14T00:58:29Z","number":5637,"body":"<img width=\"627\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/231859531-94b55b46-5643-4acb-975c-884d11900fcb.png\">\r\n\r\n\r\n* Update fontawesome libs to latest version\r\n* Update login UI to new designs\r\n* If only a single public button is made available, we show the horizontal button style:\r\n<img width=\"685\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/231901544-a2bbe68d-394d-40e1-86a7-18a9babd6a5a.png\">\r\n","mergeCommitSha":"149c993e71cc2757667d72ce0becb6754a4ca6db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5637","title":"Update login UI","createdAt":"2023-04-13T19:56:58Z"}
{"state":"Merged","mergedAt":"2023-04-18T18:33:44Z","number":5638,"body":"Fixes UNB-1161\r\n\r\n* Continue attempt to connect to hub\r\n* On startup, pull login token from hub\r\n* As hub logs in/out, reflect that in IntelliJ\r\n* Handle thread and PR actions sent from hub\r\n\r\nSlight change to the hub prefs to generalize the first 'Other Insights in' option:\r\n<img width=\"532\" alt=\"Screenshot 2023-04-13 at 3 59 05 PM\" src=\"https://user-images.githubusercontent.com/2133518/231901172-084ca91e-37ad-43ab-aed0-28250ee61043.png\">\r\n\r\n","mergeCommitSha":"b9599a6b4382a5c20ef66021b9dfb506d746d9a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5638","title":"JetBrains <-> Hub Integration","createdAt":"2023-04-13T22:46:58Z"}
{"state":"Merged","mergedAt":"2023-04-13T23:25:54Z","number":5639,"body":"Changes\r\n- implement `patchInstallation`\r\n- implement `listInstallationRepos` properly\r\n- remove implementation for deprecated `legacyListInstallations`\r\n- remove implementation for deprecated `connectInstallation`\r\n\r\n## Example of `listInstallationRepos`\r\n```json\r\n{\r\n  \"installation\": {\r\n    \"avatarUrl\": \"https://bitbucket.org/workspaces/getunblocked/avatar/?ts=**********\",\r\n    \"displayName\": \"Unblocked\",\r\n    \"fullPath\": \"getunblocked\",\r\n    \"htmlUrl\": \"https://bitbucket.org/getunblocked/\",\r\n    \"installationId\": \"{79f8f678-37d1-4d28-8ce1-e33d0f063c81}\",\r\n    \"isInstalled\": false,\r\n    \"provider\": \"bitbucket\"\r\n  },\r\n  \"reposInstalled\": [],\r\n  \"reposNotInstalled\": [\r\n    {\r\n      \"fullName\": \"getunblocked/sample\",\r\n      \"ownerName\": \"getunblocked\",\r\n      \"repoName\": \"sample\",\r\n      \"webUrl\": \"https://bitbucket.org/getunblocked/sample\",\r\n      \"httpUrl\": \"https://bitbucket.org/getunblocked/sample.git\",\r\n      \"sshUrl\": \"ssh://bitbucket.org/getunblocked/sample.git\",\r\n      \"externalId\": \"{70032227-068a-4683-bd23-cc0783d9f3df}\",\r\n      \"scpUrl\": \"*****************:getunblocked/sample.git\"\r\n    },\r\n    {\r\n      \"fullName\": \"getunblocked/chatgpt-retrieval-plug\",\r\n      \"ownerName\": \"getunblocked\",\r\n      \"repoName\": \"chatgpt-retrieval-plug\",\r\n      \"webUrl\": \"https://bitbucket.org/getunblocked/chatgpt-retrieval-plug\",\r\n      \"httpUrl\": \"https://bitbucket.org/getunblocked/chatgpt-retrieval-plug.git\",\r\n      \"sshUrl\": \"ssh://bitbucket.org/getunblocked/chatgpt-retrieval-plug.git\",\r\n      \"externalId\": \"{942f7e3e-4d98-4c5a-897f-3c77cadb1d17}\",\r\n      \"scpUrl\": \"*****************:getunblocked/chatgpt-retrieval-plug.git\"\r\n    }\r\n  ]\r\n}\r\n```\r\n","mergeCommitSha":"0360d92703753ac647088d9306c5eaa75e3426ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5639","title":"Implement install APIs","createdAt":"2023-04-13T22:57:47Z"}
{"state":"Merged","mergedAt":"2022-03-15T17:55:16Z","number":564,"mergeCommitSha":"85bfeafd97cf9b3c60061dec19b6f2d943ea9f5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/564","title":"Add team-based APIs","createdAt":"2022-03-11T20:00:51Z"}
{"state":"Merged","mergedAt":"2023-04-13T23:18:22Z","number":5640,"mergeCommitSha":"e9dd4c7d10b4594d1d14c8e1953faf38b7597fd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5640","title":"REduce summary temprerature","createdAt":"2023-04-13T23:18:14Z"}
{"state":"Merged","mergedAt":"2023-04-13T23:48:22Z","number":5641,"mergeCommitSha":"72dc27c4fb45fe3fac9b9d8d97dc450580e7a13d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5641","title":"Install flow lists GitLab user repos","createdAt":"2023-04-13T23:32:33Z"}
{"state":"Merged","mergedAt":"2023-04-13T23:50:00Z","number":5642,"mergeCommitSha":"6c2bc0d630947ff213ae647d52c4ed9101c27d4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5642","title":"Reduce prompt input","createdAt":"2023-04-13T23:48:34Z"}
{"state":"Merged","mergedAt":"2023-04-14T00:09:20Z","number":5643,"mergeCommitSha":"826e3aab8ad73284a8af89c31a9f7471e01cf0ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5643","title":"Fix listInstallationRepos installation ID","createdAt":"2023-04-14T00:08:45Z"}
{"state":"Merged","mergedAt":"2023-04-14T06:06:59Z","number":5644,"body":"Now we construct the sub-heading according to this schema, where the fileName is optional:\r\n\r\n- Top-level comment PR thread:\r\n\r\n      PR #{prNumber}\r\n\r\n\r\n- Code-level comment PR thread:\r\n\r\n      PR #{prNumber} · {fileName}\r\n\r\n## Before\r\n<img width=\"520\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/231947210-3762a325-2199-4c9a-ad51-8a62716cd86c.png\">\r\n\r\n\r\n## After\r\n<img width=\"525\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/231947331-ffc18911-cd30-420b-9723-51efe55ada2d.png\">\r\n","mergeCommitSha":"6164586312e39268f2294e983aad4ee8d1baf6ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5644","title":"Hub: Top-level PR comment sub-heading says \"Unknown code reference\"","createdAt":"2023-04-14T00:35:06Z"}
{"state":"Merged","mergedAt":"2023-04-14T02:07:01Z","number":5645,"mergeCommitSha":"7657c84d762f508bb92d7e1e367580e6308e949e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5645","title":"Try again","createdAt":"2023-04-14T02:05:51Z"}
{"state":"Merged","mergedAt":"2023-04-14T03:13:59Z","number":5646,"mergeCommitSha":"40be330956a5916e83e751f97b726ffa20805b1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5646","title":"Fix again","createdAt":"2023-04-14T03:13:01Z"}
{"state":"Merged","mergedAt":"2023-04-19T22:48:50Z","number":5647,"body":"### Login\r\nhttps://user-images.githubusercontent.com/858772/232643987-d80c7e51-a9c1-4072-935d-df3711d53c78.mp4\r\n\r\n### Allow Notifications\r\nhttps://user-images.githubusercontent.com/858772/232644239-e1ef5735-c6ee-4e14-aafb-4f7cd2303f3b.mp4\r\n\r\n### Notifications Enabled\r\nhttps://user-images.githubusercontent.com/858772/232644383-21366002-fa30-42de-b26d-8bd694786cbd.mp4\r\n\r\n### IDE Selection (VSCode)\r\nhttps://user-images.githubusercontent.com/858772/232644850-a3239655-cfb8-40df-a7b8-aa81c40fb239.mp4\r\n\r\n### IDE Selection (IntelliJ)\r\nhttps://user-images.githubusercontent.com/858772/232645041-87609a02-0e42-45ef-b300-747c229fb8d4.mp4\r\n\r\n### IDE Selection (Single VSCode and Single Jetbrains)\r\nhttps://user-images.githubusercontent.com/858772/232645303-dba535a4-ba0e-4dac-8c3a-557fbfbc4927.mp4\r\n\r\n### IDE Selection (Multiple VSCodes)\r\nhttps://user-images.githubusercontent.com/858772/232645714-c95af5f6-f31b-471b-ad36-07c619847e34.mp4\r\n\r\n### IDE Selection (Multiple Jetbrains)\r\nhttps://user-images.githubusercontent.com/858772/232646259-5bf5f8a2-3644-4bce-b140-3b9ab7db9127.mp4\r\n\r\n### IDE Selection (Multiple IDEs)\r\nhttps://user-images.githubusercontent.com/858772/232646532-dcf581aa-7b64-49e4-82e4-0876dcfe786a.mp4\r\n\r\n### Tour\r\nhttps://user-images.githubusercontent.com/858772/232646997-e74ed3dc-7d0f-4ef4-a28b-2710adf3812c.mp4\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n","mergeCommitSha":"60039ac7fe2ef4275b819b4ee73c6a41328fbfe8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5647","title":"New onboarding flow","createdAt":"2023-04-14T06:07:34Z"}
{"state":"Merged","mergedAt":"2023-04-14T06:15:40Z","number":5648,"mergeCommitSha":"d791a5d9a3f349f84fe981749e2acb33dd0c1abf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5648","title":"Fix embedding generation","createdAt":"2023-04-14T06:15:34Z"}
{"state":"Merged","mergedAt":"2023-04-16T05:05:00Z","number":5649,"body":"This adds support for rendering Bitbucket @-mentions in markdown.\r\n\r\nHowever, there is still follow on work to render @-mentions in titles and previews.","mergeCommitSha":"99ed81cc32f03d91eff44fcb7c5119f4aa554d2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5649","title":"Resolve Bitbucket @-mentions to the user display name in PR comments","createdAt":"2023-04-14T07:39:37Z"}
{"state":"Merged","mergedAt":"2022-03-11T22:14:57Z","number":565,"body":"- Converted existing TCP probes to HTTP on __health\r\n- Added a startup probe\r\n- Added SERVICE_GIT_SHA env var\r\n- Regenerated base charts for pusher and api services to include changes mentioned above. ","mergeCommitSha":"9ed8007b54df58fd40c234d17a915ae035fb492d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/565","title":"Convert service probes to HTTP","createdAt":"2022-03-11T21:59:08Z"}
{"state":"Merged","mergedAt":"2023-04-14T14:48:52Z","number":5650,"body":"Reverts NextChapterSoftware/unblocked#5637","mergeCommitSha":"6f1fd57f02212cfc8a8a0939ba99435e90d99185","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5650","title":"Revert \"Update login UI\"","createdAt":"2023-04-14T14:44:11Z"}
{"state":"Merged","mergedAt":"2023-04-14T18:52:02Z","number":5651,"body":"Single login button was missing click handler.","mergeCommitSha":"11401a971f70c85b457b7e19cdbceada2f3e7504","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5651","title":"Fix login button","createdAt":"2023-04-14T15:30:35Z"}
{"state":"Merged","mergedAt":"2023-04-14T18:51:51Z","number":5652,"body":"Fix Create Insight issues from demo Fridays.\r\n\r\n1. Merge issue where typescript types were invalid... Not sure how builds did *not* catch this\r\n2. Create Insight gutter icon renderer was not being disposed properly.","mergeCommitSha":"6743210b4802d06d691811b8a2e07f861816ef4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5652","title":"Fix create insight intelli j","createdAt":"2023-04-14T17:34:16Z"}
{"state":"Merged","mergedAt":"2023-04-17T22:01:19Z","number":5653,"body":"Add Repo Selector during web onboarding..\r\n\r\n<img width=\"1189\" alt=\"CleanShot 2023-04-14 at 11 17 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/232125314-49b2cd2a-5d35-4a40-a329-ad05745f5aff.png\">\r\n","mergeCommitSha":"75f15128472da53c848994790ae2490ce4f5eafe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5653","title":"[unb 1153] bitbucket repo selection UI to selects","createdAt":"2023-04-14T18:18:18Z"}
{"state":"Merged","mergedAt":"2023-04-14T20:06:57Z","number":5654,"body":"It's very sad, but this has to go. Just witnessed the pulsing icon responsible for pinning app CPU to 50%","mergeCommitSha":"f8e07b3479b0ed296a5331e834a5f57f3af3ecac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5654","title":"Drop pulsing Upgrade indicator on menubar icon","createdAt":"2023-04-14T18:47:30Z"}
{"state":"Closed","mergedAt":null,"number":5655,"body":"So that we don't double count as we migrate GitHub TLCs to threaded TLCs. Only to be merged once migration is done.","mergeCommitSha":"b3c991d8f9143ae432217ce54c816b3dac50b198","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5655","title":"Dont include PullRequestComments and PullRequestReviews in count","createdAt":"2023-04-14T18:59:31Z"}
{"state":"Closed","mergedAt":null,"number":5656,"body":"Just some prep work in anticipation of migrating GitHub TLCs (`PullRequestCommentModel` and `PullRequestReviewModel`) to threaded TLCs. \r\n\r\nThis change skips going out to the database to retrieve `PullRequestCommentModel`s and `PullRequestReviewModel`s if the presence of threaded TLCs is detected. This is so that we don't return duplicate TLC comments during the migration.","mergeCommitSha":"29641c7c74a59f8c7b0307de16d83c94e931f3b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5656","title":"Dont query db for PullRequestComments and PullRequestReviews if theyre threaded","createdAt":"2023-04-14T19:11:07Z"}
{"state":"Merged","mergedAt":"2023-04-14T21:50:29Z","number":5657,"body":"As demoed this morning.","mergeCommitSha":"f0fb1642ba3c4b550be0b62bc9cf84c58e11d6ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5657","title":"Fix dashboard URLs in local environment","createdAt":"2023-04-14T19:48:32Z"}
{"state":"Merged","mergedAt":"2023-04-14T21:04:30Z","number":5658,"body":"Usually this takes less than 2 min.\n\nWe should fast fail, rather than waiting for the entire job to timeout.\n\nSee also:\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1681503106692759","mergeCommitSha":"dd9813c4d440a5141a08c341140deb0d9b549b87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5658","title":"Set a timeout for \"Set up SDK\" step in the services workflow","createdAt":"2023-04-14T20:51:23Z"}
{"state":"Closed","mergedAt":null,"number":5659,"body":"This hostname is a DNS alias for `localhost`:\n```\nlocalhost.proxyman.io\n```\n\nSee also\nhttps://docs.proxyman.io/troubleshooting/couldnt-see-any-request-from-localhost-server#3.-solutions","mergeCommitSha":"e39410f4b8f8504b90946c639fa3833280b834b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5659","title":"Route localhost traffic through an external address that proxyman can intercept","createdAt":"2023-04-14T20:56:57Z"}
{"state":"Open","mergedAt":null,"number":566,"body":"![image](https://user-images.githubusercontent.com/********/157976946-ad58c2ab-b3fb-4220-8e28-8242406abe46.png)\r\n","mergeCommitSha":"0e78a2586cc2c5093dbdd68f8b07fe4aadf8c323","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/566","title":"Icon color fixes","createdAt":"2022-03-11T22:04:25Z"}
{"state":"Merged","mergedAt":"2023-04-17T17:03:21Z","number":5660,"body":"We'll just use `/download`.\r\n\r\nThis saves us a huge amount on the landing page bundle size, as we were bundling the `unblocked-download.svg` as an inline image in the bundle, which is silly.","mergeCommitSha":"259cc88a2f89d5d118cd94288569d0f888eed9dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5660","title":"Remove `/getunblocked` landing page","createdAt":"2023-04-14T21:55:17Z"}
{"state":"Merged","mergedAt":"2023-04-15T15:22:17Z","number":5661,"mergeCommitSha":"ef0b27dccf08f1688c4aed89f69a671c9f3180c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5661","title":"REmove model endpointws","createdAt":"2023-04-15T15:21:57Z"}
{"state":"Merged","mergedAt":"2023-04-16T03:16:47Z","number":5662,"body":"Ugly static HTML files.  This will test that they actually end up at the right place, and that our web server is configured to serve them correctly.","mergeCommitSha":"1b9462db7581d164bd49a6116b6f657dfe349715","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5662","title":"Deploy privacy policy and ToS","createdAt":"2023-04-16T02:47:09Z"}
{"state":"Merged","mergedAt":"2023-04-17T16:12:10Z","number":5663,"body":"Will wait until this is in before merging: https://github.com/NextChapterSoftware/unblocked/pull/5662\r\n\r\nAdd links to ToS and privacy policy in login page.\r\n\r\n<img width=\"1592\" alt=\"Screenshot 2023-04-15 at 8 01 46 PM\" src=\"https://user-images.githubusercontent.com/2133518/232264010-83eddb04-bcf6-42c1-baee-b649913446ba.png\">\r\n","mergeCommitSha":"dd0ab08cbcb46bad7b97f26d2527e1a47c7bca56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5663","title":"Login UI link to ToS and privacy pages","createdAt":"2023-04-16T03:03:42Z"}
{"state":"Merged","mergedAt":"2023-04-16T03:52:48Z","number":5664,"body":"Temporary change to prevent showing duplicate top-level comments in GitHub pull requests in the UI while we migrate them to threads.","mergeCommitSha":"2491abb1b677566f65b0e1777f9f1521055e4fba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5664","title":"Hide threaded TLCs for GitHub and GitHubEnterprise","createdAt":"2023-04-16T03:17:11Z"}
{"state":"Merged","mergedAt":"2023-04-17T21:26:04Z","number":5665,"body":"This reverts commit 2491abb1b677566f65b0e1777f9f1521055e4fba.","mergeCommitSha":"5cab091eb790769bd6739e680cfeb9ccaf0964c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5665","title":"Revert \"Hide threaded TLCs for GitHub and GitHubEnterprise (#5664)\"","createdAt":"2023-04-16T04:03:40Z"}
{"state":"Merged","mergedAt":"2023-04-17T05:51:27Z","number":5666,"body":"This change declares a restricted access service to gate access to DEV based on provider host, org, and user.\r\n\r\nAnother PR will follow to consume the restricted access service in places where we create team and user accounts.","mergeCommitSha":"fece318ef2da6cd5ec62c3b626fc13e59e8031e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5666","title":"Restrict access to DEV","createdAt":"2023-04-16T04:32:56Z"}
{"state":"Merged","mergedAt":"2023-04-18T04:09:13Z","number":5667,"body":"Creates threads from top-level comments during bulk ingestion and from webhooks received.","mergeCommitSha":"e8019bd8e810ee17e55689ea97798b782c0b6021","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5667","title":"Create threads from top-level comments","createdAt":"2023-04-16T06:09:00Z"}
{"state":"Merged","mergedAt":"2023-04-17T11:03:03Z","number":5668,"body":"- Fixed the path rewrites to handle RAW HTML (for landing page)\r\n- Added more tests to make sure we don't break the SPA routing (for dashboard)\r\nTested these changes in Dev and worked as expected. I know the solution is hacky but I couldn't find a better one.","mergeCommitSha":"33467717faf363e7f2922d45602a55fac1b84a73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5668","title":"fix the path rewrite function","createdAt":"2023-04-16T18:22:39Z"}
{"state":"Merged","mergedAt":"2023-04-17T04:14:17Z","number":5669,"body":"Bitbucket is really odd. They have two different types of IDs, so we need\nto support both in the @-mention code:\n\n - `63ea8d19c5061c632c0c8c31`\n - `557058:3a0bac81-3b25-46c3-af58-b56159e4d511`","mergeCommitSha":"7ccdbee2c4e4612cce6601e5f6297ea849e8051b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5669","title":"Bitbucket external IDs come in different formats","createdAt":"2023-04-17T02:01:12Z"}
{"state":"Merged","mergedAt":"2022-03-11T23:02:41Z","number":567,"mergeCommitSha":"1dcb1e603b01612dd3d615ce01dc1236edc51e8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/567","title":"Add readiness probe and update descriptions","createdAt":"2022-03-11T22:40:42Z"}
{"state":"Merged","mergedAt":"2023-04-17T20:10:43Z","number":5670,"body":"Adding this so that once TLCs are migrated to threads, replying to the thread or updating a TLC writes back to GitHub and GitHub enterprise.","mergeCommitSha":"d83c5ab9265a2a5383be813b08dfd9102933bcd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5670","title":"Update UnblockedPRCommentService to support write-back for threaded TLCs","createdAt":"2023-04-17T06:02:38Z"}
{"state":"Merged","mergedAt":"2023-04-17T16:16:49Z","number":5671,"body":"Remove goofy bits at the top and bottom of the ToS","mergeCommitSha":"4fb699837a4989b58d6fdcd04c1d11d60b5b9ff4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5671","title":"Better ToS","createdAt":"2023-04-17T16:08:46Z"}
{"state":"Merged","mergedAt":"2023-04-17T17:44:00Z","number":5672,"body":"If the `AvailableEnterpriseSection` section is empty, right now the spacing is a bit off.  This fixes it.","mergeCommitSha":"2c5f6c3c81d58e9602a2a3181845b03bdba31c8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5672","title":"Fix login spacing","createdAt":"2023-04-17T17:02:38Z"}