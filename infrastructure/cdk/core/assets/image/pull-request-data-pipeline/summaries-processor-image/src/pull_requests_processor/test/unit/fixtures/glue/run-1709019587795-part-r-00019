{"state":"Merged","mergedAt":"2022-11-23T02:47:41Z","number":3771,"mergeCommitSha":"b18033bf68e3dcb01421e0ceeec50d9070258920","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3771","title":"ActiveMQ cdk","createdAt":"2022-11-23T02:45:24Z"}
{"state":"Merged","mergedAt":"2022-11-23T02:55:42Z","number":3772,"mergeCommitSha":"df1d41875c187b205e5f2f43b0fd533a8095e004","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3772","title":"ActiveMQ passwords","createdAt":"2022-11-23T02:54:24Z"}
{"state":"Merged","mergedAt":"2022-11-23T04:34:07Z","number":3773,"body":"- Update eks\r\n- Update eks\r\n- Update values\r\n- More values\r\n","mergeCommitSha":"dc46f02aeb99e38cd85491286dc388a3c711f1b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3773","title":"UpdateEKS","createdAt":"2022-11-23T04:33:45Z"}
{"state":"Merged","mergedAt":"2022-11-23T04:49:47Z","number":3774,"mergeCommitSha":"aacc0dbafc7c516edf77cd4f0c94d6535ba66ea4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3774","title":"Fix infra","createdAt":"2022-11-23T04:49:37Z"}
{"state":"Merged","mergedAt":"2022-11-23T05:11:38Z","number":3775,"mergeCommitSha":"f03b7933d597a5b3b32d151b72155f21b5d2ff86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3775","title":"Add activemq dependnecy","createdAt":"2022-11-23T04:56:20Z"}
{"state":"Merged","mergedAt":"2022-11-23T05:23:47Z","number":3776,"mergeCommitSha":"df817c0fed4479e13d9e9e589e38e2959090d2c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3776","title":"EnableActiveMQ","createdAt":"2022-11-23T05:12:31Z"}
{"state":"Closed","mergedAt":null,"number":3777,"mergeCommitSha":"73cf7695b3d04729a7d784a0310fa69df1544f9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3777","title":"Fix build","createdAt":"2022-11-23T05:31:47Z"}
{"state":"Merged","mergedAt":"2022-11-23T05:33:46Z","number":3778,"mergeCommitSha":"b60cf92bd405436a440ab7bda774e73b5836163a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3778","title":"Fix auth service","createdAt":"2022-11-23T05:33:41Z"}
{"state":"Merged","mergedAt":"2022-11-23T19:58:40Z","number":3779,"body":"Per https://github.com/NextChapterSoftware/unblocked/pull/3770#discussion_r1030165532","mergeCommitSha":"46afa00f30dca0359b99c6a028dd95682c4cfe47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3779","title":"Add image tag to sourcemark job spec","createdAt":"2022-11-23T16:03:35Z"}
{"state":"Merged","mergedAt":"2022-02-17T18:40:54Z","number":378,"mergeCommitSha":"9c81fc5232d9d9abf9db538d394065f6b8238041","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/378","title":"Fix build and rm log","createdAt":"2022-02-17T18:30:22Z"}
{"state":"Merged","mergedAt":"2022-11-23T17:59:00Z","number":3780,"mergeCommitSha":"ae832d2bd5174b044649d86974c62832290f575c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3780","title":"Move ot cheaper prod instance","createdAt":"2022-11-23T17:58:52Z"}
{"state":"Merged","mergedAt":"2022-11-23T18:09:42Z","number":3781,"mergeCommitSha":"91b464527ec2cd6eb1b959c29b2ac2c0b245d4de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3781","title":"Remove topic limit","createdAt":"2022-11-23T18:00:43Z"}
{"state":"Merged","mergedAt":"2022-11-23T18:54:20Z","number":3782,"body":"If the token has a repo claim, then we authorize the repo IDs found in the request path and requests query parameters.\r\n\r\nNothing sets the claim right now; but source mark scheduler will in a following change.\r\n\r\nedit","mergeCommitSha":"f6abb25c7f9689c176d0f57e69c6517a3120b997","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3782","title":"Introduce repo request authorization","createdAt":"2022-11-23T18:29:28Z"}
{"state":"Merged","mergedAt":"2022-11-23T19:23:06Z","number":3783,"mergeCommitSha":"7468dd894ad868a8a0ccd3158aabea04884858ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3783","title":"Fix smaller screen rendering for pr info views","createdAt":"2022-11-23T18:45:29Z"}
{"state":"Merged","mergedAt":"2022-11-24T22:14:58Z","number":3784,"body":"Setup basic route for web video composer. Set this up so there's a route hub app can point towards.\r\n\r\nTo be implemented once we introduce a \"VideoMetadataAPI\"\r\n","mergeCommitSha":"755b3e0edd68287b3a7e085e8da3d9d2d43fe08a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3784","title":"Basic route for web video composer","createdAt":"2022-11-23T19:12:44Z"}
{"state":"Merged","mergedAt":"2022-11-23T19:56:48Z","number":3785,"body":"Removes closed PRs from search results","mergeCommitSha":"54ce0f09bf359b153681a289138575fb9826de20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3785","title":"Delete search entries for closed PRs","createdAt":"2022-11-23T19:23:42Z"}
{"state":"Merged","mergedAt":"2022-11-23T21:09:08Z","number":3786,"mergeCommitSha":"878939a78a0c139f6d633c5cd8e77e0c73c39c11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3786","title":"Remove unused import","createdAt":"2022-11-23T21:09:02Z"}
{"state":"Merged","mergedAt":"2022-11-23T21:20:41Z","number":3787,"body":"Priority queue support needs to be enabled via ActiveMQ configuration files.\r\n\r\nNamely:\r\n       `             <policyEntry queue=\">\" prioritizedMessages=\"true\"/>`\r\n\r\n","mergeCommitSha":"924be56b26d3db9ea41bccf330eb53351ba491b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3787","title":"Add priority queue support","createdAt":"2022-11-23T21:10:32Z"}
{"state":"Merged","mergedAt":"2022-11-23T22:13:01Z","number":3788,"mergeCommitSha":"7cb90697614bddd56cb3cb52102366c8a7f128c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3788","title":"Null repoIds parameter means all repos","createdAt":"2022-11-23T21:47:41Z"}
{"state":"Merged","mergedAt":"2022-11-24T19:15:17Z","number":3789,"mergeCommitSha":"be3c828adf0ef4aea12c8614839042bf31b19af0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3789","title":"Video Walkthrough draft API","createdAt":"2022-11-23T22:30:42Z"}
{"state":"Merged","mergedAt":"2022-02-17T19:20:39Z","number":379,"mergeCommitSha":"57421d8f4b2a1e26df6882b09b128141b43dc0f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/379","title":"Try again","createdAt":"2022-02-17T19:20:25Z"}
{"state":"Merged","mergedAt":"2022-11-24T22:31:29Z","number":3790,"body":"Will add tests in the next PR with the Store implementation","mergeCommitSha":"8a1aa537212a6d8924b061e14a45d1032e7e6bd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3790","title":"Add VideoDraftModel","createdAt":"2022-11-23T22:57:00Z"}
{"state":"Merged","mergedAt":"2022-11-24T00:13:02Z","number":3791,"body":"<img width=\"864\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/203659365-e18a83ec-4182-4d80-8320-84f28e752b9b.png\">\r\n\r\n* Refactors existing sorting code used in vscode (the logic was moved into a shared Utils helper)\r\n","mergeCommitSha":"7ca35a2863c68043a7e901c136c4187017b8b491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3791","title":"Add insight sort dropdown to topic view","createdAt":"2022-11-23T23:06:58Z"}
{"state":"Merged","mergedAt":"2022-11-24T00:02:45Z","number":3792,"mergeCommitSha":"10dfcbbe608feceb196a7159812061e3b6654558","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3792","title":"Move topic mapping logic to its own lib","createdAt":"2022-11-23T23:24:52Z"}
{"state":"Merged","mergedAt":"2022-11-24T01:44:43Z","number":3793,"mergeCommitSha":"a48845f06ecbfc60b914078e53f1ede0d5166442","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3793","title":"Add more event configuration","createdAt":"2022-11-24T00:37:04Z"}
{"state":"Merged","mergedAt":"2022-11-25T00:07:15Z","number":3794,"mergeCommitSha":"f24f4943ba627cf4d4d97c16c49da5fb29529ace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3794","title":"Map topics to threads and pull requests when created","createdAt":"2022-11-24T01:10:20Z"}
{"state":"Merged","mergedAt":"2022-11-24T02:18:24Z","number":3795,"mergeCommitSha":"c0294d39ec22010cf761165be75c67b7a3627900","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3795","title":"Move remaining stuff to standard event queues","createdAt":"2022-11-24T01:47:06Z"}
{"state":"Merged","mergedAt":"2022-11-24T03:03:17Z","number":3796,"mergeCommitSha":"e6fbee29064b284e72883fc3cd40e7bf505aff31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3796","title":"LeveragePriorityQueues","createdAt":"2022-11-24T02:47:20Z"}
{"state":"Merged","mergedAt":"2022-11-24T03:55:01Z","number":3797,"mergeCommitSha":"2916c87b981b6bf1bbe5521868a39cc332389692","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3797","title":"Fix local stack","createdAt":"2022-11-24T03:54:52Z"}
{"state":"Merged","mergedAt":"2022-11-24T07:28:13Z","number":3798,"body":"For now admin web service is minting tokens.","mergeCommitSha":"cfa17dcdb9f53ddb13b847c3502cc97a29c51dcb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3798","title":"Auth Service internal API vends token with {team, repo, resource list}","createdAt":"2022-11-24T07:19:04Z"}
{"state":"Merged","mergedAt":"2022-11-24T08:44:12Z","number":3799,"body":"This is driven from events now.","mergeCommitSha":"cf7595a73050448398b143a0e1e8ef68ffaf0943","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3799","title":"Remove SM agent default fallback parameters","createdAt":"2022-11-24T08:28:32Z"}
{"state":"Merged","mergedAt":"2022-01-18T18:01:06Z","number":38,"body":"Basic message view to render string based content and potential snippets.\r\n\r\nMakes little assumptions on the models and props are meant to be temporary until we work on models. \r\nThe hope is to front-load some of the UI styling work for when we start implementing feature work end to end.\r\n\r\n<img width=\"1028\" alt=\"CleanShot 2022-01-14 at 16 20 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/149600931-7748cd00-b85a-400c-b1c4-a7a1bd12fa02.png\">\r\n\r\n","mergeCommitSha":"d764f27cdf89054a6d9e83b65ed371bdcaed691d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/38","title":"Message & Thread Views","createdAt":"2022-01-15T00:20:17Z"}
{"state":"Merged","mergedAt":"2022-02-22T19:30:49Z","number":380,"body":"This adds a check to the `getChannelsModifiedSince` call to pre-check if data has changed on a channel, before fetching.  In this PR every store will make a separate call to `getChannelsModifiedSince`.  This includes the following work:\r\n\r\n* Added a test UI (under the 'Group conversations' option in the web dashboard) that displays the current set of threads, and a button to add a thread.\r\n* Add ability for client tests to override the base request URL for mocking\r\n* Fix bug where CORS disallowed POST, breaking the channel API\r\n* Fix bug where CORS disallowed sending If-Modified-Since, breaking the channel API\r\n* Fix bug where the API service interpreted message content as a number array instead of an encoded string\r\n\r\nThere is a unit test for DataCacheStore that tests this.\r\n\r\nNext PRs will add:\r\n* Multiplexing the `getChannelsModifiedSince` so that we will make a single call for all stores\r\n* Add a unit test specifically for ThreadStore, with a mock server implementation","mergeCommitSha":"e63ca217bae9ea38bd1d5b5b8d01c98176c18d7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/380","title":"Add push channel checking to client data cache","createdAt":"2022-02-17T23:38:45Z"}
{"state":"Merged","mergedAt":"2022-11-25T19:23:09Z","number":3800,"body":"VSCode will now always try to connect with video app. This is in preparation for hub-initiated video walkthroughs where VSCode still needs to act as a file mark reference provider.\r\n\r\nFile Marks are no longer created on the fly but generated by the walkthrough initiator with data (file hash, commit hash, repo Id) provided by VSCode during the walkthrough.","mergeCommitSha":"dde877b59aa71c44e5006947135e04e158fb3d38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3800","title":"Persistant VSCode to Video App Connection","createdAt":"2022-11-24T17:25:37Z"}
{"state":"Merged","mergedAt":"2022-11-24T19:20:39Z","number":3801,"mergeCommitSha":"f7119962f8c73cdf7dfe769324a2b70e60bd1992","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3801","title":"Add batch queue processing","createdAt":"2022-11-24T18:24:08Z"}
{"state":"Merged","mergedAt":"2022-11-24T20:03:37Z","number":3802,"body":"https://linear.app/unblocked/issue/UNB-776/visible-insights-extract-commits-for-the-visible-part-of-a-file","mergeCommitSha":"110a6a4185fe3bca69c0892618adc8e2a6d052e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3802","title":"Visible insights: Extract commits for the visible part of a file","createdAt":"2022-11-24T19:14:02Z"}
{"state":"Merged","mergedAt":"2022-11-25T00:26:12Z","number":3803,"mergeCommitSha":"ac8478fe3fb373fec068336c7c8cbb3488ef6f20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3803","title":"Add video draft store","createdAt":"2022-11-24T19:32:37Z"}
{"state":"Merged","mergedAt":"2022-11-24T19:56:56Z","number":3804,"mergeCommitSha":"65c6b38df90dac884999508fa5e45cf88e66b7fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3804","title":"Transactional consumers","createdAt":"2022-11-24T19:38:14Z"}
{"state":"Merged","mergedAt":"2022-11-24T20:34:01Z","number":3805,"body":"One of UNSET | OFF | ON.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-773/remote-client-config-capabilities-should-be-displayed-as-tristate\r\n\r\n<img width=\"990\" alt=\"Screenshot 2022-11-24 at 12 23 50\" src=\"https://user-images.githubusercontent.com/1798345/203859071-c863ef1a-282b-4264-bec3-060e83b98d39.png\">\r\n\r\n","mergeCommitSha":"1f31bc4ab878d96c35abc2e360f5ddb6f5194b6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3805","title":"Remote client config capabilities should be displayed as tristate booleans","createdAt":"2022-11-24T20:05:48Z"}
{"state":"Merged","mergedAt":"2022-11-24T21:57:36Z","number":3806,"body":"(Botched rebase, lost all these changes somehow from #3805)","mergeCommitSha":"36ec04c28c8ac4a19291463985480684d39c122d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3806","title":"Missed a file update in previous PR","createdAt":"2022-11-24T21:42:55Z"}
{"state":"Merged","mergedAt":"2022-11-24T22:57:24Z","number":3807,"body":"A little hacky but this operation is going to be replaced soon.","mergeCommitSha":"df4b042ff94cf2e0378d8df3472ecb48c2b85a58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3807","title":"Dont return PR-associated slack threads from getTopicRelatedInsights","createdAt":"2022-11-24T21:57:39Z"}
{"state":"Merged","mergedAt":"2022-11-25T00:50:54Z","number":3808,"mergeCommitSha":"d93c38d52332257607b09b23515a36243e4e1589","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3808","title":"Add video draft service","createdAt":"2022-11-24T22:05:24Z"}
{"state":"Merged","mergedAt":"2022-11-24T22:42:58Z","number":3809,"mergeCommitSha":"c81f911ded19d8fc1bf11ae20131be3fadaa8871","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3809","title":"Set rollback cause","createdAt":"2022-11-24T22:11:59Z"}
{"state":"Merged","mergedAt":"2022-02-17T23:56:47Z","number":381,"body":"To be used when trying to find a line matching the original text sequence in a changeset.","mergeCommitSha":"ebc5e972636cc281c9918609fe48e4cd21525748","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/381","title":"Fuzzy string compare for detecting moved code and modified code","createdAt":"2022-02-17T23:41:46Z"}
{"state":"Merged","mergedAt":"2022-11-24T22:34:38Z","number":3810,"body":"Missed this when we created the endpoint, so GET /config is currently throwing 401 for the config endpoint when in readonly mode.","mergeCommitSha":"dc1e7872c4cc2d50948aeecf61c1e2844c1c0da8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3810","title":"Allow readonly mode to access GET /config","createdAt":"2022-11-24T22:18:52Z"}
{"state":"Merged","mergedAt":"2022-11-25T00:07:23Z","number":3811,"body":"No need to be putting thist stuff there\r\n","mergeCommitSha":"76f923c5304996d12e1ebb78a49c998cde084319","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3811","title":"Should not be storing redis cluster info in helm charts","createdAt":"2022-11-24T22:53:23Z"}
{"state":"Merged","mergedAt":"2022-11-24T23:50:12Z","number":3812,"mergeCommitSha":"1a109eff919e85fdb0bbd159cae5307298b4d04c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3812","title":"Reindexing event should be low priority","createdAt":"2022-11-24T23:25:49Z"}
{"state":"Merged","mergedAt":"2022-11-25T00:58:18Z","number":3813,"mergeCommitSha":"13a6ad1c87852235f141002d1ea614fbad002d2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3813","title":"Video Draft API Implementation","createdAt":"2022-11-24T23:50:19Z"}
{"state":"Merged","mergedAt":"2022-11-25T23:32:39Z","number":3814,"body":"<img width=\"706\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/203877786-4abed4a2-9bab-4a01-bb0c-da2bda44b99b.png\">\r\n\r\n* Add InsightFilterUtils for filtering by type helpers \r\n* Refactor sorting/filtering dropdowns into own file \r\n* These filters are done client-side but should ultimately be part of the API\r\n","mergeCommitSha":"82235877aab4469dd86d561305e6b5d28d76321c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3814","title":"Add filter by type dropdown","createdAt":"2022-11-25T00:28:25Z"}
{"state":"Merged","mergedAt":"2022-11-25T01:02:48Z","number":3815,"body":"There’s no point in waiting on messages and it affects coroutine timeouts.\r\n","mergeCommitSha":"e5f50e2b0c9fee0c05f61520689b7d14d1a1a4a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3815","title":"Event queue should not be waiting for messages","createdAt":"2022-11-25T00:51:01Z"}
{"state":"Closed","mergedAt":null,"number":3816,"mergeCommitSha":"47146b58d9753e642da69b3c43762c1fcbec8e9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3816","title":"Update topic mappings after indexing","createdAt":"2022-11-25T00:56:24Z"}
{"state":"Merged","mergedAt":"2022-11-25T02:10:18Z","number":3817,"mergeCommitSha":"8faa11653c105ec22434a15cc3e4cc761b814900","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3817","title":"persistent send","createdAt":"2022-11-25T02:00:19Z"}
{"state":"Merged","mergedAt":"2022-11-25T16:45:21Z","number":3818,"mergeCommitSha":"5b8cf793173c50c9d6ea13a23e8530da8259cc1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3818","title":"Convert to batch processor","createdAt":"2022-11-25T16:09:39Z"}
{"state":"Merged","mergedAt":"2022-11-25T17:50:24Z","number":3819,"body":"HealthCheck logs are inundating logz.io.\r\n\r\nhttps://app.logz.io/#/goto/e087b6d0c42a3419f3ff8cd91656f566?switchToAccountId=411850","mergeCommitSha":"5f6d80f43b2d12820abe9b4d05aadaba0a158d1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3819","title":"Disable health checks for logs","createdAt":"2022-11-25T16:52:57Z"}
{"state":"Merged","mergedAt":"2022-11-25T17:37:17Z","number":3820,"mergeCommitSha":"5cb6a71e5d3d408c7e25e848c737aeb2409482f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3820","title":"Remove debug lines","createdAt":"2022-11-25T17:23:09Z"}
{"state":"Closed","mergedAt":null,"number":3821,"mergeCommitSha":"89916599cd29a75fbfb3a111924d03a8e6f2b0de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3821","title":"Create topic mapping queue","createdAt":"2022-11-25T18:28:37Z"}
{"state":"Closed","mergedAt":null,"number":3822,"body":"Was deprecated in August by Kay.\n\nDefinitely safe to remove now.","mergeCommitSha":"102fa3017addbd4d284b410e6c175a2a10703908","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3822","title":"Obsolete the deprecated PullRequest descriptionMessage field","createdAt":"2022-11-25T19:19:44Z"}
{"state":"Merged","mergedAt":"2022-11-25T22:13:57Z","number":3823,"body":"To keep topic mappings up to date as new pull requests and threads are created","mergeCommitSha":"dfa1363dd9227ad9570ce67c865fc918195c23a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3823","title":"Create topic mapping job","createdAt":"2022-11-25T20:02:25Z"}
{"state":"Merged","mergedAt":"2022-11-25T21:53:53Z","number":3824,"mergeCommitSha":"228c9cde76199ecda21242624cbfe5ad0024adc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3824","title":"Shuffle stuff around","createdAt":"2022-11-25T20:21:52Z"}
{"state":"Merged","mergedAt":"2022-11-25T22:45:52Z","number":3825,"body":"Turns out we're skipping over single message threads.","mergeCommitSha":"a0b1bbd992967c64ec44f06b34e94871e2c4e6f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3825","title":"Handle when threadTs is null","createdAt":"2022-11-25T21:53:33Z"}
{"state":"Merged","mergedAt":"2022-11-25T22:45:39Z","number":3826,"body":"It pains me to remove this beautiful ~hack~ code.","mergeCommitSha":"2fe4f796968624bb01837a0b83518a5155036eae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3826","title":"Revert \"Dont return PR-associated slack threads from getTopicRelatedInsights (#3807)","createdAt":"2022-11-25T22:28:55Z"}
{"state":"Merged","mergedAt":"2022-11-26T00:12:30Z","number":3827,"body":"https://linear.app/unblocked/issue/UNB-784/the-getreposourcemarks-contains-redundant\r\n\r\nExpected to pretty dramatically reduce getRepoSourceMarks response payload size.","mergeCommitSha":"f62f0abfd649dd97ca23dc9af15012d9cf6da690","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3827","title":"The getRepoSourceMarks contains redundant points[].overrideSnippetEncoded","createdAt":"2022-11-25T22:41:07Z"}
{"state":"Merged","mergedAt":"2022-11-29T23:51:59Z","number":3828,"body":"Send Contributors as part of RepoFileReference.\r\nThis will be used as part of https://github.com/NextChapterSoftware/unblocked/pull/3813","mergeCommitSha":"2f4897dba418eb85d889eccf4ae0dfced75a0d45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3828","title":"Send Contributors as part of repo file ref","createdAt":"2022-11-25T22:45:38Z"}
{"state":"Merged","mergedAt":"2022-11-26T01:31:02Z","number":3829,"mergeCommitSha":"62b1f87c6d7dffc8a8cc0548a9baa8b90abfb740","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3829","title":"The sourcepoint response can be further optimized to exclude isOriginal unless true","createdAt":"2022-11-26T01:09:16Z"}
{"state":"Merged","mergedAt":"2022-02-18T19:41:30Z","number":383,"body":"Update Link API as a link in markdown [contain **Different** __styling__ ~types~](url)","mergeCommitSha":"7cbd723f82092849444f7b981bebfcffb75b3caf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/383","title":"Update Link API","createdAt":"2022-02-18T19:34:52Z"}
{"state":"Merged","mergedAt":"2022-11-28T07:35:55Z","number":3830,"body":"Fixes these 500s:\r\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1669493750027639\r\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1669598145728729\r\n\r\nExamples where a trusted original point has a null snippet:\r\n- https://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/repos/f8e94478-7548-4000-a625-1a43267e0fc5/threads/2ec4e957-b145-4b63-baa1-cd9045654ace\r\n- https://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/repos/f8e94478-7548-4000-a625-1a43267e0fc5/threads/6e5c4f01-f7af-4399-a9ff-d8f268ad5288\r\n- https://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/repos/f8e94478-7548-4000-a625-1a43267e0fc5/threads/50a6da04-bec0-4c45-a52e-c6a8fb49130b\r\n- https://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/repos/f8e94478-7548-4000-a625-1a43267e0fc5/threads/236b5c95-a206-47d8-aece-ddddc4e7c87f\r\n- https://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/repos/f8e94478-7548-4000-a625-1a43267e0fc5/threads/402b8af2-a5e7-456a-93c8-f885fbc4e090","mergeCommitSha":"64bb969ccb29ee7684d49d7ed3dac23822cc5573","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3830","title":"Prefer trusted sourcepoints only if they have a better snippet than the untrusted original","createdAt":"2022-11-28T07:26:17Z"}
{"state":"Merged","mergedAt":"2022-11-28T08:48:45Z","number":3831,"mergeCommitSha":"025992c65c4e25e35410f11eb872f324cb60886f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3831","title":"add repo to be used for job queue controller","createdAt":"2022-11-28T08:36:21Z"}
{"state":"Merged","mergedAt":"2022-11-28T17:59:34Z","number":3832,"mergeCommitSha":"677a267b369ce7f47e578eb194569b7a80fcfb94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3832","title":"Remove version info dupes","createdAt":"2022-11-28T17:58:01Z"}
{"state":"Merged","mergedAt":"2022-11-29T00:39:18Z","number":3833,"body":"<img width=\"1563\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204353569-33053f7c-9b6f-4045-a71c-c447ba263794.png\">\r\n\r\nRight now it's missing most of the data in the designs (waiting for API changes in https://github.com/NextChapterSoftware/unblocked/pull/3767) ","mergeCommitSha":"18ae70735f5d24a028005781484a2afa728573d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3833","title":"Update dashboard topics view ","createdAt":"2022-11-28T18:38:07Z"}
{"state":"Merged","mergedAt":"2022-11-29T22:13:44Z","number":3834,"body":"Notebooks for various features/sources.","mergeCommitSha":"1a03681fdafd097a6e632b5aa88420bc60c11317","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3834","title":"Topic exploration","createdAt":"2022-11-28T18:51:09Z"}
{"state":"Merged","mergedAt":"2022-11-28T19:00:10Z","number":3835,"mergeCommitSha":"dc0445915aacc38262dd0b7fced62daa8a2719a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3835","title":"Start data pipeline","createdAt":"2022-11-28T18:56:29Z"}
{"state":"Merged","mergedAt":"2022-11-28T19:17:08Z","number":3836,"mergeCommitSha":"4587d999eabbdafb47b8994dc7ff42b205a060b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3836","title":"Fix service template","createdAt":"2022-11-28T19:16:14Z"}
{"state":"Merged","mergedAt":"2022-11-28T20:42:14Z","number":3837,"mergeCommitSha":"95948e002906559f4bf334dd4e2fb1558e30775d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3837","title":"Add slack data service","createdAt":"2022-11-28T19:36:55Z"}
{"state":"Merged","mergedAt":"2022-11-28T19:47:51Z","number":3838,"mergeCommitSha":"ed10480ead8062ea4d47dc46f7fd91f46ac3c274","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3838","title":"update docs","createdAt":"2022-11-28T19:47:02Z"}
{"state":"Merged","mergedAt":"2022-11-28T20:01:15Z","number":3839,"mergeCommitSha":"6041afd5a64da2b28ae6f0b698989d66a6381706","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3839","title":"Slack data permissions","createdAt":"2022-11-28T20:01:05Z"}
{"state":"Merged","mergedAt":"2022-02-18T20:12:07Z","number":384,"mergeCommitSha":"e5dde724b172c20dffb10f3165a01678b7934c5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/384","title":"Fix readme","createdAt":"2022-02-18T20:09:19Z"}
{"state":"Merged","mergedAt":"2022-11-28T21:34:26Z","number":3840,"mergeCommitSha":"cd139e6ff208e256bbd9696bd52a63f4654a351f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3840","title":"Update to new gradle","createdAt":"2022-11-28T21:00:47Z"}
{"state":"Merged","mergedAt":"2022-11-28T21:28:23Z","number":3841,"body":"Fixes https://b-67992006-3bfa-4064-9e22-102dbd0cfd2c-1.mq.us-west-2.amazonaws.com:8162/admin/message.jsp?id=ID%3Awebhookservice-6c58cd77b8-c47ct-42911-1669621760208-1%3A2%3A1%3A1%3A222&JMSDestination=DLQ.hooks_scm","mergeCommitSha":"d177f95e80e6f315ebdca21932dd67beb2164bc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3841","title":"Add new GitHubPullRequestEvent.Action","createdAt":"2022-11-28T21:06:19Z"}
{"state":"Merged","mergedAt":"2022-11-28T21:47:30Z","number":3842,"body":"open stomp port needed by controller ","mergeCommitSha":"bc63a14b078f31e5831500fad8f93de7c7504a54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3842","title":"open stomp port","createdAt":"2022-11-28T21:43:04Z"}
{"state":"Merged","mergedAt":"2022-11-28T21:58:43Z","number":3843,"mergeCommitSha":"62af7a6f3be52f2b1c5d1f6477492abb2f924b3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3843","title":"Fix popover alignment","createdAt":"2022-11-28T21:43:43Z"}
{"state":"Merged","mergedAt":"2022-12-02T19:29:28Z","number":3844,"body":"A top level search API returning insights.\r\n\r\nNeeds to handle filtering by:\r\n* string query (regular string search)\r\n* sort (i.e. by recency)\r\n* insight types\r\n* repoIds\r\n* topicIds\r\n* topic expert Ids\r\n\r\n<img width=\"463\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204390833-e69c0676-0a3e-4ccc-862f-a7afbdc46ae5.png\">\r\n<img width=\"662\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204391603-899950eb-5b4b-4f7b-ad0c-2aa7d61a48cd.png\">\r\n","mergeCommitSha":"54b6c76705ce95e94df9a926558d0c11de806099","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3844","title":"Insight search API","createdAt":"2022-11-28T22:11:29Z"}
{"state":"Merged","mergedAt":"2022-11-30T21:21:13Z","number":3845,"body":"Basic web composer for walkthroughs.\r\n\r\nPR is mainly to setup UI for testing integration with hub app.\r\nStyling is quite rough due and will be updated once final designs are available.\r\n\r\nSeparate PR to implement contributors. Requires significant refactoring as logic is duplicated in a few places right now.\r\n\r\n<img width=\"1115\" alt=\"CleanShot 2022-11-28 at 14 05 09@2x\" src=\"https://user-images.githubusercontent.com/1553313/204399687-9e3da7ab-4a29-46ee-b528-e141e0f46d09.png\">\r\n","mergeCommitSha":"ee667f701291508c053d0bdca2940ffb1313140d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3845","title":"Basic Web composer for Video Walkthroughs","createdAt":"2022-11-28T22:12:52Z"}
{"state":"Merged","mergedAt":"2022-11-28T23:18:03Z","number":3846,"mergeCommitSha":"7d77997eb3062b2c185d3c7908890311a8cf6351","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3846","title":"Ability for job config to specify default image tag and/or registry","createdAt":"2022-11-28T22:23:22Z"}
{"state":"Merged","mergedAt":"2022-11-29T00:04:17Z","number":3847,"body":"Unique identifier used by the job for telemetry purposes.","mergeCommitSha":"d8f95edd754d3656c458cfa2ded96951e92edca6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3847","title":"Add jobId to the job config","createdAt":"2022-11-28T23:32:53Z"}
{"state":"Merged","mergedAt":"2022-11-29T00:39:00Z","number":3848,"body":"Mostly just moving things around; in anticipation for reuse for incoming views","mergeCommitSha":"01d1c80a78fa647f0ee17fce5d862b98799ff8af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3848","title":"Refactor Insight specific components and types","createdAt":"2022-11-28T23:38:59Z"}
{"state":"Merged","mergedAt":"2022-11-30T00:35:26Z","number":3849,"mergeCommitSha":"cda48e0f6b6b2c01a262eeeb4d19e8ea481fa27c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3849","title":"Add walkthrough IPC to hub","createdAt":"2022-11-29T00:03:14Z"}
{"state":"Merged","mergedAt":"2022-02-18T21:31:32Z","number":385,"body":"Did not update rendering for links when updating spec.\r\n\r\nWorkflows will now run on common folder changes.","mergeCommitSha":"e0d9a96597e784a118fd2dcca719e7b60fb12eec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/385","title":"Fix inline links","createdAt":"2022-02-18T21:24:33Z"}
{"state":"Merged","mergedAt":"2022-11-29T00:50:02Z","number":3850,"mergeCommitSha":"5c3adffe5bfaf2fcee1555c35867f51b52390f6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3850","title":"Fixing test??","createdAt":"2022-11-29T00:35:28Z"}
{"state":"Merged","mergedAt":"2022-11-29T17:24:55Z","number":3851,"body":"Update icons per updated designs:\r\n<img width=\"154\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204413915-8287ccb5-fd3d-44bd-ae5b-bab6393a9dbc.png\">\r\n\r\ndashboard:\r\n<img width=\"296\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204413987-d4d8f866-818e-4590-9924-cc7cfcf96ebc.png\">\r\n\r\nvscode:\r\n<img width=\"404\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204414018-3ebdf733-e9b7-4018-864b-28567be87c76.png\">\r\n\r\n\r\n","mergeCommitSha":"14ba7886bee1ca0b0edec708127c9ecf208e8dba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3851","title":"Update icons","createdAt":"2022-11-29T01:11:29Z"}
{"state":"Merged","mergedAt":"2022-11-30T20:48:29Z","number":3852,"mergeCommitSha":"a129c5348eb264b535c843a8e1db66322b082935","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3852","title":"Don't deploy sourcemaps in dashboard or VSCode extension","createdAt":"2022-11-29T18:05:20Z"}
{"state":"Merged","mergedAt":"2022-11-29T22:17:26Z","number":3853,"mergeCommitSha":"0c22738cc1970a76df518b903708cf9ef81650c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3853","title":"Add dashboard url to video draft","createdAt":"2022-11-29T18:06:40Z"}
{"state":"Merged","mergedAt":"2022-11-29T18:33:34Z","number":3854,"body":"Step 1 on on the path to removing repo from TopicModel:\r\n\r\nhttps://chapter2global.slack.com/archives/C045VGYML95/p1669662693581689","mergeCommitSha":"1ea56c82b556fcadeec0728bd1a58886bad19061","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3854","title":"Make TopicModel.repo optional","createdAt":"2022-11-29T18:07:27Z"}
{"state":"Merged","mergedAt":"2022-11-29T20:51:36Z","number":3855,"body":"This will let us specify where the topic came from. The intention here is the data pipeline will store generated topics in the database by calling TopicStore.createTopics, specifying the source.\r\n\r\nIn the next PR, I'll add the ability to set a config flag that controls the source of the topics that are returned to clients. This way we can compare the topics between difference sources.","mergeCommitSha":"e38ed249d8284ab485812475b52f3678eec2bf42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3855","title":"Add TopicModel.source property","createdAt":"2022-11-29T19:39:28Z"}
{"state":"Merged","mergedAt":"2022-11-29T19:54:49Z","number":3856,"mergeCommitSha":"976c5afe6be66ab0c0970ac2bfa299b5cbc3258f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3856","title":"Fix 500 in getRepoSourceMarks operation affecting Sentry","createdAt":"2022-11-29T19:47:04Z"}
{"state":"Merged","mergedAt":"2022-11-29T20:00:48Z","number":3857,"mergeCommitSha":"fedb1997a070ddc7bed282cc15ca9428ca1beb76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3857","title":"Improve job event logging","createdAt":"2022-11-29T19:54:34Z"}
{"state":"Merged","mergedAt":"2022-11-29T21:20:09Z","number":3858,"body":"Adding service accounts for Job controllers:\r\n- Gets access to activemq crews \r\n- Gets read-only permission on ECR registry ","mergeCommitSha":"f049dcb6cd4f71f4f129c9cad7f243f1ee9eac6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3858","title":"Add controller service account","createdAt":"2022-11-29T20:04:24Z"}
{"state":"Merged","mergedAt":"2022-11-29T20:55:42Z","number":3859,"body":"Kind of hard to test that this will work, until we deploy it.\r\n\r\nSetting the variable on my machine:\r\n```\r\nset -x JOB_CONTROLLER_ENV prod\r\n```\r\n\r\nResults in this log output:\r\n```\r\nBuild SHA 939057d9fb5951d8eea14fb3b2d82374e2bb21ea\r\nApp type source-agent\r\nProduct Number 1\r\nProduct Version development\r\nUsing environment prod from JOB_CONTROLLER_ENV environment variable\r\n```","mergeCommitSha":"8718b690c75609a87cf324f8c885b348073a69bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3859","title":"Setup source agent to run against PROD","createdAt":"2022-11-29T20:41:27Z"}
{"state":"Merged","mergedAt":"2022-02-22T22:59:03Z","number":386,"body":"Add logic to translate MessageEditor to Protobuf Block\r\nAdded support for quotes blocks in message view.\r\n\r\nDiscussion thread in stories so that editor markdown will populate the thread UI","mergeCommitSha":"994e0aadeaa952b2febf3659488879485fda01df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/386","title":"Message Editor Translator","createdAt":"2022-02-22T07:26:01Z"}
{"state":"Merged","mergedAt":"2022-11-29T21:21:44Z","number":3860,"mergeCommitSha":"228f84416d40427c1c50faaa13c4872419c3d33e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3860","title":"Slack in the cloud","createdAt":"2022-11-29T21:05:15Z"}
{"state":"Merged","mergedAt":"2022-11-29T21:39:40Z","number":3861,"mergeCommitSha":"7f944e91d723b9997e576e13178622b76868e234","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3861","title":"Enable verbose source agent logging","createdAt":"2022-11-29T21:37:51Z"}
{"state":"Merged","mergedAt":"2022-11-29T22:03:26Z","number":3862,"body":"![CleanShot 2022-11-29 at 13 43 28@2x](https://user-images.githubusercontent.com/1924615/204654429-a230c71a-5a3c-4ea7-a015-bcb498f25304.png)","mergeCommitSha":"8514af164d2ae779c3721060a1c536613b14c916","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3862","title":"Add topic source to team settings model","createdAt":"2022-11-29T21:38:45Z"}
{"state":"Merged","mergedAt":"2022-11-29T22:19:47Z","number":3863,"body":"No longer using create PR flow.\r\nRemoving to lower cost of maintenance.\r\n","mergeCommitSha":"d4c0edaf55b2318c32597bd4f18196eaddefa499","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3863","title":"Remove create PR","createdAt":"2022-11-29T21:56:09Z"}
{"state":"Merged","mergedAt":"2022-11-29T22:02:00Z","number":3864,"mergeCommitSha":"104a09a37a3812d42e290d03c47783e65a32217e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3864","title":"Fix buckets","createdAt":"2022-11-29T22:01:54Z"}
{"state":"Merged","mergedAt":"2022-11-29T22:15:35Z","number":3865,"mergeCommitSha":"1be795de2aeada5f0ba34a9ec67bb5906251b516","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3865","title":"Fix rate limiting","createdAt":"2022-11-29T22:14:42Z"}
{"state":"Closed","mergedAt":null,"number":3866,"mergeCommitSha":"41e7269148021cbf70c5ba0589a466b4d76c0dac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3866","title":"Add contributors to RepoFileReference in IPC protocol","createdAt":"2022-11-29T22:14:54Z"}
{"state":"Closed","mergedAt":null,"number":3867,"mergeCommitSha":"4cf2f9dc601f167f298eb15ebdac3d8042f1ada6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3867","title":"java 19 upgrade","createdAt":"2022-11-29T22:43:22Z"}
{"state":"Merged","mergedAt":"2022-11-30T23:25:18Z","number":3868,"body":"with mock data:\r\n![image](https://user-images.githubusercontent.com/13431372/204664583-1543ec23-d81d-4969-b046-2400cd057007.png)\r\n\r\n* Note: hiding the Experts column until experts are implemented \r\n* Note: right now insights count is hardcoded to return 0, David will be working on the service implementation of this count to backfill. Should still be safe to merge since this is under a feature flag ","mergeCommitSha":"39c11bcb387ee8a505fd761eb5c0122261075f1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3868","title":"Add insights count column","createdAt":"2022-11-29T22:44:36Z"}
{"state":"Merged","mergedAt":"2022-11-29T23:06:03Z","number":3869,"mergeCommitSha":"a65920ba0c059e8482b9105831d655f702015e49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3869","title":"Source agent limited to 2 hours and runs in verbose mode","createdAt":"2022-11-29T23:03:32Z"}
{"state":"Merged","mergedAt":"2022-02-22T16:48:51Z","number":387,"body":"Missing an S","mergeCommitSha":"b60cef91b1aa23b0e660d367c4d510ed032e4e2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/387","title":"Update kube-setup.md","createdAt":"2022-02-22T16:40:41Z"}
{"state":"Merged","mergedAt":"2022-11-30T20:39:43Z","number":3870,"body":"The explorer insights panel now has an option to display only the PRs and threads relevant to the visible part of the focused editor.  The hope is that this will provide more relevant results in the UI.\r\n\r\nSome implementation details:\r\n- The `ActiveFileManager` now publishes a second stream (`activeRangeStream`) that is updated with the currently-active file *and* view range.\r\n- A new stream (`ViewScopedInsightStream`) uses this to understand what file and line range are being viewed.\r\n- `ViewScopedInsightStream` also subscribes to the currently-active file view, and whenever that file changes, queries the blame commit list for the file (this is in the helper `CurrentFileCommitStream`).\r\n- `ViewScopedInsightStream` uses this blame and view information to filter down the global set of threads and PRs.\r\n- An option was added to the dropdown menu to turn this feature on and off.  This necessitated reimplementing the menu using the custom `Dropdown` components instead of the HTML Select.  The dropdown menu now looks like this:\r\n\r\n<img width=\"393\" alt=\"Screen Shot 2022-11-29 at 3 54 14 PM\" src=\"https://user-images.githubusercontent.com/2133518/204674161-9780858b-3917-4a8b-9de9-c4b342f8c3a3.png\">\r\n\r\nFixes https://linear.app/unblocked/issue/UNB-779/visible-insights-update-insights-as-you-scroll","mergeCommitSha":"9d2566a4ec2a966368753ded14eea1dc610f2982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3870","title":"Add view-scoping option to VSCode explorer insights panel","createdAt":"2022-11-29T23:07:31Z"}
{"state":"Merged","mergedAt":"2022-11-29T23:12:20Z","number":3871,"mergeCommitSha":"4db179ecc6e3d1a30c9ca5f0c9814f022266eb0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3871","title":"up replica count","createdAt":"2022-11-29T23:12:14Z"}
{"state":"Merged","mergedAt":"2022-11-29T23:48:34Z","number":3872,"body":"Due to lifecycle changes with VSCode -> Walkthrough app connection, no longer need to log errors on connection failures. \r\n\r\nThis is now an expected error case as VSCode constantly tries connecting with walkthrough app.","mergeCommitSha":"4182e33d3d233506dc6459930eba2fc02833f04b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3872","title":"Remove log for walkthrough app","createdAt":"2022-11-29T23:32:40Z"}
{"state":"Merged","mergedAt":"2022-12-01T20:22:11Z","number":3873,"mergeCommitSha":"f71a601450a23c95f23443742d644979c7494d35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3873","title":"Upload video for composer","createdAt":"2022-11-29T23:34:56Z"}
{"state":"Merged","mergedAt":"2022-11-30T06:57:36Z","number":3874,"body":"Follow up from https://github.com/NextChapterSoftware/unblocked/pull/3862 where the API will now return topics based on the source type chosen for a team. Currently the only option is `Default` but as we add additional topic generation sources, we will have others to select from.","mergeCommitSha":"d42fa8ea57bd3b9ef3efa1f5b2ed3c67da0e8b39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3874","title":"Return topics based on team setting","createdAt":"2022-11-30T01:06:27Z"}
{"state":"Merged","mergedAt":"2022-11-30T02:16:55Z","number":3875,"mergeCommitSha":"aaa9dd25db0db56396f1ba1d0019ca27ac3759f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3875","title":"Add real team member to source agent api auth token to get pusher working","createdAt":"2022-11-30T02:10:24Z"}
{"state":"Merged","mergedAt":"2022-11-30T04:14:12Z","number":3876,"mergeCommitSha":"6e2a7597734021bb8d631f4f29d168c2055ce172","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3876","title":"Optimize","createdAt":"2022-11-30T02:11:02Z"}
{"state":"Merged","mergedAt":"2022-11-30T03:06:49Z","number":3877,"mergeCommitSha":"27fa57ca09ede60a229c16b718a1800dc99175de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3877","title":"Should be SOURCEMARK_DEBUG not SOURCEMARK_VERBOSE","createdAt":"2022-11-30T03:06:28Z"}
{"state":"Merged","mergedAt":"2022-11-30T06:49:43Z","number":3878,"mergeCommitSha":"2c89a080b7a48f49c4c055a8e331733a2ce4c34e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3878","title":"machine learning stuff should not be mixed with ci stuff","createdAt":"2022-11-30T04:56:20Z"}
{"state":"Merged","mergedAt":"2022-11-30T05:38:49Z","number":3879,"body":"Gets rid of almost all the eslint warnings:\r\n\r\n* Use `unknown` where appropriate\r\n* Use real types where appropriate\r\n* Fix code when appropriate\r\n* Disable lint warnings when we're implementing an untyped JS API and types are meaningless (ie winston)\r\n\r\nThis doesn't fix the log sanitizer, will do that next or get Rashin to do it.","mergeCommitSha":"2a1c021389c768ac1c8529839995053d82eeb3c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3879","title":"Reduce eslint warnings","createdAt":"2022-11-30T05:13:11Z"}
{"state":"Merged","mergedAt":"2022-02-22T17:29:48Z","number":388,"body":"To address this thread: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1645132647022029","mergeCommitSha":"9774e0e2b079891bce5b596a55c3ac42410e54c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/388","title":"make cdk diff less noisy","createdAt":"2022-02-22T17:21:37Z"}
{"state":"Merged","mergedAt":"2022-11-30T18:04:31Z","number":3880,"mergeCommitSha":"472b5ef3dd9d6b2278eae185337f210a6a53a354","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3880","title":"activemq hostname","createdAt":"2022-11-30T18:04:12Z"}
{"state":"Merged","mergedAt":"2022-11-30T18:08:02Z","number":3881,"mergeCommitSha":"c9ef0e139579cd1ff3de03a372d161a29942eda6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3881","title":"cleanup pip","createdAt":"2022-11-30T18:07:44Z"}
{"state":"Merged","mergedAt":"2022-11-30T21:15:02Z","number":3882,"mergeCommitSha":"9d755c31e639bff5d343532d2d31df7f769fe790","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3882","title":"Move topics page to team level","createdAt":"2022-11-30T19:41:59Z"}
{"state":"Merged","mergedAt":"2022-11-30T20:08:37Z","number":3883,"mergeCommitSha":"3770f48e92a384355a4eed0cf3db2e715231937e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3883","title":"Add slack metadata","createdAt":"2022-11-30T20:05:16Z"}
{"state":"Merged","mergedAt":"2022-11-30T20:39:28Z","number":3884,"body":"https://linear.app/unblocked/issue/UNB-790/trigger-source-agent-jobs-for-all-repos-public-with-at-least-one","mergeCommitSha":"a5082effc223793d26cb5f0a83d3ac7ee3ad4fd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3884","title":"Batch source-agent jobs for all repos","createdAt":"2022-11-30T20:26:33Z"}
{"state":"Merged","mergedAt":"2022-11-30T20:47:20Z","number":3885,"mergeCommitSha":"ab7316c80ab08b135c9455d2b781f486c9375ae3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3885","title":"Fix content type","createdAt":"2022-11-30T20:45:20Z"}
{"state":"Merged","mergedAt":"2022-12-02T19:00:44Z","number":3886,"body":"Support more languages, more reliably, in VSCode's syntax hilighter.\r\n\r\n* Always pass the file path into the shiki highlighter.  Before, we were submitting three different values (VSCode's language ID, the file extension, and the file extension with a dot before it), so the highlighter sometimes worked in some UIs, for some languages, and didn't for others.\r\n* Add mappings for more languages.  This gets a bunch of common languages working correctly.","mergeCommitSha":"6d0d3e458ea9b60f9c4b3b4b6f2942cb21dc8634","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3886","title":"Support more languages more reliably in VSCode","createdAt":"2022-11-30T21:22:43Z"}
{"state":"Closed","mergedAt":null,"number":3887,"mergeCommitSha":"28a2fcf5b696a13445423e1d81b4671b3fb03428","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3887","title":"Jeff/draft web composer","createdAt":"2022-11-30T21:30:35Z"}
{"state":"Merged","mergedAt":"2022-11-30T21:47:06Z","number":3888,"mergeCommitSha":"a604772476657239f2859a16490342cb659972d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3888","title":"Just show the topic name on the topics page","createdAt":"2022-11-30T21:31:58Z"}
{"state":"Merged","mergedAt":"2022-12-01T22:11:51Z","number":3889,"mergeCommitSha":"05d0394bc3038478b443e2b5882bd139cd6aea3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3889","title":"Allow the Hub to smoothly transition through Walkthrough permissions mode","createdAt":"2022-11-30T21:55:14Z"}
{"state":"Merged","mergedAt":"2022-02-23T22:23:30Z","number":389,"body":"This pr is likely going to get ripped apart. :)\r\n\r\nThis pr achieves the following:\r\n1. Add TreeView and TreeViewNode shared components.\r\n2. Add simple store to manage state for now. (not zustand).\r\n\r\nIn flux (to be probably changed as I work on this shit more on the VScode end of things)\r\n1. On the VScode end, currently using modeling behaviour similar to how VSCode does it with a tree hierarchy.\r\n2. On the UI end, the final state is a flattened version of the VScode model.\r\n\r\n<img width=\"346\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/155199515-cae4b7c4-baa7-408b-80ea-89fdbb167ab8.png\">\r\n","mergeCommitSha":"91507570452d634f5685f4c87e4a17ba9975d13a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/389","title":"Add Tree View and Store","createdAt":"2022-02-22T18:45:38Z"}
{"state":"Merged","mergedAt":"2022-12-05T19:41:13Z","number":3890,"body":"An attempt to consolidate majority of state / logic when it comes to contributors.\r\nMajority of state and logic at the view level is now held within a `ContributorsContext`\r\n\r\nThis context provides the list of selected contributors and multiple utilities to child components which are accessed by context hooks.\r\n\r\nThe full list of contributors itself is managed outside this context and is passed in as it can be updated outside the context of the webview (e.g. VSCode)\r\n\r\nThis PR does *not* fully implement contributors on the dashboard. Is primarily focused on refactoring contributors on VSCode + Web Extension first.\r\n","mergeCommitSha":"742af08299b4227d4522f0b5153b3ee773eb5406","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3890","title":"Refactor Contributors","createdAt":"2022-11-30T22:22:39Z"}
{"state":"Merged","mergedAt":"2022-11-30T23:08:18Z","number":3891,"mergeCommitSha":"a6334d34f717e0d3ae54577e2131257edb1b767f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3891","title":"Allow creating topics with score","createdAt":"2022-11-30T22:53:51Z"}
{"state":"Merged","mergedAt":"2022-12-01T00:00:24Z","number":3892,"body":"Renaming `Default` to `UserSelected` since this is more accurate, and adding a `Histogram` source so that we can compare the histogram approach to topic generation vs. other (ex ML) approaches.","mergeCommitSha":"c6193a4b2618d24dd0e50720e79fd0f6263c888a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3892","title":"Add histogram enum","createdAt":"2022-11-30T23:36:54Z"}
{"state":"Merged","mergedAt":"2022-12-01T00:28:55Z","number":3893,"body":"- no threads: no sourcemarks, just a waste of resources\n- no active team member with account: cannot successfully auth with API","mergeCommitSha":"4416653e377ccc699f29bed7228aff679c58a0ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3893","title":"Do not trigger jobs if no sourcemarks or no active team members","createdAt":"2022-12-01T00:11:51Z"}
{"state":"Merged","mergedAt":"2022-12-01T01:02:40Z","number":3894,"body":"The data is a little stale so we'll need to rerun the generation logic, but this adds the logic to show it.","mergeCommitSha":"5738d636e9341e44c6f74f2095b7c8e1dc883812","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3894","title":"Show top N topics generated by histogram method","createdAt":"2022-12-01T00:54:06Z"}
{"state":"Merged","mergedAt":"2022-12-01T01:21:07Z","number":3895,"body":"https://linear.app/unblocked/issue/UNB-791/sourcemark-diffparser-needs-to-handle-embedded-line-endings ","mergeCommitSha":"ee22af07e2ffb9609adb15390bd7a39085a19a81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3895","title":"Sourcemark DiffParser handle embedded carriage return characters","createdAt":"2022-12-01T00:57:21Z"}
{"state":"Merged","mergedAt":"2022-12-01T20:14:00Z","number":3896,"body":"Dropdown refactor from earlier broke the width implementation of the children items.\r\n\r\nbefore:\r\n<img width=\"307\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204942960-1c1470ed-0242-4540-a0b8-82ca367f2d31.png\">\r\n\r\nafter: \r\n<img width=\"312\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/204942976-c60623a5-9975-45bb-8936-4139e27693d5.png\">\r\n","mergeCommitSha":"d20df2033b147dd70b43c6e5780352da5db2dc33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3896","title":"Make all dropdown items full width","createdAt":"2022-12-01T01:21:36Z"}
{"state":"Merged","mergedAt":"2022-12-01T17:59:48Z","number":3897,"body":"This change just adds debugging so we can track down the source of the errors.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-681/snippetfinder-selection-range-is-outside-snippet-range\r\n\r\nhttps://sentry.io/organizations/nextchaptersoftware/issues/3750413542","mergeCommitSha":"ea342ba517a56b29831787496775f4f26c5ea508","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3897","title":"SnippetFinder: selection range is outside snippet range","createdAt":"2022-12-01T01:28:41Z"}
{"state":"Merged","mergedAt":"2022-12-01T22:27:08Z","number":3898,"body":"<img width=\"481\" alt=\"CleanShot 2022-12-01 at 12 15 40@2x\" src=\"https://user-images.githubusercontent.com/858772/205150945-670848de-75f8-41c1-9d2e-fa4710f5d990.png\">\r\n<img width=\"485\" alt=\"CleanShot 2022-12-01 at 12 15 22@2x\" src=\"https://user-images.githubusercontent.com/858772/205150956-38887775-85cd-4c96-ade1-fb7560d5554e.png\">\r\n","mergeCommitSha":"38a6a1f50f2d1d30570d61311a2d6ee12f99a3b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3898","title":"Add video tab to hub","createdAt":"2022-12-01T04:58:23Z"}
{"state":"Merged","mergedAt":"2022-12-01T06:33:02Z","number":3899,"mergeCommitSha":"adb97f818704f5eb8d4c4295ed3d27b98b70a813","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3899","title":"Remove score","createdAt":"2022-12-01T06:18:41Z"}
{"state":"Merged","mergedAt":"2022-01-17T18:41:26Z","number":39,"body":"This change adds 2 new services, the secret service and the identity service.\r\n\r\n## Problem\r\nThe auth service is a publicly exposed service. It should not house secrets, and especially not secrets used to mint identity tokens. \r\n\r\nIt should also not interact with GitHub on the final validation step because if popped it could \"fake\" the identity validation step with GitHub and launch a confused deputy attack on the secret service. \r\n\r\n## Proposal\r\n\r\nDefence in depth. We introduce secret service that manages interactions with GitHub to perform the code exchange and identity verification, and then finalizes the transaction by minting a codeswell  identity token.\r\n\r\n<img width=\"1622\" alt=\"CleanShot 2022-01-17 at 07 47 17@2x\" src=\"https://user-images.githubusercontent.com/858772/149800709-fd5eeb74-731b-4434-bcf3-308cfbb297c0.png\">\r\n","mergeCommitSha":"5ffb19a939e6a1578fa14c40d9bb022d0d340250","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/39","title":"Add secrets service to auth design","createdAt":"2022-01-17T05:11:10Z"}
{"state":"Merged","mergedAt":"2022-02-22T19:22:56Z","number":390,"body":"MessageTransformer was being exported within shared `webComponents`\r\n\r\nThis means when MessageTransformer is pulled in by Mock data generator which is used by VSCode, it pulls in web views which causes runtime issues.\r\n\r\n<img width=\"557\" alt=\"CleanShot 2022-02-22 at 11 02 17@2x\" src=\"https://user-images.githubusercontent.com/1553313/155202689-8c1ada51-41d8-473b-8ec1-aed44910b4ce.png\">\r\n\r\n<img width=\"618\" alt=\"CleanShot 2022-02-22 at 11 02 08@2x\" src=\"https://user-images.githubusercontent.com/1553313/155202696-7d1d25f1-03f9-485f-b3ab-309cd23cb091.png\">\r\n\r\nUpdated location of MessageTransformer into utils to fix issue.\r\n\r\n","mergeCommitSha":"08ac1738a4ed232a6efbd8fa912086c4059cded7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/390","title":"Update location of message transformer","createdAt":"2022-02-22T19:15:35Z"}
{"state":"Merged","mergedAt":"2022-12-01T18:44:48Z","number":3900,"body":"Likely going to replace `ThreadTopicModel` and `PullRequestTopicModel` with a single join table (`InsightTopicModel`?) so this code will likely go away, but let's add this so that we can show the insight count in the meantime.","mergeCommitSha":"abade7cae349740bc24ef3763f84c7e4496bd8e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3900","title":"Return insight count for topics","createdAt":"2022-12-01T06:44:39Z"}
{"state":"Merged","mergedAt":"2022-12-01T19:12:27Z","number":3901,"body":"We had combined fetching the topics with aggregating the topics and team members into one step, so whenever the team member list changed, it would trigger re-fetching the topics.\r\n\r\nThis separates the two steps out into separate streams, so now we only re-fetch topics when the logged in user changes, and we aggregate whenever the team member list changes.","mergeCommitSha":"df36cd09b7989e8d531c137f67d4efc47b534ed2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3901","title":"Don't re-fetch topics whenever auth token refreshes","createdAt":"2022-12-01T18:57:23Z"}
{"state":"Merged","mergedAt":"2022-12-01T19:42:02Z","number":3902,"body":"- consistently add repo fields as metadata\n- allow 5 seconds for logs to flush (hopefully)","mergeCommitSha":"9b6a5e3707252e8aa088a1430698100942943c41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3902","title":"Better source-agent logging","createdAt":"2022-12-01T19:41:37Z"}
{"state":"Merged","mergedAt":"2022-12-02T20:17:58Z","number":3903,"body":"* Use `getTopicRelatedInsights` temporarily until the new search API (https://github.com/NextChapterSoftware/unblocked/pull/3844)\r\n* Refactor TopicView to use new store \r\n* No change in UI ","mergeCommitSha":"d85a1bae476178b61f2ed0d6de1ae3d0723c4920","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3903","title":"Refactor using InsightStore","createdAt":"2022-12-01T20:12:50Z"}
{"state":"Merged","mergedAt":"2022-12-01T21:37:04Z","number":3904,"body":"https://app.logz.io/#/dashboard/cost?pageNumber=1&pageSize=10&table=logs\r\n\r\nFeel free to push back @davidkwlam @rasharab if you need these","mergeCommitSha":"6eebfa1d11deec8421564c3d6bf4517d975ba22d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3904","title":"Reduce top spammy logs","createdAt":"2022-12-01T21:26:44Z"}
{"state":"Merged","mergedAt":"2022-12-03T00:26:28Z","number":3905,"body":"<img width=\"647\" alt=\"CleanShot 2022-12-01 at 13 27 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/205166382-6b34bc83-b534-476d-a50f-14ca8e219f5f.png\">\r\n","mergeCommitSha":"edc5854aa6ae97b6a5d4ae08d08aa051d22a4466","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3905","title":"Web composer style","createdAt":"2022-12-01T21:50:58Z"}
{"state":"Merged","mergedAt":"2022-12-01T23:11:04Z","number":3906,"body":"That was a lot of text! ","mergeCommitSha":"74b71111f6489984c6eb41962100438e8b722201","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3906","title":"remove all sqs permissions","createdAt":"2022-12-01T22:30:49Z"}
{"state":"Merged","mergedAt":"2022-12-01T22:52:42Z","number":3907,"mergeCommitSha":"d9eaad76dfd0a0c15247d9ff9830102ba8c5a2fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3907","title":"Context logger has JOB_ID","createdAt":"2022-12-01T22:47:50Z"}
{"state":"Merged","mergedAt":"2022-12-02T18:42:18Z","number":3908,"body":"Fixes UNB-792\r\n\r\nWe were loading the highlight.js web worker bundle for every single separate `CodeBlock` element.  This bundle is big, the data was uncached, so a lot of network traffic happened, and the JS VM got seriously bogged down.  Debugging the dashboard was basically impossible, and the browser would occasionally kill the tab from memory and CPU pressure.\r\n\r\nWe didn't notice this before, because in most UIs we only syntax-hilighted once code block at a time (in the discussion thread view).  Now that we display groups of threads and PRs together this is a real problem.\r\n\r\nThis PR ensures the bundle is only loaded once\r\n- Use `PromiseProxy` to proxy promise requests from the dashboard to the web worker and back\r\n- Had to move `LazyValue` into the shared utils lib, but that's where it should have been in the first place","mergeCommitSha":"00e1a0607c79f595eea29bc1a0d2fbd71d30d145","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3908","title":"Only load highlight.js once in the dashboard","createdAt":"2022-12-01T23:36:43Z"}
{"state":"Merged","mergedAt":"2022-12-02T00:15:03Z","number":3909,"mergeCommitSha":"b5af461903d01bb4cd95e89e536144cd6ac3b17e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3909","title":"Add avatarUrl to Team API model","createdAt":"2022-12-01T23:58:35Z"}
{"state":"Merged","mergedAt":"2022-02-23T23:50:20Z","number":391,"body":"Add muxing of channel requests.  Included work:\r\n\r\n* Add `ChannelPoller` class, which tracks all channel updates globally.  This is generally used as a static instance so channel polling is shared.  In unit tests, a unique ChannelPoller is passed in for each test, so that channel polling state doesn't bleed between tests.\r\n* Add a mock \"PetStore\" API with a `getCats` and `getDogs` endpoint and `/cats` and `/dogs` channels.  This lets us test that channel muxing works as expected.  This is just a fake API without any spec, I thought this would be easier to test, as the test won't have to be changed whenever our API changes.\r\n* Add a bunch of unit tests for this that work against the mock PetStore API.  These verify that fetching and channel polling behaviour works as expected.","mergeCommitSha":"5a434ab6b1be518b4d9b79f078859a0b5d08560a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/391","title":"Muxer for channel-changed requests","createdAt":"2022-02-23T00:59:41Z"}
{"state":"Merged","mergedAt":"2022-12-02T01:35:13Z","number":3910,"mergeCommitSha":"ceea482e7a13c9d8c36c23886664560da81bb18d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3910","title":"Add avatar to walkthrough team selector","createdAt":"2022-12-02T00:36:49Z"}
{"state":"Merged","mergedAt":"2022-12-02T01:10:19Z","number":3911,"mergeCommitSha":"1732a422d5701b7ffaf83ae52b68b326b63607e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3911","title":"Update Draft Url","createdAt":"2022-12-02T00:57:52Z"}
{"state":"Merged","mergedAt":"2022-12-02T01:35:49Z","number":3912,"mergeCommitSha":"9992369e782676060f4c832e59e05b2c927e8fca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3912","title":"Add team to dashboard url path for videoDraft","createdAt":"2022-12-02T01:08:56Z"}
{"state":"Merged","mergedAt":"2022-12-02T22:16:33Z","number":3913,"mergeCommitSha":"5b848aaa9b8f440ce6311a7a3ea919bb2a6265b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3913","title":"Persist job status","createdAt":"2022-12-02T01:35:44Z"}
{"state":"Merged","mergedAt":"2022-12-02T04:34:25Z","number":3914,"body":"Need to use getAssetResponse Url, not create","mergeCommitSha":"9d3b56069e5c0ea2b973a49fc2870658a074d02d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3914","title":"Fetch AssetResponse","createdAt":"2022-12-02T04:15:05Z"}
{"state":"Merged","mergedAt":"2022-12-02T04:33:49Z","number":3915,"mergeCommitSha":"58a220241c9ec36a9956332e03429d487ff3150e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3915","title":"Fix avatars in team selector","createdAt":"2022-12-02T04:33:26Z"}
{"state":"Merged","mergedAt":"2022-12-02T04:44:41Z","number":3916,"body":"Instead of depending on Walkthrough app to foreground VScode (which we cannot depend on anymore since hub can start a walkthrough), VSCode foregrounds itself on complete video.","mergeCommitSha":"b6d7fcef4a70af93db295f49ec7112957d0c2cc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3916","title":"VSCode foregrounds itself on walkthrough complete","createdAt":"2022-12-02T04:36:47Z"}
{"state":"Merged","mergedAt":"2022-12-02T04:49:26Z","number":3917,"mergeCommitSha":"5b8ab9ef409c26b27ef52bcf5d1744222d7fb7e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3917","title":"optimize histogram","createdAt":"2022-12-02T04:49:22Z"}
{"state":"Merged","mergedAt":"2022-12-02T05:52:06Z","number":3918,"mergeCommitSha":"719e4e53ed95bb5c45f5c3182a96e3457c79a0f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3918","title":"Update","createdAt":"2022-12-02T05:51:51Z"}
{"state":"Merged","mergedAt":"2022-12-02T06:57:17Z","number":3919,"mergeCommitSha":"131f75d8cac03c86bbb0e1bf5e75451f1974fd91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3919","title":"Create topic page","createdAt":"2022-12-02T06:48:20Z"}