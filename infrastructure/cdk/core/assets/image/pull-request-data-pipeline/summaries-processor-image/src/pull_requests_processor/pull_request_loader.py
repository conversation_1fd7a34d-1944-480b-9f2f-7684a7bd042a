from datetime import datetime
import glob
import json
import logging

from typing import List

from pull_requests_processor.pull_request_types import PullRequest, PullRequestState


class PullRequestLoader:
    def load(
        self,
        input_glob: str,
        oldest_pr_number: int,
        oldest_datetime: datetime,
    ) -> List[PullRequest]:
        pull_requests: dict[str, PullRequest] = {}
        for file in sorted(glob.glob(input_glob)):
            logging.info(file)
            new_pull_requests = self.__load_file(
                file=file,
                oldest_pr_number=oldest_pr_number,
                oldest_datetime=oldest_datetime,
            )
            for new_pull_request in new_pull_requests:
                if not pull_requests.get(new_pull_request.html_url):
                    pull_requests[new_pull_request.html_url] = new_pull_request

        return list(pull_requests.values())

    def __load_file(
        self,
        file: str,
        oldest_pr_number: int,
        oldest_datetime: datetime,
    ) -> List[PullRequest]:
        pull_requests: List[PullRequest] = []
        with open(file) as f:
            for line in f:
                pull_request_json = json.loads(line)
                pull_request = self.__parse_pull_request(pull_request_json=pull_request_json)
                if (
                    pull_request.number >= oldest_pr_number
                    and pull_request.state
                    in (
                        PullRequestState.UNKNOWN,
                        PullRequestState.MERGED,
                    )
                    and pull_request.created_at >= oldest_datetime
                ):
                    pull_requests.append(pull_request)
        return pull_requests

    def __parse_pull_request(self, pull_request_json: dict) -> PullRequest:
        pull_request_comment = pull_request_json.get("comment") or {}
        pull_request_number = pull_request_json.get("number")
        pull_request_merge_commit_sha = pull_request_json.get("mergeCommitSha")
        pull_request_title = pull_request_json.get("title")
        pull_request_body = pull_request_json.get("body") or pull_request_json.get("description")
        pull_request_html_url = pull_request_json.get("htmlUrl")
        pull_request_state = PullRequestState.from_str(pull_request_json.get("state", "unknown"))
        pull_request_created_at = datetime.fromisoformat(pull_request_json.get("createdAt"))
        pull_request_merged_at = (
            datetime.fromisoformat(pull_request_json.get("mergedAt")) if pull_request_json.get("mergedAt") else None
        )

        return PullRequest(
            number=pull_request_number,
            merge_commit_sha=pull_request_merge_commit_sha,
            html_url=pull_request_html_url,
            title=pull_request_title,
            body=pull_request_body,
            state=pull_request_state,
            created_at=pull_request_created_at,
            merged_at=pull_request_merged_at,
        )
