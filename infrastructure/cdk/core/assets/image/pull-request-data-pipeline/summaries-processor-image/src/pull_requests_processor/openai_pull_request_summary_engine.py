from langchain.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, ChatPromptTemplate
from langchain_community.chat_models import ChatOpenAI
from langchain_core.runnables import RunnableSerializable

from pull_requests_processor.pull_request_types import PullR<PERSON><PERSON>, PullRequestSummaryResult
from pull_requests_processor.openai_constants import OPENAI_API_KEY, DEFAULT_OPENAI_CHAT_MODEL


class OpenAIPullRequestSummaryEngine:
    _chat_openai_llm: ChatOpenAI
    _llm_chain: RunnableSerializable
    _human_template = """Write a summary for a pull request using the provided git diff. Keep the summary below 2048 characters.

    {git_diff}
    """
    _system_template = """You are a helpful assistant used to describing the results of a git diff and listing the most affected files in the git diff.  Provide the output in three sections: Summary, Notable Changes and Affected Files. Do NOT preface the sections with titles.

In the summary section, you MUST follow these rules:
    1. Summarize the entirety of the pull request.
    2. Summarize notable changes across the files.

In the notable changes section, you MUST follow these rules:
    1. Enumerate the most significant changes across the git diff.

In the affected files section, you MUST follow these rules:
    1. List only the most affected files in the git diff sorted in descending order by the size of the diff change.
    2. Use the relative path.
    3. Do not list more than 10 files.
    4. Use markdown unordered list for representing the list.

Example Output;
Summary:
<summary>

Notable Changes:
<notable changes>

Affected Files:
<affected files>
    """
    _system_message_prompt = SystemMessagePromptTemplate.from_template(_system_template)
    _human_message_prompt = HumanMessagePromptTemplate.from_template(_human_template)
    _chat_messages = [
        _system_message_prompt,
        _human_message_prompt,
    ]
    _chat_prompt = ChatPromptTemplate.from_messages(_chat_messages)

    def __init__(self, openai_api_key: str = OPENAI_API_KEY):
        self._chat_openai_llm = ChatOpenAI(
            openai_api_key=openai_api_key,
            model=DEFAULT_OPENAI_CHAT_MODEL,
            temperature=0,
            max_tokens=1024,
        )
        self._llm_chain = self._chat_prompt | self._chat_openai_llm

    def predict(self, pull_request: PullRequest, git_diff: str) -> PullRequestSummaryResult:
        results = self._llm_chain.invoke(input={"git_diff": git_diff})
        return PullRequestSummaryResult(
            summary=results,
            pull_request=pull_request,
        )

    async def async_predict(self, pull_request: PullRequest, git_diff: str) -> PullRequestSummaryResult:
        results = await self._llm_chain.ainvoke(input={"git_diff": git_diff})
        return PullRequestSummaryResult(
            summary=results,
            pull_request=pull_request,
        )
