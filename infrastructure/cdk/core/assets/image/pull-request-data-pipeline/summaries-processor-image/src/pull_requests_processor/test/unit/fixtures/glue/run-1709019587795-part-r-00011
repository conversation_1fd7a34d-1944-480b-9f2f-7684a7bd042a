{"state":"Merged","mergedAt":"2022-07-20T04:50:31Z","number":2363,"mergeCommitSha":"de97e9fb59a8c3eeb0dae0b195465af9da23160d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2363","title":"Fix switch statement","createdAt":"2022-07-20T04:42:29Z"}
{"state":"Closed","mergedAt":null,"number":2364,"mergeCommitSha":"71b5cd2c125c135dca7afc966d585783497af463","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2364","title":"[Do not merge] Revert limiting priority jobs (just in case)","createdAt":"2022-07-20T04:51:54Z"}
{"state":"Merged","mergedAt":"2022-07-20T16:52:57Z","number":2365,"body":"![CleanShot 2022-07-19 at 22 36 28](https://user-images.githubusercontent.com/858772/179904929-e2461193-d6ee-41c6-bac6-d9be49d3faf6.gif)\r\n","mergeCommitSha":"1f5fb2157304c9fc82d6239cba8667cab1d1323f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2365","title":"Add notification flow to onboarding","createdAt":"2022-07-20T05:27:59Z"}
{"state":"Merged","mergedAt":"2022-07-20T13:32:52Z","number":2366,"body":"![CleanShot 2022-07-20 at 00 48 45](https://user-images.githubusercontent.com/1924615/179926964-9edc5f72-3bdd-40d7-8077-cb9d523bda16.png)\r\n","mergeCommitSha":"c4cc14589a5e1a17bfe4f47daf5b1ec0d4386253","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2366","title":"Show onboardings on admin console","createdAt":"2022-07-20T07:49:45Z"}
{"state":"Merged","mergedAt":"2022-07-20T17:51:24Z","number":2367,"mergeCommitSha":"c73ee11ca73846addf926c68b8e590e758b2cf22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2367","title":"update intercom apis","createdAt":"2022-07-20T17:26:34Z"}
{"state":"Merged","mergedAt":"2022-07-20T18:42:52Z","number":2368,"body":"Currently call `API.Persons.putOnboarding` whenever the \"org installation\" step is shown.\r\nThis occurs in main \"onboarding\" page & the sidebar onboarding page.\r\n\r\nWe want to only call this for the main onboarding flow as these PRs should be prioritized for the tutorial.\r\n\r\nFYI, putOnboarding will be called with *all* the repos in VSCode, even if they have already been onboarded.","mergeCommitSha":"8984558c886e8b8db381a250a122920b9f3beb31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2368","title":"Prioritize Initial Onboarding","createdAt":"2022-07-20T17:27:37Z"}
{"state":"Merged","mergedAt":"2022-07-20T18:14:31Z","number":2369,"mergeCommitSha":"d26dd1331e129c8b8e528f663ad4ce44c0c20da4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2369","title":"update templates","createdAt":"2022-07-20T17:51:15Z"}
{"state":"Merged","mergedAt":"2022-02-03T00:55:23Z","number":237,"mergeCommitSha":"a8de1ed6da104c4303d8fd129f35864029f9c5a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/237","title":"Disable CodeCov upload","createdAt":"2022-02-03T00:49:44Z"}
{"state":"Merged","mergedAt":"2022-07-20T18:23:09Z","number":2370,"body":"This broke one of the onboarding UIs a bit.","mergeCommitSha":"89a258dd9d11b6fe3c183ca265be81dd0cb9421f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2370","title":"Fix incorrect font size","createdAt":"2022-07-20T18:15:33Z"}
{"state":"Merged","mergedAt":"2022-07-22T04:50:19Z","number":2371,"body":"As requested by KCH","mergeCommitSha":"fd95558e1899a1e4a128f6d77bc2570279b964b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2371","title":"Don't trim thread titles","createdAt":"2022-07-20T19:24:13Z"}
{"state":"Merged","mergedAt":"2022-07-20T20:40:56Z","number":2372,"mergeCommitSha":"b0552b4d844c2bc59644d456dfc7b8cef4337634","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2372","title":"Lower Github rate limit","createdAt":"2022-07-20T20:37:42Z"}
{"state":"Closed","mergedAt":null,"number":2373,"mergeCommitSha":"3e66a03294c22c54254dfb3a510cd106b411cbc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2373","title":"Drop threshold to 500","createdAt":"2022-07-20T20:37:51Z"}
{"state":"Merged","mergedAt":"2022-07-20T21:33:28Z","number":2374,"body":"sorry \uD83D\uDE2C ","mergeCommitSha":"6f444d975309390df8776fcf91a4c7a3e53426f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2374","title":"Fix build","createdAt":"2022-07-20T21:33:15Z"}
{"state":"Merged","mergedAt":"2022-07-20T23:35:02Z","number":2375,"body":"Longer standing problem is that this stuff is not apparently clear.\r\nAs of now we have:\r\n1. Identities\r\n2. Persons\r\n3. TeamMembers\r\n4. Teams\r\n\r\nAmongst those we have:\r\n1. Teams that are deleted\r\n2. Team Members that are not part of the team.\r\n3. Team Members that have identities but do not have persons.\r\n\r\nNot sure what else I'm missing, but this rabbit hole is getting bigger...\r\n\r\nWe need to encapsulate this sort of logic into some sort of Kotlin DSL pattern.","mergeCommitSha":"dac443e70ef7da24dced1b93ee7bd26601697c1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2375","title":"Address team members with no accounts","createdAt":"2022-07-20T22:52:58Z"}
{"state":"Merged","mergedAt":"2022-07-20T23:23:28Z","number":2376,"body":"Remove Intercom from `https://getunblocked.com`, but keep it in the download / extensions pages.","mergeCommitSha":"b22b8e25af37398b453c429875ab3598456c067d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2376","title":"Remove intercom from doepicshit landing page","createdAt":"2022-07-20T23:01:50Z"}
{"state":"Merged","mergedAt":"2022-07-21T00:06:09Z","number":2377,"body":"## Old\r\n<img width=\"483\" alt=\"CleanShot 2022-07-20 at 16 10 31@2x\" src=\"https://user-images.githubusercontent.com/858772/*********-e3834370-7920-4842-ae44-d4eba244be6c.png\">\r\n\r\n## Button Changes\r\n<img width=\"500\" alt=\"CleanShot 2022-07-20 at 16 42 25@2x\" src=\"https://user-images.githubusercontent.com/858772/*********-0c46e164-f2eb-47d8-a851-c4ce26aa1288.png\">\r\n\r\n\r\n## On Disclosure Hover\r\n<img width=\"135\" alt=\"CleanShot 2022-07-20 at 16 42 32@2x\" src=\"https://user-images.githubusercontent.com/858772/180100858-861b577f-c1b0-4bdf-90e1-b64c5bdeddb0.png\">\r\n\r\n\r\n","mergeCommitSha":"fa280d189ebcd04614811d31d4f57efeac43590b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2377","title":"Update button styles","createdAt":"2022-07-20T23:08:35Z"}
{"state":"Merged","mergedAt":"2022-07-22T17:33:15Z","number":2378,"body":"Updates to VSCode 'Current File' panel:\r\n\r\n* Set correct title\r\n* Add resizing bar so the Current File panel can be resized\r\n* Add loading and \"no threads\" states","mergeCommitSha":"72ad48f680f88d046af8461650cb40503ec7bf78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2378","title":"VSCode 'Current File' panel, v2","createdAt":"2022-07-20T23:16:06Z"}
{"state":"Merged","mergedAt":"2022-07-21T17:57:10Z","number":2379,"body":"![CleanShot 2022-07-20 at 16 24 12](https://user-images.githubusercontent.com/13431372/180099197-36f13970-37b9-4e19-875b-f487fd240683.gif)\r\n\r\n<img width=\"1388\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/180100020-07582349-134a-4516-96b6-b6bb39ade759.png\">\r\n","mergeCommitSha":"47dab7fe56ee79adbc25dbc8bb364ad36ad04774","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2379","title":"Add loading view to dashboard ","createdAt":"2022-07-20T23:32:07Z"}
{"state":"Merged","mergedAt":"2022-02-03T02:55:43Z","number":238,"body":"Openapi tasks are dumb as shit when it comes to deleting generated output directories.\r\n\r\nWe now have added a custom openapi incremental cleanup task that monitors change to a spec file, and if there's a change, it will wipe out the generated directory.\r\n\r\nMultiple people have complained about problems with stagnant types lying around so this fixes that.\r\n\r\n```\r\nExecuting incrementally\r\nMODIFIED: private.yml\r\nDeleting openapi generated directory: /Users/<USER>/chapter2/unblocked/apiservice/build/generated because spec file has changed: /Users/<USER>/chapter2/unblocked/api/private.yml\r\n```\r\n","mergeCommitSha":"d8aa90dc7d80740d0efd21e962a47d68376b2c7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/238","title":"Make incremental builds smart with openapi generation","createdAt":"2022-02-03T02:38:20Z"}
{"state":"Merged","mergedAt":"2022-07-20T23:59:50Z","number":2380,"mergeCommitSha":"0659525383318c83935f17fa23f607cf8749b976","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2380","title":"make templates more gmail friendly","createdAt":"2022-07-20T23:44:39Z"}
{"state":"Merged","mergedAt":"2022-07-21T23:35:18Z","number":2381,"body":"- use git user information for this repo\r\n- limit use of log\r\n- remove ls-files, which is unbounded\r\n- remove use of `exec` because it does not handle shell escaping properly\r\n\r\nhttps://linear.app/unblocked/issue/UNB-456/exception-in-onboarding-caused-by-git-log","mergeCommitSha":"acbe89ce004de341ef4d62378039cfdfdf4658ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2381","title":"Fix Exception in onboarding caused by git log","createdAt":"2022-07-21T00:44:55Z"}
{"state":"Merged","mergedAt":"2022-07-21T04:15:59Z","number":2382,"body":"Fixes spam errors.\n\nhttps://app.logz.io/#/goto/578ef38c176dcb3a4de17e00e68ee901?switchToAccountId=411850","mergeCommitSha":"515cac0ef459cd94e973351f60bef220364ab936","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2382","title":"Fix for Unrecognized hub thread action errors","createdAt":"2022-07-21T03:44:54Z"}
{"state":"Merged","mergedAt":"2022-07-21T15:30:02Z","number":2383,"mergeCommitSha":"c4cbde966732e5c3137d13a8d20077a7356f64bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2383","title":"Fix thread unread count and partition team members","createdAt":"2022-07-21T05:34:47Z"}
{"state":"Merged","mergedAt":"2022-07-21T15:30:54Z","number":2384,"body":"- cleanup person page\n- show onboarding install intent time\n- cleanup stats page\n- show datatable footer info","mergeCommitSha":"58ce851ee13ff68437281a4f6433e1fe96f6bfca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2384","title":"Admin web improvements","createdAt":"2022-07-21T06:05:20Z"}
{"state":"Merged","mergedAt":"2022-07-21T17:11:07Z","number":2385,"mergeCommitSha":"bb562b2012143bdb10440e278694d20cd5d20a01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2385","title":"Clean up sendinblue","createdAt":"2022-07-21T16:55:16Z"}
{"state":"Merged","mergedAt":"2022-07-21T23:20:43Z","number":2386,"body":"\r\n<img width=\"505\" alt=\"CleanShot 2022-07-21 at 15 32 56@2x\" src=\"https://user-images.githubusercontent.com/858772/180326376-4cca0eee-2c3e-41d4-88ef-585d27c4be46.png\">\r\n\r\n<img width=\"520\" alt=\"CleanShot 2022-07-21 at 15 33 03@2x\" src=\"https://user-images.githubusercontent.com/858772/180326389-8c34f9ad-7961-4b72-9b3a-b9cb41ecf2d7.png\">\r\n\r\n\r\n![CleanShot 2022-07-21 at 11 27 54](https://user-images.githubusercontent.com/858772/180287110-e4a82e7b-4e8b-415d-9fc2-9ef12472fa7f.gif)\r\n","mergeCommitSha":"6cacc825978ee189fbd1b7948d2755ef77c3d535","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2386","title":"Allow Notifications banner when notifications turned off","createdAt":"2022-07-21T18:25:33Z"}
{"state":"Merged","mergedAt":"2022-07-21T20:28:02Z","number":2387,"body":"Stupid typo. This needs tests so badly.\r\n\r\nThe impact of this bug was pretty awful:\r\n1. any uncommitted changes in the repo would cause all sourcemarks to disappear\r\n2. even without uncommitted changes, the rendered sourcemark line position was the _original_ sourcemark, instead of the line position resolve for the HEAD commit.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-468/source-marks-disappear-for-the-entire-repo-when-a-source-code-edit","mergeCommitSha":"9b98fca4068ca44408c6b7dd00a2575efbaf2fe9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2387","title":"Source marks disappear from the entire repo with uncommitted changes","createdAt":"2022-07-21T19:23:28Z"}
{"state":"Merged","mergedAt":"2022-07-21T21:33:31Z","number":2388,"body":"![CleanShot 2022-07-21 at 12 49 56](https://user-images.githubusercontent.com/13431372/180303072-1d5c1c76-31c7-4513-a935-3bf987f062da.gif)\r\n\r\n\r\n","mergeCommitSha":"25d6a80ecb2eb2bbb84c4cf374756fafa89ac119","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2388","title":"UNB-466 [Onboarding] always close the sidebar and fix filtering logic","createdAt":"2022-07-21T19:50:32Z"}
{"state":"Merged","mergedAt":"2022-07-26T21:20:53Z","number":2389,"body":"Using native GIF rendering. Seems to be ok\r\n\r\n![CleanShot 2022-07-26 at 09 12 46](https://user-images.githubusercontent.com/858772/181057354-b49de0fc-76f1-4461-9966-56e7c2f213af.gif)\r\n\r\n![CleanShot 2022-07-26 at 09 12 06](https://user-images.githubusercontent.com/858772/181057407-d74f222d-acea-4d22-83cb-ba8b7dc3677a.gif)\r\n\r\n","mergeCommitSha":"5865e42505c68f714c9b463b0a0052675126d979","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2389","title":"Animate notifications onboarding panel","createdAt":"2022-07-21T20:35:01Z"}
{"state":"Merged","mergedAt":"2022-02-04T01:12:52Z","number":239,"body":"## Problem\r\nBecause `/login/options` is now an API call, there's no way to set the secret cookie, which is necessary to prevent XSS attacks. \r\n\r\n## Proposal\r\nRe-introduce the `/login` endpoint, and modify the `/login/options` response to contain urls that point to the `/login` endpoint. \r\n\r\nWhen browsers hit the `/logon` endpoint, the service will set the secret cookie and redirect the browser to the auth provider. \r\n\r\nThe flow now looks like this:\r\n![image](https://user-images.githubusercontent.com/858772/152275852-d971f36b-5e69-4689-849a-f7dacd13f45f.png)\r\n","mergeCommitSha":"ed543c5c18b9dd2b683183397178d320304abc6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/239","title":"Re-add login endpoint to bounce the browser and set cookies","createdAt":"2022-02-03T03:18:15Z"}
{"state":"Merged","mergedAt":"2022-07-21T22:28:33Z","number":2390,"mergeCommitSha":"2060a5035fb044b31c2fa1cd60510ccec1e9a58c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2390","title":"Dashboard bug fixes","createdAt":"2022-07-21T22:01:52Z"}
{"state":"Merged","mergedAt":"2022-07-22T23:57:04Z","number":2391,"body":"Setup basic onboarding flow for web extension.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/180325547-9e319b81-a1c9-4933-8ad8-9e2117997d40.mp4\r\n\r\n\r\nTODO in subsequent PRs: \r\nhttps://linear.app/unblocked/issue/UNB-479/update-installredirect-to-properly-handle-webextension-onboarding\r\nhttps://linear.app/unblocked/issue/UNB-473/update-web-extension-sidebar-intro-states","mergeCommitSha":"8e42c2c4d3f24129939b64e2a5c68bc2a1124994","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2391","title":"Github App onboarding for web extension","createdAt":"2022-07-21T22:30:43Z"}
{"state":"Closed","mergedAt":null,"number":2392,"body":"Part of the effort to move towards bulk ingestion of pull requests.","mergeCommitSha":"77a7135e998254ea850c3480e161328fab3a24d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2392","title":"Add streaming requests for pull requests and pull request comments","createdAt":"2022-07-21T23:02:55Z"}
{"state":"Closed","mergedAt":null,"number":2393,"body":"The idea is here is to allow quick caching of pull requests and comments during bulk ingestion. When individual pull request ingestion jobs are being processed, the cache can be checked and IFF there's a miss will we go out to the API.\r\n\r\nThis cache will also be written to by webhook handlers while we're undergoing bulk ingestion, so that we don't lose updates while an object is in the cache but not in the database.\r\n\r\nThis cache is designed to handle multiple versions of the same object written to the cache. The idea is to not evict on write (as that is slower and can't be atomic without some scripting as I understand) but instead just returns the latest version on read.\r\n\r\nEach key-value has a TTL of 1 week, which should give us plenty of time to write it to the database.","mergeCommitSha":"c706f7733c5393eb6ebba2bf3410fcbc0ed84740","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2393","title":"Create redis cache for bulk ingested pull requests and review comments","createdAt":"2022-07-21T23:33:54Z"}
{"state":"Merged","mergedAt":"2022-07-22T00:03:43Z","number":2394,"mergeCommitSha":"54f7a9dcb50192b5afd4284a5064fbc491a9bb02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2394","title":"Change discussion to messages in allow banner text","createdAt":"2022-07-22T00:03:06Z"}
{"state":"Merged","mergedAt":"2022-07-22T00:39:56Z","number":2395,"body":"This means that hard-deleted threads will no longer appear in the editor gutter, which matches the behaviour in the web extension","mergeCommitSha":"b56c2eadb44e640a78571b029b52fb6a568eedea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2395","title":"Filter deleted threads from gutter renderer","createdAt":"2022-07-22T00:25:18Z"}
{"state":"Merged","mergedAt":"2022-07-22T02:00:57Z","number":2396,"body":"There are so many ways this can be fucked if we continue to do this manually.\r\n\r\nAutomation now, automation for life.","mergeCommitSha":"8a062a74e73961c6a62fe0aedf115d687a4ac6e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2396","title":"Automate secrets deployment","createdAt":"2022-07-22T01:56:05Z"}
{"state":"Merged","mergedAt":"2022-07-22T04:08:01Z","number":2397,"body":"Very basic release note helper tool:\r\n1. Copy and paste Product SHAs from two versions into the _from_ and _to_ fields\r\n2. Click **Compare** which brings you to GitHub's commit compare view\r\n<img width=\"900\" alt=\"Screen Shot 2022-07-21 at 19 20 07\" src=\"https://user-images.githubusercontent.com/1798345/180348654-b6f6c734-d5f8-4bcd-9b56-517a37bba63e.png\">\r\n\r\n<img width=\"900\" alt=\"Screen Shot 2022-07-21 at 19 20 35\" src=\"https://user-images.githubusercontent.com/1798345/180348714-d4031b07-c3f5-455b-a883-f486bdf14d2a.png\">\r\n\r\n\r\n→ https://github.com/NextChapterSoftware/unblocked/compare/1f5fb21...54f7a9d","mergeCommitSha":"1f6020ea4e7b4b117c306ee19e82bef8daa44d4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2397","title":"Admin web compare versions to generate release notes","createdAt":"2022-07-22T02:15:26Z"}
{"state":"Merged","mergedAt":"2022-07-25T16:18:32Z","number":2398,"body":"Eliminates 3 git calls everytime we update sourcemarks for a file.\r\n\r\n(update = load file, edit file, save file, or create thread in file)\r\n\r\nhttps://linear.app/unblocked/issue/UNB-431/source-mark-resolutionrendering-for-is-slow-to-load-for-file","mergeCommitSha":"935d669c511f19245d3a4087225761af86bf726f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2398","title":"Optimize cache lookup performance of RepoResolver","createdAt":"2022-07-22T04:13:33Z"}
{"state":"Merged","mergedAt":"2022-07-22T18:49:11Z","number":2399,"mergeCommitSha":"be1345d6b69ed4b0d808cae0d843bb9faebddf62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2399","title":"re-enable auto-ingestion","createdAt":"2022-07-22T16:35:16Z"}
{"state":"Merged","mergedAt":"2022-01-08T00:43:01Z","number":24,"body":"\r\n<img width=\"636\" alt=\"CleanShot 2022-01-07 at 14 45 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/148616923-32e08dfc-9efd-4ed7-b40c-8125e7ea06b5.png\">\r\n\r\nSetup Codeblocks with highlight.js\r\n\r\nMoved highlighting to web worker to avoid slowdowns when dealing with large chunks of code.\r\nWill start off initially with unhighlighted code and swap out highlighted code once ready.","mergeCommitSha":"1e38ac88abd7f00e6e42bd0813206a7eca6e108e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/24","title":"Code block","createdAt":"2022-01-07T21:41:55Z"}
{"state":"Merged","mergedAt":"2022-02-03T05:16:38Z","number":240,"body":"1. Update global config environment variable name\r\n2. Fix up readme","mergeCommitSha":"69fd4ac4b2538706e3029246d36296247226f3c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/240","title":"Rename k8s secret to GITHUB_APP_KEY","createdAt":"2022-02-03T05:11:15Z"}
{"state":"Merged","mergedAt":"2022-07-22T17:32:23Z","number":2400,"mergeCommitSha":"17ffb426ba765d2d38a9db9e9292dd38fc5bf8df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2400","title":"revert changes","createdAt":"2022-07-22T17:32:15Z"}
{"state":"Closed","mergedAt":null,"number":2401,"mergeCommitSha":"acb0ee85c84b782901bee685c11045bd262cd1ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2401","title":"Fix onboarding step","createdAt":"2022-07-22T17:35:15Z"}
{"state":"Merged","mergedAt":"2022-07-22T22:35:14Z","number":2402,"mergeCommitSha":"141875923afd141e6afa7586c1ca03cb5810818e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2402","title":"Fix onboarding","createdAt":"2022-07-22T17:42:28Z"}
{"state":"Merged","mergedAt":"2022-07-22T20:07:05Z","number":2403,"body":"https://user-images.githubusercontent.com/13431372/180499637-a2b5f53c-8674-4a15-ba69-cffc098ff27d.mp4\r\n\r\n","mergeCommitSha":"a3854f63c7053e1355261bfe52f3eaef9d68ba14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2403","title":"Don't show select checkbox if there are <1 contributors","createdAt":"2022-07-22T18:16:16Z"}
{"state":"Merged","mergedAt":"2022-07-22T20:01:12Z","number":2404,"body":"https://linear.app/unblocked/issue/UNB-469/expo-onboarding-activity-panel-needs-to-be-detected\r\n\r\n![CleanShot 2022-07-22 at 11 59 42](https://user-images.githubusercontent.com/13431372/180505922-939a86cb-15b1-49cc-b97a-c6a87db5d258.gif)\r\n","mergeCommitSha":"8e5cb53f9e3f84ca24d69302b1bc0fcb10545037","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2404","title":"UNB-469 Open activity bar at end of onboarding","createdAt":"2022-07-22T18:55:33Z"}
{"state":"Merged","mergedAt":"2022-07-22T19:24:03Z","number":2405,"mergeCommitSha":"7bd2dc4fe0a44685a48b935901e321af8a2f6e37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2405","title":"Enable campaigns for dev","createdAt":"2022-07-22T19:10:23Z"}
{"state":"Merged","mergedAt":"2022-07-22T22:29:52Z","number":2406,"mergeCommitSha":"00d7ea2ff5d4ac663d20223954543fa2829915bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2406","title":"Hide extension banner once extension is installed","createdAt":"2022-07-22T20:36:40Z"}
{"state":"Merged","mergedAt":"2022-07-22T20:37:32Z","number":2407,"mergeCommitSha":"1f4a7e8756b47870b486b01d634c3b028ea0885f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2407","title":"disable verbose","createdAt":"2022-07-22T20:37:27Z"}
{"state":"Merged","mergedAt":"2022-07-22T21:28:30Z","number":2408,"mergeCommitSha":"347d55c7a08c937e3bf55dd3ffa0e3abc40736d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2408","title":"Allow for senders to be provided via email templates","createdAt":"2022-07-22T21:09:10Z"}
{"state":"Merged","mergedAt":"2022-07-26T06:40:01Z","number":2409,"body":"- Moves Git functionality to Git-layer\r\n\r\n- Fixes bugs in Git blame:\r\n   - We were only looking at the first line in range\r\n   - Line range was off by one, so we were blaming the wrong line\r\n   - Should have excluding whitespace and detecting moves\r\n\r\n- We were excluding Git identities that have a no-reply email. This is\r\n  fine for people that do not have unblocked accounts, but _not_ for\r\n  people who have unblocked accounts.\r\n\r\n- Add tests\r\n\r\n- Related to:\r\n  https://linear.app/unblocked/issue/UNB-455/expo-onboarding-remove-dependency-on-vscode-git-extension\r\n","mergeCommitSha":"51c415640e23f6bd0b29ac744b923a20bafd8e3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2409","title":"Refactor to separate shell-Git and extension-plugin-Git and fix blame bugs","createdAt":"2022-07-22T21:38:26Z"}
{"state":"Merged","mergedAt":"2022-02-03T08:10:04Z","number":241,"body":"Implements two requests:\r\n\r\n`installations()` -> returns list of installs for github app (unpaginated for now, 100 results max)\r\n`installationAccessToken(installationId: String)` -> returns install access token for install specific queries \r\n\r\nI have not implemented the token cache yet. The `GitHubAppClient` is dumb and is not cache aware. I'll drop a caching layer in front of the client in `GitHubApiImpl`","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/241","title":"Implements github install access token request","createdAt":"2022-02-03T06:04:57Z"}
{"state":"Merged","mergedAt":"2022-07-26T17:26:07Z","number":2410,"body":"Right now there's only a `PrComment` model on the Thread object, meaning that the hyperlinks in each message UI always leads to the first comment of the pr comment thread. Each message should have its own `PrComment` model and further its own `commentHtmlUrl`.\r\n\r\n* There's a bit of client side work to migrate from using the deprecated fields in the prComment model.","mergeCommitSha":"d8c30bb7e7ad66ec4b4d606ecf46fbf3eb9125f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2410","title":"Add prCommentUrl to MessageLinks model","createdAt":"2022-07-22T21:56:11Z"}
{"state":"Merged","mergedAt":"2022-07-25T16:59:18Z","number":2411,"body":"This PR dumps our use of NSCache for the data stores. There is migration logic so that we don't end up logging people out and trashing their data when they upgrade.\r\n\r\nThe code repetition is a bit hairy. I'm going to spend a morning next week DRYing up this codebase, including this tomfoolery","mergeCommitSha":"079037ec03a2472a2a0e29b95a2d25fe559a98fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2411","title":"NSCache makes Peter sad","createdAt":"2022-07-22T22:56:01Z"}
{"state":"Merged","mergedAt":"2022-07-22T23:59:10Z","number":2412,"body":"Dennis ask, i'm not doing this shit manually.","mergeCommitSha":"b9679cd2f28ee3c82c598916acf611f8c124e708","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2412","title":"Mark users as internal","createdAt":"2022-07-22T23:34:53Z"}
{"state":"Merged","mergedAt":"2022-07-23T00:35:49Z","number":2413,"mergeCommitSha":"f763d96805f7dc12a4d6964bef2bf79f758aafeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2413","title":"Secrets were being printed out during deployment","createdAt":"2022-07-23T00:35:24Z"}
{"state":"Merged","mergedAt":"2022-07-25T17:25:08Z","number":2414,"body":"Final updates for this UI:\r\n\r\n* Add as a webview in the file explorer.  This was more involved then I expected, I had to refactor some of the existing UI, but the guts of the code is reused in the two views.\r\n* Add an animated image to the 'no threads in this file' state\r\n<img width=\"1331\" alt=\"Screen Shot 2022-07-25 at 9 08 58 AM\" src=\"https://user-images.githubusercontent.com/2133518/180825100-491fe76a-34f6-42bb-ae2d-55d1db071956.png\">\r\n<img width=\"1602\" alt=\"Screen Shot 2022-07-25 at 9 09 08 AM\" src=\"https://user-images.githubusercontent.com/2133518/180825121-6e3b919b-eefc-42c1-a2fe-1eb7abf9aa6f.png\">\r\n\r\n","mergeCommitSha":"073c39dd333259d6776a7898562b1f0e288660a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2414","title":"VSCode 'Current File' UI V3","createdAt":"2022-07-25T16:11:41Z"}
{"state":"Merged","mergedAt":"2022-07-25T16:43:47Z","number":2415,"body":"Changes it to:\r\n\r\n<img width=\"765\" alt=\"CleanShot 2022-07-25 at 09 15 39@2x\" src=\"https://user-images.githubusercontent.com/1924615/180825904-df17342d-3494-4815-abc3-880dd978685b.png\">\r\n","mergeCommitSha":"bd6e58098443d8542f7d159ac3ddddcb9beb7f44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2415","title":"Update signature","createdAt":"2022-07-25T16:17:33Z"}
{"state":"Merged","mergedAt":"2022-07-25T18:06:55Z","number":2416,"mergeCommitSha":"f9989d3874f75772be1d8bf3a5a4b7c2f0548459","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2416","title":"Admin web reorder team members so Unblocked users are shown first","createdAt":"2022-07-25T16:54:12Z"}
{"state":"Merged","mergedAt":"2022-07-25T17:06:35Z","number":2417,"mergeCommitSha":"707cd69e6fb7b91e40187150f1df2191f584c413","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2417","title":"Always check for updates","createdAt":"2022-07-25T16:54:24Z"}
{"state":"Merged","mergedAt":"2022-07-25T17:06:16Z","number":2418,"mergeCommitSha":"158d13c11b41ec645275ced01f23c02eddcefd6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2418","title":"Enable sendinblue in production","createdAt":"2022-07-25T17:05:52Z"}
{"state":"Merged","mergedAt":"2022-07-25T17:41:02Z","number":2419,"body":"Missed Web client for :https://github.com/NextChapterSoftware/unblocked/pull/2311","mergeCommitSha":"d86a82e33a869051afda33d1103d472b7176549e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2419","title":"Update web to use commentHtmlUrl","createdAt":"2022-07-25T17:08:15Z"}
{"state":"Merged","mergedAt":"2022-02-03T21:41:04Z","number":242,"mergeCommitSha":"d74fb8631eaca8910a8c90693b6edd2f27137a5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/242","title":"TeamMembers API","createdAt":"2022-02-03T15:30:36Z"}
{"state":"Merged","mergedAt":"2022-07-25T17:37:23Z","number":2420,"mergeCommitSha":"f0fba8c57904018d2b365ff9271677662d5848f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2420","title":"Show upgrade regardless of onboarding status","createdAt":"2022-07-25T17:35:30Z"}
{"state":"Merged","mergedAt":"2022-07-25T18:06:58Z","number":2421,"body":"https://linear.app/unblocked/issue/UNB-425/show-the-source-code-known-to-have-the-sourcemark-in-a-read-only","mergeCommitSha":"9473abcaf1ed7258c9798dd59b290ed0b12bb689","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2421","title":"Git utility to get file content at any revision","createdAt":"2022-07-25T17:56:41Z"}
{"state":"Merged","mergedAt":"2022-07-26T19:55:55Z","number":2422,"body":"<img width=\"1522\" alt=\"Screen Shot 2022-07-25 at 11 23 02 AM\" src=\"https://user-images.githubusercontent.com/94733135/180847517-d4e99c24-1ca8-4407-bf16-c3b702d3fa51.png\">\r\n\r\nAdds a download button to version page. Download links are generate using S3 signed urls valid for one hour. ","mergeCommitSha":"1b7369eb474955bfe75683cadfaf0f1142374dfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2422","title":"add download button for versions","createdAt":"2022-07-25T18:24:47Z"}
{"state":"Merged","mergedAt":"2022-07-26T04:10:49Z","number":2423,"body":"I've ranked it at the same level as thread title but we can change later.\r\n\r\nWill require re-indexing (but not re-ingestion)","mergeCommitSha":"2c347d463cafe5e8c9c6fbede4393c38158dfd22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2423","title":"Make threads searchable by PR title edit","createdAt":"2022-07-25T19:05:18Z"}
{"state":"Merged","mergedAt":"2022-07-25T21:04:44Z","number":2424,"body":"Massive amount of exceptions in logz.io that is polluting our environments.\r\nWe should be using insertIgnore to handle uniqueness failures for now.","mergeCommitSha":"81baeba79b9ccd635b7d8d1f5854f414f36fdf5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2424","title":"Only spit exceptions when necessary when inserting versions","createdAt":"2022-07-25T19:59:56Z"}
{"state":"Merged","mergedAt":"2022-07-25T21:02:39Z","number":2425,"body":"<img width=\"584\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/180866519-768adafb-c145-46e1-8e1f-f34841996661.png\">\r\n\r\n(Part of work to support the mentions invite flow feature)\r\n","mergeCommitSha":"9a4b2db45d5d26fb9b755d0135fb7c4cc0b4757b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2425","title":"Add styled modal component to vscode","createdAt":"2022-07-25T20:16:31Z"}
{"state":"Merged","mergedAt":"2022-07-25T20:20:08Z","number":2426,"body":"Hacky fix","mergeCommitSha":"5aaa979127a775b5bc7d52a1f62d880edfa37e31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2426","title":"Fetch lots more source marks","createdAt":"2022-07-25T20:19:37Z"}
{"state":"Merged","mergedAt":"2022-07-25T23:19:15Z","number":2427,"body":"Just used for debug right now. Simple fix.","mergeCommitSha":"df8081bf44af14688e0f61111ee6a228432c9de2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2427","title":"Support for multiple root commits","createdAt":"2022-07-25T21:54:37Z"}
{"state":"Merged","mergedAt":"2022-07-27T19:42:12Z","number":2428,"body":"https://user-images.githubusercontent.com/1553313/180885603-a57ab018-d662-442b-a088-df83a202b89b.mp4\r\n\r\n<img width=\"673\" alt=\"CleanShot 2022-07-26 at 12 42 15@2x\" src=\"https://user-images.githubusercontent.com/1553313/181098354-1629c39a-3499-40dc-b0db-3df887d8f40d.png\">\r\n\r\nNext steps are to get a recommended repo and redirect to repo.\r\n\r\nComplete unb-494","mergeCommitSha":"455d7490c22a4af4a6bbce342f5fb15e51a07dfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2428","title":"Login dialog after web extension installation","createdAt":"2022-07-25T22:37:14Z"}
{"state":"Merged","mergedAt":"2022-07-26T00:26:49Z","number":2429,"body":"After GH App installation in web extension, we want the popup page to end at this install/redirect UI.\r\nWeb extension will eventually close the popup once it's received a notification that the GH app has been installed\r\n\r\n<img width=\"1060\" alt=\"CleanShot 2022-07-25 at 15 59 13@2x\" src=\"https://user-images.githubusercontent.com/1553313/180889004-13f316c9-7e9c-4cb6-af55-75ab1bffcf9c.png\">\r\n\r\nVSCode GH App Installation triggers same component but has a redirectLocation which renders different messaging.","mergeCommitSha":"1347fd40e9bae229e4a2f00a26b833ed3bcd2c4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2429","title":"Update text for missing redirect flow","createdAt":"2022-07-25T23:04:41Z"}
{"state":"Merged","mergedAt":"2022-02-03T17:27:35Z","number":243,"mergeCommitSha":"994b9c320add217f55b1e50198d469650250a151","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/243","title":"Person API implementation","createdAt":"2022-02-03T15:31:16Z"}
{"state":"Merged","mergedAt":"2022-07-25T23:25:05Z","number":2430,"body":"Support for multiple root commits\n\nMock out logger\n\nBetter undefined behaviour","mergeCommitSha":"a49ba9d120d49dc78ecb25863d92d60b8590519e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2430","title":"Support for multiple root commits 2","createdAt":"2022-07-25T23:22:53Z"}
{"state":"Merged","mergedAt":"2022-07-25T23:45:21Z","number":2431,"body":"This mocks out the logger globally so we don't have to keep adding it everywhere","mergeCommitSha":"2186ca1401bf43c315e0ddf0f487f33891534788","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2431","title":"Mock out Winston logger in all tests","createdAt":"2022-07-25T23:36:36Z"}
{"state":"Merged","mergedAt":"2022-07-26T18:40:41Z","number":2432,"body":"<img width=\"391\" alt=\"CleanShot 2022-07-25 at 17 11 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/180895638-bafdace3-c263-47da-bfb8-e7175c3befdf.png\">\r\n\r\n\r\n<img width=\"399\" alt=\"CleanShot 2022-07-25 at 17 02 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/180895555-7a795e44-c0bc-4094-99df-87995973bd09.png\">\r\n\r\n","mergeCommitSha":"5ca02dd2450b7f092b34ed1ce46d0d3670a9c67d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2432","title":"Update intro state to Sofia pro","createdAt":"2022-07-26T00:12:08Z"}
{"state":"Merged","mergedAt":"2022-07-26T16:49:43Z","number":2433,"body":"Part 1 of a multi-part endeavour. This adds an optional description text field to the `VersionInfoModel`, which is expected to store Markdown text. \r\n\r\nThe Hub will render the markdown using a 3rd party Markdown rendering engine like https://github.com/kyle-n/HighlightedTextEditor \r\n\r\nNext after this PR: Adding description editing capabilities to the admin console, and giving the Hub rendering super-powers.\r\n\r\nThen after that: description composites at the API layer when the client is > 1 version behind","mergeCommitSha":"4eeeb61059f149292679472c19462f620fc33f5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2433","title":"Add version info description","createdAt":"2022-07-26T03:31:13Z"}
{"state":"Merged","mergedAt":"2022-07-26T16:07:19Z","number":2434,"body":"Absolutely gruesome hack, but it works, and doesn't seem to do much to CPU and power (I guess `Task` is pretty intelligent):\r\n\r\n- Not enough to just reset the window level. To AppKit, all bets are off once the popover's window level is set manually. We initially set it to `.normal` to facilitate the system prefs z-order on top, but then to reset the level we have to close and re-open the hub with the animation flag turned off.\r\n- There's really no other way to listen for active status of another app besides polling it. The alternative is a much more gruesome and definitely not future proof KVO hack.","mergeCommitSha":"491fcb7d1b95fd3e3ae79795b2ce971d5ee86c63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2434","title":"Fixes hub going to the background when system prefs dismissed","createdAt":"2022-07-26T05:11:13Z"}
{"state":"Merged","mergedAt":"2022-07-26T20:16:27Z","number":2435,"body":"For bulk ingestion of pull requests, we're going to start creating database records for every pull request, not just the ones with threads.\r\n\r\nThe `getPullRequests` API operation returns all open PRs. Let's make sure it keeps returning PRs with threads, since it's used to create tree nodes in the sidebar.","mergeCommitSha":"31720b941ee4b46029ab82ee759fa746fb54ac92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2435","title":"PullRequestStore.findOpen only returns pull requests with threads","createdAt":"2022-07-26T06:58:07Z"}
{"state":"Merged","mergedAt":"2022-07-26T18:45:37Z","number":2436,"body":"Introduce 3 new APIs:\r\n- getRepo\r\n- getRepoStats\r\n- getAllRepoStats\r\n\r\nhttps://linear.app/unblocked/issue/UNB-488/api-to-recommend-repos-by-conversation-count\r\n\r\n<img width=\"755\" alt=\"Screen Shot 2022-07-26 at 11 27 04\" src=\"https://user-images.githubusercontent.com/1798345/181083608-f5ede5e0-1091-49c0-bc85-b64105cf2b90.png\">\r\n","mergeCommitSha":"8397d40be4b6f5c6e20020f2bd7c5d968ac0c359","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2436","title":"API to recommend repos by conversation count","createdAt":"2022-07-26T07:27:57Z"}
{"state":"Merged","mergedAt":"2022-07-26T17:08:53Z","number":2437,"body":"Utility that attempts to use git log -> contributors to map a name to an email.\r\nNecessary so that users don't have to explicitly type in emails all the freaking type when they're @mention'ing a team member without an account.","mergeCommitSha":"abb07f5e596a435530b2ef6639200774bf8a1914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2437","title":"Stupid little utility to recommend contributor based off name","createdAt":"2022-07-26T14:51:52Z"}
{"state":"Merged","mergedAt":"2022-07-26T17:20:59Z","number":2438,"body":"Fixes UNB-497\r\n\r\n* Remove the animated image in the Current File UI\r\n* Set the background for the 'Contact Support' pane to match the Current File UI background.  This only takes effect in the regular Sidebar UI\r\n\r\n<img width=\"1610\" alt=\"Screen Shot 2022-07-26 at 9 59 39 AM\" src=\"https://user-images.githubusercontent.com/2133518/*********-176ac2e7-02e8-46c6-9a47-27c3b75b88a5.png\">\r\n ","mergeCommitSha":"803d4781fb2b9f685f688fef023ec7767644640a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2438","title":"Remove Current File UI image","createdAt":"2022-07-26T17:00:49Z"}
{"state":"Merged","mergedAt":"2022-07-26T17:34:34Z","number":2439,"body":"<img width=\"1001\" alt=\"CleanShot 2022-07-26 at 10 03 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/181067270-e7b36e73-cccc-44c6-914a-0ebd181f8bfe.png\">\r\n<img width=\"1186\" alt=\"CleanShot 2022-07-26 at 10 03 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/181067277-f3c0d902-6505-45d4-abbf-c704d50e83f0.png\">\r\n\r\n\r\nScope web extension to just github.com, and not subdomains.\r\naka will not show up in docs.github.com or api.github.com etc...","mergeCommitSha":"61a8194687c58b21a94da2f50cdd1f1e80ddc9d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2439","title":"Only scope to github.com","createdAt":"2022-07-26T17:04:40Z"}
{"state":"Merged","mergedAt":"2022-02-03T18:22:40Z","number":244,"mergeCommitSha":"d525e1aa48bd6b6df9f664223239acc2ddf825fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/244","title":"Improve documentation","createdAt":"2022-02-03T18:03:59Z"}
{"state":"Merged","mergedAt":"2022-07-26T17:39:35Z","number":2440,"mergeCommitSha":"3a6b347318cf56a079c17c9dddf417e2eb23a5ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2440","title":"Allow for retrieving all contributors associated with name","createdAt":"2022-07-26T17:22:13Z"}
{"state":"Merged","mergedAt":"2022-07-26T18:17:32Z","number":2441,"mergeCommitSha":"30aa76f0320c689d633ee7e1041b8c64ea7f6919","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2441","title":"Threads are searchable by pull request number","createdAt":"2022-07-26T18:00:06Z"}
{"state":"Merged","mergedAt":"2022-07-27T17:39:50Z","number":2442,"body":"Update selector to be more generic.\r\n\r\nWe were also only disabling turbo when one entered the files pages in a PR.\r\nUpdated to disable turbo on the anchor elements the moment one enters the PR view.\r\n\r\nFixes UNB-498\r\n","mergeCommitSha":"b64c98493dd3ac6fe9d9995162adc66b623d7580","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2442","title":"Disable turbo events more aggressively","createdAt":"2022-07-26T18:30:03Z"}
{"state":"Merged","mergedAt":"2022-07-26T20:38:10Z","number":2443,"body":"https://linear.app/unblocked/issue/UNB-488/api-to-recommend-repos-by-conversation-count","mergeCommitSha":"abf08247d3e936103957f8cb15a6f95ac93dea7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2443","title":"Implementation of getRepo","createdAt":"2022-07-26T19:10:28Z"}
{"state":"Closed","mergedAt":null,"number":2444,"body":"The core issue is described here: https://chapter2global.slack.com/archives/C036YH3QF7T/p1658856665704539\r\n\r\n---\r\nThe expected behaviour for pagination is:\r\n* For each per-second poll, the client makes up to n page fetches.  We store the lastModified from the last-fetched page.\r\n* On the next poll, we use this lastModified to see if there are any more pages to fetch.\r\n* The expectation is that this will let us eventually load all the data, loading roughly n pages every second or two\r\n\r\nThe bug is a corner-case in this situation, here’s the scenario we hit yesterday: we load 5 pages per poll, 500 SMs per page, and the Unblocked repo has 2017 SMs:\r\n* On the first poll, we will load all 5 pages, fetching (500+500+500+500+17)=2017 SMs.  However, since the client never got an empty page, we consider that we still have more data to load, and nothing is returned to the SourceMark engine.  We store the lastModified from that final 17-SM page.\r\n* On the next channel poll, we use that lastModified in the channel poll call, which indicates (correctly) that there is no more data to load for that collection.  So the SourceMark store is not told that it needs to do a fetch, so it never fetches the final (empty) page, and never returns the data to the SourceMark engine.\r\n* The data will only be sent to the SourceMark engine once there is another channel poll trigger for sourcemarks (ie, some client updates SM’s).\r\n* The tests I wrote were correct, but tested paging when there was one additional page, so it worked as expected.\r\n* Customers would only have run into this if they had (5*x) pages of SM data.\r\nI did a hacky fix for this yesterday, today I’ll fix this properly in VSCode.  There’s a separate question about how we should do paged loading on polling, which maybe won’t be relevant once we extract the SM engine out of the IDE.\r\n---\r\n\r\nThis PR fixes pagination to re-poll correctly while we are in the middle of loading data.  Also added a test case for this exact scenario.","mergeCommitSha":"0bf022eeee8096db8cccab03f37362313fd5252c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2444","title":"Fix client pagination bug","createdAt":"2022-07-26T20:18:11Z"}
{"state":"Merged","mergedAt":"2022-07-27T19:55:48Z","number":2445,"body":"Fixes UNB-406\r\n\r\nCurrently, we're only updating the state when we receive a pull request webhook. This PR updates that to upsert on every webhook: create if it doesn't exist or update it if the `updatedAt` field is newer than what we've stored in the db.\r\n\r\nThis way we fix the issue of titles not updating when they're changed, plus this is more in-line with our planned bulk ingestion of pull requests where we create a pull request model for every pull request.","mergeCommitSha":"7c9d043f1dcecb1c1080ef65e4eb1c967efa5af0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2445","title":"Upsert PullRequest model on receiving a webhook","createdAt":"2022-07-26T20:29:25Z"}
{"state":"Merged","mergedAt":"2022-07-27T17:39:40Z","number":2446,"body":"Remove unused tabID (from older architecture where we needed to communicate with background script).\r\n\r\nThis allows us to trigger an initial load of the sidebar, injecting it into the dom before navigation events are dispatched from background script.\r\n\r\nFixes UNB-496","mergeCommitSha":"8c9e03e0ce9095c460024e204a52face55195ada","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2446","title":"Speed up initial sidebar injection","createdAt":"2022-07-26T20:35:37Z"}
{"state":"Merged","mergedAt":"2022-07-27T17:35:29Z","number":2447,"body":"fixes: https://linear.app/unblocked/issue/UNB-488/api-to-recommend-repos-by-conversation-count","mergeCommitSha":"f7d9e988c5f766e480f8dc869381e6fb974ae33f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2447","title":"Implementation of getRepoStats and getAllRepoStats","createdAt":"2022-07-26T20:40:08Z"}
{"state":"Merged","mergedAt":"2022-07-26T21:08:48Z","number":2448,"body":"- Fixed the path to include the S3 directory\r\n- Added tests for it","mergeCommitSha":"375434dea870e8a5df3bc0b97f23d0fa19e44910","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2448","title":"fix path in s3 download link","createdAt":"2022-07-26T20:51:53Z"}
{"state":"Merged","mergedAt":"2022-07-26T22:16:58Z","number":2449,"body":"Fixes UNB-484\r\n\r\nFixes issues with text editor indentation:\r\n* When an editor is associated with a file outside of a known repo (ie, an unsaved file, or one in an unusual folder), indent with no content so we are consistent.\r\n* Render indentation immediately when an editor opens\r\n* Reduce the debounce time on editor creation/deletion/focus events, so that indentation can happen quicker","mergeCommitSha":"a41bd39b1cf3f8d98d33bb060fb9b93e1866e63b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2449","title":"Fix editor indentation issues","createdAt":"2022-07-26T21:13:28Z"}
{"state":"Merged","mergedAt":"2022-02-03T22:53:27Z","number":245,"body":"Opens clients, shared code, and API folder in a single VSCode window","mergeCommitSha":"01c2dd37e5a0accabcca9c12cb39c882add2bac3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/245","title":"Add client workspace file","createdAt":"2022-02-03T18:35:23Z"}
{"state":"Merged","mergedAt":"2022-07-26T21:54:17Z","number":2450,"body":"Forgot to add the the assert condition and also the platform in url","mergeCommitSha":"e2528b70a85a675fb6562e89de5a3c340b56ac5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2450","title":"forgot to assert","createdAt":"2022-07-26T21:39:55Z"}
{"state":"Merged","mergedAt":"2022-07-27T23:17:16Z","number":2451,"body":"<img width=\"476\" alt=\"CleanShot 2022-07-26 at 15 00 11@2x\" src=\"https://user-images.githubusercontent.com/858772/181119878-3ebb4ee8-ebf4-4923-b46b-0941cc51ea29.png\">\r\n\r\n<img width=\"481\" alt=\"CleanShot 2022-07-26 at 15 00 39@2x\" src=\"https://user-images.githubusercontent.com/858772/181119921-4b58f829-918e-4201-99eb-9f8ae2993da6.png\">\r\n\r\n![CleanShot 2022-07-27 at 15 02 42](https://user-images.githubusercontent.com/858772/181382964-76dc3edc-99ec-4806-9d07-b09ac4aa33b4.gif)\r\n\r\n\r\n![CleanShot 2022-07-27 at 15 12 52](https://user-images.githubusercontent.com/858772/181382997-6653be67-b5ce-460f-9696-718c86f05158.gif)\r\n\r\n","mergeCommitSha":"340cd625a0058935954a2c7b66cb863b52a1a22f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2451","title":"Adds update description text to hub","createdAt":"2022-07-26T21:59:54Z"}
{"state":"Merged","mergedAt":"2022-07-26T23:23:12Z","number":2452,"body":"After CreateDiscussion dialog is dismissed, we need to make sure that the `CreateDiscussionUtils.createDiscussionStateStream` stream is cleaned up.\r\n\r\nIf not, this instance will be shared with subsequent instances of CreateDiscussion on the same file causing some unexpected behaviours (e.g. cannot create a second thread on file without refresh)","mergeCommitSha":"4ff54fb3d8942c6f59e86b84577c2eb1ceb1929d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2452","title":"Proper Cleanup in create discussion","createdAt":"2022-07-26T22:03:38Z"}
{"state":"Merged","mergedAt":"2022-07-27T19:24:59Z","number":2453,"body":"The VSCode 'git' module was created without a top-level path module name, so everyone referenced it using relative paths.  Plus, everything in the 'git' module was referencing 'utils' by relative paths.  Fix these.\r\n\r\nThere are still a couple places where 'git' directly references files in 'sourcemark', which I think doesn't make sense -- sourcemark uses git, not the other way around?  So I think we should move those files into git?  I'm not 100% sure though.","mergeCommitSha":"227cd72fccf8e60a01f6b28b15614a09fe35ed3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2453","title":"Fix git/utils module references","createdAt":"2022-07-26T22:34:59Z"}
{"state":"Merged","mergedAt":"2022-07-27T01:19:32Z","number":2454,"body":"Prevents a double scrollbar in the sidebar, somehow got into a previous commit.  I never noticed it until I debugged on a smaller screen.","mergeCommitSha":"72674cee2f0e14e5dd8a8a386c8fd6bf83d88c86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2454","title":"Fix bad overflow value","createdAt":"2022-07-27T01:11:55Z"}
{"state":"Closed","mergedAt":null,"number":2455,"body":"When Clio onboarded today, the highest priority repo was ingested first but an error was returned by one of the GitHub's API operations. The onboarding sync job continued with the other repos before returning to the first one, which caused ingesting the first repo to come right at the end.\r\n\r\nThis PR changes it so that the job only attempts the highest priority repo on each loop before exiting. This way the job keeps attempting the highest priority jobs and only moves onto the lower priority repos once done.","mergeCommitSha":"0740d2ba9a04c3d9a6962c732726456a77b1b78f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2455","title":"Only run ingestion on the first repo on each loop","createdAt":"2022-07-27T06:10:36Z"}
{"state":"Merged","mergedAt":"2022-07-27T21:37:30Z","number":2456,"body":"Totally functional but I need CSS help because this looks horrific. @kaych @jeffrey-ng @matthewjamesadam  please save me from myself \uD83D\uDE2D\r\n\r\n<img width=\"1396\" alt=\"CleanShot 2022-07-27 at 11 14 22@2x\" src=\"https://user-images.githubusercontent.com/858772/181343373-219cdf8d-4645-4b81-9ee1-a35acaa78aff.png\">\r\n\r\n","mergeCommitSha":"103bea6edb87901e97d03a0065334b4a5475337f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2456","title":"Add version markdown editor to admin console","createdAt":"2022-07-27T17:13:31Z"}
{"state":"Merged","mergedAt":"2022-07-27T20:51:54Z","number":2457,"mergeCommitSha":"61d65dcabff220c21843cd8c7036e73f30e3e6ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2457","title":"Incident: disable recommendations during ingestion","createdAt":"2022-07-27T20:50:31Z"}
{"state":"Merged","mergedAt":"2022-07-27T22:24:48Z","number":2458,"body":"Optimize these queries:\r\nhttps://ui.honeycomb.io/unblocked/environments/production/result/qvHNHTPqGMM?useStackedGraphs\r\n\r\n> **Warning**\r\nThe biggest risk here is that the index is not created in the background, meaning that the DB locks the MessageModel for writes; reads continue.","mergeCommitSha":"80f5c35cd56e215db8d85fa7c2749d2bbe42b8f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2458","title":"DB Index on MessageModel","createdAt":"2022-07-27T22:09:02Z"}
{"state":"Closed","mergedAt":null,"number":2459,"body":"If necessary, reverts NextChapterSoftware/unblocked#2458","mergeCommitSha":"e00fc927d80bac8b93273a8a07c4aa67ae0e2df5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2459","title":"Revert \"DB Index on MessageModel\"","createdAt":"2022-07-27T22:25:19Z"}
{"state":"Merged","mergedAt":"2022-02-03T19:11:40Z","number":246,"body":"Mahdi needs this for kube related checks...","mergeCommitSha":"4c800b562d6efa03d244a2020d81faaaa26c3ccd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/246","title":"Add health checks","createdAt":"2022-02-03T18:56:24Z"}
{"state":"Closed","mergedAt":null,"number":2460,"body":"Refactor ThreadsSidebar away from manually sending load events to observing stream listeners.","mergeCommitSha":"163878f6431b6f449cd3ed5b254b75488d792a5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2460","title":"Refactor Thread Sidebar","createdAt":"2022-07-27T22:37:27Z"}
{"state":"Merged","mergedAt":"2022-08-03T00:11:05Z","number":2461,"body":"PromiseProxy service can now transport errors.\r\n\r\nErrors only go one direction though. From Service -> Client.","mergeCommitSha":"9c700d083d53e106348cd0c61d21a8c2971592d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2461","title":"Add support for error handling in proxy service","createdAt":"2022-07-27T23:51:12Z"}
{"state":"Merged","mergedAt":"2022-07-28T01:27:07Z","number":2462,"body":"All significant queries on SourcePointModel use the mark clause.\nhttps://ui.honeycomb.io/unblocked/environments/production/result/ufDJCeWsUbZ?useStackedGraphs","mergeCommitSha":"4a9172eeb31d8da8e211f0ae68c0662e9c6ff2a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2462","title":"DB Index on SourcePointModel","createdAt":"2022-07-28T01:11:50Z"}
{"state":"Merged","mergedAt":"2022-07-28T19:34:03Z","number":2463,"body":"No need to go out to the GitHub API if the pull request model exists. This is part of the effort to make ingestion faster.","mergeCommitSha":"c4ba48b640fc6a7f7cf1fc6c06f5e26596b69771","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2463","title":"Ingestion checks for existing pull request model before going to GitHub API","createdAt":"2022-07-28T05:34:51Z"}
{"state":"Merged","mergedAt":"2022-07-28T21:18:35Z","number":2464,"mergeCommitSha":"4f6bd22e3abb36723fcb06ddc176f9ec3b064962","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2464","title":"Create team member with null role for PR author if not found","createdAt":"2022-07-28T20:03:44Z"}
{"state":"Merged","mergedAt":"2022-07-29T00:03:14Z","number":2465,"body":"- indexes created based on N-worst performance analysis in PROD\n  https://ui.honeycomb.io/unblocked/environments/production/result/zJbv9vbJGXc?hideCompare\n- reorder queries to ensure that query planner uses new repo index","mergeCommitSha":"85d8dfab6fb71b25856ad542b794dbccb64f381a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2465","title":"DB Index on SourceMarkModel","createdAt":"2022-07-28T23:34:20Z"}
{"state":"Closed","mergedAt":null,"number":2466,"body":"- indexes created based on N-worst performance analysis in PROD\n  https://ui.honeycomb.io/unblocked/environments/production/result/BBzida8VbdM?hideCompare\n- all slow queries covered","mergeCommitSha":"9999e481e9842655774967195b2f0722c39b2c64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2466","title":"DB Index on TeamModel","createdAt":"2022-07-28T23:39:10Z"}
{"state":"Merged","mergedAt":"2022-07-29T00:02:49Z","number":2467,"body":"- reorder queries to ensure that query planner uses existing compound {team, identity} index\n  https://ui.honeycomb.io/unblocked/environments/production/result/nzsuNqySyQG?hideCompare","mergeCommitSha":"8ea325e6a2975ef773dc07358d3b75271be6b5ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2467","title":"Use DB Index on TeamMemberModel","createdAt":"2022-07-28T23:45:00Z"}
{"state":"Merged","mergedAt":"2022-07-29T01:22:18Z","number":2468,"body":"- indexes created based on N-worst performance analysis in PROD\n  https://ui.honeycomb.io/unblocked/environments/production/result/jQAXTLYVd6H?hideCompare\n\n- reorder queries to ensure that query planner uses {id} or {team, pullRequest} indexes","mergeCommitSha":"d59459af143ae570bf992de9c9ba9e1e7810fb97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2468","title":"DB Index on ThreadModel","createdAt":"2022-07-28T23:50:48Z"}
{"state":"Merged","mergedAt":"2022-08-02T16:55:29Z","number":2469,"body":"Judging by the prs I saw last week, when necessary, we need to validate queries are appropriately using database indices when we need them through tests.\r\nUnlike Skywagon, throwing exceptions is an **OPT-IN** approach. You will get errors logged out, but exceptions are now configurable.\r\n\r\nTo that effect\r\n1. This pr piggy backs on Exposed's interceptor framework to allow us to explain all test queries before they are executed. We parse the resulting plan results in order to determine if we're doing a sequential scan of tables or doing some sort of query that is hitting a lot of rows.\r\n2. We use coroutine context elements to hold on to the interceptors in order to eliminate the need for explicitly passing interceptors around all over the place.\r\n3. Add the ability to configure how a database test is set up via a configuration argument:\r\n```\r\n    fun deletingTeamCascades() = suspendingDatabaseTest({\r\n        throwOnSlowQuery = true\r\n    }) {\r\n    }\r\n```\r\n4. Add ability to disable query planning (i.e. for DAO queries that are localized to tests)\r\n```\r\n        withStatementInterceptorsDisabled {\r\n            // ... expect team and sourceMark to be deleted\r\n            suspendedTransaction {\r\n                assertThat(TeamDAO.findById(teamId)).isNull()\r\n                assertThat(SourceMarkDAO.find { SourceMarkModel.group eq markID }.count()).isZero\r\n                assertThat(SourceMarkDAO.find { SourceMarkModel.team eq teamId }.count()).isZero\r\n                assertThat(SourceMarkDAO.find { SourceMarkModel.message eq messageID }.count()).isZero\r\n            }\r\n        }\r\n```\r\n\r\nTESTING:\r\n1. Took a look at a few of the prs that @richiebres created, and validated that this pr correctly catches queries that are not using indices and that fucked us last week.\r\n\r\ni.e.\r\n```\r\ncom.nextchaptersoftware.db.interceptors.explain.SlowQueryException: \r\n           Sql:\r\n            [SELECT teammodel.id, teammodel.\"createdAt\", teammodel.\"modifiedAt\", teammodel.\"displayName\", teammodel.provider, teammodel.\"providerExternalId\", teammodel.\"providerLogin\", teammodel.\"providerDisplayName\", teammodel.\"providerAvatarUrl\", teammodel.\"providerHtmlUrl\", teammodel.\"providerExternalInstallationId\", teammodel.\"deletedProviderExternalId\", teammodel.\"deletedProviderExternalInstallationId\", teammodel.\"subscribedReleaseChannel\" FROM teammodel WHERE (teammodel.provider = ?) AND (teammodel.\"providerExternalId\" = ?) AND (teammodel.\"deletedProviderExternalId\" IS NULL)]\r\n           Line:\r\n            [Seq Scan on teammodel  (cost=10000000000.00..10000000082.46 rows=1 width=328)]\r\n           Plan:\r\n            [Seq Scan on teammodel  (cost=10000000000.00..10000000082.46 rows=1 width=328)\r\n  Filter: ((\"deletedProviderExternalId\" IS NULL) AND (provider = 1) AND (\"providerExternalId\" = '7376'::text))\r\n]\r\n           Match:\r\n            [Seq Scan]\r\n```","mergeCommitSha":"547fcea6f985b06bcfee583208188ab449589575","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2469","title":"Add execution query planning to database test framework","createdAt":"2022-07-30T09:51:48Z"}
{"state":"Merged","mergedAt":"2022-02-03T23:42:55Z","number":247,"body":"Attempts to move data store logic into the database layer (`package com.nextchaptersoftware.db.stores`). If we're OK with this pattern, I'll start writing tests.","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/247","title":"Implement createThread and updateThread operations","createdAt":"2022-02-03T19:31:52Z"}
{"state":"Merged","mergedAt":"2022-07-31T00:24:51Z","number":2470,"mergeCommitSha":"2c4ac22702303f7131c8cbf12bcbe2f018d1fcc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2470","title":"Move away from logging sql queries as it slows ci","createdAt":"2022-07-30T23:55:55Z"}
{"state":"Merged","mergedAt":"2022-08-02T15:12:05Z","number":2471,"body":"Just logging basically. Aggregated in Timelion in Logzio","mergeCommitSha":"b66d8ee3060a71eebd5d7747db65d56e582392f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2471","title":"Sourcemark file count stats","createdAt":"2022-08-02T13:59:30Z"}
{"state":"Merged","mergedAt":"2022-08-05T16:33:29Z","number":2472,"body":"Adds a background job that does bulk ingestion\r\n\r\nTODO\r\n- ~Richie to confirm file sha sentinel value~ Using `ffffffffffffffffffffffffffffffffffffffff `\r\n- Replace queues with FIFO queues","mergeCommitSha":"48650b1fc996e626d3a91dd3520d1dce5b867db8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2472","title":"Add bulk pull request ingestion","createdAt":"2022-08-02T15:31:34Z"}
{"state":"Merged","mergedAt":"2022-08-02T23:01:12Z","number":2473,"body":"https://user-images.githubusercontent.com/13431372/182437950-5893b321-9c17-455e-8516-4eac2d1ff5d9.mp4\r\n\r\nhttps://user-images.githubusercontent.com/13431372/182440282-633c41ba-ca3e-4bc7-92b2-6594744c04b6.mp4\r\n","mergeCommitSha":"bda287e97e68f9ad2b6e92a6f9c4eb639346cd7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2473","title":"Invitation flow when @-mentioning team members","createdAt":"2022-08-02T17:54:15Z"}
{"state":"Merged","mergedAt":"2022-08-02T20:07:42Z","number":2474,"body":"…ount","mergeCommitSha":"be1c2dea5360d38186a6104d3f82b7e75c26f89c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2474","title":"Ensure we send appropriate emails if team member does not have an account","createdAt":"2022-08-02T19:38:48Z"}
{"state":"Merged","mergedAt":"2022-08-05T21:42:10Z","number":2475,"body":"Timeout GH App installation spinner after 1 minute.\r\n","mergeCommitSha":"fd9c0807ac39627d438853b78d8c1f16d9108e12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2475","title":"Jeff/unb 499 gh app installation failure in vscode","createdAt":"2022-08-02T20:31:59Z"}
{"state":"Merged","mergedAt":"2022-08-02T21:48:32Z","number":2476,"mergeCommitSha":"d2053d755b3d4a14b43f89d9bfa9ff914ef023f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2476","title":"Fix bug where thread participants were not being isolated to a thread","createdAt":"2022-08-02T21:14:16Z"}
{"state":"Merged","mergedAt":"2022-08-02T21:48:57Z","number":2477,"mergeCommitSha":"1eb452ec76f86b6e990455c72c31bbefe38575a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2477","title":"Adds additional response logging","createdAt":"2022-08-02T21:35:27Z"}
{"state":"Merged","mergedAt":"2022-08-02T23:20:37Z","number":2478,"body":"Implement much of the read-only sourcemark resolution view.  This PR implements:\r\n\r\n* A read-only view with the source code from the original commit, syntax-hilighted using Shiki to match the VSCode theme.  A banner indicates the reason for the read-only view.  This is shown for most of the failure conditions.\r\n* A separate view, offering to take the user to GitHub.  This is used for cases where we we don't have the commit at all, or where there has been a fault.\r\n\r\nThis PR does *not* implement:\r\n* Git resolution actions (fetch/pull/checkout) or buttons (will be in a followup PR)\r\n* Many small bits that will make the read-only source code view nicer -- we will need to polish this somewhat to make it closer to VSCode, for instance, we should probably word-wrap if the user has enabled this.\r\n\r\n<img width=\"1502\" alt=\"Screen Shot 2022-08-02 at 2 27 34 PM\" src=\"https://user-images.githubusercontent.com/2133518/*********-b8ba0aff-3b2c-42a4-b00d-c518f18e9b5c.png\">\r\n\r\n<img width=\"1502\" alt=\"Screen Shot 2022-08-02 at 2 27 29 PM\" src=\"https://user-images.githubusercontent.com/2133518/*********-dd60a91f-1a6d-465c-82ce-5316d8c7466e.png\">\r\n\r\n","mergeCommitSha":"97a08b61a6f2decd7b07ab28dcac944b7e9900a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2478","title":"Read-only SourceMark resolution view (part 1)","createdAt":"2022-08-02T21:35:45Z"}
{"state":"Merged","mergedAt":"2022-08-03T00:25:50Z","number":2479,"body":"Setup API spec for recommended team members to be used in https://linear.app/unblocked/issue/UNB-219/add-recommended-team-members-to-creatediscussion-ui","mergeCommitSha":"ae96b51a36dea209998866e9a89c3eabff874e72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2479","title":"Add API for recommended team members","createdAt":"2022-08-02T22:20:02Z"}
{"state":"Merged","mergedAt":"2022-02-03T22:21:51Z","number":248,"body":"- Add documentation\r\n- update\r\n","mergeCommitSha":"deeb05d2ec56d1063be8a4dd946bbbdaf57f2454","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/248","title":"Fix frontend documentation","createdAt":"2022-02-03T21:14:36Z"}
{"state":"Merged","mergedAt":"2022-08-03T00:57:17Z","number":2480,"body":"Before, slow query analysis was toggled false by default.\r\nWe've now enabled it as true for all tests, and you can disable it if you so choose.\r\nAlso add a few indices that test exposed as necessary.\r\n\r\nDelete cascade tests are all disabled because 95% is just custom test DAO queries that are inefficient.","mergeCommitSha":"62fb879bf11987e48d30751269c45eaf5eefa7c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2480","title":"Enable slow query analysis across tests","createdAt":"2022-08-02T22:40:00Z"}
{"state":"Merged","mergedAt":"2022-08-02T23:52:35Z","number":2481,"mergeCommitSha":"d2a9e3f4f259973c9906f849369f672cfae0cd39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2481","title":"Clients log numbers as numeric types","createdAt":"2022-08-02T23:14:18Z"}
{"state":"Merged","mergedAt":"2022-08-03T00:35:20Z","number":2482,"mergeCommitSha":"4aaac9bfeb11733079391b75285f72b2d45fe14d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2482","title":"Add clock drift test for refresh token","createdAt":"2022-08-02T23:37:22Z"}
{"state":"Merged","mergedAt":"2022-08-03T01:18:43Z","number":2483,"body":"Exclude open PRs from threads mine view\n\nAdd failing test to prevent regression.\n\nBreaking change was #2468.\n\nhttps://linear.app/unblocked/issue/UNB-510/my-open-pr-discussions-appear-under-my-discussions","mergeCommitSha":"bf0b5e75ad3008821dae9dfe10464fe67f81cb4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2483","title":"Exclude open PRs from threads mine view","createdAt":"2022-08-02T23:51:18Z"}
{"state":"Merged","mergedAt":"2022-08-03T01:44:17Z","number":2484,"mergeCommitSha":"98d887d18c56a441bb8722e3b3528db53e4cf5ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2484","title":"Admin: Fix wrapping in Sourcepoints table","createdAt":"2022-08-03T00:12:10Z"}
{"state":"Merged","mergedAt":"2022-08-03T00:44:34Z","number":2485,"body":"This was getting annoying in Honeycomb, where it's not possible to aggregate\nbased on UUIDs because there are two UUIDs (one uppercase UUID for Apple and\none for every other clients).","mergeCommitSha":"1046713617a3f492000d54a327e98d161c6fb59d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2485","title":"Sanitize UUIDs from Apple","createdAt":"2022-08-03T00:28:51Z"}
{"state":"Merged","mergedAt":"2022-08-03T06:12:44Z","number":2486,"mergeCommitSha":"b2d7a03bed96c11eb1cf97ea0eb1114e2165145d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2486","title":"New log field to force numeric datatype in logzio","createdAt":"2022-08-03T06:06:58Z"}
{"state":"Merged","mergedAt":"2022-08-03T06:43:12Z","number":2487,"body":"```json\n{\n  \"_index\": \"logzioCustomerIndex220802_v8\",\n  \"_type\": \"doc\",\n  \"_id\": \"pB5eYoIB_p8kqhRxY3KM.account-411850\",\n  \"_version\": 1,\n  \"_score\": null,\n  \"_source\": {\n    \"@timestamp\": \"2022-08-03T06:22:12.762+0000\",\n    \"index-failed-reason\": \"{\\\"type\\\":\\\"mapper_parsing_exception\\\",\\\"reason\\\":\\\"object mapping for [platform] tried to parse field [platform] as object, but found a concrete value\\\"}\",\n    \"type\": \"logzio-index-failure\",\n    \"message\": \"{\\\"platform.version\\\":\\\"98d887d18c56a441bb8722e3b3528db53e4cf5ac\\\",\\\"level\\\":\\\"DEBUG\\\",\\\"hostInfo\\\":{\\\"user\\\":\\\"root\\\",\\\"hostname\\\":\\\"adminwebservice-57fc56dbd5-45ncw\\\"},\\\"productNumber\\\":\\\"297\\\",\\\"message\\\":\\\"Failed to insert new version due to uniqueness constraints\\\",\\\"type\\\":\\\"java\\\",\\\"platform\\\":\\\"macos\\\",\\\"tags\\\":[\\\"_logz_http_bulk_json_8070\\\"],\\\"environment\\\":\\\"prod\\\",\\\"productVersion\\\":\\\"1.0.297\\\",\\\"@timestamp\\\":\\\"2022-08-03T06:22:09.291+0000\\\",\\\"service\\\":\\\"adminwebservice\\\",\\\"thread_name\\\":\\\"DefaultDispatcher-worker-2\\\",\\\"productAgent\\\":\\\"Hub\\\",\\\"logger_name\\\":\\\"com.nextchaptersoftware.version.VersionService\\\"}\",\n    \"tags\": [\n      \"_logzio_reindex\"\n    ]\n  },\n  \"fields\": {\n    \"@timestamp\": [\n      \"2022-08-03T06:22:12.762Z\"\n    ]\n  },\n  \"highlight\": {\n    \"index-failed-reason\": [\n      \"@kibana-highlighted-field@{\\\"type\\\":\\\"mapper_parsing_exception\\\",\\\"reason\\\":\\\"object mapping for [platform] tried to parse field [platform] as object, but found a concrete value\\\"}@/kibana-highlighted-field@\"\n    ]\n  },\n  \"sort\": [\n    1659507732762\n  ]\n}\n```","mergeCommitSha":"1cd73d4fe4eaf50b181a65d7665a915bf14d345c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2487","title":"Fix logzio mapping conflict","createdAt":"2022-08-03T06:27:44Z"}
{"state":"Merged","mergedAt":"2022-08-03T21:19:05Z","number":2488,"body":"Not used:\nhttps://ui.honeycomb.io/unblocked/environments/production/result/HfJUsCnxjDq?hideMarkers&useLogScale&useStackedGraphs","mergeCommitSha":"b905a1c00833483800c3830a0399cef566f2f733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2488","title":"Remove getTeamMembersDeprecated API","createdAt":"2022-08-03T06:38:20Z"}
{"state":"Closed","mergedAt":null,"number":2489,"body":"It was throwing a method not found exception. Not sure how this compiled...","mergeCommitSha":"ab7fde58d76986fb923ec8de8fe92d4257c40ffe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2489","title":"Fix local stack","createdAt":"2022-08-03T07:04:52Z"}
{"state":"Merged","mergedAt":"2022-02-03T23:47:02Z","number":249,"body":"![image](https://user-images.githubusercontent.com/13431372/152442059-041b045a-beb3-4699-8cae-28b46530f7f4.png)\r\n\r\n* Chat/Home felt like features so I left them capitalized but can change if needed\r\n* left a top level `app/` directory but not super sure how necessary this is\r\n* added aliases for the new top level directories but can also remove these if needed\r\n* wasn't sure whether ThreadView and MessageView should live under Chat/ or components/","mergeCommitSha":"715f020a1cd82f2b159b46076db4ceb29dbf9b0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/249","title":"Reconfigure web directory into semantic structure","createdAt":"2022-02-03T22:48:56Z"}
{"state":"Merged","mergedAt":"2022-08-03T15:44:06Z","number":2490,"mergeCommitSha":"987504c303998358064e45eb8ca9552ffb927163","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2490","title":"More duck types","createdAt":"2022-08-03T15:07:39Z"}
{"state":"Merged","mergedAt":"2022-08-03T20:39:08Z","number":2491,"body":"Updated recommended team member spec to include reason.\r\n\r\nCan be used to communicate with user *why* a team member was recommended. (e.g. `because you review John’s PRs`)","mergeCommitSha":"47261a2ddc269c53903b3c2dd8dd2296f4790f86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2491","title":"Update recommeneded team member","createdAt":"2022-08-03T17:10:15Z"}
{"state":"Merged","mergedAt":"2022-08-03T21:25:05Z","number":2492,"body":"Eliminate one possible cause of spurious Hub logouts. Theory: an apiservice instance comes up with corrupted secrets and services some refresh requests before hitting a non-recoverable error and shuts down before logs can be flushed.","mergeCommitSha":"1085af02dd7af0e7e49abfb72f1504b94a2995bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2492","title":"Kill the apiservice if keys weren't loaded correctly","createdAt":"2022-08-03T18:58:08Z"}
{"state":"Merged","mergedAt":"2022-08-04T17:50:43Z","number":2493,"mergeCommitSha":"28e64257cd1b784b76c88b0116a0ddca6f8cbe94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2493","title":"Get pull request comments from GitHub API","createdAt":"2022-08-03T19:00:40Z"}
{"state":"Merged","mergedAt":"2022-08-03T19:06:47Z","number":2494,"body":"Already deleted manually via cdk via `cdk destroy`","mergeCommitSha":"2112f3df7581650065c1f7e2fe27715b74c9f00d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2494","title":"Delete pinpoint stack","createdAt":"2022-08-03T19:02:54Z"}
{"state":"Merged","mergedAt":"2022-08-04T18:03:32Z","number":2495,"body":"https://user-images.githubusercontent.com/13431372/182688720-175847e8-8f43-43f4-b594-02ffb8c8ea79.mp4\r\n\r\n","mergeCommitSha":"db4680c7416df4e9cbfbb0ed658a77495ffc64b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2495","title":"Fix open current file explorer thread view column","createdAt":"2022-08-03T19:03:47Z"}
{"state":"Merged","mergedAt":"2022-08-03T19:32:37Z","number":2496,"body":"Why the hell is this _trace_ severity? Anyway this change exposes the following message:\n\n```kt\nlogger.trace(\"Responding unauthorized because of error ${error.message}\")\n```","mergeCommitSha":"0270b443e03a51b2d688183f92ee7154cd76afb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2496","title":"Log ktor authentication class errors","createdAt":"2022-08-03T19:17:12Z"}
{"state":"Merged","mergedAt":"2022-08-03T20:32:58Z","number":2497,"body":"- new API model field is optional for now\n- new DB model field is optional for now\n- includes migration to backfill DB model\n\nRelated to https://linear.app/unblocked/issue/UNB-502/sourcemark-engine-should-backfill-sourcepoints-with-file-hashes","mergeCommitSha":"6c10c4d68657fbac629c1f7ed9bc6b137f495dfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2497","title":"Introduce concept of trusted source point","createdAt":"2022-08-03T19:56:32Z"}
{"state":"Merged","mergedAt":"2022-08-03T22:02:48Z","number":2498,"body":"Add git fetch/pull/commit, intercom support, and view in GitHub actions to the read-only SourceMark resolution view.\r\n\r\nAfter this, one more PR to do, that will add:\r\n* Some kind of UI indication while the git operation is taking place (a spinner in the button?) -- this will be interesting, and depending on VSCode's API I'm not certain it will be possible.\r\n* Add a popup to enable auto-fetch\r\n\r\n<img width=\"1368\" alt=\"Screen Shot 2022-08-03 at 9 06 43 AM\" src=\"https://user-images.githubusercontent.com/2133518/182698105-eb80edf7-f8a7-47ea-9989-f4edc9978216.png\">\r\n<img width=\"1228\" alt=\"Screen Shot 2022-08-03 at 11 09 45 AM\" src=\"https://user-images.githubusercontent.com/2133518/182698123-3e45b9f2-cc26-4bbe-bf6b-282e759725c8.png\">\r\n<img width=\"1429\" alt=\"Screen Shot 2022-08-03 at 11 59 02 AM\" src=\"https://user-images.githubusercontent.com/2133518/182698130-5d607432-637c-402d-8366-063bed92a732.png\">\r\n<img width=\"1429\" alt=\"Screen Shot 2022-08-03 at 11 59 38 AM\" src=\"https://user-images.githubusercontent.com/2133518/182698137-16c60c81-285a-45e1-a2a9-aab00e002ed6.png\">\r\n<img width=\"1367\" alt=\"Screen Shot 2022-08-03 at 12 51 23 PM\" src=\"https://user-images.githubusercontent.com/2133518/182698145-20a6b4ee-8339-4af6-a297-9f827bf96f99.png\">\r\n<img width=\"1530\" alt=\"Screen Shot 2022-08-03 at 12 52 45 PM\" src=\"https://user-images.githubusercontent.com/2133518/182698151-b22cc4d0-dd5b-46db-88e3-d1283fd634e0.png\">\r\n<img width=\"1530\" alt=\"Screen Shot 2022-08-03 at 12 53 37 PM\" src=\"https://user-images.githubusercontent.com/2133518/182698152-b00d0bf3-6c08-42f9-9eec-81cc68dda55f.png\">\r\n","mergeCommitSha":"3490a191f844de3e7accc718f4dbcc8dbb543fa5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2498","title":"Add actions to read-only resolution view","createdAt":"2022-08-03T19:58:36Z"}
{"state":"Merged","mergedAt":"2022-08-03T21:27:28Z","number":2499,"body":"Do not merge. Merge only when the migration has successfully run.","mergeCommitSha":"bb74423272ba92d2a566dd45f205a85807a47175","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2499","title":"Make SourcePointModel isTrusted field non-nullable","createdAt":"2022-08-03T20:00:46Z"}
{"state":"Merged","mergedAt":"2022-02-04T05:06:38Z","number":250,"mergeCommitSha":"10e350c34ef548e79aa93b7b7f110dfd6080f779","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/250","title":"Remove unncessary !!","createdAt":"2022-02-04T00:38:39Z"}
{"state":"Merged","mergedAt":"2022-08-03T21:28:50Z","number":2500,"mergeCommitSha":"c37e2e6a8c4995390240e9d7f83131fdecd1d0d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2500","title":"fix lint","createdAt":"2022-08-03T21:05:44Z"}
{"state":"Merged","mergedAt":"2022-08-03T23:11:25Z","number":2501,"body":"We need to be able to recommend valid emails from git (when we can).\r\nTo that end:\r\n1. We add mention event handlers to DiscussionThreadCommand so we can populate gitContributor information when a team member is mentioned.\r\n2. We add recommendedContributor to GitContributor such that we can use it as a fallback if email field is not populated.\r\n\r\nFOR NEW DISCUSSION:\r\nKay has a no-reply email on git, so she's ignored.\r\n![CleanShot 2022-08-03 at 14 17 17](https://user-images.githubusercontent.com/3806658/182714044-2236c9fc-babf-46f7-ad19-f951e8a28fb5.gif)\r\n\r\nFOR EXISTING DISCUSSION:\r\n![CleanShot 2022-08-03 at 14 24 13](https://user-images.githubusercontent.com/3806658/182714833-8f3b5214-ed93-4400-9d54-f98beeed7790.gif)\r\n","mergeCommitSha":"cef86a02a47af9595b114627c5b8141b4cfcd4c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2501","title":"Ensure we recommend emails in instances where we can for invite flow","createdAt":"2022-08-03T21:09:53Z"}
{"state":"Merged","mergedAt":"2022-08-16T23:40:42Z","number":2502,"body":"Add client support for recommended team members\r\n\r\nRecommended team members will always appear at the bottom of list.\r\n\r\nDo not merge until backend done.","mergeCommitSha":"414b57605f56a016bf5fdd90971e2e26eea6ad8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2502","title":"Jeff/unb 219 add recommended team members to","createdAt":"2022-08-03T21:23:37Z"}
{"state":"Merged","mergedAt":"2022-08-15T18:15:34Z","number":2503,"body":"## Summary\r\n\r\nIntroducing some changes to set up the app for \"seamless\" updates via a non-sandboxed embedded app. This PR does the following:\r\n\r\n- Modify the installer to set user-level ownership on /Applications/Unblocked.app\r\n- Introduce a non-sandboxed embedded app (currently a stub)\r\n\r\nOwnership permissions change has been tested with the following installer scenarios:\r\n- Fresh install\r\n- Upgrade from previous install where ownership is `root:wheel`\r\n- Upgrade from previous install where ownership is already `user:staff`\r\n\r\nFailed attempts to change ownership are passive. The plan for \"seamless install\" is to fall back to the installer if the ownership preconditions are not met.","mergeCommitSha":"44b1a9bc6cc16aafea4bd15eedd93b5fcaa602a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2503","title":"Modify /Application/Unblocked.app ownership permissions ","createdAt":"2022-08-03T21:27:33Z"}
{"state":"Merged","mergedAt":"2022-08-15T23:45:39Z","number":2504,"body":"Seamless installs are facilitated as follows:\r\n- Main app downloads and stores new version metadata and pkg (as it does now)\r\n- Main app launches unsandboxed helper app via launchd (`NSWorkspace.open()`)\r\n- Helper app unpacks installer package using undocumented (\uD83D\uDE31) `pkgutil` command\r\n- Helper app drops the bits to `/Application/Unblocked.app` and shells out to install vscode extension\r\n- Helper app restarts Hub and quits\r\n\r\n\r\n![CleanShot 2022-08-05 at 15 28 48](https://user-images.githubusercontent.com/858772/183219550-dd680e96-1c54-42b9-b929-f18c3f40dc2b.gif)\r\n\r\nThe Hub will be rejected from the App Store so long as we're embedding an unsandboxed app","mergeCommitSha":"5bafe49396d7686d3326ac69fe1f8ed052728c0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2504","title":"\"Seamless\" installs","createdAt":"2022-08-03T21:27:36Z"}
{"state":"Closed","mergedAt":null,"number":2505,"body":"Thor hammer...","mergeCommitSha":"e1054739b040f2efd6eed37c0a4e7a2bdb372873","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2505","title":"Last resort defence","createdAt":"2022-08-03T21:46:58Z"}
{"state":"Merged","mergedAt":"2022-08-03T23:47:27Z","number":2506,"body":"This is displayed in the 'Not in Local Repo' case -- we offer to `git fetch`, but also encourage the user to turn on auto-fetch, as this should generally resolve this.\r\n\r\nOne issue @benedict-jw  -- there is always a 'Cancel' button!  Should we remove the 'Don't Fetch Automatically' button?\r\n\r\n<img width=\"432\" alt=\"Screen Shot 2022-08-03 at 3 11 54 PM\" src=\"https://user-images.githubusercontent.com/2133518/182721696-de5daa7d-a030-4433-81f7-e2a3e03df971.png\">\r\n ","mergeCommitSha":"0ef9f75cd86178c995e4eabf35ff021b7383f849","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2506","title":"Add auto-fetch query to read-only SM error view","createdAt":"2022-08-03T22:14:52Z"}
{"state":"Merged","mergedAt":"2022-08-03T23:00:20Z","number":2507,"body":"## Summary\r\n\r\nAuth in the Hub occurs in 3 phases:\r\n1. Refresh the token\r\n2. Call `getPerson` and `getTeams` to bring the stores into a consistent state with the new token\r\n3. Publish the combined data\r\n\r\nIf macOS goes to sleep between steps 1 and 2, it's possible for the calls in step 2 to fail with a 401. This error is propagated to the call site for refresh, and incorrectly interpreted as a 401 from `/api/login/refresh`, triggering a logout. \r\n\r\nThe fix is to wrap the \"finalization\" logic in a try/catch, so that if any of the logic during finalization fails, auth token refresh is retried again","mergeCommitSha":"cadfb12888b9b9f28ce25adcdf5cb1358f85fe81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2507","title":"Auth finalization can get interrupted and cause a logout","createdAt":"2022-08-03T22:41:24Z"}
{"state":"Merged","mergedAt":"2022-08-03T23:24:03Z","number":2508,"body":"<img width=\"1616\" alt=\"CleanShot 2022-08-03 at 15 45 08@2x\" src=\"https://user-images.githubusercontent.com/1924615/182725261-30ede050-737c-4480-b0d7-d72097092a18.png\">\r\n","mergeCommitSha":"2861621e3b67501144e672052f225b0dac6496df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2508","title":"Show thread ranks on thread page","createdAt":"2022-08-03T22:46:06Z"}
{"state":"Merged","mergedAt":"2022-08-04T01:03:56Z","number":2509,"body":"- Update deps\r\n- Update a few dependencies\r\n","mergeCommitSha":"5909a05ec5997fe1244181ea1f7bd2fc1f8c2694","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2509","title":"UpgradeDependencies","createdAt":"2022-08-03T23:51:56Z"}
{"state":"Closed","mergedAt":null,"number":251,"body":"Just investigating why comparing the `createdAt` for the same model differs when the model is created locally versus when the same model retrieved from the DB after saving.","mergeCommitSha":"f8a13ea5159b1dd0e46ce1f28a59711a70672198","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/251","title":"[DO NOT MERGE] Compare createdAt","createdAt":"2022-02-04T00:40:43Z"}
{"state":"Merged","mergedAt":"2022-08-04T03:17:03Z","number":2510,"body":"In the SourceMark resolution UI, if the user elects to fetch/pull/checkout, afterwards we will re-resolve the sourcemark location and reload the UI.  If the git operation solved the problem, this should cause the UI to display the source code.\r\n\r\nOne last PR coming up after: need to add spinners or grey out the action buttons while this is happening.","mergeCommitSha":"ce74c96af74198dc48e98bca358b261d2a2a4ae3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2510","title":"Reload SM resolution UI after trying git operations","createdAt":"2022-08-03T23:59:46Z"}
{"state":"Merged","mergedAt":"2022-08-04T17:22:15Z","number":2511,"mergeCommitSha":"04ecd9f08165ba0dedc17a2f53975334938ea750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2511","title":"Remove deprecated fields that havent been used in a while","createdAt":"2022-08-04T16:57:40Z"}