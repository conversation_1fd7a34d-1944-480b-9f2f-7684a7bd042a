{"state":"Merged","mergedAt":"2022-06-24T17:24:35Z","number":1997,"body":"![CleanShot 2022-06-23 at 21 27 37](https://user-images.githubusercontent.com/858772/175462225-ee49900d-ee49-47b8-bdae-c92cbbc11e9e.gif)\r\n","mergeCommitSha":"4d9578777b38d00319efcaf1dfe8542f981b9859","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1997","title":"Accordion participants","createdAt":"2022-06-24T04:27:20Z"}
{"state":"Merged","mergedAt":"2022-06-24T21:02:02Z","number":1998,"body":"There is a custom UI for this coming up, but for now we're using the default UI.\r\n\r\n<img width=\"1017\" alt=\"Screen Shot 2022-06-23 at 9 46 38 PM\" src=\"https://user-images.githubusercontent.com/2133518/175464148-174fa6ce-071c-4dd2-9eda-db5a2a051748.png\">\r\n","mergeCommitSha":"ac0a158a6bc0c114a34ef6e90568bf35190e9893","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1998","title":"Show default intercom UI in dashboard","createdAt":"2022-06-24T04:47:22Z"}
{"state":"Merged","mergedAt":"2022-06-24T17:01:23Z","number":1999,"body":"Setup-java is shit at gradle caching.\r\nThere's a better action that seems to handle this better.","mergeCommitSha":"2e1cacbe8e12aaf29809d455c0c84d96bc5b672e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1999","title":"Move to more efficient gradle caching for builds","createdAt":"2022-06-24T15:01:08Z"}
{"state":"Closed","mergedAt":null,"number":2,"mergeCommitSha":"a2e052526ddc30a6459b7acea9f6f58ec8b3a348","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2","title":"new testfile","createdAt":"2021-12-09T23:49:30Z"}
{"state":"Closed","mergedAt":null,"number":20,"mergeCommitSha":"f6c629deab0ff01268db151c489bec5c2d8aa340","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/20","title":"Mobx Implementation","createdAt":"2022-01-04T05:32:32Z"}
{"state":"Merged","mergedAt":"2022-02-01T17:13:04Z","number":200,"mergeCommitSha":"f62b493fe8825a665d484cb21a5d0929cc5d4d05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/200","title":"Update service tokens","createdAt":"2022-02-01T17:12:55Z"}
{"state":"Merged","mergedAt":"2022-06-24T17:45:28Z","number":2000,"mergeCommitSha":"ce6a252a0c5c1874bde4154f5daed1b45bbc195e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2000","title":"Update version","createdAt":"2022-06-24T16:46:28Z"}
{"state":"Merged","mergedAt":"2022-06-24T17:13:23Z","number":2001,"mergeCommitSha":"8fffb2030f8b32c710a6356046322df67f2d1323","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2001","title":"Logging for denied installations","createdAt":"2022-06-24T17:00:41Z"}
{"state":"Merged","mergedAt":"2022-06-24T18:11:18Z","number":2002,"body":"Fixes these exceptions https://app.logz.io/#/goto/e4216df8ae4d385b57fa753a0a7c91c8?switchToAccountId=411850","mergeCommitSha":"7be09c4c4ef8e04d1327ce09cf124d0aba45c46d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2002","title":"Do model creation in the store","createdAt":"2022-06-24T17:46:18Z"}
{"state":"Merged","mergedAt":"2022-06-24T19:34:24Z","number":2003,"mergeCommitSha":"f23784463e875463f3b0a8a58d6bcfe3148b2261","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2003","title":"Move services out","createdAt":"2022-06-24T19:22:00Z"}
{"state":"Merged","mergedAt":"2022-06-24T21:00:39Z","number":2004,"mergeCommitSha":"70310b79e46756f5c57a27a948e42114ae18a345","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2004","title":"move dependencis around","createdAt":"2022-06-24T20:08:04Z"}
{"state":"Merged","mergedAt":"2022-06-24T20:26:20Z","number":2005,"mergeCommitSha":"27a9f9d1e2d85124bd979b2391d0b677cb718f87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2005","title":"Fix builds","createdAt":"2022-06-24T20:25:41Z"}
{"state":"Merged","mergedAt":"2022-06-24T22:28:41Z","number":2006,"body":"Hits the GitHub API to get the pull request, comments, and files, and spits back the body. This will help with debugging https://chapter2global.slack.com/archives/C02HEVCCJA3/p1656037453385509?thread_ts=1656033667.693789&cid=C02HEVCCJA3\r\n\r\n<img width=\"1920\" alt=\"CleanShot 2022-06-24 at 13 38 15@2x\" src=\"https://user-images.githubusercontent.com/1924615/175663963-5ae1b392-1e6d-4ee4-bba6-bd567b03e0ef.png\">\r\n","mergeCommitSha":"469900d2db25f8b308cbe298c1543430473e7d53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2006","title":"Get PullRequest, comments, and files through the admin console","createdAt":"2022-06-24T20:38:06Z"}
{"state":"Merged","mergedAt":"2022-06-27T20:18:25Z","number":2007,"body":"Update Intercom UI in the VSCode sidebar, the VSCode discussion view, and the web extension discussion view:\r\n<img width=\"1033\" alt=\"Screen Shot 2022-06-24 at 1 45 45 PM\" src=\"https://user-images.githubusercontent.com/2133518/175667965-59163776-9120-4774-9867-19cb349a4816.png\">\r\n<img width=\"1033\" alt=\"Screen Shot 2022-06-24 at 1 46 07 PM\" src=\"https://user-images.githubusercontent.com/2133518/175667973-cc353fc7-3afa-4078-8213-8a3749401e73.png\">\r\n<img width=\"1296\" alt=\"Screen Shot 2022-06-24 at 2 17 31 PM\" src=\"https://user-images.githubusercontent.com/2133518/175698349-b4e7d2e4-92d2-4544-8024-a7e434ad41cf.png\">\r\n\r\nSome notes:\r\n\r\n* VSCode displays intercom inline, while the web extension does not -- it will launch the dashboard with intercom open\r\n* Web extension sidebar is coming up in a future PR","mergeCommitSha":"4aaadac5520ac20e9eb618ded1dd7356387f1491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2007","title":"Update Intercom UI in VSCode / web extension","createdAt":"2022-06-24T21:59:52Z"}
{"state":"Merged","mergedAt":"2022-07-04T22:18:26Z","number":2008,"body":"- Using k6 testing frame work to create perf tests \r\n- At the moment only includes Read tests (no data mutation)\r\n- Started with most expensive read calls to impose a load on the system \r\n- Provides `localdev`, `smoke`, `load` and `stress` test scenarios. \r\n- `localdev` is only used when developing tests \r\n- `smoke` performs light tests simulating a load that typical user login and some light activity would do\r\n- `load` and `stress` modes perform heavier and more aggressive reads. They are designed to pose a significant load on API service  and pusher service \r\n\r\nI am still working on this but want to have the base templates for now. \r\n\r\nUpdate:\r\n- Added GitHub actions workflows to run smoke tests \r\n- For now workflows don't cause deployment failures until we are confident that tests are working ","mergeCommitSha":"8c7f4851017b9e57397416c0236dca013972317d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2008","title":"first pass for creating perf tests","createdAt":"2022-06-24T22:25:24Z"}
{"state":"Merged","mergedAt":"2022-06-25T01:04:44Z","number":2009,"body":"<img width=\"1782\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175719096-fe5aa2ab-9af8-4bf5-b768-41892ab6b8df.png\">\r\n<img width=\"1185\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175719257-e913b719-5204-4b8b-a233-aae47491a853.png\">\r\n<img width=\"707\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175719151-8592f6f0-dd82-426c-ba0f-9f723031cd91.png\">\r\n","mergeCommitSha":"1c25a07feea820fc49341b75158f1dd9d139034e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2009","title":"Update dashboard color theming","createdAt":"2022-06-24T22:34:08Z"}
{"state":"Closed","mergedAt":null,"number":201,"body":"I remember @matthewjamesadam mentioning yesterday that the clients won't really care about `SourceMarkGroup`s but will instead query `/threads` with a list of `SourceMark` ids (see https://www.notion.so/nextchaptersoftware/Data-Model-b9c282a52230418d9d2235d933954768).","mergeCommitSha":"307fb2df0a0dd1208b8509cf6134287a2357fb43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/201","title":"[RFC] Rename SourceMarkGroupId to SourceMarkId","createdAt":"2022-02-01T17:22:52Z"}
{"state":"Merged","mergedAt":"2022-06-24T22:59:26Z","number":2010,"body":"Fix the situation where a new user has no teams.\r\n\r\nSince there are no teams, there will be no resolved repos. Therefore we can short circuit the process with an empty list.\r\n\r\nTested with AppleUB account which is part of no non-deleted teams\r\n<img width=\"1471\" alt=\"CleanShot 2022-06-24 at 15 48 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-\r\n<img width=\"1177\" alt=\"CleanShot 2022-06-24 at 15 48 52@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-adc3cc78-2eb3-4004-8819-7adcfa643875.png\">\r\na31e718f-097e-44bf-aff7-be237199344d.png\">\r\n.","mergeCommitSha":"4f8de7bbdb785aa422fab1cb640bd9b68d6c0c27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2010","title":"Fix issue no teams (aka new onboarding user)","createdAt":"2022-06-24T22:49:39Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:38:04Z","number":2011,"body":"SwiftUI code diffs are horrible.  This looks like I rewrote each of the UIs, but what I did was:\r\n\r\n* Remove the ZStack, and the HStack/Button that added the question-mark intercom button\r\n* Add a Button (link-styled) to the parent VStack\r\n\r\n<img width=\"532\" alt=\"Screen Shot 2022-06-24 at 3 57 07 PM\" src=\"https://user-images.githubusercontent.com/2133518/175720757-e60f6753-ec76-457f-a232-69fa0605852f.png\">\r\n\r\n","mergeCommitSha":"b530927a4444d4b5a20389e1ead921b847cdb97b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2011","title":"Update hub app Intercom UIs","createdAt":"2022-06-24T22:57:38Z"}
{"state":"Merged","mergedAt":"2022-06-25T00:32:51Z","number":2012,"body":"- Uninstall repos via webhook\n- Add test for TeamStore.uninstallProvider\n- Add test for RepoStore uninstallRepos and rename\n- Refactor GitHubInstallationMaintenanceJob","mergeCommitSha":"f3a6ae44e925a89441174c7017b65229709f6526","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2012","title":"Uninstall repos via webhook","createdAt":"2022-06-24T23:00:36Z"}
{"state":"Merged","mergedAt":"2022-06-24T23:16:27Z","number":2013,"mergeCommitSha":"7f7388c7469327db4b8c694d64eee13e6df308e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2013","title":"Update unblocked app icon","createdAt":"2022-06-24T23:15:26Z"}
{"state":"Merged","mergedAt":"2022-06-27T15:57:53Z","number":2014,"body":"Update sidebar styling..\r\n<img width=\"300\" alt=\"CleanShot 2022-06-24 at 16 11 08@2x\" src=\"https://user-images.githubusercontent.com/1553313/175749546-1f94226f-a4f5-4860-8825-a73554fd3034.png\">\r\n.","mergeCommitSha":"ed7073b210182d99aed43189309c3900b9ec488d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2014","title":"Jeff/unb 345 update web extension sidebar styling","createdAt":"2022-06-24T23:55:01Z"}
{"state":"Merged","mergedAt":"2022-06-27T16:24:41Z","number":2015,"mergeCommitSha":"edbb45d827e09756815b992651b35e66172bda80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2015","title":"Remove team member via hook","createdAt":"2022-06-25T00:25:05Z"}
{"state":"Merged","mergedAt":"2022-06-27T04:29:27Z","number":2016,"body":"<img width=\"424\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175753315-54e73077-6675-4604-a08f-5b7de8265b26.png\">\r\n\r\n<img width=\"427\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175753943-541bda96-3e65-442e-b75c-d3efe44347fc.png\">\r\n\r\n(vscode change to be reflected in a new build)\r\n\r\n* Some other small copy/clean up things","mergeCommitSha":"9b91c4e921fc858d91e5497bae6ce1c430582765","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2016","title":"Update client icons","createdAt":"2022-06-25T01:39:24Z"}
{"state":"Merged","mergedAt":"2022-06-25T03:42:48Z","number":2017,"body":"Mention emails if not existing participants on message creation.","mergeCommitSha":"888aed81e5523a797205b2bbbc6da1902bba8275","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2017","title":"Emails for everyone","createdAt":"2022-06-25T03:13:19Z"}
{"state":"Merged","mergedAt":"2022-06-26T20:31:51Z","number":2018,"mergeCommitSha":"0225b6aadc3597a1342d7080efb9881bb0c0cb41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2018","title":"Add logging extensions for MDC functionality in coroutines (fuck you mu)","createdAt":"2022-06-26T20:19:43Z"}
{"state":"Merged","mergedAt":"2022-06-27T02:19:32Z","number":2019,"mergeCommitSha":"8e1166a2b4b83075bba8aae0809c01965fbcde08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2019","title":"Add Ascend Org to prod","createdAt":"2022-06-27T02:17:47Z"}
{"state":"Merged","mergedAt":"2022-02-03T17:24:57Z","number":202,"body":"* Leverage `storybook-stylesheet-toggle` addon\r\n* Add a `light-theme.css` stylesheet \r\n* Toggle between themes using the paintbrush icon in the top panel:\r\n1.\r\n![image](https://user-images.githubusercontent.com/********/152031043-4d4cbcc4-f82e-4665-a24e-5f55c35ff311.png)\r\n\r\n2.\r\n![image](https://user-images.githubusercontent.com/********/152030921-ad4e785c-2a60-4b75-b662-fe9070df8464.png)\r\n\r\n3.\r\n![image](https://user-images.githubusercontent.com/********/152030972-82d1119f-286f-45f4-8c75-a0bc40aa7fed.png)\r\n","mergeCommitSha":"d0c48d291ec0aef2a1172803416ec4f4f8a126d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/202","title":"Add support for light theme in vscode storybook","createdAt":"2022-02-01T18:44:24Z"}
{"state":"Merged","mergedAt":"2022-06-27T03:01:54Z","number":2020,"mergeCommitSha":"9fdb34e619a0330ce5ffdeefe1ae83086f857247","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2020","title":"Add test","createdAt":"2022-06-27T02:36:24Z"}
{"state":"Merged","mergedAt":"2022-06-27T03:18:14Z","number":2021,"mergeCommitSha":"8aa336ea358659e22d78bec33c28f620d3cd5898","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2021","title":"move to mdc context","createdAt":"2022-06-27T03:05:14Z"}
{"state":"Merged","mergedAt":"2022-06-27T03:37:53Z","number":2022,"mergeCommitSha":"3fed363da86191c3c90934de8f1cf33aa6bf3dde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2022","title":"Try renaming","createdAt":"2022-06-27T03:17:16Z"}
{"state":"Merged","mergedAt":"2022-06-27T16:02:31Z","number":2023,"mergeCommitSha":"a5be02411e70427962f63e1ff4f706ed6b9129e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2023","title":"update","createdAt":"2022-06-27T03:57:36Z"}
{"state":"Merged","mergedAt":"2022-06-27T05:26:29Z","number":2024,"body":"During org installation step, we *need* to refresh teams before refreshing repos.\r\n\r\nThese teams are generated with an app installations, so we need to either poll or have push.\r\nThis used to work as we \"polled\" teams once a minute but that was unreliable.\r\n","mergeCommitSha":"2edb58779515549199778e44586178f6750886ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2024","title":"Fix refreshing org installation","createdAt":"2022-06-27T05:11:06Z"}
{"state":"Merged","mergedAt":"2022-06-27T17:37:42Z","number":2025,"body":"Motivation:\n1. PITA to maintain this thing\n2. requires a deployment\n3. requires that we know the GitHub Org ID in advance\n4. somewhat responsible for this issue: https://linear.app/unblocked/issue/UNB-350/installation-and-repo-ingestion-in-a-weird-state-after-onboarding\n\nPurpose of the allowlist is to prevent unauthorized use of our service.\nRight now that's not really a concern given that nobody knows about\nthe product.\n\nFuture:\n- Simpler solution is just toggle team authorization in admin web.\n- By default all teams are not authorized, meaning that users are\n  restricted from logging into unblocked at the service level.\n- Installations are still ingested regardless.","mergeCommitSha":"665bee1da75cb0448794e21980dab7d6fc91d3e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2025","title":"Remove allowedOrgs config","createdAt":"2022-06-27T06:35:06Z"}
{"state":"Merged","mergedAt":"2022-06-27T06:52:13Z","number":2026,"body":"Manually refresh auth when 401 failure due to missing team credentials.\r\n\r\nThis will be removed once we can poll installations API","mergeCommitSha":"6a9d2e4bbc1c3c0d4f423a1d5d162c2ca3610a1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2026","title":"Refresh Auth when repo 401","createdAt":"2022-06-27T06:44:28Z"}
{"state":"Merged","mergedAt":"2022-06-27T18:21:15Z","number":2027,"mergeCommitSha":"27860047877129f55313f9ea5cd27bbaca3e7c98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2027","title":"Rename org and add member via hook","createdAt":"2022-06-27T06:51:03Z"}
{"state":"Merged","mergedAt":"2022-06-27T07:44:00Z","number":2028,"mergeCommitSha":"4de6c5358f56dc5819a7d57d00c6b047d51daeac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2028","title":"Hide deleted teams in disclosure element to declutter","createdAt":"2022-06-27T07:08:39Z"}
{"state":"Merged","mergedAt":"2022-06-27T16:11:26Z","number":2029,"body":"The only caveat with this approach is that it may be possible the user has multiple installers alive at once, and in that case we won't know which one belongs to us. \r\n\r\nThere is a possible workaround for this though (if we deem it important): we have can the installer drop its PID into user defaults. \r\n\r\nBehaviour after this change:\r\n\r\n- When _any_ installer is open, hub will not pop open. Hub will pop open when _all_ installer processes have exited.\r\n- Manually popping open the hub will cancel this behaviour. There's an open question about whether this is desirable, or if we want to pop the hub open regardless of previous user interaction.\r\n\r\nResolves UNB-346","mergeCommitSha":"c333209d64361bebe522e218f656e0366075996c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2029","title":"Only open the hub after the installer is gone","createdAt":"2022-06-27T15:35:37Z"}
{"state":"Merged","mergedAt":"2022-02-01T19:16:08Z","number":203,"body":"- Fixed an issue in Route table rule creation. We were getting dupe entries back when querying CDK for subnets list\r\n- Fixed ECR replication for prod and added replication target to ECR config\r\n- Re-created prod RDS database\r\n- Set prod RDS DB to 2 instances\r\n- Updated RDS ServiceAccount role in EKS \r\n- Added backup policy and 30 day default retention to RDS\r\n\r\nAll changes have been deployed. ","mergeCommitSha":"64a2e731a56ef5eb80e0f88ad53aea18ddbadaaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/203","title":"A whole bunch of cleanup and minor fixes","createdAt":"2022-02-01T18:49:11Z"}
{"state":"Merged","mergedAt":"2022-06-27T16:21:32Z","number":2030,"body":"Resolves UNB-347","mergeCommitSha":"6c8201d2404bf73eb8a47a94ea6208b6dce5eec8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2030","title":"Don't know upgrade notifications until onboarding is complete","createdAt":"2022-06-27T16:00:27Z"}
{"state":"Merged","mergedAt":"2022-06-27T18:04:56Z","number":2031,"body":"Resolves UNB-348","mergeCommitSha":"a899c3267655c113e4355f34b9c544514149394a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2031","title":"Increase onboarding launch timeout","createdAt":"2022-06-27T16:56:40Z"}
{"state":"Merged","mergedAt":"2022-06-27T17:26:31Z","number":2032,"body":"Was missing return statement for find repo.\r\nTherefore never updating the repoStore state...\r\n\r\nOnce this initial release is out, I'll put a lot more focus on adding tests to these stores...","mergeCommitSha":"73f5fba87435b99260391d159277a7dcd4b00941","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2032","title":"Add return statement","createdAt":"2022-06-27T17:17:13Z"}
{"state":"Merged","mergedAt":"2022-06-27T19:11:13Z","number":2033,"body":"<img width=\"1851\" alt=\"CleanShot 2022-06-27 at 11 49 23@2x\" src=\"https://user-images.githubusercontent.com/1924615/*********-63139cf7-db52-4461-b996-eae8e321d371.png\">\r\n\r\nHelps answer the following (from https://www.notion.so/nextchaptersoftware/User-Metrics-af26d39abbf24aca8269809eb27ef93c):\r\n\r\n```\r\n- Which members of the team have created an account and logged in?\r\n    - Which members of the team have yet to log in\r\n```\r\n\r\n@richiebres I see on the the TeamMemberPage it says \"Has Person Account\". Can/should we rename that to \"Is Unblocked User\" or \"Has Unblocked Account\"?","mergeCommitSha":"fc0a96c79d1278b5feba7edf2ceccf7f1932391d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2033","title":"Show unblocked user status on TeamMembersPage","createdAt":"2022-06-27T17:20:09Z"}
{"state":"Merged","mergedAt":"2022-06-27T17:45:47Z","number":2034,"body":"Async operation without await in a try catch skips the catch block.\r\n\r\n","mergeCommitSha":"022d5ad9dac5f70b1023fd52d98630bd1c0961c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2034","title":"Add awaits to try catch in createKnowledge","createdAt":"2022-06-27T17:28:13Z"}
{"state":"Merged","mergedAt":"2022-06-27T17:40:53Z","number":2035,"body":"- update\r\n- list mentions\r\n","mergeCommitSha":"347dcc2f32b6c02581ac5a94f270bb0d7c4785bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2035","title":"MinorAdminChange","createdAt":"2022-06-27T17:38:05Z"}
{"state":"Merged","mergedAt":"2022-06-27T18:38:16Z","number":2036,"mergeCommitSha":"d3e3ccfb09c717ca2dfb85a6490523fc747d3d46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2036","title":"Add missing margin and update message editor focus state","createdAt":"2022-06-27T17:48:05Z"}
{"state":"Merged","mergedAt":"2022-06-27T19:07:47Z","number":2037,"body":"https://medium.com/@erickpranata/kotlin-preconditions-kt-7f257c4a97dd","mergeCommitSha":"8f0dcc313e900a9daa828e095950e9a4b3c9b09c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2037","title":"Use kotlin preconditions","createdAt":"2022-06-27T18:30:38Z"}
{"state":"Merged","mergedAt":"2022-06-27T19:58:05Z","number":2038,"body":"Really should never happen","mergeCommitSha":"6cf5843dc077ac7b05efebcc496af1d98632d011","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2038","title":"Throw error if repo is not SCM connected","createdAt":"2022-06-27T19:10:50Z"}
{"state":"Merged","mergedAt":"2022-06-27T19:55:44Z","number":2039,"body":"It would be very unusual for a team to uninstall their SCM Org minutes after\nonboarding. So treating this scenario as a bug and retrying. The impact will\nbe that these messages will b e retried a number of times. Ideally, we should\nqueue affinity/isolation and short circuit the main queue to reduce the\nimpact on other teams.\n\nFailing to treat this scenario as an retryable event lead to a massive drop\nin PR comment ingestion, which we have to fix afterwards. See impact in the\nissue below:\nhttps://linear.app/unblocked/issue/UNB-350#comment-4c50c04c","mergeCommitSha":"80e3f0d09cee595f3bc7d7f361b7545f3cb8d78b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2039","title":"Should retry PR comment job when team is not connected","createdAt":"2022-06-27T19:20:44Z"}
{"state":"Merged","mergedAt":"2022-02-02T00:14:15Z","number":204,"body":"## Summary\r\n\r\nThis PR implements a small GitHub REST client to perform the token exchange and fetch user info, including primary email. I've also brought in Mockito to create a few mocks. ","mergeCommitSha":"de1c100c01675e43bea89fedbc2085da496bba54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/204","title":"Pulls identity info from GitHub after authorization","createdAt":"2022-02-01T18:52:01Z"}
{"state":"Merged","mergedAt":"2022-06-27T20:17:57Z","number":2040,"body":"This reverts commit 01c30d6b5b14564b237ea8ac193a371ebc3da25e.\r\n\r\nReason for revert:\r\nhttps://linear.app/unblocked/issue/UNB-309/getsourcemarklatestpoint-returns-commits-not-on-local-branch\r\n\r\nTested and the result of this change is an increase in the `PointNotInRepo` case, which is expected.\r\n<img width=\"1051\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/176025925-763e180e-e4eb-48da-b6f7-82b512f3d545.png\">\r\n","mergeCommitSha":"8e6d8dd291da018b4f64dd7916c1280ee4037788","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2040","title":"Revert \"SourceMark engine should handle force pushes (#1680)\"","createdAt":"2022-06-27T19:51:09Z"}
{"state":"Merged","mergedAt":"2022-06-27T20:52:53Z","number":2041,"body":"Fixed up some template parameters and addresses weird out of bounds issues with contents.\r\n\r\n<img width=\"703\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/*********-6fdb5aec-951e-471e-8768-704a023e0b1c.png\">\r\n","mergeCommitSha":"2c1e5fcffcb69cb9915da4ace8753ded61230875","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2041","title":"Update email templates","createdAt":"2022-06-27T19:53:27Z"}
{"state":"Merged","mergedAt":"2022-06-27T20:39:05Z","number":2042,"body":"The context values for our log lines still don't look right: \r\n\r\nhttps://app.logz.io/#/goto/42a0a1599761fac3e8a9433c0a61d993?switchToAccountId=411850\r\n\r\nAdding this here just to confirm before digging deeper.","mergeCommitSha":"da2297b5a65dd413739ea0c2cc46d97e2febbce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2042","title":"[sorry richiebres :(] Add log line with repoId and pr number","createdAt":"2022-06-27T20:34:04Z"}
{"state":"Merged","mergedAt":"2022-06-27T21:59:41Z","number":2043,"body":"per new designs:\r\n<img width=\"829\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-4d653a7a-48b5-479b-9c39-74b50ebb9166.png\">\r\n\r\nalso:\r\n* Added breakpoint for dashboard margin\r\n* Fix line spacing of user icon element in the web extension sidebar now that the font is no longer Sofia Pro\r\n* Pin the header of the dashboard threads overview and style the discussion header the same","mergeCommitSha":"b81a35bd1ebbaf5dd77e225915b2782654ca0b69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2043","title":"Style dashboard dropdown","createdAt":"2022-06-27T20:52:49Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:07:04Z","number":2044,"mergeCommitSha":"73757ddf856a54f6ee3b77106cd3b17753673b01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2044","title":"Fixes various missing team states","createdAt":"2022-06-27T21:01:44Z"}
{"state":"Merged","mergedAt":"2022-06-28T00:39:01Z","number":2045,"body":"Prev size of 1024x1024 was downsized too sharply:\r\n<img width=\"115\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176038975-f1d31688-943d-47f6-92b0-f2fa7e8e2c97.png\">\r\n(before and after)","mergeCommitSha":"c2e1a49d0326bd8b0f900b171ac6a1801a5d5959","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2045","title":"Update favicon with 32x32","createdAt":"2022-06-27T21:31:58Z"}
{"state":"Merged","mergedAt":"2022-06-28T00:30:43Z","number":2046,"body":"Shows a banner on every team-related page when the team is deleted.","mergeCommitSha":"b46b0d645ff52e8a9641a6d3e448a15f4d0185f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2046","title":"Admin web banner for deleted team","createdAt":"2022-06-27T21:38:28Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:44:17Z","number":2047,"body":"<img width=\"1073\" alt=\"Screen Shot 2022-06-27 at 2 38 47 PM\" src=\"https://user-images.githubusercontent.com/2133518/176040064-c91928e3-aede-4303-b2d4-611907516173.png\">\r\n","mergeCommitSha":"85ab2adde92a190153724c7de19caa2801ddc65a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2047","title":"Show intercom button in web extension sidebar","createdAt":"2022-06-27T21:40:03Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:31:17Z","number":2048,"body":"```\r\nTooltip each row to see full title (probably a better experience we can add later)\r\nIn an unpinned state, keep tab sidebar expands.\r\nAdd delay when you mouse off of the sidebar. It’s quite jumpy right now.\r\nAdd border box for the pin icon. Also quite finicky right now.\r\n```","mergeCommitSha":"45d6af7b816a0e77af260701f49ba9cfbd2fca62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2048","title":"Sidebar styling updates","createdAt":"2022-06-27T22:00:50Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:36:27Z","number":2049,"mergeCommitSha":"3e53d80ce844b1ab2045e95eb90e5b252346ecd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2049","title":"Update hub icon","createdAt":"2022-06-27T22:30:19Z"}
{"state":"Merged","mergedAt":"2022-02-02T22:25:54Z","number":205,"body":"Adds some basic theming for the MessageEditor for VSCode and web.\r\n\r\nSome questions:\r\n* Is the approach OK?  I did the simple thing and defined CSS classes that each project can provide.\r\n* Is this basic file structure OK?  We now have a lot of 'MessageEditor' files and classes.  Not sure if this is confusing or OK or not.","mergeCommitSha":"c89258192dafab6ba3f22974250cf8f816bfc9b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/205","title":"Basic MessageEditor theming for web and vscode","createdAt":"2022-02-01T19:35:18Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:39:00Z","number":2050,"body":"Fixes UNB-351\r\n\r\n@kaych LMK if this is a problem or not, looks like they weren't really hooked up to anything?","mergeCommitSha":"305cc19a51f5ecaf07431bb3e4b0ab11f823b5ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2050","title":"Remove unused reply/pin action buttons","createdAt":"2022-06-27T22:30:58Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:42:47Z","number":2051,"body":"Should the occasion arise...\n","mergeCommitSha":"60cc6546a951fef114b6fe1be9aa7f8959dce6b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2051","title":"Adds disabled test for quick token generation","createdAt":"2022-06-27T22:31:15Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:32:41Z","number":2052,"body":"<img width=\"644\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/176047042-59622525-9b87-4f0d-856f-e81e07877e32.png\">\r\n","mergeCommitSha":"c843128d8cc442d610c46a2415ec54f8ce876b64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2052","title":"Fix email parsing of html","createdAt":"2022-06-27T22:32:21Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:58:26Z","number":2053,"body":"Token must be refreshed during onboarding or the team claims won't update and subsequent team-based requests will fail\n","mergeCommitSha":"4bc68b4f67730111b439b189aba269db425329b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2053","title":"Poll refresh token during onboarding","createdAt":"2022-06-27T22:44:59Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:58:15Z","number":2054,"mergeCommitSha":"53b81cea773600349bc450ead7706fe69a88ad81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2054","title":"Send view thread metrics when thread is opened from hub","createdAt":"2022-06-27T22:53:16Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:31:02Z","number":2055,"body":"We want to send thread events on every open event.\r\n\r\nRemoved guard but keeping metricsCache code to allow rate limiting.","mergeCommitSha":"44ff0c36807216dcfdc05bd1d6628e9b9fc63f66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2055","title":"Send thread event on every request","createdAt":"2022-06-27T23:00:51Z"}
{"state":"Merged","mergedAt":"2022-06-27T23:36:56Z","number":2056,"body":"\r\n![CleanShot 2022-06-27 at 16 25 12](https://user-images.githubusercontent.com/3806658/176053309-0c6a1159-6218-4f34-8e59-42aac07c8e0e.gif)\r\n","mergeCommitSha":"ac19458db56f844a19d2ac2abcc0f6bab905f861","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2056","title":"Add team member info for @mention in web ","createdAt":"2022-06-27T23:24:49Z"}
{"state":"Merged","mergedAt":"2022-06-28T16:41:59Z","number":2057,"body":"richie/unb-300-handle-installation_repositories-event","mergeCommitSha":"e842e378720d4b23f8bdb738999bcd84b7428053","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2057","title":"Add repo via hook","createdAt":"2022-06-27T23:26:06Z"}
{"state":"Merged","mergedAt":"2022-06-27T23:45:28Z","number":2058,"mergeCommitSha":"2def97b6bb4da3ea57d11d88668b3bc24da6f14d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2058","title":"Log running processes on startup","createdAt":"2022-06-27T23:45:16Z"}
{"state":"Merged","mergedAt":"2022-06-28T00:40:20Z","number":2059,"body":"Consistently order teams. Newer teams shown first.","mergeCommitSha":"df780e462b21c76cf7c617b1c7190a7261c66c8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2059","title":"Admin order teams","createdAt":"2022-06-27T23:50:50Z"}
{"state":"Merged","mergedAt":"2022-02-01T20:50:57Z","number":206,"mergeCommitSha":"0ab8c94eabcd03ff2ad1fe733dd93f8994a0bd02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/206","title":"Add rds information and update configuration","createdAt":"2022-02-01T20:46:49Z"}
{"state":"Merged","mergedAt":"2022-06-29T03:33:25Z","number":2060,"mergeCommitSha":"5c8edd1d92ebdb0c9a69a6baee94ad9201e98e3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2060","title":"Calculate DAT, WAT, and MAT","createdAt":"2022-06-28T00:11:43Z"}
{"state":"Merged","mergedAt":"2022-06-28T02:08:04Z","number":2061,"body":"![CleanShot 2022-06-27 at 17 36 47](https://user-images.githubusercontent.com/3806658/176062379-e018105b-3ed3-4449-aec5-789a8279e85c.gif)\r\n","mergeCommitSha":"f0c7a64d2c9c0d71acc7be974b4d0bdbe8efe526","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2061","title":"Add @mention for web extension","createdAt":"2022-06-28T00:35:25Z"}
{"state":"Merged","mergedAt":"2022-06-28T03:14:48Z","number":2062,"mergeCommitSha":"62e1f7be63baddfef46702865947db5f23d35103","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2062","title":"Add installer detection logging during onboarding","createdAt":"2022-06-28T03:14:18Z"}
{"state":"Merged","mergedAt":"2022-06-28T22:48:12Z","number":2063,"body":"This is part 1 of allowing us to do dashboard development against dev.  Having to run the dashboard against a local stack has been a significant time-waster, generally it'd be easier to have to keep fewer parts of the system running at a time.\r\n\r\nThis part changes the API service in such a way that (I hope) login is allowed from a localhost dashboard, in the dev environment:\r\n\r\n* Update CORS for dev, to allow access from localhost development ports\r\n* Update `/login` call, adding an optional `redirectUrl` query parameter.  The idea here is that clients could provide the ultimate exchange URL to end up at.  If nothing is provided, the default redirect URL for that environment is used.\r\n* Update the Auth config to specify which \"alternative\" redirect URLs are allowed for each environment.  Basically, dev allows redirecting back to localhost, and all other envs do not allow any other alternatives.  We don't want people jumping to random URLs in prod, so I locked this down.\r\n\r\nThe next PR would add the client bits to run against dev from a local dashboard environment.  I have to deploy this first to see if it works though.\r\n\r\nThe only concern I have here is that the config files are not typed at all -- if I mess things up, and (say) fail to pass in a required value for the prod config, or provide something of the wrong type, I assume that will cause some problems?","mergeCommitSha":"2c15ad12e7e61b1e9b2917634b29573eb5a3b05b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2063","title":"Allow logging into dev from localhost","createdAt":"2022-06-28T04:41:17Z"}
{"state":"Merged","mergedAt":"2022-06-28T16:47:18Z","number":2064,"body":"Currently we are CPU bound in load tests. Other instance types turned out to be very expensive. For the preview release we will still rely on burstable instances until we can make a good case for higher end instance types. \r\n\r\nResized prod to use t4g.large instance size (largest burstable type available). We might want to invest in multi-writer cluster before resizing instances again. ","mergeCommitSha":"dc0039f96737d0f58f8da2f8164a638b68a48468","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2064","title":"Upgrade rds instance size prod","createdAt":"2022-06-28T08:21:35Z"}
{"state":"Merged","mergedAt":"2022-06-28T18:03:20Z","number":2065,"body":"Renaming the repo also needs to be more lenient to account for a repo rename\nracing against the org rename and winning.","mergeCommitSha":"200c4b4c76d8aec1686fc2656bf3ccc1a486b270","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2065","title":"Renaming organization must rename repo owner part","createdAt":"2022-06-28T16:05:25Z"}
{"state":"Merged","mergedAt":"2022-06-30T08:14:34Z","number":2066,"mergeCommitSha":"66be31c712ee59ee4fe14f876091bb4fd202f915","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2066","title":"Get timestamp of last thread view","createdAt":"2022-06-28T16:20:21Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:26:07Z","number":2067,"mergeCommitSha":"5a7723233a6d71de5bf7322ce7c7e9f0b05c4806","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2067","title":"Teams with no threads shouldn't crash the hub","createdAt":"2022-06-28T17:10:52Z"}
{"state":"Merged","mergedAt":"2022-06-29T00:00:39Z","number":2068,"body":"This duplicates functionality in the `GitHubInstallationMaintenanceJob`, but the use cases are different:\r\n- webhook install will only ever _create_ teams, members, repos\r\n- the maintenance job _creates and destroys_ teams, members, repos\r\n\r\nhttps://linear.app/unblocked/issue/UNB-299/handle-installation-event","mergeCommitSha":"c7a2a841ef28a31886f724ef164976d1c478d231","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2068","title":"Handle installation hook","createdAt":"2022-06-28T17:12:35Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:30:05Z","number":2069,"body":"- Updated existing asset error images with the new ones for 404 and 401 codes\r\n- Added a new image to be used for deleted assets once we implement the functionality ","mergeCommitSha":"64af9b2cb67d5e734c304ea0299054999f4faf7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2069","title":"Update assets error images","createdAt":"2022-06-28T17:17:40Z"}
{"state":"Merged","mergedAt":"2022-02-01T21:21:18Z","number":207,"mergeCommitSha":"3505875baea1f6918c3335f3c85a4ae921dfc1c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/207","title":"Remove unused delegate class","createdAt":"2022-02-01T21:18:56Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:38:46Z","number":2070,"body":"The \"withLoggingContext\" that we were using from mu library will not work as it makes assumptions that we're not. using coroutines.\r\n\r\nJust doing simple copy and write logic as what it was doing was wrong.\r\n\r\nAdded stress test that reproed the problem.","mergeCommitSha":"3ab0753eef08516d3e395f3bdb5fbd5ad7106927","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2070","title":"Fix logging part 2","createdAt":"2022-06-28T17:21:33Z"}
{"state":"Merged","mergedAt":"2022-06-29T20:57:03Z","number":2071,"body":"Add support for ignoring fallback dialogs.\r\n\r\nAPI for passing one shot messages and receiving a response isn't great for multi browser support... \r\n\r\n![CleanShot 2022-06-27 at 15 53 03@2x](https://user-images.githubusercontent.com/1553313/176245672-61193835-8e33-4b6f-9fcd-884a726b8eab.png)\r\n\r\n","mergeCommitSha":"8d8fbac40c94a291e1e4f1931943b078a844d005","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2071","title":"Update styling for fallback Dialog","createdAt":"2022-06-28T17:30:09Z"}
{"state":"Merged","mergedAt":"2022-06-28T19:07:40Z","number":2072,"mergeCommitSha":"49d43658d0ce02564fb3417b46b9a301fcbfcc1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2072","title":"Center spinner in VSCode","createdAt":"2022-06-28T17:43:02Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:51:44Z","number":2073,"mergeCommitSha":"687a7a0e91cc7fa81dcada2b9d7da7838d8ad728","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2073","title":"update","createdAt":"2022-06-28T17:50:44Z"}
{"state":"Merged","mergedAt":"2022-06-28T17:59:30Z","number":2074,"mergeCommitSha":"40644665a44fbe7f079b19932c0d23f1fdc9ce3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2074","title":"Update email assets","createdAt":"2022-06-28T17:54:17Z"}
{"state":"Merged","mergedAt":"2022-06-28T18:22:01Z","number":2075,"body":"I have already enabled this on one prod instance to see query times. The default retention of 7 days is free so we should have them enabled in Dev and prod. \r\n\r\nIt's kinda like the Mongo dashboard and shows all the slow queries, long waits and locks etc. ","mergeCommitSha":"2976d0876433980d5c42433d69c0bbe1c620289c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2075","title":"enable performance insights","createdAt":"2022-06-28T18:13:21Z"}
{"state":"Merged","mergedAt":"2022-07-02T22:59:28Z","number":2076,"body":"Simple Breakpoints for VSCode sidebar.\r\n\r\n<img width=\"528\" alt=\"CleanShot 2022-06-28 at 11 37 55@2x\" src=\"https://user-images.githubusercontent.com/1553313/176258947-7ce8613d-a280-4c77-97a0-9ad90f73d290.png\">\r\n<img width=\"475\" alt=\"CleanShot 2022-06-28 at 11 48 20@2x\" src=\"https://user-images.githubusercontent.com/1553313/176259344-e581d07e-52f7-493b-b97a-cb4b3dd7e523.png\">\r\n\r\n","mergeCommitSha":"6d78315e3ba37fcb4068e5881cf335683d8391f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2076","title":"Sidebar breakpoints","createdAt":"2022-06-28T18:48:37Z"}
{"state":"Merged","mergedAt":"2022-06-28T18:58:37Z","number":2077,"mergeCommitSha":"0e55ed36fb974386504c91fc091ce8fbe63da7b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2077","title":"More favicon nits","createdAt":"2022-06-28T18:51:25Z"}
{"state":"Merged","mergedAt":"2022-06-28T19:07:53Z","number":2078,"body":"<img width=\"904\" alt=\"CleanShot 2022-06-28 at 11 53 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/176260459-804e7384-76dc-44b8-87a4-dc18550a53ff.png\">\r\n<img width=\"1108\" alt=\"CleanShot 2022-06-28 at 11 52 52@2x\" src=\"https://user-images.githubusercontent.com/1553313/176260466-c3d19240-2a3b-4a1d-941b-4a74d131e389.png\">\r\n<img width=\"1004\" alt=\"CleanShot 2022-06-28 at 11 52 43@2x\" src=\"https://user-images.githubusercontent.com/1553313/176260469-c2f56c15-61da-4b27-b56a-e4feb7761b58.png\">\r\n\r\n","mergeCommitSha":"08a0fa20fb1175675c80cc3948459b3a70d9f463","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2078","title":"Use IconForThread to generate source mark icons","createdAt":"2022-06-28T18:54:39Z"}
{"state":"Merged","mergedAt":"2022-06-29T00:10:33Z","number":2079,"body":"Needed for push\r\n\r\nResolves UNB-274","mergeCommitSha":"7531158668582c841d21bba33f5705da1328e5c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2079","title":"Add lastModified header to getTeamMembers response","createdAt":"2022-06-28T19:02:00Z"}
{"state":"Merged","mergedAt":"2022-02-02T19:03:23Z","number":208,"body":"Basic VSCode auth with message UI.\r\n\r\nNo webview in place.\r\n![CleanShot 2022-02-01 at 22 25 15](https://user-images.githubusercontent.com/1553313/152105158-96863ffd-b60f-4d92-9b8c-1bcbe1e0391d.gif)\r\n\r\n\r\n","mergeCommitSha":"4fe2e211e751546b08cb55b690e2d20abf3b0541","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/208","title":"VSCode Auth","createdAt":"2022-02-01T21:27:31Z"}
{"state":"Merged","mergedAt":"2022-06-28T19:10:50Z","number":2080,"body":"In GH light mode, the border was very heavy.  This now matches how the sidebar search input border looks.","mergeCommitSha":"9432e38ff892f607ca284a0ddc850b6a5deef631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2080","title":"Fix 'Contact Support' sidebar border in web extension","createdAt":"2022-06-28T19:06:14Z"}
{"state":"Merged","mergedAt":"2022-06-29T17:55:37Z","number":2081,"body":"Opening a project from sidebar project selector did *not* re-open the sidebar to correct state.\r\n\r\nUpdating flow to keep plugin sidebar open on workspace open.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/176262802-de12df6e-a01e-4b93-a844-de6e98b0fddf.mp4\r\n\r\n ","mergeCommitSha":"169c5d23f570836445b0b2def19e4403ce88b958","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2081","title":"Fix issue with sidebar project selector","createdAt":"2022-06-28T19:06:35Z"}
{"state":"Merged","mergedAt":"2022-06-28T19:10:35Z","number":2082,"body":"This reverts commit 8e6d8dd291da018b4f64dd7916c1280ee4037788.\r\n\r\nReasons for rolling back the revert is because without this bug,\r\nsourcemark doon't show properly when there are merge commits.\r\n\r\nFucked up. Both will be fixed once we handle merge commit properly.","mergeCommitSha":"30c3019b1a81982a8d79952a8a7c82bebae8d363","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2082","title":"Revert \"Revert \"SourceMark engine should handle force pushes (#1680)\" (#2040)\"","createdAt":"2022-06-28T19:10:29Z"}
{"state":"Merged","mergedAt":"2022-06-28T19:32:19Z","number":2083,"mergeCommitSha":"9b16e069dbb78184208448c1ab1cbe183853c7ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2083","title":"update","createdAt":"2022-06-28T19:17:10Z"}
{"state":"Merged","mergedAt":"2022-06-28T20:05:04Z","number":2084,"body":"When one installs the GH App without coming from VSCode, we will now redirect user to `Landing/download` instead of to VSCode.\r\n\r\nAka instead of seeing this:\r\n<img width=\"1052\" alt=\"CleanShot 2022-06-28 at 12 21 08@2x\" src=\"https://user-images.githubusercontent.com/1553313/176267672-57fd28de-60b2-4a0b-9ca3-98bdda6a31d0.png\">\r\n\r\nThey will be redirected immediately to \r\n<img width=\"812\" alt=\"CleanShot 2022-06-28 at 12 21 33@2x\" src=\"https://user-images.githubusercontent.com/1553313/176267719-58c45e57-6a11-4a65-bf69-5767abab2ae4.png\">\r\n\r\n","mergeCommitSha":"516fc6d919fbb8347f21d5ed1887a7f5d35586fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2084","title":"Install Redirect to Download","createdAt":"2022-06-28T19:20:49Z"}
{"state":"Merged","mergedAt":"2022-06-28T23:37:23Z","number":2085,"body":"Resolves UNB-160","mergeCommitSha":"f23ca8bc6c293c3e340176ebd85704fac7654909","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2085","title":"Defer asking for notification permissions until first unread","createdAt":"2022-06-28T20:12:18Z"}
{"state":"Merged","mergedAt":"2022-06-28T20:20:55Z","number":2086,"mergeCommitSha":"da3f3dcade6a2c6611f8ff6abdc8c68cbaf8e3f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2086","title":"add configuration set name","createdAt":"2022-06-28T20:20:44Z"}
{"state":"Merged","mergedAt":"2022-06-28T21:30:48Z","number":2087,"body":"Directly importing the assets in the html doesn't work in the bundled build (needs relative path), use plugin to generate a manifest to create the icons instead \r\n\r\nThis does generate a manifest json but I think its contents are relatively safe? \r\n<img width=\"553\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176288580-5762373d-e03c-470f-9ebe-7cece0002895.png\">\r\n","mergeCommitSha":"be84dd382fa669ad0e03bcfb6f84c801e7755ff9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2087","title":"Use manifest plugin for favicons","createdAt":"2022-06-28T21:11:10Z"}
{"state":"Merged","mergedAt":"2022-06-28T23:45:01Z","number":2088,"body":"<img width=\"1180\" alt=\"CleanShot 2022-06-28 at 14 24 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/176301522-a4042023-fc05-40f3-ab65-3877420fbf4e.png\">\r\n\r\nMoved some styles to mixins.\r\nMoved some constants to shared constants.\r\n\r\nOverall I think we need to refactor out the landing page css into a more generic format post release.","mergeCommitSha":"4646529ec25906758cd87da91d3e75d461c52525","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2088","title":"Web login page","createdAt":"2022-06-28T21:29:44Z"}
{"state":"Merged","mergedAt":"2022-06-30T06:49:19Z","number":2089,"body":"Fixes UNB-266","mergeCommitSha":"e352219265fea5fae6f4b037b0784ce947fc8051","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2089","title":"Change \"```suggestion\" thread titles to \"Suggestion: <code>...\"","createdAt":"2022-06-28T21:33:47Z"}
{"state":"Merged","mergedAt":"2022-02-01T21:55:02Z","number":209,"body":"Generates type-safe Kotlin code from GraphQL files:\r\n```shell\r\n$ tree apiservice/build/generated/src/main/kotlin/\r\napiservice/build/generated/src/main/kotlin/\r\n└── com\r\n    └── nextchaptersoftware\r\n        └── clients\r\n            └── githubGraphql\r\n                ├── GraphQLTypeAliases.kt\r\n                ├── UserQuery.kt\r\n                └── userquery\r\n                    └── User.kt\r\n```\r\n\r\nThe submodule is added to get the published GitHub V4 GraphQl schema. The intention is that we uplevel this submodule occasionally as GitHub release new versions.","mergeCommitSha":"59c278769fd5e2d55771030812f6c801fed387b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/209","title":"GitHub graphql generator","createdAt":"2022-02-01T21:27:40Z"}
{"state":"Merged","mergedAt":"2022-06-28T22:12:40Z","number":2090,"mergeCommitSha":"a64b3614d6db10fb36d084571fc9b9baf6dfab8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2090","title":"Fix synchronous logging apis (mu sucks)","createdAt":"2022-06-28T21:43:25Z"}
{"state":"Merged","mergedAt":"2022-06-28T23:00:07Z","number":2091,"body":"I thought I tested this on Safari but it was probably showing a cached icon. Needed to test on a private browser.\r\n\r\nbefore:\r\n<img width=\"279\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176315101-1fb8712b-17ef-4012-91e9-822e2d10cd60.png\">\r\nafter:\r\n<img width=\"387\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176315122-ab31831d-d94d-4d61-a9d9-7ac747988cff.png\">\r\n","mergeCommitSha":"75696ea285b9e1ee38697bd3b09152b98a15d32a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2091","title":"Images need to be 32x32 for safari to recognize","createdAt":"2022-06-28T22:30:00Z"}
{"state":"Merged","mergedAt":"2022-06-28T23:05:29Z","number":2092,"mergeCommitSha":"2b46c9e48a26c671f002d789eef9fddc7842c096","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2092","title":"Add extension download links to hub","createdAt":"2022-06-28T22:50:44Z"}
{"state":"Merged","mergedAt":"2022-06-28T23:53:35Z","number":2093,"body":"<img width=\"428\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176320173-a74b684f-fb87-4892-8f9b-324111ad4b9e.png\">\r\n<img width=\"105\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176320188-5fcbe18b-3cd6-455c-a092-c97961911aab.png\">\r\n","mergeCommitSha":"d6caa1a7b39c64fcf84898aa25f25a0bc2569c11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2093","title":"Update extension manifest icons","createdAt":"2022-06-28T23:20:44Z"}
{"state":"Merged","mergedAt":"2022-06-29T00:29:45Z","number":2094,"mergeCommitSha":"33398adc9151c9a4dec0ad86b6171278c64df84f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2094","title":"Disable preauth secret cookie in dev and local","createdAt":"2022-06-28T23:32:58Z"}
{"state":"Merged","mergedAt":"2022-06-29T01:59:58Z","number":2095,"body":"- Memoize GitHub API responses in memory\n- Introduce general cached pattern.\n- Can extend in future for LRU, or expiry, or distributed store backed (PG or Redis).\n\nhttps://linear.app/unblocked/issue/UNB-370/optimize-getinstallations-api-call","mergeCommitSha":"a10d8bcd04e0d534c4cf4051d02ce3b74486e403","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2095","title":"Optimize getInstallations API call to facilitate polling during onboarding flow","createdAt":"2022-06-29T00:24:11Z"}
{"state":"Merged","mergedAt":"2022-06-29T01:13:22Z","number":2096,"body":"Ben just hit this bug.","mergeCommitSha":"0b5f6f7106d729c37bdced67e0a5655b8585fe63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2096","title":"Fix for failure to add owners to organizations","createdAt":"2022-06-29T00:45:17Z"}
{"state":"Merged","mergedAt":"2022-06-29T02:08:10Z","number":2097,"mergeCommitSha":"2513a6ecf85259e48fa92520ccf2dc60f2af349a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2097","title":"Too much padding after removing extensions menu items","createdAt":"2022-06-29T02:05:49Z"}
{"state":"Merged","mergedAt":"2022-06-29T17:18:40Z","number":2098,"body":"per Ben's request:\r\n<img width=\"960\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176340309-22fd418d-1a34-46b6-a0e1-843ddbba7fd7.png\">\r\n\r\n* also upped the z-index of the modal dialog to layer over the GH pinned header (they must've upped the z-index recently?)\r\n<img width=\"1203\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176340431-5c641a39-bb0d-4873-a268-63c6ebc6389f.png\">\r\n","mergeCommitSha":"6def4a067b941e031c2b3d7ee644f55a77643001","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2098","title":"Add thread icon to extension dialog","createdAt":"2022-06-29T02:48:33Z"}
{"state":"Merged","mergedAt":"2022-06-29T03:10:54Z","number":2099,"body":"This reverts commit 40644665a44fbe7f079b19932c0d23f1fdc9ce3b.\r\n","mergeCommitSha":"2606bfe16ea27d3f8ed45180569e8e82fc60b370","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2099","title":"Revert \"Update email assets (#2074)\"","createdAt":"2022-06-29T03:10:27Z"}
{"state":"Merged","mergedAt":"2022-02-01T22:14:19Z","number":210,"body":"Use the new TypeKit project reference.","mergeCommitSha":"5a3ae191becf01d46d082ad81b6e52569365ff01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/210","title":"Fix TypeKit","createdAt":"2022-02-01T21:50:53Z"}
{"state":"Merged","mergedAt":"2022-06-29T03:13:43Z","number":2100,"body":"- Revert \"Revert \"Update email assets (#2074)\" (#2099)\"\r\n- update\r\n","mergeCommitSha":"008b02f40d1c550a7facf78f68f5bca109f67923","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2100","title":"NewAssets","createdAt":"2022-06-29T03:13:22Z"}
{"state":"Merged","mergedAt":"2022-06-29T06:15:21Z","number":2101,"body":"- Add Ben to list of demo users\n- Ignore duplicate errors, which occurs only for demo users when the \"fake\"\n  network relationship failed to overwrite the \"real\" network relationship.\n- Also flip the order to that fake relationships take precedence.","mergeCommitSha":"21f7a7cb8460c4829ce5f559dbc9d55a89610bac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2101","title":"Recommendation demo hack fixes","createdAt":"2022-06-29T06:07:07Z"}
{"state":"Merged","mergedAt":"2022-06-29T17:46:37Z","number":2102,"body":"Rename our GitHub Apps to drop the trailing \"App\" part of the name. Testing in DEV first.\r\n\r\nContext\r\nhttps://chapter2global.slack.com/archives/C02GEN8LFGT/p1656516800603349\r\n\r\n\r\n![image](https://user-images.githubusercontent.com/1798345/176483516-0f944166-d84d-4fb5-942b-95e8a2227ae8.png)\r\n\r\n","mergeCommitSha":"b728bd56d37450c6cc6c680f7780107cd76f7dbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2102","title":"Rename GitHub App in DEV","createdAt":"2022-06-29T15:53:40Z"}
{"state":"Merged","mergedAt":"2022-06-29T16:56:15Z","number":2103,"mergeCommitSha":"880a694dc8b255fa6b1fe980b5fcf1bb6b1372a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2103","title":"Always return empty for deprecated /teamMembers channel","createdAt":"2022-06-29T16:31:05Z"}
{"state":"Merged","mergedAt":"2022-06-29T17:19:48Z","number":2104,"body":"Client changes that allow running local dashboard against dev.\r\n\r\n`npm run start:dev` runs the dashboard against dev\r\n`npm run start:local` runs the dashboard against local stack","mergeCommitSha":"8d5d7b358f84d6fd7ed421202f554922debcd7a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2104","title":"Allow running local dashboard against dev","createdAt":"2022-06-29T16:56:49Z"}
{"state":"Merged","mergedAt":"2022-06-29T18:28:37Z","number":2105,"body":"Rename our GitHub Apps to drop the trailing \"App\" part of the name.\n\nContext\nhttps://chapter2global.slack.com/archives/C02GEN8LFGT/p1656516800603349\n\n- Note that renaming the PROD app from \"Unblocked App\" to \"Unblocked\"\n  would change the app to `https://github.com/apps/unblocked`.\n- The problem is that the `unblocked` token is taken.\n- Here's the hack: we introduce a zero width character in the display\n  name, so that the display name \"Un▊blocked\" appears as \"Unblocked\",\n  which forces the token to be `un-blocked` which is available.","mergeCommitSha":"dc6e6264f89a6f197a7466efeee65d4f2412634e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2105","title":"Rename GitHub App in PROD","createdAt":"2022-06-29T17:10:29Z"}
{"state":"Merged","mergedAt":"2022-06-29T20:36:38Z","number":2106,"body":"Thread creation is web extension was failing depending on the current url.\r\n\r\nCurrentURL: https://github.com/NextChapterSoftware/unblocked/blame/main/vscode/webpack.dev.js#L9\r\n```\r\nhref=\"/NextChapterSoftware/unblocked/raw/main/vscode/webpack.dev.js\"\r\n```\r\n\r\nCurrent URL: https://github.com/NextChapterSoftware/unblocked/blob/9bcca7731aa854fc5bc6c5adc9897078e5cbe02e/vscode/webpack.dev.js\r\n```\r\nhref=\"/NextChapterSoftware/unblocked/raw/9bcca7731aa854fc5bc6c5adc9897078e5cbe02e/vscode/webpack.dev.js\"\r\n```\r\n\r\nTherefore we prefer fetching the permalink when it exists.\r\nWe cannot *only* depend on the permalink though since it doesn't exist when the Curren URL references a commit...\r\n\r\n```\r\ndata-permalink-href=\"/NextChapterSoftware/unblocked/raw/b728bd56d37450c6cc6c680f7780107cd76f7dbe/vscode/webpack.dev.js\"\r\n```","mergeCommitSha":"68ede94c9bd4c00f65a48ad5ffc257a01c2e1408","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2106","title":"Use Permalink as it contains commit hash instead of branch name","createdAt":"2022-06-29T17:54:09Z"}
{"state":"Merged","mergedAt":"2022-06-29T18:19:35Z","number":2107,"mergeCommitSha":"1bb0db9735d0de7830a720299c0e41e8cecbd100","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2107","title":"Store needs to be localized to editor","createdAt":"2022-06-29T18:01:03Z"}
{"state":"Merged","mergedAt":"2022-06-29T20:52:31Z","number":2108,"body":"Port creation did not work as I had expected.\r\nPort.sender.url is the URL of the page when the port was created, not when the newest/current message is sent.\r\n\r\nThis caused issues when navigating between repositories.\r\n\r\nAdded some cleanup to reduce churn.","mergeCommitSha":"80c831dcc540f5a9285f9249b8ff0ee0377084ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2108","title":"Fix issue with subsequent navigation","createdAt":"2022-06-29T18:20:59Z"}
{"state":"Merged","mergedAt":"2022-06-29T18:30:32Z","number":2109,"mergeCommitSha":"caee00361a7fa7aa6f5fbbfc97f7e1febfc2cb55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2109","title":"update email templtes","createdAt":"2022-06-29T18:29:03Z"}
{"state":"Merged","mergedAt":"2022-02-01T22:30:25Z","number":211,"body":"Update chromatic token for shared library","mergeCommitSha":"70d6f5e499327c295e037a900813a073c9509aae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/211","title":"Update chromatic token","createdAt":"2022-02-01T22:04:37Z"}
{"state":"Merged","mergedAt":"2022-06-29T22:35:09Z","number":2110,"body":"Fixes UNB-364\r\n\r\nFixes two issues:\r\n* If you go to the dashboard root, we left an extra history entry when redirecting to /mine\r\n* Whenever you viewed a thread, we would add extra entries in the history as we modified the search parameters\r\n","mergeCommitSha":"d87334f37b3af81e015f5ed79b6830c26e0c5047","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2110","title":"Fix navigation/search operations that left extra elements in the history","createdAt":"2022-06-29T18:42:42Z"}
{"state":"Merged","mergedAt":"2022-07-04T17:24:38Z","number":2111,"body":"* Separates out the logic in the Dropdown component so that the Popover is a separate component re: [UB discussion](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/09444ab3-5bd0-4255-9e11-1c2c15139639) (changes to the Dropdown component in terms of UI output should be a no-op)\r\n* Refactored some of the styling so that they are shared mixins between the Dropdown and Popover components\r\n* Fix some layout bugs","mergeCommitSha":"dd51762c6a959492f4f8b6061e9d2ea69c1aaf67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2111","title":"Add Popover component","createdAt":"2022-06-29T19:42:59Z"}
{"state":"Merged","mergedAt":"2022-06-29T21:43:03Z","number":2112,"body":"The current logic error always shows a notification if a message wasn't previous marked as unread. This is not really what we want. \r\n\r\nThis PR exploits a particular behaviour of `Set.insert()`, which is that `oldMember` will actually be the `newMember` if it was able to insert the item. If the same item already exists, `insert()` will return the previous item. The `lastMessageCreatedAt` field is not counted towards item uniqueness.\r\n\r\nThis means that we only need to check that `newItem.lastMessageCreatedAt > oldItem.lastMessageCreatedAt` because if it is successfully inserted, then these values will be the same (this is the marked unread case)","mergeCommitSha":"c2df7397c9a7c29cc76325f17d320039036a75d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2112","title":"Suppress notifications when marked unread","createdAt":"2022-06-29T20:45:47Z"}
{"state":"Merged","mergedAt":"2022-06-29T21:33:59Z","number":2113,"body":"There's a launch test now enabled in CI. This PR comments out the UI tests because they're doing nothing and will waste build minutes","mergeCommitSha":"49ef2ecc634535b2a842343516bce276d3da932d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2113","title":"Enable launch testing","createdAt":"2022-06-29T21:10:42Z"}
{"state":"Merged","mergedAt":"2022-06-29T21:41:40Z","number":2114,"body":"Github changed the selector ID...\r\n\r\nThis is used to get the latest commit hash for source mark rendering & discussion creation.","mergeCommitSha":"7ab4b3f2fd2549fded0c37f3c1485466aa166a99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2114","title":"New Selector","createdAt":"2022-06-29T21:11:43Z"}
{"state":"Merged","mergedAt":"2022-06-29T21:36:47Z","number":2115,"mergeCommitSha":"7a257150a8e5c7fb66f451e62619f1188760a960","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2115","title":"AddEmailConfigurationSets","createdAt":"2022-06-29T21:23:03Z"}
{"state":"Merged","mergedAt":"2022-06-29T22:11:27Z","number":2116,"body":"Reverts NextChapterSoftware/unblocked#2112\r\n\r\n","mergeCommitSha":"c65f8f1d8019366e5c61e50f9310cf24bbf5897a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2116","title":"Revert \"Suppress notifications when marked unread\"","createdAt":"2022-06-29T22:11:20Z"}
{"state":"Merged","mergedAt":"2022-06-29T23:14:10Z","number":2117,"mergeCommitSha":"a24a19e1b0ac779dda2d2b2cf3a086886bea7fc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2117","title":"Try tagging more details","createdAt":"2022-06-29T22:32:28Z"}
{"state":"Merged","mergedAt":"2022-06-29T23:39:08Z","number":2118,"body":"encodeURI !== encodeURIComponent.\r\n\r\nWas causing some issues with installation url.\r\n\r\nAlso removed unnecessary encoding/decoding in web.\r\n","mergeCommitSha":"6cc305a0fc2595281c68150f611bc7ad31c260ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2118","title":"EncodeUriComponent instead of encodeUri","createdAt":"2022-06-29T23:11:57Z"}
{"state":"Merged","mergedAt":"2022-06-29T23:37:52Z","number":2119,"body":"Slate's placeholder logic is pretty simple, it renders a placeholder if the document does *not* have any leaf nodes with text in them.  This doesn't work for our document, because we can have lists that do not have text, but count as content.  This leads to goofy things like this:\r\n<img width=\"1286\" alt=\"Screen Shot 2022-06-29 at 4 12 27 PM\" src=\"https://user-images.githubusercontent.com/2133518/176562289-716d38a3-1587-46aa-9a36-5eaff560e8b3.png\">\r\n\r\nThis adds custom placeholder creation and rendering logic, stolen from the Slate source code.\r\n","mergeCommitSha":"1ef3da38ba2e1663a4b06bdcfb9c62d639f2f6b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2119","title":"Don't render editor placeholder when we have an empty list","createdAt":"2022-06-29T23:26:20Z"}
{"state":"Merged","mergedAt":"2022-02-01T23:15:23Z","number":212,"mergeCommitSha":"78f613765662eee81b9b8ab47f848dfaa32c700d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/212","title":"Update dependencies","createdAt":"2022-02-01T23:09:11Z"}
{"state":"Merged","mergedAt":"2022-06-29T23:37:20Z","number":2120,"mergeCommitSha":"5b30c00439d6f0421f4185e727a592667d73c62f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2120","title":"update","createdAt":"2022-06-29T23:37:13Z"}
{"state":"Merged","mergedAt":"2022-06-29T23:58:52Z","number":2121,"mergeCommitSha":"d23008707e95e499bb084b46de2529698b143422","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2121","title":"update","createdAt":"2022-06-29T23:39:17Z"}
{"state":"Merged","mergedAt":"2022-06-30T00:23:24Z","number":2122,"body":"<img width=\"534\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176566169-5fefeb55-4995-4c06-a178-14a2afda031c.png\">\r\n<img width=\"537\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176566206-767d6de9-151f-430b-8b22-a16c4b87ec3f.png\">\r\n<img width=\"539\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/176566223-e5f56f81-6f9c-4530-a00e-3fc49ef74e61.png\">\r\n![CleanShot 2022-06-29 at 17 11 42](https://user-images.githubusercontent.com/********/176566626-cabbbb1e-afcb-456a-9101-78149ce989c3.gif)\r\n\r\n","mergeCommitSha":"101979f8ab48c7d7b4bad43272b9233ab5371cd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2122","title":"Update adding email flow per designs","createdAt":"2022-06-30T00:12:37Z"}
{"state":"Merged","mergedAt":"2022-06-30T00:37:30Z","number":2123,"body":"Disable the checkbox if the input is invalid, per Ben's request","mergeCommitSha":"a26e3e0c5dfc6ae1cc09aa2958e741e9a5ad5a80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2123","title":"Fix checkbox state","createdAt":"2022-06-30T00:26:47Z"}
{"state":"Merged","mergedAt":"2022-06-30T06:24:35Z","number":2124,"body":"https://linear.app/unblocked/issue/UNB-371/sourcemark-file-hash-wrong-after-pairwise-compare-of-whitespace-only","mergeCommitSha":"ec736106c2328b5f7df75ed3293af68ba6b82fb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2124","title":"SourceMark file hash wrong after pairwise compare of whitespace only changes","createdAt":"2022-06-30T06:01:08Z"}
{"state":"Merged","mergedAt":"2022-06-30T08:06:20Z","number":2125,"mergeCommitSha":"5170a2bdd74feafadedf65c36fd454406325213c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2125","title":"Remove no longer needed code","createdAt":"2022-06-30T07:48:20Z"}
{"state":"Merged","mergedAt":"2022-07-03T01:02:44Z","number":2126,"body":"Addresses bugs where the engine failed to recognize a file rename\nor file move unless the content of the file had also been modified.","mergeCommitSha":"3ca8461200d3c84dc0befbda94a4bc4c5e990775","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2126","title":"SourceMark file rename handling works when there is no file content change","createdAt":"2022-07-01T06:02:53Z"}
{"state":"Merged","mergedAt":"2022-07-03T01:02:32Z","number":2127,"mergeCommitSha":"64891323a9715e8228e8c11c3f1f9bf00ba20c74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2127","title":"Show alert when team is not SCM connected","createdAt":"2022-07-01T06:03:12Z"}
{"state":"Merged","mergedAt":"2022-07-04T04:26:47Z","number":2128,"body":"This is the sole cause of all these exceptions:\n```\nDiff content is not based off the old point\n```\n\nhttps://linear.app/unblocked/issue/UNB-114/vscode-exception-diff-content-is-not-based-off-the-old-point","mergeCommitSha":"15ca5ec2a27ae45b810ceca8ec844771b34e7543","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2128","title":"Validate file content sha of original ingested source points","createdAt":"2022-07-04T04:09:50Z"}
{"state":"Merged","mergedAt":"2022-07-04T18:16:21Z","number":2129,"body":"https://linear.app/unblocked/issue/UNB-374/concurrent-updates-of-email-templates-causing-problems\r\n\r\nCreated a parallel test that validated there was a bug that needed to be fixed.\r\nThe cleanup should be within the redis lock.","mergeCommitSha":"e1c5c5acc8460b420f0b261899d728c648396c06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2129","title":"Ensure that email templates cleanup is in-band with redis lock","createdAt":"2022-07-04T17:36:08Z"}
{"state":"Merged","mergedAt":"2022-02-01T23:42:46Z","number":213,"mergeCommitSha":"0922bbf12f7d83f6a409b8e58b383b477c775d04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/213","title":"Use import aliases","createdAt":"2022-02-01T23:37:43Z"}
{"state":"Merged","mergedAt":"2022-07-04T18:28:26Z","number":2130,"body":"Add flag to only build Mac app, not safari.","mergeCommitSha":"3ebd76f38319993a8299ca732fc0c90372c8e1e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2130","title":"Only build mac for web extension","createdAt":"2022-07-04T18:09:16Z"}
{"state":"Merged","mergedAt":"2022-07-04T19:32:06Z","number":2131,"body":"Adds a separate 'watch' debug target for VSCode, against dev/prod envs.  When you run this, it will run webpack in watch mode, and will do quick rebuilds whenever you save files.  You can hit Cmd+R in VSCode to reload and pull in changes.\r\n\r\nThis should be faster then the previous debug targets, which required full rebuilds.  We had tried enabling webpack caching but it causes a lot of bugs.\r\n\r\nThe one downside to this is that you need to manually shut down the watch session in the 'Terminal' UI, if you want to re-build from scratch (ie if you change the webpack config).","mergeCommitSha":"90c5cead692f24f19ec298a2dee6684dabde8e8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2131","title":"Add watch debug target for VSCode","createdAt":"2022-07-04T18:42:02Z"}
{"state":"Merged","mergedAt":"2022-07-04T20:46:48Z","number":2132,"body":"This change collects the parents commit(s) in addition to the commit\nfor each node in the commit history.\n\n- For a repo with linear history there will always be just one parent.\n  This has been our current assumption.\n- For repos with merges, there can be between 1 and 3 parents per node.\n\nThis change has no behaviour impact. The parent information will be\nused for graph traversal in a follow up change.","mergeCommitSha":"6502bebfdcfa6454cf3781006a4d80ae5cf5cb12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2132","title":"Sourcemark engine collects commit graph information","createdAt":"2022-07-04T19:01:21Z"}
{"state":"Merged","mergedAt":"2022-07-05T17:32:14Z","number":2133,"body":"https://linear.app/unblocked/issue/UNB-309/getsourcemarklatestpoint-returns-commits-not-on-local-branch\r\n\r\nDo not merge until these are fixed:\r\n- https://linear.app/unblocked/issue/UNB-377/pr-ingested-source-point-file-hashes-are-inconsistent\r\n- https://linear.app/unblocked/issue/UNB-341/sourcemark-engine-must-handle-merge-commits","mergeCommitSha":"a7f09a48ca15f55f48921443bf9cbf85d46333c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2133","title":"Fix getSourceMarkLatestPoint returns commits not on local branch","createdAt":"2022-07-04T20:51:03Z"}
{"state":"Merged","mergedAt":"2022-07-04T23:35:10Z","number":2134,"body":"Add loading state after triggering org installation.\r\n\r\n![CleanShot 2022-07-04 at 13 58 15](https://user-images.githubusercontent.com/1553313/177215960-0d7b3415-d1e1-4479-b9d9-931a5efdc97b.gif)\r\n","mergeCommitSha":"42ad50a2748c3c3f870c19b18782273c34dff50d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2134","title":"Add Spinner to org installation","createdAt":"2022-07-04T20:59:23Z"}
{"state":"Merged","mergedAt":"2022-07-04T21:27:47Z","number":2135,"body":"* Update design for @-mentioned contributor row \r\n<img width=\"532\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/177216027-47001a16-bc6f-44c3-947d-06b1ea66b2b5.png\">\r\n\r\n* The mentioned row is not toggleable\r\n* once the mention is deleted, it becomes a regular row\r\n![CleanShot 2022-07-04 at 13 58 48](https://user-images.githubusercontent.com/********/177215999-543f37e5-9682-4e54-a4cc-318ec4102103.gif)\r\n\r\n* Some general refactoring\r\n","mergeCommitSha":"6591a119ec87b8fc4cfcc7fa828dabeba3e59248","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2135","title":"Polish add email flow ","createdAt":"2022-07-04T21:00:14Z"}
{"state":"Merged","mergedAt":"2022-07-04T21:15:14Z","number":2136,"mergeCommitSha":"f0e111c784cc030a0a7c117eb561136ec5a005f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2136","title":"UpdateLogging","createdAt":"2022-07-04T21:15:08Z"}
{"state":"Merged","mergedAt":"2022-07-04T21:17:46Z","number":2137,"mergeCommitSha":"459e559e468b84c119fee999e114b45e746fb86f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2137","title":"update","createdAt":"2022-07-04T21:17:34Z"}
{"state":"Merged","mergedAt":"2022-07-04T22:47:32Z","number":2138,"mergeCommitSha":"02caa6f98095c9a8dac457fe158a7ea17db878e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2138","title":"fix the conditon so we only run smoke tests once","createdAt":"2022-07-04T22:21:17Z"}
{"state":"Merged","mergedAt":"2022-07-04T23:05:47Z","number":2139,"body":"![CleanShot 2022-07-04 at 15 28 57](https://user-images.githubusercontent.com/********/177222103-7ac15f35-c5e5-4e54-8791-9ea2f340c01c.gif)\r\n\r\n* Temporary patch for first issue outlined [here](https://chapter2global.slack.com/archives/C02HEVCCJA3/p1656961996626159?thread_ts=1656960677.774089&cid=C02HEVCCJA3)\r\n* The idea is that for now we should always expect users to have threads, so show a loading state when the getThreads call returns an empty value and wait for the threads to populate naturally. If it loads for too long without resolving any threads (>1min?), have some way for the user to contact us (note: in the gif, the timeout is shortened to 3000ms for brevity)\r\n* The true solution is to add a flag to either the getThreads response or Repo model to signify whether the service is still ingesting PRs or not, then the client can read this flag and know to show an intermittent loading state or a true empty threads state. This will require a bit more involvement/refactoring and is less pressing of an issue for the immediate future since we've already ingested the PRs for the teams we plan to onboard in the immediate future (note: this fix should land sometime this week) ","mergeCommitSha":"c5b6cc5a3663811a635b07e301000332346732a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2139","title":"Update no threads state view","createdAt":"2022-07-04T22:36:31Z"}
{"state":"Merged","mergedAt":"2022-02-02T00:59:27Z","number":214,"body":"minor re-structure to make it more obvious which model type you're importing\r\n```\r\nimport com.nextchaptersoftware.db.models.Thread\r\nimport com.nextchaptersoftware.db.models.ThreadModel\r\nimport com.nextchaptersoftware.api.models.CreateThreadRequest\r\nimport com.nextchaptersoftware.api.models.UpdateThreadRequest\r\n```","mergeCommitSha":"5301affead49b563ffdba7a008de25966620752a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/214","title":"Move API models under api package","createdAt":"2022-02-02T00:22:31Z"}
{"state":"Merged","mergedAt":"2022-07-05T06:58:01Z","number":2140,"mergeCommitSha":"bb1b402bd835345f3f58b1ca5218020c2557d668","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2140","title":"change trigger events and add checkout step","createdAt":"2022-07-04T23:02:09Z"}
{"state":"Merged","mergedAt":"2022-07-04T23:38:55Z","number":2141,"body":"before:\r\n<img width=\"650\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/177224161-918ab4cf-fe73-4327-9921-f494e57d95b0.png\">\r\n\r\nafter:\r\n<img width=\"646\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/177224211-8af3eba4-89a1-424d-8525-188a406ed414.png\">\r\n\r\n","mergeCommitSha":"f4060dfe35ef025dc893e92430eb99fe3d19c13d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2141","title":"Fix rendering of the mention dropdown in the web extension","createdAt":"2022-07-04T23:09:04Z"}
{"state":"Merged","mergedAt":"2022-07-04T23:32:21Z","number":2142,"mergeCommitSha":"93561dd063aff80416a09917810ba05e4f28b6ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2142","title":"Do not use invalid team memberships for sending emails","createdAt":"2022-07-04T23:16:48Z"}
{"state":"Merged","mergedAt":"2022-07-05T17:32:04Z","number":2143,"body":"Added ability to manually add users in web extension\r\n\r\nhttps://user-images.githubusercontent.com/1553313/177225198-5720774c-df54-4bea-b638-91fc39a1ef2b.mp4\r\n\r\n\r\n","mergeCommitSha":"d0cc5f3bc63173010f79f2dd5177969c5d528f9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2143","title":"Manually add user in web extension","createdAt":"2022-07-04T23:29:43Z"}
{"state":"Merged","mergedAt":"2022-07-05T00:35:29Z","number":2144,"body":"2 problems when editing a message:\r\n1. Rendering images\r\n2. Uploading images\r\n\r\n![CleanShot 2022-07-04 at 17 14 58](https://user-images.githubusercontent.com/3806658/177227540-da338370-0b72-476f-9610-0c801a8e4690.gif)\r\n","mergeCommitSha":"8780e8d195beff899b98892b7b8ba0343cc7f29a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2144","title":"Fix how images are uploaded for message edits","createdAt":"2022-07-05T00:13:42Z"}
{"state":"Merged","mergedAt":"2022-07-05T00:46:19Z","number":2145,"body":"Updating findMembershipsForPerson such that it only shows memberships that are not for deleted teams.","mergeCommitSha":"b32b616b19e4deb6a1a56795b565e8bf25e01c08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2145","title":"Richie is right again","createdAt":"2022-07-05T00:32:58Z"}
{"state":"Merged","mergedAt":"2022-07-05T16:04:09Z","number":2146,"body":"I'm playing with blending modes with the footer. There are two modes: \"within window\" and \"behind window\". \"Within Window\" allows content in the hub to blur underneath. \"Behind Window\" just makes the footer a part of the window itself, and so the content just disappears. The effect is subtle and can't be seen with a still image. I personally prefer \"Behind Window\" because the footer pops out too much using \"Within Window\". The footer should be a part of the background\r\n\r\n### Within Window\r\n\r\n<img width=\"583\" alt=\"CleanShot 2022-07-04 at 19 09 20@2x\" src=\"https://user-images.githubusercontent.com/858772/177235670-7532b467-09f4-494f-b482-79ea18320e0a.png\">\r\n\r\n\r\n<img width=\"590\" alt=\"CleanShot 2022-07-04 at 19 10 40@2x\" src=\"https://user-images.githubusercontent.com/858772/177235809-753ad898-7692-49b6-8be1-afb4e5f43295.png\">\r\n\r\n### Behind Window\r\n\r\n<img width=\"589\" alt=\"CleanShot 2022-07-04 at 19 18 32@2x\" src=\"https://user-images.githubusercontent.com/858772/177236798-9df24cbf-3947-410d-ae52-5f755f4c39bc.png\">\r\n\r\n<img width=\"573\" alt=\"CleanShot 2022-07-04 at 19 18 15@2x\" src=\"https://user-images.githubusercontent.com/858772/177236807-ec4f9b5a-1070-4015-8512-5fe07529a116.png\">\r\n\r\n","mergeCommitSha":"ce92fdd0c4cbf2f82b2e5c41a31f2cab5a47100c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2146","title":"Drop the tabs","createdAt":"2022-07-05T02:09:04Z"}
{"state":"Merged","mergedAt":"2022-07-05T03:06:16Z","number":2147,"mergeCommitSha":"12c1ef6039cd3ead75d1ebc5bbb39dff363e6937","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2147","title":"Fix url","createdAt":"2022-07-05T03:05:28Z"}
{"state":"Merged","mergedAt":"2022-07-05T04:55:21Z","number":2148,"mergeCommitSha":"ddd93ee900421753cb4fb75e0511f6dbc12b2aa7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2148","title":"Fix welcome emails","createdAt":"2022-07-05T04:55:14Z"}
{"state":"Merged","mergedAt":"2022-07-05T05:28:29Z","number":2149,"mergeCommitSha":"57194a5cb59d1055b018e4dea2272ca5b691aa87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2149","title":"AddDashboardUrl","createdAt":"2022-07-05T05:25:36Z"}
{"state":"Merged","mergedAt":"2022-02-03T06:21:32Z","number":215,"body":"This will also create the `SourceMark`s in `CreateMessageRequest.sourcemarks`. Next PR will fix the Message DB model to replace `body` with `blocks`","mergeCommitSha":"76f3c7811eecbda564115c17960b37132986aace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/215","title":"Implement createMessage and updateMessage operations","createdAt":"2022-02-02T00:25:13Z"}
{"state":"Merged","mergedAt":"2022-07-05T19:06:08Z","number":2150,"body":"https://linear.app/unblocked/issue/UNB-341/sourcemark-engine-must-handle-merge-commits","mergeCommitSha":"3404e1d1f638a360f8df4e801a9bd3a25683f926","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2150","title":"Replace sourcemark linear propagation with graph propagation","createdAt":"2022-07-05T05:29:28Z"}
{"state":"Merged","mergedAt":"2022-07-05T17:12:02Z","number":2151,"body":"Part of the fix needed for this:\nhttps://linear.app/unblocked/issue/UNB-377/pr-ingested-source-point-file-hashes-are-inconsistent","mergeCommitSha":"489af9ae5795a0d3982872d3970aca3ffd0e4005","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2151","title":"Filter out corrupt/invalid original points","createdAt":"2022-07-05T07:00:08Z"}
{"state":"Merged","mergedAt":"2022-07-05T14:03:47Z","number":2152,"body":"I am stuck in CI hell! ","mergeCommitSha":"032e7953fedc127a3987f065faf27a2e098f918e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2152","title":"fix a typo in the action path","createdAt":"2022-07-05T07:31:13Z"}
{"state":"Merged","mergedAt":"2022-07-05T15:45:31Z","number":2153,"mergeCommitSha":"81b01d86205fb2738f4e75a7b8a56ad31399765a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2153","title":"ses stack","createdAt":"2022-07-05T15:04:17Z"}
{"state":"Merged","mergedAt":"2022-07-05T17:39:49Z","number":2154,"body":"It's all build config and package changes. I could add some code in the installer scripts to check for and remove `UnblockedHub.app` if we think that's needed","mergeCommitSha":"b068d854c1d3cb156aac97e9501604d3d6aa35a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2154","title":"Change app name from UnblockedHub.app to Unblocked.app","createdAt":"2022-07-05T17:03:13Z"}
{"state":"Merged","mergedAt":"2022-07-05T17:38:13Z","number":2155,"body":"- Moved the smoke test to Dev deploy step under a conditional. This should eliminate all those new extra jobs we see in GitHub actions \r\n- Fixed the conditional to run smoke tests only on API service deploys ","mergeCommitSha":"1a4071c6886364b791b6a96b6037aba85d247dbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2155","title":"fix smoke tests to avoid multiple runs","createdAt":"2022-07-05T17:05:54Z"}
{"state":"Merged","mergedAt":"2022-07-05T18:19:57Z","number":2156,"body":"When ingesting a thread, we should create a source point with a commit ID that is consistent with the file.","mergeCommitSha":"a81b2ac6b034fd71d316f70761574617ec57ac11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2156","title":"Create commit sourcepoint in addition to merge sourcepoint","createdAt":"2022-07-05T17:33:03Z"}
{"state":"Merged","mergedAt":"2022-07-05T18:04:59Z","number":2157,"body":"Dev API calls are slower than prod due to resource limitations. This sets a higher threshold for smoke tests. ","mergeCommitSha":"bbd40db35b0eb3308d899894fd41b35d51482ab1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2157","title":"make smoke tests less sensitive","createdAt":"2022-07-05T17:59:56Z"}
{"state":"Merged","mergedAt":"2022-07-05T18:12:12Z","number":2158,"mergeCommitSha":"16da15f776ae18a4385774062a3eaf4f39fa91d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2158","title":"reduce ses dimensions for cost savings","createdAt":"2022-07-05T18:12:01Z"}
{"state":"Merged","mergedAt":"2022-07-05T18:40:33Z","number":2159,"body":"Related to this:\nhttps://linear.app/unblocked/issue/UNB-388/do-not-generate-unread-messages-for-team-members-who-are-not-in","mergeCommitSha":"827eecdb775fd625396e24304b281e141e841884","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2159","title":"Admin web shows thread unreads for each team member","createdAt":"2022-07-05T18:27:43Z"}
{"state":"Merged","mergedAt":"2022-02-02T01:14:24Z","number":216,"body":"- Just API stubs; implementation in next PR.\r\n- Addresses https://github.com/NextChapterSoftware/unblocked/pull/204#pullrequestreview-869666679","mergeCommitSha":"cf2bd85c224f797e157b99194ef68e8d2ad71248","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/216","title":"Add Teams endpoint","createdAt":"2022-02-02T01:08:56Z"}
{"state":"Merged","mergedAt":"2022-07-05T20:47:36Z","number":2160,"body":"Basic implementation of a Promise-forwarding proxy and a Fetch proxy.  This isn't hooked up to anything right now, I'm getting this in so we can test if the mechanism works in the web extension or not.  If it does, this will get cleaned up with tests, error handling, etc.","mergeCommitSha":"0efbcd7748f1898a151f5097e39d1aac1c57d42e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2160","title":"Add PromiseProxy and FetchProxy","createdAt":"2022-07-05T18:40:18Z"}
{"state":"Merged","mergedAt":"2022-07-05T19:13:33Z","number":2161,"body":"Listener was refreshing unexpectedly.\r\nThis caused the port listener to be removed/added.\r\nThe initial listener reference that should have received the \"loaded\" request was removed which led to missing data.\r\n\r\nTemporary fix is to fix the refresh issue but this shows some fragility in the content port architecture. We will be revisiting this by moving majority of state/logic into content scripts.","mergeCommitSha":"459dd580f1881616e588bad2519905937defa8d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2161","title":"Fix content port issue","createdAt":"2022-07-05T18:54:13Z"}
{"state":"Merged","mergedAt":"2022-07-05T19:18:47Z","number":2162,"mergeCommitSha":"4af315291b9b08b0c3954ef40f49d86cccc6bcfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2162","title":"cleanup kinesis","createdAt":"2022-07-05T19:18:40Z"}
{"state":"Merged","mergedAt":"2022-07-05T21:15:34Z","number":2163,"body":"Feature parity w vscode: \r\n\r\n<img width=\"674\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/177402398-d42acfad-d007-4fb5-a7a2-eae076124777.png\">\r\n\r\n![CleanShot 2022-07-05 at 12 37 06](https://user-images.githubusercontent.com/********/177402731-d18dadea-683f-48c0-9d07-9885834a1e7a.gif)\r\n\r\n","mergeCommitSha":"4b110f04d7d3620d0641a30ca2ca171ec0c5d488","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2163","title":"[Web Extension] Fix mentions in create discussion","createdAt":"2022-07-05T19:38:08Z"}
{"state":"Merged","mergedAt":"2022-07-05T21:04:41Z","number":2164,"body":"Dennis mentioned this. Will try to get sortable tables working in a future change.","mergeCommitSha":"245a21f46f6e9da002de9b96c8cffada60dd51bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2164","title":"Admin: Sort People table by most recently created","createdAt":"2022-07-05T20:27:14Z"}
{"state":"Merged","mergedAt":"2022-07-05T21:27:49Z","number":2165,"body":"Part 2 of https://github.com/NextChapterSoftware/unblocked/pull/2156\r\n\r\nThis will let us trigger re-ingestion and create any missing sourcepoints.","mergeCommitSha":"651aca67ff01dedecbdfe1c7ba36463c9772f567","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2165","title":"Create missing sourcepoints when syncing a PR thread","createdAt":"2022-07-05T20:29:18Z"}
{"state":"Merged","mergedAt":"2022-07-05T20:59:15Z","number":2166,"body":"https://linear.app/unblocked/issue/UNB-391/clear-unread-threads-in-admin-web","mergeCommitSha":"7de647c20ed3bcbd9dcc7780e54ce4b0e550c2ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2166","title":"Clear unread threads in admin web","createdAt":"2022-07-05T20:46:36Z"}
{"state":"Merged","mergedAt":"2022-07-05T21:28:43Z","number":2167,"mergeCommitSha":"c34361b778b80011f34d82a779ab15015e97f756","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2167","title":"Adds section headers","createdAt":"2022-07-05T21:18:06Z"}
{"state":"Merged","mergedAt":"2022-07-05T21:37:41Z","number":2168,"body":"<img width=\"598\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/177419185-7cf412ea-2fdb-409f-9f33-b1baa0ebdc30.png\">\r\n","mergeCommitSha":"f87aecd07d33c7f765d18fde755daeaa98b9c7a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2168","title":"Fix line height alignment","createdAt":"2022-07-05T21:25:45Z"}
{"state":"Merged","mergedAt":"2022-07-06T00:20:42Z","number":2169,"mergeCommitSha":"00a9d51a406bd4f6f7b681a47cf33cee82775615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2169","title":"Make builds go a minute faster. ","createdAt":"2022-07-05T21:51:36Z"}
{"state":"Merged","mergedAt":"2022-02-02T07:51:48Z","number":217,"body":"Dev/Prod secrets are loaded via pod specific environment variables encrypted on kubernetes.\r\nThey are uploaded using `make setup-k8s-env` per environment (dev/prod).\r\nIf you want to do this yourself, I would highly recommend the documentation that Mahdi has written up.\r\nhttps://www.notion.so/nextchaptersoftware/AWS-IAM-User-Access-Setup-9df153f43d6b43228d7973df5aa26d40\r\n\r\nTo load them, we us the wonderful lightbend optional system/env variables:         \r\n```\r\nclientSecret: ${?GITHUB_CLIENT_SECRET}\r\nclientKey: ${?GITHUB_CLIENT_KEY}\r\n```\r\n\r\nThe process of loading:\r\n1. Attempt to load from secrets.conf located ~/.secrets/unblocked/secrets.conf\r\n2. Attempt to load from environment specific conf.\r\n3. Attempt to load from global.conf\r\n\r\nValidated this works as expected...","mergeCommitSha":"ec0fa9963fecc1ec10a41b0e10099a81ff5d398e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/217","title":"Ensure github secrets are deployed to dev/prod","createdAt":"2022-02-02T05:04:01Z"}
{"state":"Merged","mergedAt":"2022-07-05T23:25:03Z","number":2170,"body":"* Fix the loader to 100vh/100vw\r\n* Flag off onboarding flag when users hit the last step pf onboarding","mergeCommitSha":"0ab36bce4821a7fc5d12a498cf65857db1258a8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2170","title":"Fix loading window for tutorial wizard","createdAt":"2022-07-05T23:12:31Z"}
{"state":"Merged","mergedAt":"2022-07-06T00:43:22Z","number":2171,"body":"2 changes:\r\n- Try to fall back to opening the hub as the installer user if the local user can't be resolved. It really shouldn't matter since the installer user has higher privileges\r\n- Try this 3 times with a sleep pause of 1 second between each try. The thinking here is that the post-install script is somehow running before the system is ready to launch the app","mergeCommitSha":"0aea16e04cd841e1d2ef6c3f71a260a7c3c301a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2171","title":"Add resiliency to post-install app start script","createdAt":"2022-07-05T23:22:20Z"}
{"state":"Merged","mergedAt":"2022-07-05T23:52:11Z","number":2172,"body":"Like for submodule changes: https://github.com/NextChapterSoftware/unblocked/pull/812/files\r\n\r\nhttps://app.logz.io/#/goto/9da973b62d008a12d4941c08195cfdac?switchToAccountId=411850\r\n\r\nSee: video-app/macOS/agora","mergeCommitSha":"bead2c6274759e113571f6b936cbe54fd440f8c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2172","title":"blob_url can be null","createdAt":"2022-07-05T23:39:11Z"}
{"state":"Merged","mergedAt":"2022-07-06T03:30:04Z","number":2173,"body":"Removed automatic redirect to dashboard.\r\n\r\nUpdated styling of base.\r\n\r\nTODO: Refactor out \"Landing\" page into base component or mixin\r\n\r\n<img width=\"1312\" alt=\"CleanShot 2022-07-05 at 16 41 08@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-503ad3f2-5a26-45e3-ad58-d53599da9a57.png\">\r\n","mergeCommitSha":"865691a17b2fe8bad437f6c8c7d17c6922717667","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2173","title":"Update Redirect Styling","createdAt":"2022-07-05T23:47:36Z"}
{"state":"Merged","mergedAt":"2022-07-06T00:15:09Z","number":2174,"body":"Couldn't do what I was doing here, in particular introducing @mentions before whitespaces.\r\n\r\n![CleanShot 2022-07-05 at 17 03 05](https://user-images.githubusercontent.com/3806658/177435998-db4c7707-3c9a-4c22-8807-a701a1d48d5b.gif)\r\n","mergeCommitSha":"c57d13e1962917ceb54e2cc79dcef408b1c16c0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2174","title":"mentionOnChange should handle whitespaces better","createdAt":"2022-07-06T00:02:49Z"}
{"state":"Merged","mergedAt":"2022-07-06T01:00:38Z","number":2175,"body":"https://linear.app/unblocked/issue/UNB-395/fatal-invalid-filter-spec-objecttype=blob","mergeCommitSha":"a889d2236c1924a262263cfbef3525b5a5d4c155","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2175","title":"Fix for Git file content hash lookup","createdAt":"2022-07-06T00:05:34Z"}
{"state":"Merged","mergedAt":"2022-07-06T03:39:30Z","number":2176,"body":"Hook into install callback to set initial pinned state.\r\n![CleanShot 2022-07-05 at 17 22 30](https://user-images.githubusercontent.com/1553313/177437553-dfaf9d08-ca84-4a58-b474-67942439a677.gif)\r\n\r\n","mergeCommitSha":"6091cda256661228078fc156e03e8725f08d906c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2176","title":"Set sidebar pinned on new install","createdAt":"2022-07-06T00:23:26Z"}
{"state":"Merged","mergedAt":"2022-07-06T00:29:13Z","number":2177,"mergeCommitSha":"40318638edd737a736c95130c27982a7c33e4f42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2177","title":"Fix regression","createdAt":"2022-07-06T00:28:59Z"}
{"state":"Merged","mergedAt":"2022-07-08T18:44:58Z","number":2178,"body":"We shouldn't create a thread unread for anonymous users. This is so that when they join they're not hit with a bunch of unread messages.","mergeCommitSha":"2d3e01a767bb6d71da5524af0e538d8be119f82b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2178","title":"Don't create ThreadUnreads for anonymous users for PR ingest/sync/@-mention","createdAt":"2022-07-06T00:29:07Z"}
{"state":"Merged","mergedAt":"2022-07-07T16:33:05Z","number":2179,"body":"Scope host permissions.\r\n\r\nThis is a scary change.... We had initially done this due to a keepAlive hack.\r\nWe'll need to dog food this to truly understand if it causes any issues. ","mergeCommitSha":"19e8a79ee484b9f3222d4925960ab640383e185e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2179","title":"Update host permisisons","createdAt":"2022-07-06T00:37:21Z"}
{"state":"Merged","mergedAt":"2022-02-02T17:47:45Z","number":218,"body":"## Problem\r\n\r\nThe current `/login` endpoint returns a 302 redirect to the SCM oauth authorization endpoint. This is problematic for clients because it doesn't mesh well with openapi generators, requiring custom templates. Rather than treat the request as browser originated, we should assume api requests are always from the client and return a payload.\r\n\r\n## Proposal\r\n\r\nChange `/login` to `/login/options` and return a `LoginOptions` payload:\r\n```\r\n{\r\n    \"providers\": [\r\n        {\r\n            \"provider\":\"github\",\r\n            \"oauthUrl\":\"https://github.com/login/oauth/authorize?state=<base64encodedPayload>&etc=etc\r\n        },\r\n        {\r\n            \"provider\":\"bitbucket\",\r\n            \"oauthUrl\":<etc>\r\n        }\r\n    ]\r\n}\r\n```\r\n\r\nAdditionally, we can remove the `provider` argument from the `/login` and `/preAuth` APIs, and instead shuttle the provider arg through the `oauthUrl` `state` parameter. This simplifies the client model and keeps the provider selection encoded by the server. \r\n\r\n## Possible Next Steps\r\nFor now the `clientType` is still being shuttled as a cookie. We discussed embedding a final \"landing url\" within the oauth redirect uri, but I don't see a strong use case for this yet. Please comment.","mergeCommitSha":"f92f35a4a394f4f74585d508695aa978651d7190","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/218","title":"Change login api to return object with provider details","createdAt":"2022-02-02T06:27:32Z"}
{"state":"Merged","mergedAt":"2022-07-06T04:04:08Z","number":2180,"body":"\r\n<img width=\"563\" alt=\"CleanShot 2022-07-05 at 20 03 53@2x\" src=\"https://user-images.githubusercontent.com/858772/177459195-fa43b265-8a71-4577-9ee2-66751209f93a.png\">\r\n\r\n\r\n<img width=\"533\" alt=\"CleanShot 2022-07-05 at 20 02 14@2x\" src=\"https://user-images.githubusercontent.com/858772/177459114-7e026b16-c771-46ef-bd9f-fbeb4ef31bab.png\">\r\n\r\n","mergeCommitSha":"7bd13bd7a7747568cbd8edb75971db540efd1916","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2180","title":"Header section touchups","createdAt":"2022-07-06T00:43:00Z"}
{"state":"Merged","mergedAt":"2022-07-06T02:13:46Z","number":2181,"body":"![CleanShot 2022-07-05 at 19 00 19](https://user-images.githubusercontent.com/********/177451472-5594a7e5-b918-4c9c-949e-f0432e73b5a2.gif)\r\n\r\nSome slight refactoring to showing the mention dropdown and add whitespace after inserting mentions","mergeCommitSha":"0bc58004fdb44cd36241ec8ff8f330e975202d4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2181","title":"Update mentions whitespacing","createdAt":"2022-07-06T02:04:19Z"}
{"state":"Merged","mergedAt":"2022-07-06T05:30:28Z","number":2182,"body":"This got more involved then I thought it would be:\r\n* MessageEditor can tell if a message has content, but the form that it publishes this is not super helpful when it comes to handling this state in the parent code.\r\n* So I ended up making a MessageUtils class that has helpers for detecting empty-content messages, and generating plain-text messages.  It's ugly.\r\n* I'm open to alternatives","mergeCommitSha":"334652ee496768dc521cfe8dafb9422b1ca270ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2182","title":"Allow making new threads with empty message content","createdAt":"2022-07-06T02:42:40Z"}
{"state":"Closed","mergedAt":null,"number":2183,"body":"Off by one error?\nhttps://app.logz.io/#/goto/f189782e311856f3eb65b6c52fb831bc?switchToAccountId=411850","mergeCommitSha":"83b18e4f2e0be1339719e3e78dc041bd57d7cae5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2183","title":"Fix: originalStartLine (38) is greater than originalLine (38)","createdAt":"2022-07-06T03:19:04Z"}
{"state":"Merged","mergedAt":"2022-07-06T05:36:49Z","number":2184,"body":"With a minor tweak, we now support every Git version that VSCode supports. Back to 2.0.5 in 2014.\n\nhttps://linear.app/unblocked/issue/UNB-233/validate-that-the-sourcemark-engine-works-on-older-git-versions","mergeCommitSha":"c465e488c3a65ce27e544373cb754601fbceac5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2184","title":"Support even older Git versions","createdAt":"2022-07-06T03:59:53Z"}
{"state":"Merged","mergedAt":"2022-07-06T04:54:35Z","number":2185,"body":"<img width=\"730\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-0e4811a8-81a6-4d92-8ec3-5b6aec3fc3ad.png\">\r\n\r\n\r\nalso small typo fix for the icons stack ","mergeCommitSha":"876ed3da5395468cd3e7cfe6b666859f0d522f89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2185","title":"Update onboarding asset","createdAt":"2022-07-06T04:34:17Z"}
{"state":"Merged","mergedAt":"2022-07-06T04:46:12Z","number":2186,"mergeCommitSha":"63daba68f9bb0863d28fa2c8150e130d2d45b5a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2186","title":"Use filled support icon","createdAt":"2022-07-06T04:40:52Z"}
{"state":"Merged","mergedAt":"2022-07-06T05:19:34Z","number":2187,"mergeCommitSha":"3a96a8e93b600c509efbba9a9a9b468ca2f9bd3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2187","title":"Fix installer script loop","createdAt":"2022-07-06T05:00:00Z"}
{"state":"Merged","mergedAt":"2022-07-08T02:47:16Z","number":2188,"body":"Fixes UNB-385","mergeCommitSha":"388d4e0b55cb276a92815ebd8858a62be83611a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2188","title":"Create unread ThreadUnreads for @mentioned team members","createdAt":"2022-07-06T06:03:50Z"}
{"state":"Merged","mergedAt":"2022-07-06T16:33:27Z","number":2189,"body":"We were starting runner instances when build jobs were not supposed to run because of a missing conditional. ","mergeCommitSha":"58182b6e5073cf649fd828d62f2916434ea07497","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2189","title":"start ec2 runners only when files have changed","createdAt":"2022-07-06T16:21:00Z"}
{"state":"Merged","mergedAt":"2022-02-02T23:32:55Z","number":219,"body":"### 4-way join\r\n* I'm not concerned about the 4-way join for now. I think optimization of this should be driven by empirical performance issues.\r\n* We _could_ change to 3-way join, denormalizing by adding PersonID to TeamMember model; but the problem is that we would need to consistently (transitionally) update _both_ the IdentityModel and TeamMemberModel when a user dis/connects an identity.\r\n\r\n### API-DB coupling problem\r\nSee #220.","mergeCommitSha":"6a96f3dcc6959f4cc09dba4dac7b30071b9be83e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/219","title":"Implement Team apis","createdAt":"2022-02-02T17:11:44Z"}
{"state":"Closed","mergedAt":null,"number":2190,"mergeCommitSha":"b23ed0e51ddf100809b26caeefba7af7cb1420d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/2190","title":"Add NotificationPreferences to Person model","createdAt":"2022-07-06T16:50:36Z"}