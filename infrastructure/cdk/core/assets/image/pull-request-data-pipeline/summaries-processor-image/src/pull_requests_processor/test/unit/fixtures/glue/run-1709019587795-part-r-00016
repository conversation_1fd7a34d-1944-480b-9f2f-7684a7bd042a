{"state":"Merged","mergedAt":"2022-10-05T19:41:45Z","number":3200,"mergeCommitSha":"5e69384976434a47a86069598abd2ed21778b4fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3200","title":"SM Engine reports throughput and cumulative usage","createdAt":"2022-10-05T19:10:47Z"}
{"state":"Merged","mergedAt":"2022-10-05T20:29:28Z","number":3201,"body":"Fixes UNB-680\r\n\r\nI will add a test for this in my other PR (https://github.com/NextChapterSoftware/unblocked/pull/3188)","mergeCommitSha":"0cf5536e4651249c36776963e2a61bbe96938c2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3201","title":"Sort SourceMarks by line","createdAt":"2022-10-05T19:51:42Z"}
{"state":"Merged","mergedAt":"2022-10-05T21:44:13Z","number":3202,"body":"https://linear.app/unblocked/issue/UNB-633/source-mark-tracking-works-when-moved-by-commits-but-does-not-work\r\n\r\nanother regression. needs _system-level_ tests badly.","mergeCommitSha":"bedb59dfdaf1ada30a264df8dd7171fa1d676b14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3202","title":"Source mark tracking works when moved by commits but does not work when moved by untracked changes","createdAt":"2022-10-05T21:41:32Z"}
{"state":"Merged","mergedAt":"2022-10-05T21:56:10Z","number":3203,"body":"<img width=\"1234\" alt=\"CleanShot 2022-10-05 at 14 50 32@2x\" src=\"https://user-images.githubusercontent.com/1924615/194170618-48619fd9-3a71-4b0b-8e78-a4c3c0bf87e4.png\">\r\n","mergeCommitSha":"7a3a43a26ca4b5146f7f92849a76c72286fecc00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3203","title":"Create pull request summary page for admin console","createdAt":"2022-10-05T21:50:54Z"}
{"state":"Merged","mergedAt":"2022-10-11T20:54:07Z","number":3204,"body":"* Write new onboarding flow with slides instead of the tutorial\r\n\r\nhttps://user-images.githubusercontent.com/13431372/194167694-28950385-38ff-4005-b8d1-ee91d57ddaa7.mp4\r\n\r\n* This is hooked up to the previous command so it should flow through seamlessly\r\n* Planning to remove the old onboarding code in a separate PR to cut down on the diffs \r\n\r\n","mergeCommitSha":"f66b6491e86c3c3a20ce9250fd55f70041389202","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3204","title":"Onboarding revamp","createdAt":"2022-10-05T21:59:05Z"}
{"state":"Merged","mergedAt":"2022-10-07T21:23:31Z","number":3205,"body":"Live preview isn't there yet. This is just to get the layout and controls working properly. The one thing that is actually working is sniffing out the default screen using the window metadata APIs. The rest is mocked\r\n\r\n<img width=\"462\" alt=\"CleanShot 2022-10-05 at 16 32 29@2x\" src=\"https://user-images.githubusercontent.com/858772/194183045-d2a78f4f-342d-42ee-8d96-ac1b683416a2.png\">\r\n","mergeCommitSha":"352d3dac8e8177df8910090acd49bcf1af604e4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3205","title":"Create walkthrough settings window","createdAt":"2022-10-05T23:40:49Z"}
{"state":"Merged","mergedAt":"2022-10-07T21:31:28Z","number":3206,"body":"Add PR-with-slack-threads icon, and use it in the sidebar UIs and the PR view tab icons.\r\n\r\nAs part of this, I cleaned up the way that the \"Open PR\" command worked a bit, to reduce boilerplate.\r\n\r\n<img width=\"1283\" alt=\"Screen Shot 2022-10-05 at 4 59 59 PM\" src=\"https://user-images.githubusercontent.com/2133518/194184968-ad66d08d-484e-4681-aac8-ca0214d66331.png\">\r\n","mergeCommitSha":"4dffe1080994d9eca5938f8f7453ecea7da3dbf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3206","title":"Add Slack PR icons","createdAt":"2022-10-06T00:01:27Z"}
{"state":"Merged","mergedAt":"2022-10-06T18:35:00Z","number":3207,"mergeCommitSha":"516146991d4fd5f6fe0876d46d9adc82141491e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3207","title":"Identify slack threads during PR ingestion","createdAt":"2022-10-06T00:11:46Z"}
{"state":"Merged","mergedAt":"2022-10-06T03:45:30Z","number":3208,"mergeCommitSha":"e98d4e7e11003844f6005462df1b1867072848e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3208","title":"getPullRequestInfo returns hasSlackThreads","createdAt":"2022-10-06T03:34:47Z"}
{"state":"Closed","mergedAt":null,"number":3209,"mergeCommitSha":"6425093525d6ce806ed07e6b5b1fe2fdfba6e33d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3209","title":"Add some logs","createdAt":"2022-10-06T04:30:30Z"}
{"state":"Merged","mergedAt":"2022-02-14T17:21:22Z","number":321,"body":"Sorry for the excessive noise on the PR. Getting CI/CD to work is a bit of a pain. \r\n\r\n- Added cdk config json for mgt environment. I missed it in one of my previous PRs \r\n- Changed IAM stack to create an Admin Read-only role. This will be used to scope some of our automation and audit permissions later on. \r\n- Added flag for IAM readonly role to all env \r\n- Added CI/CD workflows for infra \r\n\r\nThis is all working now and we have full CI/CD for our core infra. Management account is disabled for now because it's our root account and we can't just grant Admin on it. I am working on scoped permissions but we don't run any resources in management account so it should be fine. ","mergeCommitSha":"99113357dce8e8bdd3bd400161ab1e8459ea0672","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/321","title":"Ci for cdk deploy","createdAt":"2022-02-11T19:25:25Z"}
{"state":"Merged","mergedAt":"2022-10-06T17:39:09Z","number":3210,"mergeCommitSha":"1e1706c6df157b4c7d1018dd4803caeb70c2e21a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3210","title":"Dont count reviews with no content","createdAt":"2022-10-06T16:07:58Z"}
{"state":"Merged","mergedAt":"2022-10-12T20:28:06Z","number":3211,"body":"<img width=\"620\" alt=\"CleanShot 2022-10-06 at 09 23 48@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-9a45fbdd-1ce1-4b6f-99ab-cc9449ebd7fb.png\">\r\n","mergeCommitSha":"222091b83a61b033f8cb497b96677240154e8636","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3211","title":"Update default PR tab to discussions","createdAt":"2022-10-06T16:25:37Z"}
{"state":"Merged","mergedAt":"2022-10-06T17:03:10Z","number":3212,"body":"Will expand the heuristics in a follow up PR","mergeCommitSha":"1f8658df0961902ec951a9e9f4b3352fd19b011e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3212","title":"Introduce SlackThreadRelevance service","createdAt":"2022-10-06T16:53:17Z"}
{"state":"Merged","mergedAt":"2022-10-06T17:36:56Z","number":3213,"mergeCommitSha":"51d72c4e19ba84c8e4bbc7a3bf3539beddecad4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3213","title":"Expand SlackThreadRelevance heuristics","createdAt":"2022-10-06T17:30:10Z"}
{"state":"Merged","mergedAt":"2022-10-07T00:58:59Z","number":3214,"body":"Add basic mock UI for Walkthrough.\r\nMain work was figuring out how to render video from outside extension. \r\n\r\n<img width=\"973\" alt=\"CleanShot 2022-10-06 at 10 44 29@2x\" src=\"https://user-images.githubusercontent.com/1553313/194383349-1ff38569-8849-49d4-ba5b-004f2724bdb0.png\">\r\n\r\nWill be filling out logic and styling in future PR after integration with Video App.\r\n","mergeCommitSha":"f7d6d75f081d64827169933cba38ca84871822f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3214","title":"VSCode Video suppport","createdAt":"2022-10-06T17:49:07Z"}
{"state":"Merged","mergedAt":"2022-10-06T17:52:13Z","number":3215,"mergeCommitSha":"4e6a77308950bf242fdeb6e45c19410820feceba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3215","title":"Fix indentation","createdAt":"2022-10-06T17:52:08Z"}
{"state":"Merged","mergedAt":"2022-10-07T21:43:45Z","number":3216,"mergeCommitSha":"e19c7dba2643185ac193a4ccb00a5534d007353b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3216","title":"Wire up data sources for settings","createdAt":"2022-10-06T18:58:46Z"}
{"state":"Merged","mergedAt":"2022-10-06T19:21:55Z","number":3217,"body":"- Slack installations\r\n- More auth\r\n","mergeCommitSha":"9ad185703fb939645117e99618095445209cea27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3217","title":"AddSlackAuth","createdAt":"2022-10-06T19:07:48Z"}
{"state":"Merged","mergedAt":"2022-10-06T21:43:50Z","number":3218,"mergeCommitSha":"7864614330dc60d7945bbb8ce4ef39af1e343753","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3218","title":"Log found slack urls in TLCs","createdAt":"2022-10-06T21:32:09Z"}
{"state":"Merged","mergedAt":"2022-10-06T23:13:36Z","number":3219,"mergeCommitSha":"7fd65c1d310ec265903c3435f4b4bb1d0bdedd11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3219","title":"Fix install bugs","createdAt":"2022-10-06T23:05:27Z"}
{"state":"Merged","mergedAt":"2022-02-11T23:35:12Z","number":322,"body":"This is an example of adding DAO support for modifiedAt status queries. Unfortunately the `UUIDEntityClass` structure doesn't expose the typing information for the underlying table, so this can't be implemented as extensions unless reflection is used. ","mergeCommitSha":"0936557235d118dce2bb8373182cd72e4478d901","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/322","title":"Add modifiedAt queries to ThreadModel","createdAt":"2022-02-11T19:30:11Z"}
{"state":"Merged","mergedAt":"2022-10-06T23:34:34Z","number":3220,"mergeCommitSha":"616fffbf55f4a7595dcf2831bdd5cd191d8578be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3220","title":"Ingest linked slack thread","createdAt":"2022-10-06T23:27:24Z"}
{"state":"Merged","mergedAt":"2022-10-06T23:46:33Z","number":3221,"body":"## Problem\r\n\r\nThe VSCode Auto Save setting causes deterministic bugs in source mark rendering when moving source\r\nmarks between files in unsaved or uncommitted editors. Bugs are present regardless of the setting, but\r\nthe bugs are different (or at least present differently) depending on the setting.\r\n\r\n## Explanation\r\n\r\nThe problem arises when one half of the move is saved and the other part of the move is unsaved. These\r\ntwo sets of hunks are treated like sequential commits, where the saved hunks are processed first, then\r\nthe unsaved hunks are processed.\r\n\r\nIf the saved hunks remove the mark then it is treated as deleted or heavily modified, and propagates\r\nno further. If the unsaved hunks are saved, then all hunks are processed together and we can detect\r\nthe move. This fully explains the Auto Save onFocusChange scenario.\r\n\r\nA similar but opposite set of events occurs for the Auto Save off scenario. In this case the move is\r\ntracked correctly while all files are unsaved. As soon as one file is saved the move is split across\r\ntwo sets of sequentially processed hunks.\r\n\r\n## Changes\r\n\r\nSolution is to stop processing both sets of saved/unsaved hunks separately. Right now we generate two\r\nsets of diff hunks from two separate diff commands: `HEAD..saved`, and `saved..unsaved`.\r\n\r\nInstead we need to generate a unified set of diff hunks from a single diff: `HEAD..unsaved`. The Git\r\nnative way to do this is to add the unsaved files as blobs to the Git object database. That allows us\r\nto diff the blobs: `HEAD{blob}..unsaved{blob}`. This also allows us to take advantage of the Git CAS\r\nfor caching.\r\n\r\nGit gc will eventually prune the object IDs that we add as blobs after 2 weeks, by default.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-685/auto-save-vscode-setting-breaks-source-mark-cross-file-move-rendering","mergeCommitSha":"976ff3b0eeacbbd813589d6f9346a2dbbe05de09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3221","title":"Leverage Git object database to combine saved and unsaved changes for uncommitted SM move detection","createdAt":"2022-10-06T23:31:49Z"}
{"state":"Closed","mergedAt":null,"number":3222,"body":"This reverts commit 616fffbf55f4a7595dcf2831bdd5cd191d8578be.","mergeCommitSha":"d5802183a682fd3aa96bf5f1787be93433915c0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3222","title":"Revert \"Ingest linked slack thread (#3220)\"","createdAt":"2022-10-06T23:58:13Z"}
{"state":"Merged","mergedAt":"2022-10-07T00:08:27Z","number":3223,"body":"Caller asks for `N` results. Function calcualtes `N+2` results in case some\nfiles fail to generate any sourcemarks, then returns top `N` of those.","mergeCommitSha":"d4a605c2d0e48ad9e86fab175c5ff4fb87583aca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3223","title":"Guard against get top-N files returning few than expected results","createdAt":"2022-10-06T23:59:28Z"}
{"state":"Merged","mergedAt":"2022-10-07T00:16:19Z","number":3224,"mergeCommitSha":"daf30def289168a312b93c2af3b5478648c01dd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3224","title":"Add slack sercrets","createdAt":"2022-10-07T00:13:52Z"}
{"state":"Merged","mergedAt":"2022-10-07T19:00:12Z","number":3225,"body":"Follow up from #3221\n\nfixes https://linear.app/unblocked/issue/UNB-685/auto-save-vscode-setting-breaks-source-mark-cross-file-move-rendering","mergeCommitSha":"9c7543508470b9c7098309e1852dcaaa193673c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3225","title":"Address move issues for getPointForSourceMark","createdAt":"2022-10-07T00:28:19Z"}
{"state":"Merged","mergedAt":"2022-10-07T21:14:21Z","number":3226,"mergeCommitSha":"f12ee8859ce771d41c892489e2c0861929bd6ecb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3226","title":"Add admin console","createdAt":"2022-10-07T00:39:12Z"}
{"state":"Merged","mergedAt":"2022-10-07T04:06:16Z","number":3227,"mergeCommitSha":"23ac148d01a13527ab44f6ba62b4f28fd122d6f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3227","title":"Fix up transactions","createdAt":"2022-10-07T03:57:49Z"}
{"state":"Merged","mergedAt":"2022-10-13T16:58:07Z","number":3228,"mergeCommitSha":"24f9c9a627b345ad10f41692d298779e838897ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3228","title":"Capture Preview","createdAt":"2022-10-07T06:54:35Z"}
{"state":"Merged","mergedAt":"2022-10-07T18:18:56Z","number":3229,"mergeCommitSha":"39986b92e8ef7d21e0b2eb83e3a556c7310bce9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3229","title":"Parse slack links from PR descriptions","createdAt":"2022-10-07T18:05:15Z"}
{"state":"Merged","mergedAt":"2022-02-11T21:23:49Z","number":323,"body":"logz.io in debug mode logs a lot of crap","mergeCommitSha":"047494b92f58307bfc18d46abf358b47ff229843","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/323","title":"Fix logging crap","createdAt":"2022-02-11T21:19:40Z"}
{"state":"Merged","mergedAt":"2022-10-07T18:23:54Z","number":3230,"body":"I removed `--no-prefix` in favour of `--dst-prefix=|||`, which broken everyone except for me\nbecause I had this setting in my `~/.gitconfig` that masked the issue:\n\n```\n[diff]\n        noprefix = true\n```\n\nProper solution here is to run system level tests in CI in a reproducibile environment\nor environments.","mergeCommitSha":"9d799aff1d4ba4861d926fbb622fd6486b93163e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3230","title":"Fix SM engine regression caused by Git diff parameter change","createdAt":"2022-10-07T18:14:44Z"}
{"state":"Merged","mergedAt":"2022-10-07T18:50:28Z","number":3231,"mergeCommitSha":"186332d5cab0d35c6c57dc30b34feb5567ef133e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3231","title":"Parse slack links from PR reviews and code-level comments","createdAt":"2022-10-07T18:29:41Z"}
{"state":"Merged","mergedAt":"2022-10-07T19:37:43Z","number":3232,"mergeCommitSha":"d37971705dbda7b795b6e9d67c13d8da14981eea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3232","title":"Parse slack links during bulk ingestion","createdAt":"2022-10-07T19:01:37Z"}
{"state":"Merged","mergedAt":"2022-10-07T19:28:27Z","number":3233,"body":"- Reorg stuff and add antoher queue\r\n- Update\r\n","mergeCommitSha":"a053ab5585b24efdca40420150595668d6ff64f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3233","title":"MakeEventQueueMoreGeneric","createdAt":"2022-10-07T19:21:46Z"}
{"state":"Merged","mergedAt":"2022-10-07T19:58:12Z","number":3234,"mergeCommitSha":"f53301c822a54be0a72459e980d7686451a78431","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3234","title":"Move to a new module","createdAt":"2022-10-07T19:45:56Z"}
{"state":"Merged","mergedAt":"2022-10-07T20:38:49Z","number":3235,"body":"We temporarily added this to dev builds to show Slack threads, but we're not really using it.  It's confusing, so remove it.","mergeCommitSha":"2239e6e354a1c86d35925bb62a1a1e284546c662","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3235","title":"Remove 'Current File Threads' panel from sidebar","createdAt":"2022-10-07T20:22:47Z"}
{"state":"Merged","mergedAt":"2022-10-07T21:13:20Z","number":3236,"body":"Testing webhooksa nd stuff","mergeCommitSha":"145dcecb140852ea41d9b662ef7eaed666927722","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3236","title":"Move to event queue","createdAt":"2022-10-07T20:44:04Z"}
{"state":"Merged","mergedAt":"2022-10-07T23:28:47Z","number":3237,"mergeCommitSha":"4cd39dd34b9f19b4871a6d55056e4f40121c0e6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3237","title":"Add event handler for ingestion","createdAt":"2022-10-07T22:54:26Z"}
{"state":"Merged","mergedAt":"2022-10-11T18:48:12Z","number":3238,"mergeCommitSha":"991aca9d971b57e43313f7bccaf1958653dc951a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3238","title":"Fix discussion title editing","createdAt":"2022-10-07T23:26:05Z"}
{"state":"Merged","mergedAt":"2022-10-08T00:38:11Z","number":3239,"mergeCommitSha":"0f5adc92599dd4c407efbfca33435413652c0961","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3239","title":"Slack ingestion","createdAt":"2022-10-08T00:37:49Z"}
{"state":"Merged","mergedAt":"2022-02-11T22:01:10Z","number":324,"body":"Update redirect urls for oauth dance.","mergeCommitSha":"d08a740a7833a5cf753abdbb4f8cfa68bf86245c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/324","title":"Update redirect urls","createdAt":"2022-02-11T21:31:31Z"}
{"state":"Merged","mergedAt":"2022-10-08T01:02:20Z","number":3240,"mergeCommitSha":"1bf232fe38906a805799544773bd34a40bee23dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3240","title":"Upgrade openapi","createdAt":"2022-10-08T00:53:56Z"}
{"state":"Merged","mergedAt":"2022-10-08T02:43:12Z","number":3241,"mergeCommitSha":"27782056d1ec9f335cccaf594e80c2de45a47121","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3241","title":"Add slack ingestion hooks","createdAt":"2022-10-08T02:27:16Z"}
{"state":"Merged","mergedAt":"2022-10-11T17:06:13Z","number":3242,"mergeCommitSha":"2bdfbf6455e1a794e246415ab42eea6de7a77cd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3242","title":"Rename classes","createdAt":"2022-10-11T17:05:17Z"}
{"state":"Merged","mergedAt":"2022-10-11T19:29:29Z","number":3243,"body":"We can shortcut the expensive part of the string comparison when one string is much larger than another.","mergeCommitSha":"7210ae748dcbc15cb92e62e1f3b8900ed222a3ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3243","title":"Upper bound the Levenshtein distance to handle very large strings","createdAt":"2022-10-11T17:45:51Z"}
{"state":"Merged","mergedAt":"2022-10-11T17:57:05Z","number":3244,"body":"This reverts commit 1bf232fe38906a805799544773bd34a40bee23dd.\r\n","mergeCommitSha":"77d20303123f3cf52df6f104c33533946e7dd02c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3244","title":"Revert \"Upgrade openapi (#3240)\"","createdAt":"2022-10-11T17:57:00Z"}
{"state":"Merged","mergedAt":"2022-10-11T18:25:41Z","number":3245,"body":"When we change gradle it could impact almost all projects.  For instance, changing the OpenAPI version or build scripts will impact Swift and TS clients.","mergeCommitSha":"62adcdcfc00b4ff49092af046ad34e2bbebb13f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3245","title":"Update CI filters to rebuild everything when gradle settings change","createdAt":"2022-10-11T18:10:37Z"}
{"state":"Merged","mergedAt":"2022-10-11T19:30:00Z","number":3246,"body":"We tried this before, but the diff size blew out the cache killing memory and performance.\r\nWe restrict the size of the diffs, so might be better now.","mergeCommitSha":"de244689aec9f755d1e5224857ff1afe91c25fbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3246","title":"SM engine caches Git diffs","createdAt":"2022-10-11T18:18:01Z"}
{"state":"Merged","mergedAt":"2022-10-11T18:28:46Z","number":3247,"mergeCommitSha":"0722d9d52b623568aaaa309a37e04d81fa5d0b83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3247","title":"Move SlackThreadIngestionService to lib-slack-ingestion","createdAt":"2022-10-11T18:19:01Z"}
{"state":"Merged","mergedAt":"2022-10-11T19:30:38Z","number":3248,"body":"- drop character transposition correction which is really more useful for typos\n- about 2X faster with other minor optimizations","mergeCommitSha":"c083b0177e0b8ce85efa913121d4c88058ac1bab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3248","title":"Further optimize by dropping character transposition correction and streamlining","createdAt":"2022-10-11T18:40:37Z"}
{"state":"Merged","mergedAt":"2022-10-11T20:34:27Z","number":3249,"body":"To track when we've extracted Slack thread references from all pull requests in a repo","mergeCommitSha":"77baee194d0c941a22996ede2fedfe38b9c5fdfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3249","title":"Add PullRequestIngestionModel.slackIngestionComplete property","createdAt":"2022-10-11T18:58:05Z"}
{"state":"Merged","mergedAt":"2022-02-11T21:57:13Z","number":325,"mergeCommitSha":"f3e2c1a9a0ffe000213eb85269618008dfb39bf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/325","title":"Fixes missing auth header for V3 client","createdAt":"2022-02-11T21:37:06Z"}
{"state":"Merged","mergedAt":"2022-10-13T23:43:41Z","number":3250,"body":"Setup basic IPC communication between VSCode and video app.\r\n\r\nmany features and polish still need to be done but this sets up the models and communication between the apps.\r\n\r\n\r\nUploading CleanShot 2022-10-11 at 12.06.25.mp4…\r\n\r\n","mergeCommitSha":"496762f92c7c344be99379daa166c88702217d49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3250","title":"Basic ipc implemnetation","createdAt":"2022-10-11T19:07:57Z"}
{"state":"Merged","mergedAt":"2022-10-11T19:14:07Z","number":3251,"mergeCommitSha":"a46b50b90c428bd1cadc2cf309e9b23200ae5503","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3251","title":"Add new infra slack queues","createdAt":"2022-10-11T19:13:45Z"}
{"state":"Merged","mergedAt":"2022-10-28T16:14:16Z","number":3252,"mergeCommitSha":"24b51c7868e874a6cd059e475705b20652f4f57b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3252","title":"Fix open api code gen","createdAt":"2022-10-11T20:33:08Z"}
{"state":"Merged","mergedAt":"2022-10-11T21:36:52Z","number":3253,"body":"Update RepoStore instance method to follow other patterns.\r\n\r\naka change `RepoStore.instance()....` to `RepoStore.instance....`","mergeCommitSha":"3d6c991da29750271dafdec50e99eada440536cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3253","title":"Update RepoStore Instance","createdAt":"2022-10-11T20:57:29Z"}
{"state":"Merged","mergedAt":"2022-10-11T22:50:05Z","number":3254,"mergeCommitSha":"dec38ea951d82b27cca402917003715fad0b3e75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3254","title":"Fix action buttons","createdAt":"2022-10-11T21:26:24Z"}
{"state":"Merged","mergedAt":"2022-10-11T21:49:51Z","number":3255,"body":"Need to be able to trigger slack and team ingestion after app install.","mergeCommitSha":"361771f22bd8d204af2a152d9ae93c2b900ff9f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3255","title":"Add slack queue processing","createdAt":"2022-10-11T21:28:43Z"}
{"state":"Merged","mergedAt":"2022-10-13T17:44:19Z","number":3256,"mergeCommitSha":"28ab521835815d51d375ebed8c93a09968ac6d1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3256","title":"Capture camera and add to preview","createdAt":"2022-10-11T22:10:33Z"}
{"state":"Merged","mergedAt":"2022-10-11T23:07:23Z","number":3257,"body":"Doug want's to do some data mining magic on prod data. \r\n\r\nWe should use the RO instance `databasestack-mainapp32bc6243-1s2nwgqjjscqp.cluster-ro-czdnrjl0hm44.us-west-2.rds.amazonaws.com`","mergeCommitSha":"289cb546ad20aa77549f4e1ad2cebada871fd188","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3257","title":"open temporary access to prod db","createdAt":"2022-10-11T22:59:29Z"}
{"state":"Merged","mergedAt":"2022-10-11T23:27:48Z","number":3258,"mergeCommitSha":"e2c757370b6d9ef306ff55cc2ee121aaec44d29f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3258","title":"Include PullRequestIngestions where slackIngestionComplete is null","createdAt":"2022-10-11T23:03:48Z"}
{"state":"Merged","mergedAt":"2022-10-12T17:35:11Z","number":3259,"mergeCommitSha":"e3c6edff757ea05d0c10a1306dcd4a8573178896","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3259","title":"Implement ingestLinkedSlackThreads","createdAt":"2022-10-11T23:28:05Z"}
{"state":"Merged","mergedAt":"2022-02-11T22:23:38Z","number":326,"body":"Addresses https://github.com/NextChapterSoftware/unblocked/pull/313#discussion_r804344071﻿\n","mergeCommitSha":"e2481eeffdf796ff81098475d3c47b191ee700f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/326","title":"Drop id from graphql queries","createdAt":"2022-02-11T22:19:10Z"}
{"state":"Merged","mergedAt":"2022-10-12T00:15:42Z","number":3260,"body":"For real this time","mergeCommitSha":"815fcaedfc0aa0f1a863e0e4fd5916d6b7681090","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3260","title":"Include PullRequestIngestions where slackIngestionComplete is null","createdAt":"2022-10-12T00:04:25Z"}
{"state":"Merged","mergedAt":"2022-10-12T00:39:08Z","number":3261,"mergeCommitSha":"a5579c3b30401627b7bc4298c0c20df519ef1f67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3261","title":"Generalize some slack ingestion code","createdAt":"2022-10-12T00:22:24Z"}
{"state":"Merged","mergedAt":"2022-10-13T00:17:29Z","number":3262,"mergeCommitSha":"48f90aff197cfdb40941364e60c2d082a4d790ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3262","title":"Filter out insignificant Git commands from command log report in VSCode","createdAt":"2022-10-12T04:21:18Z"}
{"state":"Merged","mergedAt":"2022-10-13T17:51:00Z","number":3263,"mergeCommitSha":"53eb156ce7d7b6fe49bb56476d483d6dcbfb7390","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3263","title":"Add walkthrough mode","createdAt":"2022-10-12T16:06:11Z"}
{"state":"Merged","mergedAt":"2022-10-12T17:02:08Z","number":3264,"body":"https://linear.app/unblocked/issue/UNB-687/vscode-hangs-downloading-source-marks-for-sentry\n\nThis change from 10 MB to 50 MB is a workaround solution. Even with this change it take ~40 seconds\non `getsentry/sentry` to download all the source mark/point data.\n\nBetter solution is to prune the response content to make much leaner.","mergeCommitSha":"c2c63956ed0e4201eaaa52f5203838660dfdac11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3264","title":"VSCode hangs downloading source marks for Sentry","createdAt":"2022-10-12T16:59:11Z"}
{"state":"Merged","mergedAt":"2022-10-13T16:04:07Z","number":3265,"body":"### Summary\r\n\r\nThe previous update logic did two things that prevented a successful upgrade in the scenario where the download fails:\r\n1. Always save the latest info regardless of download status\r\n2. Bypass download if the previously received update version is >= the most recent version\r\n\r\nThis combination caused the download and upgrade process to short-circuit. \r\n\r\nThe new logic resolves this issue:\r\n1. Still always save the latest info regardless of download status so there's a fallback in the settings menu\r\n2. Always download the installer if we don't have it for the most recent version","mergeCommitSha":"896d6af4c1412156f771032d3fd4dffe88d34487","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3265","title":"[SENSITIVE CHANGE]: Always run installer when file download completes","createdAt":"2022-10-12T17:15:51Z"}
{"state":"Merged","mergedAt":"2022-10-14T00:08:44Z","number":3266,"body":"Added additional metadata as backup for when a source mark is not generated.\r\n\r\nThis will still allow the client to render the name of the file & a link to GH.","mergeCommitSha":"eeaa458ac04b4fd81b9851b007fa3c80daed1d4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3266","title":"[BREAKS API ON MAIN] Update API to include additional code reference data","createdAt":"2022-10-12T18:37:56Z"}
{"state":"Merged","mergedAt":"2022-10-12T19:54:35Z","number":3267,"body":"This pr is a first pass at thread -> n pull requests.\r\nThe join table is the first pass at this.\r\n\r\nWhat needs to be done:\r\n1. Migration\r\n2. Api updates\r\n3. Removing setting pullRequest in thread model.\r\n4. Back filling pull requests upon pr ingestion.","mergeCommitSha":"0f0184a922eeac2389acc0eb7442efbe92a9b817","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3267","title":"Threads can be associated with multiple pull requests","createdAt":"2022-10-12T18:58:38Z"}
{"state":"Merged","mergedAt":"2022-10-12T23:09:37Z","number":3268,"body":"Validated with ingestion locally.","mergeCommitSha":"7b42dc08306bb773816799e50b907dbe847f1304","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3268","title":"Add slack message urls","createdAt":"2022-10-12T22:47:20Z"}
{"state":"Merged","mergedAt":"2022-10-14T22:21:43Z","number":3269,"mergeCommitSha":"7ca47d2e84fc955cda0afb0727cb006f477be75d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3269","title":"Add floating camera view to screen sharing mode","createdAt":"2022-10-12T23:42:43Z"}
{"state":"Merged","mergedAt":"2022-02-11T23:25:28Z","number":327,"body":"Small cleanup thing","mergeCommitSha":"7382e2e6a0a466a977d3ee3ce638e1e32a12c65b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/327","title":"Generate relaxed json serializer from templates","createdAt":"2022-02-11T22:53:05Z"}
{"state":"Merged","mergedAt":"2022-10-13T15:58:28Z","number":3270,"body":"A good friend once told me that a thread should not precluded from having multiple pull requests.\r\nIt has an inviolable right to that, and you know what, he's right.","mergeCommitSha":"380b73adbc2e970b313a91f0771fcb98a9dd2e4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3270","title":"Add laminator migrator","createdAt":"2022-10-13T01:20:39Z"}
{"state":"Merged","mergedAt":"2022-10-13T16:59:39Z","number":3271,"body":"Backend db models to support saving which slack channels we should ingest\r\n\r\n<img width=\"905\" alt=\"CleanShot 2022-10-12 at 23 33 31@2x\" src=\"https://user-images.githubusercontent.com/1924615/195519771-c24e1b83-538e-45d9-ab5e-eb639350a874.png\">\r\n\r\nIf we ultimately end up having an \"all public models\" option, I'm thinking we can create a SlackTeamModelPreferencesModel to save that.","mergeCommitSha":"eeef790faad0e685fb6328408f0b706fb8bafc76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3271","title":"Add SlackChannelPreferencesModel","createdAt":"2022-10-13T06:35:41Z"}
{"state":"Merged","mergedAt":"2022-10-13T16:59:21Z","number":3272,"mergeCommitSha":"f7201b75c17ec76affe28f40d4112ee69d6ee29a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3272","title":"Upgrad admin settings","createdAt":"2022-10-13T16:59:12Z"}
{"state":"Merged","mergedAt":"2022-10-13T17:28:10Z","number":3273,"mergeCommitSha":"fb886a60479cf312fecb3b25346fd176862d35d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3273","title":"optimize backfill","createdAt":"2022-10-13T17:28:06Z"}
{"state":"Merged","mergedAt":"2022-10-13T21:21:55Z","number":3274,"mergeCommitSha":"ef3c131148c29e02da1c54ebb8651668183d326e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3274","title":"Add SlackTeamPreferencesModel","createdAt":"2022-10-13T18:10:48Z"}
{"state":"Merged","mergedAt":"2022-10-13T21:45:50Z","number":3275,"body":"We'll use this to list the slack channels during onboarding","mergeCommitSha":"2b93d2e8bb91cb53838e543183a59c9058e621e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3275","title":"Add SlackChannelStore.findBySlackTeam","createdAt":"2022-10-13T18:34:10Z"}
{"state":"Merged","mergedAt":"2022-10-13T20:09:07Z","number":3276,"mergeCommitSha":"bc55ece9b89561c6865a1936347c1948341c2e48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3276","title":"ADd infra queue","createdAt":"2022-10-13T20:08:57Z"}
{"state":"Merged","mergedAt":"2022-10-13T20:43:32Z","number":3277,"mergeCommitSha":"f3e1aeaf184f22cb2c57575fe1e5b646aaffe374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3277","title":"Initial channel singestion","createdAt":"2022-10-13T20:17:02Z"}
{"state":"Merged","mergedAt":"2022-10-13T20:52:04Z","number":3278,"mergeCommitSha":"791f2f2255e2232c01d084b15a3bdb6b1ea8eaba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3278","title":"Improve backfill","createdAt":"2022-10-13T20:51:10Z"}
{"state":"Merged","mergedAt":"2022-10-13T22:49:13Z","number":3279,"body":"The original intention was to test the current spec against origin/main, not against HEAD which is often behind your current changes locally.","mergeCommitSha":"6cb1c11b64635daf1825b02387f13dfb908182e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3279","title":"API compat test must use current spec, not HEAD spec","createdAt":"2022-10-13T21:42:09Z"}
{"state":"Merged","mergedAt":"2022-02-14T21:51:07Z","number":328,"body":"Add more required fields to the Message model needed to build client UI ","mergeCommitSha":"3eac55ae2af537e2d30d65b993b8140da3e481d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/328","title":"Add fields to Message model","createdAt":"2022-02-11T22:59:47Z"}
{"state":"Merged","mergedAt":"2022-10-13T22:10:48Z","number":3280,"mergeCommitSha":"856d5a0cd4aa813c48f702887ff2abac27773228","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3280","title":"update","createdAt":"2022-10-13T22:10:38Z"}
{"state":"Merged","mergedAt":"2022-10-13T22:32:39Z","number":3281,"body":"https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/4663e0c7-e120-4f87-b9f4-cebbc51387d3","mergeCommitSha":"82a84ce2e49980d6d5076efeea198e4d45817556","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3281","title":"Change to DESC_NULLS_FIRST","createdAt":"2022-10-13T22:17:31Z"}
{"state":"Merged","mergedAt":"2022-10-17T18:39:08Z","number":3282,"body":"<img width=\"533\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/195722518-2d81f674-3233-4a55-919f-0f62fd915649.png\">\r\n<img width=\"532\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/195722535-91d621bf-b15e-4428-838e-9b5e9a8efa09.png\">\r\n<img width=\"529\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/195722559-82055cea-25d7-4886-84dd-723ecaf82a37.png\">\r\n\r\n* Refactor some PR view components to reuse for the slack view\r\n* Rewrite the thread header section to include additional data (participants and # comments) ","mergeCommitSha":"ad623109bb5c81cf3dcc70d94105ce38d5d55a35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3282","title":"Slack threads in the PR view","createdAt":"2022-10-13T22:42:50Z"}
{"state":"Merged","mergedAt":"2022-10-18T17:17:51Z","number":3283,"body":"This field is null when we've ingested a slack thread before the associated pull request has been ingested","mergeCommitSha":"8daa52a14cf93d16991940eeb51cf1eebc160325","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3283","title":"Backfill null ThreadPullRequestModel.pullRequest when pull request is ingested","createdAt":"2022-10-13T23:14:24Z"}
{"state":"Merged","mergedAt":"2022-10-18T23:49:55Z","number":3284,"body":"### Changes\r\n- labels a bunch of fields optional to support FileMark concept, but does not actually make optional\r\n- labels other fields optional for bandwidth and storage optimization, but does not actually make optional\r\n- introduce new `getRepoSourceMarks` operation to replace `getSourceMarks`, again for bandwidth and storage optimization","mergeCommitSha":"c9b1fbc66ce6993831d038c00f608f273e233fdf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3284","title":"Alter sourcemark model to represent the concept of a \"FileMark\"","createdAt":"2022-10-13T23:58:57Z"}
{"state":"Merged","mergedAt":"2022-10-14T17:17:27Z","number":3285,"body":"Right now the logic will skip ingesting a slack thread if the referenced pull request doesn't exist. This isn't ideal since it's possible slack ingestion happens before pull request ingestion.\r\n\r\nWe should create the ThreadPullRequestModels even if the pull request doesn't exist, as long as we have the repo and number. When PR ingestion eventually creates that pull request, we'll backfill the ThreadPullRequestModel.pullRequest field here https://github.com/NextChapterSoftware/unblocked/pull/3283","mergeCommitSha":"943d073e80b8f187267f927571ca456b3f556af9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3285","title":"Create ThreadPullRequestModels even when the pull request has yet-to-be ingested","createdAt":"2022-10-14T00:02:28Z"}
{"state":"Merged","mergedAt":"2022-10-14T00:35:23Z","number":3286,"mergeCommitSha":"ad000eb2d705e75768779af655cec6b1d2d0d877","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3286","title":"We should not be conflating slack threads with pr threads","createdAt":"2022-10-14T00:05:44Z"}
{"state":"Merged","mergedAt":"2022-10-14T00:24:09Z","number":3287,"mergeCommitSha":"4cfa072e89e12e4b4a1049f9ebb5d354f12873ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3287","title":"[BREAKS API ON MAIN] Fix typo in API name","createdAt":"2022-10-14T00:12:51Z"}
{"state":"Merged","mergedAt":"2022-10-14T17:29:42Z","number":3288,"mergeCommitSha":"3be4b9c4c5a6a81328b34299a210d6d004f6823c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3288","title":"IMprove admin console for multiple pull requets","createdAt":"2022-10-14T01:23:55Z"}
{"state":"Merged","mergedAt":"2022-10-14T22:46:09Z","number":3289,"mergeCommitSha":"fd0072deca569d647ccb2b7e582a4b39516d9c66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3289","title":"Screen recording with mic but no camera","createdAt":"2022-10-14T04:17:20Z"}
{"state":"Merged","mergedAt":"2022-02-11T23:31:25Z","number":329,"body":"Setup deployment configs per environment ","mergeCommitSha":"d2fed47cb98a89cb5bdc0312d33e33cb92179c7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/329","title":"Update deployment config settings per env","createdAt":"2022-02-11T23:06:16Z"}
{"state":"Merged","mergedAt":"2022-10-14T17:53:10Z","number":3290,"mergeCommitSha":"846b33ba3aa74e5f53a7d8587862e19c190f70e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3290","title":"Upserts shold be in stores","createdAt":"2022-10-14T17:30:31Z"}
{"state":"Merged","mergedAt":"2022-10-14T18:18:17Z","number":3291,"body":"A pull request query for slack threads should now be using ThreadPullRequestStore to query thread to pull request association.\r\n\r\n<img width=\"1707\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/195912377-6dbcfcf7-3eb5-45ed-895e-6c439e9bfccb.png\">\r\n","mergeCommitSha":"6c0c4e40a0f161fcdc80525a08bdc0a07eac3ee0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3291","title":"Slack thread were not showing pu","createdAt":"2022-10-14T18:04:21Z"}
{"state":"Closed","mergedAt":null,"number":3292,"mergeCommitSha":"7001ef4e18a0eb447d7927bfabfff927889f525c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3292","title":"Fix PullRequestStore.hasSlackThreads","createdAt":"2022-10-14T18:05:02Z"}
{"state":"Closed","mergedAt":null,"number":3293,"mergeCommitSha":"c8651aa40621cb9f662dbf41a00082d8d32150c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3293","title":"Camera track is being written out but is not rendered","createdAt":"2022-10-14T19:16:20Z"}
{"state":"Merged","mergedAt":"2022-10-14T22:57:17Z","number":3294,"body":"I will centralize assets in a followup PR.\r\n\r\nThis one just gets the app embedded so we can start working with permissions effectively","mergeCommitSha":"57af1d13a985809bf2919fc356fdea2dca66b9e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3294","title":"Embed walkthrough app in the hub","createdAt":"2022-10-14T21:52:37Z"}
{"state":"Merged","mergedAt":"2022-10-14T22:15:09Z","number":3295,"mergeCommitSha":"e1bc9c243c889bc62ac00bac12b759b8d6759825","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3295","title":"Update slack installations api","createdAt":"2022-10-14T22:03:07Z"}
{"state":"Merged","mergedAt":"2022-10-19T21:14:49Z","number":3296,"body":"Basic end to end flow for video uploading.\r\n\r\nThere's definitely overlap with createKnowledge but also a bunch of custom logic here.\r\nNext few PRs will be doing some refactoring to share code between create walkthrough and knowledge... (collaborators temporarily commented out due to this)\r\n\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/196244772-dbea23e0-8265-4f11-9ad3-e0cc2ff98d85.mp4\r\n\r\n","mergeCommitSha":"c699b8748c228621c3b065f5f12b1cffc684cb8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3296","title":"Basic Video Upload","createdAt":"2022-10-14T22:54:41Z"}
{"state":"Merged","mergedAt":"2022-10-18T21:48:27Z","number":3297,"body":"To support: \r\n\r\n<img width=\"724\" alt=\"CleanShot 2022-10-14 at 16 53 10@2x\" src=\"https://user-images.githubusercontent.com/1924615/195958857-92407b72-e83d-4e20-b4c1-b89d04733434.png\">\r\n","mergeCommitSha":"77e404b79ede606d0c69f19db0e5138c78f14327","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3297","title":"[API] Define Slack configuration endpoints","createdAt":"2022-10-14T23:53:46Z"}
{"state":"Merged","mergedAt":"2022-10-16T22:00:47Z","number":3298,"body":"One of three initial checkins for the Unblocked iPhone experience.\r\n\r\n1: Add UnblockedMobile target with native login + web feed.\r\n2: Single-sign (in-app web browser + native use same AuthToken).\r\n3: Native (Pull) notifications (APNS is later).","mergeCommitSha":"90dbfc270dd57659db72e9cd9401f31d16098f6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3298","title":"Unblocked Mobile","createdAt":"2022-10-15T01:20:55Z"}
{"state":"Merged","mergedAt":"2022-10-16T23:59:25Z","number":3299,"body":"I can't really see this working to fix the issue. If it does it will be surprising","mergeCommitSha":"e0d12870a9f6ec8ecf6dcdfcf0680325d978cf97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3299","title":"Why does Apple do this to us","createdAt":"2022-10-16T23:58:05Z"}
{"state":"Merged","mergedAt":"2022-01-13T23:29:08Z","number":33,"body":"Introduce SCSS Base which takes the variables and sets defaults.\r\n\r\nAlso moved normalization code into SCSS Base","mergeCommitSha":"f65139d273238ae56379628de90238c9e3b2d550","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/33","title":"Refactor SCSS Base","createdAt":"2022-01-13T21:14:54Z"}
{"state":"Merged","mergedAt":"2022-02-14T23:07:10Z","number":330,"body":"Addresses https://github.com/NextChapterSoftware/unblocked/pull/315#discussion_r804949221﻿\n","mergeCommitSha":"5a1daeef7ed787c6fc19cf759d52951408c2c993","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/330","title":"Add author to message and thread","createdAt":"2022-02-11T23:52:46Z"}
{"state":"Merged","mergedAt":"2022-10-17T00:08:30Z","number":3300,"mergeCommitSha":"5147ce07ba37914ac7ef081de28c100f49342964","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3300","title":"Skip install","createdAt":"2022-10-17T00:08:07Z"}
{"state":"Merged","mergedAt":"2022-10-17T17:35:48Z","number":3301,"body":"No-op incremental recalculation should be extremely fast\r\nhttps://linear.app/unblocked/issue/UNB-682/no-op-incremental-recalculation-should-be-extremely-fast\r\n\r\n### Results\r\n\r\n- No improvement on full recalculation, as expected.\r\n- Incremental wallclock runtime recalculation improvement:\r\n  - `expo/expo`: 40%\r\n  - `unblocked`: 30%","mergeCommitSha":"86561dd226350898e54707792dd7159fe83a3cb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3301","title":"Move to bulk Git commands to reduce process exec overhead","createdAt":"2022-10-17T14:46:44Z"}
{"state":"Merged","mergedAt":"2022-10-17T17:12:05Z","number":3302,"mergeCommitSha":"ed585b8db995cd5655470d9cd74f391a014b5e36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3302","title":"Use correct mobile bundle id","createdAt":"2022-10-17T17:03:50Z"}
{"state":"Merged","mergedAt":"2022-10-17T18:24:22Z","number":3303,"body":"Some team attributes, icons etc. were not being ingested.\r\nLet's do it now...","mergeCommitSha":"388b19e7e6fa95d0f2ed0e5e01a53a3c1c751572","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3303","title":"ingest more team info","createdAt":"2022-10-17T18:13:04Z"}
{"state":"Merged","mergedAt":"2022-10-17T18:20:58Z","number":3304,"mergeCommitSha":"80b8231893749c95c62a70b5f6768293ca452a23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3304","title":"Remove alpha channel from icons","createdAt":"2022-10-17T18:20:24Z"}
{"state":"Merged","mergedAt":"2022-10-17T18:38:57Z","number":3305,"mergeCommitSha":"b46c7eb3fca8bc617cfc0a1eeb734fb8f8096a37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3305","title":"Declare app encryption use","createdAt":"2022-10-17T18:38:37Z"}
{"state":"Merged","mergedAt":"2022-10-17T18:45:25Z","number":3306,"mergeCommitSha":"7c1917cf02f1e14a24af8bfeb5a8beec71b5117b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3306","title":"Add link to slack team","createdAt":"2022-10-17T18:45:03Z"}
{"state":"Merged","mergedAt":"2022-10-18T20:07:22Z","number":3307,"body":"Uses the same permissions manager as previous iterations.\r\n\r\nThere are some notable stateful things to be aware of:\r\n1. The user could complete permissions in any order. \r\n2. Screen recording requires an app restart, but Mic and Camera do not. If the user grants screen recording first, it is therefore possible for the app to continue running before and after all permissions are granted\r\n3. Cancelling the permissions process terminates the app\r\n\r\n\r\n<img width=\"532\" alt=\"CleanShot 2022-10-17 at 12 07 21@2x\" src=\"https://user-images.githubusercontent.com/858772/196261564-7879b67b-3d4c-4ef1-9e3a-f9ddcb767471.png\">\r\n","mergeCommitSha":"af07d601718ca5ebb9f0ed0f60e3f4b26c5d23e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3307","title":"Add permissions pre-requisites for walkthrough app","createdAt":"2022-10-17T19:05:27Z"}
{"state":"Merged","mergedAt":"2022-10-17T20:09:05Z","number":3308,"mergeCommitSha":"91237bbe20ccde1f67a278dd97bfab7016731dbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3308","title":"Standardize user secrets","createdAt":"2022-10-17T19:47:04Z"}
{"state":"Merged","mergedAt":"2022-10-17T21:33:22Z","number":3309,"body":"## Changes\r\nIf at least one point is trusted and exists in the tree/repo then ignore all the other point.\r\n\r\nThis is expected to significantly reduce the frequency of these Git commands:\r\n- `git rev-parse SHA:FILE`\r\n- `git show SHA:FILE`\r\n\r\nhttps://linear.app/unblocked/issue/UNB-682/no-op-incremental-recalculation-should-be-extremely-fast\r\n\r\n## Results for `getsentry/sentry`\r\n- Approx 7.3% faster.\r\n\r\n- Before (1624.1 seconds)\r\n```\r\n  count     sum     max     avg cmd\r\n ------  ------  ------  ------ ------\r\n  13037  139753      55    10.7 git rev-parse SHA:FILE\r\n   9926  102085      35    10.3 git show SHA:FILE\r\n```\r\n\r\n- After (1504.5 seconds)\r\n```\r\n  count     sum     max     avg cmd\r\n ------  ------  ------  ------ ------\r\n   2284   19474      23     8.5 git rev-parse SHA:FILE\r\n     34     337      14     9.9 git show SHA:FILE\r\n\r\n```","mergeCommitSha":"2c1555b20d4287a37c90790abacf80e9617449d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3309","title":"Avoid re-validating untrusted points over and over on each client","createdAt":"2022-10-17T21:03:15Z"}
{"state":"Merged","mergedAt":"2022-02-14T18:16:57Z","number":331,"body":"Turns out the graphql query wont return commit id or sha for files for our github app. Lets use the rest api to get comments and files for a pull request, because we can get that info through the github rest api. ﻿\r\n","mergeCommitSha":"155409adb09ad630f1e2e55583bb634c37c2723e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/331","title":"Add ability to get comments and files for a pull request from the rest api","createdAt":"2022-02-12T00:32:21Z"}
{"state":"Merged","mergedAt":"2022-10-18T20:46:56Z","number":3310,"body":"Make screen capture more betterer","mergeCommitSha":"4c3b701a8b2ad71978c0ac96d22905dde298b7dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3310","title":"Fix screen capture filter","createdAt":"2022-10-17T21:29:44Z"}
{"state":"Merged","mergedAt":"2022-10-17T23:24:15Z","number":3311,"body":"For Sentry -- this ensures that the sidebar loads much quicker on startup.  Make the initial SM load in the background, so that it doesn't affect the loading of any other data.  We could potentially do this for *all* data stores, but it seemed more complicated to integrate this with the ChannelPoller, the risk was higher (larger blast radius), and SourceMark loading is the only place where we're loading significant data.\r\n\r\nThis supersedes https://github.com/NextChapterSoftware/unblocked/pull/2444","mergeCommitSha":"b62a554f7b368fa97bfa824dd2e2c32b5cb3a5fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3311","title":"Async load all sourcemarks","createdAt":"2022-10-17T21:37:38Z"}
{"state":"Merged","mergedAt":"2022-10-17T22:44:47Z","number":3312,"body":"before:\r\n<img width=\"1028\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/196290270-aa0e3b22-ade1-43c8-a742-6ce44fef88f8.png\">\r\n\r\nafter:\r\n<img width=\"1119\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/196290303-3f5f49d6-2dcd-4746-97f9-95ae80165d14.png\">\r\n<img width=\"1134\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/196290324-85e98ca4-a277-4cb8-9288-12bb7d7c63de.png\">\r\n","mergeCommitSha":"bae94edf33bc7a3a6711eb29a15b9ebb2bc5b07e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3312","title":"Fix rendering slack icons in large viewports","createdAt":"2022-10-17T21:49:01Z"}
{"state":"Merged","mergedAt":"2022-10-17T22:27:42Z","number":3313,"body":"Still needs work since threads with replies may not have valuable information, but let's start with this.","mergeCommitSha":"15fb021521205f98498a790f4d1bdb2f076c5a14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3313","title":"Only ingest slack threads containing replies","createdAt":"2022-10-17T22:02:25Z"}
{"state":"Merged","mergedAt":"2022-10-17T22:43:36Z","number":3314,"mergeCommitSha":"4b70b6e6bbaea9cdcf5cc09ec08ce18af4880554","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3314","title":"Show number of slack threads for repo in admin console","createdAt":"2022-10-17T22:35:24Z"}
{"state":"Merged","mergedAt":"2022-10-17T23:11:38Z","number":3315,"body":"- Encrypt slack secrets\r\n- Need 4096-bit rsa key to encrypt slack secrets, we were using 512, but slack tokens will not work with that as the maximum bytes size for a 512-bit rsa key is 64 bytes.\r\n","mergeCommitSha":"856cfcae339b1dbd0e27e35c08ffb6beecc678f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3315","title":"EncryptSlackSecrets","createdAt":"2022-10-17T22:49:26Z"}
{"state":"Merged","mergedAt":"2022-10-17T23:27:42Z","number":3316,"body":"https://linear.app/unblocked/issue/UNB-682/no-op-incremental-recalculation-should-be-extremely-fast\r\n\r\n## Changes\r\n\r\n- do not recalculate previous stopped points\r\n\r\n\r\n## Results for `getsentry/sentry`\r\n\r\n- 7.3X incremental runtime improvement \uD83C\uDF89 \r\n\r\n- before (1,504.6 sec)\r\n```\r\n  count     sum     max     avg cmd\r\n ------  ------  ------  ------ ------\r\n   6341 1379272     654   217.5 git rev-list --dense --ancestry-path --first-parent SHA ^SHA -- FILE\r\n   1625   18717      31    11.5 git diff --diff-filter=DR --find-renames=50% --name-status SHA^ SHA --\r\n```\r\n\r\n- after (207.3 sec)\r\n```\r\n  count     sum     max     avg cmd\r\n ------  ------  ------  ------ ------\r\n     68   17735     571   260.8 git rev-list --dense --ancestry-path --first-parent SHA ^SHA -- FILE\r\n     37     390      22    10.5 git diff --diff-filter=DR --find-renames=50% --name-status SHA^ SHA --\r\n```","mergeCommitSha":"84ef9dedddf690c3443e28a3a8f6dcbd1c91910a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3316","title":"SM engine does not reprocess stop points","createdAt":"2022-10-17T23:11:38Z"}
{"state":"Merged","mergedAt":"2022-10-18T18:47:22Z","number":3317,"body":"* Update dashboard routes to handle settings sections\r\n* Add placeholder for Slack UI \r\n* Add feature flag for Slack UI (flagged off for prod) ","mergeCommitSha":"829187fd8814f2c257360e3a6f0da838eedc7b62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3317","title":"[Slack] Add dashboard routes","createdAt":"2022-10-18T00:10:59Z"}
{"state":"Merged","mergedAt":"2022-10-18T02:03:22Z","number":3318,"mergeCommitSha":"ede2bbacd0eec8beab8e104b144105967176784a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3318","title":"Fix slack team admin console","createdAt":"2022-10-18T00:24:33Z"}
{"state":"Merged","mergedAt":"2022-10-21T15:27:09Z","number":3319,"body":"Should only be merged once we have support for configuring the slack integration.","mergeCommitSha":"e10b347c5fd527bd49d493ecdef6a46d2152fccc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3319","title":"Only ingest slack channels configured for ingestion","createdAt":"2022-10-18T18:06:45Z"}
{"state":"Merged","mergedAt":"2022-02-12T01:24:01Z","number":332,"body":"The target port has changed since we moved to port 443 for dev/prod","mergeCommitSha":"f1e2f5b7128026de1202f8443a5ec5c4d53f1c60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/332","title":"Fix port","createdAt":"2022-02-12T01:22:22Z"}
{"state":"Merged","mergedAt":"2022-10-20T16:06:43Z","number":3320,"mergeCommitSha":"9174ab3927f001c6420b6c70593524a5b8a06f9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3320","title":"[BREAKS API ON MAIN] Video metadata model","createdAt":"2022-10-18T18:07:31Z"}
{"state":"Merged","mergedAt":"2022-10-19T00:00:36Z","number":3321,"body":"- Use in Unblocked API client\n- Replace VSCode usage\n- Replace pusher support","mergeCommitSha":"2e39e9741c7185fefb48eab2b323868973f04dfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3321","title":"Use new getRepoSourceMarks API [BREAKS API ON MAIN]","createdAt":"2022-10-18T18:23:24Z"}
{"state":"Merged","mergedAt":"2022-10-18T19:00:03Z","number":3322,"body":"- Slack Token Service\r\n- Fix test\r\n- Fix\r\n","mergeCommitSha":"3ae6d428fd32b955584737280a0000985b1861bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3322","title":"MoveToTokenBasedAuth","createdAt":"2022-10-18T18:46:22Z"}
{"state":"Merged","mergedAt":"2022-10-18T21:13:20Z","number":3323,"mergeCommitSha":"11a9191982b5a264cc5170cdb5ebb7511f5cf620","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3323","title":"[BREAKS API ON MAIN] make installations for slack tied to team","createdAt":"2022-10-18T19:19:30Z"}
{"state":"Merged","mergedAt":"2022-10-18T21:10:41Z","number":3324,"body":"https://linear.app/unblocked/issue/UNB-691/old-versions-of-git-do-not-support-ls-tree-format","mergeCommitSha":"1bba5094bd6bbab1244174274b39cb4302fa1b18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3324","title":"Support ls-tree for older versions of Git","createdAt":"2022-10-18T19:25:48Z"}
{"state":"Closed","mergedAt":null,"number":3325,"mergeCommitSha":"c0c320a83f67dc03eabc054b7bfb151d6b8aca33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3325","title":"Weird rebase issues","createdAt":"2022-10-18T20:44:40Z"}
{"state":"Closed","mergedAt":null,"number":3326,"mergeCommitSha":"18e4e72d1f7a611b1d13e473a1d86b0ab5a999b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3326","title":"Fix videoapp permissions","createdAt":"2022-10-18T20:44:44Z"}
{"state":"Merged","mergedAt":"2022-10-18T23:34:05Z","number":3327,"body":"When adding @mention support, onSendMessage was not included in dependency array.\r\n\r\nTherefore, whenever title was updated, this callback was never updated with the new title value.","mergeCommitSha":"72f4356a7a853530699fa13f48cfbe789fcc2f80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3327","title":"Fix issue with missing dependency","createdAt":"2022-10-18T21:16:07Z"}
{"state":"Merged","mergedAt":"2022-10-18T22:38:36Z","number":3328,"mergeCommitSha":"986b497532c3d519e186aa9a3f55e200675f03e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3328","title":"Fix at mentions","createdAt":"2022-10-18T22:14:24Z"}
{"state":"Merged","mergedAt":"2022-10-19T18:05:59Z","number":3329,"mergeCommitSha":"c6bef9cfdf9c47328583d9b7da41e5e9dc127b7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3329","title":"Show slack configuration in the admin console","createdAt":"2022-10-18T22:17:33Z"}
{"state":"Merged","mergedAt":"2022-02-14T22:12:29Z","number":333,"body":"https://github.com/NextChapterSoftware/unblocked/pull/315#discussion_r805055630","mergeCommitSha":"2417dca0dc15dea0df391262c9fbed51d52c86fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/333","title":"Add Hash data type for parsing commit and file SHAs","createdAt":"2022-02-12T01:23:00Z"}
{"state":"Merged","mergedAt":"2022-10-18T23:19:39Z","number":3330,"mergeCommitSha":"cdfe2b4e96815b16b2441705335f41067ad60dfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3330","title":"fix tests","createdAt":"2022-10-18T23:00:59Z"}
{"state":"Merged","mergedAt":"2022-10-18T23:24:55Z","number":3331,"body":"So that the client can poll the existing `/pullRequests/{pullRequestId}/info` push channel","mergeCommitSha":"21e5b681cab0a054fb1ecdde9652c5a3f5dcaf81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3331","title":"getPullRequestInfo returns last modified header","createdAt":"2022-10-18T23:05:39Z"}
{"state":"Merged","mergedAt":"2022-10-20T21:50:31Z","number":3332,"body":"The previous discussion thread layout in VSCode was more spacious, which looked fine for GitHub messages where descriptions are typically longer. However, the abundance of padding made Slack messages hard to read, as they typically have shorter descriptions. \r\n\r\nThis PR attempts to address that in the following ways:\r\n\r\nAdd a narrow breakpoint where the layout with the avatar aligned-left can be applied sooner\r\n![CleanShot 2022-10-19 at 10 15 20](https://user-images.githubusercontent.com/13353189/196760046-9d1de7db-6114-4117-b551-f42eb755d8d3.gif)\r\n\r\nTighten up the line-height, margins, and padding for message lists and containers. \r\n![CleanShot 2022-10-19 at 09 56 50 2@2x](https://user-images.githubusercontent.com/13353189/196760217-1d411bcf-dc95-48f3-b38d-9652f0af70ac.png)\r\n","mergeCommitSha":"3e476434e09522907397de691641998b31ee6b8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3332","title":"Update breakpoints and tighten up message layout","createdAt":"2022-10-19T00:02:39Z"}
{"state":"Merged","mergedAt":"2022-10-21T00:25:45Z","number":3334,"body":"Initial endpoint/schemas of the service that will receive the output of client generated topics+ratings (built by @matthewjamesadam) and then grow up to serve as the basis for server generated recommended topics (using all the PR data + Slack conversations, etc.).\r\n\r\nAsking for a review and also seeing if these new definitions break anything I don't know about in the checkin pipeline. :-)\r\n\r\nFor our initial exploration, we are scoping topics to repos rather than teams, thus the endpoints hang off of /repo.","mergeCommitSha":"0006c82302e72df1069252a1f48087dd196b1123","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3334","title":"Topic Extractor Service","createdAt":"2022-10-19T03:01:58Z"}
{"state":"Merged","mergedAt":"2022-10-19T16:30:48Z","number":3335,"body":"afdssadf\r\n\r\n\uD83D\uDC4D ","mergeCommitSha":"a135feb1353967d8aa214dad4d3d8cc82dd7350a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3335","title":"Slack auth test","createdAt":"2022-10-19T16:30:38Z"}
{"state":"Merged","mergedAt":"2022-10-19T17:33:40Z","number":3336,"body":"API isn't being used yet. The implementation is doing the correct thing, returning an object instead of an array of objects, but the definition was wrong.","mergeCommitSha":"d64355e7105aa3a7c07e4cfcfe298b25345705c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3336","title":"[BREAKS API ON MAIN] Fix getSlackConfiguration response definition","createdAt":"2022-10-19T17:12:05Z"}
{"state":"Merged","mergedAt":"2022-10-19T17:35:21Z","number":3337,"mergeCommitSha":"4a8f42142074e1cb407d1df2f7d0d5f14e3eb8df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3337","title":"getSlackConfiguration returns 404 if not found","createdAt":"2022-10-19T17:25:48Z"}
{"state":"Merged","mergedAt":"2022-10-19T18:18:45Z","number":3338,"body":"Fixes UNB-690","mergeCommitSha":"3f149f3eae907a8e6ebda3c47326b81041994e22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3338","title":"Add emoji pre processor","createdAt":"2022-10-19T17:39:59Z"}
{"state":"Closed","mergedAt":null,"number":3339,"mergeCommitSha":"23b24b006952076b2473817edadaa9dfb1c9e4e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3339","title":"Updating most of the onboarding wording ","createdAt":"2022-10-19T18:50:24Z"}