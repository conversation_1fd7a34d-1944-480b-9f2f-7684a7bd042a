{"state":"Merged","mergedAt":"2022-05-09T23:01:01Z","number":1221,"body":"Item #4 from this list:\nhttps://chapter2global.slack.com/archives/C03EHLTT09X/p1652127508120699?thread_ts=1652117421.190939&cid=C03EHLTT09X","mergeCommitSha":"cd0f8011f8104c1242fdae6f45e76a318d9ed2c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1221","title":"Touch thread when message is edited to force thread refresh","createdAt":"2022-05-09T21:33:42Z"}
{"state":"Merged","mergedAt":"2022-05-11T20:31:12Z","number":1223,"body":"Adds search capabilities to VSCode.\r\n\r\nUsing existing TreeView architecture which *requires* a root node. Will look into cleaning this up in subsequent PR as it may require refactoring the TreeViews.\r\n\r\nSearch command currently works by opening the Unblocked sidebar and is dependant on the input's autofocus to gain focus. \r\n\r\nTODO: \r\n* Explicitly add logic to force focus onto input if autofocus fails (e.g. if unblocked sidebar is already open, command will not focus search bar) https://github.com/NextChapterSoftware/unblocked/issues/1224\r\n* When opening a thread from the search results, it may re-open the non-search sidebar due to how focus states are generated right now. Needs investigation. https://github.com/NextChapterSoftware/unblocked/issues/1225\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/167506609-79261172-545b-4da5-a22f-facdbc00d4af.mp4\r\n\r\n\r\n\r\nStacked on https://github.com/NextChapterSoftware/unblocked/pull/1192.\r\n\r\nInput UI will be updated once https://github.com/NextChapterSoftware/unblocked/pull/1216 is in.","mergeCommitSha":"02f6ffe235b8145d589689b7c4b3f929e992f414","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1223","title":"VScode search","createdAt":"2022-05-09T22:06:50Z"}
{"state":"Merged","mergedAt":"2022-05-10T00:10:06Z","number":1226,"mergeCommitSha":"63fdc58108652f49c3a7c48044f5724f88f115dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1226","title":"Leverage AWS template API for emails ","createdAt":"2022-05-09T22:16:11Z"}
{"state":"Merged","mergedAt":"2022-05-11T06:17:50Z","number":1227,"body":"For the team isolation effort, I'm going to start by adding simple team filters, so that we have something working even if it's not the best. I'll then start investigating row-level isolation.\r\n\r\nThis PR adds team filters for the `find` operations called by the API service.","mergeCommitSha":"9c7a1d8e9fb3dddbd453a45032d1d2b4e4c21e95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1227","title":"[Team Isolation] Scope thread, message, and repo find operations to a specific team","createdAt":"2022-05-09T22:34:20Z"}
{"state":"Merged","mergedAt":"2022-05-10T00:15:37Z","number":1228,"body":"PROBLEM:\r\n\r\nhttps://user-images.githubusercontent.com/1553313/167510544-3296d253-66bb-4c54-ba9b-71a4803ced34.mp4\r\n\r\n\r\n\r\nWebview controller is sending the rerender event *before* the `RenderWebview` has registered its event listener.\r\n\r\nProposal is to try rerendering on load. Should be no downside as the code is guarding for props.\r\n\r\nSidebar still works since the Auth event comes in periodically but depending on this causes for a sluggish UI.\r\n\r\n","mergeCommitSha":"692bacf42d7fb7ccce152a1f20a7f3a13be6629a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1228","title":"Fix VSCode rerender issue","createdAt":"2022-05-09T22:43:39Z"}
{"state":"Merged","mergedAt":"2022-05-10T00:15:30Z","number":1229,"body":"Trailing slash broke parsing on cloudfront lambda edge.","mergeCommitSha":"5b0a7f01e1badc7d088387b08d53ff07412b2fc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1229","title":"Fix prod asset links","createdAt":"2022-05-10T00:14:51Z"}
{"state":"Merged","mergedAt":"2022-01-25T19:52:46Z","number":123,"body":"https://www.notion.so/nextchaptersoftware/Client-Server-APIs-89e41ebff84d4170ace273ad1956cd09#16f7d23cc02544c3ba8b7294ad3f69dd","mergeCommitSha":"be6775396c0b72ce92595bf634a936e4a094e9a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/123","title":"Idempotent API Resource Creation","createdAt":"2022-01-25T17:25:30Z"}
{"state":"Merged","mergedAt":"2022-05-10T00:26:46Z","number":1230,"body":"- Deploy on sharedConfigs changes\r\n- Update\r\n","mergeCommitSha":"5b07df71afc220570e284a7b18d612d9081164da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1230","title":"FixGithubActions","createdAt":"2022-05-10T00:25:49Z"}
{"state":"Merged","mergedAt":"2022-05-10T01:36:06Z","number":1231,"body":"This pr does several things:\r\n1. Cleans up empty array elements from splitting path components.\r\n2. Makes url parsing a little more fault tolerant such that it handles double slashes.\r\n3. Fixes up tests (godmode api etc.)\r\n","mergeCommitSha":"bec0701b708ccf2991f8b1c5e80cee78b7fd6231","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1231","title":"Update cloudfront tests and make more fault tolerant:","createdAt":"2022-05-10T01:31:56Z"}
{"state":"Merged","mergedAt":"2022-05-10T01:47:12Z","number":1232,"mergeCommitSha":"e6c619ff0fa98dd3d6fc697400229ad169e474a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1232","title":"Fix pretty","createdAt":"2022-05-10T01:40:40Z"}
{"state":"Merged","mergedAt":"2022-05-10T14:51:21Z","number":1233,"mergeCommitSha":"ba97fdbb914e483e150ba57866f836f59930b320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1233","title":"Allow service to use more memory","createdAt":"2022-05-10T14:51:12Z"}
{"state":"Merged","mergedAt":"2022-05-10T19:26:32Z","number":1235,"body":"Collects all the push handlers in a set and only fires them once per channel notification. Previous implementation was firing fetch twice because were listening on both threads and unreads, but only a single fetch is necessary","mergeCommitSha":"516ac0cb47da5e054c660aee024af92b1edbd71b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1235","title":"Prevent duplicate notifications","createdAt":"2022-05-10T18:30:51Z"}
{"state":"Merged","mergedAt":"2022-05-11T06:57:30Z","number":1236,"body":"Improvements\r\n- initial full recalculation runs in background.\r\n- introduce initial partial recalculation that calculates point for sourcemarks of tree-view\r\n  threads only. block getting source marks for file on the partial recalculation only.  partial\r\n  recalculation triggered on initial thread store load.\r\n- avoid inadvertently rerunning initial full recalculation\r\n\r\nFuture improvements\r\nhttps://www.notion.so/nextchaptersoftware/VSCode-thread-loading-times-6bffeede04f04199a583232d30587b08#17f3ae64b8d0467381302b150689b180\r\n\r\n- [ ] get original source point from FatThreads\r\n- [ ] create sourcemark calculator much earlier in the flow, basically as soon as we have resolved the repo\r\n- [ ] triggers an update of open source editors on full recalculation completion.","mergeCommitSha":"bbc050097c52a312357ae2393b0bea594240699d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1236","title":"Optimize initial sourcemark calculation","createdAt":"2022-05-10T20:40:23Z"}
{"state":"Merged","mergedAt":"2022-05-10T22:23:45Z","number":1237,"body":"- Extended SES provider to support template List,Create,Update and Delete\r\n- Added another application plugin to create/update templates on service startup. It will also take care of removing orphaned templates\r\n- Added tests for the new functionality\r\n- Modified existing email tests to use a dynamically created template\r\n- Added two basic template files\r\n- Removed DB init from notification service startup (we don't care about db here)\r\n\r\n\r\n**Note on the `EmailTemplate` class:** \r\nThe built-in Template class provided by AWS SDK was not serializable by Kotlin. Instead of adding more serializers I just created a wrapper class to make our life easier. This class can later be extended to generate/ingest templates as well. ","mergeCommitSha":"34735a55fbe91775c332d8d97cf8f988c2a675e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1237","title":"Add support for template CRUD","createdAt":"2022-05-10T21:53:14Z"}
{"state":"Merged","mergedAt":"2022-05-10T22:41:01Z","number":1238,"mergeCommitSha":"87c7185a848149a9139385895b1d827328d4a010","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1238","title":"Move away from junit4 annotaiton","createdAt":"2022-05-10T22:27:28Z"}
{"state":"Merged","mergedAt":"2022-05-11T17:41:14Z","number":1239,"body":"Orders thread search results based what matches the query. Results are sorted in order of:\r\n\r\n- Thread author\r\n- Thread reply authors\r\n- Thread title\r\n- Thread message bodies\r\n\r\nSo search results that match the thread author are ranked higher than those that match on authors of replies, and matches on the thread title are ranked higher than matches on thread message bodies.","mergeCommitSha":"c8f414e8b51c4309352e7c35ee3a5fe572b654d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1239","title":"Add ranking and sorting of search results","createdAt":"2022-05-10T22:40:47Z"}
{"state":"Merged","mergedAt":"2022-01-25T20:21:28Z","number":124,"body":"Make sure we use openvpn to allow for kube helm chart deployment.","mergeCommitSha":"5ceedd074b300bb9a3e26dfb63af090616d63c85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/124","title":"Move Deployments to using OpenVPN","createdAt":"2022-01-25T19:15:46Z"}
{"state":"Closed","mergedAt":null,"number":1240,"mergeCommitSha":"9c8a358249ed519ddcbe860c43f9106c91392294","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1240","title":"grant notification service access to manage SES templates","createdAt":"2022-05-10T22:52:18Z"}
{"state":"Merged","mergedAt":"2022-05-13T21:05:01Z","number":1241,"body":"Setup functional web extension search.\r\n\r\nStyling is a bit off as we currently do not have designs. Will revisit once designed. https://github.com/NextChapterSoftware/unblocked/issues/1242\r\n\r\nInput box styling waiting on https://github.com/NextChapterSoftware/unblocked/pull/1216\r\n\r\nTODO: useBeforeUnload with other contentPortManagers  https://github.com/NextChapterSoftware/unblocked/issues/1243\r\n\r\n\r\nWill revert to auto filter once https://github.com/NextChapterSoftware/unblocked/pull/1264 is in as there are some changes to the base store.","mergeCommitSha":"55d7d4916fc56b252e5a1a44b49ec0246aaa210c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1241","title":"Basic web extension search","createdAt":"2022-05-10T23:00:21Z"}
{"state":"Merged","mergedAt":"2022-05-12T19:53:41Z","number":1244,"body":"Adding swift GRPC back to the build!\r\n\r\nAdds CI dependency: `brew install swift-protobuf grpc-swift`\r\n\r\nThe client (VSCode) and service (Hub) interaction is as follows:\r\n\r\n1. VSCode initiates connection to Hub (1 for thread actions, 1 for auth). Resulting streams are bidirectional\r\n2. Hub receives VSCode thread handling capabilities on thread action stream, stores for action handling\r\n3. Hub pushes token updates to VSCode via the token stream, and issues action requests via the thread action stream","mergeCommitSha":"b7377443dabf7f1d1b3987975d3aceefb350aadb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1244","title":"Add swift grpc definition for client IPC","createdAt":"2022-05-10T23:39:45Z"}
{"state":"Merged","mergedAt":"2022-05-11T05:04:01Z","number":1245,"body":"When we ingest PR threads, we don't create `ThreadUnreadModels` so that they don't appear unread in unblocked. \r\n\r\nWe want to start creating them when someone adds a reply in unblocked, so that thread participants see the thread is unread when someone adds a new message.","mergeCommitSha":"d35ac2d8b8ad279314ab91fcd5b4ea0265029fb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1245","title":"Create missing thread unreads when creating a message","createdAt":"2022-05-10T23:57:00Z"}
{"state":"Merged","mergedAt":"2022-05-11T01:51:59Z","number":1246,"body":"Was getting tired of handling naming collisions between annotations between junit4 and junit5, the latter being what we're using.","mergeCommitSha":"440d6e4e9900e647dfe8b7a1da98a3911b717bea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1246","title":"The kotlin-test framework is heavily reliant on junit4, move away","createdAt":"2022-05-11T01:08:36Z"}
{"state":"Merged","mergedAt":"2022-05-11T04:46:15Z","number":1247,"body":"Resources while debugging/test have different paths compared to when running a jar from command line.\r\n\r\nDuring debug/tests, it uses the absolute path on the filesystem.\r\n\r\nWhen using bundled resource in jar, the scheme is unique:\r\n\r\ni.e.\r\njar:/templates/blah\r\n\r\nYou have to handle those cases distinctly.\r\n\r\nTESTING:\r\n```\r\nrasharab@Rashins-MacBook-Pro libs % bash ./runServiceJAR.sh -j notificationservice-1.0.0-all.jar\r\n21:11:23,181 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-local.xml] at [jar:file:/Users/<USER>/chapter2/unblocked/build/libs/notificationservice-1.0.0-all.jar!/logback-local.xml]\r\n21:11:23,185 |-INFO in ch.qos.logback.core.joran.spi.ConfigurationWatchList@40f33492 - URL [jar:file:/Users/<USER>/chapter2/unblocked/build/libs/notificationservice-1.0.0-all.jar!/logback-local.xml] is not of type file\r\n21:11:23,208 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - debug attribute not set\r\n21:11:23,208 |-INFO in ch.qos.logback.core.joran.action.ShutdownHookAction - About to instantiate shutdown hook of type [ch.qos.logback.core.hook.DelayingShutdownHook]\r\n21:11:23,210 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]\r\n21:11:23,212 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [STDOUT]\r\n21:11:23,215 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property\r\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to WARN\r\n21:11:23,238 |-ERROR in ch.qos.logback.core.joran.action.AppenderRefAction - Could not find an appender named [LogzioLogbackAppender]. Did you define it below instead of above in the configuration file?\r\n21:11:23,238 |-ERROR in ch.qos.logback.core.joran.action.AppenderRefAction - See http://logback.qos.ch/codes.html#appender_order for more details.\r\n21:11:23,238 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [STDOUT] to Logger[ROOT]\r\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [io.ktor.auth.jwt] to TRACE\r\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.nextchaptersoftware] to DEBUG\r\n21:11:23,238 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.nextchaptersoftware.plugins.Monitoring] to DEBUG\r\n21:11:23,239 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.nextchaptersoftware.sourcemarks.git.GitRunner] to INFO\r\n21:11:23,239 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - End of configuration.\r\n21:11:23,239 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@4fbdc0f0 - Registering current configuration as safe fallback point\r\n\r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: Initializing email template: ThreadInviteTemplate.json \r\n{  } \r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: 'ThreadInviteTemplate' already exists, updating it \r\n{  } \r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: done updating :'ThreadInviteTemplate' \r\n{  } \r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: Initializing email template: RawTemplate.json \r\n{  } \r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: 'RawTemplate' already exists, updating it \r\n{  } \r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: done updating :'RawTemplate' \r\n{  } \r\n21:11:23 | INFO  | c.n.n.p.EmailTemplates: Cleaning orphaned templates \r\n```","mergeCommitSha":"17e1f2eae583e5692fee04bc3003a39499f46318","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1247","title":"Ensure we load resources correctly when bundled in final jar","createdAt":"2022-05-11T04:11:51Z"}
{"state":"Merged","mergedAt":"2022-05-11T05:43:44Z","number":1248,"body":"This fixes the: \r\n```\r\nException in thread \"DefaultDispatcher-worker-1\" kotlinx.coroutines.CoroutinesInternalError: Fatal exception in coroutines machinery for DispatchedContinuation[Dispatchers.IO, Continuation at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt)@7e45291]. Please read KDoc to 'handleFatalException' method and report this incident to maintainers\r\n\tat kotlinx.coroutines.DispatchedTask.handleFatalException(DispatchedTask.kt:144)\r\n\tat kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:115)\r\n\tat kotlinx.coroutines.internal.LimitedDispatcher.run(LimitedDispatcher.kt:42)\r\n\tat kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:95)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:749)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)\r\n\tat kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)\r\n\tSuppressed: kotlinx.coroutines.DiagnosticCoroutineContextException: [org.jetbrains.exposed.sql.transactions.experimental.TransactionCoroutineElement@2b0b3414, org.jetbrains.exposed.sql.transactions.experimental.TransactionScope@2cb8a5b7, DeferredCoroutine{Active}@46c6b9e1, Dispatchers.IO]\r\nCaused by: java.lang.IllegalStateException: Please call Database.connect() before using this code\r\n\tat org.jetbrains.exposed.sql.transactions.NotInitializedManager.currentOrNull(TransactionApi.kt:39)\r\n\tat org.jetbrains.exposed.sql.transactions.TransactionManager$Companion.currentOrNull(TransactionApi.kt:124)\r\n\tat org.jetbrains.exposed.sql.transactions.experimental.TransactionCoroutineElement.updateThreadContext(Suspended.kt:35)\r\n\tat org.jetbrains.exposed.sql.transactions.experimental.TransactionCoroutineElement.updateThreadContext(Suspended.kt:31)\r\n\tat kotlinx.coroutines.internal.ThreadContextKt.updateThreadContext(ThreadContext.kt:78)\r\n\tat kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:224)\r\n\t... 6 more\r\n```","mergeCommitSha":"267260d2368738cc38431a246b8639ec727c37e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1248","title":"this is to fix the health probes which I think still depend on DB","createdAt":"2022-05-11T05:19:04Z"}
{"state":"Merged","mergedAt":"2022-05-11T16:45:45Z","number":1249,"body":"Multiple people @davidkwlam @mahdi-torabi have been burrned by implicit health checkers.\r\nI have decided that their pain needs to be addressed.","mergeCommitSha":"a3712b99c1c5f85453478955ef7d8799e7f7b0d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1249","title":"Make health checkers explicit","createdAt":"2022-05-11T16:25:15Z"}
{"state":"Merged","mergedAt":"2022-01-26T20:55:22Z","number":125,"body":"- just stubs for now\r\n- implementation comes in follow up","mergeCommitSha":"0bfa85a87e591c344b76e3d6c7458b69c3ae0248","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/125","title":"Identity related APIs","createdAt":"2022-01-25T21:01:14Z"}
{"state":"Merged","mergedAt":"2022-05-11T21:29:52Z","number":1251,"body":"Add Development prod script for web extension\r\n\r\nFixes two bugs:\r\n1. When trying to render source points, we tried attaching to DOM too early. Add timeout to wait for GH rendering first.\r\n2. Fetch filePath from DOM instead of URL due to issues with branch names.","mergeCommitSha":"a217ad36559ca8246d151c389aee0f9454569dd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1251","title":"Web extension source point bugs","createdAt":"2022-05-11T17:02:23Z"}
{"state":"Merged","mergedAt":"2022-05-11T18:01:46Z","number":1252,"mergeCommitSha":"9ccb5a7498b41def2ac21ef5b02fbf1bd5a277f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1252","title":"Clean up dependencies","createdAt":"2022-05-11T17:45:47Z"}
{"state":"Merged","mergedAt":"2022-05-11T18:20:50Z","number":1253,"body":"We don't need these columns anymore. Let's first make them nullable then once deployed we can drop them.","mergeCommitSha":"f05f377d98220b91ffeca49fcaf3d620689f5141","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1253","title":"Make ThreadSearchModel.modelId and ThreadSearchModel.modelType nullable","createdAt":"2022-05-11T17:47:16Z"}
{"state":"Merged","mergedAt":"2022-05-11T19:00:36Z","number":1254,"body":"Now that the columns are nullable and https://github.com/NextChapterSoftware/unblocked/pull/1253 has deployed to all environments, it's safe to remove these columns from our models. \r\n\r\nNote that this will not drop the columns from the database table because exposed's data migration feature doesn't drop columns. That needs to happen manually.","mergeCommitSha":"4cbf5c54478c265707439d021f6a828efc0ae77c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1254","title":"Drop ThreadSearchModel.modelId and ThreadSearchModel.modelType columns","createdAt":"2022-05-11T18:43:16Z"}
{"state":"Merged","mergedAt":"2022-05-12T18:02:10Z","number":1255,"body":"This PR switches our workflow to the new in-house GitHub action for spot instances. \r\nIt's currently using the MaxPerformance scheduling which means we try to get the largest available spot instance for the cost of an on-demand instance","mergeCommitSha":"51d7872a3be6fd37090df584a5f0720369c55750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1255","title":"Testing Spot EC2 action runner (WIP) Do NOT merge","createdAt":"2022-05-11T19:15:48Z"}
{"state":"Merged","mergedAt":"2022-05-11T20:05:40Z","number":1256,"mergeCommitSha":"1b66f68a50d2c9414ea6beea02f3489a683fd05a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1256","title":"Add pricing permissions for deploybot","createdAt":"2022-05-11T19:54:28Z"}
{"state":"Merged","mergedAt":"2022-05-11T20:59:25Z","number":1258,"body":"This job will eventually ingest comments from pull requests. Adding this now to test that we can consume from the queue before adding our logic.","mergeCommitSha":"0160b007ca092220a305fc8428db7416fa865847","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1258","title":"Set up a new job to read from pr_ingestion queue","createdAt":"2022-05-11T20:36:37Z"}
{"state":"Merged","mergedAt":"2022-05-11T21:56:54Z","number":1259,"body":"To simplify the logic","mergeCommitSha":"3ffd137747044578d1ad0f60ccceece09256c000","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1259","title":"Use insertIgnore in GitHubModelTransformers.createIdentities","createdAt":"2022-05-11T21:11:13Z"}
{"state":"Merged","mergedAt":"2022-01-26T00:42:06Z","number":126,"body":"Should we also run VSCode and Web workflows on `api/` changes @matthewjamesadam ?","mergeCommitSha":"48b1629dec6da4ae74298970ffbf213a61f81c3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/126","title":"Run CI on api/ changes","createdAt":"2022-01-25T21:14:54Z"}
{"state":"Merged","mergedAt":"2022-05-12T22:08:12Z","number":1260,"mergeCommitSha":"5936386efb370ff6928268adee01e7747e919aec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1260","title":"Get more data for invite emails","createdAt":"2022-05-11T22:57:06Z"}
{"state":"Merged","mergedAt":"2022-05-12T00:01:06Z","number":1261,"body":"We'll need this for backing off pull request ingestion when we get close to the limit\r\n\r\n![CleanShot 2022-05-11 at 16 07 00@2x](https://user-images.githubusercontent.com/1924615/167961676-357e0708-9003-4ffd-93d5-a9d885b8ae6f.png)\r\n","mergeCommitSha":"43194117485d774ce5ee04b2e71a9fe24f24a351","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1261","title":"Show GitHub rate limit on admin team page","createdAt":"2022-05-11T23:07:35Z"}
{"state":"Merged","mergedAt":"2022-05-13T00:17:05Z","number":1262,"body":"Verified using Evan grpc CLI","mergeCommitSha":"6a491d078fb9bce00eb74eb6ada6817955bd7292","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1262","title":"Implement hub grpc service","createdAt":"2022-05-11T23:45:06Z"}
{"state":"Merged","mergedAt":"2022-05-12T00:23:16Z","number":1263,"body":"Needed for https://github.com/NextChapterSoftware/unblocked/pull/1260","mergeCommitSha":"e718e400fa44d12e941d8abf41c3ebd939beeb0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1263","title":"Add getAnchorSourceMarkOriginalSourcePointForThread","createdAt":"2022-05-12T00:03:39Z"}
{"state":"Merged","mergedAt":"2022-05-12T23:35:28Z","number":1264,"body":"Revert search behaviour to auto result. The original reason to move away from auto result was a jarring loading screen in-between searching instances. Removed search loading state for better flow UX.\r\n\r\nMove debouncer logic from VC to search store.\r\n","mergeCommitSha":"fc27cde8789b5c61e45f74fbe145461f970dbd42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1264","title":"Update search behaviour","createdAt":"2022-05-12T00:20:15Z"}
{"state":"Merged","mergedAt":"2022-05-13T20:21:29Z","number":1265,"body":"Has some temporary code in here as a hack until we move to fat thread models. Just so people can play with it\r\n\r\nThis PR also moves the Hub to use Prod by default","mergeCommitSha":"1a5aa85306e556c5586e94cafe82ffac64edd53c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1265","title":"Load thread in dashboard on click","createdAt":"2022-05-12T00:46:49Z"}
{"state":"Merged","mergedAt":"2022-05-12T01:51:50Z","number":1266,"body":"Got basic configuration library up for connecting to honeycomb and adding proper shutdown handlers to flush data.\r\n","mergeCommitSha":"dc34762385162b9325b23eb204faca8cfbd6a2da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1266","title":"Add basic trace library","createdAt":"2022-05-12T01:22:30Z"}
{"state":"Merged","mergedAt":"2022-05-12T06:06:30Z","number":1267,"body":"Part of the work to move towards event-based PR ingestion","mergeCommitSha":"78253a40ca0bff3fdd689dcfb0001ea12a24f0f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1267","title":"Emit pull request ingestion events","createdAt":"2022-05-12T05:43:14Z"}
{"state":"Merged","mergedAt":"2022-05-13T05:52:02Z","number":1268,"body":"### changes\r\n- order by unread then last message created time -- so that clients do not have to fetch all threads\r\n- limit and cursor pagination -- supports paging from _any_ thread in the thread collection\r\n- fat thread results -- thread, unread, participant IDs, repo ID, messages, sourcemark, and original source points\r\n- filters out archived\r\n- filters by repos\r\n\r\n### next\r\n- new apis in https://github.com/NextChapterSoftware/unblocked/pull/1271","mergeCommitSha":"1fb1af0d95b9360e97d4dbe964cdf4e33a7e6b33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1268","title":"Query to support My Threads in tree view","createdAt":"2022-05-12T07:56:03Z"}
{"state":"Merged","mergedAt":"2022-05-12T17:20:38Z","number":1269,"mergeCommitSha":"040dc803453847fbd4525e7e37458a0e472923ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1269","title":"update","createdAt":"2022-05-12T17:06:43Z"}
{"state":"Merged","mergedAt":"2022-01-26T21:48:46Z","number":127,"body":"Adds a skeleton for a WYSIWYG editor using Slate.js.  This is a totally unstyled UI and only supports the following:\r\n\r\n* Bold/italic text, through either a button, a hotkey (Cmd+B), or entering in markdown-style `*something*`.\r\n* Quote blocks\r\n* Code blocks\r\n* Image blocks (though this is just a proof-of-concept of how it could work)\r\n\r\nThere are many things left to do:\r\n* Add more features in the [editor UI doc](https://www.notion.so/nextchaptersoftware/Editor-UI-c226822470fe4e41afa65ee68c9ee878)\r\n* Figure out how to test this thing\r\n* Add styling support so VSCode / web can be styled correctly\r\n* Find a way to delegate platform-supplied behaviour (for example, syntax hilighting)","mergeCommitSha":"5641d7448cd9f4f645f5ddbf88e92f4b2b9cf8a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/127","title":"MessageEditor basics","createdAt":"2022-01-25T21:36:12Z"}
{"state":"Merged","mergedAt":"2022-05-12T22:48:13Z","number":1270,"body":"The implementation is simple: Continue to run the poller once a second, but only actually query for the channels that we need to on each poll.  This could be made more precise but is probably fine for now.\r\n\r\nThe unit tests are pretty messy.  I'd like to upgrade us to Jest 28 which would let us use fake timers to write these tests synchronously, so they would be 100% precise and deterministic.  That will come later too.","mergeCommitSha":"a87276e3be90896356a9db9a7ced9ffebd83f714","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1270","title":"Add per channel poll rate to ChannelPoller","createdAt":"2022-05-12T20:55:10Z"}
{"state":"Merged","mergedAt":"2022-05-12T22:39:13Z","number":1271,"body":"- Thread listing API return `ThreadInfo` model that contains Thread, Message, Participants, SourceMarks, etc.\n- Changed `getThread` API to return `ThreadInfo` too. Technically a breaking change, but no client is using currently so fine.\n- Introduce cursor-based paging on new thread listing APIs, and limits.\n- Deprecated `getThreads`, which we will remove once all client have transitioned to new thread listing APIs.\n- Deprecated `participants` on `Thread` as it is duplicative and not needed where `Thread` object is now used (archiving, restoring, updating).","mergeCommitSha":"d08ae2a23cbbed474474f86f3f9a11ee0ecf4c40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1271","title":"New thread listing APIs","createdAt":"2022-05-12T20:59:56Z"}
{"state":"Merged","mergedAt":"2022-05-17T20:48:18Z","number":1272,"body":"Setup GRPC for HubClient.\r\n\r\nInfluenced by https://github.com/NextChapterSoftware/unblocked/blob/af73f48da2675a984c67577dc7be0171c2d13191/vscode/src/sourcemark/SourceMarkAgentMonitor.ts\r\n\r\n","mergeCommitSha":"5f5615fba05dd8f2371144a96fac136704042ca3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1272","title":"Setup GRPC Client and AUth","createdAt":"2022-05-12T22:45:37Z"}
{"state":"Merged","mergedAt":"2022-05-12T23:37:04Z","number":1273,"mergeCommitSha":"8241471d10332266c9039a16e8c0b2bc4ccce8df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1273","title":"Clean up duplicated code","createdAt":"2022-05-12T22:57:17Z"}
{"state":"Merged","mergedAt":"2022-05-13T03:19:07Z","number":1274,"mergeCommitSha":"d9c574e885b7f7273208f2bb53f3c06aa0af77ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1274","title":"Pull out reused code and add tests","createdAt":"2022-05-13T00:10:35Z"}
{"state":"Merged","mergedAt":"2022-05-13T02:28:54Z","number":1275,"body":"For now just debugging in admin web, but will be leveraged for directing users to owner-specific tasks.","mergeCommitSha":"39ff13424f000d9af611697c51ffe36ed89736a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1275","title":"Persist GitHub Org role","createdAt":"2022-05-13T01:35:44Z"}
{"state":"Merged","mergedAt":"2022-05-13T02:38:30Z","number":1276,"body":"<img width=\"1374\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/168199368-ddbc90ef-09e7-4967-b6fc-3ad5c2d2009c.png\">\r\n","mergeCommitSha":"ef083e57b310423808913a960440a7c6d318b691","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1276","title":"Progress bar component - use for rate limit","createdAt":"2022-05-13T02:27:41Z"}
{"state":"Merged","mergedAt":"2022-05-13T06:35:16Z","number":1277,"body":"Plan:\r\nhttps://chapter2global.slack.com/archives/C02GEN8LFGT/p1652393321894119","mergeCommitSha":"1607020dd76764d15416874f0b120e4a13c609b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1277","title":"Archived threads API","createdAt":"2022-05-13T06:04:10Z"}
{"state":"Merged","mergedAt":"2022-05-13T17:55:37Z","number":1278,"mergeCommitSha":"40a928a3d5c9c562cc114c520e0eba2af7ec4073","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1278","title":"Remove testAuthentication api operation","createdAt":"2022-05-13T06:34:26Z"}
{"state":"Merged","mergedAt":"2022-05-13T07:39:44Z","number":1279,"body":"Reverts NextChapterSoftware/unblocked#1275\r\n\r\nBUG: Removes owners from team, breaking auth for pretty much everyone.","mergeCommitSha":"84dd0833c9aa96524fc1e0fd3588f56b3068eb1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1279","title":"Revert \"Persist GitHub Org role\"","createdAt":"2022-05-13T07:38:01Z"}
{"state":"Merged","mergedAt":"2022-01-25T22:23:07Z","number":128,"body":"- Added Calico installation instructions (Also installed it on Dev cluster)\r\n- Added default deny policy yaml (not applied yet)\r\n- Created helm template for network policy\r\n\r\nThis is the yaml generated by helm template command. Some values are missing because template command does not have release names but the rest look to be in order. \r\n```yaml\r\n---\r\n# Source: service/templates/network-policy.yaml\r\nkind: NetworkPolicy\r\napiVersion: networking.k8s.io/v1\r\nmetadata:\r\n  name: RELEASE-NAME-service\r\n  labels:\r\n    helm.sh/chart: service-0.1.0\r\n    app.kubernetes.io/name: service\r\n    app.kubernetes.io/instance: RELEASE-NAME\r\n    app.kubernetes.io/version: \"1.16.0\"\r\n    app.kubernetes.io/managed-by: Helm\r\n  annotations:\r\n    external-dns.alpha.kubernetes.io/hostname: RELEASE-NAME.us-west-2.dev.usecodeswell.com\r\n    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http\r\n    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:us-west-2:129540529571:certificate/9f9742d5-3454-43ce-a7e2-c7bae0c36bc5\r\n    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: https\r\nspec:\r\n  podSelector:\r\n    matchLabels:\r\n      app.kubernetes.io/name: service\r\n      app.kubernetes.io/instance: RELEASE-NAME\r\n  ingress:\r\n  - from:\r\n    - podSelector:\r\n        matchLabels:\r\n          app.kubernetes.io/name: service\r\n          app.kubernetes.io/instance: RELEASE-NAME\r\n    ports:\r\n    - port: 8080\r\n  egress:\r\n  - to:\r\n    - podSelector:\r\n        matchLabels:\r\n          app.kubernetes.io/name: service\r\n          app.kubernetes.io/instance: RELEASE-NAME\r\n```","mergeCommitSha":"e70a13e0c3bb78e536a34287f056e595b89c134e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/128","title":"Add Calico to EKS and network policy for API service","createdAt":"2022-01-25T22:11:43Z"}
{"state":"Merged","mergedAt":"2022-05-14T05:23:17Z","number":1280,"body":"Just a refactor of the PR ingestion logic to speed ingestion up.\r\n\r\n(sorry for the big PR)","mergeCommitSha":"ffae3c806b7ff191b7c796fda14b7ae7943cd254","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1280","title":"[Performance] Pull request ingestion is event-based","createdAt":"2022-05-13T16:28:51Z"}
{"state":"Merged","mergedAt":"2022-05-13T18:15:50Z","number":1281,"body":"# Unblocked Video App\r\n\r\n ## Setup]\r\nRun `make setup`, then open `Unblocked Video App.xcodeproj`","mergeCommitSha":"c12517a0fea7ed6c69a68c03704ce52d09f4d41a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1281","title":"Update the readme with project setup steps","createdAt":"2022-05-13T17:28:02Z"}
{"state":"Merged","mergedAt":"2022-05-13T19:46:58Z","number":1282,"body":"Re-apply the previously reverted PR #1279, which failed because it marked all of the Org owners as deactivated.\r\n\r\nThe fix https://github.com/NextChapterSoftware/unblocked/pull/1282/commits/e009b31ba47bec50cdef03fdda0f76bb636bd169 is to process both the members and owners in one shot.","mergeCommitSha":"1f8010f5688afdcdbd71c491cc08c3e7f953612d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1282","title":"Persist org role -- with bug fix","createdAt":"2022-05-13T17:29:34Z"}
{"state":"Merged","mergedAt":"2022-05-13T18:25:33Z","number":1283,"body":"Never used or needed to be exposed.","mergeCommitSha":"0d5d1d96060d1b27f2330d4e240d6902edb4025d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1283","title":"Remove sourceMarkGroupId from thread API response","createdAt":"2022-05-13T18:06:49Z"}
{"state":"Merged","mergedAt":"2022-05-13T18:20:26Z","number":1284,"mergeCommitSha":"184fdc54eee880aa43ad83f1d5d640d5a4c843ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1284","title":"Add basic honeycomb framework and etsts","createdAt":"2022-05-13T18:07:46Z"}
{"state":"Merged","mergedAt":"2022-05-13T18:29:34Z","number":1285,"body":"Client-side machinery assumes models have an id property:\nhttps://chapter2global.slack.com/archives/C03EHLTT09X/p1652464840003519","mergeCommitSha":"fc59ccc94958e16a75cfc4ee78da9ce6a6aeecf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1285","title":"Add thread ID to ThreadInfo","createdAt":"2022-05-13T18:18:07Z"}
{"state":"Merged","mergedAt":"2022-05-13T18:30:29Z","number":1286,"mergeCommitSha":"b663b11789a63ac76a5750a8ede145b9b00e4d13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1286","title":"Disable flaky test","createdAt":"2022-05-13T18:30:25Z"}
{"state":"Merged","mergedAt":"2022-05-13T19:28:01Z","number":1287,"mergeCommitSha":"6545d372a94d6e4b3dde78da4d686ac9a869caa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1287","title":"update thread invites email template","createdAt":"2022-05-13T18:43:51Z"}
{"state":"Closed","mergedAt":null,"number":1288,"mergeCommitSha":"5109c6ed3489fc09c48604eef961ec5da7a750d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1288","title":"update","createdAt":"2022-05-13T18:45:53Z"}
{"state":"Merged","mergedAt":"2022-05-13T20:37:00Z","number":1289,"body":"In the generated TS code, `ThreadInfo.id` was optional.  I suspect this is an outcome of using the `allOf` trick here....?\r\n\r\nNot sure if there's a better way to deal with this, I'm open to suggestions.","mergeCommitSha":"9e3b3611a2df85f65b1f073966464199f7272ace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1289","title":"Make ThreadInfo.id non-optional","createdAt":"2022-05-13T19:02:55Z"}
{"state":"Closed","mergedAt":null,"number":129,"body":"Prep for loading page. Not in any designs.\r\n\r\n![CleanShot 2022-01-25 at 15 12 03](https://user-images.githubusercontent.com/1553313/151075324-46831cc4-0d61-4d0e-8be5-cb344b78d80d.gif)\r\n\r\n","mergeCommitSha":"06c86dba2155c38b39e5ccfa8f9d3e09a7bec2d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/129","title":"Spinner","createdAt":"2022-01-25T23:12:42Z"}
{"state":"Merged","mergedAt":"2022-05-13T20:03:59Z","number":1290,"mergeCommitSha":"00afd763dff8f35daa9542b36ca8f5e52ebf8f66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1290","title":"change escape characters","createdAt":"2022-05-13T19:52:48Z"}
{"state":"Merged","mergedAt":"2022-05-13T21:00:44Z","number":1291,"body":"Github removed the data-permalink-href property on the selector...\r\n\r\nMove to basic href.","mergeCommitSha":"a7ecd224570b64b5df3eab63ed8f9f503ef81662","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1291","title":"Fix Create Discussion in Web Extension","createdAt":"2022-05-13T20:38:47Z"}
{"state":"Merged","mergedAt":"2022-05-13T20:57:33Z","number":1292,"mergeCommitSha":"5d90bc4119bcb1e6c48c056abbca4b9a93d89544","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1292","title":"Update escaping","createdAt":"2022-05-13T20:46:57Z"}
{"state":"Closed","mergedAt":null,"number":1293,"mergeCommitSha":"567a300d77bd244e5727575b9ee8757d31d6125f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1293","title":"trying single slash escapes","createdAt":"2022-05-13T20:47:51Z"}
{"state":"Merged","mergedAt":"2022-05-13T21:50:47Z","number":1294,"body":"And a logout action to the token request","mergeCommitSha":"eef51fdbaa7a349cf1b3563713d8067257cce8f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1294","title":"Add nullibility to tokens","createdAt":"2022-05-13T21:10:05Z"}
{"state":"Merged","mergedAt":"2022-05-13T22:14:29Z","number":1295,"mergeCommitSha":"a3b9c97111ccb1af78e6b095b21e6c80d8159677","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1295","title":"Send emails to recipients","createdAt":"2022-05-13T21:58:35Z"}
{"state":"Merged","mergedAt":"2022-05-13T22:47:00Z","number":1296,"mergeCommitSha":"50a761fe9c59de02f3cf5fe95d3b43f8a95adb6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1296","title":"Add grpc dependency","createdAt":"2022-05-13T22:38:42Z"}
{"state":"Merged","mergedAt":"2022-05-14T01:37:05Z","number":1297,"body":"Hooked up these:\n - `getThread`\n - `getThreadsForMe`\n\nThese will come in follow up PR:\n - `getThreadsRecommended`\n - `getThreadsArchived`","mergeCommitSha":"2265349d3153dcb28feb2c8cdc9314337d3f4878","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1297","title":"Plumbing new thread APIs to thread info store","createdAt":"2022-05-13T23:12:29Z"}
{"state":"Merged","mergedAt":"2022-05-17T00:22:51Z","number":1298,"body":"Updating to match App Store connect.","mergeCommitSha":"e874d77cbfec1734fcdcbbb32d93c488a48d2d5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1298","title":"Update web extension version","createdAt":"2022-05-13T23:31:38Z"}
{"state":"Merged","mergedAt":"2022-05-13T23:40:20Z","number":1299,"mergeCommitSha":"b91c864dd6b1bf588ff2c10aff4fc671592cac43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1299","title":"Add image caching for avatars","createdAt":"2022-05-13T23:38:41Z"}
{"state":"Merged","mergedAt":"2021-12-15T17:18:46Z","number":13,"mergeCommitSha":"8ca11287a2fbbc1d2b57d16ab3052d18e7431370","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/13","title":"Web primitives","createdAt":"2021-12-15T00:31:43Z"}
{"state":"Merged","mergedAt":"2022-01-31T17:04:44Z","number":130,"body":"Setup spinner for loading screen. Currently not in designs.\r\n\r\n![CleanShot 2022-01-25 at 15 12 03](https://user-images.githubusercontent.com/1553313/151075767-4cbe7725-63fa-4c8d-a535-251f490fc09b.gif)\r\n\r\n","mergeCommitSha":"4ca7dd1914c8d290d2e757a042accf66d69f9bdf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/130","title":"Spinner","createdAt":"2022-01-25T23:17:04Z"}
{"state":"Merged","mergedAt":"2022-05-14T18:26:55Z","number":1300,"mergeCommitSha":"7071d5bff943b37a42be77ec01af19b739c0403c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1300","title":"Support if-modified-since on getThread operation","createdAt":"2022-05-14T01:27:51Z"}
{"state":"Merged","mergedAt":"2022-05-16T22:43:15Z","number":1301,"body":"Login action is wired up, all other actions are not. We had a brief discussion about intercom on Friday - probably needs to go to the dashboard.\r\n\r\n### Light\r\n\r\n<img width=\"528\" alt=\"CleanShot 2022-05-14 at 14 01 42@2x\" src=\"https://user-images.githubusercontent.com/858772/168448262-e1f277c0-7d64-434c-9047-e4754910058a.png\">\r\n\r\n<img width=\"579\" alt=\"CleanShot 2022-05-14 at 14 08 37@2x\" src=\"https://user-images.githubusercontent.com/858772/168448276-4110d8cf-b016-445f-87b3-a659f350fcf2.png\">\r\n\r\n![CleanShot 2022-05-14 at 14 10 45](https://user-images.githubusercontent.com/858772/168448296-2d3d7c2f-4f9b-449f-9590-266c2c734b1b.gif)\r\n\r\n<img width=\"538\" alt=\"CleanShot 2022-05-14 at 14 27 16@2x\" src=\"https://user-images.githubusercontent.com/858772/168448649-768e0aac-eb2f-484f-a296-4bce682fe587.png\">\r\n\r\n\r\n\r\n### Dark\r\n<img width=\"570\" alt=\"CleanShot 2022-05-14 at 14 01 27@2x\" src=\"https://user-images.githubusercontent.com/858772/168448301-57150960-3171-4f88-9534-2e7feba162f4.png\">\r\n\r\n<img width=\"585\" alt=\"CleanShot 2022-05-14 at 14 08 46@2x\" src=\"https://user-images.githubusercontent.com/858772/168448304-306c04a0-aaaf-4734-9604-9b34469f7d67.png\">\r\n\r\n![CleanShot 2022-05-14 at 14 09 48](https://user-images.githubusercontent.com/858772/168448307-9700824b-1346-4bbd-8866-692b9a01b3e2.gif)\r\n\r\n<img width=\"559\" alt=\"CleanShot 2022-05-14 at 14 27 08@2x\" src=\"https://user-images.githubusercontent.com/858772/168448652-ddad2024-562a-49a0-a710-f12003b183f2.png\">\r\n\r\n\r\n","mergeCommitSha":"9777b319e7e0272f24a5c6271e9c5091213b0631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1301","title":"Onboarding Views","createdAt":"2022-05-14T16:30:57Z"}
{"state":"Merged","mergedAt":"2022-05-15T05:36:18Z","number":1302,"body":"- Clients should use the PR comment url instead of the PR url when\r\n  the discussion is sourced from a PR comment.\r\n- Also fix bug where we were returning deleted messages in API.","mergeCommitSha":"d6e04b5a6a252d75bafb80e5e8ee553407279338","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1302","title":"Populate PR comment url on thread","createdAt":"2022-05-15T02:53:10Z"}
{"state":"Merged","mergedAt":"2022-05-20T23:55:06Z","number":1303,"body":"Note: Can review, but merge only when clients are ready to move on.\r\n\r\nTODO\r\n- [x] `shared/stores/ThreadStore.ts` needs to be rewritten / removed @matthewjamesadam ","mergeCommitSha":"2d93f818afacbb14f6b9ba4014b7cebfb4143346","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1303","title":"Obsolete getThreads and Threads.participants","createdAt":"2022-05-15T03:13:42Z"}
{"state":"Merged","mergedAt":"2022-05-17T05:20:05Z","number":1304,"body":"## Summary\r\n\r\nThis turns out to be quite the ceremony:\r\n\r\n1. Create launcher app target\r\n2. Set launcher target to run in background only\r\n3. Set \"Skip Install\" build step in launcher target\r\n4. Embed launcher app in main app at `Contents/Library/LoginItems` using copy files build script phase in main app target\r\n5. Make sure app is signed with Developer ID or notarized (launcher will fail if not)\r\n6. When main app launches, call `SMLoginItemSetEnabled(<launcher app bundle id>)`\r\n7. In launcher app, do some detection work to see if main app is running, if not:\r\n8. Grab launch app bundle path and pop up 3 levels to `Contents`, then down to main app binary\r\n9. Call `NSWorkspace.openApplication()`\r\n10. Kill launcher","mergeCommitSha":"19ebfe1394e1753d4f803d1ae0a278679b65d03c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1304","title":"Starts the hub on login","createdAt":"2022-05-15T22:59:20Z"}
{"state":"Merged","mergedAt":"2022-05-16T23:35:12Z","number":1305,"body":"* Detect when badges aren't enabled (ie, user has not enabled proposed APIs for our extension) -- this can be detected when setting the badge fails\r\n* When this happens, show a UI that tells them what to do\r\n\r\n@benedict-jw this is just a temporary UI while our badge API is waiting for final approval.  Wording suggestions welcome.\r\n\r\n![CleanShot 2022-05-15 at 20 19 16](https://user-images.githubusercontent.com/2133518/168514438-b994358a-aa19-4700-ac32-5303e3ca8863.gif)\r\n","mergeCommitSha":"71588bfd344a24f228e378e91b437e11c1f9156e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1305","title":"UI to guide people to enable badge UI","createdAt":"2022-05-16T03:22:16Z"}
{"state":"Merged","mergedAt":"2022-05-16T20:59:14Z","number":1306,"body":"I need to add a test for the `PullRequestIngestionJob` but the basic idea here is to check whether we're close to our rate limit and delay ingesting PRs for the team until it resets. We'll do that by setting the timeout on the message to ~ the reset time so that it'll be released to be consumed once the rate limit resets.\r\n\r\nFor now I've set the threshold for delay at 1000 remaining requests.","mergeCommitSha":"3b0450a6f4c0cebe25b357e61eebf684feacc13b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1306","title":"Delay PR ingestion until rate limit resets","createdAt":"2022-05-16T16:17:55Z"}
{"state":"Merged","mergedAt":"2022-05-16T17:29:59Z","number":1307,"body":"Part 1 of adopting ThreadInfos.  This is a replacement for DataCacheStream, most of the code is a slightly modified copy of the DataCacheStream code.  The differences are:\r\n* ApiDataStream makes no assumption about the data it contains (ie, doesn't hold an array, and doesn't insist that its elements have an `id` property)\r\n* ApiDataStream only returns individual returned API elements, not a fully-composed list.  I will add a helper later that makes composing an array of final elements easy, for data where this makes sense.\r\n\r\nSome notes:\r\n* This isn't being used anywhere.  Next PR will use this in a few places.\r\n* I had to disable some tests to get this in.  I will enable them as the features they're tied to are enabled.\r\n","mergeCommitSha":"c10328edbd83637c5b64c0bd1447347cb26bc75a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1307","title":"Add ApiDataStream","createdAt":"2022-05-16T16:20:19Z"}
{"state":"Closed","mergedAt":null,"number":1308,"mergeCommitSha":"bc6e2237ae83320e515b366f73eebdcf4ffd1ea2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1308","title":"[WiP] Add onboarding status to Person model","createdAt":"2022-05-16T16:23:17Z"}
{"state":"Merged","mergedAt":"2022-05-19T16:06:49Z","number":1309,"body":"Caveats:\r\n- 'Archived' API is not yet implemented, so that UI is removed\r\n- Search API is not yet implemented, so that UI is disabled","mergeCommitSha":"aaad15f12faf12d2f01f7f30f5a329894c07a1d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1309","title":"Use ThreadInfo API for dashboard","createdAt":"2022-05-16T18:46:39Z"}
{"state":"Merged","mergedAt":"2022-01-25T23:21:33Z","number":131,"body":"Reverts Chapter2Inc/codeswell#128","mergeCommitSha":"0a018a6ecf295b5e68541e4f8d052506baee0b28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/131","title":"Revert \"Add Calico to EKS and network policy for API service\"","createdAt":"2022-01-25T23:17:31Z"}
{"state":"Merged","mergedAt":"2022-05-16T20:15:00Z","number":1311,"body":"We add the ability to trace ALL ktor server calls through plugin interception.\r\n","mergeCommitSha":"24668a07ffb44730d37b23e560ba1ad954772158","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1311","title":"Add ktor server tracing","createdAt":"2022-05-16T20:03:04Z"}
{"state":"Merged","mergedAt":"2022-05-17T17:54:47Z","number":1313,"body":"Fetch recent projects from VSCode.\r\n\r\nHacky method of grabbing recent workspaces & folders from an internal `storage.json` file which represents the menu bar state.\r\n\r\nOriginal intention was to grab data from internal DB but ran into issues running sqlite3.\r\n\r\nNo UI at the moment, just informational popup from command.\r\n<img width=\"602\" alt=\"CleanShot 2022-05-16 at 14 34 30@2x\" src=\"https://user-images.githubusercontent.com/1553313/168686634-9adecf2b-7a03-4d9e-9ea1-6cf4590d8e00.png\">\r\n\r\n","mergeCommitSha":"c671ea8d84b7ec3d27ebbb4cc61e33411e739847","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1313","title":"VSCode recent projects","createdAt":"2022-05-16T21:36:55Z"}
{"state":"Merged","mergedAt":"2022-05-17T03:10:26Z","number":1314,"body":"Sort by {rank, lastMessageCreatedAt}","mergeCommitSha":"497e66d520bc34ad8caaf1ecb78d441ed33f43ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1314","title":"Tiebreak recommended threads on lastMessageCreatedAt","createdAt":"2022-05-16T22:40:51Z"}
{"state":"Merged","mergedAt":"2022-05-17T04:08:58Z","number":1315,"body":"Small PR to dump the team members view. Placeholder text for now","mergeCommitSha":"1716f16ab52288a14dab40ae026ac4f00f8650b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1315","title":"Peter remove team view","createdAt":"2022-05-16T22:42:11Z"}
{"state":"Merged","mergedAt":"2022-05-17T02:11:41Z","number":1316,"mergeCommitSha":"917acd4db1e5abe17838d871c354082a1fcdc1a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1316","title":"Fix expiration logic","createdAt":"2022-05-16T23:36:25Z"}
{"state":"Merged","mergedAt":"2022-05-17T18:36:53Z","number":1317,"body":"Add links to file from anchor source point in web extension.\r\n\r\nCurrently navigates existing tab to new URL and highlights relevant code.\r\n<img width=\"711\" alt=\"CleanShot 2022-05-16 at 17 09 20@2x\" src=\"https://user-images.githubusercontent.com/1553313/168701994-385ac708-90fd-4849-956b-a9bcba54c62d.png\">\r\n<img width=\"877\" alt=\"CleanShot 2022-05-16 at 17 09 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/168701997-49e37bef-bccf-4bae-8ab1-84e540d5b6e3.png\">\r\n\r\n","mergeCommitSha":"911e5603b84b6190b252b84039e165938ae144c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1317","title":"Anchor links in web extension","createdAt":"2022-05-17T00:11:53Z"}
{"state":"Merged","mergedAt":"2022-05-17T17:40:00Z","number":1318,"body":"TODO in next PR...\r\n - [ ] refactor store, needed for search and pusher, by splitting in phases:\r\n      1. input validation (move to API layer)\r\n      1. get thread IDs only, using bespoke predicate\r\n      1. decorate\r\n      1. re-sort\r\n","mergeCommitSha":"4820b5d8d26562032b26407b17ca5cda2d2dadfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1318","title":"Implement getThreadsArchived API","createdAt":"2022-05-17T00:32:52Z"}
{"state":"Merged","mergedAt":"2022-05-17T16:00:42Z","number":1319,"mergeCommitSha":"8a1e4a9299ad64257278196b3266e4362dae960f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1319","title":"AddTelemetryForJdbcCalls","createdAt":"2022-05-17T01:29:01Z"}
{"state":"Closed","mergedAt":null,"number":132,"mergeCommitSha":"4524e008bd4dc976b3eb87ef2eb54b07f5d8cea6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/132","title":"update","createdAt":"2022-01-25T23:42:42Z"}
{"state":"Merged","mergedAt":"2022-05-17T04:37:26Z","number":1320,"body":"fixed ...\r\n\r\nhttps://user-images.githubusercontent.com/1798345/168725408-b170b49a-3c2d-4e69-9dc7-8dea83642535.mov\r\n\r\n","mergeCommitSha":"d00441a1154c91eb4fa095fe6718e97a38f0ff3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1320","title":"Fix participant thread count in Hub","createdAt":"2022-05-17T03:54:28Z"}
{"state":"Closed","mergedAt":null,"number":1321,"mergeCommitSha":"1ff37a2122511a673324043b984cfd1ebd63f44c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1321","title":"Send notification emails to participants on thread creation","createdAt":"2022-05-17T17:22:10Z"}
{"state":"Merged","mergedAt":"2022-05-17T17:42:29Z","number":1322,"body":"## Summary\r\nCurrent behaviour is not re-entrant, so if you back out of the auth flow you get stuck. The only way to fix this with the current UX (login page remains until you log in) is to make it re-entrant.","mergeCommitSha":"dfdfe236be544613dbf9cff3f409bacfc163702a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1322","title":"Make auth re-entrant","createdAt":"2022-05-17T17:25:16Z"}
{"state":"Merged","mergedAt":"2022-05-19T17:18:15Z","number":1323,"body":"Spec change to support GH App installation for an org.\r\n\r\nClients will provide a list of cloneURLs and the API service will return all the relevant organizations and their install status.\r\n\r\nClients will continue polling request until all the clone urls within the request match with a corresponding repo in the org's response.\r\n\r\n<img width=\"714\" alt=\"CleanShot 2022-05-17 at 11 40 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/168886738-2fcc1a92-0b14-4b49-a375-239c2a815b08.png\">\r\n","mergeCommitSha":"14cf7364ba5305cb8ca97f282ecc384ffca0d4d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1323","title":"Onboarding Installations Spec Change","createdAt":"2022-05-17T18:46:23Z"}
{"state":"Merged","mergedAt":"2022-05-17T19:38:36Z","number":1324,"body":"re: [new font](https://chapter2global.slack.com/archives/C02US6PHTHR/p1652721717086719) for branding ","mergeCommitSha":"216a83c8b44a306c6c69389726c1b8b2638d5083","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1324","title":"Update template with new font","createdAt":"2022-05-17T19:07:38Z"}
{"state":"Merged","mergedAt":"2022-05-17T20:54:40Z","number":1325,"body":"This pr allows us to pass baggages down to spans while still maintaining type.\r\nTo maintain cross-platform data passing, the designers of opentelemetry prohibited baggage value types other than string.\r\n\r\nTo get around that, baggages now have type metadata.\r\nWhen a new span is created, it looks at any baggages in the context, and propagages the correctly typed baggage contents down.","mergeCommitSha":"cb5c04405773f7cd2cb509d8732048a427a164f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1325","title":"Add typed baggages","createdAt":"2022-05-17T20:23:53Z"}
{"state":"Merged","mergedAt":"2022-05-17T22:00:56Z","number":1326,"mergeCommitSha":"e58d4d68bb800332cd39681827833c7524a3d283","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1326","title":"Rename ThreadUnread fields","createdAt":"2022-05-17T21:15:53Z"}
{"state":"Merged","mergedAt":"2022-05-18T02:58:44Z","number":1327,"body":"total rewrite, should be easier to consume and more efficient now","mergeCommitSha":"01b01fc9ab1691e45abbad286a72a6be18b9d8af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1327","title":"Refactor ThreadInfo store","createdAt":"2022-05-17T21:37:54Z"}
{"state":"Merged","mergedAt":"2022-05-18T20:32:12Z","number":1328,"mergeCommitSha":"d0099fde81e2eac6b6d400c6d4b67d47c212124f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1328","title":"Add thread recommended store implementation","createdAt":"2022-05-17T22:45:51Z"}