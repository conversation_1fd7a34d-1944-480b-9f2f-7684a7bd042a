{"state":"Merged","mergedAt":"2023-06-20T20:18:26Z","number":6704,"body":"This has already been deployed. ","mergeCommitSha":"f284f1f8afbb9e5d831bbe47df911d63aecb2364","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6704","title":"change to ubuntu 22.04 LTS","createdAt":"2023-06-20T20:18:17Z"}
{"state":"Merged","mergedAt":"2023-06-21T21:57:46Z","number":6705,"body":"Add logs to the UpdateService to debug issues with installer\r\n\r\nFunctionally should not change anything but worth taking a closer look as I've changed the order of the if statements a bit.","mergeCommitSha":"c9d6a5241af4b889e9e5890f466d965195a6bdbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6705","title":"Logs to debug hub installer","createdAt":"2023-06-20T21:31:02Z"}
{"state":"Merged","mergedAt":"2023-06-22T00:04:09Z","number":6706,"body":"This fixes overlay/z-index issues we see with context menus. Some dropdowns should behave as modals; as in they should pop to the top level DOM (on top of the content) and lock the content behind it until dismissed. This PR introduces an optional `portal` argument into the Dropdown component which allows for this functionality.\r\n\r\nIt's important to note that once the content div `.dropdown__items` has been popped, it essentially breaks out of the `.dropdown` parent container; this may break some CSS styling depending on how the style definitions are defined or scoped. ","mergeCommitSha":"e18bfede9879d727eddb1acac2bc8b9ef0c0c503","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6706","title":"Refactor dropdown to have portal capability","createdAt":"2023-06-20T21:41:11Z"}
{"state":"Merged","mergedAt":"2023-06-20T21:54:29Z","number":6707,"mergeCommitSha":"30cdca8deddb5349d7bb25612f4291ed80b39d9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6707","title":"resizing root volume to 200GB","createdAt":"2023-06-20T21:54:23Z"}
{"state":"Merged","mergedAt":"2023-06-20T21:54:41Z","number":6708,"mergeCommitSha":"afaa247096ab5bd31b8d2bfb3b208aeff82515e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6708","title":"Expire dead letter queue","createdAt":"2023-06-20T21:54:34Z"}
{"state":"Merged","mergedAt":"2023-06-20T22:39:11Z","number":6709,"body":"Every topic that is `TopicSourceType.Approved` is approved. The `isApproved` flag is now only set by customers not admins.","mergeCommitSha":"10a46fd11ee2d295b0b7d59b0821d92d4d7b4ab6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6709","title":"Remove isApproved filter for TopicSouceType.Approved","createdAt":"2023-06-20T21:57:51Z"}
{"state":"Merged","mergedAt":"2022-03-28T22:14:19Z","number":671,"body":"## Summary\r\nWill accept the rootCommitSha but currently ignores it and only uses the repo url.  Performs access checks at the team level. ","mergeCommitSha":"81ab0dd8a43cb731adbbcfb570a67e34c8ebfdf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/671","title":"Implements findRepo","createdAt":"2022-03-26T18:03:55Z"}
{"state":"Merged","mergedAt":"2023-06-20T22:32:21Z","number":6710,"body":"Was missing a `+`","mergeCommitSha":"0b7b40ff1d11d962d6971e5d73026a795d65fa1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6710","title":"Fix feedback bug in admin console thread page","createdAt":"2023-06-20T22:20:14Z"}
{"state":"Merged","mergedAt":"2023-06-21T01:13:26Z","number":6711,"body":"@richiebres is going to eviscerate this pr. I get it.\r\n","mergeCommitSha":"a6774bc48ad27d417b83f20f12a2bb3a7132fda7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6711","title":"Fresh tokens","createdAt":"2023-06-20T22:23:22Z"}
{"state":"Merged","mergedAt":"2023-06-21T00:23:40Z","number":6712,"mergeCommitSha":"001eaaf395e2d73f32fcaf8ff866116a24f0456c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6712","title":"Revert removal of topics from template","createdAt":"2023-06-20T23:45:16Z"}
{"state":"Merged","mergedAt":"2023-06-21T00:56:34Z","number":6713,"body":"The webpack build setting (which is set when doing local VSCode builds) should override the local user defaults value.","mergeCommitSha":"bc0f39ebd48ad1f8f10ec3349c1130db24ed3894","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6713","title":"Use build environment spec","createdAt":"2023-06-21T00:32:44Z"}
{"state":"Merged","mergedAt":"2023-06-21T19:14:13Z","number":6714,"mergeCommitSha":"628c7a8f71566c593d575732103dfdc266cf5514","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6714","title":"Move expert summary generation to use templates","createdAt":"2023-06-21T00:33:47Z"}
{"state":"Closed","mergedAt":null,"number":6715,"body":"Update MessageView to handle FeedbackButton for IDEs\r\n\r\n\r\n<img width=\"245\" alt=\"CleanShot 2023-06-20 at 21 52 38@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/c81d574e-2324-40de-87fc-4829596c341a\">\r\n<img width=\"253\" alt=\"CleanShot 2023-06-20 at 21 52 18@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ccfff330-e300-486d-bb36-a503f3bb5ab5\">\r\n","mergeCommitSha":"45bf9e941067667c0f26cec5d22c23b074a0b39b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6715","title":"Feedback button for IDEs","createdAt":"2023-06-21T04:56:30Z"}
{"state":"Merged","mergedAt":"2023-06-21T21:57:36Z","number":6717,"body":"Rough home base to get things started for landing page.\r\n\r\nLots of work needs to be done to consolidate variables but I didn't think that was necessary at this moment as things are still figured out. Lots of the padding numbers & colours don't match existing values.\r\n\r\nWe can consolidate once we're finished adjusting styles.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/eef86e6c-0250-41a0-9277-e95f7ac995ed\r\n\r\n","mergeCommitSha":"cbf220259b84fbffea39753b68fa05a08a2f7b83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6717","title":"Basic wip landing page","createdAt":"2023-06-21T06:30:22Z"}
{"state":"Merged","mergedAt":"2023-06-21T15:58:09Z","number":6718,"body":"Use adaptive color in searchView","mergeCommitSha":"64afd2e649fac3ea59b65e630e06d6e88e6bb5dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6718","title":"Update adaptive color","createdAt":"2023-06-21T06:36:53Z"}
{"state":"Merged","mergedAt":"2023-06-22T00:00:10Z","number":6719,"mergeCommitSha":"85e5396df41e24997d6a1715da6214fd31dbe0a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6719","title":"Move search from API to search service","createdAt":"2023-06-21T06:40:32Z"}
{"state":"Merged","mergedAt":"2022-03-28T20:02:21Z","number":672,"body":" Its no longer necessary since:\r\n\r\n1) ThreadParticipant is never modified, just created\r\n2) ThreadParticipant is only created when a message is added to a thread, and adding a message to a thread will always update the Thread.lastMessageCreatedAt thus Thread.modifiedAt will be updated too\r\n\r\nThis was causing a bug where the getThreads operation was returning the incorrect results based on the `X-Unblocked-If-Modified-Since` header.\r\n","mergeCommitSha":"de6f46c96a06bab4b8ebe53e7d5a2414f658919c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/672","title":"Threads query does not filter on ThreadParticipant.modifiedAt","createdAt":"2022-03-28T04:00:00Z"}
{"state":"Merged","mergedAt":"2023-06-21T16:09:46Z","number":6720,"mergeCommitSha":"e14e4493cd1e7fa8b8ead893de3fdc8ab6dca573","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6720","title":"Fix installer build","createdAt":"2023-06-21T15:58:46Z"}
{"state":"Merged","mergedAt":"2023-06-21T17:51:16Z","number":6721,"mergeCommitSha":"674cd52a194c92e6427078e4d4a7c310ee63749b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6721","title":"Add author to inference example","createdAt":"2023-06-21T17:31:23Z"}
{"state":"Merged","mergedAt":"2023-06-21T18:18:15Z","number":6722,"mergeCommitSha":"8dbbf6ac2929ca9b096ddcc41298052fadf9e5c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6722","title":"Do not allow edits to global template","createdAt":"2023-06-21T17:37:02Z"}
{"state":"Merged","mergedAt":"2023-06-21T19:35:51Z","number":6723,"body":"This will solve the issue where top-level comment threads weren't being archived for low-relevance. We do a full ingest of a pull request when it's merged so this is a good point to go through all associated threads and archive those that are low-relevance. ","mergeCommitSha":"2b42ab7bb5786f3935b1828bf6388605c6833b92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6723","title":"Archive low relevance threads when doing a full ingest","createdAt":"2023-06-21T18:12:50Z"}
{"state":"Merged","mergedAt":"2023-06-21T21:51:59Z","number":6724,"mergeCommitSha":"b193e7efea8564d0e8dabd34ef5c9d9600f95b86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6724","title":"Add the full documents content to the examples","createdAt":"2023-06-21T18:13:53Z"}
{"state":"Open","mergedAt":null,"number":6725,"body":"Once we converted top-level comments into threads the clients no longer needed to call these operations.","mergeCommitSha":"cf3a18c7903781d18bb57062c3b38ca1ed029d30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6725","title":"Deprecate pull request block CRUD operations","createdAt":"2023-06-21T18:25:29Z"}
{"state":"Merged","mergedAt":"2023-06-27T19:28:18Z","number":6726,"body":"<img width=\"558\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/248e09ce-9096-4ad8-b48a-ec123ba08c81\">\r\n\r\n* Add tooltip component\r\n* Add tooltip to MessageInput in the dashboard\r\n    * Current ruleset to show the tooltip is: `!hasContent && isFocused && isQA && !(1:1)`","mergeCommitSha":"a33961b3264b0b3436f3b3b58cf2da9dca4c6e11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6726","title":"Add info tooltip for lack of continuation","createdAt":"2023-06-21T18:56:51Z"}
{"state":"Merged","mergedAt":"2023-06-21T19:34:11Z","number":6727,"body":"<img width=\"792\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/df9904c4-ba7c-4a4c-a1c2-fbab900ba22e\">\r\n","mergeCommitSha":"dc3a640ddd8140e1801e8326f8244d8188dbd92b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6727","title":"Remove topic trending ","createdAt":"2023-06-21T19:03:08Z"}
{"state":"Merged","mergedAt":"2023-06-21T20:02:28Z","number":6728,"body":"- RunSuspendCatching\r\n- Move stuff over\r\n","mergeCommitSha":"f650c40a3390f96cd1cee4cfee5adc3dedfd046f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6728","title":"MoveAwayFromRunCatching","createdAt":"2023-06-21T19:18:38Z"}
{"state":"Merged","mergedAt":"2023-06-22T21:51:18Z","number":6729,"body":"Keyboard Shortcuts were not being set properly when hub is running and \"semantic search\" flag is enabled.\r\n\r\nUpdated KeyboardShortcuts to be responsive based on capabilities.\r\n\r\nAlso removed duplicates on capabilities publishers.\r\n\r\nFixes this: https://app.intercom.com/a/apps/crhakcyc/conversations/554","mergeCommitSha":"70902d9f069daed8e6ace179044e5f3961db8147","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6729","title":"Update keyboard shortcuts to be responsive based on capabilities","createdAt":"2023-06-21T19:38:02Z"}
{"state":"Merged","mergedAt":"2022-03-28T16:25:41Z","number":673,"body":"the Phony attribute was getting unwieldy.\r\nsplit it so it's per make target.\r\n\r\nAlso, I don't think it's a good idea to do per project clean. The root clean should invoke all submodule cleans, and if it doesn't , I'll fix it.\r\n\r\nSame with lint and detekt.","mergeCommitSha":"22e7c8f359cb5cafe747bcb68a9bca8e1ca3ff73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/673","title":"Cleanup Makefile","createdAt":"2022-03-28T16:20:02Z"}
{"state":"Merged","mergedAt":"2023-06-21T20:12:56Z","number":6730,"mergeCommitSha":"5202682bc75cc5a19550c1d5d715442929ba77cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6730","title":"Fix test","createdAt":"2023-06-21T20:01:11Z"}
{"state":"Closed","mergedAt":null,"number":6731,"body":"I can wrap this in a coroutine if we think that's a good idea.","mergeCommitSha":"e3ffef8a5b1b4d9a3d87ef211fa7067d9d5d3af1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6731","title":"Trigger repo source code ingestion for all repos in a team","createdAt":"2023-06-21T20:13:04Z"}
{"state":"Merged","mergedAt":"2023-06-21T22:04:16Z","number":6732,"body":"Fix bug that prevented file -> topic mapping for some files (`SourceMarkProvider.ts`).\r\n\r\nThis broke with this PR: https://github.com/NextChapterSoftware/unblocked/pull/5424.  Jeff was working around a bug where the `FileSourceMarkStream` was returning an empty list of items if no sourcemarks (or threads associated with sourcemarks) resolved correctly.  This fixes the root bug properly and undoes the previous fix.","mergeCommitSha":"c29c302064d9d7c119dc34532c68533914be1624","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6732","title":"Fix file -> topic resolution","createdAt":"2023-06-21T20:24:49Z"}
{"state":"Merged","mergedAt":"2023-06-21T22:19:16Z","number":6733,"body":"Need to create the #ask-unblocked-downvote and #ask-unblocked-dev-downvote channels in slack","mergeCommitSha":"c831377a820232f36f065877a7039b917b32d030","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6733","title":"Announce downvoted ask-unblocked answer","createdAt":"2023-06-21T21:28:16Z"}
{"state":"Merged","mergedAt":"2023-06-21T21:48:34Z","number":6734,"body":"* MessageEditor should keep focus after mention dropdown click\r\n* Feedback button should be green if current user has approved ","mergeCommitSha":"d78425293baf5bb198b23aa4582ef4231837643d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6734","title":"Nit fixes","createdAt":"2023-06-21T21:30:39Z"}
{"state":"Merged","mergedAt":"2023-06-21T22:03:17Z","number":6735,"body":"Conditional title based on if current user is author of Q&A\r\n\r\nhttps://chapter2global.slack.com/archives/C02US6PHTHR/p1687381846923389","mergeCommitSha":"9942b87fc93c82a7d242931310ae400fb06c6bd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6735","title":"Conditional title based on author","createdAt":"2023-06-21T21:46:12Z"}
{"state":"Merged","mergedAt":"2023-06-21T22:10:39Z","number":6736,"body":"Fetch 50 threads for mine/recommended.\r\n\r\nAs part of this, lower the default polling rate for many channels:\r\n  * Sidebar onboarding status (10s)\r\n  * Sourcemarks (10s)\r\n  * Repo Pull requests (10s)\r\n  * Repos (10s)\r\n  * Gutter icon thread updates (10s)\r\n  * Team members (60s)\r\n  * Mine/recommended thread listing (10s)\r\n\r\nI also increased the overlay maximum time to 30 seconds -- since some of these operations may result in a longer delay until we get the new data.\r\n","mergeCommitSha":"4b6b8212fa0ad606033743ea6df52cb924e27dd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6736","title":"Fetch more threads for mine/recommended","createdAt":"2023-06-21T21:46:59Z"}
{"state":"Merged","mergedAt":"2023-06-21T23:05:35Z","number":6737,"body":"Directly inject access to semantic search service & teams into Search View.\r\n\r\nThis is in preparation of pulling SearchView out of SearchHeaderView and have it live by itself within a popover to achieve new search UX\r\n\r\nUploading CleanShot 2023-06-21 at 13.05.28.mp4…\r\n\r\n","mergeCommitSha":"d2568492966080fe9ff73d3dfc110d17b8fa51c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6737","title":"Remove search view dependencies","createdAt":"2023-06-21T22:19:10Z"}
{"state":"Closed","mergedAt":null,"number":6739,"mergeCommitSha":"7c1f4a66c0791a340e223cfdcfc5f409ef8434e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6739","title":"Sort Unlabeled MLInferenceExamples by sentiment so that downvoted ones come first","createdAt":"2023-06-21T22:22:55Z"}
{"state":"Merged","mergedAt":"2022-03-28T18:18:54Z","number":674,"body":"<img width=\"317\" alt=\"CleanShot 2022-03-28 at 09 47 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/160447370-4b5ca48d-0ae5-4e72-977a-8c1f606704b2.png\">\r\n","mergeCommitSha":"a28b093026a1881320439ca5373562dde0c26d9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/674","title":"Simple styling for web extension sidebar","createdAt":"2022-03-28T16:48:47Z"}
{"state":"Merged","mergedAt":"2023-06-21T22:42:26Z","number":6740,"mergeCommitSha":"b32d33f7e2ff82931fe499224ac5b5eb41968888","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6740","title":"Reorder Person page menu so that dangerous actions are at the bottom of the list","createdAt":"2023-06-21T22:33:28Z"}
{"state":"Merged","mergedAt":"2023-06-21T23:16:07Z","number":6741,"mergeCommitSha":"65ef67ddb5f1fc0805489c4bca6db13b531f61f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6741","title":"Provide default MLInferenceExample.sentiment value to fix ordering in admin console","createdAt":"2023-06-21T22:38:50Z"}
{"state":"Merged","mergedAt":"2023-06-22T00:04:59Z","number":6742,"body":"Affected two teams:\n - Cribl https://admin.prod.getunblocked.com/teams/e0c510e2-6e19-4947-a19a-98f45478d171/repos\n - LevL https://admin.prod.getunblocked.com/teams/82492fe7-c48b-421f-9ff0-77b4d8433454/repos","mergeCommitSha":"8af165efc8c90309b20626c049d89e7de2839430","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6742","title":"Installing a subset of repos incorrectly installed all repos","createdAt":"2023-06-21T22:40:31Z"}
{"state":"Closed","mergedAt":null,"number":6743,"body":"Filter out non-current team members from thread/PR expert lists.\r\n\r\nI'm not sure if this is how we want to do this -- the user will still appear as a topic expert.","mergeCommitSha":"382410cc7185cc91c5c6787d37efc91b017ff7a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6743","title":"Remove non-current thread/PR experts","createdAt":"2023-06-21T22:51:25Z"}
{"state":"Merged","mergedAt":"2023-06-22T20:00:03Z","number":6744,"mergeCommitSha":"5e15767f84d3641bdb1ab55c9157a9572232c0da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6744","title":"Fix provider storage","createdAt":"2023-06-21T23:10:32Z"}
{"state":"Merged","mergedAt":"2023-06-22T03:56:07Z","number":6745,"mergeCommitSha":"e0b60ede3eb5e95760e3a50c51da203fa2dc7b1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6745","title":"Add original questioner to example and result models","createdAt":"2023-06-21T23:18:18Z"}
{"state":"Merged","mergedAt":"2023-06-21T23:22:39Z","number":6746,"mergeCommitSha":"a0b0409442eee6eda4ec431d44aede9f723eb99d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6746","title":"Truncate the query because it can be out of control","createdAt":"2023-06-21T23:21:05Z"}
{"state":"Closed","mergedAt":null,"number":6747,"body":"This will set the minimum period between client polls.","mergeCommitSha":"4e916759fa36abf9bc029572fca2ef165129c540","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6747","title":"Add pusher minimum polling period client cfg","createdAt":"2023-06-21T23:23:53Z"}
{"state":"Merged","mergedAt":"2023-06-22T00:12:03Z","number":6748,"mergeCommitSha":"5d725d75103e080a6098379a8c0b80aa112a3262","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6748","title":"Record and log the queue latency and processing latency for search queries","createdAt":"2023-06-22T00:01:38Z"}
{"state":"Merged","mergedAt":"2023-06-22T03:20:49Z","number":6749,"body":"Slack integration webhooks are deprecated and are essentially going to eventually be removed from apis.\r\nWe need to move to app level incoming webhooks as this pr does.\r\nIncoming webhooks are cooupled to a channel and cannot be changed.\r\n\r\nTested everything is kosher.\r\n","mergeCommitSha":"6206c8c964b23a5a0ff50b09fb2d67fdd4b6da21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6749","title":"FixUpSlackNotifications","createdAt":"2023-06-22T00:11:19Z"}
{"state":"Closed","mergedAt":null,"number":675,"body":"- Adding debug flag to helm command so we get more output when deployments fail\r\n\r\nUpdate: \r\nRemoved deployment sequencing change. Richie will add a delay to make sure other services wait while one is doing schema update. ","mergeCommitSha":"bd125dadc7e5dcdde030c9ad7a129572bd082a23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/675","title":"add more logging for helm deploys","createdAt":"2022-03-28T17:49:42Z"}
{"state":"Merged","mergedAt":"2023-06-23T00:22:15Z","number":6750,"body":"Follow on cleanup from #6719.","mergeCommitSha":"bdb3d4a72ef9bc61655f04c64a54de371d8096a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6750","title":"Remove legacy search queues","createdAt":"2023-06-22T00:24:37Z"}
{"state":"Merged","mergedAt":"2023-06-22T03:59:28Z","number":6751,"body":"Topic and thread experts should generally always be current users.  The one exception to this is when we directly view a user, we should be able to see their fully resolved list of expertise.\r\n\r\nI ended up fixing this in the topic aggregation stage -- the `experts` and `expertMembers` properties are now current members only.  These were used a lot, all over the place, and AFAICT we pretty much always want current members.\r\n\r\nI added a new `allExpertMembers` property that includes non-current members.  This is used in the user view.\r\n\r\nIn the future we might want to add a `topicExpertIds` onto `TeamMember` so that we don't need to reverse-map this stuff, and so the service can control this instead of doing it client-side","mergeCommitSha":"e824804a4ef94c2a166239a4dc6a92fad5f80b35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6751","title":"Experts should (almost) always be current users","createdAt":"2023-06-22T00:40:27Z"}
{"state":"Merged","mergedAt":"2023-06-22T01:58:43Z","number":6752,"mergeCommitSha":"1bc33467c3015ee6a6cc8e5f96d8a6ae0e8efeb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6752","title":"More accurate queue latency measurement plus more frequent polling","createdAt":"2023-06-22T01:58:03Z"}
{"state":"Merged","mergedAt":"2023-06-22T03:39:02Z","number":6753,"mergeCommitSha":"20271584e15deb4d529f1eaecb43d38af510135a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6753","title":"Fix test","createdAt":"2023-06-22T03:38:56Z"}
{"state":"Merged","mergedAt":"2023-06-22T04:01:32Z","number":6754,"mergeCommitSha":"5819ddfd890651e545ea670cd19b6ceb988b6ffe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6754","title":"Increase consumers to 10","createdAt":"2023-06-22T04:00:32Z"}
{"state":"Merged","mergedAt":"2023-06-22T04:56:27Z","number":6755,"mergeCommitSha":"b392e55b0dcc524a3bf51bda8071d0f3ff0234a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6755","title":"Update secrets","createdAt":"2023-06-22T04:56:21Z"}
{"state":"Merged","mergedAt":"2023-06-22T05:26:09Z","number":6756,"mergeCommitSha":"f50346dd9ec675ffd7e3477502fa0b93626e69e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6756","title":"Fix errant comma in global config","createdAt":"2023-06-22T05:26:03Z"}
{"state":"Merged","mergedAt":"2023-06-22T07:06:10Z","number":6757,"mergeCommitSha":"4fa5f81bb06dd7df737e1cf46bd84a3d6fef7d61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6757","title":"Display original questioner instead of bot","createdAt":"2023-06-22T05:35:12Z"}
{"state":"Merged","mergedAt":"2023-06-22T07:50:23Z","number":6758,"mergeCommitSha":"0e9e81f66e93c185dbe8fc909a40d3cf79644971","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6758","title":"Backfill example questioner with thread author","createdAt":"2023-06-22T07:05:44Z"}
{"state":"Merged","mergedAt":"2023-06-22T14:44:18Z","number":6759,"body":"Search jobs had non-zero latency even when the queue was completely\r\nempty. The reason was that queue jobs use an interval poll, meaning\r\nthat the expected latency is half the interval time. Changed this\r\nto a blocking wait (long poll) instead, so that when the queue is\r\nempty messages are consumed immediately (zero latency).\r\n\r\nUsing a single queue (a recent change) a new high priority job can\r\nbe starved when all consumers are busy processing low priority work\r\nuntil those jobs have completed. The problem is that low priority\r\njobs can take 10s of seconds, meaning that a new high-priority job\r\nmay queue excessively. Introduced dedicated consumers that only\r\nservice high priority jobs, meaning that there will always be N\r\nidle consumers waiting for high-priority work even when the shared\r\nqueue is full.\r\n\r\nWith these two changes, high-priority jobs should not have any\r\nqueue latency, at least until we need to serve more than 16\r\nconcurrent search queries.","mergeCommitSha":"d566084d3f3e78fbf1e8eb95c4853f9b5b364733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6759","title":"Introduce realtime high-priority event consumer for search","createdAt":"2023-06-22T07:35:46Z"}
{"state":"Closed","mergedAt":null,"number":676,"body":"We need to:\r\n\r\n1) Check that we're never allowing deleting a message if its the only message in a thread\r\n2) Update `Thread.lastMessageCreatedAt`\r\n3) Groom thread participants & thread unreads\r\n4) Don't actually delete the message content from db, just don't return it in the API on the outbound","mergeCommitSha":"29eeb523db3fa24d6636505f99acb618b76edca7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/676","title":"Fix deleteMessage","createdAt":"2022-03-28T17:51:35Z"}
{"state":"Merged","mergedAt":"2023-06-22T14:53:34Z","number":6760,"mergeCommitSha":"d1c768bc0249690f9651b2d41960bd766e815205","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6760","title":"Fix repo selection for first time installation","createdAt":"2023-06-22T14:40:54Z"}
{"state":"Merged","mergedAt":"2023-06-22T16:00:04Z","number":6761,"mergeCommitSha":"c96c044fc742d1d4a078c69df644a7bee5b02b59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6761","title":"Bump for deploy","createdAt":"2023-06-22T15:59:38Z"}
{"state":"Merged","mergedAt":"2023-06-22T17:10:47Z","number":6762,"mergeCommitSha":"dc2c1931b9ea73dc6b11037afca6ac2ac827744d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6762","title":"Disable growth channel for dev","createdAt":"2023-06-22T17:10:40Z"}
{"state":"Merged","mergedAt":"2023-06-22T19:14:27Z","number":6763,"mergeCommitSha":"ade582072ded098831373918870a45195b5dc06c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6763","title":"Moves TopicSummaryService to Peter Prompts","createdAt":"2023-06-22T17:34:25Z"}
{"state":"Merged","mergedAt":"2023-06-23T15:29:07Z","number":6764,"body":"Fixes scrolling in repo selection onboarding for Bitbucket / GL","mergeCommitSha":"591e6ed657db19ba25f4bfe6991c74c4c0830331","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6764","title":"Fix scrolling repo selection onboarding","createdAt":"2023-06-22T17:35:00Z"}
{"state":"Merged","mergedAt":"2023-06-22T19:13:30Z","number":6765,"mergeCommitSha":"fa903c8d315feb0f509a3cf3a05e5be37215596a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6765","title":"Add duration and validation record to MLInferenceResult","createdAt":"2023-06-22T18:01:15Z"}
{"state":"Merged","mergedAt":"2023-06-22T18:44:10Z","number":6766,"body":"So that we're notified when GitHub issues are created, edited, or deleted. Next PR will add logic to the handler (it just logs for now).","mergeCommitSha":"0fd4121cda4255524de2bd966b5c1e6d943df7cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6766","title":"Add handler for GitHub issue events","createdAt":"2023-06-22T18:16:58Z"}
{"state":"Merged","mergedAt":"2023-06-22T21:51:51Z","number":6767,"body":"Repo maintenance should respect the team's repo selection setting (which\nis relevant for Bitbucket and GitLab), but fallback to the\nall-repos-selected setting when unspecified (which is relevant for GitHub).","mergeCommitSha":"2ef3dd63fd51ae591e2dbca0bf3ade9b62697d4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6767","title":"Fix another repo selection bug","createdAt":"2023-06-22T18:23:09Z"}
{"state":"Merged","mergedAt":"2023-06-22T19:54:59Z","number":6768,"mergeCommitSha":"35ae3907690eda3d69f12cf528494cafa1596fe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6768","title":"Fix up slack notifier","createdAt":"2023-06-22T18:46:25Z"}
{"state":"Merged","mergedAt":"2023-06-22T21:13:34Z","number":6769,"body":"Renders messages down to text","mergeCommitSha":"d5a9ede6558e649174128db699c9ee3123c8311a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6769","title":"Message text renderer","createdAt":"2023-06-22T19:28:26Z"}
{"state":"Merged","mergedAt":"2022-04-01T19:52:41Z","number":677,"body":"![image](https://user-images.githubusercontent.com/13431372/160455657-30c2f87c-35d2-4e1b-b259-bdb773690b99.png)\r\n![image](https://user-images.githubusercontent.com/13431372/160455737-ac3bc8bb-3620-4358-9fa5-3c7f09340664.png)\r\n* Stream unread properties onto the ThreadAndParticipants model (we should probably rename this?? `ThreadAggregate` ?)\r\n\r\nNOTES:\r\n* I haven't added the badge to the unblocked sidebar icon for unreads (can do that in a separate PR)\r\n* [DO NOT MERGE] - this is waiting on the implementation of the `/unreads` pusher channel to test before merging ","mergeCommitSha":"8330380c7ecc365408d38ee7d8819c0623c75ca9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/677","title":"Implement unreads into clients","createdAt":"2022-03-28T18:12:26Z"}
{"state":"Merged","mergedAt":"2023-06-22T21:30:09Z","number":6770,"body":"No longer need slackbotservice as we are building Q&A flow.\r\n","mergeCommitSha":"84a6070497bcdf23c70fe758efceef87b0ab7dd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6770","title":"[BREAKS API ON MAIN] Goodbye dead service","createdAt":"2023-06-22T20:19:51Z"}
{"state":"Closed","mergedAt":null,"number":6771,"mergeCommitSha":"7e48cac5f967062ae2891aa4f3126986ec8a272f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6771","title":"Add Topic.relatedQuestions property","createdAt":"2023-06-22T21:15:09Z"}
{"state":"Merged","mergedAt":"2023-06-22T21:52:09Z","number":6772,"mergeCommitSha":"804c0ac64bba3a388179e5d4cb98e7bfbc5dfbdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6772","title":"Create results for topics and experts summaries","createdAt":"2023-06-22T21:28:45Z"}
{"state":"Merged","mergedAt":"2023-06-23T00:05:34Z","number":6774,"body":"<img width=\"1048\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/e58c0aa4-9e8e-45f8-a584-606fe81ca37c\">\r\n","mergeCommitSha":"b1d178d6c5af8b232133a494e863164b03f88128","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6774","title":"Clean up insight cards/dashboard","createdAt":"2023-06-22T21:49:21Z"}
{"state":"Merged","mergedAt":"2023-06-22T22:36:05Z","number":6775,"body":"There is no valid scenario where refreshing an access token vends a token that has expired.\nBetter to fast fail here.","mergeCommitSha":"35ea35834ca0179e7568ee37b988713b2ec782d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6775","title":"Assert that the OAuth refresh token process works","createdAt":"2023-06-22T22:00:37Z"}
{"state":"Merged","mergedAt":"2023-06-23T15:11:34Z","number":6776,"mergeCommitSha":"53f70439153ab6bfb60218af7b8acfdb2b7118df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6776","title":"Add regression test pipeline","createdAt":"2023-06-22T23:20:53Z"}
{"state":"Merged","mergedAt":"2023-06-23T00:17:24Z","number":6777,"mergeCommitSha":"0b8521799ccb4eb2d5faf2e148d58bf89af9d3e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6777","title":"Debug message latency","createdAt":"2023-06-23T00:07:21Z"}
{"state":"Merged","mergedAt":"2023-06-23T04:16:05Z","number":6778,"mergeCommitSha":"8ddb2e4f53cef71e00cb23dedbf3ca106799b6cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6778","title":"Enable Slack App Mention","createdAt":"2023-06-23T03:18:58Z"}
{"state":"Merged","mergedAt":"2023-06-23T04:25:05Z","number":6779,"mergeCommitSha":"30d0efd848af6208b6989fe145aa9049f052ba42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6779","title":"Fix build","createdAt":"2023-06-23T04:24:59Z"}
{"state":"Merged","mergedAt":"2022-03-28T19:17:35Z","number":678,"mergeCommitSha":"0b20bf703dc75d7b99a7f64140cc6ef9371839b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/678","title":"Temporary fix for app install id in fixtures","createdAt":"2022-03-28T18:22:17Z"}
{"state":"Merged","mergedAt":"2023-06-23T16:13:01Z","number":6780,"mergeCommitSha":"e574aeefb52696ebfff7609600a68605b235f63d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6780","title":"Add feature flag for hub search","createdAt":"2023-06-23T06:08:28Z"}
{"state":"Merged","mergedAt":"2023-06-23T06:17:34Z","number":6781,"mergeCommitSha":"b8bb7e0c987fb0f83304f242bebb0f484e0e7f72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6781","title":"Fix build","createdAt":"2023-06-23T06:17:28Z"}
{"state":"Merged","mergedAt":"2023-06-23T08:01:25Z","number":6782,"mergeCommitSha":"b52a19c8f5e2751bca614f8586b4280d6405b853","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6782","title":"Add question and answer","createdAt":"2023-06-23T06:29:48Z"}
{"state":"Merged","mergedAt":"2023-06-23T08:27:33Z","number":6783,"mergeCommitSha":"1090883c86c2897b4c1687109d752ffe7ca0d42c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6783","title":"Ensure we send sentTimestampMs in all cases","createdAt":"2023-06-23T06:30:56Z"}
{"state":"Merged","mergedAt":"2023-06-23T07:19:40Z","number":6784,"mergeCommitSha":"c6c2b398e5aa04883b809d2084abcf7cff771749","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6784","title":"Slack bolt was bringing in transitive dependency","createdAt":"2023-06-23T07:19:33Z"}
{"state":"Merged","mergedAt":"2023-06-23T15:18:59Z","number":6786,"mergeCommitSha":"0c6cc647cd1a82d609af4b219f0661b1d92c62b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6786","title":"Add logging to topic summary updater","createdAt":"2023-06-23T15:18:51Z"}
{"state":"Merged","mergedAt":"2023-06-23T15:24:38Z","number":6787,"mergeCommitSha":"c85e1fc74a6aa5aefddc5a96db0de95eeacb53a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6787","title":"Debug message priority","createdAt":"2023-06-23T15:21:05Z"}
{"state":"Merged","mergedAt":"2023-06-23T15:26:00Z","number":6788,"mergeCommitSha":"aa42b1a4733db0da6eb2c1320a0a2ea0d9edaf3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6788","title":"Fix examples fields","createdAt":"2023-06-23T15:25:13Z"}
{"state":"Merged","mergedAt":"2023-06-23T16:28:22Z","number":6789,"mergeCommitSha":"8003c392009b64ec89f6aea5e90b4d42ab54b082","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6789","title":"Must clone first before editing templates","createdAt":"2023-06-23T15:56:11Z"}
{"state":"Merged","mergedAt":"2022-03-28T22:08:14Z","number":679,"mergeCommitSha":"c4501e4d6934f29a8eb779203f127009f6f173a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/679","title":"Improve GitHub regex to match on web paths","createdAt":"2022-03-28T19:07:34Z"}
{"state":"Merged","mergedAt":"2023-06-23T16:28:54Z","number":6790,"mergeCommitSha":"5e21727d44c88bdc17867b88e1bc4756e40e8fc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6790","title":"Log topic summary prompt","createdAt":"2023-06-23T16:28:07Z"}
{"state":"Merged","mergedAt":"2023-06-23T17:10:49Z","number":6791,"mergeCommitSha":"aff0d902754bdc3f05511fcd43ffc7764a50fb41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6791","title":"Reduce search service consumer count to 1 to test a contention hypothesis","createdAt":"2023-06-23T17:08:11Z"}
{"state":"Merged","mergedAt":"2023-06-23T18:01:30Z","number":6792,"mergeCommitSha":"b1416f1e5d4cf315d37808a74b9bb0bf9ec47af9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6792","title":"Ability to delete a repo in admin web","createdAt":"2023-06-23T17:40:44Z"}
{"state":"Merged","mergedAt":"2023-06-23T18:16:25Z","number":6793,"body":"As part of implementing chat continuation, we'll need to eventually call the same logic from the create message operations. Let's move this into it's own service so that it can be shared.","mergeCommitSha":"0ed60d3e9f3cde37ca5e3650acfdcfd7d32cbdae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6793","title":"Move bot notification logic into a NotifyBotService","createdAt":"2023-06-23T17:47:49Z"}
{"state":"Merged","mergedAt":"2023-06-23T18:08:20Z","number":6794,"mergeCommitSha":"50cc55759ddbfe06d7787cec5f3e919005867f82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6794","title":"Fix delete repo cascade and fix redirect to repos page","createdAt":"2023-06-23T18:08:03Z"}
{"state":"Merged","mergedAt":"2023-06-23T18:53:15Z","number":6795,"body":"This reverts commit aff0d902754bdc3f05511fcd43ffc7764a50fb41.\n\nDidn't work.","mergeCommitSha":"a06d8b51ddb2a1dc94cbe7e67238209098586010","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6795","title":"Revert \"Reduce search service consumer count to 1 to test a contention hypothesis (#6791)\"","createdAt":"2023-06-23T18:10:09Z"}
{"state":"Merged","mergedAt":"2023-06-26T20:58:16Z","number":6796,"body":"Setup Hub Suggestion. Hidden behind feature flag.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/3f8d8b24-689a-4145-8859-d0cb4330c5bb\r\n\r\nIntroduced multiple concepts:\r\n\r\n* Generic Suggestions that can be used within SuggestionTextField & Popover\r\n* BorderlessWindow which allows us to render a Window that takes in any SwiftUI View *within* a swiftUI View\r\n","mergeCommitSha":"45aab673083930d5704a9254a6c1973acf307e53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6796","title":"Hub suggestion","createdAt":"2023-06-23T18:15:06Z"}
{"state":"Merged","mergedAt":"2023-06-23T18:56:44Z","number":6797,"mergeCommitSha":"8d974da3d8c57bb928826a6dcc5209a33f3aaa17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6797","title":"Fix topic and expert summarizer","createdAt":"2023-06-23T18:55:04Z"}
{"state":"Merged","mergedAt":"2023-06-23T19:05:49Z","number":6798,"mergeCommitSha":"f489ac2558c81600d19b1ebb54839ef3326f6710","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6798","title":"Remove prefetch","createdAt":"2023-06-23T19:05:43Z"}
{"state":"Merged","mergedAt":"2023-06-23T19:48:59Z","number":6799,"mergeCommitSha":"dbbdc4962f3a18be0fa7998db1bf70ae4f533e36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6799","title":"FML","createdAt":"2023-06-23T19:45:53Z"}
{"state":"Merged","mergedAt":"2022-01-18T21:49:39Z","number":68,"body":"Fixes action failures on `main`:\r\nhttps://github.com/Chapter2Inc/codeswell/actions?query=branch%3Amain+is%3Afailure","mergeCommitSha":"51553b4bf94a45a67d287796abcd205d4d9616b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/68","title":"Paths Changes Filter requires checkout on push trigger","createdAt":"2022-01-18T21:37:47Z"}
{"state":"Merged","mergedAt":"2022-03-29T16:03:56Z","number":680,"body":"Had to resize Kube nodes in Dev to an issue with pod counts on nodes. EC2 instances have a limit on number of pods they can run (https://github.com/awslabs/amazon-eks-ami/blob/master/files/eni-max-pods.txt)\r\n\r\nThis was causing a lot of alarms and crashlooping pods\r\n\r\n\r\n**Note:** I have already deployed this to Dev Kube cluster","mergeCommitSha":"2cf4365e1d7dd46f74b569327576d9f0aec52e32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/680","title":"Resize kube nodes in dev","createdAt":"2022-03-28T19:23:25Z"}
{"state":"Merged","mergedAt":"2023-06-23T20:21:12Z","number":6800,"body":"Fixes https://app.logz.io/#/goto/3c1be6f672a239e20b6026ac75024c52?switchToAccountId=411850\r\n\r\nk.s.MissingFieldException: Fields [account_id, nickname] are required for type with serial name 'com.nextchaptersoftware.scm.bitbucket.models.BitbucketUser', but they were missing\r\n\tat k.s.i.PluginExceptionsKt.throwMissingFieldException(PluginExceptions.kt:20)\r\n\tat c.n.s.b.m.BitbucketUser.<init>(BitbucketUser.kt:7)\r\n\tat c.n.s.b.m.BitbucketUser$$serializer.deserialize(BitbucketUser.kt:7)\r\n\tat c.n.s.b.m.BitbucketUser$$serializer.deserialize(BitbucketUser.kt:7)\r\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\r\n\t... 60 common frames omitted","mergeCommitSha":"025c25c71b15a5cfa47076b773628b76177e0dbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6800","title":"Handle missing BitbucketUser fields","createdAt":"2023-06-23T20:03:11Z"}
{"state":"Merged","mergedAt":"2023-06-23T20:20:41Z","number":6801,"mergeCommitSha":"9f22bb2fd9d35d38de225c2ac4f441bd6a59def8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6801","title":"Add logging to topic summary updater","createdAt":"2023-06-23T20:20:12Z"}
{"state":"Merged","mergedAt":"2023-06-23T20:21:47Z","number":6802,"body":"Reverts NextChapterSoftware/unblocked#6801","mergeCommitSha":"59507771e75fef115c3cc0a742595d54b583ae81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6802","title":"Revert \"Add logging to topic summary updater\"","createdAt":"2023-06-23T20:21:42Z"}
{"state":"Merged","mergedAt":"2023-06-23T20:26:24Z","number":6803,"mergeCommitSha":"dbb3498ac1bfa992c0c6f3f7d95bb04c2b91398f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6803","title":"Add logging to topic summary updater","createdAt":"2023-06-23T20:26:03Z"}
{"state":"Merged","mergedAt":"2023-06-23T21:43:22Z","number":6804,"mergeCommitSha":"26b8ad4eb546bc1c5175823348330e4d3f57ed7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6804","title":"Debug logging","createdAt":"2023-06-23T21:40:15Z"}
{"state":"Merged","mergedAt":"2023-06-28T03:35:45Z","number":6805,"body":"New UI for the explorer insights panel\r\n\r\n* VSCode only for now -- once this is finalized I'll add it to IntelliJ too\r\n* The data here is not finalized yet -- next PR will add topic content to the Discussions view\r\n* The search bar isn't hooked up to anything yet (designs are yet to be finalized)\r\n* This is hidden behind a feature flag.\r\n\r\n<img width=\"1427\" alt=\"Screenshot 2023-06-26 at 10 41 39 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/d6fdc888-491b-457a-a03b-89da51709ba1\">\r\n<img width=\"1427\" alt=\"Screenshot 2023-06-26 at 10 41 42 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/11f59470-bc53-4970-a57f-6a388514708f\">\r\n","mergeCommitSha":"c87c061b5bff65f3b02b447a8f2c1f348077c977","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6805","title":"New explorer panel UI","createdAt":"2023-06-23T22:37:07Z"}
{"state":"Merged","mergedAt":"2023-06-23T23:38:51Z","number":6806,"body":"Fixes a bug introduced by https://github.com/NextChapterSoftware/unblocked/pull/6709","mergeCommitSha":"da08e0c149b238d5a0fb425cd068b2c99786e027","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6806","title":"Exclude unapproved topics","createdAt":"2023-06-23T22:54:18Z"}
{"state":"Merged","mergedAt":"2023-06-28T21:05:43Z","number":6807,"body":"Adds dashboard URLs to search results.\r\n\r\nPrimarily used for hub to navigate to dashboard when there's extra search results.","mergeCommitSha":"b0ee41ee8c841b6c236cf16fa0626b7ca758c7b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6807","title":"Add dashboard URL to search results","createdAt":"2023-06-23T23:35:15Z"}
{"state":"Merged","mergedAt":"2023-06-24T00:05:06Z","number":6808,"mergeCommitSha":"649b06612e5575fc6d2d6a769672b9d96ff68eca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6808","title":"More logging","createdAt":"2023-06-24T00:04:55Z"}
{"state":"Merged","mergedAt":"2023-06-27T19:17:46Z","number":6809,"body":"<img width=\"895\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/b58d2ddf-c518-43e2-8960-b273054a1334\">\r\n","mergeCommitSha":"b6ddccb274cb1236e70ddebb26bf33c3d6f6b55c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6809","title":"Add sort to param in search UI","createdAt":"2023-06-24T00:10:15Z"}
{"state":"Merged","mergedAt":"2022-03-28T20:45:58Z","number":681,"mergeCommitSha":"d6dc636a12d908a904ea8af18d96d3af5e81102a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/681","title":"Remove DataCacheStore","createdAt":"2022-03-28T20:05:19Z"}
{"state":"Merged","mergedAt":"2023-06-24T00:21:29Z","number":6810,"mergeCommitSha":"2a9b3222dba5fc48024ca5d7fee5563e989d0aaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6810","title":"Change message selector","createdAt":"2023-06-24T00:21:23Z"}
{"state":"Merged","mergedAt":"2023-06-24T00:33:47Z","number":6811,"mergeCommitSha":"df96526bc0a14019da8ea2bc99ca0f86319dfd78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6811","title":"Disable persistent","createdAt":"2023-06-24T00:33:40Z"}
{"state":"Merged","mergedAt":"2023-06-24T00:53:13Z","number":6812,"mergeCommitSha":"9a164f49f2dca60a249a4e7bcef956740c94a513","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6812","title":"Change timings","createdAt":"2023-06-24T00:53:03Z"}
{"state":"Merged","mergedAt":"2023-06-24T01:23:53Z","number":6813,"mergeCommitSha":"30cdb24ae855a9d71097bf6bfe0ebaa51bc64841","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6813","title":"Mot to async service","createdAt":"2023-06-24T01:23:24Z"}
{"state":"Merged","mergedAt":"2023-06-24T04:17:42Z","number":6814,"mergeCommitSha":"953c33447c4f0aa43d96bb2a613f51ce9b11d995","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6814","title":"Test non transactiona","createdAt":"2023-06-24T04:17:35Z"}
{"state":"Merged","mergedAt":"2023-06-24T04:51:20Z","number":6815,"body":"- Revert \"Test non transactiona (#6814)\"\r\n- Revert \"Mot to async service (#6813)\"\r\n- Revert \"Change timings (#6812)\"\r\n- Revert \"Disable persistent (#6811)\"\r\n- Revert \"Change message selector (#6810)\"\r\n- Revert \"More logging (#6808)\"\r\n- Revert \"Debug logging (#6804)\"\r\n","mergeCommitSha":"e82a4ddd76fd386386268d4abf1852977c243c8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6815","title":"RevertAllMyChanges","createdAt":"2023-06-24T04:50:49Z"}
{"state":"Merged","mergedAt":"2023-06-24T04:58:42Z","number":6816,"mergeCommitSha":"cd965d15fbf10b55d475699da25f73895a493621","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6816","title":"Upgrade activemq","createdAt":"2023-06-24T04:54:38Z"}
{"state":"Merged","mergedAt":"2023-06-26T17:19:03Z","number":6818,"mergeCommitSha":"63bbb425126b12c9896610f84b14958cb3d2deae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6818","title":"Increase task priority","createdAt":"2023-06-26T16:57:58Z"}
{"state":"Merged","mergedAt":"2023-06-26T17:45:20Z","number":6819,"mergeCommitSha":"2ece38898aac79f5fa80f5f2907ddee4e09649a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6819","title":"Allow viewing global prompts, but not editing","createdAt":"2023-06-26T17:35:31Z"}
{"state":"Merged","mergedAt":"2022-03-28T20:34:38Z","number":682,"body":"- Removed all unused dependencies\r\n- Removed duplicate detekt.yaml file and modified gradle to use the file a few levels up the tree.\r\n\r\nbuilds clean locally.","mergeCommitSha":"03aaea7510c7c62312be7ecc1640f5aad9958b16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/682","title":"Clean gradle dependencies to finish up API service migration","createdAt":"2022-03-28T20:18:35Z"}
{"state":"Merged","mergedAt":"2023-06-26T18:29:26Z","number":6820,"body":"- Deployed honeycomb proxy to both environments. \r\n- Finished setting up and verifying CI/CD for both environments. \r\n\r\nDeployments in Dev and Prod are currently set to two pods. We can scale them up as needed. For instructions about how to connect to the service as well as how to update the rules look at the README file at `infrastructure/eks/refinery/README.md`.\r\n\r\nNext step is to modify services in each environment to use the proxy. ","mergeCommitSha":"8ba4db861a6634ca6acf0c6f24df13177753316c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6820","title":"Setup honeycomb refinery","createdAt":"2023-06-26T18:12:05Z"}
{"state":"Merged","mergedAt":"2023-06-26T18:48:35Z","number":6821,"mergeCommitSha":"9d18a3cd25dcf3fa8620db1155c7ab62d7261da9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6821","title":"Fix null-kind bug for global templates","createdAt":"2023-06-26T18:46:26Z"}
{"state":"Merged","mergedAt":"2023-06-27T16:14:22Z","number":6822,"body":"The idea here is to include with the prompt the previous messages from the same thread. These templates will allow us to configure how those previous messages are formatted and where they're placed in the prompt.\r\n\r\n<img width=\"1843\" alt=\"CleanShot 2023-06-26 at 11 51 25@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/8134b9c8-139e-4ef0-8883-46c58342dab2\">\r\n","mergeCommitSha":"7ed2ce7964b0312c4fca76e7d8d1becdf3833747","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6822","title":"Chat continuation: Add templates for chat continuation to prompt","createdAt":"2023-06-26T18:54:31Z"}
{"state":"Merged","mergedAt":"2023-06-28T16:59:54Z","number":6823,"body":"This is flagged behind the same `SemanticSearch` feature flag as the hub. Teams without this flag enabled will see the old search input/UI.\r\n\r\n<img width=\"968\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/981be58e-63ab-41d9-a78e-9e116c33eab7\">\r\n\r\nSemantic search generation:\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13431372/6dc89f09-83ca-4760-a558-591ed36c58dd\r\n\r\nPG search integration:\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13431372/e3c7f527-8b82-48d2-b471-f641ff175190\r\n\r\n","mergeCommitSha":"3e64d90dfb8ee343bd5e30f1e84dacdfc4fa28d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6823","title":"Update dashboard search to QA input","createdAt":"2023-06-26T18:55:04Z"}
{"state":"Merged","mergedAt":"2023-06-26T20:12:52Z","number":6824,"body":"We need to alter activemq settings to allow for high priority jobs to always trump lower priority tasks.\r\nActiveMQ’s caching is catered towards high throughput which prevents this.\r\n\r\nSo we have changes a few things:\r\n1. Caching on clientSize is now 1. Clients never cache producer entries.\r\n2. Caching for message-selector based queues is increased to 50000 entries to allow them always pick up entries","mergeCommitSha":"820814b19161232e70426312b98b7527661ff39c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6824","title":"Attempt to fix activemq settings to alllow for high priority tasks","createdAt":"2023-06-26T19:58:46Z"}
{"state":"Merged","mergedAt":"2023-06-26T20:37:05Z","number":6825,"mergeCommitSha":"41aaffd3265646249967cf6ef166e319c7da6d5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6825","title":"Bump client assets commit","createdAt":"2023-06-26T20:16:59Z"}
{"state":"Merged","mergedAt":"2023-06-27T00:21:50Z","number":6826,"body":"before:\r\n<img width=\"785\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/22029df9-79d4-42c7-ad2a-c82054bfac99\">\r\n\r\nafter:\r\n<img width=\"794\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/492494a2-fb1f-4e93-946e-a925c832be99\">\r\n","mergeCommitSha":"c720f1a9d86e0efb8dcbd97a4858367f2ef9566c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6826","title":"Update video insight cards","createdAt":"2023-06-26T20:40:08Z"}
{"state":"Merged","mergedAt":"2023-06-26T23:15:47Z","number":6827,"mergeCommitSha":"e1964c9b1c77d9b4a8866e3ec371d132a9740ad2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6827","title":"Move stuff around","createdAt":"2023-06-26T20:49:02Z"}
{"state":"Merged","mergedAt":"2023-06-26T22:39:48Z","number":6828,"body":"This will run tests and show the summary of the test against previous runs. The next PR will allow us to click through on each run to view the details of each result","mergeCommitSha":"880d5d36f704eac343daad03408f5da974ab1b8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6828","title":"Add template testing page","createdAt":"2023-06-26T21:53:43Z"}
{"state":"Merged","mergedAt":"2023-06-27T16:57:55Z","number":6829,"body":"To allow chat continuation, we need to fire a bot search event when user adds a follow up message. \r\n\r\nMain changes:\r\n- move NotifyBotService out of `ThreadService` and into `ThreadsApiDelegateImpl`\r\n- add NotifyBotService to `MessageApiDelegateImpl`\r\n \r\nBlocked on https://github.com/NextChapterSoftware/unblocked/pull/6822","mergeCommitSha":"5ddb70475a66473d013f981c874920a9abc552a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6829","title":"Chat continuation: Trigger bot search event when a follow up message is created","createdAt":"2023-06-26T22:34:29Z"}
{"state":"Merged","mergedAt":"2022-03-29T18:35:59Z","number":683,"mergeCommitSha":"9de6fee3b5995785b643cc8c9f16a64b4112a12e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/683","title":"Polls installs every hour for org/repo updates","createdAt":"2022-03-28T20:19:39Z"}
{"state":"Merged","mergedAt":"2023-06-27T14:55:29Z","number":6830,"mergeCommitSha":"c9ac24c907436eae137c7e333bfdb73a3e3e8ef6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6830","title":"Offload individual tests to the search service","createdAt":"2023-06-26T22:38:11Z"}
{"state":"Merged","mergedAt":"2023-06-26T23:25:28Z","number":6831,"mergeCommitSha":"609fb6eb26de96945dbe9bb7644ed64573cec157","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6831","title":"Removes the last bit of pre Peter prompting from the team page.","createdAt":"2023-06-26T22:54:11Z"}
{"state":"Merged","mergedAt":"2023-06-27T18:30:14Z","number":6832,"mergeCommitSha":"81293812517964ecc57c4e7763ec95d9bec1caac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6832","title":"richify topic events","createdAt":"2023-06-27T01:43:13Z"}
{"state":"Merged","mergedAt":"2023-06-27T18:39:42Z","number":6833,"body":"Supports arbitrary SSH config hostname nicknames.\r\n\r\nThis was the _Cribl_ example which is verified with tests:\r\n```sh\r\nHost bucket\r\n    HostName bitbucket.org\r\n    User git\r\n    IdentityFile ~/.ssh/bitbucket.org_ed25519\r\n    ForwardAgent no\r\n```","mergeCommitSha":"1a648a2de146fcc82e50867946b4fec28735c720","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6833","title":"Resolve Git repo hostname using user's SSH config unblocking Cribl","createdAt":"2023-06-27T02:11:35Z"}
{"state":"Merged","mergedAt":"2023-06-27T17:20:16Z","number":6834,"body":"Setup new run configuration to run IntelliJ extension against local stack.","mergeCommitSha":"3ad43e0cfaa8361e316f3112787c2a6dcf9f2760","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6834","title":"Setup local intellij stack","createdAt":"2023-06-27T17:02:50Z"}
{"state":"Merged","mergedAt":"2023-06-27T17:05:15Z","number":6835,"mergeCommitSha":"2cd20d6ac7157030124c44f55df5acc0edff7e18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6835","title":"Wrap in transaction","createdAt":"2023-06-27T17:05:00Z"}
{"state":"Merged","mergedAt":"2023-06-27T18:40:49Z","number":6836,"body":"Context: bots members will always have `isCurrentMember == false`. The only exception is the unblocked bot.\r\nhttps://chapter2global.slack.com/archives/C02GEN8LFGT/p1687889130572009?thread_ts=1687887791.284319&cid=C02GEN8LFGT","mergeCommitSha":"4b34d251a9494989bae783d459c1d8fae72018c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6836","title":"Dont filter out bots","createdAt":"2023-06-27T18:23:25Z"}
{"state":"Closed","mergedAt":null,"number":6838,"body":"With chat continuation, when we pass the chat history we have a template to tell the model the participants in the chat. It might be a good idea to also include the identifier of the participant asking the question in `TemplatedQuery`.","mergeCommitSha":"a35c3cf89757241c075ac5bec6086c6018860139","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6838","title":"Chat continuation: Add team member ID to TemplatedQuery to give us the option of identifying the questioner in the prompt","createdAt":"2023-06-27T18:35:27Z"}
{"state":"Merged","mergedAt":"2023-06-27T20:07:58Z","number":6839,"mergeCommitSha":"4d9746c7cb2543daa5bd69320f8f003de419ad0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6839","title":"Queue load test","createdAt":"2023-06-27T18:45:25Z"}
{"state":"Merged","mergedAt":"2022-03-29T17:46:58Z","number":684,"body":"This PR adds an endpoint for each public service's APM checks. These checks are used by Grafana to monitor our infra availability. e.g to make sure we don't throw 502s during deployment rollovers.\r\n\r\nWe are only exposing shallow checks via Exact path matching. Deep checks are too expensive and someone could use them to DDoS our service.","mergeCommitSha":"11dd0b8504b81832da4701453151b15c4bf8569f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/684","title":"Expose APM endpoints for shallow checks","createdAt":"2022-03-28T20:33:01Z"}
{"state":"Merged","mergedAt":"2023-06-27T19:53:08Z","number":6840,"mergeCommitSha":"183fd5fa055f21e38fdb0c32da510b44ce0e8571","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6840","title":"Optimize q and a topics","createdAt":"2023-06-27T18:49:32Z"}
{"state":"Merged","mergedAt":"2023-06-27T19:28:52Z","number":6841,"mergeCommitSha":"ec7fb31e853ab175156c19f01bdba493212d344d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6841","title":"Privacy: Cleanup pinecone namespace when deleting team","createdAt":"2023-06-27T19:01:35Z"}
{"state":"Merged","mergedAt":"2023-06-30T17:16:46Z","number":6842,"body":"Consolidate form to create topics \r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13431372/9524b4dd-1d0f-4e7a-a5ae-009ff74384ea\r\n\r\n","mergeCommitSha":"fef2419662aeebf4539aa8a0b23257b1e59de5e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6842","title":"New create topic form","createdAt":"2023-06-27T19:41:21Z"}
{"state":"Merged","mergedAt":"2023-06-27T20:17:53Z","number":6843,"mergeCommitSha":"a1ff4a5faa6c5661395ffbffe6c4a1370ed8d4c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6843","title":"Chat continuation: filter out loading messages from chat history","createdAt":"2023-06-27T19:57:12Z"}
{"state":"Merged","mergedAt":"2023-06-27T20:53:10Z","number":6844,"mergeCommitSha":"0d850cd657bb5ae7767015f2851e654e03986e2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6844","title":"Catch exception","createdAt":"2023-06-27T20:04:49Z"}
{"state":"Merged","mergedAt":"2023-06-27T20:09:13Z","number":6845,"mergeCommitSha":"5cc6f20b9cb08a3464e4bc03390ecde82ad514f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6845","title":"Adds more detailed description to feature flags","createdAt":"2023-06-27T20:06:45Z"}
{"state":"Merged","mergedAt":"2023-06-27T21:53:39Z","number":6846,"mergeCommitSha":"691b52cd75a4293918ea1007908c9096a14d7f85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6846","title":"Render regression test page to see details of test","createdAt":"2023-06-27T20:33:25Z"}
{"state":"Merged","mergedAt":"2023-06-27T21:52:58Z","number":6847,"mergeCommitSha":"d09e4588cdc9bc99fb97f21d67880bf310baeaed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6847","title":"expert service","createdAt":"2023-06-27T21:29:32Z"}
{"state":"Merged","mergedAt":"2023-06-27T22:19:12Z","number":6848,"mergeCommitSha":"8f2546268772eeecba6945f2460999bd3e175885","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6848","title":"Fix editor","createdAt":"2023-06-27T21:54:01Z"}
{"state":"Merged","mergedAt":"2023-06-28T22:25:28Z","number":6849,"body":"Multiline text field\r\n \r\n \r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/8c1ed85f-4186-445e-992a-be01bdbaea6c\r\n\r\n\r\n\r\n","mergeCommitSha":"2bb869a3253c7442e8d274b3b88b958ce0def2b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6849","title":"Hub search multiline text field","createdAt":"2023-06-27T22:07:59Z"}
{"state":"Merged","mergedAt":"2022-03-28T21:32:00Z","number":685,"body":"Reverts NextChapterSoftware/unblocked#682","mergeCommitSha":"a4200a40e7f46d88752ad6558f8c4b6038b6ab0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/685","title":"Revert \"Clean gradle dependencies to finish up API service migration\"","createdAt":"2022-03-28T21:21:15Z"}
{"state":"Merged","mergedAt":"2023-06-28T00:05:13Z","number":6850,"mergeCommitSha":"f9881b2a29820a323a3777a0de8d53ebf6477a1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6850","title":"Move expert stuff out of topic service","createdAt":"2023-06-27T23:09:57Z"}
{"state":"Merged","mergedAt":"2023-06-27T23:53:02Z","number":6851,"mergeCommitSha":"6be261b66231e15e02cc9f714f1c66fdebeb3f1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6851","title":"Command click a table row to open in background page","createdAt":"2023-06-27T23:45:24Z"}
{"state":"Merged","mergedAt":"2023-06-28T00:24:25Z","number":6852,"body":"Use the same throttling pattern we use in the IDEs, and catch any logging failures.","mergeCommitSha":"6e43389202cd5b4212290f1057b5c7c6c91c570c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6852","title":"Throttle web logging","createdAt":"2023-06-28T00:12:01Z"}
{"state":"Merged","mergedAt":"2023-06-28T00:41:51Z","number":6853,"body":"- Add internal channel\r\n- Secrets\r\n","mergeCommitSha":"98c1d5a68833debd47a75d270218be0adb6a41fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6853","title":"Add internal slack notification for prod","createdAt":"2023-06-28T00:15:28Z"}
{"state":"Merged","mergedAt":"2023-06-28T21:48:50Z","number":6854,"body":"Add basic capability to delete thread in hub.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/ebf59031-976d-4bd5-a245-c4f59a36d280\r\n\r\n","mergeCommitSha":"83e5125a297400bc08833a7a89594b7b3368adc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6854","title":"Add basic delete thread for hub","createdAt":"2023-06-28T00:18:22Z"}
{"state":"Merged","mergedAt":"2023-06-28T01:22:33Z","number":6855,"mergeCommitSha":"c58e63ef67f487dfaffc48b150b4eafc58f33ec5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6855","title":"Tune validation pass criteria","createdAt":"2023-06-28T01:21:31Z"}
{"state":"Merged","mergedAt":"2023-06-28T01:49:38Z","number":6856,"mergeCommitSha":"87217c4a8b326135f38577cdcd7c7f231e9900a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6856","title":"Fix divide by zero and wrong progress value","createdAt":"2023-06-28T01:48:16Z"}
{"state":"Merged","mergedAt":"2023-06-28T01:56:55Z","number":6857,"body":"- Search service should be using insider service\r\n- Lint\r\n- noopinsiderservice\r\n","mergeCommitSha":"d97fd2c6c111b028f05a8fe3903cb60f771ea9e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6857","title":"MoveAwayFromNoopService","createdAt":"2023-06-28T01:56:19Z"}
{"state":"Merged","mergedAt":"2023-07-12T17:25:08Z","number":6858,"body":"Provide data for the new explorer UI:\r\n\r\n* `ActiveFileTopicQuestionStream` is a new stream that returns a list of questions for the current file, based on that file's associated topics.  It does a search to get QA threads for the topic, and if there aren't any, it will supply canned questions.\r\n* I rearranged a bit of the explorer insight data typing to make this work a bit smoother.  The topic and question data is now sent alongside the file insights in the `ready` state, instead of being separated.","mergeCommitSha":"951778f2996619b7296a535de451a96ff767389d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6858","title":"New explorer panel data","createdAt":"2023-06-28T04:04:45Z"}
{"state":"Merged","mergedAt":"2023-06-28T19:46:07Z","number":6859,"body":"- add lib-embedding abstraction to centralize embedding operations for a team\n- add button to clear (done) and rebuild (not done yet) golden record embeddings\n- cleanup team deletion making sure that embeddings are cleared properly","mergeCommitSha":"f5ddee05ecea7d8e7af90f56ecc7fcc8375d1a5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6859","title":"Placeholder to clear and rebuild golden record embeddings","createdAt":"2023-06-28T05:32:42Z"}
{"state":"Merged","mergedAt":"2022-03-28T22:39:55Z","number":686,"body":"We’ve had a longstanding problem where we were replicating config files across ervices.\r\n\r\nthis should not be done.\r\n\r\nTo avoid this:\r\n1. Create a config package\r\n2. All services are dependent on config package\r\n3. Any wonky overrides related to running in docker etc. should be done via environment variables.\r\n\r\nTESTING:\r\n1. Tested each service independent of next.\r\n2. Tested each service ran correctly via docker using \"run-local-stack\"","mergeCommitSha":"e6739d8194ec8a31ec39612bb272b61c120cc510","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/686","title":"Move to using a specialized config package shared across services","createdAt":"2022-03-28T22:04:08Z"}
{"state":"Merged","mergedAt":"2023-06-28T17:50:30Z","number":6860,"body":"We're finding that follow-up questions in chat continuations ignores the contents of the previous message. This change adds a couple new templates to allow us to experiment the prompt to address the issue.\r\n\r\nThe main idea here is to replace the query with the entire conversation (including the query) for a chat continuation. So for the first question, the query will be in the `templatedQuery`. When a user asks a follow-up question, that query will be in the `templatedConversation` along with the previous messages.\r\n\r\nOnce we go live with this, we can remove the three templates we added in https://github.com/NextChapterSoftware/unblocked/pull/6822","mergeCommitSha":"618de13e7b41209dad0ef47f01c0c8c8ea6cbd9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6860","title":"Chat continuation: replace query template with conversation template for chat continuations","createdAt":"2023-06-28T17:10:55Z"}
{"state":"Merged","mergedAt":"2023-06-28T17:38:15Z","number":6861,"mergeCommitSha":"163d3ea032f8014c921d861606697b4c34b16be1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6861","title":"Fix up slack notifications","createdAt":"2023-06-28T17:20:39Z"}
{"state":"Merged","mergedAt":"2023-06-28T18:44:39Z","number":6862,"mergeCommitSha":"33154178d6d28b562637b201ca7ef1d1eb799550","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6862","title":"Update codebase term","createdAt":"2023-06-28T18:12:52Z"}
{"state":"Merged","mergedAt":"2023-06-28T18:21:03Z","number":6863,"mergeCommitSha":"9d29f3ac4de0d352e9c8788cffe1c0079bb4bafa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6863","title":"Fix pr summary","createdAt":"2023-06-28T18:20:40Z"}
{"state":"Merged","mergedAt":"2023-06-28T21:10:09Z","number":6864,"body":"Pete's suggestion works well:\r\n\r\n```\r\nHuman: here are the rules:\r\n<rules>\r\n...\r\n</rules>\r\n\r\nAssistant: Acknowledged.\r\n\r\nHuman: Here is our conversation up until this point:\r\n<conversation>\r\n...\r\n</conversation>\r\n\r\nAssistant: Great.\r\n\r\nHuman: {question}\r\n\r\nAssistant:\r\n```\r\n\r\nLets remove final query from the conversation, and instead of replacing the query with the whole conversation just insert the conversation history above the query.","mergeCommitSha":"126f6636942f68f0422943774dfce8d5bd0dedbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6864","title":"Chat continuation: insert the conversation history instead of replacing the query with the whole chat","createdAt":"2023-06-28T18:55:04Z"}
{"state":"Merged","mergedAt":"2023-06-29T01:14:04Z","number":6865,"body":"- add event to embed a golden record\n- add event to remove embedding of a golden record\n- bulk backfill of golden records\n- refactoring","mergeCommitSha":"e6a185729e93f9391c7f679ec8fd934616dc228c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6865","title":"Refactor embedding code and embed golden records","createdAt":"2023-06-28T19:46:30Z"}
{"state":"Merged","mergedAt":"2023-06-28T19:53:42Z","number":6866,"mergeCommitSha":"e5371ca77a95d3027db311a8fab3434a6001f520","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6866","title":"Set anthropic client","createdAt":"2023-06-28T19:53:36Z"}
{"state":"Merged","mergedAt":"2023-06-29T21:10:15Z","number":6867,"mergeCommitSha":"c09187f80a671c219f5245a2fe7cd672a8a0148a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6867","title":"Remove the default 'Search' category on many of our DB queries for examples and validation records","createdAt":"2023-06-28T20:06:22Z"}
{"state":"Merged","mergedAt":"2023-06-28T20:12:40Z","number":6868,"mergeCommitSha":"9e9edc24e1299461d9b9ba26330c111c252de3b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6868","title":"Fix slack notifier","createdAt":"2023-06-28T20:12:32Z"}
{"state":"Merged","mergedAt":"2023-06-28T20:19:16Z","number":6869,"body":"I tried to setup CI/CD for this but it requires the deploy-user to have cluster-admin. That means the deploy user could modify resources in other namespaces including `kube-system` which a high security risk. For now we will continue with manual deployments. ","mergeCommitSha":"1616e88850adeb389e5f6fccdead80f123c01ced","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6869","title":"update falco rules to reduce noise","createdAt":"2023-06-28T20:19:09Z"}
{"state":"Merged","mergedAt":"2022-03-28T23:02:22Z","number":687,"body":"Update DataCacheStream Typing to help include initializedState for ThreadStore ForThread","mergeCommitSha":"8ba09b2e853aa3640c66fa377044a9442f8fc6d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/687","title":"Update ThreadStore ForThread","createdAt":"2022-03-28T22:18:23Z"}
{"state":"Merged","mergedAt":"2023-06-29T16:52:08Z","number":6870,"body":"We're using `TemplatedConversation` instead now","mergeCommitSha":"4dc6fe336105a794bad40013fb617eef8fe7dec0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6870","title":"Chat continuation: remove no longer used templates","createdAt":"2023-06-28T22:22:25Z"}
{"state":"Merged","mergedAt":"2023-06-28T23:21:47Z","number":6871,"body":"Was not using the teamID stored within user defaults.","mergeCommitSha":"48d719bda8f8ed6432529727185c9bbedd85bd4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6871","title":"Remember previous team","createdAt":"2023-06-28T22:24:31Z"}
{"state":"Merged","mergedAt":"2023-07-07T18:33:18Z","number":6872,"body":"With new API, open search results in dashboard if there are more than 10 results.\r\n\r\n<img width=\"591\" alt=\"CleanShot 2023-06-28 at 15 37 55@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/2ea3662a-03dd-4161-b015-196c619ae87f\">\r\n","mergeCommitSha":"beb8f2a4b77f434f5ea41f85f27cfaab54ac4025","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6872","title":"Open dashboard for related insights","createdAt":"2023-06-28T22:38:31Z"}
{"state":"Merged","mergedAt":"2023-06-29T00:06:56Z","number":6873,"body":"* When you focus the message editor in a QA thread with only you and the bot as participants, mention the bot right away.\r\n* Show a tooltip to warn the user about the bot behaviour and mentions.\r\n\r\nI added a new global API we can use -- `ClientWorkspace` (which is a global and can be used in any context) now has `getLocalStorage` and `setLocalStorage` helpers.  These can be used anywhere to store values in a local store.  I've implemented this in the dashboard only, but next PR will add them to the IDEs.  The IDE webviews will shuttle the values to and from the extension.\r\n\r\nI also added a new hook, `useLocalStorageState`, which is a drop-in replacement for `useState` that stores its value in local storage, so hooking UI and storage together is easy.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/27b3ca6c-e9c6-4d33-a632-0aa0daa53d0a\r\n\r\n<img width=\"717\" alt=\"Screenshot 2023-06-28 at 3 40 39 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/abe2f24d-b253-48e7-a974-4a83238f8255\">\r\n","mergeCommitSha":"a4dc03afd9742fddbd33124955cdd3a8ce99e0ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6873","title":"Add continuation bot instruction UI","createdAt":"2023-06-28T22:44:13Z"}
{"state":"Merged","mergedAt":"2023-06-29T16:35:37Z","number":6874,"body":"Upload bot assets to be used as avatar images. Will be returned to clients as part of TeamMember model in avatarURl.\r\n\r\nDecided against data urls as assets were significantly larger than existing avatarURL.\r\n\r\nUploading assets first before returning on backend.","mergeCommitSha":"7bb1b9e47eb9982c814c02c9abbdd2eef6f86194","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6874","title":"Upload bot assets","createdAt":"2023-06-28T22:45:12Z"}
{"state":"Merged","mergedAt":"2023-06-28T22:57:24Z","number":6875,"body":"This line of code is causing a crash because of unsafe Carbon bridging in the underlying library:\r\n```swift \r\nkeys = KeyboardShortcuts.getShortcut(for: _toggleSearch)?.description\r\n```\r\nThe underlying issue is that calling the Carbon API on any other dispatch queue except for main will blow up","mergeCommitSha":"3b7628f1d3c0425bb51fe4f2dc3aa28a3bab897a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6875","title":"Fix really bad crash","createdAt":"2023-06-28T22:48:57Z"}
{"state":"Merged","mergedAt":"2023-06-28T23:59:34Z","number":6876,"mergeCommitSha":"63ce2889ff60e1e4a4a286e15381c017ee8f84af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6876","title":"Move notifications to richie queue","createdAt":"2023-06-28T23:12:35Z"}
{"state":"Merged","mergedAt":"2023-06-29T00:02:24Z","number":6877,"mergeCommitSha":"46de14d294b306a2c9d12b99b074e12b6a48a9db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6877","title":"Fix name","createdAt":"2023-06-29T00:02:14Z"}
{"state":"Merged","mergedAt":"2023-06-29T00:59:33Z","number":6878,"mergeCommitSha":"78cb4150962ce8a2b999f0216f3c2b86f405eb20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6878","title":"Increase instance type","createdAt":"2023-06-29T00:59:19Z"}
{"state":"Merged","mergedAt":"2023-06-29T15:49:07Z","number":6879,"body":"## Changes\r\n\r\n - adds button to drop notes\r\n - include notes in pinecone document lookup\r\n\r\n## Rollout\r\n\r\n 1. team page button to drop all notes\r\n 2. team page button to embed golden records\r\n 3. golden record embeddings are automatically managed from this point on as sentiment is received","mergeCommitSha":"07a8bbff98544f03fdad63f94419f2f8e1be9813","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6879","title":"Enable golden record note documents","createdAt":"2023-06-29T06:10:25Z"}
{"state":"Merged","mergedAt":"2023-06-29T16:46:53Z","number":6880,"body":"This makes it consistent with the MessageEditor.\r\n\r\nAlso, the mention dropdown will match to both the display name and user name -- I figure people might have muscle memory from other services when mentioning.","mergeCommitSha":"8ef53e8207b8ffc71ae81dd6af64308215a446cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6880","title":"MessageView should render display names in @mentions","createdAt":"2023-06-29T16:26:46Z"}
{"state":"Merged","mergedAt":"2023-06-29T18:29:26Z","number":6881,"body":"Fix hub state machine issue.\r\n\r\nDelayedSetState was causing a infinite loop with focus state where we would flip between `search` & `searchFocused`\r\n\r\nWe had a \"delayedSetState\" because of the previous implementation where we wanted to delay rendering the SearchView. \r\nIn the current implementation, this is no longer necessary.\r\n\r\nBefore:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/90990fb0-46b1-4761-a3ef-64067e0b1ce1\r\n\r\nAfter:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/80c40059-4ae4-4482-a058-497d1cd72bd5\r\n\r\n","mergeCommitSha":"06eea7d0fe77400af81804af9db253f319e0140a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6881","title":"Fix Hub State Issue","createdAt":"2023-06-29T16:50:28Z"}
{"state":"Merged","mergedAt":"2023-06-29T18:28:33Z","number":6882,"mergeCommitSha":"ac1b6795f659e7a1eb095e74bd9cc8cb178c0113","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6882","title":"Wrap in transaction","createdAt":"2023-06-29T17:03:06Z"}
{"state":"Merged","mergedAt":"2023-06-29T22:08:53Z","number":6883,"mergeCommitSha":"d3935559ec72b5df5f670b520732771a53c34c3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6883","title":"Regression testing for topics and experts","createdAt":"2023-06-29T17:07:41Z"}
{"state":"Merged","mergedAt":"2023-06-29T20:55:20Z","number":6884,"body":"Remove feedback request when creating a message on a QA without feedback","mergeCommitSha":"b176d8c0ed4919cba3794d148fd08377e0d8d13e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6884","title":"Remove create feedback","createdAt":"2023-06-29T17:12:43Z"}
{"state":"Merged","mergedAt":"2023-06-29T18:48:10Z","number":6885,"mergeCommitSha":"2f0e7836bbc96ddedfac7dd471f4c7d13bedb771","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6885","title":"Fix Pinecone filter delete","createdAt":"2023-06-29T18:44:48Z"}
{"state":"Merged","mergedAt":"2023-06-29T19:39:53Z","number":6886,"body":"Bitbucket Cloud API vends 12 character merge commit SHAs.\n\nOur IDE clients supply full 40 character SHA-1.","mergeCommitSha":"818ca9e8f1dfb5a6f88af1b6a14577a279ce6c00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6886","title":"Fix merged commit hash lookup for Bitbucket Cloud","createdAt":"2023-06-29T19:03:59Z"}
{"state":"Merged","mergedAt":"2023-06-29T20:53:01Z","number":6887,"mergeCommitSha":"c04851f3f0a3535adebb4ce00949975312a6ecbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6887","title":"Richie queue for slack","createdAt":"2023-06-29T20:13:52Z"}
{"state":"Merged","mergedAt":"2023-06-29T20:47:02Z","number":6888,"body":"Prevent empty semantic search queries.\r\n\r\nTrigger suggestion field to close immediately after search.","mergeCommitSha":"225648655d89f389ac71713385c0da2f72d389de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6888","title":"Handle empty hub search","createdAt":"2023-06-29T20:20:05Z"}
{"state":"Merged","mergedAt":"2023-06-29T23:30:21Z","number":6889,"mergeCommitSha":"736a85dd2e093606d2787ac89537c183c990fa59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6889","title":"Add gitlab and bitbucket code-level insight icons","createdAt":"2023-06-29T20:22:13Z"}
{"state":"Closed","mergedAt":null,"number":689,"body":"Moving forward to clean database in local stack we need to run `make drop-database` explicitly.\r\n\r\nThis should make life easier for frontend folks when using local stack. \r\n\r\n","mergeCommitSha":"aa2dce3920782ac7837f56e9939edb30e9964d9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/689","title":"support persistent data in  run-local-stack","createdAt":"2022-03-28T22:34:26Z"}
{"state":"Merged","mergedAt":"2023-06-29T20:55:12Z","number":6890,"body":"* There was an issue with key events not being propagated between the input and the dropdown component, and the focus not being able to be shared between the two. This PR wraps the input with the [headlessui ComboBox component,](https://headlessui.com/react/combobox) which handles all interactive keyboard events \r\n* There'll be some additional work needed to refactor this into the current local ComboBox wrapper component, but that touches other files so it will be done in a separate PR","mergeCommitSha":"d4cd12a8c32d80dd32a0b7fc53948bb2ea3fec17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6890","title":"Fix key events in the dashboard QA input","createdAt":"2023-06-29T20:30:18Z"}
{"state":"Merged","mergedAt":"2023-06-29T23:32:15Z","number":6891,"body":"Missed this in the previous PR.  Add a placeholder with bot mention instructions, shown whenever the bot is part of a thread.  The text is a bit different than the popover tooltip @benedict-jw not sure if that matters?\r\n\r\n<img width=\"1483\" alt=\"Screenshot 2023-06-29 at 2 05 06 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/41a7aac3-8784-436a-9114-e18ac472b477\">\r\n","mergeCommitSha":"a07b7114f11ece996d7abe235f343f38fc569a8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6891","title":"Add placeholder text for bot mention","createdAt":"2023-06-29T21:06:41Z"}
{"state":"Merged","mergedAt":"2023-06-30T17:24:12Z","number":6892,"body":"- Added a refinery.env file to local secrets to hold the Honeycomb API key \r\n- Added refinery container to \r\n  - `docker-compose-ci.yml`\r\n  - `docker-compose-local.yml` \r\n  - `docker-compose-local-intellij.yml`\r\n- Exposed the following ports in docker compose for refinery:\r\n  - \"9091\"   # HTTP\r\n  - \"9090\"   # GRPC\r\n- Added two config files for refinery:\r\n  - `docker/refinery-config.toml` (Refinery process config goes here. You won't need to change it!)\r\n  - `docker/refinery-rules.toml` **(Refinery sampling rules go here. Modify as you need)**\r\n\r\nNote: You will need to these run once this has been merged for your local stack to continue working:\r\n\r\nAt the root of our repo:\r\n`cd secrets`\r\n`make decrypt-local-secrets` and provide the password stored in 1Password under the name `Local Env Vault`","mergeCommitSha":"2cebf459c57bc893061240d56f55211e22c96093","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6892","title":"adding refinery to local stack","createdAt":"2023-06-29T21:17:44Z"}
{"state":"Merged","mergedAt":"2023-06-29T22:16:01Z","number":6893,"mergeCommitSha":"4e4826298d708ed5a4692b9fbf31df6dd672f271","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6893","title":"Add template selector to semantic search page in admin console","createdAt":"2023-06-29T21:41:59Z"}
{"state":"Merged","mergedAt":"2023-06-29T22:28:45Z","number":6894,"mergeCommitSha":"5e2727600ac6cf15382a273e670fe2a3b5692987","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6894","title":"Richie queues for slack data","createdAt":"2023-06-29T21:42:55Z"}
{"state":"Merged","mergedAt":"2023-06-29T22:31:17Z","number":6895,"mergeCommitSha":"27a822b21f4582ab420e73b666ee2995f82d8a49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6895","title":"Admin: Stop impersonating from the banner","createdAt":"2023-06-29T22:27:26Z"}
{"state":"Merged","mergedAt":"2023-06-29T22:55:45Z","number":6896,"mergeCommitSha":"f444ba12372fbc92c95e2dad60c8ba3976f805dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6896","title":"Richie queues for scm data","createdAt":"2023-06-29T22:31:59Z"}
{"state":"Merged","mergedAt":"2023-06-29T23:23:55Z","number":6897,"mergeCommitSha":"d8e6c5d4a3d245e3373b8969dffa4ef3f366e5ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6897","title":"Richie queues for source code service","createdAt":"2023-06-29T22:57:15Z"}
{"state":"Merged","mergedAt":"2023-06-30T16:31:32Z","number":6898,"body":"When a user posts a follow up question, the prompt should include documents relevant to that question plus the questions that came before it. The changes in this PR include in the document search query the previous questions/messages in the thread.\r\n\r\nThis limits the document search query to only include non-bot messages. IMO, including bot responses in the document search doesn't make sense since it's possible these responses include things outside of our document embeddings. Happy to discuss, though.\r\n\r\nWe also need to limit the query used to created the embedding since I understand there is a limit. We only want to include the last `n` messages such that the total token length does not exceed the maximum. I've put this logic in the `SemanticSearchQueryService` but it might not be the best place for this.","mergeCommitSha":"b28fab9d247547542adbc1304bb5bb1ab9e419d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6898","title":"Chat continuation: include previous non-bot messages for document retrieval","createdAt":"2023-06-29T23:15:00Z"}
{"state":"Merged","mergedAt":"2023-06-29T23:48:29Z","number":6899,"mergeCommitSha":"5e1ec3d7f2bb46cf09ffa2191e684f37cc5f65c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6899","title":"Richie queues for folder data","createdAt":"2023-06-29T23:24:55Z"}
{"state":"Merged","mergedAt":"2022-01-18T23:55:44Z","number":69,"mergeCommitSha":"8122e4d9f7ed14f3463c36bf704388090437ef9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/69","title":"minor: gradle updates","createdAt":"2022-01-18T23:30:18Z"}
{"state":"Merged","mergedAt":"2022-03-28T22:50:07Z","number":690,"mergeCommitSha":"99ac9179234eaf87235c54f3096156cf9a089e4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/690","title":"Improve logging","createdAt":"2022-03-28T22:42:19Z"}
{"state":"Merged","mergedAt":"2023-06-30T00:13:30Z","number":6900,"mergeCommitSha":"2219efe075bae9707e0d8a485704d4108f9bf954","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6900","title":"Richie queue for transcription events","createdAt":"2023-06-29T23:49:41Z"}
{"state":"Merged","mergedAt":"2023-06-30T01:58:06Z","number":6901,"body":"Only add the `@Unblocked` bot mention for new messages, not when editing a message.","mergeCommitSha":"2b6eed30c0686bf79919edba5ec55cd185a24ff3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6901","title":"Don't add bot mention when editing a message","createdAt":"2023-06-30T00:07:35Z"}
{"state":"Merged","mergedAt":"2023-06-30T01:23:11Z","number":6902,"mergeCommitSha":"6eb62a78cedca205b13dab554e1eba77f5f39de9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6902","title":"Richie queues for video service","createdAt":"2023-06-30T00:15:50Z"}
{"state":"Merged","mergedAt":"2023-06-30T01:23:22Z","number":6903,"mergeCommitSha":"89af6b11caaf7e4bc08e7b5847b31f8e2f5aba68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6903","title":"Richie queus for linear data","createdAt":"2023-06-30T00:42:57Z"}
{"state":"Merged","mergedAt":"2023-06-30T00:52:22Z","number":6904,"body":"- hide neutral examples by default because the page is slow to load\n- order by recency\n- group examples by sentiment","mergeCommitSha":"3436b59a7696ff152c54a231ffd2a4ddc8b07133","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6904","title":"Fix inference page","createdAt":"2023-06-30T00:46:34Z"}
{"state":"Merged","mergedAt":"2023-06-30T01:52:42Z","number":6905,"body":"This was much easier than expected.\n\nWill need to reingest source code.","mergeCommitSha":"353c732318c6c8e44420bfd3dc2d645889dc8c4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6905","title":"Embed file path with source code embedding","createdAt":"2023-06-30T01:40:00Z"}
{"state":"Merged","mergedAt":"2023-06-30T02:40:44Z","number":6906,"mergeCommitSha":"41d3e7249378275aaa50755089a4676b02624c66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6906","title":"Fix up serialization of polymorphic types for events","createdAt":"2023-06-30T01:47:11Z"}
{"state":"Merged","mergedAt":"2023-06-30T02:19:30Z","number":6907,"mergeCommitSha":"2e46cd96a3f9e481a00da3daff63f5dae2ba9c5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6907","title":"Fix source code events queue name","createdAt":"2023-06-30T02:19:24Z"}
{"state":"Merged","mergedAt":"2023-06-30T03:26:41Z","number":6908,"mergeCommitSha":"7d120f643b62dfd22e59cb15f192b6d450ed4f81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6908","title":"Update event name","createdAt":"2023-06-30T03:26:36Z"}
{"state":"Merged","mergedAt":"2023-06-30T04:12:22Z","number":6909,"mergeCommitSha":"0e70c06f459351ccf33d1a5aac90ea1509eed008","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6909","title":"Make polymorphic types static for serialization","createdAt":"2023-06-30T03:43:11Z"}
{"state":"Merged","mergedAt":"2022-03-28T23:18:06Z","number":691,"mergeCommitSha":"d8f96b405ad8a71c641d0692f64886f171ce4e24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/691","title":"update","createdAt":"2022-03-28T23:14:37Z"}
{"state":"Merged","mergedAt":"2023-06-30T07:09:37Z","number":6910,"mergeCommitSha":"9bdd8d065a4916644b459dcd35c6965b51fba086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6910","title":"Fix bert","createdAt":"2023-06-30T07:07:24Z"}
{"state":"Merged","mergedAt":"2023-07-04T17:40:44Z","number":6912,"body":"Sometimes users will send follow-up messages without @-mentioning Unblocked, but will edit their message to add it. When this happens, we should get a bot message.\r\n\r\nThis change also adds logic so that if a bot response has already been created it won't create another. ","mergeCommitSha":"aa379a14da8c6f7c8051e35520e7c04cea60c643","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6912","title":"Chat continuation: get a bot response if a user updates a message to @-mention unblocked","createdAt":"2023-06-30T17:59:13Z"}
{"state":"Merged","mergedAt":"2023-06-30T18:52:10Z","number":6913,"body":"This is used for document retrieval for a query","mergeCommitSha":"25cde20b9e45e9ebfd6675e85bb6896c2ccf6640","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6913","title":"Chat continuation: set InstructorEmbedder.inputMaxTokenSize to 8192","createdAt":"2023-06-30T18:30:51Z"}
{"state":"Merged","mergedAt":"2023-07-10T16:25:22Z","number":6914,"mergeCommitSha":"a78ef2846c8a62b58c1ad10381778d8ce6aa00c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6914","title":"IMprove pr summary prompt","createdAt":"2023-06-30T18:42:09Z"}
{"state":"Merged","mergedAt":"2023-07-04T18:02:29Z","number":6915,"body":"Fixes:\r\n* Casing mismatch in experts dropdown\r\n* Spaces not being reflected in dropdown inputs\r\n* Enter to submit QA threads \r\n* :focus-within state not respected in Safari on button click ","mergeCommitSha":"eb9417916232342bad80f6958bbfb9181e9769e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6915","title":"Dashboard bug fixes","createdAt":"2023-07-01T02:07:44Z"}
{"state":"Closed","mergedAt":null,"number":6916,"body":"## Changes\n\n- [x] new DocumentEmbeddingServices for experts and summaries\n- [ ] generate embedding event when creating/modifying summaries\n- [ ] generate embedding removal event when topics/experts are removed (this is important to clean up the embedding space as these will skew the results)\n\n## Note\n\nThese are still the short-form summaries. We need to generate long-form summaries that are 500 words in length.","mergeCommitSha":"4f92ca9763b99969776ae05c98bbcbd134f5adcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6916","title":"Embed topic and expert summaries","createdAt":"2023-07-04T03:44:52Z"}
{"state":"Merged","mergedAt":"2023-07-04T16:55:01Z","number":6917,"body":"https://chapter2global.slack.com/archives/C045VGYML95/p1688450217771699","mergeCommitSha":"e02c9ba6e20bc94b8d66110b7f895165a2a93ab1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6917","title":"Upvote slack announcements","createdAt":"2023-07-04T15:27:34Z"}
{"state":"Merged","mergedAt":"2023-07-06T22:17:18Z","number":6918,"body":"API proposal for granular feedback\r\n\r\n<img width=\"1494\" alt=\"Screenshot 2023-07-04 at 10 17 03 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/50c693f9-20b1-4d79-b433-555d4ce78569\">\r\n\r\n- Add an enum (`FeedbackType`) containing the types of feedback we allow (for now: approve, disapprove, ambiguous (for 'meh' or 'ok'))\r\n- This enum replaces the boolean approve/disapprove in MessageFeedback and UpdateMessageFeedbackRequest\r\n- This PR adds the enum to the feedback model, and keeps the old (boolean) value.  Values are translated both ways.\r\n- I've added a migration as well\r\n- Once we've migrated, we can remove the old boolean values","mergeCommitSha":"b2b3920f772e542c2a3da37de7b2298e3ef6b159","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6918","title":"Granular feedback API","createdAt":"2023-07-04T17:17:55Z"}
{"state":"Merged","mergedAt":"2023-07-04T18:32:29Z","number":6919,"body":"API test are generating real slack messages\r\nhttps://chapter2global.slack.com/archives/C05FA43NM4N/p1688485269272639","mergeCommitSha":"343066244eff220a77d046d222cc254286bed9ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6919","title":"API tests should not send slack messages","createdAt":"2023-07-04T17:49:37Z"}
{"state":"Merged","mergedAt":"2022-03-29T01:13:53Z","number":692,"body":"Per: https://github.com/lettuce-io/lettuce-core/blob/6.1.8.RELEASE/src/test/java/io/lettuce/examples/ConnectToMasterSlaveUsingElastiCacheCluster.java","mergeCommitSha":"5d0c434dc7dc43a3798ea4545add4836a10237e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/692","title":"Use elasticache master/replica client","createdAt":"2022-03-28T23:19:12Z"}
{"state":"Merged","mergedAt":"2023-07-04T18:32:14Z","number":6920,"mergeCommitSha":"655a494687f929972d49d41ad826a817601ad78b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6920","title":"Work harder to filter out generated code","createdAt":"2023-07-04T17:49:40Z"}
{"state":"Merged","mergedAt":"2023-07-06T23:09:49Z","number":6921,"body":"Setup Topic View for Hub Onboarding. Currently hidden way from users in an unaccessible state.\r\nWill be working on \"Ask Unblocked a Question\" next.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/ed1c24d8-e0dc-43bb-8885-195feaf32906\r\n\r\n<img width=\"966\" alt=\"CleanShot 2023-07-04 at 09 06 41@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/fcf62d33-ceba-46da-a626-c61d7cf13508\">\r\n","mergeCommitSha":"9f00788f063014bc9d6d1cac504a8f9b47035407","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6921","title":"Hub Onboarding Topic view","createdAt":"2023-07-04T18:04:54Z"}
{"state":"Merged","mergedAt":"2023-07-04T19:15:30Z","number":6922,"mergeCommitSha":"6f2341c0bd37fb0a7e51dfc5ca0596238deefbda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6922","title":"Set the thread author to be the author of the second message if the first message is deleted","createdAt":"2023-07-04T18:26:50Z"}
{"state":"Merged","mergedAt":"2023-07-04T19:27:20Z","number":6923,"body":"Previous change broke prod secrets, because I fat findered and encrypted using local secret by mistake.","mergeCommitSha":"3ad5897b57231a81a9c1b3a7f2971bbe3fc7a040","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6923","title":"Fix prod secret encryption","createdAt":"2023-07-04T19:26:26Z"}
{"state":"Merged","mergedAt":"2023-07-04T22:24:59Z","number":6924,"mergeCommitSha":"1eae1b5d9f4d55541ecaf6ddea301c6cafb45892","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6924","title":"Add hubPRTItle feature flag","createdAt":"2023-07-04T21:59:40Z"}
{"state":"Merged","mergedAt":"2023-07-05T05:13:45Z","number":6925,"body":"Setup Hub PR Title AB Test.\r\n\r\nWith flag disabled:\r\n<img width=\"586\" alt=\"CleanShot 2023-07-04 at 15 33 35@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/762ea753-76e5-4136-8707-f33d757886cb\">\r\n\r\nWith flag enabled:\r\n<img width=\"583\" alt=\"CleanShot 2023-07-04 at 15 39 32@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/9ebd8e33-4d56-4cde-9188-d0130c2a9206\">\r\n","mergeCommitSha":"aeac124a93ea5d5bcf97364f6c2ca0c5294f946b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6925","title":"Setup Hub PR Title","createdAt":"2023-07-04T22:46:32Z"}
{"state":"Merged","mergedAt":"2023-07-05T00:08:48Z","number":6926,"body":"If a thread was previously archived by the system but the service judges the thread to have valuable contents, it should unarchive the thread. If the thread was previously archived by a team member, do nothing.","mergeCommitSha":"b016c679d3a7dc00a146ddcd323845195b88d713","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6926","title":"Restore a system archived thread if deemed now relevant","createdAt":"2023-07-04T22:52:40Z"}
{"state":"Merged","mergedAt":"2023-07-04T23:25:20Z","number":6927,"body":"https://www.notion.so/nextchaptersoftware/Reference-Generation-29e2b1db30994470aaebda30317290a3\r\n\r\nNext PRs will add: TF-IDF, embedding relevance engines.","mergeCommitSha":"7e6aa2c304f15fd027c819276377dc150110f5b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6927","title":"Dummy relevance engine","createdAt":"2023-07-04T23:10:12Z"}
{"state":"Merged","mergedAt":"2023-07-05T00:26:30Z","number":6928,"body":"Fixes this bug: https://app.intercom.com/a/inbox/crhakcyc/inbox/admin/5472711/conversation/579?view=List\r\n\r\nThe problem is that visible text editors can appear outside of the tab list -- specifically, they can appear in the bottom row (all the editors in the Output tab).  The check we were doing here was incorrect, and the end result is if you had one of the Output tabs open, editors would open in the wrong order.\r\n\r\nTBH the way we specify the column ordering here is kind of wonky, I'd like to refactor this but I'll do that another time.","mergeCommitSha":"42b9d83a378da9e07fcaa488dd1d6ac25f507db9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6928","title":"Fix editor tab ordering in VSCode","createdAt":"2023-07-04T23:13:21Z"}
{"state":"Merged","mergedAt":"2023-07-05T04:49:45Z","number":6929,"mergeCommitSha":"cb5cb8a11c1a2247bf57bd9cf89722f59347fcce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6929","title":"TF-IDF (Term Frequency-Inverse Document Frequency) for document references","createdAt":"2023-07-05T00:31:58Z"}
{"state":"Merged","mergedAt":"2022-03-28T23:58:56Z","number":693,"body":"1. Remove code dupe.\r\n2. Move to modern java arguments (probably should dedupe this as well)","mergeCommitSha":"9fd14a4bbb98d36063856159be63a6f1f04759e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/693","title":"Generalize service health checks","createdAt":"2022-03-28T23:43:14Z"}
{"state":"Merged","mergedAt":"2023-07-05T05:10:12Z","number":6930,"body":"More resilient to special characters now.\r\n\r\nAddresses this failure:\r\nhttps://us-west-2.console.aws.amazon.com/cloudwatch/home?region=us-west-2#logsV2:log-groups/log-group/$252Faws$252Fsagemaker$252FProcessingJobs/log-events/SourceCodeEmbeddings-bdac95ba-c2be-4dde-b089-c6516989ba1a$252Falgo-1-**********\r\n\r\n```c\r\nUnicodeDecodeError: 'utf-8' codec can't decode byte 0xd1 in position 1: invalid continuation byte\r\n\r\nTraceback (most recent call last):\r\n  File \"/process_source_code.py\", line 121, in <module>\r\n    process_source_code_data(inputs=task_inputs)\r\n  File \"/process_source_code.py\", line 99, in process_source_code_data\r\n    source_files_processed = process_repo(repo_dir)\r\n  File \"/process_source_code.py\", line 59, in process_repo\r\n    files: list[str] = FileTraversal(root_path=root_path, min_file_size=256).list_text_files()\r\n  File \"/file_traversal.py\", line 78, in list_text_files\r\n    return self.list_text_files_recur(self.abs_root_path)\r\n  File \"/file_traversal.py\", line 99, in list_text_files_recur\r\n    text_files.extend(self.list_text_files_recur(abs_file_path))\r\n  File \"/file_traversal.py\", line 99, in list_text_files_recur\r\n    text_files.extend(self.list_text_files_recur(abs_file_path))\r\n  File \"/file_traversal.py\", line 99, in list_text_files_recur\r\n    text_files.extend(self.list_text_files_recur(abs_file_path))\r\n  [Previous line repeated 4 more times]\r\n  File \"/file_traversal.py\", line 94, in list_text_files_recur\r\n    if not self.skip_file(abs_file_path, rel_file_path, filename):\r\n  File \"/file_traversal.py\", line 117, in skip_file\r\n    if self.is_generated_file(abs_file_path):\r\n  File \"/file_traversal.py\", line 70, in is_generated_file\r\n    page = file.read(page_size)\r\n  File \"/usr/local/lib/python3.10/codecs.py\", line 322, in decode\r\n    (result, consumed) = self._buffer_decode(data, self.errors, final)\r\n```","mergeCommitSha":"7c27304c0f033daff183e43b6a6f57dd4564c194","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6930","title":"Source code ingest handles special characters","createdAt":"2023-07-05T05:07:32Z"}
{"state":"Merged","mergedAt":"2023-07-05T17:32:09Z","number":6931,"mergeCommitSha":"bbb34e4dac95694eca65e5ad0a6cd487bfd16023","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6931","title":"Debug why references are always listed as not \"Included\" in inference page","createdAt":"2023-07-05T17:29:53Z"}
{"state":"Merged","mergedAt":"2023-07-05T18:54:25Z","number":6932,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/e8e431d5-6d4a-4748-88de-ac5e0ae9d0ec\r\n\r\nThis PR also updates the routes to from `/topic/` to `/component`\r\n\r\n","mergeCommitSha":"860f42f5013b8ecefae6e957c7509bd5613a6a7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6932","title":"Move components into settings","createdAt":"2023-07-05T17:47:50Z"}
{"state":"Merged","mergedAt":"2023-07-05T23:40:59Z","number":6933,"body":"## Background\r\n\r\nClient has a sourcemark store, which is a projection of the PG DB source of truth.\r\nOnce the client store has been initially loaded, the client relies on the sourcemark\r\ncursor to keep the client store perfectly synchronized with the DB store.\r\n\r\n## Problems\r\n\r\n- Archived and deleted items were not included in the synchronization responses.\r\n- Client store was not persisting arrived and deleted state changes.\r\n- SM engine was not filtering out archived and delete marks.\r\n\r\n## Changes\r\n\r\n- When a last modified cursor has been supplied, we now include all items modified\r\n  since the previous timestamp, even if they have been archived or deleted.\r\n- Client now updates isArchived and isDeleted state in the store cache.\r\n- SM engine now filters out marks that are deleted or archived.","mergeCommitSha":"30c4075dc92ce3bf7a45cdadfa86f06085ebe5ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6933","title":"SourceMarkProvider.getSourceMarksForFile returns outdated sourcemarks","createdAt":"2023-07-05T18:51:07Z"}
{"state":"Merged","mergedAt":"2023-07-10T17:07:07Z","number":6934,"body":"* Fix a bug where the floating window (used in the context menu) would clear the selected row state, as its `hide` callback would fire on every click.\r\n* Only override the selected row while the context menu is open (the context menu state is now view-specific)","mergeCommitSha":"2a790dcfbee9c6922f943e9dd6a6f5ed4ab3ecf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6934","title":"Fix VSCode row selection","createdAt":"2023-07-05T20:10:43Z"}
{"state":"Merged","mergedAt":"2023-07-21T20:14:23Z","number":6935,"body":"SVGs just do not work well in Swift. No native support for SVGs and the libraries would not want to work well with Bitbucket default avatar which is a SVG with data url built within.\r\n\r\nAs a workaround, we will no longer render SVG avatars in the Hub. Render initials icon instead.\r\n\r\n<img width=\"606\" alt=\"CleanShot 2023-07-05 at 13 50 10@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/7faf0799-0e45-4a2f-a821-768d630b13cc\">\r\n","mergeCommitSha":"c995557baebb919d830734d57359a3f928109e13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6935","title":"Setup backup initials for svgs","createdAt":"2023-07-05T20:54:07Z"}
{"state":"Merged","mergedAt":"2023-07-05T22:00:43Z","number":6936,"body":"Bug: index `0` is falsey so hitting enter on the first suggested thread element led to us creating a new QA thread instead of just opening the first thread","mergeCommitSha":"ff3a7df07a3038a9e65a6169c2e4ff7bbfe73a37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6936","title":"Fix submit bug","createdAt":"2023-07-05T21:18:43Z"}
{"state":"Merged","mergedAt":"2023-07-20T05:40:26Z","number":6937,"body":"Update bot avatar. Opted for hosted asset s data url was 3x the size.\r\nhttps://dev.getunblocked.com/bot-assets/bot-avatar.svg","mergeCommitSha":"ca78a8b753b180dcc5354edd54bdeed9813e9f65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6937","title":"Update bot avatar url to new image","createdAt":"2023-07-05T21:19:39Z"}
{"state":"Merged","mergedAt":"2023-07-05T22:31:54Z","number":6938,"body":"Rejig how the VSCode launch commands work:\r\n\r\n* Rename the labels to be shorter (so they don't clip) and consistent (`action:env` like we do elsewhere)\r\n* Group by action (watch vs debug)\r\n* Fix prod debug and watch actions -- these would previously use the user defaults environment value, now they won't\r\n\r\n<img width=\"409\" alt=\"Screenshot 2023-07-05 at 3 05 50 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/bb3aea49-94f7-4640-a41d-4d3732f2fde0\">\r\n","mergeCommitSha":"d5e29363c733809c92b3ee51be0382eb8b3e5f05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6938","title":"Update VSCode launch commands","createdAt":"2023-07-05T22:07:56Z"}
{"state":"Merged","mergedAt":"2023-07-05T22:31:13Z","number":6939,"mergeCommitSha":"ad21d1ae1eeeda1cba5769c5a7e6f9a7e92d2c47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6939","title":"More debug for references listed as not \"Included\" in inference page","createdAt":"2023-07-05T22:29:47Z"}
{"state":"Merged","mergedAt":"2023-07-07T06:21:45Z","number":6940,"body":"Main changes:\r\n- Adds a `MessageModel.references` property, which will be a subset of `MLInferenceResult.documents`.\r\n- Updates the API service to return a value for `Message.referenceMetadata`. This property will be null if reference resolution is in progress, non-null if complete or if message is not a bot response\r\n- Adds a migration to backfill `MessageModel.references` for bot messages\r\n\r\nTODO (probably in follow up PRs)\r\n- Call the `RelevanceEngine` to populate the `MessageModel.references` field\r\n- Add a flag so that the API service only returns `Message.referenceMetadata` in dev while we iterate on the `RelevanceEngine`","mergeCommitSha":"7645fabcd30d4d0462a47ad954abcf5bbefc8f8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6940","title":"Implement Message.referenceMetadata","createdAt":"2023-07-06T00:05:00Z"}
{"state":"Merged","mergedAt":"2023-07-06T01:25:46Z","number":6941,"body":"In the current search templates `maxExamples=0` so with this change we'll save a few\ncycles fetching these documents on every search query, and only show the examples on\nthe inference result page if they were supplied to the template.","mergeCommitSha":"d7ea9171029515dc4419bb581b68bb5d46e36436","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6941","title":"ML template maxExamples constraint is actually used now","createdAt":"2023-07-06T00:59:17Z"}
{"state":"Merged","mergedAt":"2023-07-17T21:12:26Z","number":6942,"body":"* Passing a single `prevRoute` property meant every time a DetailLayoutView stacks on top of the other, the previous 'prevRoute' gets overwritten. This means when closing the Detail view, we can only display current  route -1 before losing track of the stack\r\n* Instead of passing through a single route string in the state, we should store a list of routes in the state and treat that as the stack. This means that we should only push a route to the navigation stack list iff we're navigating to a DetailLayoutView (thread/pr/settings)\r\n* I've explored writing a context instead (i.e. keeping track of stacked routes in a setState, and moving away from leveraging the navigation state). The issue with this approach is that at best we'd have to write out own `navigate` function and completely lose the ability to use the built-in react router `Navigate`/`Link` components (we do something similar here with a helper hook for navigation but this is just a wrapper for the router `navigate` fn) \r\n    * still open to doing this if we think that's the right thing to do","mergeCommitSha":"2aeee669b9a1a1dd2d478db14ce7ea69fd74daaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6942","title":"Fix dashboard layout stacking","createdAt":"2023-07-06T16:53:35Z"}
{"state":"Merged","mergedAt":"2023-07-06T20:04:46Z","number":6943,"mergeCommitSha":"e93783e8655ba0edc855d168d78f45b5a650b67c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6943","title":"Include package.json files in source code ingestion","createdAt":"2023-07-06T18:33:26Z"}
{"state":"Merged","mergedAt":"2023-07-07T22:59:49Z","number":6944,"body":"This doesn't change _anything_ in the API.","mergeCommitSha":"453b2d8aa7f1b54f5c4276607dfa82e4e0f605f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6944","title":"Added the team name and topic name to the topic change slack notif","createdAt":"2023-07-06T19:56:01Z"}
{"state":"Merged","mergedAt":"2023-07-06T20:19:43Z","number":6945,"body":"* Set the relevant score to `1` on topic creation (regression, this was missed on the refactor)\r\n* Manually show/hide the delete component section (only in edit topic views)","mergeCommitSha":"d1cd7b0fa5336fd44ec5689f64b05c4140a4cd39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6945","title":"Fix dashboard topic management","createdAt":"2023-07-06T20:00:45Z"}
{"state":"Merged","mergedAt":"2023-07-07T18:47:46Z","number":6946,"body":"This is to allow showing references in order of relevance so that most relevant will be first in the list.","mergeCommitSha":"f3316bc9fd97b1c4f4dee5ae0df6bdb872e5c3c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6946","title":"Replace ReferenceMetadata with an ordered list of references","createdAt":"2023-07-06T20:10:48Z"}
{"state":"Merged","mergedAt":"2023-07-07T02:22:59Z","number":6947,"mergeCommitSha":"95bd706f900196d7c931e01304bbf77f0de762ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6947","title":"Add MLReferenceResolverType as template option","createdAt":"2023-07-06T22:49:03Z"}
{"state":"Merged","mergedAt":"2023-07-07T03:27:54Z","number":6948,"mergeCommitSha":"badeee70e7ff3e94f727fe7ada670a354c56eb3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6948","title":"Add resolve references event handler","createdAt":"2023-07-06T22:56:50Z"}
{"state":"Merged","mergedAt":"2023-07-07T16:15:29Z","number":6949,"body":"Source Code Pro optically looks larger than Sofia Pro. I'm dropping it down so it looks on par with our body font.\r\n\r\n**Code Block Header - Current:**\r\n\r\n<img width=\"761\" alt=\"CleanShot 2023-07-06 at 15 56 34@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/913507dc-3ddc-4e07-8b8e-108f2fb3f763\">\r\n\r\n**Code Block Header - Proposed Change:**\r\n\r\n<img width=\"761\" alt=\"CleanShot 2023-07-06 at 15 57 33@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/244f69e2-3f3a-4740-b5fb-21bbf9a45517\">\r\n\r\n**Inline Code Block - Current:**\r\n\r\n<img width=\"630\" alt=\"CleanShot 2023-07-06 at 16 01 34@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/da24fbd0-14a7-4a93-8d6d-7d3be3dc1d94\">\r\n\r\n**Inline Code Block - Proposed Change:**\r\n\r\n<img width=\"627\" alt=\"CleanShot 2023-07-06 at 16 01 12@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/4083dab6-575e-4f03-bcfb-05edb765daca\">\r\n\r\n**Message Editor Code Block - Current:**\r\n\r\n<img width=\"667\" alt=\"CleanShot 2023-07-06 at 16 02 47@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/7c4f01ec-0459-4878-a42a-851bfe1c0f1a\">\r\n\r\n\r\n**Message Editor Code Block - Proposed Change:**\r\n\r\n<img width=\"669\" alt=\"CleanShot 2023-07-06 at 16 02 24@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/625af93d-db76-43bd-bf6e-6300fa9dc51b\">\r\n\r\n","mergeCommitSha":"e27e9295a1f6b52554324eacf8bca8e28ffdfa7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6949","title":"Drop code font sizes","createdAt":"2023-07-06T23:00:33Z"}
{"state":"Merged","mergedAt":"2023-07-07T18:30:52Z","number":6950,"body":"API to return sample questions used for onboarding purposes.\r\n\r\n<img width=\"717\" alt=\"CleanShot 2023-07-06 at 16 07 06@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ee069687-1db5-491e-be1c-28b5cbcb5642\">\r\n\r\nI think there's some expectations that we pre-generate the responses.","mergeCommitSha":"4c3c685a40b1dbdef29a6bcc03ca5db4b1b5449d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6950","title":"Sample question api","createdAt":"2023-07-06T23:07:52Z"}
{"state":"Merged","mergedAt":"2023-07-10T19:01:19Z","number":6951,"body":"First draft of end to end Hub onboarding flow.\r\n\r\nWould like to get this in as the PR is getting large and it would be nice to start dog-fooding the flow while the details are implemented.\r\n\r\nHidden behind feature flag.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f56cfeac-0710-43f8-be69-551eaa14c41b\r\n\r\nRemaining work:\r\n* Initial \"Welcome Page\" UI flourish\r\n* Video Assets\r\n* Real API data for recommended questions.\r\n","mergeCommitSha":"36179e8a81ea0c62b000702b0449384b69ce5e3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6951","title":"First Draft Hub Tutorial","createdAt":"2023-07-06T23:10:51Z"}
{"state":"Merged","mergedAt":"2023-07-07T03:48:26Z","number":6952,"mergeCommitSha":"4e9fa28896b4368ad1dde183f6ca6347d5f88a14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6952","title":"Send reference resolution event at tail end of semantic search result","createdAt":"2023-07-06T23:13:45Z"}
{"state":"Merged","mergedAt":"2023-07-07T05:06:49Z","number":6953,"body":"This is needed so that source code file references are related to their parent repo.\n\nIt's a mess in here right now, and very fragile.","mergeCommitSha":"e7fa99ebae3d019624945288bc53af4786eebd56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6953","title":"Introduce ReferenceArtifact and plumb SourceCode repoId from Pinecone to ReferenceArtifact","createdAt":"2023-07-07T00:05:47Z"}
{"state":"Merged","mergedAt":"2023-07-11T22:41:06Z","number":6954,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/0cecca9b-6795-4c74-aae5-a71151387788\r\n\r\n* This leverages the existing `getRecommendedThreads` API right now; I suspect we'll need to augment the existing API to fit the new UI intentions\r\n* Is under the `/recommended` route in the dashboard, will need to directly navigate to the route to see the UI while in development ","mergeCommitSha":"ac95b009c124d0dcd743016302ec327637b70525","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6954","title":"Recommended feed first pass","createdAt":"2023-07-07T00:52:24Z"}
{"state":"Merged","mergedAt":"2023-07-07T15:25:03Z","number":6955,"mergeCommitSha":"e85a842e1fe8d543c2c623574e18cd12b1dc915d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6955","title":"Implement ResolveReferencesEventHandler.handle","createdAt":"2023-07-07T06:47:56Z"}