{"state":"Merged","mergedAt":"2023-06-12T17:22:16Z","number":6459,"body":"Updated the following operations to handle isPrivate.\r\n\r\ngetThreadsForMe - NOOP\r\ngetThreadsForPullRequest\r\ngetThreadsForCommits\r\ngetPullRequestThreads\r\nfetchThreads\r\ngetThreadRecommendations\r\ngetThreadsArchived\r\ncreateThread\r\ngetThread\r\n\r\nTODO: Search\r\n","mergeCommitSha":"c1cf5b1611a67a367716e81d24087eb1348243c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6459","title":"Setup isPrivate on backend","createdAt":"2023-06-08T18:44:24Z"}
{"state":"Closed","mergedAt":null,"number":646,"body":"Threads returned by the API service are sorted such that those with the most recent messages come first","mergeCommitSha":"6b47679a8980559e4192957978bdee74d6d63561","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/646","title":"Sort threads by lastMessageCreatedAt desc","createdAt":"2022-03-23T06:43:47Z"}
{"state":"Merged","mergedAt":"2023-06-14T16:01:50Z","number":6460,"body":"The approach here is to left join the `ThreadParticipantModel` on the `SearchInsightModel` when running a search query and check to see that the team member running the search is participant on that insight.\r\n\r\nThis means adding a `SearchInsightModel.insightIsPrivate` property so that we can do that check when this field is true.\r\n\r\nThis is blocked on the addition of the `ThreadModel.isPrivate` field because ultimately `SearchInsightModel.insightIsPrivate` needs to take its value from thread model.\r\n\r\n","mergeCommitSha":"9969346801bf23542df1b72763c4cde9a33ef227","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6460","title":"Update search to include a private thread only when the team member is a participant","createdAt":"2023-06-08T18:49:44Z"}
{"state":"Merged","mergedAt":"2023-06-08T19:15:49Z","number":6461,"mergeCommitSha":"87fa4a3f1ea9e526eab1e699ca7a3ee901cf3892","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6461","title":"Improve topic extraction","createdAt":"2023-06-08T19:01:25Z"}
{"state":"Merged","mergedAt":"2023-06-08T20:48:40Z","number":6462,"body":"Adding a reader instance to Dev Postgres. \r\n\r\n~Once this has been deployed I'll provide the hostname so we could point pusher at it.~\r\nLooks like Dev already has the reader endpoint configured for pusher so traffic should start shifting to new instance automatically. I'll monitor. ","mergeCommitSha":"de52460ca64ef0948806f3c601ed1959cf6c1da5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6462","title":"add reader instance to dev","createdAt":"2023-06-08T20:43:31Z"}
{"state":"Merged","mergedAt":"2023-06-09T02:39:59Z","number":6463,"mergeCommitSha":"e0b020db9a9e8439402b35751ddcefbe6a0daa9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6463","title":"Move some stuff around in the ML models","createdAt":"2023-06-08T20:45:18Z"}
{"state":"Merged","mergedAt":"2023-06-08T21:32:10Z","number":6464,"body":"To reduce the number of write ops","mergeCommitSha":"8b15baa13fe2b9ed2cfb8facf0d208c1cf203aee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6464","title":"Don't call upsert if the `GitHubIssuesIngestionModel` already exists","createdAt":"2023-06-08T20:47:22Z"}
{"state":"Merged","mergedAt":"2023-06-08T21:55:21Z","number":6465,"body":"Adding the beginnings of MLInferenceExample management in the admin console","mergeCommitSha":"048f987e9eedf70df50adefe616cc4f23fb067d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6465","title":"Add ability to promote a generated Q&A to an MLInferenceExample","createdAt":"2023-06-08T21:30:02Z"}
{"state":"Merged","mergedAt":"2023-06-08T23:47:31Z","number":6466,"body":"API will be implemented once this is in: https://github.com/NextChapterSoftware/unblocked/pull/6459","mergeCommitSha":"ddb3a4388c473f4e9218dd574cf304e559d2c63c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6466","title":"Add isPrivate Thread property","createdAt":"2023-06-08T22:24:28Z"}
{"state":"Merged","mergedAt":"2023-06-09T22:05:11Z","number":6467,"body":"This will help with generating questions and answers for larger pull requests. The prompt may need some tuning since this is a different model but that's an ongoing thing.","mergeCommitSha":"d286dd4c8c7ffa5a38774024d90d3af46204c575","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6467","title":"Use anthropic for generating questions and answers from pull requests","createdAt":"2023-06-08T23:27:30Z"}
{"state":"Merged","mergedAt":"2023-06-09T07:41:55Z","number":6468,"mergeCommitSha":"4fe88c435fd022f964cc27f0febf26f86410a6ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6468","title":"Wire up the templates and results","createdAt":"2023-06-08T23:41:05Z"}
{"state":"Merged","mergedAt":"2023-06-09T00:03:23Z","number":6469,"body":"When a user added a message to a thread from an Unblocked UI we record the\nevent as a signal of activity.\n\nHowever, we incorrectly recorded the _thread_ author as the actor, instead\nof the _message_ author.","mergeCommitSha":"e2cadf33762a2d9cbb2f213021d8026b7ee25288","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6469","title":"Fix for incorrect MessageCreated activity attribution","createdAt":"2023-06-08T23:56:51Z"}
{"state":"Merged","mergedAt":"2022-03-25T23:19:28Z","number":647,"body":"- Adds a `deleteMessage` operation so that clients can allow deleting messages from a thread\r\n- Add validations to ensure only the message author can update or delete a message ","mergeCommitSha":"3041119a3c82ccb59cb6061d91b41075eabe424d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/647","title":"Add deleteMessage operation","createdAt":"2022-03-23T18:17:57Z"}
{"state":"Merged","mergedAt":"2023-06-09T02:47:06Z","number":6470,"mergeCommitSha":"0727dd4a5663cf1439f4ea7ea0a9f6d867b5f330","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6470","title":"Log the sematic search prompt in PY-land","createdAt":"2023-06-09T00:26:14Z"}
{"state":"Merged","mergedAt":"2023-06-09T00:56:00Z","number":6471,"mergeCommitSha":"c1950cc54868e2d5f8eeb6de5a1e4e15c727752a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6471","title":"Add anthropic search completion service","createdAt":"2023-06-09T00:44:11Z"}
{"state":"Merged","mergedAt":"2023-06-09T16:24:28Z","number":6472,"mergeCommitSha":"fabe4c14b4fe4caca27542b119be5407b67be861","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6472","title":"Fix feedback delete","createdAt":"2023-06-09T16:18:50Z"}
{"state":"Merged","mergedAt":"2023-06-09T20:38:53Z","number":6473,"body":"Add a `setContent` method to MessageInputContext, so anyone in the context can set the content for an editor in the context.\r\n\r\nEach `MessageInput` registers itself as a handler within its context, and will be called to set content as necessary.","mergeCommitSha":"62f9d0e24bb58f82f4e83a9218607cceec19f586","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6473","title":"Allow setting msg editor content via context","createdAt":"2023-06-09T17:03:36Z"}
{"state":"Merged","mergedAt":"2023-06-09T19:23:45Z","number":6474,"mergeCommitSha":"09eac3377106ad9a16640573e21e605ba98f8cdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6474","title":"Use upsert","createdAt":"2023-06-09T18:08:55Z"}
{"state":"Merged","mergedAt":"2023-06-17T00:26:29Z","number":6475,"mergeCommitSha":"b317475b024a499010fd66dfcd9d44f5fdd35cb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6475","title":"Update insightCounts and searchInsights request to reflect provider","createdAt":"2023-06-09T18:27:35Z"}
{"state":"Merged","mergedAt":"2023-06-09T19:34:27Z","number":6476,"mergeCommitSha":"98a888a1bde39dc9bcd1c10c3945e92366b54847","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6476","title":"Can't delete global template","createdAt":"2023-06-09T18:32:53Z"}
{"state":"Merged","mergedAt":"2023-06-09T19:58:31Z","number":6477,"body":"<img width=\"1912\" alt=\"CleanShot 2023-06-09 at 11 43 18@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/91bd368f-39ae-4850-bb6e-682e75677880\">\r\n\r\n<img width=\"1916\" alt=\"CleanShot 2023-06-09 at 11 43 30@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/a202a778-af8a-4a1b-94aa-f7c4b24a3c69\">\r\n","mergeCommitSha":"4e0962aaad2f95fae02be95ad5ecf03b924ea2ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6477","title":"Clean up inference examples page in the admin console","createdAt":"2023-06-09T18:43:48Z"}
{"state":"Merged","mergedAt":"2023-06-09T19:26:34Z","number":6478,"body":"- Increased deployment timeout by 2 mins. \r\n- Added helm rollback step and gave it a max timeout of 5 mins \r\n\r\nWe will adjust the timeout further later if necessary. Next step would be to fix deploy jobs to support a re-run. ","mergeCommitSha":"7df36e5315b787e6680652cf4a8c832b653c0ddd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6478","title":"add helm rollback on job cancelation","createdAt":"2023-06-09T19:10:09Z"}
{"state":"Merged","mergedAt":"2023-06-09T22:07:46Z","number":6479,"mergeCommitSha":"88b99083a4693400ce9ebf76aa1495907e7c7bb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6479","title":"Add query template","createdAt":"2023-06-09T19:14:17Z"}
{"state":"Merged","mergedAt":"2022-03-23T23:16:13Z","number":648,"body":"Super basic first pass at moving PR ingestion to the SCM service. Next step is to break down PR ingestion into smaller chunks so that ingestion does all happen in one go of the job instance.\r\n\r\nTested locally and this works","mergeCommitSha":"b5588313c77cd7d6a9251de59ee9dffb017281f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/648","title":"Move pull request ingestion to background service","createdAt":"2022-03-23T21:20:17Z"}
{"state":"Merged","mergedAt":"2023-06-09T21:03:25Z","number":6480,"body":"I am hoping that after this we can re-run deploy jobs without having to re-run the entire pipeline","mergeCommitSha":"9ac858f63053b2145a067201f8e9ff2a67267485","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6480","title":"trying to support deploy job reruns","createdAt":"2023-06-09T19:53:55Z"}
{"state":"Closed","mergedAt":null,"number":6481,"body":"before:\r\n<img width=\"855\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/be34d8fb-b245-4819-b380-0ce31af2064c\">\r\n\r\nafter:\r\n<img width=\"849\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/d913e976-ed5b-4d80-bff3-372fe584dcbd\">\r\n","mergeCommitSha":"61080910a4b73955218fdd37e1c23df2ed4fbd1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6481","title":"Wrap pre block","createdAt":"2023-06-09T20:33:06Z"}
{"state":"Merged","mergedAt":"2023-06-09T22:05:33Z","number":6482,"mergeCommitSha":"bf958311f966b37aa09c4fc6200af407c482811d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6482","title":"Render prompt on semantic search page","createdAt":"2023-06-09T21:20:22Z"}
{"state":"Merged","mergedAt":"2023-06-09T21:39:19Z","number":6483,"mergeCommitSha":"258480e36f98fb832870bbbc40df100e09394bc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6483","title":"Move to ml alb endpoints","createdAt":"2023-06-09T21:22:55Z"}
{"state":"Merged","mergedAt":"2023-06-09T21:36:13Z","number":6484,"body":"Reverts NextChapterSoftware/unblocked#6480","mergeCommitSha":"79f3b590ce77f2e96aad7c19f2834d04b19938fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6484","title":"Revert \"trying to support deploy job reruns\"","createdAt":"2023-06-09T21:36:05Z"}
{"state":"Merged","mergedAt":"2023-06-09T21:50:47Z","number":6485,"body":"Also exlcude videos because they are mostly demo noise.\n\nAnd exclude PR comments as they are mostly very low value.","mergeCommitSha":"c94bc0b08f50e41602a64ea24ea41f572e797a7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6485","title":"Excluded notes from document retrieval because these are mostly previous unapproved Q&As","createdAt":"2023-06-09T21:40:15Z"}
{"state":"Merged","mergedAt":"2023-06-09T21:45:07Z","number":6486,"mergeCommitSha":"1dd264eb0a042e69a4306d396d97c971d94bd1f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6486","title":"FML","createdAt":"2023-06-09T21:44:51Z"}
{"state":"Merged","mergedAt":"2023-06-09T22:06:03Z","number":6487,"body":"Fixes #6486\r\n\r\n<img width=\"768\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/d29652cb-9295-4423-abd6-582464775ff2\">\r\n","mergeCommitSha":"e11915907364ee59d3d1454fc59619991fac8503","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6487","title":"Fix disabled button","createdAt":"2023-06-09T22:02:44Z"}
{"state":"Merged","mergedAt":"2023-06-14T17:52:35Z","number":6488,"mergeCommitSha":"3a36560c12fa57b7945fc1a025009b0bd2357f5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6488","title":"Add method to set provider for existing SearchInsightModels","createdAt":"2023-06-09T22:10:12Z"}
{"state":"Merged","mergedAt":"2023-06-12T22:41:48Z","number":6489,"body":"Match new designs in the dashboard:\r\n\r\n* `MessageInput` can show two kinds of sending UIs: `inline` (the paper plane button within the editor, used for replying) and `outside` (cancel/send buttons below, used for editing).\r\n* Add inline send button to MessageEditor\r\n* Dashboard reply editor now has no top border and padding matches thread content\r\n\r\nNote: the IDE UIs are largely unchanged, I will update them next week after getting some feedback from Ben.\r\n\r\n<img width=\"1465\" alt=\"Screenshot 2023-06-09 at 3 31 15 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/62357e16-430a-4b24-962e-8c3c9875f065\">\r\n","mergeCommitSha":"c2d6c432fae2681ded267281fd20a15bee6863bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6489","title":"Update message sending buttons and UI","createdAt":"2023-06-09T22:33:01Z"}
{"state":"Merged","mergedAt":"2022-03-23T23:01:33Z","number":649,"mergeCommitSha":"362190e9dd6556f42a0b69538b96bf5aec937eac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/649","title":"Use providerExternalInstallationId in PullRequestIngestion","createdAt":"2022-03-23T21:54:10Z"}
{"state":"Merged","mergedAt":"2023-06-13T19:37:34Z","number":6490,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/89e85651-8d8c-4d5b-872a-b59bdf07c722\r\n\r\nAt the moment, all logic is done client-side. We take the list of topics generated for the thread, map them into a single list, and render the experts. For the invite flow, we filter out the thread author and the current user. \r\n\r\nUltimately I think we need to build an API to get this list of experts and sort them appropriately given the list of topics (i.e. if X is an expert of 3 of the thread topics, they should be 'weighted' higher). We can centralize the discrimination logic as well as control how many experts to show on the service.","mergeCommitSha":"1e42e227d2814d612b81434e140164583b717e66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6490","title":"Integrate ask expert functionality","createdAt":"2023-06-09T22:50:00Z"}
{"state":"Merged","mergedAt":"2023-06-10T00:00:04Z","number":6491,"body":"Make it more visible.","mergeCommitSha":"c82b93fd45af057dfb1f98cdf602c218954d68fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6491","title":"Move ML outside the overflow menu","createdAt":"2023-06-09T23:35:16Z"}
{"state":"Merged","mergedAt":"2023-06-09T23:52:21Z","number":6492,"body":"Motivation:\n\n1. Many PRs have little data which skews the lookup scores. For example,\n   a query \"What are insights\" will score a PR that has \"Test insights\" but\n   nothing else (no description, no comments) very highly because it's a\n   close vector match.\n\n   Correct long term solution is to filter out low value PRs and threads\n   during ingestion.\n\n2. PRs do not provide knowledge about the _current_ state of the system,\n   which means they are not useful for queries about the steady state of\n   the system. PRs are more appropriate for temporal queries like \"what's\n   Doug been up to recently\", \"who has been fixed perf issues\", \"what is\n   the buggiest componet this quarter\", \"when did we enable the landing\n   page\". Right now we're more interested in getting steady state\n   knowledge since our documents/prompts do not have a concept of time\n   yet.","mergeCommitSha":"7b768185b22310735875aea5bf01535a3cbd57ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6492","title":"Exclude PR documents for semantic search","createdAt":"2023-06-09T23:50:09Z"}
{"state":"Merged","mergedAt":"2023-06-10T02:47:54Z","number":6493,"body":"I figured it out. The ordering of the element _content_ and element _attributes_\r\nis important. All attributes must be declared _before_ the content, otherise the\r\ncontent would need to be re-rendered once the attributes are set.\r\n\r\nSee related #6487.","mergeCommitSha":"1844214dbeed316791282cf937420b2dbc38404c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6493","title":"Actually fix disabled toggle in ActionButton","createdAt":"2023-06-10T02:46:52Z"}
{"state":"Merged","mergedAt":"2023-06-10T23:30:45Z","number":6494,"body":"Bot accounts (identities and members) are managed as follows:\r\n\r\n1. Team member maintenance, which is responsible for creating team members, invokes the\r\n   bot account manager to ensure that a Bot member exists.\r\n\r\n2. The account manager is responsible for upserting exactly one _Bot member_ for a team,\r\n   lazily creating the member when invoked. It creates the mmeber based on the\r\n   appropriate Bot identity for the team SCM provider.\r\n\r\n3. The account manager ensures that a deterministic Bot _identity_ exists for every SCM\r\n   provider. Identities are global SCM-specific, not team-bound. Identities are upserted\r\n   lazily as needed when upserting bot members.\r\n\r\nSee also #6409\r\n\r\n<img width=\"790\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/b1fd8ea1-a70e-492e-92ad-49c0e052c700\">\r\n","mergeCommitSha":"6206ff2b9df8f2737ab7b1652703250da550ad03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6494","title":"Introduce BotAccountManager to ensure that an Unblocked Bot account exists in each team","createdAt":"2023-06-10T06:48:27Z"}
{"state":"Merged","mergedAt":"2023-06-10T22:30:03Z","number":6495,"body":"Discussion:\nhttps://chapter2global.slack.com/archives/C045VGYML95/p1686378186488329?thread_ts=**********.125839&cid=C045VGYML95","mergeCommitSha":"3abcd1c2da12230a5cbc0b81b4acca412d53f592","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6495","title":"Do not embed archived PRs","createdAt":"2023-06-10T06:54:46Z"}
{"state":"Merged","mergedAt":"2023-06-10T07:42:20Z","number":6496,"body":"- Do not enable embeddings\n- Do not enable embeedings\n","mergeCommitSha":"f0574c93c22e76001920e9f4aeeacdf7ac6995a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6496","title":"DoNotEnableEmbeddings","createdAt":"2023-06-10T07:42:06Z"}
{"state":"Merged","mergedAt":"2023-06-14T17:25:31Z","number":6497,"body":"This allows any thread (new or existing) to mention the bot and generate a bot response.\n\nBlocked on #6409.","mergeCommitSha":"ca7edcf9931608dda6a61422886799e50a1985bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6497","title":"Remove isQaThread from createThread; use bot mention instead","createdAt":"2023-06-11T01:00:57Z"}
{"state":"Merged","mergedAt":"2023-06-13T06:31:40Z","number":6498,"body":"- [x] Delete by team (for team delete)\r\n\r\nWill do these in follow up changes:\r\n- [ ] Delete by team and source provider (for integration uninstallation)\r\n- [ ] Delete by team and group (for repo, slack channel, jira project uninstall)","mergeCommitSha":"46199b8eed675dc0d0a34c65f14567c1f4cc6cde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6498","title":"Ability to selectively drop vectors from Pinecone","createdAt":"2023-06-11T01:58:57Z"}
{"state":"Merged","mergedAt":"2023-06-11T04:26:56Z","number":6499,"body":"Removes this `(edited)` pill. Open to a better way of implementing this.\r\n\r\n\r\n## Before\r\n<img width=\"528\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/3b022489-3ed8-407a-ac01-0c72ba3c8a34\">\r\n\r\n\r\n## After\r\n<img width=\"678\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/6bb1c412-22b2-4261-b22b-e8f59ff31da1\">\r\n","mergeCommitSha":"9b5a57353e2d7001e0e5b54be5428190db5be0ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6499","title":"Do not mark streaming message updates from LLMs as edited","createdAt":"2023-06-11T04:01:11Z"}
{"state":"Merged","mergedAt":"2022-01-18T20:34:52Z","number":65,"body":"Allows us to require all status check in monorepo.","mergeCommitSha":"7d142159f4bd719bf400afe344085e684215ce5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/65","title":"Skip workflow if targetted files have not changed","createdAt":"2022-01-18T20:30:45Z"}
{"state":"Merged","mergedAt":"2022-03-25T19:07:36Z","number":650,"body":"\r\nhttps://user-images.githubusercontent.com/1553313/159812200-0a1faa37-71e0-4390-b9cb-9fdbfaed273e.mp4\r\n\r\nBasic setup for web extension.\r\n\r\nAdded message passing with port manager.\r\nBasic Auth and Thread loading.\r\n\r\nTODO:\r\n* Issues with content + background lifecycle. If one refreshes the page, previous instances of connections are not 100% cleaned up.\r\n\r\n\r\n\r\n\r\n","mergeCommitSha":"43831f5ece355dc7863be5a837d2099733aecad5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/650","title":"Very basic setup with Extension","createdAt":"2022-03-23T23:21:49Z"}
{"state":"Merged","mergedAt":"2023-06-11T05:15:31Z","number":6500,"body":"<img width=\"768\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/8bd96fd1-e02b-4dca-a998-051ce1a6efce\">\r\n","mergeCommitSha":"2c942260810aeec977c95d8785d12c48c04e4c3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6500","title":"Source code embedding filters import types from more languages","createdAt":"2023-06-11T05:11:09Z"}
{"state":"Merged","mergedAt":"2023-06-11T05:43:22Z","number":6501,"body":"When repo was made optional, the INNER join on RepoModel in admin web search\nno longer matched a result. This changes it to LEFT join to reflect the new model.","mergeCommitSha":"bac514eb8c442f3db258f1dd315ed1023aa78eec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6501","title":"Fix admin thread search for threads without repos","createdAt":"2023-06-11T05:31:38Z"}
{"state":"Merged","mergedAt":"2023-06-13T03:45:24Z","number":6502,"body":"<img width=\"1269\" alt=\"Screenshot 2023-06-12 at 16 02 12\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/8139bc77-a42b-478a-990d-f24be4a011fc\">\r\n","mergeCommitSha":"5eb2b921e1dc36dafcab95e05b42b91780f78eec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6502","title":"Ability to view GitHub installations in admin web","createdAt":"2023-06-12T05:50:37Z"}
{"state":"Merged","mergedAt":"2023-06-12T18:09:12Z","number":6503,"body":"Fix retry request.\r\nAdd unit tests for retry.","mergeCommitSha":"41bb7ad88e3fd0d0a9b61ff9e49d23676d56d3bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6503","title":"Fix retry request","createdAt":"2023-06-12T16:46:39Z"}
{"state":"Merged","mergedAt":"2023-06-13T17:53:32Z","number":6504,"body":"Implement API to update thread privacy","mergeCommitSha":"6223856ba3ef65553e57f2f52c5bea482e3b1a2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6504","title":"[BREAKS API ON MAIN] API to update thread privacy","createdAt":"2023-06-12T18:09:35Z"}
{"state":"Merged","mergedAt":"2023-06-12T20:12:27Z","number":6505,"body":"This PR just adds some additional structure and the ability to move examples between the 3 categories:\r\n- Golden\r\n- Validation\r\n- Unlabeled\r\n\r\nNext up: Rending the example/result page to view the prompt that was used, the documents, as well as edit the answer and provide feedback.\r\n\r\nFuture note: when we run experiments, we'll probably need to filter this list to product generated examples only or the experiment results will bleed into this list, which could create quite a lot of duplication. Maybe just a toggled filter for this?","mergeCommitSha":"365ce6c17c1d4d901b9049e82e9c0b3517a8065d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6505","title":"Organize Inference Examples page and add actions","createdAt":"2023-06-12T19:16:12Z"}
{"state":"Merged","mergedAt":"2023-06-12T19:32:59Z","number":6506,"body":"{\r\n  \"PullRequestSummary\": {\r\n    \"ProcessEnvironment\": {\r\n      \"IsDavidLovely\": \"true\"\r\n    },\r\n    \"ProcessingJobName\": \"DavidIsLovely\",\r\n    \"ProcessOutput\": \"s3://pull-request-data-pipeline-sandbox-dev-us-west-2\"\r\n  }\r\n}\r\n\r\n<img width=\"492\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/3806658/f8698217-a207-4035-bbd8-c931ffb65eaa\">\r\n\r\nhttps://us-west-2.console.aws.amazon.com/states/home?region=us-west-2#/v2/executions/details/arn:aws:states:us-west-2:129540529571:execution:PullRequestDataPipelineStateMachine:befecff3-0789-4383-b137-ee26af6eed02","mergeCommitSha":"b102d9473f1d2e268953132987fe088f1338062d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6506","title":"Add framework for pull request data pipeline","createdAt":"2023-06-12T19:20:23Z"}
{"state":"Merged","mergedAt":"2023-06-15T05:47:44Z","number":6507,"body":"Updated clients to render private banner.\r\n\r\n<img width=\"1143\" alt=\"CleanShot 2023-06-12 at 11 49 08@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/e78c73d1-7ed2-4d50-882f-563972aa602e\">\r\n<img width=\"1095\" alt=\"CleanShot 2023-06-12 at 11 50 30@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/9f7cf91b-ee27-487e-9d2c-4e06c65b9298\">\r\n\r\nIDE Clients have a more generic styling for the private banner.\r\n\r\n<img width=\"1111\" alt=\"CleanShot 2023-06-12 at 12 03 07@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ea4d7120-7346-498c-affd-4b8b7ed04dfa\">\r\n<img width=\"540\" alt=\"CleanShot 2023-06-12 at 12 55 29@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/7edfcd9d-4094-43d6-9a04-45b8f158a0e7\">\r\n","mergeCommitSha":"50f278e2a175497e0d4428868a813716236ba033","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6507","title":"Private thread banner","createdAt":"2023-06-12T19:58:32Z"}
{"state":"Merged","mergedAt":"2023-06-14T01:56:54Z","number":6508,"body":"Add `bot` user status to our icons; get rid of any extra styling/invite UIs for bot users.\r\n\r\n<img width=\"814\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8e67d069-c7d3-4ccc-bfd5-165b74fe52aa\">\r\n","mergeCommitSha":"8b4a03c5a5707b153ed07252cb3b7fcee89ea301","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6508","title":"Add bot user status","createdAt":"2023-06-12T20:26:54Z"}
{"state":"Merged","mergedAt":"2023-06-12T21:54:57Z","number":6509,"mergeCommitSha":"d15d57e7622afa11bb74d5f125a0d110f9a160df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6509","title":"Include CSS and SCSS files in source code embeddings","createdAt":"2023-06-12T21:48:15Z"}
{"state":"Merged","mergedAt":"2022-03-23T23:53:15Z","number":651,"mergeCommitSha":"6ce1dcc2e29b5451920c5f8a25eb0c55660860d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/651","title":"Fix query for PR ingested threads","createdAt":"2022-03-23T23:34:56Z"}
{"state":"Merged","mergedAt":"2023-06-12T21:51:10Z","number":6512,"mergeCommitSha":"ac5362b0c6e81b33ab089d1fdcae6b498a9d0d28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6512","title":"Add glue etl","createdAt":"2023-06-12T21:51:05Z"}
{"state":"Closed","mergedAt":null,"number":6513,"mergeCommitSha":"1380948902440bffd438f77333dd752015b48b08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6513","title":"Add Provider to PullRequest","createdAt":"2023-06-12T22:11:39Z"}
{"state":"Merged","mergedAt":"2023-06-12T22:12:17Z","number":6514,"mergeCommitSha":"58aa5184d5d400024eb88ea12afa07c7beee2f5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6514","title":"Remove printf","createdAt":"2023-06-12T22:12:11Z"}
{"state":"Merged","mergedAt":"2023-06-12T23:08:47Z","number":6515,"body":"In the next PR I will update the slackbot links to point to this page instead of the message page","mergeCommitSha":"2d011b80435128fe2012df1fe11bcb5480ac7fc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6515","title":"View inference result in admin console","createdAt":"2023-06-12T22:48:01Z"}
{"state":"Merged","mergedAt":"2023-06-12T22:50:11Z","number":6516,"mergeCommitSha":"a09a2a373b5075a101e24f6dd33b4cb9807f9a19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6516","title":"Reduce retries","createdAt":"2023-06-12T22:49:58Z"}
{"state":"Merged","mergedAt":"2023-06-12T23:44:51Z","number":6517,"body":"We archive/filter out PR code-level threads that we consider to not contain valuable content.\r\n\r\nThreads are considered to have valuable content if they have any of the following:\r\n- lists\r\n- images\r\n- links\r\n- code snippets\r\n- quotes\r\n- videos\r\n\r\nIf a thread has none of the above, then we archive the thread if it has a single message with a word count of 1. This means threads like \"LGTM\" are filtered out.\r\n\r\nThis PR proposes increasing the minimum threshold to consider the word count of the whole thread by archiving a thread if it has fewer than 15 words. This means a short thread will be archived even if it has multiple comments.","mergeCommitSha":"8eae1ac848a281872720e2b1847856a798e03729","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6517","title":"Consider the total word count of the entire thread when filtering insights","createdAt":"2023-06-12T23:15:13Z"}
{"state":"Merged","mergedAt":"2023-06-12T23:47:37Z","number":6518,"body":"Flag will be used across all clients and services.","mergeCommitSha":"75ac28264f82b748760bfeedae1ea329a49279e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6518","title":"Feature flag for semantic search","createdAt":"2023-06-12T23:25:43Z"}
{"state":"Merged","mergedAt":"2023-06-13T06:16:01Z","number":6519,"body":"Don't think we can remove this completely or it would be possible for a customer to coerce the model into bankrupting us :) ","mergeCommitSha":"600c0644544107ca16f57eda443f6bad3174b69d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6519","title":"Increase anthropic max token restriction to 2048","createdAt":"2023-06-12T23:30:24Z"}
{"state":"Merged","mergedAt":"2022-03-24T17:06:35Z","number":652,"body":"This is part one of replacing DataCacheStore with DataCacheStream:\r\n\r\n1. Replace the DataCacheStore unit tests with tests on DataCacheStream\r\n2. DataCacheStream has no overlay functionality, and we'll need it so I added it.  There is a `DataCacheStreamOverlay` helper class that represents all overlaid data, it internally generates a stream of the overlay data, and a helper to overlay it onto a base stream.\r\n\r\nNext PR will actually replace the stores, I think. ","mergeCommitSha":"7db8c8fc671f7f82d8d554ca58842044151f1a19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/652","title":"Add overlay functionality to data streams","createdAt":"2022-03-23T23:38:11Z"}
{"state":"Merged","mergedAt":"2023-06-12T23:39:18Z","number":6520,"mergeCommitSha":"33fd697cf49b04236e3a1ee9d070e9aaed297d30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6520","title":"Woopsies","createdAt":"2023-06-12T23:38:59Z"}
{"state":"Merged","mergedAt":"2023-06-13T19:55:32Z","number":6521,"body":"Clip wide content in the MessageEditor and MessageView, and wrap preformatted blocks in both.","mergeCommitSha":"b108754c4ecede8c480391664b582e5f9a92316b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6521","title":"Fix preformatted code wrapping","createdAt":"2023-06-12T23:41:31Z"}
{"state":"Merged","mergedAt":"2023-06-13T18:23:29Z","number":6523,"body":"If this mod is acceptable, I will do the same for expert summaries.","mergeCommitSha":"9ecb574887c2e5c4f0ac420d96a4abf3f01ae005","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6523","title":"Add lib-search-semantic + lib-ml with their magical goodness into `TopicSummaryService`","createdAt":"2023-06-12T23:59:35Z"}
{"state":"Merged","mergedAt":"2023-06-14T06:01:02Z","number":6524,"body":"Update hub to handle \"private\" Q&A\r\n<img width=\"659\" alt=\"CleanShot 2023-06-12 at 14 41 39@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/2605b241-d258-4208-843f-f8d2c04083bd\">\r\n<img width=\"671\" alt=\"CleanShot 2023-06-12 at 14 41 42@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ff4f001f-c9a1-41ab-829f-cb6cd077475a\">\r\n\r\n<img width=\"624\" alt=\"CleanShot 2023-06-12 at 16 58 28@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/fd0548eb-3094-44a1-90aa-150aab43213a\">\r\n<img width=\"583\" alt=\"CleanShot 2023-06-12 at 16 58 23@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ee46f685-2602-4b3a-9213-8ccb1067ec02\">\r\n\r\nTODO: Hookup create API boolean from https://github.com/NextChapterSoftware/unblocked/pull/6504","mergeCommitSha":"07e838c5ef67137ed8cd9820a5db72bf8bc2580c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6524","title":"Update hub with private Q&A","createdAt":"2023-06-13T00:00:13Z"}
{"state":"Merged","mergedAt":"2023-06-14T23:26:40Z","number":6525,"mergeCommitSha":"d07d8c7652c7a186b6ad70de9e14f71972b4bbf0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6525","title":"Use semantic search insight client flag in hub","createdAt":"2023-06-13T00:42:46Z"}
{"state":"Merged","mergedAt":"2023-06-13T01:03:35Z","number":6526,"mergeCommitSha":"1e224c8e155da22f2dd6e37864c737ba68331ffa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6526","title":"Clarify embedding naming","createdAt":"2023-06-13T00:48:53Z"}
{"state":"Merged","mergedAt":"2023-06-13T03:47:56Z","number":6527,"body":"This PR https://github.com/NextChapterSoftware/unblocked/pull/6517 archives threads with little content. Lets skip for open PRs so that folks continue to get notifications.","mergeCommitSha":"d1bdfed2cbb18b87a1218e9c69f4add7bbc70332","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6527","title":"Dont archive thread for open PRs","createdAt":"2023-06-13T02:16:35Z"}
{"state":"Merged","mergedAt":"2023-06-13T19:55:41Z","number":6528,"body":"When the mention/team member dropdown handles keypresses, stop their propagation.\r\n\r\nFixes UNB-1329","mergeCommitSha":"71d9499be486dfbaf4dff45530809e66dfddedbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6528","title":"Stop key propagation in mention dropdown","createdAt":"2023-06-13T02:32:17Z"}
{"state":"Merged","mergedAt":"2023-06-13T05:50:31Z","number":6529,"body":"Same as #6495 but for threads instead of PRs.","mergeCommitSha":"d7ab151414f982dde5a6b038b47c42e32c099583","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6529","title":"Do not embed archived threads","createdAt":"2023-06-13T03:57:45Z"}
{"state":"Merged","mergedAt":"2022-03-23T23:59:53Z","number":653,"body":"This prevents attempts to create duplicate identities","mergeCommitSha":"a03b256e77c0b3276e1423ad1dd24246a76ada06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/653","title":"Remove duplicate authors","createdAt":"2022-03-23T23:49:52Z"}
{"state":"Merged","mergedAt":"2023-06-13T05:09:39Z","number":6530,"mergeCommitSha":"08a03153dacfd212bba9b7a690c78d01ce7e3900","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6530","title":"Small handler fixes for inference example viewer","createdAt":"2023-06-13T04:20:29Z"}
{"state":"Merged","mergedAt":"2023-06-13T05:50:47Z","number":6531,"body":"If the `SemanticSearch` client config flag is enabled for a team, then we\nmake the bot a current member of the team. This allows the bot to show up\nan @-mention in text input.\n\nWhile we feature is still in development, we do not expose the bot as a\ncurrent member. Note that all teams still has a bot mmeber, it's just\nthat they can't interract with it until the flag is enabled.","mergeCommitSha":"0f8dea6981e3d32f4f07e4db0a7e7f9632f02d56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6531","title":"Unblocked bot is current member only if SemanticSearch is true","createdAt":"2023-06-13T04:28:35Z"}
{"state":"Merged","mergedAt":"2023-06-13T05:01:53Z","number":6532,"mergeCommitSha":"ef9e152e75084d14867527caa588f7188224f6ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6532","title":"Fix PullRequestIngestionServiceTest","createdAt":"2023-06-13T04:52:03Z"}
{"state":"Merged","mergedAt":"2023-06-13T05:54:13Z","number":6533,"mergeCommitSha":"9d438a951ba66cb9ee231a261c67300d340c4356","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6533","title":"Add inference example to ask-unblocked posts","createdAt":"2023-06-13T05:11:31Z"}
{"state":"Merged","mergedAt":"2023-06-14T17:17:00Z","number":6534,"mergeCommitSha":"a0f04a5b1fb40d7884092b52b112fc2757a4bf24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6534","title":"Move expert summaries to new semantic search + ml","createdAt":"2023-06-13T05:57:30Z"}
{"state":"Merged","mergedAt":"2023-06-13T16:03:10Z","number":6535,"body":"A couple of these tests were passing when they shouldn't have been due to cache issues.","mergeCommitSha":"a7ae99f5f0b18db62e518951e6c5df86ee2299ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6535","title":"Fix TopicStoreTest","createdAt":"2023-06-13T15:35:17Z"}
{"state":"Merged","mergedAt":"2022-03-24T18:41:57Z","number":654,"body":"### Overview\r\nI started the pusher service by using Peter's work in scm service as a template. From there it was just modifying a few paths and moving implementation code from api-service. Rest of this PR is basically moving db tests and modifying Gradle files to get cross project shared test dependencies to work. \r\n\r\n### Changes\r\n- Added new Gradle config to export tests in each project as a jar \r\n- Cleaned up a whole bunch of unused plugins and dependencies along with some cosmetic changes to Gradle task defintions\r\n- Moved DB tests (required by pusher tests and api service tests) to `models` project next to db package code\r\n- Created a new project for `pusherservice` at `projects/services/pusherservice` \r\n- Created entry point service application and added all necessary required plugins/utils/resources\r\n- Cleaned up all references to pusher in apiservice \r\n- Modified dependencies in `apiservice` and new `pusherservice` to create dependency on `models` project test jar\r\n- Moved `pusher` tests out of `apiservice` and into the new pusher project\r\n- Changed CICD workflow to deploy new `pusherservice` instead of a duplicate of apiservice \r\n- Added a new route `/apm/pusher` serving health endpoints for public (Grafana APM checks) consumption. I will add the same change to all other services in a separate PR. This will allow us to do APM checks on all public facing services.\r\n\r\n### TODOs and Caveats\r\n- Some code (mainly util classes in both tests and actual source) are duplicated. I will try to deduce most of that in my next PR to move all shared tests to their respective packages \r\n- This PR only moves DB tests to address a dependency issue. All other shared tests will be moved in my next PR\r\n- We still need to find a cleaner approach to config management \r\n","mergeCommitSha":"268a05c0ff693dfca64379708fc85e3897aecd63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/654","title":"Create pusher service","createdAt":"2022-03-24T07:30:17Z"}
{"state":"Merged","mergedAt":"2023-06-13T16:13:35Z","number":6540,"body":"As a performance optimization skipped upserting the bot team member if it already\nexists for a team. This has the downside that we can't actually change the team member\nproperties.\n\nThis change removes the shortcut so we actually upsert during team maintenance.","mergeCommitSha":"d5fcb4b6acb39180a9f6b7ef2b2ec39b4ecb8df0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6540","title":"BotAccountManager should actually upsert team members","createdAt":"2023-06-13T16:10:48Z"}
{"state":"Merged","mergedAt":"2023-06-13T18:04:15Z","number":6541,"body":"This new change should make sure we never leave a deployment in a pending state. ","mergeCommitSha":"9f79e2ea471db9fff0c70362c1685fe42a336da6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6541","title":"Improve deployment cancelation ","createdAt":"2023-06-13T17:48:30Z"}
{"state":"Merged","mergedAt":"2023-06-13T18:29:16Z","number":6542,"body":"I'll keep a close eye on this one because it could break prod deployments. If it works, then we should regain the ability to rerun deploy jobs without having to rerun an entire workflow","mergeCommitSha":"188a4d7011bf19fdb68aecc61682b38a404a510b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6542","title":"add step dependency to make sure output is available","createdAt":"2023-06-13T18:00:19Z"}
{"state":"Merged","mergedAt":"2023-06-13T19:02:01Z","number":6543,"mergeCommitSha":"ddc54184afb4b145a05aabeb360f62c0c6e5811b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6543","title":"Link it all up in the admin console for ez navigation","createdAt":"2023-06-13T18:11:19Z"}
{"state":"Merged","mergedAt":"2023-06-13T23:10:51Z","number":6544,"body":"Make sure message mentions are treated as team participants when creating a thread.","mergeCommitSha":"65e0143ff7a2d5b8af052cf12ed01257c857797e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6544","title":"Merge mentions team participants on thread creation","createdAt":"2023-06-13T19:10:51Z"}
{"state":"Merged","mergedAt":"2023-06-13T19:58:23Z","number":6546,"body":"The video walkthrough preview UI was applying its own padding.  Remove it and use the default detail view padding.","mergeCommitSha":"309612f562f976f4e110a6346977af8bc4a19cd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6546","title":"Fix walkthrough preview layout","createdAt":"2023-06-13T19:48:53Z"}
{"state":"Merged","mergedAt":"2023-06-13T20:00:33Z","number":6547,"mergeCommitSha":"ab96aa4366bccbdf095c2d165320b8c57cf8660d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6547","title":"Fix broken link","createdAt":"2023-06-13T20:00:10Z"}
{"state":"Merged","mergedAt":"2023-06-13T20:58:38Z","number":6548,"body":"Fix update thread private test.\r\n","mergeCommitSha":"088d9e40ffedffbfd23993e467a5c021595176db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6548","title":"Fix update private test","createdAt":"2023-06-13T20:09:46Z"}
{"state":"Merged","mergedAt":"2023-06-14T23:36:14Z","number":6549,"mergeCommitSha":"bdef7906c523c22e604cfe6a1deac2b592435436","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6549","title":"Script to generate histogram of diff sizes from commits/PRs","createdAt":"2023-06-13T20:43:53Z"}
{"state":"Merged","mergedAt":"2022-03-24T17:49:41Z","number":655,"mergeCommitSha":"0b6e91afd54ba637ed19ccc411f4a44535199662","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/655","title":"Add logging to PollingBackgroundJob","createdAt":"2022-03-24T17:32:36Z"}
{"state":"Merged","mergedAt":"2023-06-13T20:46:04Z","number":6550,"mergeCommitSha":"3ec9810695a9316cfe8a9b7a2de771a3c09600a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6550","title":"More broken links","createdAt":"2023-06-13T20:45:35Z"}
{"state":"Merged","mergedAt":"2023-06-13T21:41:59Z","number":6551,"body":"The bot is assumed to have an account, which prevents the client from\noffering to invite the bot to the team.","mergeCommitSha":"de493d85f9e8d60349377fbc743b2818bdafbf03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6551","title":"Team member API populates isUnblockedBot and hasAccount","createdAt":"2023-06-13T21:04:59Z"}
{"state":"Merged","mergedAt":"2023-06-14T05:50:11Z","number":6552,"body":"Scopes Hub to a specific team.\r\n\r\nThis is a bit rough right now as we still try to support old / new UI with the EnableSearch flag.\r\n\r\n\r\nSearch enabled\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/0314de93-cbc3-4677-90ce-2cb0a5036802\r\n\r\nNon-search enabled\r\n<img width=\"564\" alt=\"CleanShot 2023-06-13 at 14 49 45@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/1cb7bd76-bc8e-4324-82ce-41fd285d559a\">\r\n","mergeCommitSha":"6d9ad44083e43382ec8bd3d0fa2bb5a8bee88114","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6552","title":"Scope hub list to specific team","createdAt":"2023-06-13T21:51:22Z"}
{"state":"Merged","mergedAt":"2023-06-13T22:58:58Z","number":6553,"body":"This fixes the feedback UI so that the buttons update immediately on click.\r\n\r\nThe overlay we applied when giving message feedback was wrong.  The cancel function triggered immediately, so the overlay was never applied.","mergeCommitSha":"d71572dd071854f9f6a13a511bde25f918beb78a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6553","title":"Fix feedback message overlay","createdAt":"2023-06-13T22:05:14Z"}
{"state":"Merged","mergedAt":"2023-06-14T17:06:47Z","number":6554,"mergeCommitSha":"79a56e471f499ac1aef288692e21b2d9be8d8091","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6554","title":"Use correct claude 100k model name","createdAt":"2023-06-13T22:15:39Z"}
{"state":"Merged","mergedAt":"2023-06-13T22:27:41Z","number":6555,"mergeCommitSha":"6fe5fb0d54e2042323259e7ba860a7a4907026d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6555","title":"Remove completion lambda","createdAt":"2023-06-13T22:27:30Z"}
{"state":"Merged","mergedAt":"2023-06-14T01:14:58Z","number":6556,"mergeCommitSha":"e441b1928508fdd7da28d1a4d1952492cb42e7e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6556","title":"Fix data pipeline post process lambda and add retry logic for lambda","createdAt":"2023-06-14T00:46:07Z"}
{"state":"Merged","mergedAt":"2023-06-14T01:29:40Z","number":6557,"mergeCommitSha":"88d98badeefb2b299ecfd1277e82d78081bb7d6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6557","title":"Only push to cache from non-pr branch","createdAt":"2023-06-14T01:23:37Z"}
{"state":"Merged","mergedAt":"2023-06-14T17:37:07Z","number":6558,"body":"- Only push to cache from non-pr branch\r\n- Updat egradle\r\n","mergeCommitSha":"d8f1f6e81bc43eafab26aa9347731e0bf98e76c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6558","title":"Update gradle","createdAt":"2023-06-14T01:31:52Z"}
{"state":"Merged","mergedAt":"2023-06-14T02:58:12Z","number":6559,"body":"Jetbrains builds are slow on github runners.\r\nMove to ec2.","mergeCommitSha":"31727b54ed724995835faf6706bb795413821ce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6559","title":"Try fixing jetbrains","createdAt":"2023-06-14T02:31:33Z"}
{"state":"Merged","mergedAt":"2022-03-24T18:02:40Z","number":656,"mergeCommitSha":"3744d6ab67ab6c687c94e77eb9f0f8e260370c35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/656","title":"Update Jeff's IP","createdAt":"2022-03-24T17:59:05Z"}
{"state":"Merged","mergedAt":"2023-06-14T04:01:16Z","number":6560,"body":"<img width=\"1048\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/7513050c-42b7-40fd-a333-a67971c3928f\">\r\n","mergeCommitSha":"51de457f0a28c5370ebc2d036dc84213cfb96273","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6560","title":"Sort relevant topics to beginning of description","createdAt":"2023-06-14T02:31:47Z"}
{"state":"Merged","mergedAt":"2023-06-14T03:33:35Z","number":6561,"mergeCommitSha":"f73870339cf079ef7173bf873589ae922ee2cc7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6561","title":"Fix PipelineContextExtensionsTest","createdAt":"2023-06-14T02:37:10Z"}
{"state":"Merged","mergedAt":"2023-06-14T16:50:17Z","number":6562,"mergeCommitSha":"d9627625f5a015c3e6123ac7d3b2c48a09185917","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6562","title":"increased instance timeouts","createdAt":"2023-06-14T03:23:34Z"}
{"state":"Merged","mergedAt":"2023-06-14T05:19:58Z","number":6563,"mergeCommitSha":"046b52e1c65365fa9e022908d82b920d8ccc7e3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6563","title":"Add installation page and abilty to suspend GitHub installations","createdAt":"2023-06-14T04:45:27Z"}
{"state":"Merged","mergedAt":"2023-06-14T15:35:56Z","number":6564,"body":"The idea here is we can pass it list of threads (for example, all threads where a team member is a participant) and have it extract questions and answers from those threads.","mergeCommitSha":"46dfa69335769718e5ea5ecf22bb6882ada9a09c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6564","title":"Create an Anthropic prompt that extracts questions and answers from a thread","createdAt":"2023-06-14T05:34:47Z"}
{"state":"Merged","mergedAt":"2023-06-14T16:17:11Z","number":6565,"body":"Add badge for private threads.\r\n\r\nRequires comparison for true due to `Boolean?` typing. Let me know ","mergeCommitSha":"5295dfccf90c6c0a110ba5d97c8a19dce2b808fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6565","title":"Add badge for private threads","createdAt":"2023-06-14T06:30:53Z"}
{"state":"Merged","mergedAt":"2022-03-24T20:35:27Z","number":657,"body":"![image](https://user-images.githubusercontent.com/13431372/159989912-5c422292-2c1d-4d2a-8cc1-5d8b4cf84bae.png)\r\n\r\n* Manually clip the thread title string to a sensible length for the tab title (right now the tab width stretches to the full width of the title string)\r\n* Ideally we should display the full string and limit the width of the tab but I couldn't figure out a way to do this via the extension API. Even in the regular vscode editor settings, there's no easy way to manually size the tabs, there's setting to set the sizing from `fit` to `shrink` but this doesn't really apply to our use case ","mergeCommitSha":"637d9d6009250e720a6f1782c30a96c30c897321","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/657","title":"Clip the thread title for the tab","createdAt":"2022-03-24T18:57:09Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:56:46Z","number":6571,"body":"The feedback button is the first mutating UI that can happen quickly -- the problem is that if you give feedback multiple times quickly, the overlays stack -- the second overlay would cancel immediately (as its cancel clause was fulfilled), while the first would not cancel at all.\r\n\r\nThe fix is:\r\n* Allow overlays that must be unique to specify an ID.  When an overlay with an ID is added, any previous overlay with that ID is removed.\r\n* Only check for overlay cancellation when the stream provides new data.  I don't think we should be immediately cancelling overlays, we should only cancel them when the new stream data updates with the fulfilled value.","mergeCommitSha":"8c00f07185175afa4b1e04c723e11ba04e4dfdc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6571","title":"Fix feedback overlay behaviour","createdAt":"2023-06-14T18:12:00Z"}
{"state":"Merged","mergedAt":"2023-06-14T18:16:36Z","number":6572,"mergeCommitSha":"49712fcffb90f2421beb0457d2312dbe0df619ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6572","title":"Fix token timeout","createdAt":"2023-06-14T18:16:30Z"}
{"state":"Merged","mergedAt":"2023-06-14T18:22:25Z","number":6573,"mergeCommitSha":"377cad1a910df9d24609358b53968d0468efe116","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6573","title":"Include more small files in embedding space","createdAt":"2023-06-14T18:21:30Z"}
{"state":"Merged","mergedAt":"2023-06-14T18:28:52Z","number":6574,"mergeCommitSha":"66881003a1d9c1061b79ad9f11107398aa72776c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6574","title":"Reduce ml costs","createdAt":"2023-06-14T18:28:42Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:57:01Z","number":6575,"body":"By default flex and grid cells have their min size set to the content size, which means when content gets very large nothing gets clipped.  I fixed this the wrong way (using overflow: hidden), this fixes it the right way (setting a min size of zero on the column).","mergeCommitSha":"6881492dda97e7fccd92f1df90128e06e4216a81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6575","title":"Fix MessageEditor mention clipping","createdAt":"2023-06-14T18:31:12Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:36:47Z","number":6576,"body":"Old port was never cleaned up. Therefore, hub tried connecting to old port.\r\n\r\nMake walkthrough app status checks more frequent. Waiting for a second slowed down the connection quite a bit.\r\n\r\nNone of this should actually affect *when* the walkthrough app appears though...","mergeCommitSha":"1f95aa005611be83eabd910a0fa6c203bcbede51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6576","title":"Fixes for slow connection with walkthrough app","createdAt":"2023-06-14T18:41:57Z"}
{"state":"Merged","mergedAt":"2023-06-14T21:09:53Z","number":6577,"body":"Going to just push this live and see what happens. When in doubt, do what rashin does.","mergeCommitSha":"8607ea0691968e92b501765540575de122114fd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6577","title":"Removed the \"expert in the components\"","createdAt":"2023-06-14T19:12:33Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:36:11Z","number":6578,"body":"<img width=\"1080\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/ba5e6b0b-0225-48f0-8025-3e1c41a67a17\">\r\n\r\n* Move the topics list to the right column\r\n* Remove subheader from the thread header ","mergeCommitSha":"00129774706f87928c4ecb9b69c4a30bfc705121","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6578","title":"Update/condense discussion view","createdAt":"2023-06-14T19:58:29Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:39:31Z","number":6579,"body":"This is essentially a 1:1 dupe of LangChain's implementation.\r\nSome observations, you will get some wonky results, and I've observed that this produced no-better results than our original approach for certain queries.\r\n\r\ni.e.\r\n\r\nFor example, for \"How do we change which Anthropic model we're using?\":\r\n\r\nChoice(text=Changing which Anthropic model we are using requires a shift in our perspective and understanding of the universe. We must first understand the different models and their implications, and then decide which one best fits our current understanding of the universe. Once we have chosen a model, we must then adjust our thinking and behavior to fit the model. This may involve changing our beliefs, values, and practices to align with the model. Additionally, we must be willing to accept new evidence and adjust our model accordingly. Finally, we must be open to the possibility that our model may need to be changed in the future as our understanding of the universe evolves., index=0, logprobs=null, finishReason=stop)\r\n\r\nThe resulting document set was garbage...\r\nWe should experiment with integrating this in another PR....\r\n\r\n","mergeCommitSha":"8dd23736bd1b184a3ce04f9d97baf571e4ac10b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6579","title":"Add basic hyde implementation","createdAt":"2023-06-14T20:09:14Z"}
{"state":"Merged","mergedAt":"2022-03-24T19:33:25Z","number":658,"body":"Fix a small package naming mistake","mergeCommitSha":"fd72b4da269991992e9623916f8bc5e3707382ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/658","title":"fix a copy-paste mistake","createdAt":"2022-03-24T19:01:16Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:12:13Z","number":6580,"body":"@matthewjamesadam improves the message view formatting in admin web.","mergeCommitSha":"d0a87f3a8a22b27f2e2684c0874032853c165ccc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6580","title":"Admin web code block styling","createdAt":"2023-06-14T20:11:19Z"}
{"state":"Merged","mergedAt":"2023-06-14T23:36:40Z","number":6581,"mergeCommitSha":"a9e070290ca2fdde86cbd631d7ff63664dc68f83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6581","title":"Add documents to example admin page","createdAt":"2023-06-14T20:12:34Z"}
{"state":"Merged","mergedAt":"2023-06-15T23:16:21Z","number":6582,"body":"Add ability to update human feedback on inference examples","mergeCommitSha":"f70eae4220ede277bfd172b199cd09b1a4f439c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6582","title":"API for clients to set humanFeedback","createdAt":"2023-06-14T20:17:15Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:48:45Z","number":6583,"body":"# Before\r\n<img width=\"770\" alt=\"Screenshot 2023-06-14 at 13 21 46\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/0e7fcb92-cd5d-46ac-9d52-1e9a706e1dad\">\r\n\r\n\r\n# After\r\n<img width=\"780\" alt=\"Screenshot 2023-06-14 at 13 21 29\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/2b621f80-9eb7-4570-9818-9455070b12b9\">\r\n","mergeCommitSha":"7f607f6b43982ff762030f7db219bcd0aea58046","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6583","title":"Increase list indentation","createdAt":"2023-06-14T20:20:47Z"}
{"state":"Merged","mergedAt":"2023-06-14T20:56:13Z","number":6584,"body":"When you mention an expert, don't add extra node content above, and focus the editor after.","mergeCommitSha":"63237e6ee4db758505ac70171ba2a7ad44ad9663","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6584","title":"Fix mention expert","createdAt":"2023-06-14T20:34:09Z"}
{"state":"Closed","mergedAt":null,"number":659,"body":"I'm certain there's a better way to do this.","mergeCommitSha":"20b146cf89fedb23cb0fe79b40c33467e3e8cb27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/659","title":"Fix logging","createdAt":"2022-03-24T19:59:40Z"}
{"state":"Merged","mergedAt":"2023-06-15T17:42:29Z","number":6590,"body":"Fixes this issue \r\n<img width=\"692\" alt=\"CleanShot 2023-06-14 at 14 09 24@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/2c0af253-35c1-4fba-9f04-a2f6ce8be25f\">\r\n","mergeCommitSha":"c3a92624bac4e703147591581b049670f64d81e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6590","title":"Add start_number property to MessageBody.ListBlock","createdAt":"2023-06-14T20:58:12Z"}
{"state":"Open","mergedAt":null,"number":6591,"body":"Currently the client grabs the experts for the topics of a given thread and renders them in the order of the topics. \r\n\r\nMoving this into an API call would allow for the service to control the weighting of the list of experts as well as standardize the sorting. It could also separate the thread topic/thread expert dependency (i.e. allow for returning experts for threads that have no topics).","mergeCommitSha":"4dc2c13aa0d8d0abd9055b4d1850d47f3e9eedb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6591","title":"Add experts list to ThreadInfo and PullRequest","createdAt":"2023-06-14T21:14:30Z"}
{"state":"Merged","mergedAt":"2023-06-15T23:51:52Z","number":6592,"body":"…#6488)\"\r\n\r\nThis reverts commit 3a36560c12fa57b7945fc1a025009b0bd2357f5e.\r\n\r\nNo longer needed.","mergeCommitSha":"1ccf0fd1b351c14db0922ac56f7d16583d2579be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6592","title":"Revert \"Add method to set provider for existing SearchInsightModels (…","createdAt":"2023-06-14T21:32:02Z"}
{"state":"Merged","mergedAt":"2023-06-15T04:49:53Z","number":6593,"mergeCommitSha":"e842de0a830a71415170ffe1eefe6dfd70ea5c5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6593","title":"Add authorId to documents","createdAt":"2023-06-14T22:08:48Z"}
{"state":"Merged","mergedAt":"2023-06-14T23:34:06Z","number":6598,"mergeCommitSha":"77b2e19fa2108fe0c9a6b64f3e6c74407d98a505","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6598","title":"Refactor document search","createdAt":"2023-06-14T23:10:03Z"}
{"state":"Merged","mergedAt":"2023-06-14T23:34:47Z","number":6599,"body":"- actually use the template `Max Documents` parameter in document queries\n- make multiple pinecone queries in parallel and combine","mergeCommitSha":"3b22c20e12ad5f98d492773d870d3e614f393542","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6599","title":"Introduce documents sets","createdAt":"2023-06-14T23:10:06Z"}
{"state":"Merged","mergedAt":"2022-01-18T21:13:46Z","number":66,"body":"- Removed old ECR repos creation per account. All repos are created under a single (sec-ops) account and then replicated to all other regions/environments \r\n- Added `SecOpsBuildConfig` to support deployment configs for a new environment type \r\n- Added ecr registry stack for `sec-ops` shared account along with replication rules \r\n- Created ECR policy to allow replication from `sec-ops` account in consumer accounts \r\n- Did some code cleanup and refactoring to make the build configs more readable \r\n- Replaced the old main function which controls what stacks to deploy based on environment types \r\n\r\nThese changes have been deployed. We now have working ECR replication. I'll provide IAM creds for our automation account out of band using 1password","mergeCommitSha":"4968925d369af6fb365eb20ba0f7a67bcefa7e0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/66","title":"Cdk refactor - make ecr shared and replicate it to every account","createdAt":"2022-01-18T20:31:37Z"}
{"state":"Merged","mergedAt":"2022-03-24T21:32:38Z","number":660,"body":"Removes thread-model DataCacheStore, replacing it with streams usage.","mergeCommitSha":"2f3a5ce2cd67369dc9e4f34ff65ffd0e43e8d407","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/660","title":"Remove thread DataCacheStore","createdAt":"2022-03-24T20:35:07Z"}
{"state":"Merged","mergedAt":"2023-06-15T18:29:00Z","number":6600,"body":"- fix(deps): update dependency com.aallam.openai:openai-client to v3.2.5\r\n- Add logging\r\n","mergeCommitSha":"68f61815405709027d2af997cf96152a70c9f019","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6600","title":"AddLogging","createdAt":"2023-06-14T23:33:25Z"}
{"state":"Closed","mergedAt":null,"number":6602,"mergeCommitSha":"8891e1d259c30ab98ad14ef7e418fccb1db2189c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6602","title":"Adds the approved topic names to the topic classification query","createdAt":"2023-06-15T00:07:10Z"}
{"state":"Merged","mergedAt":"2023-06-15T23:59:10Z","number":6603,"body":"<img width=\"1036\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/3bedd9bc-6acd-4827-b84d-179024e8a516\">\r\n","mergeCommitSha":"50a5e8ae4842ab32c420d77f8d93ba9219cd51c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6603","title":"Condense PR view ","createdAt":"2023-06-15T00:23:29Z"}
{"state":"Merged","mergedAt":"2023-06-17T00:33:15Z","number":6604,"mergeCommitSha":"70ed0122e4c9161d57891050fc801d7d8a97dcc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6604","title":"Pull Request Summary","createdAt":"2023-06-15T03:04:56Z"}
{"state":"Merged","mergedAt":"2023-06-15T03:25:25Z","number":6605,"mergeCommitSha":"94da6d33bd85f0d2c6ccf09625cffe93301e0319","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6605","title":"Add descriptions for new Score and Date fields","createdAt":"2023-06-15T03:24:53Z"}
{"state":"Merged","mergedAt":"2023-06-15T05:30:06Z","number":6606,"body":"Looks big but it's fairly straightforward and most of the changes are tests:\r\n- Add templates for topics and teammembers\r\n- Add admin console things\r\n- Some more serialization magic etc","mergeCommitSha":"57ef4fdce9a6ad1e53d7935e24108c70e02547ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6606","title":"Add Topics and TeamMembers to prompt","createdAt":"2023-06-15T03:59:28Z"}
{"state":"Merged","mergedAt":"2023-06-15T06:20:06Z","number":6607,"mergeCommitSha":"d0aec4d335d18295b1b697cd08b38f8a00c0aaa9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6607","title":"Fix janky code previews in admin web","createdAt":"2023-06-15T06:19:39Z"}
{"state":"Merged","mergedAt":"2023-06-15T15:52:23Z","number":6608,"body":"Flip boolean to make public...","mergeCommitSha":"87d28be63559650b8b5afd4a730011ea82581508","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6608","title":"Fix make public","createdAt":"2023-06-15T15:00:57Z"}
{"state":"Merged","mergedAt":"2023-06-15T17:31:30Z","number":6609,"mergeCommitSha":"2b4c8fdd44e29ab281b39c755f4c5c555824cb40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6609","title":"Mark private questions in Slack","createdAt":"2023-06-15T17:19:07Z"}
{"state":"Merged","mergedAt":"2022-03-24T20:53:47Z","number":661,"body":"Change pusher path so we could use `/api/channels` prefix on Application Loadbalancers when routing traffic. This is  to forward different paths to services responsible for those sets of functionality","mergeCommitSha":"fa70141ae460c2fde34f04d234736a7f6abe7dd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/661","title":"change pusher path","createdAt":"2022-03-24T20:38:28Z"}
{"state":"Merged","mergedAt":"2023-06-15T19:38:08Z","number":6611,"mergeCommitSha":"0dc7da67ae2a26b780b213720d6ed1c70df2126a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6611","title":"Center align message editor","createdAt":"2023-06-15T17:40:59Z"}
{"state":"Merged","mergedAt":"2023-06-15T19:33:28Z","number":6612,"mergeCommitSha":"295223986025d25e4eebe70f94900fb94a85245f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6612","title":"Converts the bot to a current member immediately on demand after the semantic search flag has been toggled","createdAt":"2023-06-15T18:35:23Z"}
{"state":"Merged","mergedAt":"2023-06-15T21:34:49Z","number":6613,"body":"New branding-based hub icons.\r\n\r\nI had to do a bunch of messing around with the models to get the data in the right place -- it does feel odd to me that we have all these partials and other structs, they seem to obscure the original data and make everything harder to work with.  I understand that we want to be able to render and interact with both ThreadInfos and PullRequests, but I wonder if instead of making structs representing Insights, if instead we should have an Insight protocol that both API models implement.\r\n\r\n<img width=\"572\" alt=\"Screenshot 2023-06-15 at 11 48 23 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/8924d7f3-1da3-4c56-ac8f-8eb93da4b9a4\">\r\n","mergeCommitSha":"dad1a96775e0002f93f3d94e69b8520cb279bda2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6613","title":"Update hub icons","createdAt":"2023-06-15T18:37:05Z"}
{"state":"Merged","mergedAt":"2023-06-15T21:23:01Z","number":6614,"body":"Basically stripping out all the logic for what icons to show.","mergeCommitSha":"25daac4273b253781a811f7113323070f1235f34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6614","title":"Update jetbrains gutter icons","createdAt":"2023-06-15T19:00:08Z"}
{"state":"Merged","mergedAt":"2023-06-15T20:34:28Z","number":6616,"mergeCommitSha":"cf6a29682fa08f41fb16ba5508a9d2592a3ab011","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6616","title":"Add pusher notifications when we update topics","createdAt":"2023-06-15T19:41:08Z"}
{"state":"Merged","mergedAt":"2023-06-15T21:05:34Z","number":6617,"body":"Bunch of stuff going on here:\r\n- Remove document sections. Flatten the documents ordered by relevancy\r\n- Include \"Questioner\" in the prompt\r\n- Get rid of newlines in the prompt if the field is missing and it's the only thing on the line\r\n- Dump team members from the prompt","mergeCommitSha":"b65515d13d1c44595d0c94e7f1bd58785af5d3c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6617","title":"Improve prompt","createdAt":"2023-06-15T20:24:16Z"}
{"state":"Merged","mergedAt":"2023-06-15T22:19:14Z","number":6618,"mergeCommitSha":"686bbd71bf9f9afc66a06291627fbcff64418c27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6618","title":"Exclude short Linear / Jira / GitHub issue documents","createdAt":"2023-06-15T21:02:58Z"}
{"state":"Merged","mergedAt":"2023-06-15T22:24:27Z","number":6619,"body":"This change provisions an Ec2 instance of type P2.2xlarge running latest ubuntu LTS for PowerML experiments. \r\n\r\n- This instance is provisioned under our sec-ops account. That's where we run cross environment shared resources or things that need direct external access.\r\n- If the PowerML experiments turn out to be successful then we need to rework this in a more secure way to eliminate runtime dependencies and cross environment dependencies\r\n- Security groups allow connections to server on 22 and 443 ports \r\n- Instance is provisioned on the same subnet as our bastion host\r\n- It can reach Dev DB but prod is still blocked. When needed we can open that path \r\n- We need to provision additional paths for any Kube services to communicate with this. For that I'll need a list of services so I can modify their respective network policies. \r\n- SSH key is stored in 1Password infra vault\r\n\r\n**Hostname:**\r\npowerml.secops.getunblocked.com\r\n**SSH command:**\r\nssh <EMAIL> -i ~/PATH/TO/KEY/powerml-ec2.pem\r\n\r\nRelated Slack threads:\r\nhttps://chapter2global.slack.com/archives/C048Z3NFS93/p1686693007859839\r\nhttps://chapter2global.slack.com/archives/C045VGYML95/p1686771206437699\r\n\r\n\r\n@pwerry this is a departure from our security model. For now I'll create the necessary Drata exceptions. ","mergeCommitSha":"8c001252b1e462fcfb415ed16c4f6b77f582ebf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6619","title":"adding p3 instance for powerml experiments","createdAt":"2023-06-15T22:09:32Z"}
{"state":"Merged","mergedAt":"2022-03-28T18:30:25Z","number":662,"body":"## Summary\r\n\r\nInitially implemented against the GitHub `/app/installations` API with a since parameter, but we discovered that `since` is only applied to `createdAt` and not `updatedAt`, which defeats the purpose. Therefore we are currently ripping down all the installs and manually checking the `updatedAt` value for further processing.\r\n\r\nProcessing includes org data, org members, and repos. Anonymous identities are created for users who are not already in the system. Users already in the system are wired up to the team. \r\n\r\n## Caveats\r\nWe've learned it is not possible to obtain the root commit sha for a repo from GitHub without content permissions. For now this value is being set to some random sha (and not currently used in the system). We may be able to fix this in the future by allowing clients to pass us the sha value during `findOrCreateRepo()`, and then assign a confidence score as more clients roll in. \r\n\r\nOr alternatively, if we introduce the concept of a `RepoMember`, each user can have their own root sha? (This might cause problems if we're doing sourcemark things on the backend, so we might need both)\r\n\r\n","mergeCommitSha":"1fd1de3b0f8f19cce0d1cbb8490b54abeac3a437","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/662","title":"GH Org Installs","createdAt":"2022-03-24T21:48:30Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:17:36Z","number":6620,"body":"Feedback dialog implementation\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/c15edb4a-b9b5-4e98-9215-82e3628c574c\r\n\r\n\r\n\r\nTODO: Hookup feedback API. Waiting on PR to get API in but running into some CI issues...\r\n","mergeCommitSha":"70e480eb1a8588c8b4a2c189b68e36e0f03858a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6620","title":"Feedback dialog","createdAt":"2023-06-15T22:17:17Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:33:08Z","number":6621,"body":"Use as:\n\n```kotlin\nval primaryId = TeamMemberAssociationStore.resolvePrimaryMemberId( id )\n\nval memberAndIdentity = IdentityService.getMemberIdentityCache( primaryId )\n```","mergeCommitSha":"5fced54b3a851fe17e31825721f6df8d3c2ec383","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6621","title":"Resolve a team member to it's primary if the primary relationship exists","createdAt":"2023-06-15T22:19:40Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:14:16Z","number":6622,"body":"…ted.","mergeCommitSha":"1a26a556ffb5f57ee1c4a679c05e7ebef8f7fa3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6622","title":"Make sure we do not update topic mappings until QA thread is not updated.","createdAt":"2023-06-15T23:29:52Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:34:13Z","number":6623,"body":"- add author display name\n- remove author id","mergeCommitSha":"8eef1955647a873cc345be541e0d5da966a7f6a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6623","title":"Add insight author name to prompt","createdAt":"2023-06-15T23:56:13Z"}
{"state":"Merged","mergedAt":"2023-06-19T17:18:44Z","number":6624,"body":"this automatically creates new summaries for a new topics and their experts when the users creates a new topic or edits one. ","mergeCommitSha":"5809945739d09fb4e5251698fc7e8f3062a725ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6624","title":"UNB-1306: Add calls to summary services when someone creates a new topic with new experts","createdAt":"2023-06-16T00:11:22Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:35:25Z","number":6625,"mergeCommitSha":"3ab88f4e39125cac432b2caff848e83ad15b0760","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6625","title":"Add PRs and Walkthroughs back into the document lookup","createdAt":"2023-06-16T00:18:06Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:30:08Z","number":6626,"mergeCommitSha":"ab1c3e8b9dee4550816313608eb93cec01b518bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6626","title":"Fix templates using optinonal source mark file reference","createdAt":"2023-06-16T00:30:02Z"}
{"state":"Merged","mergedAt":"2023-06-16T05:32:59Z","number":6627,"body":"Need your eyes on this one @davidkwlam ","mergeCommitSha":"597eac331babecfa9ccbbac75309e84e75981500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6627","title":"Pass content as markdown to Pinecone / LLM","createdAt":"2023-06-16T00:48:36Z"}
{"state":"Closed","mergedAt":null,"number":6628,"mergeCommitSha":"9a16dc565af43c45a43c98d4cea016a25ed1903f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6628","title":"Increase test run","createdAt":"2023-06-16T00:54:15Z"}
{"state":"Merged","mergedAt":"2023-06-16T17:48:17Z","number":6629,"body":"If a thread has a bot message that does *not* have feedback, show feedback dialog on message creation.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/4fa5a292-8d19-46bb-b062-20344513dbb2\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f9bf1124-0168-4ed0-9609-4a51a70e5a13\r\n\r\n","mergeCommitSha":"f6a7cef335f5dd8e75832630e630dd70fe3936a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6629","title":"Q&A Feedback dialogs","createdAt":"2023-06-16T00:55:42Z"}
{"state":"Merged","mergedAt":"2022-03-24T22:11:33Z","number":663,"body":"* sort threads on the client to sort latest first (per last comment in https://github.com/NextChapterSoftware/unblocked/pull/646)\r\n* some small clean up items","mergeCommitSha":"8a4167304180c750ef5a9d73fbdf234908c7864e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/663","title":"Sort threads by lastMessageCreatedAt","createdAt":"2022-03-24T21:48:55Z"}
{"state":"Merged","mergedAt":"2023-06-16T07:30:14Z","number":6630,"body":"Missing navigate on click row for experts","mergeCommitSha":"cd08bfe14ad4782f0760f85d9ae2fcf124934d22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6630","title":"Missing Navigate","createdAt":"2023-06-16T04:59:30Z"}
{"state":"Merged","mergedAt":"2023-06-16T09:23:56Z","number":6631,"mergeCommitSha":"29486b81342a05f82bf54c32e332c838ea9a950d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6631","title":"Uses searchInsightStore rather than pinecone","createdAt":"2023-06-16T05:30:12Z"}
{"state":"Merged","mergedAt":"2023-06-16T13:53:25Z","number":6632,"body":"Reverts NextChapterSoftware/unblocked#6631","mergeCommitSha":"cc9a58052dd391664788f510c994ea1ef506c745","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6632","title":"Revert \"Uses searchInsightStore rather than pinecone\"","createdAt":"2023-06-16T10:22:01Z"}
{"state":"Merged","mergedAt":"2023-06-16T14:50:53Z","number":6633,"body":"Uses searchInsightStore rather than pinecone (#6631)\n\nAdd missing parameter","mergeCommitSha":"710d406fc124977d7f9ef5567e847c0efa69e4ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6633","title":"Uses searchInsightStore rather than pinecone (#6631) + fix","createdAt":"2023-06-16T14:50:22Z"}
{"state":"Merged","mergedAt":"2023-06-16T16:36:37Z","number":6634,"body":"Previously done for threads here #6627 ","mergeCommitSha":"7c58772b690284b92254105945314c5b3509a591","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6634","title":"PR descriptions should preserve markdown for embedding / LLM","createdAt":"2023-06-16T16:11:09Z"}
{"state":"Merged","mergedAt":"2023-06-16T23:44:15Z","number":6635,"body":"New hub layout.  There were a lot of hacks involved in getting surrounding borders to render correctly.\r\n\r\n<img width=\"572\" alt=\"Screenshot 2023-06-16 at 4 15 19 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/3573b0db-b8f5-4522-acdf-8d6563a595e9\">\r\n<img width=\"572\" alt=\"Screenshot 2023-06-16 at 4 15 31 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/86af2e3f-5240-471c-8e30-52d0a157ab17\">\r\n","mergeCommitSha":"cdac9d023fee52f6f3546ba6e777ebe5233c8039","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6635","title":"New hub layout","createdAt":"2023-06-16T16:52:08Z"}
{"state":"Merged","mergedAt":"2023-06-16T17:03:42Z","number":6636,"mergeCommitSha":"551c9216d50b414aefa6f1ea0a440fe640cf45e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6636","title":"Fix bug in plumbing max documents to Pinecone top-K vectors","createdAt":"2023-06-16T16:59:42Z"}
{"state":"Merged","mergedAt":"2023-06-16T19:35:14Z","number":6637,"body":"Going to use the TopicService for generating questions and answers rather than spinning up another service, but we can easily move this to another service.","mergeCommitSha":"7b00b79a15aad1538d1d22ee4669df87d48d5e33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6637","title":"Wire up TopicService to handle question answer generation events","createdAt":"2023-06-16T17:15:06Z"}
{"state":"Merged","mergedAt":"2023-06-16T17:46:17Z","number":6638,"mergeCommitSha":"2f60fd0ea13e39d67bd292b9348010f2ae018a8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6638","title":"Fix lint","createdAt":"2023-06-16T17:37:22Z"}
{"state":"Merged","mergedAt":"2023-06-16T17:38:42Z","number":6639,"mergeCommitSha":"bad7960101f57d47bb005f4d63f3f33100471852","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6639","title":"Quick and dirty fix for mark as read not refreshing","createdAt":"2023-06-16T17:38:19Z"}
{"state":"Merged","mergedAt":"2022-03-25T20:11:07Z","number":664,"body":"These should take some of the pain out of local testing and development. Take a look at the bottom of root README file for full list.\r\n\r\n\r\n- Added new make targets (described in root README) to help with local lint, build and test tasks. They also take care of DB drops between runs\r\n- Added a new docker compose file to run docker containers with all Kotlin services. It also takes care of mapping tasks on one endpoint just like we do in Dev\r\n- Added necessary config files for docker environment\r\n- Added environment file `services.env` to pass common env vars to local service containers launched by compose\r\n- Added nginx config and container  to take care of path mappings\r\n- Also changed ports in `scmservice` and `pusherservice` back to 8080 for consistency\r\n","mergeCommitSha":"ce3961387d630177f53861efc7d59d3fbf97254d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/664","title":"Improve local dev experience","createdAt":"2022-03-25T18:42:57Z"}
{"state":"Merged","mergedAt":"2023-06-16T17:42:28Z","number":6640,"mergeCommitSha":"0446a0e39b53003bd91b5af93b53cfecffe71285","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6640","title":"Fix text part of emails","createdAt":"2023-06-16T17:42:15Z"}
{"state":"Merged","mergedAt":"2023-06-16T21:58:53Z","number":6641,"body":"Message editors submit on enter by default.\r\n\r\nAdd chin for newline information.\r\n<img width=\"719\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/0e73ee7d-f518-44cf-818e-25437bad49b8\">\r\n<img width=\"473\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/77d2b94f-bdee-4457-a672-23bdb458ef7d\">\r\n\r\n","mergeCommitSha":"685e3f4eff71edc0af5e971067d3cabd7709e68c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6641","title":"Submit messages on enter","createdAt":"2023-06-16T18:00:53Z"}
{"state":"Closed","mergedAt":null,"number":6642,"mergeCommitSha":"71e156c1fcb9d29282ee25ce3e37ef557decba6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6642","title":"Emails fix","createdAt":"2023-06-16T18:06:34Z"}
{"state":"Merged","mergedAt":"2023-06-16T18:29:29Z","number":6643,"body":"We were pointing at their master branch and they seem to have broken their master. We should use explicit versions instead\r\n\r\nhttps://github.com/Wandalen/wretry.action/commits/master","mergeCommitSha":"d98635888ec6b253ed5272304ab67c35ddd8f578","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6643","title":"seems the action repo broke their main. We should use explicit versions","createdAt":"2023-06-16T18:22:05Z"}
{"state":"Merged","mergedAt":"2023-06-16T20:04:25Z","number":6644,"mergeCommitSha":"e49295dee4eaa0f7e27fbe649d3edc2a0d038725","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6644","title":"Use better document labels in the prompt","createdAt":"2023-06-16T18:35:40Z"}
{"state":"Merged","mergedAt":"2023-06-16T21:17:16Z","number":6645,"mergeCommitSha":"7bf3873319f9380fcbbec35300706e04f663d5c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6645","title":"Add current date to prompt","createdAt":"2023-06-16T19:43:51Z"}
{"state":"Merged","mergedAt":"2023-06-16T21:17:53Z","number":6647,"mergeCommitSha":"48113cfd7e0133f9d4001f2a026c72ed87cecdb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6647","title":"Parse structured output from LLM","createdAt":"2023-06-16T20:14:28Z"}
{"state":"Merged","mergedAt":"2023-06-16T21:01:45Z","number":6648,"mergeCommitSha":"b7e3377b580b89482ed7f9ec9b72e07bbe52aa68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6648","title":"Add prompt to evaluate whether a thread has an interesting question","createdAt":"2023-06-16T20:28:37Z"}
{"state":"Merged","mergedAt":"2023-06-17T18:23:18Z","number":6649,"body":"\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/fde9f4c1-f02a-4a15-8d79-2b4ad865ac5c\r\n\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/dc985012-34ac-4320-b40e-389da9f8a1b3\r\n\r\n<img width=\"958\" alt=\"CleanShot 2023-06-16 at 13 55 31@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/01b7dfd1-0e95-4440-a135-2a09cb2684c2\">\r\n","mergeCommitSha":"ea91b0dac10cd60957594f4479c9a248701519f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6649","title":"Download popup after feedback","createdAt":"2023-06-16T21:32:44Z"}
{"state":"Merged","mergedAt":"2022-03-25T22:02:34Z","number":665,"body":"* actual implementation of the sidebar detecting the webview panel state will require a more complicated implementation, leaving off for now\r\n* this will add a hover coloring state, the selected state classname is there but awaiting implementation ","mergeCommitSha":"a465979cfa31a52ea2c83a6826518d5036e63a5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/665","title":"Add hover/selected thread styles","createdAt":"2022-03-25T20:33:09Z"}
{"state":"Merged","mergedAt":"2023-06-16T22:18:23Z","number":6651,"body":"This will ask the model to determine whether a thread contains an interesting question, then extract it if true. We can probably collapse the two into the same prompt.","mergeCommitSha":"15018f3ef77beeff68a69c4e90b65ab85f8e8854","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6651","title":"Add logic to extract a question and answer from a thread","createdAt":"2023-06-16T22:07:44Z"}
{"state":"Merged","mergedAt":"2023-06-16T23:04:57Z","number":6652,"body":"Subheader:\r\n<img width=\"716\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/850dea3b-85f4-41fd-9386-1ed9a9077aa8\">\r\n\r\nRestoration banner:\r\n<img width=\"749\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/b0e78e2a-e082-4705-96e8-e089c90640d2\">\r\n\r\n","mergeCommitSha":"b31c03655d6225058a523899e98c0d51188895ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6652","title":"Reimplement subheader in dashboard threads","createdAt":"2023-06-16T22:50:53Z"}
{"state":"Merged","mergedAt":"2023-06-17T01:16:28Z","number":6653,"mergeCommitSha":"4ad16a9f0d56c7c97cd841c87cdb363bb687c0d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6653","title":"With a nasty parsing hack to create chat messages","createdAt":"2023-06-16T23:07:25Z"}
{"state":"Merged","mergedAt":"2023-06-17T00:34:27Z","number":6654,"body":"- Topics\r\n- lint\r\n","mergeCommitSha":"df5f95ca940ab18cb5138e0bb8d66dac308f3db7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6654","title":"UpdateExtractionPrompt","createdAt":"2023-06-17T00:15:27Z"}
{"state":"Merged","mergedAt":"2023-06-17T02:56:16Z","number":6655,"mergeCommitSha":"b7f84eaac1bd6fdab59edfbfc0ddc3deb5c9e985","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6655","title":"Revert spacing change and fix subheader layout","createdAt":"2023-06-17T00:25:20Z"}
{"state":"Merged","mergedAt":"2023-06-17T01:18:02Z","number":6656,"mergeCommitSha":"f7bb92ab4246ff9385804d0e99e01032b52b81bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6656","title":"Source document references are shorter","createdAt":"2023-06-17T00:41:48Z"}
{"state":"Merged","mergedAt":"2023-06-17T01:57:18Z","number":6657,"body":"- Rename\r\n- Update\r\n- Update\r\n","mergeCommitSha":"662202d70422a49d99853726f844b1ee14b03433","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6657","title":"PrIngestion","createdAt":"2023-06-17T01:20:31Z"}
{"state":"Merged","mergedAt":"2023-06-18T00:44:48Z","number":6658,"body":"This will in theory fix the notifications that pop up when a QA thread is created\r\n\r\nfixes https://linear.app/unblocked/issue/UNB-1331/hub-notifies-user-when-creating-thread-with-bot","mergeCommitSha":"5bb120c4fb900261d4b2df62f62fa4f9a80e15c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6658","title":"Don't mark loading messages as unread","createdAt":"2023-06-17T01:49:33Z"}
{"state":"Merged","mergedAt":"2023-06-19T16:53:37Z","number":6659,"mergeCommitSha":"b7c2a3da9819d2f4bbe3bf4f17d791b9365011ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6659","title":"Pull Request Summary Ingestion [PHASE 1]","createdAt":"2023-06-17T03:31:30Z"}
{"state":"Merged","mergedAt":"2022-03-28T16:23:33Z","number":666,"body":"Each service gets a subpath under `/apm` e.g `/apm/pusherservice`. We can use these paths to allow external traffic for /__deepcheck and /__shallowcheck for each service. \r\n\r\nI am planning to have external monitoring (Grafana checks) watch each service's availability. We need to make sure no user traffic is dropped during rollovers and keep monitoring our services for that.\r\n\r\nNote: Without a dedicated like the ones above ALB will always route checks to API service since that is our catch all service. With these path changes we can route each service's checks to it directly using the ALB.\r\n\r\n","mergeCommitSha":"7c57fae4766f84a00e1b842b16ed0f4b727a400c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/666","title":"Adding APM health check paths to all service","createdAt":"2022-03-25T20:35:02Z"}
{"state":"Merged","mergedAt":"2023-06-20T18:25:24Z","number":6660,"body":"<img width=\"244\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/4a8dfe25-a034-4eb6-85e9-029c8f41d6c2\">\r\n\r\n","mergeCommitSha":"a89a9e47f4aa7b2f3cf4f582da0279874799f1e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6660","title":"Implement filter by provider dropdown","createdAt":"2023-06-17T04:07:23Z"}
{"state":"Merged","mergedAt":"2023-06-17T05:36:18Z","number":6661,"body":"Temporary to confirm the chat messages are being constructed correctly","mergeCommitSha":"ce69a1ff5ba281559ce7e38630733e29c831d583","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6661","title":"Add logging to openAI message parser","createdAt":"2023-06-17T04:56:31Z"}
{"state":"Merged","mergedAt":"2023-06-17T06:07:19Z","number":6662,"body":"... based on feedback\r\n\r\n<img width=\"572\" alt=\"Screenshot 2023-06-16 at 10 25 57 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/8dac0880-8d20-4fcf-9301-d83872499a3f\">\r\n","mergeCommitSha":"7b72c752e29339cec0409ab3a9ad4c6133192dfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6662","title":"Cleanup new hub layout","createdAt":"2023-06-17T05:26:04Z"}
{"state":"Merged","mergedAt":"2023-06-17T06:34:42Z","number":6663,"body":"Fixes this caused by breaking change here: #6656\n\n```\nField 'reference' is required for type with serial name 'com.nextchaptersoftware.db.models.MLTypedDocument', but it was missing at path: $[0] at path: $[0] at path: $[0]\nkotlinx.serialization.MissingFieldException: Field 'reference' is required for type with serial name 'com.nextchaptersoftware.db.models.MLTypedDocument', but it was missing at path: $[0] at path: $[0] at path: $[0]\n```","mergeCommitSha":"e703ceae55b5d8298d8272afdefcd8de08d548fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6663","title":"Fix for MLInferenceExample deserialization for legacy results","createdAt":"2023-06-17T05:56:19Z"}
{"state":"Merged","mergedAt":"2023-06-17T07:01:25Z","number":6664,"mergeCommitSha":"c57ecfacc41fa04fdb5c5781d90b46d666ae8563","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6664","title":"Use simple procedural parser for chat prompts","createdAt":"2023-06-17T06:37:34Z"}
{"state":"Merged","mergedAt":"2023-06-17T18:27:50Z","number":6665,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/89dd2aee-b895-4b29-bfe1-31e5f5ad804b\r\n\r\n","mergeCommitSha":"a55be7bad9c6c1170e0fd0cc527903d4596e8438","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6665","title":"Lower z-index on detail layout view","createdAt":"2023-06-17T18:14:09Z"}
{"state":"Merged","mergedAt":"2023-06-18T01:03:42Z","number":6666,"body":"* Render inline code elements with styling\r\n* Render lists starting from a non-1 offset\r\n* Render subscript, superscript, and strikethrough text\r\n* More consistent padding below block elements\r\n* Fix IDE styling (this is still pretty basic though -- especially jetbrains)\r\n\r\nBonus change:\r\n* Tweak MessageEditor border and @-mention elements to match new designs","mergeCommitSha":"791c423de359bbb462f13336338429ad2b8d4780","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6666","title":"MessageView renders bot content better","createdAt":"2023-06-18T00:30:28Z"}
{"state":"Merged","mergedAt":"2023-06-18T03:24:57Z","number":6667,"mergeCommitSha":"8bf25fa49c508f672194b7e5204d32cbcfa6e4a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6667","title":"Add GPT 4 32K model","createdAt":"2023-06-18T02:47:09Z"}
{"state":"Merged","mergedAt":"2023-06-18T05:03:22Z","number":6668,"body":"I used the same Observable pattern we used for the thread store.\r\n\r\n<img width=\"572\" alt=\"Screenshot 2023-06-17 at 9 36 06 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/b7bde942-3fd9-4abc-ac89-d289de0e3d62\">\r\n","mergeCommitSha":"b7861ba49ab9d0e35a535e678c568510365c032e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6668","title":"Add author names in message summary in hub","createdAt":"2023-06-18T04:37:13Z"}
{"state":"Merged","mergedAt":"2023-06-18T05:42:40Z","number":6669,"body":"The invite menu was removed from the thread header in a previous PR -- this adds it back in the menu.\r\n\r\nThere's a bunch of goofiness here hoisting state to the top level of the app -- I think we may be better off with a single ObservableObject that holds all the app state, that anyone can modify, instead of passing it around like this.\r\n\r\nAlso -- this UX feels kinda funny.  I'm wondering if we should instead just pop this up in a dialog?\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/a7bfe06e-816e-4605-b590-7a061191bb8d\r\n\r\n","mergeCommitSha":"504381be0161435366263084a4c00d617a96a79b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6669","title":"Add invite menu to hub","createdAt":"2023-06-18T05:29:46Z"}
{"state":"Merged","mergedAt":"2022-03-28T17:26:40Z","number":667,"body":"- Moved API service directory to `projects/services`\r\n- Updated gradle.settings with new project path\r\n- Moved helm charts for each service to its respective directory under `/projects`\r\n- Updated helm ansible playbooks with new paths\r\n- Updated CI/CD workflow for services to use the new paths\r\n- Updated local env docker compose file with new path for Api service dockerfile\r\n- Updated make targets with new path for apiservice project","mergeCommitSha":"8da3bdce0ca1b2365f611a57be352cfef4a696e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/667","title":"Move API service to its final resting place","createdAt":"2022-03-25T21:27:15Z"}
{"state":"Merged","mergedAt":"2023-06-18T19:07:22Z","number":6671,"mergeCommitSha":"0f70dec3fd569416bf2d8dc1b1b4139210a31b3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6671","title":"Fix for message editing and add tests to prevent regressing again","createdAt":"2023-06-18T17:44:18Z"}
{"state":"Merged","mergedAt":"2023-06-18T19:07:47Z","number":6672,"body":"All teams should, by default, embed their documents to drive search.","mergeCommitSha":"a111137472a429ec4fdbcc5ecbfb8f553ff12543","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6672","title":"Enable HuggingFace embeddings by default","createdAt":"2023-06-18T17:50:06Z"}
{"state":"Merged","mergedAt":"2023-06-19T05:43:55Z","number":6673,"mergeCommitSha":"b8e4c0880264b5baef100bf0fd867bf13a48b55f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6673","title":"Add org name to prompt","createdAt":"2023-06-19T04:33:40Z"}
{"state":"Merged","mergedAt":"2023-06-19T16:49:01Z","number":6674,"body":"Do not show feedback buttons if initial feedback is set.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/afc0d74d-318f-4c20-8ec8-44454da47034\r\n\r\n","mergeCommitSha":"85fc5ed79332460d701b99dfd8188e4ea0ee3357","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6674","title":"Conditional Feedback","createdAt":"2023-06-19T16:33:17Z"}
{"state":"Merged","mergedAt":"2023-06-19T16:51:19Z","number":6675,"mergeCommitSha":"4953f9b8ac1727e3f8b28968b5fc43bff5be86ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6675","title":"Increase lambda interval","createdAt":"2023-06-19T16:51:12Z"}
{"state":"Merged","mergedAt":"2023-06-19T18:04:59Z","number":6676,"body":"- Create an IAM role with Read-only S3 access policy in each environment account\r\n- Create an IAM group in root account that grants users access to the read-only S3 role. \r\n\r\nThis group is used to grant our team members access to all S3 buckets without the need for an Admin level group. ","mergeCommitSha":"b80b31581e529185e425f0820b07b181b925fca9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6676","title":"adding a read-only group for S3 access","createdAt":"2023-06-19T18:02:23Z"}
{"state":"Merged","mergedAt":"2023-06-19T18:03:06Z","number":6677,"mergeCommitSha":"43d228bc47df3b309084b92bb6acf03b970bf046","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6677","title":"Fix bitbucket parsing","createdAt":"2023-06-19T18:02:51Z"}
{"state":"Merged","mergedAt":"2023-06-19T18:31:07Z","number":6678,"body":"- https://dev.getunblocked.com/dashboard/team/18aefcf8-782c-4f07-a687-7efeaee2467c/thread/10d3607b-b5a6-41a7-ae6a-8323766796e4","mergeCommitSha":"fcee04b665a2019474f2992b9f8e526ac7671b0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6678","title":"Pinecone query does not crash when you ask for zero results","createdAt":"2023-06-19T18:08:22Z"}
{"state":"Merged","mergedAt":"2023-06-19T19:54:20Z","number":6679,"body":"Render @mention inline text, and render content in lists as well\r\n\r\n<img width=\"572\" alt=\"Screenshot 2023-06-19 at 11 21 09 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/962659a3-09cf-4065-8687-b61f78703a04\">\r\n","mergeCommitSha":"af811346bc4b2d395c60b16a730f498905cfbec5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6679","title":"Improved hub text rendering","createdAt":"2023-06-19T18:21:53Z"}
{"state":"Merged","mergedAt":"2022-03-28T18:32:23Z","number":668,"body":"Replace Message DataCacheStore with streams.  Message objects now get an Author TeamMember annotation.  A lot of this is plumbing and mock changes because we're now passing around annotated ThreadAndParticipants and MessageAndAuthor objects in more places.","mergeCommitSha":"6a1f16f818532b4af6e6048bedab14ed30aee79f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/668","title":"Replace Message DataCacheStore with DataCacheStream","createdAt":"2022-03-25T22:06:07Z"}
{"state":"Merged","mergedAt":"2023-06-19T20:54:09Z","number":6680,"body":"Refactored search mode + inbox state into a single state machine. Consolidates and simplifies updating state.\r\n\r\nUpdated behaviour to conditionally enter \"searchFocused\" on opening hub.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/02602c3e-3120-48cc-9d92-e3d1f76db685\r\n\r\n","mergeCommitSha":"128b1515d33c35c9bcf0d395d379a38417fea716","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6680","title":"Update Hub state machine + search focused on open","createdAt":"2023-06-19T19:15:02Z"}
{"state":"Merged","mergedAt":"2023-06-20T00:07:44Z","number":6681,"mergeCommitSha":"0cb0924af989a1e19f4cc2732c27f8eeb321531b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6681","title":"Increases the # of documents used for the expert summaries","createdAt":"2023-06-19T19:20:42Z"}
{"state":"Merged","mergedAt":"2023-06-19T20:07:31Z","number":6682,"mergeCommitSha":"f71016cae7e3f3712210778e5b03b137584fd72d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6682","title":"Add pr summaires ingestion","createdAt":"2023-06-19T19:25:58Z"}
{"state":"Merged","mergedAt":"2023-06-19T20:06:09Z","number":6683,"body":"Part of moving bot search to search-service.","mergeCommitSha":"f35cd9fb5058989156fda20f57f2fae7d373f42c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6683","title":"Refactor thread API","createdAt":"2023-06-19T19:33:17Z"}
{"state":"Merged","mergedAt":"2023-06-19T21:33:39Z","number":6684,"body":"Slack notifications take time, so defer till after the message has been persisted.","mergeCommitSha":"3e6d3961e71b9ecbb3cafa47d53d849d27050858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6684","title":"Persist the bot message *before* sending Slack notification","createdAt":"2023-06-19T20:37:30Z"}
{"state":"Merged","mergedAt":"2023-06-19T21:13:06Z","number":6685,"mergeCommitSha":"d8dce22294dfcecec674fb50bcd91fb6894d9829","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6685","title":"Add state machine","createdAt":"2023-06-19T20:51:47Z"}
{"state":"Merged","mergedAt":"2023-06-19T22:08:05Z","number":6686,"body":"Fix issue where incoming streams into DiscussionThreadView Stream was causing a rerender to occur unexpectedly.\r\n\r\nThis caused some `mapStreamAsync` within the streams to rerun, causing a flash as the states changed.","mergeCommitSha":"6a1b45fd42b5725873b60c3aa16a6298fc760bcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6686","title":"Dedupe values for reloading thread","createdAt":"2023-06-19T21:23:05Z"}
{"state":"Merged","mergedAt":"2023-06-19T22:13:48Z","number":6687,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/a7cd9da7-f6bd-409c-b24e-bb64b3b802ea\r\n\r\n\r\n* Remove borders in between messages in thread\r\n* Add cancel button to inline message editor\r\n* Allow ESC key to cancel out of editing state\r\n* Move editor inline buttons to chin\r\n* Update IDEs and PR threads to use inline editor\r\n* Update editing state to use inline editor","mergeCommitSha":"a654024cc7bb89559ec23adb06ceb5129a157eb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6687","title":"Update thread and message editor styling across clients","createdAt":"2023-06-19T21:39:41Z"}
{"state":"Merged","mergedAt":"2023-06-19T23:17:03Z","number":6688,"mergeCommitSha":"0318d70c38cfc1438765a4ac3d94f5dcecf91007","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6688","title":"Add scm bucket and aws glue to pul request pipeline","createdAt":"2023-06-19T22:53:10Z"}
{"state":"Merged","mergedAt":"2023-06-20T22:17:42Z","number":6689,"body":"In the dashboard, we clip the bottom of the thread listing (to display the message editor), so if you post a new message it could disappear.  Scroll when a new message is posted.\r\n\r\nI added a ScrollContext to allow child UIs to control scrolling.  This also allowed fixing a bug in the new topic UI.","mergeCommitSha":"3a9b933efc03dbf19735cc127e59b05e22228a3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6689","title":"Scroll to the bottom of the thread after posting a new message","createdAt":"2023-06-19T23:18:49Z"}
{"state":"Merged","mergedAt":"2022-03-25T22:24:07Z","number":669,"body":"More horsepower ","mergeCommitSha":"6a13ef8a768aa55389ed6cd063a873c4e0feda88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/669","title":"increasing Kubernetes resources for all services","createdAt":"2022-03-25T22:13:16Z"}
{"state":"Merged","mergedAt":"2023-06-20T01:26:15Z","number":6690,"body":"I want to flag out the new IDE explorer UI work as we might need to iterate on it a bit, so the old one will remain active while we're doing so.","mergeCommitSha":"83f1033f966d243b24a68f6be479bfb10dacecdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6690","title":"Add feature flag for new explorer IDE work","createdAt":"2023-06-20T00:30:06Z"}
{"state":"Merged","mergedAt":"2023-06-20T16:21:48Z","number":6691,"mergeCommitSha":"ac20e0d47e98bbfcdced927affbeaafaa4507c97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6691","title":"name in q parameter this was left over from the port to pg search.","createdAt":"2023-06-20T01:10:43Z"}
{"state":"Merged","mergedAt":"2023-06-20T01:12:10Z","number":6692,"mergeCommitSha":"6206fe848b780aa4d458605010bb9db3c7b56545","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6692","title":"Increase teimouts","createdAt":"2023-06-20T01:12:05Z"}
{"state":"Merged","mergedAt":"2023-06-20T01:51:08Z","number":6693,"body":"Skeleton for new explorer UI.\r\n\r\nThis code (`NewExplorerUI/*`) is a clone of the existing UI (`ExplorerUI/*`).  The one that is rendered is determined by the feature flag.  This way we can do whatever we need to to the new UI without breaking the old one.","mergeCommitSha":"2779829c17c5d4dc5af38d2961099edaf10d997d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6693","title":"New explorer UI","createdAt":"2023-06-20T01:28:41Z"}
{"state":"Merged","mergedAt":"2023-06-20T02:09:36Z","number":6694,"mergeCommitSha":"19f2438af5513f90e27520728d2753fde6e79e73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6694","title":"Increase leeway","createdAt":"2023-06-20T02:09:29Z"}
{"state":"Merged","mergedAt":"2023-06-20T03:21:18Z","number":6695,"mergeCommitSha":"273778692f7bcef730295c2dd290a98daddbe44e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6695","title":"Show pull request summary","createdAt":"2023-06-20T03:21:04Z"}
{"state":"Merged","mergedAt":"2023-06-20T06:35:48Z","number":6696,"body":"Instead of this\r\n```kotlin\r\nsealed class Base(\r\n    open val id: String,\r\n) {\r\n    data class Sub(\r\n        override val id: String,\r\n    ) : Base(id)\r\n}\r\n```\r\n\r\nDo this, which simplifies the constructor hierarchy for large classes and\r\neliminates duplicate base val properties when serialized:\r\n```kotlin\r\nsealed class Base {\r\n    abstract val id: String\r\n\r\n    data class Sub(\r\n        override val id: String,\r\n    ) : Base()\r\n}\r\n```\r\n\r\nSee also:\r\nhttps://github.com/Kotlin/kotlinx.serialization/blob/master/docs/polymorphism.md#sealed-classes","mergeCommitSha":"e2b8e8e24639023d66df9e144ae605dc65d8d469","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6696","title":"Prefer abstract sealed class base vals over open base vals","createdAt":"2023-06-20T04:44:27Z"}
{"state":"Merged","mergedAt":"2023-06-21T06:07:41Z","number":6697,"mergeCommitSha":"f9380139a9a66e7ab3d0a4c4045d3c738547b41b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6697","title":"Add topic and expert template kinds to team settings","createdAt":"2023-06-20T05:55:08Z"}
{"state":"Merged","mergedAt":"2023-06-20T18:14:13Z","number":6698,"mergeCommitSha":"0225a68f42b9787abce945f73b8899261421e133","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6698","title":"Show \uD83D\uDC4D/\uD83D\uDC4E messages from Thread page in admin console","createdAt":"2023-06-20T16:21:19Z"}
{"state":"Merged","mergedAt":"2023-06-20T16:29:43Z","number":6699,"mergeCommitSha":"9f251899c7597ffbcd0a627609d2ab1c65df7372","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6699","title":"Fix the inference document included column","createdAt":"2023-06-20T16:23:19Z"}
{"state":"Merged","mergedAt":"2022-01-18T20:50:24Z","number":67,"mergeCommitSha":"818ed54a85e41458bfa54f500e98a6f07d6f6a29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/67","title":"Fix bug in check-for-changes job","createdAt":"2022-01-18T20:45:41Z"}
{"state":"Merged","mergedAt":"2022-03-25T23:10:53Z","number":670,"body":"I guess this never worked and nobody noticed?  This is preventing the DEV dashboard from loading the syntax hilighting library.","mergeCommitSha":"8e51b8cff2353be57ca4e90a892b89d540c7312c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/670","title":"Dasboard: fix dev public path","createdAt":"2022-03-25T23:04:05Z"}
{"state":"Merged","mergedAt":"2023-06-20T22:06:15Z","number":6700,"body":"Refactored TeamStore to be based on a single publisher.\r\n\r\nComputing variables and team / selectedTeam publisher from this single state publisher should hopefully prevent weird states. e.g. Teams but no selected team.\r\n\r\nRefactor does *not* change the TeamStore interface. External APIs still remain nearly identical.\r\n","mergeCommitSha":"6fd1e93b4b506bc4346fcf562d710decb0ce3868","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6700","title":"Refactor teamStore to potentially fix missing selected team","createdAt":"2023-06-20T18:27:44Z"}
{"state":"Merged","mergedAt":"2023-06-20T18:55:25Z","number":6701,"mergeCommitSha":"d20954adc1e81821ddf5ea17183b902b65a018d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6701","title":"Add search index event","createdAt":"2023-06-20T18:34:41Z"}
{"state":"Merged","mergedAt":"2023-06-20T21:27:09Z","number":6702,"body":"These are going into the embedding space instead.","mergeCommitSha":"082b2960bba0184ff18bbda217298cacb0216008","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6702","title":"Remove topics from prompt","createdAt":"2023-06-20T19:13:30Z"}
{"state":"Merged","mergedAt":"2023-06-20T20:24:37Z","number":6703,"mergeCommitSha":"0bc631dcf71192fa62ac5ca72d8474fbf1d0e6f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6703","title":"Cleanup dependencies in lib-summary-queue","createdAt":"2023-06-20T19:14:16Z"}