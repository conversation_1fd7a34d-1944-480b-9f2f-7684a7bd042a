{"state":"Closed","mergedAt":null,"number":4937,"body":"Reverts NextChapterSoftware/unblocked#4930","mergeCommitSha":"7dfbd7d699d025b25d0defd13814ca7413525c5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4937","title":"[Just In Case] - Revert \"Add CSP for realz\"","createdAt":"2023-02-22T05:26:46Z"}
{"state":"Merged","mergedAt":"2023-02-22T05:54:22Z","number":4938,"mergeCommitSha":"2a4affa419c64ccb50841f527e7d4eb8f2ae8b7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4938","title":"Remove unsafe-inline from style-src (report only)","createdAt":"2023-02-22T05:51:07Z"}
{"state":"Merged","mergedAt":"2023-02-22T06:23:02Z","number":4939,"mergeCommitSha":"bd5a795b2ebb767da13f57a7ef2ce4641eaa28e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4939","title":"Remove report only policy checking removal of unsafe-inline (we have to keep it)","createdAt":"2023-02-22T06:20:05Z"}
{"state":"Merged","mergedAt":"2022-03-07T21:23:58Z","number":494,"mergeCommitSha":"d5c2e91bae4c84809cd7087638155db01c1d0c2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/494","title":"Introduce SourceMark data class and create SPs in thread creation","createdAt":"2022-03-07T20:01:30Z"}
{"state":"Merged","mergedAt":"2023-02-22T06:45:24Z","number":4940,"body":"No longer used. See #4914 and #4924.\r\n\r\nMigration plan:\r\n\r\n- [x] local\r\n- [x] dev\r\n- [x] prod","mergeCommitSha":"d5cd58d7a6fecf1a32b5e9f662b8aa50adadbd28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4940","title":"Drop IdentityModel.accessToken column","createdAt":"2023-02-22T06:31:39Z"}
{"state":"Merged","mergedAt":"2023-02-22T16:21:58Z","number":4941,"body":"- Added lambda function to create RDS snapshots and export them to S3\r\n- Added a replica bucket to cold site and configured bucket replications between hot and cold regions\r\n- Added DB CPU, IO and Free Disk alarms (required by drata)\r\n- Added an SNS topic to deliver alarms to environment admin email account All changes have been deployed to both dev and prod.\r\n\r\n\r\nI hate compliance work! I really do!","mergeCommitSha":"422b44eee7bcad8f820f66ecb9f77e0aa3050cff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4941","title":"Automated RDS backups to S3 and DB alarms","createdAt":"2023-02-22T11:35:05Z"}
{"state":"Merged","mergedAt":"2023-02-22T17:42:13Z","number":4942,"mergeCommitSha":"963ee1b37cd41d9c34874c372ac89a6e0755d641","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4942","title":"Remove unused GitHubAppApi functions","createdAt":"2023-02-22T17:21:50Z"}
{"state":"Merged","mergedAt":"2023-02-22T17:41:25Z","number":4943,"body":"I removed a single symbol when I was cleaning up the code and missed this. Oops.","mergeCommitSha":"3fa795f60f8091ff8408ea5620e79e41d98d11ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4943","title":"Fix bug where search UI wouldn't start up","createdAt":"2023-02-22T17:27:54Z"}
{"state":"Merged","mergedAt":"2023-02-22T17:50:58Z","number":4944,"mergeCommitSha":"1ff6e1b7985c8c6d99c75b07b116048e4247e299","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4944","title":"Remove unused imports","createdAt":"2023-02-22T17:46:06Z"}
{"state":"Merged","mergedAt":"2023-02-22T17:53:06Z","number":4945,"mergeCommitSha":"3d602231eea787338b5452fd322544cec3a08fcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4945","title":"Fix lint","createdAt":"2023-02-22T17:51:46Z"}
{"state":"Merged","mergedAt":"2023-02-22T18:49:43Z","number":4946,"body":"Right now its returning a GitHub-specific GraphQL response, which makes it difficult to abstract. This PR changes it so that it only returns what the caller needs: a list of PR numbers, plus a cursor for the next page if there is one.","mergeCommitSha":"6fb298c3fa20a9352a64f9e13165a1ded6a8a1ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4946","title":"V4User.searchPullRequestsWithComments returns a generic result","createdAt":"2023-02-22T18:07:49Z"}
{"state":"Merged","mergedAt":"2023-02-22T21:27:24Z","number":4947,"body":"Moving VSCode RepoStore to shared/IDE.\r\n","mergeCommitSha":"802b1df6e878c253da583dac0a98ea24c73309a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4947","title":"IDE RepoStore","createdAt":"2023-02-22T18:15:59Z"}
{"state":"Merged","mergedAt":"2023-02-22T18:23:25Z","number":4948,"mergeCommitSha":"5658f7707fb655a6df37950456fa82872f537d7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4948","title":"adjust the alarms to make them less noisy","createdAt":"2023-02-22T18:17:30Z"}
{"state":"Merged","mergedAt":"2023-02-22T18:41:13Z","number":4949,"mergeCommitSha":"3e1474fad30b39a077a4e374b8e2403ec605ab61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4949","title":"Style validator was not working with private.yml","createdAt":"2023-02-22T18:22:22Z"}
{"state":"Merged","mergedAt":"2022-03-07T21:45:08Z","number":495,"body":"- Added Agora S3 stack to our App environment so we get it deployed to prod as well. \r\n- Renamed the bucket to include environment name. S3 bucket names are global and this was causing a conflict. ","mergeCommitSha":"9865d848683b920f070303eeaa922a1d33b08396","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/495","title":"deploy agora s3 bucket and user to all envs","createdAt":"2022-03-07T20:11:45Z"}
{"state":"Merged","mergedAt":"2023-02-22T18:49:07Z","number":4950,"mergeCommitSha":"a0c43f6b8a68433346f69b1c0c3b84b9112bb4ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4950","title":"Move to filecollection for style task","createdAt":"2023-02-22T18:46:21Z"}
{"state":"Merged","mergedAt":"2023-02-22T19:04:03Z","number":4951,"body":"Make these alarms less noisy","mergeCommitSha":"b75aea5fc86c3d9d0d803527e76911a0c1aeda0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4951","title":"make them less noisy","createdAt":"2023-02-22T18:47:32Z"}
{"state":"Merged","mergedAt":"2023-02-22T19:43:13Z","number":4952,"body":"Similar to https://github.com/NextChapterSoftware/unblocked/pull/4946, this function needs to return a generic response before we can abstract it for the work to support Bitbucket and GitLab.","mergeCommitSha":"a1b7a1dc496a12d5878f22060fa707a36a2b48c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4952","title":"V4Org.pullRequestReviewThreads returns a generic result","createdAt":"2023-02-22T19:29:37Z"}
{"state":"Merged","mergedAt":"2023-02-22T21:10:30Z","number":4954,"body":"We currently merge SCM accounts on the backend when the SCM account email matches. However,\nwe found that this leads to unexpected behaviour.\n\nConsider the scenario:\n- user creates Unblocked account using GitHub with \"<EMAIL>\"\n- user creates Unblocked account using Bitbucket with \"<EMAIL>\" (same email)\n\nOld behaviour:\n- user signs into Unblocked using GitHub, sees all Bitbucket and GitHub teams\n- user signs into Unblocked using Bitbucket, sees all Bitbucket and GitHub teams\n\nNew behaviour:\n- user signs into Unblocked using GitHub, sees only GitHub teams\n- user signs into Unblocked using Bitbucket, sees only Bitbucket teams\n\nIf we want to show a merged view of teams regardless of the SCM account they\nsign in with, then we need to adequately explain when this merge takes place\nand provide the ability to unmerge the SCM accounts.","mergeCommitSha":"7404c6ddc766f522d87c0e5737eb30fe054891f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4954","title":"Signing in with an SCM account provides access to the teams for that SCM account only","createdAt":"2023-02-22T20:07:58Z"}
{"state":"Merged","mergedAt":"2023-02-23T19:25:09Z","number":4958,"body":"- Java 19\r\n- Test ci\r\n","mergeCommitSha":"b075833fc944cc1c4c9ba004795a5ac3c6b29b2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4958","title":"TestJava19","createdAt":"2023-02-22T21:09:34Z"}
{"state":"Merged","mergedAt":"2022-03-14T21:23:43Z","number":496,"body":"* Build command and form to create pull request (with mocked data)\r\n![image](https://user-images.githubusercontent.com/********/*********-67e28058-3464-40ab-aeb1-e2e00400c5b5.png)\r\n![image](https://user-images.githubusercontent.com/********/*********-845d2f21-507f-4d23-a327-b981d0451830.png)\r\n\r\n* Integrate drag and drop rows for the notes\r\n* Add ExpandableSection component for use/re-use\r\n* Add BranchTag component for use/re-use\r\n* NOTE: API to create pull requests needs to be added (https://github.com/NextChapterSoftware/unblocked/pull/500)\r\n* NOTE: To create the PR, we'll need to first update the note messages with the latest changes, and then post the data to the pull request as comments/issues \r\n* There's missing sourcemark data to populate both the code block in each note thread as well as the sourcepoint reference (TODOs added in the code)","mergeCommitSha":"bf68dd90e529a1e85e0882628db99f2f489b744e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/496","title":"Create Pull Request form","createdAt":"2022-03-07T21:01:13Z"}
{"state":"Merged","mergedAt":"2023-02-22T22:32:13Z","number":4961,"body":"We will be making some changes to make topics with `TopicSource.Curated` source as the approved topics for a customer. This will let us pre-populate the list of approved topics for a customer during onboarding.\r\n\r\nTo better reflect this change, lets rename the `TopicSource.Curated` enum to `TopicSource.Approved`.","mergeCommitSha":"f04d3e2ebba36c6091504c8e53a90e2170bf5437","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4961","title":"Rename TopicSource.Curated to TopicSource.Approved","createdAt":"2023-02-22T21:40:16Z"}
{"state":"Merged","mergedAt":"2023-02-22T23:07:25Z","number":4962,"body":"Refactor Agent into its own service.\r\n\r\nAdd Token Service to receive and send tokens to agent.","mergeCommitSha":"f5f68bebc44323e89642a967ecb65c94fcc64893","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4962","title":"JB Token Transport layer","createdAt":"2023-02-22T21:42:17Z"}
{"state":"Merged","mergedAt":"2023-02-22T22:31:42Z","number":4963,"body":"<img width=\"995\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/220765986-05a7f943-68b5-45d3-8112-c08da46a5ede.png\">\r\n<img width=\"889\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/220766124-5c274777-13cc-4f45-9fb2-1c896f9aa233.png\">\r\n\r\nalso add button to clear filters in the mobile dashboard:\r\n<img width=\"775\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/220766236-8857c680-18c6-4b58-9293-a98ba50b13bd.png\">\r\n","mergeCommitSha":"3c360130788011ce61a39953292bd298a3b6858e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4963","title":"Add topic descriptions to dashboard","createdAt":"2023-02-22T21:44:52Z"}
{"state":"Merged","mergedAt":"2023-02-22T22:39:46Z","number":4967,"body":"Added a new stack to create IAM role and Lambda function which scans all the log groups and sets a maximum retention of 90 days on each one. Special thanks to ChatGPT for writing the actual function!!","mergeCommitSha":"e2f4ea6342266b14e98d9a924f3a18c6defae39d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4967","title":"Add lambda function to enforce global log retention","createdAt":"2023-02-22T22:25:37Z"}
{"state":"Merged","mergedAt":"2023-02-22T23:43:14Z","number":4969,"body":"Allow deleting any topic that was not created by a customer.","mergeCommitSha":"8f8032deac92ce2e5abd1f248691ced51599eb94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4969","title":"Update delete topic logic","createdAt":"2023-02-22T23:23:52Z"}
{"state":"Merged","mergedAt":"2022-03-09T05:12:15Z","number":497,"body":"Include thread participants in the thread api model. ","mergeCommitSha":"83462c65bcae74cee8db928f9cc4d43665e3cd2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/497","title":"Include thread participants for threads","createdAt":"2022-03-07T21:20:44Z"}
{"state":"Merged","mergedAt":"2023-02-23T00:24:19Z","number":4971,"mergeCommitSha":"ee30e633a85aa24cf95920125f3d6c6de1a5d1a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4971","title":"Refactor to use CryptoUtils","createdAt":"2023-02-23T00:23:45Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:08:04Z","number":4972,"body":"<img width=\"698\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/220795071-4b54c7bb-fb02-451e-b9cb-debb21d0f86d.png\">\r\n\r\nAdd flags to the `Person` model to keep track of a new user's progress per the post-onboarding checklist. \r\n* `hasViewedTopFile` will be manually set by the `/person/hasViewedTopFile` endpoint\r\n* `hasCreatedNote` will be set upon the user's first thread POST\r\n* `hasCreatedWalkthrough` will be set upon the user's first walkthrough POST","mergeCommitSha":"ff43821d47f144fff379adf18335f9dc85b43a3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4972","title":"Add flags and endpoints for post-onboarding tasks","createdAt":"2023-02-23T00:32:57Z"}
{"state":"Merged","mergedAt":"2023-02-23T23:35:04Z","number":4973,"mergeCommitSha":"b6b415ccb92a1ce2c31bb6b5a417358d5af6244e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4973","title":"API service returns TopicSource.Approved topics","createdAt":"2023-02-23T00:41:42Z"}
{"state":"Merged","mergedAt":"2023-02-23T01:02:46Z","number":4974,"body":"YOLO","mergeCommitSha":"48ad4c58cb407b27cbe1d11a08003c4bd1a5cacb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4974","title":"Added test scheme to bypass SkipCORS in tests for test clients, reordered plugins","createdAt":"2023-02-23T00:42:54Z"}
{"state":"Merged","mergedAt":"2023-02-23T01:19:44Z","number":4975,"mergeCommitSha":"ed2bc0cd753da5302b0fabefec8a15845d26e0b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4975","title":"Refactor ScmAuthApi","createdAt":"2023-02-23T01:03:12Z"}
{"state":"Merged","mergedAt":"2023-02-23T20:59:41Z","number":4976,"body":"Refactored token providers to cache values in memory. Reduces load on native storage implementations. Also allows for push based token providers (e.g. JetbrainsTokenProvider)\r\n\r\n\r\n","mergeCommitSha":"45497ecc32a7f23ce70c1d6a911cc3f633e3ebbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4976","title":"Refactored token providers","createdAt":"2023-02-23T01:10:14Z"}
{"state":"Merged","mergedAt":"2023-02-23T01:28:53Z","number":4977,"mergeCommitSha":"56279936eb5c15592b0394a723c8f69703da18f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4977","title":"Cleanup old migration","createdAt":"2023-02-23T01:19:49Z"}
{"state":"Merged","mergedAt":"2023-02-23T02:48:52Z","number":4978,"mergeCommitSha":"74128b6fbcffedae4370b6f1eac16f42376a8482","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4978","title":"Test Bitbucket Cloud in DEV","createdAt":"2023-02-23T02:47:23Z"}
{"state":"Merged","mergedAt":"2023-02-23T04:29:30Z","number":4979,"mergeCommitSha":"9af0ae0bdc6dc59b4e4625e1a8b526d12f143e87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4979","title":"Introduce ScmAppApiFactory to abstract \"App\" API","createdAt":"2023-02-23T04:12:40Z"}
{"state":"Merged","mergedAt":"2022-03-07T21:58:24Z","number":498,"mergeCommitSha":"ce642ebd42a51bd50d2062de96307635019e4b6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/498","title":"Rip out Thundra","createdAt":"2022-03-07T21:52:35Z"}
{"state":"Merged","mergedAt":"2023-02-23T05:55:22Z","number":4980,"body":"OAuth implementation require a code exchange API parameter called `redirect_uri`\nthat matches one of the pre-configured redirect URLs declared in the OAuth app\nconfiguration.\n\nGitHub is a bit lenient here, which is why we did not catch until now with GitLab\nand Bitbucket.","mergeCommitSha":"89349c8223707b13a5d2bece45534ff6237ef151","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4980","title":"Fix OAuth code exchange","createdAt":"2023-02-23T05:20:55Z"}
{"state":"Merged","mergedAt":"2023-02-23T06:25:43Z","number":4981,"mergeCommitSha":"dfae0145d29e7419130d9249427e418ab6049be1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4981","title":"alarm is too noisy","createdAt":"2023-02-23T06:22:24Z"}
{"state":"Merged","mergedAt":"2023-02-23T08:16:41Z","number":4985,"mergeCommitSha":"0b1c73ba5d782a9cf394247c0d73209e34d8b956","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4985","title":"Use OAuthTokens class for SCM user APIs and persist refresh token","createdAt":"2023-02-23T07:40:48Z"}
{"state":"Closed","mergedAt":null,"number":4989,"body":"Alternatively we could fire an event and have it be handled in the topic service if we don't want to call this from the SCM service.","mergeCommitSha":"9767eedebb3bc842010af61acf749d509e3f4b1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4989","title":"[RFC] Trigger topic generation once bulk ingestion is complete","createdAt":"2023-02-23T17:26:37Z"}
{"state":"Merged","mergedAt":"2023-02-23T18:19:10Z","number":4990,"mergeCommitSha":"e7d8bab48b464e0642277e3d145988de3e6c4030","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4990","title":"David has inspired me to add proper permissions","createdAt":"2023-02-23T18:16:55Z"}
{"state":"Merged","mergedAt":"2023-02-23T19:34:52Z","number":4991,"body":"Will immediately revert when I get logs","mergeCommitSha":"4fbc1258137dbacc943eaacccd942ebf7c4a4de5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4991","title":"Add logging to capture CORS data in prod","createdAt":"2023-02-23T19:13:14Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:00:26Z","number":4992,"mergeCommitSha":"67347646ae48f4869bf20dab6aa65e65e5d8994b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4992","title":"Add background job to handle topic generation events","createdAt":"2023-02-23T19:32:20Z"}
{"state":"Merged","mergedAt":"2023-02-23T20:04:22Z","number":4993,"body":"- Added a new type of lifecycle rule for log buckets to remove data after 90 days\r\n- Enabled bucket versioning on any buckets that hold user data or logs\r\n- Enabled user data (customer) data lifecyle rule on buckets that hold temporary user data. This rule makes sure delete markers and older versions of objects are removed before 30 days","mergeCommitSha":"95be561964c4d091351e93e154066bcdcdb1ba68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4993","title":"Enable bucket versioning and lifecyle rules","createdAt":"2023-02-23T19:45:30Z"}
{"state":"Merged","mergedAt":"2023-02-23T20:08:45Z","number":4994,"body":"https://github.com/JLLeitschuh/ktlint-gradle/pull/634\r\n\r\nThat pr actuallly fixes the problem.\r\nI forked it and did a local maven build of the plugin here:\r\nhttps://github.com/NextChapterSoftware/ktlint-gradle\r\n","mergeCommitSha":"883c0917d40cd74b27e66ba0201dbdd5606687f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4994","title":"Move to custom ktlint gradle plugin with gradle 8 support","createdAt":"2023-02-23T19:59:17Z"}
{"state":"Merged","mergedAt":"2023-02-23T20:37:23Z","number":4995,"body":"`expiredObjectDeleteMarker` was clashing with the other configs. Removed it from Backup and Log retention policy rules","mergeCommitSha":"f1c5d8d5109e6891aad0c6f63cff831b13b4e3f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4995","title":"removed clashing configs","createdAt":"2023-02-23T20:30:18Z"}
{"state":"Merged","mergedAt":"2023-02-23T20:48:45Z","number":4996,"mergeCommitSha":"e699f931434625401eeadca7a9a737b5d840d5b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4996","title":"remove the bad config from waf logs bucket","createdAt":"2023-02-23T20:48:39Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:18:37Z","number":4997,"body":"Setup basic AuthStore in agent. Auth status is then piped to Kotlin plugin.\r\n\r\n<img width=\"594\" alt=\"CleanShot 2023-02-23 at 17 23 58@2x\" src=\"https://user-images.githubusercontent.com/1553313/221070555-0cea7e77-2acb-4a27-b15b-1fcc2ef9a226.png\">\r\n\r\nNext step is setting up auth sidebar.","mergeCommitSha":"29dbb979d1d8f0c8849c36a59f7301c3018eaea5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4997","title":"JB AuthStore","createdAt":"2023-02-23T21:00:51Z"}
{"state":"Merged","mergedAt":"2023-02-23T22:40:41Z","number":4998,"mergeCommitSha":"09c52fb69b883931314a223222bdd899e545cf4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4998","title":"Add array support to exposed","createdAt":"2023-02-23T21:11:07Z"}
{"state":"Merged","mergedAt":"2023-02-23T23:19:27Z","number":4999,"mergeCommitSha":"8e98c02eb40a806e6a3bae26ae15a4e8bb5d6b30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4999","title":"Remove CORS debug logging","createdAt":"2023-02-23T22:00:07Z"}
{"state":"Closed","mergedAt":null,"number":5,"mergeCommitSha":"6a459a9eb52066983fe8a5158b9ee12ef72989a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5","title":"Break","createdAt":"2021-12-10T01:02:43Z"}
{"state":"Merged","mergedAt":"2022-01-18T17:54:02Z","number":50,"body":"This makes the VSCode project match the Web project, more or less.","mergeCommitSha":"cc4b53d6e56361c07b9d7958ea27a0e81b0f997f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/50","title":"Shuffle files and folders in VSCode project","createdAt":"2022-01-18T01:09:51Z"}
{"state":"Closed","mergedAt":null,"number":500,"mergeCommitSha":"78a4325910abfa4fbac1c08c17b25d71f48ae173","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/500","title":"Add create PR models and endpoints","createdAt":"2022-03-07T22:21:41Z"}
{"state":"Merged","mergedAt":"2023-02-24T01:09:24Z","number":5000,"body":"Should be a lot more precise, especially when there has been a large code refactor.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-1018","mergeCommitSha":"3fcb0324cf52224ca1445827b2855da54d58da05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5000","title":"Fix misplaced sourcemark by considering the best candidate based on multi-line similarity","createdAt":"2023-02-23T22:47:06Z"}
{"state":"Merged","mergedAt":"2023-02-24T20:59:51Z","number":5001,"body":"Simple hostname validation check. Upgrades are always hosted within our domain so hardcoding the hostname is fine. macOS TLS then guarantees domain name validation checks on the outbound request. \r\n\r\nFor an attacker to get around this check, they would have to deploy a bad payload to our S3 bucket. The most likely vector for this attack is through GitHub CI/CD. Remediation in this scenario is basically zero.","mergeCommitSha":"9cf4df73ebad74346febd1594ebe62d8f6e8b5ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5001","title":"A compromised API service could send users to a bad upgrade payload","createdAt":"2023-02-23T22:55:52Z"}
{"state":"Merged","mergedAt":"2023-02-24T00:10:42Z","number":5002,"mergeCommitSha":"0f8b0c85ac52c95c0990ae2bd2679abb54e65aa1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5002","title":"Add topic keywords","createdAt":"2023-02-23T23:27:13Z"}
{"state":"Merged","mergedAt":"2023-02-23T23:59:41Z","number":5003,"body":"Adding cpu alarms for OpenVPN, Bastion Host and Gradle Cache node. Required for SOC2 stuff. \r\n\r\nI have already deployed these changes. I know we can eliminate some code redundancy here but frankly it's not worth the effort. ","mergeCommitSha":"4736a9d2dfbf3fdd643e6a4a9c237076c9775b95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5003","title":"add cpu monitoring to resources under secops account","createdAt":"2023-02-23T23:53:05Z"}
{"state":"Merged","mergedAt":"2023-02-24T00:11:38Z","number":5004,"body":"Reverts NextChapterSoftware/unblocked#4999 and adds more ","mergeCommitSha":"683c0873744258564bac717d7ef567188b7e9d29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5004","title":"Aggressive CORS logging","createdAt":"2023-02-23T23:53:33Z"}
{"state":"Merged","mergedAt":"2023-02-24T02:36:06Z","number":5005,"mergeCommitSha":"a31edaeddbd602c48a5840cc395715f36a20c411","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5005","title":"Remove jetbrains duplicate eslint and treat react-hooks/exhaustive-deps as errors","createdAt":"2023-02-24T00:14:59Z"}
{"state":"Merged","mergedAt":"2023-02-24T00:50:22Z","number":5006,"mergeCommitSha":"6e2efef92cf27365dba0c9f97ad3787e8bc1d557","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5006","title":"Fix deployment","createdAt":"2023-02-24T00:50:04Z"}
{"state":"Merged","mergedAt":"2023-02-24T01:44:03Z","number":5007,"mergeCommitSha":"0c3d342f2cb8eb615ed1612855b4cab8aed7fabc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5007","title":"Remove state in storage context","createdAt":"2023-02-24T01:00:05Z"}
{"state":"Merged","mergedAt":"2023-02-24T02:21:50Z","number":5008,"mergeCommitSha":"d2a9718c8180adfa2ec509e7b795520a82ba0351","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5008","title":"Fix topic generation","createdAt":"2023-02-24T02:21:19Z"}
{"state":"Merged","mergedAt":"2023-02-24T02:22:42Z","number":5009,"body":"- Fix topic generation\r\n- Fi xtopics\r\n","mergeCommitSha":"f54e02cd86a81332c66aefd87928bfd3d1ffd8bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5009","title":"Forgot a change","createdAt":"2023-02-24T02:22:36Z"}
{"state":"Merged","mergedAt":"2022-03-08T06:14:19Z","number":501,"body":"Implementation will come later. Just want to be clear on the interface. The IPC will plugin into this.","mergeCommitSha":"b96e2711db3a5e811ff3cfd0116d503dcdf4e0f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/501","title":"SourceMark API interface","createdAt":"2022-03-07T23:49:41Z"}
{"state":"Merged","mergedAt":"2023-02-24T02:25:53Z","number":5010,"mergeCommitSha":"726c4d7b0e52e07962be9d78b42d4a560a52c068","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5010","title":"Increase topic generation","createdAt":"2023-02-24T02:25:38Z"}
{"state":"Merged","mergedAt":"2023-02-24T03:35:23Z","number":5011,"mergeCommitSha":"bd0e5771d5e873cb1e7c5485b599702c8a786e8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5011","title":"Add forward proxy support to all services that were missing it","createdAt":"2023-02-24T03:23:56Z"}
{"state":"Merged","mergedAt":"2023-02-24T04:11:43Z","number":5012,"mergeCommitSha":"a67fea667b5374c79eeba3603348542e9d5a3502","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5012","title":"Removes aggressive CORS logging","createdAt":"2023-02-24T04:02:24Z"}
{"state":"Merged","mergedAt":"2023-02-24T07:47:52Z","number":5013,"mergeCommitSha":"d373fa2a227c963987e453333e1cbd53e0501b59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5013","title":"we were using the wrong metrics namespace","createdAt":"2023-02-24T07:29:16Z"}
{"state":"Merged","mergedAt":"2023-02-24T15:47:08Z","number":5015,"mergeCommitSha":"f961a7e8ab7eb41fc27cde7aba040ed6e1c91f94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5015","title":"approved topics improvements","createdAt":"2023-02-24T15:46:11Z"}
{"state":"Merged","mergedAt":"2023-02-24T21:19:30Z","number":5016,"mergeCommitSha":"79e9bac18f5dc8e979a9bdcae78356f5c49b0614","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5016","title":"Add secrets service","createdAt":"2023-02-24T16:57:04Z"}
{"state":"Merged","mergedAt":"2023-02-24T17:32:37Z","number":5017,"mergeCommitSha":"cb0df5f1e4e982b9f8c3c86b4f35b05310af8ae2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5017","title":"Update admin console topics page","createdAt":"2023-02-24T17:24:01Z"}
{"state":"Merged","mergedAt":"2023-02-24T18:29:54Z","number":5018,"mergeCommitSha":"ce1f1d89d98c284fe95a0b65e8d1ed2c14b1348c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5018","title":"Upgrade java","createdAt":"2023-02-24T18:29:46Z"}
{"state":"Merged","mergedAt":"2023-02-24T18:31:59Z","number":5019,"mergeCommitSha":"de9d83fa5c82b8f573d9558b433d1083cec3d1b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5019","title":"Change action name for github","createdAt":"2023-02-24T18:31:48Z"}
{"state":"Merged","mergedAt":"2022-03-09T22:44:21Z","number":502,"body":"![image](https://user-images.githubusercontent.com/********/157146459-efb65c94-8db4-473d-81be-09856af89c91.png)\r\n\r\n![image](https://user-images.githubusercontent.com/********/157145263-f84a2af3-f619-4134-9f4a-95c9732cfd84.png)\r\n","mergeCommitSha":"4a3f2610fd26c76a239ec35cb6a0d2f1a1cb9d57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/502","title":"Fix code block styling and add custom panel icons","createdAt":"2022-03-08T01:05:57Z"}
{"state":"Merged","mergedAt":"2023-02-24T18:53:57Z","number":5020,"mergeCommitSha":"f6038acff98f8b7427fa81e965d04a82fdd0774c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5020","title":"Update clearing logic in SearchContext","createdAt":"2023-02-24T18:46:46Z"}
{"state":"Merged","mergedAt":"2023-03-01T19:30:04Z","number":5021,"body":"Moves much of the IDE sourcemark integration code from `/vscode` into `/shared/ide`.\r\n\r\n* Moved `SourceMarkEngine` and `SourceMarkProvider` -- top level API for the SM engine\r\n* Moved `FileSourceMarkStream` -- stream that returns SMs for a file.  I also refactored this code quite a bit to be more stream-centric, it's cleaner and easier to follow now, and works better with varying input APIs\r\n* Removed `FocusTracker` -- it was duplicated functionality from `FocusStream`.\r\n* Added `IDEWorkspace` -- an abstraction for workspace/project services.  Right now provides editor events, but this could grow quite a bit\r\n* Moved associated tests and mocks\r\n\r\nNext PR will be similar, moving more things","mergeCommitSha":"f23fad69adc77f8508004e66b1eb1ea95f2a2de9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5021","title":"Refactor IDE/sourcemark integration","createdAt":"2023-02-24T19:15:12Z"}
{"state":"Merged","mergedAt":"2023-02-24T19:43:39Z","number":5022,"mergeCommitSha":"aa881323d4348f9f148d853cff622a143c67b9e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5022","title":"Remove topics feature flag","createdAt":"2023-02-24T19:18:01Z"}
{"state":"Merged","mergedAt":"2023-02-24T19:53:18Z","number":5023,"mergeCommitSha":"3ebabd527dcbcf8fd91af0a958506fb3fbae0243","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5023","title":"Fix diff codegen","createdAt":"2023-02-24T19:53:09Z"}
{"state":"Merged","mergedAt":"2023-02-24T20:04:34Z","number":5024,"mergeCommitSha":"babccbcbc6f9eacb383efd26f0aaacbc8dd60e85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5024","title":"Use Java 19 for all macOS builders","createdAt":"2023-02-24T19:53:30Z"}
{"state":"Merged","mergedAt":"2023-02-24T22:06:47Z","number":5025,"body":"Adding service name (e.g apiserivce) as common name to all pod certificates. ","mergeCommitSha":"2c7af63c3e47f5259f19f6918dcff87c7f1cb5ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5025","title":"Adding service name as common name to certificates","createdAt":"2023-02-24T21:54:31Z"}
{"state":"Merged","mergedAt":"2023-02-24T23:40:36Z","number":5026,"mergeCommitSha":"90e58ff9b69a121c209cd992e8f699cf58c04d8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5026","title":"Update ChannelPoller tests to use mock timer","createdAt":"2023-02-24T22:46:35Z"}
{"state":"Merged","mergedAt":"2023-02-25T00:50:18Z","number":5027,"body":"Since we want recommended threads exist before team members onboard, this logic will need to run for every team member and not just those with unblocked accounts. That means this logic could take a little while.","mergeCommitSha":"044901673a6c792064ab86137e93d1d031f505bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5027","title":"backfillRecommendations runs for all team members","createdAt":"2023-02-24T22:58:40Z"}
{"state":"Merged","mergedAt":"2023-03-01T19:09:58Z","number":5028,"mergeCommitSha":"d991bebd2d6c812fb94cc51b6dbe990ab63e75dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5028","title":"Create pusher channel for /person/onboardingStatus","createdAt":"2023-02-25T00:42:37Z"}
{"state":"Merged","mergedAt":"2022-03-08T22:00:45Z","number":503,"body":"Completes _Source Mark Scheduler_ section of plan\r\nhttps://www.notion.so/nextchaptersoftware/SourceMark-Plan-cb82862d6b104880bd2342efafb59441","mergeCommitSha":"140eff499e01144d17817a8e76fb81076bcd851e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/503","title":"Scheduler runs only when Git tree changes, backs off with inactivity, and throttles down eventually","createdAt":"2022-03-08T15:37:28Z"}
{"state":"Merged","mergedAt":"2023-02-27T19:28:34Z","number":5031,"mergeCommitSha":"6fa4b8ea88449faf34ff11a1b1996dc73b749806","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5031","title":"SCM OAuth token refresh APIs","createdAt":"2023-02-27T03:44:09Z"}
{"state":"Merged","mergedAt":"2023-02-27T18:26:40Z","number":5032,"body":"Default was 64, now 1.","mergeCommitSha":"7144d1cd0d5fce5d86cc42f616b16cd0ddc0f709","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5032","title":"Do not downsample Honeycomb auth-service events","createdAt":"2023-02-27T18:20:05Z"}
{"state":"Closed","mergedAt":null,"number":5033,"mergeCommitSha":"b2c6df14e4141ad2744e1f2010a0743e004d74ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5033","title":"Engagement score in admin web","createdAt":"2023-02-27T18:21:22Z"}
{"state":"Merged","mergedAt":"2023-02-27T19:08:50Z","number":5034,"body":"We need to migrate our ALB record to standard region so we could make our DR story simpler. I'll do the CloudFront cutover later tonight.","mergeCommitSha":"750867b14b2eff343903489aeafe231a4402146a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5034","title":"Adding new ALB record under the shared region","createdAt":"2023-02-27T18:57:13Z"}
{"state":"Merged","mergedAt":"2023-02-27T19:22:27Z","number":5035,"body":"React StrictMode  will cause useEffect hooks to call twice. https://beta.reactjs.org/reference/react/StrictMode#fixing-bugs-found-by-double-rendering-in-development\r\nThis is problematic for useEffects that make post requests twice within useEffects. \r\n\r\nThis *only* occurs in local development. Production builds (aka deployed dev & prod) will not have these issues.\r\nAdded a cache to dedupe exchange requests for local dev. ","mergeCommitSha":"d2bb093eb4968dfdbb525adb21f5910d13b08703","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5035","title":"Reduce multiple exchange requests","createdAt":"2023-02-27T19:04:57Z"}
{"state":"Merged","mergedAt":"2023-02-27T19:30:38Z","number":5036,"mergeCommitSha":"b15bec317576d058f18248a98f084f2d652de1ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5036","title":"Fix bug that resulted in file editor not splitting correctly","createdAt":"2023-02-27T19:11:35Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:12:06Z","number":5039,"mergeCommitSha":"b3813e6ff16b88eadadd73015157225f19edb78f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5039","title":"Video Panel Action for Hub IPC","createdAt":"2023-02-27T19:21:59Z"}
{"state":"Merged","mergedAt":"2022-03-08T21:00:21Z","number":504,"mergeCommitSha":"3419a2de1aa882c723107e24d6cdcf5d79118ff8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/504","title":"Jeff/vscode deployment","createdAt":"2022-03-08T19:35:03Z"}
{"state":"Merged","mergedAt":"2023-02-27T20:41:21Z","number":5042,"body":"We were incorrectly limiting the metrics that are used for activity in admin web.","mergeCommitSha":"d4590852d32103bb1702f9e265fe99e243041fea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5042","title":"Engagement metrics are greatly under-reporting","createdAt":"2023-02-27T20:39:09Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:15:54Z","number":5043,"mergeCommitSha":"01b5f764f7050988ece2624b8803ea7e99b4ebb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5043","title":"Search page can find members who do not have accounts","createdAt":"2023-02-27T21:06:25Z"}
{"state":"Closed","mergedAt":null,"number":5044,"mergeCommitSha":"bcac7296eb1a14e32fa3e9f8dd783a1a7b6d5a11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5044","title":"Basic tunnel service","createdAt":"2023-02-27T21:39:27Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:12:18Z","number":5045,"mergeCommitSha":"37c612fa6022603a0aa4e2a55f950458bf4a0ca2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5045","title":"Unsubscribe from pusher for removed/deleted teams","createdAt":"2023-02-27T21:47:50Z"}
{"state":"Merged","mergedAt":"2023-02-27T22:37:12Z","number":5046,"body":"As per Dennis' request.","mergeCommitSha":"d5a07e26acc51ea2eda97c47df0839bf64bf16d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5046","title":"Change \"Create Topic\" to \"Add Topic\"","createdAt":"2023-02-27T22:10:07Z"}
{"state":"Closed","mergedAt":null,"number":5047,"mergeCommitSha":"8e0898dda5cc9d12b24c178a84a01840b603e75d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5047","title":"Rename label","createdAt":"2023-02-27T22:35:13Z"}
{"state":"Closed","mergedAt":null,"number":5048,"mergeCommitSha":"325473d5203a6a6b1785c76b87a28f754e342d7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5048","title":"Replace PersonModel.hasCreatedNote with  PersonModel.hasCreatedThread","createdAt":"2023-02-27T23:06:38Z"}
{"state":"Merged","mergedAt":"2023-03-01T22:59:19Z","number":5049,"body":"Naming up in the air","mergeCommitSha":"25b8fc70ff5e16d2a19da8d925666c7a17b0005d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5049","title":"[Onboarding] Add agentType header and dismiss property","createdAt":"2023-02-27T23:34:35Z"}
{"state":"Merged","mergedAt":"2022-03-09T18:26:26Z","number":505,"body":"Note:\r\n- src not encrypted, encoded as JSON in db\r\n- not required to upload snippets yet to avoid disruption\r\n\r\nNext:\r\n- api to get /  set snippets (#508 and #511 )\r\n\r\nhttps://www.notion.so/nextchaptersoftware/SourceMark-Plan-cb82862d6b104880bd2342efafb59441","mergeCommitSha":"af5984e0a7c94b80455a219bdeb4ffdc9e07eca8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/505","title":"Introduce SourceMark snippet DB models","createdAt":"2022-03-08T22:31:49Z"}
{"state":"Merged","mergedAt":"2023-02-28T22:57:51Z","number":5050,"body":"This will move a topic into the `Approved` list instead of creating a copy. This does mean we might recreate a topic if we re-run topic generation but I confirmed with Doug that is OK for now, since this is meant to help him during topic curation.","mergeCommitSha":"4c66d26ba57d9e548f0058376607e19429fe651f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5050","title":"Update the topic source instead of creating a duplicate","createdAt":"2023-02-27T23:57:35Z"}
{"state":"Merged","mergedAt":"2023-03-01T01:29:32Z","number":5051,"body":"NOTE: This is all client logic with mock data, and is manually hidden from the UI. Just wanted to get these first bits in before hooking up to the API and into the current onboarding flow.\r\n\r\n<img width=\"1414\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/221719269-0acb156e-3f94-4770-aa54-99439b9f31e3.png\">\r\n\r\n<img width=\"1428\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/221719373-26946083-091f-446b-b2eb-af45e5aa67da.png\">\r\n\r\n<img width=\"1427\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/221719387-d9991a36-f4e6-4279-934e-573fe2d8d19d.png\">\r\n\r\n<img width=\"1424\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/221719412-be2d012e-05c8-4788-b87b-a17f67bed189.png\">\r\n\r\n<img width=\"1422\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/221719207-4e0ccf0e-1229-4f28-a179-24b5fd1fb55d.png\">\r\n\r\n","mergeCommitSha":"6faf2fe80b26f397adcd3c4f00876b1f5c1c6f53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5051","title":"Add client bits for onboarding toast","createdAt":"2023-02-28T00:21:25Z"}
{"state":"Merged","mergedAt":"2023-02-28T00:43:46Z","number":5052,"mergeCommitSha":"f24949e2804d66667914ee6284824bb754aa43b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5052","title":"add powerml v2","createdAt":"2023-02-28T00:23:17Z"}
{"state":"Merged","mergedAt":"2023-02-28T01:02:05Z","number":5053,"mergeCommitSha":"00385759e8e563f3fe46ae84da7c704a190fb11e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5053","title":"Fix powerml image","createdAt":"2023-02-28T01:01:58Z"}
{"state":"Merged","mergedAt":"2023-02-28T02:19:09Z","number":5054,"mergeCommitSha":"60a3226a48873c383831a3aa6a3c8ad1f9e543c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5054","title":"Fix casing","createdAt":"2023-02-28T02:19:02Z"}
{"state":"Merged","mergedAt":"2023-03-01T00:32:13Z","number":5055,"body":"- Fix casing\r\n- Add chunked\r\n- Update\r\n","mergeCommitSha":"06821e2e77e60a8496a18b917f9c6b2ba146e9fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5055","title":"AddChunked","createdAt":"2023-02-28T02:49:30Z"}
{"state":"Merged","mergedAt":"2023-03-03T05:27:51Z","number":5056,"mergeCommitSha":"282e3f56350d3330853f8351337e81422b083250","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5056","title":"OAuth token refresh service","createdAt":"2023-02-28T04:23:47Z"}
{"state":"Merged","mergedAt":"2023-02-28T06:25:42Z","number":5057,"mergeCommitSha":"e47f534b6e85c20df97a56f59587c4060243c7ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5057","title":"Add more internal users and teams","createdAt":"2023-02-28T05:45:15Z"}
{"state":"Merged","mergedAt":"2023-02-28T06:32:40Z","number":5058,"mergeCommitSha":"6d73d5eaf8e2df22744e9d8b619df2c75440d16c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5058","title":"Better version compare","createdAt":"2023-02-28T06:21:14Z"}
{"state":"Merged","mergedAt":"2023-02-28T07:16:58Z","number":5059,"body":"- Added the DNS stack to our standard region\r\n- Moved alb CNAME DNS records to standard region (makes it possible to deploy during a DR)\r\n- Removed the old CNAME records from Dev and Prod\r\n- Add a conditional to handle missing peering connection (happens during DR setup)","mergeCommitSha":"939b23bf2856f4e4360589a93119ada2c07cbe17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5059","title":"DNS recrod migration and bug fixes","createdAt":"2023-02-28T07:12:21Z"}
{"state":"Merged","mergedAt":"2022-03-09T17:54:00Z","number":506,"body":"Setup polling from data cache stores to only occur when authenticated.\r\n\r\nAlso fixed bug with Sidebar & Auth which was causing unnecessary renders.","mergeCommitSha":"635bbcd4371e912bb22a7408b0bc3e9a57e3e7b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/506","title":"Polling only when authenticated","createdAt":"2022-03-08T23:27:46Z"}
{"state":"Merged","mergedAt":"2023-02-28T18:59:14Z","number":5060,"body":"Enabling bucket versioning on remaining S3 buckets to address a Drata failing test.","mergeCommitSha":"ff01a62791e7c0337817b53414bce59ecc83815e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5060","title":"enable versioning on more buckets","createdAt":"2023-02-28T18:55:28Z"}
{"state":"Merged","mergedAt":"2023-03-03T06:08:04Z","number":5061,"body":"Setup Agent <-> Webview communication with existing WebviewContentController & RenderWebview pattern.\r\n\r\nRefactored majority of WebviewContentController & RenderWebview into shared / ide.\r\n\r\nFor IntelliJ, communication between the two flows through Kotlin with a \"TunnelService\"\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/221978887-8d948492-880b-4511-957d-96c0787727ff.mp4\r\n\r\n","mergeCommitSha":"3bdde9083d99cb5fb7198d6d6fbce2ba0f3ee9f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5061","title":"IntelliJ Agent <-> Webview Communication","createdAt":"2023-02-28T19:29:28Z"}
{"state":"Merged","mergedAt":"2023-02-28T20:26:23Z","number":5062,"body":"The state being stored in the row components caused a rerender of the row on each click, causing the menu to rerender and close. Storing the state in the parent components seems to fix this.\r\n\r\nhttps://user-images.githubusercontent.com/********/221961018-db7a981f-394a-4803-b742-b29db757bc1e.mp4\r\n\r\n","mergeCommitSha":"dd4e9300901f2460393d0fe0edb191a9c8654320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5062","title":"Fix sidebar item context click actions","createdAt":"2023-02-28T19:33:12Z"}
{"state":"Merged","mergedAt":"2023-02-28T21:00:15Z","number":5063,"body":"Fixes flickering in the topic pill list in the VSCode explorer insights panel.\r\n\r\nThe topic stream has a mid-stream async operation, to fetch topics for a file, whenever the sourcemarks for a file update.  While this async operation was running the stream state would be `loading`.\r\n\r\nWith this change, we only use the `loading` state for the initial load.","mergeCommitSha":"c109e3347ebc0579c719eec5946a8eadc403a683","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5063","title":"Fix flickering in topic pills","createdAt":"2023-02-28T19:47:12Z"}
{"state":"Merged","mergedAt":"2023-03-01T01:21:17Z","number":5064,"mergeCommitSha":"b0d9eec609dd9410de3943f7c9f516346acfc588","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5064","title":"Add lock unit tests","createdAt":"2023-02-28T22:35:02Z"}
{"state":"Merged","mergedAt":"2023-03-03T01:38:37Z","number":5066,"body":"Decouple service initiation from Webviews.\r\n\r\n","mergeCommitSha":"2c47059ee7bb37fc08c055a0e9538ce88aa4f021","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5066","title":"Setup Startup Service","createdAt":"2023-03-01T00:26:01Z"}
{"state":"Merged","mergedAt":"2023-03-01T01:42:05Z","number":5067,"mergeCommitSha":"9c6597966c4858704336fb52c804fc72b58aa54d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5067","title":"Fix scripts","createdAt":"2023-03-01T01:41:52Z"}
{"state":"Merged","mergedAt":"2023-03-01T05:25:46Z","number":5068,"body":"This is an accuracy-vs-speed tradeoff. This change aims for speed.\r\n\r\nStill passes the regression test added in #5000.","mergeCommitSha":"8fc6a06417a9b78148c0161b504b11cf759ba2fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5068","title":"Sourcemark engine matches moved code faster","createdAt":"2023-03-01T03:13:35Z"}
{"state":"Merged","mergedAt":"2023-03-01T06:16:48Z","number":5069,"body":"Very basic first attempt at generating an activity timeline. Improve by:\n1. coarse grouping of mulitple events by date\n2. better visuals to make easier to parse. see examples in linear.\n\nhttps://linear.app/unblocked/issue/UNB-1031/visualize-user-engagement-events-in-admin-web","mergeCommitSha":"4f8307ee03fea7615706d43e5ed9b74f4f270e6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5069","title":"Visualize user engagement events in admin web","createdAt":"2023-03-01T06:13:54Z"}
{"state":"Merged","mergedAt":"2022-03-09T04:51:28Z","number":507,"body":"https://www.notion.so/nextchaptersoftware/SourceMark-Plan-cb82862d6b104880bd2342efafb59441#029ccb55b7174ca9a5a1ff710bbadf41","mergeCommitSha":"ef3c61218cbe5212dbc9e3f472ef76ca042eb699","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/507","title":"Implement findSourceMarks and getSourceMarkLatestPoint API endpoints","createdAt":"2022-03-08T23:33:35Z"}
{"state":"Merged","mergedAt":"2023-03-01T19:06:10Z","number":5070,"body":"- Added `SERVICE_REGION` env var to deployments \r\n- Modified GitHub action to supply the region value \r\n- Renamed all environment specific values.yaml files to include region in the file name","mergeCommitSha":"6de96d29cf3c923c0aaf2b72914c57f00c25e1e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5070","title":"Add region env var","createdAt":"2023-03-01T06:22:18Z"}
{"state":"Merged","mergedAt":"2023-03-01T23:21:17Z","number":5071,"body":"Needs https://github.com/NextChapterSoftware/unblocked/pull/5049 to be merged first","mergeCommitSha":"21165e082c5989cc0e1af8875974b5c2df1bac14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5071","title":"Drop unused PersonModel columns","createdAt":"2023-03-01T06:42:26Z"}
{"state":"Merged","mergedAt":"2023-03-01T19:13:22Z","number":5072,"mergeCommitSha":"0c72fd9f95cf50f8d854765cbb21445dfd8bd7c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5072","title":"Use updated hub folder name","createdAt":"2023-03-01T18:44:24Z"}
{"state":"Merged","mergedAt":"2023-03-01T20:54:25Z","number":5073,"body":"Slack thread: https://chapter2global.slack.com/archives/C02T1N1LK19/p1677694176000879\r\n\r\n- Modified deployment spec and default values.yaml  to use \"Recreate\" as the default deployment strategy. This is mainly intended for our backend services.\r\n- Modified values.yaml file for all front-end services to set deployment strategy to 20% rolling update\r\n- Did the same change for Encryption and Secret service. Even though they are internal they still need to be HA\r\n- Added affinity configuration to auth service. It was missing one.\r\n- Updated helm charts with latest changes described above\r\n- Removed deployment concurrency restriction from GH action deployment workflow\r\n- Fixed the region env var. I forgot to supply the value in my last PR","mergeCommitSha":"d2ef3b6f3eb2b9bebc1010d2c62af4e340f6dd16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5073","title":"Change deployment strategy for backend services","createdAt":"2023-03-01T19:37:28Z"}
{"state":"Merged","mergedAt":"2023-03-02T23:18:59Z","number":5074,"body":"Moves most of the data handling for explorer insights into `/shared/ide/sidebar`.\r\n\r\n* New `FileInsightStream` stream, which is the stream of insights for the active file (ie, the top-level stream for the insights panel)\r\n* Add active file streams to `IDEWorkspace`\r\n* Removed `CurrentFilePane` and `RelatedPullRequestsPane` and `DiffStatRenderer` as we don't use them at all.","mergeCommitSha":"b61482bf10277f70c1ee2f2c3133a63179635a49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5074","title":"Refactor explorer insight streams","createdAt":"2023-03-01T19:38:35Z"}
{"state":"Merged","mergedAt":"2023-03-01T20:58:56Z","number":5075,"body":"Fixes UNB-1033\r\n\r\nhttps://linear.app/unblocked/issue/UNB-1033/hub-installation-update-opening-hub-content-list-leads-to-false","mergeCommitSha":"6b012a0296804072f8613f8f31d9fe57e1386fe3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5075","title":"Don't record a ContentView event right after an upgrade","createdAt":"2023-03-01T20:37:04Z"}
{"state":"Merged","mergedAt":"2023-03-01T20:41:14Z","number":5077,"body":"https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/rds/client/start_export_task.html","mergeCommitSha":"78dc6ef168a4eac061a5470e850984dba85dd4b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5077","title":"we were not backing up RDS because of a stupid extra param","createdAt":"2023-03-01T20:38:51Z"}
{"state":"Merged","mergedAt":"2023-03-01T20:50:26Z","number":5078,"mergeCommitSha":"2fade1f316e3ebe048d2a7773a3e9f9959458993","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5078","title":"Fix main","createdAt":"2023-03-01T20:50:17Z"}
{"state":"Merged","mergedAt":"2022-03-09T18:26:54Z","number":508,"body":"(Sorry about the whitespace changes. New IDE settings updated this, but at least now it's consistent with the rest of the file. To review cleanly, click this link https://github.com/NextChapterSoftware/unblocked/pull/508/files?w=1)","mergeCommitSha":"1faf74ae85b3a5e25bb6155e919e0f1409a4bebb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/508","title":"Expose source snippets in API","createdAt":"2022-03-09T05:45:19Z"}
{"state":"Merged","mergedAt":"2023-03-01T22:23:44Z","number":5080,"body":"- Created a bucket under secops account\r\n- Setup a GH workflow to push a copy of our code to it ever night\r\n\r\nTested it and worked like a charm","mergeCommitSha":"4d4416bc51951f0ec4a8d2707f840fefb40f906b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5080","title":"Backup Unblocked code to s3","createdAt":"2023-03-01T21:57:44Z"}
{"state":"Merged","mergedAt":"2023-03-01T23:13:05Z","number":5081,"body":"Fix TS imports forever everywhere always.\r\n\r\n* Set up a lint rule so that we have fixed import grouping and ordering.  We group first by node modules (`react`), then (`@api` and `@models`), then `@otherAliases`, then `@fortawesome`, then `./relativePaths`, then `StyleFiles.scss`.\r\n* This auto-fixes, so Cmd+S in VSCode works automatically.  *No more manual import fixing ever*.\r\n* `npm run fix-lint` will also auto-fix this","mergeCommitSha":"fd5d0f279efd52c6888dcd624e0bfaf22f2ecffd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5081","title":"Fix all TS imports forever","createdAt":"2023-03-01T22:27:55Z"}
{"state":"Merged","mergedAt":"2023-03-01T22:58:52Z","number":5082,"body":"Setting request = limit resource values will tell Kubernetes that this pod requires guaranteed QoS. This should prevent other pods CPU/Mem bursts taking down Webhook service. \r\n\r\nPrior to adding the new Kube host to prod I saw a bunch of these restarts. We should avoid having Webhook service impacted by any other services. ","mergeCommitSha":"a8dc0bacb1b74db9fc2845cec0ed252893d45d04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5082","title":"Guaranteed QoS for Webhook pods","createdAt":"2023-03-01T22:52:35Z"}
{"state":"Merged","mergedAt":"2023-03-01T23:32:41Z","number":5084,"body":"Moving forward we will tread admin console as a user facing service.","mergeCommitSha":"a8b328aea2d0f4354147b26753783d29deb5c509","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5084","title":"Change admin console deployment strategy to rolling update","createdAt":"2023-03-01T23:26:36Z"}
{"state":"Merged","mergedAt":"2023-04-12T18:21:55Z","number":5085,"body":"Add support for multiple providers.\r\n\r\nCurrently does *not* support shortcut to \"onboarded\" enterprise providers. Will require Hub to keep a \"cookie\".\r\n\r\n<img width=\"527\" alt=\"CleanShot 2023-03-01 at 15 37 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/222291555-0f8dd2b3-e01d-4257-a6e9-db4c36385843.png\">\r\n<img width=\"552\" alt=\"CleanShot 2023-03-01 at 15 37 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/222291562-d7ed9b59-4ee2-487b-b398-0c837eb53e6a.png\">\r\n<img width=\"505\" alt=\"CleanShot 2023-03-01 at 15 37 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/222291571-5de67b0b-aec7-4e51-bec5-3ff1d2994393.png\">\r\n","mergeCommitSha":"66c44d706f1271664f26e9beb0c70b27aa169ebd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5085","title":"[UNB-1151] Hub login buttons","createdAt":"2023-03-01T23:35:39Z"}
{"state":"Merged","mergedAt":"2023-03-02T17:27:11Z","number":5086,"mergeCommitSha":"5ad89531e11a7d0becb51b561f9e060b51dd6c29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5086","title":"Fix powerml code","createdAt":"2023-03-02T00:01:01Z"}
{"state":"Merged","mergedAt":"2023-03-02T00:44:48Z","number":5087,"mergeCommitSha":"d9e9bdffc49340b75b2efb4ff54312514670347b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5087","title":"Fix PersonModel api bug","createdAt":"2023-03-02T00:42:05Z"}
{"state":"Merged","mergedAt":"2023-03-02T02:07:23Z","number":5088,"body":"Changing the deployment strategy to the good old rollover but with a twist. We sett the max surge and max unavailable to 100% to achieve the same effect of a Recreate strategy.","mergeCommitSha":"7e5ef93c037c816e267f1c879e171e5b14645c28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5088","title":"changing deployment strategy so rollbacks show as job failures","createdAt":"2023-03-02T00:48:28Z"}
{"state":"Closed","mergedAt":null,"number":5089,"body":"For now let's keep setting this field, we can remove if we no longer need this field.","mergeCommitSha":"eb2ae7d06d5c7a220595ca1bd5358dd3d07810d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5089","title":"Add back setting hasSeenTutorial","createdAt":"2023-03-02T01:07:55Z"}
{"state":"Merged","mergedAt":"2022-03-09T18:27:16Z","number":509,"body":"We keep regressing, so enforce that all of the `SHOULD` violations are fixed.","mergeCommitSha":"66aa360abde9fc0c08b53f458545c72a89829a81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/509","title":"Fix Zally lint and treat warnings as errors","createdAt":"2022-03-09T06:39:25Z"}
{"state":"Merged","mergedAt":"2023-03-02T01:40:30Z","number":5090,"body":"A person has seen a tutorial if they have seen the tutorial in VSCode or IntelliJ.\n\nDeprecate `updatePersonTutorial`, because this is fully covered by `updateOnboardingStatus`.","mergeCommitSha":"bc5e8ccd43cf8db3af4c6c5cc4af4abc2d3d66ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5090","title":"Redefine has seen tutorial on person","createdAt":"2023-03-02T01:12:08Z"}
{"state":"Merged","mergedAt":"2023-03-03T05:33:43Z","number":5091,"mergeCommitSha":"e5740c0c78fd32c6db48adb9aaf6ba0f57bd2281","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5091","title":"Use OAuth refresh flow","createdAt":"2023-03-02T01:54:04Z"}
{"state":"Merged","mergedAt":"2023-03-02T22:21:04Z","number":5092,"body":"Follow up to https://github.com/NextChapterSoftware/unblocked/pull/5036\r\n\r\nThe bug described in #5036 only happens when there are no visible text editors. i.e. when there's no currently active text editor to open a `Beside` column to. So treat these differently depending on whether there are visible text editors.\r\n\r\nAlternatively, if we wanted to truly make the ordering of the editors consistent, we could, in the empty editor case, open an empty tab and backfill the discussion to Beside and the code to Active. Since I think the case where users open discussions with zero active text editors open is rare, this ie likely not necessary for now.","mergeCommitSha":"7b96da2f288d26adcb5b5764d9f40ac66df59d71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5092","title":"Fix text editor ordering","createdAt":"2023-03-02T01:55:29Z"}
{"state":"Merged","mergedAt":"2023-03-02T17:14:27Z","number":5093,"mergeCommitSha":"6efb44c0fb4d6d27bd04a7213bb83a4629c1cafd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5093","title":"Add Person.hasSeenTutorial property with tests","createdAt":"2023-03-02T02:00:58Z"}
{"state":"Merged","mergedAt":"2023-03-07T04:04:44Z","number":5094,"body":"Client usage:\r\n```kotlin\r\n        val hello = helloRequest {\r\n            auth = authBundle {\r\n                teamId = team.toString()\r\n                identity = user.toString()\r\n                jwt = token\r\n            }\r\n            message = \"hi\"\r\n        }\r\n```\r\nMethod authorizer now introspects the received message for the auth bundle and validates its fields against the jwt.\r\n\r\nProjects that make use of lib-service-grpc will need to include the proto definition for `auth.proto` to import the `AuthBundle` type.","mergeCommitSha":"ce587ffc7ff4aab7880691df122c59051ce1a3da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5094","title":"Implements Authorization Interceptor for GRPC service to service auth","createdAt":"2023-03-02T06:33:33Z"}
{"state":"Merged","mergedAt":"2023-03-02T06:34:27Z","number":5095,"mergeCommitSha":"33ce1f0328fe9f3727d11265902d07dcb88be33c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5095","title":"Add grpc authorization primitives and service wrappers","createdAt":"2023-03-02T06:33:36Z"}
{"state":"Merged","mergedAt":"2023-03-02T07:58:45Z","number":5096,"body":"Don't care version adoption for clients of users that have churned out.","mergeCommitSha":"6ef46c957a46c3c7d9dde243b8c2c5d3bcabc115","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5096","title":"Only consider recent client version adoption","createdAt":"2023-03-02T07:31:46Z"}
{"state":"Merged","mergedAt":"2023-03-02T18:33:15Z","number":5097,"body":"We were sending walkthrough notifications to *all* contributors... Not just selected.","mergeCommitSha":"17459315ccf7cb0b85a5584173226b24daa746e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5097","title":"Fix selected git contributors for video walkthrough","createdAt":"2023-03-02T18:27:06Z"}
{"state":"Merged","mergedAt":"2023-03-02T20:02:27Z","number":5098,"body":"Creating a walkthrough isn't specific to either VSCode or IntelliJ so we should set both flags here whenever a walkthrough is created for the first time.","mergeCommitSha":"710b6593eae601c007eb1a985e7c9300b11e53e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5098","title":"Set both hasCreatedWalkthrough flags","createdAt":"2023-03-02T18:49:48Z"}
{"state":"Merged","mergedAt":"2023-03-02T22:41:39Z","number":5099,"body":"When ValueStream's type is optional the initial value behaviour was a bit ambiguous:\r\n* If an initial value is set to a defined value, all subscribers would receive that value\r\n* If an initial value is undefined, new subscribers would not receive anything\r\n* If `updateValue(undefined)` was called, existing subscribers would receive a stream update with value of `undefined`, but *new* subscribers afterwards would still receive nothing.\r\n\r\nThis fixes the inconsistency.  Add tests.  Also, remove wrapping the current value in an object, and replace the getter with a function instead.","mergeCommitSha":"1a92155f457dcde131aee1f31e7ff2b7c43191d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5099","title":"Make ValueStream initial value behaviour clear","createdAt":"2023-03-02T18:53:22Z"}
{"state":"Merged","mergedAt":"2022-01-18T06:15:34Z","number":51,"mergeCommitSha":"296786bea8260a20f7d800e94d3c4aaa41031df5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/51","title":"Use Exposed ORM and add initial models","createdAt":"2022-01-18T03:41:33Z"}
{"state":"Merged","mergedAt":"2022-03-17T12:45:47Z","number":510,"body":"## Summary\r\n\r\nAdd a high level, this PR adds the business logic for Video Channel management. \r\n\r\n## Next steps\r\n- Agora auth\r\n- Background job that cleans abandoned channels\r\n- API delegate implementation and API <-> DB model transformers\r\n- Redis channel heartbeats\r\n\r\nSee comments for more details. ","mergeCommitSha":"ee012b5bf1da0d0bba6139c6cc576f1fef6ff0c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/510","title":"Video Channel Service","createdAt":"2022-03-09T07:13:45Z"}
{"state":"Merged","mergedAt":"2023-03-02T19:10:43Z","number":5100,"mergeCommitSha":"17caef5694aee35274b520e20f479200e214f8fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5100","title":"Improve powerml utilities","createdAt":"2023-03-02T19:08:07Z"}
{"state":"Merged","mergedAt":"2023-03-02T19:48:02Z","number":5101,"body":"\uD83E\uDD17 ","mergeCommitSha":"bd957d4dba2c7a5426dc4582083c34980874de66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5101","title":"Add PersonsApiDelegateImpl integration tests","createdAt":"2023-03-02T19:10:11Z"}
{"state":"Closed","mergedAt":null,"number":5102,"mergeCommitSha":"9ff993b3398ded20cd5fc34baaee7aaae4f4c91a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5102","title":"nFix index accessor","createdAt":"2023-03-02T19:19:14Z"}
{"state":"Merged","mergedAt":"2023-03-02T19:34:23Z","number":5103,"mergeCommitSha":"5a128fcce81c677a3b674d0388b03a28536cc421","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5103","title":"Fix first message lookups","createdAt":"2023-03-02T19:25:28Z"}
{"state":"Merged","mergedAt":"2023-03-02T20:26:59Z","number":5104,"mergeCommitSha":"3c30ba08aa50f25fc345ec3cbedc56f0dc9d5f52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5104","title":"Remove identityName from logs","createdAt":"2023-03-02T20:20:03Z"}
{"state":"Merged","mergedAt":"2023-03-02T21:01:19Z","number":5105,"body":"- Created service accounts for cluster auto-scaler\r\n- Deployed service accounts to Dev and Prod\r\n- Deployed autoscaler to both Dev and prod. They are working as expected.\r\n\r\nI'll keep monitoring them to make sure we don't over-provision","mergeCommitSha":"e3b6ebf78640496b82265a0bfb9a4cf5a8561dd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5105","title":"Add Cluster Auto-Scaler to our Kubernetes infra","createdAt":"2023-03-02T20:57:39Z"}
{"state":"Merged","mergedAt":"2023-03-06T23:41:49Z","number":5106,"body":"Proposed Installations V2 Spec\r\n\r\n1. Text is now provided by backend\r\n2. Install URL is optional. Not necessary for BB or GL\r\n3. Introduce new API to confirm BB & GL installations\r\n\r\n\r\nSee also\r\nhttps://chapter2global.slack.com/archives/C04FC045M1A/p1677793681678139?thread_ts=**********.949709&cid=C04FC045M1A","mergeCommitSha":"cca57e2d838fa01c0bac0cbba706206bffb00052","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5106","title":"Installations V2 Spec","createdAt":"2023-03-02T21:46:51Z"}
{"state":"Merged","mergedAt":"2023-03-06T23:21:40Z","number":5107,"body":"Note that we've run a migration so that all existing users will have `hasDismissedToast` set to true, so no existing users should see this UI, only newly onboarded users. \r\n\r\nIf you'd like to test this (i.e. on yourself), there is a `Reset onboarding states` button on the person page in the admin console. If you do this, make sure to toggle on the `Unblocked: Tutorial Wizard` so that the hub knows that you've properly onboarded.","mergeCommitSha":"d18069241d7ab3fc214de82e8df438530d7038b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5107","title":"Integrate APIs into new onboarding command","createdAt":"2023-03-02T22:20:14Z"}
{"state":"Merged","mergedAt":"2023-03-03T00:56:53Z","number":5108,"body":"I've run this and dropped the columns in both dev and prod.","mergeCommitSha":"2c36a6b7f06fd72573c1293098a1a5ac7a8118e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5108","title":"Remove no longer needed code","createdAt":"2023-03-02T22:36:42Z"}
{"state":"Merged","mergedAt":"2023-03-02T23:02:26Z","number":5109,"mergeCommitSha":"56b210f217e3961bc99d2bde849fc281d4c34b95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5109","title":"Add service region config","createdAt":"2023-03-02T22:38:59Z"}
{"state":"Merged","mergedAt":"2022-03-09T21:04:19Z","number":511,"mergeCommitSha":"dc2214df8d01cf8f20cb8f6e5a762cf0ad613c05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/511","title":"Wire up snippets","createdAt":"2022-03-09T07:47:14Z"}
{"state":"Merged","mergedAt":"2023-03-06T17:47:45Z","number":5110,"mergeCommitSha":"1199fee5db0af8dcd13313afe3400f824eed7479","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5110","title":"Fix disabled button after editing","createdAt":"2023-03-02T23:37:52Z"}
{"state":"Merged","mergedAt":"2023-03-03T00:47:24Z","number":5111,"body":"When a new team is onboarded, we trigger this for each repo during bulk ingestion. Ideally we only want to do this once after all pull requests have been ingested. \r\n\r\nLet's disable this for now while I come up with a better approach.","mergeCommitSha":"d93a6a9ca9ef8ac1cff5f7ed8f9bfa1db03515ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5111","title":"Disable auto triggering of topic generation during bulk ingestion","createdAt":"2023-03-03T00:32:32Z"}
{"state":"Merged","mergedAt":"2023-03-03T06:48:52Z","number":5113,"mergeCommitSha":"1ebb4930414b361056c41ce7b20dd642043292c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5113","title":"Introduce local stack GitHub Cloud App","createdAt":"2023-03-03T05:20:26Z"}
{"state":"Merged","mergedAt":"2023-03-03T07:12:04Z","number":5114,"mergeCommitSha":"6d71c9c9b677f1e9fb3899759442afa2dc131736","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5114","title":"Deal with relative future times","createdAt":"2023-03-03T07:01:21Z"}
{"state":"Merged","mergedAt":"2023-03-03T17:59:08Z","number":5115,"mergeCommitSha":"b0338295bb68c88c0766a2bd7e0d23c6f2f72efb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5115","title":"Add pusher region","createdAt":"2023-03-03T17:58:48Z"}
{"state":"Merged","mergedAt":"2023-03-03T20:56:23Z","number":5116,"mergeCommitSha":"9ef6a87493b575c9289bdd0d5225df7e85a567aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5116","title":"getting sick of crazy amount of service initialization code","createdAt":"2023-03-03T19:04:59Z"}
{"state":"Merged","mergedAt":"2023-03-06T06:19:32Z","number":5117,"mergeCommitSha":"1dd23be55caf9531a15394d896db2b51f672b588","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5117","title":"Debug logs for token refresh","createdAt":"2023-03-03T19:30:52Z"}
{"state":"Merged","mergedAt":"2023-03-05T17:54:08Z","number":5118,"body":"Moves AuthSidebar into shared/ide\r\n\r\nMajority of this PR is moving code around.","mergeCommitSha":"ed7e38765abfdbb4f0be7684ed992e14ee7cd541","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5118","title":"Move AuthSidebar to Shared","createdAt":"2023-03-03T19:59:42Z"}
{"state":"Merged","mergedAt":"2023-03-06T06:31:28Z","number":5119,"mergeCommitSha":"0a1ddef22112cd56d122936b0c821a7f30f8de24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5119","title":"SCM no-auth APIs for Bitbucket and GitLab to support installation flow","createdAt":"2023-03-03T22:23:18Z"}
{"state":"Merged","mergedAt":"2022-03-09T18:32:10Z","number":512,"body":"next:\r\n- (Dave) make API client\r\n- (Dave) plugin the API -- unlocks the ability for the SourceMark application to run against DEV / PROD\r\n- (Dave) use modifiedSince -- more efficient\r\n- (Richie) cache the content in memory (on the `task`)\r\n","mergeCommitSha":"9b3a55ce4271100bee359605bbffc3a141bfb74b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/512","title":"Split out SourceMarkProvider","createdAt":"2022-03-09T18:22:28Z"}
{"state":"Closed","mergedAt":null,"number":5120,"body":"Adds a new job to the topic service that finds triggers topic generation for teams where the following are true:\r\n\r\n- created in the last 7 days\r\n- have completed* pull request ingestion for all repos\r\n- have not previously had topic generation triggered\r\n\r\n*The ingestion flags may be set to true but we might still be processing ingestion events off the queue. However, for the purposes of topic ingestion this is good enough since we should have enough data for topic generation.","mergeCommitSha":"d5ca34aa55370b28737499674737220fad8f49b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5120","title":"Trigger topic generation when pull request ingestion is complete for newly onboarded teams","createdAt":"2023-03-03T23:20:04Z"}
{"state":"Merged","mergedAt":"2023-03-04T00:51:40Z","number":5121,"mergeCommitSha":"2959a69202531dff7f19d64396372795f982e21e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5121","title":"Fix Jetbrains token Provider","createdAt":"2023-03-03T23:59:58Z"}
{"state":"Closed","mergedAt":null,"number":5122,"body":"* Rm unnecessary person id from OnboardingStatus models\r\n* Add `hasDismissedToast` to new models","mergeCommitSha":"c087b4dc969faeb5e2071d6c889ffcc206fa39be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5122","title":"Rev OnboardingStatus models","createdAt":"2023-03-04T00:59:09Z"}
{"state":"Merged","mergedAt":"2023-03-06T06:32:33Z","number":5123,"body":"Encountered this when dealing with Bitbucket HTTP clone urls:\n```\nhttps://<EMAIL>/getunblocked/sample.git\n```","mergeCommitSha":"d22be58903805bd252f6ddeb15f92027f96c8e62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5123","title":"Repo URL parsing must take into account special characters in the user-info part","createdAt":"2023-03-04T01:22:59Z"}
{"state":"Merged","mergedAt":"2023-03-06T06:33:42Z","number":5124,"mergeCommitSha":"03736069e74608769bda4484c24eb07e44e00bfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5124","title":"SCMs like GitLab and Bitbucket don't have install urls","createdAt":"2023-03-04T01:28:22Z"}
{"state":"Merged","mergedAt":"2023-03-05T21:51:12Z","number":5125,"body":"Dev outage was caused by changes to security policy in Kubernetes. We run our services in unprivileged pods. That means ports lower than or equal 1024 are not allowed for a service. All of our services run on 443.\r\n\r\nThis change introduces a listener port which is used to bring up the service while keeping the old service.port parameter as is. This is because the service.port configuration param is used in many places to construct urls referring to our system and the outside world would have to access us via port 443 (Loadbalancer and CloudFront). ","mergeCommitSha":"6cf6273e20adfffa4dfcfe8968eb5e169ffecdad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5125","title":"change listener port of services","createdAt":"2023-03-05T19:06:29Z"}
{"state":"Merged","mergedAt":"2023-03-06T17:05:22Z","number":5126,"body":"https://stackoverflow.com/questions/49981601/difference-between-targetport-and-port-in-kubernetes-service-definition\r\n\r\nAlso, moving away from defaults for config.","mergeCommitSha":"b4d8abfb6676f75a73f8092c90a871d468a2dcad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5126","title":"Move to kube naming conventions for ports","createdAt":"2023-03-05T22:08:49Z"}
{"state":"Merged","mergedAt":"2023-03-05T22:53:16Z","number":5127,"body":"- Fixed the egress traffic. It was being blackholed for some reason\r\n- I also changed the deployment percentages because right now helm doesn't wait for backend service rollovers. It always shows a success. This is to test if changing it to anything other that 100% would force it to wait.","mergeCommitSha":"a9dff16f00e008f469fd20d18a3870e0aa1d3b61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5127","title":"Fix service egress","createdAt":"2023-03-05T22:47:39Z"}
{"state":"Merged","mergedAt":"2023-03-06T04:54:48Z","number":5128,"body":"Documentation reference: https://kubernetes-sigs.github.io/aws-load-balancer-controller/v2.1/guide/ingress/annotations/#traffic-routing\r\n\r\n\r\nSince we are now using a cluster with networking being done fully via an overlay, we need to change how our load balancers target services.\r\n\r\nManually tested in Dev and works ","mergeCommitSha":"f1774016839907b4f70b953e144ccd7500845b89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5128","title":"fix for broken loadbalancer config in Dev","createdAt":"2023-03-06T04:34:59Z"}
{"state":"Merged","mergedAt":"2023-03-07T21:52:53Z","number":5129,"body":"Setup AuthSidebar in IntelliJ with full auth flow.\r\n\r\nCurrently *not* possible to logout. Will add that shortly :)\r\n\r\n<img width=\"1034\" alt=\"CleanShot 2023-03-07 at 10 44 49@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-77146e0a-1ca6-4369-8646-bab2f5c4589d.png\">\r\n\r\n","mergeCommitSha":"ccb8bfdb44b7571e211e86c474d5a0bf51a55c0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5129","title":"Auth Sidebar coordinator","createdAt":"2023-03-06T06:50:57Z"}
{"state":"Merged","mergedAt":"2022-03-09T18:37:16Z","number":513,"body":"Current `getPerson` API returns an array of `Person` when it should only return a single `Person`","mergeCommitSha":"a05014174af61ee1de29ae00abe1aa73663d5561","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/513","title":"Update Persons API","createdAt":"2022-03-09T18:24:12Z"}
{"state":"Merged","mergedAt":"2023-03-06T18:09:31Z","number":5130,"body":"Service template needs to move to bootstrapper","mergeCommitSha":"1aa6878603d1c781252bfbc6f60e315f6ae624e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5130","title":"Fix up service template","createdAt":"2023-03-06T18:08:39Z"}
{"state":"Merged","mergedAt":"2023-03-06T18:42:09Z","number":5131,"body":"- Added the overlay CIDR ranges\r\n- Updated installation instructions for Calico CNI ","mergeCommitSha":"abc58e5203bbd688b911b80ce982576a6a449ba1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5131","title":"Update EKS readme with new Calico installation instructions","createdAt":"2023-03-06T18:40:28Z"}
{"state":"Merged","mergedAt":"2023-03-06T19:18:05Z","number":5132,"mergeCommitSha":"b4e4d1efcb30d6b5d44578b70a89f459760e718a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5132","title":"UNB-1024 Manually align radio center","createdAt":"2023-03-06T19:10:33Z"}
{"state":"Merged","mergedAt":"2023-03-07T00:36:01Z","number":5133,"body":"* Pipe workspace repo and selected file state through to the agent\r\n* Add a heartbeat monitor so agent dies when parent shuts down\r\n* Write \"current file\" insights out to stdout so we can verify it works","mergeCommitSha":"3aa8ae451f68951d99d01b78196bcfbeeab1d4cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5133","title":"Set up explorer insight stream in Jetbrains","createdAt":"2023-03-06T19:48:10Z"}
{"state":"Merged","mergedAt":"2023-03-06T21:45:25Z","number":5134,"body":"Just from the topic page though. Needs another PR to allow editing the topic name from the topics overview page.","mergeCommitSha":"4bba3a5fd8d97fe733bcb28b5a7b3c0c9b6ebb12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5134","title":"Add ability to rename the topic from the topic page","createdAt":"2023-03-06T19:59:59Z"}
{"state":"Merged","mergedAt":"2023-03-10T19:22:19Z","number":5135,"body":"Updated VSCode Installations to use the new API. \r\n\r\nMajority of text is provided by API.\r\n\r\n<img width=\"775\" alt=\"CleanShot 2023-03-06 at 11 38 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/223224963-3326cbd3-574d-45f9-8bc1-eb588d1f4b83.png\">\r\n<img width=\"670\" alt=\"CleanShot 2023-03-06 at 11 47 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/223224968-873d4cae-0ae8-47f5-b177-12654aeb0bed.png\">\r\n<img width=\"724\" alt=\"CleanShot 2023-03-06 at 11 53 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/223224977-8aff4898-2413-4ff7-a36b-593868b72836.png\">\r\n","mergeCommitSha":"edc798f591395f96e121d4d3eeb9699b0ff499e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5135","title":"Installations client Implementation","createdAt":"2023-03-06T20:36:48Z"}
{"state":"Merged","mergedAt":"2023-03-06T20:58:57Z","number":5136,"mergeCommitSha":"33f80f7e1ac95ce6a9111c1bb3d9858ce57d73c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5136","title":"move to 0.0.11 llama","createdAt":"2023-03-06T20:57:15Z"}
{"state":"Merged","mergedAt":"2023-03-08T23:56:44Z","number":5137,"body":"For the new sidebar updates:\r\n\r\n<img width=\"308\" alt=\"image (1)\" src=\"https://user-images.githubusercontent.com/1924615/223275161-b92c1d6d-abfa-4f04-a9b8-9c809fd4c67d.png\">\r\n\r\nOne option is to use the existing `getTopics` operation to populate the `Trending Topics` section. To do that, we can add a `TopicType` enum and ask the client to pass this when it wants a list of trending topics.\r\n\r\nThe logic to decide what topics are trending is still TBD but that shouldn't matter to clients. They should just be able to call this operation and get back a list of `N` topics that are trending.\r\n\r\nOpen to other suggestions if this is not the ideal approach.","mergeCommitSha":"4ca2850d9b619c2f5ca1fe61e87aa43a1c25773f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5137","title":"Add ability to get trending topics with the getTopics operation","createdAt":"2023-03-06T22:58:16Z"}
{"state":"Merged","mergedAt":"2023-03-06T23:27:05Z","number":5138,"body":"When you have VSCode open on the root folder, run linting and prettier by default on save.","mergeCommitSha":"d9076f147f044b040ad837ba9e0d20795162b7ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5138","title":"Root VSCode settings","createdAt":"2023-03-06T23:25:31Z"}
{"state":"Merged","mergedAt":"2023-03-07T04:31:21Z","number":5139,"mergeCommitSha":"857a111a96eee0a2ddee8c589d232f1ba3edda55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5139","title":"Add GRPC bindings for ktor","createdAt":"2023-03-07T00:10:18Z"}
{"state":"Merged","mergedAt":"2022-03-11T20:31:24Z","number":514,"body":"![image](https://user-images.githubusercontent.com/********/157509352-78438196-78f8-4852-81bd-93406d9a9dc3.png)\r\n\r\nIcons need to be replaced, will update once https://github.com/NextChapterSoftware/unblocked/pull/502 goes in (imports all the custom type icons)","mergeCommitSha":"e8905ae98beac50d1e183f461ba94a9965c67f82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/514","title":"Fix sidebar styling to match mocks","createdAt":"2022-03-09T18:42:47Z"}
{"state":"Merged","mergedAt":"2023-03-07T01:34:17Z","number":5140,"body":"There were a few problems:\r\n1. We were not correctly outputting content to outputDirectory\r\n2. We were not correctly specifying inputs for generation tasks.\r\n","mergeCommitSha":"e134ce1b461b4c9fc9db3d27f34731acd74308c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5140","title":"Fix incremental builds for new components directory for api generation","createdAt":"2023-03-07T01:02:05Z"}
{"state":"Merged","mergedAt":"2023-03-07T18:28:59Z","number":5141,"mergeCommitSha":"f4a90befb26a41907dce2e643942d322a4fed183","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5141","title":"Fix margin spacing and remove duped code","createdAt":"2023-03-07T01:20:11Z"}
{"state":"Closed","mergedAt":null,"number":5142,"mergeCommitSha":"5fa3c34bb647b622ad89eb2e3781abc97c64eaf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5142","title":"Remove close other editors fn","createdAt":"2023-03-07T01:31:06Z"}
{"state":"Merged","mergedAt":"2023-03-07T01:43:14Z","number":5143,"mergeCommitSha":"43d83038b85b09824384a7414e2729fd2484ad53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5143","title":"Update makefile","createdAt":"2023-03-07T01:42:48Z"}
{"state":"Merged","mergedAt":"2023-03-07T16:58:37Z","number":5144,"body":"Ignore everything except GIF.swift. The rest is SwiftFormat changes after the update","mergeCommitSha":"4832e78d56cd469f09125106ea05c69f5f42b393","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5144","title":"Add more ViewController lifecycle methods to stop GIF animations","createdAt":"2023-03-07T04:57:54Z"}
{"state":"Merged","mergedAt":"2023-03-07T07:05:00Z","number":5145,"body":"- Bitbucket workspaces should be deserialized as BitbucketOrg\r\n- GitLab groups should be deserialized as GitLabOrg\r\n- Fix user-agent\r\n- Fix development assertion","mergeCommitSha":"17325bc55a0d9e7fb8e6a46f20eed5f508ab51e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5145","title":"Various fixes for Bitbucket and GitLab install flow from VSCode","createdAt":"2023-03-07T06:23:54Z"}
{"state":"Merged","mergedAt":"2023-03-07T06:37:37Z","number":5146,"mergeCommitSha":"e012bcf3574d7e18299a32b46decbbcb3f01e389","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5146","title":"Revert some CI script changes that break deployment","createdAt":"2023-03-07T06:26:27Z"}
{"state":"Merged","mergedAt":"2023-03-07T07:24:24Z","number":5147,"body":"- Only need recursive clone for build step of IDE workflows for assets,\n  and build step of services workflow for GraphQL.\n- Only need PAT when cloning a private submodule,\n  or when make write changes to the repo.","mergeCommitSha":"499331b0319ef8aaac4049a0e344d3f736961895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5147","title":"Remove submodule clone and PAT where possible","createdAt":"2023-03-07T07:15:47Z"}
{"state":"Closed","mergedAt":null,"number":5148,"mergeCommitSha":"4729003fdc4f89828754d90ffe338a4fee040d20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5148","title":"Fix VSCode installer build in CI","createdAt":"2023-03-07T15:50:41Z"}
{"state":"Merged","mergedAt":"2023-03-07T18:33:07Z","number":5149,"body":"- Clean up emoji parser\r\n- Rename\r\n","mergeCommitSha":"2b53149e49a76c51c2b6285fab572c8b25f87701","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5149","title":"CleanUpEmojiParser","createdAt":"2023-03-07T16:01:01Z"}
{"state":"Merged","mergedAt":"2022-03-09T19:43:43Z","number":515,"mergeCommitSha":"1695b474c8d23bd304f0efb892ae8f061b4ebcc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/515","title":"Increase DEV log level to DEBUG","createdAt":"2022-03-09T19:43:34Z"}
{"state":"Merged","mergedAt":"2023-03-07T19:18:51Z","number":5150,"mergeCommitSha":"63a58bbea6acb6cc23b408dadd8a2c83813b61d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5150","title":"Fix Installer CI build... maybe","createdAt":"2023-03-07T18:01:51Z"}
{"state":"Merged","mergedAt":"2023-03-08T20:54:34Z","number":5151,"body":"Updated styling of buttons for VSCode Login Buttons.\r\n\r\nOrder of LoginProviders is also now sorted.\r\n\r\n<img width=\"397\" alt=\"CleanShot 2023-03-07 at 11 02 34@2x\" src=\"https://user-images.githubusercontent.com/1553313/223524605-52948381-0d9c-4d1d-84ec-55c7570cea55.png\">\r\n","mergeCommitSha":"39ad8065e40107fba73ab6b014607a75f09520ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5151","title":"Updated Login buttons for VSCode","createdAt":"2023-03-07T18:11:46Z"}
{"state":"Merged","mergedAt":"2023-03-07T23:23:38Z","number":5152,"body":"<img width=\"302\" alt=\"CleanShot 2023-03-07 at 10 56 12@2x\" src=\"https://user-images.githubusercontent.com/1924615/223523198-25f7e6d6-b69e-4750-b387-2ec4dea3ecb7.png\">\r\n\r\n\r\n<img width=\"1548\" alt=\"CleanShot 2023-03-07 at 10 55 55@2x\" src=\"https://user-images.githubusercontent.com/1924615/223523152-3ca78390-b74d-43a7-96ee-1c770c3f2e55.png\">\r\n","mergeCommitSha":"28648bd41b4ff6b6a55d54ea52f44307f36cd57e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5152","title":"Add ability to toggle individual onboarding states from the person page","createdAt":"2023-03-07T18:56:27Z"}
{"state":"Merged","mergedAt":"2023-03-07T19:24:51Z","number":5153,"body":"I don't like hardcoded paths in core app. This should help us move those paths out and make them an infra controlled settings ","mergeCommitSha":"3338ce721ec64113c3011fee95c7692d6cf5ed1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5153","title":"add environment variables for certs","createdAt":"2023-03-07T19:18:50Z"}
{"state":"Merged","mergedAt":"2023-03-07T22:16:11Z","number":5154,"body":"Add import alias based on conversations here:\r\nhttps://chapter2global.slack.com/archives/C02US6PHTHR/p1677884659948689","mergeCommitSha":"e0a94d70fe8e4accb8359686b2bdad7502287139","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5154","title":"Add Import Alias","createdAt":"2023-03-07T19:39:27Z"}
{"state":"Merged","mergedAt":"2023-03-07T23:43:55Z","number":5157,"body":"<img width=\"1052\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223572790-537f070e-087f-458d-aa9c-99a400a769f0.png\">\r\n<img width=\"1049\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223572808-d4f3dbfd-8578-4784-9dde-254f4d4d1e37.png\">\r\n<img width=\"1053\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223572827-ffe8abaf-03d4-449f-a6e2-413d7aa4d933.png\">\r\n","mergeCommitSha":"f30a69948c09432757cb41e1ca72feb396e66481","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5157","title":"Add assets to onboarding checklist","createdAt":"2023-03-07T22:55:29Z"}
{"state":"Merged","mergedAt":"2023-03-08T17:06:29Z","number":5159,"body":"- Adds `listInstallations` which returns `List<Installation>`\r\n- Some API refactoring to pull repos out of the old Installation object","mergeCommitSha":"0dd031b493290e9e56b60b770adbad4c07957dc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5159","title":"[BREAKS API ON MAIN] Add listInstallations","createdAt":"2023-03-07T23:39:25Z"}
{"state":"Merged","mergedAt":"2022-03-10T00:44:26Z","number":516,"body":"Remove caching logic that mapped team members to a team.\r\nNo longer necessary as we've introduced teamID to a thread.\r\n\r\nThings can be cleaned up quite a bit if we move to a stream / publisher model. Joining data is a bit cumbersome atm. Being able to join streams of data should help.","mergeCommitSha":"8ad7052de09f0a1726b8799e4fb48690a7ee9f59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/516","title":"Remove global Team member cache","createdAt":"2022-03-09T19:44:05Z"}
{"state":"Merged","mergedAt":"2023-03-07T23:53:52Z","number":5160,"body":"Add .gitignore to the clientAssets submodule\r\n\r\nFixes:\r\n<img width=\"535\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223580783-d4030993-0dc3-4481-bf67-05f4ea3d408e.png\">\r\n<img width=\"467\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223580846-77d9212d-972e-43ae-8e27-11ef8dc8eb65.png\">","mergeCommitSha":"68e5d44d632bb91a24526bf38b62ba1f1524fb7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5160","title":"Add clientAssets .gitignore and dashboard nits","createdAt":"2023-03-07T23:43:14Z"}
{"state":"Merged","mergedAt":"2023-03-08T01:32:39Z","number":5161,"body":"No behaviour changes.","mergeCommitSha":"7713a770bbde3fb4836b460ce9b073d18adf42a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5161","title":"Refactor TeamStore clauses","createdAt":"2023-03-07T23:52:34Z"}
{"state":"Open","mergedAt":null,"number":5163,"body":"There's a lot going on here \uD83D\uDE05\r\n\r\n1. Secret service is now booting a GRPC service with a healthcheck endpoint. \r\n2. For health checks, Docker is using the OSS tool grpc_health_probe, which k8s has out-of-the-box support for as well. The health probe executes the same checks as the HTTP `__deepcheck` and `__shallowcheck` requests. @mahdi-torabi we need to add a health probe. See `docker-compose-local.yml` for an example.\r\n3. I added a refresh token endpoint that accepts an authed request. Requests to authed endpoints require an `AuthBundle` in the message to perform token validation. See `Secret.proto` for details.\r\n4. There are some additional and _required_ env vars added to config (see global.conf and local.conf). These are:\r\n    a. `SECRET_SERVICE_HOSTNAME`  // Part of `grpcEndpoints` config, should be the hostname other services use to call the secretservice\r\n    b. `SECRET_SERVICE_PORT` // Part of `grpcEndpoints` config\r\n    c. `AUTH_SERVICE_IDENTITY` // The SAN for the authservice, part of `servicesMetadata.identities` configuration\r\n    d. `SECRET_SERVICE_IDENTITY` // The SAN for the secretservice, part of `servicesMetadata.identities` configuration\r\n5. Identity validation is now performed using Subject Alternative Name, as opposed to Common Name. The SAN used for identity validation must be of type DNS, and be the first entry in the SAN list. \r\n6. I've added a root CA cert and openssl cnf in a directory called `self-signed-certs`. We can use these to generate additional certs for local development purposes. See the `src/main/resources/certs` directories in the auth service and secret service for an example.\r\n7. A small change to the CORS plugin code was needed because of a change to nullability of the schemes field in `HostConfig`","mergeCommitSha":"c4f25e6a5546a6ddac715addbb676a424a53566c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5163","title":"Bootstrap GRPC secretservice","createdAt":"2023-03-08T00:20:03Z"}
{"state":"Merged","mergedAt":"2023-03-08T22:25:14Z","number":5165,"body":"Move the shared implementation from `/shared/webComponents` into `/shared/ide`.\r\nVSCode now uses the shared implementation.","mergeCommitSha":"c7c6362e50c464c57b67095296a2d608abe78c27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5165","title":"Remove duplicate ExplorerInsights code","createdAt":"2023-03-08T00:40:36Z"}
{"state":"Merged","mergedAt":"2023-03-08T01:43:09Z","number":5167,"body":"Turns out IntelliJ Startup is very slow.\r\n\r\n`postStartupActivity` is triggered double digit seconds after the editor launches.\r\nThis causes a race with the timeout within JetbrainsTokenProvider and potentially triggers an unnecessary \"logout\" operation due to a failed setupAuth.\r\n\r\nRemove this timeout and just *wait* for the TokenProvider to send the first copy of its tokens before running setupAuth.\r\n\r\nTokenService was also having an issue with null credentials.","mergeCommitSha":"b77599650fabee4536700ca390520b1c35066186","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5167","title":"Fix Startup Loading","createdAt":"2023-03-08T01:22:45Z"}
{"state":"Merged","mergedAt":"2023-03-08T17:13:08Z","number":5168,"body":"### Problem\r\nCaused by the move to components. The `private.yml` file used to be self-contained, so we could get a content stream from Git for that file at any revision. After the transition to components, the revision-specific `private.yml` would be accurate, but the individual components would be resolved from HEAD in all cases. That’s why the checker could not catch changes in components changes.\r\n\r\n### Solution\r\nSince the spec is now 100s of files, we just ditto the entire repo into `/tmp`, using a _git worktree_ to do it efficiently. The relative references to components are consistent once again.","mergeCommitSha":"9ccfa16d0c86b57f5824e8af2f23a2975b405d5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5168","title":"Use Git worktree for API compatibility test","createdAt":"2023-03-08T01:31:57Z"}
{"state":"Merged","mergedAt":"2023-03-08T03:20:03Z","number":5169,"body":"Noticed that all cases where there were dup threads, the thread created timestamp was the same.\r\nI have no logs to indicate otherwise, but there is some logic causing dupe calls.\r\n\r\nIn the meanwhile, moving to race-condition safe upsert stuff I added a while back and added tests that reproduced this.","mergeCommitSha":"0a8df065dfd0bf7c79eb3e6aed60e58722f1402a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5169","title":"Fix concurrent slack threads","createdAt":"2023-03-08T02:41:31Z"}
{"state":"Merged","mergedAt":"2022-03-09T21:28:38Z","number":517,"body":"It's working\r\n\r\nbefore:\r\n```\r\ncurl -X GET 'localhost:8080/api/preauth' -i\r\nHTTP/1.1 404 Not Found\r\n```\r\n\r\nafter:\r\n```\r\ncurl -X GET 'localhost:8080/api/preauth' -i\r\nHTTP/1.1 200 OK\r\n```","mergeCommitSha":"a6ad0f152a479c521cb7058fcab51eeac56eddce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/517","title":"Add /api subpath","createdAt":"2022-03-09T19:54:34Z"}
{"state":"Merged","mergedAt":"2023-03-08T16:57:11Z","number":5170,"body":"We need another way to achieve the same functionality.\r\n\r\nSome options are:\r\n1. version the old operation API\r\n2. separate endpoint for hasDismissedToast flag\r\n","mergeCommitSha":"3055d5f01a5b6c9176a1defef6f49b76a1b8fd61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5170","title":"Revert hasDismissedToast to unbreak API","createdAt":"2023-03-08T05:38:07Z"}
{"state":"Merged","mergedAt":"2023-03-08T07:00:27Z","number":5171,"mergeCommitSha":"633eba2872e197e85fb29aeadb4b06193edcf404","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5171","title":"add more mem to search service prod","createdAt":"2023-03-08T06:54:34Z"}
{"state":"Merged","mergedAt":"2023-03-08T18:14:59Z","number":5172,"body":"I think PATCH method might be a good pattern for requests like this. Should be non-breaking when we add another requests param, because PATCH semantics expect every input parameter to be optional.","mergeCommitSha":"d9dcbb44a28703750ea81ec83e6f17ee8a0cd6d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5172","title":"Use patch for OnboardingStatusUpdate","createdAt":"2023-03-08T07:13:02Z"}
{"state":"Closed","mergedAt":null,"number":5173,"body":"Reverts NextChapterSoftware/unblocked#5159","mergeCommitSha":"2c0e3a6b24c30de6e193923d4ced1e849e33ea0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5173","title":"Revert \"[BREAKS API ON MAIN] Add listInstallations\"","createdAt":"2023-03-08T17:06:45Z"}
{"state":"Merged","mergedAt":"2023-03-08T19:03:22Z","number":5174,"body":"- Adds `listInstallations` which returns `List<Installations>`\n- Some API refactoring to pull repos out of the old Installation object","mergeCommitSha":"03250149c1cbfd3132655219b5d58c7c46207b2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5174","title":"[BREAKS API ON MAIN] Add listInstallations","createdAt":"2023-03-08T17:19:08Z"}
{"state":"Merged","mergedAt":"2023-03-08T17:51:00Z","number":5175,"body":"Add additional logging to logger which should include more error information.","mergeCommitSha":"922a5d307bce2c066581ba522bf426e809ffb11c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5175","title":"UpdateLogging for initial error","createdAt":"2023-03-08T17:30:03Z"}
{"state":"Merged","mergedAt":"2023-03-08T22:50:23Z","number":5176,"body":"Render Discussion Threads in sidebar (real data)\r\n\r\n\r\nhttps://user-images.githubusercontent.com/1553313/223791987-34938b14-9522-4493-b8c8-ca574759c1f7.mp4\r\n\r\nCodeblock rendering will be a separate PR with source mark resolution.\r\nMove DiscussionThread types into shared.\r\n\r\n`DiscussionThread.tsx` was *not* moved to shared in this PR.\r\nReason being there is still CodeBlock implementation being different. Majority of the code is still the same. This will be resolved in a separate PR.\r\n\r\n\r\n\r\n","mergeCommitSha":"c0fa8920031e3fbad407aab5618df9eff65118dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5176","title":"Render discussion threads in IntelliJ","createdAt":"2023-03-08T17:55:59Z"}
{"state":"Merged","mergedAt":"2023-03-08T18:36:42Z","number":5177,"body":"This will create an IAM role giving RDS access to backups S3 buckets to import backups. \r\n\r\nAttaching the role to RDS would be done manually as part of the DB restore procedure in our DR playbook","mergeCommitSha":"a360048de3572afca7c7b3163df546eaf8a2fc64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5177","title":"add S3 role to be used for DB backup imports","createdAt":"2023-03-08T18:28:21Z"}
{"state":"Merged","mergedAt":"2023-03-08T19:18:10Z","number":5178,"mergeCommitSha":"0cb6ea39a4df68b4b42bc383214a52d04bc9222e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5178","title":"Set Person hasDismissedToast for PATCH /person/onboardingStatus","createdAt":"2023-03-08T18:35:05Z"}
{"state":"Merged","mergedAt":"2023-03-08T20:04:50Z","number":5179,"body":"https://user-images.githubusercontent.com/********/223823485-9b56fe60-f995-426e-8e3d-1e430dc2bb64.mp4\r\n\r\nhttps://user-images.githubusercontent.com/********/223823704-8432c2c1-783a-4e4a-8c00-36d0ff19ff9d.mp4\r\n\r\n","mergeCommitSha":"7f5f9f2aff2371673c157bb8a89d5e922ebc8bb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5179","title":"Make onboarding UIs responsive","createdAt":"2023-03-08T19:27:49Z"}
{"state":"Merged","mergedAt":"2022-03-09T22:50:55Z","number":518,"body":"just sort the tags","mergeCommitSha":"7c8e8105dc846449148903ded8c8e8724eb77b9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/518","title":"minor api cleanup","createdAt":"2022-03-09T21:55:48Z"}
{"state":"Merged","mergedAt":"2023-03-08T19:56:42Z","number":5180,"mergeCommitSha":"6cfda9955b3165f98c4e3a617bec7e5d77032f6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5180","title":"Fix GRPC tests to use first available port","createdAt":"2023-03-08T19:47:02Z"}
{"state":"Merged","mergedAt":"2023-03-08T20:48:13Z","number":5181,"mergeCommitSha":"3f29ec1d2827b3d2e3d3e6e42a6d9aeb71602ef6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5181","title":"Attempt moving dev to new poewrml","createdAt":"2023-03-08T20:47:40Z"}
{"state":"Merged","mergedAt":"2023-03-08T21:00:07Z","number":5182,"mergeCommitSha":"250d8f4d328eda164c4e16c367b6524e7d6e57ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5182","title":"Fix typo","createdAt":"2023-03-08T20:51:08Z"}
{"state":"Merged","mergedAt":"2023-03-08T21:09:31Z","number":5183,"mergeCommitSha":"62a93d203e19a48e19c9b3588feb5b12c94e8ff1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5183","title":"Fix cdk","createdAt":"2023-03-08T21:09:25Z"}
{"state":"Merged","mergedAt":"2023-03-08T21:30:07Z","number":5184,"body":"<img width=\"1071\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223852923-ef89054c-f894-4776-a66e-10bf83811ab6.png\">\r\n","mergeCommitSha":"faecb629049f841b84460991eeccc20c1112dfdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5184","title":"Fix skip link alignment","createdAt":"2023-03-08T21:21:29Z"}
{"state":"Merged","mergedAt":"2023-03-08T22:36:41Z","number":5185,"mergeCommitSha":"10babbfb746f915be96b8910f1395c29eb2b58da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5185","title":"Fix powerml","createdAt":"2023-03-08T21:33:21Z"}
{"state":"Merged","mergedAt":"2023-03-08T21:37:20Z","number":5186,"body":"Fix typo","mergeCommitSha":"ecaa1ba00b5369abdc3560803f990ca17b42e50d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5186","title":"Fix typo","createdAt":"2023-03-08T21:36:37Z"}
{"state":"Merged","mergedAt":"2023-03-09T01:30:31Z","number":5187,"body":"Also adds GitLab and Bitbucket specific pagination.\r\n","mergeCommitSha":"22ac624de3b8653795a9088e3a1f6010d3a830f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5187","title":"Implement listInstallations API","createdAt":"2023-03-08T21:45:21Z"}
{"state":"Merged","mergedAt":"2023-03-08T22:43:55Z","number":5188,"mergeCommitSha":"a7a3c2cf0c0b1450876b24c8aa1d05332de22e57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5188","title":"Enable powerml for prod","createdAt":"2023-03-08T22:41:30Z"}
{"state":"Open","mergedAt":null,"number":5189,"body":"<img width=\"1016\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223868243-6b4885c3-4b73-42f6-84a8-90d8b20af539.png\">\r\n\r\n<img width=\"1014\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/223868287-bdbf8d4e-da2e-468f-bf03-61dbea932c67.png\">\r\n","mergeCommitSha":"cad3210c2bc8b5573134971ccf8147fb77718472","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5189","title":"Update onboarding copy","createdAt":"2023-03-08T22:43:53Z"}
{"state":"Merged","mergedAt":"2022-03-11T20:46:19Z","number":519,"body":"Anchor/header sourcemark\r\n\r\nUpdated design:\r\n![image](https://user-images.githubusercontent.com/********/157543238-8fd947bd-8f7f-43fa-a4b8-b033c9859a1f.png)\r\n","mergeCommitSha":"f309e77fe4e3e8fefc911b7a9576a0466d739023","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/519","title":"Add anchor sourcemark id to the Message model","createdAt":"2022-03-09T21:57:40Z"}
{"state":"Merged","mergedAt":"2023-03-08T23:14:55Z","number":5190,"body":"Enable the explorer insights UI in Jetbrains","mergeCommitSha":"09737cdeef7ccf93788bd4751c6388f29c16bd35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5190","title":"Jetbrains explorer insights","createdAt":"2023-03-08T23:06:59Z"}
{"state":"Merged","mergedAt":"2023-03-08T23:23:58Z","number":5191,"mergeCommitSha":"f8a5d3c4deb1e4512661f9f3d2dbe7bafdeeb2dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5191","title":"reduce powerml load","createdAt":"2023-03-08T23:23:51Z"}
{"state":"Merged","mergedAt":"2023-03-08T23:48:18Z","number":5192,"mergeCommitSha":"67536f54e23cefa30f7c521100767050ca794040","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5192","title":"Fix powerml utils","createdAt":"2023-03-08T23:47:30Z"}
{"state":"Merged","mergedAt":"2023-03-09T00:00:53Z","number":5193,"mergeCommitSha":"40aab5a33dabdd7ff69f151051d10c7e79f93261","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5193","title":"Add powerml tests","createdAt":"2023-03-09T00:00:26Z"}
{"state":"Merged","mergedAt":"2023-03-09T17:03:01Z","number":5194,"body":"- Fix pill and topic tag styling\r\n- Hook up thread display","mergeCommitSha":"9d4c52fdaa332be78e433e21ce9c63dcc95065f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5194","title":"A few tiny jetbrains tweaks","createdAt":"2023-03-09T00:11:18Z"}
{"state":"Merged","mergedAt":"2023-03-10T01:27:39Z","number":5195,"body":"Refactor our VSCode SourceMark helpers into shared.\r\n\r\nUpdate DiscussionThreadStream to handle basic SM resolution.\r\n\r\n<img width=\"1391\" alt=\"CleanShot 2023-03-08 at 17 03 05@2x\" src=\"https://user-images.githubusercontent.com/1553313/223888201-184845ea-2d3f-4c9d-b724-22ea350863f8.png\">\r\n","mergeCommitSha":"586859d396e4bfc0b6ffedb5fe486283da880fc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5195","title":"Render source mark","createdAt":"2023-03-09T01:03:20Z"}
{"state":"Merged","mergedAt":"2023-03-09T01:17:34Z","number":5196,"mergeCommitSha":"6079141e371739064ed0b59121419b2b60049f64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5196","title":"Increase sample size","createdAt":"2023-03-09T01:17:22Z"}
{"state":"Merged","mergedAt":"2023-03-09T04:05:47Z","number":5197,"mergeCommitSha":"68f57c3bc7ccb0d2a8927fa3773143df66a3c140","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5197","title":"Increase sample size","createdAt":"2023-03-09T04:05:36Z"}
{"state":"Merged","mergedAt":"2023-03-09T05:07:42Z","number":5198,"mergeCommitSha":"37f3b7f4b928c457985b06bd50ef6c39c693c69c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5198","title":"Add powerml retry semantics","createdAt":"2023-03-09T05:05:28Z"}
{"state":"Merged","mergedAt":"2023-03-09T05:14:36Z","number":5199,"mergeCommitSha":"eedb0542030b283f509057a7727b0543f447b427","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/5199","title":"install tenacity","createdAt":"2023-03-09T05:11:47Z"}
{"state":"Merged","mergedAt":"2022-01-18T05:27:47Z","number":52,"body":"### Detekt IntelliJ Setup\r\n\r\n 1. Install **Detekt** IntelliJ plugin\r\n 2. Configure Detekt\r\n     1. Open IntelliJ **Preferences**\r\n     2. Toggle only these checkboxes on:\r\n         - [x] Enable Detekt\r\n         - [x] Enable rules upon the default configuration\r\n         - [x] Enable rules upon the default configuration\r\n     3. Set **Configuration Files** to `PATH_TO_REPO/apiservice/detekt.yml`","mergeCommitSha":"1f8d82682fd1ea0b43efd0795612d5bb10acfe49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/52","title":"Add Detekt static analysis","createdAt":"2022-01-18T05:02:54Z"}