{"state":"Merged","mergedAt":"2023-07-24T21:39:23Z","number":7180,"body":"* Update references icon coloring and dropdown alignment\r\n* Make all elements actual links \r\n* Clean up tutorial search query after consumption\r\n\r\n<img width=\"607\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/c17b47fb-06b6-4156-8f28-bdf0fe788ca1\">\r\n\r\n\r\n","mergeCommitSha":"be3092acea1a25495fe2a114184fd23031bf852a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7180","title":"Clean up discussion right column","createdAt":"2023-07-21T18:52:54Z"}
{"state":"Merged","mergedAt":"2023-07-21T19:15:50Z","number":7181,"mergeCommitSha":"fd22ec6e9b01a98ba80ef8bd4eca3a4ab1c31236","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7181","title":"Fix dependency versions ofr buildsrc","createdAt":"2023-07-21T19:00:55Z"}
{"state":"Merged","mergedAt":"2023-07-21T19:05:21Z","number":7182,"mergeCommitSha":"e0c6cfaf4366a0f924d96daaffdd3567c6f46718","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7182","title":"Update install text","createdAt":"2023-07-21T19:05:15Z"}
{"state":"Closed","mergedAt":null,"number":7183,"body":"remove obsolete positioning text","mergeCommitSha":"7225e0addc677cb44fc72187bcb1b436544029b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7183","title":"Update Welcome.rtf","createdAt":"2023-07-21T19:05:21Z"}
{"state":"Merged","mergedAt":"2023-07-21T20:28:15Z","number":7186,"mergeCommitSha":"30d1cc03e5d18f304033ee428dc1e3c9ebc195b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7186","title":"Add retries","createdAt":"2023-07-21T20:28:00Z"}
{"state":"Merged","mergedAt":"2023-07-21T22:52:03Z","number":7187,"mergeCommitSha":"9fa3492d56d128fb2213c680d9872681652eee59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7187","title":"Deprecate HubSearch and SemanticSearch as they are enabled by default now","createdAt":"2023-07-21T20:46:47Z"}
{"state":"Merged","mergedAt":"2023-07-21T22:14:54Z","number":7188,"mergeCommitSha":"6cf13d305e1d1a7f82871bbc10dc9378e8563c0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7188","title":"Update submodule commit","createdAt":"2023-07-21T21:23:13Z"}
{"state":"Merged","mergedAt":"2023-07-21T22:26:45Z","number":7189,"body":"I believe all teams are set to OpenAI, should we just make it the default for every new team?\r\n\r\n<img width=\"1205\" alt=\"CleanShot 2023-07-21 at 14 37 53@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/30b8aebb-ec36-4bc0-b0b1-d3eaab128672\">\r\n","mergeCommitSha":"e0fe4559e3a169e5f3ef8a9a23477e53e70e9e70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7189","title":"Make default topic recommendation and classification source be OpenAI","createdAt":"2023-07-21T21:38:23Z"}
{"state":"Merged","mergedAt":"2022-03-30T18:15:15Z","number":719,"mergeCommitSha":"d54afa2115619d5044c495d76c3d6402cacdb8fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/719","title":"Add richie CIDR to WAF allowlist","createdAt":"2022-03-30T15:47:33Z"}
{"state":"Merged","mergedAt":"2023-07-25T22:09:16Z","number":7190,"body":"Hub Tooltip will persist until user dismisses.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/39334553-1533-47ce-9dd4-3db3f2c530e8\r\n\r\n","mergeCommitSha":"396373f7456dd445e3ab8c3fda332f87538b23eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7190","title":"Tooltip open until user dismisses","createdAt":"2023-07-21T22:08:15Z"}
{"state":"Merged","mergedAt":"2023-07-21T22:52:52Z","number":7191,"mergeCommitSha":"38579e6b11cbaf143cafb613e64dd787fdcd3fa8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7191","title":"Deprecate OnboardingV2 feature flag that has been enabled by default","createdAt":"2023-07-21T22:17:27Z"}
{"state":"Merged","mergedAt":"2023-07-21T22:40:32Z","number":7192,"body":"During tutorial, bot instructions were appearing while tutorial was occurring.\r\n\r\nDo not show bot instructions until discussion is no longer a tutorial discussion.","mergeCommitSha":"9e93da5771b8d8bd52645d9e209504a5ca2d3678","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7192","title":"Hide bot instruction while tutorial","createdAt":"2023-07-21T22:23:36Z"}
{"state":"Merged","mergedAt":"2023-07-22T00:19:31Z","number":7193,"mergeCommitSha":"70bff9ce76f759bfc62687dc830e714cfeb5352c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7193","title":"IsQuestionAsTemplate","createdAt":"2023-07-21T23:45:18Z"}
{"state":"Closed","mergedAt":null,"number":7194,"mergeCommitSha":"c16331a86535824aacf8c9734cc681953278a578","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7194","title":"Update DiscussionTutorialMessageTooltip.tsx","createdAt":"2023-07-22T14:22:51Z"}
{"state":"Merged","mergedAt":"2023-07-22T16:55:25Z","number":7195,"mergeCommitSha":"b79af0f07fdf50b3d625d944c0f8d6b4543d9eeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7195","title":"Update DownloadInstaller.tsx","createdAt":"2023-07-22T14:25:39Z"}
{"state":"Merged","mergedAt":"2023-07-24T17:39:15Z","number":7196,"body":"Add overview / discussions tabs to IntelliJ.  Styling is busted, I will fix that in the next PR, this UI is disabled until I've got everything working, which shouldn't take long.\r\n\r\nThe tabs are added as two separate webviews, which is simple code-wise but not super efficient.  I will look into improving this later, if needed.","mergeCommitSha":"581046ff6da7e8f2bd4d056b6202ab38feda33ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7196","title":"IntelliJ new explorer UI (pt 1)","createdAt":"2023-07-23T02:40:17Z"}
{"state":"Merged","mergedAt":"2023-07-24T18:05:20Z","number":7197,"body":"Add theming to the explorer panel and tooltips/feedback section in IntelliJ.\r\n\r\nAfter this goes in, two things remain:\r\n* Invert the semantic search input box (ie light colours in dark mode) in both IDEs\r\n* Add a dropdown in the semantic search input box that directs people to full-text search results\r\n\r\n<img width=\"1480\" alt=\"Screenshot 2023-07-23 at 5 37 42 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/2dde13e3-8057-4973-9eeb-c7dbcb874b67\">\r\n<img width=\"1480\" alt=\"Screenshot 2023-07-23 at 5 37 51 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/6f2265c2-bf24-4b5c-8462-5fe43f0dafe9\">\r\n","mergeCommitSha":"802db599849ec5434031c110296e20cefed86ad2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7197","title":"IntelliJ QA theming","createdAt":"2023-07-24T00:40:26Z"}
{"state":"Merged","mergedAt":"2023-07-24T01:44:56Z","number":7198,"body":"Still too high:\n\n- Drop default DEV events\n- Reduce PROD slow events","mergeCommitSha":"c22c6db5365df5616b79ec2f53037f0bbf2f7e49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7198","title":"Tweak refinery","createdAt":"2023-07-24T01:44:34Z"}
{"state":"Merged","mergedAt":"2023-07-24T20:37:03Z","number":7199,"body":"Invert the search bar theme colours in dark mode.  Light mode themes use the regular input colours.\r\n\r\n<img width=\"1362\" alt=\"Screenshot 2023-07-24 at 11 50 41 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/dec57814-04fd-4ec3-864f-af90a88d82ce\">\r\n<img width=\"1362\" alt=\"Screenshot 2023-07-24 at 11 50 50 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/09829430-bb4a-4668-bf18-2a7c905a9c3a\">\r\n<img width=\"1359\" alt=\"Screenshot 2023-07-24 at 11 46 17 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/c7d3b4e0-7a7c-40b4-a6f3-ec54eaffa8dc\">\r\n<img width=\"1359\" alt=\"Screenshot 2023-07-24 at 11 46 39 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/3d89f7be-53a8-4ac5-ba21-36b344dcf1a8\">\r\n","mergeCommitSha":"2667699186c067a9dbb0b2c5319477978c97b037","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7199","title":"Inverted search bar theme in IDEs","createdAt":"2023-07-24T03:45:57Z"}
{"state":"Merged","mergedAt":"2022-01-19T00:28:58Z","number":72,"body":"```\r\n> Task :apiservice:compileKotlin\r\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/api/AuthApiDelegateImpl.kt: (10, 29): This locations API is experimental. It could be changed or removed in future releases.\r\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/api/AuthApiDelegateImpl.kt: (11, 91): This locations API is experimental. It could be changed or removed in future releases.\r\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/plugins/Routing.kt: (11, 5): This locations API is experimental. It could be changed or removed in future releases.\r\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/plugins/Routing.kt: (11, 13): This locations API is experimental. It could be changed or removed in future releases.\r\nw: /Users/<USER>/work/codeswell/apiservice/src/main/kotlin/com/codeswell/plugins/Routing.kt: (15, 9): This locations API is experimental. It could be changed or removed in future releases.\r\n```\r\n\r\nFrom: https://kotlinlang.org/docs/opt-in-requirements.html#module-wide-opt-in","mergeCommitSha":"c7a66f2a2e158023fe501d9a95f5d00e16e9f67b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/72","title":"Hide annoying KtorExperimentalLocationsAPI warnings","createdAt":"2022-01-19T00:19:29Z"}
{"state":"Merged","mergedAt":"2022-03-30T17:38:12Z","number":720,"mergeCommitSha":"e0a74a269de565a1f4cc20294170c872178af092","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/720","title":"Move SM app to separate gradle subproject","createdAt":"2022-03-30T16:03:18Z"}
{"state":"Closed","mergedAt":null,"number":7201,"body":"Emails for Campaign\r\nCurrently *not* templated as the data is static (not user specific)\r\n\r\nWill be reusing this email & not use components at the moment to generate all three emails.\r\n\r\nThese are not hooked up at all as assets and content are still wip.\r\n\r\n<img width=\"995\" alt=\"CleanShot 2023-07-24 at 10 17 39@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ec3014f9-ed9f-4623-aa6a-940f9da3b60a\">\r\n<img width=\"824\" alt=\"CleanShot 2023-07-24 at 10 11 23@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/a25d7437-b7a1-4049-9c5c-c834f40a2ec2\">\r\n","mergeCommitSha":"b68b0be5b552c84276cae4026af62a9eb9022655","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7201","title":"Emails for campaign","createdAt":"2023-07-24T17:18:18Z"}
{"state":"Merged","mergedAt":"2023-07-24T17:49:47Z","number":7202,"body":"@rasharab @mahdi-torabi We'd like to turn off Honeycomb entirely in DEV to spare EPM for PROD. This is a really stupid way of doing this. Perhaps there's a better way?","mergeCommitSha":"b5f53f6dd194d663ec00a73a3a5409dc7ba799bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7202","title":"Disable Honeycomb in DEV via refinery","createdAt":"2023-07-24T17:21:23Z"}
{"state":"Merged","mergedAt":"2023-07-24T22:05:08Z","number":7203,"body":"* remove Slack row from onboarding Connect identities UI \r\n* For personal settings UI, remove ability to connect to Slack, only show Slack if we can find a secondary membership where `provider === Provider.Slack`","mergeCommitSha":"ed5cb3a1c05242bf3afd6483482797df26b86535","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7203","title":"Hide integration row where !userOauthUrl","createdAt":"2023-07-24T17:34:35Z"}
{"state":"Merged","mergedAt":"2023-07-24T18:33:20Z","number":7204,"body":"The logic is identical under the hood","mergeCommitSha":"3fd2f25a98fa6e368fe0408d2b55010d065a787a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7204","title":"Replace TopicStore.getTopicsForSource with TopicStore.getApprovedTopics where appropriate","createdAt":"2023-07-24T18:07:26Z"}
{"state":"Merged","mergedAt":"2023-07-24T19:01:16Z","number":7205,"mergeCommitSha":"ae8ddb623791156fadc01f593226dd85990294fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7205","title":"Do not embed golden records","createdAt":"2023-07-24T18:29:47Z"}
{"state":"Merged","mergedAt":"2023-07-24T20:10:48Z","number":7206,"body":"Also move business models away from api models.\r\n\r\nThis is really only for Slack ATM.","mergeCommitSha":"834a74ffc9d8f791a7f1a63a4d83e4f707692a8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7206","title":"Installations can have null user oauth urls","createdAt":"2023-07-24T18:35:23Z"}
{"state":"Closed","mergedAt":null,"number":7207,"body":"This reverts commit 3fd2f25a98fa6e368fe0408d2b55010d065a787a.\r\n\r\n(just in case)","mergeCommitSha":"a735a246d6c1f19d4c9a3040610afe8d5bcb5c64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7207","title":"Revert \"Replace TopicStore.getTopicsForSource with TopicStore.getApprovedTopics where appropriate (#7204)\"","createdAt":"2023-07-24T18:35:33Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:52:25Z","number":7208,"body":"Setup Scroll to thread in Web PR. Will handle IDEs next.\r\n\r\nThis work is a precursor to removing discussion threads for code level conversations in a PR.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/87dffc11-349d-4722-8f54-b205f3419987\r\n\r\n","mergeCommitSha":"77330d692953c8f10c240493cf5a760bca3ab50e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7208","title":"Setup Scroll to thread in Web PR","createdAt":"2023-07-24T18:48:01Z"}
{"state":"Merged","mergedAt":"2023-07-24T19:41:04Z","number":7209,"body":"This is phase 1 of 3 in this plan:\r\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1690224781579599\r\n\r\nThis change:\r\n- 100% eliminates _cpu_ on `Clio/themis`\r\n- significantly reduces _memory_ on `Clio/themis` (only 200MB on my machine)\r\n\r\nHowever:\r\n- this is inaccurate as we cannot track sourcemarks moved between files, and cannot track moved files\r\n- it take 3 minutes to download sourcemarks on my machine (https://ui.honeycomb.io/unblocked/environments/production/result/qQZ1y78Jga5?useStackedGraphs)\r\n- during that 3 minute interval no insights will be shown, so the file Insights view will show a spinner\r\n\r\n  <img width=\"400\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/85c04cb6-7469-4e30-9a92-4f8b2e9b810d\">\r\n\r\n","mergeCommitSha":"ee914cebc8071c25376bf8ecf9cff076dc574b3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7209","title":"Disable full recalculation for massive customer repo","createdAt":"2023-07-24T19:11:25Z"}
{"state":"Merged","mergedAt":"2022-03-30T18:12:12Z","number":721,"body":"Trying to get the sub-project configs to be as terse as possible. Lot's of duplication right now.","mergeCommitSha":"210f068282305ca89dbec09949f957a0e532be86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/721","title":"Gradle refactor","createdAt":"2022-03-30T16:03:19Z"}
{"state":"Merged","mergedAt":"2023-07-24T19:52:20Z","number":7210,"mergeCommitSha":"ab683374b31f5eb7e92004d0656c52fc08c3e963","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7210","title":"Move to handlers","createdAt":"2023-07-24T19:23:47Z"}
{"state":"Merged","mergedAt":"2023-07-24T20:53:03Z","number":7211,"body":"Remove feature flag (used in VSCode) and boolean flag (IntelliJ).  I'll remove all the dead code later.","mergeCommitSha":"06397b3d8ce9bfa7b22db199c425a89aa38a92af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7211","title":"Enable new explorer UI globally","createdAt":"2023-07-24T20:02:01Z"}
{"state":"Merged","mergedAt":"2023-07-24T20:52:53Z","number":7212,"body":"* 2px border radius on text inputs and text areas\r\n* Don't always display discussion tab (oops)","mergeCommitSha":"7792ed945aa5a03fa5dce734a8fa6bda57484202","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7212","title":"Quick VSCode explorer panel fixes","createdAt":"2023-07-24T20:22:04Z"}
{"state":"Merged","mergedAt":"2023-07-25T03:32:40Z","number":7213,"body":"Drops summarizes from the step functions. Uses the topic summary templates, and things like Richie queues for resilience etc. We've been doing this already for manual topic summary generation, and it works well.","mergeCommitSha":"737f446fe8c3a73c14453aa3feef5de6cea1e514","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7213","title":"Move topic summarization to kotlin services","createdAt":"2023-07-24T20:22:43Z"}
{"state":"Merged","mergedAt":"2023-07-24T20:39:26Z","number":7214,"mergeCommitSha":"83055db9999d76719ce67df85685b169bb7bf35e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7214","title":"Make topic mapping for walkthroughs high-priority","createdAt":"2023-07-24T20:23:03Z"}
{"state":"Merged","mergedAt":"2023-07-24T21:45:08Z","number":7215,"mergeCommitSha":"111c94ba5c76b448275dfa2cae2a2d8326149e35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7215","title":"IsQuestionAnswerTemplates","createdAt":"2023-07-24T21:09:02Z"}
{"state":"Merged","mergedAt":"2023-07-24T22:50:17Z","number":7216,"body":"To support adding a push channel for topics","mergeCommitSha":"a142ca1ee274d3e701afd8d2c8e772861ad5366f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7216","title":"Update Topic.modifiedAt when updating experts","createdAt":"2023-07-24T21:13:19Z"}
{"state":"Merged","mergedAt":"2023-07-24T23:08:05Z","number":7218,"mergeCommitSha":"5b5d8004e7d3c3e82f0cd15e9d62e670ff1fa878","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7218","title":"Restrict recommended threads to those with a message within the last 30 days","createdAt":"2023-07-24T22:47:35Z"}
{"state":"Merged","mergedAt":"2023-07-25T16:26:29Z","number":7219,"body":"- Explorer overview tab uses 12pt font\r\n- Explorer search icon should be smaller\r\n- Auto-sizing text areas should never show scroll bars","mergeCommitSha":"9ffcbf6a4c5f9466450ae19a4cc7ef342beb0531","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7219","title":"Fix small explorer UI bugs","createdAt":"2023-07-24T23:35:40Z"}
{"state":"Merged","mergedAt":"2022-03-30T19:43:20Z","number":722,"mergeCommitSha":"acde3d1d1cc880d69dc0aae7a2d303d6af2fd502","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/722","title":"Add nginx proxy for services running in intellij","createdAt":"2022-03-30T19:42:17Z"}
{"state":"Closed","mergedAt":null,"number":7220,"body":"This change makes the `topics` push channel symmetric with the `getTopics` operation","mergeCommitSha":"ec457d494788b0db4ddb2a004b001b700d16cff6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7220","title":"Topics push channel should take query parameter","createdAt":"2023-07-24T23:49:53Z"}
{"state":"Merged","mergedAt":"2023-07-25T16:22:46Z","number":7221,"body":"- ImproveSemanticSearchResponses\r\n- More changes\r\n- Add answer prompt template\r\n- More bot fixes\r\n","mergeCommitSha":"039c5c8b933aa67ff69a6f57464658887dced376","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7221","title":"Semantic search should evaluate question and answer before posting it...","createdAt":"2023-07-24T23:56:45Z"}
{"state":"Merged","mergedAt":"2023-07-25T18:19:43Z","number":7222,"body":"Requested by Ben:\r\n* Shrink search bar padding\r\n* Reduce search bar font size\r\n* Fix the weird block (scroll bar) that would display for some people in the auto-sizing text area component","mergeCommitSha":"8aad0a04093567b36ca8f9f398e5bd790115c193","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7222","title":"A couple small UI tweaks","createdAt":"2023-07-25T17:39:12Z"}
{"state":"Merged","mergedAt":"2023-07-25T18:18:50Z","number":7223,"mergeCommitSha":"97daa95902bdb5a6b43de96e806a09de7131d9de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7223","title":"More slack bot optimizations","createdAt":"2023-07-25T17:57:19Z"}
{"state":"Merged","mergedAt":"2023-07-26T16:30:02Z","number":7224,"body":"* Add `getSourceMarkStreamForFile`, `setActiveFiles`, `setActiveMarks` to `ISourceMarkProvider`\r\n* Update `FileSourceMarkStreams` to fetch sourcemarks using the stream API when the hybrid SM flag is enabled","mergeCommitSha":"aeac204395fddd8738d0d2744d7178aba899bb2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7224","title":"SourceMarkProvider updates to support mark streaming","createdAt":"2023-07-25T18:30:49Z"}
{"state":"Merged","mergedAt":"2023-07-26T00:35:30Z","number":7225,"body":"Takes list of file paths.\n\nReturns a list of sourcemarks.","mergeCommitSha":"22667f8fb5c14e42082859181db1dfd6b79b49b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7225","title":"Introduce findRepoSourceMarksByFile","createdAt":"2023-07-25T18:47:42Z"}
{"state":"Merged","mergedAt":"2023-07-25T21:34:17Z","number":7226,"body":"Hybrid cloud/local sourcemark algorithm, where the cloud-based engine precomputes file paths for Git revisions, and the local engine only downloads and recalculates sourcemarks for opened file only.\n\nSee also:\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1690224781579599\n- https://www.notion.so/nextchaptersoftware/Address-Source-Mark-Scaling-Issues-48e123a953e841ac950da37356c9bdef?pvs=4#bd2e64bce96c4ddeba7b1419b1b24a04","mergeCommitSha":"692ed6c8922e78d15b32d00e4f95dbc305b513cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7226","title":"HybridSourcemarkEngine flag","createdAt":"2023-07-25T19:11:19Z"}
{"state":"Merged","mergedAt":"2023-07-25T22:00:58Z","number":7227,"body":"Uploaded all the email assets to client-assets submodule.\r\n\r\nUpdated ci-web to pull from client assets.\r\n\r\nPaths of assets should *not* change as the s3 folder is identical.","mergeCommitSha":"2c4dba8c791c76e0954f320903e0a3fa3934735b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7227","title":"Upload email assets","createdAt":"2023-07-25T20:30:56Z"}
{"state":"Merged","mergedAt":"2023-07-25T23:19:32Z","number":7228,"body":"We set alarm thresholds based on prod DB metrics. We don't really care about the Dev alarms. \r\n\r\nUpdated Read, Write volume IOPS and available free storage alarms with more reasonable numbers to reduce CloudWatch email alarm noise.","mergeCommitSha":"c75b896dd4362da973e434607e709fd8cbc418f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7228","title":"update RDS alarms to reduce noise","createdAt":"2023-07-25T21:00:28Z"}
{"state":"Merged","mergedAt":"2023-07-25T22:24:25Z","number":7229,"body":"This will make it easier to add additional data sources for our Q&A feature","mergeCommitSha":"b6d96853f7f3f0b47fce84dfb70618ea15ad15a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7229","title":"Create a single data service to eventually replace all of the current *dataservices","createdAt":"2023-07-25T21:27:52Z"}
{"state":"Merged","mergedAt":"2022-04-05T01:02:41Z","number":723,"body":"This will be changing as I address feedback from https://github.com/NextChapterSoftware/unblocked/pull/718","mergeCommitSha":"318c4b1a79dcb07ae89f22ddf8022f7424866c42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/723","title":"Video recording api","createdAt":"2022-03-30T19:55:39Z"}
{"state":"Merged","mergedAt":"2023-07-25T22:18:09Z","number":7230,"mergeCommitSha":"538e364595a20c4d9b07fed4f87d8cbbb65eb08d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7230","title":"Do not create slack thread unless the bot question has been validated","createdAt":"2023-07-25T21:40:10Z"}
{"state":"Merged","mergedAt":"2023-07-26T00:36:13Z","number":7231,"body":"Takes a list of sourcemark IDs.\n\nReturns list of \"full\" sourcemarks, meaning the sourcemark and _all_ of it's sourcepoints.","mergeCommitSha":"7ad79dc90433dfa51030bb63e50f3b693c6069c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7231","title":"Introduce findRepoSourceMarksById","createdAt":"2023-07-25T22:02:15Z"}
{"state":"Merged","mergedAt":"2023-07-25T22:45:36Z","number":7233,"mergeCommitSha":"16a4eb9a5b5461ab8522c957b2cedca90aee392e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7233","title":"Move vscode to runner","createdAt":"2023-07-25T22:26:47Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:39:35Z","number":7234,"mergeCommitSha":"b387aac297566f5c7b3f60f92867904e0f2d52b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7234","title":"Consume new recommended APIs","createdAt":"2023-07-25T22:37:58Z"}
{"state":"Merged","mergedAt":"2023-07-25T23:05:37Z","number":7235,"body":"This is being replaced by the `DataService`","mergeCommitSha":"8ff4728303fb628c99e24fe206d55d1ddb85e16e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7235","title":"Remove linear data service","createdAt":"2023-07-25T22:38:13Z"}
{"state":"Merged","mergedAt":"2023-07-25T22:50:52Z","number":7236,"body":"Add submodule fetch to asset upload","mergeCommitSha":"03afd4f07733090955b1f8552cd1b3fa3202ae30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7236","title":"Add submodule","createdAt":"2023-07-25T22:50:15Z"}
{"state":"Merged","mergedAt":"2023-07-25T23:03:05Z","number":7237,"mergeCommitSha":"e90e7342eadce7140f7b45c3536aed56c4c5c8b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7237","title":"Web uses ec2 runner","createdAt":"2023-07-25T22:50:27Z"}
{"state":"Merged","mergedAt":"2023-07-26T00:26:45Z","number":7238,"body":"Let's see how this does. I'll increase the document retrieval size to something very large and we'll see if length ordering gives us better results.\r\n\r\nNote: this will still only generate tweet sized summaries, so they might not provide us with good enough summaries for embeddings.\r\n\r\n\r\nIf the summaries generated after this change are still poor, then I'll implement the other things listed here:\r\nhttps://chapter2global.slack.com/archives/C045VGYML95/p1690311542463839?thread_ts=1690302336.780819&cid=C045VGYML95\r\n\r\nSummary of that slack thread:\r\n1. Retrieve large (1000+) records for each source type (PR summaries, slack, Jira/Linear) by recency\r\n2. Omit data sources without useful metadata, low fi content, or that bias results (sourcecode, notes, pr comments?)\r\n3. Sort documents by length, mux through types up to some maximum number (this assumes that length ordering will give us the highest quality data roughly distributed through time)\r\n4. Run large (500 word) summarization on document set for topic\r\n5. Queue snippit generation for small topic summary display in UX, which just summarizes the summary in 2 sentences.\r\n","mergeCommitSha":"fba470c66e8dfb7954fa270c3ff4898f1fe1b919","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7238","title":"Filter out garbage data for summaries","createdAt":"2023-07-25T23:24:00Z"}
{"state":"Merged","mergedAt":"2023-07-25T23:45:36Z","number":7239,"mergeCommitSha":"66033cc253e505d91d9573e69669b47a8e0f4575","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7239","title":"Explicitly do topic summaries for slack threads","createdAt":"2023-07-25T23:28:19Z"}
{"state":"Merged","mergedAt":"2022-04-01T20:51:34Z","number":724,"body":"![image](https://user-images.githubusercontent.com/13431372/160925948-a3e5acbf-4b6f-427f-9d17-ca71b4d38016.png)\r\n![image](https://user-images.githubusercontent.com/13431372/160926040-99f0d5b4-ef3a-4851-97df-28ac2e64d35b.png)\r\n![image](https://user-images.githubusercontent.com/13431372/160926090-3d43fdf5-84e1-43ec-b434-d14ae3049f62.png)\r\n\r\n![image](https://user-images.githubusercontent.com/13431372/160926250-242eb3cb-a304-4856-b715-2682b3e4a791.png)\r\n![image](https://user-images.githubusercontent.com/13431372/160926267-ee91b153-99f5-4c8c-a938-629d3fed3c75.png)\r\n\r\nNOTES:\r\n* Context menu styling is just a simple box for now since there are no mocks for this yet \r\n* For now, we hide the context menu entirely if it is the anchor message (ie the first message) \r\n    * This will be updated once we add more functionality to the context menu\r\n* Temporarily(?) using the web window.confirm dialog - can probably replace with our own popup service once it is implemented\r\n* There's a backend issue with deleting the last message, but is to be handled by https://github.com/NextChapterSoftware/unblocked/pull/676\r\n\r\nThis PR also addresses a few issues:\r\n* There was a race condition between the two places calling initializeAuth - removed the call in the AuthSidebarWebviewProvider\r\n* Update useStream to take a useMemo-like protocol to prevent it from running on every render\r\n","mergeCommitSha":"330c9cfda02ec06f436b96e07a8bb9ec28447381","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/724","title":"Hook up ability to delete messages","createdAt":"2022-03-30T20:41:01Z"}
{"state":"Merged","mergedAt":"2023-07-26T00:36:43Z","number":7240,"mergeCommitSha":"2e64c3c8a5d3b9d0f464f29843d1cc3de2413ee2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7240","title":"Implement new sourcemark APIs","createdAt":"2023-07-25T23:35:03Z"}
{"state":"Merged","mergedAt":"2023-07-25T23:50:34Z","number":7241,"mergeCommitSha":"70c56da8b1553fd18f9b485e758801546220e3b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7241","title":"Fix context links","createdAt":"2023-07-25T23:37:55Z"}
{"state":"Merged","mergedAt":"2023-07-26T17:37:12Z","number":7242,"mergeCommitSha":"03d42892f7ed955f956bd6d8630496297244e78c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7242","title":"Persist Jira issues to S3","createdAt":"2023-07-25T23:40:28Z"}
{"state":"Merged","mergedAt":"2023-07-25T23:55:42Z","number":7244,"mergeCommitSha":"6436e8d9cff0594e36b3f0374a2f83f402b68479","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7244","title":"Deploy data service","createdAt":"2023-07-25T23:53:24Z"}
{"state":"Merged","mergedAt":"2023-07-26T00:09:02Z","number":7245,"mergeCommitSha":"ae2349516b07ac26f560ce21b7e53b5e916b1a20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7245","title":"jira-data bucket","createdAt":"2023-07-26T00:08:37Z"}
{"state":"Merged","mergedAt":"2023-07-26T00:48:23Z","number":7246,"mergeCommitSha":"bd3ab7fbaf92ffb501ed62efdda3da8ab3a101e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7246","title":"Trigger sourcemark-in-cloud on non-public repos too","createdAt":"2023-07-26T00:42:36Z"}
{"state":"Merged","mergedAt":"2023-07-26T01:43:38Z","number":7248,"mergeCommitSha":"402ab7ba9d3356787ad8b242030665f39c2f772e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7248","title":"log prompt","createdAt":"2023-07-26T01:39:16Z"}
{"state":"Merged","mergedAt":"2023-07-26T03:25:14Z","number":7249,"mergeCommitSha":"19ed4f60d27333f9c4a9ed805544c4d118ab0280","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7249","title":"Remove web extension from isntaller","createdAt":"2023-07-26T03:25:06Z"}
{"state":"Merged","mergedAt":"2022-03-30T21:43:34Z","number":725,"body":"1. orderly shutdown of SM server and SM scheduler background tasks\r\n2. moves JVM process to application level","mergeCommitSha":"877fa43548551bdc51758efaa35ec06d7db668a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/725","title":"Deregister SM background tasks cleanly on shutdown","createdAt":"2022-03-30T20:49:12Z"}
{"state":"Merged","mergedAt":"2023-07-26T18:58:17Z","number":7251,"mergeCommitSha":"365af3317402ee1fa889d0ed9fba36a6f21531ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7251","title":"Add new slack channels","createdAt":"2023-07-26T18:36:35Z"}
{"state":"Merged","mergedAt":"2023-08-10T22:41:46Z","number":7252,"body":"I noticed a few cases where documents containing triple backticks were throwing off the prompt (which tells the model that documents are enclosed in triple backticks). It just replaced the triple backticks with triple quotes within the document content itself. \r\n\r\nThere's an open question as to whether this will cause the model to see a triple quote enclosed piece of document content and fail to interpret it as a Markdown code block, but it seemed to behave correctly in the OpenAI playground. ","mergeCommitSha":"3650ad252064bd5346dc635dcfd72f70b1c971e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7252","title":"Sanitize conversation content","createdAt":"2023-07-26T19:03:43Z"}
{"state":"Merged","mergedAt":"2023-07-26T19:51:55Z","number":7253,"mergeCommitSha":"ac5f825d599b8197756aa4445dfaaae7a6756bf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7253","title":"Slack bot notifications","createdAt":"2023-07-26T19:40:07Z"}
{"state":"Merged","mergedAt":"2023-07-26T21:05:05Z","number":7255,"body":"Updated welcome email.\r\n\r\n`make update-notification-templates` was run to generate json.\r\n\r\n<img width=\"733\" alt=\"CleanShot 2023-07-26 at 12 51 16@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/da2903cf-6ac9-4961-9216-8d3fc8a41e2f\">\r\n","mergeCommitSha":"b9d841cf6b534071fcc0f01f9ff9e3def97dd9a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7255","title":"Updated welcome email to new designs","createdAt":"2023-07-26T19:51:57Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:48:54Z","number":7256,"mergeCommitSha":"77739e19bd5b6cde26befa3f92d022bacdb061af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7256","title":"Add sourcecode docs for topic summary generation","createdAt":"2023-07-26T20:04:08Z"}
{"state":"Merged","mergedAt":"2023-07-26T21:38:42Z","number":7258,"body":"Removes all the onboarding bits from old onboarding flow from IDE clients.\r\n\r\nAlso removed TutorialChecklist bits.","mergeCommitSha":"f1cc966aaad0a1816d6b647138916a997f57dd51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7258","title":"Remove IDE onboarding bits","createdAt":"2023-07-26T20:54:42Z"}
{"state":"Merged","mergedAt":"2023-07-26T21:32:38Z","number":7259,"mergeCommitSha":"b2943f768b46abc0595240ebb3d5f40ef347e7f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7259","title":"Allow for shadow mode for slack bot","createdAt":"2023-07-26T21:12:06Z"}
{"state":"Merged","mergedAt":"2022-03-30T20:57:59Z","number":726,"mergeCommitSha":"633f017fb26c31b4a63bbee7a214e8aa17fc0922","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/726","title":"Update office ip","createdAt":"2022-03-30T20:56:42Z"}
{"state":"Merged","mergedAt":"2023-07-28T20:46:54Z","number":7261,"body":"VSCode provides the set of all file tabs (note: not visible editors, but all tabs) to the SM engine for preloading.\r\nWill do IntelliJ next","mergeCommitSha":"187098d1a6199d3163cd125fbdc749333311d8fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7261","title":"VSCode provide active tab files to SM engine","createdAt":"2023-07-26T21:41:29Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:15:35Z","number":7262,"body":"Farewell.","mergeCommitSha":"85e91af110b79b4ac264afde5d0f52f272cc25ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7262","title":"Remove web extension","createdAt":"2023-07-26T21:42:08Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:11:55Z","number":7263,"mergeCommitSha":"40327621b6fb84b2e5a53a160525f39e24bab73f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7263","title":"Remove dead VSCode onboarding assets","createdAt":"2023-07-26T22:02:52Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:25:27Z","number":7264,"body":"**JEFF IS MY FRIEND**\r\n\r\n`I` don't usually do this but I felt bad for Jeff having to deal with emails...\r\n\r\nRemoving textPart in email tempaltes. Too error prone and not necessary.\r\nRemoving a lot of unnecessary fields for welcome email template payload with @jeffrey-ng changes.\r\nCleaning stuff up.\r\n\r\n@jeffrey-ng ","mergeCommitSha":"30a54a4c4508ffdfdf6a3bec0dce471bae850ddb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7264","title":"Clean up Welcome Email backend code","createdAt":"2023-07-26T22:03:37Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:26:57Z","number":7265,"body":"Next PR will remove the SCM data service.","mergeCommitSha":"b6ba2a8ebab73309782365f83162cbfa16ffaeae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7265","title":"New DataService handles SCM data events","createdAt":"2023-07-26T22:07:00Z"}
{"state":"Merged","mergedAt":"2023-07-26T22:50:58Z","number":7266,"mergeCommitSha":"834762cceb6a9d2770a98c9562c7826b943d617c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7266","title":"New DataService handles folder data events","createdAt":"2023-07-26T22:34:53Z"}
{"state":"Merged","mergedAt":"2023-07-26T23:35:57Z","number":7267,"body":"\uD83E\uDD17 ","mergeCommitSha":"28a3f947dfa13b79e70a3ab2a2b2951ddcda10a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7267","title":"New DataService handles slack data events","createdAt":"2023-07-26T22:57:50Z"}
{"state":"Merged","mergedAt":"2023-07-27T15:59:46Z","number":7268,"body":"Update dashboard URLs to send PR threads to PR View instead of discussion view","mergeCommitSha":"86d31f28f2bff13c08b8c3680bb61b28ca304a2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7268","title":"Update thread dashboard url","createdAt":"2023-07-26T23:12:14Z"}
{"state":"Merged","mergedAt":"2023-07-26T23:12:31Z","number":7269,"mergeCommitSha":"1c93c32b5f4a3c76c9c6474f0926d8d99efd99d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7269","title":"Remove lineardata","createdAt":"2023-07-26T23:12:24Z"}
{"state":"Merged","mergedAt":"2022-03-31T22:39:03Z","number":727,"mergeCommitSha":"17c4a3ae6a45fedcce126ca072d661b2927874cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/727","title":"Address bugs in SM shutdown","createdAt":"2022-03-30T22:15:01Z"}
{"state":"Merged","mergedAt":"2023-07-26T23:22:33Z","number":7270,"mergeCommitSha":"3d94c616ddc23ff6122f3a35aa66aa384bea9ef3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7270","title":"this rule has been very noise. It's triggered by a maintenance activity","createdAt":"2023-07-26T23:19:16Z"}
{"state":"Merged","mergedAt":"2023-07-26T23:44:32Z","number":7272,"body":"Tooltip notification will open by default whenever one restarts the hub.\r\nThis should have been false but was toggled to true for testing purposes...","mergeCommitSha":"d05920bd3a3043109c72fca1d0a18cb104b4ffa1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7272","title":"Default value should be false","createdAt":"2023-07-26T23:36:03Z"}
{"state":"Merged","mergedAt":"2023-07-26T23:56:34Z","number":7273,"body":"All of this is handled by the new data service","mergeCommitSha":"8e352b786d4572c6f3656143bd2d14e141fb1e4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7273","title":"Remove no longer needed libs and services","createdAt":"2023-07-26T23:41:15Z"}
{"state":"Merged","mergedAt":"2023-07-27T23:16:59Z","number":7274,"body":"* Remove most of the `index.ts` references that are used in the landing page.\r\n* When bundling, write out larger images (> 10k) as separate image files, don't inline\r\n\r\nThis reduces the bundle size by about 3/4s","mergeCommitSha":"76ecd34c6a194945f474905209050f0bd486e97e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7274","title":"Optimize landing page","createdAt":"2023-07-26T23:54:23Z"}
{"state":"Merged","mergedAt":"2023-07-27T00:14:50Z","number":7275,"mergeCommitSha":"08b79d81a33f148d8ea33419e4202cf973f61c4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7275","title":"Remove folderdata, scmdata, and slackdata form ci workflows","createdAt":"2023-07-26T23:58:59Z"}
{"state":"Merged","mergedAt":"2023-07-28T21:26:15Z","number":7276,"body":"Launching PR threads from the hub will now open them within the PR view in IDEs\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/468114b0-e405-4f61-b3f1-0736a81ade2d\r\n\r\n","mergeCommitSha":"9daf308d89b1908a3c18c84fae2cad475f4fff21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7276","title":"Hookup launching PR threads from clients","createdAt":"2023-07-27T00:04:47Z"}
{"state":"Merged","mergedAt":"2023-07-27T21:41:04Z","number":7277,"body":"note: this is still early, not working","mergeCommitSha":"b933e89bb6240d0aeb7713c13bceabba17c75154","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7277","title":"Stream-based sourcemark by file lookup","createdAt":"2023-07-27T01:34:01Z"}
{"state":"Merged","mergedAt":"2023-07-27T04:17:31Z","number":7278,"mergeCommitSha":"3d43c09e6d23551ac336e753be65e0897fa9331b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7278","title":"Fix up zipping logic","createdAt":"2023-07-27T04:13:08Z"}
{"state":"Merged","mergedAt":"2023-07-27T15:59:30Z","number":7279,"body":"Fix: Cannot interact with Menu when the Tooltip is up.\r\nhttps://chapter2global.slack.com/archives/C02US6PHTHR/p1690419783063249\r\n\r\nFix: Tooltip placement is wrong when switching between Hub “tabs”\r\nhttps://chapter2global.slack.com/archives/C02US6PHTHR/p1690419867696269\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/20d265ec-218b-473b-979f-e7e05916d4cd\r\n\r\n\r\n","mergeCommitSha":"87a0eb1b3d8a5929baed5563eb7be9fc375c2a7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7279","title":"Fix tooltip issues","createdAt":"2023-07-27T06:24:58Z"}
{"state":"Merged","mergedAt":"2022-03-30T23:02:29Z","number":728,"mergeCommitSha":"627e0eca12afd1c5220634168d912f18aff9b264","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/728","title":"Debug logging for schema update fail","createdAt":"2022-03-30T22:48:47Z"}
{"state":"Merged","mergedAt":"2023-07-27T17:51:33Z","number":7280,"body":"This ensures a new stacking context is created so that the background layer always appears correctly underneath the content.","mergeCommitSha":"87f5ad676453ce549fc883c2fbd119f5954539fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7280","title":"Fix feedback button background layering","createdAt":"2023-07-27T17:14:59Z"}
{"state":"Merged","mergedAt":"2023-07-27T19:26:38Z","number":7281,"body":"https://chapter2global.slack.com/archives/C03NVHV37EF/p1690439100440849","mergeCommitSha":"d0c19c9ad9615615700fcf53672660235523f343","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7281","title":"Ignore auto-generated comments","createdAt":"2023-07-27T18:08:38Z"}
{"state":"Merged","mergedAt":"2023-07-27T18:33:38Z","number":7282,"body":"This was breaking the explorer panel, as we would have multiple people subscribing and unsubscribing from the stream.","mergeCommitSha":"5a5068a8186cc5cd704d8b625cddb994e4d0835b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7282","title":"Remember client config stream values","createdAt":"2023-07-27T18:22:11Z"}
{"state":"Merged","mergedAt":"2023-07-27T18:52:31Z","number":7283,"mergeCommitSha":"ce70bce81ce372e95b5c15af5d48ebf81a0a89fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7283","title":"Add unit tests for last change to client config store","createdAt":"2023-07-27T18:41:01Z"}
{"state":"Merged","mergedAt":"2023-07-27T19:24:54Z","number":7284,"mergeCommitSha":"a37b6675202c7ff62d4fd5508665730872681e35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7284","title":"Display a slice of channels","createdAt":"2023-07-27T18:47:00Z"}
{"state":"Merged","mergedAt":"2023-07-27T21:39:13Z","number":7285,"body":"Strategy:\r\n- get sourcemarks streams working independently (`SOURCEMARK_STREAMS` env var)\r\n- get sourcemark hybrid mode working independently (`HybridSourcemarkEngine` feature flag)\r\n- then integrate both of them","mergeCommitSha":"dead6adfc6c8ceee531f6a04df6e16e2440afd80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7285","title":"Decouple sourcemarks streams from sourcemark hybrid mode","createdAt":"2023-07-27T19:16:35Z"}
{"state":"Merged","mergedAt":"2023-07-27T19:42:52Z","number":7286,"body":"Also updating background job polling intervals.","mergeCommitSha":"bf089c7388283082cb54da9c48628073bc2b97af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7286","title":"Change priorities for onboarding slack events","createdAt":"2023-07-27T19:26:01Z"}
{"state":"Merged","mergedAt":"2023-07-27T21:39:54Z","number":7287,"mergeCommitSha":"8d71ae38736b3d70f0c255636b71c760889dd927","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7287","title":"Cleanup unused sourcemark methods","createdAt":"2023-07-27T19:40:55Z"}
{"state":"Merged","mergedAt":"2023-07-28T18:17:12Z","number":7288,"body":"Add message input icon in footer\r\n\r\n<img width=\"1489\" alt=\"CleanShot 2023-07-27 at 14 16 54@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/7be1670c-e1f3-4fad-971b-791be33ccfaa\">\r\n","mergeCommitSha":"c963046bd3cfa0908a5f56e23f0d4aa29ce3e912","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7288","title":"Add author icon next to footer message input","createdAt":"2023-07-27T21:17:46Z"}
{"state":"Merged","mergedAt":"2023-08-10T18:08:41Z","number":7289,"mergeCommitSha":"d84cfb84f6d1f1e0e9dd9a287da32d69734061fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7289","title":"Reverts the zip action, excludes source code (since it's not in PG anyways)","createdAt":"2023-07-27T21:39:31Z"}
{"state":"Merged","mergedAt":"2022-04-03T03:15:53Z","number":729,"mergeCommitSha":"60e1e5bf96959c254bd88f42e3c2e1252b64825b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/729","title":"Make pusher team bound","createdAt":"2022-03-30T23:18:58Z"}
{"state":"Merged","mergedAt":"2023-07-28T04:40:16Z","number":7290,"mergeCommitSha":"895a836c95e0ac5288010ddc5a2a5169f18affe3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7290","title":"Improve words and grammar fix","createdAt":"2023-07-27T21:43:10Z"}
{"state":"Merged","mergedAt":"2023-07-27T22:23:33Z","number":7291,"mergeCommitSha":"461526a52ef2c80a9b9d12630718abb76de0e1e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7291","title":"Add mention filtering and slack message sending","createdAt":"2023-07-27T22:00:41Z"}
{"state":"Merged","mergedAt":"2023-07-27T23:06:00Z","number":7292,"body":"* Set width of slack channel dropdown (max and min)\r\n* Fix topic card column rendering for better spacing","mergeCommitSha":"920dc423b04f7922aab8f7d86c9ad0abdd0128d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7292","title":"Dashboard polish items","createdAt":"2023-07-27T22:54:09Z"}
{"state":"Merged","mergedAt":"2023-07-28T18:16:47Z","number":7293,"body":"Added subtitle with PR title to match Hub design.\r\n<img width=\"785\" alt=\"CleanShot 2023-07-27 at 15 26 46@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/3f5afc17-b952-4c93-9b09-dc9664f08cc4\">\r\n","mergeCommitSha":"36c598257953f6220515f114f9fb30d555fd21b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7293","title":"Updated dashboard summary","createdAt":"2023-07-27T23:05:50Z"}
{"state":"Merged","mergedAt":"2023-07-27T23:29:02Z","number":7294,"mergeCommitSha":"b9722694283ccc80c2008a762d3163c17d23c8dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7294","title":"Filter non-authored docs from experts query","createdAt":"2023-07-27T23:13:31Z"}
{"state":"Merged","mergedAt":"2023-07-28T00:23:59Z","number":7295,"body":"This change means that when Hybrid mode is on:\n\n 1. no sourcemarks are downloaded by default for the workspace/project\n 2. when the user clicks on any thread in the UI, we fetch the sourcemark on demand from the server (and cache it)","mergeCommitSha":"20275cb69bcd4517c16e6f5fc897962b52a1c618","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7295","title":"HybridSourcemarkEngine fetches sourcemarks by ID on demand","createdAt":"2023-07-27T23:28:09Z"}
{"state":"Merged","mergedAt":"2023-07-27T23:47:29Z","number":7296,"body":"Opening port 5001 to Unblock Doug with the experiment work.","mergeCommitSha":"de03f42e6991068e3cdd1dc12dfbae76d9985def","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7296","title":"opening port 5001","createdAt":"2023-07-27T23:46:16Z"}
{"state":"Merged","mergedAt":"2023-07-28T04:57:01Z","number":7297,"body":"This was screwing up search results for our team","mergeCommitSha":"7f2c0dd62b7c53c56be0f9d3a2a546b7e680db02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7297","title":"Remove old SearchInsightAuthorModels","createdAt":"2023-07-27T23:53:04Z"}
{"state":"Merged","mergedAt":"2023-07-28T17:34:36Z","number":7298,"mergeCommitSha":"1eb6314f9c1b6a995ade35f08ab8c07be05cfad1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7298","title":"Add message diagnostics","createdAt":"2023-07-27T23:55:34Z"}
{"state":"Merged","mergedAt":"2023-07-28T17:36:51Z","number":7299,"mergeCommitSha":"0c6b270fee969c47893e1e758c6cbcd213e2ed74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7299","title":"Remove teamMemberId from document search lest we include private threads","createdAt":"2023-07-28T00:00:52Z"}
{"state":"Merged","mergedAt":"2022-01-20T01:22:01Z","number":73,"body":"Shows a new Webview-rendering skeleton.","mergeCommitSha":"00992dfe4fbae117ea5ca055619dab265dff6cda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/73","title":"Webview rendering skeleton","createdAt":"2022-01-19T00:55:17Z"}
{"state":"Merged","mergedAt":"2022-03-30T23:33:41Z","number":730,"body":"* unbreaks the MessageEditor ","mergeCommitSha":"16914724f8ec7937f765aeeb039dbe669ea494a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/730","title":"Memoize data cache streaming hook","createdAt":"2022-03-30T23:26:18Z"}
{"state":"Merged","mergedAt":"2023-07-28T17:32:42Z","number":7300,"body":"Removing much of the dashboard code accidentally removed some of the detail/overview layout styling we were relying on.  This re-adds it to the landing page.\r\n\r\nI don't love this solution but it's quick.","mergeCommitSha":"a4db18452260558b35c333f1b147c966fe80821f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7300","title":"Fix landing page scrolling","createdAt":"2023-07-28T00:36:19Z"}
{"state":"Merged","mergedAt":"2023-07-28T04:00:17Z","number":7301,"body":"This is dead code removal.\n\nNot clear when this was removed.\n\nDoesn't matter, because the HybridSourcemarkEngine will be far more efficient. We'll probably just remove partial recalculation entirely once the HybridSourcemarkEngine is live.","mergeCommitSha":"4eb6fceaa48d1961040faf0514a2a20533de9fd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7301","title":"Remove unused thread mine/recommended partial recalculation","createdAt":"2023-07-28T02:46:56Z"}
{"state":"Merged","mergedAt":"2023-07-28T04:19:00Z","number":7302,"mergeCommitSha":"22fa879bbf4b7cfb6ddc7607b4e1dc988187fbc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7302","title":"SM store can fetch marks by file","createdAt":"2023-07-28T03:02:34Z"}
{"state":"Merged","mergedAt":"2023-07-28T04:23:55Z","number":7303,"body":"https://chapter2global.slack.com/archives/C03NVHV37EF/p1690515763845649?thread_ts=1690439100.440849&cid=C03NVHV37EF","mergeCommitSha":"9583ddbc6d2bf1edfb9542c2c49d45cd63ac109c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7303","title":"Social network ignores ignored authors, automated threads, open PRs","createdAt":"2023-07-28T04:00:28Z"}
{"state":"Merged","mergedAt":"2023-07-30T22:42:59Z","number":7304,"mergeCommitSha":"167362f00232b8572256e6fc07e75a897bb253c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7304","title":"Expose store changed events","createdAt":"2023-07-28T04:01:26Z"}
{"state":"Merged","mergedAt":"2023-07-28T17:13:44Z","number":7305,"body":"@matthewjamesadam we need to live on this to ensure it's solid, so just enabling by default and we'll hopefully have good signal before next IDE release.","mergeCommitSha":"9e298d004928b66b05ca76a696514e7dd4479bab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7305","title":"Enable sourcemark get-mark-by-file stream by default","createdAt":"2023-07-28T04:22:51Z"}
{"state":"Merged","mergedAt":"2023-07-28T06:44:45Z","number":7306,"body":"- Update CDK packages to get latest resource version for RDS Cluster\r\n- Upgrade Dev DB to Postgres 15.2 \r\n- Enabled IO Optimized config on Dev DB.\r\n\r\nI have deployed these changes to Dev locally and it went through with no issues. We do however need to make sure DB is upgraded to 15.2 before enabling IO Optimized configuration. \r\n\r\n\r\nProd is still unchanged. Prod deployment will be done Sunday evening. ","mergeCommitSha":"3c77082547507d8b2785309381be2beaaf19dd39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7306","title":"upgrade Dev DB and enable IO optimize config","createdAt":"2023-07-28T06:43:16Z"}
{"state":"Merged","mergedAt":"2023-07-31T23:31:43Z","number":7307,"body":"We now fetch marks from the server for files:\r\n- of active editor tabs\r\n- of uncommitted files since HEAD\r\n\r\nWhen a file is opened:\r\n- we immediately calculate updated sourcemarks for the file.\r\n- we refresh the calculation whenever the editor tells us to (usually due to an edit).\r\n- we fetch marks for the file, which in turn triggers a calculation of marks for HEAD. we subscribe to\r\n  calculator updates for the file, so that whenever this calculation finishes we calculate updated sourcemarks for the file.\r\n\r\nStore will fetch marks for a file at most every 15 minutes. It chunks the files to avoid large requests.\r\nIt clamps the file to avoid huge numbers of requests in the unlikely case that the user has 1000s of uncomitted files, or opened files.","mergeCommitSha":"5b07f47dd037881c6c31f2cec9e5112ab0616e71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7307","title":"Sourcemark update pipeline completed","createdAt":"2023-07-28T16:04:35Z"}
{"state":"Merged","mergedAt":"2023-07-28T16:30:01Z","number":7308,"mergeCommitSha":"7e96f83ad0976c4ef3ce18c39154bd8df39d413d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7308","title":"Fix download page background","createdAt":"2023-07-28T16:21:16Z"}
{"state":"Merged","mergedAt":"2023-07-28T20:28:36Z","number":7309,"body":"I kind of felt like removing the checksums here because I'm not sure they're useful, but left them for now","mergeCommitSha":"839568ba25f20b487c5afcf2e0efd03de994d8e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7309","title":"Add VSCode plugin download link to admin UI","createdAt":"2023-07-28T16:29:19Z"}
{"state":"Merged","mergedAt":"2022-03-31T04:29:08Z","number":731,"body":"## Summary\r\nMuch like threads before we realized messages carry an implicit participant update, video push requires that the subscriber is notified of both channel and participant updates","mergeCommitSha":"da258d73fa782828352a0f188f82993fb0818497","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/731","title":"Add video channel push","createdAt":"2022-03-31T00:08:28Z"}
{"state":"Merged","mergedAt":"2023-08-02T21:08:46Z","number":7310,"body":"* Move a lot of the client thread sorting to a hook for reusability (i.e. for other clients)\r\n* Fix bug where we were possibly displaying topics outside of the user's expertise ","mergeCommitSha":"9876ee8254e525cce0989f7ce14633cfde173769","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7310","title":"Add recommended helper hook for reuse","createdAt":"2023-07-28T17:36:54Z"}
{"state":"Merged","mergedAt":"2023-07-28T17:47:29Z","number":7311,"body":"Not sure what happened in my PR, this got cut out","mergeCommitSha":"50cccb1b763ae8139a4d838afbad056349f9939a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7311","title":"Fix landing page scrolling","createdAt":"2023-07-28T17:39:34Z"}
{"state":"Merged","mergedAt":"2023-07-28T18:00:27Z","number":7312,"mergeCommitSha":"6ecb97e199fe6e7b4757b9c68d340283579cf96d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7312","title":"Add campaign service","createdAt":"2023-07-28T17:52:21Z"}
{"state":"Merged","mergedAt":"2023-07-28T18:22:38Z","number":7313,"mergeCommitSha":"e35673ce49cdc71d6063c16b2b8b5d797c6b4bc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7313","title":"Add more message diagnostics","createdAt":"2023-07-28T18:22:25Z"}
{"state":"Merged","mergedAt":"2023-07-28T19:47:20Z","number":7314,"body":"These failures happen when the binary can't even be found -- in this case we were never resolving the promise at all.  This will hopefully get us further on Windows/linux.","mergeCommitSha":"0833568be258e87320c3b4a2c574764c59e9d0e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7314","title":"Handler Runner internal failures","createdAt":"2023-07-28T18:38:07Z"}
{"state":"Merged","mergedAt":"2023-07-28T21:50:23Z","number":7315,"mergeCommitSha":"673acf0165b3f05217628853067e4c71fab142ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7315","title":"Errors were being swallowed and messages were hanging","createdAt":"2023-07-28T19:10:45Z"}
{"state":"Merged","mergedAt":"2023-07-28T19:56:29Z","number":7316,"body":"Motivation:\nhttps://chapter2global.slack.com/archives/C05D7H25PV5/p1690568418547219?thread_ts=1690561522.141289&cid=C05D7H25PV5","mergeCommitSha":"85df0eaa02f8c078c7069870f59f40450986b3a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7316","title":"Embed source file name as well as path to reinforce names in lookup","createdAt":"2023-07-28T19:34:23Z"}
{"state":"Merged","mergedAt":"2023-07-28T20:20:54Z","number":7317,"body":"Remove hooks index","mergeCommitSha":"3584ed599746528cf1f436fb9ced71330aa07077","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7317","title":"Remove hooks index","createdAt":"2023-07-28T19:53:46Z"}
{"state":"Merged","mergedAt":"2023-07-28T23:48:16Z","number":7318,"body":"Admin web Person page query incorrectly included memberships of non-SCM teams\r\n","mergeCommitSha":"9bf0160b74e1d2ca9d03a0e52fca11cfcdc8b788","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7318","title":"Admin web Person page query incorrectly included memberships of non-SCM teams","createdAt":"2023-07-28T20:27:04Z"}
{"state":"Merged","mergedAt":"2023-07-28T20:49:09Z","number":7319,"mergeCommitSha":"1f2f0107c50b434c9246f8bbc1b730b72d715f7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7319","title":"Remove web hook index","createdAt":"2023-07-28T20:40:11Z"}
{"state":"Merged","mergedAt":"2022-03-31T21:00:31Z","number":732,"body":"* On startup, if a sourcemark agent (SMA) is not running, VSCode will launch it.  The launch code right now runs the java app which is pretty hokey, this will improve once SMA is a native binary\r\n* On startup, if a SMA is running, VSCode will attempt to connect to it\r\n* Every second, VSCode will send a heartbeat to the SMA\r\n* If the SMA is dead (pid no longer exists, or heartbeat fails), VSCode will re-launch the SMA\r\n\r\nThere is some trickiness around launching the SMA.  When VSCode starts up, it could open a bunch of workspaces, each of which has a separate extension instance.  We don't want each extension instance to launch a separate SMA, so we use a lockfile.  The lockfile contains a json object like so:\r\n\r\n```\r\n{\r\n  pid: <SMA pid>\r\n  port: <SMA port>\r\n}\r\n```\r\n\r\nExtensions lock the file to get exclusive read/write access, so only one extension instance will attempt to launch the SMA at a time.\r\n\r\nThis thing is going to be a nightmare to write tests for -- I'm thinking through how it can be unit tested.","mergeCommitSha":"719886c633fd59a63aba5cef18a6e6feed986934","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/732","title":"Source mark agent monitor","createdAt":"2022-03-31T00:18:21Z"}
{"state":"Merged","mergedAt":"2023-07-28T22:00:16Z","number":7320,"mergeCommitSha":"7ec26f2b34c8147d72f9349a725956a099bbd79f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7320","title":"Remove web auth","createdAt":"2023-07-28T21:52:22Z"}
{"state":"Merged","mergedAt":"2023-07-28T22:35:21Z","number":7321,"mergeCommitSha":"474810af4463b72e6b7763f9926d87351edd606e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7321","title":"Looks like a bug where drop == false is not enough to sample the bugger","createdAt":"2023-07-28T22:35:16Z"}
{"state":"Merged","mergedAt":"2023-07-29T01:38:37Z","number":7322,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/9a324b2b-73c9-4478-a6d6-b37225d007da\r\n\r\n","mergeCommitSha":"f7dbf561bb7c3a932158812022de5b09af5a7c2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7322","title":"Add landing page mobile navigator ","createdAt":"2023-07-28T23:34:40Z"}
{"state":"Merged","mergedAt":"2023-07-31T21:30:35Z","number":7323,"body":"**Footer Styling:**\r\n\r\n<img width=\"1311\" alt=\"CleanShot 2023-07-28 at 16 49 34@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/bec889f9-7999-4e16-bc98-13ac34775f91\">\r\n\r\n\r\n**Blog Post Styling:**\r\n\r\n@kaych I think we'll need an alternate header for the blog. Just plain white without the anchor links in the middle.\r\n\r\n<img width=\"1668\" alt=\"CleanShot 2023-07-28 at 16 50 13@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13353189/00419f49-c2e2-4081-93a9-3369f0f361c8\">\r\n","mergeCommitSha":"f73735f80075d648615f4ed7a8db0bf8b647d188","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7323","title":"Landing page footer and blog","createdAt":"2023-07-28T23:51:24Z"}
{"state":"Merged","mergedAt":"2023-07-31T20:24:42Z","number":7324,"body":"- Move `AutoPlayVideo` into the landing page project as it's only used there and the styling otherwise is awkward\r\n- Add replay button, displayed 1 second after the video ends\r\n\r\n<img width=\"1710\" alt=\"Screenshot 2023-07-31 at 9 45 56 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/3ba5f27d-6a2f-466c-bd52-19ab9eb2aef3\">\r\n","mergeCommitSha":"d5bbe7ef77e01fb7d66299a9c48d3f3bb7972d4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7324","title":"Landing page replay button","createdAt":"2023-07-28T23:53:59Z"}
{"state":"Merged","mergedAt":"2023-07-31T06:19:37Z","number":7325,"mergeCommitSha":"5c953ee1c82077982a31a89ee2ed5f991aca6645","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7325","title":"Renew locks during bulk pull request ingestion","createdAt":"2023-07-31T05:36:42Z"}
{"state":"Merged","mergedAt":"2023-07-31T08:36:51Z","number":7326,"body":"- Enabled I/O optimized configuration\r\n- Upgraded our instance type in prod to memory optimized instances. They are more suitable for large queries and data science type applications such as our AI work\r\n- Enabled rolling upgrades.\r\n- Updated DB config in DR regions to match primary region configuration","mergeCommitSha":"e5ce0e86af16db07347ffae1c52eee09ded0d71e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7326","title":"- Major and minor Postgres engine upgrade from 14.3 to 15.2","createdAt":"2023-07-31T08:35:37Z"}
{"state":"Merged","mergedAt":"2023-07-31T18:17:07Z","number":7327,"mergeCommitSha":"03d2a97ecdef1abb144ac58d0ee8e19f07e5feb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7327","title":"Remove usage of shared stores index.ts","createdAt":"2023-07-31T17:13:46Z"}
{"state":"Merged","mergedAt":"2023-07-31T18:11:05Z","number":7328,"body":"There were too many stacked scrolling elements which led to multiple scroll bars in mobile view.\r\n\r\n* Get rid of the scrolling on the `body` element; we didn't need this anymore for either landing or dashboard\r\n* Turn off the scrolling on the `landing` element if viewing `home` since it has its own scrolling behaviour\r\n* Fix videos not playing in mobile\r\n* Fix other responsive bits of UI ","mergeCommitSha":"f4861b266873571e5983d5eabe104da9666243a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7328","title":"Fix scrolling issues in landing page","createdAt":"2023-07-31T17:53:23Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:39:46Z","number":7329,"body":"This is the oldest one we actually can run on right now.  We use newer APIs that older builds do not support, and use a newer version of java.\r\n\r\nWe can look into supporting older versions if we think it's worthwhile, but at least this means we shouldn't accidentally try to run on older IntelliJs.","mergeCommitSha":"ad60ff1d0dff2a850bc326a9117257a02a978b53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7329","title":"Set correct oldest-supported JB version","createdAt":"2023-07-31T18:38:49Z"}
{"state":"Merged","mergedAt":"2022-03-31T01:19:33Z","number":733,"mergeCommitSha":"63c8d691e121e6e0cc8e09f78c9084e3f5c4712a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/733","title":"clean ecr repo every hour","createdAt":"2022-03-31T01:16:42Z"}
{"state":"Merged","mergedAt":"2023-07-31T20:44:45Z","number":7330,"mergeCommitSha":"81a30b8ad037196f8c2e07317590a782fbb7216f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7330","title":"Remove shared IDE Stores","createdAt":"2023-07-31T18:39:15Z"}
{"state":"Merged","mergedAt":"2023-07-31T18:48:23Z","number":7331,"mergeCommitSha":"0352dae9ff4bcda1b50feec4680e48850a83fb36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7331","title":"Increaase polling rate","createdAt":"2023-07-31T18:48:15Z"}
{"state":"Merged","mergedAt":"2023-07-31T19:59:28Z","number":7332,"body":"- First set of slack bot changes\n- Add at mention functionality\n- More at mention changes\n- Update\n- Fix imports\n- Fix at mention bug\n","mergeCommitSha":"00f20cc9848a6c24c5603491fca75103fb8b7e96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7332","title":"Add at mention functionality to slack bot and ensure @ mention is required for in-thread queries","createdAt":"2023-07-31T19:47:18Z"}
{"state":"Merged","mergedAt":"2023-07-31T20:47:57Z","number":7333,"body":"The fix is a bit of a hack but I'm not sure there's a better way to do this.  This seems to possibly be a bug in Safari.","mergeCommitSha":"3cc1cec4837aa8b37604c62abc440bd70d17bdeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7333","title":"Don't show a sub-pixel border on videos in landing page","createdAt":"2023-07-31T20:38:51Z"}
{"state":"Merged","mergedAt":"2023-07-31T20:57:53Z","number":7334,"mergeCommitSha":"04274fab197e4daa8da6341889029e8d8d92ece8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7334","title":"Fix slack feedback","createdAt":"2023-07-31T20:42:58Z"}
{"state":"Merged","mergedAt":"2023-07-31T21:11:47Z","number":7335,"mergeCommitSha":"59ab63b94c9f91ee19e3130cf9913e5edbeed244","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7335","title":"Fix text in dialog","createdAt":"2023-07-31T21:11:31Z"}
{"state":"Closed","mergedAt":null,"number":7336,"body":"?","mergeCommitSha":"2fa8f4641d9e82c5f92fcfd5f8ec54524141627c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7336","title":"TS extension methods","createdAt":"2023-07-31T21:23:52Z"}
{"state":"Merged","mergedAt":"2023-08-01T20:22:35Z","number":7337,"body":"There's no common framework for these two routes -- @kaych should this be deduped with a common navigator or something?","mergeCommitSha":"f758162f9fa585c693b774f405f900f415d14aad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7337","title":"Add intercom to landing page and blog","createdAt":"2023-07-31T21:59:49Z"}
{"state":"Merged","mergedAt":"2023-07-31T23:03:15Z","number":7338,"mergeCommitSha":"ff36e6f5e402532b371de1323c2ad95614f0e374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7338","title":"Include provider when deleting an installation by external ID","createdAt":"2023-07-31T22:27:45Z"}
{"state":"Merged","mergedAt":"2023-08-01T00:32:02Z","number":7339,"body":"* Update buttons to point to dashboard\r\n* Simplify scrolling logic\r\n* Center the nav menu links \r\n* Point landing to dev environment for parity","mergeCommitSha":"ebf2e93bafa61639bf0f435f52a37d5e2c877ac4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7339","title":"More landing page work","createdAt":"2023-07-31T22:52:51Z"}
{"state":"Merged","mergedAt":"2022-04-01T21:20:29Z","number":734,"body":"Lot of plumbing to setup web extension discussions.\r\n\r\n<img width=\"728\" alt=\"CleanShot 2022-03-30 at 21 33 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/160977245-7c76a8cd-1fae-4a08-8d31-ce81e7cb1315.png\">\r\n\r\n\r\nWill be subsequent PRs to cleanup styling.\r\nRefactor components.","mergeCommitSha":"c2c39e80ffbe8a42f8546d1738e7e24652dde675","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/734","title":"Extension Discussions","createdAt":"2022-03-31T04:34:52Z"}
{"state":"Merged","mergedAt":"2023-08-03T16:53:05Z","number":7340,"body":"Removes shared component index ","mergeCommitSha":"9675b6fae94cd3417d57268f3c9098bded4af4a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7340","title":"Remove shared components index","createdAt":"2023-07-31T23:15:33Z"}
{"state":"Merged","mergedAt":"2023-08-01T02:55:12Z","number":7341,"body":"For our confluence OAuth apps","mergeCommitSha":"8f28d4c4bf114d8d59df3d49f724b787811f914d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7341","title":"Add CONFLUENCE_CLIENT_SECRET","createdAt":"2023-07-31T23:16:45Z"}
{"state":"Merged","mergedAt":"2023-08-01T16:30:37Z","number":7342,"body":"Cleaner console output on startup.","mergeCommitSha":"512fc40c1a5c520e19cce2bb4a72c183f0de08aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7342","title":"Remove runtime react warnings","createdAt":"2023-08-01T00:18:39Z"}
{"state":"Merged","mergedAt":"2023-08-01T14:41:28Z","number":7343,"body":"### Problem\r\nThe core problem is data synchronization between client and server. When a user creates, or archives a sourcemark thread, then a 30 second overlay will appear to fulfil the users' request. However, we do not currently sync mark associated with open files, and we cache the state for a long time (15 mins).\r\n\r\n### Changes\r\n1. reduce case from 15 mins to 25 seconds\r\n2. filter out archived marks from the store\r\n\r\n### Limitation\r\nUnfortunately, this does not address the problem while the file is open. When the user closes and reopens the file after some time (25 secs) has passed then we will refetch and pull in fresh marks. We'll need to build an efficient file-to-mark poller to get this working.","mergeCommitSha":"474f6112f7ec671473d361427ab98ef0c0a3beaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7343","title":"Filter out archived marks from IDE","createdAt":"2023-08-01T04:10:47Z"}
{"state":"Merged","mergedAt":"2023-08-01T14:42:46Z","number":7344,"body":"No behaviour change.","mergeCommitSha":"a47ccc698cf0a350efd8c1571834105d96de6dc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7344","title":"Refactor to move stream functionality to SourceMarkScheduler","createdAt":"2023-08-01T04:55:28Z"}
{"state":"Merged","mergedAt":"2023-08-01T14:43:36Z","number":7345,"body":"Also, `findRepoSourceMarksByFile` must return archived marks in order to invalidate the client-side cache (called \"store\"), which may have the mark in a non-archived state.\r\n\r\nFixes limitation raised in #7343.","mergeCommitSha":"97f8cb6d032448f642cb0797fe8f69796d9223af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7345","title":"Continuously poll for server changes to sourcemarks for visible files","createdAt":"2023-08-01T05:15:49Z"}
{"state":"Merged","mergedAt":"2023-08-01T14:52:30Z","number":7346,"mergeCommitSha":"a012d7fd21a2f98bbe938b4b345a23101c935759","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7346","title":"Add confluence modules","createdAt":"2023-08-01T07:52:16Z"}
{"state":"Merged","mergedAt":"2023-08-01T21:22:33Z","number":7347,"mergeCommitSha":"93c8e37bbccb1b48457c391df30d04c48a4800fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7347","title":"Add Confluence provider","createdAt":"2023-08-01T15:21:46Z"}
{"state":"Merged","mergedAt":"2023-08-01T16:10:28Z","number":7348,"mergeCommitSha":"bb9b04bd174e5b99cae5dd9dd7a680df573e4218","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7348","title":"Possible crash fix for upgrade-while-in-background","createdAt":"2023-08-01T15:55:18Z"}
{"state":"Merged","mergedAt":"2023-08-01T19:02:59Z","number":7349,"body":"This is an optional object that allows the IDE to provide context for thread creation","mergeCommitSha":"b806547cc6df3bf02bf2a5452de5e24cb04d63e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7349","title":"Add context values to thread creation API","createdAt":"2023-08-01T17:38:48Z"}
{"state":"Merged","mergedAt":"2022-03-31T16:00:14Z","number":735,"body":"Office wifi outage. New IP. Update.","mergeCommitSha":"1f1a8c65e27e73b7c72a6437208d037e18853bf3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/735","title":"Update IP","createdAt":"2022-03-31T15:55:38Z"}
{"state":"Merged","mergedAt":"2023-08-03T22:33:40Z","number":7350,"body":"When launching the PRView for a specific thread, we should open the relevant file editor when possible.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/ce2b3b27-3aeb-416d-9ac5-d461699df92e\r\n\r\n","mergeCommitSha":"48ae01c9189ff9ba9c6e6ec83a1a1f6a4420ef9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7350","title":"Open file for target thread in pr","createdAt":"2023-08-01T18:57:30Z"}
{"state":"Merged","mergedAt":"2023-08-01T20:22:57Z","number":7351,"body":"Do not go to llm to determine if a question, if we can just ping pinecone to see if there's any relevant document in the mix.","mergeCommitSha":"1e976f180d3b897217efdec50d9e50602c3a7ba9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7351","title":"Add relevant question check","createdAt":"2023-08-01T19:10:00Z"}
{"state":"Merged","mergedAt":"2023-08-01T19:27:33Z","number":7352,"body":"Reducing the scale of these deployments to help with excessive load on the DB.","mergeCommitSha":"c61fbbe5986b9b8115fa778110fa2a8e100c7673","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7352","title":"reduce max scale for data and topic services","createdAt":"2023-08-01T19:19:20Z"}
{"state":"Merged","mergedAt":"2023-08-01T22:16:15Z","number":7353,"body":"We weren't passing the `currentTeamMember` through the view chain correctly... but we don't even need to do this, we have a context that always has the current Person object.","mergeCommitSha":"50acbd0b4f1573f36ff74ccf9598816982716588","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7353","title":"@mention bot in IDE correctly","createdAt":"2023-08-01T19:44:57Z"}
{"state":"Merged","mergedAt":"2023-08-01T22:01:07Z","number":7354,"body":"Open references from the client.\r\n\r\nIssue with RenderWebview types","mergeCommitSha":"5dbcbfcf0a6fe67bb369b49d28695384ddecc256","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7354","title":"Fix client references","createdAt":"2023-08-01T21:25:09Z"}
{"state":"Merged","mergedAt":"2023-08-01T23:12:01Z","number":7355,"body":"https://chapter2global.slack.com/archives/C02VCS8L4R5/p1690926505481149?thread_ts=1690907016.344209&cid=C02VCS8L4R5","mergeCommitSha":"4409fd3c6f80028e24105554c639650ce584b9dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7355","title":"Fix findInstallationsAndRepos bug","createdAt":"2023-08-01T22:02:48Z"}
{"state":"Merged","mergedAt":"2023-08-01T22:49:28Z","number":7356,"mergeCommitSha":"de383fdfed0100cc7be0d446d17ac0cd3b32f25f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7356","title":"Add Confluence pages api","createdAt":"2023-08-01T22:16:50Z"}
{"state":"Merged","mergedAt":"2023-08-01T23:30:43Z","number":7357,"body":"All IDEs will use hybrid sourcemark engine by default.\n\nSourcemark headless cloud agent must continue to use non-hybrid mode.","mergeCommitSha":"5b2d7e01dc5f9259ef0dcf276b0801b5767fcfea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7357","title":"Enable hybridSourcemarkEngine by default","createdAt":"2023-08-01T22:24:42Z"}
{"state":"Merged","mergedAt":"2023-08-02T18:09:14Z","number":7358,"body":"\r\nHidden for web\r\n\r\n<img width=\"823\" alt=\"CleanShot 2023-08-01 at 16 18 45@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/83646aea-e12a-4387-aeaa-4db05abdd434\">\r\n<img width=\"780\" alt=\"CleanShot 2023-08-01 at 16 18 38@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/fb06a97c-959d-4df8-8bbf-eb5bc52d39c0\">\r\n","mergeCommitSha":"c1911e9fd69a0e53ca2c6d5da81727f351456292","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7358","title":"Updated ide references","createdAt":"2023-08-01T23:19:17Z"}
{"state":"Merged","mergedAt":"2023-08-02T01:21:31Z","number":7359,"body":"If you're wondering why I'm doing what I'm doing, the `Link` header looks like:\r\n\r\n```\r\nLink: </wiki/api/v2/pages?body-format=storage&limit=2&cursor=eyJpZCI6IjMzMjQ0IiwiY29udGVudE9yZGVyIjoiaWQiLCJjb250ZW50T3JkZXJWYWx1ZSI6MzMyNDR9>; rel=\"next\"\r\n```\r\n\r\nYou're not misreading: that is a partial uri, where the initial one looks like:\r\n\r\n```\r\nhttps://api.atlassian.com/ex/confluence/************************4c94efc9b85f/wiki/api/v2/pages?body-format=storage&limit=2\r\n```\r\n\r\nSo `Link` is relative to `https://api.atlassian.com/ex/confluence/************************4c94efc9b85f/`","mergeCommitSha":"80c544277042ac0571ab47e5f444c5d86ca48dfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7359","title":"Add streaming for Confluence pages","createdAt":"2023-08-01T23:54:08Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:59:53Z","number":736,"mergeCommitSha":"a45fe59b6e72bcc3fb87fcfe34bd5baf56e31aeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/736","title":"Video background service scaffolding","createdAt":"2022-03-31T17:29:19Z"}
{"state":"Merged","mergedAt":"2023-08-02T00:17:22Z","number":7360,"mergeCommitSha":"d8c3bcd6b336bd7a93cf862eedb55887a79723d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7360","title":"Remove sendinblue","createdAt":"2023-08-02T00:17:16Z"}
{"state":"Merged","mergedAt":"2023-08-02T18:35:37Z","number":7361,"body":"This was just kind of stupid.  Safari lets you style `::placeholder` with ellipsis, but embedded chrome (which VSCode / IntelliJ use) does not.  So `TextArea` now has a new property that uses a custom overlay for the placeholder, which does clip correctly.\r\n\r\n<img width=\"1582\" alt=\"Screenshot 2023-08-01 at 8 04 34 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/544c1ccb-4130-469e-b28f-9b2a87ca7711\">\r\n","mergeCommitSha":"dd6aee21a1ccb5d3459a8c6a84a1198154d2c3f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7361","title":"Clip with ellipsis in VSCode search input","createdAt":"2023-08-02T03:04:47Z"}
{"state":"Merged","mergedAt":"2023-08-02T18:35:48Z","number":7362,"mergeCommitSha":"9cdc0b124d801b69177bb392eb4dcf7e562f004f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7362","title":"Supply repos when making QA threads","createdAt":"2023-08-02T03:06:42Z"}
{"state":"Merged","mergedAt":"2023-08-02T19:20:05Z","number":7363,"body":"<img width=\"1582\" alt=\"Screenshot 2023-08-01 at 8 24 38 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/75a0aa8e-4528-44ac-abd6-6336611401a6\">\r\n","mergeCommitSha":"92f8f216706bab829ca80e3df5f61a4fbca96f7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7363","title":"Show workspace name in search bar","createdAt":"2023-08-02T03:24:46Z"}
{"state":"Merged","mergedAt":"2023-08-02T19:29:05Z","number":7364,"mergeCommitSha":"57cb9e5ce2cbe94d8e7d6932da7e347a792a6cfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7364","title":"Source code language detection","createdAt":"2023-08-02T03:48:51Z"}
{"state":"Merged","mergedAt":"2023-08-02T19:37:45Z","number":7365,"mergeCommitSha":"c4565e178f6d9bea48d1a7ba252e7054d343aefc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7365","title":"Install llama-index dependency to support code splitting","createdAt":"2023-08-02T03:48:54Z"}
{"state":"Merged","mergedAt":"2023-08-02T17:00:24Z","number":7366,"body":"<img width=\"2560\" alt=\"CleanShot 2023-08-01 at 22 44 22@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/e688e921-3c2d-438c-a30d-67af9eff1f30\">\r\n","mergeCommitSha":"7856eb2b52e24b917cc5f25e0c6cee5e8a2dfbae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7366","title":"[Admin Console] Show connected integrations on teams page","createdAt":"2023-08-02T05:45:35Z"}
{"state":"Merged","mergedAt":"2023-08-02T16:41:55Z","number":7367,"body":"The JB plugin crashes in IntelliJ 2023.2.  The reason is unclear (it's dying in the guts of JCEF).  The code we're calling isn't even needed right now, so I'm commenting it out in the hopes it fixes it for now, and gives us time to dig deeper.\r\n\r\nAnother strange thing is that running our plugin in debug works fine.  Prod builds don't.","mergeCommitSha":"db3e5ac30159d4b89f71b2979e7a1a47e7f0a453","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7367","title":"Maybe fix JB plugin","createdAt":"2023-08-02T16:12:07Z"}
{"state":"Merged","mergedAt":"2023-08-02T22:30:53Z","number":7368,"body":"### Algorithm\r\n1. identify language of file\r\n2. try to partition file using language-specific AST (tree-sitter based CodeSplitter from llama-index)\r\n3. fallback to simple SentenceSplit which cuts but newline (only really works well for Python, pretty rubbish for other languages)","mergeCommitSha":"9d0317f1b4f2beb73b1356f99878e3c49cfdc457","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7368","title":"Source code partitioning","createdAt":"2023-08-02T16:18:57Z"}
{"state":"Merged","mergedAt":"2023-08-02T23:18:15Z","number":7369,"body":"Also increase max file size from 20 KiB to 200 KiB, meaning that we will create up to 100 separate embeddings for the largest files.","mergeCommitSha":"32fbaf9a0d888e0762ff28547475c3581ac89e9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7369","title":"Use partitioning for source code embedding","createdAt":"2023-08-02T16:19:00Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:01:30Z","number":737,"body":"Validated synth worked\r\n","mergeCommitSha":"fc6a7c6f6922a295a4c8a49b16a0d54fda0a6384","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/737","title":"Cleanup cdk","createdAt":"2022-03-31T17:54:54Z"}
{"state":"Merged","mergedAt":"2023-08-02T17:01:56Z","number":7370,"mergeCommitSha":"18b598d2e821d5d24fcee365bf61528766f76966","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7370","title":"Add new feature flag for repo context in semantic search","createdAt":"2023-08-02T16:50:43Z"}
{"state":"Merged","mergedAt":"2023-08-02T17:46:09Z","number":7371,"mergeCommitSha":"5cc9c5df482b6dbb39bd0452a7b5c4c0e53cda9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7371","title":"Add text filter to bot","createdAt":"2023-08-02T17:44:56Z"}
{"state":"Merged","mergedAt":"2023-08-02T17:50:32Z","number":7372,"mergeCommitSha":"1444031fd568dbbedef89d33d314bca06f3c1410","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7372","title":"Update pinecone filter","createdAt":"2023-08-02T17:50:26Z"}
{"state":"Merged","mergedAt":"2023-08-02T18:35:21Z","number":7373,"mergeCommitSha":"69ff32889985fb16510dedab7535a7e489bd454e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7373","title":"Increase saving gradle cache timeout","createdAt":"2023-08-02T18:35:16Z"}
{"state":"Merged","mergedAt":"2023-08-02T18:56:32Z","number":7374,"mergeCommitSha":"4394cfed7803780d191e8c902a4782b2d7c15ff8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7374","title":"Update","createdAt":"2023-08-02T18:56:26Z"}
{"state":"Merged","mergedAt":"2023-08-02T19:36:20Z","number":7375,"mergeCommitSha":"226daa60b952ae473d9d8f8d856929d6025eeacc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7375","title":"Disable gradle cache","createdAt":"2023-08-02T19:36:14Z"}
{"state":"Merged","mergedAt":"2023-08-15T18:51:15Z","number":7376,"body":"Fixes issues with onboarding new repos for organizations that have the GH app already installed.\r\n\r\nInstallationStore was originally written when polling for installations (through `findInstallationsAndRepos`) was too expensive. We worked around that by polling for teams as a proxy for installations but that was flawed as we wouldn't catch situations where teams didn't change but the `reposInstalled` within the installation changed.\r\n\r\nWe've since updated installation request to be cached so it is now safe to poll for installations directly.\r\n","mergeCommitSha":"57c88def604e65a8bdb46c890d8d92d226414830","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7376","title":"Fix installations for onboarded organizations","createdAt":"2023-08-02T19:49:42Z"}
{"state":"Merged","mergedAt":"2023-08-02T19:57:54Z","number":7377,"body":"### Workarounds\n- Install build tools (gcc) to build from source if necessary\n- Ensure latest version of pip\n- Install tree-sitter-languages using pip\n\n### Problem\nFails locally in docker for mac with:\n\n```\n => [1/9] FROM docker.io/library/python:3.10-slim-bullseye@sha256:d707899eabc86ba1047d6fa002c24c62e37bb6047e470075cc593284e8533884\n => CACHED [2/9] RUN apt-get update   && apt-get clean    && apt-get autoclean   && apt-get autoremove\n => CACHED [3/9] RUN apt-get install -y git\n => CACHED [4/9] RUN apt-get install -y build-essential\n => CACHED [5/9] RUN pip3 install --upgrade pip\n => ERROR [6/9] RUN pip3 install tree-sitter-languages==1.7.0\n------\n > [6/9] RUN pip3 install tree-sitter-languages==1.7.0:\n#0 0.466 ERROR: Could not find a version that satisfies the requirement tree-sitter-languages==1.7.0 (from versions: none)\n#0 0.466 ERROR: No matching distribution found for tree-sitter-languages==1.7.0\n------\nfailed to solve: process \"/bin/sh -c pip3 install tree-sitter-languages==1.7.0\" did not complete successfully: exit code: 1\n```","mergeCommitSha":"eeb570a1240236ec0db674a0e2ba4daee4ac0b3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7377","title":"Try to workaround tree-sitter install in docker:linux","createdAt":"2023-08-02T19:56:26Z"}
{"state":"Merged","mergedAt":"2023-08-03T00:28:02Z","number":7379,"mergeCommitSha":"5e7bd0db04aea251058d67711c5ceda0f09dcb81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7379","title":"try gradle cache fix 2","createdAt":"2023-08-02T20:44:26Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:12:21Z","number":738,"body":"- Cleanup cdk\r\n- Revert \"Cleanup cdk\"\r\n","mergeCommitSha":"0ae28ec033983e39629448a7823bc9ae072a0419","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/738","title":"Revert","createdAt":"2022-03-31T18:12:16Z"}
{"state":"Merged","mergedAt":"2023-08-02T21:45:30Z","number":7380,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/748682bf-2c13-4b02-85d7-eaccba540b2b\r\n\r\n* Also some mobile view optimizations (fix scroll, feedback buttons,  insight icon sizing)","mergeCommitSha":"736adea0de3e2a806c4d292d47e18946d9b0f150","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7380","title":"Add QA input to mobile dashboard","createdAt":"2023-08-02T20:47:18Z"}
{"state":"Merged","mergedAt":"2023-08-03T17:45:06Z","number":7381,"body":"* Open PR views for thread summary views\r\n* Add empty view to recommended UI\r\n* Add responsive support for message feedback buttons in IDEs\r\n* Fix some ellipsing issues","mergeCommitSha":"813f57a6409ddd0853c8dd48f27631d412f6f31e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7381","title":"Threads open PR view where possible","createdAt":"2023-08-02T21:28:08Z"}
{"state":"Merged","mergedAt":"2023-08-02T22:02:20Z","number":7382,"body":"To allow sync jobs that run after the initial ingest to grab just recently modified pages.","mergeCommitSha":"207f1a7b2d3293ad3e7eb8bf14a5209e3fa15641","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7382","title":"Stream confluence pages in modified-at descending order","createdAt":"2023-08-02T21:28:50Z"}
{"state":"Merged","mergedAt":"2023-08-03T16:02:50Z","number":7383,"body":"This was a race condition on startup:\r\n\r\n* The agent has a `ValueStream` that is used to send view updates to the plugin\r\n* On startup, this is sometimes not connected to immediately, but we want to update multiple views.\r\n* `ValueStream` only ever caches the last value, so the previous view updates would be lost\r\n\r\nThis adds a new option to `ValueStream` that queues pending values while there are no subscribers.  This is appropriate if you want a value stream that behaves more like a traditional queue.","mergeCommitSha":"403d73e820b475434511e2e7c782df8ef7ef2637","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7383","title":"Fix bug where JB IDE would only show 'Discussions' tab","createdAt":"2023-08-02T21:37:07Z"}
{"state":"Merged","mergedAt":"2023-08-02T23:27:02Z","number":7384,"mergeCommitSha":"f58185c794e50eeabf89ad850c89da6e687f225e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7384","title":"Missed one button","createdAt":"2023-08-02T21:44:22Z"}
{"state":"Merged","mergedAt":"2023-08-02T21:52:37Z","number":7385,"body":"### More hacks for python ecosystem\nNone of this shit makes any sense, but whatever...\n\n1. Install `langchain` using pip -- no idea why poetry doesn't work\n2. Install `torch` using pip -- same\n\n### Fixes\n\n1. Force linux/amd64 so that docker environment on mac is consistent with production\n2. No need to update pip, so removed\n3. Poetry install was inadvertently running non-incrementallty so every docker build\n   was a full slow re-install. Now we explicitly COPY the lock file to ensure that\n   poetry install only runs when the lock file changes.\n4. Invoke as `poetry run python` instead of `python` so ensure that we run in the\n   correct environment.\n5. Fix local non-docker execution.","mergeCommitSha":"d5b035a5434dcd007d9ec8c21ca5627743fe7e5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7385","title":"Cleanup source code docker / makefile","createdAt":"2023-08-02T21:49:35Z"}
{"state":"Merged","mergedAt":"2023-08-02T23:27:20Z","number":7386,"mergeCommitSha":"b0b64a27001dfabcc68ddb5ebcc696b7c9c3768b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7386","title":"Create confluence ingestion job","createdAt":"2023-08-02T22:40:41Z"}
{"state":"Merged","mergedAt":"2023-08-03T17:09:59Z","number":7387,"mergeCommitSha":"b53dfc829501e31e770ec5aac20e07b86329e81f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7387","title":"Update Confluence OAuth scopes","createdAt":"2023-08-02T23:38:09Z"}
{"state":"Merged","mergedAt":"2023-08-03T15:18:48Z","number":7388,"body":"We use a custom element for this -- have to apply the inverted theme to that custom element","mergeCommitSha":"37b0885cb24b0a83819c7b586e7d6ba2f22ada08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7388","title":"Fix JB search input placeholder style","createdAt":"2023-08-02T23:46:10Z"}
{"state":"Merged","mergedAt":"2023-08-03T00:10:50Z","number":7389,"body":"Not actually a problem in most cases:\n\n```js\n{\n  context: 'SourceMarkScheduler',\n  service: 'vscode',\n  environment: 'local',\n  type: 'nodejs',\n  process: 'extension',\n  teamId: '9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361',\n  repoId: '7fce6ebb-5989-48e7-9af0-fff594769a8a',\n  repoRootPath: '/Users/<USER>/work/NextChapterSoftware/unblocked',\n  pid: 42950,\n  file: FilePath {\n    value: 'shared/node/sourcemark/engine/SourceMarkStore.ts'\n  },\n  level: 'error',\n  message: 'getFileRelativeToRepo: expected file to be absolute',\n  timestamp: '2023-08-02T23:47:43.766Z'\n}\n```\n\nThis change addresses the spurious error log message.","mergeCommitSha":"23b740191f1c3fbef7fde9c0fa820e127d184700","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7389","title":"Fix harmless spurious sourcemark error","createdAt":"2023-08-02T23:57:39Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:15:32Z","number":739,"body":"- Revert \"Cleanup cdk (#737)\"\r\n- try again\r\n","mergeCommitSha":"6a58ce1774532d708ae35d8fe4a808cd4bd16757","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/739","title":"Fix again","createdAt":"2022-03-31T18:15:25Z"}
{"state":"Merged","mergedAt":"2023-08-03T15:51:39Z","number":7390,"body":"The API here is legitimately confusing -- you can either stream the value, or use the context.  We should probably fix this so we just use the stream everywhere, but that will take some work to work correctly in webviews.\r\n\r\nThis only affects the semantic search placeholder label.","mergeCommitSha":"abe80c694b365896b343f322003851724f4edb94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7390","title":"Check semantic search flag correctly","createdAt":"2023-08-03T00:04:00Z"}
{"state":"Merged","mergedAt":"2023-08-03T21:24:14Z","number":7391,"body":"I am going to have an adult engineer look at this and then do something like:\r\n\r\n1. add lib-lamini for the lamini client\r\n2. add lib-statsig for the statsig client\r\n3. test this in dev","mergeCommitSha":"3fae4a96672f9f7fb506d73e3bee1f7a2a325fc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7391","title":"Statsig+Lamini+Pythia-2.8B-Deduped topic classifier","createdAt":"2023-08-03T00:08:54Z"}
{"state":"Merged","mergedAt":"2023-08-03T01:31:43Z","number":7392,"body":"- try gradle cache fix 2\r\n- Add pgtrm indices to optimize string matching for fuzzy string matching\r\n","mergeCommitSha":"2c23ff5c48ba48ceeb528dc2a758a62177eece45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7392","title":"AddAbilityToGenerateFastStringIndexingInPostgres","createdAt":"2023-08-03T00:24:31Z"}
{"state":"Merged","mergedAt":"2023-08-03T18:04:26Z","number":7393,"body":"Basic search (currently using LIKE operator using trigram indices, but will be moving to postgres fuzzy matching operators with sorted scoring)\r\nThe\\ before/after crap for pagination will be implemented in another pr.","mergeCommitSha":"304f3b464a81890aeb6b363682e9c7e54cc1934b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7393","title":"Add slack channels search support","createdAt":"2023-08-03T03:04:19Z"}
{"state":"Merged","mergedAt":"2023-08-03T05:39:18Z","number":7394,"body":"Exclude extensions that are commonly associated with files that can contain private keys.\n\nNote for future:\n- A more rigorous approach should include filename matching and content pattern matching.","mergeCommitSha":"fc4490a04c7c418b9f70c583a5e30a2ef8acd10a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7394","title":"Do not ingest source code files that may contain private keys","createdAt":"2023-08-03T05:31:58Z"}
{"state":"Merged","mergedAt":"2023-08-03T05:40:01Z","number":7395,"body":"These are usually huge machine-generated files. So we drop them.\n- gem.lock\n- yarn.lock\n- poetry.lock\n- package.lock","mergeCommitSha":"818bd0fa8ce443b6e2e6cbf1475f46757a6704e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7395","title":"Exclude source code lock files","createdAt":"2023-08-03T05:38:33Z"}
{"state":"Merged","mergedAt":"2023-08-03T07:30:16Z","number":7396,"body":"### Problems\r\nSource code ingest is timing out for very large repos (clio), because:\r\n\r\n- we are now embedding far more documents (probably 3X or 4X more)\r\n- there is a small cost to partitioning itself\r\n\r\n### Changes\r\n- extend timeout to 120 mins\r\n- skip partitioning for small files\r\n- skip code splitting for erb files because tree-splitter is crashing out","mergeCommitSha":"d766d3dcee076c21e19d8f4078d072aadad43fdf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7396","title":"Mitigate source code ingestion exceeding 60 min stopping condition","createdAt":"2023-08-03T07:05:12Z"}
{"state":"Merged","mergedAt":"2023-08-03T07:30:58Z","number":7397,"body":"Related to https://linear.app/unblocked/issue/UNB-1417","mergeCommitSha":"36682f60c20387ed80efd2c88d2a90688eefa626","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7397","title":"Tree-sitter-markdown assertions are crashing source ingestion","createdAt":"2023-08-03T07:28:19Z"}
{"state":"Merged","mergedAt":"2023-08-03T16:45:35Z","number":7398,"body":"IDEs were missing dialog stylings\r\n\r\n<img width=\"596\" alt=\"CleanShot 2023-08-03 at 08 40 10@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/36bea3f6-f132-4907-951a-043053841ce5\">\r\n\r\n<img width=\"658\" alt=\"CleanShot 2023-08-03 at 08 39 46@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/7932b431-91e6-4cf1-90f2-de33695c5d6c\">\r\n","mergeCommitSha":"6fa94432dc78f4bcb101dd8aa4d5d2ccbcae32dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7398","title":"Add Styling for multi file dialog in IDEs","createdAt":"2023-08-03T15:42:33Z"}
{"state":"Merged","mergedAt":"2023-08-03T18:11:02Z","number":7399,"mergeCommitSha":"008e595157a420982187310003c3f3b563a05ee2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7399","title":"Create confluence s3 buckets","createdAt":"2023-08-03T18:01:19Z"}
{"state":"Merged","mergedAt":"2022-01-19T05:41:23Z","number":74,"body":"## Preamble\r\n\r\nI got too deep into the implementation and decided to chunk it up, starting with this API definition. I also ripped out the authentication openapi generator templating because it's broken. I'll fix that in a separate PR.\r\n\r\nThe tests are also pretty janky, and will be fixed in subsequent PRs when I wire everything up. There's some refactoring we will need to do soon to wire up configurations and build out some testing utilities for mocks.\r\n\r\n## Summary\r\n\r\nFirstly, this part of the API only deals with the client -> apiservice interactions. The two endpoints are:\r\n\r\n### /login \r\nWill immediately bounce the client with a redirect to the auth provider\r\n\r\n### /login/exchange?state=nonce;code=exchange_code\r\nAuth provider will bounce the client with a redirect back to us, containing the exchange code\r\n\r\n","mergeCommitSha":"c70038cd6258cda18b00af56dca15fa27e924852","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/74","title":"First crack at auth API","createdAt":"2022-01-19T03:47:23Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:26:04Z","number":740,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1648748930523219","mergeCommitSha":"19963960664bba88a39160b35e3a9602bbd10b57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/740","title":"Return all threads if no repos","createdAt":"2022-03-31T18:17:25Z"}
{"state":"Merged","mergedAt":"2023-08-03T20:45:15Z","number":7400,"mergeCommitSha":"e44e89a7ad3a2aabf626b54a155f83d04e5fe4da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7400","title":"Add permissions for the confluence s3 bucket","createdAt":"2023-08-03T18:22:47Z"}
{"state":"Merged","mergedAt":"2023-08-03T21:01:19Z","number":7401,"body":"This will let us ingest confluence blog posts too","mergeCommitSha":"6c7714b8ad3ec1bb63409c5c5f4f99ca3125e09d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7401","title":"Update confluence models","createdAt":"2023-08-03T20:37:05Z"}
{"state":"Merged","mergedAt":"2023-08-04T17:45:32Z","number":7402,"body":"The close button on the feedback tooltip was not working -- closing the tooltip with the X button would not apply the feedback.  The function arguments got mixed up, it was complicated so I moved them into an object args interface instead.","mergeCommitSha":"dc1204b56d2d968ac53372b3a9c1130f70f3d5c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7402","title":"Fix feedback tooltip close button","createdAt":"2023-08-03T21:47:36Z"}
{"state":"Merged","mergedAt":"2023-08-03T22:05:39Z","number":7403,"mergeCommitSha":"735f78ef0efc23b6e1d96db7b69870b661c28294","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7403","title":"Add Confluence spaces api","createdAt":"2023-08-03T21:49:14Z"}
{"state":"Merged","mergedAt":"2023-08-04T17:44:36Z","number":7404,"mergeCommitSha":"a1c5dc43d7352cf697da7ff1c8b66f4dc4f9b643","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7404","title":"Wrap feedback buttons when necessary","createdAt":"2023-08-03T22:05:15Z"}
{"state":"Merged","mergedAt":"2023-08-03T23:25:54Z","number":7405,"body":"- Removes at least one of the python hacks added here\n  https://github.com/NextChapterSoftware/unblocked/pull/7385\n\n- Partitioning will need accurate token counting in order to avoid clipping code in the embedding space. Adding tiktoken for that purpose.","mergeCommitSha":"52187295ab182ef1afec71b3809f4a96353f9485","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7405","title":"Remove langchain docker hack + Add tiktoken dependency for accurate partitioning","createdAt":"2023-08-03T22:06:14Z"}
{"state":"Merged","mergedAt":"2023-08-03T22:08:47Z","number":7406,"mergeCommitSha":"45f67ee444511e1e1e9f74b3021ab8a8ba636a75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7406","title":"Add redis to dataservice","createdAt":"2023-08-03T22:08:33Z"}
{"state":"Merged","mergedAt":"2023-08-03T22:53:02Z","number":7407,"body":"There is now a call dependency between routing and security.\r\n\r\nhttps://stackoverflow.com/questions/********/how-do-you-configure-ktor-to-enable-jwt-authentication\r\n","mergeCommitSha":"c4d1127d04e470f9e60b5b6b6efeb1ddfe448f78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7407","title":"Fix adminweb service","createdAt":"2023-08-03T22:52:54Z"}
{"state":"Merged","mergedAt":"2023-08-04T00:00:45Z","number":7408,"body":"https://chapter2global.slack.com/archives/C040JKLK5MG/p1691095953113939?thread_ts=**********.913389&cid=C040JKLK5MG","mergeCommitSha":"666a7cd92da12686d6a666461dab7f7da0df9442","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7408","title":"Auto release builds to DEV environment","createdAt":"2023-08-03T23:25:29Z"}
{"state":"Merged","mergedAt":"2023-08-04T01:09:06Z","number":7409,"mergeCommitSha":"97714ced31a8aa94b3b65d245bebe70a347e67b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7409","title":"Add ConfluenceSiteModel and ConfluenceSpaceModel","createdAt":"2023-08-03T23:29:25Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:24:24Z","number":741,"mergeCommitSha":"e42e5d1a5a3c6c45dfec32c12aba7bcb0533b1bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/741","title":"Update","createdAt":"2022-03-31T18:24:17Z"}
{"state":"Merged","mergedAt":"2023-08-03T23:50:14Z","number":7410,"body":"## Problem\nThe `clio/themis` repo is still timing out after 2 hours.\n\n## Changes\nSet timeout to 3 hours.\n\n## Proposed future work\nThe correct approach is to make the source code ingestion pipeline incremental. The benefits would be:\n1. source code is no longer cleared during the ingestion run\n1. we can run source code ingestion frequently (hourly vs weekly), which means that the code is not out of date\n1. it runs in minutes instead of hours\n1. we avoid rate Pinecone rate limits\n1. much cheaper. AWS SageMaker is expensive","mergeCommitSha":"47e3465e27d13f5fce995c859b505122e7a208b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7410","title":"Source code ingestion 3 hour stopping condition","createdAt":"2023-08-03T23:40:46Z"}
{"state":"Merged","mergedAt":"2023-08-10T15:16:57Z","number":7411,"body":"SlackConfigurationV2.\r\n\r\nThree changes:\r\n\r\n1. Instead of just Slack channel IDs, we include the entire model. With previous iterations of the UI, we were fetching *all* Slack Channels which allowed clients to match IDs in configuration with full channel list. This is no longer the case as we move towards a search based API and eventually deprecate the getSlackChannels API which is inefficient in large teams.\r\nDB should still only store channel IDs but API service will match IDs and return full Slack Channel models.\r\n\r\n2. Patterns - If there is a Slack channel that contains a pattern, it should be treated as enabled.\r\n\r\n3. botEnabled - With this enabled, Unbot will start replying to slack channels selected. Could potentially be a separate list in the future???\r\n\r\n<img width=\"661\" alt=\"CleanShot 2023-08-03 at 16 50 43@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/43b9caf6-b526-4848-8deb-53cd656b178f\">\r\n","mergeCommitSha":"62b64f2eb9a547c8f8e54f5a1fba8de65b733731","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7411","title":"Introduce getSlackConfigurationV2","createdAt":"2023-08-03T23:50:59Z"}
{"state":"Merged","mergedAt":"2023-08-04T01:47:44Z","number":7412,"mergeCommitSha":"ab1589e0bb16215bb87e45233935e26cd7ca4f27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7412","title":"Repo scoping bot message responses","createdAt":"2023-08-04T00:55:59Z"}
{"state":"Merged","mergedAt":"2023-08-04T18:06:38Z","number":7413,"body":"@rasharab : happy birthday!","mergeCommitSha":"d4f917f5e781d5f360a81ce702f6366180f45397","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7413","title":"Add unblocked topic mapping source","createdAt":"2023-08-04T04:02:17Z"}
{"state":"Merged","mergedAt":"2023-08-04T06:09:52Z","number":7414,"body":"This is going to create a lot more embeddings, possibly 2X.","mergeCommitSha":"20aeb63aa14c6a354da87de0aa8fa38bc027d1df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7414","title":"Lower partition size to fit in context window","createdAt":"2023-08-04T06:08:36Z"}
{"state":"Merged","mergedAt":"2023-08-04T17:47:04Z","number":7415,"body":"* This logic was rerunning on save because the 'teamMember' had changed (which we didn't want)","mergeCommitSha":"3c7f93de6f1bd0589700c5e8c381be6eb66b3ac0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7415","title":"Fix diffing","createdAt":"2023-08-04T17:11:55Z"}
{"state":"Merged","mergedAt":"2023-08-04T18:56:58Z","number":7416,"body":"Reference shows empty title because it refers to a Slack thread was was deleted\r\n\r\nNever return deleted threads to a user.\r\n","mergeCommitSha":"81eb1fee2f849b888b0eaf1ee8cc9cdc4979d09f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7416","title":"Reference shows empty title because it refers to a Slack thread was was deleted","createdAt":"2023-08-04T18:34:10Z"}
{"state":"Merged","mergedAt":"2023-08-04T19:40:23Z","number":7417,"mergeCommitSha":"27cc798a5e8901f8bfffc4f0bbd4e4542307d634","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7417","title":"Persist Confluence pages to S3","createdAt":"2023-08-04T19:02:17Z"}
{"state":"Merged","mergedAt":"2023-08-04T19:11:05Z","number":7418,"body":"This fixes one half of the problem.\n\nAlso need to populate the `{metadata}` template field in the prompt from all types of docuemnts, not just source code files. This will be done in another PR.","mergeCommitSha":"3c57b5acfc1d96cf306628cba53b31bc5b760daf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7418","title":"Partial fix for garbled metadata in source code documents","createdAt":"2023-08-04T19:09:37Z"}
{"state":"Merged","mergedAt":"2023-08-04T19:58:19Z","number":7419,"mergeCommitSha":"da021171b6046fcc1038ef0b7dd8489cd6686cd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7419","title":"Move to latest kotlin/ktor/gradle dependnecies","createdAt":"2023-08-04T19:15:36Z"}
{"state":"Merged","mergedAt":"2022-03-31T18:56:48Z","number":742,"mergeCommitSha":"79b95c96f65dd843ffd0b4a2aa5570e6f451d138","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/742","title":"Fix test config","createdAt":"2022-03-31T18:52:45Z"}
{"state":"Merged","mergedAt":"2023-08-08T16:08:11Z","number":7420,"mergeCommitSha":"6241bf337e139a81ec43d15e1a963059ad2186de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7420","title":"Minor: cleanup auto release note","createdAt":"2023-08-04T19:18:11Z"}
{"state":"Merged","mergedAt":"2023-08-04T23:56:10Z","number":7421,"body":"min. event to start testing end to end","mergeCommitSha":"c887dcab11c9dd7b8919577a9a56d3a5941393d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7421","title":"Add statsig events","createdAt":"2023-08-04T19:47:28Z"}
{"state":"Merged","mergedAt":"2023-08-04T21:24:08Z","number":7422,"body":"We'll need to do this immediately after connecting with OAuth so that users can configure the spaces, but for now this'll do.","mergeCommitSha":"757101d32fede823b70ae686a0c71b6ed3c87cbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7422","title":"Sync confluence spaces","createdAt":"2023-08-04T20:22:40Z"}
{"state":"Merged","mergedAt":"2023-08-04T20:25:21Z","number":7423,"body":"Configuration follows this pattern:\r\n1. For local, we override the standard global configuration with the secrets.conf file. (HOCON overrides)\r\n2. For Dev/prod, we provide secrets via kube secrets using environment variables.\r\n","mergeCommitSha":"fdff7b657a13a2662524693f7557ac0968f26e52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7423","title":"Fix dev and prod statsig","createdAt":"2023-08-04T20:24:18Z"}
{"state":"Merged","mergedAt":"2023-08-08T15:49:54Z","number":7424,"mergeCommitSha":"47f68f0d22861bf7a2f875b628b71b54961a623d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7424","title":"Sync confluence spaces during auth install","createdAt":"2023-08-04T21:57:43Z"}
{"state":"Merged","mergedAt":"2023-08-09T23:32:59Z","number":7425,"body":"Allow users to manually install IDE plugins from the hub.  This is needed if people delete the plugin, or otherwise need to reinstall.\r\n\r\n<img width=\"236\" alt=\"Screenshot 2023-08-04 at 3 05 47 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/f8c00c2a-7f7b-4bdd-a4ea-e18c044bdb1c\">\r\n<img width=\"552\" alt=\"Screenshot 2023-08-04 at 3 05 58 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/aa1ffdff-bbf1-45c7-acdd-5a5c61d65829\">\r\n<img width=\"552\" alt=\"Screenshot 2023-08-04 at 3 06 06 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/eae34f56-fa92-4474-af01-358aeb21aa8e\">\r\n","mergeCommitSha":"1c081750ac729292950862119119351af4b6f474","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7425","title":"Hub option to install IDE plugins","createdAt":"2023-08-04T22:08:40Z"}
{"state":"Merged","mergedAt":"2023-08-05T21:44:13Z","number":7426,"body":"* Don't send an initial request on the SM update stream\r\n* When in-place editing the gutter icons, only shift the current line if content is being added at the beginning of the line","mergeCommitSha":"f30bc799b3027d3613c8c83c4eb81a78bf2e0de7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7426","title":"Fix some small SM IDE bugs","createdAt":"2023-08-04T22:32:15Z"}
{"state":"Merged","mergedAt":"2023-08-05T05:29:54Z","number":7428,"mergeCommitSha":"f2bd0fc109baee79fbe23988f9bdf7e1182a73fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7428","title":"Learning Ktor","createdAt":"2023-08-05T00:55:48Z"}
{"state":"Merged","mergedAt":"2023-08-08T18:48:44Z","number":7429,"body":"The client will provide the set of files in the IDE (tabs in the IDE that we can map to a repo, basically) -- for each file we also specify:\r\n- If the file content is visible, ie the tab for this file is selected.  There can be 0...n visible tabs, generally one for every split file editor view.\r\n- If the file content is active, ie it is the editor receiving input.  There will be zero or one active tabs.\r\n\r\nIf we feel it's useful, we can use these properties to further bias the answer -- active and visible files will be the most important.  I've considered trying to also order these files in a meaningful way (most recently opened, etc) but the IDEs don't provide useful APIs for this easily, so I've left it for a future task if we think it will be useful.","mergeCommitSha":"1171551fcdb60f309ae24704e099880c094f5a56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7429","title":"API for client to provide file context when creating a QA thread","createdAt":"2023-08-05T21:54:26Z"}
{"state":"Merged","mergedAt":"2022-04-05T21:50:51Z","number":743,"body":"Initial implementation of RepoStore and the respective thread requests for web dashboard","mergeCommitSha":"5a66c5efd397a2c22df07ff0e7a76e2981fb0e38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/743","title":"Setup Repostore for web dashboard","createdAt":"2022-03-31T21:52:40Z"}
{"state":"Merged","mergedAt":"2023-08-07T06:03:01Z","number":7430,"body":"- Adjusting the volume write alarm to a higher threshold because we recently upgraded instances and switched to IO Optimized configuration\r\n- Reduce the CPU alert threshold to 80 so we could get a warning well in advance of DB becoming unavailable\r\n- Kept the read IO alarm as it was since we mostly stay under 10k IOPs and have more than enough capacity to accommodate 50K","mergeCommitSha":"c7a4e14078df138d609bc5a47abc04566225ef20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7430","title":"this was spamming my email and had to reduce the noise","createdAt":"2023-08-07T06:02:54Z"}
{"state":"Merged","mergedAt":"2023-08-14T21:21:59Z","number":7431,"body":"Stacks on https://github.com/NextChapterSoftware/unblocked/pull/7429\r\n\r\n* Add `IDEContextStore` -- this tracks and caches file context.  It basically subscribes to the set of current files, resolves repos for each one, and tracks last-visible timestamp.\r\n* Use this when creating QA thread\r\n\r\nIntelliJ will come next -- all we need to do is supply the file listing in the IDEWorkspaceContext and everything else will start working.","mergeCommitSha":"1a1d3ee8fe6cca435697753452c25182d2115a63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7431","title":"VSCode provide file context in QA creation","createdAt":"2023-08-08T02:32:05Z"}