{"state":"Merged","mergedAt":"2021-12-10T00:57:14Z","number":1,"mergeCommitSha":"e85c01e4de929a456a0421d2cf2885d64137eaff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1","title":"Update colour and break tests","createdAt":"2021-12-09T23:46:36Z"}
{"state":"Merged","mergedAt":"2021-12-14T19:04:04Z","number":10,"mergeCommitSha":"23fefc07428c4d5dda3608fef6fdccc652a019e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10","title":"Opt in chromatic","createdAt":"2021-12-14T19:01:41Z"}
{"state":"Merged","mergedAt":"2022-01-21T22:33:12Z","number":100,"body":"- Added CDK code to create wildcard public certificates for environment's main hosted zone (e.g dev.usecodeswell.com for dev)\r\n- Added the cert to helm chart for API service","mergeCommitSha":"fb5db4581cf58bb5ef4d5d02a654758ce6cf64e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/100","title":"Add wildcard ssl cert","createdAt":"2022-01-21T22:27:26Z"}
{"state":"Merged","mergedAt":"2022-04-25T20:05:08Z","number":1000,"body":"Replaced the old policy with a more generic one allowing all request params to be forwarded from viewer to origin. ","mergeCommitSha":"db1161dede83bd319c71e5ec3f5db44d57f7edb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1000","title":"allow all viewer request params to be forwarded.","createdAt":"2022-04-25T20:01:22Z"}
{"state":"Merged","mergedAt":"2024-01-09T21:40:27Z","number":10000,"mergeCommitSha":"81f0b33a255898c14ea1918aa1b94c226c97d476","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10000","title":"Fix SimpleLengthContentFilter to skip empty documents","createdAt":"2024-01-09T19:07:45Z"}
{"state":"Merged","mergedAt":"2024-01-09T19:26:46Z","number":10001,"body":"@rasharab FYI\r\n\r\n```\r\n11:19:08 a.m. | UPDATE_FAILED        | AWS::AmazonMQ::Broker        | activemqbroker\r\nCloudFormation cannot update a stack when a custom-named resource requires replacing. Rename primary-dev-activemq and update the stack again.\r\n\r\n\r\n ❌  ActiveMQStack failed: Error: The stack named ActiveMQStack failed to deploy: UPDATE_ROLLBACK_COMPLETE: CloudFormation cannot update a stack when a custom-named resource requires replacing. Rename primary-dev-activemq and update the stack again.\r\n    at FullCloudFormationDeployment.monitorDeployment (/opt/homebrew/lib/node_modules/aws-cdk/lib/index.js:421:10934)\r\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\r\n    at async Object.deployStack2 [as deployStack] (/opt/homebrew/lib/node_modules/aws-cdk/lib/index.js:424:180618)\r\n    at async /opt/homebrew/lib/node_modules/aws-cdk/lib/index.js:424:163866\r\n\r\n ❌ Deployment failed: Error: The stack named ActiveMQStack failed to deploy: UPDATE_ROLLBACK_COMPLETE: CloudFormation cannot update a stack when a custom-named resource requires replacing. Rename primary-dev-activemq and update the stack again.\r\n    at FullCloudFormationDeployment.monitorDeployment (/opt/homebrew/lib/node_modules/aws-cdk/lib/index.js:421:10934)\r\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\r\n    at async Object.deployStack2 [as deployStack] (/opt/homebrew/lib/node_modules/aws-cdk/lib/index.js:424:180618)\r\n    at async /opt/homebrew/lib/node_modules/aws-cdk/lib/index.js:424:163866\r\n\r\nThe stack named ActiveMQStack failed to deploy: UPDATE_ROLLBACK_COMPLETE: CloudFormation cannot update a stack when a custom-named resource requires replacing. Rename primary-dev-activemq and update the stack again.\r\n```","mergeCommitSha":"e3ffb4d7d56f922aced48354acb33a68d9b081d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10001","title":"disable active-standby deployment until we resolve deployment issues","createdAt":"2024-01-09T19:23:16Z"}
{"state":"Merged","mergedAt":"2024-01-09T22:40:37Z","number":10002,"body":"<img width=\"1488\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/5059f732-f140-4325-ac2f-91a13c47622c\">\r\n","mergeCommitSha":"ba448648db3dbc951fe0feeaaefe017d3f070ac1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10002","title":"Add demo suggested questions","createdAt":"2024-01-09T19:48:19Z"}
{"state":"Merged","mergedAt":"2024-01-09T22:58:10Z","number":10003,"body":"For the record, the more I\"m looking at the current client implementation, the more I'm realizing that the client might have to **fuse** semantic results with the canonical sourcemarks + file prs if we want to maintain consistency with what we currently hanve.\r\n\r\nThere is **zero** possibility we're going to get anything representative of our current results (lots of prs based off commits and sourcemarks), and that's to be expected.","mergeCommitSha":"f5695e7cace53b6c29a337aeceb94d65a595069c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10003","title":"Add apis for semantically searching for insights","createdAt":"2024-01-09T19:53:22Z"}
{"state":"Merged","mergedAt":"2024-01-09T20:34:41Z","number":10004,"body":"I have test this via CloudFront and both the old and new queries return the same result. This way we can remove any dependency on broker name so we could roll out active_standby configuration. ","mergeCommitSha":"752987b2bd6e3d7b570d92b3b70b85ecfd606346","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10004","title":"remove all references to broker name in autoscaler queries","createdAt":"2024-01-09T20:01:28Z"}
{"state":"Merged","mergedAt":"2024-01-10T19:52:40Z","number":10005,"body":"Update text and ability to skip download page.\r\n\r\nWe decide on text based on target team's pending state. Both navigate to root.\r\nIn pending state, root would show the pending UI.\r\nIn normal state, root would show ask a question.\r\n\r\nSlight updates to download styling. Center vertically to match pending UI.\r\n\r\n![CleanShot 2024-01-09 at 11 57 25@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/2fa2c01a-6003-45b3-8fbb-a56800c81513)\r\n![CleanShot 2024-01-09 at 11 47 07@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/7c422bbe-ba46-4387-9407-40ffc6f3fb79)\r\n","mergeCommitSha":"856cb015b14badf0f96f296d6e9847ab1f0134d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10005","title":"Add skip capability to download","createdAt":"2024-01-09T20:05:18Z"}
{"state":"Merged","mergedAt":"2024-01-09T22:57:16Z","number":10006,"body":"![CleanShot 2024-01-09 at 13 22 22@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/e099ec34-eebe-4cb4-b23c-5c0f3d2a34db)\r\n","mergeCommitSha":"46c9c14b1d3e070501a11dadb05fb61d81b76bf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10006","title":"Update new processing email","createdAt":"2024-01-09T21:24:49Z"}
{"state":"Merged","mergedAt":"2024-01-09T21:32:33Z","number":10007,"mergeCommitSha":"cc54ce922120c8887b547889f354738d8d5e5d03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10007","title":"Reduce topic generation duration","createdAt":"2024-01-09T21:31:07Z"}
{"state":"Merged","mergedAt":"2024-01-09T23:37:41Z","number":10008,"mergeCommitSha":"3b71abba50e3771cb48351398410da2092a69c39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10008","title":"Filter out SearchResults if web site has been removed","createdAt":"2024-01-09T21:40:40Z"}
{"state":"Merged","mergedAt":"2024-01-09T21:42:16Z","number":10009,"body":"- Reduce topic duration\r\n- REduce number of repos\r\n","mergeCommitSha":"0944c89e18302b740d3b9bea21487f405a90fba6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10009","title":"ReduceDuration","createdAt":"2024-01-09T21:41:54Z"}
{"state":"Merged","mergedAt":"2022-04-25T22:00:33Z","number":1001,"body":"<img width=\"803\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165168450-cb0720f0-1ef7-412b-b625-61443480f1f2.png\">\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/issues/995","mergeCommitSha":"b887fa2e505c17907a4733823d2141e4c8e43902","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1001","title":"Make sure pre elements in editor and MessageView wraps text","createdAt":"2022-04-25T20:21:11Z"}
{"state":"Merged","mergedAt":"2024-01-09T21:47:31Z","number":10010,"mergeCommitSha":"d2f8643892fac929c9a44341a178da8603aa660e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10010","title":"Fix test","createdAt":"2024-01-09T21:44:40Z"}
{"state":"Merged","mergedAt":"2024-01-09T23:40:41Z","number":10011,"mergeCommitSha":"b86785d877310bff0a9fa9e7f88ecc094c156ab3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10011","title":"Create span for openAI calls","createdAt":"2024-01-09T22:39:10Z"}
{"state":"Merged","mergedAt":"2024-01-09T23:43:55Z","number":10012,"mergeCommitSha":"292ce546f5df2344ba8638c56068147d6936cf9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10012","title":"Fix WebIngestionPage in admin console","createdAt":"2024-01-09T23:35:12Z"}
{"state":"Merged","mergedAt":"2024-01-10T00:16:00Z","number":10013,"mergeCommitSha":"6cf28557e0b3979d17a3f4975daf7de181a90895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10013","title":"Assets update","createdAt":"2024-01-09T23:38:57Z"}
{"state":"Merged","mergedAt":"2024-01-10T00:02:33Z","number":10014,"body":"obviates the need for https://github.com/NextChapterSoftware/unblocked/pull/9973","mergeCommitSha":"90180df19f33ce425bbeb2e56fb314d1fe5d68c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10014","title":"Threads returned for standard teams are *always* unblocked threads","createdAt":"2024-01-09T23:40:51Z"}
{"state":"Merged","mergedAt":"2024-01-10T00:08:46Z","number":10015,"body":"- Remove topic summary\r\n- Remove dead code\r\n","mergeCommitSha":"be1ce514fb276b7079898a3613abaa550465601c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10015","title":"RemoveTopicSummary","createdAt":"2024-01-09T23:47:03Z"}
{"state":"Merged","mergedAt":"2024-01-10T00:16:47Z","number":10016,"mergeCommitSha":"a3ec90a445b9365d1a82263c1f2228b65df0e079","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10016","title":"[Admin Console] Show traits for web ingestion site","createdAt":"2024-01-10T00:08:15Z"}
{"state":"Merged","mergedAt":"2024-01-20T00:00:18Z","number":10017,"body":"Remove DiscussionThreadCommand implementation and replace with ViewRouter based command.","mergeCommitSha":"c87f6177ec0b1585ba7a8a5e932d04dd0220b8f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10017","title":"Use ViewRouter for discussion threads","createdAt":"2024-01-10T00:15:45Z"}
{"state":"Merged","mergedAt":"2024-01-10T05:13:29Z","number":10018,"mergeCommitSha":"934d06488794f0265cd0e38aabb116c0353f4d3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10018","title":"Fix repeated /mine querying in dashboard, when a \"mine\" stream has no data","createdAt":"2024-01-10T00:17:42Z"}
{"state":"Merged","mergedAt":"2024-01-10T00:46:14Z","number":10019,"mergeCommitSha":"a9b9444575d8deb37ffca05dd76a5657bb21e5b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10019","title":"Standard teams should not fetch threads for scm and q&a","createdAt":"2024-01-10T00:23:29Z"}
{"state":"Merged","mergedAt":"2022-04-25T21:49:22Z","number":1002,"body":"Creating internal AdminWeb service\r\n    - Added ECR repo named `adminweb`\r\n    - Added step to and deploy build new `adminwebservice`\r\n    - Created Kotlin service project for adminwebservice with a placeholder noop service\r\n    - Added helm configuration to create an internal ALB\r\n    - Modified CDK config to create a certificate for adminweb.prod.getunblocked.com as well as the us-west-2 alb address\r\n    - Removed two old unused CNAME records \r\n    - Added service account for adminweb service to EKS\r\n\r\n    All infra and EKS changes have been deployed.\r\n\r\n**Note**: Once deployed `adminweb` service will only be accessible via VPN ","mergeCommitSha":"33643c9173e480eb168c7f39069d9d08f51de56b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1002","title":"Add adminweb service","createdAt":"2022-04-25T21:07:23Z"}
{"state":"Merged","mergedAt":"2024-01-10T01:01:56Z","number":10020,"mergeCommitSha":"dd518a6e9c2de9c9b2474df9c60885c9daa5bf0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10020","title":"GEneralize file context","createdAt":"2024-01-10T00:33:53Z"}
{"state":"Merged","mergedAt":"2024-01-10T06:42:11Z","number":10021,"body":"Follow on from API change here #10014.","mergeCommitSha":"5a6f11dc57f30ea9abce1087e952ed498f38b664","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10021","title":"Threads subscribed for standard teams are *always* unblocked threads","createdAt":"2024-01-10T01:10:15Z"}
{"state":"Merged","mergedAt":"2024-01-10T19:15:57Z","number":10022,"mergeCommitSha":"f30452222c6efba2d6af01e873be9be517b66c66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10022","title":"Fix routing parameter name in admin console","createdAt":"2024-01-10T01:12:11Z"}
{"state":"Merged","mergedAt":"2024-01-10T04:50:49Z","number":10023,"body":"Content tweaks from Dennis:\r\n![CleanShot 2024-01-09 at 17 30 52@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/1d6ae7e5-6cfe-49a3-955d-2fad6564dd87)\r\n","mergeCommitSha":"222f00459e3b36efb403c8182780cbf0febbc24d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10023","title":"Content tweaks for open source callout","createdAt":"2024-01-10T01:31:17Z"}
{"state":"Merged","mergedAt":"2024-01-10T03:03:56Z","number":10024,"mergeCommitSha":"e09520ac677d0709df5247d59b981be01f994bf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10024","title":"Admin web tool to edit suggested questions","createdAt":"2024-01-10T01:46:22Z"}
{"state":"Merged","mergedAt":"2024-01-10T03:18:42Z","number":10025,"mergeCommitSha":"a1cce7469913398503260d4f12d964e8a53f412a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10025","title":"Use persisted suggestions in API","createdAt":"2024-01-10T02:54:37Z"}
{"state":"Merged","mergedAt":"2024-01-10T04:28:09Z","number":10026,"mergeCommitSha":"9fcaa40c0346383417e3185ec206d3915e7f5fd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10026","title":"Shuffle suggested demo questions","createdAt":"2024-01-10T04:16:27Z"}
{"state":"Merged","mergedAt":"2024-01-10T22:29:01Z","number":10027,"body":"This is only enabled if the `FeatureSemanticSearchFile` flag is enabled, otherwise the old mechanism is used.\r\n\r\nThis is a bunch of temporary code, so we can test out the file content semantic search functionality.  I wedged the reference object as another case in the IDE Insight type.","mergeCommitSha":"db98e367e3fdb0db907c4903e8b378e94752ee8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10027","title":"Do file content semantic search in IDE explorer panel","createdAt":"2024-01-10T04:48:38Z"}
{"state":"Merged","mergedAt":"2024-01-10T17:18:47Z","number":10028,"body":"Fix accidental undefined dereference.  I'll see if there is a TS or eslint rule we can enable to avoid this in the future, but it's a bit of an odd corner case in the syntax.","mergeCommitSha":"542d7e39557d78e8275797961726aa6ca26e265b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10028","title":"Avoid dereferencing undefined value","createdAt":"2024-01-10T05:05:04Z"}
{"state":"Merged","mergedAt":"2024-01-10T05:59:48Z","number":10029,"mergeCommitSha":"4b8da0a71e2e7122a718554feffc39e488d984f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10029","title":"Demo template websites are editable in admin web","createdAt":"2024-01-10T05:17:54Z"}
{"state":"Merged","mergedAt":"2022-04-25T22:00:42Z","number":1003,"body":"* also update sourcemark icon to reflect thread type","mergeCommitSha":"5f5c528849f39386cd3e2eb448704e046b111b91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1003","title":"Remove thread archiving restriction","createdAt":"2022-04-25T21:37:11Z"}
{"state":"Merged","mergedAt":"2024-01-10T06:28:56Z","number":10030,"mergeCommitSha":"3ca8578049b7319575856c230f9a78df31d95900","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10030","title":"Show suggestion counts in admin web templates page","createdAt":"2024-01-10T05:37:06Z"}
{"state":"Merged","mergedAt":"2024-01-10T07:39:47Z","number":10031,"body":"Reverts NextChapterSoftware/unblocked#10011","mergeCommitSha":"18c122a51a3b879dc92ecff74388035d4ac68d5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10031","title":"Revert \"Create span for openAI calls\"","createdAt":"2024-01-10T07:20:50Z"}
{"state":"Merged","mergedAt":"2024-01-10T17:35:38Z","number":10032,"body":"The site has been disabled here:\nhttps://admin.prod.getunblocked.com/demo/templates/abe1873a-6a77-43ec-a85d-8600b700b801","mergeCommitSha":"bf1580cf71f7d13c112c614fe35ab5243eae895f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10032","title":"Remove the temporary stripe.com website ingestion denial","createdAt":"2024-01-10T07:49:55Z"}
{"state":"Merged","mergedAt":"2024-01-10T19:10:46Z","number":10033,"body":"Update pointer to upload email brand assets","mergeCommitSha":"d99cb056a116b0435e6173dc962a273721b4ff35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10033","title":"EmailBrands","createdAt":"2024-01-10T17:53:43Z"}
{"state":"Merged","mergedAt":"2024-01-10T19:22:35Z","number":10034,"body":"Reverts NextChapterSoftware/unblocked#10031","mergeCommitSha":"da5beabf35fd6bc7d48b1acc347f19866f66bb8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10034","title":"Create span for openAI calls","createdAt":"2024-01-10T17:54:45Z"}
{"state":"Merged","mergedAt":"2024-01-10T20:37:41Z","number":10035,"mergeCommitSha":"e16e7a03357fdfb383ea834470c9e7b90918640c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10035","title":"Upload circle","createdAt":"2024-01-10T19:47:24Z"}
{"state":"Merged","mergedAt":"2024-01-10T22:39:03Z","number":10036,"mergeCommitSha":"b5408d059a873acf56242336650e316cfbb13278","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10036","title":"Add content source to content filters","createdAt":"2024-01-10T20:57:04Z"}
{"state":"Merged","mergedAt":"2024-01-10T21:20:10Z","number":10037,"mergeCommitSha":"55f4f392b6ff91b84226300663342ea3faf71e1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10037","title":"Reduce frequency of crawling demo team websites to once a week","createdAt":"2024-01-10T20:58:38Z"}
{"state":"Merged","mergedAt":"2024-01-10T22:33:29Z","number":10038,"body":"This will widen the modal for Large and X-Large breakpoints. \r\n\r\nLarge: 720px\r\n![CleanShot 2024-01-10 at 14 18 04@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/59e9e567-321e-4c4b-accd-a625a3d68648)\r\n\r\n\r\n\r\nX-large: 800px\r\n![CleanShot 2024-01-10 at 14 19 11@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/19517ee1-5e94-44ee-ab17-070144760436)\r\n","mergeCommitSha":"390346040e78df924b09bbdb14cf40895b29d678","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10038","title":"Additional breakpoints for open source options modal","createdAt":"2024-01-10T21:04:35Z"}
{"state":"Merged","mergedAt":"2024-01-10T21:56:30Z","number":10039,"body":"- First pass semantic file search\r\n- Semantic search file reference test\r\n","mergeCommitSha":"0bf33891d0dcba9ebe3d6303d8d628008a5ccf32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10039","title":"SemanticDocumentMatching2","createdAt":"2024-01-10T21:22:12Z"}
{"state":"Merged","mergedAt":"2022-04-25T22:58:35Z","number":1004,"mergeCommitSha":"1a4e907f4942170e7a53554b2a1d814d09c11883","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1004","title":"add cors workaround to auth lambda edge","createdAt":"2022-04-25T22:38:12Z"}
{"state":"Merged","mergedAt":"2024-01-10T21:55:12Z","number":10040,"mergeCommitSha":"65e46ccabc46c91bf18a6fb1ebdc4b527a72fad9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10040","title":"Fix the team mode check in WebIngestionEventEnqueueService","createdAt":"2024-01-10T21:31:36Z"}
{"state":"Merged","mergedAt":"2024-01-10T23:51:22Z","number":10041,"body":"* Demo mode now works in Bitbucket.  Our demo mode code assumed that by the time demo mode was starting, that we had an originating team to work with.  We would store the originating team's ID, so that we could return to it when exiting demo mode.  However, in our Bitbucket UI, when we get to the repo selector (where we offer demo mode), we have not created the original team yet -- this would fail.  I refactored the code to instead store an originating URL to return to.\r\n* Note that we also have to store the originating provider in local storage, so that we can render the correct \"Add your <provider> Repo!\" label and icon in the banner.\r\n* Change the web TeamContext to correctly handle demo teams.  We will never store demo teams as the selected team in local storage (which means we will not ever auto-return to that team), and we will never by default navigate to a demo team.","mergeCommitSha":"8546c0578c64a6534ba78077ff506c0123b04b48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10041","title":"Fix some demo mode bugs","createdAt":"2024-01-10T21:53:34Z"}
{"state":"Merged","mergedAt":"2024-01-10T23:33:50Z","number":10042,"mergeCommitSha":"f9ea65cc9aa181cf3b97396a62859ceef63d4312","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10042","title":"Repo code ingestion runs only for teams active in last 60 days","createdAt":"2024-01-10T22:44:11Z"}
{"state":"Merged","mergedAt":"2024-01-11T00:23:10Z","number":10043,"mergeCommitSha":"5d4ed542a297a7f97bf2a7ab6b09ba5a6a597cea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10043","title":"Repo PR summary ingestion runs only for teams active in last 60 days","createdAt":"2024-01-10T23:04:34Z"}
{"state":"Merged","mergedAt":"2024-01-11T01:33:47Z","number":10044,"body":"Will add langchain prompt templates tomorrow.","mergeCommitSha":"efe0fe3ff00ed73023c14125c463142cb2748ddc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10044","title":"flip the llmcontentfilter on in prod","createdAt":"2024-01-10T23:10:58Z"}
{"state":"Merged","mergedAt":"2024-01-11T00:33:42Z","number":10045,"mergeCommitSha":"9f4e57ce88e9f08b9ec18edfc8b56d1bb22065b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10045","title":"Repo topic ingestion runs only for teams active in last 60 days","createdAt":"2024-01-10T23:18:58Z"}
{"state":"Merged","mergedAt":"2024-01-11T00:58:47Z","number":10046,"body":"Motivation is cost saving.\n\nTopics allow us to generate recommendations feed from peers, and identify experts associated with documents and threads.\n\nHowever, since personal teams only have one user by definition, there is currently no benefit to topic ingestion for these teams.","mergeCommitSha":"9bddf151a638e276854384adc54c902618afd9ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10046","title":"Repo topic ingestion does not run for personal teams","createdAt":"2024-01-10T23:31:13Z"}
{"state":"Merged","mergedAt":"2024-01-11T00:39:30Z","number":10047,"mergeCommitSha":"95da60d649bd3adb1d17e40f8c15003e955ee70c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10047","title":"Remove navigation elements and capture H1 headers when scraping a web page","createdAt":"2024-01-10T23:48:43Z"}
{"state":"Merged","mergedAt":"2024-01-11T00:51:50Z","number":10048,"mergeCommitSha":"9e8f40357ba254745667a4c1a49f5121f8d94151","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10048","title":"Row style updates","createdAt":"2024-01-11T00:39:37Z"}
{"state":"Merged","mergedAt":"2024-01-11T20:41:20Z","number":10049,"body":"Setup basic email activity store.\r\n\r\nRecords what emails are sent to who. \r\nTo be used for both metrics / stats + downstream purposes (e.g. only send email 2 if email 1 is sent, etc...)\r\n\r\nFirst usage is for processComplete email. Do not resend to processComplete email to same person + team combination.\r\n\r\nAdded UI in admin console\r\n![CleanShot 2024-01-10 at 16 36 58@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/1601ebc4-02ad-46a0-9450-16143fe987bb)\r\n","mergeCommitSha":"f5420daeef6fa55a6e2748351980a9b06d406b9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10049","title":"Email event activity store","createdAt":"2024-01-11T00:46:34Z"}
{"state":"Merged","mergedAt":"2022-04-25T23:00:00Z","number":1005,"body":"Small copy paste mistake in Github action workflow for deploying admin web service. ","mergeCommitSha":"6c5db30d7480a109b82f2dd078dc8d4f2460a68f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1005","title":"fix a copy paste mistake in adminweb service deployment code","createdAt":"2022-04-25T22:42:57Z"}
{"state":"Merged","mergedAt":"2024-01-11T01:04:50Z","number":10050,"mergeCommitSha":"c526972b34a73e9cc73ce204f3b4f3bb9ee87b18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10050","title":"Fix typos on the admin console","createdAt":"2024-01-11T00:56:17Z"}
{"state":"Merged","mergedAt":"2024-01-11T01:30:05Z","number":10051,"mergeCommitSha":"25a0b1a3608d4a650e6127b0973e56be7edf8b39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10051","title":"Force web ingestion when triggered from the admin console","createdAt":"2024-01-11T01:13:40Z"}
{"state":"Merged","mergedAt":"2024-01-11T03:25:07Z","number":10052,"body":"We were using repoFilePath for loading the file, when we should be using filePath.\r\n\r\nRepoFilePath is relative to the repository and not the absolute path. \r\nEssentially we were passing null strings for partialContent. :)\r\n","mergeCommitSha":"d7a9cf67359a59945011ae094afb151fd4a6187e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10052","title":"[BUG FIX] Fix partialContent loading from file","createdAt":"2024-01-11T02:17:56Z"}
{"state":"Merged","mergedAt":"2024-01-11T03:48:23Z","number":10053,"mergeCommitSha":"3ef55000e3f7a97e8715b0189d5542c2805ef338","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10053","title":"Add new PR test and update prompt to limit output","createdAt":"2024-01-11T03:04:09Z"}
{"state":"Merged","mergedAt":"2024-01-11T03:29:42Z","number":10054,"mergeCommitSha":"0ed7d9549d9e78bc82f092a9bc94ad68c7753fd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10054","title":"Pass in root to check whether web site is configured for ingestion","createdAt":"2024-01-11T03:20:03Z"}
{"state":"Merged","mergedAt":"2024-01-11T03:38:58Z","number":10055,"mergeCommitSha":"2f17b230c4ca213a8711b860bb8ed36e9fb5a425","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10055","title":"Add more docs","createdAt":"2024-01-11T03:27:06Z"}
{"state":"Merged","mergedAt":"2024-01-11T05:06:18Z","number":10056,"mergeCommitSha":"ab56273b2869720835e229be799da7f2245fe707","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10056","title":"change the prompt format","createdAt":"2024-01-11T04:40:13Z"}
{"state":"Merged","mergedAt":"2024-01-11T19:38:04Z","number":10057,"mergeCommitSha":"7070c9f41c32b227c96a969b8bc773844811ad49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10057","title":"Compute the top languages used by a demo repo","createdAt":"2024-01-11T07:03:54Z"}
{"state":"Merged","mergedAt":"2024-01-11T18:33:45Z","number":10058,"mergeCommitSha":"99190b1df7b0793e4a137292d477c8149a44d129","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10058","title":"Update clientAssets - new onboarding email campaign asset","createdAt":"2024-01-11T18:21:23Z"}
{"state":"Merged","mergedAt":"2024-01-11T21:30:34Z","number":10059,"body":"![CleanShot 2024-01-11 at 10 57 44@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/4b786563-dad1-4182-84cc-1720179f7015)\r\n","mergeCommitSha":"2dc48cb2ad688735c32ff9f694ed7a07ed67e007","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10059","title":"Demo mode QA input screen updates","createdAt":"2024-01-11T18:58:23Z"}
{"state":"Merged","mergedAt":"2022-04-26T23:26:14Z","number":1006,"body":"Update extension to allow invite participants to a new discussion.\r\n\r\n<img width=\"743\" alt=\"CleanShot 2022-04-25 at 16 07 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/165189391-91a58e4e-01d8-4827-bbbf-8d23c72adf3d.png\">\r\n\r\n## Caveats\r\n\r\nSince we're in GitHub, we do not have access to Git, specifically blame. This means we are unable to mine the local source file for emails and metadata that would be used to display data such as \"hasMostContributions\".\r\n\r\nWe also *only* have access to the GH usernames (from DOM). This may cause issues if there are users who have *not* connected to Unblocked as we no longer have email as a backup.\r\n\r\n ","mergeCommitSha":"c0f9c5b970ef18d66cb3df17e2a3d5758f534f82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1006","title":"Web extension Invite Flow","createdAt":"2022-04-25T23:16:14Z"}
{"state":"Merged","mergedAt":"2024-01-11T21:54:05Z","number":10060,"mergeCommitSha":"be969b937a77bde941fb31c5345f6b3f93a14081","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10060","title":"Moves LLMContentFilter to StructuredPrompts","createdAt":"2024-01-11T18:59:03Z"}
{"state":"Merged","mergedAt":"2024-01-11T19:10:08Z","number":10061,"mergeCommitSha":"16c54505015f024de8ca3d0c572174ea81efe2a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10061","title":"Upgrade refinery","createdAt":"2024-01-11T19:10:00Z"}
{"state":"Merged","mergedAt":"2024-01-11T19:43:54Z","number":10062,"body":"Richie yelled at me that I need to make wiser usage of our honeycomb traces, and as usual, Richie is too smart.","mergeCommitSha":"b1332ff9b8c913a498feb0d366c676350ec774a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10062","title":"Goodbye lettuce tracing!","createdAt":"2024-01-11T19:20:57Z"}
{"state":"Merged","mergedAt":"2024-01-11T19:48:35Z","number":10063,"body":"Remove Team Questions, Download, and Docs links in demo mode","mergeCommitSha":"4d8c3ebd222f3c90a22f89da4d5285e4e729afe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10063","title":"Fix navigation bar items in demo mode","createdAt":"2024-01-11T19:32:43Z"}
{"state":"Merged","mergedAt":"2024-01-11T20:53:27Z","number":10064,"mergeCommitSha":"fb707b3aaf25dfb37af994d26a857be03d1b9a30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10064","title":"Filter demo templates using all template data","createdAt":"2024-01-11T20:13:27Z"}
{"state":"Merged","mergedAt":"2024-01-11T21:38:02Z","number":10065,"mergeCommitSha":"d414824343fbf03b0c07db3db37a457a463cc9ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10065","title":"Allow Matt's gitlab test group in dev","createdAt":"2024-01-11T21:23:51Z"}
{"state":"Merged","mergedAt":"2024-01-11T21:47:28Z","number":10066,"mergeCommitSha":"63d1d1717ca276ec49af0ac7bdae141c31ad4889","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10066","title":"Fix naming","createdAt":"2024-01-11T21:47:23Z"}
{"state":"Merged","mergedAt":"2024-01-11T22:06:49Z","number":10067,"body":"RRF implementation is now generalized.\r\n","mergeCommitSha":"d95fd8eb37531f45cb50be31c0cad4532244f65e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10067","title":"Improve reciprocal rank fusion","createdAt":"2024-01-11T21:51:00Z"}
{"state":"Merged","mergedAt":"2024-01-11T22:07:31Z","number":10068,"mergeCommitSha":"5f03b85f818a4ab2ce83468309f8007a8aebc58d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10068","title":"[SKIP TESTS] - add logging to suggestions extractor","createdAt":"2024-01-11T22:03:30Z"}
{"state":"Merged","mergedAt":"2024-01-11T22:23:11Z","number":10069,"body":"More content tweaks from Dennis. I also adjusted the max widths to accommodate.\r\n![CleanShot 2024-01-11 at 14 13 35@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/8a51dff4-cf7a-4f77-987c-3bf90afedaf2)\r\n\r\n","mergeCommitSha":"aaed1affe9eb204257265df0bd96fb327a4c26f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10069","title":"Open source callout content tweaks","createdAt":"2024-01-11T22:12:27Z"}
{"state":"Merged","mergedAt":"2022-04-25T23:39:15Z","number":1007,"body":"- Add initial auth for webview\r\n- Add cloudfront cors tests\r\n","mergeCommitSha":"9142ca03f1382e464dacd64784ff953c668a7ed4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1007","title":"Cleanup cors handling in lambda","createdAt":"2022-04-25T23:20:22Z"}
{"state":"Merged","mergedAt":"2024-01-11T23:31:42Z","number":10070,"body":"Just a first pass, will refine and generalize this for all ingestion events that hit external sites or APIs.","mergeCommitSha":"7c42dc7640851044ed2a6d6f113a6c56f4819103","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10070","title":"Implement simple backoff when ingesting web sites","createdAt":"2024-01-11T22:20:20Z"}
{"state":"Merged","mergedAt":"2024-01-11T23:33:22Z","number":10071,"mergeCommitSha":"78cdf8b0e7064adf1676c871098c7db7713ea022","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10071","title":"Show demo template languages in admin web","createdAt":"2024-01-11T22:26:52Z"}
{"state":"Merged","mergedAt":"2024-01-12T00:20:49Z","number":10072,"mergeCommitSha":"184e21bdf8e1a52bf98f696980989a40f21d460b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10072","title":"adds two new PR test cases + small prompt changes","createdAt":"2024-01-11T23:57:37Z"}
{"state":"Merged","mergedAt":"2024-01-12T00:23:54Z","number":10073,"mergeCommitSha":"0507f22313b08fc3676727c0336c6c7e5854697b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10073","title":"Make searchIdentifiable","createdAt":"2024-01-11T23:58:58Z"}
{"state":"Merged","mergedAt":"2024-01-12T22:41:11Z","number":10074,"body":"Setup system to send template emails using templates setup on SendGrid.\r\n\r\nAs we define new transactional emails, add templates to SendGrid and add the relevant events within our system (e.g. SendGridTemplateEvent, templateId within Configs + SendGridTemplateProvider)\r\n\r\nHave setup NewIntegrationCompletedProcessing as a test. Not hooked up atm.\r\n\r\nhave updated Sendgrid secrets across all environments to allow for transactional email.\r\n\r\n\r\n","mergeCommitSha":"f86304502513008504be3b37d62d8a49ff3ac52e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10074","title":"Sendgrid transactional emails","createdAt":"2024-01-12T00:12:33Z"}
{"state":"Merged","mergedAt":"2024-01-12T01:01:16Z","number":10075,"mergeCommitSha":"fbc2ee183bad84f5e2cd5a541998de003b6b98f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10075","title":"Use new RRF implementation for semantic file search","createdAt":"2024-01-12T00:27:39Z"}
{"state":"Merged","mergedAt":"2024-01-12T00:55:34Z","number":10076,"body":"This is kindof a weird scenario where a person has created two linked accounts in Unblocked (having signed in using both GitHub and Bitbucket say). If the person tries an Unblocked demo team while logged in as the GitHub account, then subsequently tries the same Unblocked demo while logged in as the Bitbucket account, then there will ligitimately be 3 members in that sandboxed demo team.","mergeCommitSha":"e26ff398ce41dfa2c090d4ae4dd087c52abc125a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10076","title":"Admin web current members view includes demo current members from different providers","createdAt":"2024-01-12T00:34:34Z"}
{"state":"Merged","mergedAt":"2024-01-12T00:53:04Z","number":10077,"body":"While I investigate some false positives that I've observed","mergeCommitSha":"1d2de95433df852d928505af2fe4202a98a74cdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10077","title":"Temporarily disable embedding removal in NotionPageValidationService","createdAt":"2024-01-12T00:44:29Z"}
{"state":"Merged","mergedAt":"2024-01-12T01:41:47Z","number":10078,"mergeCommitSha":"ab8033b867d172fe894485918151083b44e4e7a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10078","title":"List admin web languages","createdAt":"2024-01-12T01:17:58Z"}
{"state":"Merged","mergedAt":"2024-01-12T06:49:30Z","number":10079,"mergeCommitSha":"50b2628cdf377f3f21a657190522039d20c69f1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10079","title":"Fix bug where demo template repos were never ingested, because the template team was never active","createdAt":"2024-01-12T06:39:41Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:10:12Z","number":1008,"body":"## Summary\r\n\r\nHacky as usual. I considered using a `PreferenceKey` to signal the parent view to render the invite pane but opted for a callback instead. It's a bit hacky because I couldn't figure out how to set a State property to null without writing a State extension to allow for this, so I used a sentinal instead for now. I'll swoop in to fix that grossness and all the code duplication in another PR once we feel the UX is in a stable place.\r\n\r\nThere's also a lot of styling duplication, but I'm not going to spend any time refactoring this until the patterns are clear and stable.\r\n\r\n\r\n## GIF\r\n\r\n![CleanShot 2022-04-26 at 11 17 28](https://user-images.githubusercontent.com/858772/165366034-304ef66f-e665-4163-9ce6-3f21eb97c7d3.gif)\r\n\r\n## ScreenShots\r\n\r\n### Light \r\n\r\n*Thread List*\r\n<img width=\"604\" alt=\"CleanShot 2022-04-28 at 10 23 22@2x\" src=\"https://user-images.githubusercontent.com/858772/165812444-074b1f42-0aba-4238-8854-d50a21e198fb.png\">\r\n\r\n*Collapsed*\r\n\r\n<img width=\"543\" alt=\"CleanShot 2022-04-28 at 10 23 35@2x\" src=\"https://user-images.githubusercontent.com/858772/165812528-fd69810f-8512-4a87-a6d6-c9b6cd125446.png\">\r\n\r\n\r\n*Expanded TeamMember Icons*\r\n\r\n<img width=\"504\" alt=\"CleanShot 2022-04-28 at 10 23 49@2x\" src=\"https://user-images.githubusercontent.com/858772/165812639-9f3c867c-bf7a-4507-b3c7-89ba9b3facc1.png\">\r\n\r\n\r\n*Team Member List*\r\n<img width=\"544\" alt=\"CleanShot 2022-04-28 at 10 24 08@2x\" src=\"https://user-images.githubusercontent.com/858772/165812779-0abb693c-9bd9-4f96-ba56-df1bbe985ecb.png\">\r\n\r\n*Invite Sheet*\r\n\r\n<img width=\"520\" alt=\"CleanShot 2022-04-28 at 10 24 24@2x\" src=\"https://user-images.githubusercontent.com/858772/165812844-7f16eaf1-d8b8-4ce0-b3fa-6d9e4ed2719a.png\">\r\n\r\n*Invite Sent Banner*\r\n<img width=\"546\" alt=\"CleanShot 2022-04-28 at 10 25 06@2x\" src=\"https://user-images.githubusercontent.com/858772/165812959-c86ad33c-0d67-48b0-93b5-3a1ac0bf49a3.png\">\r\n\r\n### Dark\r\n\r\n*Thread List*\r\n\r\n<img width=\"533\" alt=\"CleanShot 2022-04-28 at 10 25 36@2x\" src=\"https://user-images.githubusercontent.com/858772/165813037-fbefbaff-c056-4397-a146-fb8c94ac0339.png\">\r\n\r\n*TeamMember List*\r\n<img width=\"568\" alt=\"CleanShot 2022-04-28 at 10 25 44@2x\" src=\"https://user-images.githubusercontent.com/858772/165813108-0bff58e4-b51b-43d1-a9dd-c1b0f591763d.png\">\r\n\r\n*Invite Sheet*\r\n<img width=\"563\" alt=\"CleanShot 2022-04-28 at 10 25 50@2x\" src=\"https://user-images.githubusercontent.com/858772/165813183-7a2b4ce0-ca95-4152-8e44-37c83b0f2898.png\">\r\n\r\n*Invite Success Banner*\r\n\r\n<img width=\"514\" alt=\"CleanShot 2022-04-28 at 10 26 28@2x\" src=\"https://user-images.githubusercontent.com/858772/165813233-d2c592eb-c8ad-40fd-948e-d94c464742d8.png\">\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n","mergeCommitSha":"9c4cf8996f98a5c20a70c238da4f50759f6ee379","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1008","title":"Invite Sheet","createdAt":"2022-04-26T00:10:21Z"}
{"state":"Merged","mergedAt":"2024-01-12T07:41:26Z","number":10080,"body":"- Add score filtering\r\n- Add score cutoff\r\n","mergeCommitSha":"4affa2a730abe0f300cefd01798f0bb0fdebb3b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10080","title":"[SKIP TESTS] RRF Part 2","createdAt":"2024-01-12T07:17:22Z"}
{"state":"Merged","mergedAt":"2024-01-12T18:45:05Z","number":10081,"mergeCommitSha":"37343f551cd5c77adccae0d9b16e7e75d493deae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10081","title":"Order demo templates by name and exclude disabled websites","createdAt":"2024-01-12T17:15:54Z"}
{"state":"Merged","mergedAt":"2024-01-12T17:43:30Z","number":10082,"mergeCommitSha":"5d784bc56379571e17a7eeb5466f61226f1d77ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10082","title":"Remove mouse event handling for read-only settings","createdAt":"2024-01-12T17:30:33Z"}
{"state":"Merged","mergedAt":"2024-01-12T21:32:03Z","number":10083,"mergeCommitSha":"0eea9a4f81aa0019af93b7e2eff3fbeba61f082b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10083","title":"Enable GitLab Cloud in PROD","createdAt":"2024-01-12T18:47:47Z"}
{"state":"Merged","mergedAt":"2024-01-12T20:28:06Z","number":10084,"mergeCommitSha":"5aeca8a8551027bff068acc05ee6edf73a967194","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10084","title":"Add API tests for website ingestion","createdAt":"2024-01-12T19:42:41Z"}
{"state":"Merged","mergedAt":"2024-01-12T20:36:35Z","number":10085,"mergeCommitSha":"e6c2b7e634dd32f542dba03cee33345a93f3ed95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10085","title":"Add simple memory profiler","createdAt":"2024-01-12T20:34:18Z"}
{"state":"Merged","mergedAt":"2024-01-12T21:04:22Z","number":10086,"body":"Right now we expand each of the sub-groups. I now think doing this was a mistake in retrospect because each sub-group then becomes it’s own sandboxed team, which leads to further headaches for users as they cannot ask questions across their sub-groups.\n\nhttps://chapter2global.slack.com/archives/C06E1MXEH1N/p1705090865992659","mergeCommitSha":"2517a448c52ebe7f7e34bc165e0526291737682b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10086","title":"Only show top-level GitHub Groups on the Group Selection page","createdAt":"2024-01-12T20:48:13Z"}
{"state":"Merged","mergedAt":"2024-01-12T21:11:20Z","number":10087,"body":"No meaningful code changes.  I moved files around so the ExplorerInsights webview and streams live under `/shared/ide/sidebar/ExplorerInsights`, and removed the `new_` prefix on the CSS, as the old webviews are long gone.","mergeCommitSha":"6a89a9b04afdd7a1384c327b526ea901a3e688fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10087","title":"Move ExplorerInsights webview files around","createdAt":"2024-01-12T20:59:29Z"}
{"state":"Merged","mergedAt":"2024-01-12T23:52:12Z","number":10088,"mergeCommitSha":"03ea9f537fc728f6c60ffa8bdbbeca06210d958c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10088","title":"Add mlfunctions flag to templates","createdAt":"2024-01-12T21:06:59Z"}
{"state":"Merged","mergedAt":"2024-01-12T21:23:45Z","number":10089,"mergeCommitSha":"dda9fab0db4743aec448a82d2db080009ab39240","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10089","title":"Remove GitLab sub-group access from DEV","createdAt":"2024-01-12T21:07:40Z"}
{"state":"Merged","mergedAt":"2024-01-12T21:52:42Z","number":10090,"mergeCommitSha":"285fc802fcd361358d474960b64a4f5ec462e526","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10090","title":"Fix memory dump","createdAt":"2024-01-12T21:52:06Z"}
{"state":"Merged","mergedAt":"2024-01-12T22:06:18Z","number":10091,"body":"Reverts NextChapterSoftware/unblocked#10083","mergeCommitSha":"d3693a41906a962c8bab576fdba9d873345b81f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10091","title":"Revert \"Enable GitLab Cloud in PROD\"","createdAt":"2024-01-12T22:05:41Z"}
{"state":"Merged","mergedAt":"2024-01-14T09:14:57Z","number":10092,"body":"Trace\nhttps://ui.honeycomb.io/unblocked/environments/production/result/c9mUe19NeSB/trace/kGGLbx1FDgr?fields[]=s_name&fields[]=s_serviceName&span=8bcb230152d66a07","mergeCommitSha":"b6dd584522ef88a2fde75846c8dd074c7b95903f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10092","title":"Fix slow people page queries","createdAt":"2024-01-12T22:23:29Z"}
{"state":"Merged","mergedAt":"2024-01-15T17:18:34Z","number":10093,"mergeCommitSha":"f4c99f992df62aeed105d9d56f7187199f8e84f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10093","title":"Remove training cutoff blurb from MLFunctions","createdAt":"2024-01-12T22:42:57Z"}
{"state":"Merged","mergedAt":"2024-01-13T01:37:09Z","number":10094,"body":"We currently have an inconsistent mix of both terms. Use \"discussion\" only now.","mergeCommitSha":"c00069b9ee7eeeaccd307d27a139b2cfe6da5292","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10094","title":"Consolidate \"Insight\" to \"Discussion\"","createdAt":"2024-01-13T00:20:34Z"}
{"state":"Merged","mergedAt":"2024-01-15T17:33:55Z","number":10095,"body":"Easier to see what's connected at a glance","mergeCommitSha":"9882a5cba44e267b60d28f6a54e4ef7136b760da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10095","title":"[Admin Console] Add overview table of connected integrations on team page","createdAt":"2024-01-13T00:22:04Z"}
{"state":"Merged","mergedAt":"2024-01-15T19:22:43Z","number":10096,"mergeCommitSha":"fb207b24c1da1d6f6b5345829ec356523f35b0c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10096","title":".unblocked.ignore","createdAt":"2024-01-13T00:42:21Z"}
{"state":"Open","mergedAt":null,"number":10097,"body":"Setup infra to support follow up emails for inactive users post onboarding.\r\n\r\nTwo \"drip\" campaigns that can occur after a user onboards and has no activity.\r\n1. Inactive after a team transitions from pending -> enabled (received processingComplete email)\r\n2. Inactive when joining a team that is enabled (did *not* received processingComplete email)\r\n\r\nEmails are defined within SendGrid and match with template Ids.","mergeCommitSha":"af11291e4c6864277af2d6586c8eb5c55294d3aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10097","title":"[WIP] Processing complete followup","createdAt":"2024-01-13T00:45:37Z"}
{"state":"Merged","mergedAt":"2024-01-13T05:44:41Z","number":10098,"body":"Enabling some hacks for tonight's build, so we can test things out.","mergeCommitSha":"733d8d005df3f2e454650dc7781c616181a55421","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10098","title":"Some quick hacks to enable testing for IDE semantic searching","createdAt":"2024-01-13T05:33:12Z"}
{"state":"Merged","mergedAt":"2024-01-13T06:27:56Z","number":10099,"mergeCommitSha":"fef922b54d6f6f30c64b80b28c8d323c566ef36c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10099","title":"Deprecate TryUnblocked feature flag now that it's live","createdAt":"2024-01-13T06:02:46Z"}
{"state":"Merged","mergedAt":"2022-01-21T23:07:40Z","number":101,"mergeCommitSha":"82c32cb26cdcb3857868b528e57401526c22ffbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/101","title":"Fix dev config","createdAt":"2022-01-21T22:38:29Z"}
{"state":"Merged","mergedAt":"2022-04-26T17:35:03Z","number":1010,"body":"- The shell is there: routing, tabs, breadcrumbs\r\n- will add team implementation in next PR\r\n\r\n<img width=\"1083\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/*********-2d93d400-20ec-4aa4-aed0-31022ebd73b7.png\">\r\n","mergeCommitSha":"55898293b3ee1b5290f68af19a09b083b3733112","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1010","title":"Introduce Admin Web","createdAt":"2022-04-26T00:45:55Z"}
{"state":"Merged","mergedAt":"2024-01-13T06:23:49Z","number":10100,"mergeCommitSha":"0d818a14b939b4fc6b30963340c9bd50f9c397f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10100","title":"Remove another \"insight\" text reference","createdAt":"2024-01-13T06:09:36Z"}
{"state":"Merged","mergedAt":"2024-01-15T01:36:29Z","number":10101,"body":"Access token (non-expiring) is added as a header for each request","mergeCommitSha":"45619daef5af483338b53abd4b383614e386a17e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10101","title":"Notion API does not create a new http client for each request","createdAt":"2024-01-13T06:31:48Z"}
{"state":"Merged","mergedAt":"2024-01-13T07:12:34Z","number":10102,"mergeCommitSha":"ab76dce8f4ae324f569559267d7aad95123c483c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10102","title":"Replace more insight references","createdAt":"2024-01-13T06:48:51Z"}
{"state":"Merged","mergedAt":"2024-01-15T17:19:43Z","number":10103,"mergeCommitSha":"51d7fe81ad0a8b5fcc7bf5c37d27e5c1852bd379","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10103","title":"Confluence and Jira API does not create a new http client for each request","createdAt":"2024-01-13T07:05:54Z"}
{"state":"Merged","mergedAt":"2024-01-15T21:04:28Z","number":10104,"body":"* Add security bumper to the bottom of the login UI and SCM connect UIs.\r\n* Fixed login UI centering/scrolling bugs (also fixed the \"redirecting...\" login UI centering bug)\r\n* Add new `Bumper` dashboard component -- this is generic, just for layout/design\r\n* Add new `SecurityQuestionsBumper` and `NoReposBumper` that define the unique two bumper UIs, plus the `ConnectTeamBumpers` that combines them into a row.\r\n* Note that these are different bumpers then the existing `DemoTemplateBumper` -- they look similar, but differ in almost every way.\r\n\r\n![CleanShot 2024-01-13 at 11 29 34@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/9af26428-5e10-4a83-997b-08f13ce32fa4)\r\n<img src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/63de4c24-eaa3-4004-a736-e5d8c5a75e69\" height=\"500\" />\r\n\r\n![CleanShot 2024-01-13 at 11 00 05@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/5519bed5-53fc-4598-bf3f-16ba01cbaff3)\r\n\r\n<img src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/00a33b0c-94b4-4507-a2aa-bee21f052eb6\" height=\"500\" />\r\n\r\n![CleanShot 2024-01-13 at 11 01 56@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/47eda8a7-5291-4771-aac2-c4ad057227bb)\r\n","mergeCommitSha":"d2ea43febe713995f8582783abb0010ca9f1ec6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10104","title":"Add bumpers","createdAt":"2024-01-13T19:37:31Z"}
{"state":"Merged","mergedAt":"2024-01-13T22:28:50Z","number":10105,"mergeCommitSha":"68acf142eaa6bfe0218c2f6b9b8d469b18415d0b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10105","title":"Hide old sidebar","createdAt":"2024-01-13T21:44:57Z"}
{"state":"Merged","mergedAt":"2024-01-14T00:48:53Z","number":10106,"mergeCommitSha":"276e15a648dd534af0c88b999925303692ac4f43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10106","title":"Fix plural typo","createdAt":"2024-01-14T00:33:47Z"}
{"state":"Merged","mergedAt":"2024-01-14T01:34:48Z","number":10107,"mergeCommitSha":"9378438bfbeeedf5426b66cd34d733dd5e4bdcb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10107","title":"Sort demo teams page","createdAt":"2024-01-14T01:12:34Z"}
{"state":"Merged","mergedAt":"2024-01-14T05:20:48Z","number":10108,"mergeCommitSha":"76599b5a57c6fe6fbb6cf0e5d33a9ffd98faff6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10108","title":"Make re-crawling websites once a week the default for all teams","createdAt":"2024-01-14T05:10:07Z"}
{"state":"Merged","mergedAt":"2024-01-14T22:55:32Z","number":10109,"mergeCommitSha":"f8a7c819472655769b6c36cce8881ec3bf5ec6e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10109","title":"fix diagnostics","createdAt":"2024-01-14T22:54:05Z"}
{"state":"Merged","mergedAt":"2022-04-26T00:50:59Z","number":1011,"mergeCommitSha":"3335f940e9a9e78ba6ddba248ee265dfb30087a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1011","title":"Add cors response headers and fix stuff","createdAt":"2022-04-26T00:49:10Z"}
{"state":"Merged","mergedAt":"2024-01-15T01:21:14Z","number":10110,"mergeCommitSha":"dd76bbe7ddf5c4cd32d0ff55be6be0b80842f993","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10110","title":"Use HEAD method for GitLab hostname detection","createdAt":"2024-01-15T01:04:22Z"}
{"state":"Merged","mergedAt":"2024-01-15T01:35:35Z","number":10111,"body":"- Ability to list personal GitLab account, in addition to GitLab groups.\r\n- Ability to list/select/sync GitLab repos from personal accounts.\r\n- Ability to create personal GitLab teams in Unblocked.","mergeCommitSha":"ad593af01df7591978783479b1cbb39e0506234d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10111","title":"GitLab personal account support","createdAt":"2024-01-15T01:04:26Z"}
{"state":"Merged","mergedAt":"2024-01-15T01:50:37Z","number":10112,"body":"We mostly don't (and won't) approve teams. It's automated.","mergeCommitSha":"f29d8fd73b5f910064974682cc0593b98d563748","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10112","title":"Re-label \"Pending Approval\" as \"Processing\"","createdAt":"2024-01-15T01:04:32Z"}
{"state":"Merged","mergedAt":"2024-01-15T06:49:12Z","number":10113,"body":"Simialr to `fetchThreads` operation.","mergeCommitSha":"1823f2d0dc368b7f81c87973647fae29698efea6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10113","title":"Introduce fetchPullRequests API operation to bulk fetch PRs by ID","createdAt":"2024-01-15T04:10:27Z"}
{"state":"Merged","mergedAt":"2024-01-15T18:47:32Z","number":10114,"mergeCommitSha":"0dbcc01907f532425f722ea32a0172a86d4da9b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10114","title":"Swap [source] text for document title in links produced by model","createdAt":"2024-01-15T18:33:30Z"}
{"state":"Merged","mergedAt":"2024-01-15T21:13:40Z","number":10115,"mergeCommitSha":"64558cc6919a5d989a1c47e9e7d5eabf50f1d0ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10115","title":"Add quick engaged repos stat","createdAt":"2024-01-15T20:55:24Z"}
{"state":"Merged","mergedAt":"2024-01-16T00:16:22Z","number":10116,"mergeCommitSha":"c809e5b1057be4fd0c91ad43e0ede1da117d9f8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10116","title":"Enable topic ingestion for all","createdAt":"2024-01-15T21:29:53Z"}
{"state":"Merged","mergedAt":"2024-01-15T22:45:40Z","number":10117,"body":"Adding new email assets for Jeff","mergeCommitSha":"97b79632e4d30665f912ec7a8d5e6cb53738b341","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10117","title":"Submodule bump for new email assets","createdAt":"2024-01-15T21:54:40Z"}
{"state":"Merged","mergedAt":"2024-01-17T19:51:27Z","number":10118,"body":"StreamProxy proxies stream contents across a memory boundary.  This will be used to automatically proxy store streams across the plugin/webview boundary.","mergeCommitSha":"d3e21d64195024e831c02a7758e774d336c8df81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10118","title":"Add StreamProxy","createdAt":"2024-01-15T22:24:28Z"}
{"state":"Merged","mergedAt":"2024-01-15T22:47:21Z","number":10119,"mergeCommitSha":"fb974f7c3f5ebdebb632f34c07569f96f98b810f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10119","title":"[Admin Console] Fix links in overview table of connected integrations","createdAt":"2024-01-15T22:39:06Z"}
{"state":"Merged","mergedAt":"2022-04-26T00:54:52Z","number":1012,"mergeCommitSha":"e6cd1a996155e3eb0a41f91db60442438c39c31a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1012","title":"More headers cleanup","createdAt":"2022-04-26T00:51:42Z"}
{"state":"Merged","mergedAt":"2024-01-15T22:49:55Z","number":10120,"body":"Handle situation where team member icon is missing.\r\n`discussion_thread__footer_icon` wrapper provides the correct column alignment even when the icon is missing.\r\n\r\n![CleanShot 2024-01-15 at 14 37 02@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/0c711dcb-37f8-4e2f-9b91-ff069eab797d)\r\n![CleanShot 2024-01-15 at 14 33 39@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/5c28d29d-f652-42e9-bcdf-053e82a23a58)\r\n","mergeCommitSha":"4f6e7ad01d48fa89ebbf5f8aa21af64607bb6ebf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10120","title":"Fix broken demo input","createdAt":"2024-01-15T22:39:07Z"}
{"state":"Merged","mergedAt":"2024-01-17T00:02:46Z","number":10121,"body":"Plus upgrade a couple other libraries where older versions only worked with typescript 4","mergeCommitSha":"ed1effbcc9585abc2bee43dd8be2df4dcaf8f1c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10121","title":"Upgrade to TypeScript 5","createdAt":"2024-01-15T22:59:18Z"}
{"state":"Merged","mergedAt":"2024-01-16T02:55:57Z","number":10122,"mergeCommitSha":"eea1771e601ced98238241dcccc54671ff6f0558","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10122","title":"Remove dead approval code for topics","createdAt":"2024-01-16T00:59:34Z"}
{"state":"Merged","mergedAt":"2024-01-16T01:48:17Z","number":10123,"mergeCommitSha":"2644dfae4392f81bf0a413abe51032d3de5b5202","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10123","title":"Remove ununsed InactivePersonCampaignMilestone which was never completed","createdAt":"2024-01-16T01:21:31Z"}
{"state":"Merged","mergedAt":"2024-01-16T17:51:25Z","number":10124,"mergeCommitSha":"0776d26945e3b877f73849177fa7f2c26c45fd3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10124","title":"Remove more stale topic code","createdAt":"2024-01-16T03:00:12Z"}
{"state":"Merged","mergedAt":"2024-01-16T04:33:59Z","number":10125,"mergeCommitSha":"981c3284951be294f9894dfc3af46550fdb0651e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10125","title":"Fix topic counts and fix topic display","createdAt":"2024-01-16T04:25:05Z"}
{"state":"Merged","mergedAt":"2024-01-17T22:48:52Z","number":10126,"body":"Will let us wait for integrations added during onboard to complete before enabling the team.","mergeCommitSha":"de91d26e8a1e452e8d57167e0a7b57a37dbdca41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10126","title":"Add store function to check that all integrations have completed initial ingestion","createdAt":"2024-01-16T05:42:01Z"}
{"state":"Merged","mergedAt":"2024-01-17T00:12:40Z","number":10127,"body":"* Join in thread and PR data.  Display subtitle and footer content\r\n* Threads come from the existing ThreadStore\r\n* PRs come from a one-off cached stream (`PullRequestListStream`) for this UI -- we don't really deal with raw `PullRequest` models anywhere else\r\n* Only show the loading state when switching files -- within a file, scrolling updates simply appear\r\n\r\n![CleanShot 2024-01-16 at 14 48 00@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/aa12a62f-d390-4eee-a413-b41ab4fb370e)\r\n","mergeCommitSha":"25d6936d0755491c4c4e539e5a209977c9793bf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10127","title":"Join thread/PR subtitle content in explorer insights UI","createdAt":"2024-01-16T06:29:05Z"}
{"state":"Merged","mergedAt":"2024-01-16T07:41:11Z","number":10128,"mergeCommitSha":"0f0d8e1698d6fcdecfa69c41c1e4305f201d1d01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10128","title":"Install and manage GitLab webhooks on the GitLab Group","createdAt":"2024-01-16T06:42:26Z"}
{"state":"Merged","mergedAt":"2024-01-16T18:37:43Z","number":10129,"body":"- Add approved for now\r\n- Try again\r\n","mergeCommitSha":"a96e4df528947765e6f0ae76130b3c05895251e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10129","title":"AddApprovedForNow","createdAt":"2024-01-16T18:12:58Z"}
{"state":"Merged","mergedAt":"2024-01-16T23:20:13Z","number":10130,"body":"\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/38fbb720-0d41-4b3b-a3b2-3ed31d17a3bb\r\n\r\n","mergeCommitSha":"db20821d29d1a56b89f259c4a860a4f1f5a420c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10130","title":"Create Source mark on new QA","createdAt":"2024-01-16T18:13:07Z"}
{"state":"Merged","mergedAt":"2024-01-16T19:18:48Z","number":10131,"mergeCommitSha":"4899339005474c31a201c5ee24211329d3c76d6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10131","title":"Dont call getOrCreate from the admin console","createdAt":"2024-01-16T18:55:22Z"}
{"state":"Merged","mergedAt":"2024-01-16T21:33:06Z","number":10132,"body":"I am about to merge a lot of changes to main branch in EC2 Action Builder repo. I tagged the current main commit and this PR should pin us to the current version while I finish up my work. ","mergeCommitSha":"e6634bcca9cc63556940535172e443fdb5bfa93d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10132","title":"pin runner action to current version of main tagged as 0.0.2","createdAt":"2024-01-16T21:11:13Z"}
{"state":"Merged","mergedAt":"2024-01-16T22:17:38Z","number":10133,"body":"Contact us to ~see~ **review** our SOC 2 and security documentation.","mergeCommitSha":"91789b452b2924c1d35b5199e2a2547764113e87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10133","title":"Update bumper text","createdAt":"2024-01-16T22:06:13Z"}
{"state":"Merged","mergedAt":"2024-01-16T23:20:25Z","number":10134,"body":"\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/fbcc714f-8da2-4c4b-8a39-65d82f4cf884\r\n\r\n","mergeCommitSha":"e94ccede2a2637b5ee06c20a554b332a642785a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10134","title":"Add QA animations","createdAt":"2024-01-16T22:30:41Z"}
{"state":"Merged","mergedAt":"2024-01-17T19:05:39Z","number":10135,"body":"As part of the work to *remove* topics from the clients, we need another method to populate experts.\r\n\r\nAdd a list of teamMembers as experts to a threadInfo.\r\nSince PRs are also composed of threads, this should apply there as well.","mergeCommitSha":"b80bd396388de3812673e39a00dc93f5936b1bc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10135","title":"Add experts to threadInfo","createdAt":"2024-01-16T22:41:44Z"}
{"state":"Merged","mergedAt":"2024-01-16T23:54:52Z","number":10136,"mergeCommitSha":"94953d989635b0b544f897e6dd8a0bb6f0bb69d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10136","title":"Revert topicsourcetype","createdAt":"2024-01-16T23:53:06Z"}
{"state":"Closed","mergedAt":null,"number":10137,"mergeCommitSha":"d3f0adf2523663220061b1966ddaf0ab87e650dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10137","title":"Format local document as a query for semantic lookup","createdAt":"2024-01-17T00:16:18Z"}
{"state":"Merged","mergedAt":"2024-01-17T21:46:31Z","number":10138,"body":"![CleanShot 2024-01-16 at 16 17 07@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/c950c775-efc8-4158-8454-119c8e1abad2)\r\n![CleanShot 2024-01-16 at 16 16 59@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/0dbdaba4-646c-49fe-8b6f-c36ee2b8dfc5)\r\n![CleanShot 2024-01-16 at 16 16 38@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/76d646ee-a42d-4b3b-9cf4-a471ac8f4d56)\r\n","mergeCommitSha":"4c265caedeb51811a4f2939c490698d846161d66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10138","title":"Qa gif","createdAt":"2024-01-17T00:19:42Z"}
{"state":"Merged","mergedAt":"2024-01-17T00:54:33Z","number":10139,"mergeCommitSha":"d538d181de025ef0a561c6faacee300d3d5dd993","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10139","title":"Add test fixture for GitLab","createdAt":"2024-01-17T00:37:40Z"}
{"state":"Merged","mergedAt":"2022-04-26T22:54:09Z","number":1014,"body":"Background:\r\nIn lieu of that, I had to hack up image downloads to using a browser fetch, which while it might work for images, will probably not work well for videos.\r\n\r\nThis pr does the following things:\r\n1. Add authentication injections into vscode webview. WebviewContentController subscribes to Authstore events and posts messages to RenderWebview. The RenderWebview will subsequently store token in a zustand store for access anywhere.\r\n2. Add an AssetDownloader which will take Authentication token from zustand store and attempt to query cloudfront endpoint for asset and generate an object blob url. (Not sure how well this will work for video assets).\r\n\r\nTesting:\r\nValidated that the asset was correctly being uploaded and the asset download was going through cloudfront.\r\n![CleanShot 2022-04-25 at 19 16 10](https://user-images.githubusercontent.com/3806658/165206298-764e47e6-a9c7-463c-a9b6-513298c4ca4a.gif)\r\n\r\n","mergeCommitSha":"0a405d2e88b1f85589530764d3ff8073435188d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1014","title":"Integrate authentication into vscode webview image loads","createdAt":"2022-04-26T01:52:23Z"}
{"state":"Merged","mergedAt":"2024-01-17T01:44:24Z","number":10140,"mergeCommitSha":"c699ac9212af03e72e1f40d9ae600200dd01ac5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10140","title":"First stab at relevancy","createdAt":"2024-01-17T00:40:42Z"}
{"state":"Merged","mergedAt":"2024-01-17T17:18:48Z","number":10141,"mergeCommitSha":"9c29a45a96c10f8944d4d061995e66fd28bb6155","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10141","title":"Create ingestion models for Linear and Jira","createdAt":"2024-01-17T00:51:32Z"}
{"state":"Merged","mergedAt":"2024-01-17T18:06:10Z","number":10142,"mergeCommitSha":"cdebe293af21da62da0b8502270623ad0e30d31a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10142","title":"ThreadInfo.pullRequest is only populated for PR threads","createdAt":"2024-01-17T01:11:42Z"}
{"state":"Merged","mergedAt":"2024-01-17T01:32:50Z","number":10143,"body":"Fix issue with unencoded content. ","mergeCommitSha":"583b3dcaee75704c82fdb50463c25b9d859dac78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10143","title":"Fix issue","createdAt":"2024-01-17T01:21:17Z"}
{"state":"Merged","mergedAt":"2024-01-17T05:45:03Z","number":10144,"mergeCommitSha":"65c33bccce664bcb39115da983c1f3fec9ac3755","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10144","title":"Implement GitLab webhook handlers","createdAt":"2024-01-17T02:48:50Z"}
{"state":"Merged","mergedAt":"2024-01-17T04:24:55Z","number":10145,"mergeCommitSha":"368f0354e2bcc49e74e2e9bc7859046db2478f7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10145","title":"Remove explorer insights dropdown when using semantic search","createdAt":"2024-01-17T04:13:07Z"}
{"state":"Merged","mergedAt":"2024-01-17T04:35:06Z","number":10146,"mergeCommitSha":"e7fedabe7920231be53be1fdc2d8ed4a327a3a4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10146","title":"Fix JB build","createdAt":"2024-01-17T04:24:15Z"}
{"state":"Merged","mergedAt":"2024-01-17T05:36:22Z","number":10147,"mergeCommitSha":"d0b860318895094f6925161d91037a57f0174226","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10147","title":"Add hasSlackThreads to fetchPullRequests","createdAt":"2024-01-17T04:28:01Z"}
{"state":"Merged","mergedAt":"2024-01-18T01:12:28Z","number":10148,"mergeCommitSha":"d4bf5312b2c1971d925f805e6a0c2f2a6b042fa8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10148","title":"GitLab merge request handler","createdAt":"2024-01-17T17:29:04Z"}
{"state":"Merged","mergedAt":"2024-01-17T20:34:05Z","number":10149,"mergeCommitSha":"a51326115d2ee7a5bee30d0e67b2dbe874e22f4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10149","title":"Fix Jira admin console page","createdAt":"2024-01-17T18:52:27Z"}
{"state":"Merged","mergedAt":"2022-04-26T17:02:45Z","number":1015,"body":"* Flatten the MessageView children hierarchy so that we can apply a grid layout onto them\r\n* Use grid layout to present two different layouts in VSCode\r\n* Show title in first message.  Note: with this change re-titling a thread is no longer possible, this will be fixed in a future PR.\r\n\r\n(Note, I see a bug in the images below, looks like there is extra spacing in the message views that *don't* have a title)\r\n\r\nExpanded layout, this is what is used when you have a thread view that spans the whole window (and what would be used in the dashboard):\r\n<img width=\"1456\" alt=\"Screen Shot 2022-04-25 at 9 51 34 PM\" src=\"https://user-images.githubusercontent.com/2133518/165224167-a3d98fe2-1541-4e85-b635-9eede6016d9a.png\">\r\n\r\n\r\nCompressed layout, this is what is used in a split view:\r\n<img width=\"1456\" alt=\"Screen Shot 2022-04-25 at 9 51 41 PM\" src=\"https://user-images.githubusercontent.com/2133518/165224178-0998dd03-c346-42bd-b9ed-459223f599dd.png\">\r\n\r\n","mergeCommitSha":"ecba3bb16b9d164cbccc2b4c6481aca149a36b4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1015","title":"Rejig MessageView layout","createdAt":"2022-04-26T04:51:18Z"}
{"state":"Merged","mergedAt":"2024-01-17T20:40:41Z","number":10150,"body":"After speakign with someone smarter than me, Richard Bresnan, he iindicated that we need to check archivedAt field for user-based thread deletions.\r\n","mergeCommitSha":"4d3fdd9473f56920403b5924eb0583dac5496b32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10150","title":"Fix deletion checks across codebase","createdAt":"2024-01-17T19:05:41Z"}
{"state":"Merged","mergedAt":"2024-01-18T05:06:17Z","number":10151,"body":"Add \"Ask Question\" to submenu in VSCode + lightbulb.\r\nAsk \"Ask Question\" gutter icon to IntelliJ","mergeCommitSha":"73faa389d4581e634b61a261701024f39d9e802f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10151","title":"Ask question submenu action","createdAt":"2024-01-17T19:14:37Z"}
{"state":"Merged","mergedAt":"2024-01-17T20:02:28Z","number":10152,"body":"* Add footer\r\n* Set footer opacity to match designs\r\n* Add external link icon\r\n\r\n![CleanShot 2024-01-17 at 11 48 22@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/93a1ad3c-ce30-4aad-9b45-7942d7999bdb)\r\n","mergeCommitSha":"f6fdef3ca6c5a0f573a093136f5dc4e7efda1b8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10152","title":"Render Notion/Web document references correctly in explorer insight panel","createdAt":"2024-01-17T19:50:24Z"}
{"state":"Merged","mergedAt":"2024-01-17T21:16:27Z","number":10153,"mergeCommitSha":"d4a58f84600cc967b3013ee9079989886aca96db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10153","title":"Fix Linear admin console page","createdAt":"2024-01-17T20:49:10Z"}
{"state":"Merged","mergedAt":"2024-01-23T21:12:38Z","number":10154,"body":"Utilize ThreadInfo for experts instead of topic based.\r\n\r\nHave not fully removed topics as it's still being used by the \"GeneralSidebar\". Once we get the thumbs up to remove the old sidebar, we can remove even more of the topic code.","mergeCommitSha":"0776daa677bf93eca0f5e1d1818ba3a8f1a6e0e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10154","title":"Integrate ThreadInfo Experts","createdAt":"2024-01-17T21:18:27Z"}
{"state":"Merged","mergedAt":"2024-01-17T22:08:16Z","number":10155,"body":"Vetted in VSCode and Dashboard:\r\n\r\nInsights panel:\r\n![CleanShot 2024-01-17 at 13 49 15@2x](https://github.com/NextChapterSoftware/unblocked-client-assets/assets/********/e079b106-c3f3-4e59-ac40-3f8b502b31b7)\r\n\r\nGutter:\r\n![CleanShot 2024-01-17 at 13 50 11@2x](https://github.com/NextChapterSoftware/unblocked-client-assets/assets/********/1272993e-c458-40cf-a2e2-0c6e13f0c7ec)\r\n\r\n\r\nDashboard:\r\n![CleanShot 2024-01-17 at 13 50 48@2x](https://github.com/NextChapterSoftware/unblocked-client-assets/assets/********/4e0b5631-d4fe-42eb-9b69-4ed3bb382cce)\r\n","mergeCommitSha":"014def4f4595d062220c7d5c2dee72601d8beeb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10155","title":"submodule bump","createdAt":"2024-01-17T21:56:12Z"}
{"state":"Merged","mergedAt":"2024-01-17T22:37:08Z","number":10156,"mergeCommitSha":"82505487b2b24f0915e648a6405bebb5ba78f571","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10156","title":"Change label","createdAt":"2024-01-17T22:24:22Z"}
{"state":"Merged","mergedAt":"2024-01-18T02:59:55Z","number":10157,"body":"Move topicexperts to threadBundleDecorator","mergeCommitSha":"a380790bbbb3e1a21c42d6d4ee0b74ac85fd2843","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10157","title":"Topic Experts","createdAt":"2024-01-17T23:06:51Z"}
{"state":"Merged","mergedAt":"2024-01-18T00:04:29Z","number":10158,"body":"Mostly moving code out of `CodeIngestionCompletionService` into a new `IngestionCompletionService` class. This will let us call the same code when an integration has been fully ingested. \r\n\r\nNo behavioural change until I uncomment the `if (!ingestionStore.isInitialIngestionOfAllIntegrationsComplete(teamId = teamId)) return` line of code.","mergeCommitSha":"33a903222c2ee409896664cf6c61c9453548ff63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10158","title":"Pull out code from CodeIngestionCompletionService to allow enabling a team when all integrations have been ingested","createdAt":"2024-01-17T23:12:19Z"}
{"state":"Merged","mergedAt":"2024-01-18T00:01:55Z","number":10159,"body":"VSCode decorations does *not* render svgs in the markdown renderer.\r\n\r\nThis is only really an issue right now with our bot avatar. Adding a check locally to swap to local bot avatar.\r\nAnother workaround would be for server to send bot avatar as a png.\r\n\r\n![CleanShot 2024-01-17 at 15 15 17@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/df0ceb20-f308-4677-80ba-f7aef767c82f)\r\n","mergeCommitSha":"5ab64c906aac7b57cc25898d9ebc3c5f9621583e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10159","title":"Fix local bot icon","createdAt":"2024-01-17T23:16:43Z"}
{"state":"Merged","mergedAt":"2022-04-26T21:33:18Z","number":1016,"body":"The token is stored unencrypted for now. We will need to encrypt before launch.","mergeCommitSha":"2d4f6641a764cace54d96c900b862b54c4a6b75c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1016","title":"Store access token on Identity model and use for GitHub messages","createdAt":"2022-04-26T05:02:18Z"}
{"state":"Merged","mergedAt":"2024-01-18T00:00:45Z","number":10160,"body":"Similar to threadInfo experts, add experts to PR.\r\n","mergeCommitSha":"634421459089d80d7eda0a80a50a4e74687903f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10160","title":"Add pr experts","createdAt":"2024-01-17T23:18:47Z"}
{"state":"Merged","mergedAt":"2024-01-18T16:31:25Z","number":10161,"body":"* Clean up SidebarRow css --> move common styles into the shared css\r\n* Tweak layouts when there is no content","mergeCommitSha":"4eddd3452ef09b2a687545c72db9a2debaa2f2dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10161","title":"List layout tweaks","createdAt":"2024-01-18T00:22:09Z"}
{"state":"Merged","mergedAt":"2024-01-18T05:30:01Z","number":10162,"body":"Add button to hide ask sidebar instructions.\r\n\r\nCurrently only displayed on hover. @benedict-jw Let me know if you want it to be static instead.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/416a051f-39a0-4fb5-9d93-a8dd59299314\r\n\r\n","mergeCommitSha":"0f1a2345e1b75ed7da6171e483adb19433c4cb6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10162","title":"Add button to hide instructions","createdAt":"2024-01-18T00:43:55Z"}
{"state":"Merged","mergedAt":"2024-01-18T22:35:43Z","number":10163,"body":"Still need to add for slack and web ingestion.","mergeCommitSha":"b49166d215016ca2805a7368d2730c94e2744356","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10163","title":"Call IntegrationIngestionCompletionService.integrationIngestionCompleted after completing ingestion","createdAt":"2024-01-18T00:47:17Z"}
{"state":"Merged","mergedAt":"2024-01-18T01:26:34Z","number":10164,"body":"Change labels for the \"no file selected\" and \"no insights found\" states","mergeCommitSha":"087a0fcda28e2d08eb1d60d957fa49b905aef2cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10164","title":"More label changes for explorer insights panel","createdAt":"2024-01-18T01:15:13Z"}
{"state":"Merged","mergedAt":"2024-01-18T08:38:43Z","number":10165,"body":"Currently, the SM engine is incapable of trusting sourcemarks received from the API service because none of the sourcepoints include a code snippet.\n\nThis change will allow `SnippetFinder` to validate the original source points.","mergeCommitSha":"1afdf124440929a15d956ccfded628a614a07728","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10165","title":"PR ingestion generates GitLab merge request comment snippets","createdAt":"2024-01-18T07:20:30Z"}
{"state":"Merged","mergedAt":"2024-01-18T16:43:39Z","number":10166,"body":"buttons are now disabled if they are dependent on other buttons \r\n\r\n<img width=\"628\" alt=\"Screenshot 2024-01-18 at 08 35 00\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/c0b0b7d5-fa55-4188-acda-d3a770d272d6\">\r\n","mergeCommitSha":"8afc911a41c10329173d697df97f0e136ef0931d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10166","title":"Render slack settings as disabled to respect constraints","createdAt":"2024-01-18T16:31:53Z"}
{"state":"Merged","mergedAt":"2024-01-18T17:39:49Z","number":10167,"body":"All done except for UI tweaks, which can follow later.\r\n\r\nhttps://www.notion.so/nextchaptersoftware/GitLab-Enterprise-Tasks-02d23727bf51437b80bf7697caf22fdc?pvs=4","mergeCommitSha":"2a52ff219a5316521362e1495a793877abb11ce1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10167","title":"Enable GitLab Cloud in PROD","createdAt":"2024-01-18T16:33:26Z"}
{"state":"Merged","mergedAt":"2024-01-18T19:53:51Z","number":10168,"body":"https://chapter2global.slack.com/archives/C02GEN8LFGT/p1705594257167639","mergeCommitSha":"07765e3cafcbcda10c0a07e504b791e5a8e1cf65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10168","title":"Remove \"connect later\" on connect integrations webpage","createdAt":"2024-01-18T16:56:25Z"}
{"state":"Merged","mergedAt":"2024-01-18T17:36:14Z","number":10169,"body":"![CleanShot 2024-01-18 at 09 09 47@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/94215f5b-5cbb-484f-b2db-3bf07b4e7899)\r\n","mergeCommitSha":"13b90437e5a2e88651b506b5959eed82b25ab2a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10169","title":"Add z","createdAt":"2024-01-18T17:10:04Z"}
{"state":"Merged","mergedAt":"2022-04-26T17:48:11Z","number":1017,"mergeCommitSha":"0eccf8e1fbe2c3b7689e078cd1876341bf396731","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1017","title":"Hide SMs of an archived thread in IDE","createdAt":"2022-04-26T17:01:42Z"}
{"state":"Merged","mergedAt":"2024-01-18T18:22:51Z","number":10170,"body":"Intercom Auto Launch was never being triggered as TeamContext was rerouting without query parameters to /questions *before* `show_intercom` was registered.\r\n\r\nUpdated component into a wrapper which happens before all the auth / redirect logic.\r\n\r\n**Tested this locally by setting breakpoints**","mergeCommitSha":"25f1c9650c5e810dcbabf0c23614578c48b7df8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10170","title":"Fix intercom auto launch","createdAt":"2024-01-18T17:21:10Z"}
{"state":"Merged","mergedAt":"2024-01-18T18:51:24Z","number":10171,"body":"* In IDE thread/PR views, only show file names in code blocks, don't show full paths.  The tooltip will show the full file path.\r\n* If the header has a click handler (ie, open the associated file), when you click on the file name, only navigate, do not expand/collapse","mergeCommitSha":"d44fdfc6f86eda9211ba706e3252c706f6282f20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10171","title":"Fix path display in code snippets","createdAt":"2024-01-18T18:38:02Z"}
{"state":"Merged","mergedAt":"2024-01-18T19:46:35Z","number":10172,"body":"Switch to the public version of our Github Action. Removed unused IAM action and outputs. We no longer need the checkout action. ","mergeCommitSha":"a63cb2223e882789d94085c917c3fea483f71e03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10172","title":"testing the new action","createdAt":"2024-01-18T18:40:38Z"}
{"state":"Merged","mergedAt":"2024-01-18T20:40:49Z","number":10173,"body":"This was all VSCode-specific stuff.\r\n\r\nAlso, remove commented-out auto-launch code.","mergeCommitSha":"515b4092850235b88edf4c15b0e2101a5b675d3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10173","title":"Don't auto-open thread-associated files in VSCode","createdAt":"2024-01-18T18:58:27Z"}
{"state":"Merged","mergedAt":"2024-01-19T19:48:20Z","number":10174,"mergeCommitSha":"07a80c72f72649f80bb19aba43ce6b44ebd6cb9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10174","title":"Upgrade to new exposed library","createdAt":"2024-01-18T19:31:02Z"}
{"state":"Merged","mergedAt":"2024-01-18T20:03:09Z","number":10175,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1705597539061789","mergeCommitSha":"cac245bb82b27ae229e30798788e23ee8fe8b043","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10175","title":"Allow impersonation in all environments","createdAt":"2024-01-18T19:39:29Z"}
{"state":"Merged","mergedAt":"2024-01-18T20:13:14Z","number":10176,"mergeCommitSha":"75e35b14907c52a0910f7af797af7720d1f78903","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10176","title":"Increase query compression timeout to 10s","createdAt":"2024-01-18T19:52:47Z"}
{"state":"Merged","mergedAt":"2024-01-19T20:06:23Z","number":10177,"body":"## Motivation\r\nhttps://chapter2global.slack.com/archives/C06E1MXEH1N/p1705344008329659\r\n\r\n\r\n## Changes\r\n\r\n- [x] filter incorporates the repo owner name\r\n- [x] smart filtering by tokenizing (consider generalizing this for other filters in the UI)\r\n- [x] repo row has secondary text for repo owner name\r\n  - [x] only show secondary text if subgroup present\r\n- [x] styling to actually make look like Ben's design\r\n\r\n\r\n## Current progress (no styling)\r\n<img width=\"797\" alt=\"Screenshot 2024-01-18 at 12 06 03\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/8fefacf2-90fd-4722-81bd-7183032270c5\">\r\n\r\n\r\nSupposed to be styled like this\r\n<img width=\"795\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/58ba8db2-8ead-467a-ace7-1187849322c7\">\r\n","mergeCommitSha":"1b3c946f186a38274cc422f501027f5c54d0894c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10177","title":"Show repo owner in repo selection component (RepoRow)","createdAt":"2024-01-18T19:58:58Z"}
{"state":"Merged","mergedAt":"2024-01-18T20:18:17Z","number":10178,"mergeCommitSha":"8b5f99ab33852b11c92a9d0a0d1936d55d62dfd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10178","title":"Add ability to get stack overflow questions from admin console","createdAt":"2024-01-18T20:04:38Z"}
{"state":"Merged","mergedAt":"2024-01-18T20:36:34Z","number":10179,"body":"Keeping the intercomContext as that implementation is shared with web components.\r\n\r\nIDEs will now launch dashboard and trigger the intercom experience there.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f5f0f83b-5a2c-4d0b-9997-a06e44236bef\r\n\r\n","mergeCommitSha":"0890fa2be8d8d7834ea2b95d24101347c90b43ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10179","title":"Update clients to navigate to intercom in dashboard","createdAt":"2024-01-18T20:15:47Z"}
{"state":"Merged","mergedAt":"2022-04-26T17:08:20Z","number":1018,"mergeCommitSha":"6cf9d33e8a1a6c7236fb56d385b947be6558ac24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1018","title":"Remove unnecessary roles","createdAt":"2022-04-26T17:08:12Z"}
{"state":"Merged","mergedAt":"2024-01-18T21:00:48Z","number":10180,"body":"Use the existing `createThreadStream`, which triggers when a stream is locally created, deleted, or restored, to re-query the API endpoint to get refreshed values.\r\n\r\nNo overlay because that seemed like overkill right now.","mergeCommitSha":"f766fee0c06189a36bd498c3a7bbe5db15e35bd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10180","title":"Remove threads from explorer UI on deletion","createdAt":"2024-01-18T20:49:29Z"}
{"state":"Merged","mergedAt":"2024-01-18T22:02:34Z","number":10181,"mergeCommitSha":"b26d5c33b4293d8f1b9328f40eab669374836c1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10181","title":"Stack overflow bots have accountId -1","createdAt":"2024-01-18T21:34:01Z"}
{"state":"Merged","mergedAt":"2024-01-18T22:18:44Z","number":10182,"mergeCommitSha":"7f670eefa899b1b651230491a20d5e5ecce99c38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10182","title":"Fix My Questions tab","createdAt":"2024-01-18T22:07:19Z"}
{"state":"Merged","mergedAt":"2024-01-18T23:12:49Z","number":10183,"mergeCommitSha":"51a7bac7fededa0e4e131a0c75ac98827ca61d54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10183","title":"Clean up Notion event generation logic","createdAt":"2024-01-18T22:36:52Z"}
{"state":"Merged","mergedAt":"2024-01-19T00:08:50Z","number":10184,"mergeCommitSha":"e4b7a0a0d3568518a28a9ad790da76d0ed681d01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10184","title":"Add ability to limit SO questions retrieved by max activity timestamp","createdAt":"2024-01-18T23:15:07Z"}
{"state":"Merged","mergedAt":"2024-01-19T00:29:54Z","number":10185,"mergeCommitSha":"5f1ec154ee40ffca53514dae2fe055aa907ec70f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10185","title":"Update emails to use check icon.","createdAt":"2024-01-18T23:21:52Z"}
{"state":"Merged","mergedAt":"2024-01-19T00:29:45Z","number":10186,"body":"Update intelliJ ask QA icon\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/991ad071-ee0e-4191-90be-70b676ac0d51\r\n\r\n","mergeCommitSha":"7c9618766382bb74e5bcc74848be6165d13c0393","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10186","title":"Update icon","createdAt":"2024-01-18T23:27:27Z"}
{"state":"Merged","mergedAt":"2024-01-18T23:41:24Z","number":10187,"mergeCommitSha":"b39d726a07cebf6b69bd913336bf751b38d2cc98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10187","title":"Revert \"Call IntegrationIngestionCompletionService.integrationIngestionCompleted after completing ingestion\"","createdAt":"2024-01-18T23:33:15Z"}
{"state":"Closed","mergedAt":null,"number":10188,"body":"This reverts commit b39d726a07cebf6b69bd913336bf751b38d2cc98.","mergeCommitSha":"4647d031ce71503b49a06954c3544623333e688d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10188","title":"Reenable \"Call IntegrationIngestionCompletionService.integrationIngestionCompleted after completing ingestion\"","createdAt":"2024-01-18T23:49:44Z"}
{"state":"Merged","mergedAt":"2024-01-19T00:16:57Z","number":10189,"body":"Show exporter insight data on IDE load (we previously waited until the first file was switched)\r\n","mergeCommitSha":"90c6ccde78e8b4c19fbb2c099b0aefac86e6a3f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10189","title":"Explorer UI -- show data immediately on startup","createdAt":"2024-01-18T23:54:44Z"}
{"state":"Merged","mergedAt":"2022-04-26T17:51:14Z","number":1019,"body":"ALB was mapping request paths to `/` and it was causing 404s! \r\ne.g `adminweb.dev.getunblocked.com/__shallowcheck` was being passed to service at `/__shallowcheck` but the service is expecting it `/api/__shallowcheck`\r\n\r\nAlso our Admin web pages are at `adminweb.{dev, prod}.getunblocked.com`","mergeCommitSha":"b2f32cccf142c5aad3389437264903f9a89105b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1019","title":"fix the api path","createdAt":"2022-04-26T17:35:17Z"}
{"state":"Merged","mergedAt":"2024-01-19T00:24:39Z","number":10190,"mergeCommitSha":"93b694017af8550479ba27bb210bc4025f2859c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10190","title":"Dont call ingestionCompletionService.enableTeam from IntegrationIngestionCompletionService","createdAt":"2024-01-19T00:11:28Z"}
{"state":"Merged","mergedAt":"2024-01-19T23:51:03Z","number":10191,"mergeCommitSha":"38f9bb10e0d1bbcdf3d73092f607b13a0c50a1cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10191","title":"Add ability to mark ingestion event as triggered by integration installation","createdAt":"2024-01-19T06:36:28Z"}
{"state":"Merged","mergedAt":"2024-01-19T19:05:41Z","number":10192,"body":"These are enabled by default now:\n- bitbucket featureEnabled\n- gitlab featureEnabled","mergeCommitSha":"eaf34117d726a55680226e60be1960c9ba716779","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10192","title":"Obsolete some SCM feature flags","createdAt":"2024-01-19T18:49:07Z"}
{"state":"Merged","mergedAt":"2024-01-19T19:40:28Z","number":10193,"body":"When this bug triggers, `setInterval` and `setTimeout` only trigger at most once a second.  I still don't fully understand why, but this is a reasonable workaround: use `requestAnimationFrame` repeatedly until the correct amount of time has passed.  `requestAnimationFrame` does not seem to be affected in the same way as `setInterval`.","mergeCommitSha":"774de7b0367aa6e4a8a783be7143767c5cd7a6c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10193","title":"Fix message streaming VSCode bug","createdAt":"2024-01-19T19:28:42Z"}
{"state":"Merged","mergedAt":"2024-01-19T22:41:43Z","number":10194,"body":"Turns out, if you just return an empty string, it won't render the tab \uD83E\uDD37 \r\n\r\n![CleanShot 2024-01-19 at 14 28 40@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/d93e0d0d-0a15-4f15-a90d-d9c0a772cab9)\r\n","mergeCommitSha":"f3521bc7c52b3e1860057e43e85bf6a32663d334","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10194","title":"Update sidebar titles in IntelliJ","createdAt":"2024-01-19T22:29:20Z"}
{"state":"Merged","mergedAt":"2024-01-19T23:05:19Z","number":10195,"body":"* `FocusActionStream` is a new stream that allows UIs to request focus in other UIs.\r\n* The 'Ask Unblocked' button now uses this stream to request focus be set in the Ask Unblocked input","mergeCommitSha":"c652923c9252706c9ad859f352fc5d4c68a9476b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10195","title":"'Ask Unblocked' button now sets focus correctly","createdAt":"2024-01-19T22:53:14Z"}
{"state":"Closed","mergedAt":null,"number":10196,"mergeCommitSha":"e41fcf679c2e4c6c2f58649c504ba56c2d94a7c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10196","title":"Add activity feed API","createdAt":"2024-01-19T23:48:28Z"}
{"state":"Merged","mergedAt":"2024-01-20T00:23:48Z","number":10197,"mergeCommitSha":"e07efbe77d6aa183b203208926663bdc39f43b78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10197","title":"Create IngestionProgressService to track the progress of ingesting an installation","createdAt":"2024-01-19T23:59:49Z"}
{"state":"Merged","mergedAt":"2024-01-20T00:50:26Z","number":10198,"mergeCommitSha":"b1bf62050f372346ab2ddb8c3278ac2f7a5947da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10198","title":"Version Page in admin web is filterable","createdAt":"2024-01-20T00:26:00Z"}
{"state":"Merged","mergedAt":"2024-01-22T21:30:15Z","number":10199,"mergeCommitSha":"252927a98daf41176199aa4ee62ed731745a055f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10199","title":"Increment ingestion progress when an event is marked as isInitialIngestion=true","createdAt":"2024-01-20T00:48:41Z"}
{"state":"Merged","mergedAt":"2022-01-21T23:34:57Z","number":102,"body":"Tiny PR to add replicaset permissions for deploybot user ","mergeCommitSha":"3e70acdc544f3c6d495dfc55ddc25a5549a9de24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/102","title":"add replicaset access for deployer user","createdAt":"2022-01-21T23:18:35Z"}
{"state":"Merged","mergedAt":"2022-04-26T20:39:38Z","number":1020,"body":"environment object must be passed into the webview in vscode in order for the proper environment data to be parsed -- update other clients to behave similarly\r\n\r\nslack: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1650987270566339","mergeCommitSha":"18d311dcdf67fe4b073970396864adff685d4491","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1020","title":"Fix baseurl bug in vscode","createdAt":"2022-04-26T17:46:50Z"}
{"state":"Merged","mergedAt":"2024-01-22T20:03:37Z","number":10200,"body":"@cancelself @richiebres Please review responsibilities / requirements\r\n\r\n@benedict-jw @matthewjamesadam could you make sure I didn't break any routing etc?","mergeCommitSha":"d300d951fb8ea3788b2798f6816d018c8d160d41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10200","title":"Add Data Engineer JD","createdAt":"2024-01-21T21:54:13Z"}
{"state":"Merged","mergedAt":"2024-01-22T02:17:39Z","number":10201,"mergeCommitSha":"2b4f5283b87a0557cb9896f3bb539cec0bca4a8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10201","title":"Fix mailto bug","createdAt":"2024-01-22T02:05:42Z"}
{"state":"Merged","mergedAt":"2024-01-22T15:47:12Z","number":10202,"mergeCommitSha":"e3240c5044c327e9f4047b95d96eea251d7c1c78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10202","title":"[SKIP TESTS] scale up pusher memory and pod count","createdAt":"2024-01-22T15:38:03Z"}
{"state":"Merged","mergedAt":"2024-01-22T16:50:41Z","number":10203,"mergeCommitSha":"129f70fc880cf9aebae8661438c7a5ef7ff242fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10203","title":"[SKIP TESTS] scale pusher up","createdAt":"2024-01-22T16:41:55Z"}
{"state":"Merged","mergedAt":"2024-01-22T17:20:07Z","number":10204,"mergeCommitSha":"8690c79ddeff6dac891b99d7c3b5ba6b4d93637e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10204","title":"Submodule bump - Data Engineer Image","createdAt":"2024-01-22T17:15:51Z"}
{"state":"Merged","mergedAt":"2024-01-22T18:48:08Z","number":10205,"mergeCommitSha":"bde2bc13bb9e1739c3aebb7f5cd3b87fea22802a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10205","title":"Remove no longer used team setting","createdAt":"2024-01-22T18:12:43Z"}
{"state":"Merged","mergedAt":"2024-01-22T19:18:27Z","number":10206,"body":"Motivation for change is that the button is unnecessary. There are two reasons:\n\n1. the relevant repo APIs (eg `listScmInstallationRepos`) are not paged, so there is no _performance_ penalty to showing all\n2. there is a persistent chin in the UI that is visible regardless of how many repos are in the list","mergeCommitSha":"65daea29223129ff40b131e8c81229b30a51a3d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10206","title":"Remove the \"Show More\" button on repos page","createdAt":"2024-01-22T18:22:20Z"}
{"state":"Merged","mergedAt":"2024-01-22T18:26:11Z","number":10207,"body":"- Add count rule for total pusher calls per user. \r\n- Reduce per-user per-client pusher limit to 500","mergeCommitSha":"b0e4ec2e800ba0d9bc7abaeafbb2684aed0a9538","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10207","title":"Update Pusher rate limit rules","createdAt":"2024-01-22T18:22:23Z"}
{"state":"Open","mergedAt":null,"number":10208,"body":"Backend support for \r\n\r\n<img width=\"534\" alt=\"CleanShot 2024-01-22 at 11 40 24@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/109ad21c-3d53-486f-959d-13dfd4a91365\">\r\n","mergeCommitSha":"f7da3573c0e6aabc230d2ebbc2f296f5ea3f1999","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10208","title":"Add team setting to override the web ingestion site limit","createdAt":"2024-01-22T18:50:02Z"}
{"state":"Merged","mergedAt":"2024-01-22T19:28:57Z","number":10209,"body":"- Archived repos were already filtered out anyway. This change just makes it more efficient.\r\n- GitLab has a concept of sharing repos with other groups. This is just confusing, so we're not showing these repos in Unblocked.","mergeCommitSha":"e4c78512fe6eb58c2c6c67e081ad55fdce55fadc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10209","title":"Do not include archived or shared-with-group GitLab repos","createdAt":"2024-01-22T19:18:50Z"}
{"state":"Merged","mergedAt":"2022-04-26T23:44:16Z","number":1021,"mergeCommitSha":"9d1f644668b983abaf4a96d672c418f8d3c30cda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1021","title":"Add adminweb team pages","createdAt":"2022-04-26T18:13:00Z"}
{"state":"Merged","mergedAt":"2024-01-24T00:39:21Z","number":10210,"body":"Ask Question Selected State\n\nRemove webviewEventHandler selected Thread ID\n\nAdd selected state to explorer insights discussion tab","mergeCommitSha":"70497b59f497344ac5cc3f061bc1251703b0decc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10210","title":"Selected State for Sidebars","createdAt":"2024-01-22T19:22:10Z"}
{"state":"Merged","mergedAt":"2024-01-22T20:07:14Z","number":10211,"body":"## Changes\n- `forked_from_project` field returns a \"simple\" GitLab project rather than a full project\n\n## Bug\n```\nk.s.MissingFieldException: Fields [visibility, archived] are required for type with serial name 'com.nextchaptersoftware.scm.gitlab.models.GitLabRepo', but they were missing\n\tat k.s.i.PluginExceptionsKt.throwMissingFieldException(PluginExceptions.kt:20)\n\tat c.n.s.g.m.GitLabRepo.<init>(GitLabRepo.kt:10)\n\tat c.n.s.g.m.GitLabRepo$$serializer.deserialize(GitLabRepo.kt:10)\n\tat c.n.s.g.m.GitLabRepo$$serializer.deserialize(GitLabRepo.kt:10)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\t... 57 common frames omitted\nWrapped by: k.s.MissingFieldException: Fields [visibility, archived] are required for type with serial name 'com.nextchaptersoftware.scm.gitlab.models.GitLabRepo', but they were missing at path: $[40].forked_from_project\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:93)\n\tat k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\n\tat k.s.e.AbstractDecoder.decodeNullableSerializableElement(AbstractDecoder.kt:79)\n\tat c.n.s.g.m.GitLabRepo$$serializer.deserialize(GitLabRepo.kt:10)\n\tat c.n.s.g.m.GitLabRepo$$serializer.deserialize(GitLabRepo.kt:10)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\tat k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\n\tat k.s.e.AbstractDecoder.decodeSerializableElement(AbstractDecoder.kt:70)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableElement(StreamingJsonDecoder.kt:165)\n\tat k.s.e.CompositeDecoder$DefaultImpls.decodeSerializableElement$default(Decoding.kt:533)\n\tat k.s.i.CollectionLikeSerializer.readElement(CollectionSerializers.kt:80)\n\tat k.s.i.AbstractCollectionSerializer.readElement$default(CollectionSerializers.kt:51)\n\tat k.s.i.AbstractCollectionSerializer.merge(CollectionSerializers.kt:36)\n\tat k.s.i.AbstractCollectionSerializer.deserialize(CollectionSerializers.kt:43)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\tat k.s.json.Json.decodeFromString(Json.kt:107)\n```","mergeCommitSha":"8e7730b5fc6921ae73c78cadd91e16b21747637f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10211","title":"Fix GitLab listScmInstallationRepos API 500 due to missing fields on fork repo","createdAt":"2024-01-22T19:52:16Z"}
{"state":"Merged","mergedAt":"2024-01-22T20:25:52Z","number":10212,"mergeCommitSha":"fc63f61225cecdb379743fff9b59df66d9c1bf50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10212","title":"Refactor GitLab repo APIs to fix another instance of crashing/filtering","createdAt":"2024-01-22T20:25:02Z"}
{"state":"Merged","mergedAt":"2024-01-22T22:03:17Z","number":10213,"mergeCommitSha":"16f35cc9c69833820717345f06639a38ebf28e50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10213","title":"Fix mailto typo on Data Engineer page","createdAt":"2024-01-22T20:44:46Z"}
{"state":"Merged","mergedAt":"2024-01-22T21:10:22Z","number":10215,"mergeCommitSha":"f9613caa876330affe532432ef15a260af11dc9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10215","title":"Fix data engineer preview image","createdAt":"2024-01-22T21:07:30Z"}
{"state":"Merged","mergedAt":"2024-01-22T21:52:03Z","number":10216,"mergeCommitSha":"bcffdae94787e0996d764dee2a81302c3fcded32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10216","title":"Fix the preview (again)","createdAt":"2024-01-22T21:51:33Z"}
{"state":"Merged","mergedAt":"2024-01-22T22:13:17Z","number":10217,"body":"Not fully working, but initial skeleton is there.\r\n\r\n## login\r\n<img width=\"1109\" alt=\"Screenshot 2024-01-22 at 14 11 44\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/55f122ba-30de-4568-a89b-c857938c6500\">\r\n\r\n## add new host\r\n<img width=\"983\" alt=\"Screenshot 2024-01-22 at 14 11 14\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/83a03ece-53c6-4463-9a69-79cfbb408521\">\r\n","mergeCommitSha":"7cef6311ce75fd0cbb8cffd4ae86d6621b8d5838","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10217","title":"Enable GitLab Self-hosted in DEV","createdAt":"2024-01-22T22:10:48Z"}
{"state":"Merged","mergedAt":"2024-01-22T22:53:28Z","number":10218,"mergeCommitSha":"75f778e78555150f8423d8e0e2ad07325b42c608","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10218","title":"Add ability to get percentage progress for ingestion","createdAt":"2024-01-22T22:20:04Z"}
{"state":"Merged","mergedAt":"2024-01-23T17:04:44Z","number":10219,"body":"This means we don't need to pull the store content into the traits files","mergeCommitSha":"53b102d2e2cfed1e41ea2f220628e46fa787d939","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10219","title":"Detach StreamProxy traits from stream registration","createdAt":"2024-01-22T22:46:29Z"}
{"state":"Merged","mergedAt":"2024-01-22T23:43:51Z","number":10220,"mergeCommitSha":"ce1f261177e2452d7e579b2b1023a3d378bf9072","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10220","title":"Create NoopIngestionProgressService","createdAt":"2024-01-22T23:09:04Z"}
{"state":"Merged","mergedAt":"2024-01-30T03:47:59Z","number":10221,"body":"Add AuthStateCache to manage previously registered enterprise.\n\n","mergeCommitSha":"362b95db00004db52fab18ef176fdf71d5ef459d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10221","title":"Setup previously signedin enterprise","createdAt":"2024-01-22T23:24:57Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:13:54Z","number":10222,"body":"Implements file commit history call with GQL.\r\n\r\nGitHub only for now\r\n\r\nOnly currently pulling the following data:\r\n- commit sha\r\n- date\r\n- author name\r\n- author external id","mergeCommitSha":"35ef499386e1a219cc9a5463b8ffdfc78b0c2e79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10222","title":"Implement file commit history SCM API query","createdAt":"2024-01-23T00:14:53Z"}
{"state":"Merged","mergedAt":"2024-01-23T05:15:27Z","number":10223,"body":"Changed UnregisteredEnterpriseProvider:\r\n- Add `oauthUrl` for GitLab Self-hosted\r\n- Add `encryptionPublicKey` for GitLab Self-hosted","mergeCommitSha":"c1cfcbe5374e5b33f77f9b3831fc3a833e440a4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10223","title":"Add GitLab support to findEnterpriseProviders API","createdAt":"2024-01-23T00:28:09Z"}
{"state":"Merged","mergedAt":"2024-01-23T05:38:34Z","number":10224,"body":"Splits the legacy `createEnterpriseProvider` into two:\n\n- createGitHubEnterpriseProvider\n\n- createGitLabEnterpriseProvider","mergeCommitSha":"e339aeb6e22e575ec381c6f5dc15098717e76333","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10224","title":"Support GitLab in the createEnterpriseProvider API","createdAt":"2024-01-23T01:48:41Z"}
{"state":"Merged","mergedAt":"2024-01-23T03:26:45Z","number":10225,"mergeCommitSha":"c426c3b37fd7024841dc4e06cc84bb9fd689e121","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10225","title":"Add semantic match analysis","createdAt":"2024-01-23T03:25:38Z"}
{"state":"Merged","mergedAt":"2024-01-23T06:45:35Z","number":10226,"mergeCommitSha":"fe0e606fb2900073b845e0e957fdc7f2f479078d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10226","title":"Add VPC ID to SSM so we could use it in other workflows","createdAt":"2024-01-23T06:44:32Z"}
{"state":"Merged","mergedAt":"2024-01-23T07:19:19Z","number":10227,"mergeCommitSha":"49dbf3e96778453898c3687597894492d093a038","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10227","title":"Add permissions needed for Slack notification action","createdAt":"2024-01-23T06:53:24Z"}
{"state":"Merged","mergedAt":"2024-01-23T21:37:11Z","number":10228,"mergeCommitSha":"5c739273732d2f1815fddd0a710505fff4541fc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10228","title":"Create job to periodically check and update ingestion completed status","createdAt":"2024-01-23T07:09:20Z"}
{"state":"Merged","mergedAt":"2024-01-23T10:58:07Z","number":10229,"mergeCommitSha":"5027ef3fde7b199a4ec8d4de5e7406e8d6bfe1d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10229","title":"[SKIP TESTS] trying deal with pusher 137 exit codes","createdAt":"2024-01-23T10:52:18Z"}
{"state":"Merged","mergedAt":"2024-01-23T18:58:09Z","number":10230,"body":"Being crushed by applications that aren't relevant.. maybe this helps","mergeCommitSha":"95ce9f037660c3f9226ce2b4b555b748232df9b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10230","title":"Update Data Engineer JD","createdAt":"2024-01-23T17:12:06Z"}
{"state":"Closed","mergedAt":null,"number":10231,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1706009032920129","mergeCommitSha":"ae2a3298a02242ec5297658de472e64f4e3ccc5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10231","title":"Downsample pusher service `/unreads` channel by 5X","createdAt":"2024-01-23T17:23:47Z"}
{"state":"Merged","mergedAt":"2024-01-23T18:14:59Z","number":10232,"mergeCommitSha":"e4d34a56e85587a8f6edeebea6fa716b909b7125","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10232","title":"Double reader instances","createdAt":"2024-01-23T17:37:44Z"}
{"state":"Merged","mergedAt":"2024-01-23T18:19:43Z","number":10233,"mergeCommitSha":"7d751441c81dce41f9d2f0d6a96f3906b5c7e7df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10233","title":"Pusher is limited to 60 per 5 mins","createdAt":"2024-01-23T18:12:37Z"}
{"state":"Merged","mergedAt":"2024-01-23T19:38:58Z","number":10234,"body":"Data processing and basic UI for the new tabbed explorer insights UI:\r\n\r\n* Replace the old `overview` and `discussions` tabs with `overview`, `docs`, and `pullRequests`.  `overview` is only relevant in the old UX, `docs` is only relevant in the new UX, and `pullRequests` are relevant in both\r\n* When using the new UX, the source stream merges the pull request and semantic search streams for the file.  The old UX only uses the pull request stream.\r\n* This PR does not update the row content or layout, that will come later today\r\n* This PR does not add badges, that will come later today\r\n* JB tabbed stuff coming later today\r\n\r\n![CleanShot 2024-01-23 at 10 49 08@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/ec41fb5d-9ea6-4600-9c74-023db9d1dce8)\r\n","mergeCommitSha":"ceeab471c8ecd8a794c2802c805d712ab7fb1d7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10234","title":"New tabbed explorer insights UI","createdAt":"2024-01-23T18:59:44Z"}
{"state":"Merged","mergedAt":"2024-01-23T20:14:37Z","number":10235,"mergeCommitSha":"5539234bc92c122e0b7195cbc050aa59d392d13b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10235","title":"Remove thread join on /unreads channel and add teamId in query","createdAt":"2024-01-23T19:12:03Z"}
{"state":"Merged","mergedAt":"2024-01-23T19:37:53Z","number":10236,"mergeCommitSha":"a88a8c4a7d5aadff3011d94a83ebfa7b732f7b88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10236","title":"Add prod pusher refinery","createdAt":"2024-01-23T19:37:46Z"}
{"state":"Merged","mergedAt":"2024-01-23T20:08:10Z","number":10237,"body":"* Reduce overall padding\r\n* Reduce line heights\r\n* Reduce icon size\r\n* Remove footers","mergeCommitSha":"6a40dba0a317ad57d14fa92cf7b8b37e97b5e13a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10237","title":"More compact sidebar rows to match new designs","createdAt":"2024-01-23T19:40:20Z"}
{"state":"Merged","mergedAt":"2024-01-23T20:57:22Z","number":10238,"body":"Partially revert #10233.\n\nWorks out to 1 per second on average.","mergeCommitSha":"c2b3d21a109b3460ddba1ab12be2e37fe94523cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10238","title":"Allow users to make up to 300 pusher requests per 5 min","createdAt":"2024-01-23T20:53:07Z"}
{"state":"Merged","mergedAt":"2024-01-23T23:30:18Z","number":10239,"body":"Go to a two-tab view for JB IDE insights tool window.\r\n\r\nI ended up duplicating the \"Discussions\" and \"Pull Requests\" bundles, because there was no other (easy) way to differentiate between the views, to give them different labels.  But their content is the same.","mergeCommitSha":"107118f8207142683c7fc0bec724539d8c31dc9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10239","title":"New explorer insights UI in JB IDEs","createdAt":"2024-01-23T21:02:44Z"}
{"state":"Merged","mergedAt":"2024-01-23T21:53:59Z","number":10240,"mergeCommitSha":"f36712514323baf54599c927da53bfaf1271b1be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10240","title":"Fix header matching and make pusher per-user rule more aggressive","createdAt":"2024-01-23T21:52:58Z"}
{"state":"Merged","mergedAt":"2024-01-23T21:58:16Z","number":10241,"mergeCommitSha":"bd3dd758fca8e4168a8e66cc9c19baccd4306b88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10241","title":"Remove the extra reader instance we added this morning","createdAt":"2024-01-23T21:57:27Z"}
{"state":"Merged","mergedAt":"2024-01-23T22:51:04Z","number":10242,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1706047335024459\r\n\r\nI took the N-worst logs by volume.","mergeCommitSha":"951b859dc824b5578cf4efa44e385ded9c93264c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10242","title":"[RFC] Remove spammy logs","createdAt":"2024-01-23T22:27:20Z"}
{"state":"Merged","mergedAt":"2024-01-23T23:13:26Z","number":10243,"mergeCommitSha":"f4c07cd3f1729eb5b8abe2ec0ac33d7332c410c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10243","title":"Only embed latest docs for React Native demo team","createdAt":"2024-01-23T22:58:47Z"}
{"state":"Merged","mergedAt":"2024-01-23T23:28:41Z","number":10244,"body":"Used this tool:\r\n```\r\nprojects/scripts/src/main/kotlin/com/nextchaptersoftware/scripts/security/GenerateRSAKeyPair.kt\r\n```\r\n\r\nUsed 256 bit length; seems enough considering the key is used to encrypt a string of length 64 chars.\r\n\r\n","mergeCommitSha":"3cc63245bfdd333dcfd7ed83335ec3bc7ea1ff07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10244","title":"GitLab Self-hosted end-2-end encryption keys for handling OAuth2 App Secrets","createdAt":"2024-01-23T23:12:47Z"}
{"state":"Merged","mergedAt":"2024-01-23T23:53:21Z","number":10245,"mergeCommitSha":"30a76d20376a3d3b7dcc70a005dcb5f500302c16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10245","title":"GitLab key is 2048 bits","createdAt":"2024-01-23T23:37:37Z"}
{"state":"Merged","mergedAt":"2024-01-25T00:09:03Z","number":10246,"mergeCommitSha":"9525eeb0d3666bc1b6fdc83dd08e8c2c8ab8ab77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10246","title":"Add 'who is the expert for...' ML function","createdAt":"2024-01-23T23:41:30Z"}
{"state":"Merged","mergedAt":"2024-01-23T23:56:51Z","number":10247,"mergeCommitSha":"16439b23065fb399bb087d375e3db31be8ce4c63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10247","title":"[SKIP TESTS] Launch dropExcludedUrlsForSite in a coroutine","createdAt":"2024-01-23T23:48:50Z"}
{"state":"Merged","mergedAt":"2024-01-24T00:55:17Z","number":10248,"body":"Setup Distinct experts on client side.\n","mergeCommitSha":"703eeb65b2afd0e86c1784823621d652b24162e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10248","title":"Setup distinct experts","createdAt":"2024-01-23T23:59:15Z"}
{"state":"Merged","mergedAt":"2024-01-24T00:22:28Z","number":10249,"body":"![CleanShot 2024-01-23 at 16 08 42@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/af1a8b7e-945e-4780-aedf-a112db7d0415)\r\n![CleanShot 2024-01-23 at 16 07 21@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/35057d4a-b679-4639-8980-56dbb9e4309c)\r\n","mergeCommitSha":"58baebe42c651ae7777e93bae03324cb56b85f68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10249","title":"Add explorer panel badges","createdAt":"2024-01-24T00:10:24Z"}
{"state":"Merged","mergedAt":"2022-04-26T18:57:07Z","number":1025,"mergeCommitSha":"4bbe2a1a7cdc097e1fe2b16b46d5126679189ea2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1025","title":"Fix shit","createdAt":"2022-04-26T18:33:10Z"}
{"state":"Merged","mergedAt":"2024-01-24T18:13:25Z","number":10250,"body":"Initial setup for Gitlab Manifest flow.\r\n\r\n** Not guaranteed to work. Untested API requests**\r\n\r\n![CleanShot 2024-01-23 at 16 42 25@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/21830400-e568-46f1-8ffb-b8a08a2c86de)\r\n\r\n\r\n![CleanShot 2024-01-23 at 16 24 15@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/0ac63e34-c625-4fb0-bf9b-57038ae5c4a4)\r\n\r\n","mergeCommitSha":"9d592a7d4e892eefdc58e33d6d05ee6bf72c114f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10250","title":"Initial setup for GL Manifest Flow","createdAt":"2024-01-24T00:15:26Z"}
{"state":"Merged","mergedAt":"2024-01-24T00:46:13Z","number":10251,"body":"PRs then Docs, at least for now.","mergeCommitSha":"994ce396732b1d1f46dfec925520b75b9ea01069","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10251","title":"Swap explorer UI tab order","createdAt":"2024-01-24T00:33:02Z"}
{"state":"Merged","mergedAt":"2024-01-24T00:42:08Z","number":10252,"mergeCommitSha":"333d6377b9d7657563175e983d8438cf6d646771","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10252","title":"Update llm prompt for topics","createdAt":"2024-01-24T00:41:55Z"}
{"state":"Merged","mergedAt":"2024-02-10T00:23:57Z","number":10253,"mergeCommitSha":"7a66e30aa0286cac61b59f150ade0656fdc7ea76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10253","title":"Track progress of web ingestion based on the percentage of events handled","createdAt":"2024-01-24T00:43:49Z"}
{"state":"Merged","mergedAt":"2024-01-24T20:47:38Z","number":10254,"body":"So we can test other reranking/cross-encoder mechanisms","mergeCommitSha":"235a0130aeb1b1e095b8e65dd023748f3b969b3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10254","title":"Make DocumentRerankService an interface","createdAt":"2024-01-24T03:17:10Z"}
{"state":"Merged","mergedAt":"2024-01-24T04:10:08Z","number":10255,"mergeCommitSha":"b47594608268035db0ff21391e343a9ed6b1af3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10255","title":"Fix up topic and experts","createdAt":"2024-01-24T03:25:34Z"}
{"state":"Merged","mergedAt":"2024-01-24T17:20:35Z","number":10256,"mergeCommitSha":"ecf5ed288c6c41bbf1bc4afb0e059665cd25fd7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10256","title":"Fix dropExcludedUrlsForSite","createdAt":"2024-01-24T05:35:48Z"}
{"state":"Merged","mergedAt":"2024-01-24T07:26:07Z","number":10257,"body":"Latency issue caused by subquery join on PullRequest table, which generates an enormous number of identical repo result rows — one repo row for every PR for that repo. Use distinct solves this.\r\n\r\nThere was a potential correctness problem too, since the output query did not have a distinct either, but this would only be a problem if limit was greater than 1.\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1706041616852609?thread_ts=1706009032.920129&cid=C02HEVCCJA3","mergeCommitSha":"9b56800afe5180be94392e7c51fb9ae9cb20c27e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10257","title":"Fix slow DB query in RepoPullRequestIngestionService","createdAt":"2024-01-24T07:16:34Z"}
{"state":"Merged","mergedAt":"2024-01-24T09:00:28Z","number":10258,"mergeCommitSha":"716c98a9dd68a8b4a2ed18797efdeea1cda457a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10258","title":"SCM host detection refactor","createdAt":"2024-01-24T08:36:31Z"}
{"state":"Merged","mergedAt":"2024-01-24T20:34:02Z","number":10259,"body":"This is just a vanity change. Manifest just looks weird in the web URL.","mergeCommitSha":"e6f284cf4ea81ea1ea791b53daf2bbaa54df472c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10259","title":"Rename 'manifest' route to 'enterprise'","createdAt":"2024-01-24T17:23:17Z"}
{"state":"Merged","mergedAt":"2022-04-26T19:38:16Z","number":1026,"body":"Turns out there was no VPN issue. We just needed to use https!! \r\n`https://adminweb.dev.getunblocked.com/api/__shallowcheck` is working fine now. \r\n\r\n- Modified paths so it doesn't do any prefix matching and forwards everything to admin web service\r\n- Updated the secondary health probe path to match routes.kt\r\n- Updated ALB health probe to get rid of those annoying 404s in logs ","mergeCommitSha":"ed4c0ef7e842c4254064fe9bb872f8c25147d661","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1026","title":"fix admin web paths","createdAt":"2022-04-26T19:26:36Z"}
{"state":"Merged","mergedAt":"2024-01-24T17:51:22Z","number":10260,"body":"In the new explorer UI, the 'PR' tab should only display PR threads.  Filter out any non-PR (QA, notes) threads.","mergeCommitSha":"3359da9e5a720e94b6a80c678faaaf9276dff38c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10260","title":"Filter out non-PR threads in new explorer UI 'PR' tab","createdAt":"2024-01-24T17:29:35Z"}
{"state":"Merged","mergedAt":"2024-01-24T18:11:05Z","number":10261,"mergeCommitSha":"0b032765c965d0087594ebb65202005b7f89dd1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10261","title":"Add excluded URLs for dev React Native demo team","createdAt":"2024-01-24T18:00:31Z"}
{"state":"Merged","mergedAt":"2024-01-24T18:25:25Z","number":10262,"mergeCommitSha":"b7bee3e9548bf25447ff650fc8532dc73470f83a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10262","title":"[SKIP TESTS] Do not show slack","createdAt":"2024-01-24T18:25:19Z"}
{"state":"Merged","mergedAt":"2024-01-24T18:55:48Z","number":10263,"mergeCommitSha":"b3c5287820ac15c4134bca811040160744805f14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10263","title":"Skip cache check when enqueuing a web ingestion event for retrying later","createdAt":"2024-01-24T18:42:07Z"}
{"state":"Merged","mergedAt":"2024-01-24T19:47:12Z","number":10264,"body":"This change grants VPC access to the processing jobs. Hopefully it will force all internet bound calls to go through the VPC. ","mergeCommitSha":"048bf7983906796bcdf0a70ce3ca13147e7faaa4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10264","title":"Use NAT for processing job internet access","createdAt":"2024-01-24T19:03:58Z"}
{"state":"Merged","mergedAt":"2024-01-24T21:21:13Z","number":10265,"body":"\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/b91b93a6-2a52-4888-90c3-b1d2c0bf9b0f \r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/7627b143-217a-4a28-a8f8-d34f12b1de60\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f4517bbe-1d8b-4408-bc23-2ec78265b388\r\n\r\n\r\nLet me know if you want to change the styles @benedict-jw ","mergeCommitSha":"a9993ec42578cfcd6183e201a020c20d0b12aac0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10265","title":"Copy code block","createdAt":"2024-01-24T19:05:15Z"}
{"state":"Merged","mergedAt":"2024-01-24T19:26:58Z","number":10266,"body":"* The new explorer insights UI always sorts PRs by recency\r\n* The old explorer insights UI sorts by the selected sorting criteria","mergeCommitSha":"16a4a5817c4eabbf0bfb1e4195ce86140cc0228b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10266","title":"Fix sorting in explorer UI","createdAt":"2024-01-24T19:15:06Z"}
{"state":"Merged","mergedAt":"2024-01-24T20:01:03Z","number":10267,"mergeCommitSha":"76de50cad76ebe14ef9a79ff601aa86c87d714f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10267","title":"Fix WebIngestionUrlValidationService to only check whether a domain ends with a denied domain","createdAt":"2024-01-24T19:27:52Z"}
{"state":"Merged","mergedAt":"2024-01-24T21:02:31Z","number":10268,"body":"For some reason, updating the JB tab title causes the tool window to stop updating.  I'll dig into this at some point, it'll probably require building a debug IJ instance, but for now, removing the title updating so we're unblocked.","mergeCommitSha":"582e24b1560107f28d4db72ee933800f65d71faf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10268","title":"Remove JB tab updating","createdAt":"2024-01-24T19:42:29Z"}
{"state":"Merged","mergedAt":"2024-01-24T20:46:23Z","number":10269,"body":"![CleanShot 2024-01-24 at 11 51 31@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/8cec86d1-cb25-42d5-9e7a-4ed228071a40)\r\n","mergeCommitSha":"cb0dc2eeb975a5e13de4b35bc4887260f5e8331a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10269","title":"Add UI to show firewall error with IP addresses from meta config","createdAt":"2024-01-24T19:52:37Z"}
{"state":"Merged","mergedAt":"2022-04-26T20:28:00Z","number":1027,"body":"- Renamed CName record to `admin.{dev, prod}.getunblocked.com`\r\n- Regenerated SSL certs \r\n- Modified ALB configs to use new certs \r\n- Removed old unused wildcard cert","mergeCommitSha":"c2aa0ebbb636fb4c4f12d33f1c354ea5175e19f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1027","title":"fix cname for admin web","createdAt":"2022-04-26T20:13:03Z"}
{"state":"Merged","mergedAt":"2024-01-24T20:52:17Z","number":10270,"mergeCommitSha":"5b6a412b6e436714b99099897239290615e340c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10270","title":"Add ability to delete a demo template website","createdAt":"2024-01-24T20:01:35Z"}
{"state":"Merged","mergedAt":"2024-01-24T21:06:41Z","number":10271,"body":"I tweaked the message a bit\r\n<img width=\"957\" alt=\"Screenshot 2024-01-24 at 12 12 01\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/9c0b70ea-f72e-406f-a16c-d6b9a7fe18a4\">\r\n\r\n\r\nRelated UI change #10269.\r\n\r\nRelated discussion\r\nhttps://chapter2global.slack.com/archives/C06E1MXEH1N/p1706123031719389","mergeCommitSha":"dfb2a6ec09ce251b201f3f3ad23b907ff7d18a31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10271","title":"Update enterprise IP address allowlist message","createdAt":"2024-01-24T20:11:38Z"}
{"state":"Merged","mergedAt":"2024-01-24T21:11:15Z","number":10272,"body":"The expanded state in the Slack-in-PR view works via `useLocalStorageState`.  We had been setting a resolving value of `true` for this state, which meant that while the state was loading, we would render the view with an expanded UI, which mean if the state ultimately resolved to `false`, you'd see the UI flicker while the data was loading.\r\n\r\nI added `*Optional` variants of the `useLocalStorageState` methods, which return an `undefined` value while the value is loading.  In this case it means we can simply not show the UI until we know the final state.\r\n\r\n![CleanShot 2024-01-24 at 12 45 33@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/cf1696c0-13c6-4499-bc88-98a639d4899e)\r\n","mergeCommitSha":"dfc289580abf694028eac3eaf8d3abf8ce623391","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10272","title":"Reduce slack-in-PR header flicker","createdAt":"2024-01-24T20:47:33Z"}
{"state":"Merged","mergedAt":"2024-01-24T21:28:45Z","number":10273,"body":"Adding user secrets to search service. These secrets are automatically loaded as env vars. \r\n\r\nHere's an example of how we pass multiple secrets to a service: https://github.com/NextChapterSoftware/unblocked/blob/dfc289580abf694028eac3eaf8d3abf8ce623391/projects/services/scmservice/.helm/scmservice/values-prod-us-west-2.yaml#L13","mergeCommitSha":"550a53f97460ebb78a2abf96979201213b7e200a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10273","title":"Add user secrets to search service","createdAt":"2024-01-24T21:19:52Z"}
{"state":"Merged","mergedAt":"2024-01-24T21:39:23Z","number":10274,"mergeCommitSha":"d1e1d7b7894740062aeac2e977499379cf4c2be4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10274","title":"[SKIP TESTS] Filter out private threds","createdAt":"2024-01-24T21:38:31Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:32:39Z","number":10275,"body":"Add \"getEnterpriseProvider\" APi to get Provider by ID.\r\n\r\nWill allow for less stateful routes on dashboard + reentrant behaviour (aka refresh)\r\n\r\n![CleanShot 2024-01-24 at 13 54 26@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/55d59c21-a924-48b4-b67a-d2400f1668f9)\r\n","mergeCommitSha":"198c5fd8d096c775dbceb1b6ab362f7f9dd1a518","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10275","title":"Get Enterprise Provider by ID","createdAt":"2024-01-24T21:54:34Z"}
{"state":"Closed","mergedAt":null,"number":10276,"mergeCommitSha":"bb190ac796ec50d26ecb6270f0b73715b9c86ba6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10276","title":"Add vpc networking permissions","createdAt":"2024-01-24T22:00:40Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:02:51Z","number":10277,"body":"Provided role overrides construct role configuraiton","mergeCommitSha":"bab877afce7fc28b097680d41a236e9ce6e6c7a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10277","title":"Sagemaker processing job role needs network interfce permissions","createdAt":"2024-01-24T22:01:58Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:17:44Z","number":10278,"body":"```\r\nException in thread \"main\" java.lang.ExceptionInInitializerError\r\n\tat com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder.<init>(ServiceBootstrapBuilder.kt:28)\r\n\tat com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder$Companion.bootstrap(ServiceBootstrapBuilder.kt:32)\r\n\tat com.nextchaptersoftware.searchservice.ApplicationKt.main(Application.kt:7)\r\n\tat com.nextchaptersoftware.searchservice.ApplicationKt.main(Application.kt)\r\nCaused by: com.sksamuel.hoplite.ConfigException: Error loading config because:\r\n\r\n    - Could not instantiate 'com.nextchaptersoftware.config.GlobalConfig' because:\r\n\r\n        - 'agora': - Could not instantiate 'com.nextchaptersoftware.config.AgoraConfig' because:\r\n\r\n            - 'appId': Missing from config\r\n\r\n            - 'appCert': Missing from config\r\n\r\n            - 's3BucketAccessKey': Missing from config\r\n\r\n            - 's3BucketSecretKey': Missing from config\r\n\r\n        - 'anthropic': - Could not instantiate 'com.nextchaptersoftware.config.AnthropicConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n        - 'authentication': - Could not instantiate 'com.nextchaptersoftware.config.AuthenticationConfig' because:\r\n\r\n            - 'tokenPrivateKey': Missing from config\r\n\r\n            - 'tokenPublicKey': Missing from config\r\n\r\n        - 'azureOpenAI': - Could not instantiate 'com.nextchaptersoftware.config.AzureOpenAIConfig' because:\r\n\r\n            - 'cneDeployment': - Could not instantiate 'com.nextchaptersoftware.config.AzureOpenAIDeploymentConfig' because:\r\n\r\n                - 'apiKey': Missing from config\r\n\r\n            - 'eusDeployment': - Could not instantiate 'com.nextchaptersoftware.config.AzureOpenAIDeploymentConfig' because:\r\n\r\n                - 'apiKey': Missing from config\r\n\r\n            - 'eus2Deployment': - Could not instantiate 'com.nextchaptersoftware.config.AzureOpenAIDeploymentConfig' because:\r\n\r\n                - 'apiKey': Missing from config\r\n\r\n            - 'ncusDeployment': - Could not instantiate 'com.nextchaptersoftware.config.AzureOpenAIDeploymentConfig' because:\r\n\r\n                - 'apiKey': Missing from config\r\n\r\n        - 'cohere': - Could not instantiate 'com.nextchaptersoftware.config.CohereConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n        - 'encryption': - Could not instantiate 'com.nextchaptersoftware.config.Encryption' because:\r\n\r\n            - 'userSecretsPublicKey': Missing from config\r\n\r\n            - 'userSecrets4096PublicKey': Missing from config\r\n\r\n        - 'honeycomb': - Could not instantiate 'com.nextchaptersoftware.config.HoneycombConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n        - 'intercom': - Could not instantiate 'com.nextchaptersoftware.config.IntercomConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n            - 'identityVerificationSecret': Missing from config\r\n\r\n        - 'openAI': - Could not instantiate 'com.nextchaptersoftware.config.OpenAIConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n        - 'pinecone': - Could not instantiate 'com.nextchaptersoftware.config.PineconeConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n        - 'providers': - Could not instantiate 'com.nextchaptersoftware.config.ProvidersConfig' because:\r\n\r\n            - 'slack': - Could not instantiate 'com.nextchaptersoftware.config.SlackConfig' because:\r\n\r\n                - 'signingSecret': Missing from config\r\n\r\n                - 'clientSecret': Missing from config\r\n\r\n                - 'botAuthToken': Missing from config\r\n\r\n            - 'jira': - Could not instantiate 'com.nextchaptersoftware.config.JiraConfig' because:\r\n\r\n                - 'clientSecret': Missing from config\r\n\r\n            - 'linear': - Could not instantiate 'com.nextchaptersoftware.config.LinearConfig' because:\r\n\r\n                - 'apiKey': Missing from config\r\n\r\n                - 'clientSecret': Missing from config\r\n\r\n                - 'signingSecret': Missing from config\r\n\r\n            - 'confluence': - Could not instantiate 'com.nextchaptersoftware.config.ConfluenceConfig' because:\r\n\r\n                - 'clientSecret': Missing from config\r\n\r\n            - 'notion': - Could not instantiate 'com.nextchaptersoftware.config.NotionConfig' because:\r\n\r\n                - 'clientSecret': Missing from config\r\n\r\n            - 'google': - Could not instantiate 'com.nextchaptersoftware.config.GoogleConfig' because:\r\n\r\n                - 'clientSecret': Missing from config\r\n\r\n        - 'sendGrid': - Could not instantiate 'com.nextchaptersoftware.config.SendGridConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\r\n        - 'statsig': - Could not instantiate 'com.nextchaptersoftware.config.StatsigConfig' because:\r\n\r\n            - 'apiKey': Missing from config\r\n\tat com.sksamuel.hoplite.ConfigLoader$returnOrThrow$1.invoke(ConfigLoader.kt:219)\r\n\tat com.sksamuel.hoplite.ConfigLoader$returnOrThrow$1.invoke(ConfigLoader.kt:216)\r\n\tat com.sksamuel.hoplite.fp.ValidatedKt.getOrElse(Validated.kt:115)\r\n\tat com.sksamuel.hoplite.ConfigLoader.returnOrThrow(ConfigLoader.kt:216)\r\n\tat com.nextchaptersoftware.config.GlobalConfig.<clinit>(GlobalConfig.kt:531)\r\n\t... 4 more\r\n```\r\n\r\nAh I know what’s going on. Our default values.yaml file in the parent helm chart has  unblocked-service-secrets-env  as a default value. Since we override it we need to specify it again in our override. The behaviour seems to be  replace not merge!\r\n\r\n\r\nLook at helm/baseservice/values.yaml line 74","mergeCommitSha":"aef1c92720c79f2d32645dac94f15dffc4802a18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10278","title":"[SKIP TESTS] Add service secrets","createdAt":"2024-01-24T22:09:05Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:22:58Z","number":10279,"mergeCommitSha":"14ed232a11f482755bb9d43cf3dd75443a71db6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10279","title":"Exclude old documentation for template sites","createdAt":"2024-01-24T22:11:17Z"}
{"state":"Merged","mergedAt":"2022-04-26T21:03:29Z","number":1028,"mergeCommitSha":"d2b0bf7a58f7cd19da189c81aa37cbe290fa12d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1028","title":"Admin: try to get health checks working","createdAt":"2022-04-26T21:00:12Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:36:23Z","number":10280,"body":"![CleanShot 2024-01-24 at 14 22 51@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/c2dd2fc5-0c6a-45d1-9796-8e9b4d47b247)\r\n","mergeCommitSha":"d35b001bfbc021b3f67267d704cd70043f567397","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10280","title":"Tab badges in VSCode","createdAt":"2024-01-24T22:23:41Z"}
{"state":"Merged","mergedAt":"2024-01-24T22:36:48Z","number":10281,"mergeCommitSha":"149410de1bb5eafca8cd564008aeaa148d7d78ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10281","title":"Enable VPC networking for prod pipelines","createdAt":"2024-01-24T22:36:03Z"}
{"state":"Closed","mergedAt":null,"number":10282,"body":"Scrollbars in VSCode never behave as overlay scrollbars -- they always reserve space for the scrollbar, if the content is scrollable.  The end result is ugly gaps on the right-hand side for content that horizontally spans the view:\r\n\r\n![CleanShot 2024-01-24 at 14 38 20@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/04620405-ea83-4303-890e-30f070ce84d3)\r\n\r\nThis removes all scrollbar visibility.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/565979da-9bac-4de3-af08-329ebf870542\r\n\r\n","mergeCommitSha":"99877c1185971b387bebdeee3d3fddd901dece65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10282","title":"Remove all scrollbars in VSCode webviews","createdAt":"2024-01-24T22:38:55Z"}
{"state":"Merged","mergedAt":"2024-01-24T23:32:17Z","number":10283,"body":"On Enterprise Provider app creation, navigate to corresponding OAuth page.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f91590d7-37cb-4fad-a7ce-e2665fc31fb8\r\n\r\n","mergeCommitSha":"689fe4a993061e61791c8ef8f8c51f00d48773f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10283","title":"Add enterprise provider oauth","createdAt":"2024-01-24T23:01:43Z"}
{"state":"Merged","mergedAt":"2024-01-24T23:13:57Z","number":10284,"body":"We had an algorithm for trimming source code whitespace, but it was tied into sourcemark code, so I extracted it out into a new `SourceMarkUtils` file so it can be used more broadly.\r\n","mergeCommitSha":"f59afa6f8f6f8f93d1fc1e2d26bbb302cfafdc6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10284","title":"Trim starting source code whitespace in Ask a Question","createdAt":"2024-01-24T23:02:04Z"}
{"state":"Merged","mergedAt":"2024-01-24T23:07:38Z","number":10285,"mergeCommitSha":"39880aaab515ad53340c463c7dbbb31c271c4e38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10285","title":"[SKIP TESTS] Add ability to add slack","createdAt":"2024-01-24T23:07:28Z"}
{"state":"Merged","mergedAt":"2024-01-24T23:26:42Z","number":10286,"body":"Need to use this if documents are partitioned.","mergeCommitSha":"763e79006f7de97e510e38085d767ea27dab4caa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10286","title":"Use SearchEvent.RemoveDocumentEmbedding in dropExcludedUrlsForSite","createdAt":"2024-01-24T23:17:52Z"}
{"state":"Merged","mergedAt":"2024-01-25T03:26:32Z","number":10287,"body":"## Motivation\r\nThis scenario occurred when Mahdi was setting up our instance. We don't connect to instances unless SSL is working.\r\n\r\n## Old message\r\nSee https://chapter2global.slack.com/archives/C06E1MXEH1N/p1706130224384709\r\n\r\n\r\n## New message\r\n<img width=\"1160\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/b6083de4-da2a-4320-819b-a1b6dc9e63fd\">\r\n","mergeCommitSha":"d86fc38ff32557c946b7eff375f700ea759bed2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10287","title":"Handle SSL certificate issues in enterprise validation","createdAt":"2024-01-24T23:41:07Z"}
{"state":"Merged","mergedAt":"2024-01-25T00:12:43Z","number":10288,"body":"- Move to another mpodel for vpc setu\n- Try again\n","mergeCommitSha":"c367d3ea223054c980c279ff411748c085cb2ffa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10288","title":"UpdateDeployment","createdAt":"2024-01-25T00:02:35Z"}
{"state":"Merged","mergedAt":"2024-01-25T00:23:53Z","number":10289,"body":"Remove unused code. MainSidebar now takes the role of InsightsToolWindow","mergeCommitSha":"137114bc7b8dd85156832e2298a3a8c00299333e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10289","title":"Remove insights tool window","createdAt":"2024-01-25T00:11:53Z"}
{"state":"Merged","mergedAt":"2022-04-26T22:46:10Z","number":1029,"body":"* Attempt at some alias clean up -- `@models` and `@shared-api-models` export the same thing, consolidate\r\n* Fix API import reference in RestoreThreadDialog to fix the web stories ","mergeCommitSha":"d7821021033fd13354de03eab7aa64e175a5b65c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1029","title":"Fix storybook and clean up imports","createdAt":"2022-04-26T21:30:26Z"}
{"state":"Merged","mergedAt":"2024-01-25T01:38:12Z","number":10290,"body":"I've updated the IntelliJ tool window icons for the new UI.\r\n\r\n![CleanShot 2024-01-24 at 16 11 46@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/d10b5fb3-aa23-4a3f-be44-12b3e3fa0aab)\r\n\r\n![CleanShot 2024-01-24 at 16 11 31@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/04781d28-66d4-405c-8cd9-3db37bb7534c)\r\n\r\n\r\nI've also updated the labels that appear in the Old UI, that similarly appear on hover in the new UI.\r\n\r\n**Insight > Related Content**\r\n\r\n![CleanShot 2024-01-24 at 16 00 57@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/2b63bc80-29a0-45f2-a1b5-0487eb1fd74f)\r\n\r\n![CleanShot 2024-01-24 at 16 13 50@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/22cdf171-3c02-46e3-ad00-cc8ef7d71a6f)\r\n\r\n\r\n**Unblocked > Ask Unblocked**\r\n\r\n![CleanShot 2024-01-24 at 16 03 52@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/45129e0a-7c50-4812-ba2c-1c4974450984)\r\n\r\n![CleanShot 2024-01-24 at 16 14 39@2x](https://github.com/NextChapterSoftware/unblocked/assets/********/e830f4ea-a79b-4e42-b04c-f2f98a1dfd67)\r\n","mergeCommitSha":"71e1ec0a2c83c9b46782847d44237c58148ff69e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10290","title":"Fixing IJ tool window icons","createdAt":"2024-01-25T00:12:02Z"}
{"state":"Merged","mergedAt":"2024-01-25T00:26:25Z","number":10291,"mergeCommitSha":"d85934a542e72bb2ebacc4dc5d015968775c170a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10291","title":"Oops -- fix newline splitting","createdAt":"2024-01-25T00:14:24Z"}
{"state":"Merged","mergedAt":"2024-01-25T00:28:00Z","number":10292,"body":"There’s. command from our package.json I added a while back to auto-update dependencies.\r\nnpm run update-dependencies\r\n","mergeCommitSha":"f988a734711113a80193351482d2a0517971b662","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10292","title":"Update cdk deps","createdAt":"2024-01-25T00:18:34Z"}
{"state":"Merged","mergedAt":"2024-01-26T03:33:06Z","number":10293,"mergeCommitSha":"d5cc9f3146254edc0cb9e487cfcca82a8fb2b0f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10293","title":"Add ability to download and parse PDF files (dev and local only for now)","createdAt":"2024-01-25T00:26:44Z"}
{"state":"Merged","mergedAt":"2024-01-25T00:40:42Z","number":10294,"mergeCommitSha":"f83c91644d1fa30f5f435e5d30e3b483f3d73a6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10294","title":"Remove title casing from explorer row footer","createdAt":"2024-01-25T00:29:32Z"}
{"state":"Merged","mergedAt":"2024-01-25T04:55:07Z","number":10295,"body":"Add flatMapAsync on Collection\n\nRevert change to expert service","mergeCommitSha":"35eac2ea18f0bb9e53f5c4e146e97b67d71c4390","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10295","title":"Add flatMapAsync on Collection","createdAt":"2024-01-25T00:50:24Z"}
{"state":"Merged","mergedAt":"2024-01-25T04:25:45Z","number":10296,"body":"- local internet addresses are not valid\n- gitlab.com is not a valid enterprise provider (although it does look like one)","mergeCommitSha":"b2cf601262e0d62bc8c0774e4ab888bf27ba75e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10296","title":"Enterprise host validate for localhost and gitlab.com","createdAt":"2024-01-25T01:21:33Z"}
{"state":"Merged","mergedAt":"2024-01-25T01:41:32Z","number":10297,"mergeCommitSha":"9668b005b80be0788bb5ae2b67b92d223e2ad70d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10297","title":"Give search service access to SCM secrets","createdAt":"2024-01-25T01:24:03Z"}
{"state":"Merged","mergedAt":"2024-01-25T01:49:49Z","number":10298,"mergeCommitSha":"6f483ed3e2de9fc46ccb58733ce0b5e6b576f6c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10298","title":"Remove SCM secrets from campaign-service","createdAt":"2024-01-25T01:36:13Z"}
{"state":"Merged","mergedAt":"2024-01-25T02:34:44Z","number":10299,"mergeCommitSha":"cc09f477da5f8871d1c35c210f919055a95ef2a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10299","title":"Fix RedisExtensionsTest maybe?","createdAt":"2024-01-25T02:26:24Z"}
{"state":"Merged","mergedAt":"2022-01-31T18:18:36Z","number":103,"body":"Implemented thread summary view with rough props.\r\n\r\nUpdated basic headers based on web design doc. Only using h1 - h3. Removed the rest\r\nThis included updating how the Mixins work and separating into separate files due to circular dependencies.\r\n\r\n<img width=\"1506\" alt=\"CleanShot 2022-01-21 at 15 22 28@2x\" src=\"https://user-images.githubusercontent.com/1553313/150612494-9415df95-b46e-48ca-afee-1267e262a1de.png\">\r\n\r\n","mergeCommitSha":"a2e0b3dd6460ba0f8296715428d4f8d86da5d7e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/103","title":"Thread summary view","createdAt":"2022-01-21T23:23:03Z"}
{"state":"Merged","mergedAt":"2022-04-26T23:26:28Z","number":1030,"body":"Update build pipeline to support multiple browsers from a single src.\r\n\r\nXcode project working but code signing failing...\r\n```\r\nerror: Signing for \"Unblocked for Safari (iOS)\" requires a development team. Select a development team in the Signing & Capabilities editor. (in target 'Unblocked for Safari (iOS)' from project 'Unblocked for Safari')\r\nerror: Signing for \"Unblocked for Safari Extension (iOS)\" requires a development team. Select a development team in the Signing & Capabilities editor. (in target 'Unblocked for Safari Extension (iOS)' from project 'Unblocked for Safari')\r\n** BUILD FAILED **\r\n```\r\n\r\nWill look into how to handle this in CI.\r\n\r\n<img width=\"724\" alt=\"CleanShot 2022-04-26 at 15 21 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/165402339-4a9fb7b0-75a5-4870-b665-fda200621324.png\">\r\n","mergeCommitSha":"193435d7ccc1ce708add720b2c721339f211f9da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1030","title":"Streamline web extension build pipeline","createdAt":"2022-04-26T21:57:52Z"}
{"state":"Merged","mergedAt":"2024-01-25T03:53:22Z","number":10300,"body":"## Scenario\n- Identity provider: Bitbucket (`c9c401aa-1d81-4c69-936a-9384f3709d22`)\n- Installation provider: GitHub (`2~null~{9261c8df-baad-466b-b384-d546e87bd895}`)\n- Trace: https://ui.honeycomb.io/unblocked/environments/production/result/86djno8a4Wt/trace/FK2LbP2SmbQ\n\n## Root cause\nUnknown. Still unclear how this is even possible.\n\n## Change\nFast fail, instead of slow fail. We must not send GitHub SCM secrets to another third party (Bitbucket).","mergeCommitSha":"fcf5acdabc79d26f726b145d30e73a7ac3524f56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10300","title":"Expect that installation provider matches identity provider in `connectScmInstallation`","createdAt":"2024-01-25T03:32:18Z"}
{"state":"Merged","mergedAt":"2024-01-25T05:31:30Z","number":10301,"body":"Optimize ML functions\n\nUse flatMapAsync\n\nFix up prompt","mergeCommitSha":"3dfa28c2aec71163e976e2dc8d0c22812f66b7a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10301","title":"Optimize ML functions","createdAt":"2024-01-25T04:17:53Z"}
{"state":"Merged","mergedAt":"2024-01-25T18:21:40Z","number":10302,"body":"will add the Unblocked implementation later after I figure out a reasonable API across all the different approaches we want to test.","mergeCommitSha":"2838a80aec5a31ff45657b812e80d7f88bbc36a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10302","title":"Add DecisionDocumentRerankService + UnblockedDocumentRerankService","createdAt":"2024-01-25T05:01:08Z"}
{"state":"Merged","mergedAt":"2024-01-25T05:56:54Z","number":10303,"body":"https://chapter2global.slack.com/archives/C06E1MXEH1N/p1706161221055349","mergeCommitSha":"679ef032e1a70de532ca857ab3d0b3d6dc5abd3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10303","title":"Fix provider mixup in findEnterpriseProviders","createdAt":"2024-01-25T05:48:39Z"}
{"state":"Merged","mergedAt":"2024-01-25T06:21:50Z","number":10304,"mergeCommitSha":"b4a9ee28f234549e6fea9bffea3557f078d12d59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10304","title":"Fix enterprise button styoling","createdAt":"2024-01-25T06:07:48Z"}
{"state":"Merged","mergedAt":"2024-01-25T06:38:37Z","number":10305,"body":"Only works for GitHub Enterprise right now.","mergeCommitSha":"b8374fcc2784a8efbed2a4c0932626236c3d1e30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10305","title":"Implement getEnterpriseProvider","createdAt":"2024-01-25T06:13:43Z"}
{"state":"Merged","mergedAt":"2024-01-25T19:55:04Z","number":10306,"body":"The unformatted code renderer and formatted code renderer had significant stylistic differences, which caused the code to \"jump\" after async formatting.  This was especially noticeable in the Ask a Question UI.\r\n\r\nThis was a lot of very ugly CSS hacking.  The CodeBlock component has two variants, in three clients, and all have their own quirks -- next time I do major work here I'm going to spend time simplifying this.","mergeCommitSha":"ff085c5192086afbfec99fd1f0b194e6bab5a26f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10306","title":"Unformatted source code now matches sizing of formatted source code","createdAt":"2024-01-25T06:16:55Z"}
{"state":"Merged","mergedAt":"2024-01-25T06:22:32Z","number":10307,"mergeCommitSha":"ab155c2f8b4dd278657360a60d0ae40453ae52fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10307","title":"[SKIP TESTS] - log doc retrieval for experts function","createdAt":"2024-01-25T06:22:04Z"}
{"state":"Merged","mergedAt":"2024-01-25T06:50:37Z","number":10308,"mergeCommitSha":"09c97933296eb1e7b692aa952b7d8993251a344c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10308","title":"Error view","createdAt":"2024-01-25T06:40:25Z"}
{"state":"Merged","mergedAt":"2024-01-25T06:56:13Z","number":10309,"mergeCommitSha":"5b3d1ed454439c9ea313b2d357c972b245f2f8ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10309","title":"[SKIP TESTS] - log the bejezeljuice out of the expert function","createdAt":"2024-01-25T06:55:59Z"}
{"state":"Merged","mergedAt":"2022-04-28T18:37:28Z","number":1031,"body":"Adding an avatar and link to come later\r\n\r\n<img width=\"787\" alt=\"CleanShot 2022-04-26 at 15 46 15@2x\" src=\"https://user-images.githubusercontent.com/1924615/165405051-e218cca0-b301-407d-8b63-f1b1fe4f48c0.png\">\r\n","mergeCommitSha":"0f56a811b9ae4970159acd73d533933ab55a9bc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1031","title":"Update signature for PR comments created and edited from unblocked","createdAt":"2022-04-26T22:45:57Z"}
{"state":"Merged","mergedAt":"2024-01-25T07:15:39Z","number":10310,"mergeCommitSha":"e3952c856a9c71eef80b48a612a69b8abdf94994","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10310","title":"[SKIP TESTS] - tried to serialize wrong field","createdAt":"2024-01-25T07:15:24Z"}
{"state":"Merged","mergedAt":"2024-01-25T08:49:27Z","number":10311,"mergeCommitSha":"71028e9674db280388748885a91dfe2aa98497f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10311","title":"[SKIP TESTS] - fall back to login if username is missing","createdAt":"2024-01-25T08:04:09Z"}
{"state":"Merged","mergedAt":"2024-01-25T18:05:09Z","number":10312,"body":"Unhide close from Ask sidebar instructions\r\n\r\n![CleanShot 2024-01-25 at 08 49 31@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/a6314fdb-5f75-4549-af37-cf86995fc498)\r\n","mergeCommitSha":"311f3f4dc8c2e66184fd12111c0016045a61e578","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10312","title":"Unhide close in ask sidebar instructions","createdAt":"2024-01-25T16:49:58Z"}
{"state":"Merged","mergedAt":"2024-01-29T17:17:41Z","number":10313,"body":"Hides \"Ask a question\" in VSCode when new UI is not enabled.","mergeCommitSha":"885204fa158657b29bb895a8d545693edc3f0eaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10313","title":"Hide start question in lightbulb","createdAt":"2024-01-25T18:10:58Z"}
{"state":"Merged","mergedAt":"2024-01-25T18:35:08Z","number":10314,"mergeCommitSha":"f715ecdf0c1dc93a15ffdab54abe59bad2f06471","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10314","title":"improve diagnostics script","createdAt":"2024-01-25T18:35:00Z"}
{"state":"Merged","mergedAt":"2024-01-26T02:53:55Z","number":10315,"body":"Once a month is plenty. We can always force a reingest from the admin console.","mergeCommitSha":"df5e544bf19fbed99fbd469d1f3e623182e3973a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10315","title":"Increase web reingest interval for demo teams","createdAt":"2024-01-25T18:42:30Z"}
{"state":"Merged","mergedAt":"2024-01-25T19:14:46Z","number":10316,"mergeCommitSha":"38dc1bdcaea2aaef76bdc333cfd40672f693cf6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10316","title":"Vend GitLab OAuthUrl and PublicKey from `findEnterpriseProviders`","createdAt":"2024-01-25T18:52:21Z"}