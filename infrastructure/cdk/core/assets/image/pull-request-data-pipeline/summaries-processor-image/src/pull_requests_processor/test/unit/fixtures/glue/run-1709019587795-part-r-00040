{"state":"Merged","mergedAt":"2023-12-11T17:18:23Z","number":9616,"mergeCommitSha":"a746d836e1142c68ddb77b7db7455cf32ee95d48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9616","title":"Backfill WebIngestionSiteModels","createdAt":"2023-12-11T00:44:37Z"}
{"state":"Merged","mergedAt":"2023-12-11T06:10:30Z","number":9617,"mergeCommitSha":"1fc2807c2fb1a030a99dd56288e34eade31e3528","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9617","title":"Kick main CI","createdAt":"2023-12-11T06:10:12Z"}
{"state":"Merged","mergedAt":"2023-12-11T18:39:02Z","number":9618,"body":"... when there's only one thread","mergeCommitSha":"ba00b0fee676c94311138ff351c91588650aa99a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9618","title":"Fix dashboard thread loading","createdAt":"2023-12-11T17:56:19Z"}
{"state":"Closed","mergedAt":null,"number":9619,"mergeCommitSha":"d9cc8116ea80324dfc4ae8d6418fb9353daf969c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9619","title":"Adjust PR parser to handle GitLab merge requests","createdAt":"2023-12-11T19:33:53Z"}
{"state":"Merged","mergedAt":"2022-04-21T16:01:24Z","number":962,"body":"Generalize spinner and add storybooks to validate.\r\n\r\n<img width=\"614\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/164351934-f59c86ce-ada4-45fd-b125-5161182c188f.png\">\r\n","mergeCommitSha":"6dc4c9eabdc6560da5dd0f882d583efd089e7f5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/962","title":"Generalize spinner","createdAt":"2022-04-21T01:11:58Z"}
{"state":"Merged","mergedAt":"2023-12-11T20:17:44Z","number":9620,"mergeCommitSha":"8260a9bbce0398e5baa3e3a8b083a4249d4215cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9620","title":"Adjust PR parser to handle GitLab merge requests","createdAt":"2023-12-11T20:05:24Z"}
{"state":"Merged","mergedAt":"2023-12-11T21:29:28Z","number":9621,"mergeCommitSha":"fdd2151021f628d9731427551254d82f5ef6e474","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9621","title":"Handle GitLab repo url sub-groups","createdAt":"2023-12-11T20:05:28Z"}
{"state":"Merged","mergedAt":"2023-12-11T21:56:28Z","number":9622,"mergeCommitSha":"3758c66e1096a478e40755e6cb82fe8e4e1a6c00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9622","title":"Add Web ingestion admin console page","createdAt":"2023-12-11T21:26:25Z"}
{"state":"Closed","mergedAt":null,"number":9623,"body":"Removes overview tab + corresponding Topic streams from the IDEs\r\n\r\nThis removes `API.topics.getRelatedTopics` usage\r\n\r\n![CleanShot 2023-12-11 at 13 45 01@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/e340a6f6-743b-4b85-9fda-ddb7e3ebe611)\r\n\r\n\r\n![CleanShot 2023-12-11 at 13 53 05@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/85a49885-246c-4d28-9260-18a849af8bc3)\r\n","mergeCommitSha":"34f3fb6abf34d1025ec545868679ef21b77a8eba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9623","title":"[DO NOT MERGE] Remove overview tab from IDEs","createdAt":"2023-12-11T21:53:36Z"}
{"state":"Merged","mergedAt":"2023-12-11T23:25:12Z","number":9624,"body":"Remove topics from UserProfile in Dashboard.\r\n\r\n![CleanShot 2023-12-11 at 14 08 10@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/bc4ccfe5-be93-428a-b53e-335c3f760319)\r\n","mergeCommitSha":"d4a92e21d189cbecfaa0a6e707297fe658c2b991","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9624","title":"Removes topics from user profile","createdAt":"2023-12-11T22:08:59Z"}
{"state":"Merged","mergedAt":"2023-12-11T23:25:03Z","number":9625,"body":"A few things that were missed before:\r\n\r\n* Clip to 25 sorted/filtered items, with a 'Show More' button\r\n* Fix the 'No repos' state UI (matches designs)\r\n* Remove some dead code\r\n\r\n![CleanShot 2023-12-11 at 14 15 26@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/4ff168a6-0967-43ed-8ad8-614d07cbb5a5)\r\n![CleanShot 2023-12-11 at 14 15 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/6b9ce071-e1ef-482b-aaa9-5f155e5d4445)\r\n","mergeCommitSha":"fdcc19ad2b6435a3b545eb6cd08b508169ee575f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9625","title":"Repo table fixes","createdAt":"2023-12-11T22:15:50Z"}
{"state":"Merged","mergedAt":"2023-12-11T22:33:20Z","number":9626,"mergeCommitSha":"28025cd387a2464c92220bfa47673c787811d98b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9626","title":"Add plot visualizer","createdAt":"2023-12-11T22:26:44Z"}
{"state":"Merged","mergedAt":"2023-12-11T23:02:41Z","number":9627,"mergeCommitSha":"3692b4947ccea89fc1a334d47cc4a8f4f15ee8c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9627","title":"Don't ask user about repo removal when onboarding BitBucket / GitLab","createdAt":"2023-12-11T22:49:26Z"}
{"state":"Merged","mergedAt":"2023-12-11T23:02:22Z","number":9628,"mergeCommitSha":"c3d8f9a3caf3244eb6580a9687cddb78daac2666","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9628","title":"Hide \"Web\" tab from team members page","createdAt":"2023-12-11T22:52:49Z"}
{"state":"Merged","mergedAt":"2023-12-12T00:17:31Z","number":9629,"body":"Removing topics from Discussion / PR Views in both dashboard + IDE.\r\nKeeping experts for now.","mergeCommitSha":"ad1ea3f2330b6c76e41d4bcb077e9997914b06d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9629","title":"Remove topics from Discussion/PR Views","createdAt":"2023-12-11T23:11:22Z"}
{"state":"Merged","mergedAt":"2022-04-21T05:30:05Z","number":963,"mergeCommitSha":"6bbc53b70f645218a90c693ed075ddc0588956bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/963","title":"Drop macos build timeout to 20 minutes","createdAt":"2022-04-21T05:16:03Z"}
{"state":"Merged","mergedAt":"2023-12-12T00:11:23Z","number":9630,"body":"Light mode doesn't have any PR data, so showing these tabs is kind of mean...","mergeCommitSha":"82c037b347af18c1328179f09e51186856903586","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9630","title":"Only show My Discussion tabs in full mode","createdAt":"2023-12-11T23:59:01Z"}
{"state":"Merged","mergedAt":"2023-12-12T00:10:07Z","number":9631,"mergeCommitSha":"c73678af4ef17e91a8ef8da0efd64ef518b36522","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9631","title":"Update prompt","createdAt":"2023-12-12T00:08:42Z"}
{"state":"Merged","mergedAt":"2023-12-12T01:30:14Z","number":9632,"mergeCommitSha":"857faa124af1b4c641cff31cd20146d2b2a875fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9632","title":"Topic score cutoff","createdAt":"2023-12-12T00:39:20Z"}
{"state":"Merged","mergedAt":"2023-12-12T06:25:37Z","number":9633,"body":"Updates a path that's not being used yet","mergeCommitSha":"41a0b1dc304c07f7c97d14f6b2a686f62e3d15b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9633","title":"[BREAKS API ON MAIN] Upsert Web installation","createdAt":"2023-12-12T01:15:18Z"}
{"state":"Merged","mergedAt":"2023-12-12T01:35:24Z","number":9634,"mergeCommitSha":"ca523232d04270410de873aefcd1bf2c3f5e1aae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9634","title":"Increase scale counts","createdAt":"2023-12-12T01:34:49Z"}
{"state":"Merged","mergedAt":"2023-12-12T02:15:25Z","number":9635,"mergeCommitSha":"b41d9acc674297591b57e8d2bf2b67357ed24f3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9635","title":"Add logging to StackOverflowTeamsClient","createdAt":"2023-12-12T02:04:50Z"}
{"state":"Merged","mergedAt":"2023-12-12T19:58:38Z","number":9636,"body":"Right now, when onboarding with GH, the following flow can occur:\r\n\r\n* User adds Unblocked app in GH\r\n* User is redirected to InstallRedirect `/install/redirect`\r\n* InstallRedirect creates team, checks team status\r\n* If the team status is `repoSelectionNeeded`, the user was redirected to PendingProcessing `/new/team/xyz/pending`\r\n* This displayed the GitHub config UI\r\n* After saving, the user was redirected to `/team/xyz`, which means they would skip the integrations UI while onboarding\r\n\r\nThe core problem is that the Pending UI `/new/team/xyz/pending` is also used outside of onboarding -- if the user drops out at the pending state, and then comes back, we want them to redirect back to this step, but we don't want to force them to always go through the integrations UI.\r\n\r\nThis PR fixes it by adding a different route:\r\n\r\n* If InstallRedirect detects an invalid SCM state, it will redirect to InvalidScmState at `/new/team/xyz/invalid-scm-state`\r\n* InvalidScmState will render the same SCM state and resolution UIs, for any blocking states (`missingValidRepos`, `repoSelectionNeeded`, `uninstalled`, `suspended`), but once the team state goes to `ok` or `pending`, it will redirect to the integrations UI.  This route is only used during onboarding.\r\n* If the user finds their way to the dashboard outside of onboarding, they will continue to be redirected to `/pending`, which after resolution will redirect them back to the team root.\r\n","mergeCommitSha":"eafc4852794109abc81563d6884433c7a5494433","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9636","title":"Fix onboarding issue where users would end up skipping integration UI","createdAt":"2023-12-12T03:49:21Z"}
{"state":"Merged","mergedAt":"2023-12-12T04:07:03Z","number":9637,"body":"Display a popup UI when semantic search fails in the IDE.\r\n\r\nThis is really just so we don't fail completely silently.\r\n\r\n![CleanShot 2023-12-11 at 19 54 04@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/4786d88b-7a09-49b2-bb12-7c29a0279570)\r\n","mergeCommitSha":"0c309b089dc44084ba70774aaa785a1ed83f3eb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9637","title":"Don't fail silently on semantic search failure","createdAt":"2023-12-12T03:54:16Z"}
{"state":"Merged","mergedAt":"2023-12-12T03:58:48Z","number":9638,"mergeCommitSha":"42db499c766b2946442da98327b5783566a28d6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9638","title":"Increase repo count","createdAt":"2023-12-12T03:58:33Z"}
{"state":"Merged","mergedAt":"2023-12-12T04:24:46Z","number":9639,"mergeCommitSha":"5e2d3cb920a039c44d2d5873afaa9fd28c0e8724","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9639","title":"Fix infra","createdAt":"2023-12-12T04:24:33Z"}
{"state":"Merged","mergedAt":"2022-04-25T17:50:45Z","number":964,"body":"# Summary\r\n\r\nBunch of stuff going on here:\r\n- Had to fall back to AppKit for status bar integration because SwiftUI just doesn't have the facilities for it\r\n- Used storyboard with no window hack\r\n- Had to use global event hack to close popover in some \"special\" cases (comments inline)\r\n- Popover is not perfectly centred as shown in the designs. NSPopover provides a \"preferred edge\" interface. Maybe we can hack around this, maybe not. If we have to implement our own popover using NSWindow we can do that, it just gets a little more complicated because we have to draw out own path shape etc\r\n\r\n\r\n![CleanShot 2022-04-20 at 22 26 19](https://user-images.githubusercontent.com/858772/164380167-e94bcb8b-fec9-429b-9815-8947a55b6fe4.gif)\r\n\r\n\r\n","mergeCommitSha":"0ef019c794722194a2388c0bd3f22061cf19ef16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/964","title":"Integrate with status bar","createdAt":"2022-04-21T05:24:30Z"}
{"state":"Merged","mergedAt":"2023-12-12T18:21:42Z","number":9640,"body":"Fix IndexOutOfBoundsException for local context\n\nAdd tests","mergeCommitSha":"e844d3ba1df1b39366d113802161eadfd0f4aef6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9640","title":"Fix IndexOutOfBoundsException for local context","createdAt":"2023-12-12T05:39:15Z"}
{"state":"Merged","mergedAt":"2023-12-12T17:37:12Z","number":9641,"mergeCommitSha":"9e4ff58d57bccda18e19a4b56966724f665710c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9641","title":"Fix Stack Overflow team name and token validation","createdAt":"2023-12-12T06:59:33Z"}
{"state":"Merged","mergedAt":"2023-12-12T17:16:43Z","number":9642,"body":"This reverts commit 42db499c766b2946442da98327b5783566a28d6c.\r\n\r\nThis didn't manage to deploy because of a failing test (also it's breaking PR and main builds ❤️).","mergeCommitSha":"467d040a300405dbf6cb1491e3b08d366a197a26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9642","title":"Revert \"Increase repo count (#9638)\"","createdAt":"2023-12-12T17:05:29Z"}
{"state":"Merged","mergedAt":"2023-12-20T00:56:15Z","number":9643,"body":"Removes Topic selection step from onboarding flow & corresponding code.\r\nSample questions should still work without the user topic selection.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/fcf2c884-cb6e-470e-8c16-a3b4a2ca5b8e\r\n\r\n","mergeCommitSha":"9ffa05981c15fea0dec062ff370f187becbc4cda","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9643","title":"Remove topic selection step","createdAt":"2023-12-12T17:18:06Z"}
{"state":"Closed","mergedAt":null,"number":9644,"body":"Operation isn't in use yet","mergeCommitSha":"c4184056742e1de75684c5d7504bc7b138eae6c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9644","title":"[BREAKS API ON MAIN] Remove unused parameter from postWebIngestionValidation","createdAt":"2023-12-12T18:02:13Z"}
{"state":"Merged","mergedAt":"2023-12-12T18:28:42Z","number":9645,"body":"<img width=\"1016\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/187a9e21-a5a1-4149-857a-0e7a05f3376d\">\r\n","mergeCommitSha":"a902e29d2117dfea14ed8e74a2c8b06eb8064cb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9645","title":"Invite page should link to login","createdAt":"2023-12-12T18:16:49Z"}
{"state":"Closed","mergedAt":null,"number":9646,"body":"This is an unused operation","mergeCommitSha":"a50a5153887bb270767289e8759a11f0330d1925","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9646","title":"[BREAKS API ON MAIN] Remove unused parameter from postWebIngestionValidation","createdAt":"2023-12-12T18:54:22Z"}
{"state":"Merged","mergedAt":"2023-12-12T21:52:16Z","number":9647,"body":"Removes topics UI + helpers from the dashboard.\r\n\r\nAlso removed entrypoints from the IDE.","mergeCommitSha":"f07788249454a8d4c109073766e1429b88866bd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9647","title":"Remove topics from dashboard","createdAt":"2023-12-12T19:10:13Z"}
{"state":"Merged","mergedAt":"2023-12-13T08:41:16Z","number":9648,"body":"<img width=\"1440\" alt=\"Screenshot 2023-12-12 at 11 19 44\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/5a5b38f9-eefc-4492-88b6-97363db98175\">\r\n","mergeCommitSha":"f493e0759bb997611ba13479ff1321b6cf693863","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9648","title":"Segmentation of weekly/monthly active users/teams","createdAt":"2023-12-12T19:16:59Z"}
{"state":"Merged","mergedAt":"2023-12-12T20:10:12Z","number":9649,"mergeCommitSha":"6575bd88d3485a6565fd07e7942f97cd202aaa0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9649","title":"[BREAKS API ON MAIN] Rev the url validation endpoint","createdAt":"2023-12-12T19:17:28Z"}
{"state":"Merged","mergedAt":"2022-04-21T06:25:36Z","number":965,"body":"These will be used for pushing message updates back to GitHub on threads that were ingested from pull requests.","mergeCommitSha":"fa95f822f3370c70b69d46d716d91ea8a9cfe93d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/965","title":"Add GitHubAppClient methods for updating pull request comments","createdAt":"2022-04-21T05:49:29Z"}
{"state":"Merged","mergedAt":"2023-12-12T20:10:25Z","number":9650,"body":"Have to run it in an environment to understand the performance characteristics. Sits behind a template setting","mergeCommitSha":"0caa0f43a7713f5f94a62107a5ac613e2c21dcc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9650","title":"Adds recursive retrieval","createdAt":"2023-12-12T19:18:21Z"}
{"state":"Merged","mergedAt":"2023-12-12T19:59:00Z","number":9651,"body":"Don't display references, feedback, or suggestions when streaming message content.  Wait until interpolation completes.\r\n\r\nThis required hoisting the interpolation state up into the MessageView.  It's a bit ugly.  I'm open to alternative suggestions.","mergeCommitSha":"13f234b3327b3f01a9c8dc2f09fa15b61d33393e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9651","title":"Don't display extra content when streaming messages","createdAt":"2023-12-12T19:46:00Z"}
{"state":"Merged","mergedAt":"2023-12-12T20:46:55Z","number":9652,"body":"We should move away form using explcit endpoint for mistral queries because it's not environment-specific.\r\nUse standardized model for sagemaker llm chain calls.","mergeCommitSha":"ffc500e6b87e1fbcff14b3c2a1da00bb6cfbb834","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9652","title":"Add sagemaker llm utilties","createdAt":"2023-12-12T19:55:30Z"}
{"state":"Merged","mergedAt":"2023-12-12T20:51:42Z","number":9653,"mergeCommitSha":"20cfc3fecca220b5f733c916f968e5082b73dfe4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9653","title":"Fix label in admin console","createdAt":"2023-12-12T20:51:26Z"}
{"state":"Merged","mergedAt":"2023-12-12T21:20:33Z","number":9654,"mergeCommitSha":"6f40ec11be0f91e122c22fcea89be316cde9c6e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9654","title":"Remove deprecation","createdAt":"2023-12-12T21:07:09Z"}
{"state":"Merged","mergedAt":"2023-12-12T21:43:18Z","number":9655,"mergeCommitSha":"ee1d76b61234a0e9fd8352d4f3d8b7d098261d18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9655","title":"Keep polling for message updates while streaming, even when UI is in the background","createdAt":"2023-12-12T21:12:19Z"}
{"state":"Merged","mergedAt":"2023-12-12T21:17:29Z","number":9656,"mergeCommitSha":"cfa7848e79dc6cef4761197d624b2146b992b61e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9656","title":"Re-rank with original query","createdAt":"2023-12-12T21:14:21Z"}
{"state":"Merged","mergedAt":"2023-12-12T21:38:18Z","number":9657,"body":"* Adjust the current velocity more aggressively -- this means we will speed up or slow down more responsively\r\n* When we have all the content from the service, stream way faster.","mergeCommitSha":"f1a95d64c1032d8336f2ec564f4c76bd9d53ff95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9657","title":"Tweak message streaming speed","createdAt":"2023-12-12T21:25:36Z"}
{"state":"Merged","mergedAt":"2023-12-12T22:47:54Z","number":9658,"mergeCommitSha":"dec7a16b1b43eb30de3df13c1852cbffd080bc75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9658","title":"Remove web page embedding if it no longer exists","createdAt":"2023-12-12T21:49:23Z"}
{"state":"Merged","mergedAt":"2023-12-12T21:58:36Z","number":9659,"mergeCommitSha":"773df6c022b014975e5bc0addc485128ae56bdb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9659","title":"Re-rank with original semantic search query","createdAt":"2023-12-12T21:54:05Z"}
{"state":"Merged","mergedAt":"2022-04-21T16:05:16Z","number":966,"body":"Looks like 100 wasn't enough, sometimes the sidebar content doesn't load.  Hacky hack.","mergeCommitSha":"faf4c7afb7d30296ca6fdc662f7001afcf318aa1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/966","title":"Extend sidebar load time","createdAt":"2022-04-21T15:58:31Z"}
{"state":"Merged","mergedAt":"2023-12-12T22:07:09Z","number":9660,"mergeCommitSha":"e40021e69385b12f367c69e497ccd1b1e7ec02fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9660","title":"Fix up classifiers","createdAt":"2023-12-12T22:00:33Z"}
{"state":"Merged","mergedAt":"2023-12-12T23:57:59Z","number":9661,"mergeCommitSha":"6243a8e730b8f02e76d44671a0b9b74588db8481","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9661","title":"Move to new classifier codde","createdAt":"2023-12-12T22:19:14Z"}
{"state":"Merged","mergedAt":"2023-12-13T00:40:45Z","number":9662,"body":"Allow users to provide feedback in PR threads with QA responses: \r\n\r\n<img width=\"810\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/cd39b450-0798-47e1-b708-d724c79fea44\">\r\n","mergeCommitSha":"37ddb8c6a529d7295e74340bd2da126a92e322b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9662","title":"Show QA feedback in pr message views","createdAt":"2023-12-13T00:02:19Z"}
{"state":"Merged","mergedAt":"2023-12-13T01:18:58Z","number":9663,"mergeCommitSha":"5a738c5e147588c8072188e6d18fa1b610540aef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9663","title":"Only return Provider.Web in dev","createdAt":"2023-12-13T00:08:52Z"}
{"state":"Merged","mergedAt":"2023-12-13T21:30:14Z","number":9664,"body":"<img width=\"1255\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/6543fe06-facd-4341-b6a5-277eee78ec70\">\r\n<img width=\"810\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/d09fb360-cb02-4031-ac0a-be42bbfe99a2\">\r\n<img width=\"828\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8c5d88b0-2f63-4102-ae34-9d953d3056f0\">\r\n\r\n\r\n\r\nNote: Web left navigation item is hidden.","mergeCommitSha":"9ae79e20011d3eafeca7ef335fbc8662da81ccea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9664","title":"Web Ingestion UI","createdAt":"2023-12-13T00:15:58Z"}
{"state":"Merged","mergedAt":"2023-12-13T04:16:23Z","number":9665,"body":"More calls to cohere, but results should be faster. Should suspend prod deploys before rolling out ","mergeCommitSha":"2151a621efd100d77b34cdccb35f80e00b049b32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9665","title":"Parallelize re-rank of local and repo focused docs","createdAt":"2023-12-13T00:46:54Z"}
{"state":"Merged","mergedAt":"2023-12-13T19:32:39Z","number":9666,"body":"Since the validation route is team-scoped, we can add exceptions for specific teams (i.e. links to stack overflow for open source).","mergeCommitSha":"446f7a735f77baa0cdfad82a402a51aaa372bfe9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9666","title":"Add list of URLs to deny for web ingestion","createdAt":"2023-12-13T00:53:52Z"}
{"state":"Merged","mergedAt":"2023-12-13T01:01:33Z","number":9667,"mergeCommitSha":"cccee7ccd07aa447ab665ae58273e78978188ac3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9667","title":"Standardize llm chains for topic mapping","createdAt":"2023-12-13T00:54:43Z"}
{"state":"Merged","mergedAt":"2023-12-13T20:54:49Z","number":9668,"body":"Bouncy\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/5802a380-26bf-424f-a202-ef6df9770434\r\n\r\n\r\n\r\n![CleanShot 2023-12-12 at 16 53 27@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b4e84a0f-a062-4f94-9c5a-1f90ccd99aee)\r\n![CleanShot 2023-12-12 at 16 54 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/5dde7151-f254-457a-8ab7-13f51e35e217)\r\n![CleanShot 2023-12-12 at 16 58 00@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/d8e8aaf5-15d5-4d62-b3f8-94e5dc6df7b1)\r\n","mergeCommitSha":"cd907283bdc3fdfa0a558f565d1a0671f456499e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9668","title":"New 'Unblocked is thinking...' UI","createdAt":"2023-12-13T00:59:56Z"}
{"state":"Merged","mergedAt":"2023-12-13T01:06:48Z","number":9669,"mergeCommitSha":"e51e7c6860d8f3602572e370e4ecb9c41d42a339","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9669","title":"FixFileTraversalTest","createdAt":"2023-12-13T01:06:09Z"}
{"state":"Merged","mergedAt":"2022-04-21T16:23:54Z","number":967,"mergeCommitSha":"d9d2d4ba28de40087c076d42d7744d28c132ac99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/967","title":"Notarize only after merging to main","createdAt":"2022-04-21T16:10:40Z"}
{"state":"Merged","mergedAt":"2023-12-13T18:31:36Z","number":9670,"body":"New version of VSCode, they tweaked one of the settings we use","mergeCommitSha":"4c064aed56aba0d2d3125e0896cb782f43b8322d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9670","title":"New VSCode settings","createdAt":"2023-12-13T01:15:50Z"}
{"state":"Merged","mergedAt":"2023-12-13T02:15:30Z","number":9671,"body":"was going to add .txt too, but it turns out that is going to need a different prompt.","mergeCommitSha":"85c90dfaa775f2ad333b9e225a0aa32360082418","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9671","title":"add support for .jsonl files to source code classifier","createdAt":"2023-12-13T02:03:39Z"}
{"state":"Merged","mergedAt":"2023-12-13T23:01:47Z","number":9672,"body":"Changes\r\n\r\n`How do new developers to the $TEAM team onboard?`\r\n\r\nto \r\n\r\n`What steps should a new engineer on the $TEAM team follow before contributing to the codebase?`\r\n\r\nas the current sample question can return an answer not specific to developers.  For example, [this](https://admin.prod.getunblocked.com/teams/45cccbbe-1976-4122-bee4-80b04f82525f/inferenceExamples/f7c472b8-6f99-49f4-b5a1-2d45f021b558) one from Drata pulled in documents for onboarding a marketing manager.\r\n\r\nCompare to [this](https://admin.prod.getunblocked.com/teams/45cccbbe-1976-4122-bee4-80b04f82525f/inferenceExamples/70368b8d-706f-4e86-b43a-b517cf6fbd65) which is much more specific.\r\n\r\nHere are examples for AppDirect and cribl:\r\nhttps://admin.prod.getunblocked.com/teams/e0c510e2-6e19-4947-a19a-98f45478d171/inferenceExamples/065fc033-62ab-49d8-8f0a-d1e2b806a360\r\nhttps://admin.prod.getunblocked.com/teams/cc094b73-83a3-460f-a491-f57d60c80734/inferenceExamples/ef76b568-650c-421d-8c58-48abbf29d23a","mergeCommitSha":"1bc7a7c2552d0a5549fe6cbaf9b0d56303f3ebe3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9672","title":"Update sample question","createdAt":"2023-12-13T06:41:42Z"}
{"state":"Merged","mergedAt":"2023-12-13T18:32:33Z","number":9673,"mergeCommitSha":"a95c5b8f9dc60221911193702ea6b5c5eccac181","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9673","title":"Add logging to NotionPageIngestionService","createdAt":"2023-12-13T17:21:56Z"}
{"state":"Merged","mergedAt":"2023-12-13T18:10:01Z","number":9674,"mergeCommitSha":"e2d0d18054211a590b0611f9bf5bade9f9bf02b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9674","title":"Fix build","createdAt":"2023-12-13T18:06:10Z"}
{"state":"Merged","mergedAt":"2023-12-13T20:21:36Z","number":9675,"mergeCommitSha":"d3c69b2297bbc19a6b664bf6326bb37936a1181d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9675","title":"Input sanitizer cleanup","createdAt":"2023-12-13T18:21:42Z"}
{"state":"Merged","mergedAt":"2023-12-13T20:43:31Z","number":9676,"body":"Added framework for new sidebar in IDEs that currently lives in parallel with existing sidebar. Just VSCode atm.\r\n\r\n![CleanShot 2023-12-13 at 10 33 56@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/cec18b57-1aab-4e48-895a-7d4a2e9aae0c)\r\n\r\nHidden by default behind a feature flag.\r\n\r\n","mergeCommitSha":"49f7e9dd6442aa5da0c5a00c964204d5ba7fac7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9676","title":"Add base for new sidebar","createdAt":"2023-12-13T18:42:36Z"}
{"state":"Merged","mergedAt":"2023-12-13T18:51:26Z","number":9677,"mergeCommitSha":"4599c53062dfbbc544d8e1b672ef4b98bb5d3e3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9677","title":"Backfill segmentation metrics for 1 year","createdAt":"2023-12-13T18:50:31Z"}
{"state":"Merged","mergedAt":"2023-12-14T05:44:09Z","number":9678,"mergeCommitSha":"b2393c322ce0fa4ec69498589122ba2db83138eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9678","title":"Remove Unblocked is thinking...","createdAt":"2023-12-13T20:46:19Z"}
{"state":"Merged","mergedAt":"2023-12-13T21:11:10Z","number":9679,"body":"Don't throw","mergeCommitSha":"7e6efc25cfeca62d05b98bc365ef02680112491e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9679","title":"Handle 404s when retrieving notion child blocks","createdAt":"2023-12-13T20:55:13Z"}
{"state":"Merged","mergedAt":"2022-04-21T16:49:28Z","number":968,"mergeCommitSha":"774707c7f8b3a1f5ca5fd3c3c37ed10ee8584095","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/968","title":"Up cpu and memory for services","createdAt":"2022-04-21T16:20:38Z"}
{"state":"Merged","mergedAt":"2023-12-13T21:44:47Z","number":9680,"mergeCommitSha":"110e70cb2c57a38fc8e2bd03d6cb2b65d97a1890","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9680","title":"Fix loading view width","createdAt":"2023-12-13T21:31:19Z"}
{"state":"Merged","mergedAt":"2023-12-13T21:38:31Z","number":9681,"mergeCommitSha":"7544a3c20415800fccd8870117ed6870e0fe2d42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9681","title":"Fix one last settings.json","createdAt":"2023-12-13T21:38:18Z"}
{"state":"Merged","mergedAt":"2023-12-13T23:27:11Z","number":9682,"mergeCommitSha":"e6a2ff78cface51f5b9469b65d9f5e41fff04d50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9682","title":"Add topic ingestion continuations","createdAt":"2023-12-13T22:11:47Z"}
{"state":"Merged","mergedAt":"2023-12-13T23:32:34Z","number":9683,"body":"Initial setup to merge sidebar specific streams into main sidebar.","mergeCommitSha":"42fd630dd20156bc715e14ce1c859147cac71154","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9683","title":"Initial setup of myQuesitons stream","createdAt":"2023-12-13T22:12:24Z"}
{"state":"Merged","mergedAt":"2023-12-13T22:37:41Z","number":9684,"mergeCommitSha":"13c738f7965173e143746104ae5a1c3d86a29741","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9684","title":"Admin web navigation for growth metrics","createdAt":"2023-12-13T22:28:16Z"}
{"state":"Merged","mergedAt":"2023-12-13T22:55:04Z","number":9685,"mergeCommitSha":"13c879a8bd2c310f7f35ccf202df563e5ac1773d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9685","title":"Include URLs in customer-integrations channel slack message for web ingestion","createdAt":"2023-12-13T22:38:36Z"}
{"state":"Merged","mergedAt":"2023-12-13T23:30:17Z","number":9686,"mergeCommitSha":"ba49364dd2189fc9863d4b12311135cbcd48fcb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9686","title":"Move pull request summaries to mistral","createdAt":"2023-12-13T23:18:03Z"}
{"state":"Merged","mergedAt":"2023-12-13T23:54:13Z","number":9687,"mergeCommitSha":"40e6c1cf49105294c0980974009587c111710069","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9687","title":"Add logging to WebIngestionEventHandler","createdAt":"2023-12-13T23:36:30Z"}
{"state":"Merged","mergedAt":"2023-12-13T23:56:48Z","number":9688,"mergeCommitSha":"fe076db9369507a43b4a5fbd5b07d7bb3eeda252","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9688","title":"fix action-slack","createdAt":"2023-12-13T23:56:38Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:38:02Z","number":9689,"mergeCommitSha":"93576b7c6d9dd567441adbbd92e7d8c90ff128fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9689","title":"Dont apply length filtering for DocumentType.Documentation","createdAt":"2023-12-13T23:58:19Z"}
{"state":"Merged","mergedAt":"2022-04-21T17:03:00Z","number":969,"body":"Parity with web/vscode\r\n\r\nAlso some small styling patches to the extension sidebar","mergeCommitSha":"968c44ccf422f6a98bba089504bea4105061481a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/969","title":"Set to thread to read on discussion load","createdAt":"2022-04-21T16:29:00Z"}
{"state":"Merged","mergedAt":"2023-12-14T00:01:55Z","number":9690,"body":"This reverts commit fe076db9369507a43b4a5fbd5b07d7bb3eeda252.\r\n","mergeCommitSha":"f7a31f5c4490aebf369afd1034b0133fbc3d14a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9690","title":"Revert \"fix action-slack (#9688)\"","createdAt":"2023-12-14T00:01:44Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:49:59Z","number":9691,"body":"* Followup to web ingestion work\r\n* Add some URL parsing to be able to truncate past the url hostname. This necessitated some light URL manipulation/validation on the client to make sure that the string is able to be parsed by the native `URL` constructor\r\n\r\n<img width=\"797\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/7aa1187d-f194-4cac-a5f1-87092b44d4a8\">\r\n<img width=\"795\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/21da0b2c-6f35-4fd5-99fc-0a8c5b329a9c\">\r\n","mergeCommitSha":"fc7708afcac38b747e311a7e6ae36ec955196785","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9691","title":"Add url truncation and validation","createdAt":"2023-12-14T00:28:55Z"}
{"state":"Merged","mergedAt":"2023-12-14T00:37:31Z","number":9692,"mergeCommitSha":"06e7bc597e848cbda4011e96afd3f1d0415a6432","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9692","title":"depprecate","createdAt":"2023-12-14T00:37:20Z"}
{"state":"Merged","mergedAt":"2023-12-15T19:00:54Z","number":9693,"mergeCommitSha":"0beabab8b72a9c0739dbdf6f87e67722d843a42b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9693","title":"Unhide web ingestion nav item from dashboard","createdAt":"2023-12-14T00:38:03Z"}
{"state":"Merged","mergedAt":"2023-12-14T01:02:10Z","number":9694,"mergeCommitSha":"92eabddc4a9e20e9b40c270798c9d92f86ed4306","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9694","title":"[SKIP TESTS] Add logging to WebIngestionEventHandler","createdAt":"2023-12-14T00:50:52Z"}
{"state":"Merged","mergedAt":"2023-12-14T01:11:03Z","number":9695,"mergeCommitSha":"9cfe2f88a33fd7ce0ad597505afb7ddfcac64845","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9695","title":"Try deprecating again","createdAt":"2023-12-14T01:10:57Z"}
{"state":"Merged","mergedAt":"2023-12-14T01:44:44Z","number":9696,"body":"- Try deprecating again\r\n- Removellama2timestwo\r\n","mergeCommitSha":"c2014b8d65b125ede409f30bae52df63544e0a07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9696","title":"RemoveLLama2Times2","createdAt":"2023-12-14T01:43:13Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:26:28Z","number":9697,"body":"Refactor how syntax highlighting works in all clients.  This is in preparation for the new \"Ask a Question\" IDE UI, and getting syntax highlighting into the message view:\r\n\r\n* `ClientWorkspace` now has a `syntaxHighlighter` property, of type `SyntaxHighlightService`.  This means any part of any code anywhere can run syntax highlighting without sweating the details.  Each part of each client implements syntax highlighting in whatever way makes the most sense:\r\n* Dashboard: Uses new `AsyncHighlightJsService`, which calls highlight.js asynchronously in a web worker (so we don't block rendering)\r\n* IntelliJ plugin: Uses new `SyncHighlightJsService`, which calls highlight.js synchronously.  No need for async here.\r\n* VSCode plugin: Uses `CodeToHtmlRenderer`, which uses shiki to render synchronously.  No need for async here.\r\n* IDE webviews: proxies the request (via PromiseProxy) to the plugin asynchronously.  Rendering is not blocked.\r\n\r\nThis will allow us to greatly simplify all the code around syntax highlighting.  For instance, we can now have a single `CodeBlock` component.  We don't need to pre-highlight code in all of the IDE commands.  I haven't cleaned up all of this yet, I will do that as I visit other code.","mergeCommitSha":"8f5f72a30fd1fadbae761e52ca9234b33cc55a56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9697","title":"Refactor syntax highlighting","createdAt":"2023-12-14T05:04:40Z"}
{"state":"Closed","mergedAt":null,"number":9698,"body":"Remove everything.  All NPM packages, all references, etc.  Testing if this fixes the timer bugs, won't merge until I'm convinced it is the root cause.","mergeCommitSha":"412408ec4ebb62aa7a2cbadac7cea91a0b3a269f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9698","title":"Completely remove Sentry","createdAt":"2023-12-14T05:49:33Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:34:10Z","number":9699,"body":"This can happen if you look at the thread list while the thread is in the loading state.\r\n\r\n![CleanShot 2023-12-13 at 22 16 48@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/fa667150-7b09-41de-8aa4-a512ab00dfe2)\r\n","mergeCommitSha":"9e0eb864b030a328910e2ecfea458c3640d19848","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9699","title":"Show loading dots in dashboard thread lists","createdAt":"2023-12-14T06:18:20Z"}
{"state":"Merged","mergedAt":"2022-01-21T18:42:00Z","number":97,"body":"- Added EKS VPC as an imported resource (infrastructure/cdk/core/cdk.context.json  are generated by cdk)\r\n- Added routes to both coreVPC as well as EKS VPC to route traffic through VPC peering connection \r\n- Added service account definition for RDS IAM based access\r\n- Created documentation for managing IAM mapped service accounts using `eksctl`\r\n\r\nAll changes have been deployed. RDS connection is now working thanks to all of Rashin's hard work. ","mergeCommitSha":"98e03e03244ac0061e6bba239d40a9cd5b23dc29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/97","title":"Ckd rds access for eks","createdAt":"2022-01-21T07:30:13Z"}
{"state":"Merged","mergedAt":"2022-04-21T20:23:05Z","number":970,"body":"Combination of changes below make the lookup of marks for a file O(1) as long as\r\nthe current commit has been previously fully calculated for all sourcemarks at\r\nleast once.\r\n\r\n- Use Git tree SHA\r\n\r\n  A commit SHA is a hash of the content tree and metadata, whereas a tree SHA is\r\n  a hash of the content tree only.\r\n\r\n  Since the sourcemark engine only needs to rerun when the content tree changes,\r\n  we now use tree SHAs where possible to avoid unnecessary re-work.\r\n\r\n  The impact of this change is that the source mark engine will not unnecessarily\r\n  rerun when a user amends a commit message, for example.\r\n\r\n- Cache marks by file\r\n\r\n  For every each tree SHA, create a cache from file-to-markIDs at the given tree\r\n  SHA. Prune the set of marks passed to the engine to only those that can possibly\r\n  affect the current file, which includes all uncommitted files if the current\r\n  file is edited.\r\n\r\n- Cache point by mark\r\n\r\n  For every each tree SHA, maintain a cache from markID-to-point at the given\r\n  tree SHA.","mergeCommitSha":"9d354ff4f3e19fd0d9532c98192487495ef0ba87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/970","title":"Sourcemark Engine processes file changes incrementally","createdAt":"2022-04-21T17:27:18Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:31:49Z","number":9700,"mergeCommitSha":"1e8a0d706bcbe9bf63ff30bb9e1c3a0d89ade864","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9700","title":"Log error on failure to scrape a web page","createdAt":"2023-12-14T18:20:27Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:29:04Z","number":9701,"mergeCommitSha":"62a51523c8bbb814999c0d4c8de6615f359296ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9701","title":"increase cpu/memory","createdAt":"2023-12-14T18:28:38Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:30:43Z","number":9702,"body":"- increase cpu/memory\r\n- Revert \"increase cpu/memory\"\r\n- Try again\r\n","mergeCommitSha":"2bd4a1babc3cafa55903e3c07998e4f346a2cff8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9702","title":"UpMemory2","createdAt":"2023-12-14T18:30:29Z"}
{"state":"Merged","mergedAt":"2023-12-14T18:57:11Z","number":9703,"body":"We block onboarding until PR ingestion is complete, so this UI is no longer necessary --- plus, the value returned by the API seems to be incorrect in some scenarios, so the UI is now misleading.","mergeCommitSha":"5dd7211832cda76d6ebe0a58066e1432651226a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9703","title":"Remove unnecessary loading UI","createdAt":"2023-12-14T18:44:25Z"}
{"state":"Merged","mergedAt":"2023-12-15T17:36:53Z","number":9704,"body":"Buttons that act as links should text align start\r\n![CleanShot 2023-12-14 at 10 58 20@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/e3c42695-8412-46bb-b304-90ebb1d978c5)\r\n","mergeCommitSha":"a37adf703ca9c324a8f5b9e100e54b007a06a5bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9704","title":"Links should align start","createdAt":"2023-12-14T18:59:12Z"}
{"state":"Merged","mergedAt":"2023-12-14T23:17:58Z","number":9705,"body":"\r\n![CleanShot 2023-12-14 at 10 41 15@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/728a2cf4-9f9e-419a-98d6-fe4f9d69878d)\r\n","mergeCommitSha":"0eafb92b41b3e386ae4143bc6ca2341c452dfcb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9705","title":"Add MyQuestions sidebar to mainsidebar","createdAt":"2023-12-14T19:01:14Z"}
{"state":"Merged","mergedAt":"2023-12-14T19:14:09Z","number":9706,"body":"- increase cpu/memory\r\n- Revert \"increase cpu/memory\"\r\n- Try again\r\n- Fix llm prompts\r\n","mergeCommitSha":"ea9d23f9911cb17c73204f07ce67a1b9c67bd0b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9706","title":"FixLLMPrompts","createdAt":"2023-12-14T19:13:45Z"}
{"state":"Merged","mergedAt":"2023-12-14T20:48:21Z","number":9707,"mergeCommitSha":"99442e253e0751adc0ef296291c7766716314a20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9707","title":"adds no extension files to the classifier","createdAt":"2023-12-14T19:18:26Z"}
{"state":"Merged","mergedAt":"2023-12-14T20:07:51Z","number":9708,"mergeCommitSha":"26a2e1b2993229d962b37e9f93a56599294f6ce5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9708","title":"Look for existing id before generating","createdAt":"2023-12-14T19:34:58Z"}
{"state":"Merged","mergedAt":"2023-12-14T19:57:31Z","number":9709,"mergeCommitSha":"6624b3f8e10e2d60d47f34298875ce7f0960f217","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9709","title":"Increase repetition penalty","createdAt":"2023-12-14T19:57:21Z"}
{"state":"Merged","mergedAt":"2022-04-21T22:41:35Z","number":971,"body":"- Added config for SQS queues\r\n- Added a email_notification queue in both Dev and Prod configs\r\n- Added a new stack to create SQS queues\r\n- Updated notification service account to add SQS reader permissions\r\n- Removed Redis creds from notification service helm values files. I will be removing extra permissions from service account once these changes have been deployed. If I remove them now it will cause service crashloop","mergeCommitSha":"4c63a7746fef6742bf91e8987f1e153748710a5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/971","title":"Adding SQS queue to CDK","createdAt":"2022-04-21T18:10:48Z"}
{"state":"Merged","mergedAt":"2023-12-14T20:37:30Z","number":9710,"mergeCommitSha":"342ccd528c705d9882a308603c8427928e859ed0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9710","title":"Try new auto scaling","createdAt":"2023-12-14T20:36:31Z"}
{"state":"Merged","mergedAt":"2023-12-15T21:27:10Z","number":9711,"body":"Remove duplicate CodeBlock, FileHeader, and MarkReference components.  Web and IDEs had separate implementations for these that were mostly the same, but not quite -- creating a lot of inconsistencies in behaviour and code.\r\n\r\nAs a part of this, I got rid of the expansion maps that we were explicitly keeping everywhere.  The expand/collapse state is now run in one line of code, using `useLocalStorageState` instead, at the view site where we use it.","mergeCommitSha":"32e9bcd19338db2b399903adf1ad04cb49e5cdfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9711","title":"Remove duplicate code components","createdAt":"2023-12-14T22:46:34Z"}
{"state":"Merged","mergedAt":"2023-12-14T23:08:13Z","number":9712,"mergeCommitSha":"12ec5b98789cdcd18b21028be4e9d74f640d17a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9712","title":"[SKIP TESTS] Remove logging in WebIngestionEventHandler","createdAt":"2023-12-14T22:48:30Z"}
{"state":"Merged","mergedAt":"2023-12-15T20:33:28Z","number":9713,"mergeCommitSha":"f0d618a941974b1c787c4fc5a6385b43c0d5276e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9713","title":"Statically typed ML functions in kotlin","createdAt":"2023-12-14T22:59:49Z"}
{"state":"Merged","mergedAt":"2023-12-15T18:26:10Z","number":9714,"mergeCommitSha":"ee418accc2d69786183372c820a177c9aa39ec13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9714","title":"Enable web integration in PROD","createdAt":"2023-12-14T23:36:42Z"}
{"state":"Merged","mergedAt":"2023-12-15T00:32:27Z","number":9715,"mergeCommitSha":"00432817e60864605792d154b40ee310b0181fc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9715","title":"Change docmouent source to Provider.Web","createdAt":"2023-12-15T00:08:23Z"}
{"state":"Merged","mergedAt":"2023-12-15T01:09:51Z","number":9716,"mergeCommitSha":"5242f7101f0648919efd906358461d3483437628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9716","title":"Safely get sparse vectors","createdAt":"2023-12-15T00:23:25Z"}
{"state":"Merged","mergedAt":"2023-12-15T03:22:59Z","number":9717,"mergeCommitSha":"c2066e13ba80021f813234538e045fa8a1e5029b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9717","title":"add .txt support to llm_source_code_classifier","createdAt":"2023-12-15T00:53:46Z"}
{"state":"Merged","mergedAt":"2023-12-15T12:05:29Z","number":9718,"body":"Hit this with 500 internal server errors during topic mapping.\r\nWe do some hacky sparse vector validation in random locations.\r\nThis pr standardizes that.","mergeCommitSha":"abbb1d5146a2a83ba187c37bc3afa1cb36b97166","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9718","title":"Generalize validation of sparse vectors","createdAt":"2023-12-15T11:40:54Z"}
{"state":"Merged","mergedAt":"2023-12-15T12:53:39Z","number":9719,"mergeCommitSha":"bfcb1958feb0e7920de5f9fda22bcf6be3e19e59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9719","title":"Increase ci timeout","createdAt":"2023-12-15T12:53:31Z"}
{"state":"Merged","mergedAt":"2022-04-21T20:39:06Z","number":972,"body":"- Background so it does not affect critical path to showing marks, so takes 0 seconds now.\r\n- Add debug metrics.\r\n- Reduce batch size to 500 which bring batch request latency down\r\n  to ~800ms. Was up to 3s per batch.\r\n- Fixed a reentrant bug where the _same_ points were uploaded multiple\r\n  times by different calls. JS is single-threaded but not thread-safe.\r\n- Overall latency went from ~16 second to ~5, due to contention probably.\r\n\r\n\r\n## Thread safety issue\r\n\r\nSee example debug output below which show how multiple concurrent runs process different batches in parallel. The runs are interleaved. These were previously reading from the same shared state and clobbering the server.\r\n\r\n<img width=\"774\" alt=\"Screen Shot 2022-04-21 at 13 05 36\" src=\"https://user-images.githubusercontent.com/1798345/164543686-9ad0fca8-4227-44b7-bb06-9efc2e410608.png\">\r\n\r\n","mergeCommitSha":"297f67ed12fd3f2215e612cdf00ad02ef15dfb64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/972","title":"Background sourcepoint upload","createdAt":"2022-04-21T20:01:28Z"}
{"state":"Merged","mergedAt":"2023-12-15T14:31:19Z","number":9720,"body":"- Add logging to topics ippeline\r\n- ADd logging\r\n","mergeCommitSha":"39b0f4e41139bbd5df36c0a3ad213e39a61ed1ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9720","title":"FixJsonparsing","createdAt":"2023-12-15T14:31:13Z"}
{"state":"Merged","mergedAt":"2023-12-15T19:11:16Z","number":9721,"mergeCommitSha":"2cb1d3846c519ff65c1918f561eb7a219a56e9b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9721","title":"Increase re-enqueue delay for Notion page ingestion","createdAt":"2023-12-15T18:39:18Z"}
{"state":"Merged","mergedAt":"2023-12-15T19:46:13Z","number":9722,"mergeCommitSha":"be19201687493376b1a59dadef017f661ef53c23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9722","title":"Dont generate events for ignored urls","createdAt":"2023-12-15T19:34:44Z"}
{"state":"Merged","mergedAt":"2023-12-19T00:56:21Z","number":9723,"body":"Deprecate the following feature flags, and remove any reference to the flags in clients:\r\n\r\n* `UseVSCodeGitProvider`: this was meant as a fallback in case the custom provider had bugs, but we haven't had to enable it for anyone.  Remove the flag check and remove the VSCode git provider.\r\n* `FeatureTopicDescriptions`: not referenced anywhere\r\n* `SemanticSearchRepoContext`: This has been enabled for everyone for ages, and only affects placeholder text in the IDEs.  Remove the flag reference, always display repo context in the placeholder.\r\n* `InterpolatedMessageRendering`: Enable this permanently in all clients\r\n* `FeatureNewInbox`: Enable this permanently in all clients","mergeCommitSha":"b4984bcbb8e0dede96bb97ce111e5ebdd97fa79d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9723","title":"Deprecate a bunch of feature flags","createdAt":"2023-12-15T19:37:58Z"}
{"state":"Merged","mergedAt":"2023-12-15T20:44:09Z","number":9724,"mergeCommitSha":"5c3f3ee98e43197d104146c025b70434b8e0ce5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9724","title":"Do not compressed the first question to the bot in a thread","createdAt":"2023-12-15T20:29:53Z"}
{"state":"Merged","mergedAt":"2023-12-15T20:53:44Z","number":9725,"mergeCommitSha":"5d003af97c71921b5b32b824c51085bfce3b7480","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9725","title":"[SKIP TESTS] Temporarily disable web site ingestion events","createdAt":"2023-12-15T20:42:00Z"}
{"state":"Merged","mergedAt":"2023-12-18T18:14:58Z","number":9726,"body":"Follows similar pattern to toolwindow initialization but webviewProvider significantly simpler.\r\nWill be adding Insight into this toolwindow next.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/ec1bfd87-9ed2-4b58-bb54-07c213df91d1\r\n\r\n","mergeCommitSha":"a9dc5273ead556d04ad57630602a12b970894604","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9726","title":"Setup IntelliJ to handle new MainSidebar","createdAt":"2023-12-15T21:13:28Z"}
{"state":"Merged","mergedAt":"2023-12-15T21:52:15Z","number":9727,"body":"We're generating too many events during crawling, and using redis during message handling to check whether we've visited the page recently. Let's do the redis check before we enqueue to avoid generating excessive messages.","mergeCommitSha":"418818bbcd8b112b123b7a66e495462ce494c3f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9727","title":"[Web Ingestion] Prevent repeated event enqueuing for the same url","createdAt":"2023-12-15T21:32:37Z"}
{"state":"Merged","mergedAt":"2023-12-15T22:05:42Z","number":9728,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/cae886a9-bd83-4e23-8917-ccd0a3b40078\r\n\r\n","mergeCommitSha":"78fb29ae75c9b6976578eaa49c37bac49de54910","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9728","title":"Clear local selected state of sidebar rows","createdAt":"2023-12-15T21:35:01Z"}
{"state":"Merged","mergedAt":"2023-12-15T22:07:47Z","number":9729,"mergeCommitSha":"db0840d1e757ad08ab5d3602de9fab6ed6bdeacf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9729","title":"fix cache cleanup job permissions","createdAt":"2023-12-15T21:54:55Z"}
{"state":"Merged","mergedAt":"2022-04-21T21:50:20Z","number":973,"mergeCommitSha":"7d75942f5a324472f7f0d4c9290462c1e15ad2e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/973","title":"Assert that range is valid and report errors","createdAt":"2022-04-21T20:19:42Z"}
{"state":"Merged","mergedAt":"2023-12-15T22:40:28Z","number":9730,"mergeCommitSha":"76c6255f898c0335e41f842278a6096ea41e3c96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9730","title":"[SKIP TESTS] Set cache entry after enqueing event","createdAt":"2023-12-15T22:28:34Z"}
{"state":"Merged","mergedAt":"2023-12-15T23:15:38Z","number":9731,"body":"## motivation\n- currently we retrieve 200 docs per user query, because theye is a possibility that some of the docs are associated with disconnected repos, which when filtered out will result in no docs. once this change is live and is actively managing repos, then we can reduce the doc retrieval limit to somthing more reasonable, like 100, which will reduce retrieval and reranking latency.\n- reclaim storage cost/capacity especially in Pinecone. approx 1,680 repos.","mergeCommitSha":"bfccce1bb3d51e4e9f37d1c18bca59c3243d4257","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9731","title":"Cleanup disconnected repos","createdAt":"2023-12-15T22:36:33Z"}
{"state":"Merged","mergedAt":"2023-12-15T23:37:39Z","number":9732,"mergeCommitSha":"6e8f19af7493e6a48f4f156ab1db04fe3f644cb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9732","title":"Cleanup deselected repos","createdAt":"2023-12-15T23:09:16Z"}
{"state":"Merged","mergedAt":"2023-12-19T00:43:33Z","number":9733,"body":"This only syntax highlights once we have completed streaming the content for the block, for now.  We can add some amount of syntax highlighting while streaming, but that will require more work, as the syntax highlighters don't like to render content alongside un-highlighted content.\r\n\r\n![CleanShot 2023-12-15 at 15 16 38@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/c13c5f3a-c165-417e-8db8-c160a3de534e)\r\n","mergeCommitSha":"cf99811420fd197e469bf6174b5ebb495828207a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9733","title":"Syntax highlight MessageView CodeBlock content","createdAt":"2023-12-15T23:16:12Z"}
{"state":"Closed","mergedAt":null,"number":9734,"mergeCommitSha":"a2190cc91da4e89fb22c933de4f3f6bbf01792e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9734","title":"Add ability to clear an integration identity's token from the admin console","createdAt":"2023-12-15T23:18:46Z"}
{"state":"Merged","mergedAt":"2023-12-15T23:24:05Z","number":9735,"mergeCommitSha":"2f5c6bb90c7cc4a2b706520e1f00c9f88d540a99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9735","title":"Fix jetbrains build","createdAt":"2023-12-15T23:23:03Z"}
{"state":"Merged","mergedAt":"2023-12-15T23:49:05Z","number":9736,"mergeCommitSha":"45ecd2b91f743aa60213813c26b09a4c6478af1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9736","title":"Add functions template","createdAt":"2023-12-15T23:26:46Z"}
{"state":"Merged","mergedAt":"2023-12-16T00:02:06Z","number":9737,"mergeCommitSha":"baf99ee859f3027b514b426617bee255de97ec40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9737","title":"Get tokens from the latest authed Notion identity","createdAt":"2023-12-15T23:42:49Z"}
{"state":"Merged","mergedAt":"2023-12-19T01:08:33Z","number":9738,"body":"![image](https://github.com/NextChapterSoftware/unblocked/assets/13431372/b9bce54c-dbe9-4943-abf3-4d624ecdf2f6)\r\n\r\n<img width=\"1378\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/3a52e718-abda-44ac-bb92-02f2e0f3a32d\">\r\n","mergeCommitSha":"663a485216292109ea596806e58572d80af4a0cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9738","title":"Add loading tooltip for repo processing state","createdAt":"2023-12-16T00:39:44Z"}
{"state":"Merged","mergedAt":"2023-12-16T01:02:15Z","number":9739,"mergeCommitSha":"0147453a786ac941259330f9164b583a873af147","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9739","title":"Show repo cleanup in admin web","createdAt":"2023-12-16T01:01:10Z"}
{"state":"Merged","mergedAt":"2022-04-21T21:47:54Z","number":974,"body":"Similar to Chrome extension, we need to punch a hole into our CORS setup for safari web extension...\r\nThis sucks...\r\n\r\nWe may want to figure out a better way to do this post April.\r\n\r\n<img width=\"990\" alt=\"CleanShot 2022-04-21 at 14 19 01@2x\" src=\"https://user-images.githubusercontent.com/1553313/164554775-c01662a6-8f08-4973-a9b4-4cd46fbc41cc.png\">\r\n\r\n","mergeCommitSha":"67d2ea1d10c3862eabe83597ecaec387d5a41f74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/974","title":"Update CORS for safari extension","createdAt":"2022-04-21T21:25:08Z"}
{"state":"Merged","mergedAt":"2023-12-16T01:12:28Z","number":9740,"mergeCommitSha":"7e6830e997403f7cce808de62310743d87891337","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9740","title":"Add logging to NotionOauthTokenProvider","createdAt":"2023-12-16T01:01:37Z"}
{"state":"Merged","mergedAt":"2023-12-17T22:53:37Z","number":9741,"mergeCommitSha":"506413d7128bccc2072767d553e5f9f33e49512d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9741","title":"add .sql to source classifier/filter","createdAt":"2023-12-16T01:04:13Z"}
{"state":"Merged","mergedAt":"2023-12-16T01:52:13Z","number":9742,"body":"Hard delete the repo if the team is deleted / disconnected.","mergeCommitSha":"9982a7662b0ca361838a0ac319a09ebce7169932","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9742","title":"Fix bug in delete deletion","createdAt":"2023-12-16T01:19:25Z"}
{"state":"Merged","mergedAt":"2023-12-16T04:21:08Z","number":9743,"mergeCommitSha":"5824fa5c1b3bc6e3104e8ddba2a81b796e665b67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9743","title":"Add more logging to NotionIngestionService","createdAt":"2023-12-16T03:58:44Z"}
{"state":"Merged","mergedAt":"2023-12-16T05:43:02Z","number":9744,"mergeCommitSha":"182ae7881133be4f1fd0ebc8864b48cda7e41680","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9744","title":"Fix repo delete bug","createdAt":"2023-12-16T05:19:45Z"}
{"state":"Merged","mergedAt":"2023-12-16T06:27:46Z","number":9745,"body":"Lets us see if we have access to a page","mergeCommitSha":"ffdb21c73b9e5e0098bf62ab073372c83066277d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9745","title":"Add ability to search Notion pages by title from admin console","createdAt":"2023-12-16T06:05:26Z"}
{"state":"Merged","mergedAt":"2023-12-16T07:31:52Z","number":9746,"mergeCommitSha":"1159b752cf0bd3e9a1daa7d2ad0073aabb128d9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9746","title":"Add ability to get block children for a page or block from admin console","createdAt":"2023-12-16T07:21:56Z"}
{"state":"Merged","mergedAt":"2023-12-17T05:38:06Z","number":9747,"mergeCommitSha":"a638905b89e4ba84b19633b81d5cf5319ed2099b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9747","title":"Try to unblocked stuck repo delete","createdAt":"2023-12-17T05:36:57Z"}
{"state":"Merged","mergedAt":"2023-12-17T07:52:05Z","number":9748,"mergeCommitSha":"c854908e561d71d14929abbf17813e54ae00339a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9748","title":"Fix has repos bug","createdAt":"2023-12-17T05:46:12Z"}
{"state":"Merged","mergedAt":"2023-12-17T22:51:03Z","number":9749,"mergeCommitSha":"374f5e298dca0c4e4164d8e580e0b203edae4706","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9749","title":"Introduce DB indexes for efficient deletes","createdAt":"2023-12-17T22:27:44Z"}
{"state":"Merged","mergedAt":"2022-04-21T23:55:45Z","number":975,"body":"Pr does a few things.\r\n1. Adds an overlay spinner on images being uploaded.\r\n2. Associated a unique upload id with uploads so we're no longer dependent on file names.\r\n3. Fixes a bug with Normalization of nodes when dragging and dropping into an empty editor. ","mergeCommitSha":"50dc85de68cfa6a5a733b2b603ff3bd4ef5b7597","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/975","title":"Add spinner and fix stuff","createdAt":"2022-04-21T21:25:23Z"}
{"state":"Merged","mergedAt":"2023-12-18T03:03:43Z","number":9750,"mergeCommitSha":"57af69fb4cd57bf182f605a19244a15aff0f9ada","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9750","title":"Add Stack Overflow Articles API","createdAt":"2023-12-18T02:40:51Z"}
{"state":"Merged","mergedAt":"2023-12-18T04:29:38Z","number":9751,"body":"Vast majority of tables do not need an index on `modifiedAt`. Saves GBs in index space.","mergeCommitSha":"93716acd0c9eb33184400495d65e5b1ba3ef8883","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9751","title":"Remove unused indexes, especially modifiedAt","createdAt":"2023-12-18T04:07:30Z"}
{"state":"Merged","mergedAt":"2023-12-18T06:00:58Z","number":9752,"mergeCommitSha":"93ea5f6bc9f19cb7f2bdf527b63dd8dd93a12bd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9752","title":"Drop DB tables and indexes","createdAt":"2023-12-18T05:42:03Z"}
{"state":"Merged","mergedAt":"2023-12-18T18:23:59Z","number":9753,"body":"## Changes\r\n\r\n1. remove us-east config\r\n2. drop all DEV search-service events (80% of DEV events)\r\n3. drop all PROD topic-service events (40% of PROD events)","mergeCommitSha":"2195278964fa57382d8d84ba557f7f58fef8537a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9753","title":"Reconfigure Refinery dropping lowest values events to avoid Honeycomb throttling","createdAt":"2023-12-18T18:09:31Z"}
{"state":"Merged","mergedAt":"2023-12-18T19:32:22Z","number":9754,"mergeCommitSha":"3aeb13f53b0ea7fc9465e41e815eb930d1a72c42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9754","title":"Add ability to get Notion pages and installing user from admin console","createdAt":"2023-12-18T19:09:46Z"}
{"state":"Merged","mergedAt":"2023-12-18T23:15:28Z","number":9755,"mergeCommitSha":"bb19a2c3ec7b80931ff35bebc3040daefce5dd18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9755","title":"Adds Mistral to ml-client","createdAt":"2023-12-18T19:56:45Z"}
{"state":"Merged","mergedAt":"2023-12-18T20:35:06Z","number":9756,"mergeCommitSha":"7ffd81ecc3c3430368c111e8d92db0b627e61e05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9756","title":"Adds some missing foreign team indexes","createdAt":"2023-12-18T20:11:15Z"}
{"state":"Merged","mergedAt":"2023-12-20T18:39:26Z","number":9757,"body":"Integrates discussion view into main sidebar.\r\n\r\n* Required updating mainSidebar to utilize postMessage.\r\n* This also updated parts of DiscussionThreadView to utilize ClientWorkspace instead of postMessage. ** This will affect existing views as well**","mergeCommitSha":"47924f663e2cf32b2e709e0e479fbe69fe3726a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9757","title":"Integrate discussion view into main sidebar","createdAt":"2023-12-18T21:09:57Z"}
{"state":"Merged","mergedAt":"2023-12-18T22:38:28Z","number":9758,"mergeCommitSha":"e44a2b105f786c8534d68581efbff233e2881c08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9758","title":"[SKIP TESTS] Add ability to specify identity when getting a page through the admin console","createdAt":"2023-12-18T22:16:17Z"}
{"state":"Merged","mergedAt":"2023-12-20T01:09:18Z","number":9759,"mergeCommitSha":"e90ece9fc5cfaa2856928a799187e0cecdb5661c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9759","title":"ML Functions","createdAt":"2023-12-18T23:49:59Z"}
{"state":"Merged","mergedAt":"2022-04-21T23:17:10Z","number":976,"mergeCommitSha":"e1cc54dfad15ed0d23cb6a70070cc63dfe0a8557","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/976","title":"add redis access back","createdAt":"2022-04-21T23:03:23Z"}
{"state":"Merged","mergedAt":"2023-12-19T00:19:00Z","number":9760,"body":"With embedding disabled (enabled in next PR)","mergeCommitSha":"0e14a0bf6ed43d7b90b3e95446dbd7f86b43b543","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9760","title":"Add logic for ingesting Stack Overflow articles","createdAt":"2023-12-18T23:56:16Z"}
{"state":"Merged","mergedAt":"2023-12-19T22:32:30Z","number":9761,"body":"Don't have permission to deploy this to only dev for testing.","mergeCommitSha":"9604ba1bf7a146577b28d5a5eae0336b8c3ee81f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9761","title":"adds LLMContentFilter + LLMContentFilterTest","createdAt":"2023-12-19T00:14:34Z"}
{"state":"Merged","mergedAt":"2023-12-19T19:37:45Z","number":9762,"body":"When interpolating messages, when we are render-constrained for whatever reason, calculate the correct number of characters to jump, so that overall velocity is still correct.\r\n\r\nThis doesn't fix the IDE throttled rendering issue, but when it happens it will still be usable.  As a side effect, this calculates velocity much better then the old algorithm, and will handle situations like limited CPU and rendering speeds as well.\r\n\r\nVSCode rendering will behave roughly like this:\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/a2af861e-edf2-4763-8e70-e4b55ead53ec\r\n\r\n","mergeCommitSha":"29a511800f3b28c5a51c96239a961a9dae4ba2b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9762","title":"Less broken IDE message rendering","createdAt":"2023-12-19T00:23:36Z"}
{"state":"Merged","mergedAt":"2023-12-19T20:19:46Z","number":9763,"mergeCommitSha":"653ff66e2a30685f41d97e7a4aa79e7361376f13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9763","title":"Enable embedding of Stack Overflow articles","createdAt":"2023-12-19T00:30:37Z"}
{"state":"Merged","mergedAt":"2023-12-19T22:30:12Z","number":9764,"body":"Skeleton of flow to create and list demo templates.\r\n\r\n<img width=\"1297\" alt=\"Screenshot 2023-12-19 at 12 42 17\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/0b4a3454-cff3-4503-96e8-4b311adbdb29\">\r\n","mergeCommitSha":"b8a3150067c3047bea79063585d501ff3a74e409","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9764","title":"Introduce demo templates","createdAt":"2023-12-19T00:56:41Z"}
{"state":"Merged","mergedAt":"2023-12-19T18:54:28Z","number":9765,"body":"The last time empty states were updated were before we introduced \"Ask Unblocked\". This includes a visual update, and also includes call to actions that are a little more helpful than what we had before (which was nothing at all).\r\n\r\n**My Discussions**\r\n\r\n![CleanShot 2023-12-18 at 17 59 08@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/ae1e8d09-4a67-42bc-96a9-0d3a28d605fa)\r\n\r\n\r\n**No Unreads**\r\n\r\n![CleanShot 2023-12-19 at 10 49 06@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/6e14d974-b90f-4bc2-b593-37a2eb9914a7)\r\n\r\n\r\n**Team Questions**\r\n\r\n![CleanShot 2023-12-19 at 08 52 16@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/6426058b-bb5a-4bab-be54-760587af6c42)\r\n\r\n\r\n**Recommended**\r\n\r\n![CleanShot 2023-12-19 at 08 52 35@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/46e046e0-138c-4f37-aad2-588f13e76c6f)\r\n\r\n\r\n**Recently Deleted**\r\n\r\n![CleanShot 2023-12-19 at 09 04 39@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/2f99e166-d9ff-4c28-8476-c60f7d2f5f7d)\r\n\r\n**Responsive**\r\n\r\n![Group 4945](https://github.com/NextChapterSoftware/unblocked/assets/13353189/3f8953a0-bf8a-4dba-b310-de890e1e74bd)\r\n\r\n","mergeCommitSha":"d550f134da295249581f9d01217b4a7bdaeb1891","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9765","title":"Updating empty list states on dashboard","createdAt":"2023-12-19T02:02:29Z"}
{"state":"Merged","mergedAt":"2023-12-19T11:53:22Z","number":9766,"mergeCommitSha":"fb1ac08751d9cbaa1e10f567bbdee8f29a1364ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9766","title":"Add basic topc experts functionality","createdAt":"2023-12-19T11:44:31Z"}
{"state":"Merged","mergedAt":"2023-12-19T12:12:33Z","number":9767,"mergeCommitSha":"b80cdf655f7253f6a63c34bec96b105c001e02bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9767","title":"Fix max documents during topics proceessing","createdAt":"2023-12-19T12:12:25Z"}
{"state":"Merged","mergedAt":"2023-12-19T18:41:22Z","number":9768,"mergeCommitSha":"9dc8f67578220737161a700bbaab4544bea86bcb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9768","title":"Retry Notion page API call on HttpRequestTimeoutException","createdAt":"2023-12-19T18:15:49Z"}
{"state":"Merged","mergedAt":"2023-12-19T19:16:43Z","number":9769,"body":"These legacy teams have invalid GitHub App installs:\r\nhttps://admin.prod.getunblocked.com/search?q=%224fac47e6-7d44-42d0-aa26-5b77e252c9d8%22+%220f583ea6-bf2e-4a1a-9050-686106f4e12f%22+%229c20cfb1-e0a3-422c-aba0-c48647ff5b91%22+%22a7ffc408-e93e-48b9-ac87-871fc76af33d%22+%2229cbd453-4a25-4792-9424-6f172abfab70%22+%222ef232b5-7866-4dfc-8c44-dd495ea9c33c%22+%22da80b4f7-f535-4950-a7aa-2e30fd9173ba%22+%22b3b49c13-3243-4494-a6b0-72bbfdc15383%22\r\n\r\nAs a result their repos are selected but not connected. Right now the repo cleanup process is fighting with repo ingestion: where repo cleanup thinks they should be deleted, and repo ingestion recreates them as soon as they are deleted.\r\n\r\nThis PR changes the repo cleanup so that it only deletes repos if they are _both_ disconnected and deselected.","mergeCommitSha":"52431ddbc1605d2b83899537edbf9f794f5e502b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9769","title":"Repo cleanup takes into account teams with invalid installations","createdAt":"2023-12-19T18:52:59Z"}
{"state":"Merged","mergedAt":"2022-04-21T23:37:23Z","number":977,"body":"This is an attempt to match how we displayed thread views in the OctoberDemo:\r\n\r\n* When you display a thread view, display a text editor for the code associated with that view beside it (in a split view)\r\n* Reuse the single webview, so if you launch another thread UI it re-focuses that tab\r\n* Display the text editor in a \"preview\" UI so it is also reused.","mergeCommitSha":"565f388caf1a56edda3f745c03adef7055b4bd31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/977","title":"Launch thread UI and text editor UI in split view","createdAt":"2022-04-21T23:09:00Z"}
{"state":"Merged","mergedAt":"2023-12-19T19:04:07Z","number":9770,"mergeCommitSha":"7d8677eca16b8dff9b9755f931b3655101ef262d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9770","title":"CI installer has content:write permission for Git tag creation","createdAt":"2023-12-19T19:01:08Z"}
{"state":"Merged","mergedAt":"2023-12-19T19:22:59Z","number":9771,"body":"More restrictive version of the fix that Rashin made here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/9674/files","mergeCommitSha":"709b71fade55b2b7011dd1d77fbff4ce1b1821f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9771","title":"Tighten up CI permissions","createdAt":"2023-12-19T19:09:45Z"}
{"state":"Merged","mergedAt":"2023-12-19T19:49:03Z","number":9772,"mergeCommitSha":"bb602242595451f4ca5183165a9c2cdd53de4385","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9772","title":"Remove dead code for topic experts","createdAt":"2023-12-19T19:22:33Z"}
{"state":"Merged","mergedAt":"2023-12-19T19:36:09Z","number":9773,"body":"Use the TeamRepo store for the \"repos are loading\" warnings, instead of the EnabledReposStore.  EnabledReposStore should only be used to edit the enabled repos.  TeamRepos has pusher support, and will update as repos change state.","mergeCommitSha":"2ba890956ef780104dd7cd6970e96fbf31554a67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9773","title":"Use TeamRepoStore for \"repos are loading\" warnings","createdAt":"2023-12-19T19:24:13Z"}
{"state":"Merged","mergedAt":"2023-12-19T20:04:13Z","number":9774,"body":"Removes selected tab index stream from webview controllers.\r\nmoved to localstorage based approach.","mergeCommitSha":"552ed4190a1f462b1670b72bf598d3996e0c8a9d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9774","title":"Remove selected tab index from PR view","createdAt":"2023-12-19T19:27:41Z"}
{"state":"Merged","mergedAt":"2023-12-19T20:51:44Z","number":9776,"body":"Removes expert tutorial tooltip\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/eaa86140-ff5f-4204-80bc-baa0cbce4467\r\n\r\n","mergeCommitSha":"ad9031f75e4a2eb347c55203697977a5e3976dd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9776","title":"Update to remove expert tutorial tooltip","createdAt":"2023-12-19T19:59:56Z"}
{"state":"Merged","mergedAt":"2023-12-19T21:02:06Z","number":9777,"body":"Removes personal profile entry point.\r\n\r\nKeeping UI code for revival in future.\r\n\r\n![CleanShot 2023-12-19 at 12 02 01@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/389ab7c4-517d-45a2-acb8-c8170df5547c)\r\n","mergeCommitSha":"556910fb8453054d8603979ab7756e36d4a3940f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9777","title":"Remove personal profile entry points","createdAt":"2023-12-19T20:03:22Z"}
{"state":"Merged","mergedAt":"2023-12-19T21:38:27Z","number":9778,"mergeCommitSha":"8b9fe4b33ea85af0b247e48409504d3b96e0ed15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9778","title":"Populate topic experts from pipeline","createdAt":"2023-12-19T21:01:57Z"}
{"state":"Merged","mergedAt":"2023-12-19T21:34:51Z","number":9779,"body":"These were sitting uncommitted when I merged my last PR.","mergeCommitSha":"dd9e18f783140c9cf1dfdbe804810a581e27a61e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9779","title":"Update empty states","createdAt":"2023-12-19T21:03:43Z"}
{"state":"Merged","mergedAt":"2022-04-21T23:36:33Z","number":978,"body":"Changes, building on #953:\r\n- just plumbing, no behaviour change yet\r\n- unrelated lint fixes","mergeCommitSha":"14d8473c6a50ba2249ac1d18df6b613c3ce10f22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/978","title":"Update signatures for passing unsaved files to engine","createdAt":"2022-04-21T23:28:00Z"}
{"state":"Merged","mergedAt":"2023-12-19T21:43:47Z","number":9780,"mergeCommitSha":"04c29badf1e110f2e6ae633c6d3aea85ae972f76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9780","title":"Change topic mapping instance type","createdAt":"2023-12-19T21:43:32Z"}
{"state":"Merged","mergedAt":"2023-12-19T22:08:30Z","number":9781,"body":"Changes \r\n\r\n`What tools, technologies, and frameworks do we use at <TEAM>?`\r\n\r\nto \r\n\r\n`What developer tools, technologies, and frameworks do we use at <TEAM>?`","mergeCommitSha":"7e1546835bdb9b4b50c5660cac73251840268cdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9781","title":"Update sample question to bias towards developer-focused documents","createdAt":"2023-12-19T21:49:54Z"}
{"state":"Merged","mergedAt":"2023-12-19T22:14:30Z","number":9782,"body":"Prod rate limit rules are unchanged. This only applies in Dev until we are confident these changes are safe for prod deployment\r\n\r\n- Added support for excluding strings (paths) in a rate limit rule\r\n- Modified the catch all rate limit rule in Dev to exclude logs and pusher. Those paths have a dedicated rule\r\n\r\nThis is to address an edge case where some API calls triggered the catch all rule but user had not yet exhausted their log and pusher quota. The catch all rule ended up blocking all requests including pusher and logs!","mergeCommitSha":"c5ac48bbebd736a3060cd5dbd6c962e00e2ab83d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9782","title":"Add support for excluding paths from rate limit rules","createdAt":"2023-12-19T22:13:21Z"}
{"state":"Merged","mergedAt":"2023-12-19T23:10:57Z","number":9783,"body":"Send the selected snippet and range if we have one, if we don't use the visible snippet and range.","mergeCommitSha":"a06f4a9776f75b8822b3dc538b5dd91b33f422d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9783","title":"Use selected snippet for local context, when it exists","createdAt":"2023-12-19T22:35:55Z"}
{"state":"Merged","mergedAt":"2023-12-19T23:45:23Z","number":9784,"body":"First stab at the 'Ask a Question' sidebar UI.\r\n\r\nThis gets the data in place and tracks and displays selection.  Clicking Submit doesn't do anything yet, and other things are missing (no incognito mode, etc).  The CodeBlock styling needs more work to match the designs.\r\n\r\n![CleanShot 2023-12-19 at 15 15 24@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b9d2bec3-18e7-4dbc-aa94-bc17a51f78ed)\r\n","mergeCommitSha":"3cf1687a45ac7b88fb78350b47dce46b62af1a63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9784","title":"Ask a Question IDE UI","createdAt":"2023-12-19T23:16:47Z"}
{"state":"Merged","mergedAt":"2023-12-20T01:22:50Z","number":9785,"body":"<img width=\"1610\" alt=\"Screenshot 2023-12-19 at 16 58 55\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/dfc9c619-6926-487a-ab5f-c1d83bf29c46\">\r\n","mergeCommitSha":"f1d161cde506d7be1292ccbab223826fc5d36a23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9785","title":"Add demo template page to show and edit details","createdAt":"2023-12-20T00:58:14Z"}
{"state":"Merged","mergedAt":"2023-12-20T01:49:41Z","number":9786,"mergeCommitSha":"c6bce345aee532689b8d288da357c9d101c3d26f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9786","title":"Add email to topic expert propagation","createdAt":"2023-12-20T01:23:26Z"}
{"state":"Merged","mergedAt":"2023-12-20T05:34:44Z","number":9787,"mergeCommitSha":"b52c20e8a5ee38a4e51cdadf987c72c6f3515599","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9787","title":"Tweak \"Bot repeatedly failed to reply to a question\" slack alert message","createdAt":"2023-12-20T05:19:23Z"}
{"state":"Merged","mergedAt":"2023-12-20T08:26:59Z","number":9788,"mergeCommitSha":"b7a4c4dc386e9b73b6fd12c767419d22db37194d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9788","title":"Show parent for Notion page","createdAt":"2023-12-20T08:11:40Z"}
{"state":"Merged","mergedAt":"2023-12-20T15:22:29Z","number":9789,"mergeCommitSha":"4e48a499e00b4e9e4f872d143e56ca413cc4fe28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9789","title":"Exclude bots","createdAt":"2023-12-20T15:10:18Z"}
{"state":"Merged","mergedAt":"2022-04-22T00:17:50Z","number":979,"body":"Quick fix and docs for safari extension\r\n\r\nCurrently not checking in the generated xcodeproj.\r\n\r\n","mergeCommitSha":"1f18c363ba288b5d3d848f21cfc514d596bc89ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/979","title":"Safari web extension","createdAt":"2022-04-22T00:02:26Z"}
{"state":"Merged","mergedAt":"2023-12-20T16:59:29Z","number":9790,"mergeCommitSha":"73c3055f31120cb8b8f7072cc2cea42ac50b9415","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9790","title":"Remove dead topic fields","createdAt":"2023-12-20T16:36:18Z"}
{"state":"Merged","mergedAt":"2023-12-20T17:41:23Z","number":9791,"mergeCommitSha":"43dd32ec0aa0f64ac23af4e103bc261f516f4835","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9791","title":"Wrap whole function execution service in try/catch","createdAt":"2023-12-20T17:27:40Z"}
{"state":"Merged","mergedAt":"2022-01-21T22:26:02Z","number":98,"body":"Similar to Jeff's PR but leverages hoplite config for env specific cors. \r\n\r\n@rasharab I tried the following to get reference substitution working, which didn't work:\r\n\r\n```js\r\napiservice {\r\n    hostName: localhost\r\n    port: 1234\r\n    schemes: [http, https]\r\n}\r\n\r\ncors {\r\n    hosts: [\r\n        ${?apiservice}\r\n    ]\r\n}\r\n```\r\n\r\nAnd several other variants like \r\n```js\r\napiservice = {\r\n ...\r\n}\r\n\r\ncors {\r\n    hosts: [\r\n        ${?apiservice}\r\n    ]\r\n}\r\n```\r\n\r\netc\r\n","mergeCommitSha":"0fc213fe591bcba3e99bf93619731e20697c3ba6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/98","title":"Add cors config","createdAt":"2022-01-21T19:09:42Z"}
{"state":"Merged","mergedAt":"2022-04-22T00:12:25Z","number":980,"body":"We were using `Git.getCurrentRepo`, which doesn't work correctly in multi-repo workspaces (it picks a random repo).  So randomly we couldn't open the correct file for a thread in the Unblocked repo.\r\n\r\nThis is the worst offender, but there are still a couple places using `Git.getCurrentRepo`, I'll look into removing the rest in a follow-on PR.","mergeCommitSha":"91b092986ba80957add39bca3185c9e295eaa292","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/980","title":"VSCode thread view: open file on correct repo","createdAt":"2022-04-22T00:05:47Z"}
{"state":"Merged","mergedAt":"2023-12-20T18:26:49Z","number":9807,"mergeCommitSha":"d18e64ca4f4688fe4cbf03bbb26b1765a25a5e7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9807","title":"Log ML Functions prompt and response","createdAt":"2023-12-20T18:19:58Z"}
{"state":"Merged","mergedAt":"2023-12-20T18:38:03Z","number":9808,"mergeCommitSha":"04aa48babde727dbe8d4dc1e894290ea099e60c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9808","title":"[SKIP TESTS] Add ability to get a specific Notion block from the admin console","createdAt":"2023-12-20T18:31:12Z"}
{"state":"Merged","mergedAt":"2023-12-20T19:42:13Z","number":9809,"body":"Makes client spinner logic easier.\n\nMotivation\nhttps://chapter2global.slack.com/archives/C02US6PHTHR/p1703099087800119","mergeCommitSha":"a7596993bad921b89b27bf92f47a3fde735194e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9809","title":"Empty repos are treated as completed processing","createdAt":"2023-12-20T19:23:25Z"}
{"state":"Merged","mergedAt":"2022-04-22T18:31:43Z","number":981,"body":"This all runs in the API service. Once we have encrypted user secrets and a message queue, we can move this to the SCM service.","mergeCommitSha":"a6c9ae5b9447ae69d9820fe93a5b509cd988a6a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/981","title":"Create, update, and delete messages in GitHub from unblocked","createdAt":"2022-04-22T00:35:30Z"}
{"state":"Merged","mergedAt":"2023-12-20T19:51:13Z","number":9810,"mergeCommitSha":"189900eda78f8155bb8102246cab9cc8872ac5d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9810","title":"Fix message ordering","createdAt":"2023-12-20T19:28:33Z"}
{"state":"Merged","mergedAt":"2023-12-20T22:14:30Z","number":9811,"mergeCommitSha":"e7fcb85d8f0cc22a97754f79f57e5c9dfb670e8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9811","title":"Add incognito mode button to Ask a Question UI","createdAt":"2023-12-20T19:52:58Z"}
{"state":"Merged","mergedAt":"2023-12-20T22:25:16Z","number":9812,"body":"The search API does not guarantee it'll return all shared documents, so let's ingest any child pages that we come across while ingesting a document.","mergeCommitSha":"82d93687ff5f851411c75a5777737bf3eee31bf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9812","title":"Ingest Notion child pages","createdAt":"2023-12-20T19:53:32Z"}
{"state":"Merged","mergedAt":"2023-12-20T20:32:51Z","number":9813,"mergeCommitSha":"aff30f9b522db4bc8e8dcdb5070f745af22355c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9813","title":"allow the ec2 start script to have write permission on actions","createdAt":"2023-12-20T20:16:07Z"}
{"state":"Merged","mergedAt":"2023-12-20T20:48:27Z","number":9814,"body":"This reverts commit 1945de51861e6f44c9249d0ac7827163eef73037.\r\n","mergeCommitSha":"69ad84f13173af488c9b1dab8a743eb95cd652a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9814","title":"[SKIP TESTS] Revert \"fix(deps): update dependency io.pinecone:pinecone-client to v0.7.1 (#9798)\"","createdAt":"2023-12-20T20:48:21Z"}
{"state":"Merged","mergedAt":"2023-12-20T21:08:30Z","number":9815,"mergeCommitSha":"99cd36bcd41e1a8b975eeebd16ed824f28e59430","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9815","title":"Fix ML Invocation serialization","createdAt":"2023-12-20T20:48:41Z"}
{"state":"Merged","mergedAt":"2023-12-20T20:52:58Z","number":9816,"mergeCommitSha":"22385f9001717af4eda55f2ed7058622fd3f0b91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9816","title":"Disable repo deletes","createdAt":"2023-12-20T20:52:32Z"}
{"state":"Merged","mergedAt":"2023-12-20T21:03:45Z","number":9817,"body":"This reverts commit 22385f9001717af4eda55f2ed7058622fd3f0b91.","mergeCommitSha":"d5dccffc4a7361e86091f10dd80b3af98f09ab9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9817","title":"Revert \"[SKIP TESTS] Disable repo deletes (#9816)\"","createdAt":"2023-12-20T21:03:23Z"}
{"state":"Merged","mergedAt":"2023-12-20T22:47:04Z","number":9818,"body":"* `openThread` command now has an optional UI target property, specifying if the thread should be opened in the sidebar or editor\r\n* ClientWorkspaceActions are now executed through a PromiseProxy, instead of a webview command -- this allows surrounding code to `await` and ensure it succeeded, or chain work together.\r\n* Implement `askQuestion` action for the IDEs","mergeCommitSha":"4b7408599a216a8ff50b50520e764a9da6130c40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9818","title":"Hook up Ask a Question in sidebar","createdAt":"2023-12-20T22:17:22Z"}
{"state":"Merged","mergedAt":"2024-01-03T21:29:03Z","number":9819,"body":"Still exists duplicate code within IDE specific commands / webview providers. Those will be cleaned up once we fully migrate over to new sidebar.","mergeCommitSha":"f283a13f92262f2dc335b78498d8436e47d3e48c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9819","title":"Integrate PRView into MainSidebar","createdAt":"2023-12-20T23:08:30Z"}
{"state":"Merged","mergedAt":"2022-04-22T05:38:53Z","number":982,"body":"It works.","mergeCommitSha":"228dbbe9656833c136c9a8ee6421a8731117a231","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/982","title":"Recalculate unsaved files","createdAt":"2022-04-22T03:36:02Z"}
{"state":"Merged","mergedAt":"2023-12-20T23:39:45Z","number":9820,"mergeCommitSha":"2157fd591c7d5d954d843838848703b9a089f5f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9820","title":"Add repo focus function","createdAt":"2023-12-20T23:14:34Z"}
{"state":"Merged","mergedAt":"2023-12-20T23:53:37Z","number":9821,"mergeCommitSha":"49de7e0fdfc1627d3c0d868afc398a2ff04cc628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9821","title":"Add team member and prs functions","createdAt":"2023-12-20T23:39:04Z"}
{"state":"Merged","mergedAt":"2023-12-21T00:24:14Z","number":9822,"body":"TODO\r\n- [ ] must run DEV migration to cleanup schema after deploy\r\n- [ ] must run PROD migration to cleanup schema after deploy","mergeCommitSha":"073b046d8a6bd198992921704d5a6568353fe978","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9822","title":"Introduce demo parent teams that are responsible for shared demo resources","createdAt":"2023-12-21T00:01:54Z"}
{"state":"Merged","mergedAt":"2023-12-21T00:55:11Z","number":9823,"mergeCommitSha":"3cf8726981c5e610efea524b070be44531d2f075","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9823","title":"Show link to page properties for block","createdAt":"2023-12-21T00:04:13Z"}
{"state":"Merged","mergedAt":"2023-12-21T07:05:04Z","number":9824,"mergeCommitSha":"28b44dc9cee6dcdc77038ecec7e8925655b25a17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9824","title":"Open PRs without PR comments are now returned again","createdAt":"2023-12-21T01:02:18Z"}
{"state":"Merged","mergedAt":"2023-12-21T03:12:37Z","number":9827,"body":"- Increase test heap size\r\n- Incerase heap size\r\n- Update\r\n","mergeCommitSha":"f563d872bd9557082d2f685d4ba970b912a78ebd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9827","title":"IncreaseHeapSize","createdAt":"2023-12-21T03:12:28Z"}
{"state":"Merged","mergedAt":"2023-12-21T04:11:57Z","number":9829,"mergeCommitSha":"0d75536f6c9494278bb521292a3a4d18d25148bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9829","title":"Renovate should ignore text fixtures","createdAt":"2023-12-21T04:11:50Z"}
{"state":"Merged","mergedAt":"2022-04-22T18:32:24Z","number":983,"body":"Ideally, this will be simplified if I can move everything within the webview.\r\nRight now, this separation of authorized api calls between extension and webview is annoying as fuck for vscode.","mergeCommitSha":"8f2900c462f6531948300c873fdb06715b43fe26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/983","title":"Add assets to knowledge","createdAt":"2022-04-22T16:24:07Z"}
{"state":"Merged","mergedAt":"2023-12-21T05:47:37Z","number":9832,"body":"Reverts NextChapterSoftware/unblocked#9806","mergeCommitSha":"5198129ce3444567560e3b2e4cf1200d81e98c83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9832","title":"Revert \"chore(deps): update plugin org.jmailen.kotlinter to v4\"","createdAt":"2023-12-21T05:24:23Z"}
{"state":"Merged","mergedAt":"2023-12-21T06:35:44Z","number":9833,"mergeCommitSha":"aa2fb2e2d9299c63d88e23238de850bc7d60001a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9833","title":"Clean up code for ignoreing Drata UAT notion pages","createdAt":"2023-12-21T05:48:14Z"}
{"state":"Merged","mergedAt":"2023-12-21T06:58:47Z","number":9834,"body":"Support for Git cloning Demo teams, even though they do not have api access credentials.","mergeCommitSha":"535dd690242b25524cd41e2be684f10cd1377cc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9834","title":"Account for Demo teams that have no credentials","createdAt":"2023-12-21T06:01:59Z"}
{"state":"Merged","mergedAt":"2023-12-21T06:47:35Z","number":9835,"mergeCommitSha":"db686aa877c1aa42c88d4b2e4464cd487695746f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9835","title":"chore(deps): update plugin org.jmailen.kotlinter to v4 (#9806)\" (#9832)","createdAt":"2023-12-21T06:23:33Z"}
{"state":"Merged","mergedAt":"2023-12-21T07:59:36Z","number":9836,"mergeCommitSha":"3039d9b35565b9a0a1e9c76afe88f74b617d29cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9836","title":"Skip ingesting a notion page if it hasn't been edited since last ingestion (unless that was > a day ago)","createdAt":"2023-12-21T07:37:02Z"}
{"state":"Merged","mergedAt":"2023-12-21T08:12:26Z","number":9837,"mergeCommitSha":"d4f501e88e2dbefeb042fe4c73249312d96bca16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9837","title":"Render demo websites","createdAt":"2023-12-21T07:49:39Z"}
{"state":"Merged","mergedAt":"2023-12-21T18:08:25Z","number":9838,"mergeCommitSha":"1e51bb46f6b1eb459384f89ad80a1c9aa7f1e0a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9838","title":"Don't uninstall demo teams","createdAt":"2023-12-21T17:47:53Z"}
{"state":"Merged","mergedAt":"2023-12-22T05:59:18Z","number":9839,"mergeCommitSha":"17fb4c37ee0ef57f23ee6a5c63bf8d1bf5a461c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9839","title":"Ingest pages and databases linked from Notion pages","createdAt":"2023-12-22T05:25:08Z"}
{"state":"Merged","mergedAt":"2023-12-22T17:58:29Z","number":9840,"mergeCommitSha":"61f86e1229044428b6dee075b719854482871b83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9840","title":"[SKIP TESTS] Add debug","createdAt":"2023-12-22T17:58:15Z"}
{"state":"Merged","mergedAt":"2023-12-23T00:03:13Z","number":9841,"mergeCommitSha":"c7fe55b24bda34417838eab6f998ef6c0c62039b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9841","title":"Rebuild topic pipeline code","createdAt":"2023-12-23T00:02:17Z"}
{"state":"Merged","mergedAt":"2023-12-27T21:44:29Z","number":9848,"mergeCommitSha":"9139b86c18e5ca815f4cd8e14d31d8a582357769","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9848","title":"Demo team activity is not included in growth metrics","createdAt":"2023-12-27T20:48:54Z"}
{"state":"Merged","mergedAt":"2023-12-27T22:08:33Z","number":9849,"mergeCommitSha":"1de795a198b1cf8128795e023c3235ac30c057bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9849","title":"Do not show demo teams in the teams page","createdAt":"2023-12-27T20:48:58Z"}
{"state":"Merged","mergedAt":"2022-04-22T19:21:27Z","number":985,"body":"* For some reason setting the badge to undefined doesn't seem to remove the badge anymore -- manually setting the badge to 0 and an empty string tooltip seems to result in the same desired behaviour","mergeCommitSha":"57b24a8e644cca0ce76e13d4382a4e3c0529732e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/985","title":"Fix badge clearing","createdAt":"2022-04-22T19:14:04Z"}
{"state":"Merged","mergedAt":"2023-12-29T19:31:59Z","number":9850,"mergeCommitSha":"1be95b5d975a13d2ed0f438f7c4270cd354bbf02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9850","title":"Placeholder for demo teams pages","createdAt":"2023-12-29T19:21:11Z"}
{"state":"Merged","mergedAt":"2023-12-29T20:19:33Z","number":9852,"mergeCommitSha":"2e1d08a5eef67eae01964b4f1de859d9d6b9b457","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9852","title":"Conditional build ingestion and disable in local stack","createdAt":"2023-12-29T19:29:17Z"}
{"state":"Merged","mergedAt":"2023-12-30T05:56:37Z","number":9853,"mergeCommitSha":"913f7858d1a7b213e3ffe2532fab95e65cf44ed9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9853","title":"Set preserveKeywordCasing to silence Exposed warnings","createdAt":"2023-12-30T04:28:19Z"}
{"state":"Merged","mergedAt":"2023-12-31T02:47:33Z","number":9854,"body":"Responsible for demo team lifecycle management.","mergeCommitSha":"a39baa5d375e288ccad14dcccbb604657956891a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9854","title":"Introduce demo team service","createdAt":"2023-12-30T04:28:23Z"}
{"state":"Merged","mergedAt":"2023-12-31T05:37:33Z","number":9855,"body":"- Ability to list/get demo templates.\n- Ability to list/get/create/delete demo teams.","mergeCommitSha":"87d7f219d84d97a86ed80ee4173d459019d4eadf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9855","title":"Introduce Demo APIs","createdAt":"2023-12-31T04:44:23Z"}
{"state":"Merged","mergedAt":"2023-12-31T06:05:25Z","number":9856,"body":"- deletes team\n- deletes all content\n- uninstalls github app for org (if github)","mergeCommitSha":"107ccd3e3e2c64662c189d58e3a3f09c670d35f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9856","title":"Delete team API","createdAt":"2023-12-31T05:41:29Z"}
{"state":"Merged","mergedAt":"2023-12-31T06:49:34Z","number":9857,"mergeCommitSha":"df51e3c9d1d460b181bf76a5d37e4bb7a73f5e80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9857","title":"Demo template deletion should remove embeddings","createdAt":"2023-12-31T05:41:32Z"}
{"state":"Merged","mergedAt":"2023-12-31T20:02:43Z","number":9858,"body":"Use separate APIs to retrieve Demo teams and Demo templates:\n- listDemoTemplates\n- listDemoTeams\n- getDemoTemplate\n- getDemoTeam","mergeCommitSha":"e438fe296b7a19418f9f128461bafab3579e5f85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9858","title":"Teams APIs should not return Demo or DemoSharedTemplate teams","createdAt":"2023-12-31T19:49:26Z"}
{"state":"Merged","mergedAt":"2023-12-31T21:10:46Z","number":9859,"body":"API break is to change `createDemoTeam` from 200 to 201. No impact because API is not used and not released.","mergeCommitSha":"529d441d4f3e46b4744d31bb8e818d206d864645","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9859","title":"[BREAKS API ON MAIN] Demo team API integration tests","createdAt":"2023-12-31T20:47:33Z"}
{"state":"Merged","mergedAt":"2022-04-22T21:07:34Z","number":986,"mergeCommitSha":"a9b9f0e2a9dda2d2617ccf78e976d47eab62b4e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/986","title":"Updates Agora submodule URL to use public https endpoint","createdAt":"2022-04-22T20:59:39Z"}
{"state":"Merged","mergedAt":"2024-01-02T16:48:19Z","number":9860,"mergeCommitSha":"00711219682536c6ecdca8d37233d1e64f36b876","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9860","title":"Reingest web sites immediately if a customer updates the configuration","createdAt":"2024-01-02T00:52:17Z"}
{"state":"Merged","mergedAt":"2024-01-02T17:15:07Z","number":9861,"mergeCommitSha":"c76103c8dabd66b9387a42655ddfb41164940968","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9861","title":"404 when scraping is not an error","createdAt":"2024-01-02T17:04:09Z"}
{"state":"Merged","mergedAt":"2024-01-02T18:45:20Z","number":9862,"body":"* If the Show Unreads Only filter is on, we show the first page of fetched threads with client-side filtering for unreads. The bug is that we only compared the fetched (pre-filtered) page with the MAX_FETCHED page size; this can cause the UI to look like it's hanging with infinite scrolling:\r\n<img width=\"789\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/56da4709-f937-41b0-91d2-27e5e99da0d0\">\r\n\r\n* This is a quick solution where we separately track whether there are more unreads to fetch by keeping track of the filtered unreads size relative to the number of pages fetched. A better solution may involve writing a separate stream for unreads or integrating these values into the API somehow(?) but this seems to work for now \r\n","mergeCommitSha":"7000c4dc6bea0e498d3cccc718e52ab2210f0d07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9862","title":"Fix load more unreads ","createdAt":"2024-01-02T18:12:04Z"}
{"state":"Merged","mergedAt":"2024-01-02T18:34:02Z","number":9863,"mergeCommitSha":"37c45484806d4458408d0b5028a15e7beb1d1182","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9863","title":"Reduce logs generated for Notion request timeout","createdAt":"2024-01-02T18:25:05Z"}
{"state":"Merged","mergedAt":"2024-01-02T19:13:59Z","number":9864,"mergeCommitSha":"007fb9d51042a10444ba377d029354117ccba2fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9864","title":"Fix IntelliJ IDE settings for pinecone-utils","createdAt":"2024-01-02T19:11:19Z"}
{"state":"Merged","mergedAt":"2024-01-03T06:31:28Z","number":9865,"body":"I am increasing the max scale to deal with the annoying max Kube pod scale alarm. I'll probably have to do this in multiple steps to avoid stressing the queue. \r\n\r\nDepending on how this goes we might also end up scaling up the queue. ","mergeCommitSha":"5d28f4ae21f32b8eb382a60b1641b71cf52c84ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9865","title":"Increasing max allowed scale for topic service to 10","createdAt":"2024-01-02T19:24:21Z"}
{"state":"Closed","mergedAt":null,"number":9866,"mergeCommitSha":"846eaf2f97533dc3b2e2b549d2772e56a70179e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9866","title":"Use new Pinecone index","createdAt":"2024-01-02T19:41:24Z"}
{"state":"Merged","mergedAt":"2024-01-02T21:40:58Z","number":9867,"mergeCommitSha":"601d4f1593b1cd94d7d2a990de2e92d61f12a286","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9867","title":"Expose 'demo' team mode and add TryUnblocked feature flag","createdAt":"2024-01-02T20:12:17Z"}
{"state":"Merged","mergedAt":"2024-01-02T21:17:19Z","number":9868,"mergeCommitSha":"439251482af71b6e5b5009aab29cefeb2addacfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9868","title":"try-unblocked added","createdAt":"2024-01-02T20:51:22Z"}
{"state":"Merged","mergedAt":"2024-01-02T21:09:17Z","number":9869,"mergeCommitSha":"574ffd30bd24a73521a9a84eee7f532b78b86fdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9869","title":"Trigger web reingest only after committing site updates","createdAt":"2024-01-02T20:58:25Z"}
{"state":"Merged","mergedAt":"2022-04-26T00:07:31Z","number":987,"body":"## Summary\r\n\r\nImplements views for threads and teams. The list is not implemented as a real `List` because I was struggling with styling, meaning if the list is very long it may impact performance. There is a max size to the popover height, and I had to implement a nasty preference hack to coerce the `ScrollView` to resize itself when its content height changes.\r\n\r\nThere's quite a bit of cleanup in this one too","mergeCommitSha":"b365ab4ac507f8d22cbb0e7048e9e8b908b810d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/987","title":"Add threads and team member UX to Hub App","createdAt":"2022-04-22T22:19:35Z"}
{"state":"Merged","mergedAt":"2024-01-02T22:34:02Z","number":9870,"mergeCommitSha":"d5651b7fedd40ee77945fca4bd970ffab455a9a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9870","title":"Ability to publish/withdraw demo templates and only return published templates in API","createdAt":"2024-01-02T21:10:18Z"}
{"state":"Merged","mergedAt":"2024-01-02T22:04:56Z","number":9871,"mergeCommitSha":"dd3322f254b72c353f024a8ab0a28d385d7eba2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9871","title":"Remove unnecessary launching of coroutine","createdAt":"2024-01-02T21:46:03Z"}
{"state":"Merged","mergedAt":"2024-01-02T22:08:45Z","number":9872,"body":"Add failure reasons to regression test results\n\nlint fixes","mergeCommitSha":"433cbe1fe1c40a5c32414490bcd95beb5628ee4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9872","title":"Add failure reasons to regression test results","createdAt":"2024-01-02T21:48:52Z"}
{"state":"Merged","mergedAt":"2024-01-02T22:59:01Z","number":9873,"mergeCommitSha":"befe35fd4bd0993eb71d7a3514fe5efba192a8d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9873","title":"View demo team instance counts by template on demo template admin page","createdAt":"2024-01-02T22:09:20Z"}
{"state":"Merged","mergedAt":"2024-01-02T23:24:38Z","number":9874,"mergeCommitSha":"439809a98bab56e7e19aece0d5612d2d8adb9c94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9874","title":"Show demo teams in admin with owner","createdAt":"2024-01-02T22:40:55Z"}
{"state":"Merged","mergedAt":"2024-01-02T23:03:49Z","number":9875,"mergeCommitSha":"e98f35cbfcad326ab4f6199ccb9ee5e2f1a03723","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9875","title":"Improve cell colouring for regression tests","createdAt":"2024-01-02T22:43:29Z"}
{"state":"Merged","mergedAt":"2024-01-02T23:37:14Z","number":9876,"mergeCommitSha":"de066a63191fa682c13eefeaa950e1cca79ca914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9876","title":"Fix duplicate regression failure messages","createdAt":"2024-01-02T23:24:12Z"}
{"state":"Merged","mergedAt":"2024-01-03T19:14:28Z","number":9877,"body":"- Pin the \"reply\" MessageEditor to the bottom of the DiscussionThread UI.  This gives a much better UX for QA threads.  The designs have had this for a long time, we just never implemented it in the IDEs.\r\n- Update the MessageEditor CSS in the dashboard and VSCode to match updated designs.  The MessageEditor has a slight \"halo\" UI now.\r\n![CleanShot 2024-01-02 at 15 29 03@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/108986ea-28a6-400e-8fce-9132d842d257)\r\n![CleanShot 2024-01-02 at 15 29 16@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/109737d7-1eb0-4cc7-9e6b-8d651ce1ade1)\r\n","mergeCommitSha":"63de943b28d0cd0a150f0b7cfdbd36e7b3acce67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9877","title":"Update \"reply\" message input","createdAt":"2024-01-02T23:25:46Z"}
{"state":"Merged","mergedAt":"2024-01-03T02:23:15Z","number":9878,"mergeCommitSha":"e2a72f7f6a90d3fea84ae7aed6c069ad489847a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9878","title":"Demo team questions use demo template embeddings","createdAt":"2024-01-02T23:29:17Z"}
{"state":"Merged","mergedAt":"2024-01-02T23:44:59Z","number":9879,"mergeCommitSha":"4f761f7bf73d8a5df9f4a94d9f6f76a3f45fc08b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9879","title":"[SKIP TESTS] Fix instance counts in demo template page","createdAt":"2024-01-02T23:35:12Z"}
{"state":"Merged","mergedAt":"2022-04-27T00:05:49Z","number":988,"body":"Current cache used in web extension may not handle API schema updates gracefully.\r\n\r\nAdded a git commit versioning identifier within the cache to trigger dropping cache.\r\n","mergeCommitSha":"27aed173f311b71c53075e200e6e0da700009e38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/988","title":"Setup Expiring extension cache","createdAt":"2022-04-22T22:45:00Z"}
{"state":"Merged","mergedAt":"2024-01-02T23:53:19Z","number":9880,"mergeCommitSha":"4852d7c166f2ed3d87a306a891e9d99450dd613f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9880","title":"Fix Slack text for regression announcements","createdAt":"2024-01-02T23:40:09Z"}
{"state":"Merged","mergedAt":"2024-01-02T23:58:27Z","number":9881,"mergeCommitSha":"ce6a6dea63d3b2b64cfe89422aed83c6c05f93b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9881","title":"[SKIP TESTS] Admin route to show demo teams for a particular template","createdAt":"2024-01-02T23:47:34Z"}
{"state":"Merged","mergedAt":"2024-01-03T00:27:42Z","number":9882,"mergeCommitSha":"5b4a1a330ecd5bf7928fd119a36c8173d4066b48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9882","title":"Open new QA threads in VSCode editor, if the sidebar feature flag is unset","createdAt":"2024-01-03T00:15:15Z"}
{"state":"Merged","mergedAt":"2024-01-03T00:42:19Z","number":9883,"mergeCommitSha":"b5cece99bd296c8b0a6d81e19ab4212711e96a2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9883","title":"[SKIP TESTS] Show related demo teams on demo template page","createdAt":"2024-01-03T00:31:31Z"}
{"state":"Merged","mergedAt":"2024-01-03T00:59:07Z","number":9884,"mergeCommitSha":"f76b5e6fce432bc169110ef707b076979151a56b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9884","title":"Add titles to open PR list","createdAt":"2024-01-03T00:46:16Z"}
{"state":"Merged","mergedAt":"2024-01-03T02:48:49Z","number":9885,"mergeCommitSha":"7be671075c9b2b85fbe153aef1f9e465ba9c851c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9885","title":"Validate the site exists in postWebIngestionValidateUrl","createdAt":"2024-01-03T01:01:51Z"}
{"state":"Merged","mergedAt":"2024-01-03T02:48:01Z","number":9886,"mergeCommitSha":"c3fecd829bff98ccd7624579b84dba730f2bab92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9886","title":"[SKIP TESTS] Assert that demo teams are always enabled when created","createdAt":"2024-01-03T02:37:01Z"}
{"state":"Merged","mergedAt":"2024-01-03T03:01:24Z","number":9887,"mergeCommitSha":"7dc1c9a479aab70ac6cffc464e42b04684acebb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9887","title":"[SKIP TESTS] Render demo alerts on team pages","createdAt":"2024-01-03T02:52:34Z"}
{"state":"Merged","mergedAt":"2024-01-03T03:29:37Z","number":9888,"mergeCommitSha":"b11a9878f1cb0462e368a37755a55c819c544b84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9888","title":"Cleanup legacy provider role values","createdAt":"2024-01-03T03:06:35Z"}
{"state":"Merged","mergedAt":"2024-01-03T17:30:01Z","number":9889,"body":"the previous prompt classified @aliases as bot messages","mergeCommitSha":"2aa4606f700da023bf72d8a8fa34d6809baf6171","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9889","title":"fixes some false positives","createdAt":"2024-01-03T05:22:04Z"}
{"state":"Merged","mergedAt":"2022-04-25T16:13:57Z","number":989,"body":"Added basic test to validate.","mergeCommitSha":"4ac5c7c239c387cacfa498fcf3ceee63dbedc83d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/989","title":"Add cookie auth to edge lambdas","createdAt":"2022-04-23T23:10:24Z"}
{"state":"Merged","mergedAt":"2024-01-04T00:15:28Z","number":9890,"body":"When the `FeatureNewIDESidebar` feature flag is enabled:\r\n- Remove the tabbed explorer insights UI, only display the discussions UI\r\n- Remove the search input, replace it with a button to link to the new 'Ask a Question' UI\r\n\r\n![CleanShot 2024-01-02 at 21 21 18@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/26b247d4-0d41-4342-b1a4-b2c9a3a685e0)\r\n","mergeCommitSha":"34e5e69194754c0c4e2d3400c9bbcec2eab1b816","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9890","title":"Update explorer insights UI to link to the new 'Ask a Question' UI","createdAt":"2024-01-03T05:23:45Z"}
{"state":"Merged","mergedAt":"2024-01-03T16:03:20Z","number":9891,"mergeCommitSha":"9dee90f026216c261414cc5360c79c2f3aee13dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9891","title":"Warn if repo-related documents are filtered from semantic search","createdAt":"2024-01-03T15:49:34Z"}
{"state":"Merged","mergedAt":"2024-01-03T18:30:58Z","number":9892,"mergeCommitSha":"64fb5ece86a7ca386ef27e61ad0afc7ac5d91c03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9892","title":"scale topic service back down to 5","createdAt":"2024-01-03T18:21:52Z"}
{"state":"Merged","mergedAt":"2024-01-03T22:32:13Z","number":9893,"body":"Updating the empty state button labels:\r\n\r\n\"Ask a New Question\" to \"Ask a Question\"\r\n\r\n","mergeCommitSha":"89585c0d08339c4c8fc61630396e81776f4cf466","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9893","title":"Update \"ask a question\" button label for empty state","createdAt":"2024-01-03T18:39:07Z"}
{"state":"Merged","mergedAt":"2024-01-03T19:12:57Z","number":9894,"mergeCommitSha":"735a9bf06384265398eaf91bfe907dc02dc56443","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9894","title":"[SKIP TESTS] Stop generting topic events","createdAt":"2024-01-03T18:50:08Z"}
{"state":"Merged","mergedAt":"2024-01-03T18:56:08Z","number":9895,"mergeCommitSha":"9325dd757caa036a2a423472539b334c771b8d3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9895","title":"[SKIP TESTS] scale topic service to zero","createdAt":"2024-01-03T18:54:59Z"}
{"state":"Merged","mergedAt":"2024-01-03T23:03:25Z","number":9896,"body":"Removes all code related to creating notes.","mergeCommitSha":"39fd96a0f1bcde7c6c1179910d807eb137920a35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9896","title":"Remove all createInsight related code","createdAt":"2024-01-03T19:06:23Z"}
{"state":"Closed","mergedAt":null,"number":9897,"mergeCommitSha":"259f8d1db15889a69b4511a78f93f18ee0b118aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9897","title":"Temporarily prevent RepoTopicIngestionService from creating events","createdAt":"2024-01-03T19:17:59Z"}
{"state":"Merged","mergedAt":"2024-01-03T19:48:04Z","number":9898,"body":"Add regression test logging to debug slack notifications\n\nsmall refactor","mergeCommitSha":"86be0637d31578344d56aafd5cf2c8f3a8d09e1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9898","title":"Add regression test logging to debug slack notifications","createdAt":"2024-01-03T19:34:18Z"}
{"state":"Merged","mergedAt":"2024-01-04T22:22:18Z","number":9899,"body":"Some code that wasn't actually being used:\r\n\r\n- MessageEditor toolbar UI and theming\r\n- IDE Discussion & PR view participant sections","mergeCommitSha":"39ec3647c62cfa68da6acaec6a72699bf6877e6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9899","title":"Remove dead code","createdAt":"2024-01-03T19:48:10Z"}
{"state":"Merged","mergedAt":"2022-01-21T19:43:11Z","number":99,"body":"- Re-created RDS cluster so it would create a default db named \"maindb\"\r\n- Updated service account role in EKS\r\n\r\nThese changes have been deployed ","mergeCommitSha":"5c1d702f807e75c5fe3578dc49120d3ee487ccc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/99","title":"Change RDS to create a default `mind`","createdAt":"2022-01-21T19:33:43Z"}
{"state":"Merged","mergedAt":"2022-04-26T05:30:07Z","number":990,"body":"This will be used when posting messages to GitHub from unblocked.","mergeCommitSha":"2e9017bee807363518b13479a9f2376087402e50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/990","title":"Convert unblocked messages to markdown","createdAt":"2022-04-25T08:24:38Z"}
{"state":"Merged","mergedAt":"2024-01-03T21:13:12Z","number":9900,"mergeCommitSha":"db5b46dce4e399fdfaabc7acff178aa0a3329b68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9900","title":"Use bot teammember for semantic search page requests","createdAt":"2024-01-03T20:59:50Z"}
{"state":"Merged","mergedAt":"2024-01-03T21:19:12Z","number":9901,"mergeCommitSha":"7641b5a2fc31b18c3799f8f219f632785199e697","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9901","title":"Fix regression announcements to slack","createdAt":"2024-01-03T21:06:50Z"}
{"state":"Merged","mergedAt":"2024-01-03T21:45:57Z","number":9902,"mergeCommitSha":"6340e63661c0c2d904322ad1324ccd92df8a3689","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9902","title":"Only trigger immediate web ingestion for added sites","createdAt":"2024-01-03T21:23:14Z"}
{"state":"Merged","mergedAt":"2024-01-03T22:45:59Z","number":9903,"mergeCommitSha":"6d9a09ec1d1a75db9a94cafc9743481b362b36d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9903","title":"Validate URL when configuring web site for ingestion","createdAt":"2024-01-03T22:30:36Z"}
{"state":"Merged","mergedAt":"2024-01-05T19:32:00Z","number":9904,"body":"![image](https://github.com/NextChapterSoftware/unblocked/assets/13431372/9b42de01-2da2-4b83-80f0-4abc42db4628)\r\n\r\n<img width=\"1482\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/5d7d89c0-b9ee-4a87-a03c-b97b547d977f\">\r\n<img width=\"934\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/58c2b759-1f80-4666-9464-a88008f0e596\">\r\n\r\n* Scoped to feature flag; turned on only for my user\r\n* Right now the rows don't do anything, will integrate and build Demo Team UI in next PR","mergeCommitSha":"8b48471491b7eb50d7eb2cdec4dc5e7a4013d1b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9904","title":"Add try unblocked dialog entry points","createdAt":"2024-01-03T22:45:25Z"}
{"state":"Merged","mergedAt":"2024-01-04T01:21:09Z","number":9905,"mergeCommitSha":"33a2c0ee4fd35e90b1bb45bd42344e0d3f46e0a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9905","title":"Fix false positives","createdAt":"2024-01-03T23:54:59Z"}
{"state":"Closed","mergedAt":null,"number":9906,"mergeCommitSha":"3f8a64a612487d14784fdfe48ac532ec8e23a9db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9906","title":"[SKIP TESTS] Fix jetbrain workflow permission for action runner workflow","createdAt":"2024-01-04T00:22:40Z"}
{"state":"Merged","mergedAt":"2024-01-04T00:43:46Z","number":9907,"body":"Merge if test job run succeeds:\r\nhttps://github.com/NextChapterSoftware/unblocked/actions/runs/7404015338","mergeCommitSha":"fef1a77edf67a94e5b4c5ffc9d5b9302553db47b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9907","title":"CI installer needs action:write permission passed to ci-jetbrains job","createdAt":"2024-01-04T00:30:47Z"}
{"state":"Merged","mergedAt":"2024-01-04T17:01:51Z","number":9908,"mergeCommitSha":"814c1252301fb1eea4df3e0179d2635e96480117","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9908","title":"Schedule retry for scraping a page if we get a 429","createdAt":"2024-01-04T00:55:56Z"}
{"state":"Merged","mergedAt":"2024-01-04T01:55:23Z","number":9909,"mergeCommitSha":"79bf8f898aff24e35ec589ddaab6a37a3108b1cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9909","title":"Data table filtering and client-side pagination","createdAt":"2024-01-04T01:44:46Z"}
{"state":"Merged","mergedAt":"2022-04-25T22:10:16Z","number":991,"body":"Update docs for chrome extension.","mergeCommitSha":"dd8a289c6d9b2560bb7dcb42e38a552e9c6f376c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/991","title":"Update web-extension README","createdAt":"2022-04-25T16:13:54Z"}
{"state":"Merged","mergedAt":"2024-01-05T02:26:48Z","number":9910,"mergeCommitSha":"66e2952eac5f9a81ec6aefeffb50c3f3ad4c3372","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9910","title":"LLMContentFilter: adds inference timing","createdAt":"2024-01-04T02:08:32Z"}
{"state":"Merged","mergedAt":"2024-01-04T03:30:14Z","number":9911,"mergeCommitSha":"0b7e45dc437c6574596cbfaeb9d631258a0d9592","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9911","title":"Pagination tweaks in admin web","createdAt":"2024-01-04T03:20:48Z"}
{"state":"Merged","mergedAt":"2024-01-04T05:22:13Z","number":9912,"body":"Faster query.","mergeCommitSha":"92354975b6a589719a7304ecf315c77be7bc533f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9912","title":"Optimize people page","createdAt":"2024-01-04T05:06:52Z"}
{"state":"Merged","mergedAt":"2024-01-04T06:16:20Z","number":9913,"mergeCommitSha":"c89d255daf722c8fe9f6b3afbcfaecceac385fee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9913","title":"[SKIP TESTS] Fix people page pagination when a user has no teams","createdAt":"2024-01-04T06:06:56Z"}
{"state":"Merged","mergedAt":"2024-01-04T07:01:09Z","number":9914,"mergeCommitSha":"b1e496eceb261cf2d9a6519c459cc0323d628b42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9914","title":"Skip data table creation if the table has any colSpan rows","createdAt":"2024-01-04T06:50:12Z"}
{"state":"Merged","mergedAt":"2024-01-04T07:49:14Z","number":9915,"mergeCommitSha":"b7f14f179d501917f8356a25dc609b5b204ef2cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9915","title":"Backfill growth metrics periodically","createdAt":"2024-01-04T07:38:24Z"}
{"state":"Merged","mergedAt":"2024-01-09T19:14:44Z","number":9916,"body":"Merging this will reconfigure the prod active MQ to bring up a secondary standby instance in a different AZ. The actual failover process requires changing our application configuration to support Transport Failover.\r\n\r\nDocs for transport failover:\r\n- https://activemq.apache.org/failover-transport-reference.html\r\n- https://docs.aws.amazon.com/amazon-mq/latest/developer-guide/active-standby-broker-deployment.html\r\n\r\nCDK Diff output.\r\n```\r\nIncluding dependency stacks: NetworkStack\r\nStack NetworkStack\r\nThere were no differences\r\nStack ActiveMQStack\r\nResources\r\n[~] AWS::AmazonMQ::Broker activemq-broker activemqbroker replace\r\n ├─ [~] DeploymentMode (requires replacement)\r\n │   ├─ [-] SINGLE_INSTANCE\r\n │   └─ [+] ACTIVE_STANDBY_MULTI_AZ\r\n └─ [~] SubnetIds (requires replacement)\r\n     └─ @@ -1,5 +1,8 @@\r\n        [ ] [\r\n        [ ]   {\r\n        [ ]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCisolatedSubnet1Subnet995CB683F00FCFF6\"\r\n        [+]   },\r\n        [+]   {\r\n        [+]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCisolatedSubnet2Subnet9F04C5F64946038D\"\r\n        [ ]   }\r\n        [ ] ]\r\n\r\n\r\n✨  Number of stacks with differences: 1\r\n****************************************************\r\n*** Newer version of CDK is available [2.118.0]  ***\r\n*** Upgrade recommended (npm install -g aws-cdk) ***\r\n****************************************************\r\n```","mergeCommitSha":"adc9743547bf169c33d1d0a4cd96c8cca7c8b5a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9916","title":"Adding support for Active-Standby MQ deployment in prod","createdAt":"2024-01-04T08:51:52Z"}
{"state":"Merged","mergedAt":"2024-01-04T18:18:54Z","number":9917,"body":"Some typing issues made it so that postMessage was not passed down correctly to view.","mergeCommitSha":"ebd1f1415f13485583bbf5d624567f14eccd5083","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9917","title":"Fix type issue in PR view","createdAt":"2024-01-04T18:07:10Z"}
{"state":"Merged","mergedAt":"2024-01-04T21:04:14Z","number":9918,"mergeCommitSha":"462e18dc996dfe72c03afa63ba799fd815598a0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9918","title":"client assets bump - testing out new email campaign assets","createdAt":"2024-01-04T19:07:36Z"}
{"state":"Merged","mergedAt":"2024-01-04T19:57:40Z","number":9919,"body":"Fix issue where MainSidebarStream + view was constantly being subscribed / unsubscribed.\r\nThis prevented new views from launching within sidebar.","mergeCommitSha":"3a824a1b486c39866bd98019a6eef6ef6ac35d68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9919","title":"Fix main sidebar","createdAt":"2024-01-04T19:45:14Z"}
{"state":"Merged","mergedAt":"2022-04-25T20:15:25Z","number":992,"body":"Reinitialize auth whenever web extension reinitializes due to service worker restart.\r\n\r\n","mergeCommitSha":"068959f4b7ccbfb49add8345eebaf92c8bbb0ea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/992","title":"Web extension reinitialize auth.","createdAt":"2022-04-25T18:07:49Z"}
{"state":"Merged","mergedAt":"2024-01-04T21:08:26Z","number":9920,"mergeCommitSha":"98b34e45b81c218a8363b5328d512fb243e038fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9920","title":"Validation tests for demo team JWT authentication","createdAt":"2024-01-04T20:03:14Z"}
{"state":"Merged","mergedAt":"2024-01-04T21:45:42Z","number":9921,"body":"Motivation is to use for filtering in the client.\r\nhttps://chapter2global.slack.com/archives/C069WLKFQE8/p1704233211192389\r\n\r\nCould be used for other purposes, eg: tooltips etc.","mergeCommitSha":"0ee1b7f5ef314e10a4abd46564f5f30302643f31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9921","title":"Expose Demo Template data sources in the template APIs","createdAt":"2024-01-04T20:56:57Z"}
{"state":"Merged","mergedAt":"2024-01-04T21:36:12Z","number":9923,"mergeCommitSha":"6fda7d405a9aacfde0676cfdd7046d5c0a8ef1a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9923","title":"Remove isolated subnets and rename CoreVPC config to VPCConfig","createdAt":"2024-01-04T21:32:34Z"}
{"state":"Merged","mergedAt":"2024-01-04T21:54:20Z","number":9924,"body":"We had a bunch of bugs when we mark threads as read, during thread view:\r\n\r\n- The PR view never marked threads as viewed in the IDEs (we were directly trying to use the unreadThreadStore in the view -- oops)\r\n- The thread view in IntelliJ would accidentally mark threads as unread, instead of read, if `ThreadInfo.unread` was unset\r\n\r\nThe root problem is that we had a lot of code doing the same thing, slightly differently, in a bunch of places.  We now use a ClientWorkspace action to mark threads as unread, and we only automatically call this in two places: `ThreadView` (for all direct thread views) and `PullRequestThreadBlock` (when we focus on a single thread in a PR).\r\n\r\nNote that the user-initiated read/unread actions aren't touched in this PR -- the code there could definitely be cleaned up too","mergeCommitSha":"787959f39f2e65886e1fabd6072c6c1d8271d36c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9924","title":"Fix thread-read marking during view","createdAt":"2024-01-04T21:42:40Z"}
{"state":"Merged","mergedAt":"2024-01-05T22:28:00Z","number":9925,"body":"Move postAsset to ClientWorkspace + removes need for temporary assets stream.","mergeCommitSha":"87c790e9c74c3cf76cffdb349f05808b7b091175","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9925","title":"ClientWorkspace PostAssets","createdAt":"2024-01-04T21:48:46Z"}
{"state":"Merged","mergedAt":"2024-01-04T22:46:50Z","number":9926,"mergeCommitSha":"2e466d755eb8f7fc6c8c7302540d601ee087dbcd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9926","title":"Respect Retry-After header if exists","createdAt":"2024-01-04T22:22:34Z"}
{"state":"Merged","mergedAt":"2024-01-04T23:29:31Z","number":9927,"mergeCommitSha":"ed1ed8d10539fb2e927625a93fff478e15a76c0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9927","title":"Populate template data sources","createdAt":"2024-01-04T22:41:21Z"}
{"state":"Merged","mergedAt":"2024-01-05T00:10:11Z","number":9928,"body":"Introduce concept of view router.\r\nWill allow for multiple instances of views (e.g. one for main sidebar. another for thread / pr viewer in intelliJ)","mergeCommitSha":"2de528fb4ee65ee7a8b2fcaadb4ba638125b0590","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9928","title":"Introduce VIewRouter","createdAt":"2024-01-04T22:57:30Z"}
{"state":"Merged","mergedAt":"2024-01-05T21:24:44Z","number":9929,"body":"Introduced restrictions:\r\n\r\n- deleteRepo is not allowed for demo teams\r\n- patchWebIngestionConfiguration is not allowed for demo teams\r\n\r\nThese APIs now use demo template resources:\r\n\r\n- findRepo\r\n- getRepo\r\n- getRepos\r\n- getWebIngestions\r\n- getWebIngestionConfiguration\r\n","mergeCommitSha":"be615e11553869b46b9d4a090620d046158ee911","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9929","title":"Expose fake repos and websites in API for demo teams","createdAt":"2024-01-04T23:01:51Z"}
{"state":"Merged","mergedAt":"2022-04-25T19:58:58Z","number":993,"body":"https://ktor.io/docs/compression.html","mergeCommitSha":"18bc114d4b29c3f01237d2f9551237d02285431e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/993","title":"Compress outgoing API service content","createdAt":"2022-04-25T18:21:15Z"}
{"state":"Merged","mergedAt":"2024-01-05T00:15:22Z","number":9930,"body":"Might also need to limit during download if we're worried about OOMing the service.","mergeCommitSha":"f95420736d0fc285db2a6d18a6bb5c474dca4fe4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9930","title":"Truncate large web pages before embedding","createdAt":"2024-01-04T23:53:27Z"}
{"state":"Merged","mergedAt":"2024-01-05T01:02:53Z","number":9931,"body":"Required for \"who\" style questions where the team member is known. Example \"What are Sally's open PRs\", \"Who does Richie work with most\", etc. ","mergeCommitSha":"b1d83b3cb6388ca5ca29bc4d42fc09e230a6ae83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9931","title":"Add team member lookup to ML functions","createdAt":"2024-01-05T00:45:45Z"}
{"state":"Merged","mergedAt":"2024-01-05T01:09:26Z","number":9932,"mergeCommitSha":"57fe121398331cd70a53ab88aa3ee8cbad96e2a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9932","title":"Ignore not html","createdAt":"2024-01-05T00:59:18Z"}
{"state":"Merged","mergedAt":"2024-01-05T01:43:46Z","number":9933,"body":"Need to both soft-delete the team, and remove the DemoOwnership relationship.\n\nAlso list deleted demo teams in admin web demo teams page.","mergeCommitSha":"d8c40ab670701fb3019d403d2502061b0e29ea46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9933","title":"Fix bug in demo team delete API","createdAt":"2024-01-05T01:15:40Z"}
{"state":"Merged","mergedAt":"2024-01-05T02:24:05Z","number":9934,"mergeCommitSha":"3d615a04d007238cc651ef25c6c4cebf7e3ee641","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9934","title":"Add team member lookup logging","createdAt":"2024-01-05T01:55:36Z"}
{"state":"Merged","mergedAt":"2024-01-05T19:43:44Z","number":9935,"body":"Entering demo mode:\r\n\r\n```\r\n        const team = await TeamStore.enterDemoMode(templateId);\r\n        navigate(DashboardUrls.team(team.id));\r\n```\r\n\r\n\r\nExiting:\r\n```\r\n        await TeamStore.exitDemoMode();\r\n        navigate(DashboardUrls.index());\r\n```","mergeCommitSha":"d180659e38f5adb78f82b2858e5fc707dd842113","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9935","title":"TeamStore API for demo mode","createdAt":"2024-01-05T03:29:04Z"}
{"state":"Merged","mergedAt":"2024-01-05T05:35:50Z","number":9936,"mergeCommitSha":"54e25a0dd60ad9977c2101748712ae56cfd650bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9936","title":"[SKIP TESTS] - add more ML function logging","createdAt":"2024-01-05T05:35:26Z"}
{"state":"Merged","mergedAt":"2024-01-05T06:45:34Z","number":9937,"mergeCommitSha":"3ec3a84afbeeb27c9d4e2608b259d734a90e0cf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9937","title":"Relax member matching constraints","createdAt":"2024-01-05T06:22:28Z"}
{"state":"Merged","mergedAt":"2024-01-05T07:26:32Z","number":9938,"mergeCommitSha":"45cdd09a342f5f673f82482bca12d8e9ca0ce97e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9938","title":"[SKIP TESTS] - add close colleague logging","createdAt":"2024-01-05T07:25:08Z"}
{"state":"Merged","mergedAt":"2024-01-05T10:30:22Z","number":9939,"mergeCommitSha":"fc5c2e25fbb6d67453157260338f47e2a531c2f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9939","title":"Use primary members for ML functions","createdAt":"2024-01-05T08:00:28Z"}
{"state":"Merged","mergedAt":"2024-01-05T16:07:27Z","number":9940,"mergeCommitSha":"62da84259b55c0d836bde78da6b1ae158c3a4e0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9940","title":"[SKIP TESTS] - get a little cheeky with the name matching","createdAt":"2024-01-05T16:03:49Z"}
{"state":"Merged","mergedAt":"2024-01-05T17:13:00Z","number":9941,"mergeCommitSha":"28d37c34e059b19b93d609399e29c5db7e9b004e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9941","title":"Oops","createdAt":"2024-01-05T17:12:45Z"}
{"state":"Merged","mergedAt":"2024-01-06T00:04:59Z","number":9942,"body":"Move PR Commands to use new view router in IDEs","mergeCommitSha":"e119d90589a625dc688e5312547412ce98a5c45d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9942","title":"PRview router","createdAt":"2024-01-05T17:13:13Z"}
{"state":"Merged","mergedAt":"2024-01-05T17:38:44Z","number":9943,"body":"Affected user:\nhttps://admin.prod.getunblocked.com/people/2183f4e8-f6ee-47db-9ab7-a514432b9a26\n\nIntercom:\nhttps://app.intercom.com/a/inbox/crhakcyc/inbox/admin/5621013/conversation/3700\n\nSlack:\nhttps://chapter2global.slack.com/archives/C03NVHV37EF/p1704436388133949?thread_ts=1704433054.692569&cid=C03NVHV37EF\n\nStack trace:\n```\nk.s.MissingFieldException: Field 'description' is required for type with serial name 'com.nextchaptersoftware.scm.bitbucket.models.BitbucketRepo', but it was missing\n\tat k.s.i.PluginExceptionsKt.throwMissingFieldException(PluginExceptions.kt:20)\n\tat c.n.s.b.m.BitbucketRepo.<init>(BitbucketRepo.kt:10)\n\tat c.n.s.b.m.BitbucketRepo$$serializer.deserialize(BitbucketRepo.kt:10)\n\tat c.n.s.b.m.BitbucketRepo$$serializer.deserialize(BitbucketRepo.kt:10)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\t... 66 common frames omitted\nWrapped by: k.s.MissingFieldException: Field 'description' is required for type with serial name 'com.nextchaptersoftware.scm.bitbucket.models.BitbucketRepo', but it was missing at path: $.values[0]\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:93)\n\tat k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\n\tat k.s.e.AbstractDecoder.decodeSerializableElement(AbstractDecoder.kt:70)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableElement(StreamingJsonDecoder.kt:165)\n\tat k.s.e.CompositeDecoder$DefaultImpls.decodeSerializableElement$default(Decoding.kt:533)\n\tat k.s.i.CollectionLikeSerializer.readElement(CollectionSerializers.kt:80)\n\tat k.s.i.AbstractCollectionSerializer.readElement$default(CollectionSerializers.kt:51)\n\tat k.s.i.AbstractCollectionSerializer.merge(CollectionSerializers.kt:36)\n\tat k.s.i.AbstractCollectionSerializer.deserialize(CollectionSerializers.kt:43)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)\n\tat k.s.e.AbstractDecoder.decodeSerializableValue(AbstractDecoder.kt:43)\n\tat k.s.e.AbstractDecoder.decodeSerializableElement(AbstractDecoder.kt:70)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableElement(StreamingJsonDecoder.kt:165)\n\tat c.n.s.b.m.BitbucketPageData$$serializer.deserialize(BitbucketPageData.kt:5)\n\tat c.n.s.b.m.BitbucketPageData$$serializer.deserialize(BitbucketPageData.kt:5)\n\tat k.s.j.i.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonD...\n```","mergeCommitSha":"4eed230f4a164fb093c24add855682d57bd078f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9943","title":"Fix 500 in listScmInstallationRepos for Bitbucket","createdAt":"2024-01-05T17:25:03Z"}
{"state":"Merged","mergedAt":"2024-01-05T20:26:56Z","number":9944,"mergeCommitSha":"c45ebb5b7aa5f5bbff841bc619e9952304420309","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9944","title":"Remove unused WebIngestionSiteModel.lastIngested column","createdAt":"2024-01-05T19:43:39Z"}
{"state":"Merged","mergedAt":"2024-01-05T19:59:23Z","number":9945,"mergeCommitSha":"25b27feb3bb7ad0a483d12f9948e818e1a553ce5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9945","title":"Remove JSON header fields from website scraper","createdAt":"2024-01-05T19:44:59Z"}
{"state":"Closed","mergedAt":null,"number":9946,"mergeCommitSha":"ac2e67c302ff221fd8040632552dca005a5ff12c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9946","title":"Fix HTTP client used for web ingesetion","createdAt":"2024-01-05T19:45:26Z"}
{"state":"Merged","mergedAt":"2024-01-05T20:09:16Z","number":9947,"mergeCommitSha":"8c22b6bbc9ad24f12dadc8d05fe73bfc5a412551","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9947","title":"Fix demo team listing in admin web","createdAt":"2024-01-05T19:53:57Z"}
{"state":"Merged","mergedAt":"2024-01-05T20:57:19Z","number":9948,"mergeCommitSha":"b36cb956bbd077708615c4438e6ddb13d1210e3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9948","title":"Remove web page embedding if the site is no longer configured","createdAt":"2024-01-05T20:19:06Z"}
{"state":"Merged","mergedAt":"2024-01-05T21:30:54Z","number":9949,"mergeCommitSha":"9b9f9b124a3ae41ff83566fb2c92e59fa782662b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9949","title":"add-filter-value-to-logging-output","createdAt":"2024-01-05T20:49:58Z"}
{"state":"Merged","mergedAt":"2024-01-05T21:08:16Z","number":9950,"body":"Same bug as here: #9943","mergeCommitSha":"c5d3a87f98cb58baa4ccb6d096192b20b89e55f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9950","title":"Fix GitLab onboard bug","createdAt":"2024-01-05T20:52:18Z"}
{"state":"Merged","mergedAt":"2024-01-08T16:34:20Z","number":9951,"body":"Still awating Adrian's role. Do not merge until we get that.","mergeCommitSha":"c9f99f9efbb0084743a7f7e4e9637c47e86fd439","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9951","title":"Total soft quote","createdAt":"2024-01-05T20:58:38Z"}
{"state":"Merged","mergedAt":"2024-01-05T23:07:11Z","number":9952,"mergeCommitSha":"a80ec4f11ce08d6b3de6988c755881101adc2697","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9952","title":"More betterer team member matching","createdAt":"2024-01-05T21:10:28Z"}
{"state":"Merged","mergedAt":"2024-01-05T21:48:27Z","number":9953,"mergeCommitSha":"9566535e403eed36d8b5ed301e28b7706ae63584","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9953","title":"[SKIP TESTS] Fix data table bug on demo template page","createdAt":"2024-01-05T21:25:27Z"}
{"state":"Merged","mergedAt":"2024-01-05T21:55:05Z","number":9954,"body":"Factor the layout values for VSCode and IntelliJ into one value.  Only display the reply avatar when we're above this breakpoint.","mergeCommitSha":"fc8649e9cdd62281f9036cef486a602b18f6fa3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9954","title":"Only show IDE reply avatars at larger breakpoints","createdAt":"2024-01-05T21:43:45Z"}
{"state":"Merged","mergedAt":"2024-01-05T22:06:51Z","number":9955,"body":"Motivation is that some page loads (eg: people page, and a few other) are really slow. I want to use Honeycomb to figure out what the problem is.","mergeCommitSha":"6078c7e3cdc1ce509933d28a4bbdc8d15b0ecdbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9955","title":"Add tracing to adminweb service to debug performance issues","createdAt":"2024-01-05T21:57:01Z"}
{"state":"Merged","mergedAt":"2024-01-05T22:15:38Z","number":9956,"mergeCommitSha":"7be3dcfd5d3288df72e890ef9bb939c51477a376","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9956","title":"Fix bug with editor buttons","createdAt":"2024-01-05T22:03:57Z"}
{"state":"Merged","mergedAt":"2024-01-05T22:57:37Z","number":9957,"body":"The logic for determining when to show the demo based on repo state is speculative; making some assumption about the users' state of mind.\n\nSimpler to just always provide the option to try before commmiting.","mergeCommitSha":"5b44508ebbd7de0495ab8e46fda095f317d0411d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9957","title":"Always show Try Unblocked demo option during onboarding","createdAt":"2024-01-05T22:44:37Z"}
{"state":"Merged","mergedAt":"2024-01-05T23:12:32Z","number":9958,"body":"* Add extra padding below the content in the 'Ask a Question' UI -- helps visually centre the content\r\n* Link-style buttons should be inline-flex, not inline-block.  This allows the sub-content to align correctly (fixes 'Contact Support' button layout)","mergeCommitSha":"75852d2895dffb42c42214ceefc1e681c0db58ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9958","title":"Sidebar UI tweaks","createdAt":"2024-01-05T23:01:24Z"}
{"state":"Merged","mergedAt":"2024-01-08T19:30:14Z","number":9959,"body":"![CleanShot 2024-01-05 at 15 24 31@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/f0d4e3e6-fa4f-445a-bbf1-65eb504c83bd)\r\n![CleanShot 2024-01-05 at 15 24 19@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/df43f629-d503-4228-90bc-457018e75649)\r\n","mergeCommitSha":"18b389838601155fa8945665686d022c44eb2056","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9959","title":"Fix header","createdAt":"2024-01-05T23:29:02Z"}
{"state":"Merged","mergedAt":"2022-04-25T20:43:08Z","number":996,"body":"Two TBD issues:\r\n\r\n1) What should we do when the SourcePoint isn't resolvable (requires design input)\r\n2) The first time you open any thread on a particular repo, loading can be quite slow, if the SM engine needs to do work.  Not sure what we should do in this scenario.","mergeCommitSha":"295b163bf16e90991466d0ef4686559184b1451d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/996","title":"Use SourceMark engine to determine SourcePoint in discussion view","createdAt":"2022-04-25T18:32:38Z"}
{"state":"Merged","mergedAt":"2024-01-06T02:03:29Z","number":9960,"mergeCommitSha":"a00b26661f7f1e1d4030f76fd88caf0d3c3879e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9960","title":"add PR language + test","createdAt":"2024-01-06T00:56:46Z"}
{"state":"Merged","mergedAt":"2024-01-06T20:07:01Z","number":9961,"mergeCommitSha":"9eabb99bbb459444a89cb26437eaf5127a7a98d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9961","title":"[SKIP TESTS] Disable https://stripe.com ingestion for now","createdAt":"2024-01-06T19:53:46Z"}
{"state":"Merged","mergedAt":"2024-01-08T07:20:40Z","number":9962,"body":"https://chapter2global.slack.com/archives/C02US6PHTHR/p1704695024851509","mergeCommitSha":"b565661da8116cec41627599468fd33d0d1136ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9962","title":"Fix repo processing icon","createdAt":"2024-01-08T06:52:22Z"}
{"state":"Closed","mergedAt":null,"number":9963,"body":"…as been disabled (#8865)\"\r\n\r\nThis reverts commit 8c879e3652ab4f0d751fd286aa353cf7cc969874.","mergeCommitSha":"d5d0bbb846e7fdab1ba3f28896e4e05d4e434374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9963","title":"Revert \"Filter out SearchResults if integration or confluence space h…","createdAt":"2024-01-08T18:34:41Z"}
{"state":"Merged","mergedAt":"2024-01-08T18:48:22Z","number":9964,"body":"- Revert \"[SKIP TESTS] Stop generting topic events (#9894)\"\r\n- Revert \"scale topic service to zero (#9895)\"\r\n","mergeCommitSha":"56918a9490e5a9f3e3a9222d339d755f8db8ec76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9964","title":"RevertChanges","createdAt":"2024-01-08T18:48:12Z"}
{"state":"Merged","mergedAt":"2024-01-08T19:09:02Z","number":9965,"mergeCommitSha":"9bcd7f4f23264ee9828da78b4731f1fd6836ad51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9965","title":"Do not send processing complete emails to demo teams","createdAt":"2024-01-08T18:52:41Z"}
{"state":"Merged","mergedAt":"2024-01-10T08:01:06Z","number":9966,"body":"Removes the team mode check","mergeCommitSha":"58ee70ab60cacb32d496cd25fe98013bbb7d6895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9966","title":"Ingest pull requests for all teams","createdAt":"2024-01-08T18:57:58Z"}
{"state":"Merged","mergedAt":"2024-01-08T20:10:20Z","number":9967,"body":"Don't show the full path.\r\n\r\nI added a `PathUtils` helper that will contain platform-independent path processing tools, so we can use them in any environment (`path` is node-only, and will also only process paths for your current platform).","mergeCommitSha":"4484a1f499d39d25fea46811b08edbb26ef72681","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9967","title":"Only show filename in 'Ask a Question' UI","createdAt":"2024-01-08T19:12:30Z"}
{"state":"Merged","mergedAt":"2024-01-08T21:17:05Z","number":9968,"body":"Add a metric event for when user navigates from \"Processing Complete\" email -> dashboard.\r\nNo longer need to rely on graphana for click rate data.\r\n\r\n","mergeCommitSha":"a4e82c9297f63e921235d286b186df22d11c8cd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9968","title":"Processing complete metric","createdAt":"2024-01-08T19:22:10Z"}
{"state":"Merged","mergedAt":"2024-01-08T19:39:34Z","number":9969,"mergeCommitSha":"767e320693df5292bb616ed7e5b3c192980abed8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9969","title":"[SKIP TESTS] Increase instance count","createdAt":"2024-01-08T19:39:24Z"}
{"state":"Merged","mergedAt":"2024-01-08T20:59:41Z","number":9970,"body":"Ad block may trigger scary screens when going to \"tracked\" links.\r\nDisable for now.\r\n\r\n![CleanShot 2024-01-08 at 12 08 49@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/f43ee128-f0af-42f3-aa80-1ffccacf673a)\r\n\r\n\r\nhttps://docs.aws.amazon.com/ses/latest/dg/faqs-metrics.html","mergeCommitSha":"90986c39070807323fa52bc3fe4f23dc6c73de27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9970","title":"Disable processing complete click handling ses","createdAt":"2024-01-08T20:20:34Z"}
{"state":"Open","mergedAt":null,"number":9971,"body":"[DO NOT MERGE] - Rip out team mode from Hub\n\nAdd team mode back to the swift model so CI passes","mergeCommitSha":"3acf9d50f51d1be48f595e9f8be9c404f17070cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9971","title":"[DO NOT MERGE] - Rip out team mode from Hub","createdAt":"2024-01-08T20:23:17Z"}
{"state":"Merged","mergedAt":"2024-01-08T21:01:15Z","number":9972,"mergeCommitSha":"178e1fbd95d7d1a90b796ac5820fa17cd3712fe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9972","title":"Demo team supports getTeamScmInstallationReposV2","createdAt":"2024-01-08T20:45:59Z"}
{"state":"Closed","mergedAt":null,"number":9973,"body":"In prepration for ingesting PR data for standard mode teams","mergeCommitSha":"5f30ef4431dcf589528cd5995c4d8b05d853ca2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9973","title":"'My Discussions' should always use Unblocked source in standard mode","createdAt":"2024-01-08T21:14:40Z"}
{"state":"Merged","mergedAt":"2024-01-08T22:03:16Z","number":9974,"mergeCommitSha":"b3d68ca63eafeef4e9dda622865524d0cd8d8d9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9974","title":"Add document summarization","createdAt":"2024-01-08T21:28:40Z"}
{"state":"Merged","mergedAt":"2024-01-08T21:41:54Z","number":9975,"mergeCommitSha":"5fe67e7b10611dcd19c17c7ef21c8143e7162149","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9975","title":"Move away from using v1 IntegrationInstallation apis","createdAt":"2024-01-08T21:29:48Z"}
{"state":"Merged","mergedAt":"2024-01-08T23:02:51Z","number":9976,"body":"Old:\r\n![CleanShot 2024-01-08 at 13 55 06@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/4e891e35-f82e-49df-849b-d319eb0072f9)\r\n\r\nNew:\r\n![CleanShot 2024-01-08 at 13 57 19@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/38f7d3b3-f2d7-4d24-91ad-96899de5717e)\r\n","mergeCommitSha":"91b9183d01d0f1a16abb9ed973b181ee9b64d90c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9976","title":"Fix pending processing page","createdAt":"2024-01-08T21:59:07Z"}
{"state":"Merged","mergedAt":"2024-01-08T23:04:02Z","number":9977,"body":"We can expand this to multiple repos in the future","mergeCommitSha":"83a0cc987d244a398ed12a34e1e00be97db2d12f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9977","title":"Add repo filter to document retrieval page","createdAt":"2024-01-08T22:15:20Z"}
{"state":"Merged","mergedAt":"2024-01-08T23:19:56Z","number":9978,"mergeCommitSha":"d6f66cb5a758c374fafe5689e48f27b246e06ccf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9978","title":"Run only one regression test a day","createdAt":"2024-01-08T22:59:18Z"}
{"state":"Merged","mergedAt":"2024-01-09T00:26:21Z","number":9979,"body":"Update processingComplete email to send user to `new/team/teamID/complete`\r\n\r\nThis then redirects user to `/ask` instead of download. It will also record metric.","mergeCommitSha":"4911805c64605becb96471572c0a5788cae6d512","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9979","title":"Update ProcessingComplete to send user to ask a question","createdAt":"2024-01-08T23:04:46Z"}
{"state":"Merged","mergedAt":"2022-04-25T19:39:59Z","number":998,"mergeCommitSha":"f19ea79e2c4cd98144c1df877773c4b01e391268","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/998","title":"Temp disable chromatic","createdAt":"2022-04-25T18:50:30Z"}
{"state":"Merged","mergedAt":"2024-01-08T23:24:38Z","number":9980,"body":"Needed to regenerate template JSON.\r\n\r\nFor: https://github.com/NextChapterSoftware/unblocked/pull/9970","mergeCommitSha":"78e80d5cd2cd45688dd7732bd0e856b2c921403c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9980","title":"Generate template json","createdAt":"2024-01-08T23:12:13Z"}
{"state":"Merged","mergedAt":"2024-01-09T00:13:37Z","number":9981,"mergeCommitSha":"07236560b7b1b8c4893b01c66de80a5a41862d4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9981","title":"Demo team supports listIntegrationInstallationsV2","createdAt":"2024-01-08T23:17:37Z"}
{"state":"Merged","mergedAt":"2024-01-08T23:47:50Z","number":9982,"body":"Trying to debug an issue with syncing Notion documents that only our team is experiencing","mergeCommitSha":"1f7951006c4b3e668405a5f9d6c65c60ca3f1d5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9982","title":"Revoke notion token for Rashin's user in prod","createdAt":"2024-01-08T23:19:14Z"}
{"state":"Merged","mergedAt":"2024-01-09T19:14:17Z","number":9983,"body":"<img width=\"1164\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8da75301-ef46-49fa-a594-ab574b383f41\">\r\n\r\nOutstanding items:\r\n* missing suggested questions from the main ask page (will add once API is implemented)\r\n* missing footer text below the input, not 100% sure how we want to source this string:\r\n<img width=\"660\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/f376eccd-643a-4933-b12f-92955391fc09\">\r\n\r\n","mergeCommitSha":"88e289f4296ad7b8b015745ecc8ca8c11bbe6525","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9983","title":"Dashboard demo team UI","createdAt":"2024-01-08T23:22:31Z"}
{"state":"Merged","mergedAt":"2024-01-12T21:22:45Z","number":9984,"body":"This hooks up the editor selection state into the client workspace state correctly","mergeCommitSha":"efa85ba45234e4806a75b81861ea7c50648a4848","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9984","title":"Use selection in 'Ask a Question' IntelliJ UI","createdAt":"2024-01-08T23:25:09Z"}
{"state":"Merged","mergedAt":"2024-01-09T00:42:48Z","number":9985,"mergeCommitSha":"9e7546c8be85739a366d722f5aedb6fd7703a95a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9985","title":"Implement visitedProcessingComplete API","createdAt":"2024-01-08T23:32:54Z"}
{"state":"Merged","mergedAt":"2024-01-08T23:50:03Z","number":9986,"mergeCommitSha":"f7843c7c2cea997824027bf6456942cd16f70e4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9986","title":"[SKIP TESTS] Routing changes","createdAt":"2024-01-08T23:33:10Z"}
{"state":"Merged","mergedAt":"2024-01-09T05:52:54Z","number":9987,"mergeCommitSha":"a3982a98dc1c04b23b0077b68b234754cddacd7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9987","title":"Add suggestion to connect additional repos","createdAt":"2024-01-09T00:03:17Z"}
{"state":"Merged","mergedAt":"2024-01-09T05:53:09Z","number":9988,"mergeCommitSha":"faef9ca8626d6577093737c217c46ae8493249d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9988","title":"Add Unblocked general knowledge functions","createdAt":"2024-01-09T00:05:27Z"}
{"state":"Merged","mergedAt":"2024-01-09T01:06:58Z","number":9989,"body":"The suggestions endpoints are used to retrieve a suggested questions that users can ask about a team in order to get started.\nThese questions are designed to provide users with relevant and helpful information about the team.","mergeCommitSha":"194a6ca0a633d03cae6899633cc68db741e3b27d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9989","title":"Introduce Suggested Questions","createdAt":"2024-01-09T00:08:00Z"}
{"state":"Merged","mergedAt":"2022-04-25T19:58:30Z","number":999,"body":"https://github.com/NextChapterSoftware/unblocked/issues/997","mergeCommitSha":"e1193baed16a75c344088f84b9249f443a123500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/999","title":"Update vscode treeview color","createdAt":"2022-04-25T19:33:27Z"}
{"state":"Merged","mergedAt":"2024-01-09T00:40:53Z","number":9990,"mergeCommitSha":"b1ae4253a37c5088a4c77259b870760b0dcc85f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9990","title":"Add error handling","createdAt":"2024-01-09T00:36:38Z"}
{"state":"Merged","mergedAt":"2024-01-09T19:15:41Z","number":9991,"mergeCommitSha":"ed0100a4639c0604b1228abbb841c0b7cee1a18d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9991","title":"Add ability to disable ingestion of a web site from the admin console","createdAt":"2024-01-09T00:40:26Z"}
{"state":"Merged","mergedAt":"2024-01-09T01:09:40Z","number":9992,"body":"This reverts commit 1f7951006c4b3e668405a5f9d6c65c60ca3f1d5d.","mergeCommitSha":"18f32e1571a55b609cb7bfd935d9224fb9479fb0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9992","title":"Revert \"Revoke notion token for Rashin's user in prod (#9982)\"","createdAt":"2024-01-09T00:54:18Z"}
{"state":"Merged","mergedAt":"2024-01-09T17:19:05Z","number":9993,"body":"When one returns to dashboard from processing complete email\r\n\r\nIf you’ve installed the hub > continue hub onboarding + asking first question in the hub\r\nIf you did not install the hub > ask question from dashboard","mergeCommitSha":"baffc4e2211dbfd7ca7608241de217e2b183f71b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9993","title":"Send user to continue onboarding after processComplete","createdAt":"2024-01-09T01:02:38Z"}
{"state":"Merged","mergedAt":"2024-01-09T03:23:13Z","number":9994,"body":"We are going to need to train different LLMContentFilters for different Source/Insight.Types. The general purpose LLMContentFilter has a lot of different positive false cases that should be addressed with this approach.","mergeCommitSha":"9f7945e1cb754f625eec9fd512b8ed61b9db76aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9994","title":"Adds embeddingMetadata to test harness","createdAt":"2024-01-09T02:13:15Z"}
{"state":"Merged","mergedAt":"2024-01-09T05:05:02Z","number":9995,"body":"We need to embed Notes (Q&A etc.)","mergeCommitSha":"42b25c540e1b8b25f8b185897b88288b6a0f6ff6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9995","title":"Fix content filter","createdAt":"2024-01-09T04:45:55Z"}
{"state":"Merged","mergedAt":"2024-01-09T17:45:59Z","number":9996,"mergeCommitSha":"901a974b1c80f0f58af5e5138265447650c71ffd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9996","title":"Tell the model that open PRs are a proxy for 'work in progress'","createdAt":"2024-01-09T16:48:22Z"}
{"state":"Merged","mergedAt":"2024-01-09T19:02:57Z","number":9997,"body":"Temporarily re-add topic section to IDEs.\r\nDoes not have click action for topics as topic viewer on dashboard has been removed.\r\n\r\n![CleanShot 2024-01-09 at 10 48 40@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/34e2f506-3b46-41ee-843b-e076a85e65aa)\r\n","mergeCommitSha":"44543c51eec3409e08a26763e8477b6cb7f05c70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9997","title":"Readd topic section","createdAt":"2024-01-09T18:50:25Z"}
{"state":"Merged","mergedAt":"2024-01-09T20:32:34Z","number":9998,"mergeCommitSha":"78025aefa43e342a62b810adbd92c1ca7d51210d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9998","title":"Add SuggestedQuestion persistence model and store","createdAt":"2024-01-09T18:54:49Z"}
{"state":"Merged","mergedAt":"2024-01-09T19:49:55Z","number":9999,"body":"Uplaod new asset","mergeCommitSha":"1b501a4026d9f76515fffed2af16196a68241545","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9999","title":"Updated assets","createdAt":"2024-01-09T19:03:08Z"}