import os
import unittest
from datetime import datetime

from llm_content_utils.chatml_content_handler import <PERSON>t<PERSON>ContentHandler

from pull_requests_processor.git_diff_partitioner import GitDiffPartitioner
from pull_requests_processor.pull_request_types import PullRequest, PullRequestState
from pull_requests_processor.llm_pull_request_summary_engine import LLMPullRequestSummaryEngine
from pull_requests_processor.llm_constants import LLAMA_MAX_INPUT_TOKENS, LLAMA_STOP_TOKENS, LLM_MAX_NEW_TOKENS
from pull_requests_processor.llm_pull_request_summary_prompts import (
    PullRequestSummaryCombinePrompt,
    PullRequestSummaryMapPrompt,
    PullRequestSummarySystemPrompt,
)

LLM_ENDPOINT_URL = "https://ml.alb.prod.gcp.getunblocked.com/api/ml/transformers/llama-31-8B"
MAX_INPUT_TOKENS = LLAMA_MAX_INPUT_TOKENS
STOP_TOKENS = LLAMA_STOP_TOKENS


class TestLLMPullRequestSummaryEngine(unittest.IsolatedAsyncioTestCase):
    _pull_request = PullRequest(
        number=1,
        merge_commit_sha="blah",
        html_url="blah",
        title="blah",
        body="blah",
        state=PullRequestState.MERGED,
        created_at=datetime.fromisoformat("2021-12-14T19:01:41Z"),
        merged_at=datetime.fromisoformat("2021-12-14T19:01:41Z"),
    )

    _llm_map_prompt = PullRequestSummaryMapPrompt()
    _llm_combine_prompt = PullRequestSummaryCombinePrompt()
    _llm_system_prompt = PullRequestSummarySystemPrompt()

    _content_handler = ChatMLContentHandler(system_prompt=_llm_system_prompt.get_prompt_template())

    @staticmethod
    def fixture_path(name):
        return os.path.join(os.path.dirname(__file__), "fixtures", "git", name)

    def test_large_diff(self):
        git_diff_partitioner = GitDiffPartitioner(
            max_tokens=MAX_INPUT_TOKENS,
        )

        pull_request_summary_engine = LLMPullRequestSummaryEngine(
            llm_map_prompt=self._llm_map_prompt,
            llm_combine_prompt=self._llm_combine_prompt,
            content_handler=self._content_handler,
            llm_endpoint_url=LLM_ENDPOINT_URL,
            max_tokens=MAX_INPUT_TOKENS,
            max_new_tokens=LLM_MAX_NEW_TOKENS,
            model_kwargs={"stop": STOP_TOKENS},
        )
        file = self.fixture_path("large_git_diff_1")
        with open(file) as f:
            git_diff = f.read()
            git_diff_partitions = git_diff_partitioner.split_git_diff(git_diff=git_diff)
            results = pull_request_summary_engine.predict(
                pull_request=self._pull_request,
                git_diff_partitions=git_diff_partitions,
            )
            self.assertTrue(results)

    def test_medium_diff(self):
        git_diff_partitioner = GitDiffPartitioner(
            max_tokens=MAX_INPUT_TOKENS,
        )

        pull_request_summary_engine = LLMPullRequestSummaryEngine(
            llm_map_prompt=self._llm_map_prompt,
            llm_combine_prompt=self._llm_combine_prompt,
            content_handler=self._content_handler,
            llm_endpoint_url=LLM_ENDPOINT_URL,
            max_tokens=MAX_INPUT_TOKENS,
            max_new_tokens=LLM_MAX_NEW_TOKENS,
            model_kwargs={"stop": STOP_TOKENS},
        )
        file = self.fixture_path("medium_git_diff_1")
        with open(file) as f:
            git_diff = f.read()
            git_diff_partitions = git_diff_partitioner.split_git_diff(git_diff=git_diff)
            results = pull_request_summary_engine.predict(
                pull_request=self._pull_request,
                git_diff_partitions=git_diff_partitions,
            )
            self.assertTrue(results)

    def test_small_diff(self):
        git_diff_partitioner = GitDiffPartitioner(
            max_tokens=MAX_INPUT_TOKENS,
        )

        pull_request_summary_engine = LLMPullRequestSummaryEngine(
            llm_map_prompt=self._llm_map_prompt,
            llm_combine_prompt=self._llm_combine_prompt,
            content_handler=self._content_handler,
            llm_endpoint_url=LLM_ENDPOINT_URL,
            max_tokens=MAX_INPUT_TOKENS,
            max_new_tokens=LLM_MAX_NEW_TOKENS,
            model_kwargs={"stop": STOP_TOKENS},
        )
        file = self.fixture_path("small_git_diff_3")
        with open(file) as f:
            git_diff = f.read()
            git_diff_partitions = git_diff_partitioner.split_git_diff(git_diff=git_diff)
            results = pull_request_summary_engine.predict(
                pull_request=self._pull_request,
                git_diff_partitions=git_diff_partitions,
            )
            self.assertTrue(results)

    async def test_async_large_diff(self):
        git_diff_partitioner = GitDiffPartitioner(
            max_tokens=MAX_INPUT_TOKENS,
        )
        pull_request_summary_engine = LLMPullRequestSummaryEngine(
            llm_map_prompt=self._llm_map_prompt,
            llm_combine_prompt=self._llm_combine_prompt,
            content_handler=self._content_handler,
            llm_endpoint_url=LLM_ENDPOINT_URL,
            max_tokens=MAX_INPUT_TOKENS,
            max_new_tokens=LLM_MAX_NEW_TOKENS,
            model_kwargs={"stop": STOP_TOKENS},
        )
        file = self.fixture_path("large_git_diff_1")
        with open(file) as f:
            git_diff = f.read()
            git_diff_partitions = git_diff_partitioner.split_git_diff(git_diff=git_diff)
            results = await pull_request_summary_engine.async_predict(
                pull_request=self._pull_request,
                git_diff_partitions=git_diff_partitions,
            )
            self.assertTrue(results)


if __name__ == "__main__":
    unittest.main()
