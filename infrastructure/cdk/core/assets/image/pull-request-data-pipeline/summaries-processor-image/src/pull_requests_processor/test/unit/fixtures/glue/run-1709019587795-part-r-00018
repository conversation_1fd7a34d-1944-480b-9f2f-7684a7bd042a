{"state":"Merged","mergedAt":"2022-11-01T19:59:26Z","number":3525,"body":"SCM secrets should no longer hold on to user secrets token (it’s not in its own secrets file)\r\n","mergeCommitSha":"aceb613cb4138e6df607957ed4323496ff8a967e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3525","title":"Cleanup unused secrets","createdAt":"2022-11-01T19:48:13Z"}
{"state":"Merged","mergedAt":"2022-11-01T20:53:25Z","number":3526,"body":"To allow sending a pull request index event whenever a pull request is updated via GitHub webhooks.","mergeCommitSha":"f0894b94c1fd855756c3877ac5b09d2460ab36ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3526","title":"Add SearchEventPayload.Type.PullRequest enum","createdAt":"2022-11-01T20:43:50Z"}
{"state":"Merged","mergedAt":"2022-11-02T22:39:51Z","number":3527,"body":"* Was using the faMessages icon in web extension; refactor out logic from vscode gutter icon rendering to share with multiple clients\r\n\r\nresult:\r\n<img width=\"736\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199340291-816d9f1f-6d9f-4f3b-a53f-2a6a515bb544.png\">\r\n\r\n* Also cleaned up the styling in each sourcemark row\r\n<img width=\"552\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199340816-fa59ed2e-fde3-4663-b115-6b0ea8782468.png\">\r\n","mergeCommitSha":"09896bf184a2fbe754bc9acd955c21a339caf95c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3527","title":"Fix multiple sourcemark icons in web extension","createdAt":"2022-11-01T21:03:01Z"}
{"state":"Merged","mergedAt":"2022-11-01T22:05:16Z","number":3528,"mergeCommitSha":"c6678ea43644626fe59ecfa913af70113a88700d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3528","title":"Upgrade cdk and gradle build cache node","createdAt":"2022-11-01T22:00:20Z"}
{"state":"Merged","mergedAt":"2022-11-01T22:19:43Z","number":3529,"body":"<img width=\"479\" alt=\"CleanShot 2022-11-01 at 15 09 38@2x\" src=\"https://user-images.githubusercontent.com/858772/199351519-1edcab75-2f2d-4071-a18d-12afaef15d2c.png\">\r\n<img width=\"480\" alt=\"CleanShot 2022-11-01 at 15 09 29@2x\" src=\"https://user-images.githubusercontent.com/858772/199351523-6ea3eaf0-edb8-4521-96cd-132b8b9f360e.png\">\r\n","mergeCommitSha":"5128bd9dc534c602108ade2d459d1be9e84ea2e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3529","title":"Fixes text contrast adjustments dependending on vibrancy in Ventura","createdAt":"2022-11-01T22:03:42Z"}
{"state":"Merged","mergedAt":"2022-02-17T00:33:51Z","number":353,"body":"* Compose Discussion Thread view\r\n    * A `MessageView` for single messages\r\n    * A `ThreadView` encompasses a list of MessageViews\r\n    * A `DiscussionThread` contains the ThreadView as well as the header section, list of participants, and MessageEditor for input\r\n* Add functionality to clear the MessageEditor manually \r\n    * see [note](https://github.com/NextChapterSoftware/unblocked/compare/kc/vscode/threadview?expand=1#diff-665d4d2ee00b6f824ec3e58bb1a7531f149a1f94b69548706233db5a8a86a8f9R139) about why this needs to be done this way\r\n* Add EditableInput component\r\n* NOTE: this is all built with a list of flat mock data and built on the assumption that the API/data stores will funnel in and update the data for the UI. I'd like to refactor these mocked models into the shared directory to cut down on repetition (even with live API data set up, we'll need the mocked data for the storybooks) but I can do that in the next PR \r\n\r\n\r\nViewColumn.Beside view\r\n![image](https://user-images.githubusercontent.com/13431372/154156254-ce820899-e489-4362-a172-b2173e07fa2f.png)\r\n\r\nFull view with participant section\r\n![image](https://user-images.githubusercontent.com/13431372/154156284-53c75fb6-6f9e-4563-b79f-9f08372c11cb.png)\r\n\r\n","mergeCommitSha":"b6cbf947205e6ddabe53a0dce58522239dbc2c28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/353","title":"Discussion thread view","createdAt":"2022-02-15T21:58:07Z"}
{"state":"Merged","mergedAt":"2022-11-01T23:10:04Z","number":3530,"body":"Also moves us from a dead github action that's on an old version of node.","mergeCommitSha":"9694b45bcda257db8f9c659369e250841d2250db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3530","title":"Move infra deployment to node 18","createdAt":"2022-11-01T22:16:16Z"}
{"state":"Merged","mergedAt":"2022-11-01T23:51:38Z","number":3531,"mergeCommitSha":"58c6bd641b8cc043914d7742e893d181d06ad6a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3531","title":"Initial Python Tools","createdAt":"2022-11-01T22:46:49Z"}
{"state":"Merged","mergedAt":"2022-11-02T20:19:57Z","number":3532,"body":"Event should be sent whenever a pull request, TLC, or review is created/updated.","mergeCommitSha":"4e88b92410eed0165ac88c0bf3299ebfe6869134","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3532","title":"Send index event for pull request","createdAt":"2022-11-01T23:48:01Z"}
{"state":"Merged","mergedAt":"2022-11-03T19:04:01Z","number":3533,"body":"Add new Insights panel to the VSCode explorer bar.  This new panel is called `ExplorerInsights`.  It supersedes the old panels (`ExplorerRelatedPullRequests`, `ExplorerCurrentFile`).  For now this new panel is enabled in dev, while the old panels are still displayed in prod.\r\n\r\nRight now this only displays the insights for the file, and the associated PRs, sorted by recency or comment count.  The new provider merges the existing streams together and sorts them, and the new Webview displays them in a list.  I added a new model (called `Insight` for now, though I might rename that to match our API models eventually) that represents either a Thread or a PR.\r\n\r\nSome parts of this are reused from existing code, some parts are brand new.\r\n\r\nhttps://user-images.githubusercontent.com/2133518/199603683-239dcf92-c89a-42ca-ab0c-25849fc24526.mp4\r\n\r\n","mergeCommitSha":"3a9f2de85d0e8fa471026f480a34bc4d39a2fdbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3533","title":"New VSCode Insights Explorer panel","createdAt":"2022-11-02T02:09:37Z"}
{"state":"Merged","mergedAt":"2022-11-02T20:28:52Z","number":3534,"body":"Uses Core Image filters and transforms to composite the camera and screen buffers","mergeCommitSha":"597c1577cea19f5eab0330c93b4404a76b6b363b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3534","title":"Render camera pip","createdAt":"2022-11-02T05:29:57Z"}
{"state":"Merged","mergedAt":"2022-11-03T15:50:21Z","number":3535,"body":"Replaced title field with message editor for description.\r\n\r\n<img width=\"648\" alt=\"CleanShot 2022-11-02 at 08 57 38@2x\" src=\"https://user-images.githubusercontent.com/1553313/199538934-6dedfa97-ec30-4ab7-ae30-5c50f79a725f.png\">\r\n\r\n","mergeCommitSha":"00bed0beb8c0bbf18836b47788a8ceeccd38f8fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3535","title":"Replace title with description editor in Video Walkthrough","createdAt":"2022-11-02T15:58:25Z"}
{"state":"Merged","mergedAt":"2022-11-02T18:54:39Z","number":3536,"body":"- Add basic transcription servicw\r\n- Try dev deployment\r\n","mergeCommitSha":"f9eeb87218bd58f8d96b511d28007f9141698f54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3536","title":"TranscriptionService","createdAt":"2022-11-02T17:35:44Z"}
{"state":"Merged","mergedAt":"2022-11-04T16:27:05Z","number":3537,"body":"Added related links section with some basic updates to code references row to allow for URL icons\r\n\r\n<img width=\"500\" alt=\"CleanShot 2022-11-02 at 11 02 39@2x\" src=\"https://user-images.githubusercontent.com/1553313/199567508-8b22d72f-afab-4f6c-8c78-cb2ebc881c15.png\">\r\n<img width=\"467\" alt=\"CleanShot 2022-11-02 at 11 03 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/199567562-aa5fa6a7-3e75-48cd-8c80-ad47106196f2.png\">\r\n","mergeCommitSha":"f2e4e730b865459e65b10672a18e41c8216e4b79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3537","title":"Add related links section to walkthroughs","createdAt":"2022-11-02T18:05:56Z"}
{"state":"Merged","mergedAt":"2022-11-02T18:39:45Z","number":3538,"body":"https://linear.app/unblocked/issue/UNB-723/pr-counts-are-invisible-in-vscode-light-theme\r\n\r\n<img width=\"429\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199571325-3ef2ff8d-1ee4-41de-bfc2-48daba468a30.png\">\r\n","mergeCommitSha":"81ed885ce248de1b78801cf42a5e29dcb737ee72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3538","title":"[UNB-723] Fix color of pr pane subtext","createdAt":"2022-11-02T18:24:29Z"}
{"state":"Merged","mergedAt":"2022-11-02T19:40:39Z","number":3539,"mergeCommitSha":"25fbae5c3b38e82963576fc1be270d919e0c2ac6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3539","title":"Add transcription queue","createdAt":"2022-11-02T19:25:08Z"}
{"state":"Merged","mergedAt":"2022-02-15T22:20:05Z","number":354,"body":"- Added a new stack for deploying Elasticache secure Redis clusters\r\n- Added config classes for new Redis Stack\r\n- Added config for creating IAM Mapped service accounts to help mounting AWS secret manager secrets on Kube\r\n- Updated EKS README to include AWS Secrets CSI driver installation instructions\r\n- Added docs about how to use CSI driver to mount secrets from secret manager\r\n- Updated Dev and Prod CDK configs to include new Redis stuff\r\n\r\nThe Redis cluster deployed by this code comes with both at rest as well as in-transit encryption. It uses Redis over TLS and each user can be scoped to one or more key prefixes. Auth has been enabled and all access to Redis requires password. Secrets for Redis users are stored in AWS Secret Manager","mergeCommitSha":"2edd3565006fcb6b51c5c5f500fbcef0f37a2405","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/354","title":"Add Elasticache Redis","createdAt":"2022-02-15T21:58:36Z"}
{"state":"Merged","mergedAt":"2022-11-02T19:44:18Z","number":3540,"mergeCommitSha":"df693dc16310ea2d287760141f44bd35f1b2285f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3540","title":"Fix build","createdAt":"2022-11-02T19:44:11Z"}
{"state":"Merged","mergedAt":"2022-11-02T20:30:49Z","number":3541,"body":"This is the canonical model by which we are enqueuing/dequeueing across various services.\r\n","mergeCommitSha":"34b80f1d10506ad8264c656e39ea82e1d4b0ed7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3541","title":"Add transcriptino event processors","createdAt":"2022-11-02T19:56:08Z"}
{"state":"Merged","mergedAt":"2022-11-02T20:01:25Z","number":3542,"mergeCommitSha":"866688ca72803e72570d4bd668a43fb4cbb680b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3542","title":"fix","createdAt":"2022-11-02T20:01:19Z"}
{"state":"Merged","mergedAt":"2022-11-02T20:37:05Z","number":3543,"mergeCommitSha":"b28768fc8a7ff015c68f2576d639f528bad8f1e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3543","title":"Send index event on upsert","createdAt":"2022-11-02T20:30:02Z"}
{"state":"Merged","mergedAt":"2022-11-02T20:44:48Z","number":3544,"mergeCommitSha":"0e8e439140488a4737348900ed798baf556f4726","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3544","title":"Api and webhook queue access","createdAt":"2022-11-02T20:41:44Z"}
{"state":"Merged","mergedAt":"2022-11-02T22:53:20Z","number":3545,"mergeCommitSha":"2d62556f548ed947f9d00fbc93151390cca4745d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3545","title":"Index pull request during bulk ingestion","createdAt":"2022-11-02T21:08:44Z"}
{"state":"Merged","mergedAt":"2022-11-02T23:01:00Z","number":3546,"body":"Currently slack threads are rendering as normal threads, but since we can't currently write back to slack threads, we should remove the message editor:\r\n\r\nbefore:\r\n<img width=\"1436\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199602693-f49fad6f-8c24-4076-9d0d-4c7de8bf3146.png\">\r\n\r\nafter:\r\n<img width=\"1430\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199602735-6d5a3074-2d59-4ff1-b358-5a302f779551.png\">\r\n\r\n\r\nOther:\r\n* Add an `ExternalLink` reusable component to dedupe having the same styles everywhere (it's essentially a link with an `faArrowUpRightFromSquare` icon next to it)\r\n* Add a header with slack data to the vscode thread:\r\n<img width=\"1091\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199603243-dbb76a86-1228-4422-9861-f8bb7c904679.png\">\r\n","mergeCommitSha":"8617af2af133bdf954159db296bb884a0b38635a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3546","title":"Slack threads should be readonly in Unblocked","createdAt":"2022-11-02T21:11:11Z"}
{"state":"Merged","mergedAt":"2022-11-02T22:09:28Z","number":3547,"mergeCommitSha":"db19b4f5c5a6b46a262f4f8e3acbcef063e71b44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3547","title":"Add queues to services","createdAt":"2022-11-02T21:41:25Z"}
{"state":"Merged","mergedAt":"2022-11-02T23:01:16Z","number":3548,"body":"is bleeding into production: \r\n\r\n<img width=\"1505\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199615233-0fd43481-1054-45a8-b51e-97f8e941710a.png\">\r\n","mergeCommitSha":"f60833301701504294847aa23d142aae22bc79cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3548","title":"Clean up rogue log","createdAt":"2022-11-02T22:34:17Z"}
{"state":"Merged","mergedAt":"2022-11-02T23:44:33Z","number":3549,"body":"Move notification service over to how we're doing event queues in other services.","mergeCommitSha":"4c03499fb543f5567d0482b5d334f70bbc76e25e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3549","title":"STandardize notification queues","createdAt":"2022-11-02T23:11:22Z"}
{"state":"Merged","mergedAt":"2022-02-15T22:33:58Z","number":355,"mergeCommitSha":"3942ca3eda34c1ee3709adc4c3a09ec05680621a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/355","title":"Fix merge issue between two simultanously merged PRs","createdAt":"2022-02-15T22:09:28Z"}
{"state":"Merged","mergedAt":"2022-11-03T20:02:37Z","number":3550,"body":"`SearchModel` will let us search different objects instead of just `Threads` through a `SearchableModel` enum property.\r\n\r\nThis will be used for topics V1 to search both threads and pull requests.","mergeCommitSha":"b55d0cf8745c62d27f1e2481a2b7e1cf2ffa14eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3550","title":"Replace ThreadSearchModel with SearchModel","createdAt":"2022-11-02T23:41:58Z"}
{"state":"Merged","mergedAt":"2022-11-03T00:04:04Z","number":3551,"mergeCommitSha":"0f4e42115ee95b36571b4258ede8226413eb640d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3551","title":"Fixports","createdAt":"2022-11-03T00:03:50Z"}
{"state":"Merged","mergedAt":"2022-11-03T17:00:13Z","number":3552,"body":"This reorders the message.proto to support python code gen. Hope that doesn't break another generator, but we are going to find out.","mergeCommitSha":"21e75fe19d70ff5732f198d904081f285cfa5500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3552","title":"Message deserialization + jsonlines","createdAt":"2022-11-03T00:27:56Z"}
{"state":"Merged","mergedAt":"2022-11-03T03:19:26Z","number":3553,"mergeCommitSha":"b177ebb9505ce07db8a30b07758a869cc75b9cf6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3553","title":"Hide less used tabs to declutter","createdAt":"2022-11-03T02:49:15Z"}
{"state":"Merged","mergedAt":"2022-11-03T05:08:28Z","number":3554,"mergeCommitSha":"9999113400e94adb87c170d950d7fd212a5fdc46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3554","title":"Trace instead of debug","createdAt":"2022-11-03T05:07:32Z"}
{"state":"Merged","mergedAt":"2022-11-03T21:07:02Z","number":3555,"body":"Add a new store that fetches and publishes client configurations.  This doesn't remove any of the existing flag logic, we need to first add and configure the flags we need in the service before doing that, so there will be a series of follow-on PRs.","mergeCommitSha":"7db80aaa429cf52ff44fcc42d8d4018e02eaeb76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3555","title":"Add client config store for TS","createdAt":"2022-11-03T17:30:43Z"}
{"state":"Merged","mergedAt":"2022-11-03T20:06:36Z","number":3556,"body":"Add new flags for:\r\n* New onboarding UI\r\n* Video walkthrough\r\n* New explorer insights UI","mergeCommitSha":"3bb03b20a1767cdaeabd46eb65315b6caee0d38c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3556","title":"New feature flags","createdAt":"2022-11-03T18:06:58Z"}
{"state":"Merged","mergedAt":"2022-11-03T18:37:57Z","number":3557,"mergeCommitSha":"75ae489378dc46fa23af47584e61d2b452a82a0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3557","title":"Make sure we launch the installer app bundled with the running app","createdAt":"2022-11-03T18:28:06Z"}
{"state":"Merged","mergedAt":"2022-11-04T16:12:57Z","number":3558,"body":"After recording a video, foreground IDE to display create video flow.","mergeCommitSha":"15a7ba2678c7d4558be9d53268159c00077b96f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3558","title":"Foreground VSCode after completion","createdAt":"2022-11-03T18:41:57Z"}
{"state":"Merged","mergedAt":"2022-11-04T23:39:57Z","number":3559,"body":"per feedback from Ben, make relative time ago helper show `X <time> ago` up to a year, then show `MM YYYY` \r\n\r\n<img width=\"376\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199817182-a5355e68-3601-4290-9be7-98631c4862c4.png\">\r\n\r\nshow full date in title:\r\n<img width=\"519\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/199817263-ff552b13-696d-4286-90a5-c4e89d00c8c3.png\">\r\n\r\n","mergeCommitSha":"f87f723b41345f5b07008b6add3c7588ff22ae50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3559","title":"Update relative DateTime helper","createdAt":"2022-11-03T19:33:07Z"}
{"state":"Merged","mergedAt":"2022-02-16T00:57:13Z","number":356,"body":"This way we can get the commit IDs and file hashes, since we can't get those through the graphql api.","mergeCommitSha":"9fc6e5bd6e76e7d747a1dac3e29d66441a65900c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/356","title":"Update PullRequestReviewThreadService to use rest apis to get comments and file hashes","createdAt":"2022-02-15T23:59:45Z"}
{"state":"Merged","mergedAt":"2022-11-03T20:48:27Z","number":3560,"mergeCommitSha":"1253a0c27babb4ef3eac7af9b174cfe2b8c94f97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3560","title":"Add local s3 buckets for assets","createdAt":"2022-11-03T20:00:47Z"}
{"state":"Merged","mergedAt":"2022-11-07T19:03:48Z","number":3561,"body":"Not to be merged until we reindex","mergeCommitSha":"6fb57946dea36c573814e7b1f196c1d606ab9d8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3561","title":"Use search model for searching threads","createdAt":"2022-11-03T20:29:39Z"}
{"state":"Merged","mergedAt":"2022-11-03T21:00:22Z","number":3562,"body":"There were several cache design problems with the existing tree hash cache and the uncommitted point\r\ncache that probably means these caches were fairly useless, and just consumed resources needlessly.\r\n\r\n### Problems\r\n\r\n- It's a write-through cache, which by definition means it is not correlated with read patterns.\r\n\r\n- Unbounded memory growth. Could be addressed with LRU.\r\n\r\n- Questionable effectiveness, since it's invalidated by the top-level tree hash which changes often.\r\n  I suspect this is actually totally useless, and a better cache would be a read through cache based\r\n  on FilePath and FileContentHash only.\r\n\r\n### Results\r\n\r\nRunning on the unblocked repo\r\n- no runtime difference\r\n- slightly less memory used\r\n\r\n#### Before\r\n```\r\nusage: {\r\n    rss: 90.890625,\r\n    heapUsed: 43.42457962036133,\r\n    heapTotal: 58.125,\r\n    maxRSS: 79.9375,\r\n    systemCPUTime: 7.0903529999999995,\r\n    userCPUTime: 38.353993,\r\n    wallClock: 201.20693104200063,\r\n    minorPageFault: 186022,\r\n    majorPageFault: 0\r\n}\r\n```\r\n\r\n\r\n#### After\r\n```\r\nusage: {\r\n    rss: 59.1875,\r\n    heapUsed: 43.261234283447266,\r\n    heapTotal: 39.78125,\r\n    maxRSS: 52.125,\r\n    systemCPUTime: 7.3009710000000005,\r\n    userCPUTime: 39.148971,\r\n    wallClock: 200.69948800000176,\r\n    minorPageFault: 186924,\r\n    majorPageFault: 0\r\n  }\r\n```\r\n","mergeCommitSha":"23d3e172ad233ea5503a44bdaaf64404070d5100","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3562","title":"Remove ineffective sourcemark caches","createdAt":"2022-11-03T20:31:22Z"}
{"state":"Merged","mergedAt":"2022-11-03T20:59:57Z","number":3563,"mergeCommitSha":"460355461ec0d06c591e5086b97adc5dfabbb5fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3563","title":"We should stop breaking the installer","createdAt":"2022-11-03T20:53:11Z"}
{"state":"Merged","mergedAt":"2022-11-03T22:29:53Z","number":3564,"mergeCommitSha":"93b1a4ec185d01aaa0477abc2dc298bd36978df4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3564","title":"cleanup docker","createdAt":"2022-11-03T22:23:15Z"}
{"state":"Merged","mergedAt":"2022-11-04T23:11:55Z","number":3565,"mergeCommitSha":"e39046b4779eb7562f18ead219b2ac8b5ee05509","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3565","title":"[BREAKS API ON MAIN] Add asset id to video models","createdAt":"2022-11-03T22:27:12Z"}
{"state":"Merged","mergedAt":"2022-11-04T17:20:52Z","number":3566,"mergeCommitSha":"069f22c700ef66043fd63867a2ca7b594210aaef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3566","title":"Turn accessibility back on for electron apps","createdAt":"2022-11-04T17:16:21Z"}
{"state":"Merged","mergedAt":"2022-11-04T18:29:23Z","number":3567,"mergeCommitSha":"b95894b839ab2079cefaa8a0635917f68504877f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3567","title":"Add slack runtime fixture","createdAt":"2022-11-04T18:16:34Z"}
{"state":"Merged","mergedAt":"2022-11-04T19:14:42Z","number":3568,"body":"Move all TS feature flags *except* slack to the new flag system.  Slack requires a bit more work and I'll do that in the next PR.\r\n\r\n* `notes`, `pullRequests`, `logoutButton`, `codelens` were all dropped\r\n* `newOnboarding` was mapped to the `FeatureNewOnboardingUI` flag\r\n* `showDemoUser` is removed and we use `isDeveloperBuild` instead\r\n* `video` was mapped to the `FeatureVideoWalkthrough` flag\r\n* `insightsPanel` was mapped to the `FeatureNewExplorerInsightsUI` flag\r\n","mergeCommitSha":"66d05b56ff3406590488d3e9ee59a820abd986f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3568","title":"Move most flags to new capabilities system","createdAt":"2022-11-04T18:34:32Z"}
{"state":"Merged","mergedAt":"2022-11-04T18:49:45Z","number":3569,"body":"Github has deprecated pre node-16 actions and also set-output.\r\nhttps://github.blog/changelog/2022-10-11-github-actions-deprecating-save-state-and-set-output-commands/","mergeCommitSha":"9879135bb7104b0ad025417e1bd932a68553265e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3569","title":"Fix workflows","createdAt":"2022-11-04T18:40:19Z"}
{"state":"Merged","mergedAt":"2022-02-16T01:18:12Z","number":357,"body":"I noticed the VSCode extension on the tip of `main` doesn't build -- turns out VSCode/web builds aren't running if a PR only changes `/shared`.  Fixed that, and the build-breaking bug.","mergeCommitSha":"f0237d3076a8bc10e7b2e37098ce7af26cbbd7b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/357","title":"Build web/vscode CI on 'shared' folder change","createdAt":"2022-02-16T00:28:08Z"}
{"state":"Merged","mergedAt":"2022-11-04T19:12:00Z","number":3570,"body":"Need to update another action to node 16 as node 12 is being deprecated on github.","mergeCommitSha":"5a42a8445fbb5c7c2540fb860b5424428561c3c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3570","title":"Another old action that needs to be updated","createdAt":"2022-11-04T18:54:00Z"}
{"state":"Merged","mergedAt":"2022-11-04T19:01:23Z","number":3571,"mergeCommitSha":"b217bbd331d94f6d3ebe346220b2e19a052251eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3571","title":"[UNB-706] Fix spacing","createdAt":"2022-11-04T18:55:29Z"}
{"state":"Merged","mergedAt":"2022-11-07T20:05:01Z","number":3572,"body":"This gets rid of the last of the old flag system RIP","mergeCommitSha":"1ab03c9fc86eba7cea9ac875cd763d7f9ff63c31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3572","title":"Use new slack feature flag in TS clients","createdAt":"2022-11-04T19:15:56Z"}
{"state":"Merged","mergedAt":"2022-11-04T19:27:10Z","number":3573,"mergeCommitSha":"db268a7c4522a7126c106d269b3db8db4471b634","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3573","title":"UPdate kube context","createdAt":"2022-11-04T19:27:03Z"}
{"state":"Merged","mergedAt":"2022-11-04T22:51:09Z","number":3574,"body":"Richie told me that the old way was wrong.\r\nAS usual, Richie is not off from the truth.\r\nThank you richie.","mergeCommitSha":"c8b561fd04eee149bb4b02bf816727d1cb05b40f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3574","title":"Delete old toggle code","createdAt":"2022-11-04T19:34:49Z"}
{"state":"Merged","mergedAt":"2022-11-04T21:51:18Z","number":3575,"body":"Fix bug in persisting null sourcepoint snippets\r\n\r\nWe incorrectly persisted `null` as `\"null\"`. This broke downstream API resulting in 500s in DEV:\r\n\r\n```\r\nkotlinx.serialization.json.internal.JsonDecodingException:\r\nExpected start of the object '{', but had 'EOF' instead at path: $ JSON input: null\r\n```\r\n\r\nThe inscrutable problem is that `object.encode()` produces \"null\" when the object is optional,\r\nwithout compiler warning or error, even though it's rarely what was intended IMO.\r\n","mergeCommitSha":"3b87c791f5f23e7d32e8566430fd221b5741d9cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3575","title":"Fix bug in persisting null sourcepoint snippets","createdAt":"2022-11-04T21:29:18Z"}
{"state":"Merged","mergedAt":"2022-11-06T23:31:07Z","number":3576,"body":"Adopt new SourceMark APIs in VSCode\r\n\r\n- VSCode is now aware of SourceMarks/SourcePoints and FileMarks/FilePoints\r\n- Deals with sparse file-path and sparse snippets\r\n- Unzips compressed and encoded snippets for SourceMarks on-demand\r\n- Uses more efficient upload API with better error handling","mergeCommitSha":"fdec8330526bea05ee1a886164f8a4d638ccf3b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3576","title":"Adopt new SourceMark APIs in VSCode","createdAt":"2022-11-04T21:53:39Z"}
{"state":"Merged","mergedAt":"2022-11-06T18:03:38Z","number":3577,"body":"1. We spit out a basic thread title for image\r\n2. We generate an image url\r\n","mergeCommitSha":"4c7be9b561f345f364a6af7908322f24fafbbbf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3577","title":"Add thread titles for threads anchored around images","createdAt":"2022-11-04T22:15:57Z"}
{"state":"Merged","mergedAt":"2022-11-15T23:08:56Z","number":3578,"mergeCommitSha":"ba5a03d9d42c4472682dcefd314a7a01e710c1ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3578","title":"Fix permissions and media stream initialization race","createdAt":"2022-11-04T22:37:35Z"}
{"state":"Merged","mergedAt":"2022-12-12T23:46:33Z","number":3579,"mergeCommitSha":"a76bb814aae4d0fbd76976aa6b7269f1dcf68c9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3579","title":"Dont show image and video urls in thread titles","createdAt":"2022-11-04T23:58:23Z"}
{"state":"Merged","mergedAt":"2022-02-16T00:35:11Z","number":358,"mergeCommitSha":"af266a2d3c81c8091e771d01610a999ec66f7129","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/358","title":"Add database info to readme","createdAt":"2022-02-16T00:31:19Z"}
{"state":"Merged","mergedAt":"2022-11-07T23:09:20Z","number":3580,"body":"Add a simple component for rendering pills/tokens/chips/etc","mergeCommitSha":"41eec60b2728fd1ca43082d5ef6115cf94be1551","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3580","title":"Add Pill component","createdAt":"2022-11-04T23:59:59Z"}
{"state":"Merged","mergedAt":"2022-11-07T18:30:45Z","number":3581,"body":"The joys of JSON. This is a new field [added](https://github.com/NextChapterSoftware/unblocked/pull/3565/files#diff-b03fd80df8f3a6460e353f4d2912c5bb3899448e030b1c69b962f2023829014fR107) on Friday but existing (serialized) data in the database does not have this field, so this should fix this error: \r\n\r\n<img width=\"1898\" alt=\"CleanShot 2022-11-07 at 09 53 33@2x\" src=\"https://user-images.githubusercontent.com/1924615/200380624-c5272745-70a1-4d3f-844d-6cfba950fe51.png\">\r\n","mergeCommitSha":"f1a9a78d1627126ddfd2b53b736e25c2d168ea96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3581","title":"Make null the default value when serialized object does not have field","createdAt":"2022-11-07T17:53:53Z"}
{"state":"Merged","mergedAt":"2022-11-07T18:48:26Z","number":3582,"mergeCommitSha":"63efe308fa2e6e71c0876f98c80638260c802c52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3582","title":"Fix missing code block and snippet styling","createdAt":"2022-11-07T18:15:34Z"}
{"state":"Merged","mergedAt":"2022-11-07T18:27:31Z","number":3583,"mergeCommitSha":"9be4643f66dded4d5e65e7e72982acd2d99ea461","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3583","title":"Api Cleanup","createdAt":"2022-11-07T18:19:01Z"}
{"state":"Merged","mergedAt":"2022-11-07T19:04:45Z","number":3584,"body":"- Use threadInfo.mark instead of deprecated threadInfo.sourceMark\r\n\r\n- Refactor types, per https://chapter2global.slack.com/archives/C02US6PHTHR/p1667846448901219?thread_ts=1667841821.858269&cid=C02US6PHTHR","mergeCommitSha":"f71f924d7c444434580aa436598154eb875de8e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3584","title":"Use threadInfo.mark instead of deprecated threadInfo.sourceMark","createdAt":"2022-11-07T18:52:26Z"}
{"state":"Merged","mergedAt":"2022-11-07T19:22:14Z","number":3585,"body":"I think the `source-map` devtool simply doesn't work for webviews.  I don't know why.  So I switched webviews back to use `eval-source-map`.\r\n\r\nThe problem we can into before is that eval-based source maps load *very* slowly when you start up VSCode, especially if you have any breakpoints set, which makes the repl loop very slow.  This PR mitigates this by changing the debugger launch parameters so that we only load sourcemaps for the extension.js bundle, which is the only one we debug within VSCode itself anyways.","mergeCommitSha":"929e50763777914638d41eb774fce61914e43fba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3585","title":"Fix VSCode webview debugging","createdAt":"2022-11-07T18:54:23Z"}
{"state":"Merged","mergedAt":"2022-11-07T21:57:00Z","number":3586,"body":"Add additional logging to issues when hub attempts to update versions.","mergeCommitSha":"43a7b6d2a4d7f28038b18a253a04f77cc4d26484","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3586","title":"Add additional logging context","createdAt":"2022-11-07T19:07:01Z"}
{"state":"Merged","mergedAt":"2022-11-09T21:55:55Z","number":3587,"body":"Walkthroughs should not be rendered in VSCode gutter.\r\n\r\nThis filtering is done at the rendering layer instead of store layer as we want threads to still populate insight sidebars / insight counts.\r\n\r\nTODO: Will have separate PR for web extension.","mergeCommitSha":"2cadf27297bf7be0d46875365b645c640bee9596","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3587","title":"[VSCode] Filter out walkthrough when rendering","createdAt":"2022-11-07T19:10:07Z"}
{"state":"Merged","mergedAt":"2022-11-07T21:30:11Z","number":3588,"body":"- AssemblyAI is my friend\r\n- Add secrets\r\n","mergeCommitSha":"a253a72410bcefc5d5e739a1f1030271ea941971","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3588","title":"AssemblyAi","createdAt":"2022-11-07T20:37:19Z"}
{"state":"Merged","mergedAt":"2022-11-07T21:46:48Z","number":3589,"body":"Trying to pass Range headers through to S3.","mergeCommitSha":"3692ddf004ccb936e2d9d381abadf94b6c3a971b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3589","title":"Support byte range requests","createdAt":"2022-11-07T21:41:33Z"}
{"state":"Merged","mergedAt":"2022-02-16T04:45:36Z","number":359,"body":"The old V3 client (now called V3App) should only be used for getting installation IDs. This creates a new V3Org client that is like the V4 client but for the rest API.","mergeCommitSha":"027eecabcd2defd412b8480d27959a3e49bd6b7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/359","title":"Comment and files api calls use correct token","createdAt":"2022-02-16T01:19:23Z"}
{"state":"Merged","mergedAt":"2022-11-07T22:09:13Z","number":3590,"body":"- Cleanup slack modules\r\n- Rename\r\n","mergeCommitSha":"7aee7fd92e0900bce029649537de8fa08e3535aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3590","title":"CleanupDependencies","createdAt":"2022-11-07T21:53:39Z"}
{"state":"Merged","mergedAt":"2022-11-30T23:11:43Z","number":3591,"body":"Generic input component that registers text inputs as search tokens by hitting `Enter` or `Comma`:\r\n<img width=\"281\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/200424869-5c38626b-552a-4e23-ae9b-2d197b780f26.png\">\r\n\r\nHandles basic expected keydown interactions (i.e. Backspace to delete, arrow left/right to navigate):\r\n![CleanShot 2022-11-07 at 14 08 16](https://user-images.githubusercontent.com/13431372/200425957-8cc4f25d-6055-41ff-9672-e1d1316b4155.gif)\r\n\r\nThe component also takes in a list of `suggestions` that it can display as autocomplete items:\r\n<img width=\"295\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/200425031-b267b701-428d-4df7-b78a-86f5892030b2.png\">\r\n\r\n(Will be part of the new Search UI)","mergeCommitSha":"d8215895ce920ac0f514fa0fd1a87c923385ed47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3591","title":"Token input component","createdAt":"2022-11-07T22:09:34Z"}
{"state":"Merged","mergedAt":"2022-11-09T18:17:35Z","number":3592,"body":"Need to create another PR to drop this table after this has been merged","mergeCommitSha":"aea9f8c2689475d792d8ccae7c0df4c7a9e59ee7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3592","title":"Remove ThreadSearchModel","createdAt":"2022-11-07T22:10:37Z"}
{"state":"Merged","mergedAt":"2022-11-07T23:10:31Z","number":3593,"mergeCommitSha":"f09e610e4c3d4e37ef6d4ef45632c5d24ad1203b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3593","title":"ADd transcirption queue","createdAt":"2022-11-07T23:08:04Z"}
{"state":"Merged","mergedAt":"2022-11-10T17:21:32Z","number":3594,"body":"Support for web titles was added to API spec but not implemented.\r\n\r\nVideo app will now generate titles based on accessibility.\r\n\r\n<img width=\"800\" alt=\"CleanShot 2022-11-07 at 15 26 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/200437095-a4ca6ce1-a671-4e14-ba47-244e52132a8a.png\">\r\n","mergeCommitSha":"1a2b0e6489a2641df7fda8c64d89d4c36bb68220","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3594","title":"Add title support for web url references","createdAt":"2022-11-07T23:27:40Z"}
{"state":"Merged","mergedAt":"2022-11-07T23:46:26Z","number":3595,"body":"Adding support for `authorization` query string. Positive cases for local test are broken so I couldn't validate this 100% but it has limited impact and no one is using assets so we won't need to disable prod deploys","mergeCommitSha":"fd226dc29a3b0d6ecf375f451df03b2ce35fb9e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3595","title":"parse authorization query string","createdAt":"2022-11-07T23:37:29Z"}
{"state":"Merged","mergedAt":"2022-11-08T01:31:43Z","number":3596,"mergeCommitSha":"b2bbe2a714ced247817ceaf0395312db08c6b789","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3596","title":"Add transcription webhooks","createdAt":"2022-11-07T23:46:44Z"}
{"state":"Merged","mergedAt":"2022-11-07T23:57:58Z","number":3597,"body":"The `sourcePoints` array should always have at least one sourcePoint but on the edge case that there isn't anything in there (see: https://linear.app/unblocked/issue/UNB-721/migration-to-cleanup-corrupt-blue-bubble-threads), at least load the dashboard and just hide the code block.","mergeCommitSha":"cc2fb8b17832afea4abecd63f9520a38b407ab86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3597","title":"Guard against empty list so dashboard at least loads","createdAt":"2022-11-07T23:49:25Z"}
{"state":"Merged","mergedAt":"2022-11-08T18:10:40Z","number":3598,"body":"This will return topics that were ✅ in the topic selection UI, deduped on name.","mergeCommitSha":"7c2c31016de00bc224214b2500c20c63a6d84d04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3598","title":"Implement getTopics","createdAt":"2022-11-08T01:10:40Z"}
{"state":"Merged","mergedAt":"2022-11-08T01:16:24Z","number":3599,"body":"I'll put in a proper fix for this with my next PR. This should do for now. \r\n\r\nHTTP 500s were caused by missing `Bearer ` string when taking tokens from query strings","mergeCommitSha":"9dbc05f4c7d91afb29c1be211903141a659b7039","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3599","title":"small fix","createdAt":"2022-11-08T01:12:57Z"}
{"state":"Merged","mergedAt":"2022-01-14T21:11:24Z","number":36,"body":"Following up on https://github.com/Chapter2Inc/codeswell/pull/30#discussion_r783550877\r\n\r\nRefactor Icon to include sizes.","mergeCommitSha":"7d1c5ccbd8a087015c4f41210bec8ce12faf4c30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/36","title":"Update Icon with sizes","createdAt":"2022-01-14T01:21:23Z"}
{"state":"Merged","mergedAt":"2022-02-18T00:50:00Z","number":360,"body":"### POC\r\nThis is the equivalent of the JSON representation, but much more terse to define, neater because it supports `oneof`, is faster to serialize, and smaller on the wire.\r\n\r\n\r\n### Codegen\r\n\r\nGradle task generates this.\r\n```\r\nbuild/generated/source/proto/main/\r\n├── java\r\n│   └── com\r\n│       └── nextchaptersoftware\r\n│           └── common\r\n│               └── model\r\n│                   └── MessageOuterClass.java\r\n├── js\r\n│   ├── block_blocklist_blockquote.js\r\n│   ├── blockcode.js\r\n│   ├── blockhorizontalline.js\r\n│   ├── blockimage.js\r\n│   ├── blocktext.js\r\n│   ├── formattedsegment.js\r\n│   ├── formattedtext.js\r\n│   ├── inlineelement.js\r\n│   ├── link.js\r\n│   ├── message.js\r\n│   └── plainsegment.js\r\n├── kotlin\r\n│   └── com\r\n│       └── nextchaptersoftware\r\n│           └── common\r\n│               └── model\r\n│                   ├── BlockCodeKt.kt\r\n│                   ├── BlockHorizontalLineKt.kt\r\n│                   ├── BlockImageKt.kt\r\n│                   ├── BlockKt.kt\r\n│                   ├── BlockListKt.kt\r\n│                   ├── BlockQuoteKt.kt\r\n│                   ├── BlockTextKt.kt\r\n│                   ├── FormattedSegmentKt.kt\r\n│                   ├── FormattedTextKt.kt\r\n│                   ├── InlineElementKt.kt\r\n│                   ├── LinkKt.kt\r\n│                   ├── MessageKt.kt\r\n│                   ├── MessageOuterClassKt.kt\r\n│                   └── PlainSegmentKt.kt\r\n└── ts\r\n    └── Message.ts\r\n```\r\n\r\nYou can also run command line codegen:\r\n\r\n```sh\r\nbrew install protoc\r\nnpm install -g ts-proto\r\nmkdir -p kotlin js ts\r\nprotoc -I=protos/ --kotlin_out=kotlin --js_out=js --ts_out=ts protos/Message.proto\r\n```\r\n","mergeCommitSha":"bd7231d02ad888d2a34dbf1e4df0b05bf5312645","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/360","title":"[RFC] Message as proto","createdAt":"2022-02-16T01:25:13Z"}
{"state":"Merged","mergedAt":"2022-11-08T06:23:37Z","number":3600,"mergeCommitSha":"284bf1cf0dc9475f0f2470e20d113872e02c6449","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3600","title":"revert the heaeder change to see if 502s go away","createdAt":"2022-11-08T06:20:58Z"}
{"state":"Merged","mergedAt":"2022-11-08T16:58:34Z","number":3601,"body":"Reworked caching with a new TreeCache that invalidates itself when the top-level\r\nGit tree hash changes. This allows us to cache commits safely and immutably,\r\nwhich greatly reduces calls to these Git command invocations:\r\n\r\n```\r\ngit branch --contains SHA\r\ngit cat-file -e SHA^{commit}\r\n```\r\n\r\n\r\n## Results (on private.yml)\r\n\r\nBefore\r\n```\r\n  count     sum     max     avg cmd\r\n     76    1002      26    13.2 git branch --contains SHA\r\n     43     821      35    19.1 git cat-file -e SHA^{commit}\r\n      1      40      40    40.0 git diff --full-index --ignore-all-space --no-prefix --dst-prefix=||| --unified=0 --diff-filter=ct --diff-filter=ct SHA --\r\n      1      38      38    38.0 git diff --diff-filter=DR --find-renames=50% --name-status SHA --\r\n      4      33       9     8.3 git show SHA:FILE\r\n```\r\nAfter\r\n```\r\n  count     sum     max     avg cmd\r\n      4      41      12    10.3 git show SHA:FILE\r\n      1      39      39    39.0 git diff --diff-filter=DR --find-renames=50% --name-status SHA --\r\n      1      37      37    37.0 git diff --full-index --ignore-all-space --no-prefix --dst-prefix=||| --unified=0 --diff-filter=ct --diff-filter=ct SHA --\r\n```","mergeCommitSha":"e7a8db84ee7acaa58b4ded1e951452a88051c64c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3601","title":"Introduce TreeCache to improve speed of sourcemark lookup by file","createdAt":"2022-11-08T08:32:06Z"}
{"state":"Merged","mergedAt":"2022-11-08T19:09:11Z","number":3602,"mergeCommitSha":"61dd8cc205557b4963c51af8acc3008f1872da1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3602","title":"Fix draft icon for notes and add dropdown for slack threads","createdAt":"2022-11-08T17:56:35Z"}
{"state":"Closed","mergedAt":null,"number":3603,"body":"Due to video issues, we are unable to properly handle authenticating longer streaming videos with our tokens which expire after a minute.\r\n\r\nTemporarily disabling auth for Get Auth assets to unblock streaming video feature.\r\n\r\nSolution is to introduce a service which vends longer lived tokens which have claims on the specific assets.\r\n","mergeCommitSha":"df7194f737f105bd7fb0eec4ec52b0580089f6f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3603","title":"[BREAKS API ON MAIN] Temp disable asset auth","createdAt":"2022-11-08T18:19:55Z"}
{"state":"Merged","mergedAt":"2022-11-08T19:25:21Z","number":3604,"body":"This PR just changes getTopicRelatedInsights to a POST. It's not being used right now by any clients, so we're OK to break.\r\n\r\nThis PR does not yet filter results by the sourcemark IDs and commit hashes (provided by the client) but we'll do that in another PR.","mergeCommitSha":"9d7fa2c054dc41cf5be0612d43163e387c2b3301","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3604","title":"[BREAKS API ON MAIN] Change getTopicRelatedInsights to a POST","createdAt":"2022-11-08T18:47:52Z"}
{"state":"Merged","mergedAt":"2022-11-08T19:03:53Z","number":3605,"mergeCommitSha":"473ec9d3eaa64a5289145703c989321baa31e549","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3605","title":"Add transcription webhook processing","createdAt":"2022-11-08T18:56:07Z"}
{"state":"Merged","mergedAt":"2022-11-08T23:57:36Z","number":3606,"mergeCommitSha":"56a08ee21d815a2d9cbfc0a42e45a1792d8f9d8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3606","title":"[BREAKS API ON MAIN] Add transcription proto","createdAt":"2022-11-08T20:27:35Z"}
{"state":"Merged","mergedAt":"2022-11-08T22:27:01Z","number":3607,"body":"Scoped tokens are special:\n\n- They only allow access to the exact resource (`method` + `resourcePath`) defined in token scope.\n  The entire `resourcePath` must match exactly.\n\n- They are long-lived. By default, the same expiry as the refresh token is used,\n  but we can optionally restrict this to a _shorter_ lifetime by introducing\n  a `maxLifetimeSeconds` client query parameter or header in the future if there is a need.\n\n- The server has an allowlist of permitted scopes. Attempts to get a scoped token for any\n  other resource will fail. This is the initial set:\n   - `GET /api/assets/teams/:id/:id`","mergeCommitSha":"5d8728e1082835557240502bcb66ca8f4c599cd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3607","title":"Introduce scoped auth token for long-lived resource access","createdAt":"2022-11-08T20:30:50Z"}
{"state":"Merged","mergedAt":"2022-11-09T00:41:02Z","number":3608,"body":"API breaking is a non-event, I just removed two HTTP methods that are irrelevant for APIs.","mergeCommitSha":"9807f91d1df4abcc2a70576da3d466a9917d48a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3608","title":"[BREAKS API ON MAIN] Implement vending of scope token","createdAt":"2022-11-08T21:30:46Z"}
{"state":"Merged","mergedAt":"2022-11-08T21:57:56Z","number":3609,"body":"When both fields are optional, no filtering on commit hashes or thread IDs will be applied.","mergeCommitSha":"d1b01512b3fcf90e518ab1847f242e4fcb1d3029","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3609","title":"[BREAKS API ON MAIN] Make TopicRelatedInsightsRequest fields optional","createdAt":"2022-11-08T21:32:53Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:43:40Z","number":361,"body":"Webviews in VSCode are meant to be transient -- when you click on another tab, your webview is supposed to be destroyed, because web views are expensive and you can have a lot of tabs open.  Because webviews can have state (transient view-related state), VSCode provides an API for storing and restoring state as views are destroyed and recreated: https://code.visualstudio.com/api/extension-guides/webview#persistence\r\n\r\nThis PR provides a drop-in replacement for `useState`: `useWebviewState(key, initialValue)`.  The key is a string that uniquely identifies the value within that webview.  As the state is modified, it is written to the Webview's state object.  When the webview is rendered, we pull values as needed from the state object.\r\n\r\nThe state object itself is a `Record<string, JsonValue>` -- that is, maps the state keys to the state values.  One key is special (`props`), that is used to store the last-rendered view props.  When the tab is re-focused, the result is a full set of both permanent view props and transient webview state, so the view can be rendered.\r\n\r\nSome caveats:\r\n\r\n* Only state that is JSON-serializable can be stored this way, since ultimately VSCode stores this in JSON.  So, no classes, no Maps, no Sets.  Plain typed objects and primitives.\r\n* The key is required because otherwise there is no reliable way to map state values back to state usages when deserializing.  I looked at whether React provides facilities for hooks for this, but didn't find anything promising.\r\n* There is one final piece to this puzzle: for webview panels, we need to implement a `vscode.WebviewPanelSerializer`, which will allow webview panels to re-open correctly when VSCode restarts.","mergeCommitSha":"e2d48476a981dfa4721394d6fd644aaef20e44b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/361","title":"Add VSCode Webview state API","createdAt":"2022-02-16T04:50:59Z"}
{"state":"Merged","mergedAt":"2022-11-15T19:28:35Z","number":3610,"body":"On the chance that the first paragraph of a given thread is very long, we should clip the thread title string in the tooltip to be at a legible length. \r\n\r\nbefore:\r\n<img width=\"768\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/200679684-8435a71e-3caa-486b-a6ed-71586a234363.png\">\r\n\r\nafter:\r\n<img width=\"768\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/200679868-ac7f1b7f-2798-4d7b-9ad0-5cb03c8dc922.png\">\r\n","mergeCommitSha":"3aa63445faaa517ce252d2992944305b9d7514cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3610","title":"Clip long thread title in tooltip ","createdAt":"2022-11-08T21:35:15Z"}
{"state":"Merged","mergedAt":"2022-11-08T22:20:50Z","number":3611,"body":"Add capability flag for the topics/insights UI(s) in the web dashboard","mergeCommitSha":"1434543663dc3771d050fe359104f8fd5496a16b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3611","title":"Add kotlin flag for dashboard topics UI","createdAt":"2022-11-08T21:56:46Z"}
{"state":"Merged","mergedAt":"2022-11-08T23:02:33Z","number":3612,"mergeCommitSha":"82b72ff16981a04002bb0dd557de1ec9da199aed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3612","title":"Filter topic search results by commit hashes and thread ids","createdAt":"2022-11-08T22:45:24Z"}
{"state":"Merged","mergedAt":"2022-11-09T01:15:16Z","number":3613,"body":"Forgot that GET can't take a request body.","mergeCommitSha":"7eccf52c0f23d05e705644700ed0ca1ed51819f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3613","title":"[BREAKS API ON MAIN] Change getScopedAccess to POST","createdAt":"2022-11-09T01:02:27Z"}
{"state":"Merged","mergedAt":"2022-11-09T05:56:05Z","number":3614,"body":"When uploading optional booleans, `false` is equivalent to not sending a field.\nAdds up when we send 100,000s of points.","mergeCommitSha":"7d3a76ff57feb4e925f83cab18de9b29b0938975","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3614","title":"Optimize point upload payload","createdAt":"2022-11-09T05:35:30Z"}
{"state":"Merged","mergedAt":"2022-11-10T00:33:18Z","number":3615,"body":"We implemented the video assets in a similar manner to image assets. The plan was to use `useAssetUrl` to auth the GET request. This used a helper to download the entire asset at once (e.g. image) with an auth token and then created an objectURL.\r\nThis was okay for images since we needed to download the entire image before displaying the image.\r\nThis is not the case for video… We do *not* want to download the entire video before the user can watch.\r\n\r\nTherefore, we need to use a streaming urls. This led to another problem where these streaming urls were only valid for 1 minute as they used our standard Auth Tokens. Video media requests (which used byte-range to stream) longer than 1 minute would eventually 401, causing the video to stop loading.\r\n\r\nTherefore, scoped auth tokens (https://github.com/NextChapterSoftware/unblocked/pull/3607) were introduced so we could utilize long lived auth tokens (7 days) and watch long videos :) \r\n\r\n* Parses messages and generates scoped auth tokens for each video metadata resource\r\n* Scoped auth tokens are injected into VideoBlockRenderer using contexts (similar to auth / team members)","mergeCommitSha":"36692bcf5417bd500bbb8bcbac6df208f836b281","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3615","title":"Scoped Authed video assets","createdAt":"2022-11-09T06:55:20Z"}
{"state":"Merged","mergedAt":"2022-11-09T18:34:30Z","number":3616,"mergeCommitSha":"c792bce4a5d9899626d241aa5bcf5c619b8d91e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3616","title":"[BREAKS API ON MAIN] Cleanup api","createdAt":"2022-11-09T16:09:49Z"}
{"state":"Merged","mergedAt":"2022-11-10T17:21:48Z","number":3617,"body":"Before\r\n<img width=\"1077\" alt=\"CleanShot 2022-11-09 at 09 57 55@2x\" src=\"https://user-images.githubusercontent.com/1553313/200905594-2f25a937-d74e-4f02-a0b1-530dda7a73ee.png\">\r\n\r\nAfter\r\n<img width=\"1177\" alt=\"CleanShot 2022-11-09 at 09 58 35@2x\" src=\"https://user-images.githubusercontent.com/1553313/200905602-1c97a74a-0cf4-4eb1-9b95-7cc60f5f9e91.png\">\r\n","mergeCommitSha":"a6177a0e9d38bedf71688bd41f6f92cc88d50c32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3617","title":"Fix styling","createdAt":"2022-11-09T18:00:47Z"}
{"state":"Merged","mergedAt":"2022-11-09T19:07:48Z","number":3618,"mergeCommitSha":"b8fd4ffc00ead4a9be5ff3cb558882d9a6d63e36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3618","title":"Minor cleanup","createdAt":"2022-11-09T18:26:22Z"}
{"state":"Merged","mergedAt":"2022-11-09T21:22:21Z","number":3619,"body":"<img width=\"613\" alt=\"CleanShot 2022-11-09 at 11 01 24@2x\" src=\"https://user-images.githubusercontent.com/1553313/200917993-73bbfcc7-47d9-4be4-b508-6eaad0f45280.png\">\r\n\r\nAdd walkthrough specific icon.\r\n\r\nUpdate description to be based off Marks instead of deprecated sourceMarks.","mergeCommitSha":"0c289db89d7f82b5acdaebb4073136e3d05e1f8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3619","title":"Update xcode walkthrough icon","createdAt":"2022-11-09T19:02:20Z"}
{"state":"Merged","mergedAt":"2022-02-16T18:27:04Z","number":362,"body":"This pr addresses a fine point of gradle tasks.\r\n\r\nIf you create a gradle task that is not defined as a specific type i.e. task(type: Delete), it will always run.\r\n\r\nAlso cleaning up other stuff.","mergeCommitSha":"4403a05918588c37b46e481e2b02380b51859299","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/362","title":"Fix issues with openAipClean running always","createdAt":"2022-02-16T18:21:13Z"}
{"state":"Merged","mergedAt":"2022-11-10T18:15:32Z","number":3620,"body":"Part two of https://github.com/NextChapterSoftware/unblocked/pull/3592\r\n\r\nI've tested this locally and it dropped the table without issues. Once merged I'll do this in dev first and verify there are no issues before running in prod.","mergeCommitSha":"9adef99082d655d1d0b65ecec9d3ff853d3c2407","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3620","title":"Create migration to drop ThreadSearchModel table from the database","createdAt":"2022-11-09T19:10:11Z"}
{"state":"Merged","mergedAt":"2022-11-09T19:50:39Z","number":3621,"body":"- ADd transcriptoin versioning\r\n- Update\r\n","mergeCommitSha":"753a73e37bfb6ea9023283035951960493443bf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3621","title":"TranscriptionModelChanges","createdAt":"2022-11-09T19:32:49Z"}
{"state":"Merged","mergedAt":"2022-11-16T00:17:18Z","number":3622,"body":"Need formatting...\r\n\r\nCan wait until Peter comes back to verify everything okay.","mergeCommitSha":"39939902ae24be57374136d4ba8cdb09ea41ff40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3622","title":"Swift Format","createdAt":"2022-11-09T22:26:22Z"}
{"state":"Merged","mergedAt":"2022-11-09T23:27:03Z","number":3623,"body":"Ignore certain apps from being captured.\r\n\r\nMain ones are VSCode (and its cousins) as file marks are already being captured by the extension.","mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3623","title":"Filter apps from being monitored","createdAt":"2022-11-09T22:43:09Z"}
{"state":"Merged","mergedAt":"2022-11-10T04:59:28Z","number":3624,"body":"Still tuning this, but want to test something. \r\n\r\nBasically this creates a new stop word list by creating \"gowords\" that are most likely to be use in a developer project. \r\n\r\nThis list is generated from word count of the entire unblocked db including partners (see the primary unblocked notebook) and then hand tuned for now.\r\n\r\nThis passes the local build test, but I am not too sure how to run this locally yet? Going to take a stab at that now.\r\n\r\nBasically you run dictionary.ipynb to get a new client/EnglishTopWords.json + service/english_top_words.txt.","mergeCommitSha":"339d4d2a4abc2f70f2a323dd1360a139a07a09aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3624","title":"New stop word list","createdAt":"2022-11-09T22:52:09Z"}
{"state":"Merged","mergedAt":"2022-11-10T20:41:49Z","number":3625,"body":"Add topics to the explorer insights panel.\r\n\r\n* Add `TopicStore`, which is a store that fetches and caches the topics for a team\r\n* Refactor `ExplorerInsightsWebviewProvider` to allow sourcing from unfiltered and filtered data\r\n* Update UI panel to display topics in pills\r\n\r\nNote: right now the topic data is *not* filtered by the currently-selected file, that will come in the next PR.\r\n\r\n<img width=\"338\" alt=\"Screen Shot 2022-11-09 at 3 01 40 PM\" src=\"https://user-images.githubusercontent.com/2133518/200960585-24e45474-b4c1-4819-b8be-f4f1d19d1900.png\">\r\n","mergeCommitSha":"f553742242ddd16cfd154c3dd0ae70c5efbc267f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3625","title":"Add topics to explorer insights UI","createdAt":"2022-11-09T22:59:24Z"}
{"state":"Merged","mergedAt":"2022-11-09T23:57:11Z","number":3626,"body":"The idea here is to use this function to extract 1-gram and 2-grams from a list of filepaths provided by the client.\r\n\r\nFor example, the client would send to the API service (via the `getRecommendedTopics` operation) a list of file paths like:\r\n\r\n```\r\n[\r\n    \"api/service/module\",\r\n    \"test/SearchService\",\r\n]\r\n```\r\n\r\nand the function would produce\r\n\r\n```\r\n[\r\n    \"api\",\r\n    \"service\",\r\n    \"module\",\r\n    \"test\",\r\n    \"search\",\r\n    \"api service\",\r\n    \"service module\",\r\n    \"test search\",\r\n    \"search service\",\r\n]\r\n```\r\n\r\nwhich we would then score against the histogram generated from PR and slack data before returning to the client as recommended topics.\r\n\r\nWe still need to update our histogram generation logic to extract 2-grams from PR and slack data (to come in a separate PR).","mergeCommitSha":"d56d86230c2f396a39f8e2869729f7037380d91f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3626","title":"Create extractUnigramsAndBigrams function","createdAt":"2022-11-09T23:03:28Z"}
{"state":"Merged","mergedAt":"2022-11-10T04:09:37Z","number":3627,"mergeCommitSha":"308f86d1d772b377a0e1756e27cdd00de89b6ec1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3627","title":"Add transcription backend","createdAt":"2022-11-10T01:38:43Z"}
{"state":"Merged","mergedAt":"2022-11-10T02:50:30Z","number":3628,"mergeCommitSha":"6ebae2336763aa73465093ed8a6befcc563dea42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3628","title":"Add asset helpers","createdAt":"2022-11-10T02:36:53Z"}
{"state":"Merged","mergedAt":"2022-11-10T16:11:17Z","number":3629,"mergeCommitSha":"c76f1feb7bee1627d3245a87fa99d1a929f0fe74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3629","title":"Make my life easier","createdAt":"2022-11-10T16:03:34Z"}
{"state":"Merged","mergedAt":"2022-02-16T19:00:07Z","number":363,"body":"We want to run zallyLint against multiple specs (private.yml and message.yml in jeff's pr)","mergeCommitSha":"4e59f5b9a3e388d5f22877a126a4c1ea6c4d9e47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/363","title":"Fix up zallyLint","createdAt":"2022-02-16T18:45:59Z"}
{"state":"Merged","mergedAt":"2022-11-10T16:13:55Z","number":3630,"mergeCommitSha":"2e69bba31ebcb71555cf1c3d414f2da816a3e6a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3630","title":"update","createdAt":"2022-11-10T16:13:48Z"}
{"state":"Merged","mergedAt":"2022-11-11T00:38:46Z","number":3631,"mergeCommitSha":"dfc64c34f80ae9d069629f2cc92de843603ef54d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3631","title":"Refactor sourcemark module to shared for shim app reuse","createdAt":"2022-11-10T18:43:15Z"}
{"state":"Merged","mergedAt":"2022-11-10T18:57:42Z","number":3632,"body":"- update\r\n- update\r\n","mergeCommitSha":"4bec550f8e942cd1ff1df86f2b258397397b2433","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3632","title":"AddServiceDocs","createdAt":"2022-11-10T18:45:37Z"}
{"state":"Merged","mergedAt":"2022-11-10T23:02:01Z","number":3633,"body":"* Filter the set of displayed topics so only relevant topics for the current file are displayed\r\n* When using a topic filter, only show the set of insights that are relevant to the current file","mergeCommitSha":"26aeeedf964c36423a3ad98dd7f3190f8c19dd2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3633","title":"Show per-file, topic-filtered content in explorer insights UI","createdAt":"2022-11-10T21:00:38Z"}
{"state":"Merged","mergedAt":"2022-11-10T21:28:34Z","number":3634,"mergeCommitSha":"fa20a4629530745b4d11eb9ac2f227852e2a2269","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3634","title":"Add transcription tests","createdAt":"2022-11-10T21:10:27Z"}
{"state":"Merged","mergedAt":"2022-11-10T22:33:22Z","number":3635,"mergeCommitSha":"925c2946f782b1f8439d6db94a36a9f0dc461a71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3635","title":"Enable extractUnigramsAndBigrams function","createdAt":"2022-11-10T21:23:31Z"}
{"state":"Merged","mergedAt":"2022-11-10T21:45:06Z","number":3636,"mergeCommitSha":"dab80622d4deaf9ace757e7f5ad24312f99d3feb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3636","title":"Fix minor admin error","createdAt":"2022-11-10T21:44:45Z"}
{"state":"Merged","mergedAt":"2022-11-10T21:58:02Z","number":3637,"mergeCommitSha":"c05b24ebfaf9a9bd72630734d3bc85c7350be013","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3637","title":"Asset info","createdAt":"2022-11-10T21:52:40Z"}
{"state":"Merged","mergedAt":"2022-11-10T23:17:55Z","number":3638,"body":"First pr related to topic service.\r\nnext pr is to add ci deployment.","mergeCommitSha":"b53abcfe0ecf954e71bd12436b4a6ab724bf930c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3638","title":"add topic service","createdAt":"2022-11-10T22:26:51Z"}
{"state":"Merged","mergedAt":"2022-11-10T23:19:47Z","number":3639,"mergeCommitSha":"5c448c1160ed805ee1da776eacdcea3540a4224e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3639","title":"Deploy tpoic service","createdAt":"2022-11-10T23:19:23Z"}
{"state":"Merged","mergedAt":"2022-02-16T19:32:43Z","number":364,"body":"Haven't tested this works","mergeCommitSha":"70973109ecd7861fbc5e16934dad08e82c6c5d47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/364","title":"Create repo in runtime fixtures","createdAt":"2022-02-16T18:53:54Z"}
{"state":"Merged","mergedAt":"2022-11-10T23:52:22Z","number":3640,"body":"…abase (#3620)\"\r\n\r\nThis reverts commit 9adef99082d655d1d0b65ecec9d3ff853d3c2407.","mergeCommitSha":"b667de6030a40fa55616f10965333d30543df55d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3640","title":"Revert \"Create migration to drop ThreadSearchModel table from the dat…","createdAt":"2022-11-10T23:26:36Z"}
{"state":"Merged","mergedAt":"2022-11-10T23:29:27Z","number":3641,"mergeCommitSha":"3be155968e90dfa293b9aaed83c00289d1ac7f34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3641","title":"update","createdAt":"2022-11-10T23:29:16Z"}
{"state":"Closed","mergedAt":null,"number":3642,"body":"- Fix annotation\r\n- update\r\n","mergeCommitSha":"21422d7d083add8d427b6992f9c94bdd14b4d7a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3642","title":"FixAnnotation","createdAt":"2022-11-10T23:54:31Z"}
{"state":"Merged","mergedAt":"2022-11-15T18:02:41Z","number":3643,"body":"Based on VSCode implementation:\r\nhttps://github.com/microsoft/vscode/blob/4f30fd5a83b31b86d4e141fe38c1cd27062d01d7/extensions/git/src/git.ts#L66-L165\r\n\r\n\r\n#### Note\r\nTested ok on macOS; did not test on Windows.\r\n\r\n\r\n#### Related\r\nRelated to, but does not fix, this:\r\nhttps://linear.app/unblocked/issue/UNB-528/vscode-sidebar-fails-to-load-when-xcodes-licence-agreement-needs-to-be","mergeCommitSha":"ee82cf5857ac0607acbcbd4d364b7f987b61c2bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3643","title":"Utility to find Git on Mac and Windows","createdAt":"2022-11-11T00:16:30Z"}
{"state":"Merged","mergedAt":"2022-11-11T00:57:08Z","number":3644,"mergeCommitSha":"81e947365db8ba9ae3781fca045733a9d64ebff9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3644","title":"Only remove separators for API call","createdAt":"2022-11-11T00:44:56Z"}
{"state":"Closed","mergedAt":null,"number":3645,"mergeCommitSha":"686050e3a5e40d15da9df93c634da3209afc1ec1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3645","title":"Ran npm audit fix","createdAt":"2022-11-11T00:49:16Z"}
{"state":"Merged","mergedAt":"2022-11-14T17:28:09Z","number":3646,"body":"Our previous stopwords were generated via len(all_unblocked_db_text). This set of stopwords is generate from 10hr of BERT time over the 290,000 PR title+descriptions and the ratings.","mergeCommitSha":"35598ac9e38b2a67ec6d7c048fa728ed50a12240","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3646","title":"Generated stopwords using ratings + initial BERT training","createdAt":"2022-11-11T03:14:49Z"}
{"state":"Merged","mergedAt":"2022-11-14T18:05:54Z","number":3647,"body":"- Bad plural for meadata\r\n- Fix build\r\n","mergeCommitSha":"a31381b643be8c90db6c325b90338ab924321526","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3647","title":"FixPlurals","createdAt":"2022-11-14T17:55:06Z"}
{"state":"Merged","mergedAt":"2022-11-14T18:51:27Z","number":3648,"mergeCommitSha":"1ab41fdaed84e844b7ae57ef4efd296cdcb6ea40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3648","title":"Move transcription keys out of global config","createdAt":"2022-11-14T18:30:11Z"}
{"state":"Merged","mergedAt":"2022-11-14T19:45:36Z","number":3649,"body":"This is a temporary measure for now -- because we don't have a good way of generating the set of topics relevant to a file, we will show the top 20 topics for the team.","mergeCommitSha":"46bde1c16d166651b4a92e14424ae68f53af9fdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3649","title":"Show all topics for team in explorer insights UI","createdAt":"2022-11-14T19:25:48Z"}
{"state":"Merged","mergedAt":"2022-02-16T19:17:26Z","number":365,"body":"For the lovely matt","mergeCommitSha":"088eb98fa4cad8767bd70b81557acf99264d0e2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/365","title":"Allow getting all threads","createdAt":"2022-02-16T19:07:07Z"}
{"state":"Merged","mergedAt":"2022-11-14T20:22:27Z","number":3650,"body":"- Add the new queue for topic_recommendations \r\n- Modify API service EKS/IAM permissions to allow publishing messages to the new queue \r\n- Modify Topic service EKS/IAM permissions to consuming messages from queue. \r\n\r\nOnce this change is deployed via CI/CD I still need to deploy the EKS permissions manually. ","mergeCommitSha":"cc01d9b275253b3fb31e0ae65d9fa45246e8fb03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3650","title":"added new queue and queue perms","createdAt":"2022-11-14T20:13:10Z"}
{"state":"Merged","mergedAt":"2022-11-14T20:36:44Z","number":3651,"body":"Just refactoring, no behaviour change.","mergeCommitSha":"5cd0d2dc1faa0a4a1e3cca1d6f3ee0d0e45fb398","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3651","title":"Moves the sourcemark interface down to shared layer for reuse","createdAt":"2022-11-14T20:13:31Z"}
{"state":"Merged","mergedAt":"2022-11-14T20:33:42Z","number":3652,"body":"We now are using ansible template functionality to generate a kotlin scaffolding for a new service.\r\nMuch less error prone.\r\n\r\nAs richie once said to me, do it right once, do it right forever.","mergeCommitSha":"f699f499bb711a37049f98875c451dc073cfe84e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3652","title":"Add basic scaffolding for service generation","createdAt":"2022-11-14T20:22:15Z"}
{"state":"Merged","mergedAt":"2022-11-14T20:47:44Z","number":3653,"body":"This CSS property is only supported in Chrome, but since VSCode only uses Chrome it's fine.  I didn't modify anything outside of the VSCode UI.","mergeCommitSha":"f78dac653cc4390c7cb15c7152339f0f28b52f70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3653","title":"VSCode scrollbars overlay content","createdAt":"2022-11-14T20:24:12Z"}
{"state":"Merged","mergedAt":"2022-11-14T21:10:03Z","number":3654,"mergeCommitSha":"4f57eff37b4a33428e8d068280b2288a9cf9cd69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3654","title":"Update queues","createdAt":"2022-11-14T21:06:18Z"}
{"state":"Merged","mergedAt":"2022-11-14T22:16:17Z","number":3655,"mergeCommitSha":"86d36deeaa7fd851934119ff0841ca89ab2ce65b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3655","title":"Local stack support hub","createdAt":"2022-11-14T21:47:02Z"}
{"state":"Merged","mergedAt":"2022-11-14T23:25:18Z","number":3656,"mergeCommitSha":"226a21938be12a90d5f2d1f1d6c454d487800bee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3656","title":"Add make targets","createdAt":"2022-11-14T22:47:47Z"}
{"state":"Merged","mergedAt":"2022-11-15T19:41:17Z","number":3657,"body":"This reverts commit f78dac653cc4390c7cb15c7152339f0f28b52f70.","mergeCommitSha":"155cc9e2376e26c424e755f5ec0298772186ff5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3657","title":"Revert \"VSCode scrollbars overlay content (#3653)\"","createdAt":"2022-11-14T23:03:18Z"}
{"state":"Merged","mergedAt":"2022-11-14T23:49:12Z","number":3658,"mergeCommitSha":"9582513d3cb7fb3fb2f0e763228d94df7044a970","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3658","title":"Return PR comment counts in getTopicRelatedInsights operation","createdAt":"2022-11-14T23:34:08Z"}
{"state":"Merged","mergedAt":"2022-11-15T01:04:03Z","number":3659,"mergeCommitSha":"084da729f0d65cbb3671ee3e3b23c521e3bcd3d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3659","title":"getRankedTopics does not count frequencies in input","createdAt":"2022-11-15T00:31:53Z"}
{"state":"Merged","mergedAt":"2022-11-15T01:11:25Z","number":3660,"body":"- Remove the old commands for extracting topics with only local data, or only PR or slack data.  We will always use all data we have.\r\n- Add command to extract topics including bigrams, which returns the full folder set, unparsed and unfiltered.  The only folders we filter are `.git` and `node_modules`.","mergeCommitSha":"c07cead73c03b97bd7eac7976bf708843edf9537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3660","title":"VSCode 'Extract topics with bigrams' command","createdAt":"2022-11-15T00:39:22Z"}
{"state":"Merged","mergedAt":"2022-11-15T18:27:21Z","number":3661,"body":"A node app that runs a headless version of the sourcemark agent.\n\nSeveral things are missing for this to actually be useful:\n\n- auth\n- datastore for consuming SM API content\n- RPC for interfacing with VSCode","mergeCommitSha":"43b97023b5a959e121536d8c5f13db3a620a8445","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3661","title":"Headless sourcemark agent","createdAt":"2022-11-15T01:38:51Z"}
{"state":"Merged","mergedAt":"2022-11-15T05:31:04Z","number":3662,"body":"Key was always:\n```js\n'sourcemarks.[object Object].[object Object]'\n```\n\nNot fully sure what the impact was, but definitely a bug.","mergeCommitSha":"b7648c4fd21d4848650acfe25543b3eec79c20c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3662","title":"Fix bug in SM store cache","createdAt":"2022-11-15T02:42:16Z"}
{"state":"Merged","mergedAt":"2022-11-15T22:21:51Z","number":3663,"body":"Bundling this with the VSCode CI job for now as it seems pretty wasteful to create a separate CI pipeline for this.\r\n\r\nEventually, we'll definitely have a separate CI pipeline.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-742/headless-source-agent-ci","mergeCommitSha":"8033a69b0a94697ff8fa42b714f0bc5a565ec3f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3663","title":"Headless source agent: CI","createdAt":"2022-11-15T19:27:15Z"}
{"state":"Merged","mergedAt":"2022-11-15T21:34:43Z","number":3664,"body":"<img width=\"531\" alt=\"CleanShot 2022-11-15 at 13 27 46@2x\" src=\"https://user-images.githubusercontent.com/858772/202028873-c451a9cd-21c9-447d-b872-049d6ab860cb.png\">\r\n","mergeCommitSha":"b8cef9d54324db8eeaed8954d1431ecb79818aa6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3664","title":"Add app version to settings menu","createdAt":"2022-11-15T21:27:08Z"}
{"state":"Merged","mergedAt":"2022-11-16T17:49:38Z","number":3665,"body":"For VSCode:\r\n1. We've added sentry transport to logger\r\n2. We've added sentry to catch unhandled node exceptions\r\n\r\nFor Web:\r\n1. We've added sentry to catch unhandled exceptions in React.\r\n\r\nTODO:\r\n1. Add to extension.\r\n2. Figure out if we can log to sentry via transport in web world.\r\n3. Write a doc about when to use Sentry vs Logz.io vs Honeycomb.","mergeCommitSha":"80a6e1ead87ca316d6bec155d179f38c58eec754","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3665","title":"Add Sentry to clients","createdAt":"2022-11-15T21:31:02Z"}
{"state":"Merged","mergedAt":"2022-11-15T22:35:01Z","number":3666,"body":"We're removing the topic pills for now.\r\nAlso, fix a bug where bad sourcemark data caused duplicate thread entries in the view, which broke things.","mergeCommitSha":"cc8567300461ca6baf9df5bd19e9223826072bef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3666","title":"Remove topics from explorer insights panel","createdAt":"2022-11-15T22:12:33Z"}
{"state":"Merged","mergedAt":"2022-11-15T23:26:19Z","number":3667,"body":"For a given file we want to show the list of related topics, which will be clickable in VS Code and bounce us to the dashboard to show all insights for a topic.\r\n\r\nThis is not the final implementation. Next PR will take `TopicRelatedInsightsRequest` and filter the returned topics by the thread IDs and commit hashes provided by the client.","mergeCommitSha":"4f0c343ee2f5f590215dd29ea6c6a76a86bb2fe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3667","title":"Add getRelatedTopics operation","createdAt":"2022-11-15T22:22:44Z"}
{"state":"Merged","mergedAt":"2022-11-15T22:53:54Z","number":3668,"mergeCommitSha":"587c6e88e42174f66be93109b5f67e344cd93fd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3668","title":"Add messages page","createdAt":"2022-11-15T22:46:49Z"}
{"state":"Merged","mergedAt":"2022-11-15T23:38:55Z","number":3669,"mergeCommitSha":"e3e03c1d646b3d1bb2c6102345c3f7fa0bf0f68e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3669","title":"Fix for window observability issues resulting in missed slurps","createdAt":"2022-11-15T22:59:16Z"}
{"state":"Merged","mergedAt":"2022-02-16T21:13:32Z","number":367,"mergeCommitSha":"d2107d290809abc794e2a675cc7cae3db4f55ead","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/367","title":"Make If-Modified-Since header optional for getThreads","createdAt":"2022-02-16T21:06:54Z"}
{"state":"Merged","mergedAt":"2022-11-16T22:52:15Z","number":3670,"mergeCommitSha":"eb38a9248997251d953dc2c938fd9d5e8f15f6a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3670","title":"Add models to map threads and pull requests to topics","createdAt":"2022-11-16T00:37:07Z"}
{"state":"Merged","mergedAt":"2022-11-16T19:04:22Z","number":3671,"body":"The options from the Transport were not applied to winston when extended by ThrottlingTransport.\n\nImpact was that we were _always_ sending debug logs from VSCode to the LogzIO transport,\neven though the LogzIO transport options said otherwise (`level: 'info'`).","mergeCommitSha":"14d4e1f0bd7d5c11fdda0498844be67ad6287f1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3671","title":"Fix inheritance bug in ThrottlingTransport","createdAt":"2022-11-16T08:29:12Z"}
{"state":"Merged","mergedAt":"2022-11-16T22:36:25Z","number":3672,"body":"https://linear.app/unblocked/issue/UNB-745/headless-source-agent-integrate-with-store-to-access-cloud-sourcemarks","mergeCommitSha":"c7194c865b2d29302fd2846983d9a5e34e011668","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3672","title":"Headless source agent: integrate with store to access cloud sourcemarks","createdAt":"2022-11-16T08:34:52Z"}
{"state":"Merged","mergedAt":"2022-11-16T18:52:53Z","number":3673,"mergeCommitSha":"09a44c6e04e8b8efcfda3665d7a77da21fd7a73b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3673","title":"Revert stop word dictionary and only ignore stop words for unigrams","createdAt":"2022-11-16T18:27:50Z"}
{"state":"Merged","mergedAt":"2022-11-16T19:29:04Z","number":3674,"mergeCommitSha":"cd16f4e66e52f21fedd8cd0e4fb73f35883b2385","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3674","title":"[BREAKS API ON MAIN] Add additional bools for video metadata","createdAt":"2022-11-16T18:29:55Z"}
{"state":"Merged","mergedAt":"2022-11-16T19:14:43Z","number":3675,"mergeCommitSha":"5937ce2cb662720f4c1ae990d270fabea27a2089","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3675","title":"Add audio and camera metadata to walkthrough GRPC session","createdAt":"2022-11-16T19:01:57Z"}
{"state":"Merged","mergedAt":"2022-11-16T19:36:33Z","number":3676,"mergeCommitSha":"2d87d3b2833b48bcc8d871b6e2bafea6b3dda93e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3676","title":"Helm docs","createdAt":"2022-11-16T19:36:02Z"}
{"state":"Merged","mergedAt":"2022-11-16T19:40:41Z","number":3677,"mergeCommitSha":"9872266ffac1b7ff6d61cb2d8a953d32d7593c4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3677","title":"more helm doc changes","createdAt":"2022-11-16T19:40:14Z"}
{"state":"Merged","mergedAt":"2022-11-16T20:41:54Z","number":3678,"mergeCommitSha":"339ab80822d71bfeefd6f615fa98c54ff5d1f134","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3678","title":"Add transcription is processing","createdAt":"2022-11-16T20:30:11Z"}
{"state":"Merged","mergedAt":"2022-11-16T21:24:50Z","number":3679,"mergeCommitSha":"7ef302933f937d2458abc65aeeedc654c8bfc984","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3679","title":"Combine unigram topic with its bigram version","createdAt":"2022-11-16T21:16:08Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:57:49Z","number":368,"body":"This pr adds the basic ability to indent treeItems.\r\nThe approach is a dynamic generator for paddings based off a maximum number of columns allowed.\r\n\r\n<img width=\"794\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/154369337-137e47bf-c1b6-44f5-8545-67f8343674aa.png\">\r\n","mergeCommitSha":"10dd06cb00b2784748abb91030669cc459f4fd6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/368","title":"Add indentation logic to treeView","createdAt":"2022-02-16T22:35:58Z"}
{"state":"Merged","mergedAt":"2022-11-16T21:32:29Z","number":3680,"mergeCommitSha":"300cff6ba8aa7632beff68ade5810e156623a07e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3680","title":"Escalate accessibility logging to error level","createdAt":"2022-11-16T21:22:37Z"}
{"state":"Merged","mergedAt":"2022-11-16T21:35:52Z","number":3681,"body":"Resolves UNB-751\r\n\r\nTested that this works. A previous change I made allows for this. Previously it would boot the mic/camera stream prematurely and crash.","mergeCommitSha":"d2c2000a8cc6b5c02914a46d97fbe778fc13f89b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3681","title":"Allow mic and camera selection even when disabled","createdAt":"2022-11-16T21:25:23Z"}
{"state":"Merged","mergedAt":"2022-11-16T22:49:30Z","number":3682,"body":"As my compatriot Matt Adam has suggested, we’re migration sentry transporter to use @sentry/browser.\r\nConfirmed it works for both web and vscode\r\n\r\nThe old winston sentry transporters were node-specific and did not work in non-node environments without a lot of pain.","mergeCommitSha":"4900e8aa934eea8aaacadc28e60d9d6055d717aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3682","title":"Add custom sentry transporter fro browser","createdAt":"2022-11-16T22:26:43Z"}
{"state":"Merged","mergedAt":"2022-11-16T22:35:34Z","number":3683,"mergeCommitSha":"719b578c1a511c8bf1cd6648d2b0cb6611f24641","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3683","title":"Increase Slurp observability retries","createdAt":"2022-11-16T22:35:04Z"}
{"state":"Merged","mergedAt":"2022-11-16T23:26:59Z","number":3684,"body":"This makes it reusable","mergeCommitSha":"78f828f590a2a60ee8240600e0882a44af4651c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3684","title":"Move getTopics logic to getRelevantTopics","createdAt":"2022-11-16T23:12:23Z"}
{"state":"Merged","mergedAt":"2022-11-17T19:29:38Z","number":3685,"body":"Display per-file topic pills at the bottom of the explorer insights UI.  This queries the `getRelatedTopics` API for each file and displays the resulting topics in the UI.  Note: these are not actually hooked up yet -- once Kay gets the topic pages done we can hook these up.\r\n\r\nNext PR will add the fade-out and scrolling buttons on the edges of the topic pill windows, when the topic pills overflow the content space.\r\n\r\n<img width=\"953\" alt=\"Screen Shot 2022-11-16 at 3 14 46 PM\" src=\"https://user-images.githubusercontent.com/2133518/202315100-3712bb33-eaf9-4a23-8f92-0bc434c7bf81.png\">\r\n","mergeCommitSha":"74fb55a2ff378aa49396d07fea2e3c72c56812d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3685","title":"Add per-file topic pills in explorer insight UI","createdAt":"2022-11-16T23:16:08Z"}
{"state":"Merged","mergedAt":"2022-11-16T23:42:07Z","number":3686,"body":"They didn't document this, but found an error cause field in their apis.","mergeCommitSha":"6e52b25a371677216c26cf2ea9da7662491ca874","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3686","title":"[BREAKS API ON MAIN] Add transcription error cause","createdAt":"2022-11-16T23:18:57Z"}
{"state":"Merged","mergedAt":"2022-11-16T23:52:17Z","number":3687,"mergeCommitSha":"7d923752efe68a6f126a32f2ca5a48223f51cf27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3687","title":"Source agent will fully recalculate a repo then die","createdAt":"2022-11-16T23:20:36Z"}
{"state":"Merged","mergedAt":"2022-11-16T23:57:14Z","number":3688,"mergeCommitSha":"71f8dd2e201e7a674a7706ec45a63d780bc6df1d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3688","title":"Update english_top_words.txt","createdAt":"2022-11-16T23:39:09Z"}
{"state":"Merged","mergedAt":"2022-11-16T23:59:11Z","number":3689,"body":"There are some timing issues with accessibility that can't be resolved with the usual main queue dance. This PR adds some additional timing tricks, and pulls the \"title\" field from the root element (window) instead of the web element. Safari is an example where titles are not \"slurpable\" from the web element.","mergeCommitSha":"c8d0d6d0d91c17c6eb139aaa5976d5807f760278","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3689","title":"Improve slurping reliability and use window title instead of web element title","createdAt":"2022-11-16T23:49:58Z"}
{"state":"Closed","mergedAt":null,"number":369,"body":"Matt pointed out that we could remove the POST operations for creating threads and messages, and instead do them through PUTs (which would also be used for updates). \r\n\r\nThe service would create or update based the presence of certain properties in the request body. For example, if the `PutThreadRequest` has a message, then we'd attempt to create, otherwise we'd just do an update.\r\n\r\nThoughts?","mergeCommitSha":"0545fee5abb3aeea395c88f94d1db4449ba11146","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/369","title":"[RFC] Remove POSTs and do creates with PUTs","createdAt":"2022-02-16T22:54:09Z"}
{"state":"Merged","mergedAt":"2022-11-17T00:29:11Z","number":3690,"mergeCommitSha":"663fc75eda2c360ca02aa21a2c89483f93f4ae4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3690","title":"Codify retry pattern for slurping","createdAt":"2022-11-17T00:21:18Z"}
{"state":"Merged","mergedAt":"2022-11-17T01:41:26Z","number":3691,"mergeCommitSha":"10933df137608c66034ea007946e071a29b8fb4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3691","title":"Not returning error cause correctly","createdAt":"2022-11-17T01:10:38Z"}
{"state":"Merged","mergedAt":"2022-11-17T02:16:48Z","number":3692,"mergeCommitSha":"3171260d3eb4ab9d212fb8104c95ebff864b0c90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3692","title":"Revert","createdAt":"2022-11-17T02:16:44Z"}
{"state":"Merged","mergedAt":"2022-11-17T18:59:54Z","number":3693,"mergeCommitSha":"bc3850c58ee69108b1826c720c68697e4df91983","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3693","title":"contact support","createdAt":"2022-11-17T18:36:20Z"}
{"state":"Merged","mergedAt":"2022-11-17T18:49:58Z","number":3694,"mergeCommitSha":"83865fdb5caffac4be652ea6aa3f4e47a08f5212","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3694","title":"Use stores to get TLCs and reviews for a pull request","createdAt":"2022-11-17T18:37:28Z"}
{"state":"Merged","mergedAt":"2022-11-17T20:30:08Z","number":3695,"mergeCommitSha":"363b53eed08c9648a319d4d761c788d0fe071831","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3695","title":"Unify sentry across react and node","createdAt":"2022-11-17T19:35:01Z"}
{"state":"Merged","mergedAt":"2022-11-17T19:50:42Z","number":3696,"body":"Adding retries to observation surfaced a race condition where the user flips to another app while the previous app retries are still in flight, then the previous app retries clobber the new observation. \r\n\r\nThe PR also adds url sanitization for `Notion` specifically","mergeCommitSha":"1d152b363094d47f1a5d15c181aa4cc7f1d77383","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3696","title":"Prevent observation retries clobbering the next observation set","createdAt":"2022-11-17T19:37:04Z"}
{"state":"Merged","mergedAt":"2022-11-17T20:03:44Z","number":3697,"mergeCommitSha":"d8ea0f2191f59d5bf6e86d4213602bd586d37573","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3697","title":"Fix SearchInxingJob logging","createdAt":"2022-11-17T19:58:23Z"}
{"state":"Merged","mergedAt":"2022-11-17T20:36:24Z","number":3698,"body":"CPU pressure was causing throttling and that was making health probes fail. This should hopefully address the pod crash loop. ","mergeCommitSha":"9eab22505a1a9027c064f54d62d85342edade0ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3698","title":"add more cpu/mem to search service.","createdAt":"2022-11-17T20:29:16Z"}
{"state":"Merged","mergedAt":"2022-11-18T22:04:57Z","number":3699,"body":"\r\n<img width=\"552\" alt=\"CleanShot 2022-11-18 at 13 59 07@2x\" src=\"https://user-images.githubusercontent.com/858772/202810168-c5ae8677-add6-4a42-ba4f-6709cd95664a.png\">\r\n\r\n\r\nResolves UNB-750","mergeCommitSha":"6ef4be10ea0a8725a056ecb2e32f916f456b8edc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3699","title":"Add screen capture error state, prompting user to restart their Mac","createdAt":"2022-11-17T21:22:02Z"}
{"state":"Merged","mergedAt":"2022-01-17T04:48:17Z","number":37,"body":"## Stateless GitHub App Based Auth\r\n\r\nInstall PlantUML IntelliJ plugin to view. \r\n\r\nThis auth flow assumes the GitHub App is already installed. Auth while installing is a separate flow that needs its own sequence diagram.\r\n\r\n<img width=\"1364\" alt=\"CleanShot 2022-01-14 at 14 01 07@2x\" src=\"https://user-images.githubusercontent.com/858772/*********-a193590b-3a5c-42f0-912b-2e4a17af88cb.png\">\r\n\r\n","mergeCommitSha":"ccc806d688ec4fabb3ade61368e0c1a558817216","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/37","title":"[RFC] github app auth flow proposal","createdAt":"2022-01-14T22:01:36Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:01:01Z","number":370,"body":"- Created a new IAM mapped service account for API service. We can only associated one service account with each deployment so this one now has both redis and postgres permissions.\r\n- Added template for creating Secret Provider Class (needed for mounting AWS Secret Manager objects)\r\n- Modified deployment template to mount the secret dir if a secret provider has been created\r\n- Updated all values.yaml files to reflect our latest changes\r\n- Added environment variables for Redis Username, Password and Endpoint\r\n- Minor updates to README and Make file\r\nVerified the helm chart locally. The new service account is also ready.","mergeCommitSha":"e956242ddb4c8420b81f0689c33d9d9001466fb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/370","title":"Redis credentials rollout","createdAt":"2022-02-16T22:56:46Z"}
{"state":"Merged","mergedAt":"2022-11-17T21:30:01Z","number":3700,"body":"This reverts commit 83865fdb5caffac4be652ea6aa3f4e47a08f5212.","mergeCommitSha":"85fedf315be29052a4f49d1473c1cc6258722c03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3700","title":"Revert \"Use stores to get TLCs and reviews for a pull request (#3694)\"","createdAt":"2022-11-17T21:29:40Z"}
{"state":"Merged","mergedAt":"2022-11-17T22:06:09Z","number":3701,"mergeCommitSha":"a40a9ee03e089af21655b2815cee73bb9f024a5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3701","title":"add deadletter queue for search indexig","createdAt":"2022-11-17T21:43:50Z"}
{"state":"Merged","mergedAt":"2022-11-17T23:48:47Z","number":3702,"body":"We've been having problems with granular level concurrency.\r\nTo that end, we are now making sure that concurrency for services is at the environment level.\r\nOnly one lock, rather than a lock per service(which is problematic as we can weird interleaved deployments).\r\n\r\nGithub only allows concurrency at workflow or job level.","mergeCommitSha":"7eaf1a4156bff6d9856bb64305965df67a27bf9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3702","title":"Make concurrency more coarse. Concurrency is now at an environment level deployment.","createdAt":"2022-11-17T21:44:57Z"}
{"state":"Merged","mergedAt":"2022-11-17T22:01:08Z","number":3703,"body":"… (#3694)\" (#3700)\"\r\n\r\nThis reverts commit 85fedf315be29052a4f49d1473c1cc6258722c03.","mergeCommitSha":"14d40ace760eea28b236cb180f15d0187497acbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3703","title":"Revert \"Revert \"Use stores to get TLCs and reviews for a pull request…","createdAt":"2022-11-17T21:55:30Z"}
{"state":"Merged","mergedAt":"2022-11-17T23:51:36Z","number":3704,"body":"- Add chevron buttons on each edge, if it's possible to scroll in that direction\r\n- Fade out content on each edge, if it's possible to scroll in that direction\r\n\r\n<img width=\"385\" alt=\"Screen Shot 2022-11-17 at 2 04 15 PM\" src=\"https://user-images.githubusercontent.com/2133518/202569714-467d220c-a801-48ac-ab3d-e3ed3bcc8f90.png\">\r\n","mergeCommitSha":"a79529fd664456820fbf433e270d9edca69f57e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3704","title":"Add fancy edge decorations to topic pill list","createdAt":"2022-11-17T22:04:22Z"}
{"state":"Merged","mergedAt":"2022-11-17T23:17:14Z","number":3705,"body":"Might be behind some of the timeouts https://chapter2global.slack.com/archives/C02T1N1LK19/p1668708781136999","mergeCommitSha":"a08841ad1addd393da7f60331402db849a24f6ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3705","title":"Pull queries out to shrink transaction size","createdAt":"2022-11-17T23:07:04Z"}
{"state":"Merged","mergedAt":"2022-11-17T23:59:09Z","number":3706,"body":"This will allow us to map topics to pull requests and threads.","mergeCommitSha":"74403c975fba1f0617be3859fb758a71cbc2612c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3706","title":"Create topic mappings","createdAt":"2022-11-17T23:17:44Z"}
{"state":"Closed","mergedAt":null,"number":3707,"body":"Fixes UNB-730\r\n\r\nJust show a single repo in this UI, and prioritize root repos.","mergeCommitSha":"9994cae0bd4a1bc5003823f2384d6b72ce6a29e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3707","title":"Show a single repo in the topic recommendation UI","createdAt":"2022-11-17T23:20:37Z"}
{"state":"Merged","mergedAt":"2022-11-19T00:49:09Z","number":3708,"body":"build out base Topic UI:\r\n<img width=\"1499\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/202587127-58ed90e6-5226-4e7f-acbc-abe8a8733a42.png\">\r\n\r\nbuild out historical PR route and view:\r\n<img width=\"1493\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/202587183-cfd42baa-1c17-4a10-8ddf-5576e8b21e9a.png\">\r\n","mergeCommitSha":"f9bc5895706ccae3309fc53e8da02ec4959a6ce4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3708","title":"Add topics UI/routes to dashboard","createdAt":"2022-11-18T00:14:06Z"}
{"state":"Merged","mergedAt":"2022-11-18T00:42:47Z","number":3709,"mergeCommitSha":"b0e37d6bedfa28f99574c1f356b261003b35bbd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3709","title":"Recover when search results references a non-existent thread","createdAt":"2022-11-18T00:18:25Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:51:45Z","number":371,"body":"* Add `shared/mocks/` folder for mocked models for tests\r\n    * Add `@shared-mocks` alias\r\n* Refactor areas where mock data is being used for more legibility/reusability\r\n    * This way when the models change, there should be only a few places that need updating instead of every test file where we statically define the mocked models\r\n* NOTE: vscode node.js can't recognize `btoa` so I changed to the recommended `Buffer.from(<string>).toString('base64')` \r\n\r\n![image](https://user-images.githubusercontent.com/13431372/154406947-00cea5f9-9e3f-4efd-9841-2407373742c1.png)\r\n","mergeCommitSha":"47e3ac62cf3349072f1b129aef5c363f9c9b607a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/371","title":"Refactor mocked models to shared directory","createdAt":"2022-02-17T04:45:11Z"}
{"state":"Merged","mergedAt":"2022-11-18T01:14:25Z","number":3710,"mergeCommitSha":"42cba5bca3295325eb074b3084e2a66641c02776","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3710","title":"Fade camera on mouse hover","createdAt":"2022-11-18T00:32:40Z"}
{"state":"Merged","mergedAt":"2022-11-18T03:15:47Z","number":3711,"mergeCommitSha":"883ddaf89f3ea46700b3b73557aabcd9b45df32c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3711","title":"Sourcemark agent clones repo then runs recalculation then dies","createdAt":"2022-11-18T00:53:44Z"}
{"state":"Merged","mergedAt":"2022-11-18T04:50:38Z","number":3712,"mergeCommitSha":"30a8b61f690e9d24f72c9a9fd1b169b045ca3766","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3712","title":"getRelatedTopics returns topics related to the file","createdAt":"2022-11-18T04:18:14Z"}
{"state":"Merged","mergedAt":"2022-11-18T19:17:54Z","number":3713,"mergeCommitSha":"4c4f3cb9a0b05a6b4b3ca7e0d939ad65d7a5d209","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3713","title":"Fix paths in SM agent","createdAt":"2022-11-18T17:37:57Z"}
{"state":"Merged","mergedAt":"2022-11-18T19:51:29Z","number":3714,"mergeCommitSha":"d7c8d6f9926ced04a769d0c569590a5966a304c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3714","title":"update slack jsons.","createdAt":"2022-11-18T19:39:05Z"}
{"state":"Merged","mergedAt":"2022-11-18T19:56:48Z","number":3715,"mergeCommitSha":"6228b22d7dbbd5e3be0a9392f7231a85ca9dcf40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3715","title":"Add source agent ecr","createdAt":"2022-11-18T19:56:34Z"}
{"state":"Merged","mergedAt":"2022-11-18T20:01:12Z","number":3716,"mergeCommitSha":"d4b5149c477355d26f930af9d5da867dd94003f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3716","title":"Try again","createdAt":"2022-11-18T20:01:06Z"}
{"state":"Merged","mergedAt":"2022-11-18T21:01:37Z","number":3717,"body":"- Standardize docker publish\r\n- try again\r\n","mergeCommitSha":"c6c5b899b2b6c5b2b8c4635f092bb69ad85a0147","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3717","title":"StandardizeDockerPublishing","createdAt":"2022-11-18T20:31:45Z"}
{"state":"Merged","mergedAt":"2022-11-18T21:39:10Z","number":3718,"body":"When a user creates a mark on a commit or change that has not yet been pushed to the remote,\nthen the SM engine cannot track the point through revision history for other users.\n\nThe proper fix for this is to allow creating insight bubbles from a locally edited file,\nby running the SM engine \"in reverse\" from the current local commit to the merge-base for the\ntracked branch (or the default remote HEAD).\nhttps://linear.app/unblocked/issue/UNB-178/allow-creating-insight-bubbles-from-a-locally-edited-file\n\nHowever, in the meantime, we can relax the lookup for FileMarks (since it does not need to\nexactly match lines). This fixes the problem for FileMarks for most practical cases.","mergeCommitSha":"a81b06304657ffeece8e604a1206bb332f0f0dd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3718","title":"Relax accuracy of point resolution for FileMarks","createdAt":"2022-11-18T20:47:52Z"}
{"state":"Merged","mergedAt":"2022-11-18T22:17:05Z","number":3719,"mergeCommitSha":"5ff565efc1f6253d34c6c8a5c17302ace0687462","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3719","title":"Use AppKit text for 'All Insights read' text","createdAt":"2022-11-18T22:16:40Z"}
{"state":"Closed","mergedAt":null,"number":372,"body":"Enables running PR ingestion from the command line for the purposes of generating a report.\r\n\r\nDEFINITELY DO NOT MERGE","mergeCommitSha":"f51f557319228f2e47701b95814b0d6bf3630f58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/372","title":"[WIP] [DO NOT MERGE] Generate PR ingestion report","createdAt":"2022-02-17T05:37:27Z"}
{"state":"Merged","mergedAt":"2022-11-18T23:33:03Z","number":3720,"body":"Next PR: fire a index event when transcription is done and saved to the database","mergeCommitSha":"f62923da3b3898cc2286da06be9e9e36370c03b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3720","title":"Index transcriptions","createdAt":"2022-11-18T23:11:36Z"}
{"state":"Merged","mergedAt":"2022-11-18T23:30:44Z","number":3721,"mergeCommitSha":"d715f8042c3e1afcf434905855b7af08743f56f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3721","title":"Search indexing from transcription service","createdAt":"2022-11-18T23:25:25Z"}
{"state":"Merged","mergedAt":"2022-11-19T00:47:06Z","number":3722,"mergeCommitSha":"4f7001cc63bcd97f5e9a49ff9adb0b4991647b20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3722","title":"Deploy source agent","createdAt":"2022-11-18T23:53:38Z"}
{"state":"Closed","mergedAt":null,"number":3723,"mergeCommitSha":"8f87bbd00b15c75b831be0cea982902677302f2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3723","title":"Scoring updates + BERT investigations","createdAt":"2022-11-19T00:05:12Z"}
{"state":"Merged","mergedAt":"2022-11-19T00:32:31Z","number":3724,"body":"Part 2 of https://github.com/NextChapterSoftware/unblocked/pull/3721","mergeCommitSha":"fde84ae9f4d83def45692a46043abd81513e820a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3724","title":"Emit search index event once transcription is done","createdAt":"2022-11-19T00:10:14Z"}
{"state":"Merged","mergedAt":"2022-11-19T00:33:58Z","number":3725,"body":"The topic pill horizontal scroller had a bug where occasionally, when you scrolled to the right of the view, the right edge would continue to display with a faded edge and the right chevron button.  The problem is that the scroll region can have sub-pixel widths -- this is a quick fix to ensure we've scrolled at least one pixel away from the right side.","mergeCommitSha":"415296f5a1ac59537ad836e0717cb22fd0ca0fc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3725","title":"Fix horizontal scroll view bug","createdAt":"2022-11-19T00:16:35Z"}
{"state":"Merged","mergedAt":"2022-11-19T00:52:02Z","number":3726,"mergeCommitSha":"e8c8e813f08c5f71a1d68b2562bcfa0ad18d3da8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3726","title":"Allow triggering search re-indexing for an individual thread","createdAt":"2022-11-19T00:45:36Z"}
{"state":"Merged","mergedAt":"2022-11-19T02:21:15Z","number":3727,"mergeCommitSha":"5d7f11041eac898aefb4e4ffd316f541739b4a51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3727","title":"optimize web builds","createdAt":"2022-11-19T00:52:41Z"}
{"state":"Merged","mergedAt":"2022-11-19T04:40:30Z","number":3728,"body":"The previous PR had some old updates in a previous commit that changed the stop words. Sorry for the need to reapprove.","mergeCommitSha":"244bee89aa917e9ca271cbe3918a3bb59161fa7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3728","title":"Updated scoring + BERT investigations","createdAt":"2022-11-19T03:19:38Z"}
{"state":"Merged","mergedAt":"2022-11-21T18:07:33Z","number":3729,"body":"Downside: the video app will now appear in the dock and have its own menu bar. Not the end of the world because the user won't be able to get themselves into trouble, but not ideal","mergeCommitSha":"b127c549320a5e6db1fad9e26c2376f776f98d09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3729","title":"Fixes disappearing window bug during permissions dialog","createdAt":"2022-11-19T23:58:43Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:13:42Z","number":373,"body":"https://github.com/NextChapterSoftware/unblocked/runs/5235925765?check_suite_focus=true\r\n\r\nLast run of CI/CD failed because of this missing permission. ","mergeCommitSha":"3b9043c62a28f2950383ca9358ac79285d23a63f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/373","title":"need this to fix CI","createdAt":"2022-02-17T17:12:24Z"}
{"state":"Merged","mergedAt":"2022-11-20T08:19:56Z","number":3730,"body":"Addresses these:\r\n- https://sentry.io/organizations/nextchaptersoftware/issues/3749699515\r\n- https://sentry.io/organizations/nextchaptersoftware/issues/3748051523\r\n- https://sentry.io/organizations/nextchaptersoftware/issues/3747911838","mergeCommitSha":"61aad4390b944d0605e70066ba6a6142dc434ec7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3730","title":"Fix incremental sourcemark calc when files have moved","createdAt":"2022-11-20T06:49:08Z"}
{"state":"Merged","mergedAt":"2022-11-21T06:21:15Z","number":3731,"body":"There were a few problems when we added search indexing support on transcriptions:\r\n\r\n1. We were not using the correct permission set to access the search indexing queue.\r\n2. We were swallowing transcription service exceptions such that the service was not properly aborting and not indicating a major problem.","mergeCommitSha":"f4f7cf89637804f22d342de4183577857903f295","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3731","title":"Ensure transcription service aborts on error","createdAt":"2022-11-21T06:12:30Z"}
{"state":"Merged","mergedAt":"2022-11-21T18:08:57Z","number":3732,"body":"Remove code dupe","mergeCommitSha":"7bb8dd6796a30bd1e71bfd7e9402f49c4dd11b96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3732","title":"Standardize code","createdAt":"2022-11-21T06:42:04Z"}
{"state":"Merged","mergedAt":"2022-11-21T18:34:35Z","number":3733,"mergeCommitSha":"24919d084f4f65b88c1066eaff5280b3400ba0ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3733","title":"Fix unread styling","createdAt":"2022-11-21T18:03:20Z"}
{"state":"Merged","mergedAt":"2022-11-22T17:48:36Z","number":3734,"body":"The code changes weren't landed so we shouldn't return these in search results.","mergeCommitSha":"3430182606e6fee6d29bf922ca0bd64d6212e23c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3734","title":"Don't index closed pull requests","createdAt":"2022-11-21T18:54:47Z"}
{"state":"Merged","mergedAt":"2022-11-21T19:19:36Z","number":3735,"mergeCommitSha":"23425ee7dacf794134a50483671c54f6bcb8bf0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3735","title":"Start camera and mic by default","createdAt":"2022-11-21T19:18:54Z"}
{"state":"Merged","mergedAt":"2022-11-21T20:48:21Z","number":3736,"mergeCommitSha":"9acbbf85a3ee0f911b2e28d934218b512cf74917","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3736","title":"Styling fixes for dashboard topics","createdAt":"2022-11-21T19:20:09Z"}
{"state":"Merged","mergedAt":"2022-11-21T19:42:54Z","number":3737,"mergeCommitSha":"1410df3a278db9e8d71bf757442db7c17d4b3a99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3737","title":"Save the last selected camera and mic choice","createdAt":"2022-11-21T19:39:55Z"}
{"state":"Merged","mergedAt":"2022-11-21T20:36:07Z","number":3738,"mergeCommitSha":"1b6b51f254b0ab7d15d7794d85582995264817aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3738","title":"ingest image links","createdAt":"2022-11-21T20:25:49Z"}
{"state":"Merged","mergedAt":"2022-11-21T21:40:02Z","number":3739,"body":"Slack has thoroughly thought of rate limiting when generating their clients.\r\nhttps://github.com/slackapi/java-slack-sdk/commit/543b7f8e26ef69bb83151edd11767c3f1c241a8d#diff-ebc8f5ed7e1c62aa276f2c4fc57be1b1d2368bb885ac4b4d191ad42da402c12d","mergeCommitSha":"8d6f9e296db844af24d98439c97ae853bf92184a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3739","title":"Handle slack rate limiting","createdAt":"2022-11-21T21:14:31Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:27:05Z","number":374,"mergeCommitSha":"1570ab3fb4c9605bf664526fd57b6be291360264","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/374","title":"ah forgot this one","createdAt":"2022-02-17T17:24:54Z"}
{"state":"Merged","mergedAt":"2022-11-21T21:32:45Z","number":3740,"mergeCommitSha":"863a1accdbebabe3de41d3867ded99a0f4eb103e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3740","title":"Fix chrome slurps","createdAt":"2022-11-21T21:29:53Z"}
{"state":"Merged","mergedAt":"2022-11-21T22:29:27Z","number":3741,"mergeCommitSha":"e970f4ec18195bfa35c5ccf16560246bab4b13e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3741","title":"Fixes annoying menu dismissal bug on the video preview window","createdAt":"2022-11-21T22:29:00Z"}
{"state":"Merged","mergedAt":"2022-11-22T01:12:52Z","number":3742,"body":"* Add context menu UI item to the PR view\r\n    * Set up UI architecture but will need API to send back the links instead of constructing them on the clients (see: https://linear.app/unblocked/issue/UNB-769/pullrequest-api-model-missing-properties)\r\n* Fix vscode editor focus preservation (see: https://chapter2global.slack.com/archives/C02HEVCCJA3/p1669068394991679)","mergeCommitSha":"005f86cd3a84e67231b5f5009c804f059ca25b96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3742","title":"Add context menu to dashboard pr view and fix vscode tab focus","createdAt":"2022-11-21T22:39:54Z"}
{"state":"Merged","mergedAt":"2022-11-21T23:56:42Z","number":3743,"body":"Motivation is for sourcemark cloud auth, which needs long-lived access to multiple resources.\nhttps://linear.app/unblocked/issue/UNB-768/auth-tokens-can-have-multiple-scoped-resources\n\nImpact: Tiny chance that an in-flight long-lived token will be invalidated. So if somebody is\ncurrently streaming a video while this change goes live, then the auth request will be rejected.","mergeCommitSha":"470f758107aca52a9d26e1f76c46c0ab889cf23a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3743","title":"Auth tokens can have multiple scoped resources","createdAt":"2022-11-21T22:48:42Z"}
{"state":"Merged","mergedAt":"2022-11-22T01:38:22Z","number":3744,"body":"This pr does sentry releases and ensures that we get metrics associated with exceptions etc. per release.\r\nAlso, uploads sourcemaps so that stack traces can be correctly disambiguated.\r\n\r\nhttps://sentry.io/organizations/nextchaptersoftware/releases/a86b1b7fcf85f42b0723b021b23be0cab694564d/?project=4504199684751360\r\nTested a web deployment and validated.","mergeCommitSha":"c74e3c9e0436adce19477d5ba15b5c9a73718631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3744","title":"Sentry Releases for web and vscode","createdAt":"2022-11-21T23:07:11Z"}
{"state":"Merged","mergedAt":"2022-11-21T23:56:48Z","number":3745,"mergeCommitSha":"38c8a9d77c5932c2debe2528923053d14a23ff2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3745","title":"Segregate web from vscode","createdAt":"2022-11-21T23:35:42Z"}
{"state":"Merged","mergedAt":"2022-11-22T00:59:33Z","number":3746,"body":"Source agent can only run on public repos. So we need this to know if we can run the source agent.","mergeCommitSha":"7b3f2b0eb0b5e0dbe481a21ad95751e43651456e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3746","title":"Persist repo isPublic field and show in adminweb","createdAt":"2022-11-22T00:24:38Z"}
{"state":"Merged","mergedAt":"2022-11-22T19:21:34Z","number":3747,"body":"https://linear.app/unblocked/issue/UNB-766/admin-web-button-to-trigger-sourcemark-recalculation-on-public-repo","mergeCommitSha":"32e915add08261fc8a8ae6706f35d784373e9f31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3747","title":"Admin web button to trigger sourcemark recalculation on public repo","createdAt":"2022-11-22T00:39:28Z"}
{"state":"Merged","mergedAt":"2022-11-25T00:37:56Z","number":3748,"body":"The default stemming dictionary used in dev & prod is `simple`. Let's specify the dictionary to use so that input words are stemmed during indexing.","mergeCommitSha":"2791800385c790d691e1d8480c9b35487a6ed5d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3748","title":"Fix word stemming in dev and prod","createdAt":"2022-11-22T00:45:24Z"}
{"state":"Merged","mergedAt":"2022-11-22T17:01:11Z","number":3749,"mergeCommitSha":"550f454e6358788106fd1191ab38e5432d3f8d7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3749","title":"Increase screen capture error timeout to 2 seconds","createdAt":"2022-11-22T17:00:59Z"}
{"state":"Merged","mergedAt":"2022-02-17T17:42:17Z","number":375,"mergeCommitSha":"5a55531229e3449d4eb52f5e27803b5e5736c9df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/375","title":"Update","createdAt":"2022-02-17T17:29:01Z"}
{"state":"Merged","mergedAt":"2022-11-22T18:57:05Z","number":3750,"mergeCommitSha":"dc6fcb3abc8bfd657377f784dcae5a892d92f105","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3750","title":"Add SQS infra config for sourcemark repo recalculation events","createdAt":"2022-11-22T18:09:57Z"}
{"state":"Merged","mergedAt":"2022-11-22T19:01:13Z","number":3751,"mergeCommitSha":"b88238f7e7fc06ea293af53788b522a721db0ea0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3751","title":"Remove unnecessary queue access from DEV telemetry/auth services","createdAt":"2022-11-22T18:20:57Z"}
{"state":"Merged","mergedAt":"2022-11-22T19:02:53Z","number":3752,"mergeCommitSha":"665238407fd1e8c6fb7deaa03b9b80d9f8f73e2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3752","title":"Remove unnecessary queue access from PROD telemetry/auth services","createdAt":"2022-11-22T18:22:47Z"}
{"state":"Merged","mergedAt":"2022-11-22T20:39:44Z","number":3753,"mergeCommitSha":"015b1d3c3346f272b2a658b4adf24f4fa8a7bdec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3753","title":"Remove code dupe","createdAt":"2022-11-22T18:28:24Z"}
{"state":"Merged","mergedAt":"2022-11-22T18:41:14Z","number":3754,"body":"First stab at a better ordering of the topic pills in VS code","mergeCommitSha":"196ccb3598cf1b7d32d72bdb8655147069514ae8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3754","title":"Order related topics by frequency of occurence","createdAt":"2022-11-22T18:32:30Z"}
{"state":"Merged","mergedAt":"2022-11-22T18:47:14Z","number":3755,"body":"Added search indexing priority queue. It doesn't need any EKS/IAM permission changes since we are using wildcards to specify queue names in service account configs. ","mergeCommitSha":"ce699a9321abab3165306630f31a1c7ffa8d03e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3755","title":"create search indexing priority queue","createdAt":"2022-11-22T18:46:03Z"}
{"state":"Merged","mergedAt":"2022-11-22T21:45:33Z","number":3756,"body":"- We had 6 eslint config files and inheritance was broken\r\n- Now one file","mergeCommitSha":"03d8d6187623d17ab6fc6f94e58c9526b9359bf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3756","title":"Consolidate eslint configs","createdAt":"2022-11-22T18:49:42Z"}
{"state":"Merged","mergedAt":"2022-11-22T18:55:11Z","number":3757,"body":"Ops forgot this.","mergeCommitSha":"3c712ae99236add9e67ea89c6ec0a415b174d70a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3757","title":"fix search indexing deadletter queues","createdAt":"2022-11-22T18:52:36Z"}
{"state":"Merged","mergedAt":"2022-11-22T21:46:07Z","number":3758,"body":"Need help with this \uD83D\uDE4F \r\n\r\n- [x] shared\r\n  - wasn't able to address `Unexpected any`, so these are still warnings; 26 of them in shared.\r\n- [x] source-agent\r\n- [x] vscode\r\n- [x] web\r\n- [x] web-extension","mergeCommitSha":"83360d1fe60d643f15d0ae9bd0e9b49093ffbb8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3758","title":"Fix lint after fix lint config","createdAt":"2022-11-22T18:59:54Z"}
{"state":"Merged","mergedAt":"2022-11-22T19:08:25Z","number":3759,"mergeCommitSha":"eba3b6a32e841801670169f910904479e0f35575","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3759","title":"Fix deadletter queue","createdAt":"2022-11-22T19:06:18Z"}
{"state":"Merged","mergedAt":"2022-02-17T18:05:32Z","number":376,"body":"No more having to deal with branch bullshit whne using resusable workflows or actions.\r\n\r\nhttps://github.blog/changelog/2022-01-25-github-actions-reusable-workflows-can-be-referenced-locally/","mergeCommitSha":"44aef524fa837c27b0a1b3114c2b1ef68692d250","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/376","title":"Github Actions now has support for local path references!","createdAt":"2022-02-17T17:51:39Z"}
{"state":"Merged","mergedAt":"2022-11-22T19:43:43Z","number":3760,"body":"See dependency in `projects/services/authservice/src/main/kotlin/com/nextchaptersoftware/authservice/Module.kt`","mergeCommitSha":"f01588528026cb67530895e0f82b2206a52d6467","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3760","title":"Partial revert: allow auth service to access slack queues","createdAt":"2022-11-22T19:42:26Z"}
{"state":"Closed","mergedAt":null,"number":3761,"body":"The core problem we're trying to solve here is ordering the topics in a sensible way. Related topics that are most relevant should appear first:\r\n\r\n<img width=\"326\" alt=\"CleanShot 2022-11-22 at 13 17 52@2x\" src=\"https://user-images.githubusercontent.com/1924615/203423029-d2aab721-a243-4212-a0df-d6c7248581ef.png\">\r\n\r\nOne very crude but cheap way to infer relevance is to check for the presence of the topic in the file path. \r\n\r\nThis PR adds a new property to the `TopicsFilter` so that we can order the topics returned by the api service such that topics that appear in the file path appear first. Needs a client side change to supply the file path.","mergeCommitSha":"6f7b6a1b014b33383a710fb8e6253e434b21fa3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3761","title":"Order topics based on filepath","createdAt":"2022-11-22T19:51:16Z"}
{"state":"Merged","mergedAt":"2022-11-22T20:38:59Z","number":3762,"mergeCommitSha":"dd0ebca3e2a64b49a682da73da41a457b5fb543c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3762","title":"Fix sentry","createdAt":"2022-11-22T20:38:53Z"}
{"state":"Merged","mergedAt":"2022-11-22T23:44:10Z","number":3763,"body":"https://user-images.githubusercontent.com/13431372/203427871-6ffe1bc8-0313-4c41-8810-55165804acb7.mp4\r\n\r\n","mergeCommitSha":"874e3bde834131fc2a63f3e92a21b6ab83fe18e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3763","title":"Connect vscode topics pills to dashboard ","createdAt":"2022-11-22T21:47:57Z"}
{"state":"Closed","mergedAt":null,"number":3764,"mergeCommitSha":"7d43021f6183c87f82b0db330e1c5539d540034e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3764","title":"Sort topics by sourcemark filepaths","createdAt":"2022-11-22T22:08:09Z"}
{"state":"Merged","mergedAt":"2022-11-22T23:53:56Z","number":3765,"body":"https://linear.app/unblocked/issue/UNB-771/backfill-to-populate-repo-visibility","mergeCommitSha":"d989b46f8803ca26bc567a09532a7e50016a9bcb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3765","title":"Backfill to populate repo visibility on teams","createdAt":"2022-11-22T22:52:49Z"}
{"state":"Merged","mergedAt":"2022-11-22T23:04:18Z","number":3766,"body":"The client is sorting by score.","mergeCommitSha":"51781e1325b44960848f3b13dba9bd252c8157b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3766","title":"Set topic score using topic occurence frequency in getRelatedTopics","createdAt":"2022-11-22T22:54:31Z"}
{"state":"Merged","mergedAt":"2022-11-29T19:16:55Z","number":3767,"body":"The explorer UI in the dashboard necessitates a TopicInfo wrapper type to return additional data:\r\n<img width=\"1270\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/203442378-4a4185c2-eaa5-4002-a4e3-39a7e63d9506.png\">\r\n\r\nTopics should also have a list of experts:\r\n<img width=\"645\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/203442508-ef7070a4-2c00-442d-9c9d-f87c7a6a4e10.png\">\r\n","mergeCommitSha":"191fc915d764bf40d1773c8cd370a1f28d364656","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3767","title":"[BREAKS API ON MAIN] Add properties to Topic model","createdAt":"2022-11-22T23:37:11Z"}
{"state":"Merged","mergedAt":"2022-11-23T01:05:56Z","number":3768,"mergeCommitSha":"4b6f23fa6c8ed570e3b628d3738553e94366a4f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3768","title":"Add log line to understand why slack thread isnt being ingested","createdAt":"2022-11-23T00:58:13Z"}
{"state":"Closed","mergedAt":null,"number":3769,"mergeCommitSha":"b194b2ad4260a574bbad91a77ef5d4e38b925f1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3769","title":"Assume message is top level message if it is the only one in the thread","createdAt":"2022-11-23T01:06:54Z"}
{"state":"Merged","mergedAt":"2022-02-17T18:06:31Z","number":377,"body":"So turns out Kubernetes has combined API_GROUP and VERSION fields into one when running ` kubectl api-resources -o wide`. RABC only cares about API group and I had to remove the version","mergeCommitSha":"036395f3c069693cc0cd2a1fe83e4423858bacf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/377","title":"finally fixed it","createdAt":"2022-02-17T18:05:21Z"}
{"state":"Merged","mergedAt":"2022-11-23T03:53:57Z","number":3770,"mergeCommitSha":"4759f0e750b36415cb5815ff7b7238378c55fc27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/3770","title":"Plumb sourcemark event parameters from event to agent","createdAt":"2022-11-23T02:33:46Z"}