{"state":"Merged","mergedAt":"2022-06-17T04:56:59Z","number":1871,"mergeCommitSha":"b9e05b206745b832356e4cd816bceb812264d311","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1871","title":"Drop concurrency to 5","createdAt":"2022-06-17T04:55:49Z"}
{"state":"Merged","mergedAt":"2022-06-17T06:08:03Z","number":1872,"body":"This is set in the common http client, so all requests from the app client and the org client will have this set.","mergeCommitSha":"8f7197ff22c5ade03398eddc67fe53f0de46f857","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1872","title":"Set user agent for github client","createdAt":"2022-06-17T05:31:29Z"}
{"state":"Merged","mergedAt":"2022-06-17T05:38:54Z","number":1873,"mergeCommitSha":"b819f725ac318b2530cf8a24aec6fc1d679ef123","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1873","title":"Just make the calls serially","createdAt":"2022-06-17T05:38:41Z"}
{"state":"Merged","mergedAt":"2022-06-17T20:41:40Z","number":1874,"body":"Setup LoginLocation state which dictates where users land after successful login/exchange.\r\n\r\n","mergeCommitSha":"5ff71bc685d96bb82dbec93ac1246a59fbde196a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1874","title":"Setup LoginLocation redirect for web","createdAt":"2022-06-17T06:14:29Z"}
{"state":"Merged","mergedAt":"2022-06-21T00:52:02Z","number":1875,"body":"Addresses these exceptions https://app.logz.io/#/goto/b92c702b6e86de0473f2f68517c69e8f?switchToAccountId=411850","mergeCommitSha":"fd9e6013e42634373f1166775ab6d215a5093f31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1875","title":"Handle when file sha is null","createdAt":"2022-06-17T06:36:00Z"}
{"state":"Merged","mergedAt":"2022-06-17T21:31:40Z","number":1876,"body":"<img width=\"828\" alt=\"Pasted Graphic 5\" src=\"https://user-images.githubusercontent.com/********/*********-6794b94c-3fa7-4e5b-9dfc-b092ba4bb922.png\">\r\n","mergeCommitSha":"c062d717db0d59563ac9d7ff822ac7570a5b91f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1876","title":"[Onboarding] Show README / a file if there are no committed files","createdAt":"2022-06-17T16:25:50Z"}
{"state":"Merged","mergedAt":"2022-06-17T18:06:06Z","number":1877,"mergeCommitSha":"cf01060243180b9e3ca65ac1257808b256db5bbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1877","title":"Kay Fixes :)","createdAt":"2022-06-17T16:32:12Z"}
{"state":"Merged","mergedAt":"2022-06-17T19:18:32Z","number":1878,"body":"<img width=\"375\" alt=\"CleanShot 2022-06-17 at 12 02 50@2x\" src=\"https://user-images.githubusercontent.com/1553313/174386923-948818d9-6aba-4fa8-b82d-d28f20838da8.png\">\r\n","mergeCommitSha":"6994599a0b2e5b9767312fa18e9e0be991519a87","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1878","title":"Update header with title and ellipsis","createdAt":"2022-06-17T19:03:59Z"}
{"state":"Merged","mergedAt":"2022-06-17T19:50:18Z","number":1879,"mergeCommitSha":"523400adcf7fd02d1a78890ffc430817410fcc59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1879","title":"Add service","createdAt":"2022-06-17T19:18:13Z"}
{"state":"Closed","mergedAt":null,"number":188,"mergeCommitSha":"bd58652b30675a3f979cd87b9682d1d8ff674db0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/188","title":"test -- ignore","createdAt":"2022-01-31T21:19:12Z"}
{"state":"Merged","mergedAt":"2022-06-17T21:13:04Z","number":1880,"mergeCommitSha":"033e267cd7424b71de8eba5cc3b3534d9805c660","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1880","title":"WebhookEcr","createdAt":"2022-06-17T20:32:57Z"}
{"state":"Merged","mergedAt":"2022-06-17T21:10:42Z","number":1881,"body":"- Added new service account for Webhook service \r\n- Service accounts have been deployed to dev and prod \r\n- Added deadletter queues to existing PR ingestion queues \r\n- Added deadletter and standard queue for scm_hooks ","mergeCommitSha":"e8c702b99a47781b58ae6081b82e393c859810b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1881","title":"add service accounts and new queues","createdAt":"2022-06-17T21:02:53Z"}
{"state":"Merged","mergedAt":"2022-06-18T19:52:17Z","number":1882,"mergeCommitSha":"4742e62883d45830aa61d0f15edf4281895268b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1882","title":"Hub updater","createdAt":"2022-06-17T21:11:35Z"}
{"state":"Merged","mergedAt":"2022-06-17T21:43:18Z","number":1883,"mergeCommitSha":"7fa900c9414e5a2ede2a6d5d26e1bae5708d31dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1883","title":"Deploy webhook","createdAt":"2022-06-17T21:16:49Z"}
{"state":"Merged","mergedAt":"2022-06-17T22:41:21Z","number":1884,"body":"Send an event to prioritize certain repos for PR ingestion during onboarding.","mergeCommitSha":"ef2d417bd321391cb3d2fbfe735003b86b9b4e57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1884","title":"Update onboarding request for PR ingestion","createdAt":"2022-06-17T21:24:13Z"}
{"state":"Merged","mergedAt":"2022-06-17T22:28:35Z","number":1885,"body":"Fixes UNB-289 \r\n\r\nI messed this up.  I put the `IntercomAutoLaunch` component definition in the middle of another component -- I mis-read the spacing and thought it was at the top level.  Since the function is defined in another component, it effectively is a new component every time the parent renders, which causes the react tree to be wiped out, and our state lost.\r\n\r\nAs part of fixing this, I moved all the intercom auto launching stuff into one file so it's easier to follow.\r\n\r\nWill also look at enabling this lint rule, which should catch this kind of thing: https://github.com/jsx-eslint/eslint-plugin-react/blob/master/docs/rules/no-unstable-nested-components.md","mergeCommitSha":"11c705f2c0944bd41a0ceccbe5c1e1dc79750d67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1885","title":"Fix dashboard message editor","createdAt":"2022-06-17T21:48:39Z"}
{"state":"Merged","mergedAt":"2022-06-17T22:11:26Z","number":1886,"body":"Stubs in next PR.","mergeCommitSha":"8415d459cc486ff07e5bbb3e76184d5735d48dc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1886","title":"New POST /hooks/github API endpoint","createdAt":"2022-06-17T21:51:12Z"}
{"state":"Merged","mergedAt":"2022-06-17T22:41:31Z","number":1887,"body":"Web extension only loads source marks on load. \r\nTherefore, when a new discussion was created, its source mark was never added to the UI.\r\n\r\nWe now refresh the sourcemarkstore on discussion creation.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/174405658-a1033fb7-2df1-4bd7-adf9-6896a92d0ce7.mp4\r\n\r\n\r\n","mergeCommitSha":"2917f0733fccc41dec6506f64d50314cdd5c84e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1887","title":"Refresh source marks on create discussion","createdAt":"2022-06-17T21:51:58Z"}
{"state":"Merged","mergedAt":"2022-06-17T23:05:30Z","number":1888,"body":"Will be used by webhook handlers","mergeCommitSha":"1d94dfbc3bdbf51981855db3fd009d07f2a141b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1888","title":"Add webhook models and ability to look up repo by external owner repo ID","createdAt":"2022-06-17T21:54:10Z"}
{"state":"Merged","mergedAt":"2022-06-17T23:01:24Z","number":1889,"mergeCommitSha":"5135c173530a26d180ed1e678dcd34f1fe867bff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1889","title":"Increase parallelization","createdAt":"2022-06-17T23:01:20Z"}
{"state":"Merged","mergedAt":"2022-01-31T21:40:55Z","number":189,"body":"As discussed, some renaming:\r\n\r\n1. `SourceMark` → `SourceMarkGroup` (#186)\r\n2. `SourceMark` → `SourceMarkGroup` in API (this change)\r\n3. `SourceVector` → `SourceMark`\r\n4. Relate `SourceMark` → `Message`","mergeCommitSha":"bd8f6e0f28b35314f9eb7b5c61f5858fb5fee0e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/189","title":"Rename [2 of 4]: SourceMark -> SourceMarkGroup API","createdAt":"2022-01-31T21:35:05Z"}
{"state":"Merged","mergedAt":"2022-06-20T16:04:28Z","number":1890,"body":"Fixes UNB-237\r\n\r\n* Moved UnsavedChangeTracker to a singleton that can be used anywhere, in the VSCode utils module\r\n* Use its wrapper behaviour in the two places calling getSourceMarkLatestPoint","mergeCommitSha":"b76be2a2d6f9fafceae8d026f3cfe433b5420312","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1890","title":"Pass unsaved files to getSourceMarkLatestPoint","createdAt":"2022-06-17T23:17:02Z"}
{"state":"Merged","mergedAt":"2022-06-18T01:25:01Z","number":1891,"mergeCommitSha":"72407ccc73d77297d8c694d9b87704742dc92bb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1891","title":"update deps","createdAt":"2022-06-18T00:56:01Z"}
{"state":"Merged","mergedAt":"2022-06-18T01:15:07Z","number":1892,"body":"More bullshit","mergeCommitSha":"a7761645f3029579ee03508e15f2de9c357fe42b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1892","title":"Add docker webhook","createdAt":"2022-06-18T01:13:17Z"}
{"state":"Merged","mergedAt":"2022-06-18T04:02:39Z","number":1893,"body":"Ready to hook up to queue.","mergeCommitSha":"c024f66dec38a5b924cd77c83dde2471bd7bf8a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1893","title":"GitHub Webhook API implementation with tests","createdAt":"2022-06-18T01:29:25Z"}
{"state":"Merged","mergedAt":"2022-06-18T01:39:27Z","number":1894,"mergeCommitSha":"af5ac1ebaf46c725cc68dcd131717154549c7831","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1894","title":"update","createdAt":"2022-06-18T01:39:21Z"}
{"state":"Merged","mergedAt":"2022-06-18T03:21:51Z","number":1895,"body":"No logic changes, just renaming as part of the webhook handler work.","mergeCommitSha":"6c073ae6972efe10946d6bfcff3677a7914c765a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1895","title":"Renaming classes","createdAt":"2022-06-18T03:02:03Z"}
{"state":"Merged","mergedAt":"2022-06-18T06:15:55Z","number":1896,"mergeCommitSha":"5349f2d4087bf133579eec2330afaf8fa3c559ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1896","title":"Add Honeycomb to webhook-service","createdAt":"2022-06-18T06:04:38Z"}
{"state":"Closed","mergedAt":null,"number":1897,"body":"Dumped images so we can focus on text in this PR\r\n","mergeCommitSha":"a892d8a181885ad4895569234a4da305746a8c5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1897","title":"Trying to condense...","createdAt":"2022-06-18T21:44:21Z"}
{"state":"Merged","mergedAt":"2022-06-19T18:32:18Z","number":1898,"body":"Just pass in the whole object.\r\n\r\nNo logic changes here and it's all covered by tests.","mergeCommitSha":"42321f7fb4deceeaeb13603232a21f3c607cb6f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1898","title":"Refactor PullRequestReviewThread object","createdAt":"2022-06-19T18:09:43Z"}
{"state":"Merged","mergedAt":"2022-06-19T21:03:00Z","number":1899,"mergeCommitSha":"aa470c7d85a67c585269f2a8c6b538151a4855f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1899","title":"proper honeycomb","createdAt":"2022-06-19T20:46:52Z"}
{"state":"Closed","mergedAt":null,"number":19,"mergeCommitSha":"981669d670ccd092cadb2770bc68f1194fbbf369","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/19","title":"Recoil implementation","createdAt":"2022-01-04T05:18:26Z"}
{"state":"Merged","mergedAt":"2022-01-31T21:55:51Z","number":190,"body":"Creating a source mark will happen when creating/updating a message, so the PUT operation is not needed. Similarly, we'll get source marks through messages.","mergeCommitSha":"63e23d288dd286af28ce7a4e120e74e63bc540a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/190","title":"Remove sourcemark operations","createdAt":"2022-01-31T21:45:36Z"}
{"state":"Merged","mergedAt":"2022-06-20T01:09:08Z","number":1900,"body":"Compress SCM body, then base64 encode, then JSON encode.","mergeCommitSha":"ca940e4eb0f0581cdfcf27c0f150b9db9d5c8f11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1900","title":"Compress webhook payloads","createdAt":"2022-06-19T23:07:34Z"}
{"state":"Merged","mergedAt":"2022-06-20T03:36:15Z","number":1901,"body":"More refactoring fun. We're going to need to reuse this logic in the `GitHubPullRequestReviewCommentHandler` so let's pull it out and add some tests.","mergeCommitSha":"c910d66a986ce22db0366196f61df5653bbb1a5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1901","title":"Pull out TeamMemberProvider creation into a factory","createdAt":"2022-06-20T03:05:45Z"}
{"state":"Merged","mergedAt":"2022-06-21T04:28:17Z","number":1902,"body":"Will enable in the next PR once we have the SCM service consuming the events from `hooks_scm` queue","mergeCommitSha":"e9b01db425934c0aab8efb8ac19d93aa33113914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1902","title":"Add GitHubPullRequestReviewCommentEvent and GitHubPullRequestEvent handlers","createdAt":"2022-06-20T04:48:20Z"}
{"state":"Merged","mergedAt":"2022-06-20T18:29:49Z","number":1903,"body":"- Added IP filtering rule to limit access to /api/hooks \r\n- Added GitHub web hooks IPs to whitelist \r\n- Removed old rule which was commented out","mergeCommitSha":"acb1a9d515969e87dab985326662ef34d3a3b197","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1903","title":"limit access to webhooks endpoint","createdAt":"2022-06-20T05:44:27Z"}
{"state":"Merged","mergedAt":"2022-06-20T17:36:14Z","number":1904,"body":"Dave: will need this to \"route\" the message to the appropriate handler.","mergeCommitSha":"bfb2b19529377bf79b577bf8a1083f0c6a35d895","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1904","title":"Webhook queue message contains SCM provider","createdAt":"2022-06-20T17:21:06Z"}
{"state":"Merged","mergedAt":"2022-06-20T23:46:29Z","number":1905,"body":"Part 1 of https://linear.app/unblocked/issue/UNB-297/consume-webhook-events-in-scm-service to check that consuming from the queue works.\r\n\r\nNext PRs will deserialize, decompress, and route the webhooks to the correct handler.","mergeCommitSha":"d5a5de0b0d2712029240069e7e59da9a3e15be4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1905","title":"Add ScmWebhookProcessingJob","createdAt":"2022-06-20T17:26:52Z"}
{"state":"Merged","mergedAt":"2022-06-22T17:10:11Z","number":1906,"body":"Add fallback logic for when Hub app falls back to either Web extension or eventually dashboard.\r\n\r\nTODO:\r\nRequire Popup designs for clients.","mergeCommitSha":"e3130054080c0b36dc972001ae0f0fcb90b2099b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1906","title":"Add fallback URL Logic","createdAt":"2022-06-20T17:39:27Z"}
{"state":"Merged","mergedAt":"2022-06-21T21:10:46Z","number":1907,"body":"![CleanShot 2022-06-20 at 10 56 46](https://user-images.githubusercontent.com/********/174656503-bf4f9a22-4a6e-4e47-b949-dcb1e0de4888.gif)\r\n","mergeCommitSha":"471d295d3d27b844e8d64a4a143edd14b88307c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1907","title":"UNB-165 Hook up install downloadUrl to web landing page","createdAt":"2022-06-20T17:57:20Z"}
{"state":"Merged","mergedAt":"2022-06-20T18:15:49Z","number":1908,"body":"https://api.github.com/organizations/19292465","mergeCommitSha":"d6dee5d69a049ea03085ddbb19675390a0f06ad0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1908","title":"Add Ben Test Org","createdAt":"2022-06-20T18:01:26Z"}
{"state":"Closed","mergedAt":null,"number":1909,"body":"For testing and demoing onboarding (and maybe other purposes???) we want to delete the repo from our API services.\r\n\r\nAdded basic deleting capabilities to admin console to delete repos. Would be nice to have an \"alert\" to block these requests but wasn't able to figure that out with kotlinx SSR and confirm dialogs...\r\n\r\nAn alternative to deleting the repos would be to add a \"isDeleted\" flag for soft deletes?","mergeCommitSha":"eefa1c88f4ddda9949f79bddd37e83e82eba0f08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1909","title":"Admin console delete repo","createdAt":"2022-06-20T18:46:11Z"}
{"state":"Merged","mergedAt":"2022-01-31T22:13:54Z","number":191,"body":"Continue renaming:\r\n\r\n1. `SourceMark` → `SourceMarkGroup` (#186)\r\n2. `SourceMark` → `SourceMarkGroup` in API (#189)\r\n3. `SourceVector` → `SourceMark` (this change)\r\n4. Relate `SourceMark` → `Message`","mergeCommitSha":"bc00a07d8bf1f11345dacda69ad01ee7c6d57c4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/191","title":"Rename [3 of 4]: SourceVector -> SourceMark","createdAt":"2022-01-31T21:58:22Z"}
{"state":"Merged","mergedAt":"2022-06-20T19:16:32Z","number":1910,"body":"Interesting that a unit test wouldn't have caught this since every `suspendingDatabaseTest` is a transaction block\r\n\r\nMaybe the problem here is allowing the caller to call it without a transaction. Without the default null value, the caller would be forced to open a transaction","mergeCommitSha":"ed19936e12d00928a849c39935aac3507dd35d4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1910","title":"Wrap in transaction","createdAt":"2022-06-20T18:50:48Z"}
{"state":"Merged","mergedAt":"2022-06-20T22:57:50Z","number":1911,"body":"![image](https://user-images.githubusercontent.com/********/174664590-8b2abd52-6c7c-4b72-b63f-61de8b964b98.png)\r\n\r\nMake it 12px per Dennis' feedback\r\n\r\nhttps://linear.app/unblocked/issue/UNB-291/font-sizes-for-context-menus-are-different-sizes-should-they-be","mergeCommitSha":"16bb328469c4106b49afdfbc712aa239b3cb6a28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1911","title":"UNB-291 Make context menus 12px size","createdAt":"2022-06-20T19:06:34Z"}
{"state":"Merged","mergedAt":"2022-06-20T22:48:45Z","number":1912,"body":"Fixes UNB-200\r\n\r\nWhen starting a new discussion, use the full line range (ie, start to end of every line).  We keep putting logic for how to do range conversion and processing in different files, so I moved this into a central place and wrote some tests.  Running tests involved mocking out some VSCode types, since we don't have those at runtime.","mergeCommitSha":"fd93584149e490ce903401ebf42ec4944dfa33c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1912","title":"Use full-range lines when creating new discussions","createdAt":"2022-06-20T19:22:48Z"}
{"state":"Merged","mergedAt":"2022-06-21T18:15:59Z","number":1913,"body":"We need the ability to add at mentions to contributors list in create discussion form.\r\n\r\nTo do that:\r\n1. We create new editor extensions for Mentions with function `insertMentionNode`. We piggy back on that call to trigger a callback `onMention` passed to MessageEditor.\r\n2. We modify KnowledgeForm functionality to add mentioned contributor.\r\n3. We modify `updatedContributorsWithIdentity` functionality to allow for using teamMemberId to find identity.\r\n4. We fix bug with `addContributor` functionality as it was using email for contributor id.\r\n\r\nOther Changes:\r\n1. Followed @matthewjamesadam recommendation to move away from weird nested callback behaviour for onKeyDown and onChange handlers. I agreed with him.\r\n\r\nTODO:\r\n1. Handle @mention deletions (pain in the ass, kind of).\r\n2. Annoyingly, we hash emails for teamMembers. This is annoying as I now have to make an extra call to get the email for the team member.\r\n\r\n\r\n![CleanShot 2022-06-20 at 13 53 51](https://user-images.githubusercontent.com/3806658/174676529-33be0d9c-5375-43e0-8c82-facb149a8d1c.gif)\r\n","mergeCommitSha":"7f26236a74789403b3274ec932b4d37f95de4440","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1913","title":"Add at mentions to contributors list","createdAt":"2022-06-20T20:50:44Z"}
{"state":"Merged","mergedAt":"2022-06-21T00:19:06Z","number":1914,"body":"Making changes mentioned here https://chapter2global.slack.com/archives/C03JLD3PN4X/p1655750591766849\r\n\r\nLatest build no: 184\r\nFilename: unblocked-installer-1.0.184.pkg","mergeCommitSha":"50a2d5289f3875b97f9183d00959b6f7fdf21d0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1914","title":"Switch to new version scheme","createdAt":"2022-06-20T20:57:12Z"}
{"state":"Merged","mergedAt":"2022-06-20T22:05:52Z","number":1915,"body":"<img width=\"1239\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/174678443-5d45f806-1887-416f-8872-82385067fbfd.png\">\r\n\r\nthis specific email was somehow right aligned from the extra td element. I noticed that this block was wrapped in an if conditional in the other templates but not this one -- tested on parcel and it seems to be aligned properly now? will test manually once deployed to be sure ","mergeCommitSha":"c8e8bcc9ab9182206eec7bfb38f2d123b2cff956","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1915","title":"Fix email alignment","createdAt":"2022-06-20T21:15:08Z"}
{"state":"Merged","mergedAt":"2022-06-21T16:16:27Z","number":1916,"body":"In case we are unable to generate fileHash, use commit hashes on page to find source marks","mergeCommitSha":"46e815bbdeff4f52dd7df80a4cf75f437b3932a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1916","title":"Use commit hash as backup for source mark resolution","createdAt":"2022-06-20T21:53:45Z"}
{"state":"Merged","mergedAt":"2022-06-20T22:19:07Z","number":1917,"mergeCommitSha":"3129f9902f2cd7ed9a9123919bf96772c74364b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1917","title":"Adds HMAC secret validation for github webhook events","createdAt":"2022-06-20T22:00:39Z"}
{"state":"Merged","mergedAt":"2022-06-20T22:22:58Z","number":1918,"mergeCommitSha":"867b5b86202aaae689bb2092897f4fc8e15d33ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1918","title":"Nothing to see here","createdAt":"2022-06-20T22:22:37Z"}
{"state":"Merged","mergedAt":"2022-06-20T23:32:53Z","number":1919,"mergeCommitSha":"917cb9388a5762da059e3e38e97b3113fa2d485d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1919","title":"Add test for merging an open PR","createdAt":"2022-06-20T22:32:18Z"}
{"state":"Merged","mergedAt":"2022-02-01T03:20:33Z","number":192,"body":"Continue renaming:\r\n\r\n1. `SourceMark` → `SourceMarkGroup` (#186)\r\n2. `SourceMark` → `SourceMarkGroup` in API (#189)\r\n3. `SourceVector` → `SourceMark` (#191)\r\n4. Relate `SourceMark` → `Message` (this change)\r\n\r\n---\r\n\r\n<img width=\"1228\" alt=\"Screen Shot 2022-01-31 at 19 04 14\" src=\"https://user-images.githubusercontent.com/1798345/151907268-c6b2dbde-3da4-4e63-8210-71a5da6f884d.png\">\r\n\r\n","mergeCommitSha":"d140ad8449c26fc9d30ff3834a641b6c67255ee4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/192","title":"Rename [4 of 4]: Message relates to SourceMark","createdAt":"2022-01-31T23:20:36Z"}
{"state":"Merged","mergedAt":"2022-06-20T23:22:31Z","number":1920,"mergeCommitSha":"0ca152fa5bcae1918269a75d1614383225585fc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1920","title":"Update json","createdAt":"2022-06-20T22:55:28Z"}
{"state":"Merged","mergedAt":"2022-06-20T23:02:41Z","number":1921,"mergeCommitSha":"20e41412d8f3adf21a623bd49d52b570b0dec675","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1921","title":"fix link","createdAt":"2022-06-20T23:02:28Z"}
{"state":"Merged","mergedAt":"2022-06-20T23:39:00Z","number":1922,"mergeCommitSha":"abfa03074cd540789caa7b763e7e8de839e1b8a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1922","title":"Fix sourcepoint column bug","createdAt":"2022-06-20T23:24:53Z"}
{"state":"Merged","mergedAt":"2022-06-24T03:36:02Z","number":1923,"body":"The job should continue to pick off the priority queue until its empty before taking from the non-priority queue.","mergeCommitSha":"62a23b00f126aed80fed4c0b4d1916d497b4246c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1923","title":"PullRequestIngestionJob pulls from the priority queue before taking from the non-priority queue","createdAt":"2022-06-20T23:31:34Z"}
{"state":"Merged","mergedAt":"2022-06-21T00:08:55Z","number":1924,"mergeCommitSha":"e58e1316fa89adf1e9a9373ec76d438341ccfdf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1924","title":"Add team member store","createdAt":"2022-06-20T23:48:58Z"}
{"state":"Merged","mergedAt":"2022-06-21T00:49:00Z","number":1925,"body":"Since the SES retry config is identical without and without the config override, I'm just cleaning up to avoid confusion.\n\nFrom debugger, AWS SDK retries by default:\n\n![sdk retry.png](https://graphite-user-uploaded-assets.s3.amazonaws.com/YvgTLHS5ceSd78zAcJxj/c6d44695-8018-48b0-bf0a-c41722174fe5/sdk%20retry.png)","mergeCommitSha":"b1a8bb921475fa91a90aa43e0a1e30f7d50135f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1925","title":"AWS SDK retries by default so remove unnecessary override","createdAt":"2022-06-21T00:34:38Z"}
{"state":"Merged","mergedAt":"2022-06-21T01:00:12Z","number":1926,"body":"- Scale up web hook service in prod \r\n- Deployed new EKS service account to grant scm service access to hooks_scm queue","mergeCommitSha":"dcd09b2205605596d055b4a9fe40588b72eb23de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1926","title":"scale up webhooks service","createdAt":"2022-06-21T00:45:48Z"}
{"state":"Merged","mergedAt":"2022-06-21T01:14:31Z","number":1927,"mergeCommitSha":"afe091d4fc957be93d4492371200906a0eabeee4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1927","title":"Hook up webhook-service to SQS queue","createdAt":"2022-06-21T00:49:17Z"}
{"state":"Merged","mergedAt":"2022-06-21T02:13:12Z","number":1928,"mergeCommitSha":"807f207352712b55f0bab541fe98ccea6f99354d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1928","title":"Serialize GitHub webhook headers properly","createdAt":"2022-06-21T01:58:49Z"}
{"state":"Merged","mergedAt":"2022-06-21T04:13:59Z","number":1929,"body":"Just stubs","mergeCommitSha":"5427e9448a5122a416d813f60fc4084313948f93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1929","title":"Webhook Handler Plumbing","createdAt":"2022-06-21T03:57:05Z"}
{"state":"Merged","mergedAt":"2022-02-02T21:23:21Z","number":193,"body":"* Move `Icon` and `UserIcon` components to the shared/ folder\r\n* Move common styles into the shared directory and leveraging `@forward` to reference the shared styles in the client directories\r\n* Add webUtils folder for shared consumption","mergeCommitSha":"c0f6d64b7ac471e16c0b600edb03af55f0fd1c4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/193","title":"Move components into shared directory","createdAt":"2022-02-01T00:16:24Z"}
{"state":"Merged","mergedAt":"2022-06-21T05:43:18Z","number":1930,"mergeCommitSha":"70722eb9582bd322ae109d20d8f5a3bece180980","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1930","title":"Wire up pull request webhook handlers","createdAt":"2022-06-21T05:27:01Z"}
{"state":"Merged","mergedAt":"2022-06-21T19:09:02Z","number":1931,"body":"Cleaner, more focused.\r\n\r\n![image](https://user-images.githubusercontent.com/1798345/174725258-a1863e6a-7852-4bf9-9d9f-2f66966400b4.png)\r\n","mergeCommitSha":"c7ecb57b672482e5f832caf6ff0e5bda57cae7be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1931","title":"Reduce max threads from 25 to 10 in WebExt, Web, VSCode","createdAt":"2022-06-21T05:47:26Z"}
{"state":"Merged","mergedAt":"2022-06-21T07:11:39Z","number":1932,"body":"When we reply to a PR thread from Unblocked, that will lead to a webhook sent to us after we post to GitHub, causing us to create that message (again).\r\n\r\nLet's ignore the `create` event if the message has an Unblocked signature. \r\n\r\nYup, super hacky but not sure if there's a better way. Anyways, this will address in the meantime while we think of something better.","mergeCommitSha":"e56b70f3c75b2663f60bfff91551f54df4e4422f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1932","title":"Don't handle webhooks for messages created from Unblocked","createdAt":"2022-06-21T06:46:09Z"}
{"state":"Merged","mergedAt":"2022-06-21T23:23:31Z","number":1933,"mergeCommitSha":"4abcfc0224cf1857bdc9f094c5686c2b6a304b36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1933","title":"Wire up TeamMember push","createdAt":"2022-06-21T16:16:44Z"}
{"state":"Merged","mergedAt":"2022-06-29T04:03:19Z","number":1934,"body":"Follow up to https://github.com/NextChapterSoftware/unblocked/pull/1932, though think that PR is still needed since there is a possible race condition here.","mergeCommitSha":"9994b0832563449ffe37373dfd763742445e051f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1934","title":"Dont create the message or thread if the comment exists","createdAt":"2022-06-21T17:11:41Z"}
{"state":"Merged","mergedAt":"2022-06-21T20:54:54Z","number":1935,"body":"Add a new `StreamOverlay` class.  This allows for temporary mutations in the middle of a Stream.  Hopefully the comments make it clear what I'm going for here, the idea is to make something generic that can be used in any stream.\r\n\r\nI got rid of the ApiDataStreamOverlay as it wasn't being used.  This will supersede the DataCacheStreamOverlay once it's done.\r\n\r\n\r\nThis PR just adds the basic framework.  I'm hooking this into stores in the next PR that stacks on this.","mergeCommitSha":"0361887ac8b7967441a0794bef0c86b072c096a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1935","title":"New Stream overlay","createdAt":"2022-06-21T17:26:24Z"}
{"state":"Merged","mergedAt":"2022-06-21T21:10:21Z","number":1936,"mergeCommitSha":"2d5c3f4196dc9fef8b244a29bf5a4626443a29fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1936","title":"Get AgentType from ViewThread operation call","createdAt":"2022-06-21T18:14:36Z"}
{"state":"Merged","mergedAt":"2022-06-21T22:10:36Z","number":1937,"mergeCommitSha":"****************************************","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1937","title":"Adds HMAC validation for GH webhooks","createdAt":"2022-06-21T18:20:48Z"}
{"state":"Merged","mergedAt":"2022-06-21T19:23:05Z","number":1938,"body":"![CleanShot 2022-06-21 at 11 32 47](https://user-images.githubusercontent.com/3806658/174873031-26faffb8-0368-4e2d-a409-2440cbeed1de.gif)\r\n","mergeCommitSha":"ceac430e64bbc0a2e9fe209ddd0e38875a9915bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1938","title":"Delete mentioned contributors","createdAt":"2022-06-21T18:31:10Z"}
{"state":"Merged","mergedAt":"2022-06-23T18:40:43Z","number":1939,"body":"Trigger thread events once a day per user/team tuple.\r\n\r\nSimilar pattern to how BaseAPI was setup.","mergeCommitSha":"3db4c700efcf268b2958feffcbb39d2ae9f5efe5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1939","title":"Client triggered thread events","createdAt":"2022-06-21T18:56:18Z"}
{"state":"Merged","mergedAt":"2022-02-01T00:42:30Z","number":194,"mergeCommitSha":"8b6ab5d4343bd8526c649e0f08b182f9718f8cf1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/194","title":"Add more logging","createdAt":"2022-02-01T00:38:55Z"}
{"state":"Merged","mergedAt":"2022-06-21T20:15:26Z","number":1940,"body":"part of https://linear.app/unblocked/issue/UNB-321/onboarding-polish ","mergeCommitSha":"3ad5fa4ead26be41f45b482442db4c5c3109a7a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1940","title":"[Onboarding] Spacing and bug fixes","createdAt":"2022-06-21T19:23:36Z"}
{"state":"Merged","mergedAt":"2022-06-22T17:11:36Z","number":1941,"mergeCommitSha":"31e9ef8c1771ea287eb10b9f9942d563f402fdd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1941","title":"Fix button styling","createdAt":"2022-06-21T19:54:41Z"}
{"state":"Merged","mergedAt":"2022-06-22T16:00:15Z","number":1942,"body":"Add support for dashboard redirecting.\r\n\r\nWhen VSCode triggers GH Install, it will navigate users to this dashboard install route `/install` with two query parameters.\r\n1. the target location\r\n2. The redirect location for `/install/redirect` which is stored in LocalStorage\r\n\r\nOnce GH install is done, GH will redirect to `/install/redirect`. If the localStorage variable exists, will promptly redirect immediately.\r\n\r\nUI is TODO need designs.\r\nVSCode implementation is also todo.","mergeCommitSha":"fe8038b49d170d9cde82169a7dcd0fc6ef69f033","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1942","title":"Dashboard redirect route","createdAt":"2022-06-21T20:51:58Z"}
{"state":"Merged","mergedAt":"2022-06-21T22:36:54Z","number":1943,"mergeCommitSha":"1ee342fa1cee48ddd662e57d9d55227fd76d8315","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1943","title":"Move stores away from caches directory","createdAt":"2022-06-21T20:55:20Z"}
{"state":"Merged","mergedAt":"2022-06-22T16:25:02Z","number":1944,"body":"Handle exceptions thrown by the sourcemark engine when querying for sourcemarks in a file.  If we don't handle exceptions here, the sourcemark gutter will not render at all.","mergeCommitSha":"f539e28db30c8c538cdcb0c85a41d4d10c91d348","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1944","title":"Handle SM exception","createdAt":"2022-06-21T20:59:21Z"}
{"state":"Merged","mergedAt":"2022-06-21T21:36:31Z","number":1945,"body":"<img width=\"329\" alt=\"CleanShot 2022-06-21 at 14 29 00@2x\" src=\"https://user-images.githubusercontent.com/1553313/174900592-b68ed177-874c-4ed8-9153-79ebe9e89205.png\">\r\n","mergeCommitSha":"041e24c53ab526c2fe7363d4558eda0ea994fe74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1945","title":"Update title","createdAt":"2022-06-21T21:30:34Z"}
{"state":"Merged","mergedAt":"2022-06-22T15:59:49Z","number":1946,"body":"Were not properly cleaning out the auth promise singleton in most failure cases.\r\n\r\nThis led to never actually refreshing.","mergeCommitSha":"7c704294b3ef18767e4e51956211e1c636c654dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1946","title":"Fix Refresh Issue in Web Extension","createdAt":"2022-06-21T22:01:35Z"}
{"state":"Merged","mergedAt":"2022-06-21T23:10:09Z","number":1947,"mergeCommitSha":"297c15b9584ed9b9409817f828cb90ee75f4d2d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1947","title":"Adds success logging for HMAC auth","createdAt":"2022-06-21T23:07:46Z"}
{"state":"Merged","mergedAt":"2022-06-21T23:43:44Z","number":1948,"mergeCommitSha":"8c3f8496e7dd7e5c88fbd0ae42a214ba8ad570f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1948","title":"Log auth error and delete message from scm webhook queue","createdAt":"2022-06-21T23:43:22Z"}
{"state":"Merged","mergedAt":"2022-06-22T17:18:10Z","number":1949,"body":"* Quick pass at dashboard so that things don't look too broken in smaller viewports\r\n<img width=\"236\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/174914314-f5f3dea6-11b6-4fde-8694-1336fd3efe14.png\">\r\n\r\n* Sort user icon stack by latest message authored\r\n<img width=\"120\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/174914415-7d9a4095-b74c-43a8-be7c-672bfe66c190.png\">\r\n\r\n* other small nits","mergeCommitSha":"45f26849250a49af57bcd16f59d70c3de89b148a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1949","title":"Dashboard updates","createdAt":"2022-06-21T23:45:16Z"}
{"state":"Merged","mergedAt":"2022-02-01T00:57:54Z","number":195,"mergeCommitSha":"f1a701d9ea34837b425b30c27630a75369d1700e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/195","title":"Update service tokens","createdAt":"2022-02-01T00:57:47Z"}
{"state":"Merged","mergedAt":"2022-06-22T23:40:36Z","number":1950,"body":"Message operations (create, update, remove) are reflected immediately in the main thread UI.  Updates are *not* reflected in the thread listing UIs in the sidebar, that will come in a future PR.\r\n\r\n![CleanShot 2022-06-21 at 17 09 48](https://user-images.githubusercontent.com/2133518/174916546-c50d1502-963e-402e-a031-24f9f6f2c44d.gif)\r\n","mergeCommitSha":"feeabf3b3612aa9f18d67923b5cd1b5c1e69cc7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1950","title":"Message operations are now reflected in the UI immediately","createdAt":"2022-06-22T00:07:38Z"}
{"state":"Merged","mergedAt":"2022-06-22T04:32:11Z","number":1951,"body":"Several API resources have been moved out of beta so we have to update helm charts before upgrading to Kube 1.22\r\n- Modified helm charts to use v1 version for Ingress related APIs \r\n- Regenerated helm charts. \r\n\r\nDetails here: https://kubernetes.io/docs/reference/using-api/deprecation-guide/#v1-22","mergeCommitSha":"2d88d63c730e27920d974f98263d6071f4484984","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1951","title":"update API resource service versions","createdAt":"2022-06-22T04:00:32Z"}
{"state":"Merged","mergedAt":"2022-06-22T14:18:35Z","number":1952,"mergeCommitSha":"8c2d3807ee4eadb9c9dd8d1a2e14ab78a3e6072c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1952","title":"message mentions","createdAt":"2022-06-22T05:11:30Z"}
{"state":"Merged","mergedAt":"2022-06-23T22:09:57Z","number":1953,"body":"Adds two new models\r\n\r\n1. `UserEngagementModel` <- captures user activity events\r\n2. `UserEngagementMetricsModel` <- stores daily, weekly, and monthly active users metrics\r\n\r\nThe admin console will run a job periodically every hour checking to see if the previous days metrics have been calculated and if not, it will do so.\r\n\r\nDAU is calculated as the unique active users between 00:00:00 PST and 23:59:59.999999 PST for a given day.\r\n\r\nWAU is calculated for a 7-day period starting at 00:00:00 PST on the first day and ending 23:59:59.999999 PST on the last day. \r\n\r\nMAU is calculated like WAU except for a 30-day period.\r\n\r\n(PST was chosen arbitrarily here)\r\n\r\nAdmin console is a work in progress\r\n\r\n![CleanShot 2022-06-22 at 20 19 05](https://user-images.githubusercontent.com/1924615/175200018-7fe0be7d-ff6c-4b07-b5f7-435a52b994b6.png)\r\n","mergeCommitSha":"75b83f77acf0e882ae5f3ade3b5bb7ec71a11b11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1953","title":"Calculate DAU, WAU, and MAU","createdAt":"2022-06-22T08:29:21Z"}
{"state":"Merged","mergedAt":"2022-06-22T14:48:18Z","number":1954,"mergeCommitSha":"5ae732dea051280a06083c6dfb5859c230f6c604","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1954","title":"rename","createdAt":"2022-06-22T14:45:48Z"}
{"state":"Merged","mergedAt":"2022-06-22T19:21:04Z","number":1955,"body":"- It's safe, because it's reversible.\n- It's fast, because it just updates a single model.\n\nFuture intent:\n1. release the ability to delete a team as functionality to end-users\n2. automatically purge soft-deleted teams 60 days after soft deletion (use `modifiedAt`)","mergeCommitSha":"3bc272844b375521d1c266795414e1bee903f837","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1955","title":"Ability to soft-delete a team","createdAt":"2022-06-22T15:28:55Z"}
{"state":"Merged","mergedAt":"2022-06-23T23:30:10Z","number":1956,"body":"- New events\r\n- New deserialization tests for events above\r\n- New handlers, mostly stubs for now (because this PR is getting massive)","mergeCommitSha":"6fc607d90bc467e574e3cbbd7130f4c9543e959b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1956","title":"GitHub Webhook Event Fixtures and Derserializers","createdAt":"2022-06-22T15:54:57Z"}
{"state":"Merged","mergedAt":"2022-06-22T22:54:22Z","number":1957,"mergeCommitSha":"8bb87f666fada420526b3d1c29ba2dbbdfd864db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1957","title":"Rename Events to Metrics","createdAt":"2022-06-22T16:59:50Z"}
{"state":"Merged","mergedAt":"2022-06-22T18:07:00Z","number":1958,"body":"- Introduced an environment variable to allow for adding extra JVM args from Kube deployment spec. \r\n- Added a JVM CPU arg to prefer CPU quota over CPU shares. \r\n\r\nGoing based on info provided here: https://mucahit.io/2020/01/27/finding-ideal-jvm-thread-pool-size-with-kubernetes-and-docker/\r\n\r\nThe goal is to better understand CPU requirements of JVM with regards to multi-threaded apps so we could standardize our API instance resources. Once done we can simply scale out the number of pods to accommodate more users rather than constantly tuning JVM params. ","mergeCommitSha":"bde64d5e853eafdf3d27413397360ad01acb4803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1958","title":"Testing cpu related jvm args","createdAt":"2022-06-22T17:30:34Z"}
{"state":"Merged","mergedAt":"2022-06-22T18:10:53Z","number":1959,"mergeCommitSha":"0da06d12c8e96f5e9f64c2bb1147782f4dbf4c74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1959","title":"Fix stuff","createdAt":"2022-06-22T17:50:41Z"}
{"state":"Merged","mergedAt":"2022-02-01T05:15:46Z","number":196,"body":"Follow on from [#192.](https://github.com/NextChapterSoftware/unblocked/pull/192#issuecomment-1026425881)","mergeCommitSha":"ed0b01f80f06768da619a8d7ab51ca69f7438896","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/196","title":"Rename Chat -> Thread","createdAt":"2022-02-01T03:29:53Z"}
{"state":"Merged","mergedAt":"2022-06-24T00:18:19Z","number":1960,"mergeCommitSha":"fe09b576b0968774b533abde98364ec11e098065","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1960","title":"bump","createdAt":"2022-06-22T18:28:27Z"}
{"state":"Merged","mergedAt":"2022-06-22T19:21:23Z","number":1961,"body":"Reverts NextChapterSoftware/unblocked#1958\r\n\r\n```\r\nOperating System Metrics:\r\n    Provider: cgroupv1\r\n    Effective CPU Count: 1\r\n    CPU Period: 100000us\r\n    CPU Quota: 40000us\r\n    CPU Shares: 204us\r\n    List of Processors, 4 total:\r\n    0 1 2 3\r\n    List of Effective Processors, 4 total:\r\n    0 1 2 3\r\n    List of Memory Nodes, 1 total:\r\n    0\r\n    List of Available Memory Nodes, 1 total:\r\n    0\r\n    Memory Limit: 512.00M\r\n    Memory Soft Limit: Unlimited\r\n    Memory & Swap Limit: 512.00M\r\n\r\nopenjdk version \"17.0.2\" 2022-01-18\r\nOpenJDK Runtime Environment (build 17.0.2+8-86)\r\nOpenJDK 64-Bit Server VM (build 17.0.2+8-86, mixed mode, sharing)\r\nError: Could not find or load main class\r\nCaused by: java.lang.ClassNotFoundException:\r\n```","mergeCommitSha":"6b75015231a6d3d1d0eb1d375b37a14e2d376ab0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1961","title":"Revert \"Testing cpu related jvm args\"","createdAt":"2022-06-22T18:32:31Z"}
{"state":"Merged","mergedAt":"2022-06-22T23:17:57Z","number":1962,"body":"<img width=\"839\" alt=\"CleanShot 2022-06-22 at 12 03 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/175116811-ee340d21-2de7-4f6b-80f5-9699313c3f2b.png\">\r\n<img width=\"1144\" alt=\"CleanShot 2022-06-22 at 12 04 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/175116819-56b4c628-4898-4f93-8c96-8e333a6139da.png\">\r\n\r\nUI is a bit different from the designs. The colours, sizes & etc. seem quite custom for this UI at the moment and I'm wondering if the large styling refactor would fix a lot of those issues...\r\n","mergeCommitSha":"dd2d2d9a4d6cbd113c00ad0bdd1417fd1d7a5bad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1962","title":"Setup web ignore fallback dialog","createdAt":"2022-06-22T19:07:28Z"}
{"state":"Merged","mergedAt":"2022-06-23T02:16:49Z","number":1963,"body":"<img width=\"463\" alt=\"CleanShot 2022-06-22 at 12 57 19@2x\" src=\"https://user-images.githubusercontent.com/858772/175125030-c4c7a33a-2487-47c6-b213-c2b08f2c4814.png\">\r\n<img width=\"506\" alt=\"CleanShot 2022-06-22 at 12 57 37@2x\" src=\"https://user-images.githubusercontent.com/858772/175125088-dd2bfbc0-5e64-4a84-a2a5-8c43eb9048d1.png\">\r\n\r\n","mergeCommitSha":"7f1fc21880fca4c2e0c6644ff795a28133408930","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1963","title":"Brings in new update dialog designs, minus stars effect","createdAt":"2022-06-22T19:56:44Z"}
{"state":"Merged","mergedAt":"2022-06-22T20:39:33Z","number":1964,"mergeCommitSha":"8fc9367f61a7d42aad94179c58053bf63abb5750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1964","title":"Test integration","createdAt":"2022-06-22T19:57:18Z"}
{"state":"Merged","mergedAt":"2022-06-23T03:50:09Z","number":1965,"body":"dashboard\r\n<img width=\"360\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175135905-152ca693-2af2-48b5-91f1-fa17dda3385d.png\">\r\n\r\nextension\r\n<img width=\"319\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175135941-cbb752bd-0d4f-4cfc-b26a-09a8d340e0e3.png\">\r\n\r\nvscode\r\n<img width=\"242\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175136135-c9c3a0e7-f0cb-4bfd-94a4-b7b616ca0863.png\">\r\n","mergeCommitSha":"2652d61304a51363a8485e6182ba68ec48d72c57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1965","title":"Add thread-level context menu","createdAt":"2022-06-22T21:07:08Z"}
{"state":"Merged","mergedAt":"2022-06-22T21:36:13Z","number":1966,"body":"Changing java cpu allocation to take quota based allocation over cpu share based one. Tested locally with our current JDK version and hopefully this time it won't break the deployment. \r\n\r\n\r\n\r\n> Unlike cpu.share, the quota is based on time period and not based on available CPU power. cfs_period_us is used to define the time period, it’s always 100000us (100ms). k8s has an option to allow to change this value but still alpha and feature gated. The scheduler uses this time period to reset the used quota. The second file, cfs_quota_us is used to denote the allowed quota in the quota period.\r\n","mergeCommitSha":"2b10ebf508990a3efbd9f4f0d0db4b6796558901","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1966","title":"Change CPU allocation to quota based","createdAt":"2022-06-22T21:21:33Z"}
{"state":"Merged","mergedAt":"2022-06-22T23:51:54Z","number":1967,"mergeCommitSha":"7fceb4ddaab5a4d0c8426cf1869b225abea69e33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1967","title":"Jeff/unb 327 web extension interstitial dialog","createdAt":"2022-06-22T22:35:08Z"}
{"state":"Merged","mergedAt":"2022-06-23T18:23:56Z","number":1968,"body":"Reverts NextChapterSoftware/unblocked#1966\r\n\r\nI think I have figured out the issue but want to keep this on hand so I can test without it later tonight. ","mergeCommitSha":"5dd8f3c7f0f4377646217993ef598575d5401519","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1968","title":"Revert \"Change CPU allocation to quota based\"","createdAt":"2022-06-22T22:39:05Z"}
{"state":"Merged","mergedAt":"2022-06-22T23:09:51Z","number":1969,"body":"placeholder\r\n<img width=\"408\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175167720-2571279c-91d0-44b0-b16a-37a221767f3c.png\">\r\n\r\nwith text\r\n<img width=\"409\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175167735-920c0671-c30e-4c0c-a383-dcf761044ac9.png\">\r\n","mergeCommitSha":"ecd43e5764eb6d55540c46b558dff35e5ceb00a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1969","title":"Align the search input styling with other search inputs in vscode","createdAt":"2022-06-22T22:55:02Z"}
{"state":"Merged","mergedAt":"2022-02-01T05:34:37Z","number":197,"body":"Allows us to reference the workflow-specific check-for-changes jobs.","mergeCommitSha":"3a69727cc54d5473d9eaac8923a78565e50f407d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/197","title":"Workflow: separately name check-for-changes jobs","createdAt":"2022-02-01T05:24:04Z"}
{"state":"Closed","mergedAt":null,"number":1970,"body":"Needed for Installation Flow per env.\r\n\r\n","mergeCommitSha":"8c4154b8cb5c6acce676e2e186208fb127941685","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1970","title":"Added dev gh app","createdAt":"2022-06-23T00:08:07Z"}
{"state":"Closed","mergedAt":null,"number":1971,"body":"Setup new GH App for dev.\r\n\r\nhttps://github.com/organizations/NextChapterSoftware/settings/apps/dev-unblocked-app","mergeCommitSha":"0937f57c2201fe3364ea7016dc73f400876368be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1971","title":"Add new dev conf","createdAt":"2022-06-23T00:09:02Z"}
{"state":"Merged","mergedAt":"2022-06-23T18:33:28Z","number":1972,"body":"This pr now shows the at mention icon when a user has been at mentioned for an unread message.\r\nThe at mention overrides unread icon.\r\n\r\n\r\nTESTING:\r\nFor the purposes of this demo, I made all messages unread when vended from the api service for the testing purposes. :)\r\n\r\n![CleanShot 2022-06-22 at 18 22 48](https://user-images.githubusercontent.com/3806658/175187407-ea576a29-ca20-4e41-b970-7635838cf67a.gif)\r\n","mergeCommitSha":"d8ac3880ad8def4e445291b84c806c23d0b2a0ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1972","title":"Add icons for unread at mentions","createdAt":"2022-06-23T01:20:07Z"}
{"state":"Merged","mergedAt":"2022-06-23T20:55:05Z","number":1973,"body":"Upgrade dialog and hub state will look like this when the upgrade is forced:\r\n\r\n<img width=\"521\" alt=\"CleanShot 2022-06-22 at 19 26 26@2x\" src=\"https://user-images.githubusercontent.com/858772/175193947-27b4b36e-69ac-49a1-96f8-b7815d7d7c77.png\">\r\n\r\n\r\nAlso modified update UX to match designs:\r\n\r\n![CleanShot 2022-06-22 at 19 14 52](https://user-images.githubusercontent.com/858772/175192787-e1176f2a-7e87-422e-a6bf-fff4b3c68fcd.gif)\r\n\r\n","mergeCommitSha":"37d6533b5bfacddfe448af88023da2efd8cf2cf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1973","title":"Version Obsoletion behaviour in the Hub","createdAt":"2022-06-23T01:39:43Z"}
{"state":"Closed","mergedAt":null,"number":1974,"body":"\r\n<img width=\"494\" alt=\"CleanShot 2022-06-22 at 20 16 41@2x\" src=\"https://user-images.githubusercontent.com/858772/175199767-e308dac7-3547-4e66-9603-9650ff062440.png\">\r\n\r\n","mergeCommitSha":"aea0a38a05ef61141e640f2ac0d9c3687f356bc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1974","title":"Re-orders avatars to put latest message author on top","createdAt":"2022-06-23T03:10:26Z"}
{"state":"Merged","mergedAt":"2022-06-23T18:33:17Z","number":1975,"body":"Re-do of https://github.com/NextChapterSoftware/unblocked/pull/1971\r\n\r\nIncludes all GitHub app secrets, including webhook secret. GitHub App has been properly configured to expect these secrets","mergeCommitSha":"70b4b306b6168b8f192ed42a314b31a36f7c4708","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1975","title":"GitHub Dev App secrets and config","createdAt":"2022-06-23T15:46:38Z"}
{"state":"Merged","mergedAt":"2022-06-27T20:20:35Z","number":1976,"body":"Creating and deleting threads now takes effect immediately in the sidebar UIs.\r\n\r\nFixes UNB-192.","mergeCommitSha":"fe0758e6b75475f5a1720155e397206b94f6d310","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1976","title":"Thread operations display in UI immediately","createdAt":"2022-06-23T17:13:24Z"}
{"state":"Merged","mergedAt":"2022-06-23T18:53:07Z","number":1977,"body":" CPU requests are only used when scheduling pods. It's the CPU limit which controls how CGrpups treat a service and whether or not allow it to burst and use idle cpu cycles\r\n\r\nI'll be publishing a doc with all the details about how these values get configured. This is just a first round of increase in resources to prepare for some performance tests.\r\nThere will be a few more of these before we get the final numbers.","mergeCommitSha":"6923062d6fd3c5bb4844f23d065caad92b108319","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1977","title":"Increase CPU limit and request in prod","createdAt":"2022-06-23T18:37:39Z"}
{"state":"Merged","mergedAt":"2022-06-23T22:41:31Z","number":1978,"body":"Continuation of https://github.com/NextChapterSoftware/unblocked/pull/1942\r\n\r\nVSCode will drop users off at the install route which drops off a redirect location before continuing to GH.","mergeCommitSha":"417eef1959a77b01f7c756d998818f53adc19797","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1978","title":"Update dropoff URL","createdAt":"2022-06-23T19:22:54Z"}
{"state":"Merged","mergedAt":"2022-06-24T17:06:29Z","number":1979,"body":"As part of https://linear.app/unblocked/issue/UNB-337/avatar-ordering-correctness-and-consistency\r\n\r\nVscode:\r\n<img width=\"355\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175383241-e3be8188-bb43-427a-b362-438675c30d74.png\">\r\n![CleanShot 2022-06-23 at 12 40 18](https://user-images.githubusercontent.com/********/175383746-226e2840-1ee1-49a9-a94f-cf72857a11ed.gif)\r\n\r\nExtension:\r\n<img width=\"256\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175383032-8c827fae-2f13-4c15-9602-20f51a4b2299.png\">\r\n\r\nDashboard:\r\n<img width=\"206\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175383197-ec37b65b-70a3-4700-bc06-e3edfc20a200.png\">\r\n","mergeCommitSha":"49854716f5ef9df7defd3ca1fb36d03fdb6b957a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1979","title":"Update client icon stacking","createdAt":"2022-06-23T19:38:19Z"}
{"state":"Merged","mergedAt":"2022-02-01T06:01:09Z","number":198,"body":"- separate `Threads` and `ThreadParticipants`\r\n- plural resources","mergeCommitSha":"92647894963a4539d5771642b29df07f5eb1a2ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/198","title":"API cleanup","createdAt":"2022-02-01T05:51:54Z"}
{"state":"Merged","mergedAt":"2022-06-23T20:10:27Z","number":1980,"body":"- Fix emails\r\n- Update\r\n- Update\r\n","mergeCommitSha":"eb39a35f44d385c38a4cb390c25a679b4ca358c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1980","title":"FixEmails","createdAt":"2022-06-23T19:43:59Z"}
{"state":"Merged","mergedAt":"2022-06-23T20:31:36Z","number":1981,"body":"2xlarge defines the acceptable on-demand price. This will make the system use 4xlarge instances when available and under the 2xlarge price. ","mergeCommitSha":"08fb58065f715be1a9215216c1cfe8bae4bd4ea3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1981","title":"Make spot instances use 4xlarge machines","createdAt":"2022-06-23T20:09:17Z"}
{"state":"Merged","mergedAt":"2022-06-23T21:06:03Z","number":1982,"body":"Fixes UNB-324. Handle all the various combination of thread types.","mergeCommitSha":"1450e187d46f1a21662fa1f04c9b82fda99f7dd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1982","title":"Add Open PR (green) gutter icons","createdAt":"2022-06-23T20:56:11Z"}
{"state":"Merged","mergedAt":"2022-06-24T17:05:56Z","number":1983,"body":"fixes:\r\n<img width=\"662\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175397968-b70349a0-3731-4162-b196-049dc93762ac.png\">\r\n\r\nafter:\r\n<img width=\"611\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/175432434-bc9f0b6f-1fc9-452e-a26a-a2cdbbc71f90.png\">\r\n","mergeCommitSha":"28730072746ed9bc025a6e5dae37494d6d17112d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1983","title":"Fix responsiveness","createdAt":"2022-06-23T20:56:16Z"}
{"state":"Merged","mergedAt":"2022-06-24T03:21:05Z","number":1984,"body":"<img width=\"1432\" alt=\"CleanShot 2022-06-23 at 15 50 00@2x\" src=\"https://user-images.githubusercontent.com/1924615/175427724-93445f7f-b542-4994-8a84-9bcc5a70dcd4.png\">\r\n","mergeCommitSha":"485eb6d18702f0fec37c32ebb3898367fc13dce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1984","title":"First pass at hacking together a metrics chart","createdAt":"2022-06-23T22:53:01Z"}
{"state":"Merged","mergedAt":"2022-06-23T23:43:51Z","number":1985,"mergeCommitSha":"876af6f49d64f0c8b6563dd9cfdece00107a72ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1985","title":"Add user specific logging","createdAt":"2022-06-23T23:41:26Z"}
{"state":"Merged","mergedAt":"2022-06-27T22:08:51Z","number":1986,"body":"Styling colours and font sizes is off from designs as we do not have these newish constants in our system.\r\nWill revisit with larger refactor in the coming days.\r\n\r\n\r\nError state when navigating from VSCode -> GH:\r\n<img width=\"996\" alt=\"CleanShot 2022-06-23 at 16 44 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/175433075-1090775e-67af-44ac-861f-33769cb8005d.png\">\r\n\r\nComing back from GH -> VSCode:\r\n<img width=\"1203\" alt=\"CleanShot 2022-06-23 at 16 35 41@2x\" src=\"https://user-images.githubusercontent.com/1553313/175433078-5d43506a-e60b-4c0a-aa31-6dc85ac53af3.png\">\r\n\r\n","mergeCommitSha":"4757228ecec44fac768f6a81c0fc49c80bd13080","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1986","title":"Jeff/unb 313 redirect back to unblocked after gh","createdAt":"2022-06-23T23:50:45Z"}
{"state":"Merged","mergedAt":"2022-06-24T00:00:54Z","number":1987,"body":"All servcies were depending on postgres to ensure localstack was up for docker compose.\r\n\r\nThat's not right.\r\nShould make it service-specific.","mergeCommitSha":"e1653ad22c2f2e69a15b30936d5be62a2ed1228d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1987","title":"Fix dependencies on localstack","createdAt":"2022-06-23T23:59:31Z"}
{"state":"Merged","mergedAt":"2022-06-24T00:14:09Z","number":1988,"body":"It's causing flakes","mergeCommitSha":"d73edbe91fd36acfe8c97e2eff6df695df12d5ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1988","title":"Remove unncessary check","createdAt":"2022-06-24T00:13:09Z"}
{"state":"Merged","mergedAt":"2022-06-24T01:35:38Z","number":1989,"mergeCommitSha":"80c8febe4a2624d72f61fe7350384a7dd98a713a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1989","title":"Exclude deleted teams from admin statistics","createdAt":"2022-06-24T00:24:34Z"}
{"state":"Merged","mergedAt":"2022-02-01T22:42:57Z","number":199,"mergeCommitSha":"cfd5a020a7a21e99fb771035c96035d49bd78309","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/199","title":"Add create/updateThread and create/updateMessage operations pop","createdAt":"2022-02-01T06:30:17Z"}
{"state":"Merged","mergedAt":"2022-06-24T01:27:03Z","number":1990,"mergeCommitSha":"52614f3d4a2fd671870a536186e286352087068b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1990","title":"Allow Klue in PROD","createdAt":"2022-06-24T01:26:40Z"}
{"state":"Merged","mergedAt":"2022-06-24T01:40:35Z","number":1991,"mergeCommitSha":"97f14857c262dfd86e24d62ccf9b95e665107722","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1991","title":"Add thread paticipant store","createdAt":"2022-06-24T01:27:46Z"}
{"state":"Merged","mergedAt":"2022-06-24T02:42:27Z","number":1992,"mergeCommitSha":"6c67abcbfa3a45913a43d1b81bc60a6436dac1eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1992","title":"update","createdAt":"2022-06-24T02:14:23Z"}
{"state":"Merged","mergedAt":"2022-06-24T03:06:19Z","number":1993,"mergeCommitSha":"5ec9354883688d92e42b1625b43b445a1aa4385a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1993","title":"Log number of changed files for pull request","createdAt":"2022-06-24T02:49:59Z"}
{"state":"Merged","mergedAt":"2022-06-24T03:46:44Z","number":1994,"mergeCommitSha":"351ca3eacb8047a31934f88ab3eefbbf77f43ccd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1994","title":"Update Swift GRPC","createdAt":"2022-06-24T03:38:40Z"}
{"state":"Merged","mergedAt":"2022-06-24T03:49:50Z","number":1995,"mergeCommitSha":"6f336748b9c902f23b2e125a94979731d833c097","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1995","title":"Missed a config change","createdAt":"2022-06-24T03:48:48Z"}
{"state":"Merged","mergedAt":"2022-06-24T17:25:43Z","number":1996,"body":"Ordering is now as follows:\n1. Author\n2. Participants that have not yet posted messages, ordered by join date ascending\n3. Participants that have posted messages, ordered by message recency ascending\n","mergeCommitSha":"d646295636b807d5e8757d8956dc87cf8ae52f11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1996","title":"Define new thread participant ordering","createdAt":"2022-06-24T03:58:36Z"}