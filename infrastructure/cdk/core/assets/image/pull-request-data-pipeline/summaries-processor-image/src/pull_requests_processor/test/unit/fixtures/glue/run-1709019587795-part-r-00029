{"state":"Merged","mergedAt":"2023-05-24T18:21:56Z","number":6232,"body":"We need to provide a way to allow the client to enable GitHub issue ingestion. Not sure if this is the best approach so putting this up in hopes of getting some thoughts. \r\n\r\nAs long as the team is a GitHub or GitHub enterprise team, then enabling ingestion is just a matter of flipping a boolean i.e. no additional configuration is needed. \r\n\r\nPerhaps this would make more sense in a team settings object?","mergeCommitSha":"f173825319b2ee00e3aba50876c348dcfc42d8c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6232","title":"Add operations to enable GitHub issues ingestion","createdAt":"2023-05-18T16:51:29Z"}
{"state":"Merged","mergedAt":"2023-05-18T18:02:25Z","number":6233,"mergeCommitSha":"24b7b150905c2f5dd6e9801dcf536c07e5a3b51b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6233","title":"Fix routing bug in onboarding","createdAt":"2023-05-18T17:50:26Z"}
{"state":"Merged","mergedAt":"2023-05-18T18:30:43Z","number":6234,"mergeCommitSha":"b9bd846d90c32bb08bd3a17beccc8eccf80dad6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6234","title":"Approve all teams","createdAt":"2023-05-18T18:07:11Z"}
{"state":"Merged","mergedAt":"2023-05-18T18:32:00Z","number":6235,"body":"Now that we have Bitbucket support, we should update our logic to allow detecting Bitbucket pull request links in Slack and Jira content.","mergeCommitSha":"0882e7758104e14164b8d8f9c272818305688acf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6235","title":"Update PullRequestUrl to detect Bitbucket pull request links","createdAt":"2023-05-18T18:09:48Z"}
{"state":"Merged","mergedAt":"2023-05-18T19:56:14Z","number":6236,"body":"# Before\r\nBefore this fix the user would see user-owned and team-owned Bitbucket workspaces. In this example the user is only an admin of user-owned workspaces, which has led to a confusing experience:\r\n\r\n<img width=\"900\" alt=\"with user owned\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/b5d131d4-3fdb-4c0a-adc3-3de17d07ed10\">\r\n\r\n\r\n# After\r\nWith this change, the same user would only see team-owned Bitbucket workspaces, and since this user has no team-owned workspace they will see this page:\r\n\r\n<img width=\"900\" alt=\"no user owned\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/5cbe215e-5e9f-4fd1-88f4-036bd412ce12\">\r\n","mergeCommitSha":"0756e5ea3d6123c803c07080f5f5000e68a3c230","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6236","title":"Exclude user-owned Bitbucket workspaces","createdAt":"2023-05-18T18:21:24Z"}
{"state":"Merged","mergedAt":"2023-05-18T19:00:15Z","number":6237,"mergeCommitSha":"c046a4d7352f738c2ad3dbff4302d0d7b9119c69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6237","title":"Source code data pipeline","createdAt":"2023-05-18T18:29:24Z"}
{"state":"Merged","mergedAt":"2023-05-18T18:59:42Z","number":6238,"mergeCommitSha":"94ef4bef86addc4411a6cba3d7c03f39010e519e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6238","title":"Disable delete artifacts","createdAt":"2023-05-18T18:59:32Z"}
{"state":"Merged","mergedAt":"2023-05-18T19:58:10Z","number":6239,"mergeCommitSha":"89b4f82f9de43e15009f018898d032c18fbfada5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6239","title":"Set userAccessAllowed false by default for new teams","createdAt":"2023-05-18T19:10:37Z"}
{"state":"Merged","mergedAt":"2022-03-21T20:43:32Z","number":624,"body":"As per https://chapter2global.slack.com/archives/C02HEVCCJA3/p1647644202689599\r\n\r\n`LastModified` and `IfModifiedSince` are standard headers and have standard behaviour that browsers expect.  Our usage of these headers is non-standard, and the browser behaviour was preventing the dashboard UI from working correctly, so we'll switch to custom headers `X-Unblocked-Last-Modified` and `X-Unblocked-If-Modified-Since`","mergeCommitSha":"8fb4a1476e9dc9572012e546b11a9abde3b6169e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/624","title":"Use custom headers for LastModified and IfModifiedSince","createdAt":"2022-03-21T19:46:57Z"}
{"state":"Merged","mergedAt":"2023-05-18T20:20:08Z","number":6240,"body":"i.e.\r\n\r\n```\r\n{\r\n  \"PreProcessSourceCodeEmbeddings\": {\r\n    \"ProcessingJobName\": \"RichieIsLovely3\",\r\n    \"ProcessOutput\": \"s3://source-code-data-pipeline-sandbox-dev-us-west-2\",\r\n    \"ProcessEnvironment\": {\r\n      \"richie\": \"isAwesome\"\r\n    }\r\n  }\r\n}\r\n```","mergeCommitSha":"ba24140164966dd74167c526362e869ef0d05908","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6240","title":"Add ability to specify process environment for source mark embeddings","createdAt":"2023-05-18T20:11:31Z"}
{"state":"Merged","mergedAt":"2023-05-18T20:45:52Z","number":6241,"body":"Required, non-breaking.\n\nhttps://chapter2global.slack.com/archives/C05664YMGKW/p1684435315297239","mergeCommitSha":"f20b5e9e23dd44259757b560a856f5e2708aa866","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6241","title":"Add createdAt to Team API model","createdAt":"2023-05-18T20:25:20Z"}
{"state":"Merged","mergedAt":"2023-05-18T21:22:25Z","number":6242,"body":"When dashboard initializes a GH installation flow, redirect should send users to integrations installation page on complete.\r\n\r\nVSCode based installations will provide a redirectURL that should still be respected.\r\nIf Jetbrains initializes the installation flow, we should render `you can close the tab` page.","mergeCommitSha":"b5c6ee2f0e4e078a618a831c059e69cd20f337fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6242","title":"Redirect to integrations if web installation","createdAt":"2023-05-18T20:53:40Z"}
{"state":"Merged","mergedAt":"2023-05-18T21:38:09Z","number":6243,"mergeCommitSha":"fd025b32a4a72ed575cce8542a1ff398b8b99fa7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6243","title":"Make https://bitbucket.org/chapter2/ internal","createdAt":"2023-05-18T21:12:37Z"}
{"state":"Merged","mergedAt":"2023-05-18T23:07:26Z","number":6244,"mergeCommitSha":"46329542954c16566d134848c584e2043c9672ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6244","title":"Redirect to base dashboard","createdAt":"2023-05-18T22:05:16Z"}
{"state":"Merged","mergedAt":"2023-05-18T22:21:07Z","number":6245,"body":"I'm pretty sure Gradle will just resolve the first main() it finds if it gets a miss on the configured main class but \uD83E\uDD37‍♂️","mergeCommitSha":"54bc86618180444e13fef74b949bdcf85189e117","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6245","title":"Wrong main class for application","createdAt":"2023-05-18T22:11:51Z"}
{"state":"Merged","mergedAt":"2023-05-30T16:59:02Z","number":6246,"body":"Uses black + poetry + pytest.","mergeCommitSha":"39b7eeb6ebe7cc5f1c616becfaa2d75eee005eed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6246","title":"Some source code processing utils","createdAt":"2023-05-18T22:27:51Z"}
{"state":"Closed","mergedAt":null,"number":6247,"mergeCommitSha":"d0647fb9771eb351ec5212e3ce070b42f9a389e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6247","title":"[BREAKS API ON MAIN] Make redirectUrl required field","createdAt":"2023-05-18T22:31:56Z"}
{"state":"Merged","mergedAt":"2023-05-18T23:17:31Z","number":6248,"body":"After connecting SCM Installations, new teams are created.\r\nIf we want to utilize those new team's resources immediately, we need to update the local stores with new auth token (to include new team's claim) and new teams","mergeCommitSha":"2476e58c5ea86a25eeb3e516ca0e6131bf6eb124","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6248","title":"Refresh Auth and Teams before redirect","createdAt":"2023-05-18T22:55:19Z"}
{"state":"Merged","mergedAt":"2023-05-19T00:19:31Z","number":6249,"body":"Fixes UNB-1268","mergeCommitSha":"24602399cfeb8c0e97152069c4bed64fcc6bacd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6249","title":"Trigger search reindexing after archiving closed PR threads","createdAt":"2023-05-18T23:01:28Z"}
{"state":"Merged","mergedAt":"2022-03-21T21:33:12Z","number":625,"body":"- Added flags to enable gzip compression on dashboard endpoint\r\n- Copied the same caching policy as dashboard endpoint for landing page endpoint (with compression flags)","mergeCommitSha":"e7f446bb0f8b713797e7440036c1460d9d3aa725","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/625","title":"adding gzip compression to cloudfront","createdAt":"2022-03-21T21:32:00Z"}
{"state":"Merged","mergedAt":"2023-05-19T03:42:04Z","number":6250,"mergeCommitSha":"090f22665be8fd009451e50fe2fad77098e990d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6250","title":"Add redirect intermediary for installation","createdAt":"2023-05-18T23:14:55Z"}
{"state":"Merged","mergedAt":"2023-05-24T21:56:52Z","number":6251,"body":"Preliminary work for updating our iconography.  Use clientAssets insight and branding icons for all TS code.\r\n\r\nThere is now one set of helper methods to get the icons for a thread or PR, in InsightIcon.ts, and these return icons imported from the client assets repo, so there is one source of truth.  I will update this with the new (provider-centric) icons in the next PR.","mergeCommitSha":"f62b6a449d622a70bc10f9294d38fe97851ace40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6251","title":"Remove inline insight icons","createdAt":"2023-05-18T23:49:48Z"}
{"state":"Merged","mergedAt":"2023-05-19T03:35:28Z","number":6252,"mergeCommitSha":"a2ca1e9cdddeb61fcb5583faa6b7193aea3ca7eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6252","title":"Do not send slack notification for new internal teams","createdAt":"2023-05-19T00:07:46Z"}
{"state":"Merged","mergedAt":"2023-05-19T00:41:36Z","number":6253,"mergeCommitSha":"efb1b6f86a18fba1b56bab815ecce2c74eac6383","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6253","title":"Remove client install flags for intellij, node, and vscode","createdAt":"2023-05-19T00:32:55Z"}
{"state":"Merged","mergedAt":"2023-05-23T03:12:30Z","number":6254,"mergeCommitSha":"889621a21e3380ebd4f15e35949de7a7d0d30104","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6254","title":"Install Git in source code data pipeline","createdAt":"2023-05-19T00:41:02Z"}
{"state":"Merged","mergedAt":"2023-05-19T02:13:10Z","number":6255,"mergeCommitSha":"db6499373c74a120587d07205504539dcf8a5294","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6255","title":"Add installationId and provider to redirectUrl for exchange auth","createdAt":"2023-05-19T01:02:00Z"}
{"state":"Merged","mergedAt":"2023-05-24T16:13:25Z","number":6256,"body":"Check Editor disposable state before trying to modify editor instance.\r\n\r\nAlso make sure to cleanup editors on dispose","mergeCommitSha":"a38faf16a47094209a2cacb3a3c4bcca451909f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6256","title":"Check Editor Disposal state","createdAt":"2023-05-19T01:04:09Z"}
{"state":"Merged","mergedAt":"2023-05-19T02:16:58Z","number":6257,"mergeCommitSha":"af019ec96f888d6bf15979ed6eb072c091724f1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6257","title":"Should navigate to identities UI first","createdAt":"2023-05-19T02:01:03Z"}
{"state":"Merged","mergedAt":"2023-05-19T03:56:51Z","number":6258,"body":"To more easily distinguish between VSCode and IntelliJ.","mergeCommitSha":"ca6ddc1a3ffd9401f092365acf40f225c05cb565","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6258","title":"Make IntelliJ color different in admin web","createdAt":"2023-05-19T03:55:39Z"}
{"state":"Merged","mergedAt":"2023-05-19T04:19:30Z","number":6259,"mergeCommitSha":"df43da7cc7e757a5b7757735f5c2271c04109a6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6259","title":"Fix typo in web","createdAt":"2023-05-19T03:55:42Z"}
{"state":"Merged","mergedAt":"2022-03-21T22:30:59Z","number":626,"body":"* Enable optimizations for `dev` dashboard build. This enables minification and tree-shaking\r\n* Change the `devtool` option to `source-map`, so we generate separate source map files instead of inlining\r\n* Add analyzer npm scripts","mergeCommitSha":"36f60fa4fa71003efeaede8aeaca7625cd36e197","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/626","title":"Optimize dev dashboard build","createdAt":"2022-03-21T21:43:46Z"}
{"state":"Merged","mergedAt":"2023-05-19T17:13:47Z","number":6260,"mergeCommitSha":"940a4c2ca916d7b8c8cb9e8f4e4a4060b4b34042","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6260","title":"Fix redirect url once more","createdAt":"2023-05-19T17:03:52Z"}
{"state":"Merged","mergedAt":"2023-05-19T17:32:22Z","number":6261,"mergeCommitSha":"ff8aa138c4af874e7c5ba28954851c46958640dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6261","title":"Generify state machine triggers","createdAt":"2023-05-19T17:17:21Z"}
{"state":"Merged","mergedAt":"2023-05-19T21:58:36Z","number":6262,"mergeCommitSha":"b040e833e1dc2de3fc1f3bb577c6a4f6d9da3426","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6262","title":"Add resilience to IDE installation step during onboarding","createdAt":"2023-05-19T19:19:37Z"}
{"state":"Merged","mergedAt":"2023-05-19T21:09:02Z","number":6263,"mergeCommitSha":"fed7ad35a7df718152b6c3a776637ed7b4daabd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6263","title":"Refresh auth and team listing before navigating","createdAt":"2023-05-19T20:29:33Z"}
{"state":"Merged","mergedAt":"2023-05-19T21:03:59Z","number":6264,"body":"- Source code state machine\r\n- Add more code\r\n","mergeCommitSha":"ebb6660e4365066ec842763eb72451b9e95c09e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6264","title":"AddSourceCodeStateMachine","createdAt":"2023-05-19T20:44:29Z"}
{"state":"Merged","mergedAt":"2023-05-19T21:11:37Z","number":6265,"mergeCommitSha":"d8d8ffe200acfed0318fd2c9bb4331aa82a747d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6265","title":"Fix identities redirect","createdAt":"2023-05-19T20:59:35Z"}
{"state":"Merged","mergedAt":"2023-05-19T22:56:47Z","number":6266,"mergeCommitSha":"97ff9534bc52f438bcc50e523a2077394e5e827c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6266","title":"Poll refreshing token and teams","createdAt":"2023-05-19T21:55:16Z"}
{"state":"Merged","mergedAt":"2023-05-19T22:50:58Z","number":6267,"mergeCommitSha":"43fda6a88940201b024c741a64cc83d331eff4b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6267","title":"Unb 1292 send slack notifications for topic/expert activity","createdAt":"2023-05-19T22:05:22Z"}
{"state":"Merged","mergedAt":"2023-05-23T02:06:32Z","number":6268,"mergeCommitSha":"e4ebc211943545b745bfe3c9d248e275942d19b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6268","title":"Whitelist additional test accounts in dev","createdAt":"2023-05-19T22:12:44Z"}
{"state":"Merged","mergedAt":"2023-05-19T22:29:14Z","number":6269,"mergeCommitSha":"2f796baf8e584cf142ee43dc853928a5d1206df3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6269","title":"Fix state machine invocation","createdAt":"2023-05-19T22:28:08Z"}
{"state":"Merged","mergedAt":"2022-03-21T22:22:51Z","number":627,"body":"Not sure why I forgot to add this when I implemented this endpoint...","mergeCommitSha":"6e7b25f65a53d629a0b26d1a34839826c0e06b30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/627","title":"Return latestModified header for getUnreads","createdAt":"2022-03-21T22:02:23Z"}
{"state":"Merged","mergedAt":"2023-05-19T22:56:18Z","number":6270,"mergeCommitSha":"95807f00a6f0fcb29a807fc0d123cb10c4bb6da9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6270","title":"Fix build","createdAt":"2023-05-19T22:56:13Z"}
{"state":"Merged","mergedAt":"2023-05-24T02:48:44Z","number":6271,"body":"1. Order files by name\r\n2. Dump unused files/classes","mergeCommitSha":"b0d6a8057ca3fd4aa553c7262833bc65f33a2a28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6271","title":"Late friday project cleanup time","createdAt":"2023-05-19T23:08:37Z"}
{"state":"Merged","mergedAt":"2023-05-23T16:23:08Z","number":6272,"mergeCommitSha":"70589c3208ad11218292d86acffb159a5a29a67e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6272","title":"Add Jira Issue sync job","createdAt":"2023-05-19T23:15:02Z"}
{"state":"Merged","mergedAt":"2023-05-23T03:13:20Z","number":6273,"body":"- removes identity tokens and ttl\r\n- unhook person\r\n- fail refresh unless person exists\r\n\r\nfixes: UNB-867\r\nfixes: UNB-959","mergeCommitSha":"b5181f8228e38921c9dbee5d2cc510108f9f8274","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6273","title":"Unblocked should immediately clean up if access is remotely revoked from identity provider","createdAt":"2023-05-19T23:34:51Z"}
{"state":"Merged","mergedAt":"2023-05-23T17:56:36Z","number":6274,"mergeCommitSha":"cc8e98ec37793a8a94342131d1318d6f2e32feed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6274","title":"Add PeriodicAsyncStream tests","createdAt":"2023-05-19T23:46:31Z"}
{"state":"Merged","mergedAt":"2023-05-20T02:13:47Z","number":6275,"mergeCommitSha":"2edca23fbc318c9694bec0b8820b091dcf0872b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6275","title":"Add a link for the team and updated the message text","createdAt":"2023-05-20T00:00:01Z"}
{"state":"Merged","mergedAt":"2023-05-23T21:45:21Z","number":6276,"body":"…elected too\r\n\r\n`TopicStore`: changes the filter for \"UserSelected\" aka the golden record topic and the default value of a `Approved`. Previously we would not classify topics with topic.relevant < 0 in the userselected, but we want all the user rated topics in this list and then we will show the approved or unapproved status in that list in the `TopicPage` (see below). Note this also, perhaps more importantly changes the default topic.relevant value from 1.0 to 0.0 for \"Approved\" topics.\r\n\r\n`TopicPage`: changes \"non-Approved\" to show ✅ or ❌ approved or unapproved values so we can see how the topic sources are doing from adminweb.\r\n\r\nhttps://linear.app/unblocked/issue/UNB-1291/ml-adding-a-new-topic-in-the-dashboard-should-put-the-topic-in","mergeCommitSha":"00a86c19a0c9e25d1bbdf57d66b73ebd4918f78b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6276","title":"ML: Adding a new topic in the Dashboard should put the topic in UserS…","createdAt":"2023-05-20T05:23:01Z"}
{"state":"Merged","mergedAt":"2023-05-23T03:38:15Z","number":6277,"mergeCommitSha":"ab0cb61e851759e136be0744e846cd356493364c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6277","title":"Update internal users","createdAt":"2023-05-23T03:24:28Z"}
{"state":"Closed","mergedAt":null,"number":6278,"body":"Uses jest and puppeteer to drive a headless chrome browser, so it's fast:\r\n- https://pptr.dev\r\n- https://jestjs.io/docs/puppeteer\r\n\r\nInvoke as\r\n```\r\ncd webTest && npm test\r\n```","mergeCommitSha":"5f9b81b76157b28a0f8f96949e2c35ac5f5d468c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6278","title":"[POC] Web UI Tests","createdAt":"2023-05-23T03:30:35Z"}
{"state":"Merged","mergedAt":"2023-05-24T22:17:04Z","number":6279,"body":"I tried to keep a very simple solution here: whenever we add a channel to the poller, fetch it immediately, outside of the regular tick/tock polling cycle.  This has two benefits:\r\n\r\n1) It's quicker -- we don't have to wait for the current poll and tick/tock cycle to complete.  The dashboard will probably load a bit quicker with this change\r\n2) If the pusher API is completely broken and requests are blocking, the UI will actually work, since the stores are never ultimately dependant on the `lastModified` call.","mergeCommitSha":"5dc456a6cd1c5b112a0de8e918eadb072f0e08be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6279","title":"Don't block fetch calls on pusher API","createdAt":"2023-05-23T03:52:37Z"}
{"state":"Merged","mergedAt":"2022-03-21T22:46:07Z","number":628,"body":"<img width=\"431\" alt=\"Screen Shot 2022-03-21 at 3 37 56 PM\" src=\"https://user-images.githubusercontent.com/2133518/159374498-05c7af16-7e63-4c69-8fb1-73371863f685.png\">\r\n","mergeCommitSha":"1f8110917b1c081f9c736b536cba82c9a74c0922","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/628","title":"Rename VSCode extension","createdAt":"2022-03-21T22:38:06Z"}
{"state":"Merged","mergedAt":"2023-05-23T17:29:09Z","number":6280,"mergeCommitSha":"83962238b1d3ce9abcbb8df90cc7da7aa9c6b2b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6280","title":"Fix name length on processing job names","createdAt":"2023-05-23T17:27:16Z"}
{"state":"Merged","mergedAt":"2023-05-23T18:31:01Z","number":6281,"mergeCommitSha":"dad6c44e988de54e5bb0d50c8a7ec32223d8ad7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6281","title":"Update internal teams","createdAt":"2023-05-23T18:15:50Z"}
{"state":"Merged","mergedAt":"2023-05-23T20:23:02Z","number":6282,"mergeCommitSha":"460ddc3cae38bf061989245060f6cf94258f5c20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6282","title":"Set ThreadModel.createdAt to Jira issue createdAt","createdAt":"2023-05-23T19:14:20Z"}
{"state":"Merged","mergedAt":"2023-05-24T22:17:28Z","number":6283,"body":"Update API models:\r\n\r\n- Deprecate `Thread.threadType`.  Thread functionality will be defined by source provider, capability flags, etc.\r\n- Add `Provider.unblocked`, defining content that is Unblocked-native (notes, QAs, walkthroughs)\r\n- Add `Thread.provider`, defining the source for the content\r\n","mergeCommitSha":"f9c6c8a053ede3166b94d1aeff3bb8da4639070c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6283","title":"Update thread type API models","createdAt":"2023-05-23T20:28:56Z"}
{"state":"Open","mergedAt":null,"number":6284,"body":"Properly surface errors for integration/installation streams","mergeCommitSha":"7d86307f39edceb8c9d790033a101bca84e39535","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6284","title":"Surface errors in integration streams","createdAt":"2023-05-23T22:46:11Z"}
{"state":"Merged","mergedAt":"2023-05-24T00:57:52Z","number":6285,"body":"The `getTeams` API was returning teams that were not declared in the JWT `teams` cleam.\r\nThe consequence was that the `getTeams` API behaved unlike every other authenticated API.\r\n\r\nFor example, calling `getTeams` would return `[teamA]`, but when another API made a request\r\nfor a resource (eg: repos) under *teamA* then the API could fail because the claim did not\r\ncontain *teamA*. This inconsistency appears as random behaviour in the clients.\r\n\r\nRelated to #6286.","mergeCommitSha":"17328949d4957e367234ac10958839e078ff71c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6285","title":"GetTeams API is consistent with team JWT claim","createdAt":"2023-05-23T23:01:19Z"}
{"state":"Merged","mergedAt":"2023-05-24T00:36:15Z","number":6286,"body":"TeamStore refresh should be tied to AuthStore updates.\r\nMore specifically, it should only update whenever claims on AuthToken changes.\r\n\r\n","mergeCommitSha":"b9aef97bf93cdf9ebf5c630d57f6cdca15bbcd96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6286","title":"Team scoped auth store stream","createdAt":"2023-05-23T23:28:18Z"}
{"state":"Closed","mergedAt":null,"number":6287,"mergeCommitSha":"e0ea4539f47e1b95cf047dd5b02e2a06d09853f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6287","title":"Route for nth user onboarding","createdAt":"2023-05-24T00:19:08Z"}
{"state":"Merged","mergedAt":"2023-05-30T16:27:46Z","number":6288,"mergeCommitSha":"9342a5d43c9aa24e7ec25fc59ebf45e299a5946c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6288","title":"Ensure a team is selected on first load","createdAt":"2023-05-24T02:53:17Z"}
{"state":"Merged","mergedAt":"2023-05-24T18:23:13Z","number":6289,"body":"- Trigger activemq event on process completion","mergeCommitSha":"81a2efd647533aa96ff2daad889322a1ed0be242","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6289","title":"Need to trigger completion event when source code embeddings is done.","createdAt":"2023-05-24T03:37:17Z"}
{"state":"Merged","mergedAt":"2022-03-22T05:12:46Z","number":629,"body":"`findOrCreateRepo` will come in a separate PR","mergeCommitSha":"2cc07b8a6b920171f76119c489badd7dbff0c9ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/629","title":"Implement getRepos api endpoint","createdAt":"2022-03-21T23:05:03Z"}
{"state":"Merged","mergedAt":"2023-05-24T19:56:54Z","number":6290,"mergeCommitSha":"9cfc6acaa3c66d42789c4e5abbda132401d654b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6290","title":"[Onboarding] Fix navigation after save ","createdAt":"2023-05-24T06:11:17Z"}
{"state":"Merged","mergedAt":"2023-05-24T17:41:05Z","number":6291,"body":"This alarm went crazy a couple of days ago and fired 30 pages in 5 mins. I have added an exclude rule for the Calico container. \r\n\r\nThis change has been deployed to both Dev and Prod","mergeCommitSha":"9382b6c6c83264933661931a40e4f92a0039cf3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6291","title":"Avoid having calico spamming our alarms","createdAt":"2023-05-24T17:38:31Z"}
{"state":"Merged","mergedAt":"2023-05-24T21:29:59Z","number":6292,"mergeCommitSha":"eca916f0789855e1c125dca049d1a9aefa689bff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6292","title":"Create GitHubIssuesSettings","createdAt":"2023-05-24T18:59:46Z"}
{"state":"Merged","mergedAt":"2023-05-24T20:35:07Z","number":6293,"body":"We are already using `TopicSourceType.Approved` to filter these topics.\r\n\r\nThis change is other side of a previous change in `TopicStore.approveExistingTopic` where we set Topic.isRecommended (aka Topic.isRelavant=0).\r\n\r\nThis will mean that we can show `TopicSourceType.Approved` and `Topic.isRecommended` in the main dashboard view.","mergeCommitSha":"4e1763d4989cf1dfb88ab4c1af3b240687203f33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6293","title":"TopicStore: Remove `isApproved` filter from `getApprovedTopics`","createdAt":"2023-05-24T19:38:05Z"}
{"state":"Merged","mergedAt":"2023-07-18T19:29:47Z","number":6294,"mergeCommitSha":"be45369a1802de2c604fb98996ca622bb790b1f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6294","title":"Set up nth user onboarding routing","createdAt":"2023-05-24T20:08:31Z"}
{"state":"Merged","mergedAt":"2023-05-24T21:30:10Z","number":6295,"mergeCommitSha":"5435bff40338f950a42b79e74f578ea761945b16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6295","title":"Add service","createdAt":"2023-05-24T20:12:16Z"}
{"state":"Merged","mergedAt":"2023-05-26T20:11:00Z","number":6296,"body":"Make repo ID optional for threads.\r\n\r\nRepos are unexpected for threads that have a non repo source (e.g. Slack, Jira, etc...)","mergeCommitSha":"83133d5a6dde76bd538c74fabb5b08a4a9e0e1e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6296","title":"[BREAKS API ON MAIN] Optional repo id thread","createdAt":"2023-05-24T20:17:25Z"}
{"state":"Merged","mergedAt":"2023-05-24T22:27:27Z","number":6297,"body":"Remove feature flags for Jira and Scm Settings","mergeCommitSha":"d3597af3705476b8a5d185cbf1107acab62939e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6297","title":"Remove Jira Scm Settings","createdAt":"2023-05-24T20:49:11Z"}
{"state":"Merged","mergedAt":"2023-05-24T23:28:21Z","number":6298,"mergeCommitSha":"c178ecd34432523c4b4dead36f18ad9b21e703e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6298","title":"GitHubIssuesBulkIngestionService queries GitHubIssuesSettingsStore","createdAt":"2023-05-24T21:35:42Z"}
{"state":"Merged","mergedAt":"2023-05-25T00:35:44Z","number":6299,"mergeCommitSha":"f81f9eb6bf4246bfa313eaed27ea323d65682358","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6299","title":"Update internal teams/users","createdAt":"2023-05-24T21:58:56Z"}
{"state":"Merged","mergedAt":"2022-01-18T19:42:30Z","number":63,"mergeCommitSha":"9f608498f65f8bb2cf70f05e9cadedf4f4e0c6ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/63","title":"Remove unnecessary files generated during openapi generation","createdAt":"2022-01-18T19:39:06Z"}
{"state":"Merged","mergedAt":"2022-03-22T04:47:11Z","number":630,"body":"My bad. Resources should be plural.","mergeCommitSha":"4382a954b908f68e9e24145dd8db6fe37a1c1e85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/630","title":"Use teams instead of team in paths","createdAt":"2022-03-21T23:08:07Z"}
{"state":"Merged","mergedAt":"2023-05-24T22:48:57Z","number":6300,"mergeCommitSha":"47f2be3e92ffb9f4ff4db66ea2d3fb1a1f1f0cb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6300","title":"Deprecate feature flags that have been released","createdAt":"2023-05-24T22:00:49Z"}
{"state":"Merged","mergedAt":"2023-05-29T22:56:16Z","number":6301,"mergeCommitSha":"420cdb87dfc3059d54f2f855f8a249778ae000e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6301","title":"Remove sqs","createdAt":"2023-05-24T22:14:28Z"}
{"state":"Merged","mergedAt":"2023-05-24T22:20:20Z","number":6302,"body":"- Remove sqs\r\n- Change lambda name\r\n","mergeCommitSha":"9fc469786e95031ce25dc8cde15825b4ac0b4ed9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6302","title":"RenameLambda","createdAt":"2023-05-24T22:20:13Z"}
{"state":"Merged","mergedAt":"2023-05-24T22:38:34Z","number":6303,"mergeCommitSha":"acbbb148176a2a0c57c7a7dc3246f09896adb3d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6303","title":"Change path to outputpath","createdAt":"2023-05-24T22:38:22Z"}
{"state":"Merged","mergedAt":"2023-05-26T23:01:00Z","number":6304,"body":"Add toggle and migration for `hasCompletedIntegrationsOnboarding`\r\n\r\n<img width=\"1032\" alt=\"CleanShot 2023-05-24 at 16 32 38@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/f3eba750-ca06-41d1-a943-630426915dc9\">\r\n","mergeCommitSha":"0a29c80e515df0a54c846c5064d9de38b0ef61c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6304","title":"Completed integrations flag","createdAt":"2023-05-24T23:33:35Z"}
{"state":"Merged","mergedAt":"2023-05-25T17:27:11Z","number":6305,"body":"We were using `default` clauses in ProviderUtils -- we shouldn't.  It masks issues when new providers are added.\r\n\r\n`Provider.UnknownDefaultOpenApi` can be treated as a default, meaning \"this client doesn't know about the given enum value\".","mergeCommitSha":"e2f3d89576ac9756f57bc852df243ab81f9f4a33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6305","title":"Clean up ProviderUtils","createdAt":"2023-05-24T23:35:36Z"}
{"state":"Merged","mergedAt":"2023-05-25T00:45:56Z","number":6306,"body":"I'm doing this to unblock web builds.  I will look into why the builds are so slow for my next PR","mergeCommitSha":"65e0a6c855ac669193950ce6663006a9cd2a4b0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6306","title":"Web CI builds should be given 20 minutes","createdAt":"2023-05-25T00:29:16Z"}
{"state":"Closed","mergedAt":null,"number":6307,"mergeCommitSha":"3a07236e4b4bfc00e6fe8016bfb2c794d6bc70da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6307","title":"Onboarding wording changes","createdAt":"2023-05-25T04:10:08Z"}
{"state":"Merged","mergedAt":"2023-05-25T16:06:30Z","number":6308,"mergeCommitSha":"9bffdb84819c0934e2246ec717a05c2ce14a9f5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6308","title":"Ability to delete a person","createdAt":"2023-05-25T07:42:46Z"}
{"state":"Merged","mergedAt":"2023-05-25T13:54:55Z","number":6309,"mergeCommitSha":"0234542d56257b9c48e11aed96ead785935dea83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6309","title":"Fix application names in appliation modules","createdAt":"2023-05-25T13:54:41Z"}
{"state":"Merged","mergedAt":"2022-03-22T17:13:28Z","number":631,"body":"Not used https://github.com/NextChapterSoftware/unblocked/pull/622#discussion_r831389754","mergeCommitSha":"8b5129e4950131c04781c461ca16e044d4e253c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/631","title":"Remove sourceMarkGroupIds query parameter","createdAt":"2022-03-22T16:28:07Z"}
{"state":"Merged","mergedAt":"2023-05-25T18:00:58Z","number":6310,"body":"Our makefiles were calling general tasks, which executed across all projects.  This mostly affected `generateProto`, which would ultimately build the kotlin proto libraries.","mergeCommitSha":"4cb097e0fa923e9026c497298fa8696d9d276e01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6310","title":"Ensure that proto generation doesn't rely on building kotlin libs","createdAt":"2023-05-25T17:04:48Z"}
{"state":"Merged","mergedAt":"2023-05-25T18:57:43Z","number":6311,"mergeCommitSha":"b660ec96a3258e7cfc08b014734a1d40e825eab7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6311","title":"Ability to remotely uninstall our GitHub App from a customer GitHub Org","createdAt":"2023-05-25T17:18:19Z"}
{"state":"Merged","mergedAt":"2023-05-25T21:23:11Z","number":6312,"body":"From admin web, but we can plumb this into the delete person button\nand also delete account functionality whenever we get to that.","mergeCommitSha":"2249f3ead1842cb2c877083e1358ea94858a8053","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6312","title":"Unblocked remotely revokes OAuth user authorization grants","createdAt":"2023-05-25T19:28:17Z"}
{"state":"Merged","mergedAt":"2023-05-26T22:22:19Z","number":6313,"body":"Deprecate `ThreadType` and remove its usage throughout all clients.","mergeCommitSha":"6e63047e96d2495470bb25aa936733d83742927c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6313","title":"Deprecate ThreadType","createdAt":"2023-05-25T20:04:33Z"}
{"state":"Merged","mergedAt":"2023-05-26T21:51:54Z","number":6314,"body":"To support answer quality in QA threads:\r\n<img width=\"580\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/368e241a-1367-4222-8a39-bc0dbef60a3d\">\r\n","mergeCommitSha":"728a4e152714a35d0343d6c431db3b5653a3fe78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6314","title":"Add quality metadata for message answers","createdAt":"2023-05-25T21:15:16Z"}
{"state":"Merged","mergedAt":"2023-05-25T22:17:16Z","number":6315,"body":"Whereas there might be a case for uninstalling or deauthorizing without deleting the team or person respectively;\r\nthe inverse is not true, meaning:\r\n- there is never a case where you would want to **delete a team and not uninstall**\r\n- there is never a case where you would want to **delete a person and not de-authorize**.\r\n\r\nThis change _auto-uninstalls_ when deleting a team, and _auto-deauthorizes_ when deleting a person.\r\nOperators have less buttons to hit when resetting for onboarding purposes,\r\nand cannot inadvertently forget to uninstall/deauthorize.\r\n\r\n<img width=\"327\" alt=\"team actions\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/7ab829dc-b974-45c4-91b2-841d997c8965\">\r\n\r\n<img width=\"319\" alt=\"Person actions\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/94e056b6-0192-4eaf-a3e3-82b659f8819d\">\r\n\r\n","mergeCommitSha":"3795df1071fea4b16590bec355e965e6ea3c0aad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6315","title":"Auto-uninstall and auto-deauthorize when deleting team and person","createdAt":"2023-05-25T21:39:27Z"}
{"state":"Merged","mergedAt":"2023-05-25T23:30:56Z","number":6316,"mergeCommitSha":"174c899a81982a3680195be3dc41098e6d8c201a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6316","title":"Person avatar only has SCM providers","createdAt":"2023-05-25T23:25:50Z"}
{"state":"Merged","mergedAt":"2023-05-26T23:52:50Z","number":6317,"body":"This is the actual filter that we have been looking for @davidkwlam!","mergeCommitSha":"ae5303b0cedd25b81e9630cf1ee7e6332c6ab075","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6317","title":"TopicService: Remove `isApproved` filter from `getTopics`","createdAt":"2023-05-26T00:09:56Z"}
{"state":"Merged","mergedAt":"2023-06-08T03:21:35Z","number":6318,"mergeCommitSha":"4eaed005798770b1d18ca93794a34d1447414096","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6318","title":"Data models for ML Exchange and Configuration","createdAt":"2023-05-26T04:27:32Z"}
{"state":"Merged","mergedAt":"2023-05-26T22:47:44Z","number":6319,"body":"Fixes UNB-1305\r\n\r\nI upgraded Slate while I was at it, that's the majority of this PR.","mergeCommitSha":"d873c075f60afdfba7829061f3842787b45c1dc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6319","title":"Fix nested anchors in MessageEditor","createdAt":"2023-05-26T04:52:56Z"}
{"state":"Merged","mergedAt":"2022-03-22T17:21:35Z","number":632,"body":"When auth is finished initializing, but user is not authenticated, threadAndParticipants stream will not be initialized.\r\n\r\nThis causes a dead end loading state where user is prevented from logging in.","mergeCommitSha":"b9477619fdf6683b13b4435f051e90d05b8a09fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/632","title":"Fix loading state for unauthenticated sidebar","createdAt":"2022-03-22T16:56:31Z"}
{"state":"Merged","mergedAt":"2023-05-26T06:36:49Z","number":6320,"body":"- Hugging face llm\r\n- Inference\r\n","mergeCommitSha":"fb7031cf9a62e786449f2bddad6e622dc9023fd3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6320","title":"HuggingFaceLlms","createdAt":"2023-05-26T06:31:14Z"}
{"state":"Merged","mergedAt":"2023-05-26T09:44:54Z","number":6321,"mergeCommitSha":"6417e66cdbd56400519e7871fd14ab0772274e75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6321","title":"Improve llm output","createdAt":"2023-05-26T09:39:59Z"}
{"state":"Merged","mergedAt":"2023-05-26T14:13:38Z","number":6322,"mergeCommitSha":"ff283c8074d1d3077fbdbfa5e0de9deac496243f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6322","title":"Set ThreadModel.createdAt to GitHub issue createdAt","createdAt":"2023-05-26T14:01:04Z"}
{"state":"Merged","mergedAt":"2023-05-26T16:18:51Z","number":6323,"mergeCommitSha":"856c6615b7e25b945465f096fc908a47c7a89bfe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6323","title":"Slack notifier takes into account deleted teams","createdAt":"2023-05-26T16:01:41Z"}
{"state":"Merged","mergedAt":"2023-05-26T23:09:21Z","number":6324,"mergeCommitSha":"2dd58a719e71a67dc5049947190db7a59c42533e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6324","title":"Create message for empty issue body","createdAt":"2023-05-26T16:02:51Z"}
{"state":"Merged","mergedAt":"2023-06-02T21:20:46Z","number":6325,"body":"<img width=\"732\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/9f2b5efb-4dd6-41b7-b313-e8a98a060869\">\r\n\r\n* NOTE: is missing dashboard settings equivalent of connecting/disconnecting issues","mergeCommitSha":"3a26aaed9eb4162fb6a7e72e7066b7563401caa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6325","title":"Add GH issues row to onboarding","createdAt":"2023-05-26T19:30:54Z"}
{"state":"Closed","mergedAt":null,"number":6326,"body":"fixes: UNB-1301\r\nfixes: UNB-1302\r\n\r\nThis is still WIP\r\n- [ ] fix lookup slack token by identity\r\n- [ ] hook up to team delete in admin web\r\n- [ ] hook up to person delete in admin web","mergeCommitSha":"2c685dbb82e0c1573573753f0b7ea53088e1c363","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6326","title":"Remotely uninstall Slack app and de-authorize Slack user","createdAt":"2023-05-26T21:01:45Z"}
{"state":"Merged","mergedAt":"2023-05-26T21:49:08Z","number":6327,"mergeCommitSha":"41b8fd1859437244f00eb3b0287df9be539e0efa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6327","title":"Handle optional repo","createdAt":"2023-05-26T21:30:19Z"}
{"state":"Merged","mergedAt":"2023-05-26T22:47:20Z","number":6328,"body":"Wire up semantic search in the hub:\r\n- Create QA thread in the hub\r\n- Navigate to dashboard thread after\r\n\r\nThis is hidden behind a user default for now, until we hook everything up.","mergeCommitSha":"8fe65b2b8f75ef7090d3955c74886a15502cda94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6328","title":"Semantic search in the hub","createdAt":"2023-05-26T21:30:28Z"}
{"state":"Merged","mergedAt":"2023-06-05T20:49:04Z","number":6329,"mergeCommitSha":"b8d443bd5df49f6b7bf09bfeb3ad36707e1de6cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6329","title":"Add API to update message quality","createdAt":"2023-05-26T22:08:13Z"}
{"state":"Merged","mergedAt":"2022-03-22T18:23:38Z","number":633,"body":"I think this is all we need for now. The relationship between SCM Install and Team is 1:1, so the fields are in the `Team` table. Eventually we might want to add the installation slug so we can do things like determine whether the install has the correct permissions when there are errors. `isScmConnected` is also added to the `Repo` table so we can provide hints to PR ingestion and the clients that action needs to be taken to \"re-connect\".\r\n\r\nFor now we can assume that if the correct permissions haven't been granted, `isScmConnected` is false","mergeCommitSha":"359b70c8ec749406ead3d067819036c9e98bfcfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/633","title":"Adds SCM install fields to data models","createdAt":"2022-03-22T17:19:27Z"}
{"state":"Merged","mergedAt":"2023-06-05T18:55:48Z","number":6330,"body":"API models on messages to represent references used to generate messages.\r\n\r\n<img width=\"629\" alt=\"CleanShot 2023-05-26 at 16 29 43@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/c748d7ac-518f-4451-b95d-fd6c4481c307\">\r\n","mergeCommitSha":"cd7d0f57492c7848e3fe070d8bdb63af9730a0d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6330","title":"API models for reference metadata","createdAt":"2023-05-26T23:30:20Z"}
{"state":"Merged","mergedAt":"2023-05-29T18:57:03Z","number":6331,"mergeCommitSha":"8ab4a7256667ed938e735bc61e67d3896c59ff3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6331","title":"User auth done with slack auth","createdAt":"2023-05-29T18:35:44Z"}
{"state":"Merged","mergedAt":"2023-05-29T19:48:45Z","number":6332,"mergeCommitSha":"41cc4e963e86d7f4e57eb63f36ed88ea632e7d9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6332","title":"Goodbye runtime fixtures","createdAt":"2023-05-29T19:12:11Z"}
{"state":"Merged","mergedAt":"2023-05-29T20:50:14Z","number":6333,"body":"Currently using instructor embedding.\r\nExample of usage can be found under instructor_inference_handler.py\r\n","mergeCommitSha":"f3f5ea999add518945a674d0aa8cc49eea4e7abd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6333","title":"Add huggingface instructor embedding to source code pipeline","createdAt":"2023-05-29T20:04:13Z"}
{"state":"Merged","mergedAt":"2023-05-29T21:51:51Z","number":6334,"body":"For now let's use the `NoopFilteringService` so that we're not filtering out anything. I'm going to test different prompts against our pull requests and threads and then enable filtering with `OpenAIFilteringService` once we land on a good prompt.","mergeCommitSha":"d8a3aab5566ac84c41897173c9af3b4f97ff905a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6334","title":"Add embeddings filter skeleton","createdAt":"2023-05-29T21:27:01Z"}
{"state":"Merged","mergedAt":"2023-05-29T22:54:50Z","number":6335,"body":"I think for now it would be safer to not exclude an insight from embedding unless the LLM explicitly responds with NO. Better to catch instances where its returning an unexpected response because that's a signal we need to improve the prompt.","mergeCommitSha":"349bc91f075dbb45ef4565b2f1d71c1ee15353d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6335","title":"Be safe and dont exclude insights if the model responds with anything other than YES or NO","createdAt":"2023-05-29T22:21:14Z"}
{"state":"Merged","mergedAt":"2023-05-29T22:48:03Z","number":6336,"body":"1. moves source code trigger from team page to repo page\r\n2. pass additional repo/pinecone params to Step Function input\r\n3. step function results link to AWS web resource url for debugging\r\n4. show the nunber of running Step Functions -- just showing this number for now,\r\n   but the intention is to gate Step Function execution on this value.","mergeCommitSha":"885329b56fb6f76f86addf837a73c8b2a935d591","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6336","title":"Move source code execution to repo page, etc","createdAt":"2023-05-29T22:32:17Z"}
{"state":"Merged","mergedAt":"2023-05-29T23:37:14Z","number":6337,"body":"This hits the endpoint and generates a response as expected.\r\n\r\nThe test question is `What color is the sky?`\r\n\r\nI'm only verifying that there is a second message attached to the thread here -- we should mock out the actual semantic search behaviour before adding content verification.\r\n\r\n<img width=\"1178\" alt=\"Screenshot 2023-05-29 at 3 58 21 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/2e41497f-b230-4f6f-b7ca-bbb7d109e12d\">\r\n","mergeCommitSha":"62a7c350ccb78c465002b995b8d7ae1c0334dccf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6337","title":"Hook up semantic search in CreateThreadV3","createdAt":"2023-05-29T23:00:14Z"}
{"state":"Merged","mergedAt":"2023-06-08T03:43:32Z","number":6338,"body":"<img width=\"2255\" alt=\"CleanShot 2023-06-01 at 11 12 02@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/858772/e40f53b0-dfb4-45ff-afe8-b8ba2b7e611f\">\r\n<img width=\"2208\" alt=\"CleanShot 2023-06-01 at 11 12 10@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/858772/15404c1b-f178-4882-9c69-c98d135498c4\">\r\n\r\n","mergeCommitSha":"c0ebea4b0de4dd1859ba942bd5f68019c20ae59b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6338","title":"Template editor in admin console","createdAt":"2023-05-29T23:06:10Z"}
{"state":"Merged","mergedAt":"2023-05-29T23:49:45Z","number":6339,"mergeCommitSha":"5dd8fb1ac3bb1691f18a7676255524e281321e07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6339","title":"Fix running execution banner","createdAt":"2023-05-29T23:29:36Z"}
{"state":"Merged","mergedAt":"2022-03-22T19:49:36Z","number":634,"mergeCommitSha":"80922104cd6fe036056e01cea767298f70e9d5f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/634","title":"Rename TeamMember.isDeactivated to isCurrentMember","createdAt":"2022-03-22T18:18:59Z"}
{"state":"Merged","mergedAt":"2023-05-30T00:02:41Z","number":6340,"body":"FWIW: This same prompt is used as the prefix for the topic/expert summaries. Only changing this in OpenAI for now? Not to sure how this fits in between the new inference config for the QA (and all?) prompts and the search/embedding work, just trying to find a way to change the prompt for what we are using today from adminweb?\r\n\r\nhttps://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/f442b103-77a7-455e-88eb-95c6ebd2f4ea","mergeCommitSha":"bcae71b365da360380d613af1c69a5894c4b9b01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6340","title":"This allows anyone to update the semantic search query from teamsettings.","createdAt":"2023-05-29T23:31:15Z"}
{"state":"Merged","mergedAt":"2023-05-30T00:22:26Z","number":6342,"body":"Simple mock for now","mergeCommitSha":"06d3c747b978a2bf156169d0bb2b765194e05e55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6342","title":"Mock out semantic search service in tests","createdAt":"2023-05-30T00:11:37Z"}
{"state":"Merged","mergedAt":"2023-05-30T02:28:21Z","number":6343,"body":"Oops.","mergeCommitSha":"ae1ac46de0e6c7501edff8b700349a674d7064c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6343","title":"Fix missing parameter when launching QA thread in hub","createdAt":"2023-05-30T02:18:49Z"}
{"state":"Closed","mergedAt":null,"number":6344,"body":"The `repoId` can be empty string `\"\"`","mergeCommitSha":"7a7428dbb3056db7834b8c3524fd5f0e174d7768","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6344","title":"Possible fix for pinecone result decoder","createdAt":"2023-05-30T03:50:45Z"}
{"state":"Merged","mergedAt":"2023-05-30T04:13:55Z","number":6345,"mergeCommitSha":"1bcb106d8e5781e8e5f964ea4a25bc4f341aac54","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6345","title":"Try fixing repoid parsing","createdAt":"2023-05-30T03:55:44Z"}
{"state":"Merged","mergedAt":"2023-05-30T05:42:25Z","number":6346,"mergeCommitSha":"6d310abd8002e0ef01668950dcbb7380da7ac18d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6346","title":"Move to anthropic","createdAt":"2023-05-30T04:51:21Z"}
{"state":"Merged","mergedAt":"2023-05-30T17:56:29Z","number":6347,"body":"Always use the new semantic search mechanism (CreateThread -> navigate to dashboard thread view)","mergeCommitSha":"54d4a020ca1d4f89ffd3705c20672d12ddabbd93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6347","title":"Use new semantic search API","createdAt":"2023-05-30T05:32:39Z"}
{"state":"Merged","mergedAt":"2023-05-30T06:30:51Z","number":6348,"mergeCommitSha":"2bea3d84ea6496ab68040fb2d49c3303954d78c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6348","title":"Forgot embeddings","createdAt":"2023-05-30T06:30:45Z"}
{"state":"Merged","mergedAt":"2023-05-30T06:34:41Z","number":6349,"mergeCommitSha":"c9be4a933c9c8d2f071b53316283a2604d0c7f78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6349","title":"Again","createdAt":"2023-05-30T06:34:33Z"}
{"state":"Closed","mergedAt":null,"number":635,"body":"Fixed issue where team members did not appear in thread UI.\r\nRelated to https://github.com/NextChapterSoftware/unblocked/pull/614\r\n\r\nWith that change, we no longer polled on channels that did not reply with lastModified. \r\nIn VSCode, the poller fetched the data for TeamMembers which was cached in the stream store. When we tried fetching the data again from the Zustand store, the poller refused to fetch as it had already done so for the stream store.\r\n\r\nThe \"quick\" fix here was to go from Zustand -> team store. \r\nLong term, we should remove DataCacheStore and move towards convergence on streams. Will wait for @matthewjamesadam to comment on that one.\r\n\r\n\r\n","mergeCommitSha":"52d1183d7743d5bb41cc837e568b241826dde639","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/635","title":"Fix TeamMembersIssue","createdAt":"2022-03-22T18:48:05Z"}
{"state":"Merged","mergedAt":"2023-05-30T16:21:23Z","number":6350,"mergeCommitSha":"a0bfa15f526252d5020a3bbc3a61af999c6b5560","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6350","title":"Add a test harness for evaluating filtering prompts","createdAt":"2023-05-30T16:07:17Z"}
{"state":"Merged","mergedAt":"2023-05-30T20:54:40Z","number":6351,"mergeCommitSha":"577a26f66a97362bea5d51b6c0438997830dd386","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6351","title":"Source code processor uses source inputs","createdAt":"2023-05-30T20:33:37Z"}
{"state":"Merged","mergedAt":"2023-05-30T23:50:57Z","number":6352,"body":"Wires up Git authentication to the Step Function.\r\n\r\n⚠️ **Warning**: The credentials are passed in plaintext within the git repo url user-info part to the step function. This means that AWS SF and CloudWatch will show the plaintext token.","mergeCommitSha":"dc07fe9a1700fe14a1d7bab873e589f900ed7358","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6352","title":"Pass an authenticated clone url to source code processor","createdAt":"2023-05-30T20:33:40Z"}
{"state":"Merged","mergedAt":"2023-05-30T22:27:21Z","number":6353,"body":"We will continue to discuss #machine-learning topics between the teammembers in that channel and you can invoke @unblocked via the /semanticsearch action, but if you want to tail the @unblocked bot responses to Q&A as well as to its training data, please sub #ask-unblocked in Slack.","mergeCommitSha":"ea22b643d3b3875d328b423009026ac352307854","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6353","title":"Moving @unblocked responses to the #ask-unblocked channel","createdAt":"2023-05-30T22:07:59Z"}
{"state":"Merged","mergedAt":"2023-05-31T00:04:59Z","number":6354,"body":"If there's an error running the search, continue the create the thread, and add the error as the response.  This will help us debug things while we're trying different things.","mergeCommitSha":"c3d5200756db65a236033b36bbe7252eede59aa9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6354","title":"Always add semantic search answer","createdAt":"2023-05-30T22:27:59Z"}
{"state":"Merged","mergedAt":"2023-05-30T23:35:08Z","number":6355,"mergeCommitSha":"5a317536a03852b7668717b2e802e0e2d5cf8b70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6355","title":"Increase antrhopic context size","createdAt":"2023-05-30T23:34:42Z"}
{"state":"Merged","mergedAt":"2023-05-31T16:39:52Z","number":6356,"body":"This is a temporary UI:\r\n\r\n1. We create an initial response message on the thread, saying \"Unblocked is thinking...\"\r\n2. We return the created thread immediately, so the dashboard can load\r\n3. We launch a coroutine that will run the search\r\n4. When the coroutine completes, update the message with the correct response (or error)\r\n\r\nThis is obviously not how we will do this for real -- we will run this through an async process.  Running this as a dangling coroutine is bad form, will fail on deployments, etc, but is useful for this week while we're building this.","mergeCommitSha":"87fef39c793808168e838d4b44f03dda72de954f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6356","title":"Show loading message for semantic search","createdAt":"2023-05-31T00:29:22Z"}
{"state":"Merged","mergedAt":"2023-05-31T00:56:21Z","number":6357,"body":"- Files under 1kB are skipped\n- Hidden directories are skipped\n- Hidden files are skipped\n- Flush process output for debugging","mergeCommitSha":"d4fc642c4afbc4a3097edddc17926d042c991532","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6357","title":"Skip hidden files and directories, and small files","createdAt":"2023-05-31T00:49:44Z"}
{"state":"Merged","mergedAt":"2023-05-31T06:16:33Z","number":6359,"mergeCommitSha":"ac421ba994c03b3b04dc5f99df7f1c180c8e71a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6359","title":"Hook up pinecone API","createdAt":"2023-05-31T06:13:37Z"}
{"state":"Merged","mergedAt":"2022-03-22T19:50:11Z","number":636,"body":"- Modified existing workflows to use SSH tunnelling using our Bastion host instead of VPN. This would allow us to do concurrent deploys. \r\n- Removed dependencies for sequential deploys. All service deploys happen at once upon a successful build \r\n- Added helm charts and values.yaml files for SCM service (note I will be moving all helm charts to projects directories once I have added pusher service) \r\n- Removed the old reusable Github action and replaced it with a new one that supports deploys via SSH \r\n\r\nNote: all helm files were generated by Ansible. I just updated values.yaml files. ","mergeCommitSha":"b2cc0fddd6fc9bb3a0186694eb7bd799584160fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/636","title":"Add scmservice helm and rework CI jobs to deploy it","createdAt":"2022-03-22T19:39:52Z"}
{"state":"Merged","mergedAt":"2023-05-31T16:03:44Z","number":6360,"mergeCommitSha":"9305c90d7859aea8d5b94ab5afd8d5181b58013b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6360","title":"Fix pinecone secret lookup","createdAt":"2023-05-31T16:02:45Z"}
{"state":"Merged","mergedAt":"2023-05-31T18:21:03Z","number":6361,"body":"includ epython source files in embeddings.","mergeCommitSha":"5b2b30c37a0fdaddb8dffb18c5b1bf08cf5e13d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6361","title":"Include python source files","createdAt":"2023-05-31T18:20:27Z"}
{"state":"Merged","mergedAt":"2023-05-31T18:31:25Z","number":6362,"mergeCommitSha":"642388c9980773b10ae757826b462c5dc7e846cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6362","title":"Fix search exception logging","createdAt":"2023-05-31T18:31:19Z"}
{"state":"Merged","mergedAt":"2023-06-01T00:42:19Z","number":6363,"mergeCommitSha":"20dd59d2ce8bf68e2550252bf2aae3b2b4635346","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6363","title":"Source embedding to pinecone working","createdAt":"2023-05-31T21:35:58Z"}
{"state":"Merged","mergedAt":"2023-05-31T23:51:48Z","number":6364,"body":"api gateway was converting everything to 200.","mergeCommitSha":"de6818ddf40805c7d2705b3c439c7925c6f687b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6364","title":"Api gateway should handle non 200 codes","createdAt":"2023-05-31T23:51:31Z"}
{"state":"Merged","mergedAt":"2023-06-01T02:19:50Z","number":6366,"mergeCommitSha":"f431704cc98523e4e3ae2f4512ac15d9424195e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6366","title":"Enable cloud watch logs for api gateway","createdAt":"2023-06-01T02:19:22Z"}
{"state":"Merged","mergedAt":"2023-06-01T02:38:50Z","number":6367,"mergeCommitSha":"537d319727d25ff4daf4f70c9d0ad5d9f42d4b74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6367","title":"Add retry","createdAt":"2023-06-01T02:37:49Z"}
{"state":"Merged","mergedAt":"2023-06-01T03:06:00Z","number":6368,"mergeCommitSha":"8b36dd20bdf3c64a1b04678491422892e17c71f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6368","title":"Change ml instance type","createdAt":"2023-06-01T03:05:54Z"}
{"state":"Merged","mergedAt":"2022-03-22T20:33:25Z","number":637,"body":"Deployments went into a bad state again. I'll clean them manually for now but this is a change that should prevent it from happening in the future. \r\n\r\n- Added --atomic flag to enable auto-rollbacks \r\n\r\n`--atomic                       if set, upgrade process rolls back changes made in case of failed upgrade. The --wait flag will be set automatically if --atomic is used`","mergeCommitSha":"2231baa4b2b4a80b7f30855f14a7969b3b07a86c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/637","title":"Atomic helm deploys","createdAt":"2022-03-22T20:20:42Z"}
{"state":"Merged","mergedAt":"2023-06-01T03:53:42Z","number":6370,"mergeCommitSha":"ec3581ceb32c9174c76c71d630bd47a60ad6de42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6370","title":"Remove access logs","createdAt":"2023-06-01T03:53:34Z"}
{"state":"Merged","mergedAt":"2023-06-02T20:16:48Z","number":6371,"body":"Update the dashboard layouts.\r\n\r\n<img width=\"1414\" alt=\"Screenshot 2023-05-31 at 10 41 49 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/34105cce-ec36-4426-82d4-82e01e5ca22e\">\r\n\r\nA lot of the work here was actually removing layout rules that are no longer needed, or were unnecessary, or were hiding or overriding other rules in confusing ways.  I tried to simplify this as much as I could:\r\n\r\n- The Detail layout is now much more similar to the overview layout: it displays in a full vertical view, but no longer takes up the right-hand column.\r\n- Both Detail and Overview layouts (mostly) use the same layout rules.  There are a couple tweaks for the details view, but I think this is easier to understand now.\r\n- The mobile and desktop headers now live in the same place in the DOM -- this makes writing rules against both them and the body/columns easier.\r\n- Any detail views that had a right-hand column (participants, for instance) uses the right-column hook, like the overview view.\r\n- A lot of the routes had their own custom padding, which was sometimes inconsistent.  Now the layout always has consistent padding for the left/body/right columns on all views.\r\n- The layout is now defined as a single grid that we override for each breakpoint.  Grid layouts are very flexible so we can define whatever rules make sense as the sizing changes.\r\n\r\nOne thing I haven't yet figured out is how to get scrolling to work naturally in the details view -- right now you have to have the mouse in the centre column for scroll events to work.  This isn't a regression, but it feels worse with this PR since the body area is now more narrow.\r\n","mergeCommitSha":"6ad8d6a329d59b7022dc087311fc2860ec47aa22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6371","title":"New dashboard layout","createdAt":"2023-06-01T03:53:49Z"}
{"state":"Merged","mergedAt":"2023-06-01T04:37:36Z","number":6372,"mergeCommitSha":"965028caa03159c1002b1b6a804364932f8fa7d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6372","title":"Use GPU instance for source code data pipeline","createdAt":"2023-06-01T04:27:02Z"}
{"state":"Merged","mergedAt":"2023-06-01T05:09:14Z","number":6373,"mergeCommitSha":"91a099f3bdb8982656adcd5289a88aeae06fe72d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6373","title":"Generalize ec2 helpers","createdAt":"2023-06-01T05:08:39Z"}
{"state":"Merged","mergedAt":"2023-06-01T05:21:48Z","number":6374,"mergeCommitSha":"567fa034aa30cce64d3fb6cda1dc8fcf0fade51e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6374","title":"Make ec2 instance type conversion more resilient from string","createdAt":"2023-06-01T05:16:26Z"}
{"state":"Merged","mergedAt":"2023-06-01T17:30:05Z","number":6375,"body":"Takes a pull request description and asks the model to summarize then generate a question answered by the summary. \r\n\r\nNot sure if this will work but what the hell, let's try it. That's prompt engineering for ya.","mergeCommitSha":"df27d03aed65d24778822897e6a2d98a40fdf3fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6375","title":"Create question generation service","createdAt":"2023-06-01T06:39:38Z"}
{"state":"Merged","mergedAt":"2023-06-06T19:15:33Z","number":6377,"mergeCommitSha":"74d91c138c0c153c2aebd5e751f62597d028bc61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6377","title":"Use pinecone namespace to isolate teams and update metadata","createdAt":"2023-06-01T17:12:58Z"}
{"state":"Merged","mergedAt":"2023-06-01T18:23:21Z","number":6378,"mergeCommitSha":"b82cfbf59295e4601f153b0d05150539e2a0c399","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6378","title":"Update pull request page in admin console","createdAt":"2023-06-01T18:09:52Z"}
{"state":"Merged","mergedAt":"2023-06-08T03:52:03Z","number":6379,"mergeCommitSha":"606bd62363e0ecb3ee42801265e86ba000f9d6f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6379","title":"Add template to team settings","createdAt":"2023-06-01T20:26:49Z"}
{"state":"Merged","mergedAt":"2022-03-22T23:35:43Z","number":638,"body":"![image](https://user-images.githubusercontent.com/********/159573781-020d4786-e3f7-4598-93b1-dbb33483681b.png)\r\n![image](https://user-images.githubusercontent.com/********/159573852-f33a8dd2-ef5e-4049-a99c-a17acb45f135.png)\r\n\r\n* Refactored ThreadStore to pipe down three streams of info: `all`: all threads, `mine`: threads involving current person, `team`: threads that don't involve current person\r\n* Refactored and simplified web Chats.tsx component (could probably be renamed??)\r\n* Refactored vscode Sidebar into two webviews, one purely for Auth (`AuthSidebar`) and one for post-auth UI (`Sidebar`) - also involves separating the logic from the single webview provider into two \r\n    * the sidebar views switch depending on the `unblocked:authenticated` context -- if this pattern works well, we could extend it to other parts of the extension that ought to be gated by auth as well\r\n    * Deleted a couple of unused views per new designs ","mergeCommitSha":"eb6e1bb0683f1258f10aeb460119b4f70f97bc4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/638","title":"Filter fetched threads in sidebar by mine/team","createdAt":"2022-03-22T21:04:43Z"}
{"state":"Merged","mergedAt":"2023-06-01T22:20:41Z","number":6380,"mergeCommitSha":"8916d0439d6e0fa24cf937b1bf2187dbd0f45b2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6380","title":"Add ability to trigger Q&A generation for a pull request","createdAt":"2023-06-01T21:34:30Z"}
{"state":"Merged","mergedAt":"2023-06-05T19:05:12Z","number":6381,"mergeCommitSha":"603dc00ca98125f93d1f108e9c38af006b44e2c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6381","title":"Remove UnblockedQAView","createdAt":"2023-06-01T22:03:02Z"}
{"state":"Open","mergedAt":null,"number":6382,"body":"When network is unavailable to the plugin, plugin is in a strange place.\r\nIntroduce a network state stream that allows different parts of the system to react to network availability.\r\n \r\nAs a first step, hooked up so that if a user goes from no network -> network, reload data for login page.\r\n \r\nAlso update fetch retryOn to continue retrying on non 401s... ","mergeCommitSha":"f338f0b516166c5a3b3a5f69c0889e1ce5497e83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6382","title":"Add network availability","createdAt":"2023-06-01T22:23:33Z"}
{"state":"Merged","mergedAt":"2023-06-02T00:29:22Z","number":6383,"body":"Mostly doing this for me, because it forced me to understand wtf is going on.\r\n\r\nHopefully useful for the next few days until we change it all again.\r\n\r\n![semantic-search-legacy](https://github.com/NextChapterSoftware/unblocked/assets/1798345/2a81d021-d006-4ca3-b4ca-0aadbeaf92e0)\r\n\r\n![semantic-search](https://github.com/NextChapterSoftware/unblocked/assets/1798345/89379f89-8d96-4419-90b2-6a9082a13567)\r\n","mergeCommitSha":"c1e6e7dca877c185c861ab5d483af481cd18c61c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6383","title":"Sequence diagrams for search","createdAt":"2023-06-02T00:20:22Z"}
{"state":"Merged","mergedAt":"2023-06-02T00:44:08Z","number":6384,"mergeCommitSha":"4438476cc661fd38ca3eda2d9ec2de9a17bb10c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6384","title":"AdminWeb: Dashboard returns a blank page when turn on impersonate","createdAt":"2023-06-02T00:37:54Z"}
{"state":"Merged","mergedAt":"2023-06-02T14:38:16Z","number":6385,"body":"- Dequanitize mosaicml\r\n- Fixing sequence length\r\n","mergeCommitSha":"7dae86003b1b3921dc9beb95e27181a6953e5500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6385","title":"DequantizeMosaicml","createdAt":"2023-06-02T14:38:00Z"}
{"state":"Merged","mergedAt":"2023-06-02T18:18:00Z","number":6386,"body":"This is to allow us to associate a thread with a Jira Project. This property can be used as an identifier when embedding so that we can blast away all embeddings for a project when a user disables it for Jira ingestion.","mergeCommitSha":"a61d044888692081a19b3c0aa23c553514484c2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6386","title":"Add ThreadModel.jiraProject property","createdAt":"2023-06-02T17:06:33Z"}
{"state":"Merged","mergedAt":"2023-06-05T17:34:23Z","number":6387,"body":"Just realized that we (I) have been editing what is suppose to be the stable name (topic.Name) rather than the dynamic name (topic.DisplayName). This manifested that I couldn't fix another bug with SFGiants + Ripple. ","mergeCommitSha":"c2469364022141d278f97619a6e70bd7f16432d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6387","title":"New Topic Bug: Update TopicPages to remove to lowercase and swap topic.Name + topic.DisplayName","createdAt":"2023-06-02T17:14:42Z"}
{"state":"Merged","mergedAt":"2023-06-08T03:59:32Z","number":6388,"body":"Leverages a little bit of kotlin/java reflection to get the job done quickly. \r\n\r\nThe next step is going to overlap with other work going on: \r\n1. join on the `TeamSettingsModel` and `MLInferenceTemplateModel` for the team's template, fall back to the global template if null\r\n2. Pull out templates \r\n3. Convert documents from retrieval step to templates\r\n4. Load Golden Record for team\r\n5. Compile prompt\r\n\r\n\r\nAnd then after that:\r\n- Generate examples from inference response\r\n- Integrate with thumbs-up/thumbs-down endpoint for scoring and inclusion in Golden Record","mergeCommitSha":"b68ece38f0b61a44c55d139c9556c2a4b789a16c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6388","title":"Prompt compiler","createdAt":"2023-06-02T17:23:45Z"}
{"state":"Merged","mergedAt":"2023-06-02T20:04:04Z","number":6389,"mergeCommitSha":"51cd70c6a2688c91004bd260e298aa354777a9e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6389","title":"Add my test accounts","createdAt":"2023-06-02T17:31:53Z"}
{"state":"Merged","mergedAt":"2022-03-22T23:00:41Z","number":639,"body":"- Changed service deployment workflow to trigger on action.yaml changes\r\n- Modified health probe frequencies\r\n- Removed initial delay from liveness and readiness probes. They don't make sense since we have a startup probe\r\n- added thresholds for check failures. Porbes were calling it too early resulting in service restarts on new deployments\r\n- Ran Ansible playbook to updated base helm charts and commited tgz archives for each service","mergeCommitSha":"cf6b3163aa587f3b68632f50bd8241302bcf48a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/639","title":"Fix deployment triggers and health probes","createdAt":"2022-03-22T21:09:17Z"}
{"state":"Merged","mergedAt":"2023-06-02T18:18:10Z","number":6390,"mergeCommitSha":"5f73dae41676fedf1fca2fb4767b8195f5bb966c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6390","title":"Add admin page to see all generated Q&As for a team","createdAt":"2023-06-02T17:58:14Z"}
{"state":"Merged","mergedAt":"2023-06-02T20:29:18Z","number":6391,"mergeCommitSha":"6b4107c22ac0066cdad8644ab420f8237d503e90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6391","title":"Fix notification and thread opening actions for Q&A threads","createdAt":"2023-06-02T20:10:18Z"}
{"state":"Merged","mergedAt":"2023-06-02T22:24:53Z","number":6392,"mergeCommitSha":"2908be161300aca023d4518c5f85591fdd5794fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6392","title":"Bump max content column width","createdAt":"2023-06-02T22:15:19Z"}
{"state":"Merged","mergedAt":"2023-06-02T23:14:44Z","number":6393,"body":"This will get the diffs from the GitHub API and include it in the prompt. We'll need to move this logic out of the admin console eventually. ","mergeCommitSha":"36ef2bf1ccca12302bf32e972ba12f0bf57e7115","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6393","title":"Include diffs in prompt for generating Q&As for a pull request","createdAt":"2023-06-02T23:04:54Z"}
{"state":"Merged","mergedAt":"2023-06-06T00:18:09Z","number":6394,"body":"Made the flag optional because for the vast majority of users it is false...","mergeCommitSha":"822e13a4fcd8bb7a3a87a9f72707180d0ac3b2b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6394","title":"Add flag to identify which TeamMember is the unblocked bot","createdAt":"2023-06-02T23:08:58Z"}
{"state":"Merged","mergedAt":"2023-06-05T17:33:10Z","number":6395,"body":"This is what I have been working on:\r\n\r\n- CloudFront path override for `/api/ml/*` to forward traffic to a separate alb\r\n- New alb managed in CDK with high request TTL \r\n- Each lambda function then can be added to the ALB as a target for any given path. Listener is exported from the stack to allow for imports in other stacks.\r\n```\r\nlistener.addTargets('Lambda1Target', {\r\n            priority: 1,\r\n            conditions: [elbv2.ListenerCondition.pathPatterns(['/api/ml/l1'])],\r\n            targets: [new targets.LambdaTarget(lambda1)],\r\n        });\r\n```\r\n\r\n\r\nUpdate: \r\nI have added a conditional to CloudFront stack to avoid applying the new change in prod while we are validating it. ","mergeCommitSha":"ab09faa3482cd408ac8cacb41d1ed16dd5d6c330","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6395","title":"Alb for ML lambda functions (dev only)","createdAt":"2023-06-02T23:31:59Z"}
{"state":"Merged","mergedAt":"2023-06-03T01:53:30Z","number":6396,"mergeCommitSha":"87a345033af008414433bb71699e443227dbbaba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6396","title":"Add lambda integration for ml/hf endpoints","createdAt":"2023-06-03T01:52:54Z"}
{"state":"Merged","mergedAt":"2023-06-05T18:03:52Z","number":6397,"body":"Specify the column sizes explicitly as content plus padding.\r\n\r\nThis doesn't change the values for the side columns, but it does make the centre (body) column size to be a bit bigger.  This matches what Ben was proposing on Friday, and makes the content-vs-padding sizing explicit so we can match designs better.","mergeCommitSha":"9fe95f35d9debb5c713bc3336186ee81491b7341","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6397","title":"Dashboard: add padding to column sizes","createdAt":"2023-06-05T17:49:05Z"}
{"state":"Merged","mergedAt":"2023-06-05T19:06:44Z","number":6398,"body":"This is a risky PR but we don't have any other options. The issue with the previous deployment attempt was caused by the fact that CloudFront stack modified S3 buckets in Static site bucket. The modification was to add an IAM role to the S3 bucket for CloudFront Origin access. \r\n\r\nSince we had imported those buckets directly, it resulted in a hard dependency. With this change that should change since we no longer act directly on those S3 objects. Instead we look up the buckets and act on the looked up value. ","mergeCommitSha":"1606728dc3c9aa7cbddaab0f439ff9c4320588a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6398","title":"trying to get deploys working","createdAt":"2023-06-05T19:02:09Z"}
{"state":"Merged","mergedAt":"2023-06-08T04:07:44Z","number":6399,"mergeCommitSha":"35bf353ff57113715ea256e62efcd30739809609","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6399","title":"Example service","createdAt":"2023-06-05T19:15:27Z"}
{"state":"Merged","mergedAt":"2022-01-20T22:24:51Z","number":64,"body":"This pr moves us to using a combination of AWS ECR + EKS.\r\n\r\nJOB 1 \r\n1. We build via gradle the shadowJar and output that as a job-level output in the pipeline.\r\n\r\nJOB 2:\r\n1. We set up AWS creds for central ECR repository.\r\n2. We build an image using the jar from JOB 1.\r\n3. We push to central ECR repo\r\n4. We record image tag as output.\r\n\r\nJOB 3:\r\n1. We set up AWS creds for dev environment.\r\n2. We use helm to install apiservice charts specifying image repository and tag from JOB 2.\r\n\r\nhttps://github.com/Chapter2Inc/codeswell/runs/4876134021?check_suite_focus=true\r\n\r\napiservice-charts-5bff56c576-7pj9w        0/1     CrashLoopBackOff   5          5m12s\r\nCurrently crashing because of RDS, but it's running at least...\r\n\r\nTODO:\r\n1. We need to make this more generic. (i.e. per service type) That's going to to take some thinking...","mergeCommitSha":"3f0ba96c1fd821552595f88cd941440f46f31538","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/64","title":"Github actions for deploying apiservice to kube","createdAt":"2022-01-18T19:48:03Z"}
{"state":"Merged","mergedAt":"2022-03-23T16:12:54Z","number":640,"mergeCommitSha":"9759e289557606ca43eea9cbcc55ea297a151492","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/640","title":"Add gh install redis lock","createdAt":"2022-03-22T22:05:31Z"}
{"state":"Merged","mergedAt":"2023-06-05T19:33:24Z","number":6400,"body":"Disable the new Cloudfront behaviour and origin for now while we work on removing dependencies between CloudFront stack and Static Site Stack","mergeCommitSha":"0aadd5dd483ab7fd1c31ff2f8024d4c79e3d8840","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6400","title":"disable new origin for now","createdAt":"2023-06-05T19:28:45Z"}
{"state":"Merged","mergedAt":"2023-06-05T22:42:22Z","number":6401,"body":"Reference Metadata UI.\r\n\r\nCurrently opening links in dashboard / external URL.\r\n\r\nWill have subsequent PR for IDEs to potentially launch source files locally\r\n\r\n<img width=\"676\" alt=\"CleanShot 2023-06-05 at 12 55 30@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/aa34bcd6-8968-4128-8314-d03b845bf1b4\">\r\n\r\n<img width=\"627\" alt=\"CleanShot 2023-06-05 at 13 58 59@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/2146211a-084a-4fdb-8f0f-08aff946906e\">\r\n\r\n<img width=\"643\" alt=\"CleanShot 2023-06-05 at 14 00 27@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/2d9bfd27-8b9f-48a1-bca0-a274480f79cc\">\r\n\r\n","mergeCommitSha":"f996e78ee959ea03fb361dd37c26b02a5e86084e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6401","title":"Reference metadata UI","createdAt":"2023-06-05T20:57:53Z"}
{"state":"Merged","mergedAt":"2023-06-05T21:20:42Z","number":6402,"body":"I'll clean up in my next pr ","mergeCommitSha":"78ae982a9dee4a70e4c480fd477cff1d3898ddd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6402","title":"Fix landing page origin","createdAt":"2023-06-05T21:12:03Z"}
{"state":"Merged","mergedAt":"2023-06-05T22:31:36Z","number":6403,"body":"Forgot to do this in the last checkin.","mergeCommitSha":"999ee595008aad1b73a68183cfcdb38546921d0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6403","title":"Show the topic name if the displayName is empty in adminweb","createdAt":"2023-06-05T22:22:43Z"}
{"state":"Merged","mergedAt":"2023-06-05T22:29:23Z","number":6404,"body":"Reverts NextChapterSoftware/unblocked#6402","mergeCommitSha":"35be71ed75d499df14ad92c1466a43c0f61d0841","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6404","title":"Revert \"Fix landing page origin\"","createdAt":"2023-06-05T22:25:16Z"}
{"state":"Merged","mergedAt":"2023-06-05T22:45:42Z","number":6405,"body":"This is the second revert PR to correct the missing bucket access policy for CloudFront. \r\n\r\nIBucket interface returned by the lookup function do not allow for bucket policy modifications. That's why we need this revert.","mergeCommitSha":"e91a3573e044624a2e3761b847cb5ce194bf3753","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6405","title":"second revert to get bucket policies working","createdAt":"2023-06-05T22:40:15Z"}
{"state":"Merged","mergedAt":"2023-06-05T23:01:37Z","number":6406,"mergeCommitSha":"fa480fcb7d92aec531e1546bb62b8d43345ea31d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6406","title":"adding the missing output","createdAt":"2023-06-05T22:59:40Z"}
{"state":"Merged","mergedAt":"2023-06-06T00:05:36Z","number":6407,"body":"This PR includes the diffs from the pull request in the prompt. Also, the prompt has been updated to give the model a persona. Hi professor \uD83D\uDC4B !","mergeCommitSha":"cac72a3ff8b6a47feb15dc7155e1554686a2e6e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6407","title":"Use GPT 3.5 Turbo to generate Q&As from pull requests because its better","createdAt":"2023-06-05T23:52:03Z"}
{"state":"Merged","mergedAt":"2023-06-06T00:12:35Z","number":6408,"body":"- Add ml interfaces\r\n- Update\r\n- Do not filter out large chunk sized documents\r\n","mergeCommitSha":"71e33a17f5c222e9f7385ca6424471c04d8a8076","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6408","title":"Add framework for alb -> lambda and improve seemantic search","createdAt":"2023-06-06T00:09:11Z"}
{"state":"Merged","mergedAt":"2023-06-13T22:43:41Z","number":6409,"body":"(do not merge this until the bot is hooked up)","mergeCommitSha":"e2a9478e8cea30c88ef1314a93d9bb88525d073e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6409","title":"Mention bot when creating Q&A thread","createdAt":"2023-06-06T00:36:53Z"}
{"state":"Merged","mergedAt":"2022-03-23T00:19:54Z","number":641,"body":"Related to https://github.com/NextChapterSoftware/unblocked/pull/628","mergeCommitSha":"217ef0e8905dd71846c4ac7c17efdf896cb227e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/641","title":"Change bother to unblocked in ci-vscode.yml","createdAt":"2022-03-23T00:03:11Z"}
{"state":"Merged","mergedAt":"2023-06-06T01:05:23Z","number":6410,"mergeCommitSha":"8d4fe61f2696451665b7fe07cf942ab44e635082","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6410","title":"Move files around","createdAt":"2023-06-06T01:01:21Z"}
{"state":"Merged","mergedAt":"2023-06-06T03:30:07Z","number":6411,"mergeCommitSha":"cad0eb267c9d2799631ce760a81e3640ff36e124","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6411","title":"Pass a list of ChatMessages instead of a single chat message to GPT 3.5 Turbo","createdAt":"2023-06-06T02:59:32Z"}
{"state":"Merged","mergedAt":"2023-06-06T16:23:52Z","number":6412,"body":"This allows scrolling in the dashboard when in detail layout, regardless of where the mouse is.  The detail content is now the scrollable window content.\r\n\r\nThis is a prerequisite to pinning the Reply MessageEditor to the bottom of the detail view.\r\n\r\nI think I did most of the work to support this earlier -- all I had to do was rearrange the overflows a bit and make sure the DetailLayoutView header and footer were pinned correctly.  There is one gross hack: the scrollable content has to be \"clipped\" so that it doesn't display above the header or below the footer.  I did this by having the header and footer appear with a higher z-index, with the padding area rendered opaquely.  I also had to do a little trick to get the bottom border areas rounded.","mergeCommitSha":"0f7220f093676ec2c39abf88992bebf5e1bec806","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6412","title":"Fix dashboard detail layout scrolling","createdAt":"2023-06-06T03:25:25Z"}
{"state":"Merged","mergedAt":"2023-06-06T20:51:38Z","number":6413,"body":"Updated how reference navigation works.\r\n\r\nWhen opening from dashboard, it will now prioritize opening the \"external\" URLs in another tab.\r\nWhen opening in an IDE, will open the corresponding insight in an editor / sidebar.\r\n\r\nTODO: SourceFile references will require some more work as we need to add commit hash / source mark.","mergeCommitSha":"6c334188f06d804aa6b3a265d6fa91758d43ce3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6413","title":"Reference navigation within IDE","createdAt":"2023-06-06T03:45:18Z"}
{"state":"Merged","mergedAt":"2023-06-06T17:48:47Z","number":6414,"body":"I had to change the order of endpoints by moving API behaviour to the bottom right before the `/*` catch all. Then I had to create a dummy behaviour as a placeholder to avoid the circular dependency issue with Static Site Stack. \r\n\r\n\r\nThis change has been deployed to Dev and works as expected. Once we have enabled everything in prod I will remove the dummy behaviour.","mergeCommitSha":"da44335fe4ef978b732695b0b8e4af5845593822","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6414","title":"this actually worked..CloudFront and CDK tend to be really stupid sometimes","createdAt":"2023-06-06T05:53:45Z"}
{"state":"Merged","mergedAt":"2023-06-06T19:18:38Z","number":6416,"mergeCommitSha":"6a2ee58956bb99c38c2f2ba6ec5a7bebddf89496","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6416","title":"Route DEV Q&A to #ask-unblocked-dev until we have source code access in PROD","createdAt":"2023-06-06T18:27:21Z"}
{"state":"Merged","mergedAt":"2023-06-06T22:32:50Z","number":6418,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1686068621615509","mergeCommitSha":"e9ec027549cc26c12d3d7b20ef24e784ee088ff1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6418","title":"Throw exception if GitHubUserApi failed to correctly refresh a user access token","createdAt":"2023-06-06T19:08:59Z"}
{"state":"Merged","mergedAt":"2023-06-06T19:23:28Z","number":6419,"mergeCommitSha":"b564fc8eedd41c02123f70c4bf7473c66e0c8571","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6419","title":"Cleanup api gateway endpoint paths","createdAt":"2023-06-06T19:22:49Z"}
{"state":"Merged","mergedAt":"2022-03-23T16:47:04Z","number":642,"mergeCommitSha":"1c686c7dbe219540fcfdef0b05d987a4b6b066b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/642","title":"Take first 100 characters of first message for thread title","createdAt":"2022-03-23T00:15:13Z"}
{"state":"Merged","mergedAt":"2023-06-06T19:53:23Z","number":6420,"mergeCommitSha":"33e8a14e3f22c3c110c66f1b8589ef65221472b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6420","title":"Lambda machine learning alb","createdAt":"2023-06-06T19:53:14Z"}
{"state":"Merged","mergedAt":"2023-06-06T23:30:38Z","number":6421,"body":"Will also \"tickle\" the thread for pusher","mergeCommitSha":"5cf1d3901fe3cf6680e11701a5271278e62ab1fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6421","title":"Implements message feedback with new model","createdAt":"2023-06-06T20:58:19Z"}
{"state":"Merged","mergedAt":"2023-06-06T22:44:43Z","number":6422,"body":"- Currently in a narrow column, now wider.\r\n- Adds buttons to view in web dashboard and admin web.","mergeCommitSha":"88d3acacd0a9e3d18e72b78532e6a4beb6b7d961","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6422","title":"Re-format slack Q&A messages and add buttons","createdAt":"2023-06-06T21:44:24Z"}
{"state":"Closed","mergedAt":null,"number":6424,"body":"When opening a source file reference within an IDE, we open the file in an \"unresolved\" editor as we are missing source marks.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/da44ab15-0ef7-40df-8dda-ceae6f41f18a\r\n\r\n","mergeCommitSha":"dc823837a643b2facece2e9de10688d4c42d232c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6424","title":"Source file reference navigation","createdAt":"2023-06-06T23:09:55Z"}
{"state":"Merged","mergedAt":"2023-06-07T04:42:32Z","number":6425,"body":"Now that we have source code, let's see what it does to improve our summaries? Tried to test this in the sandbox, but got blocked on connecting to pg for the text.","mergeCommitSha":"9f7643a3be06935028a6d74cc4baa641bf4c719d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6425","title":"Adding source code insights to topic + expert summary generation","createdAt":"2023-06-07T00:02:15Z"}
{"state":"Merged","mergedAt":"2023-06-07T00:37:50Z","number":6426,"body":"https://chapter2global.slack.com/archives/C05B5QHDAMT/p1686092548733869?thread_ts=1686092424.484059&cid=C05B5QHDAMT","mergeCommitSha":"257c793c801456177ca4350b00807e9cf74e8613","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6426","title":"Plumb document source (file name) from KT to ML query endpoint","createdAt":"2023-06-07T00:25:37Z"}
{"state":"Merged","mergedAt":"2023-06-07T01:21:49Z","number":6427,"mergeCommitSha":"74e83f306282f88cf5f1c327b09929784234b6c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6427","title":"Add ml endpoints that point to lambda","createdAt":"2023-06-07T01:15:43Z"}
{"state":"Merged","mergedAt":"2023-06-07T03:29:58Z","number":6429,"mergeCommitSha":"021d7dcc74fa56651c00de6dde097f3be8fc9e68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6429","title":"Add more debugging","createdAt":"2023-06-07T03:29:52Z"}
{"state":"Merged","mergedAt":"2022-03-24T17:05:42Z","number":643,"body":"* Vscode code block - only the snippet should scroll, not the entire block (ie pin the code header)\r\n* Get rid of some unused code \r\n* Rename web navigator to match vscode","mergeCommitSha":"f1ceb8e0252a6579b9f7095adc09b45ad2230e89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/643","title":"Small nit styling fixes","createdAt":"2022-03-23T00:22:42Z"}
{"state":"Merged","mergedAt":"2023-06-07T05:06:16Z","number":6430,"body":"- Removed the dummy redirect endpoint on the ALB. \r\n- Removed the conditional to deploy the ALB behaviour to prod.\r\n\r\nI'll keep a close eye on this to make sure the deployment goes through in prod. ","mergeCommitSha":"0e6d91e4b478cdae115fce8f54e6cd6e13c369cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6430","title":"enable ML alb in prod","createdAt":"2023-06-07T03:54:12Z"}
{"state":"Merged","mergedAt":"2023-06-07T17:20:59Z","number":6431,"body":"Make how the TS clients determine environment consistent:\r\n\r\n* IDE clients will now try to get the user default setting from the hub\r\n* IDE clients will pass their environment to webviews, so they always use the same environment as the extension/agent\r\n* JetBrains agent gets its environment from a tiny JS script, so the logic is in one place and consistent","mergeCommitSha":"18ec4ce2727324c92ee6c17e68b0ab152c15ada6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6431","title":"Consistent environment setting in clients","createdAt":"2023-06-07T05:32:37Z"}
{"state":"Merged","mergedAt":"2023-06-07T19:22:47Z","number":6432,"body":"<img width=\"745\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/246f3154-b5ef-4e3b-ad64-0659018806bc\">\r\n","mergeCommitSha":"9498609ff75ef13f8a75bd7106e39ae0483cb1f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6432","title":"Add feedback UI to qa threads","createdAt":"2023-06-07T16:56:13Z"}
{"state":"Merged","mergedAt":"2023-06-12T17:56:26Z","number":6433,"mergeCommitSha":"a32528a38af20168bdf861d5d04fd530cb8aae42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6433","title":"Remove Unblocked search provider in JetBrains IDEs","createdAt":"2023-06-07T17:20:27Z"}
{"state":"Merged","mergedAt":"2023-06-07T17:51:23Z","number":6434,"mergeCommitSha":"375d80e69e8ba4b2c5541fc15729efbcea3191d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6434","title":"Add ability to delete a generated QA from the overview page","createdAt":"2023-06-07T17:29:40Z"}
{"state":"Merged","mergedAt":"2023-06-07T19:24:18Z","number":6435,"body":"- **MLDocument**\r\n  Contains human readable content and human readable source description.\r\n  Source description can be anything, but is intended to be a succint human\r\n  readbable way to reference the content. For example, file name for source\r\n  code type, pull request title for a PR, issue number or title for an issue.\r\n  These may be terse or fully qualified depending on what works best for\r\n  inference like \"PR # 123\" or \"GitHub PR # 123\", etc.\r\n\r\n- **MLTypedDocument**\r\n  Same as above, but with internal fields describing the source type and\r\n  source ID.","mergeCommitSha":"effb1458d31f1cb86edc39e1c92c5a5b690422d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6435","title":"Introduce MLTypedDocument","createdAt":"2023-06-07T17:41:22Z"}
{"state":"Merged","mergedAt":"2023-06-07T18:12:17Z","number":6436,"mergeCommitSha":"86b8b5701438632e02ee399e55142f4c71871218","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6436","title":"Add anthropic client","createdAt":"2023-06-07T17:42:28Z"}
{"state":"Merged","mergedAt":"2023-06-07T19:52:14Z","number":6437,"mergeCommitSha":"4f50c19f7f624cde890c18f42de76d3454c0eb75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6437","title":"Add more insight types to summaries and start using the displayname if present.","createdAt":"2023-06-07T17:58:19Z"}
{"state":"Merged","mergedAt":"2023-06-07T20:32:58Z","number":6438,"mergeCommitSha":"7bd1490186b3262459ee0cb19381a796fba8b90b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6438","title":"Add overlay to update feedback API","createdAt":"2023-06-07T19:27:55Z"}
{"state":"Merged","mergedAt":"2023-06-07T20:18:44Z","number":6439,"body":"After this change our ML alb should only be available at `ml.alb.dev.getunblocked.com` from internal addresses. The ALB is being moved to an internal isolated subnet and will no longer be reachable from outside world. \r\n\r\nI have modified the security group to allow traffic from the following ranges:\r\n- EKS VPC address range\r\n- VPN address range \r\n- CoreVPC address range.\r\n\r\n\r\nThe diff for ALB stack:\r\n\r\n```\r\n core git:(make_ml_alb_private) ✗ cdk diff MachineLearningAlbStack  -c config=dev-us-west-2\r\nno lifecycle policy has been assigned to user-audio-assets-dev-us-west-2\r\nno lifecycle policy has been assigned to user-blob-assets-dev-us-west-2\r\nno lifecycle policy has been assigned to user-image-assets-dev-us-west-2\r\nno lifecycle policy has been assigned to user-text-assets-dev-us-west-2\r\nno lifecycle policy has been assigned to user-video-assets-dev-us-west-2\r\nIncluding dependency stacks: AcmStack, DnsStack, NetworkStack, HuggingFaceModelsStack, MachineLearningModelsStack\r\nStack AcmStack\r\nThere were no differences\r\nStack DnsStack\r\nThere were no differences\r\nStack NetworkStack\r\nOutputs\r\n[-] Output ExportsOutputRefCOREVPCpublicSubnet1SubnetA8EA77AFEDC4E690: {\"Value\":{\"Ref\":\"COREVPCpublicSubnet1SubnetA8EA77AF\"},\"Export\":{\"Name\":\"NetworkStack:ExportsOutputRefCOREVPCpublicSubnet1SubnetA8EA77AFEDC4E690\"}}\r\n[-] Output ExportsOutputRefCOREVPCpublicSubnet2Subnet4F7E21330392B5E7: {\"Value\":{\"Ref\":\"COREVPCpublicSubnet2Subnet4F7E2133\"},\"Export\":{\"Name\":\"NetworkStack:ExportsOutputRefCOREVPCpublicSubnet2Subnet4F7E21330392B5E7\"}}\r\n[-] Output ExportsOutputRefCOREVPCpublicSubnet3SubnetF19AEAF19CB816E2: {\"Value\":{\"Ref\":\"COREVPCpublicSubnet3SubnetF19AEAF1\"},\"Export\":{\"Name\":\"NetworkStack:ExportsOutputRefCOREVPCpublicSubnet3SubnetF19AEAF19CB816E2\"}}\r\n\r\nStack HuggingFaceModelsStack\r\nThere were no differences\r\nStack MachineLearningModelsStack\r\nResources\r\n[~] AWS::SageMaker::Model ml-endpoint-unblocked--semantic-search/ml-model-unblocked--semantic-search/ml-model-unblocked--semantic-search mlendpointunblockedsemanticsearchmlmodelunblockedsemanticsearch8F6421DA replace\r\n └─ [~] PrimaryContainer (requires replacement)\r\n     └─ [~] .Image:\r\n         └─ [~] .Fn::Sub:\r\n             ├─ [-] 129540529571.dkr.ecr.us-west-2.${AWS::URLSuffix}/cdk-hnb659fds-container-assets-129540529571-us-west-2:21663c72300e563530ec146ef18c0664de253663ae7c82dd9aa2861ce3a14fc4\r\n             └─ [+] 129540529571.dkr.ecr.us-west-2.${AWS::URLSuffix}/cdk-hnb659fds-container-assets-129540529571-us-west-2:ff75e54d49a734eab93775c34cfb08776a4daf50937f5c57da4dcac125bd6f83\r\n[~] AWS::SageMaker::EndpointConfig ml-endpoint-unblocked--semantic-search/ml-model-unblocked--semantic-search/ml-endpoint-config-unblocked--semantic-search mlendpointunblockedsemanticsearchmlmodelunblockedsemanticsearchmlendpointconfigunblockedsemanticsearchF968AF7D replace\r\n └─ [~] ProductionVariants (requires replacement)\r\n     └─ @@ -5,7 +5,7 @@\r\n        [ ] \"InstanceType\": \"ml.c5.2xlarge\",\r\n        [ ] \"ModelName\": {\r\n        [ ]   \"Fn::GetAtt\": [\r\n        [-]     \"mlendpointunblockedsemanticsearchmlmodelunblockedsemanticsearch8F6421DA\",\r\n        [+]     \"mlendpointunblockedsemanticsearchmlmodelunblockedsemanticsearch8F6421DA(replaced)\",\r\n        [ ]     \"ModelName\"\r\n        [ ]   ]\r\n        [ ] },\r\n\r\nStack MachineLearningAlbStack\r\nSecurity Group Changes\r\n┌───┬───────────────────────────────────────┬─────┬────────────┬─────────────────┐\r\n│   │ Group                                 │ Dir │ Protocol   │ Peer            │\r\n├───┼───────────────────────────────────────┼─────┼────────────┼─────────────────┤\r\n│ - │ ${CloudFrontIngressOnlyMlAlb.GroupId} │ In  │ TCP 443    │ pl-82a045eb     │\r\n├───┼───────────────────────────────────────┼─────┼────────────┼─────────────────┤\r\n│ + │ ${IngressOnlyMlAlb.GroupId}           │ In  │ TCP 443    │ **********/16   │\r\n│ + │ ${IngressOnlyMlAlb.GroupId}           │ In  │ TCP 443    │ ***********/16  │\r\n│ + │ ${IngressOnlyMlAlb.GroupId}           │ In  │ TCP 443    │ **********/16   │\r\n│ + │ ${IngressOnlyMlAlb.GroupId}           │ Out │ Everything │ Everyone (IPv4) │\r\n└───┴───────────────────────────────────────┴─────┴────────────┴─────────────────┘\r\n(NOTE: There may be security-related changes not in this list. See https://github.com/aws/aws-cdk/issues/1299)\r\n\r\nResources\r\n[-] AWS::EC2::SecurityGroup CloudFrontIngressOnlyMlAlb CloudFrontIngressOnlyMlAlb34DE4515 destroy\r\n[-] AWS::EC2::SecurityGroupIngress CloudFrontIngressOnlyMlAlb/from pl-82a045eb:443 CloudFrontIngressOnlyMlAlbfrompl82a045eb4432CD3A80C destroy\r\n[+] AWS::EC2::SecurityGroup IngressOnlyMlAlb IngressOnlyMlAlb1E0F79FD\r\n[~] AWS::ElasticLoadBalancingV2::LoadBalancer MlAlb MlAlb2511D728\r\n ├─ [~] SecurityGroups\r\n │   └─ @@ -7,7 +7,7 @@\r\n │      [ ] },\r\n │      [ ] {\r\n │      [ ]   \"Fn::GetAtt\": [\r\n │      [-]     \"CloudFrontIngressOnlyMlAlb34DE4515\",\r\n │      [+]     \"IngressOnlyMlAlb1E0F79FD\",\r\n │      [ ]     \"GroupId\"\r\n │      [ ]   ]\r\n │      [ ] }\r\n └─ [~] Subnets\r\n     └─ @@ -1,11 +1,11 @@\r\n        [ ] [\r\n        [ ]   {\r\n        [-]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCpublicSubnet1SubnetA8EA77AFEDC4E690\"\r\n        [+]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCisolatedSubnet1Subnet995CB683F00FCFF6\"\r\n        [ ]   },\r\n        [ ]   {\r\n        [-]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCpublicSubnet2Subnet4F7E21330392B5E7\"\r\n        [+]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCisolatedSubnet2Subnet9F04C5F64946038D\"\r\n        [ ]   },\r\n        [ ]   {\r\n        [-]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCpublicSubnet3SubnetF19AEAF19CB816E2\"\r\n        [+]     \"Fn::ImportValue\": \"NetworkStack:ExportsOutputRefCOREVPCisolatedSubnet3Subnet553F0A3E677D8443\"\r\n        [ ]   }\r\n        [ ] ]\r\n```","mergeCommitSha":"b2ae3a2724fc0ac75e45d974626a537c20b4d06d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6439","title":"making ml alb private","createdAt":"2023-06-07T19:28:04Z"}
{"state":"Merged","mergedAt":"2022-03-23T03:18:16Z","number":644,"mergeCommitSha":"1c0b95e8b4d3674a41e21f4cf771ac2a7f0d4ec7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/644","title":"Copies apiservice changes to scmservice. This is brittle...","createdAt":"2022-03-23T03:08:04Z"}
{"state":"Merged","mergedAt":"2023-06-07T21:10:59Z","number":6440,"body":"Removes old semantic search UI","mergeCommitSha":"de7b5ca58d588fbbe2de3bc7b5502b6370f616ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6440","title":"Remove old semantic search ui","createdAt":"2023-06-07T20:50:56Z"}
{"state":"Merged","mergedAt":"2023-06-07T21:12:55Z","number":6441,"body":"Stuff query related metadata into an object that can be passed around.\n\nPete is going to fill with the MLInferenceRuntimeConfiguration.","mergeCommitSha":"224ce145c5752312136b14068bbe9fa06ee89265","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6441","title":"Use QueryContext","createdAt":"2023-06-07T20:54:19Z"}
{"state":"Merged","mergedAt":"2023-06-07T21:11:17Z","number":6442,"mergeCommitSha":"719d92a0c0b14442de8da9328b9829efe7e3c90a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6442","title":"added an empty reference to get around the dependency issue","createdAt":"2023-06-07T20:55:53Z"}
{"state":"Merged","mergedAt":"2023-06-07T22:18:45Z","number":6443,"body":"Never used in production, so no need to maintain implementation.","mergeCommitSha":"5b3db724fa5b2631eaddfd77494ff8a1ee4e0d5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6443","title":"Deprecate semanticSearch API and remove implementation","createdAt":"2023-06-07T21:04:51Z"}
{"state":"Merged","mergedAt":"2023-06-07T23:04:54Z","number":6444,"body":"Forcing use of exhaustive when values makes the caller aware of the types\nthat may have been inadvertently omitted.","mergeCommitSha":"3b273e6dc9f82e786c2f008c7d99c2fbad2c498f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6444","title":"Use a safer pattern for selecting insights to use in a query","createdAt":"2023-06-07T21:12:41Z"}
{"state":"Merged","mergedAt":"2023-06-16T00:15:41Z","number":6445,"body":"Add isQaLoading on messages.\r\n\r\nCould potentially make this more generic? Somewhat equivalent to `User is typing` state one sees in messaging apps.\r\n\r\n<img width=\"356\" alt=\"CleanShot 2023-06-07 at 14 24 07@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/2de62bc1-c748-4731-bd3f-ae8f3be985bd\">\r\n","mergeCommitSha":"b597ed3bc731bac542d3822ac3989988604f5f45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6445","title":"Add isQaLoading","createdAt":"2023-06-07T21:24:00Z"}
{"state":"Merged","mergedAt":"2023-06-07T22:42:46Z","number":6446,"mergeCommitSha":"ef40c686a86e89ecbc32313d1d3a102738863b6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6446","title":"forgot to set this flag","createdAt":"2023-06-07T22:39:52Z"}
{"state":"Merged","mergedAt":"2023-06-07T23:45:10Z","number":6447,"body":"For non-semantic search, we're moving away from filtering on insight type towards filtering on provider. To allow this, we need to add a `SearchInsightModel.provider` column and update indexing logic to include the provider when upserting a search record. This provider will be sourced from the `*IndexContentService` data class.\r\n\r\nNext PR will add a migration to backfill this column.","mergeCommitSha":"d224403480f21bbecef7de7125734c5f8d1d8f34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6447","title":"Add a SearchInsightModel.provider property to enable filtering search results by provider","createdAt":"2023-06-07T23:03:12Z"}
{"state":"Closed","mergedAt":null,"number":6448,"body":"The idea is that these `\"123-pl\"` references are intelligible by the inference engine.\n\nNo idea if that is true or not but might help.\n\n- https://docs.langchain.com/docs/components/chains/index_related_chains\n- https://chapter2global.slack.com/archives/C045VGYML95/p1686164030839439","mergeCommitSha":"f8c3ea40753d96c6b8d2c770c5a0793c2052b67c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6448","title":"Use an MLPromptLabel to facilitate reference generation","createdAt":"2023-06-07T23:08:30Z"}
{"state":"Merged","mergedAt":"2023-06-08T00:12:00Z","number":6449,"mergeCommitSha":"cd4ad6988029adf734353ff95c9508b9db2d571d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6449","title":"this is temp to allow for migration","createdAt":"2023-06-07T23:51:32Z"}
{"state":"Merged","mergedAt":"2022-03-24T22:30:24Z","number":645,"mergeCommitSha":"63d3faec9f7b3e91e52025d43a9c232938308e04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/645","title":"Adds external ID to RepoModel to associate with SCM","createdAt":"2022-03-23T03:53:13Z"}
{"state":"Merged","mergedAt":"2023-06-08T00:42:48Z","number":6450,"mergeCommitSha":"f69d5794e5c1f57399bb5527459ea3d0eeda2974","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6450","title":"Inspired by Richie - ChromaDB for prod","createdAt":"2023-06-07T23:56:31Z"}
{"state":"Merged","mergedAt":"2023-06-08T04:16:34Z","number":6451,"mergeCommitSha":"89c6a918240611bc2feb9b95171f1108acbd95a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6451","title":"Compile with ledger","createdAt":"2023-06-08T00:09:44Z"}
{"state":"Merged","mergedAt":"2023-06-08T00:28:44Z","number":6452,"mergeCommitSha":"569bea3186294e3a5cdba8bb8f15146db91289a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6452","title":"another ugly hack","createdAt":"2023-06-08T00:28:19Z"}
{"state":"Merged","mergedAt":"2023-06-08T00:48:42Z","number":6453,"mergeCommitSha":"f75884d22107ecfb9c430fbe9be2e0c2a69c6b33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6453","title":"this should add the alb targets back","createdAt":"2023-06-08T00:43:13Z"}
{"state":"Merged","mergedAt":"2023-06-16T20:00:16Z","number":6454,"body":"https://github.com/NextChapterSoftware/unblocked/assets/********/2aa8095d-d404-4707-a347-c4537fce1244\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/********/62459f68-783f-412f-b9c4-c35746d9d9fb\r\n\r\n\r\n\r\nNOTE: Hub icons will also need to be updated \r\n","mergeCommitSha":"84c161c962550d225d97c843009a76348c0058a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6454","title":"Update insight icons to source","createdAt":"2023-06-08T00:48:28Z"}
{"state":"Merged","mergedAt":"2023-06-09T15:22:05Z","number":6455,"mergeCommitSha":"c6b106dfcf42d5fd858a991704df4d674bfc4ecc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6455","title":"Fix slack url links","createdAt":"2023-06-08T07:16:23Z"}
{"state":"Merged","mergedAt":"2023-06-08T23:20:30Z","number":6456,"mergeCommitSha":"5d488a3b62ca58642af5261e8f535e5128019ca2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6456","title":"Add required template field descriptions to admin console","createdAt":"2023-06-08T17:43:40Z"}
{"state":"Merged","mergedAt":"2023-08-03T21:19:18Z","number":6457,"body":"Do not upgrade until this is fixed:\r\n\r\nhttps://github.com/Kotlin/kotlinx.serialization/issues/2323","mergeCommitSha":"c309e953a8c2bd65171ece2df667c25065f3a508","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6457","title":"Upgrade ktor / kotlinx serialization","createdAt":"2023-06-08T18:09:52Z"}
{"state":"Merged","mergedAt":"2023-06-09T16:59:37Z","number":6458,"body":"Refactors the code surrounding MessageEditor to make it easier to work with:\r\n\r\n* Add `MessageInput` -- this wraps MessageEditor and adds common shared UI functionality (invite dialog and associated calculations)\r\n* Add `MessageInputContext` -- this is provided anywhere above a MessageInput, and allows providing the input properties (git contributors, invitees, etc).  It also has methods for interacting with the MessageEditor itself, so that you don't have to pipe functions through dozens of layers of UI code.  We can expand on this over time.  Right now you can sign up for submit events and add/remove mention events.\r\n* Move the Reply message editor to the bottom of the dashboard thread view\r\n\r\n<img width=\"1619\" alt=\"Screenshot 2023-06-08 at 2 50 45 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/04c5cb06-6ad9-45cd-afcf-7c6f04bc81c8\">\r\n<img width=\"1619\" alt=\"Screenshot 2023-06-08 at 2 50 53 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/2dafd28b-8ff1-4b0f-8e85-18b246f1ff6e\">\r\n","mergeCommitSha":"46431b6e78d82a6aa45ac1f84a6852df668ecc69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/6458","title":"Refactor MessageEditor parent code","createdAt":"2023-06-08T18:17:49Z"}