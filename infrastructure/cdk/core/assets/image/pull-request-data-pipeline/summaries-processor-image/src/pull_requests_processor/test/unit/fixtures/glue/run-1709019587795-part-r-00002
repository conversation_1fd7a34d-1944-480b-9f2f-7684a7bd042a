{"state":"Merged","mergedAt":"2024-02-20T17:07:51Z","number":10640,"body":"Setup recommended Invitee Email\r\n\r\n![CleanShot 2024-02-09 at 16 15 33@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/94ab58f1-faab-47e8-bf55-46b9def0362c)\r\n\r\n\r\n\r\nTODO:\r\n* Setup Email Preferences on client\r\n","mergeCommitSha":"a781bdc82f8a9095c4a7497a12245af710c43744","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10640","title":"Setup Recommended Invitee Email","createdAt":"2024-02-10T00:15:11Z"}
{"state":"Merged","mergedAt":"2024-02-12T18:49:21Z","number":10641,"mergeCommitSha":"9020ad79c6235cb69b34d75ae32d57d131e6613e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10641","title":"Try unblocked endpoint improvements","createdAt":"2024-02-10T01:12:57Z"}
{"state":"Closed","mergedAt":null,"number":10642,"mergeCommitSha":"3fe97fee54690149fa1bb50e3f42dec7827e606f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10642","title":"Add secrets","createdAt":"2024-02-10T23:19:46Z"}
{"state":"Merged","mergedAt":"2024-02-11T19:29:36Z","number":10643,"mergeCommitSha":"6507118a864b7c35afd1ac7f5d65b5ea190bd287","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10643","title":"Allow traffic from new EKS CIDR to RDS","createdAt":"2024-02-11T19:28:31Z"}
{"state":"Merged","mergedAt":"2024-02-11T19:58:19Z","number":10644,"mergeCommitSha":"f721727e226c3a88f8fa7da724cd10790c6d039b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10644","title":"Allow connections to redis from new kube cluster","createdAt":"2024-02-11T19:57:27Z"}
{"state":"Merged","mergedAt":"2024-02-11T20:23:48Z","number":10645,"body":"Reverts NextChapterSoftware/unblocked#10606","mergeCommitSha":"c15a5a3842654c136f6955543d43faaf9dc4b271","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10645","title":"Revert \"Vector metadata content encryption\"","createdAt":"2024-02-11T20:00:08Z"}
{"state":"Merged","mergedAt":"2024-02-11T20:08:07Z","number":10646,"mergeCommitSha":"db46d25a1d32592e8358c7d92c92f0e8742a4ba5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10646","title":"Allow traffic from new Kube to ActiveMQ","createdAt":"2024-02-11T20:07:22Z"}
{"state":"Merged","mergedAt":"2024-02-12T02:21:17Z","number":10647,"body":"Customer data is encrypted at rest in Pinecone, and in transit to Pinecone.\n\nOnly Unblocked services with the `unblocked-content-service-secrets-env` have access to read the encryption key:\n- embedding-service (encrypt new vector content)\n- search-service (decrypt vector content for answers)\n- api-service (decrypt vector content for related documents API)\n- adminweb-service (decrypt vector content for debugging)\n\n## Rollout plan\nEncryption is lazy; all **new** vectors will have encrypted content. Existing vectors with plaintext metadata content will not be modified. The new serverless indexes will be 100% encrpypted, since they will contain only new vectors. And we are retiring the Pod indexes in the next few weeks once all teams have transitioned to serverless.\n\nThis change will be staged as follows across several PRs:\n\n1. In this PR, decryption is implemented in KT and enabled. Encryption is implemented in KT but not enabled.\n2. Then, encryption and decryption will be implemented in PY\n3. Then, encryption will be enabled in DEV\n4. Then, encryption will be enabled in PROD","mergeCommitSha":"192908f3b160f0f6025d8e1729c50a8af5067c2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10647","title":"Vector metadata content encryption V2","createdAt":"2024-02-12T01:01:05Z"}
{"state":"Merged","mergedAt":"2024-02-13T05:34:32Z","number":10648,"mergeCommitSha":"50ec30a5d40484cb6358dbb6d5d40f7a02e6b13b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10648","title":"Enable embedding metadata content encryption in DEV","createdAt":"2024-02-12T06:07:31Z"}
{"state":"Merged","mergedAt":"2024-02-12T22:02:20Z","number":10649,"mergeCommitSha":"bfc2afe7a2641fa35635ebc83147c1ed170d1b76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10649","title":"Python encryption library","createdAt":"2024-02-12T06:37:44Z"}
{"state":"Merged","mergedAt":"2022-04-28T22:04:52Z","number":1065,"mergeCommitSha":"6f9f95581f298c372fe2925a04f174189e1215a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1065","title":"Show person identities, and some admin fixes","createdAt":"2022-04-28T21:48:47Z"}
{"state":"Merged","mergedAt":"2024-02-12T18:34:24Z","number":10650,"body":"Discussion:\nhttps://chapter2global.slack.com/archives/C06JN59CWQY/p1707758803649199","mergeCommitSha":"495d4ef5157e00e372625f99f7ecf56e06e6f94b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10650","title":"Invitees should ignore bots that are manually marked as \"Threads Ignored\"","createdAt":"2024-02-12T18:09:14Z"}
{"state":"Merged","mergedAt":"2024-02-12T20:27:49Z","number":10651,"mergeCommitSha":"3cbfc6c53200a32ca97b18247f9bd99c23424edc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10651","title":"Setup backend support for email preferences for Recommended Invitee Emails","createdAt":"2024-02-12T18:58:59Z"}
{"state":"Merged","mergedAt":"2024-02-13T20:24:55Z","number":10652,"mergeCommitSha":"7e101584b5cdabdde49b6ea9537c9bc46c2b1edb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10652","title":"Wire up EmbeddingDeleteModel store for updating Google Drive configuration","createdAt":"2024-02-12T19:25:17Z"}
{"state":"Merged","mergedAt":"2024-02-12T21:25:37Z","number":10653,"mergeCommitSha":"70460d2c1ce54dfe388bc17276159dee8c96b479","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10653","title":"Add support for environments to CDK jobs","createdAt":"2024-02-12T21:00:28Z"}
{"state":"Merged","mergedAt":"2024-02-12T23:31:14Z","number":10654,"body":"…filter to take machinelearningapi provider to be consistent with other dependencies","mergeCommitSha":"d50481335d1d2e07fb3ddb9f0713344668e0a460","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10654","title":"Remove enableLLMContentFilter from GlobalConfig + refactor llmcontent…","createdAt":"2024-02-12T21:23:41Z"}
{"state":"Merged","mergedAt":"2024-02-12T22:04:46Z","number":10655,"mergeCommitSha":"10057d96a5f36978134590bb07c79f684dd2722c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10655","title":"remove unused inputs","createdAt":"2024-02-12T21:57:44Z"}
{"state":"Merged","mergedAt":"2024-02-12T22:35:14Z","number":10656,"mergeCommitSha":"572abb885bbf3d487600ce26d316a15bc7e00cc2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10656","title":"Updated Invite icon size","createdAt":"2024-02-12T22:22:56Z"}
{"state":"Merged","mergedAt":"2024-02-13T17:17:49Z","number":10657,"body":"Algorithm works as follows:\r\n1. Do constraint calculation pass (analogous to previous \"skipConstrains\" pass), which calculates the available room in the prompt for constrained sets, but also tabulates the constrained item size recursively.\r\n2. Run through the supplied global constrained item ordering list until max size is reached, then add items to the skip list\r\n3. During the main pass, everything operates as before with existing size constraints, but _also_ skips items in step 2. This allows size constrains to span across different constrained sets, although only globally (for now).\r\n\r\nNote that this is on the item level, prompt wide.\r\n\r\nPossible enhancement: add constrained set groups to constrain only particular sets.","mergeCommitSha":"70b2a64000f42e8a3c4c1f93c40c5cbc8049356c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10657","title":"Add global sizing constraint to template","createdAt":"2024-02-12T22:35:14Z"}
{"state":"Merged","mergedAt":"2024-02-14T21:42:12Z","number":10658,"mergeCommitSha":"3b959915976f68de7906a361ef6f373dc1f0d555","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10658","title":"Generate ProviderProcessingStatus","createdAt":"2024-02-13T00:39:39Z"}
{"state":"Merged","mergedAt":"2024-02-20T23:33:38Z","number":10659,"body":"Setup a Slack announcer for when Invite emails are sent.\r\n\r\n![CleanShot 2024-02-13 at 13 55 34@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/ec126c64-0162-44e6-a57d-b7e6e2e2bfe1)\r\n","mergeCommitSha":"512946388c5d093573962a3c321f658b7c776105","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10659","title":"Slack Channel announcer for Invites","createdAt":"2024-02-13T01:11:59Z"}
{"state":"Merged","mergedAt":"2022-04-28T23:42:38Z","number":1066,"body":"I have low confidence in this change.","mergeCommitSha":"1597aa4d7af3a3e2d35b8299a642b99e4da2f548","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1066","title":"Kinda fix the duplicate person bug","createdAt":"2022-04-28T22:04:00Z"}
{"state":"Merged","mergedAt":"2024-02-13T04:06:44Z","number":10660,"mergeCommitSha":"8bbded938057b7dc905abdf50310ec380af1b8f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10660","title":"Encrypt embedding metadata during code ingestion","createdAt":"2024-02-13T01:24:38Z"}
{"state":"Merged","mergedAt":"2024-02-13T04:08:45Z","number":10661,"mergeCommitSha":"19bb16a4c54357157da4de6eee4e38efd6dc32e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10661","title":"Use content encryption in python code ingest layer","createdAt":"2024-02-13T01:25:43Z"}
{"state":"Merged","mergedAt":"2024-02-13T04:21:35Z","number":10662,"mergeCommitSha":"cc88d8eb3043bc00a819459de09d7c91101d0da2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10662","title":"Cleanup batchEmbeddings dead code","createdAt":"2024-02-13T01:31:02Z"}
{"state":"Merged","mergedAt":"2024-02-13T05:06:29Z","number":10663,"mergeCommitSha":"2c742ea2e6b0d4ae2d19b311246c6be907884158","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10663","title":"Fix source code pipeline typo","createdAt":"2024-02-13T05:05:36Z"}
{"state":"Merged","mergedAt":"2024-02-13T05:17:13Z","number":10664,"mergeCommitSha":"1b990d906cf802d09185978b7c9bbcf22b46fc80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10664","title":"Fix error messaging","createdAt":"2024-02-13T05:15:03Z"}
{"state":"Merged","mergedAt":"2024-02-13T07:09:58Z","number":10665,"mergeCommitSha":"04109e74c8928861f5235b4cf312b53159207070","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10665","title":"[SKIP TESTS] Remove refinery secret","createdAt":"2024-02-13T06:58:47Z"}
{"state":"Merged","mergedAt":"2024-02-13T07:19:36Z","number":10666,"body":"fuck python","mergeCommitSha":"131c28f970beb7f25e0b36465bfaf3e1e4b2fc13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10666","title":"Fix code ingest encryption key type","createdAt":"2024-02-13T07:16:26Z"}
{"state":"Merged","mergedAt":"2024-02-13T17:03:54Z","number":10667,"mergeCommitSha":"da9d65ef91f472f589f547f96c3ca2bb058234a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10667","title":"Enable embedding metadata content encryption in PROD","createdAt":"2024-02-13T07:59:27Z"}
{"state":"Merged","mergedAt":"2024-02-13T18:36:35Z","number":10668,"body":"Adds new section templates for:\r\n- documentation\r\n- code\r\n- pull requests\r\n- slack\r\n\r\nUses work from previous PR to handle ordered insertion from different constrained sets: https://github.com/NextChapterSoftware/unblocked/pull/10657\r\n\r\nBackwards compatible with current templates","mergeCommitSha":"4231c6ade4f7657cc66c08019daa2cdb06981315","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10668","title":"Add document type specific sections to template","createdAt":"2024-02-13T16:56:52Z"}
{"state":"Merged","mergedAt":"2024-02-13T18:24:41Z","number":10669,"mergeCommitSha":"c680a6f69350b8719358427d3e776b2e7e8c0ef7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10669","title":"Add an index column on the people and team members pages","createdAt":"2024-02-13T17:23:23Z"}
{"state":"Merged","mergedAt":"2024-02-13T21:25:48Z","number":10670,"body":"**Article:**\r\n![CleanShot 2024-02-13 at 11 29 12@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/ac949121-a399-4190-a8cb-5f410ee185d6)\r\n\r\n\r\n**Blog Landing:**\r\n![CleanShot 2024-02-13 at 11 27 26@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/ee807c96-f895-41bc-a710-2462b3c03a6e)\r\n","mergeCommitSha":"629c1fa9c6db0c364a4f8f587f5c1d87f7df522a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10670","title":"Blog Article 003 - IDE Improvements","createdAt":"2024-02-13T19:25:11Z"}
{"state":"Merged","mergedAt":"2024-02-13T20:09:44Z","number":10671,"mergeCommitSha":"66927111e184c92a90a849966d4a7249ba7be7f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10671","title":"Apply an API limit parameter to getSuggestedQuestions to avoid hardcoding numbers","createdAt":"2024-02-13T19:28:48Z"}
{"state":"Merged","mergedAt":"2024-02-13T19:42:50Z","number":10672,"body":"- Added new EKS cluster using EKS blueprints\r\n- Added service accounts\r\n- Added blueprint addons to install dependencies\r\nand more...","mergeCommitSha":"ff0e054d794dd23ff8c434267ec5163522fde537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10672","title":"Add eks cluster new","createdAt":"2024-02-13T19:33:22Z"}
{"state":"Merged","mergedAt":"2024-02-13T20:02:04Z","number":10673,"mergeCommitSha":"76b663069015902c13871da1cad7e7cb6c2debde","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10673","title":"Tweak embedding logging","createdAt":"2024-02-13T19:44:39Z"}
{"state":"Merged","mergedAt":"2024-02-13T19:59:04Z","number":10674,"mergeCommitSha":"85d28cfe08ce627d27f73224c1456a58df23620a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10674","title":"pass secrets to cdk jobs","createdAt":"2024-02-13T19:46:46Z"}
{"state":"Merged","mergedAt":"2024-02-13T20:14:53Z","number":10675,"mergeCommitSha":"d91145c243daef46ed583ac3941db51743253770","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10675","title":"fix secrets for diff job","createdAt":"2024-02-13T20:07:45Z"}
{"state":"Merged","mergedAt":"2024-02-13T21:29:28Z","number":10676,"mergeCommitSha":"305f187d63e37621fc409c1a6cba361da2676af3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10676","title":"Wire up EmbeddingDeleteModel store for updating Web ingestion configuration","createdAt":"2024-02-13T20:47:49Z"}
{"state":"Merged","mergedAt":"2024-02-13T21:10:51Z","number":10677,"body":"Add exception for Calico api service contacting Kube API","mergeCommitSha":"5c129cbe7029d89f72ed7573a6b9514bd0f81c51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10677","title":"Make falco ignore calico calls to kube api","createdAt":"2024-02-13T21:09:37Z"}
{"state":"Merged","mergedAt":"2024-02-13T21:29:06Z","number":10678,"mergeCommitSha":"e406a832e3661bdc04dc746496e4110e4c48e1b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10678","title":"Omit doc sections that have no docs","createdAt":"2024-02-13T21:14:31Z"}
{"state":"Merged","mergedAt":"2024-02-13T22:22:48Z","number":10679,"body":"1. We use the existing flow we use for encrypting/decrypting secrets using ansible playbooks so the .env is properly versioned and mainained.\r\n2. Make it easy for local decryption setup for dotenv file.\r\n3. Make cdk load dotenv into process environment memory.","mergeCommitSha":"ecb8bac8206b09d252bb426061ee027c9ae0aa0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10679","title":"Add basic environment loader ot cdk","createdAt":"2024-02-13T21:32:03Z"}
{"state":"Merged","mergedAt":"2024-02-13T22:04:30Z","number":10680,"mergeCommitSha":"408c9796c912ee0524e82e872521c5cfc1f7333c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10680","title":"Another inline reference \uD83D\uDC1B","createdAt":"2024-02-13T21:50:48Z"}
{"state":"Merged","mergedAt":"2024-02-13T23:56:23Z","number":10681,"body":"Will be needed for generating the key for pinecone serverless","mergeCommitSha":"3364ac3710243c802626c422b1f31a60cc7e7f1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10681","title":"Pass along installation ID when embedding confluence pages","createdAt":"2024-02-13T22:37:13Z"}
{"state":"Merged","mergedAt":"2024-02-13T23:25:49Z","number":10682,"mergeCommitSha":"2df1a0edbcd7403616a0fc9e6adf84ee7fcf8d36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10682","title":"Add abilityt to decrypt cdk secrets","createdAt":"2024-02-13T22:43:10Z"}
{"state":"Merged","mergedAt":"2024-02-21T00:22:37Z","number":10683,"body":"In situations where email event fails, cleanup tracking event.","mergeCommitSha":"d63d6a593e7ef9c92eb089b318f6d9aa1a6b5192","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10683","title":"Cleanup EmailEventTypes on send failiure","createdAt":"2024-02-13T22:51:19Z"}
{"state":"Merged","mergedAt":"2024-02-13T23:42:42Z","number":10684,"body":"- Try again\r\n- Remove jfrog\r\n","mergeCommitSha":"6a928199fa3687ce0f2420b09c165ba13bc76688","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10684","title":"DecryptPlaybookPart2","createdAt":"2024-02-13T23:31:26Z"}
{"state":"Merged","mergedAt":"2024-02-14T00:18:47Z","number":10685,"body":"stringData obviates the need for a base64-encoded data","mergeCommitSha":"040a76f485e9c95e2ef8fa06f928ea6c937e88b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10685","title":"Fix not rquiring base64 encoded data","createdAt":"2024-02-13T23:57:03Z"}
{"state":"Merged","mergedAt":"2024-02-14T05:03:30Z","number":10686,"mergeCommitSha":"6a2bcccfbf729ee66f8c007546a285d79e5ea7a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10686","title":"Make installationId not null for confluence embedding events","createdAt":"2024-02-14T00:01:43Z"}
{"state":"Merged","mergedAt":"2024-02-14T00:44:44Z","number":10687,"body":"- only makes suggestions when the current member is actively engaged\n- only makes suggestions at most once every 60 days\n- suggestions are ordered descending by social connection\n- refactored for use in both in emails and invite suggestion API","mergeCommitSha":"07c9263a8efe8e6b6af011ded15d8ca778f5be95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10687","title":"Introduce PeerInviteSuggestionService to automatically suggest peers to invite","createdAt":"2024-02-14T00:15:42Z"}
{"state":"Merged","mergedAt":"2024-02-14T19:13:19Z","number":10688,"mergeCommitSha":"8b9686dbc87742aa1cf4cb249fd9593fa49be12d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10688","title":"Trigger peer invite on high engagement signal","createdAt":"2024-02-14T00:50:35Z"}
{"state":"Merged","mergedAt":"2024-02-14T07:25:52Z","number":10689,"body":"- Added Grafana add-on to forward metrics to Grafana Cloud\r\n- Moved the `GRAFANA_WEBHOOK_URL_INFRA_ALARMS` and `SLACK_WEBHOOK_URL_INFRA_ALARMS` secrets to .env files (dev and prod)\r\n- Added a new `GRAFANA_K8S_METRICS_TOKEN` secret to .env files (dev and prod)\r\n- Modified CI jobs to remove references to secrets. I'll delete the secrets from repos \r\n\r\n\r\n@rasharab FYI. We are now fully using .env files for CDK deploys. ","mergeCommitSha":"e4c0b044a5f1412159352c7e181464465c08654d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10689","title":"Add grafana addon","createdAt":"2024-02-14T07:17:28Z"}
{"state":"Merged","mergedAt":"2024-02-14T07:34:13Z","number":10690,"body":"- Added more exceptions for system containers using image repository as filter\r\n- Scaled up new Dev cluster to 3 nodes to match our existing one. \r\n\r\n","mergeCommitSha":"5eef32d63c6a66cda69854e12f4870eeba110fd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10690","title":"Scale up new dev cluster and update falco rules","createdAt":"2024-02-14T07:33:26Z"}
{"state":"Merged","mergedAt":"2024-02-14T18:13:27Z","number":10691,"mergeCommitSha":"a47096cdd8b6217039cfb88cbc37c486018c9fa9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10691","title":"Remove stray quote in blog listing","createdAt":"2024-02-14T18:00:57Z"}
{"state":"Merged","mergedAt":"2024-02-14T18:17:19Z","number":10692,"mergeCommitSha":"93b325c32f1bf9d70829b7ff3991c37650a6b609","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10692","title":"Update asset","createdAt":"2024-02-14T18:05:36Z"}
{"state":"Merged","mergedAt":"2024-02-14T23:50:52Z","number":10693,"body":"<img width=\"1375\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/a8ae5985-56bf-4345-8ad1-27b4a56a6a0c\">\r\n<img width=\"1364\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/a382a6ec-2aae-4764-bbc5-50b80b3bd614\">\r\n<img width=\"1368\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/05126938-d052-4eed-b13f-fe50d17f3c40\">\r\n","mergeCommitSha":"26b4d2f2e1854ac5959444080b79bbfbeccdb633","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10693","title":"Add try unblocked entry from landing page","createdAt":"2024-02-14T18:27:37Z"}
{"state":"Merged","mergedAt":"2024-02-14T18:58:13Z","number":10694,"mergeCommitSha":"295aac8f38e17d8af5c60c05e0908c602f0fd963","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10694","title":"[SKIP TESTS] - fix possible null documents issue","createdAt":"2024-02-14T18:47:44Z"}
{"state":"Merged","mergedAt":"2024-02-14T19:38:01Z","number":10695,"body":"- Add CF only ingress security group. Delete unused code\r\n- Remove unused SNS topic\r\n- Remove unused code","mergeCommitSha":"0a72a8743fb32d87c7b57259dd76cd8810ca1e89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10695","title":"Add CF only ingress security group. Delete unused code","createdAt":"2024-02-14T19:31:36Z"}
{"state":"Merged","mergedAt":"2024-02-14T20:00:56Z","number":10696,"mergeCommitSha":"3172c5c2704ab58dd8c68539e6ce66eedac05ee7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10696","title":"use the same name as the old sg","createdAt":"2024-02-14T19:47:29Z"}
{"state":"Merged","mergedAt":"2024-02-14T20:39:44Z","number":10697,"mergeCommitSha":"2defcc95ff955a1a8353f908647f4268b8b83bf1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10697","title":"Show Lness or engagement score in admin web","createdAt":"2024-02-14T20:12:12Z"}
{"state":"Merged","mergedAt":"2024-02-14T21:33:36Z","number":10698,"mergeCommitSha":"4f6447324116570a53c0d8b46da6ccd98b073a8b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10698","title":"Show L-ness activity by default in admin web","createdAt":"2024-02-14T21:22:17Z"}
{"state":"Merged","mergedAt":"2024-02-15T21:32:59Z","number":10699,"body":"![CleanShot 2024-02-14 at 14 05 18@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/9e221b8c-f895-47ac-abf4-15f8ecc703c4)\r\n","mergeCommitSha":"3d0262ebcbe7f0d049040645db8f8c728ce5b047","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10699","title":"Add pending processing state rows","createdAt":"2024-02-14T22:06:44Z"}
{"state":"Merged","mergedAt":"2022-01-24T21:03:31Z","number":107,"body":"## Problem\r\n\r\nNative clients can't participate in the OAuth flow. In order for a native client to obtain an access token, they must piggyback on the OAuth flow and obtain the token out-of-band. \r\n\r\n## Proposal\r\n\r\nWe introduce two new endpoints: `/preauth` and `/preauth/exchange`. These endpoints facilitate the exchange of an initial client secret, which becomes associated with the server generated client nonce and piggybacks the auth flow until finally being associated with an authentication event. \r\n\r\nThe native client can then poll in the background while the user authenticates through the browser, and then upgrade their preauth token for a general access token.\r\n\r\nThe flow is illustrated in `github_app_based_user_auth_sequence.puml`\r\n\r\n## Additionally\r\n\r\nThis PR includes a few other minor additions:\r\n1. Adoption of API changes introduced in https://github.com/Chapter2Inc/codeswell/pull/96\r\n2. Introduction of an `AuthenticationConfig` for hocon-based environment loading of secrets\r\n3. Slight refactor of token configuration to make it more portable for testing","mergeCommitSha":"e8473235b570950ae5376c7c23ef6cc201f33f62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/107","title":"Add pre-auth endpoints","createdAt":"2022-01-23T03:51:34Z"}
{"state":"Merged","mergedAt":"2022-04-29T03:55:30Z","number":1070,"mergeCommitSha":"e256244d046b22930cdd955d7eece8a756d0ca46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1070","title":"Handle top-level image","createdAt":"2022-04-28T22:44:15Z"}
{"state":"Merged","mergedAt":"2024-02-14T22:22:24Z","number":10700,"mergeCommitSha":"1d545a1e05fea0ab357b44417f527e2004677415","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10700","title":"Only show L-ness on active views","createdAt":"2024-02-14T22:10:09Z"}
{"state":"Merged","mergedAt":"2024-02-15T00:41:21Z","number":10701,"mergeCommitSha":"18044c9f6e3b918a01b5b6c251ccfbed779ed375","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10701","title":"Create EmbeddingDeleteModels when confluence spaces are deselected","createdAt":"2024-02-14T22:12:50Z"}
{"state":"Merged","mergedAt":"2024-02-14T22:42:36Z","number":10702,"mergeCommitSha":"7fb996babd47daaeeac83a7633b12bd8db59a712","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10702","title":"Only pluralize if >1 repository","createdAt":"2024-02-14T22:30:11Z"}
{"state":"Merged","mergedAt":"2024-02-14T23:25:14Z","number":10703,"mergeCommitSha":"6ea0a0a194b72ee049a91e04995ac8af4294f97d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10703","title":"move logging so it is emitted in all cases","createdAt":"2024-02-14T22:37:02Z"}
{"state":"Merged","mergedAt":"2024-02-14T22:39:32Z","number":10704,"mergeCommitSha":"9e0a7d9e57f3e040ef12ac311219ff149a6c46ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10704","title":"[SKIP TESTS] - add prompt compiler logging to debug issue","createdAt":"2024-02-14T22:39:21Z"}
{"state":"Merged","mergedAt":"2024-02-15T00:00:09Z","number":10705,"mergeCommitSha":"9cad83acba2695a38dcd1fffb48b1a0cc15431f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10705","title":"Add issues section to prompt","createdAt":"2024-02-14T23:18:45Z"}
{"state":"Open","mergedAt":null,"number":10706,"mergeCommitSha":"6a4a801d11a838e8ddeda807dbe747bbf17d711d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10706","title":"Move approvedTopics logging to sensitive log store","createdAt":"2024-02-14T23:23:04Z"}
{"state":"Merged","mergedAt":"2024-02-14T23:45:40Z","number":10707,"mergeCommitSha":"e1d3c9571e004c1cb13ddf9b2f3ec1dc3ffb5ca7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10707","title":"[SKIP TESTS] - move prompt compiler logging up a level","createdAt":"2024-02-14T23:44:23Z"}
{"state":"Merged","mergedAt":"2024-02-15T00:14:10Z","number":10708,"mergeCommitSha":"aa98647e95aaeda2ae7e4cecee030bedf8497d3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10708","title":"Add Pinecone describe index stats API for debugging","createdAt":"2024-02-14T23:58:39Z"}
{"state":"Merged","mergedAt":"2024-02-15T00:33:22Z","number":10709,"mergeCommitSha":"624ce0a0c3f57180373a809f5d6d45facf9a0f02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10709","title":"Fix try unblocked responsive view","createdAt":"2024-02-15T00:20:29Z"}
{"state":"Merged","mergedAt":"2022-04-28T23:41:16Z","number":1071,"mergeCommitSha":"9285eb7168c8a0cd986ee74c095e4eb8e085554e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1071","title":"Threads page correctly filters by repo","createdAt":"2022-04-28T23:02:02Z"}
{"state":"Merged","mergedAt":"2024-02-15T16:10:34Z","number":10710,"mergeCommitSha":"523836d1b5fde0046430ff249fc9b73ad07bd5f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10710","title":"Create EmbeddingDeleteModels when jira projects are deselected","createdAt":"2024-02-15T00:45:34Z"}
{"state":"Merged","mergedAt":"2024-02-15T01:12:35Z","number":10711,"mergeCommitSha":"98f9e144e6b3d52c64681003789c98d21a6419d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10711","title":"Update hover color of outline button","createdAt":"2024-02-15T00:59:22Z"}
{"state":"Merged","mergedAt":"2024-02-15T01:46:02Z","number":10712,"mergeCommitSha":"e89f0fc2bc78ded27e793da2ddca9ffd7d2bc7e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10712","title":"Pinecone vector list HTTP API","createdAt":"2024-02-15T01:26:10Z"}
{"state":"Merged","mergedAt":"2024-02-15T17:19:31Z","number":10713,"mergeCommitSha":"0cc7c4130ad26ba07c22c2f1bcf672e473876e3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10713","title":"Create EmbeddingDeleteModels when linear teams are deselected","createdAt":"2024-02-15T05:58:39Z"}
{"state":"Merged","mergedAt":"2024-02-15T06:32:26Z","number":10714,"body":"Suppress this alarm: https://chapter2global.slack.com/archives/C04M9AH4GA0/p1707951019613799","mergeCommitSha":"e3ba2add3b9a0b3551f323949680a5f8104771f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10714","title":"Exclude Falco metric server from alarms","createdAt":"2024-02-15T06:31:24Z"}
{"state":"Merged","mergedAt":"2024-02-15T17:58:37Z","number":10715,"body":"We want stretch alignment vertically, not center alignment.","mergeCommitSha":"d0edf4a0a253ea4c7c427fb1b443b34090e05aee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10715","title":"Fix landing page login button alignment","createdAt":"2024-02-15T17:46:24Z"}
{"state":"Merged","mergedAt":"2024-02-15T18:09:03Z","number":10716,"body":"To offset the height difference for primary vs outline buttons, add borders to all buttons, but for primary types, have the border color match the background color of the button.\r\n\r\n<img width=\"1011\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/95eee546-09db-43b2-8994-e7e31e59ada4\">\r\n<img width=\"691\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/c2745f96-0c0c-4b76-bea4-9b9e3ea8fe2a\">\r\n<img width=\"439\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/acf6d113-a0c1-4d7e-956e-b6dfed4f4176\">\r\n\r\nNote: this change has only been made for the web; can address other clients as necessary but didn't want to over-optimize.\r\n","mergeCommitSha":"1405eee4641e05f5b8678a305389911ad8e3cf8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10716","title":"Add missing borders to primary button types","createdAt":"2024-02-15T17:48:35Z"}
{"state":"Merged","mergedAt":"2024-02-15T18:18:46Z","number":10717,"mergeCommitSha":"9592b94fcbfd81d8f37d79e20bb05aec4d83fe77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10717","title":"PeerInviteSuggestion emails are sent at most every 45 days (was 60)","createdAt":"2024-02-15T18:04:50Z"}
{"state":"Merged","mergedAt":"2024-02-16T01:13:47Z","number":10718,"body":"The idea here is to determine which channels are no longer `valid` after a user updates the slack configuration, and delete the embeddings for these newly invalid channels which we were previously embedding.","mergeCommitSha":"89fedf326a44bab187da619cf0ca6a1507a2ca29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10718","title":"Create EmbeddingDeleteModels when slack channels are deselected","createdAt":"2024-02-15T18:46:23Z"}
{"state":"Merged","mergedAt":"2024-02-20T17:23:36Z","number":10719,"mergeCommitSha":"a61dafdd4a2548257b8d08419bb16512743d7eb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10719","title":"Fix vector list Pinecone API","createdAt":"2024-02-15T19:16:18Z"}
{"state":"Merged","mergedAt":"2022-04-28T23:35:10Z","number":1072,"mergeCommitSha":"2c870694653774ed63f9e54e0a37504b59801702","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1072","title":"Remove unused field","createdAt":"2022-04-28T23:13:17Z"}
{"state":"Merged","mergedAt":"2024-02-15T19:42:53Z","number":10720,"mergeCommitSha":"013daeb4e7d3f53cccfe7e8c77ccb7a63fe0b1f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10720","title":"Fix rogue button borders","createdAt":"2024-02-15T19:25:51Z"}
{"state":"Merged","mergedAt":"2024-02-15T22:34:40Z","number":10721,"mergeCommitSha":"a5b53d0668e6431c019c734f4bdc85e1d3352cd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10721","title":"Add document titles to landing pages and login","createdAt":"2024-02-15T19:55:19Z"}
{"state":"Merged","mergedAt":"2024-02-15T22:23:04Z","number":10722,"mergeCommitSha":"66bf8d9ae1d7b25addb63450c40572b5267f1049","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10722","title":"Add ability to get installation by SlackTeamModel.id, JiraSiteModel.id, or LinearTeamModel.id","createdAt":"2024-02-15T20:50:26Z"}
{"state":"Merged","mergedAt":"2024-02-15T21:33:38Z","number":10723,"mergeCommitSha":"0d7f7f1ac31603c3b644c658d0d60666131b9311","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10723","title":"Split doc queries by type for semantic search","createdAt":"2024-02-15T20:57:51Z"}
{"state":"Merged","mergedAt":"2024-02-18T22:17:33Z","number":10724,"body":"Equivalent UI to the dashboard:\r\n\r\n* Added `TeamStatusObservable`, an on-demand created Observable object that tracks team status.  This is used while the relevant onboarding step is shown.\r\n* Added `Provider.image`, and added icons for each provider\r\n\r\n![CleanShot 2024-02-15 at 13 04 48@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/dbf45b5a-98ef-4788-943d-a317d6a18881)\r\n![CleanShot 2024-02-15 at 13 05 17@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/5a46ccd8-b1ce-4ad3-840f-40f8e4289161)\r\n","mergeCommitSha":"6766916dc921df2463e8923eb835b854bd6a4530","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10724","title":"Hub progress UI","createdAt":"2024-02-15T21:08:34Z"}
{"state":"Merged","mergedAt":"2024-02-15T21:21:47Z","number":10725,"body":"We've dropped most titles down from 700 to 600. These are two that haven't yet been updated.\r\n\r\n![CleanShot 2024-02-15 at 13 07 45@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/731e943e-8daf-4c0f-86dd-589df871aad8)\r\n\r\n![CleanShot 2024-02-15 at 13 08 20@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/42a1ae59-3688-4418-b340-f9b2d1c72615)\r\n","mergeCommitSha":"699a7d9375366d931016734d7ffd5a3451204a8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10725","title":"Drop landing title weight","createdAt":"2024-02-15T21:08:45Z"}
{"state":"Merged","mergedAt":"2024-02-15T21:42:27Z","number":10726,"mergeCommitSha":"670f45a0d94d54d7cdd2ea9c3c3c120b140cde05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10726","title":"[SKIP TESTS] - add issue documentation to template editor","createdAt":"2024-02-15T21:36:56Z"}
{"state":"Merged","mergedAt":"2024-02-15T23:06:41Z","number":10727,"mergeCommitSha":"0ca2ed90d056b0c20fe5a0ad2b606814cd110791","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10727","title":"Clean up landing hero button sizing","createdAt":"2024-02-15T22:35:40Z"}
{"state":"Merged","mergedAt":"2024-02-15T23:08:58Z","number":10728,"body":"I think the`github.run_id` was making the grouping label unique for every run! \r\n\r\nThis might give us what we want. I am not sure if want to include the `cancel` bit or not ?","mergeCommitSha":"4411c1d61db85bc6db60fe76684565e29f9a0787","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10728","title":"Remove workflow id from group key","createdAt":"2024-02-15T22:43:10Z"}
{"state":"Merged","mergedAt":"2024-02-15T23:02:04Z","number":10729,"body":"Fix doc retrieval in admin console\n\nlint fixes","mergeCommitSha":"39e445580f5dd6da5f285e046899c310e5b640e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10729","title":"Fix doc retrieval in admin console","createdAt":"2024-02-15T23:01:28Z"}
{"state":"Merged","mergedAt":"2022-04-29T18:37:24Z","number":1073,"body":"Setup slack notification for client releases to #client-releases","mergeCommitSha":"d19c7c1550a13da0aa377ef3becdbdd003a61b38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1073","title":"Slack notification for releases","createdAt":"2022-04-28T23:16:45Z"}
{"state":"Merged","mergedAt":"2024-02-15T23:34:25Z","number":10730,"mergeCommitSha":"d8071b776003a4a634f9cf32958864a848cf2814","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10730","title":"Updated dashboard wording","createdAt":"2024-02-15T23:22:22Z"}
{"state":"Merged","mergedAt":"2024-02-16T00:30:43Z","number":10731,"body":"For when we embed for pinecone server less","mergeCommitSha":"59de8d7487eadec2c665e05e224529621bc21446","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10731","title":"Create InstallationIdProvider","createdAt":"2024-02-15T23:22:50Z"}
{"state":"Merged","mergedAt":"2024-02-15T23:40:32Z","number":10732,"mergeCommitSha":"13ddc05dd5c9abd652edb660eb345e7298a82ffa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10732","title":"[SKIP TESTS] - additional doc page logging","createdAt":"2024-02-15T23:40:23Z"}
{"state":"Merged","mergedAt":"2024-02-16T00:09:15Z","number":10733,"body":"Reverts NextChapterSoftware/unblocked#10728","mergeCommitSha":"b95d99876cc8c187586399d7503b407813b04c94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10733","title":"Revert \"Remove workflow id from group key\"","createdAt":"2024-02-16T00:06:16Z"}
{"state":"Merged","mergedAt":"2024-02-16T00:31:50Z","number":10734,"mergeCommitSha":"63fdfd8651578721fe32714c486aa5162b5f013d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10734","title":"[SKIP TESTS] - more pinecone logging","createdAt":"2024-02-16T00:31:39Z"}
{"state":"Merged","mergedAt":"2024-02-23T02:24:39Z","number":10735,"mergeCommitSha":"c01c54feed743a7529b87ecbc5f9bcc32cc9ed51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10735","title":"Create EmbeddingDeleteModels when GitHub issues are disabled","createdAt":"2024-02-16T00:50:19Z"}
{"state":"Merged","mergedAt":"2024-02-16T17:15:19Z","number":10736,"mergeCommitSha":"39ec98237e095d6f3a621b051bf9cb1e0b783c9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10736","title":"[SKIP TESTS] - more compiler logging","createdAt":"2024-02-16T17:13:27Z"}
{"state":"Merged","mergedAt":"2024-02-16T17:55:06Z","number":10737,"mergeCommitSha":"6bc5fb90ebcb9a5de7560b8beeaa07c48a14f0b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10737","title":"[SKIP TESTS] - add logging to document sort","createdAt":"2024-02-16T17:54:00Z"}
{"state":"Merged","mergedAt":"2024-02-16T18:28:26Z","number":10738,"mergeCommitSha":"e66f4069eb45c970f36e07086ea85c9d699d0a5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10738","title":"[SKIP TESTS] Disable deploys to old dev cluster","createdAt":"2024-02-16T17:54:23Z"}
{"state":"Merged","mergedAt":"2024-02-16T18:06:39Z","number":10739,"mergeCommitSha":"9907648f3e04b14957a63d8a3ba1aff045944b36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10739","title":"[SKIP TESTS] - add logging to list merge step","createdAt":"2024-02-16T18:06:25Z"}
{"state":"Merged","mergedAt":"2022-04-29T16:30:19Z","number":1074,"body":"* Remove the logout button (flagged off for non-local environments)\r\n* Make sure the thread title doesn't wrap\r\n<img width=\"635\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/165862760-9158fa06-e17a-465e-84b3-3d801bc290a2.png\">\r\n","mergeCommitSha":"81eea877b50e261a8e76fc4210f58aa08d1897f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1074","title":"Some extension style fixes","createdAt":"2022-04-28T23:17:43Z"}
{"state":"Merged","mergedAt":"2024-02-16T20:00:51Z","number":10740,"mergeCommitSha":"809360019b1b35f98805c2e6a1fa98b0a1d5f295","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10740","title":"Fix filtering non-sourcecode docs","createdAt":"2024-02-16T19:46:20Z"}
{"state":"Merged","mergedAt":"2024-02-18T19:06:59Z","number":10741,"mergeCommitSha":"f189e74622dd7abb82200c6fb681509bc1415953","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10741","title":"[SKIP TESTS] Add startDate query param to MetricsPage","createdAt":"2024-02-18T18:57:12Z"}
{"state":"Merged","mergedAt":"2024-02-18T20:24:01Z","number":10742,"mergeCommitSha":"544b882e97f5c44496abfc351f3d4747b754f69c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10742","title":"[SKIP TESTS] Remove limits from MetricsPage","createdAt":"2024-02-18T20:14:20Z"}
{"state":"Merged","mergedAt":"2024-02-20T01:33:21Z","number":10743,"body":"remove some other test teams too.","mergeCommitSha":"29e3eb7039dbc85aa60b3554c126c21234f0414f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10743","title":"add nrsh to insider config","createdAt":"2024-02-20T00:43:47Z"}
{"state":"Merged","mergedAt":"2024-02-20T06:23:44Z","number":10744,"mergeCommitSha":"6378395f7b3e3afb3bd39822047fc9504f0358a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10744","title":"Add suspended team status state to API","createdAt":"2024-02-20T05:58:29Z"}
{"state":"Merged","mergedAt":"2024-02-20T23:00:49Z","number":10745,"body":"This replaces the old `UnsuppoertedReposInternal` and `UnsupportedReposExternal` UIs.\r\n\r\n![CleanShot 2024-02-19 at 22 19 52@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b13bd1ad-a645-40f7-9726-011935fcf6bd)\r\n","mergeCommitSha":"deb40918503ade8c1f2a520010cf846b6732397f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10745","title":"Updated 'missing valid repos' UI","createdAt":"2024-02-20T06:20:44Z"}
{"state":"Merged","mergedAt":"2024-02-20T17:19:37Z","number":10746,"body":"## Problem\r\nPinecone RPC API calls were blocking.\r\n\r\n## Motivation\r\nPinecone RPC API calls are non-blocking now; so they work like coroutines that can be suspended and parallelized using familiar kotlin concurrency model.\r\n\r\n## Related\r\n- @pwerry thread (https://chapter2global.slack.com/archives/C045VGYML95/p1708040275205279)\r\n- guava package (https://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-guava/)\r\n- await (https://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-guava/kotlinx.coroutines.guava/await.html)","mergeCommitSha":"24ac967ccf0b04e22894e226c07361bebad77289","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10746","title":"Non-blocking Pinecone client","createdAt":"2024-02-20T08:04:12Z"}
{"state":"Merged","mergedAt":"2024-02-20T18:08:02Z","number":10747,"body":"I'll follow up with a limit query parameter later","mergeCommitSha":"2111591aad3f78f111abedad5ec2b0cbda8ba099","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10747","title":"Set max suggested invitees to 6","createdAt":"2024-02-20T17:55:34Z"}
{"state":"Merged","mergedAt":"2024-02-20T18:22:27Z","number":10748,"body":"Allows right-click menu, Cmd-Click to open in a new tab, etc.","mergeCommitSha":"cdb6d779a028e4cccfb6f01d5a52b51dc5d82261","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10748","title":"'Ask a Question' button acts like a normal link","createdAt":"2024-02-20T18:09:36Z"}
{"state":"Merged","mergedAt":"2024-02-20T18:54:33Z","number":10749,"body":"- Move to lambda 3.10\r\n- Up standard lambda runtime\r\n","mergeCommitSha":"b67d4dccfbbc39ecbb997b8e0600b0430f5a78c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10749","title":"UpdateLambdas","createdAt":"2024-02-20T18:52:17Z"}
{"state":"Merged","mergedAt":"2022-04-29T05:57:02Z","number":1075,"body":"- Modified AWS provider to support a special `local` region. This region is used to point AWS clients to local instances of aws services run via localstack\r\n- Changed our configs so we only have to supply full queue names e.g `email_notifications-.fifo` or 'unblocked_pr_comments-standard'\r\n- Modified global, prod and local configs to reflect the change mentioned above\r\n- Removed queue configs from prod since it's now identical to global one\r\n- Added a special config to local config file with region set to `local`\r\n- Modified notification service to reflect AWSCLI and Config changes\r\n- Updated notification service tests and MockAWSProvider class\r\n- Added an init script to localstack docker compose config to create queues used for running local services\r\n- Added a health check to docker compose files to prevent services from running until localstack has finished init\r\n- Removed the last run config based on profiles. We no longer need it and instead we only need to set dummy environment values for environment variables e.g look at `.run/NotificationService.run.xml`\r\n\r\nTested locally and it works. I can run tests as well as running local services against localstack.","mergeCommitSha":"d9b56e408a851194a037c571cd335e0e0e0ddfcf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1075","title":"Point local services to LocalStack AWS services","createdAt":"2022-04-29T05:13:18Z"}
{"state":"Merged","mergedAt":"2024-02-20T19:36:08Z","number":10750,"body":"Forgot to remove this when I disabled the old Dev cluster. At least our system is resilient enough to do clean rollbacks","mergeCommitSha":"13725ee731c8a559be1222e0b428dbfc964d92cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10750","title":"[SKIP TESTS] Forgot to remove continue on error","createdAt":"2024-02-20T19:23:35Z"}
{"state":"Merged","mergedAt":"2024-02-21T17:09:16Z","number":10751,"body":"Team invite emails in Sendgrid.\r\n\r\n![CleanShot 2024-02-20 at 11 15 09@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/8143febf-3957-4f24-97ff-858eef875707)\r\n\r\n","mergeCommitSha":"32a721a9983d4d60a954d046d9323c0ffa269d39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10751","title":"Team invite emails","createdAt":"2024-02-20T19:35:01Z"}
{"state":"Merged","mergedAt":"2024-02-20T20:39:58Z","number":10752,"mergeCommitSha":"b8c10c76aa31aa7941b64b73d773904843244512","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10752","title":"Update team members at most once every 5 minutes","createdAt":"2024-02-20T20:26:51Z"}
{"state":"Merged","mergedAt":"2024-02-20T21:09:57Z","number":10753,"mergeCommitSha":"23d921ed9d526eedd25ce19f9e3c96215eac5f31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10753","title":"Add new llm endpoints","createdAt":"2024-02-20T20:39:32Z"}
{"state":"Merged","mergedAt":"2024-02-20T22:14:32Z","number":10754,"body":"Fix migration from client default.\r\n\r\nAdd check for preferences before sending email.","mergeCommitSha":"6fa9dae85565e735d8ce7920902ab82afc9ccc24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10754","title":"Update migration with personal preferences","createdAt":"2024-02-20T21:17:55Z"}
{"state":"Merged","mergedAt":"2024-02-21T23:01:54Z","number":10755,"body":"![CleanShot 2024-02-20 at 15 37 50@2x](https://github.com/NextChapterSoftware/unblocked/assets/1924615/0654079d-d06b-466b-ac92-fa2cf3322a33)","mergeCommitSha":"3ea9db0a1eaf1ceac814b27366f175939d56f802","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10755","title":"Add getTeamStats operation","createdAt":"2024-02-20T21:50:06Z"}
{"state":"Merged","mergedAt":"2024-02-20T22:03:05Z","number":10756,"mergeCommitSha":"9033b0c8ae2f4dfc7b50ef7386a6f21b872a843c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10756","title":"Fix alb","createdAt":"2024-02-20T22:03:00Z"}
{"state":"Merged","mergedAt":"2024-02-20T22:40:48Z","number":10757,"mergeCommitSha":"9acaf7157ad5c3df678848e3eff7a040219030f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10757","title":"GetTeamStatus API checks missingValidRepos first, before repoSelectionNeeded","createdAt":"2024-02-20T22:15:57Z"}
{"state":"Merged","mergedAt":"2024-02-20T22:57:31Z","number":10758,"mergeCommitSha":"6e111e9ff71aacbfed57b992cae0fabeedb1ef22","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10758","title":"Technical thread experiment","createdAt":"2024-02-20T22:20:30Z"}
{"state":"Merged","mergedAt":"2024-02-20T23:32:55Z","number":10759,"mergeCommitSha":"cd4a74a5c98bc96f0589e43bc337cfe544472946","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10759","title":"Add Matt's personal GL org to dev env and insiders","createdAt":"2024-02-20T23:09:56Z"}
{"state":"Merged","mergedAt":"2024-02-21T00:43:31Z","number":10760,"body":"Was not sending preferences due to missing API model conversion.","mergeCommitSha":"5ffd93ad26c084aa8ec960a6466b84ff2f556320","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10760","title":"Fix Email Preferences API","createdAt":"2024-02-20T23:24:46Z"}
{"state":"Merged","mergedAt":"2024-02-22T00:39:42Z","number":10761,"body":"https://chapter2global.slack.com/archives/C06JN59CWQY/p1708460178399059?thread_ts=1707772671.860049&cid=C06JN59CWQY\r\n\r\n![CleanShot 2024-02-20 at 15 32 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/1924615/0f12b0d8-3cdb-4485-be01-3a38803bc557)","mergeCommitSha":"c76d9cb7eff68cc5fa8e9291c11bf6efb99b4fcf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10761","title":"Add listTeamMembers","createdAt":"2024-02-20T23:33:58Z"}
{"state":"Merged","mergedAt":"2024-02-21T23:30:35Z","number":10762,"body":"Add temporary logging to PeerInviteSuggestionService to debug.","mergeCommitSha":"127e4fab3506ea3bb0479fc447723bcd452f5efa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10762","title":"Add temp logs for PeerInviteSuggestionService","createdAt":"2024-02-21T00:09:30Z"}
{"state":"Merged","mergedAt":"2024-02-21T01:57:31Z","number":10763,"mergeCommitSha":"8cbaf73092736ae79a9d77153738aa444e78935f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10763","title":"Add experiments","createdAt":"2024-02-21T01:47:43Z"}
{"state":"Merged","mergedAt":"2024-02-21T02:55:31Z","number":10765,"mergeCommitSha":"bddbf0645d7651107c2f232b884247a878c80945","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10765","title":"[SKIP TESTS] Fix experiment routing","createdAt":"2024-02-21T02:42:06Z"}
{"state":"Merged","mergedAt":"2024-02-21T03:43:56Z","number":10766,"mergeCommitSha":"6bf4a3634f28b71123a09d289f89ebbb27af8718","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10766","title":"rerun","createdAt":"2024-02-21T03:43:50Z"}
{"state":"Merged","mergedAt":"2024-02-21T04:37:33Z","number":10767,"body":"Reverts NextChapterSoftware/unblocked#10757","mergeCommitSha":"1523bba08ef41e658ee1b3942914d3cab5bb4f30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10767","title":"Revert \"GetTeamStatus API checks missingValidRepos first, before repoSelectionNeeded\"","createdAt":"2024-02-21T04:37:27Z"}
{"state":"Open","mergedAt":null,"number":10768,"body":"Add inviteID to `getLoginOptionsV3` and `OAuthState`\r\n\r\n`getLoginOptionsV3` - Encodes inviteID in the OAuth State object which is part of the login urls.\r\n\r\nThe OAuthState is decoded during the exchange stage of the auth flow.\r\nIf the user is new, we will associate the inviteID to that new user.\r\nIf the inviteID matches an existing invite, associate the inviter to the invitee.\r\nIf it does not match, store as an arbitrary string for now in hopes to match with UTM codes.\r\n\r\n ","mergeCommitSha":"da19cdb7c97f411cfeae66f0c3e08791e36b9e7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10768","title":"[RFC] Tracking invite conversions","createdAt":"2024-02-21T05:59:29Z"}
{"state":"Merged","mergedAt":"2024-02-21T19:11:10Z","number":10769,"body":"- Added function to create default deny network policy \r\n- Cleaned up the EKS stack code\r\n- Removed unused code and cdk output","mergeCommitSha":"e60456ed8e59f23150dd962a17991ba77c255b71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10769","title":"Cleanup the code and default deny policy for default namespace","createdAt":"2024-02-21T09:01:13Z"}
{"state":"Merged","mergedAt":"2022-04-29T17:45:44Z","number":1077,"body":"- Modified Make target for dropping local-stack db so it removes orphaned services first\r\n- Updated AWSProvider class to accept env var overrides for AWS endpoint. This is used within local compose environment\r\n- Added new env vars for Localstack AWS services to compose config\r\n- Fixed a mistake in notification service dockerfile. We were pushing the wrong jar and somehow it wasn't caught by checks. I am not sure how we even managed to deploy the service this far. ","mergeCommitSha":"7a27c4e1c35c66f3af4374d404a8ae8810a7a38a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1077","title":"Add support for Localstack api to local services stack","createdAt":"2022-04-29T17:31:01Z"}
{"state":"Merged","mergedAt":"2024-02-21T16:37:41Z","number":10770,"mergeCommitSha":"8a51e5149bf1318cf59816c623ec270508f20380","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10770","title":"Update kube creds","createdAt":"2024-02-21T16:37:30Z"}
{"state":"Merged","mergedAt":"2024-02-21T22:33:09Z","number":10771,"body":"https://chapter2global.slack.com/archives/C06JN59CWQY/p1707772671860049\r\n\r\n![image](https://github.com/NextChapterSoftware/unblocked/assets/1924615/2125ec76-d158-4729-b687-3f988f0f1087)\r\n","mergeCommitSha":"abef0b48ff22b2c7ccb075ea7c57e56cfb4062c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10771","title":"Add TeamMember properties for team management page","createdAt":"2024-02-21T17:28:28Z"}
{"state":"Merged","mergedAt":"2024-02-21T18:06:47Z","number":10772,"mergeCommitSha":"94dc22734d2f7455c7457dc23f3b97ae35f5e615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10772","title":"Move away from alb","createdAt":"2024-02-21T18:04:13Z"}
{"state":"Merged","mergedAt":"2024-02-21T21:59:47Z","number":10773,"mergeCommitSha":"2449d12f18e0627e135c971fefb06d4931a883d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10773","title":"Populate repo is empty during onboarding","createdAt":"2024-02-21T18:20:48Z"}
{"state":"Merged","mergedAt":"2024-02-21T18:27:47Z","number":10774,"body":"This reverts commit 94dc22734d2f7455c7457dc23f3b97ae35f5e615.\r\n","mergeCommitSha":"743e9201d8983e4f849e6d7b958c6efcb7438943","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10774","title":"Revert \"Move away from alb (#10772)\"","createdAt":"2024-02-21T18:27:35Z"}
{"state":"Open","mergedAt":null,"number":10775,"body":"This reverts commit 743e9201d8983e4f849e6d7b958c6efcb7438943.\r\n","mergeCommitSha":"96f8aba19b1c628f6432bf44aac72cccd11b054c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10775","title":"Revert \"Revert \"Move away from alb (#10772)\" (#10774)\"","createdAt":"2024-02-21T18:32:06Z"}
{"state":"Merged","mergedAt":"2024-02-21T23:12:43Z","number":10776,"mergeCommitSha":"18f1a1b9e19ee60856e5666a706a76614aed7914","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10776","title":"Move stuff around","createdAt":"2024-02-21T18:52:23Z"}
{"state":"Merged","mergedAt":"2024-02-21T21:05:35Z","number":10777,"body":"Also order by recent activity so if we hit the limit again we'll clip the _least_ recently active people.","mergeCommitSha":"6cf8462a5028af3ff71024b3e66c20dab96b32e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10777","title":"Increasing limit on people page to 1000 (was 500)","createdAt":"2024-02-21T20:40:38Z"}
{"state":"Merged","mergedAt":"2024-02-21T21:35:04Z","number":10778,"body":"Manually create user engagement on admin console.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f455ed90-29b4-4211-a719-13c79f05f3cd\r\n\r\n","mergeCommitSha":"995a5a4e18da56e983afb3ef7f71de7d4b6e9a98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10778","title":"Create user engagement on Admin","createdAt":"2024-02-21T21:04:37Z"}
{"state":"Merged","mergedAt":"2024-02-21T22:27:36Z","number":10779,"mergeCommitSha":"2f2ca93ee38ab9dd7bbac1bfaf68d5d98124144b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10779","title":"Add e5 mistral","createdAt":"2024-02-21T21:18:43Z"}
{"state":"Merged","mergedAt":"2022-04-29T18:14:17Z","number":1078,"mergeCommitSha":"936dc389871c3da36c1719b86ac9ba9a6def1fcf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1078","title":"Don't post messages to github if we cant deserialize","createdAt":"2022-04-29T17:42:52Z"}
{"state":"Merged","mergedAt":"2024-02-23T19:32:29Z","number":10780,"mergeCommitSha":"7324b3d27d8bf7b7e33729c20c9695f84dba3210","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10780","title":"Enable suggested peer email in Prod","createdAt":"2024-02-21T21:26:30Z"}
{"state":"Merged","mergedAt":"2024-02-21T21:48:24Z","number":10781,"mergeCommitSha":"408b3d9dcf1f2dd244f897d30472e0f3a14d9468","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10781","title":"Add summaries max size to prompt compiler","createdAt":"2024-02-21T21:34:37Z"}
{"state":"Merged","mergedAt":"2024-02-21T22:44:12Z","number":10782,"mergeCommitSha":"606d1300ccc4d2fec5fcbba1a5d2b6aa49340f69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10782","title":"Expand default GPT 3.5 prompt size to 16k (now supported)","createdAt":"2024-02-21T22:20:57Z"}
{"state":"Merged","mergedAt":"2024-02-21T22:25:08Z","number":10783,"mergeCommitSha":"c5aaf114b0a660b804b8b411461c681cc89ba65d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10783","title":"WRITE TO DYNMOB","createdAt":"2024-02-21T22:23:46Z"}
{"state":"Merged","mergedAt":"2024-02-21T22:43:02Z","number":10785,"mergeCommitSha":"af5e5480a5b52bc44c7ccfe11b35f6a4b5ca6367","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10785","title":"Fix people page (bad SQL)","createdAt":"2024-02-21T22:37:53Z"}
{"state":"Merged","mergedAt":"2024-02-22T04:42:40Z","number":10786,"mergeCommitSha":"4e0f3270f5be57ab4f284466e993f1b59c8bc371","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10786","title":"Refactor Pinecone config again","createdAt":"2024-02-21T22:50:30Z"}
{"state":"Merged","mergedAt":"2024-02-22T00:38:02Z","number":10787,"mergeCommitSha":"4d6e143b7435d342c63d1897b8e29f92cb12229c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10787","title":"Implement getTeamStats","createdAt":"2024-02-21T23:13:48Z"}
{"state":"Merged","mergedAt":"2024-02-21T23:26:09Z","number":10788,"mergeCommitSha":"64c821390354b1c835b239e4221866dfef5717bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10788","title":"[SKIP TESTS] - remove current teammember id function since it just confuses the model","createdAt":"2024-02-21T23:25:43Z"}
{"state":"Merged","mergedAt":"2024-02-21T23:27:40Z","number":10789,"body":"The arn we were using was incorrect for the new kube cluster deployment policies.","mergeCommitSha":"0dbf77feedeb4f4eda404b2504537e2bb85a66ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10789","title":"Fix dynamodb policies","createdAt":"2024-02-21T23:26:59Z"}
{"state":"Merged","mergedAt":"2022-04-29T18:37:38Z","number":1079,"mergeCommitSha":"37c56f5460f324717355b7e2744be8ca818d6db1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1079","title":"Better logging","createdAt":"2022-04-29T18:34:25Z"}
{"state":"Merged","mergedAt":"2024-02-21T23:50:13Z","number":10790,"mergeCommitSha":"38be0cee2eb2d8424aa5e9dacc485a63a8f8f6a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10790","title":"More stuff","createdAt":"2024-02-21T23:49:51Z"}
{"state":"Merged","mergedAt":"2024-02-22T00:06:06Z","number":10791,"mergeCommitSha":"bf0574f2c58650c24ce121be50991987717f1249","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10791","title":"Move away form alb","createdAt":"2024-02-21T23:52:41Z"}
{"state":"Merged","mergedAt":"2024-02-22T00:03:53Z","number":10792,"mergeCommitSha":"e3a7d5345360d78059976c228f85fb5cff701d5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10792","title":"Do not deploy","createdAt":"2024-02-22T00:03:45Z"}
{"state":"Merged","mergedAt":"2024-02-22T00:12:37Z","number":10793,"body":"Reverts NextChapterSoftware/unblocked#10788","mergeCommitSha":"a250d13348ae40a52564d83ab86651998a521364","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10793","title":"Revert \"[SKIP TESTS] - remove current teammember id function since it just confuses the model\"","createdAt":"2024-02-22T00:12:30Z"}
{"state":"Merged","mergedAt":"2024-02-22T01:13:27Z","number":10794,"mergeCommitSha":"5edb82bb9cad769d37a077f2a63ee8213221b656","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10794","title":"Allow nullable MLFunction parameters","createdAt":"2024-02-22T00:23:02Z"}
{"state":"Merged","mergedAt":"2024-02-22T02:10:21Z","number":10795,"body":"Motivation is to allow the client to create a GitLab/Bitbucket team from a Group/Workspace _before_ the user has selected repos.\n\nThis in turn allows us to remove the `listScmInstallationRepos` API and consolidate the GitHub and GitLab/Bitbucket onboarding flows.","mergeCommitSha":"41615ab69f268cbed50b8370e09430ca08801564","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10795","title":"connectScmInstallation no longer requires installRepositoriesIds","createdAt":"2024-02-22T00:30:16Z"}
{"state":"Merged","mergedAt":"2024-02-22T07:17:34Z","number":10796,"body":"When a new person is created + they have pending invites, notify the `customer-invites` channel\r\n\r\n![CleanShot 2024-02-21 at 16 30 09@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/f9ad0ada-9c59-45cc-a6b2-2fe4767924ee)\r\n","mergeCommitSha":"ce2e96baf152ee5290d876928afce797ee2ac6c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10796","title":"New User Slack notification with Invitation","createdAt":"2024-02-22T00:34:07Z"}
{"state":"Merged","mergedAt":"2024-02-22T00:55:59Z","number":10797,"mergeCommitSha":"c551e1e621c18f9a548e82a84621afce6f3abf6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10797","title":"Upgrade e5 mistral endpoint","createdAt":"2024-02-22T00:42:27Z"}
{"state":"Merged","mergedAt":"2024-02-22T04:17:34Z","number":10798,"mergeCommitSha":"a19a6f888c4ab8561e5aa219c5c6b3de9e61fd2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10798","title":"JetBrains plugin view logs","createdAt":"2024-02-22T01:01:23Z"}
{"state":"Merged","mergedAt":"2024-02-22T17:19:36Z","number":10799,"mergeCommitSha":"ef4a8ec573fdeae9258edd315adbd59fc2ecccc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10799","title":"Backfill TeamMemberModel.questionsAsked","createdAt":"2024-02-22T01:06:40Z"}
{"state":"Merged","mergedAt":"2022-01-24T21:58:09Z","number":108,"mergeCommitSha":"23ce9f3cc2c337a0e8cc2f9ac54ea538fcd725ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/108","title":"Add token refresh endpoint","createdAt":"2022-01-24T01:06:42Z"}
{"state":"Merged","mergedAt":"2022-05-06T03:17:21Z","number":1080,"mergeCommitSha":"91d60f295abc4bc33a9fb9d463dddfbe5c5aec7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1080","title":"Convert Message.proto to proto3","createdAt":"2022-04-29T18:54:30Z"}
{"state":"Merged","mergedAt":"2024-02-22T05:08:58Z","number":10800,"mergeCommitSha":"703611a9faf667df0fc63050e4468cc89ef6dce0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10800","title":"Introduce embedding team settings","createdAt":"2024-02-22T01:45:44Z"}
{"state":"Merged","mergedAt":"2024-02-22T02:24:16Z","number":10801,"mergeCommitSha":"64d5c55943fe9883813f3a694d7013896dd12b1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10801","title":"Remove old alb","createdAt":"2024-02-22T02:14:15Z"}
{"state":"Merged","mergedAt":"2024-02-22T03:27:55Z","number":10802,"mergeCommitSha":"5df6865c4fed14899dd8d9cb2ce5063d02f6828a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10802","title":"Remove hf api gateway","createdAt":"2024-02-22T03:25:29Z"}
{"state":"Merged","mergedAt":"2024-02-22T06:12:23Z","number":10803,"mergeCommitSha":"8e0ba5bbb81fe2556214249119f5fb91dfe4991a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10803","title":"Add logs to storage","createdAt":"2024-02-22T04:51:37Z"}
{"state":"Merged","mergedAt":"2024-02-22T05:46:13Z","number":10804,"mergeCommitSha":"a8047bab66da6fe3a446037159b9d7cf78827a80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10804","title":"Refresh team resources immediately after team is created by connectScmInstallation","createdAt":"2024-02-22T05:12:19Z"}
{"state":"Merged","mergedAt":"2024-02-22T19:14:25Z","number":10805,"mergeCommitSha":"126f4257baa947a9a3ec20e65b78cf37be151745","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10805","title":"Read-only user must not have access to connectScmInstallation and patchInstallation","createdAt":"2024-02-22T06:19:11Z"}
{"state":"Merged","mergedAt":"2024-02-22T06:52:19Z","number":10806,"body":"Motivation is to track migration to serverless","mergeCommitSha":"9e5df4f017233ef73f5ab8684ff621c0a22e74e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10806","title":"Introduce embedding/vectors page in admin","createdAt":"2024-02-22T06:42:45Z"}
{"state":"Merged","mergedAt":"2024-02-22T17:23:58Z","number":10807,"body":"We were sending the \"accepted\" invite slack notification even when there were no invites.\r\n\r\n\r\n![CleanShot 2024-02-22 at 09 01 26@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/c3cad540-51ae-43a5-af1b-724510c4a7df)\r\n","mergeCommitSha":"ac275e9a48b4cd8c35d75231974a2f6f0b3bc615","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10807","title":"Reduce slack notifications","createdAt":"2024-02-22T17:01:48Z"}
{"state":"Closed","mergedAt":null,"number":10808,"mergeCommitSha":"aefbfff4affc409920435e238260ded26fbfa614","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10808","title":"Add timestamps to TeamMemberInfo","createdAt":"2024-02-22T17:09:11Z"}
{"state":"Merged","mergedAt":"2024-02-22T18:15:07Z","number":10809,"body":"Error with using `IdentityDao.findById` outside a transaction\r\n\r\n```\r\nj.l.IllegalStateException: No transaction in context.\r\n\tat o.j.e.s.t.TransactionManager$Companion.current(TransactionApi.kt:199)\r\n\tat o.j.e.d.EntityClass.warmCache(EntityClass.kt:40)\r\n\tat o.j.e.d.EntityClass.testCache(EntityClass.kt:93)\r\n\tat o.j.e.d.EntityClass.findById(EntityClass.kt:58)\r\n\tat o.j.e.d.EntityClass.findById(EntityClass.kt:49)\r\n\tat c.n.n.e.q.h.SuggestedPeersInviteeEmailHandler.handle(SuggestedPeersInviteeEmailHandler.kt:37)\r\n\tat c.n.n.e.q.h.NotificationEventHandler.handle(NotificationEventHandler.kt:50)\r\n\t```","mergeCommitSha":"89ca25c76eec445cfe855dda40f3d36a1ff02852","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10809","title":"Fix suggested peer transaction","createdAt":"2024-02-22T17:22:59Z"}
{"state":"Merged","mergedAt":"2022-04-30T01:22:03Z","number":1081,"body":"* Fix web dashboard code block overflow, \r\n<img width=\"1203\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/166069418-48a8d54f-bb80-4897-bcb7-5377a03152cd.png\">\r\n<img width=\"1218\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/166074230-489f4a92-5892-42d1-af65-fa42227cefe2.png\">\r\n\r\n* Fix line height in message view component\r\n<img width=\"646\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/166069566-41efa975-f5b4-4e3d-9c07-2bd7557914ce.png\">\r\n<img width=\"640\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/166072077-6c50f3f9-57be-4bd8-a687-e913bbc4c00c.png\">\r\n\r\n* Add link to pr comment in message if it exists\r\n<img width=\"259\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/166071986-da82a439-28b6-4eac-af1a-66b35c720d6b.png\">\r\n\r\n","mergeCommitSha":"81fe93a06e548a720e148f46c57f30eb4ae70a2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1081","title":"More style fixes on clients","createdAt":"2022-04-29T21:34:15Z"}
{"state":"Merged","mergedAt":"2024-02-22T18:10:56Z","number":10810,"mergeCommitSha":"9d854005cf567f09a1f1356d85be6f0ec6ecf186","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10810","title":"Add latest version download endpoint","createdAt":"2024-02-22T17:48:01Z"}
{"state":"Merged","mergedAt":"2024-02-22T18:32:52Z","number":10811,"mergeCommitSha":"5c4e7d734f8c60927cf540023febc16251b49f40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10811","title":"Use explicit join for findByIdentityId","createdAt":"2024-02-22T18:02:40Z"}
{"state":"Merged","mergedAt":"2024-02-26T18:05:16Z","number":10812,"body":"Add the following \"Personal org\" UI, which displays before we display any of the existing team state UIs (uninstalled, missing repos, needs to select repos, etc):\r\n\r\n![CleanShot 2024-02-22 at 10 30 23@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/18c45fd4-77b7-475c-b8b1-819b476812c9)\r\n\r\nAs part of this, I made a fairly substantial change to the way onboarding works.  Previously, GH and BB/GL worked differently: GH would create a team as soon as we redirected back from GH, and the rest of onboarding would work in a team context.  BB/GL, though, would run through part of onboarding without a team context (using the installation ID), until repos were selected, at which point the team would be created.   This lead to a fair bit of complexity.\r\n\r\nThis PR makes BB/GL work the same as GH:\r\n\r\n* `ScmInstallationRepoOnboarding` (where we're redirected to from BB/GL) will now immediately create a team, with no repos selected.  This is the same behaviour as the GL redirect endpoint (`DashboardInstallRedirect`).  So we now have a team context to work in immediately.\r\n* After creating their team, both `ScmInstallationRepoOnboarding` and `DashboardInstallRedirect` go to a new `/new/team/<teamId>/pre-check` endpoint. This displays any error or config UIs that we need to display before going to the integrations UI.  Specifically, this will display the \"personal org\" UI, and any SCM installation errors (uninstalled, suspended, missing valid repos, repo needs selection).\r\n* Once this is complete, we go to the integrations UI\r\n","mergeCommitSha":"10c0c1ca29e874566612b8f7e41a45d40717ed4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10812","title":"Add \"Personal Org\" onboarding UI","createdAt":"2024-02-22T18:36:31Z"}
{"state":"Merged","mergedAt":"2024-02-22T18:57:41Z","number":10813,"mergeCommitSha":"141675e646079c307cd7e20b0826888a622da227","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10813","title":"Add insturctor xl","createdAt":"2024-02-22T18:41:23Z"}
{"state":"Merged","mergedAt":"2024-02-22T18:50:57Z","number":10814,"mergeCommitSha":"f6c30cbfcbd984e5421d673305b8c7064d3b1b35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10814","title":"[SKIP TESTS] - remove auth for download url","createdAt":"2024-02-22T18:50:43Z"}
{"state":"Merged","mergedAt":"2024-02-22T19:06:40Z","number":10815,"body":"We are seeing a lot of pod evictions on the new cluster. After investigating it turns out the issue is caused by the following error: \r\n`Pod was rejected: The node had condition: [DiskPressure]`\r\n\r\nLooking through the old cluster and eksctl default configuration, turns out EKS defaults to 20GB but eksctl (our old deployment method) defaults to 100GB. Hence why the old cluster did not have this issue.\r\n\r\nWe are setting the default to 100GB. This will cause a node rollover at the rate of 30% of capacity at a time. ","mergeCommitSha":"a62944502a8ec60ace6f8a210308bcda8357ae92","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10815","title":"Set root disk for kube nodes to 100GB (defaults to 20)","createdAt":"2024-02-22T19:05:55Z"}
{"state":"Merged","mergedAt":"2024-02-23T22:38:24Z","number":10816,"body":"<img width=\"1254\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/7acc61e7-26de-4ca9-9166-1feb429ec90f\">\r\n\r\nWIP: team members management view on the dashboard.\r\n* Missing some data still\r\n* Using the old getTeamMembers API\r\n* Search/sort isn't hooked up yet \r\n\r\nNote: There is no direct link to this UI but the route is live. ","mergeCommitSha":"f5ad987646651e8761e09a8d8d3c1d0e23786594","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10816","title":"Team members mgmt page (client)","createdAt":"2024-02-22T19:18:20Z"}
{"state":"Merged","mergedAt":"2024-02-22T19:58:50Z","number":10817,"mergeCommitSha":"6b47d912ee981a3c4d1d75899a4e2dc99d0b9da0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10817","title":"Wire up our new ML embedder endpoints for InstructorXL and E5Mistral","createdAt":"2024-02-22T19:21:19Z"}
{"state":"Merged","mergedAt":"2024-02-22T19:36:30Z","number":10818,"body":"Reverts NextChapterSoftware/unblocked#10815\r\n\r\nI need to rework the approach. \r\n\r\n```\r\nAWS::EKS::Nodegroup                   | dev-1/Nodegroupeks-blueprints-mng-ng (dev1NodegroupeksblueprintsmngngDF4971F6) Resource handler returned message: \"NodeGroup already exists with name eks-blueprints-mng and cluster name dev-1 (Service: Eks, Status Code: 409, Request ID: 59253019-a936-4489-adc2-235e47e5ba30)\" (RequestToken: 3b79f867-b39f-61d9-672c-e2bb8913d29e, HandlerErrorCode: AlreadyExists)\r\n[811](https://github.com/NextChapterSoftware/unblocked/actions/runs/8009702103/job/21879325038#step:10:812)\r\n ❌  dev-1 failed: Error: The stack named dev-1 failed to deploy: UPDATE_ROLLBACK_COMPLETE: Resource handler returned message: \"NodeGroup already exists with name eks-blueprints-mng and cluster name dev-1 (Service: Eks, Status Code: 409, Request ID: 59253019-a936-4489-adc2-235e47e5ba30)\" (RequestToken: 3b79f867-b39f-61d9-672c-e2bb8913d29e, HandlerErrorCode: AlreadyExists)\r\n[812](https://github.com/NextChapterSoftware/unblocked/actions/runs/8009702103/job/21879325038#step:10:813)\r\n    at FullCloudFormationDeployment.monitorDeployment (/home/<USER>/work/unblocked/unblocked/infrastructure/cdk/core/node_modules/aws-cdk/lib/index.js:421:10615)\r\n[813](https://github.com/NextChapterSoftware/unblocked/actions/runs/8009702103/job/21879325038#step:10:814)\r\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\r\n[814](https://github.com/NextChapterSoftware/unblocked/actions/runs/8009702103/job/21879325038#step:10:815)\r\n    at async Object.deployStack2 [as deployStack] (/home/<USER>/work/unblocked/unblocked/infrastructure/cdk/core/node_modules/aws-cdk/lib/index.js:424:182305)\r\n[815](https://github.com/NextChapterSoftware/unblocked/actions/runs/8009702103/job/21879325038#step:10:816)\r\n    at async /home/<USER>/work/unblocked/unblocked/infrastructure/cdk/core/node_modules/aws-cdk/lib/index.js:424:164665\r\n```","mergeCommitSha":"785463c9010bb949774f263cfa8423952b930ed9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10818","title":"Revert \"Set root disk for kube nodes to 100GB (defaults to 20)\"","createdAt":"2024-02-22T19:29:47Z"}
{"state":"Merged","mergedAt":"2024-02-22T19:46:19Z","number":10819,"body":"Adding ability to delete email events.\r\nUsed to test emails that are conditional of existing email events.","mergeCommitSha":"ac33cbb9a2002b788eb385d06dba2f2e5a37012a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10819","title":"Add ability to delete email events from admin","createdAt":"2024-02-22T19:35:55Z"}
{"state":"Merged","mergedAt":"2022-04-29T21:43:41Z","number":1082,"body":"Hashes were conflicting with GH.\r\n\r\nSwitch to query parameters which uses keys to prevent conflicts. Seems to work as expected. No reloads","mergeCommitSha":"263f1859aa002e9f72a80e0fa7ecc774191bb3dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1082","title":"Use query params instead of hash","createdAt":"2022-04-29T21:35:42Z"}
{"state":"Merged","mergedAt":"2024-02-23T00:36:39Z","number":10820,"mergeCommitSha":"00648c05b2fb1dcb0e2a5236dcb0fb5a05ac44e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10820","title":"New TeamMemberStore","createdAt":"2024-02-22T19:45:12Z"}
{"state":"Merged","mergedAt":"2024-02-22T20:49:07Z","number":10821,"mergeCommitSha":"f4be11c1aebf7a3132d7c25e0142686cd8dea902","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10821","title":"Include createdAccountAt, invitedAt, and lastActiveAt for TeamMember ","createdAt":"2024-02-22T19:57:42Z"}
{"state":"Merged","mergedAt":"2024-02-22T22:10:21Z","number":10822,"body":"<img width=\"1219\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/3c689832-2626-458b-a4a1-62dcc18b7860\">\r\n","mergeCommitSha":"40270ab0778b9347b1dae5d4d82e68d3bbb14dc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10822","title":"Configure embedding platform model in admin team page","createdAt":"2024-02-22T20:29:58Z"}
{"state":"Merged","mergedAt":"2024-02-22T22:47:27Z","number":10823,"mergeCommitSha":"565f2a38fc14821da0df0ceaa3a8e4e83c23cc96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10823","title":"Implement fetchTeamMembers","createdAt":"2024-02-22T20:58:17Z"}
{"state":"Merged","mergedAt":"2024-02-22T21:59:34Z","number":10824,"mergeCommitSha":"bebbf7773a2bf13f7333e8396e3da5f9934b0933","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10824","title":"TeamInviteeStore.getInvitedAts only cares about primary members","createdAt":"2024-02-22T21:34:59Z"}
{"state":"Merged","mergedAt":"2024-02-22T22:25:29Z","number":10825,"mergeCommitSha":"f12a2f60763162a32f674a90a1697a40166e210c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10825","title":"Add logs when sending suggested peers email","createdAt":"2024-02-22T22:01:54Z"}
{"state":"Merged","mergedAt":"2024-02-22T22:06:11Z","number":10826,"body":"Second attempt at https://github.com/NextChapterSoftware/unblocked/pull/10815\r\n\r\nWe need to change the node group name every time there are any changes other than tags or scale. ","mergeCommitSha":"79c08b5e2c6c0099f2c718bfbd3ab93e195ffcb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10826","title":"Replaced nodegroup in Dev","createdAt":"2024-02-22T22:05:54Z"}
{"state":"Merged","mergedAt":"2024-02-22T22:54:52Z","number":10827,"mergeCommitSha":"d18ba33f4870e20a8284b6f894fd654ed4f18ac8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10827","title":"Add lambda function to tag eks nodes with DrataExclude","createdAt":"2024-02-22T22:54:01Z"}
{"state":"Merged","mergedAt":"2024-02-23T04:30:34Z","number":10828,"mergeCommitSha":"fec232d25e2e039d75e5e0874e3505bccebc90f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10828","title":"Implement SCM commit diff API for MLFunctions","createdAt":"2024-02-22T22:57:08Z"}
{"state":"Merged","mergedAt":"2024-02-23T06:19:31Z","number":10829,"mergeCommitSha":"1223b90576a3085bdf84a8f3c41e8969c75987ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10829","title":"Update MLFunctions for commit compare","createdAt":"2024-02-22T22:57:11Z"}
{"state":"Merged","mergedAt":"2022-04-29T23:24:13Z","number":1083,"body":"<img width=\"1532\" alt=\"Screen Shot 2022-04-29 at 2 43 21 PM\" src=\"https://user-images.githubusercontent.com/2133518/166073564-6fbff53f-0917-4b66-83bb-fbe7d6ce90da.png\">\r\n\r\nI used a `TextDocumentContentProvider` to provide text content to an editor without needing to save it on disk.  I might change this to a webview, which would allow word-wrapping.","mergeCommitSha":"84259810d90f01410f439f33ab75b49de80a1dbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1083","title":"Show UI when sourcepoint can't be resolved","createdAt":"2022-04-29T21:51:14Z"}
{"state":"Merged","mergedAt":"2024-02-22T23:23:00Z","number":10830,"mergeCommitSha":"6158e4e9f88efbd6e10e87a30290aedee48ae80f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10830","title":"[SKIP TESTS] Always show user feedback in team stats for now","createdAt":"2024-02-22T23:08:11Z"}
{"state":"Merged","mergedAt":"2024-02-23T00:31:35Z","number":10831,"body":"Add extension methods for `mapStreamAsync` and `composeStream` so they can be used directly on the stream, instead of via `compose`.","mergeCommitSha":"9cd1ee678175ee7c5d88dfe4f4e33f9e11620085","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10831","title":"Stream extension methods","createdAt":"2024-02-22T23:23:03Z"}
{"state":"Merged","mergedAt":"2024-02-22T23:44:09Z","number":10832,"body":"Fix issue where displayName was empty and not null which caused issues in the email.","mergeCommitSha":"3e310a895fd82a66c66b43733821b8a18add0956","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10832","title":"Fixes display name","createdAt":"2024-02-22T23:30:39Z"}
{"state":"Merged","mergedAt":"2024-02-23T00:31:27Z","number":10833,"mergeCommitSha":"5fb3c1280d471c932cb5305080d5cfacb412484a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10833","title":"Return results for listTeamMembers","createdAt":"2024-02-22T23:46:49Z"}
{"state":"Merged","mergedAt":"2024-02-23T01:30:40Z","number":10834,"mergeCommitSha":"f2ee6587df8a51abd9dee7a40a57871ae55e7f96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10834","title":"Remove duplicate repos","createdAt":"2024-02-23T00:29:42Z"}
{"state":"Merged","mergedAt":"2024-02-23T04:19:36Z","number":10835,"mergeCommitSha":"4f5199ca707f22da4aaeb0fcbfee6e85e5981768","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10835","title":"Use DB unique constraint to prevent repo dups","createdAt":"2024-02-23T00:29:45Z"}
{"state":"Merged","mergedAt":"2024-02-23T17:08:43Z","number":10836,"body":"We were using teamMemberId in the eligibility check instead of the PersonID","mergeCommitSha":"c650c61bcd129bffc31375065b5750ec34eed20a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10836","title":"Fix issue with duplicate invitee emails","createdAt":"2024-02-23T00:44:34Z"}
{"state":"Merged","mergedAt":"2024-02-23T01:02:54Z","number":10837,"mergeCommitSha":"05c3b672655c23aa5b80af03990837089f485a06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10837","title":"Remove TeamMemberMigrator","createdAt":"2024-02-23T00:45:39Z"}
{"state":"Merged","mergedAt":"2024-02-23T01:55:40Z","number":10838,"mergeCommitSha":"2e84e30da28a1b792fa287b4b77e790ca8440a2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10838","title":"More to yi","createdAt":"2024-02-23T01:17:49Z"}
{"state":"Merged","mergedAt":"2024-02-23T01:55:01Z","number":10839,"mergeCommitSha":"43d1e5e22c5875eced7636792c04816b2e8c2c6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10839","title":"Do not persist PR and Thread content in embedding metadata","createdAt":"2024-02-23T01:31:17Z"}
{"state":"Merged","mergedAt":"2022-04-30T02:57:37Z","number":1084,"body":"This changes also prunes original sourcepoints that do not exist in\r\nthe current Git workspace, which will prevent points added \"after\"\r\nthe current revision from being considered for recalculation.\r\n\r\nRelated:\r\n- https://chapter2global.slack.com/archives/C036YH3QF7T/p1651265002423059\r\n- https://github.com/NextChapterSoftware/unblocked/pull/1043\r\n- https://github.com/NextChapterSoftware/unblocked/pull/1048#discussion_r860429652","mergeCommitSha":"40bd15f68db6a3edd4aa6f4c95797db33f310735","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1084","title":"Handle multiple original sourcepoints","createdAt":"2022-04-29T22:00:32Z"}
{"state":"Merged","mergedAt":"2024-02-23T02:34:04Z","number":10840,"mergeCommitSha":"d4bb91757c1cf34b523001969200fe7a0d821008","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10840","title":"Fix pr file writitng","createdAt":"2024-02-23T02:33:58Z"}
{"state":"Closed","mergedAt":null,"number":10841,"mergeCommitSha":"b63c8797f8a211de9d769f57b3ffd7e01cb7b0f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10841","title":"Chunk DB deletes because they are timing out","createdAt":"2024-02-23T03:38:09Z"}
{"state":"Merged","mergedAt":"2024-02-25T04:48:51Z","number":10842,"mergeCommitSha":"490495544c041161b1190b393831b5189bb3532d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10842","title":"Kotlin stack uses `writeEmbeddingModels` setting instead of hardcoded Pod InstructorLarge","createdAt":"2024-02-23T03:47:14Z"}
{"state":"Merged","mergedAt":"2024-02-23T19:28:00Z","number":10843,"body":"**Try Unblocked Bumper:**\r\n@matthewjamesadam I'm not sure why these aren't equal widths. Will need some assistance sorting that out.\r\n\r\n![CleanShot 2024-02-22 at 21 30 03@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/44e51782-fea1-4e66-950a-ecb4e13fe454)\r\n\r\n**Right Column Callout:**\r\n\r\n![CleanShot 2024-02-22 at 21 31 46@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/b1716fc7-3ef3-4ecf-9a4a-695fdaa1accc)\r\n\r\n**Modal Title & Description:**\r\n\r\n![CleanShot 2024-02-22 at 21 36 14@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/467286bf-5659-407a-b405-4da971402462)\r\n","mergeCommitSha":"41d75d38c7ab4d3bb681d8f7212973b25b9068cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10843","title":"Try Unblocked content updates","createdAt":"2024-02-23T05:34:00Z"}
{"state":"Merged","mergedAt":"2024-02-23T07:12:11Z","number":10844,"mergeCommitSha":"001ed11ae131bef50297e8959543b5a78e34b455","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10844","title":"[SKIP TESTS] - wrong url for github commit compare","createdAt":"2024-02-23T07:11:34Z"}
{"state":"Merged","mergedAt":"2024-02-23T07:41:31Z","number":10845,"mergeCommitSha":"b35cddfd72b1a5a3a530eace783efab8b5e153a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10845","title":"[SKIP TESTS] - more logging for commits API","createdAt":"2024-02-23T07:40:34Z"}
{"state":"Merged","mergedAt":"2024-02-23T08:17:14Z","number":10846,"mergeCommitSha":"b6c0354837ca7e2564536507134a75da8effbd94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10846","title":"[SKIP TESTS] - wrong serialization for compare response","createdAt":"2024-02-23T08:15:46Z"}
{"state":"Merged","mergedAt":"2024-02-25T06:02:53Z","number":10847,"mergeCommitSha":"a913e9014095a42713acff6b5e72cbbe7b6f1a66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10847","title":"Make embedding plaform model required in embedding events","createdAt":"2024-02-23T08:20:55Z"}
{"state":"Merged","mergedAt":"2024-02-23T16:06:28Z","number":10848,"mergeCommitSha":"ec595c9c050c9b892a600d8d97f4526e5228bce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10848","title":"[SKIP TESTS] - fix git log formatting","createdAt":"2024-02-23T16:06:20Z"}
{"state":"Merged","mergedAt":"2024-02-23T17:10:42Z","number":10849,"mergeCommitSha":"fcb7067d590f101eb882d44a704f8eb0fefcd0d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10849","title":"[SKIP TESTS] - temporarily disable summary constraints","createdAt":"2024-02-23T17:09:57Z"}
{"state":"Merged","mergedAt":"2022-04-30T02:57:59Z","number":1085,"mergeCommitSha":"82543087e12c7391fd9e0b02b1e987061a68436e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1085","title":"Link to unread pages","createdAt":"2022-04-29T22:22:41Z"}
{"state":"Open","mergedAt":null,"number":10850,"body":"We have some situations in emails where we want to redirect users directly to a page. Unfortunately, these emails are \"generic\" and have no notion of a team yet.\r\n\r\nIntroduce a default / redirect team ID that will trigger a redirect to the desired path using the cached or first team available. \r\nIf neither exists, continue on default path and redirect to `/connect` as there are no teams available.\r\n\r\nFor example `/team/redirectteam/team-settings/jira` will redirect to `/team/d896b8d6-e917-41ca-bd2c-f8836b12a35a/team-settings/jira`\r\n\r\nThis can cause some issues if used improperly. aka `/team/redirectthread/thread/ee5903d9-3e96-4777-ba8a-e98b2f98337e`. In this case, the team selected may not match the thread's team.\r\n\r\n","mergeCommitSha":"9cd4b7819a72151b37c301308e236db228e79fb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10850","title":"Add redirecting mechanism to TeamContext","createdAt":"2024-02-23T17:23:41Z"}
{"state":"Merged","mergedAt":"2024-02-23T20:00:55Z","number":10851,"mergeCommitSha":"c69c77f9821e101c768ccdced3e6193c8756610f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10851","title":"Propagate marketing version correctly","createdAt":"2024-02-23T19:16:42Z"}
{"state":"Merged","mergedAt":"2024-02-23T22:25:58Z","number":10852,"mergeCommitSha":"4af9030a860d2864eed385919e72bfa2d3e53200","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10852","title":"Add TeamStatsFeedback.neutral field","createdAt":"2024-02-23T19:34:17Z"}
{"state":"Merged","mergedAt":"2024-02-23T23:56:01Z","number":10853,"mergeCommitSha":"9da97312b5766579e3f05f1549eaf8bb65e85ce4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10853","title":"Delete EKSCTL stuff in Dev and disale CI/CD for it","createdAt":"2024-02-23T19:34:37Z"}
{"state":"Open","mergedAt":null,"number":10854,"mergeCommitSha":"e11f52800612359d33b2dc8d791d70d589ea3b51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10854","title":"Use new team member store for invitee","createdAt":"2024-02-23T19:45:21Z"}
{"state":"Merged","mergedAt":"2024-02-23T21:36:52Z","number":10855,"mergeCommitSha":"9e44abcda8322c83d2915176c28ac6c36b2fb018","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10855","title":"[RFC] Embedding Document Key schema changes","createdAt":"2024-02-23T19:49:12Z"}
{"state":"Merged","mergedAt":"2024-02-23T20:10:52Z","number":10856,"mergeCommitSha":"35609ac7be8ea4b42d861cb9afbdd932d3eacc58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10856","title":"Disable EKS related changes in Dev","createdAt":"2024-02-23T20:10:03Z"}
{"state":"Merged","mergedAt":"2024-02-23T21:22:20Z","number":10857,"mergeCommitSha":"655f2f66263d75361d5b481a7fa067ab395dee0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10857","title":"Remove dead code","createdAt":"2024-02-23T21:15:48Z"}
{"state":"Merged","mergedAt":"2024-02-23T21:57:37Z","number":10858,"mergeCommitSha":"3f93cedcc7524c7cdc7a910e193075a963cc7d58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10858","title":"Fix main","createdAt":"2024-02-23T21:46:22Z"}
{"state":"Open","mergedAt":null,"number":10859,"body":"This is to ensure that @-mentions from slack are tracked on the primary member instead of the slack member","mergeCommitSha":"b981eab9fc7e75781f4f3c6c5bc8e33ea5a17089","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10859","title":"Only increment for primary members","createdAt":"2024-02-23T21:49:07Z"}
{"state":"Merged","mergedAt":"2022-04-29T23:32:28Z","number":1086,"mergeCommitSha":"4d8078fa030f58d61a08f10490561d97f6fd1819","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1086","title":"Updated slack notify","createdAt":"2022-04-29T22:26:18Z"}
{"state":"Merged","mergedAt":"2024-02-23T22:11:05Z","number":10860,"mergeCommitSha":"4d128e44be453136fa42b4f0a639ae52f92cc12a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10860","title":"Clean up TeamMember.questionsAsked","createdAt":"2024-02-23T22:02:13Z"}
{"state":"Merged","mergedAt":"2024-02-23T23:46:24Z","number":10861,"mergeCommitSha":"561b35364bfbe20021df2872c0b7f268c95a316e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10861","title":"Fix eks network stack and add prod eks vpc","createdAt":"2024-02-23T22:16:10Z"}
{"state":"Merged","mergedAt":"2024-02-24T01:03:59Z","number":10862,"mergeCommitSha":"f08a7f6c4688e368f345d8918d82f0e80c3c7caa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10862","title":"Show question feedback on TeamPage","createdAt":"2024-02-23T23:10:59Z"}
{"state":"Open","mergedAt":null,"number":10863,"body":"Move stream operations and import in a generic place.","mergeCommitSha":"e3127d070917acfde279f0a67a2cc2595da1c123","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10863","title":"Migrate stream operations","createdAt":"2024-02-23T23:17:38Z"}
{"state":"Merged","mergedAt":"2024-02-26T16:47:29Z","number":10864,"mergeCommitSha":"5682a70722571acc1e054d01b059923754683af6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10864","title":"properly format links for commits","createdAt":"2024-02-23T23:51:30Z"}
{"state":"Merged","mergedAt":"2024-02-23T23:59:52Z","number":10865,"mergeCommitSha":"57bfeff282bb118d8adc620dbc6c14acb84b4edb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10865","title":"forgot to remove dependency on deleted job","createdAt":"2024-02-23T23:59:46Z"}
{"state":"Merged","mergedAt":"2024-02-24T01:05:11Z","number":10866,"body":"<img width=\"797\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/********/f11d073f-8801-43cd-97e9-fec92057cbb1\">\r\n\r\n* Include `userFeedback.neutral` in feedback counts\r\n* Update styling for new mocks\r\n","mergeCommitSha":"73170df3d3c5de705281191435c914403781d00c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10866","title":"Refine team stats UI","createdAt":"2024-02-24T00:07:01Z"}
{"state":"Open","mergedAt":null,"number":10867,"body":"Setup Feedback Invite.\r\n\r\nWhenever someone provides a \"great\" feedback, we prompt users to invite.\r\n\r\nIncludes some work to make \"notify\" a generic ClientWorkspace mechanism.\r\n\r\n\r\n![CleanShot 2024-02-23 at 16 12 51@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/cad93641-44f4-47a8-ba5c-b118c27edbea)\r\n![CleanShot 2024-02-23 at 16 12 23@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/f284a05c-1906-41d7-a14d-15c6b1a49f09)\r\n![CleanShot 2024-02-23 at 16 02 37@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/efd6254e-e403-466e-a13a-086758fdc6b4)\r\n","mergeCommitSha":"81bc680165bbe4a6d5c068ba1d6de82615ced154","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10867","title":"Feedback invite","createdAt":"2024-02-24T00:14:56Z"}
{"state":"Merged","mergedAt":"2024-02-24T00:56:08Z","number":10868,"mergeCommitSha":"a980f6cbdba408e4827936ad81c85cf457b8f7ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10868","title":"Fix listTeamMembers returning duplicate team members","createdAt":"2024-02-24T00:18:44Z"}
{"state":"Open","mergedAt":null,"number":10869,"mergeCommitSha":"38b7f8d278f3f35c6d711d38df5d535e44935931","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10869","title":"Introduce repo revisions to mitigate repo ingestion racing against repo deletion","createdAt":"2024-02-24T00:45:43Z"}
{"state":"Merged","mergedAt":"2024-02-24T01:17:19Z","number":10870,"mergeCommitSha":"b1d4ef8ef1573bd6c5ccce02afc331059c213f18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10870","title":"Remove dead albs","createdAt":"2024-02-24T01:12:45Z"}
{"state":"Merged","mergedAt":"2024-02-24T03:20:32Z","number":10871,"mergeCommitSha":"68bde9c43cffd574315594a980c24d3bd54d650b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10871","title":"Add pending vector deletes to Vector page, and install Vector page on Team route","createdAt":"2024-02-24T01:46:49Z"}
{"state":"Merged","mergedAt":"2024-02-24T23:44:44Z","number":10872,"body":"- Added configuration for new prod cluster\r\n- Removed refinery deployer user access. All refinery changes are going to be deployed via CDK so we don't need this anymore. \r\n- Added dependency on EksNetworkStack to EksClustersStack\r\n\r\nCluster has been deployed. I have tested various functions and they all work as expected. ","mergeCommitSha":"3b8327988f38a7960c7ec14029f0784d6704f726","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10872","title":"Deployed new prod eks cluster","createdAt":"2024-02-24T23:43:49Z"}
{"state":"Merged","mergedAt":"2024-02-25T07:00:08Z","number":10873,"mergeCommitSha":"e95d362bdd4fd920bb9735f227fb60a0c53891d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10873","title":"Use idiomatic `requireNotNull()` pattern to reduce boilerplate","createdAt":"2024-02-25T05:56:43Z"}
{"state":"Merged","mergedAt":"2024-02-25T07:50:12Z","number":10874,"body":"I have already added the following to prep for this change\r\n- KUBE_API_URL_US_WEST_2_NEW env var for `production` GH action deployment environment\r\n- DEPLOY_KUBECONFIG_US_WEST_2_NEW secret for `production` GH action deployment environment\r\n\r\nThis will trigger deployment of all secrets and ","mergeCommitSha":"aeb5874dcc664db88f75181949992c7ac2bba750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10874","title":"[SKIP TESTS] Enable service deploy prod new eks","createdAt":"2024-02-25T07:38:49Z"}
{"state":"Merged","mergedAt":"2024-02-25T08:25:35Z","number":10875,"mergeCommitSha":"21a26e750c14fab902406295d263d29e3355c477","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10875","title":"Add new EKS cidr to RDS, Redis and ActiveMQ","createdAt":"2024-02-25T08:25:22Z"}
{"state":"Merged","mergedAt":"2024-02-25T11:25:22Z","number":10876,"body":"Disable deploys to old prod eks cluster. I have manually installed all service helm charts!","mergeCommitSha":"88c1cc25fa6fc37aee544fa34216948d03d2e807","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10876","title":"[SKIP TESTS] Disable deploys to old cluster","createdAt":"2024-02-25T11:14:06Z"}
{"state":"Merged","mergedAt":"2024-02-25T20:01:37Z","number":10877,"body":"I'll remove the old cluster's NAT addresses in a separate PR. I am keeping that cluster around for another 48 hours just in case we need to do a revert!","mergeCommitSha":"8b1b1f4ab828e05cf37890b56cae81f518e3a61d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10877","title":"Add CIDRs of new EKS clusters to configs","createdAt":"2024-02-25T12:03:51Z"}
{"state":"Merged","mergedAt":"2024-02-25T21:30:12Z","number":10878,"mergeCommitSha":"fd0ac073510beecf6951c105df34b2d558ff7129","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10878","title":"Use EmbeddingDocumentKey for embedding generation in KT","createdAt":"2024-02-25T21:01:16Z"}
{"state":"Merged","mergedAt":"2024-02-25T22:15:32Z","number":10879,"body":"Staged cleaned of message schema change in #10878\r\n\r\nDo not merge until #10878 has been deployed to all environments, and the `*embedding_events` queues have been drained.","mergeCommitSha":"395b37e2b57d2595b53d143d848a920776006d9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10879","title":"Make UpsertEmbeddingEvent event embeddingDocumentId required","createdAt":"2024-02-25T21:05:23Z"}
{"state":"Merged","mergedAt":"2022-04-30T00:17:10Z","number":1088,"body":"Slack notify failing due to invalid JSON from commit message.\r\n\r\nTemporarily removing to unblock.","mergeCommitSha":"534f81b3f9b036030d5502fdc7cae41a686a686e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1088","title":"Fix slack notify","createdAt":"2022-04-29T23:52:53Z"}
{"state":"Merged","mergedAt":"2024-02-25T22:51:42Z","number":10880,"mergeCommitSha":"fb5d37e6ae41439e29bbea4d1d4adbe56de5e1c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10880","title":"GitHub commit author is optional","createdAt":"2024-02-25T22:28:56Z"}
{"state":"Merged","mergedAt":"2024-02-25T23:30:33Z","number":10881,"mergeCommitSha":"f37a8f7159f6912c7fc34571f604209bf147c497","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10881","title":"Committer is an optional field","createdAt":"2024-02-25T23:04:36Z"}
{"state":"Merged","mergedAt":"2024-02-25T23:34:16Z","number":10882,"body":"Service account permission stuff are moved to CDK. I am documenting them. ","mergeCommitSha":"f085ba23cdbe09b13d12a982cf05d61aae093146","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10882","title":"Add step function permissions to scm service","createdAt":"2024-02-25T23:31:56Z"}
{"state":"Merged","mergedAt":"2024-02-26T00:16:18Z","number":10883,"mergeCommitSha":"90c72e3e86559b13fbc08f1f6df5f895eb0df2f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10883","title":"Fix EmbeddingDocumentId serialization","createdAt":"2024-02-26T00:01:41Z"}
{"state":"Merged","mergedAt":"2024-02-26T00:34:43Z","number":10884,"body":"- Fix for admin web prod S3 permissions\r\n- Fix for Dev refinery","mergeCommitSha":"776b3240839302f60d5d74399acecc84c370f82c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10884","title":"Fix adminweb s3 policy and refinery deployment","createdAt":"2024-02-26T00:33:57Z"}
{"state":"Merged","mergedAt":"2024-02-26T01:25:48Z","number":10885,"body":"Int max size is 2^31, wich is now regularly exceeded for GitHub IDs.\n\nSee failures started 4 days ago:\nhttps://app.logz.io/#/goto/6e8cf179aeaf64fe9610c86bfde1e5f9?switchToAccountId=411850","mergeCommitSha":"3272b91dadd37eba0330c6cfbc4d5aae9d1b4b12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10885","title":"Use Long to decode GitHub IDs because they have passed 2 billion","createdAt":"2024-02-26T01:01:10Z"}
{"state":"Merged","mergedAt":"2024-02-26T16:03:15Z","number":10886,"mergeCommitSha":"c9b206d48bb8a9ede33a3520cd4d1697aa8f2e42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10886","title":"Unblocked TravelPerk","createdAt":"2024-02-26T13:56:49Z"}
{"state":"Open","mergedAt":null,"number":10887,"mergeCommitSha":"6620d7526dff2090707170299f336a14f3b5a8aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10887","title":"Implement Serverless Incremental Partition Tracking","createdAt":"2024-02-26T16:54:09Z"}
{"state":"Merged","mergedAt":"2024-02-26T18:09:40Z","number":10888,"mergeCommitSha":"6d32f5e877f94aedc0819651e96d1ff502182845","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10888","title":"Fix documentation groupID required assertion during retrieval","createdAt":"2024-02-26T17:01:01Z"}
{"state":"Merged","mergedAt":"2024-02-26T21:42:16Z","number":10889,"body":"A simple store that produces a stream of TeamMembers, based on a search criteria.  Pagination coming next PR.","mergeCommitSha":"4f2e0be1586a69822ebc25293edeb989cd2823dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10889","title":"Add TeamMemberListStore","createdAt":"2024-02-26T17:27:08Z"}
{"state":"Merged","mergedAt":"2022-05-01T23:26:32Z","number":1089,"body":"`FormattedText` inline elements from our clients can include leading or trailing whitespaces. When we convert to markdown, this can cause issues. For example:\r\n\r\n```\r\n**_This is my text _**\r\n```\r\nrenders to\r\n\r\n**_This is my text _**\r\n\r\ninstead of\r\n\r\n**_This is my text_** \r\n\r\nbecause of the the whitespace before the closing `_`.\r\n\r\nThis PR strips the whitespace before applying the markdown formatting then adds it back.","mergeCommitSha":"cc9a9558a0ea3e44c94577e9ce34408e273bcf7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1089","title":"Apply markdown to trimmed text only","createdAt":"2022-04-30T00:17:06Z"}
{"state":"Merged","mergedAt":"2024-02-26T18:15:56Z","number":10890,"mergeCommitSha":"1ae90b651417cc5192792e209871dd2c65d880b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10890","title":"[SKIP TESTS] - add ML function for authors for file","createdAt":"2024-02-26T18:14:02Z"}
{"state":"Merged","mergedAt":"2024-02-26T18:58:15Z","number":10891,"mergeCommitSha":"23d195fe971b66d2859489df01adba9882f327bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10891","title":"Don't include Unbot in \"Not Yet Joined\" count","createdAt":"2024-02-26T18:30:22Z"}
{"state":"Closed","mergedAt":null,"number":10892,"body":"So that the team management page can exclude Unbot from the list, but we still want to allow fetching bot users.","mergeCommitSha":"53e3d210bfcf5c1f07671ac6e21588e39f93d9bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10892","title":"[RFC] Allow excluding bots from listTeamMembers","createdAt":"2024-02-26T19:07:28Z"}
{"state":"Merged","mergedAt":"2024-02-26T22:07:57Z","number":10893,"mergeCommitSha":"0e4d3318f769f44fc23820b5288523c5b7baa203","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10893","title":"Temporal commit queries to the SCM","createdAt":"2024-02-26T19:27:50Z"}
{"state":"Merged","mergedAt":"2024-02-26T20:23:30Z","number":10894,"mergeCommitSha":"06899e492bb344517054f55a1eaa47a0c8cc1ee8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10894","title":"Exclude bot members from listTeamMembers","createdAt":"2024-02-26T19:40:12Z"}
{"state":"Merged","mergedAt":"2024-02-26T23:28:38Z","number":10895,"body":"addresses issue introduced in #10888","mergeCommitSha":"da87cdc981c225ad5f0e4607e9a8c60c66f5bd77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10895","title":"Bring back Documentation read-repair for Notion/StackOverflow in serverless","createdAt":"2024-02-26T19:51:59Z"}
{"state":"Merged","mergedAt":"2024-02-26T23:09:32Z","number":10896,"mergeCommitSha":"53008af0d4ea201efc16dc11dbc70c5cc1764232","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10896","title":"Temporal commit query for MLFunctions","createdAt":"2024-02-26T20:04:30Z"}
{"state":"Merged","mergedAt":"2024-02-26T20:48:15Z","number":10897,"mergeCommitSha":"4404e5c951291c7c5271618b3ca4ed88dd062b4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10897","title":"[SKIP TESTS] - small tweaks to file author prompt","createdAt":"2024-02-26T20:24:51Z"}
{"state":"Merged","mergedAt":"2024-02-26T20:35:38Z","number":10898,"body":"I'll be deleting all the other EKS docs and procedures in my next PR as part of cluster de-provisioning","mergeCommitSha":"f3c57e32b47449c865823003d07dbb20c02afcaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10898","title":"Update eks docs","createdAt":"2024-02-26T20:34:51Z"}
{"state":"Merged","mergedAt":"2024-02-26T21:22:43Z","number":10899,"mergeCommitSha":"43efa2c6f3383acc03943a05713cf73919ba35b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10899","title":"[SKIP TESTS] - another prompt tweak for authors","createdAt":"2024-02-26T21:22:19Z"}
{"state":"Merged","mergedAt":"2022-01-24T17:23:10Z","number":109,"body":"- Fixed incorrect environment name in Dev json \r\n- Grouped a all AWS related params (account ID, root region etc)\r\n- Changed the coreVPC build-config to include VPC peering targets instead of having the hardcoded \r\n- Defined a simplified interface for config expected by network-stack. This way we can call it with both build-config object types (regular and secOps types)\r\n- Cleanup unused configs \r\n- Context json was updated as a result of changing some code around VPC imports but no functionality has been changed. \r\n\r\nMade sure the new changes are compatible with Dev (no changes) and also applied the coreVPC (network-stack) change to SecOps (prep for OpenVPN work)","mergeCommitSha":"a3b8c35da0cb70e9854287a6d999dd0d27a40ccb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/109","title":"cleanup build-config to make network stack more generic","createdAt":"2022-01-24T05:57:51Z"}
{"state":"Merged","mergedAt":"2022-04-30T17:45:47Z","number":1090,"mergeCommitSha":"e538cdfd2ace36f3238ac1da7678dc9136e5444c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1090","title":"Add sourcemarks page","createdAt":"2022-04-30T17:27:42Z"}
{"state":"Merged","mergedAt":"2024-02-26T21:58:15Z","number":10900,"mergeCommitSha":"7f4196c42e1f97f10db2014709a51028c76c0e45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10900","title":"Update team stats table on TeamPage","createdAt":"2024-02-26T21:44:07Z"}
{"state":"Merged","mergedAt":"2024-02-26T22:13:10Z","number":10901,"body":"- Deleted eksctl config yamls\r\n- Deleted controller configs with global network policies (all part of cdk code now)\r\n- Deleted old ci workflows used to deploy services to old prod cluster\r\n- Deleted old refinery config (also part of CDK now)\r\n- Disabled all stacks (alb, eks-vpn etc) in cdk code which were used for the old cluster\r\n- Deleted old smoke tests directory. We haven't used them in over a yead. I don't see us going back to them","mergeCommitSha":"bed6d7e255f1cd778a9fc4ab6ba19a9040839b72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10901","title":"Deleting old prod EKS cluster and all associated code","createdAt":"2024-02-26T21:56:43Z"}
{"state":"Open","mergedAt":null,"number":10902,"body":"To enable Google, since our app is now verified ❤️. We'll also need to remove the logic from client code, @kaych you can push to this branch if that's easier than opening a new PR.","mergeCommitSha":"9f292e67a488b78c5ca833802e9c02fbe59f7470","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10902","title":"Enable Google in PROD","createdAt":"2024-02-26T22:05:45Z"}
{"state":"Merged","mergedAt":"2024-02-26T22:38:54Z","number":10903,"body":"- Removed CIDRs of old prod cluster\r\n- Removed old us-east-2 CIDRS. DR region is inactive and those IPs have been reclaimed by AWS.","mergeCommitSha":"52f41490b2bdf31ff407f36726cea099ac1508c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10903","title":"Remove old NAT CIDRS","createdAt":"2024-02-26T22:11:54Z"}
{"state":"Merged","mergedAt":"2024-02-26T22:35:54Z","number":10904,"body":"This is a no-op, just factors the body content into a separate component.  I'm doing this so that we can use the store at the same point we can manage scrolling and load-more, in my next PR.","mergeCommitSha":"cd86002b652d3bcc6789c6801eae52e9e052087f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10904","title":"Factor the body out of the Chats component","createdAt":"2024-02-26T22:24:04Z"}
{"state":"Merged","mergedAt":"2024-02-26T22:37:23Z","number":10905,"mergeCommitSha":"c6e6348c2a677ea2a6608e46a17b0e97f12e8a41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10905","title":"Add octoai","createdAt":"2024-02-26T22:37:00Z"}
{"state":"Merged","mergedAt":"2024-02-26T23:58:43Z","number":10906,"body":"This is just renaming for now.\r\n\r\nI have a plan to clean up the semantic search retrieval path without unnecessary transformations, but going to defer for a bit.\r\nhttps://gist.github.com/richiebres/907d10b4d10829d5236476c9acdfa207","mergeCommitSha":"0f77ace754c5b97b76e5c6f6b987c479be4327cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10906","title":"Refactor SearchResultsFactory","createdAt":"2024-02-26T22:51:01Z"}
{"state":"Merged","mergedAt":"2024-02-26T23:17:56Z","number":10907,"body":"- Deleted CDK stacks that are no longer needed\r\n- Fix a workflow name","mergeCommitSha":"ed42f1de7b3aa653bed93e67912a00c0dcbdab0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10907","title":"Remove EKS related stacks that are no longer needed","createdAt":"2024-02-26T23:16:45Z"}
{"state":"Merged","mergedAt":"2024-02-26T23:33:44Z","number":10908,"mergeCommitSha":"d59960b9f6736a15fb8a937b04c4a45985352703","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10908","title":"Fix cdk deploys (More EKS cleanup)","createdAt":"2024-02-26T23:33:29Z"}
{"state":"Open","mergedAt":null,"number":10909,"body":"Processing Complete email now handled by sendgrid.\r\n\r\nAlso slight updates to handle dynamic subjects.\r\n\r\n![CleanShot 2024-02-26 at 15 45 04@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/3d097fca-3929-4b08-b196-fe4dc43e3e81)\r\n","mergeCommitSha":"9aac42845f02719f51c3ef4e17e86492ac6e9f34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10909","title":"Move ProcessingCompletEmail to sendgrid","createdAt":"2024-02-26T23:54:12Z"}
{"state":"Merged","mergedAt":"2022-05-02T18:37:46Z","number":1091,"body":"Moves it out of the API service to make creating, updating, and deleting messages snappier.","mergeCommitSha":"331a16096941125783bafab1b315eaa4e215cfe5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1091","title":"Post messages to GitHub through the SCM service","createdAt":"2022-05-01T04:30:34Z"}
{"state":"Merged","mergedAt":"2024-02-27T00:25:15Z","number":10910,"body":"Both environments are on new clusters. There's no need for these controls anymore","mergeCommitSha":"b7bf634e32774a4e914f52b5736d860b74e91962","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10910","title":"[SKIP TESTS] Remove more traces of the old clusters","createdAt":"2024-02-27T00:07:28Z"}
{"state":"Merged","mergedAt":"2024-02-27T00:14:56Z","number":10911,"mergeCommitSha":"c15052a8982fdf1676b9ccd791776bc4ffb79c16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10911","title":"[SKIP TESTS] - small tweak for 'changes today'","createdAt":"2024-02-27T00:14:29Z"}
{"state":"Merged","mergedAt":"2024-02-27T00:22:18Z","number":10912,"mergeCommitSha":"71c4a1833fa4cc0dfce7fc190b79350d51e9d670","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10912","title":"[SKIP TESTS] - small prompt tweak for commit urls","createdAt":"2024-02-27T00:22:04Z"}
{"state":"Merged","mergedAt":"2024-02-27T00:41:44Z","number":10913,"body":"* Add invite toast\r\n* Update search input\r\n* Update tooltip text\r\n* Update default sort\r\n* Add view for empty search results","mergeCommitSha":"ba0fc902b492ce61d098be9da432f791d2f14f04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10913","title":"Team page UI followup","createdAt":"2024-02-27T00:23:56Z"}
{"state":"Merged","mergedAt":"2024-02-27T00:25:27Z","number":10914,"mergeCommitSha":"e456f5d708ab096dc6d86312b34370a977e6103c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10914","title":"[SKIP TESTS] - fix grammatical error","createdAt":"2024-02-27T00:25:18Z"}
{"state":"Merged","mergedAt":"2024-02-27T00:57:52Z","number":10915,"body":"1. Optimize inference by not doing summaries chain if not needed.\r\n2. Fix up completion event.\r\n","mergeCommitSha":"7bc2c23b0874402c5540ee2233ce4463d2f0c9cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10915","title":"Fix Pull Request Summaries Inference","createdAt":"2024-02-27T00:43:15Z"}
{"state":"Merged","mergedAt":"2024-02-27T01:01:11Z","number":10916,"body":"Get pull request diff\n\nSpecial instruction for scm url parsing","mergeCommitSha":"f731c79296b71afa69da6c8aebc21e36808dacf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10916","title":"Get pull request diff","createdAt":"2024-02-27T00:45:54Z"}
{"state":"Open","mergedAt":null,"number":10917,"mergeCommitSha":"2f9c2e9c21615efec5627d20d29b1a55474d0f88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10917","title":"Hide user feedback if threshold not met","createdAt":"2024-02-27T01:00:56Z"}
{"state":"Merged","mergedAt":"2024-02-27T01:55:00Z","number":10918,"mergeCommitSha":"4abfd7d05c78e7953b7911b0a245df746c4dd574","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10918","title":"[SKIP TESTS] - another crack at fixing commit links","createdAt":"2024-02-27T01:09:51Z"}
{"state":"Merged","mergedAt":"2024-02-27T01:45:17Z","number":10919,"mergeCommitSha":"b8c25a3cdee3e90f89e88747bfa1fa18862135b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10919","title":"[SKIP TEST] Fix summarization endpoint","createdAt":"2024-02-27T01:44:01Z"}
{"state":"Merged","mergedAt":"2022-05-01T19:09:33Z","number":1092,"mergeCommitSha":"b39af1221ae432d78aaf312db4efa16b911b331c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1092","title":"Admin web searches more things","createdAt":"2022-05-01T04:44:59Z"}
{"state":"Merged","mergedAt":"2024-02-27T01:49:00Z","number":10920,"mergeCommitSha":"545d9bb25535347323a06f4a891d1c8607481753","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10920","title":"Fix transformers endpoint","createdAt":"2024-02-27T01:48:54Z"}
{"state":"Open","mergedAt":null,"number":10921,"body":"Most of the work here has nothing to do with the team membership UI --  most of this is refactoring the thread infinite scroll so the same behaviour can be reused:\r\n\r\n* `InfiniteScrollSource` is an interface that stores can implement to be a data source for infinite scrolling UIs\r\n* `useInfiniteScroll` is a hook that loads data from an `InfiniteScrollSource` in the current `ScrollContext`, to give infinite scrolling behaviour","mergeCommitSha":"5e7b5d0d552222252b30b6640f3084c3bafa8cad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10921","title":"Infinite scroll in team member table","createdAt":"2024-02-27T01:49:50Z"}
{"state":"Merged","mergedAt":"2024-02-27T02:02:09Z","number":10922,"mergeCommitSha":"9ed09f0fc48d38c242d768fd6d181c0194410ae4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10922","title":"prompt tweak to hopefully fix function chaining for repo resolution","createdAt":"2024-02-27T02:01:28Z"}
{"state":"Open","mergedAt":null,"number":10923,"mergeCommitSha":"540a563e1903a83e398c83f9235dc83b295e0582","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10923","title":"Delete dead code","createdAt":"2024-02-27T02:04:13Z"}
{"state":"Merged","mergedAt":"2024-02-27T02:22:23Z","number":10924,"mergeCommitSha":"d24f1fd2f291e9a959bc1ec86f15979923e9ddc7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10924","title":"Fix icon in grid","createdAt":"2024-02-27T02:10:25Z"}
{"state":"Merged","mergedAt":"2024-02-27T02:29:18Z","number":10925,"mergeCommitSha":"691509a0afb70c000484928cdb9e70e2188f9e21","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10925","title":"Disable verbose","createdAt":"2024-02-27T02:29:10Z"}
{"state":"Merged","mergedAt":"2024-02-27T02:39:40Z","number":10926,"mergeCommitSha":"54c573f5d566e5e113f7cb30ec7a9597a116f4b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10926","title":"Add tqdm descriptions","createdAt":"2024-02-27T02:39:22Z"}
{"state":"Merged","mergedAt":"2024-02-27T04:11:26Z","number":10927,"mergeCommitSha":"1aef0223a9ee7e7948f6dfe1b778585545b3ecca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10927","title":"Increase inference speed","createdAt":"2024-02-27T04:11:08Z"}
{"state":"Merged","mergedAt":"2024-02-27T07:00:37Z","number":10928,"body":"- Removed default value for cluster name\r\n- Corrected the cluster name in workflows by removing the leading dot\r\n- Modified values files of all services to include the dot leading the cluster name\r\n- Updated all helm charts with latest changes","mergeCommitSha":"dbefbb028c31907f9c3e0f688fd00e5cef2b9075","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10928","title":"[SKIP TESTS] Make cluster name a required field in helm","createdAt":"2024-02-27T06:44:30Z"}
{"state":"Merged","mergedAt":"2024-02-27T07:18:13Z","number":10929,"mergeCommitSha":"407e8ac75d407ff4d34dc8c7275d231208508e40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/10929","title":"Add datetime clamp to pr processing","createdAt":"2024-02-27T07:16:19Z"}
{"state":"Merged","mergedAt":"2022-05-01T19:43:37Z","number":1093,"mergeCommitSha":"2da51b6380cf30521ab1036ffa86721973da6cb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1093","title":"Admin statistics page","createdAt":"2022-05-01T04:45:00Z"}
{"state":"Merged","mergedAt":"2022-05-04T00:01:13Z","number":1094,"body":"\r\nhttps://user-images.githubusercontent.com/1798345/166593663-b2100c4f-eb56-430f-b86d-f527ba62872c.mov\r\n\r\n","mergeCommitSha":"6e8d14cb0e05b84e5af848564aa7105a5fcac225","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1094","title":"Sourcemark engine handles whole-line code moves","createdAt":"2022-05-01T05:47:39Z"}
{"state":"Merged","mergedAt":"2022-05-02T22:14:30Z","number":1095,"body":"- Added IAM roles and policies needed for deploybot to create and manage EC2 instances\r\n- Modified services workflow to use a self hosted runner when running service builds (drops our build time to 10 mins) \r\n\r\nTODOs:\r\n- Docker pulls are taking about 1 min on the self hosted runner. I'll cache those images with the next AMI \r\n- We need to optimize the EC2 cost (currently 5 cents a build) and either use spot instances or purchase long term EC2 allocation","mergeCommitSha":"c6f40a8f92f9179fe2f450c3ac341bc257f0605d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1095","title":"Self-hosted runners for service builds","createdAt":"2022-05-01T21:32:03Z"}
{"state":"Merged","mergedAt":"2022-05-02T13:25:40Z","number":1096,"body":"This pr moves us away from using docker for postgres for tests.\r\nWe are now adding a custom Database base test class with initializer that uses an embedded postgres process for tests. \r\nThis allows each db test to run **independent** of the next when it comes to db modifications.\r\n\r\nIn tandem:\r\n1. We are moving to Junit5 (Jupiter), which has proper parallelization framework.\r\n2. We are using embedded postgres libraries.\r\n3. Adding dependency on a good retry library ([Failsafe](https://failsafe.dev), resilience4j is a piece of shit comparatively)\r\n\r\nOther benefits:\r\n1. No more reliance on static initialization of Postgres for tests.\r\n\r\nIn summary, this basically means much faster test runs (assuming we move to custom test runners with more cores (github actions test runners are pieces of shit) @mahdi-torabi ","mergeCommitSha":"c1c751fec4499b7c3cae993dcf8cae74b0f9474c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1096","title":"Using Embedded Postgres to parallelize tests","createdAt":"2022-05-01T23:04:45Z"}
{"state":"Merged","mergedAt":"2022-05-02T19:10:17Z","number":1097,"mergeCommitSha":"57c42c0e05ef91258d62f6e42157aae387b05ae8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1097","title":"Save pr comment URL on MessageModel","createdAt":"2022-05-01T23:53:53Z"}
{"state":"Merged","mergedAt":"2022-05-02T21:24:25Z","number":1098,"mergeCommitSha":"26cc1e1adf2b79c0dc55b6f7bcca02494252abee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1098","title":"SM engine ignores space changes","createdAt":"2022-05-02T06:01:24Z"}
{"state":"Merged","mergedAt":"2022-05-02T16:53:43Z","number":1099,"body":"Fixes this broken page:\r\nhttps://admin.dev.getunblocked.com/teams/c9c47779-235f-4ca9-aefb-96758fa96650/repos/fb06ca32-cdf0-4bcc-9e08-c8c675768235/sourcemarks/095046c7-8fb5-4e11-a3b4-0df0873ff7c9","mergeCommitSha":"dcc776cf6d7c9f300df158bb1c5c70fe2df1dcee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1099","title":"Fix sourcemark admin page","createdAt":"2022-05-02T06:02:03Z"}
{"state":"Merged","mergedAt":"2021-12-14T19:05:35Z","number":11,"mergeCommitSha":"99cde3051e38d4ac755675502db5aedbb98f34dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/11","title":"Update ci main","createdAt":"2021-12-14T19:05:15Z"}
{"state":"Merged","mergedAt":"2022-01-24T19:27:48Z","number":110,"body":"- Some cosmetic changes in database-stack\r\n- Added Openvpn stack to create the following:\r\n  - Security Group\r\n  - EC2 Instance \r\n  - Allocate elastic IP\r\n  - Create DNS A record pointing to elastic ip \r\n\r\nThese changes are deployed to `us-west-2` region in `sec-ops` account only. We do not support running VPN service in other regions and the code will throw an exception if anyone attempts to do so. \r\n\r\n**IMPORTANT NOTE**: Due to limitations in AWS CDK, peering connections and static routes for OpenVPN traffic are setup manually. ","mergeCommitSha":"c17e13275743817a7fa2182bd7507efca654fa52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/110","title":"Cdk openvpn setup","createdAt":"2022-01-24T18:18:59Z"}
{"state":"Merged","mergedAt":"2022-05-02T17:26:08Z","number":1100,"mergeCommitSha":"b0d99a05af07607345d70eb41b95a0e0b9407c59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1100","title":"Ktor 2.0.1","createdAt":"2022-05-02T06:02:42Z"}
{"state":"Merged","mergedAt":"2022-05-02T13:54:52Z","number":1101,"mergeCommitSha":"65ea7cf520cf20a1143f45637ce50970a0dae545","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1101","title":"update","createdAt":"2022-05-02T13:54:42Z"}
{"state":"Merged","mergedAt":"2022-05-02T21:40:03Z","number":1102,"mergeCommitSha":"9a2b27160c5772b2e7e92454661d6d11a3cf5d9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1102","title":"Add Title to slack releases","createdAt":"2022-05-02T16:10:22Z"}
{"state":"Merged","mergedAt":"2022-05-02T18:13:46Z","number":1105,"body":"https://github.com/NextChapterSoftware/unblocked/issues/1067\r\n\r\nNOTE: this still isn't perfect (if the user is looking at a team discussion, this would navigate back to /mine regardless) but at least is more accurate than what it was. I'm not sure how to accurately do this without having to do a bunch of prop passing or param state to know whether or not it's a /team or /mine thread - decided to forgo doing any of this since we're rethinking the sidebar organization anyway","mergeCommitSha":"a39ad2302fd162056bbd6b1b69c319c5fc81e1aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1105","title":"Update navigate path in dashboard thread view","createdAt":"2022-05-02T18:07:08Z"}
{"state":"Merged","mergedAt":"2022-05-02T21:04:33Z","number":1106,"body":"* Make sure that all clients have a consistent minimum height for the message editor\r\n* Clicking on the padding area for a message editor now focuses the editor\r\n\r\nI copied the styling here for each of the clients -- there actually is no other shared CSS for the message editor. I don't know, maybe we should make one...?\r\n\r\n<img width=\"1325\" alt=\"Screen Shot 2022-05-02 at 1 15 46 PM\" src=\"https://user-images.githubusercontent.com/2133518/166326581-d75c1fbf-4a05-4727-9dc9-54eec9ff5f6c.png\">\r\n\r\n<img width=\"1552\" alt=\"Screen Shot 2022-05-02 at 1 54 59 PM\" src=\"https://user-images.githubusercontent.com/2133518/166326607-d12667ce-d982-4d2f-a55d-c9e231819baa.png\">\r\n\r\n<img width=\"1345\" alt=\"Screen Shot 2022-05-02 at 11 34 20 AM\" src=\"https://user-images.githubusercontent.com/2133518/166326691-f3ab0613-c9e7-4e24-9649-56ca274e26e9.png\">\r\n\r\n","mergeCommitSha":"e63fd6e53523000dc0302ffa5cab761f7d55b951","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1106","title":"Fix MessageEditor spacing","createdAt":"2022-05-02T18:20:16Z"}
{"state":"Merged","mergedAt":"2022-05-02T18:25:34Z","number":1107,"body":"- Added two new standard queues `pr_ingestion` and `search_indexing`\r\n- Added Producer/Consumer permissions on `pr_ingestion` to scm service\r\n- Added Producer permission on `search_indexing` to API service\r\n- Created a new service account for Search Service with Postgres permission\r\n- Added Consumer permission on `search_indexing` to Search service (to be created)","mergeCommitSha":"8da5a1b851ec7a4e9a4c8a37bdf4f8d13b1b6b8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1107","title":"Create new queues and service account for search","createdAt":"2022-05-02T18:22:22Z"}
{"state":"Merged","mergedAt":"2022-05-02T20:26:11Z","number":1108,"mergeCommitSha":"71459f5500efc2f3df7b3047333397c3635f7794","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1108","title":"Add better messaging","createdAt":"2022-05-02T18:28:14Z"}
{"state":"Merged","mergedAt":"2022-05-03T00:11:36Z","number":1109,"body":"https://github.com/NextChapterSoftware/unblocked/issues/1076\r\n\r\n<img width=\"487\" alt=\"image\" src=\"https://user-images.githubusercontent.com/********/*********-2d841b4e-9210-48d6-94f2-1e31e67aaf8b.png\">\r\n\r\n","mergeCommitSha":"1f0472e9dda17ac4b389659bf9d03d7a71b386de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1109","title":"Pass in vertical align value to sm icon","createdAt":"2022-05-02T18:53:41Z"}
{"state":"Merged","mergedAt":"2022-01-24T21:58:28Z","number":111,"body":"## Problem\r\nDuring the authentication flow there are 2 stateful requirements:\r\n1. We must be able to validate the `nonce` and `secret` we're seeing from the client. If either are invalid, we need to drop the authentication flow.\r\n2. We need the ability to tie a successful authentication event to a pre-auth token, so that a native client can perform an access token exchange out-of-band of the OAuth flow.\r\n\r\n## Proposal\r\nIntroduce an `AuthenticationState` model that associates an `Identity`, `nonce`, and `secret`. The presence of the `Identity` denotes a successful authentication event. \r\n\r\nIdeally, this model would auto-expire after some period of time. Additionally, when an authentication flow completes, all previous `AuthenticationState` objects for that `Identity` should be deleted.","mergeCommitSha":"342b704b86a938404e7be37a92a212e0275f89c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/111","title":"Add authentication state model to facilitate auth flow","createdAt":"2022-01-24T18:37:21Z"}
{"state":"Merged","mergedAt":"2022-05-03T22:58:50Z","number":1110,"body":"## Summary\r\n\r\nThis PR sets up the stores and rips down all the data. It doesn't poll yet, so there are no notifications (coming next). Current behaviour is that it will re-fetch the data every time the hub is opened, so you may see some cached data load in followed by an update that is currently unanimated.\r\n\r\n## Caveats\r\nWe're currently using `AsyncImage` for avatar rendering, which is a macOS 12.0 API. If the user is on 11.0 they will see a grey circle. We'll have to implement this component ourselves (or borrow).\r\n\r\nThe networking is pretty heavy given the lack of polling, so we're ripping down all the threads on every request. Overall this can take up to 10 seconds to complete (like the dashboard). \r\n\r\n\r\n## Previews\r\n<img width=\"559\" alt=\"CleanShot 2022-05-02 at 14 26 41@2x\" src=\"https://user-images.githubusercontent.com/858772/166331045-25aebf90-b513-465c-9eef-ef3fa5e70f4d.png\">\r\n<img width=\"578\" alt=\"CleanShot 2022-05-02 at 14 26 30@2x\" src=\"https://user-images.githubusercontent.com/858772/166331052-3ca4cdf0-5dbb-4599-bcb7-b0fce3950e0a.png\">\r\n<img width=\"552\" alt=\"CleanShot 2022-05-02 at 14 26 12@2x\" src=\"https://user-images.githubusercontent.com/858772/166331058-f002178a-8d29-4d97-853f-979ade7469d5.png\">\r\n<img width=\"528\" alt=\"CleanShot 2022-05-02 at 14 25 59@2x\" src=\"https://user-images.githubusercontent.com/858772/166331064-2b07d5f3-9cd4-4cff-be55-e6ec9e0f9a66.png\">\r\n\r\n","mergeCommitSha":"078276b90e03e44822d86905920b9c8133dc5216","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1110","title":"Load threads and team member data for realz","createdAt":"2022-05-02T19:20:32Z"}
{"state":"Merged","mergedAt":"2022-05-03T20:28:06Z","number":1111,"body":"Upon thread creation, users can choose to invite both Unblocked members and non-UB members (i.e. via git). We want to send notification emails for both these kinds of users, but given that the email content will be different in each case, we will need some way to differentiate between UB and non UB members.\r\n\r\nProposal is to add an optional `emails` list view to the createThread request - this will be a list of non-UB email addresses. The `participants` field will remain as is, being a list of UB user uuids to both send a notification email and add an unread to the user. \r\n\r\nIf the API is agreed upon, then I'll start working on the implementation side.","mergeCommitSha":"6df8168504014e7c7954c6249217942427de3e65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1111","title":"Add emails to createThread request for invite","createdAt":"2022-05-02T19:49:53Z"}
{"state":"Merged","mergedAt":"2022-05-02T21:06:58Z","number":1112,"mergeCommitSha":"cddb883855cb300989f784cc881a2cf1e64de048","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1112","title":"Update exposed","createdAt":"2022-05-02T20:48:07Z"}
{"state":"Merged","mergedAt":"2022-05-02T23:38:30Z","number":1113,"body":"This service will be responsible for indexing. It won't be publicly accessible, will be triggered by events from the `search_indexing` queue, and doesn't need redis (so I didn't include any of the secrets).","mergeCommitSha":"7330d32ac797be60ba25c711aef91baa4767f7a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1113","title":"Add search service","createdAt":"2022-05-02T21:50:14Z"}
{"state":"Merged","mergedAt":"2022-05-02T22:21:42Z","number":1114,"body":"Broke with Ktor 2.0.1 upgrade!","mergeCommitSha":"afb490594cb2a2bb31361b13d90280ed743961a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1114","title":"Fix breadcrumb","createdAt":"2022-05-02T22:06:28Z"}
{"state":"Merged","mergedAt":"2022-05-02T23:43:57Z","number":1115,"body":"Instead of outputting entire commit message, just use the title ( aka first line)","mergeCommitSha":"f396279914f58a6591f58a3040ca413959d55a10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1115","title":"Slim down commit info to just title","createdAt":"2022-05-02T22:12:53Z"}
{"state":"Merged","mergedAt":"2022-05-02T23:23:24Z","number":1116,"body":"This reverts commit b0d99a05af07607345d70eb41b95a0e0b9407c59.","mergeCommitSha":"43888a906b6789c476ad95f9ec09dd2e12721657","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1116","title":"Revert \"Ktor 2.0.1 (#1100)\"","createdAt":"2022-05-02T22:31:58Z"}
{"state":"Merged","mergedAt":"2022-05-03T17:47:40Z","number":1117,"body":"Slowly address this list:\r\n<img width=\"689\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/166340058-d0a92d78-60ab-4300-85b0-cf787d6f4fb6.png\">\r\n\r\nSimple pr, add underline.\r\n\r\n<img width=\"616\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/166338796-5a37bea9-2778-451e-af1c-79d6bd89bd01.png\">\r\n","mergeCommitSha":"9b1a40b787bbfe2a9d0d0c444204361be5c6da43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1117","title":"Add basic support for underline rendering in message editor","createdAt":"2022-05-02T22:47:45Z"}
{"state":"Merged","mergedAt":"2022-05-02T23:27:38Z","number":1118,"body":"Should not be refreshing teams until authenticated.","mergeCommitSha":"6982290f8d09be4421b54b80dd4a160fab6b23b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1118","title":"Only refresh teams when auth","createdAt":"2022-05-02T23:11:44Z"}
{"state":"Merged","mergedAt":"2022-05-02T23:32:42Z","number":1119,"body":"Removing dead code that uses `getCurrentRepo`\r\n\r\nInstallCommand was used before current auth flow.\r\ngetCurrentRepoUrl not used anywhere.\r\n","mergeCommitSha":"6d3e6f5b6d2eaa0e51c8ff2ec9465b80b82caf45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1119","title":"Remove unused Auth and Git interfaces","createdAt":"2022-05-02T23:27:27Z"}
{"state":"Merged","mergedAt":"2022-01-24T21:32:28Z","number":112,"body":"This is Phase 1 of componentizing service deployment by using a reusable workflow.\r\nBuilding service image/deployemnt to dev has now been moved to a reusable workflow.\r\n\r\nNext phase is to make deployment each environment a composite action.","mergeCommitSha":"1a520c310909934497978ea29408134da5caa597","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/112","title":"Make service deployment a reusable workflow","createdAt":"2022-01-24T19:32:39Z"}
{"state":"Merged","mergedAt":"2022-05-03T17:38:05Z","number":1120,"body":"preventdefault cancels normal browser event handling.\r\n","mergeCommitSha":"57b0cb2f77900034ee03063186c4cb412e1d7642","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1120","title":"Fix copy and paste","createdAt":"2022-05-02T23:48:06Z"}
{"state":"Merged","mergedAt":"2022-05-03T02:30:58Z","number":1122,"mergeCommitSha":"a6317b03611d9844596da87df82d08e6117a04b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1122","title":"Enable parallel gradle builds","createdAt":"2022-05-03T00:17:05Z"}
{"state":"Merged","mergedAt":"2022-05-03T00:25:59Z","number":1123,"body":"Service deploys are failing because the repo doesn't exist yet. ","mergeCommitSha":"ac6ed9ae97240c4e024ff370b93d537c387a6cad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1123","title":"add a repo for search service","createdAt":"2022-05-03T00:24:13Z"}
{"state":"Merged","mergedAt":"2022-05-03T06:15:51Z","number":1124,"mergeCommitSha":"34ae9a10d5219a93cfdc60ad3cc64fbfebcc23e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1124","title":"Add request metadata to ktor exception handler","createdAt":"2022-05-03T05:42:20Z"}