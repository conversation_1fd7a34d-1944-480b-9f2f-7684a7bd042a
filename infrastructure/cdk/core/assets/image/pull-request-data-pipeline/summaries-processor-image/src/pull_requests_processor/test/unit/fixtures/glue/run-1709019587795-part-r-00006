{"state":"Closed","mergedAt":null,"number":150,"body":"\r\nThe current `/login` API spec causes some issues for the clients.\r\n\r\nIt currently redirects users to the desired auth location by leveraging 302 redirects. This is done by changing the browser's location to `/login`, not calling the API with fetch like all other APIs.\r\n\r\nThis is not the intended use case for the openAPI spec & codegen.\r\n\r\nTo work around this, we introduced additional tooling codegen templates so to expose the `/login` URL without actually making the network request.\r\n\r\nProposal:\r\n\r\nInstead of a `/login` endpoint that redirects users to the desired location, introduce a `/login/options` endpoint that returns a list of URLs that the client can navigate to for authentication.\r\n\r\n* Will allow the backend to determine what auth options are available.\r\n* Will allow the backend to construct these URLs which provides flexibility.\r\n* Can include extra metadata & context (aka descriptions, errors, flags, etc.)\r\n* Requires no custom codegen templates (difficult to maintain in long term)\r\n* No strange 302 behaviours from the openAPI perspective.","mergeCommitSha":"5c9788bc4417677d429d5d91ffec51c81bc14199","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/150","title":"[RFC] Login Options Proposal","createdAt":"2022-01-27T19:06:23Z"}
{"state":"Merged","mergedAt":"2022-05-31T18:47:14Z","number":1500,"body":"<img width=\"915\" alt=\"CleanShot 2022-05-27 at 17 36 11@2x\" src=\"https://user-images.githubusercontent.com/1924615/170802785-603d0046-e66f-4f8f-a29d-49fe368b63ad.png\">\r\n","mergeCommitSha":"c6370990e8b23cd4d2965b604a90965b5ea175aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1500","title":"Add avatar to footer","createdAt":"2022-05-28T00:36:37Z"}
{"state":"Merged","mergedAt":"2022-05-28T03:38:53Z","number":1501,"mergeCommitSha":"aa69959dcc5f52b5b789ca88ef380e871c6928ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1501","title":"Try again","createdAt":"2022-05-28T03:38:47Z"}
{"state":"Merged","mergedAt":"2022-05-28T03:59:19Z","number":1502,"mergeCommitSha":"7ed9adeae5342d6dcdc0ceb9b21cac6421ca5bf0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1502","title":"Try again","createdAt":"2022-05-28T03:59:12Z"}
{"state":"Merged","mergedAt":"2022-05-28T05:02:26Z","number":1503,"mergeCommitSha":"965b97cd5a67d3167887b3a24ac7c06032d4b75f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1503","title":"Fix build","createdAt":"2022-05-28T05:02:16Z"}
{"state":"Merged","mergedAt":"2022-05-30T23:06:05Z","number":1504,"body":"Setup stream for onboarding events with hub.","mergeCommitSha":"f9163f019e99563c6644375aa1171e3b3d3dbcd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1504","title":"Setup hub connection for onboarding","createdAt":"2022-05-29T19:46:37Z"}
{"state":"Merged","mergedAt":"2022-05-30T15:58:23Z","number":1505,"body":"Sidebar search stream had no initial value.\r\nTherefore, a downstream `Stream.Combine` would not combine streams as it was missing an event/value until someone typed into the search bar.\r\n\r\nAdding an initial undefined value to search stream to kick off thread sidebar loading.","mergeCommitSha":"b208ade6143bcdf007452164a451874f25e1d5c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1505","title":"Add initial value to search stream","createdAt":"2022-05-29T20:25:01Z"}
{"state":"Merged","mergedAt":"2022-05-29T23:36:32Z","number":1506,"mergeCommitSha":"533f8ea48bf9fe93b1ffd51084d07d0233b346e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1506","title":"Don't show archived threads in the gutter","createdAt":"2022-05-29T23:29:33Z"}
{"state":"Merged","mergedAt":"2022-06-14T00:15:22Z","number":1507,"body":"Fixes UNB-181 https://linear.app/unblocked/issue/UNB-181/update-thread-listing-in-tree-view-when-threads-are-archived.\r\n\r\nThis isn't a huge PR but it's got a lot of changes:\r\n- Each target for ChannelPoller can now specify a list of channels that it wants to be notified on.  These lists are dynamic, so on every poll, every target can specify a new list.  Needed for the below stuff.\r\n- The thread list stores now hold onto the last returned set of threads, and:\r\n  - Uses the last element's `cursor` for subsequent channel polls: `/threads/mine?before=<lastElementCursor>`\r\n  - Subscribes to every thread's individual thread channels by ID: `/threads/<threadId>`\r\n- The PR store now holds onto the last returned set of PRs, and:\r\n  - Subscribes to every PR's individual PR channel by ID: '/pullRequests/<prId>`\r\n\r\nThe end result is:\r\n* We use `before` when channel polling, which makes channel polling more efficient\r\n* Clients are notified when threads or PRs are deleted, and re-fetch the listings to update the UI\r\n\r\n\r\nSome thoughts:\r\n* The ChannelPoller and API cache/streaming stuff has reasonably good test coverage, so I'm not super worried about regressions\r\n* However, the individual stores have no tests, which is a pretty significant weakness.  Post-preview, we should write tests for each store, as the logic within the stores is pretty important to get right.  We can do this by mocking out the individual APIs in question, plus the poller API.\r\n","mergeCommitSha":"9f85b416776591298046ed02f0097c759c3a63bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1507","title":"Use 'before' in thread fetching, and subscribe to all individual thread/PR channels","createdAt":"2022-05-30T04:07:21Z"}
{"state":"Closed","mergedAt":null,"number":1508,"body":"Refactor wizard button into common component.\r\n\r\nStill has individual implementations for tutorial & wizard due to type differences.","mergeCommitSha":"e5ca83ec4db9ced9538d5947291ab82b22ee200f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1508","title":"Refactor wizard button","createdAt":"2022-05-30T17:55:29Z"}
{"state":"Merged","mergedAt":"2022-05-30T20:57:03Z","number":1509,"mergeCommitSha":"40dc75f514013eb360e9df07955b2aaf88ead13c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1509","title":"findSourceMarks does not return marks for archived threads","createdAt":"2022-05-30T17:58:44Z"}
{"state":"Merged","mergedAt":"2022-01-28T03:36:28Z","number":151,"mergeCommitSha":"1b01d55ed20e0580066cc1d975c19ff31f7dabcc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/151","title":"Hook up GET chat operations to the database","createdAt":"2022-01-27T19:16:18Z"}
{"state":"Merged","mergedAt":"2022-05-30T20:42:10Z","number":1510,"body":"Will be used in follow up PRs.","mergeCommitSha":"ba4c784f6e4e46bf5885b0e4bc26d981486d45cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1510","title":"Introduce RepoUrl utility","createdAt":"2022-05-30T18:02:16Z"}
{"state":"Merged","mergedAt":"2022-05-30T23:41:24Z","number":1512,"mergeCommitSha":"200ec54eddc7818a4beeaad7862d9cdb1789384a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1512","title":"Implement getInstallations API","createdAt":"2022-05-30T18:31:05Z"}
{"state":"Merged","mergedAt":"2022-05-30T19:24:35Z","number":1514,"body":"To disable prod deploys update `SUSPEND_PROD_DEPLOYS` secret value to `true`\r\nTo re-enable prod deploys update `SUSPEND_PROD_DEPLOYS` secret value to `false`","mergeCommitSha":"0cb683fad5e133b383d3454931bbedb0e18ea858","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1514","title":"adding flag to gate prod deployments","createdAt":"2022-05-30T18:37:14Z"}
{"state":"Merged","mergedAt":"2022-05-30T20:56:45Z","number":1515,"body":"This test keeps flakying in CI and I have no idea why, since it is not time dependent.\nApplying a team clause (which is more realistic anyway) might stabilize.","mergeCommitSha":"2bd22b391316f24ee194efbfee9ff79ffb1a5e1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1515","title":"Maybe fix flaky test","createdAt":"2022-05-30T18:50:14Z"}
{"state":"Merged","mergedAt":"2022-05-30T19:58:38Z","number":1516,"body":"Fixing a copy pasted mistake","mergeCommitSha":"6bf9c66dd70d29026e45fe7e91f98a26bb737a31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1516","title":"correct a mistake in secret name","createdAt":"2022-05-30T19:45:59Z"}
{"state":"Closed","mergedAt":null,"number":1517,"body":"Motivation\n1. For demos with users (like Dennis) that have low PR engagement. Product doesn't demo well when there are zero recommendations.\n2. For first launch before PRs have been ingested, we should show _something_ in the recommended collection. This will quickly change to _real_ recommendations after ingestion has run, which takes a few minutes.","mergeCommitSha":"23cb50f719eecd85f056c3a6b41b6393e04be69b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1517","title":"Recommended threads collection shows unranked threads","createdAt":"2022-05-30T20:40:41Z"}
{"state":"Merged","mergedAt":"2022-05-30T21:36:25Z","number":1518,"body":"Changed the comparison function to deal with any leading or trailing spaces. ","mergeCommitSha":"e4521dabbc3d95751412128dc43318d2a5131a65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1518","title":"this is to deal with any whitespaces.","createdAt":"2022-05-30T20:51:51Z"}
{"state":"Closed","mergedAt":null,"number":1519,"body":"@matthewjamesadam suggested this. Makes a good deal of sense to roll this into the xcodebuild","mergeCommitSha":"d8c3f9769beb2434dfe8e10c8f27159bb4d21032","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1519","title":"Move build setup to a run script build phase","createdAt":"2022-05-30T21:44:11Z"}
{"state":"Merged","mergedAt":"2022-01-27T20:22:58Z","number":152,"body":"```\r\n> Task :api:zallyLint FAILED\r\n-------- -----\r\nSEVERITY COUNT\r\n-------- -----\r\nMUST        2\r\nSHOULD     55\r\n--------------\r\nMUST\r\n--------------\r\nResult(id=M010, url=https://github.com/zalando/zally/blob/master/server/rules.md#m010-check-case-of-various-terms, title=Check case of various terms, description=Path segment 'exchange-blah' does not match camelCase ('[a-z][a-z0-9]*(?:[A-Z0-9]+[a-z0-9]*)*') but seems to be kebab-case ('[a-z][a-z0-9]*(?:-[a-z0-9]+)*'), violationType=MUST, pointer=/paths/~1preauth~1exchange-blah, lines=54..75)\r\nResult(id=129, url=https://zalando.github.io/restful-api-guidelines/#129, title=Lowercase words with hyphens, description=Use lowercase separate words with hyphens for path segments, violationType=MUST, pointer=/paths/~1preauth~1exchange-blah, lines=54..75)\r\nThe violation report can be found at /Users/<USER>/chapter2/codeswell/zally/violation.json\r\nspec has 2 MUST violations.but only 1 violations allowed\r\n\r\n```","mergeCommitSha":"3ddcdeea7075a850e6752945c2eff507d7166e3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/152","title":"Add ability to configure logging for openapi linter","createdAt":"2022-01-27T19:34:25Z"}
{"state":"Merged","mergedAt":"2022-05-30T22:15:37Z","number":1520,"body":"The current cloudfront edge functions had problems because they made assumptions that all the uris were rooted at some app path (i.e. /dashboard )\r\n\r\nSince landing page is at root (‘/‘) we needed to add special handlers to determine whether it’s a landing page request or an app request.\r\n\r\nWe’ve added comprehensive tests to ensure this does not break backwards compatibility.\r\n\r\nTesting:\r\nhttps://dev.getunblocked.com\r\nhttps://dev.getunblocked.com/\r\nhttps://dev.getunblocked.com/team/blah/invite\r\n\r\nTODO:\r\nThese functions need to be documented. Took me a little while to figure out what the purpose of these were.","mergeCommitSha":"709985b75ad88f15f0c2a633995ab151367520dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1520","title":"Fix cloudfront edge lambdas for landing page","createdAt":"2022-05-30T22:00:01Z"}
{"state":"Merged","mergedAt":"2022-05-30T22:44:31Z","number":1521,"mergeCommitSha":"9853ecd7d1862f309662b2081cd4b61a72514e24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1521","title":"So the clients have something to render until the link is wired up","createdAt":"2022-05-30T22:30:22Z"}
{"state":"Merged","mergedAt":"2022-05-31T01:01:06Z","number":1522,"body":"fake...\r\n\r\n<img width=\"845\" alt=\"Screen Shot 2022-05-30 at 15 42 11\" src=\"https://user-images.githubusercontent.com/1798345/171065396-83087112-7abf-4967-bf1c-0f6d77fbe0d9.png\">\r\n\r\n\r\nPrevious approaches messed up the algorithm:\r\n- https://github.com/NextChapterSoftware/unblocked/pull/1406\r\n- https://github.com/NextChapterSoftware/unblocked/pull/1517","mergeCommitSha":"4c22357cd2e79addbd72e7748011f32f4f5110ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1522","title":"Fake demo user comment network","createdAt":"2022-05-30T22:38:45Z"}
{"state":"Merged","mergedAt":"2022-06-01T20:42:37Z","number":1523,"body":"Add ability to manually invite user when creating discussion in VSCode.\r\n\r\nWeb extension next...\r\n\r\nhttps://user-images.githubusercontent.com/1553313/171066283-f298517f-4539-45f1-960e-25d3f1c9dff0.mp4\r\n\r\nFixes https://github.com/NextChapterSoftware/unblocked/issues/1511\r\n\r\n","mergeCommitSha":"770aed47601ee536f7fd64d599695ce88ab82013","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1523","title":"Manually invite user","createdAt":"2022-05-30T23:01:36Z"}
{"state":"Merged","mergedAt":"2022-05-30T23:21:34Z","number":1524,"mergeCommitSha":"0e0eaff722e9d4ab98600091a5d8895b7e3ffb1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1524","title":"Download invite API (teamless)","createdAt":"2022-05-30T23:03:00Z"}
{"state":"Merged","mergedAt":"2022-06-01T22:42:50Z","number":1525,"body":"<img width=\"1095\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171066913-4dbad361-7e1b-4b5b-87a9-adc22197023c.png\">\r\n<img width=\"1100\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171066926-b25d04ea-e304-47e9-8ac6-8a83d2e9ea81.png\">\r\n<img width=\"1048\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171066935-3fddf98f-28eb-41a5-a9ed-d35887300519.png\">\r\n<img width=\"1053\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171066949-dc494ee1-a42e-4014-a591-fb4ae067fcfc.png\">\r\n<img width=\"1118\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171066962-be8286ec-6ad9-4a50-a233-e63be0acdf49.png\">\r\n<img width=\"1044\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171067362-6d538878-ac95-47cc-bd54-6ebaf345d52c.png\">\r\n","mergeCommitSha":"2c4538ad34890bbba09fd2fadedea0d446e1b048","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1525","title":"[Onboarding] Create Insight steps","createdAt":"2022-05-30T23:58:01Z"}
{"state":"Merged","mergedAt":"2022-05-31T01:08:02Z","number":1526,"mergeCommitSha":"b443b4288573725bfd74ed51dd133d9e36ada6b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1526","title":"fix prod deploy flag","createdAt":"2022-05-31T00:53:17Z"}
{"state":"Merged","mergedAt":"2022-05-31T01:51:58Z","number":1527,"body":"<img width=\"273\" alt=\"Screen Shot 2022-05-30 at 18 44 07\" src=\"https://user-images.githubusercontent.com/1798345/171076515-b71fae55-4dcf-4dd4-876d-889637191274.png\">\r\n","mergeCommitSha":"5308c458f8d71b6e868726ec572f800dedc4efb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1527","title":"Fix findRepo api","createdAt":"2022-05-31T01:38:02Z"}
{"state":"Merged","mergedAt":"2022-05-31T04:40:31Z","number":1528,"body":"Repo names, owner names, repo urls can change at any time in the SCM.\nSince we rely on these properties for repo matching, we must update these properties.","mergeCommitSha":"90dedb55d21101d817d790475a3c14a95e0f7500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1528","title":"Repo sync updates existing repos","createdAt":"2022-05-31T03:11:26Z"}
{"state":"Merged","mergedAt":"2022-05-31T03:50:35Z","number":1529,"mergeCommitSha":"07c3858b5e35cd43210877739fd39923f6d64353","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1529","title":"Admin web: clickable rows and more buttons","createdAt":"2022-05-31T03:38:13Z"}
{"state":"Merged","mergedAt":"2022-01-27T21:59:19Z","number":153,"body":"This is a temp workaround until I figure out why we don't get ingress traffic from ELBs. Seems Calico policy applies to Services too!","mergeCommitSha":"13414adc0c9933ade5020e00b9956dc04e20b37a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/153","title":"temp fix while we investigate","createdAt":"2022-01-27T21:42:38Z"}
{"state":"Closed","mergedAt":null,"number":1530,"mergeCommitSha":"d897e0fc4970f718664f6a6c49d98fe4100e1fa9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1530","title":"Service provides Web Extension urls","createdAt":"2022-05-31T14:34:41Z"}
{"state":"Merged","mergedAt":"2022-05-31T17:47:17Z","number":1531,"body":"Cloudfront is dependent on ordering to determine which behaviour takes precedences when doing route matching.\r\n\r\nWe need to ensure root wildcard matching is last element in list.","mergeCommitSha":"08cab86241aec4618f937a0b26f3fce479f6fc47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1531","title":"Fix ordering of cloudfront behaviours","createdAt":"2022-05-31T17:44:24Z"}
{"state":"Merged","mergedAt":"2022-05-31T20:01:29Z","number":1532,"body":"Potentially fix issue with unexpected logouts.\r\nAlso added logging to help debug the situation.\r\n\r\nRemoved unnecessary logout operation for baseAPi","mergeCommitSha":"f13b61dc8cb54bc45ef1bbb701bf96e519248b8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1532","title":"Potentially fix logout + debugging logs","createdAt":"2022-05-31T18:11:31Z"}
{"state":"Merged","mergedAt":"2022-05-31T20:30:10Z","number":1534,"body":"Add PR title to VSCode and web extension.\r\n<img width=\"710\" alt=\"CleanShot 2022-05-31 at 11 38 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/171262544-f23271ec-5595-4c2d-8a50-8dbad6acd5a9.png\">\r\n<img width=\"974\" alt=\"CleanShot 2022-05-31 at 11 38 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/171262547-4166032e-406b-4edb-8429-bf24f1fec56c.png\">\r\n\r\n","mergeCommitSha":"db9e2a472082b496febde6b6f244314469c3e2e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1534","title":"Update PR number in CodeBlock","createdAt":"2022-05-31T18:46:39Z"}
{"state":"Merged","mergedAt":"2022-05-31T20:50:38Z","number":1535,"body":"Problem:\n- thread info subscription is skipped if an existing subscription exists,\n  however the existing thread info is cleared. this means that any update\n  will cause the thread info to be cleared, and the only way for the info\n  to be shown is for the thread info object to change on the server.\n\nSolutions:\n- clear the existing subscription to froce a re-subscription. simple and\n  seems to work, so went with this for now.\n- do not clear the thread info on update. tried this, but hard to track all\n  the places when shared memory is mutated, and hence fragile.","mergeCommitSha":"69e372764c3b3d30e5e926a9f5c156f5e511695d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1535","title":"Fix source mark rendering","createdAt":"2022-05-31T19:16:54Z"}
{"state":"Merged","mergedAt":"2022-05-31T19:57:56Z","number":1536,"mergeCommitSha":"16b211c9d6729b14e7785ebf7e03c25e9ae37deb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1536","title":"Log ip addresses in honeycomb","createdAt":"2022-05-31T19:38:02Z"}
{"state":"Merged","mergedAt":"2022-05-31T20:22:54Z","number":1537,"mergeCommitSha":"dcab028b611f83bce6100cd7ccaee64b033053bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1537","title":"Remove default title from web extension","createdAt":"2022-05-31T20:07:36Z"}
{"state":"Merged","mergedAt":"2022-05-31T20:37:29Z","number":1538,"body":"2 hours of digging through logs for 10 lines of code...Ugh I don't like AWS docs! ","mergeCommitSha":"a632e564e24dc2eb0ae56886b2d48ecd1efc3b59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1538","title":"keep WAF access logs","createdAt":"2022-05-31T20:11:32Z"}
{"state":"Merged","mergedAt":"2022-05-31T20:49:30Z","number":1539,"mergeCommitSha":"c32994ab6e35124a3661b1057341d53b73387803","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1539","title":"Don't open file in sourcemark tooltip click","createdAt":"2022-05-31T20:37:50Z"}
{"state":"Merged","mergedAt":"2022-01-27T23:50:22Z","number":154,"body":"Add ability to share web components across projects.\r\n\r\n* Shared components can go into `/shared/webComponents`.  This is a project that has its own storybook and tests, so we can test shared stuff in isolation.\r\n* Projects using these components add path definitions: `'@shared-web-components` -> `../shared/webComponents`, like we do for other path dependencies\r\n* This required moving all our NPM dependencies to the root, so that a consistent set of module dependencies got imported into each project.  Big change!\r\n\r\nTodo:\r\n* CI tweaks: we aren't running storybook/chromatic for the shared components in CI yet, that will come in the next PR\r\n\r\n\r\n","mergeCommitSha":"ec28c767859b77dc8a511dc0e481a3d4c813c30f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/154","title":"Add shared web components","createdAt":"2022-01-27T22:58:02Z"}
{"state":"Merged","mergedAt":"2022-06-02T18:18:29Z","number":1540,"body":"Fixes a bug where Unblocked includes the signature when ingesting a comment that has one:\r\n\r\n<img width=\"787\" alt=\"CleanShot 2022-05-31 at 14 56 04@2x\" src=\"https://user-images.githubusercontent.com/1924615/171290917-0a3c78ae-a9f5-47fa-b849-c5430f77a6e2.png\">\r\n\r\nThis happens when someone posts a message to a PR thread from Unblocked, then goes to GitHub to edit it. Our current ingestion logic sees that the message has been edited and re-ingests, but also includes the signature that was appended at creation time.","mergeCommitSha":"fff225afd781b430c3c2271a04aaf076f44f8dfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1540","title":"Strip unblocked signature when ingesting a comment","createdAt":"2022-05-31T20:40:01Z"}
{"state":"Merged","mergedAt":"2022-05-31T21:06:37Z","number":1541,"mergeCommitSha":"264f6b5a90b66a209409458e68a07442a6186daf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1541","title":"Add additional cloudfront headers being sent to origin","createdAt":"2022-05-31T20:45:08Z"}
{"state":"Merged","mergedAt":"2022-06-01T22:37:26Z","number":1542,"body":"<img width=\"697\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171283650-6e4fe665-1a33-470c-ab4d-226d14aebe28.png\">\r\n<img width=\"730\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171283685-c7a479b2-7ca4-4802-8527-1d4ffcc963d2.png\">\r\n","mergeCommitSha":"66d046d238d5a818c93495a8384b47dd08b451d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1542","title":"Update tutorial steps for new onboarding mocks","createdAt":"2022-05-31T21:02:29Z"}
{"state":"Merged","mergedAt":"2022-06-01T16:18:01Z","number":1543,"body":"Added lambda edge function to redirect calls to landing page `/` and `/dashboard` to domain apex. \r\n\r\ne.g \r\n`https://www.getunblocked.com/` to `https://getunblocked.com/`\r\n`http://www.getunblocked.com/` to `https://getunblocked.com/`\r\n`www.getunblocked.com/` to `https://getunblocked.com/`\r\n`https://www.getunblocked.com/dashboard` to `https://getunblocked.com/dashboard`\r\n`http://www.getunblocked.com/dashboard` to `https://getunblocked.com/dashboard`\r\n`www.getunblocked.com/dashboard` to `https://getunblocked.com/dashboard`\r\n\r\nDeployed to Dev and works as expected. ","mergeCommitSha":"1e0a217c5c0a30ccdd66f4f0eacb9488f4cbb577","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1543","title":"add www to apex redirect lambda edge","createdAt":"2022-05-31T22:36:20Z"}
{"state":"Merged","mergedAt":"2022-05-31T23:50:11Z","number":1544,"body":"before:\r\n<img width=\"259\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171296529-f01e97be-5dc3-4df4-8f87-5eeb8bce54dc.png\">\r\n\r\nafter:\r\n<img width=\"252\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171296555-0ebacfca-bfb5-47e1-92c0-0aa2845f9663.png\">\r\n\r\n\r\nbug in recent logic migration change:\r\n<img width=\"1410\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171296500-905a7046-ffee-454a-bed9-0b7f7c88a93d.png\">\r\n","mergeCommitSha":"dd9d20106221117f298bf02ec740fdbf589406e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1544","title":"Fix icon stacking","createdAt":"2022-05-31T22:50:05Z"}
{"state":"Merged","mergedAt":"2022-05-31T23:58:36Z","number":1545,"body":"We were not keying thread channels by their repoIDs while the requests themselves were scoped to repoIDs.\r\n\r\nThis was causing loading issues with web extension when one had multiple tabs open with different repos.\r\nCould also cause infinite loops since clients were observing *all* changes, not ones scoped to the repo.","mergeCommitSha":"e830ec3faaf5958705aaedd40e456fa4733ba279","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1545","title":"Fix thread loading issue","createdAt":"2022-05-31T23:02:06Z"}
{"state":"Merged","mergedAt":"2022-06-01T00:54:26Z","number":1547,"mergeCommitSha":"34148f277b4ab933be60a21e538926699a41b8b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1547","title":"Update Version","createdAt":"2022-06-01T00:00:48Z"}
{"state":"Merged","mergedAt":"2022-06-01T18:56:34Z","number":1548,"mergeCommitSha":"3c21479ef6c7d7e6453edeee69dc7c6028c4910c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1548","title":"Wire up onboarding","createdAt":"2022-06-01T00:05:00Z"}
{"state":"Merged","mergedAt":"2022-06-02T05:50:16Z","number":1549,"body":"Useful for iterating on the installer locally.","mergeCommitSha":"bb86d605a0865ea8f1a7b6d2d1a013d014cf3c5b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1549","title":"Add a manual script","createdAt":"2022-06-01T00:19:27Z"}
{"state":"Merged","mergedAt":"2022-01-31T17:24:07Z","number":155,"mergeCommitSha":"1a6bb0561a88b7c274c3615b8db40f90b236f60c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/155","title":"Setup end to end auth with github","createdAt":"2022-01-27T23:46:06Z"}
{"state":"Merged","mergedAt":"2022-06-01T01:03:36Z","number":1550,"body":"Peter has asked for email invites that do not require a team but have an authorized user with it.\r\nI'm trying to give him that.","mergeCommitSha":"659792738b33569778c5b938a66786a045a0f086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1550","title":"[WOLF] Non team email invites","createdAt":"2022-06-01T00:27:39Z"}
{"state":"Merged","mergedAt":"2022-06-01T05:06:32Z","number":1551,"body":"Fixes\r\n- Marks are cached once calculated in file-to-marks cache. The cache is used\r\n  to lookup marks when calling `getSourceMarksForFile`. Problem is that this\r\n  does not account for new marks generated by the server. So we hack for now\r\n  by augmenting the cache with a traversal over all sourcemarks matching the\r\n  file path.\r\n- After creating a discussion, poll until we observe a sourcemark associated with\r\n  the new thread returned from the calculator. Poll for 10 seconds hoping for the\r\n  mark to become available.","mergeCommitSha":"6209194f3b188979c7ad2b7d7a0d193b969a3970","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1551","title":"Fixes for source mark rendering","createdAt":"2022-06-01T03:53:16Z"}
{"state":"Merged","mergedAt":"2022-06-01T23:31:34Z","number":1552,"mergeCommitSha":"44d94380c5f719a95301ebb00a9cadbc4f91bad4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1552","title":"Enable manual triggering of the installer workflow for a branch, tag, or SHA","createdAt":"2022-06-01T05:57:58Z"}
{"state":"Merged","mergedAt":"2022-06-01T06:12:50Z","number":1553,"body":"When the user makes a new thread, overlay the thread and sourcemark in the TextEditorSourceMarks class, so we can render it immediately.\r\n\r\nThis code got pretty gross because we have to construct a fake `ThreadInfoAggregate` from the bits and pieces of data we have lying around.  The actual overlay code is not so bad.","mergeCommitSha":"fd6c578ee8c6c56ad8a42ccfa99c5088dfe1be39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1553","title":"Overlay sourcemark creation","createdAt":"2022-06-01T06:05:38Z"}
{"state":"Merged","mergedAt":"2022-06-01T16:50:05Z","number":1554,"mergeCommitSha":"869ea41fd80e4bcbe2ebec330d1fa606d40daf58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1554","title":"Update readme with env switch choices","createdAt":"2022-06-01T16:34:04Z"}
{"state":"Merged","mergedAt":"2022-06-01T17:32:14Z","number":1555,"body":"<img width=\"618\" alt=\"Screen Shot 2022-06-01 at 10 12 24\" src=\"https://user-images.githubusercontent.com/1798345/171462510-c1e15252-eea9-4a4b-b7ef-3ecebb912b1a.png\">\r\n","mergeCommitSha":"2cccfc52228f213a94584804cd9310e97a3262ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1555","title":"Change getInstallations response","createdAt":"2022-06-01T17:11:14Z"}
{"state":"Merged","mergedAt":"2022-06-01T17:54:41Z","number":1556,"mergeCommitSha":"562f97acd48ffad5f0d3264d2a98c183c1549b2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1556","title":"Cleanup hierarchy","createdAt":"2022-06-01T17:24:43Z"}
{"state":"Merged","mergedAt":"2022-06-01T17:52:53Z","number":1557,"body":"Write to user default on VSCode plugin load. \r\n\r\nTODO: When can we clean this up? Can the hub app check for this once in a while?","mergeCommitSha":"53e0c0781bf54b1400c11fcd5b8d9242845d93ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1557","title":"Add User default when plugin installed","createdAt":"2022-06-01T17:31:01Z"}
{"state":"Merged","mergedAt":"2022-06-01T17:59:25Z","number":1558,"mergeCommitSha":"7ba16fc5991dabef610e4eba9f64b50b0c96e843","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1558","title":"Nuke team cache when logging out or bad things will happen","createdAt":"2022-06-01T17:40:36Z"}
{"state":"Merged","mergedAt":"2022-06-01T17:56:15Z","number":1559,"body":"- Distinct was necessary in a previous iteration, but no longer needed.\n  removing this _might_ address ordering, since distinct can have an\n  ordering effect in some cases. Test still pass without it.\n- Also cleanup unnecessary join in archived threads query.","mergeCommitSha":"17df5e2b1400bc3da5d26a454c7ba525666ee7b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1559","title":"Possible fix for ordering issue","createdAt":"2022-06-01T17:43:52Z"}
{"state":"Merged","mergedAt":"2022-01-28T02:02:08Z","number":156,"body":"This pr does the following:\r\n1. We use ansible vault to store our secrets in the repo (it's fucking easy...)\r\n2. We use an ansible playbook to auto-descrypt secrets into ~/.secrets directory\r\n3. We update GlobalConfig to optionally load a conf file from an OPTIONAL ~/.secrets/secrets.conf\r\n4. We add a ServiceInitializer that can also load java system properties from an OPTIONAL ~/.secrets/secrets.properties into the java process system configuration.\r\n5. We update github actions to load playbook.\r\n6. We add logzio helper utilities / appenders.\r\n\r\n#4 is necessary for Logz.io appender as it needs to load it from system properties.\r\n\r\nAll you need to do:\r\n1. brew install ansible\r\n2. make setup-local-env (use password in 1password)","mergeCommitSha":"6acdaf6c36526ea51e8d64a7dd2b1099bd5ea62f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/156","title":"Add support for encrypted local and ci secrets","createdAt":"2022-01-28T00:06:14Z"}
{"state":"Merged","mergedAt":"2022-06-01T18:14:31Z","number":1560,"mergeCommitSha":"2e482939d4c35a390e0d54bdd2dc8eb6d1245afd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1560","title":"Try again","createdAt":"2022-06-01T18:01:16Z"}
{"state":"Merged","mergedAt":"2022-06-01T19:15:21Z","number":1562,"body":"Integrate real installations API instead of mock data.","mergeCommitSha":"ee4d748f9e0d61fcf83b8539dff7be3b86f6d4f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1562","title":"Use installations API","createdAt":"2022-06-01T18:57:22Z"}
{"state":"Merged","mergedAt":"2022-06-01T19:25:12Z","number":1563,"mergeCommitSha":"7cc58bd159e7d277606e2f38feba8d8408ecf7fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1563","title":"Fix cache path","createdAt":"2022-06-01T19:24:49Z"}
{"state":"Merged","mergedAt":"2022-06-01T20:06:41Z","number":1564,"mergeCommitSha":"c085bc5f496a86bb03b68c1cbb868aad0d63cb40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1564","title":"Cleanup","createdAt":"2022-06-01T19:53:42Z"}
{"state":"Merged","mergedAt":"2022-06-01T20:48:13Z","number":1565,"body":"- Added dummy error images \r\n- Modified ci-web workflow to include the new error assets and email assets in landing page deployments","mergeCommitSha":"1a979ccb58ecf3e83b7d9a744f90a56c3b526835","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1565","title":"adding dummy error images to static site bucket","createdAt":"2022-06-01T20:28:28Z"}
{"state":"Merged","mergedAt":"2022-06-02T06:16:20Z","number":1566,"body":"Many images in GitHub PR comments are HTML tags. Our proto spec doesn't have support for HTML elements, and our clients don't render HTML even if our spec did have support. \r\n\r\nThis PR will convert HTML blocks that contain only `<img>` tags to proto image inline elements so that clients can render them. Note that this is a lossy conversion: any size attributes in the original HTML will be dropped.\r\n\r\nIf the HTML block contains anything other than un-nested `<img>` tags, it will be converted to a text inline element. This is so that if we edit in unblocked and post back to GH it will be correctly rendered in GitHub.","mergeCommitSha":"e9c66ab8bc6cf845f148fef58a651327d47453cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1566","title":"Convert HTML img tags to proto image inline elements","createdAt":"2022-06-01T20:47:23Z"}
{"state":"Merged","mergedAt":"2022-06-01T22:03:51Z","number":1567,"body":"Fixed a mistake in directory paths. Deployed it to both Dev and Prod. Works as expected","mergeCommitSha":"b5f6ebf06d7620e3f96151cc26e987549bd1f718","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1567","title":"Fixed landing page and dashboard deployment jobs","createdAt":"2022-06-01T21:12:21Z"}
{"state":"Merged","mergedAt":"2022-06-02T22:04:08Z","number":1568,"mergeCommitSha":"2643a0fe134f0bbd7e5dc3406dac322db0f9cc8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1568","title":"Replace line breaks with spaces for thread titles","createdAt":"2022-06-01T21:21:17Z"}
{"state":"Merged","mergedAt":"2022-01-28T00:10:14Z","number":157,"mergeCommitSha":"0d2b252960d7f9b264f7e384507fde9852a886a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/157","title":"Drop local test database","createdAt":"2022-01-28T00:08:56Z"}
{"state":"Merged","mergedAt":"2022-06-02T15:24:11Z","number":1570,"body":"Refactor ValueCacheStream away from \"initialized\" to enums.\r\n\r\nNo logical change. Consolidates the approach and should hopefully clean up future code.","mergeCommitSha":"3a1eac7b62d0b76048000d018dc7fa27e4c3b767","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1570","title":"Refactor ValueCacheStream to use enums","createdAt":"2022-06-01T23:54:49Z"}
{"state":"Merged","mergedAt":"2022-06-02T01:34:59Z","number":1571,"body":"The approach I took is: anywhere we mutate `latestMessage` or `latestReadMessage` we now always update `isUnread` too.","mergeCommitSha":"05a2a993edd6343ba50c1a25aaaa7a6558944c4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1571","title":"Consistently update isUnread when updating latestMessage or latestReadMessage","createdAt":"2022-06-02T00:15:19Z"}
{"state":"Merged","mergedAt":"2022-06-02T03:04:02Z","number":1572,"body":"Pretty straighforward.\r\nTeam request -> link invite -> store payload in redis associated with a 30-day expiry id.\r\nUnauth'd client -> link -> get payload in redis associated with invite id (since it's 30 day limited, if not found, we abort)","mergeCommitSha":"5f7cb082041c9233a1785cd6cde3a3227a7dcd6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1572","title":"Add ability to do unauthorized team queries for invites","createdAt":"2022-06-02T00:24:42Z"}
{"state":"Merged","mergedAt":"2022-06-02T17:40:00Z","number":1574,"body":"Return custom placeholder images with 200 code for images when:\r\n- A request is not authorized (e.g image added through Unblocked rendered on Github PR UI)\r\n- 404 image not found (e.g asset was deleted from unblocked but there's a reference to it in a PR discussion\r\ne.g for 401 https://dev.getunblocked.com/assets/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/d27be7a4-02ea-4a79-a137-c90abc4a5c7b\r\n\r\n","mergeCommitSha":"567b78f3f9ce32cdefc6819815462efcd93e9d4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1574","title":"implement custom error images","createdAt":"2022-06-02T16:21:23Z"}
{"state":"Merged","mergedAt":"2022-01-28T00:20:39Z","number":158,"mergeCommitSha":"00dcbcc7a132f19c0d480d5e995b426f837c203a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/158","title":"Fix hacked user","createdAt":"2022-01-28T00:09:02Z"}
{"state":"Merged","mergedAt":"2022-01-28T00:19:04Z","number":159,"mergeCommitSha":"489d98abeda5e59e7726ea08c689cd33558710ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/159","title":"Update apiservice README for java","createdAt":"2022-01-28T00:15:50Z"}
{"state":"Merged","mergedAt":"2022-06-07T22:30:52Z","number":1598,"body":"Large PR due to refactor (sorry) to support better testability.","mergeCommitSha":"1eb3646f6d5d47424cf26c0749efd96c81441a96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1598","title":"PullRequestIngestionOnboardingService sends events to priority queue","createdAt":"2022-06-02T19:23:07Z"}
{"state":"Merged","mergedAt":"2022-06-02T21:09:06Z","number":1599,"body":"Add outline for the last onboarding tutorial step; will fill in each view consequently\r\n<img width=\"756\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171720286-543ad73a-87ea-4647-bee6-343566981374.png\">\r\n<img width=\"839\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171720312-d662cc8a-fad8-4916-bda4-a725729ff3a3.png\">\r\n<img width=\"763\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/171720322-f64cf5cf-3833-4b77-9629-fd377e481c29.png\">\r\n\r\n\r\nAlso some other polish:\r\n* Highlight sourcemark in file editor when the thread is opened\r\n* Close file editor when thread is archived\r\n* Don't clear the message editor on submit in the Start Discussion form -- if there's a lag, it looks inconsistent with the title field still populated; the webview panel will close so clearing it is moot\r\n* Clean up a bit of the listener logic ","mergeCommitSha":"634bb2216392e41063b4963b4beed2d99a1cc8e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1599","title":"[Onboarding] Outline for Stay Connected steps","createdAt":"2022-06-02T19:24:22Z"}
{"state":"Merged","mergedAt":"2021-12-23T20:39:03Z","number":16,"body":"Added support for react router.\r\n\r\nIncluded basic implementation of auth blocking for routing purposes.\r\nRefactored Navigator to be router friendly.\r\n\r\nTemporary \"Login\" page. No real auth. State handled in memory.\r\n<img width=\"1698\" alt=\"Screen Shot 2021-12-21 at 3 39 59 PM\" src=\"https://user-images.githubusercontent.com/1553313/*********-5b4c73fb-d074-41ff-bc75-99588f8c108f.png\">\r\n\r\nRunning `npm run start` will now properly render navigator with correct routing.\r\n<img width=\"1728\" alt=\"Screen Shot 2021-12-21 at 3 40 04 PM\" src=\"https://user-images.githubusercontent.com/1553313/*********-adbb0887-3a8b-4815-baf0-483e5639692c.png\">\r\n\r\n\r\n\r\n","mergeCommitSha":"8ad9862d1a4a8c980668e7463d35716329a715aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/16","title":"Integrate Router and Mocked Auth","createdAt":"2021-12-21T23:09:20Z"}
{"state":"Merged","mergedAt":"2022-01-28T17:30:33Z","number":160,"body":"Basically the same as https://github.com/Chapter2Inc/codeswell/pull/151 but for `getMessages`","mergeCommitSha":"aa3d04552f255e58d579f23a92181da9112182a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/160","title":"Hook up getMessages operation to the database","createdAt":"2022-01-28T04:15:15Z"}
{"state":"Merged","mergedAt":"2022-06-02T20:25:27Z","number":1601,"body":"More work needing to be done to place team icon on top.\r\n\r\n<img width=\"951\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/171723785-60b7af8b-217f-4bfe-8ed3-c63a202f038f.png\">\r\n","mergeCommitSha":"a7cda4548d74b0d75e453953b070057e64d1cbcc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1601","title":"AddTeamInvitePage","createdAt":"2022-06-02T19:35:21Z"}
{"state":"Merged","mergedAt":"2022-06-02T19:51:58Z","number":1602,"body":"If this doesn't work then we need another lambda for Origin response. ","mergeCommitSha":"a3f2489099c829007d6ddbeb05d22b48c129c54a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1602","title":"trying to see if cache busting headers work","createdAt":"2022-06-02T19:38:35Z"}
{"state":"Merged","mergedAt":"2022-06-02T21:35:51Z","number":1603,"body":"Ignore the fact that the settings button triggers it. That was just a convenience for dev purposes and has been reverted\r\n\r\n![CleanShot 2022-06-02 at 13 13 10](https://user-images.githubusercontent.com/858772/171730470-449ce2a9-d415-4f24-82a4-807f6c84dda1.gif)\r\n","mergeCommitSha":"1164b4562a69975e1ddf7fc19cdc4e1466b03177","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1603","title":"Take over the screen and show the hub like a boss","createdAt":"2022-06-02T20:17:12Z"}
{"state":"Merged","mergedAt":"2022-06-02T20:56:14Z","number":1604,"body":"We raised it to 10 seconds a while back because the SCM service was OOMing. We've made some improvements since, so I'd like to try dropping it down to see if it's better now.","mergeCommitSha":"5cf2f59c097796fbd155431285e84bc569349178","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1604","title":"Drop looping to 5 seconds","createdAt":"2022-06-02T20:42:33Z"}
{"state":"Merged","mergedAt":"2022-06-02T21:26:23Z","number":1605,"body":"fixes https://github.com/NextChapterSoftware/unblocked/issues/984","mergeCommitSha":"4f8d7ba2056df5825494ffd07cd134a16da706e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1605","title":"HTTP clients should retry","createdAt":"2022-06-02T21:01:43Z"}
{"state":"Merged","mergedAt":"2022-06-02T23:01:39Z","number":1606,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1654203899255739","mergeCommitSha":"b61a2185cb9a98c4a1f13e37222510408a3f7e3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1606","title":"HTTP clients should expect success throwing client/server exceptions as necessary","createdAt":"2022-06-02T21:21:32Z"}
{"state":"Merged","mergedAt":"2022-01-28T20:49:37Z","number":161,"body":"✨  As seen on Friday demo ✨\r\n\r\n<img width=\"1156\" alt=\"Screen Shot 2022-01-28 at 10 39 02\" src=\"https://user-images.githubusercontent.com/1798345/151603075-b99f5cd5-093e-462e-a7a5-2483460d101c.png\">\r\n ","mergeCommitSha":"63ab214bb3309b15b24d5182ffbe33b7fd685f18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/161","title":"API Reference Documentation","createdAt":"2022-01-28T17:52:51Z"}
{"state":"Merged","mergedAt":"2022-06-08T20:15:55Z","number":1610,"body":"New Install sidebar for VSCode.\r\n\r\nEmpty Repo State is *not* done yet: Should be related to https://github.com/NextChapterSoftware/unblocked/issues/913\r\n\r\nAlso fixes UNB-103\r\n\r\n<img width=\"684\" alt=\"CleanShot 2022-06-02 at 14 30 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/171741796-27fb7f7d-61b3-4234-b9e6-161cb2912fdb.png\">\r\n\r\n","mergeCommitSha":"c16f3c587c7e3db6d1f1ff245a9a6dfd664f5cec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1610","title":"UNB-169 VSCode Install sidebar ","createdAt":"2022-06-02T21:35:33Z"}
{"state":"Merged","mergedAt":"2022-06-08T02:19:49Z","number":1612,"body":"Needs client-side work","mergeCommitSha":"00348cda7293f70250fefb733737218142ee73f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1612","title":"Clip thread title client-side","createdAt":"2022-06-02T22:17:57Z"}
{"state":"Closed","mergedAt":null,"number":1613,"mergeCommitSha":"a597f30a22c8893eb1b6c8b817eca6161b42511b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1613","title":"fix dev and prod configs","createdAt":"2022-06-02T22:23:48Z"}
{"state":"Merged","mergedAt":"2022-06-02T22:25:49Z","number":1614,"mergeCommitSha":"7ac47efa4e98230dddc54a2694e6d245f8df8264","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1614","title":"fix stuff","createdAt":"2022-06-02T22:25:28Z"}
{"state":"Merged","mergedAt":"2022-06-02T22:31:03Z","number":1615,"mergeCommitSha":"3cd84257218db7ad423d1ac9a6d8777a9944b3c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1615","title":"Remove stale entires","createdAt":"2022-06-02T22:28:08Z"}
{"state":"Merged","mergedAt":"2022-06-03T16:06:13Z","number":1616,"body":"Don't merge this until Jeff is ready to roll","mergeCommitSha":"74d75d306c6d7a350a4eece9214422c34a7938f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1616","title":"Make onboarding shiny and chrome","createdAt":"2022-06-02T22:31:21Z"}
{"state":"Merged","mergedAt":"2022-06-02T22:57:48Z","number":1617,"body":":)","mergeCommitSha":"a081be6112c688b258b9f12adb7584b9e5cb3364","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1617","title":"Time for a clean shave","createdAt":"2022-06-02T22:44:50Z"}
{"state":"Merged","mergedAt":"2022-06-02T22:59:06Z","number":1618,"body":"<img width=\"817\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/171750752-03a3e214-cb7b-4e2c-a24d-1a41d88cb9c1.png\">\r\n","mergeCommitSha":"74280d857fb8b8af1223b429806fd270880431bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1618","title":"AddTeamIcon","createdAt":"2022-06-02T22:49:15Z"}
{"state":"Merged","mergedAt":"2022-06-02T22:50:25Z","number":1619,"mergeCommitSha":"a13c3e46a934ad77f9095ffa44366eb7bbe71308","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1619","title":"Accidentally broke post-login popup","createdAt":"2022-06-02T22:50:14Z"}
{"state":"Merged","mergedAt":"2022-01-28T20:57:07Z","number":162,"body":"For languages that support UUID, these properties and parameters will be automatically converted to UUIDs, avoiding the need to convert between string and UUID everywhere when referring these.\r\n\r\nFor languages that don't support UUID (like javascript) these stay as strings.\r\n\r\nhttps://stackoverflow.com/questions/50204588/how-to-define-uuid-property-in-json-schema-and-open-api-oas","mergeCommitSha":"1f064c3b3fbac6be12764ee77c745255e62739f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/162","title":"Add \"format: uuid\" to properties, path parameters, and query parameters gg","createdAt":"2022-01-28T20:38:16Z"}
{"state":"Closed","mergedAt":null,"number":1620,"body":"<img width=\"322\" alt=\"CleanShot 2022-06-02 at 16 10 38@2x\" src=\"https://user-images.githubusercontent.com/858772/171752883-9bd79361-b761-4cf5-8b23-cb0f4b6232f5.png\">\r\n\r\nPotentially useful for demos. Not actually wired up yet because we need another API to do this.\r\n\r\nThis is a secret menu item only accessible with an `option-command-control click`","mergeCommitSha":"c4629dbc49e367d814070e5ebd965617c57eb2a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1620","title":"Reset onboarding menu item","createdAt":"2022-06-02T23:12:12Z"}
{"state":"Merged","mergedAt":"2022-06-02T23:55:11Z","number":1621,"mergeCommitSha":"10c7edcda93112dcdc47ed68408efb678a67488e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1621","title":"Fix loading landing environment","createdAt":"2022-06-02T23:41:45Z"}
{"state":"Merged","mergedAt":"2022-06-07T00:11:08Z","number":1622,"body":"Attempt to fix issues with retrying refresh auth.\r\n\r\nDifficult to reproduce as there are potentially CORS issues causing this...\r\n","mergeCommitSha":"c6d4dbbbc78c4f1a557c8cfd365aae78526852a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1622","title":"Retry Refresh Auth UNB-166","createdAt":"2022-06-02T23:50:49Z"}
{"state":"Merged","mergedAt":"2022-06-03T16:04:57Z","number":1623,"body":"Update persons model with \"seen tutorial\" on Tutorial load.","mergeCommitSha":"a8aa9454ac1ed3bbd40ab1e4064a899aa9ed9f62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1623","title":"Update Person's \"Seen tutorial\" in VSCode","createdAt":"2022-06-03T00:08:40Z"}
{"state":"Merged","mergedAt":"2022-06-03T01:00:05Z","number":1624,"body":"related https://github.com/NextChapterSoftware/unblocked/pull/1620, https://github.com/NextChapterSoftware/unblocked/pull/1623","mergeCommitSha":"1250aaf308b26736233ab2c71c8de65b03cc398d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1624","title":"Reset tutorial onbaording from admin web","createdAt":"2022-06-03T00:44:27Z"}
{"state":"Merged","mergedAt":"2022-06-03T01:33:39Z","number":1625,"mergeCommitSha":"2aaf78939a58a0091b4edf828a16a20cfd36cc00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1625","title":"Add basic welcome email handler","createdAt":"2022-06-03T01:19:15Z"}
{"state":"Merged","mergedAt":"2022-06-03T02:18:46Z","number":1626,"mergeCommitSha":"abbdc03d31ed3f1297a8abfbb2462f0be722a04e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1626","title":"Clean up more stuff","createdAt":"2022-06-03T02:05:40Z"}
{"state":"Merged","mergedAt":"2022-06-03T04:14:08Z","number":1627,"mergeCommitSha":"fb9260b6619bd04b5b2113b40094ee226f97cf26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1627","title":"Find OnboardingModel from repo url","createdAt":"2022-06-03T03:39:22Z"}
{"state":"Merged","mergedAt":"2022-06-03T03:56:13Z","number":1628,"mergeCommitSha":"7e104d43fa8b801489f0a619aee1467cb3ccae04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1628","title":"Add team avatar urls","createdAt":"2022-06-03T03:44:45Z"}
{"state":"Merged","mergedAt":"2022-06-03T04:57:54Z","number":1629,"mergeCommitSha":"ad1ce67e5d3474339be2ef0cace00b154a7615cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1629","title":"Try again","createdAt":"2022-06-03T04:57:49Z"}
{"state":"Merged","mergedAt":"2022-01-28T20:56:16Z","number":163,"body":"- Updated Dev and SecOps config with new hostedZones \r\n- Deployed changes to Dev and generated new wildcard cert\r\n- Updated dns name prefix in `apiservice` helm chart \r\n- Updated Dev's wildcard cert ARN in helm values file \r\n- Updated EKS installation instructions to use new domain\r\nThese changes have been deployed and last piece of the puzzle is to roll out apiservice with the new domain and cert. ","mergeCommitSha":"23c3df4d878a4d61eb7d90d1411d1df560b93639","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/163","title":"Switch domain getunblocked","createdAt":"2022-01-28T20:52:54Z"}
{"state":"Closed","mergedAt":null,"number":1630,"mergeCommitSha":"397ac8d3c6126cd600857266298ea3e211acee18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1630","title":"build notification","createdAt":"2022-06-03T05:00:21Z"}
{"state":"Closed","mergedAt":null,"number":1631,"mergeCommitSha":"aeaa272a32ce1802b6cd7d15ab093dfac4458b33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1631","title":"Update ci","createdAt":"2022-06-03T07:04:08Z"}
{"state":"Merged","mergedAt":"2022-06-03T10:32:49Z","number":1632,"body":"- Revert \"Try again (#1629)\"\r\n- Revert \"Add team avatar urls (#1628)\"\r\n- Update ci\r\n","mergeCommitSha":"29bd1d7c9f21d9a9f34c830d1d4afc2f0b78c9d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1632","title":"TestEmail2","createdAt":"2022-06-03T07:16:09Z"}
{"state":"Merged","mergedAt":"2022-06-03T11:06:26Z","number":1633,"mergeCommitSha":"6b3774742aea866c721e7bdf25dc1ed2d1d04405","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1633","title":"update","createdAt":"2022-06-03T11:06:20Z"}
{"state":"Closed","mergedAt":null,"number":1635,"mergeCommitSha":"a5bbf0bc6ad79c559e21ad7e9d1bbcf59b046eed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1635","title":"[IGNORE] Testing ingestion of open PRs","createdAt":"2022-06-03T18:21:12Z"}
{"state":"Merged","mergedAt":"2022-06-03T18:39:44Z","number":1636,"mergeCommitSha":"8972395fe20034dd3dc0ef147d2739a1216bc570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1636","title":"Fix look of landing page on mobile","createdAt":"2022-06-03T18:25:49Z"}
{"state":"Merged","mergedAt":"2022-06-03T21:55:59Z","number":1637,"body":"This is more accurate since `line` and `startLine` can change, but we use it for processing the `diffHunk` which does not change.","mergeCommitSha":"a8eea3d2abeb7a95400ff7e1c655814e07b5a1cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1637","title":"Use originalLine and originalStartLine for the source snippet","createdAt":"2022-06-03T19:05:45Z"}
{"state":"Merged","mergedAt":"2022-06-03T22:07:24Z","number":1638,"body":"Add API to trigger hub highlight.\r\n\r\n@kaych Please add this when you work on the relevant step in the tutorial?","mergeCommitSha":"c9893001b11b79b36ed2d92f6392a47cff92378e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1638","title":"Add hub highlight API","createdAt":"2022-06-03T19:09:06Z"}
{"state":"Merged","mergedAt":"2022-06-03T21:41:33Z","number":1639,"mergeCommitSha":"11df65190100a4020e0e15508bcacc031fe5016c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1639","title":"Update","createdAt":"2022-06-03T20:54:23Z"}
{"state":"Merged","mergedAt":"2022-01-28T23:22:43Z","number":164,"body":"* Add prettier, ESLint, and test support to shared web components\r\n* Add CI run to shared web components","mergeCommitSha":"a86b3452ac1209f8b5aff49136bded5c526865a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/164","title":"Add CI run to shared web components","createdAt":"2022-01-28T21:50:09Z"}
{"state":"Merged","mergedAt":"2022-06-03T21:28:50Z","number":1640,"mergeCommitSha":"5f07715661ca99c48069a00264ff153ae12f50cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1640","title":"Update hasSeenTutorial flag","createdAt":"2022-06-03T21:02:47Z"}
{"state":"Merged","mergedAt":"2022-06-03T22:01:47Z","number":1641,"mergeCommitSha":"95d486a53e54575d5e393c5c32387900d7ff8448","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1641","title":"Return data objects from PersonStore","createdAt":"2022-06-03T21:43:32Z"}
{"state":"Merged","mergedAt":"2022-06-07T16:13:17Z","number":1642,"body":"More permanent fix for the temporary fix in this PR: https://github.com/NextChapterSoftware/unblocked/pull/1535\r\n\r\nEssentially, instead of unsubscribing and resubscribing to the thread stream every time the sourcemarks change, we can just reuse the cached thread data we had before.","mergeCommitSha":"b497083fb7404d3b098a7aaf846c8279a60e7c29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1642","title":"Don't resubscribe to thread changes","createdAt":"2022-06-03T21:57:15Z"}
{"state":"Merged","mergedAt":"2022-06-03T23:07:03Z","number":1643,"body":"SourceMark gutter icons in VSCode were flickering often.  The source is unexpected:\r\n\r\n1. Every 30s or so we refresh our auth token\r\n2. Every time we refresh our auth token, we refresh the TeamStore (fetch a new set of Teams)\r\n3. Whenever the set of Teams changes, we refresh the RepoStore (to resolve workspace repos)\r\n4. Whenever the set of Repos changes, we re-load all our SourceMark information for all open files.\r\n \r\nIt is expected that 3 and 4 will be very infrequent (never, really) -- the mechanism is mainly in place so that we correctly load all our SourceMarks only after we've authed, loaded teams, and resolved repos.  However, the `getTeams` API call is now returning teams with an invite URL, which changes on every call, so every time our token refreshed, all of the above would trigger.\r\n\r\nThe fix is to fix the Teams stream so only update when the actual set of teams change.","mergeCommitSha":"0d693280e30b3d5dbc24d590cffec6e05a97d94e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1643","title":"Fix flickering SourceMark gutter icons","createdAt":"2022-06-03T22:41:49Z"}
{"state":"Merged","mergedAt":"2022-06-04T01:30:51Z","number":1644,"mergeCommitSha":"137e1d79fa31441a8ce87d69295a6306cda4c97e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1644","title":"fix stuff","createdAt":"2022-06-04T00:16:39Z"}
{"state":"Merged","mergedAt":"2022-06-04T05:04:50Z","number":1645,"body":"Merry Christmas @benedict-jw !!\r\n\r\nHide scrollbars in VSCode webview UIs unless the mouse is hovering overtop.\r\n\r\nFor the `#root` item, the scrollbars should fade out.\r\n\r\nFor non-root scrollable items, there is no fadeout, but they should automatically be hidden.  There's no way to get this to work automatically.  We can add special styling as a sub-element later if we want to.\r\n\r\n![CleanShot 2022-06-03 at 20 41 52](https://user-images.githubusercontent.com/2133518/171980614-f7d7a30f-740e-4bf8-a43d-c3192eeaf5b4.gif)\r\n","mergeCommitSha":"d63b8b828240c5fa57112fce7d7f2ce9d0a49587","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1645","title":"Hide VSCode scrollbars unless mouse is over element","createdAt":"2022-06-04T03:42:20Z"}
{"state":"Merged","mergedAt":"2022-06-08T23:39:09Z","number":1646,"body":"Completes UNB-196\r\n\r\nThis turned into a bit of a beast because in order to populate the provider extension, the repo url is required. The common point of action was the `ThreadInfo` object, so I added the repo in the decorator.\r\n\r\n\r\nTo call attention to a few things:\r\n- The messages API seems to have allowed for messages without source marks and threads without messages. I don't think these are legal states? The `ThreadInfo` decorator certainly implies that.\r\n- I modified some of the message API tests to work within the bounds of legal states","mergeCommitSha":"aae0988f2251843c30f23289e3e0095013fd027c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1646","title":"Adds web extension links to threads and messages","createdAt":"2022-06-04T17:24:49Z"}
{"state":"Merged","mergedAt":"2022-06-05T05:18:47Z","number":1647,"mergeCommitSha":"63079f79f233eb6782a3e2fb4a79f43f50b9e691","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1647","title":"Fix codesigning in ci","createdAt":"2022-06-05T05:05:23Z"}
{"state":"Merged","mergedAt":"2022-06-05T05:43:28Z","number":1648,"mergeCommitSha":"ff1b47315d5ced31a35df5582079a5a174fb6d84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1648","title":"Missed keychain setup in installer script","createdAt":"2022-06-05T05:43:05Z"}
{"state":"Closed","mergedAt":null,"number":1649,"mergeCommitSha":"7d02a7726c9d6443d79c7125fb0215a6a3b35197","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1649","title":"[WIP] Encrypt user secrets stored in the DB","createdAt":"2022-06-06T00:57:05Z"}
{"state":"Merged","mergedAt":"2022-01-28T22:16:56Z","number":165,"body":"Just DRYing up the code as suggested by the lovely @rasharab https://github.com/Chapter2Inc/codeswell/pull/160#discussion_r794708698","mergeCommitSha":"c1555dbd7de80ed277bde4ba897d89cee801e20d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/165","title":"Create TestApiClient to consolidate API calls in unit tests","createdAt":"2022-01-28T21:53:03Z"}
{"state":"Merged","mergedAt":"2022-06-07T21:53:47Z","number":1650,"body":"Adds logic to encrypt the GitHub user-to-server token that we use for posting messages from Unblocked to GitHub. This uses asymmetric encryption, where the API service encrypts the user token with the public key before storing it in the database. The SCM service is the only service with access to the private key, which will be used to decrypt the token.","mergeCommitSha":"cd4d88904496cefad4292e4458c5f51768d57b33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1650","title":"Encrypt user secrets stored in the DB","createdAt":"2022-06-06T16:15:35Z"}
{"state":"Merged","mergedAt":"2022-06-07T16:06:41Z","number":1651,"body":"- \"along side\" is always \"alongside\"\n  https://prowritingaid.com/grammar/1000012/Should-I-use-alongside-or-along-side-\n- avoid use passive voice","mergeCommitSha":"4b165d96e77b12d14d60e7810e55fec35bfec621","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1651","title":"Hub copy","createdAt":"2022-06-06T16:26:54Z"}
{"state":"Merged","mergedAt":"2022-06-07T00:03:37Z","number":1652,"body":"<img width=\"783\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172229118-052e7b41-8435-456f-b9e5-6630f1b10a28.png\">\r\n<img width=\"699\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172230121-196f4890-3467-4aa3-9318-8cd5fe43d4b3.png\">\r\n<img width=\"693\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172229186-36aea7eb-e831-4638-af6b-ac5fe33c4fb2.png\">\r\n\r\n* Add CopyInput component for copy fields\r\n* Add email list validation to TextInput component \r\n* Combine the TeamStore stream into the wizard stream listener for team data \r\n* Fetch a list of repo collaborators to populate the team list; add custom sort to order the emails by occurrence and filter common domains like gmail and hotmail to the end of the list ","mergeCommitSha":"e749459ed0a29f427b525ff2d72c29e8dc750497","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1652","title":"[Onboarding] Invite page ","createdAt":"2022-06-06T19:05:33Z"}
{"state":"Merged","mergedAt":"2022-06-06T21:44:04Z","number":1653,"body":"<img width=\"770\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/172251994-9fc72c8c-eb8e-4d99-b6b7-8014de3d022f.png\">\r\n\r\n\r\n<img width=\"799\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/172252314-58b5a566-56b7-40f1-b649-ee3765d225ad.png\">\r\n","mergeCommitSha":"9ca0e7defcc445a2a928056fcea99c3633cd9c94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1653","title":"Add email icon support","createdAt":"2022-06-06T21:21:52Z"}
{"state":"Merged","mergedAt":"2022-06-06T22:14:18Z","number":1654,"mergeCommitSha":"5bff05f45058e333e7c6767aeec48de4c9e5a6ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1654","title":"cleanup","createdAt":"2022-06-06T22:14:07Z"}
{"state":"Merged","mergedAt":"2022-06-06T22:20:12Z","number":1655,"mergeCommitSha":"af999c2e7669dc709b5711cf7745bfa452712f84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1655","title":"update","createdAt":"2022-06-06T22:20:01Z"}
{"state":"Merged","mergedAt":"2022-06-07T01:37:52Z","number":1656,"mergeCommitSha":"708c307127bcafabd892dec4e50ebbd5e146d035","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1656","title":"test noficiation","createdAt":"2022-06-07T00:08:01Z"}
{"state":"Merged","mergedAt":"2022-06-07T22:58:53Z","number":1657,"body":"Redirects user from Dashboard to GH if web extension exists.\r\n\r\nChrome uses the DelcarativeNetRequest to instantly redirect.\r\n\r\nOf course, Safari doesn't support redirect using DelcarativeNetRequest yet.\r\nTherefore, using a content script on load to redirect user. This is *slightly slower* but still acceptable imo (as we have no other way atm...)\r\n\r\nChrome:\r\n\r\nhttps://user-images.githubusercontent.com/1553313/172268831-b9a3b9dd-76c9-40df-b552-2532d2ef6baf.mp4\r\n\r\nSafari:\r\n\r\nhttps://user-images.githubusercontent.com/1553313/172268991-d77adfb1-8a25-492c-a40d-a4d51878671c.mp4\r\n\r\n\r\nRequires https://linear.app/unblocked/issue/UNB-196/web-extension-threadmessage-links\r\n\r\n","mergeCommitSha":"2401ecf77e558c61674c1178bd1b4f5d6284b79f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1657","title":"Redirect from Dashboard to GH using web extension","createdAt":"2022-06-07T00:08:05Z"}
{"state":"Merged","mergedAt":"2022-06-07T17:11:49Z","number":1658,"body":"The background clipping and background color styles were causing some weird weird bugs with the UI rendering (see below):\r\n<img width=\"675\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172269297-5f44f477-df4d-4087-9ee7-efd8d3474fa1.png\">\r\n\r\nRemove them for now. NOTE: this removes the transition fadeout of the scrollbar when hovering away.\r\n","mergeCommitSha":"37fea65ee1cad786a6978073078759ae61adb72d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1658","title":"Remove background transition of scrollbar for now","createdAt":"2022-06-07T00:11:50Z"}
{"state":"Merged","mergedAt":"2022-06-07T22:34:27Z","number":1659,"body":"Fixes https://linear.app/unblocked/issue/UNB-204/open-prs-api-spec\r\n\r\nNew APIs:\r\n- getPullRequest\r\n- getPullRequests\r\n- getPullRequestThreads\r\n- getThreadsForPullRequests\r\n\r\nModified APIs:\r\n- getThreadsForMe","mergeCommitSha":"81b1f01c29a0558d991c6a87fb3b37a6b8767850","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1659","title":"Pull Requests API changes to support Open PR tree-view","createdAt":"2022-06-07T01:19:35Z"}
{"state":"Merged","mergedAt":"2022-01-28T22:20:10Z","number":166,"body":"Kubernetes secrets will be loaded into pods via environment variables\r\n\r\n1. We add a playbook to push kubernetes secrets that are encrypted by ansible over.\r\n2. We modify helm charts to load secrets dynamically","mergeCommitSha":"7b6aca9575cfaa8b595abbbb2ec18602836cf16c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/166","title":"Add ability to push secrets to kubernetes","createdAt":"2022-01-28T22:13:30Z"}
{"state":"Merged","mergedAt":"2022-06-07T03:37:08Z","number":1660,"body":"ContributorRow ids are not guaranteed to be `contributor.id`. Apparently, they can be set to `contributor.teamMemberId` or `contributor.email` and finally `contributor.id`\r\nThe problem is that this amorphous id is what is passed to `CreateKnowledgeCommand.toggleContributor`.\r\n\r\n***ContributorsList.tsx***\r\n```\r\n        <div className=\"contributors_section\">\r\n            {addingNewContributor && addContributorRow}\r\n            {contributors.map((contributor) => {\r\n                const id = contributor.teamMemberId ?? contributor.email ?? contributor.id;\r\n                const isSelected = selectedContributorIds.some((selectedContributorId) => selectedContributorId === id);\r\n\r\n                return (\r\n                    <ContributorRow\r\n                        key={id}\r\n                        id={id}\r\n                        contributor={contributor}\r\n                        isSelected={isSelected}\r\n                        onClick={onSelectContributor}\r\n                    />\r\n                );\r\n            })}\r\n        </div>\r\n    );\r\n```\r\n\r\nThat leads to a bug whereby it only searches for participants via `contributor.id` rather than the triple (`contributor.teamMemberId`, `contributor.email`, `contributor.id`).\r\n\r\n**CreateKnowledgeCommand.createDiscussion**\r\n```\r\n            const _participants = ArrayUtils.compact(\r\n                [...currentlySelectedContributorIds].map((id) =>\r\n                    currentContributors.find(\r\n                        (contributor) => contributor.id === id\r\n                    )\r\n                )\r\n            );\r\n```\r\n\r\n**QUESTION:**\r\n1. Why are we representing `ContributorRow` ids by anything else but the `contributor.id` field?\r\n2. If we want to maintain this behaviour, we should have some sort of centralized encapsulation of this triple of potential ids. This is very likely going to lead to future bugs.","mergeCommitSha":"5b600b0f1a24beb2bf405111cb998fd257e419ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1660","title":"[BUG FIX] Selected contributor emails were not being sent through to createThread Api.","createdAt":"2022-06-07T01:41:11Z"}
{"state":"Merged","mergedAt":"2022-06-08T02:45:09Z","number":1661,"body":"## Xcode Builds for the Hub\r\n\r\nWhen reviewing this PR, ignore all the code in the `generatedApi` and `GRPC` directories\r\n\r\nThis PR has a few different components:\r\n1. Necessary project settings changes to support building in Xcode Cloud (security policy, app category)\r\n2. `ci_scripts` directory rename to work with Xcode Cloud\r\n3. Introduction of Swift Codegen Diff action, which runs on a linux worker and compiles protoc-swift-gen from source (and subsequently our GRPC codegen for Hub IPC) followed by a diff of checked in source code\r\n\r\nWe were able to get all the sourcecode gen working in Xcode Cloud, but decided the economics made no sense for frequent builds like PR builds and merge builds. To generate the API and GRPC code in Xcode Cloud, we first needed to run a full `brew update` and then download and install `openjdk`. This added an additional 8 minutes of build time (to a base of 4 minutes), and ripped down over 1GB of data from the network. Yikes! Add another 2 minutes to actually generate the source and we were getting close to github actions for overall build times.\r\n\r\nInstead, we have source code checked in, which makes build times blazing fast in Xcode Cloud (4 minutes on a bad day). To deal with API consistency issues, we introduced a new GitHub action called `Verify Swift Codegen Changes`, which will fail unless the generated source in CI matches the checked-in source. Solving this is a simple matter of running `make setup` from `/video-app/macos`, then submit. \r\n\r\nThe installer builds still run in GitHub until we can figure out the coupling problem with VSCode, but given our success installing java etc, it might be possible to run and package the whole thing on an Xcode Cloud VM, which would be an order of magnitude cheaper.\r\n\r\nSpeaking of economics, here's how much we will save:\r\n\r\n### Github Actions\r\n- Current github actions usage: ~12,000 minutes per month (yes, for real. download the usage report :))\r\n- number of builds ~250\r\n- Average github actions build time: 45 minutes. This is nuts - if we imagine a more reasonable average build time of 30 minutes then we were using ~8000 minutes per month\r\n- Price per minute: $0.08\r\n- Total cost per month: $640\r\n\r\n\r\n### Xcode Cloud\r\n- Average build time: 4 minutes\r\n- Total build minutes = 250 * 4 = 1000\r\n- Total build hours per month = 16\r\n- Cost = Free\r\n\r\n### Summary\r\nNot just the cost of builds, but the turnaround time of builds makes this a complete no-brainer. We should _absolutely_ move the installer builds to Xcode Cloud as well\r\n\r\n\r\n","mergeCommitSha":"f2eddfb8ef4eaa42eef3bf6ddd18180584ef0303","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1661","title":"Build in xcodecloud","createdAt":"2022-06-07T01:56:30Z"}
{"state":"Merged","mergedAt":"2022-06-08T15:58:25Z","number":1662,"body":"- https://linear.app/unblocked/issue/UNB-154/installationsapi-not-setting-displayname-correctly\r\n- https://linear.app/unblocked/issue/UNB-189/installationsapi-returning-500\r\n","mergeCommitSha":"2809c8ace1130aee9e80b7653d5e0b7a81d7b908","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1662","title":"InstallationsAPI not setting displayName correctly","createdAt":"2022-06-07T15:26:36Z"}
{"state":"Merged","mergedAt":"2022-06-07T17:36:41Z","number":1663,"body":"- Added a new SCM only secret \r\n- Deployed the secret as a placeholder to Kubernetes. I'll need to update it with the real value once this change lands. \r\n- Modified SCM service helm chart to include the new secret and set env vars for it. ","mergeCommitSha":"77b63e8f64b271fd9c316a803295f3d98f861df6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1663","title":"add scm only secret for private key","createdAt":"2022-06-07T17:10:55Z"}
{"state":"Merged","mergedAt":"2022-06-07T18:06:02Z","number":1664,"body":"<img width=\"803\" alt=\"CleanShot 2022-06-07 at 10 43 13@2x\" src=\"https://user-images.githubusercontent.com/1553313/172448081-b418e804-1f1b-4b85-bbe7-fe1c87f6415c.png\">\r\n","mergeCommitSha":"8a3695b1af2a01db305979ad23c21964ced52413","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1664","title":"Fix snippet rendering in web extension","createdAt":"2022-06-07T17:42:58Z"}
{"state":"Merged","mergedAt":"2022-06-07T18:27:41Z","number":1665,"body":"Deployment was stuck because of a typo in the secret name. Deployed this secret and now pods are successfully rolling over. ","mergeCommitSha":"632d3b6417886283c88611ce69ea517b753c6875","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1665","title":"fixed a typo in scm secret name","createdAt":"2022-06-07T18:07:51Z"}
{"state":"Merged","mergedAt":"2022-06-07T20:18:23Z","number":1666,"body":"We are now allowing for service-level secrets.\r\n\r\nTo do that, we need to segregate global configuration from service specific configuration.\r\n","mergeCommitSha":"0cbf613781d128861d22cd8ca4f075bb3500b195","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1666","title":"Services should allow for configurations tied to them","createdAt":"2022-06-07T18:18:06Z"}
{"state":"Merged","mergedAt":"2022-06-07T19:25:14Z","number":1667,"mergeCommitSha":"46ced3e983c4dc175e3286788d3b090457751ad0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1667","title":"Fix up team urls","createdAt":"2022-06-07T18:42:00Z"}
{"state":"Merged","mergedAt":"2022-06-07T22:18:23Z","number":1668,"body":"Also, enable auto newlines (was driving me up the fucking wall)","mergeCommitSha":"d80987b7b06ace726e9ca0329214210252b7fd02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1668","title":"Update ktlint","createdAt":"2022-06-07T21:13:07Z"}
{"state":"Merged","mergedAt":"2022-06-08T19:31:41Z","number":1669,"mergeCommitSha":"978e73f7ce68c8f7a66ad40df90a2f66c4810e40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1669","title":"Repo Resolution is case sensitive","createdAt":"2022-06-07T21:15:02Z"}
{"state":"Merged","mergedAt":"2022-01-28T22:37:15Z","number":167,"body":"Update secret to use stringData rather than data so we do not have to base64 encode value.","mergeCommitSha":"fad428f1e5cf87131b12b773e5ce5a400f1b260d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/167","title":"update","createdAt":"2022-01-28T22:22:01Z"}
{"state":"Merged","mergedAt":"2022-06-07T21:58:04Z","number":1670,"body":"/\r\n<img width=\"1903\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172485222-cb9d0e4e-a46d-4bad-ae2c-3cc19afc7ba6.png\">\r\n\r\n/download\r\n<img width=\"1913\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172485256-4cc20b78-08a8-42b0-99d8-bed4c318333f.png\">\r\n\r\n/extensions\r\n<img width=\"1914\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172485301-bc0abba1-3d3d-4c58-b4f0-9e186e466d08.png\">\r\n\r\n/team/:teamID/invite/:inviteID\r\n<img width=\"1911\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172485380-40f82306-d0f0-4ba6-b9ac-0146eb652729.png\">\r\n","mergeCommitSha":"cdd8fb355a0659e2e87f4d77f8e883d5a089ffc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1670","title":"Update landing pages, add new routes","createdAt":"2022-06-07T21:25:38Z"}
{"state":"Merged","mergedAt":"2022-06-07T23:57:13Z","number":1671,"mergeCommitSha":"8a403130912c1e361529679d2445d89be10ffe71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1671","title":"Admin Web: Show tutorial state","createdAt":"2022-06-07T21:40:54Z"}
{"state":"Merged","mergedAt":"2022-06-08T19:08:12Z","number":1672,"mergeCommitSha":"0cba3f2ec8e71fdf5a3e089dd88d282597ac39e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1672","title":"Case insensitive findRepo API matching","createdAt":"2022-06-07T21:54:12Z"}
{"state":"Merged","mergedAt":"2022-06-07T22:10:49Z","number":1673,"body":"Recommended way is to use npm-check-updates to update dependnecies.\r\n\r\nDid a `ckd diff`. There's some cdk fields that are going to be deprecated that I will have to fix up, and some s3 permissions that are added, but this looks to be fine!\r\n\r\n\r\nApparently, adds some s3 permissions by default.\r\n```\r\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#fromToken is deprecated.\r\n  Use `cdk.SecretValue` instead.\r\n  This API will be removed in the next major release.\r\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretProps#secretStringBeta1 is deprecated.\r\n  Use `secretStringValue` instead.\r\n  This API will be removed in the next major release.\r\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#secretValue is deprecated.\r\n  Use `cdk.SecretValue` instead.\r\n  This API will be removed in the next major release.\r\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#fromToken is deprecated.\r\n  Use `cdk.SecretValue` instead.\r\n  This API will be removed in the next major release.\r\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretProps#secretStringBeta1 is deprecated.\r\n  Use `secretStringValue` instead.\r\n  This API will be removed in the next major release.\r\n[WARNING] aws-cdk-lib.aws_secretsmanager.SecretStringValueBeta1#secretValue is deprecated.\r\n  Use `cdk.SecretValue` instead.\r\n  This API will be removed in the next major release.\r\nStack AcmStack\r\nThere were no differences\r\nStack CustomerAssetsStack\r\nThere were no differences\r\nStack DnsStack\r\nThere were no differences\r\nStack EcrMirrorStack\r\nThere were no differences\r\nStack EksVPNConfigStack\r\nThere were no differences\r\nStack IamStack\r\nThere were no differences\r\nStack NetworkStack\r\nThere were no differences\r\nStack SqsStack\r\nThere were no differences\r\nStack StreamingAssetsStack\r\nIAM Statement Changes\r\n┌───┬─────────────────────────────────────┬────────┬──────────────────────────────────────────────────────────────────────────────────────────────────────────────┬───────────────────────────────┬───────────┐\r\n│   │ Resource                            │ Effect │ Action                                                                                                       │ Principal                     │ Condition │\r\n├───┼─────────────────────────────────────┼────────┼──────────────────────────────────────────────────────────────────────────────────────────────────────────────┼───────────────────────────────┼───────────┤\r\n│ - │ ${AgoraStreamingAssetsBucket.Arn}/* │ Allow  │ s3:Abort*                                                                                                    │ AWS:${AgoraStreamingUploader} │           │\r\n│   │                                     │        │ s3:PutObject                                                                                                 │                               │           │\r\n├───┼─────────────────────────────────────┼────────┼──────────────────────────────────────────────────────────────────────────────────────────────────────────────┼───────────────────────────────┼───────────┤\r\n│ + │ ${AgoraStreamingAssetsBucket.Arn}/* │ Allow  │ s3:Abort*                                                                                                    │ AWS:${AgoraStreamingUploader} │           │\r\n│   │                                     │        │ s3:PutObject                                                                                                 │                               │           │\r\n│   │                                     │        │ s3:PutObjectLegalHold                                                                                        │                               │           │\r\n│   │                                     │        │ s3:PutObjectRetention                                                                                        │                               │           │\r\n│   │                                     │        │ s3:PutObjectTagging                                                                                          │                               │           │\r\n│   │                                     │        │ s3:PutObjectVersionTagging                                                                                   │                               │           │\r\n└───┴─────────────────────────────────────┴────────┴──────────────────────────────────────────────────────────────────────────────────────────────────────────────┴───────────────────────────────┴───────────┘\r\n(NOTE: There may be security-related changes not in this list. See https://github.com/aws/aws-cdk/issues/1299)\r\n\r\nResources\r\n[~] AWS::IAM::Policy AgoraStreamingUploader/DefaultPolicy AgoraStreamingUploaderDefaultPolicyA4C7845B \r\n └─ [~] PolicyDocument\r\n     └─ [~] .Statement:\r\n         └─ @@ -2,6 +2,10 @@\r\n            [ ] {\r\n            [ ]   \"Action\": [\r\n            [ ]     \"s3:PutObject\",\r\n            [+]     \"s3:PutObjectLegalHold\",\r\n            [+]     \"s3:PutObjectRetention\",\r\n            [+]     \"s3:PutObjectTagging\",\r\n            [+]     \"s3:PutObjectVersionTagging\",\r\n            [ ]     \"s3:Abort*\"\r\n            [ ]   ],\r\n            [ ]   \"Effect\": \"Allow\",\r\n\r\nStack DatabaseStack\r\nThere were no differences\r\nStack RedisStack\r\nThere were no differences\r\n\r\n```","mergeCommitSha":"fe256ef1e7ab794273d91391dd207214b750edc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1673","title":"Update cdk dependencies","createdAt":"2022-06-07T22:07:20Z"}
{"state":"Merged","mergedAt":"2022-06-07T23:11:44Z","number":1674,"body":"Removes unused keep alive logic.\r\nAllows us to clean up permissions to simplify submission process.","mergeCommitSha":"fdaa68e9d0b3cd342deedf80eea3823c7460f1c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1674","title":"Remove unnecessary Keep Alive","createdAt":"2022-06-07T23:00:24Z"}
{"state":"Merged","mergedAt":"2022-06-08T16:55:37Z","number":1675,"body":"Tested it via a local deploy and confirmed secret names/values were correct.","mergeCommitSha":"ac4ecb269b10fb981e5f590600728b3904898272","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1675","title":"Move away from deprecated apis for storing secrets in ASM","createdAt":"2022-06-07T23:01:42Z"}
{"state":"Merged","mergedAt":"2022-06-08T16:05:24Z","number":1676,"body":"<img width=\"1053\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/172499935-4a3f7f8f-618f-4cb0-88a0-a208c40ec5a5.png\">\r\n\r\n* Missing assets / will add them once Ben has re-exported them all\r\n* Added API call to highlight the hub app -- will fail silently if it doesn't resolve (we don't want to block the onboarding flow if the arrow doesnt show up)\r\n* Added a top level checkbox to invite page that will select/deselect all emails","mergeCommitSha":"d034042bb93b2cd313f80e654f67b1a4bdbc287b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1676","title":"[Onboarding] Fill in last step","createdAt":"2022-06-07T23:28:58Z"}
{"state":"Merged","mergedAt":"2022-06-08T03:34:06Z","number":1677,"body":"Triggering pull request ingestion in the admin console will backfill these models/fields","mergeCommitSha":"6a7520067e8cdeacb9960ca74fe68b89f9b59742","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1677","title":"Replace ThreadModel.pr* fields with PullRequestModel","createdAt":"2022-06-08T00:01:50Z"}
{"state":"Merged","mergedAt":"2022-06-08T16:35:47Z","number":1678,"body":"Fixes UNB-212 https://linear.app/unblocked/issue/UNB-212/refactorsimplify-vscode-sidebar-treeview-rendering.  This work is needed to make implementing the Open PR Tree View UI easier.\r\n\r\nSimplify TreeViews, their data stores, etc, as per the conversation here: https://chapter2global.slack.com/archives/C02US6PHTHR/p1653685366851389:\r\n* `TreeView` is now a collapsible list of `TreeItem`s with a header\r\n* `TreeView` items are now generic: they take in a label, icon, prefix and suffix.  No more thread-specific stuff.\r\n* `TreeView` simply takes in the list of its children. This lets us get rid of all the stores and providers.\r\n* Cleaned up the sidebar provider -- there is now much less data processing happening everywhere.\r\n* Removed much of the Notes UI, since we're not really using it anywhere.  There's still some stuff left I think.\r\n\r\nLet me know if you think we shouldn't be removing any of this, I am happy to resurrect things if we think they're useful.","mergeCommitSha":"d455226ffa243c78f6a6b70acf2f647493958888","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1678","title":"Simplify VSCode tree views","createdAt":"2022-06-08T02:43:27Z"}
{"state":"Merged","mergedAt":"2022-06-08T04:03:28Z","number":1679,"mergeCommitSha":"b9e303d0606290b321efe2bb091b9cf09f489788","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1679","title":"Set createdAt for PullRequestModel","createdAt":"2022-06-08T03:44:42Z"}
{"state":"Merged","mergedAt":"2022-01-28T22:49:36Z","number":168,"mergeCommitSha":"b98068c6bc7169fc501b2ef27fef98101a78289a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/168","title":"Now anyone can generate secrets like the pros","createdAt":"2022-01-28T22:26:13Z"}
{"state":"Merged","mergedAt":"2022-06-10T02:02:49Z","number":1680,"body":"Definitely improved.\r\n\r\nBefore | After\r\n--|--\r\n<img width=\"735\" alt=\"Screen Shot 2022-06-09 at 18 51 18\" src=\"https://user-images.githubusercontent.com/1798345/172974244-ea1f1970-cfd0-46d2-9e5e-147a3f278a9e.png\"> | <img width=\"767\" alt=\"Screen Shot 2022-06-09 at 18 53 24\" src=\"https://user-images.githubusercontent.com/1798345/172974411-1fa2211a-bdf6-4f23-b69e-7b0dd9fa64e3.png\">\r\n ","mergeCommitSha":"01c30d6b5b14564b237ea8ac193a371ebc3da25e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1680","title":"SourceMark engine should handle force pushes","createdAt":"2022-06-08T05:52:50Z"}
{"state":"Merged","mergedAt":"2022-06-08T17:00:34Z","number":1681,"mergeCommitSha":"44081a47b6db0f67c795a90fba067cfffb1d595c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1681","title":"Was calling showTutorialOverlay from non-main actor","createdAt":"2022-06-08T16:55:21Z"}
{"state":"Merged","mergedAt":"2022-06-08T17:18:09Z","number":1682,"mergeCommitSha":"e0d0e10b19d1954b8f5205d3488c746101d624db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1682","title":"Fix admin button -- sorry Pete","createdAt":"2022-06-08T16:59:32Z"}
{"state":"Merged","mergedAt":"2022-06-08T18:00:44Z","number":1683,"body":"Since gen code is now checked in for Swift, if the swift gen dir is cleaned, it will cause major problems for anyone making changes or running npm commands","mergeCommitSha":"1621162596b8f4e025233f556296fd1b335f5e20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1683","title":"Remove swift gen dir from incremental cleanup target","createdAt":"2022-06-08T17:21:38Z"}
{"state":"Merged","mergedAt":"2022-06-08T17:48:05Z","number":1684,"body":"To match the designs","mergeCommitSha":"35a8ab7bfac68daecd55592fe3094a4a970f0d82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1684","title":"Style nits for landing pages","createdAt":"2022-06-08T17:29:58Z"}
{"state":"Merged","mergedAt":"2022-06-08T17:34:28Z","number":1685,"mergeCommitSha":"aa97cafbc80fc3b7cf5938bc337c8f23356c8baa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1685","title":"Cleanup ","createdAt":"2022-06-08T17:31:21Z"}
{"state":"Merged","mergedAt":"2022-06-08T18:00:36Z","number":1686,"mergeCommitSha":"e396a9ddce72df901a6edc096a2e0e9333789616","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1686","title":"Check the first message for archiving threads","createdAt":"2022-06-08T17:49:11Z"}
{"state":"Merged","mergedAt":"2022-06-08T21:01:49Z","number":1687,"body":"This storybook is broken right now anyways due to babel bugs.  This at least prevents build errors on the main bundle.","mergeCommitSha":"f7fe76f13affb97d9ec887b25d979e9a4cbacd7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1687","title":"Temp fix for storybook typing build error","createdAt":"2022-06-08T18:18:02Z"}
{"state":"Merged","mergedAt":"2022-06-09T16:57:27Z","number":1688,"mergeCommitSha":"af66aa84342908a025206102e2daee24d97f40e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1688","title":"Add pull request API endpoints","createdAt":"2022-06-08T18:31:02Z"}
{"state":"Merged","mergedAt":"2022-06-08T18:40:06Z","number":1689,"body":"This pr adds a few things:\r\n1. Dead Letter Queue Support\r\n2. Visibility Timeout Support\r\n3. Max Retention Period support\r\n\r\nAlready tested against dev\r\n","mergeCommitSha":"cb4d876bbed76b9bcf931c754f8a9b28b76c64f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1689","title":"Add ability to define dead letter queues and othe rqueue properties","createdAt":"2022-06-08T18:31:30Z"}
{"state":"Merged","mergedAt":"2022-01-28T23:05:53Z","number":169,"mergeCommitSha":"f413d458967e0695172332850ca508ceccf4fbeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/169","title":"Add GitHub App Secret and Key","createdAt":"2022-01-28T23:00:37Z"}
{"state":"Merged","mergedAt":"2022-06-08T19:51:20Z","number":1690,"body":"Remove text based loading state for now...\r\n\r\n<img width=\"547\" alt=\"CleanShot 2022-06-08 at 11 34 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/172692427-032b41c3-4077-4eb5-a39e-61bac4d628da.png\">\r\n","mergeCommitSha":"9174769f487eb92d0b534f504ad470eab057dbba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1690","title":"Setup loading state in vscode","createdAt":"2022-06-08T18:41:25Z"}
{"state":"Merged","mergedAt":"2022-06-09T00:17:15Z","number":1691,"body":"Per Dennis' feedback, only have the UB sidebar open for the Import Knowledge step, close it for ensuing steps to make the viewable canvas less busy","mergeCommitSha":"a651337febbb3702abc1748c3f1ba59eb6ea01f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1691","title":"Only open the sidebar in the first step","createdAt":"2022-06-08T18:47:49Z"}
{"state":"Merged","mergedAt":"2022-06-08T19:01:14Z","number":1692,"mergeCommitSha":"025e36f71d8218621398546601f1bc6b26951fe4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1692","title":"Cleanup dead field","createdAt":"2022-06-08T19:01:07Z"}
{"state":"Merged","mergedAt":"2022-06-08T19:11:56Z","number":1693,"mergeCommitSha":"273b47322f6a647822f3f3f35f19bc4630821578","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1693","title":"fix field thype","createdAt":"2022-06-08T19:11:49Z"}
{"state":"Merged","mergedAt":"2022-06-08T20:08:05Z","number":1694,"body":"Added AuthContext.Provider to Web extension.\r\n\r\nAlso added max-width within image_block to scale images to fit.\r\n\r\n<img width=\"1027\" alt=\"CleanShot 2022-06-08 at 12 45 25@2x\" src=\"https://user-images.githubusercontent.com/1553313/172704111-10696678-8424-46cd-97e6-1c8e0ea4302d.png\">\r\n","mergeCommitSha":"f14d8d781fc4d1e9f60c7daf7c552bb894a74541","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1694","title":"Support Authed Images","createdAt":"2022-06-08T19:47:05Z"}
{"state":"Merged","mergedAt":"2022-06-08T20:34:25Z","number":1695,"mergeCommitSha":"93f5f396ff87c683ac40ba04fdbdf1c188d28cad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1695","title":"Only the first thread message should have an anchor sourcemark","createdAt":"2022-06-08T20:16:51Z"}
{"state":"Merged","mergedAt":"2022-06-08T23:04:19Z","number":1696,"body":"I was wrong, we weren't including it. This fixes that","mergeCommitSha":"61b35c41c121f0094d196abf326433e8833bb39f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1696","title":"Include pullrequest in api ThreadInfo model","createdAt":"2022-06-08T21:00:01Z"}
{"state":"Merged","mergedAt":"2022-06-08T21:02:59Z","number":1697,"mergeCommitSha":"78bd2f6059350712ce622518d3c9ff39a9b68699","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1697","title":"Cleanup","createdAt":"2022-06-08T21:02:52Z"}
{"state":"Merged","mergedAt":"2022-06-08T21:27:30Z","number":1698,"mergeCommitSha":"f3fcd3c6473069e141927fb07162879bd2bae407","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1698","title":"Remove cache","createdAt":"2022-06-08T21:26:35Z"}
{"state":"Open","mergedAt":null,"number":1699,"mergeCommitSha":"5cc9a393d9e4de214b1622f458421d95b05e304a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1699","title":"DO NOT MERGE: Test PR for Screenshots","createdAt":"2022-06-08T21:50:43Z"}
{"state":"Merged","mergedAt":"2022-01-06T00:22:09Z","number":17,"body":"Update Icon Interface","mergeCommitSha":"f35d3a64dbb448efafe55f6f11ff77385303a3ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/17","title":"Icon interface","createdAt":"2021-12-23T21:43:07Z"}
{"state":"Merged","mergedAt":"2022-01-28T23:16:19Z","number":170,"mergeCommitSha":"ec92374b41787d5e9733466eb6fa89ee86a3d045","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/170","title":"Ensure we redeploy on descrets changes","createdAt":"2022-01-28T23:14:06Z"}
{"state":"Merged","mergedAt":"2022-06-08T23:15:35Z","number":1700,"body":"Update secrets files to ensure honeycomb key is not followed by newline as well.","mergeCommitSha":"c1986e3629f537275acde8990cef22d2bc5a1a70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1700","title":"Ensure honeycomb api key is resilient","createdAt":"2022-06-08T23:02:32Z"}
{"state":"Merged","mergedAt":"2022-06-08T23:43:51Z","number":1701,"mergeCommitSha":"e04754fef4f087f52f98e323ae3711f548ad1939","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1701","title":"Log new DB connections","createdAt":"2022-06-08T23:26:23Z"}
{"state":"Merged","mergedAt":"2022-06-09T00:32:49Z","number":1702,"mergeCommitSha":"3deb99e349db5d7d72fd1beb68cd4bd295bb7f93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1702","title":"Fix bug when generating install url for personal org","createdAt":"2022-06-08T23:39:15Z"}
{"state":"Merged","mergedAt":"2022-06-09T00:40:22Z","number":1703,"body":"We're now sending back the full thread title string so it needs to be clipped in the UI if the string is very long\r\n\r\nThere's a known issue with text-overflow ellipses in child containers of flex parents -- workaround is to use grid in the parent and the ellipsing will work as expected.","mergeCommitSha":"ccae85e75dd075f31a5a6b087956c49c722e4687","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1703","title":"Clip thread title to one line in web dashboard","createdAt":"2022-06-09T00:02:51Z"}
{"state":"Merged","mergedAt":"2022-06-09T15:22:06Z","number":1704,"body":"We were duplicating logic on icon usage in a few places, specifically the logic on what icon should be used for a specific thread.  This simplifies it down to two places:\r\n* Shared `Icon` and `ThreadIcon` components, which render an icon\r\n* A secondary usage in VSCode, which needs direct access to icon URIs.\r\n\r\nI'd like to figure out how to merge these into one, but that will wait for later.\r\n\r\n(also, I added the green \"Open PR\" icon)\r\n<img width=\"1186\" alt=\"Screen Shot 2022-06-08 at 5 05 49 PM\" src=\"https://user-images.githubusercontent.com/2133518/172737614-16769347-52d9-457e-bb3e-b87cc09eb9bd.png\">\r\n\r\n","mergeCommitSha":"43debf9b79b5cf3df354a19108b36f222e4cacd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1704","title":"Refactor icon usage a little bit","createdAt":"2022-06-09T00:08:45Z"}
{"state":"Merged","mergedAt":"2022-06-09T01:45:24Z","number":1705,"body":"Adjust retry config to reduce contention.\r\n\r\nDownside is that deployments may rollover slightly slower (on the order of low number of seconds).\r\n\r\nhttps://chapter2global.slack.com/archives/C03JVNA7P7D/p1654734540480769","mergeCommitSha":"b2ad5ff8afaef17fe56d5e37f7b4d54e5192d9a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1705","title":"Theory: DB busy lock contention during rollover is maxing out DB connections","createdAt":"2022-06-09T00:48:27Z"}
{"state":"Merged","mergedAt":"2022-06-09T03:00:05Z","number":1706,"body":"As requested, we now send different types of thread invites\r\n1. user is authed and a team member\r\n2. user is not authed.\r\n\r\nWe create a new api model to represent this (and are deprecating emails and participants fields).\r\n","mergeCommitSha":"1d2820de33a6b1e1ff7220148ef7d2787bf4077f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1706","title":"Allow for split thread invites dependent on whether authed or not user","createdAt":"2022-06-09T01:50:48Z"}
{"state":"Merged","mergedAt":"2022-06-09T03:48:10Z","number":1707,"body":"The library provides a ton of configuration options, but it's also built in an opinionated way with sensible defaults. \r\n\r\nDisabled in Prod. I have no idea how to test this so I think we just need to deploy it in dev and observe","mergeCommitSha":"1f85f337e81f9cd5283090f6513179dde3ebf302","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1707","title":"DB Connection pooling with HikariCP","createdAt":"2022-06-09T03:27:44Z"}
{"state":"Merged","mergedAt":"2022-06-09T18:21:34Z","number":1708,"mergeCommitSha":"a66940373177ffb15a7a4ad8ee6caf0755ca9518","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1708","title":"Add getThreadsForPullRequests and getPullRequestThreads operations","createdAt":"2022-06-09T04:14:07Z"}
{"state":"Merged","mergedAt":"2022-06-13T20:44:10Z","number":1709,"body":"Comments inline","mergeCommitSha":"3b05afcc705830ada2629e91eef7b78dc5db6a8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1709","title":"Client Versions API Proposal","createdAt":"2022-06-09T05:09:42Z"}