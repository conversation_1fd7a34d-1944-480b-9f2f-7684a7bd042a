{"state":"Merged","mergedAt":"2023-02-01T18:40:18Z","number":4624,"body":"Appenders should have specific thresholds.\r\nWe should not restrict seeing trace logs out in console.output for services if I'm observing from kubernetes.","mergeCommitSha":"f11fff32deb4587415cfd5fa8a75d6f4cf4534d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4624","title":"Add log thresholds","createdAt":"2023-02-01T17:57:16Z"}
{"state":"Merged","mergedAt":"2023-02-01T18:44:27Z","number":4625,"body":"This may be part of onboarding.\r\n\r\n<img width=\"439\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/216128667-85e7dd56-86d0-4c46-96ee-0a0810822787.png\">\r\n\r\nNote: This is using `DashboardUrl` right now as topics and experts do not have server-generated links. There's an open PR for this work https://github.com/NextChapterSoftware/unblocked/pull/4368. Will update this code once that work is done.","mergeCommitSha":"797e872d5b646d5452987ec2d9c3dee03de026bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4625","title":"Add team member url to expert tooltip","createdAt":"2023-02-01T18:19:04Z"}
{"state":"Merged","mergedAt":"2023-02-02T23:33:25Z","number":4626,"body":"Safe to ignore API break since this just loosens constraints. Some of these changes might mask real bugs. Please validate ","mergeCommitSha":"032c322ecb1af61a99757b3ed86d179981ee080a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4626","title":"Loosens constraints on several fields","createdAt":"2023-02-01T19:34:45Z"}
{"state":"Merged","mergedAt":"2023-02-01T19:57:13Z","number":4627,"body":"Problem\n- cannot rename a ThreadParticipantModel.member because the new member may already exist,\n  which violates the ThreadParticipantModel unique constraint.\n\nSolution\n- for each ThreadParticipantModel row involving the old member ensure that the exact same\n  row exists for the new member, ignoring the insert if it already exists.\n- the delete of the old identity will cascade to the old member, which will in turn cascade\n  to the old ThreadParticipantModel row, so everything gets nicely cleaned up.","mergeCommitSha":"f948e42f97df59fa5c37c2a34a8935d3d4130e80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4627","title":"Identity migration: Special handling for ThreadParticipantModel","createdAt":"2023-02-01T19:56:24Z"}
{"state":"Merged","mergedAt":"2023-02-01T21:28:44Z","number":4628,"mergeCommitSha":"1832f2bb01ea204f280fac634d27ad05834e620b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4628","title":"Lift intellij config readme to root","createdAt":"2023-02-01T20:02:47Z"}
{"state":"Merged","mergedAt":"2022-03-03T18:57:46Z","number":463,"body":"- Added new cert for alb.{env}.getunblocked.com (Cloudfront will talk to this endpoint)\r\n- Added above DNS record pointing it at ALB address for each env-region\r\n- Changed the Web Dashboard CI workflow to use the new CloudFront Distro ID\r\nThis is more prep work for my next PR changing helm specs. ","mergeCommitSha":"16fb43ece647d7af5be67b2ff8c4ace93a93d08d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/463","title":"Adding new DNS names and Certs for ALBs","createdAt":"2022-03-03T18:49:42Z"}
{"state":"Merged","mergedAt":"2023-02-01T22:44:29Z","number":4637,"body":"This filters out topics after generation but before we save it to the database.","mergeCommitSha":"584d49f1659a74c1753f5aadc6273ac742e4006e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4637","title":"UNB-944 Topics: Filter out common stopwords + developer terms","createdAt":"2023-02-01T22:05:50Z"}
{"state":"Merged","mergedAt":"2023-02-01T22:12:45Z","number":4638,"mergeCommitSha":"530d005fb2db9a0f967b2951093413a8cd8da877","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4638","title":"Identity migration: Upsert new team member if necessary","createdAt":"2023-02-01T22:08:12Z"}
{"state":"Merged","mergedAt":"2022-03-03T19:37:40Z","number":464,"body":"This PR includes a lot of duplicate changes but they boil down to the following:\r\n\r\n- Convert existing services to ClusterIP (NodePort) and remove DNS/Cert stuff from each\r\n- Create an ingress for each service with proper path and DNS/Cert configs\r\n- Updated base helmchart\r\n- Regenerate all helmcharts\r\n\r\nNote: All ingress deployments are combined into a single ALB by the controller (using alb groups.name annotation). ALB will take care of routing traffic to each NodePort service based on provided path configuration","mergeCommitSha":"5c863cb2567ff173c196a27cee0456a565cc59a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/464","title":"Switch everything to ALB at once (all or nothing)","createdAt":"2022-03-03T19:20:51Z"}
{"state":"Merged","mergedAt":"2023-02-01T23:08:46Z","number":4641,"mergeCommitSha":"24c091255e544c3bd621ae522d9ff304f777805d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4641","title":"cleanup enqueue","createdAt":"2023-02-01T22:29:34Z"}
{"state":"Merged","mergedAt":"2023-02-02T00:08:14Z","number":4642,"body":"Added:\r\n- getVideoChannelsForMe\r\n- getVideoChannel\r\n- getRecording\r\n- getSlackTeams\r\n- getSlackConfiguration\r\n- getSlackChannels\r\n\r\nFrom slack:\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1675271601864759","mergeCommitSha":"c190bb22e3d96463a80ec1fab9435ba10f47dc37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4642","title":"Update read-only API access","createdAt":"2023-02-01T22:45:19Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:50:19Z","number":4643,"body":"Base styling for Discussion Thread. More of a test on how integrated web views can look.\r\nDoes not replicate IntelliJ Designs atm.\r\n\r\nTODO: Import font from editor or manually import from file.\r\n\r\n<img width=\"556\" alt=\"CleanShot 2023-02-01 at 16 02 49@2x\" src=\"https://user-images.githubusercontent.com/1553313/216196714-94c5e0c7-58f5-48b0-b26c-0c58f8aa1c38.png\">\r\n\r\n","mergeCommitSha":"8783f79c8e056dc2fa93233e76b622318419d349","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4643","title":"IntelliJ Thread with injected theme","createdAt":"2023-02-01T23:02:33Z"}
{"state":"Merged","mergedAt":"2023-02-01T23:41:19Z","number":4645,"body":"Backfill of null `displayName` column on `Identity` from the optionally associated `customDisplayName` column on `Person`.","mergeCommitSha":"c11c354a44d7260ff4edb6f85b52df8cd383dbc0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4645","title":"Backfill Identity Names","createdAt":"2023-02-01T23:10:10Z"}
{"state":"Merged","mergedAt":"2023-02-01T23:48:26Z","number":4646,"body":"@davidkwlam @rasharab @dennispi : excludes specific topics based on the clockwise test. we will make these tools better as we onboard more partners.","mergeCommitSha":"a536c0c3e28f3c411b5988e48028f2389b8a62ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4646","title":"Added clockwise stop words","createdAt":"2023-02-01T23:21:52Z"}
{"state":"Merged","mergedAt":"2023-02-01T23:48:52Z","number":4647,"mergeCommitSha":"b5a407d4f68d399480d10e42629e19314fa1b859","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4647","title":"Try removing slack histogram","createdAt":"2023-02-01T23:38:49Z"}
{"state":"Merged","mergedAt":"2023-02-02T01:13:47Z","number":4648,"mergeCommitSha":"8794034aaa99424e591890ce3c6650760306e930","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4648","title":"Start moving GitHub-specific logic to client-scm","createdAt":"2023-02-01T23:45:38Z"}
{"state":"Merged","mergedAt":"2023-02-02T00:06:38Z","number":4649,"mergeCommitSha":"c3fbdd5b67c92749a3a40cdc8b05aa7006d77fa9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4649","title":"Identity migration: skip remapping member if no need","createdAt":"2023-02-02T00:00:43Z"}
{"state":"Merged","mergedAt":"2022-03-04T05:55:03Z","number":465,"body":"Cleanup following: https://github.com/NextChapterSoftware/unblocked/pull/438","mergeCommitSha":"f6229ca2b569ff1db426c44b93876efb91cd0ad2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/465","title":"Remove ThreadParticipantModel","createdAt":"2022-03-03T19:40:45Z"}
{"state":"Merged","mergedAt":"2023-02-02T00:33:40Z","number":4650,"mergeCommitSha":"5a3a10acb8fc56b54e7e4a7ca273e52c5745e319","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4650","title":"Re-enable main compat check","createdAt":"2023-02-02T00:33:22Z"}
{"state":"Merged","mergedAt":"2023-02-02T01:26:13Z","number":4651,"body":"It looks like their Slack channels are seriously impacting the PowerML results? If you look at the histogram and bert as a comparsion -- which has some good topics in there?","mergeCommitSha":"498844bbf785a95d18ace8399482b15cc2855e0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4651","title":"More Clockwise filter words","createdAt":"2023-02-02T00:34:31Z"}
{"state":"Merged","mergedAt":"2023-02-02T01:22:07Z","number":4652,"mergeCommitSha":"8e933759fd7326c26e8873b131574acc35a59ef9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4652","title":"shuffle mapping","createdAt":"2023-02-02T00:39:31Z"}
{"state":"Merged","mergedAt":"2023-02-02T00:39:46Z","number":4653,"mergeCommitSha":"d917285b9a24d9030b6b6c90d90c0f00fd6d06e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4653","title":"Facepalm","createdAt":"2023-02-02T00:39:32Z"}
{"state":"Closed","mergedAt":null,"number":4654,"mergeCommitSha":"e86f8697245118bee8a84edcc020fbf8604bef25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4654","title":"Identity uniqueness enforced by DB constraint","createdAt":"2023-02-02T00:41:07Z"}
{"state":"Merged","mergedAt":"2023-02-02T01:23:16Z","number":4655,"body":"This causes an error on startup, and I'm wondering if it is causing one of our customer's VSCode issues.  Since VSCode shipped the proposal we don't need these.","mergeCommitSha":"170c6bfa9a10ff141edc911dea9c9646be7fe9d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4655","title":"Remove VSCode declaration that we require badges proposal","createdAt":"2023-02-02T00:45:32Z"}
{"state":"Merged","mergedAt":"2023-02-02T04:09:47Z","number":4656,"mergeCommitSha":"20fe8c5d00db965581f1f4f70339607600edbea3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4656","title":"Identity externalTeamId is non-null","createdAt":"2023-02-02T00:51:34Z"}
{"state":"Merged","mergedAt":"2023-02-02T04:55:02Z","number":4657,"body":"Wait for parent PR to be deployed first.","mergeCommitSha":"e1587c7e02255d151903c3c40d788a89c54c533f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4657","title":"Identity uniqueness enforced by DB constraint","createdAt":"2023-02-02T00:51:37Z"}
{"state":"Merged","mergedAt":"2023-02-02T01:02:27Z","number":4658,"body":"It probably broke due to duplicate identities and members.\nAdded a tie-breaker on UUID just in case.\n\nAlso cleanup unused migrations.","mergeCommitSha":"3421ad96192df0d142d4c1b8247eadf189143d75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4658","title":"Re-enable team member sort in admin web","createdAt":"2023-02-02T00:55:38Z"}
{"state":"Merged","mergedAt":"2023-02-02T05:02:45Z","number":4659,"body":"There were missing dependencies in the creation command, causing us to miss added contributors to invite during the note creation. (Specifically, the `contributors` list wasn't being properly updated causing the lookup to be incomplete during the check for emails to invite)","mergeCommitSha":"5a17240f55ad1eaead2f7bf7fa862fa8809c75ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4659","title":"Add missing dependencies to callback","createdAt":"2023-02-02T01:46:19Z"}
{"state":"Merged","mergedAt":"2022-03-03T20:36:01Z","number":466,"body":"Since we are using CNAMES I had to add a few alternate hostnames. Tested it manually and it works!\r\n\r\nAlso added Ingress deploy permission for CI/CD","mergeCommitSha":"c4048e19122067bf7df41a5c7afafc136b073690","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/466","title":"Fix hostnames in ingress config","createdAt":"2022-03-03T20:13:01Z"}
{"state":"Merged","mergedAt":"2023-02-02T02:29:51Z","number":4660,"body":"- Add powerml mapping framework for topics\r\n- Add power ml stuff\r\n","mergeCommitSha":"5b48030127f63cc472e65203596e108620f0022a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4660","title":"AddPowerMLMapping","createdAt":"2023-02-02T02:11:06Z"}
{"state":"Merged","mergedAt":"2023-02-02T02:16:16Z","number":4661,"mergeCommitSha":"f365e316cbcea5527dd675aca1c8bd5487bce80c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4661","title":"powerml topic permissions","createdAt":"2023-02-02T02:16:08Z"}
{"state":"Merged","mergedAt":"2023-02-02T02:54:20Z","number":4662,"mergeCommitSha":"9ee7dad58b35b795bbbb927e775e08dcdfdf05d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4662","title":"Filter out slack bot messsages","createdAt":"2023-02-02T02:43:58Z"}
{"state":"Merged","mergedAt":"2023-02-02T04:49:19Z","number":4663,"mergeCommitSha":"2d63da92f58b397583d504360a9d616032fc7aeb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4663","title":"slack message replies verifier","createdAt":"2023-02-02T04:38:40Z"}
{"state":"Merged","mergedAt":"2023-02-02T05:15:44Z","number":4664,"mergeCommitSha":"3ab2d2a6d4c91d0f0b8da983fb855aabb5687e64","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4664","title":"Fix message replies handling","createdAt":"2023-02-02T05:15:23Z"}
{"state":"Merged","mergedAt":"2023-02-02T06:32:46Z","number":4665,"mergeCommitSha":"0538cd06b622c6d838a37ed15bc86401fad8c612","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4665","title":"we are hitting memory overcommitment errors. This should address that","createdAt":"2023-02-02T06:06:21Z"}
{"state":"Merged","mergedAt":"2023-02-02T07:09:48Z","number":4666,"body":"We went from 2 services 1 year ago to 18 services today. This is causing overcommitment alarms on Kubernetes. \r\n\r\nThis change would limit the number of concurrent deploy jobs to help avoid those. Otherwise we have to waste a lot of resources sitting idle most of the time just to accommodate deployments. \r\n\r\nIf this turns out to be very slow I will change the roll-over percentage in our helm chart to 100%","mergeCommitSha":"bae0938341fd62085d3649b5c04d915c23f1681b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4666","title":"Limit the number of concurrent service deploys ","createdAt":"2023-02-02T06:48:10Z"}
{"state":"Merged","mergedAt":"2023-02-02T07:42:24Z","number":4668,"body":"This change is to suppress debug messages on security alarm channels:\r\nhttps://chapter2global.slack.com/archives/C04MAEXF1GE/p1675306038263259\r\n\r\nGrafana escalation already has it set to `notice` \r\n\r\n\r\nChanges have been deployed to both Dev and Prod ","mergeCommitSha":"1cd17c4903f1560f7ad1aa51cc2a6a7979f10624","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4668","title":"set the default priority level for slack messages to notice","createdAt":"2023-02-02T07:41:59Z"}
{"state":"Merged","mergedAt":"2023-02-02T07:58:46Z","number":4669,"mergeCommitSha":"1fa908a42d689a6c5d7a9110f8e818614da48fce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4669","title":"Attempt serialized transactions","createdAt":"2023-02-02T07:49:19Z"}
{"state":"Merged","mergedAt":"2022-03-03T22:23:33Z","number":467,"body":"for routing\r\n```\r\n/api/channels → pusher-service\r\n/api/logs     → logs-service\r\n/api/etc      → etc-service\r\n/api/*        → api-service\r\n```","mergeCommitSha":"13c699d978a97503994a90e21037565c47df5c7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/467","title":"Update server sub-domain and base path","createdAt":"2022-03-03T21:49:58Z"}
{"state":"Merged","mergedAt":"2023-02-02T09:29:32Z","number":4670,"mergeCommitSha":"57a674fc6e44ee7beed4a7beae75280287fee970","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4670","title":"Alter resource requirements","createdAt":"2023-02-02T09:29:18Z"}
{"state":"Merged","mergedAt":"2023-02-02T17:46:09Z","number":4675,"mergeCommitSha":"ba3e390ac449633998baf1ed34d8bcd99677d6b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4675","title":"Added a few more words","createdAt":"2023-02-02T17:26:49Z"}
{"state":"Merged","mergedAt":"2023-02-02T18:22:48Z","number":4676,"mergeCommitSha":"a9d63ec9b95934556698e5f79eb0bf3786c624ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4676","title":"Don't return suggestions if query is blank","createdAt":"2023-02-02T18:06:25Z"}
{"state":"Merged","mergedAt":"2023-02-06T22:36:46Z","number":4677,"body":"Using eslint-disable has caused quite a few bugs in the client (ie causing us to miss missing dependencies); add some code to memoize the callbacks so that the frequent rerenders shouldn't happen and we can wean off of littering the code with eslint-disables.\r\n\r\nI haven't removed all of them (some of them are explicit; ie so that the code only runs once, or so that we can add an extra dependency to trigger the callback) but this takes out a big chunk. Notably, `setItem`, `getItem`, `openModal`, and `closeModal` should all be able to be callback dependencies. \r\n\r\nAlso: explicitly send back `undefined` in the searchInsights and searchInsightSuggestions APIs if the string is empty.","mergeCommitSha":"d4add067a48d4c088f88e04876b134b37edce515","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4677","title":"Remove several instances of eslint-disable","createdAt":"2023-02-02T18:17:57Z"}
{"state":"Merged","mergedAt":"2023-02-24T22:02:32Z","number":4678,"mergeCommitSha":"b5a61985c3c73bc3dfa7c6fe61ea6a134b6d2088","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4678","title":"Enable autocomplete globally","createdAt":"2023-02-02T18:20:12Z"}
{"state":"Merged","mergedAt":"2023-02-02T18:52:35Z","number":4679,"mergeCommitSha":"f8cbd18d6c232b79291bbec1237baf7357a3daf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4679","title":"Remove override styling","createdAt":"2023-02-02T18:45:26Z"}
{"state":"Merged","mergedAt":"2022-03-03T22:14:16Z","number":468,"body":"The dashboard path fix is allow both `/dashboard` and `/dashboard/` to serve index.html","mergeCommitSha":"98a5e56797daeb28a13e1479d65b08b0898a2c3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/468","title":"modify paths to have api prefix. Also fix dashboard path in CF","createdAt":"2022-03-03T22:05:55Z"}
{"state":"Merged","mergedAt":"2023-02-02T19:31:11Z","number":4680,"mergeCommitSha":"e6ce77b8e77b1844c3c8eb15c3d33a91d37e8ff4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4680","title":"Dont index archived pull requests","createdAt":"2023-02-02T18:53:47Z"}
{"state":"Merged","mergedAt":"2023-02-02T19:02:38Z","number":4681,"body":"<img width=\"728\" alt=\"CleanShot 2023-02-02 at 10 56 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/216423023-1c3ab52e-52fb-4ccf-a0bc-c4d9e7fa5c2c.png\">\r\n","mergeCommitSha":"600864385b09ca4406d40998b61ad02eb5fe6247","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4681","title":"Fix Alignment","createdAt":"2023-02-02T18:57:05Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:26:24Z","number":4682,"mergeCommitSha":"854a3ffa189a9bc5e6136807d821fec2cef05be6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4682","title":"Use contains for search suggestion if query is longer than 2 characters","createdAt":"2023-02-02T19:22:12Z"}
{"state":"Merged","mergedAt":"2023-02-03T02:23:06Z","number":4683,"body":"@rasharab is going to exclude any food related slack channels, but adding a few more words. @dennispi wanted to get topics for the duckdb open source projects","mergeCommitSha":"f29128641e95c69ed22d3792c42c225d3f83bfd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4683","title":"Added more stop words and the duckdb topic results","createdAt":"2023-02-02T19:45:09Z"}
{"state":"Merged","mergedAt":"2023-02-02T21:12:01Z","number":4684,"body":"externalTeamId was made non-optional.\r\nIt hsould not be treated as optional anywhere.","mergeCommitSha":"2eae214937700c74422fd26ad7b8cf7f8c261036","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4684","title":"Fix runtime failures due to an optional attribute","createdAt":"2023-02-02T20:49:22Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:03:17Z","number":4685,"mergeCommitSha":"9f7b7bd0b61cb58e47bbc177ef9001d664a513a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4685","title":"Add default to hide cursor during recording","createdAt":"2023-02-02T20:56:51Z"}
{"state":"Merged","mergedAt":"2023-02-04T00:39:42Z","number":4686,"body":"Probably the first of many such pull requests\r\n\r\n`GitLabDiscussions` are just grouped notes/comments. This is a convenient way to get notes/comments that are grouped into threads.","mergeCommitSha":"b205b498eea8a5a83a064bf8917aaa66d1ad0192","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4686","title":"Add GitLab merge request, notes, and user data classes","createdAt":"2023-02-02T21:15:15Z"}
{"state":"Merged","mergedAt":"2023-02-02T21:52:45Z","number":4687,"body":"I reviewed the changes, and they're all good:\r\n\r\n- Gets rid of a bunch of code duplication\r\n- Adds validation codegen, but doesn't actually call the validation (left up to the programmer)","mergeCommitSha":"2827eb1efdd5677f613244b5db66a1ce953d539b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4687","title":"Update swift codegen","createdAt":"2023-02-02T21:17:49Z"}
{"state":"Merged","mergedAt":"2023-02-02T21:41:02Z","number":4688,"mergeCommitSha":"e56282cb5e806449f40fe421445a0e4743c07254","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4688","title":"Add topic mapping source type","createdAt":"2023-02-02T21:18:35Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:13:32Z","number":4689,"mergeCommitSha":"0143a0a1a3c55f7244fc2da1b7be5137212ca47f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4689","title":"Log waitForSourceMarksLoaded to detect with sourcemark engine is stuck","createdAt":"2023-02-02T21:50:16Z"}
{"state":"Merged","mergedAt":"2022-03-04T01:09:46Z","number":469,"body":"Also sets the `Thread`'s `createdAt` to the `createdAt` of the first message","mergeCommitSha":"ec79bc4b17f4affeb2bc33cb286c93867afeac31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/469","title":"Set createdAt for ingested PR comments","createdAt":"2022-03-03T22:37:35Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:01:11Z","number":4690,"mergeCommitSha":"cbe36a8a6b719164eb4769af9cdbd78bf8d19daa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4690","title":"Make async","createdAt":"2023-02-02T22:00:52Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:07:28Z","number":4691,"mergeCommitSha":"b658faf44b736895e7d5ca489f7b5a4dcd5c0342","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4691","title":"revoke prod DB access","createdAt":"2023-02-02T22:06:35Z"}
{"state":"Merged","mergedAt":"2023-02-02T23:34:11Z","number":4692,"body":"This is for helping figure out why VSCode is getting wedged overnight.  I will probably remove a couple of the more spammy ones here afterwards.","mergeCommitSha":"45e20b0bf1338e1a37ffdec6a0585040be407727","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4692","title":"Add some logging to help troubleshoot repo and SM issues","createdAt":"2023-02-02T22:16:47Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:18:32Z","number":4693,"mergeCommitSha":"915c1980654af250dcb08d766bf78f54d2e5c1d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4693","title":"increase timeout","createdAt":"2023-02-02T22:17:57Z"}
{"state":"Merged","mergedAt":"2023-02-02T22:40:51Z","number":4694,"mergeCommitSha":"c47ad2c6ebc7be8342fd115c9f370fe293485d3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4694","title":"Update topic service instance count","createdAt":"2023-02-02T22:40:36Z"}
{"state":"Merged","mergedAt":"2023-02-03T20:58:40Z","number":4696,"body":"- Move ts_proto lib into the root npm package.json\r\n- Make a grade task to ensure root npm dependencies\r\n- Update JetBrains plugin icon and description\r\n- Add `/jetbrains` root to VSCode clients workspace","mergeCommitSha":"78e5614456434428aa8628a8d09697741b927c97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4696","title":"Update JetBrains plugin skeleton and build","createdAt":"2023-02-02T22:58:06Z"}
{"state":"Merged","mergedAt":"2023-02-10T17:11:29Z","number":4697,"body":"Copied explorer sidebar logic to shared + IntelliJ. Didn't update VSCode to use new shared logic for now to prevent regressions while testing out IntelliJ.\r\n\r\n<img width=\"1406\" alt=\"CleanShot 2023-02-02 at 14 32 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/216472270-7557a83a-294e-4dac-9139-88596013e07d.png\">\r\n<img width=\"1400\" alt=\"CleanShot 2023-02-02 at 14 33 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/216472274-3a322ed6-ded6-45f1-b5b8-6971695c6d93.png\">\r\n","mergeCommitSha":"c1da1ee33a2c37c60a221745988f53c09caa9b2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4697","title":"IntelliJ Sidebar styling","createdAt":"2023-02-02T23:14:12Z"}
{"state":"Merged","mergedAt":"2023-02-06T19:44:08Z","number":4698,"body":"This is done is a way that is backwards compatible with current tokens in flight. The token chain id is copied from the old refresh token to the new one.\r\n\r\nThe token chain id will be used to do redis lookups for an issued-at value, which is then used to invalidate refresh tokens. Using a token chain ensures that only a single client is logged out (rather than all of them). ","mergeCommitSha":"9b0faf4705596fc0f0a5b0b35b71572a15b10e67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4698","title":"Adds a token chain id to the refresh token for invalidation purposes","createdAt":"2023-02-03T00:36:28Z"}
{"state":"Merged","mergedAt":"2023-02-03T20:16:32Z","number":4699,"body":"Part of https://linear.app/unblocked/issue/UNB-674/engagement-metrics-should-take-hub-and-vscode-sidebar-interactions","mergeCommitSha":"3c84ec5ed628a9eed39e5e2f691e7b62747f63a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4699","title":"Treat tutorial event as engagement","createdAt":"2023-02-03T01:33:15Z"}
{"state":"Merged","mergedAt":"2022-01-17T22:21:02Z","number":47,"body":"Renamed ECR repo from `core-apps` to `api`. Had a brief chat with Rashin and we plan to use one per service docker images which would require multiple repositories. \r\n\r\nThis change has been deployed to Dev.\r\n\r\n\r\nDepending on how many repo/services we end up having, we might change the configuration file to be more generic and accept multiple repo configs under a single key. For now explicit repo creation is reasonable.  ","mergeCommitSha":"16218461495eca855169d4584513693dd996de6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/47","title":"rename ecr repo to api","createdAt":"2022-01-17T22:15:58Z"}
{"state":"Merged","mergedAt":"2022-03-04T22:54:47Z","number":470,"body":"This moves the client environment configs (the API base path and the dashboard URL) into one place for easier future maintenance.  The webpack configs still specify the environment we operate in.","mergeCommitSha":"f22d9c7e2cef743a893cc9fdda6c0b15fd668035","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/470","title":"Put client environment configs in one place","createdAt":"2022-03-03T23:39:07Z"}
{"state":"Merged","mergedAt":"2023-02-03T05:57:58Z","number":4700,"mergeCommitSha":"e43e661ed820a265c353bbfc2ccee945eb6d6547","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4700","title":"Lowercase topic name before comparison","createdAt":"2023-02-03T05:43:02Z"}
{"state":"Merged","mergedAt":"2023-02-03T06:07:08Z","number":4701,"mergeCommitSha":"88bf420f794def1613fe0494c0469889b171b6dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4701","title":"Update topics_to_ignore.txt","createdAt":"2023-02-03T05:56:36Z"}
{"state":"Merged","mergedAt":"2023-02-03T18:16:20Z","number":4702,"mergeCommitSha":"538251d35bfafcb440809e23a259e6606c4391dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4702","title":"Added more words","createdAt":"2023-02-03T07:53:05Z"}
{"state":"Merged","mergedAt":"2023-02-03T19:25:37Z","number":4703,"mergeCommitSha":"1728db90a03cbe21f7339dfbb7f66200582c0baf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4703","title":"Add TopicSourceType.Curated enum","createdAt":"2023-02-03T18:28:09Z"}
{"state":"Merged","mergedAt":"2023-02-06T20:05:45Z","number":4704,"mergeCommitSha":"7efcbb586236962b172c6e6bb871642de3cea343","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4704","title":"Logout API","createdAt":"2023-02-03T18:34:44Z"}
{"state":"Merged","mergedAt":"2023-02-04T01:08:10Z","number":4705,"body":"When entering Github Org name, pressing return navigated user back to previous page.\r\nThis occurred since there were multiple buttons within the form. By default, buttons are of type submit.\r\n\r\nChange the \"back\" button type so there's only a single submit button.","mergeCommitSha":"5cbb87a1f0c50a70a32c8c82f1c4705a3bfb9734","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4705","title":"Correct Button Focus in LoginManifest","createdAt":"2023-02-03T18:54:10Z"}
{"state":"Merged","mergedAt":"2023-02-03T20:35:34Z","number":4706,"body":"We need topic channel overrides to determine which channels we actually we do want to generate topics off of.","mergeCommitSha":"ded1923730ac80dd066d3a5e31a10481956d6198","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4706","title":"Add ability to toggle topic ingestion","createdAt":"2023-02-03T20:16:54Z"}
{"state":"Merged","mergedAt":"2023-02-03T21:16:22Z","number":4707,"body":"The bug here is a consequence of SwiftUI's lifecycle. New instances of the NSViewRepresentable template can get created, but the actual underlying view is passed between NSViewRepresentable instances, then `updateNSView` is called on the new NSViewRepresentable instance with the previous NSTextView instance. ","mergeCommitSha":"01a9cfb48a0a1878a385bd4fad1cd4b9a00acc4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4707","title":"Fixes unread count bug in Hub","createdAt":"2023-02-03T21:05:03Z"}
{"state":"Merged","mergedAt":"2023-02-03T23:41:10Z","number":4708,"mergeCommitSha":"5a9ce7754f5c5526d7923eb3470b0b9939294ddf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4708","title":"Engagement metrics should take Hub and VSCode-Sidebar interactions into account","createdAt":"2023-02-03T21:11:46Z"}
{"state":"Merged","mergedAt":"2022-03-07T19:27:27Z","number":471,"body":"Use Shiki as a syntax hilighter for VSCode.  I added quite a lot of commenting to CodeToHtmlRenderer explaining how it works, read through that to get a better idea of what's happening.\r\n\r\nMost of this PR is dealing with packaging issues: Shiki is meant to be used on a node server, and doesn't play well with bundlers.  It expects to find language files and the Onigiri WASM runtime in the node_modules subfolder.  So in this PR, instead, we copy those files into a `/shiki` extension subfolder, and load the files from there.","mergeCommitSha":"3105d67fb8c697ca046008e310ba72e0f153255a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/471","title":"Shiki-based syntax hilighter for VSCode","createdAt":"2022-03-04T00:22:53Z"}
{"state":"Merged","mergedAt":"2023-02-03T21:20:46Z","number":4710,"mergeCommitSha":"a52ed319a04c148f2cd5d6744ce4f2301a30fb67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4710","title":"DRY it up","createdAt":"2023-02-03T21:18:52Z"}
{"state":"Merged","mergedAt":"2023-02-03T22:34:43Z","number":4711,"mergeCommitSha":"c07328b06fca3e4572f46fd14b596bfe62b722b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4711","title":"Add ability to curate topics","createdAt":"2023-02-03T21:25:44Z"}
{"state":"Merged","mergedAt":"2023-02-03T21:43:06Z","number":4712,"body":"More logs to try to figure out why our sidebar views aren't loading for one customer","mergeCommitSha":"b8edb32d2403d9640ed37bf516f931057932857e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4712","title":"Add VSCode logs to help troubleshoot webview startup","createdAt":"2023-02-03T21:28:59Z"}
{"state":"Merged","mergedAt":"2023-02-06T21:01:03Z","number":4715,"body":"Forking the TS build broke this, this fixes it.","mergeCommitSha":"4fd52fdbf9e6fb99641272e2baf1a804401fe041","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4715","title":"Fix VSCode launch watch mode","createdAt":"2023-02-03T22:52:28Z"}
{"state":"Merged","mergedAt":"2023-02-03T23:10:51Z","number":4716,"mergeCommitSha":"f3739af5a29d045c13e9ab1fafe25a678b69a261","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4716","title":"Fix typo","createdAt":"2023-02-03T23:03:03Z"}
{"state":"Merged","mergedAt":"2023-02-03T23:32:02Z","number":4717,"mergeCommitSha":"a8e22bfd664dcc1af73823e334f72ef9916deb13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4717","title":"Turn into buttons","createdAt":"2023-02-03T23:24:07Z"}
{"state":"Merged","mergedAt":"2023-02-04T00:13:15Z","number":4718,"mergeCommitSha":"d1db347cf8e5a3e61221d52dbceaecbe2c13db6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4718","title":"Clean up buttons","createdAt":"2023-02-03T23:57:17Z"}
{"state":"Merged","mergedAt":"2023-02-04T00:26:02Z","number":4719,"body":"Possible fix for: https://linear.app/unblocked/issue/UNB-913/vscode-extension-gets-wedged-overnight","mergeCommitSha":"b40478f1ed3186b13b103d0aacb492bf585382df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4719","title":"SourceMarkScheduler construction logging and race fix","createdAt":"2023-02-04T00:03:12Z"}
{"state":"Closed","mergedAt":null,"number":472,"body":"We only set TeamMemberModel.isActive to true if the identity is not anonymous (i.e. Person model exists for identity model)\r\n\r\nI forget why we decided to rename.","mergeCommitSha":"48702846e3a42598ac600c6a2bb524825f4742fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/472","title":"[WIP] Rename isDeactivated to isCurrentMember and correctly set for PR Ingestion","createdAt":"2022-03-04T03:12:16Z"}
{"state":"Merged","mergedAt":"2023-02-04T01:05:09Z","number":4720,"mergeCommitSha":"b8709fe8eb2738129a6baeeeae9af1555e039be7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4720","title":"Drop unused token claim (externalId), previously used for allowListing based on github membership","createdAt":"2023-02-04T00:23:07Z"}
{"state":"Merged","mergedAt":"2023-02-04T01:48:49Z","number":4721,"mergeCommitSha":"7908059066cd17c9697571cb457b91dcf23bbaf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4721","title":"Throttle engagement metrics so that we record at most once per 3 hours","createdAt":"2023-02-04T01:03:28Z"}
{"state":"Merged","mergedAt":"2023-02-04T05:51:51Z","number":4722,"body":"GitHub login failure that has happened several times over the last 14 days caused by:\n```\nField 'access_token' is required for type with serial name 'com.nextchaptersoftware.scm.github.GitHubUserAccessTokenResponse', but it was missing\n```","mergeCommitSha":"9ee925387109a7b686c41db11e8f0b5d9ad21d1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4722","title":"Debug logging for GitHub code exchange failure","createdAt":"2023-02-04T05:39:52Z"}
{"state":"Merged","mergedAt":"2023-02-06T19:25:52Z","number":4724,"body":"@pwerry Note that we no longer consider enum additions breaking:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/4724/commits/1d981dbfaec6dd10975fc3c322d06b5a412c506f","mergeCommitSha":"92d716e7961c7ab60725b1e37551652f3e9d3a18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4724","title":"Adds GitLab provider types","createdAt":"2023-02-04T06:07:21Z"}
{"state":"Merged","mergedAt":"2023-02-05T03:01:22Z","number":4725,"mergeCommitSha":"3d904c8f006d3a92cda30e8c52aa96f3be7b62f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4725","title":"Remove limit for search when mapping insights to topics","createdAt":"2023-02-05T01:02:07Z"}
{"state":"Merged","mergedAt":"2023-02-06T19:41:50Z","number":4729,"mergeCommitSha":"6b9478dcda08d35707175486b4ac8fe19408a9af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4729","title":"Adds GitLab OAuth app config and secrets","createdAt":"2023-02-06T02:51:21Z"}
{"state":"Merged","mergedAt":"2022-03-04T22:20:41Z","number":473,"mergeCommitSha":"f218ef13f0acb008d2b4abc8d301807c1daf6c0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/473","title":"longer paths should have higher priority (lowest number)","createdAt":"2022-03-04T03:48:46Z"}
{"state":"Merged","mergedAt":"2023-03-13T21:05:19Z","number":4730,"mergeCommitSha":"8280f80a9de3484fddaf5573433733cd01f7cfb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4730","title":"Allow passing a null limit for topic mapping","createdAt":"2023-02-06T17:56:55Z"}
{"state":"Merged","mergedAt":"2023-02-06T20:05:20Z","number":4731,"mergeCommitSha":"188bb40aeec5187cb69b083789e190d89df5a09a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4731","title":"add modals inputs and ability to send emails to identiies","createdAt":"2023-02-06T19:56:28Z"}
{"state":"Merged","mergedAt":"2023-02-06T20:55:14Z","number":4732,"mergeCommitSha":"e34ecb520b87743bc38bdd4919aabb7e743a7bd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4732","title":"Fix build","createdAt":"2023-02-06T20:55:08Z"}
{"state":"Merged","mergedAt":"2023-02-06T23:55:36Z","number":4733,"body":"Not plugged in yet. Planning to use for logout in the auth service","mergeCommitSha":"8c5bb10fe076d2860798c393b733f628eaddce12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4733","title":"Add redis store for token chains","createdAt":"2023-02-06T21:19:06Z"}
{"state":"Merged","mergedAt":"2023-02-07T18:12:18Z","number":4734,"body":"Add support for https://github.com/NextChapterSoftware/unblocked/pull/4708\r\n\r\nAdd user engagement tracking to the following:\r\n1. Dashboard content is visible (Chat.tsx)\r\n2. VSCode Sidebar content is visible (SidebarWebview)\r\n3. VSCode Explorer content is visible (ExplorerInsightsWebview)\r\n\r\nWhen these views are opened and the client is focused, send metrics event.\r\nIf nothing has changed and the view is still open / focused after an hour (adjustable interval), send metrics event again.\r\n\r\n\r\nfixes https://linear.app/unblocked/issue/UNB-674/engagement-metrics-should-take-hub-and-vscode-sidebar-interactions","mergeCommitSha":"f05eb1618a40254e4c51eba0931fc2307b19f8fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4734","title":"Updated Client User Engagement Metrics (Not Hub)","createdAt":"2023-02-06T21:43:36Z"}
{"state":"Closed","mergedAt":null,"number":4735,"body":"Add a stream that hits the shallow health check every five seconds, and returns a true/false indicator if we have connectivity to the API or not.  Log this in VSCode.\r\n\r\nThe idea is that this can eventually be used for displaying connectivity failure banners/popups etc.\r\n\r\nI had to make some changes to the base API code a bit to allow hitting the health check as a plain fetch call, without retrying logic etc.","mergeCommitSha":"405130bf091db0695426e5d79b002caaf110d468","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4735","title":"Add ConnectivityStream","createdAt":"2023-02-06T21:55:42Z"}
{"state":"Merged","mergedAt":"2023-02-07T21:18:23Z","number":4736,"body":"On WindowOpen, send metrics event for each team.\r\n\r\nfixes https://linear.app/unblocked/issue/UNB-674/engagement-metrics-should-take-hub-and-vscode-sidebar-interactions","mergeCommitSha":"af9f402ecc2765bdf0a9d0a7e1546d2d65f36e29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4736","title":"Send Metrics for Hub Content","createdAt":"2023-02-06T22:21:38Z"}
{"state":"Merged","mergedAt":"2023-02-06T23:55:27Z","number":4737,"mergeCommitSha":"f51a6269d17be9651933c9871388fb37adf7dc50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4737","title":"Command to enable VSCode extension host debugger","createdAt":"2023-02-06T23:32:20Z"}
{"state":"Merged","mergedAt":"2023-02-06T23:50:28Z","number":4738,"body":"This will pull references to GitHub* classes out of the PR ingestion module. Next PR will update this logic to convert GitHub* classes to abstract classes so that the rest of the PR ingestion module can be purged of references to GitHub* classes.","mergeCommitSha":"de4498f82c2a2088879df14492f6dfedc7a2ec35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4738","title":"Move logic into scmservice module","createdAt":"2023-02-06T23:41:00Z"}
{"state":"Merged","mergedAt":"2023-02-07T00:31:02Z","number":4739,"mergeCommitSha":"72176acf4783f68b0ab9d2a808aa08fb55486a89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4739","title":"Installer builds will now create VSCode sourcemap artifacts","createdAt":"2023-02-06T23:45:33Z"}
{"state":"Merged","mergedAt":"2022-03-04T22:22:21Z","number":474,"body":"Defines DB models for Video Chat state management, both for channels and recordings. See diffs for explanations of classes and fields","mergeCommitSha":"6ff337b21d1cf7fe6fbb667cbeb3e0159cfc8dae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/474","title":"Video Models","createdAt":"2022-03-04T05:22:42Z"}
{"state":"Merged","mergedAt":"2023-02-07T19:35:22Z","number":4740,"body":"https://developer.apple.com/documentation/bundleresources/information_property_list/lsminimumsystemversion for https://linear.app/unblocked/issue/UNB-939/mobile-stop-stupid-build-error","mergeCommitSha":"b4c282cdeea71c05b9b46a1690a2bfe9660b8b0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4740","title":"UNB-939: Setting LSMinSysVer:","createdAt":"2023-02-07T00:00:07Z"}
{"state":"Merged","mergedAt":"2023-02-07T05:30:24Z","number":4741,"body":"This allows us sanely stage the activation of new SCMs (GitLabs and Bitbucket) in each environment as we build them out.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/blob/47b98e69ba6e290a52c0eafc1ea44c640d0229c4/projects/libs/lib-scm/src/main/resources/config/scm-global.conf#L1-L20","mergeCommitSha":"d064dcc81059ec4fd9d975d66666e0373a50867a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4741","title":"Feature flags for providers","createdAt":"2023-02-07T00:42:25Z"}
{"state":"Merged","mergedAt":"2023-02-08T17:06:21Z","number":4742,"mergeCommitSha":"17e2946251443dda54d96d072bd1e75b5f40bc70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4742","title":"Throw on forbidden so that we can retry after token is refreshed","createdAt":"2023-02-07T01:00:13Z"}
{"state":"Merged","mergedAt":"2023-02-07T02:11:44Z","number":4743,"mergeCommitSha":"f6cd58275e12fdbcd2a9c8c45a64fad5f6a1a9fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4743","title":"Add infra for powerml","createdAt":"2023-02-07T01:54:01Z"}
{"state":"Merged","mergedAt":"2023-02-07T08:05:07Z","number":4745,"body":"Logs are filled with spurious errors from 4xx cases.","mergeCommitSha":"e400d76420a3bc28d9da6d3a768e828a9e56e1c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4745","title":"Treat API 4xx as warnings, API 5xx as errors","createdAt":"2023-02-07T07:46:16Z"}
{"state":"Merged","mergedAt":"2023-02-08T17:03:32Z","number":4746,"body":"The existing pull request ingestion logic operates on GitHub-specific models (the objects returned by the GitHub API). We want to update ingestion logic to operate pull requests from any provider. \r\n\r\nTo achieve that, we will replace those GitHub-specific models with abstract models that can be created from the various provider-specific models via transformers.\r\n\r\nThis PR introduces two new abstract classes to replace their GitHub equivalents in our ingestion logic:\r\n\r\n- `ScmPullRequest` which replaces `GitHubPullRequest`\r\n- `ScmUser` which replaces `GitHubUser`\r\n\r\nThere are other GitHub-specific models (i.e. TLCs, code comments, reviews) yet to be replaced but let's start with these two classes, as doing those as well would explode the size of this PR.","mergeCommitSha":"162f356019d3d39463a1ca4ab88dcdd93274a113","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4746","title":"Introduce ScmPullRequest and ScmUser","createdAt":"2023-02-07T07:46:44Z"}
{"state":"Merged","mergedAt":"2023-02-07T18:11:58Z","number":4749,"mergeCommitSha":"965e08852cb9c0326eba0dbfbeaebf3017f46fc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4749","title":"[UNB-973] Fix participant section breakpoint","createdAt":"2023-02-07T17:54:08Z"}
{"state":"Merged","mergedAt":"2022-03-07T18:13:18Z","number":475,"body":"- Restructured the CloudFront Stack to have landing page as default origin\r\n- Changed how we add extra behaviors. Now we have to add each extra behavior explicitly in the code (no more looping over list of buckets)\r\n- Removed old CloudFront distros, DNS records and permission objects\r\n- Cleaned up config objects\r\n- Removed old DNS records in management account\r\n- Removed unused config params\r\n- Added a lambda function to return custom error responses for `/dashboard`. This is needed for SPA sites \r\n\r\nAll these changes have been deployed to both Dev and Prod. They work as expected.\r\n\r\n **Update:**\r\n\r\nClarification:\r\n- Custom Error Lambda function in this code are used to deal with some SPA site issues. We were initially doing the same thing but using CloudFront custom errors where 404 and 403 error were being redirected to `index.html` with a 200 status code. This simply a cleaner way of doing that.\r\n- Path re-write function is used to map a sub-path like `/dashboard` to `/dashboard/index.html` this is also a limitation of hosting static sites on S3\r\n\r\n**New Changes:**\r\n    - Removed IP filter lambda function\r\n    - Added WAF to CloudFront instead of IP filter lambda\r\n    - Updated WAF rule to do the same job as IP filter lambda\r\n    - Added a custom header to WAF as well as custom error lambda. This is to allow traffic coming from Lambda function to hit restricted `dashboard` site. Once we go public and dashboard is no longer behind an IP filter we can remove it. \r\n    - Made the custom error lambda generic. It's no longer site specific\r\n    - Removed more old certs\r\n    - Updated CloudFront Cert to include www.getunblocked.com domain\r\n    This PR is now ready to be merged","mergeCommitSha":"36ef65a425c9e3eb02de8c4b151c12ada366e234","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/475","title":"Cleanup old unused AWS resources and CDK code","createdAt":"2022-03-04T07:31:10Z"}
{"state":"Merged","mergedAt":"2023-02-09T18:29:57Z","number":4750,"body":"https://user-images.githubusercontent.com/13431372/217346095-cc216d5c-ff88-460a-a608-c049277f4cd3.mp4\r\n\r\n","mergeCommitSha":"0b0583e9bcfd8077035217252e7062acc2a97574","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4750","title":"[UNB-961] Fix up invite dialog with ability to skip","createdAt":"2023-02-07T19:30:08Z"}
{"state":"Merged","mergedAt":"2023-02-07T20:06:33Z","number":4751,"mergeCommitSha":"eed5c7feccd372bbb27c6923088bc48c48dbeeee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4751","title":"Login-as-user should not impersonate the Intercom user","createdAt":"2023-02-07T19:36:34Z"}
{"state":"Merged","mergedAt":"2023-02-09T17:04:31Z","number":4752,"body":"Added a health status monitor where one can log the current state of the stores.\r\nHopefully helps with debugging any issues with broken stream states.\r\n\r\nCurrently hooked up in VSCode:\r\nAuth\r\nTeam\r\nPerson\r\nCurrentFile\r\nRepo\r\n\r\n<img width=\"505\" alt=\"CleanShot 2023-02-07 at 12 14 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/217354960-45fdfc87-5bd6-438f-a379-e1b0f12848ae.png\">\r\n","mergeCommitSha":"09b4eaab7f675f1d0a29dd4fa1b8d5dd9614a464","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4752","title":"Basic Health monitor","createdAt":"2023-02-07T20:15:32Z"}
{"state":"Merged","mergedAt":"2023-02-07T23:35:33Z","number":4754,"body":"![CleanShot 2023-02-07 at 14 39 53@2x](https://user-images.githubusercontent.com/1553313/217383071-086aab80-da5f-45ca-8636-d80907422ef3.png)\r\n\r\nMax width for content.\r\n\r\nAdded border for empty contributor list.","mergeCommitSha":"20ee17f3a638af199ac2154dd31f927fd023414e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4754","title":"Updated CreateWalkthrough","createdAt":"2023-02-07T22:39:44Z"}
{"state":"Merged","mergedAt":"2023-02-07T23:15:58Z","number":4755,"body":"Auth service currently requires SCM OAuth client secret in orde to perform the OAuth `code` exchange,\r\nwhich means that the SCM and Auth services now have access to the SCM secrets.\r\n\r\nThis improves the current situation, where SCM secrets are available to *all* services.\r\n\r\nHowever, we could do better and restrict SCM secrets so that they are deployed to the SCM service only.\r\nIn order to acheive this, we would need secure service-to-service synchronous communication so that\r\nthe auth-service could offload OAuth `code` exchange to the SCM-service.\r\n\r\nFixes auth-service crash-looping:\r\n```\r\nc.s.h.ConfigException: Error loading config because:\r\n\r\n    - Could not instantiate 'com.nextchaptersoftware.scm.config.ScmConfig' because:\r\n\r\n        - 'gitlab': - Could not instantiate 'com.nextchaptersoftware.scm.config.GitLabConfig' because:\r\n\r\n            - 'clientSecret': Missing from config\r\n\r\n        - 'webhookAuthentication': - Could not instantiate 'com.nextchaptersoftware.scm.config.WebhookAuthentication' because:\r\n\r\n            - 'githubHMACSecret': Missing from config\r\n\tat c.s.h.ConfigLoader$returnOrThrow$1.invoke(ConfigLoader.kt:215)\r\n\tat c.s.h.ConfigLoader$returnOrThrow$1.invoke(ConfigLoader.kt:212)\r\n\tat c.s.h.fp.ValidatedKt.getOrElse(Validated.kt:115)\r\n\tat c.s.h.ConfigLoader.returnOrThrow(ConfigLoader.kt:212)\r\n\tat c.n.s.c.ScmConfig.<clinit>(ScmConfig.kt:76)\r\n\t... 15 common frames omitted\r\nWrapped by: j.l.ExceptionInInitializerError: null\r\n\tat c.n.a.ModuleKt.module$default(Module.kt:45)\r\n\tat c.n.a.ApplicationKt$main$1$2.invoke(Application.kt:41)\r\n\tat c.n.a.ApplicationKt$main$1$2.invoke(Application.kt:34)\r\n\tat c.n.s.ServerKt$startServer$1.invoke(Server.kt:19)\r\n\tat c.n.s.ServerKt$startServer$1.invoke(Server.kt:13)\r\n\tat i.k.s.e.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:324)\r\n\tat i.k.s.e.ApplicationEngineEnvironmentReloading$instantiateAndConfigureApplication$1.invoke(ApplicationEngineEnvironmentReloading.kt:313)\r\n\tat i.k.s.e.ApplicationEngineEnvironmentReloading.avoidingDoubleStartup(ApplicationEngineEnvironmentReloading.kt:341)\r\n\tat i.k.s.e.ApplicationEngineEnvironmentReloading.instantiateAndConfigureApplication(ApplicationEngineEnvironmentReloading.kt:313)\r\n\tat i.k.s.e.ApplicationEngineEnvironmentReloading.createApplication(ApplicationEngineEnvironmentReloading.kt:150)\r\n\tat i.k.s.e.ApplicationEngineEnvironmentReloading.start(ApplicationEngineEnvironmentReloading.kt:280)\r\n\tat i.k.s.n.NettyApplicationEngine.start(NettyApplicationEngine.kt:211)\r\n\tat c.n.service.ServerKt.startServer(Server.kt:23)\r\n\tat c.n.a.ApplicationKt.main(Application.kt:34)\r\n```","mergeCommitSha":"0c10a962a05105fb6907077a5472f99270b74d16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4755","title":"Deploy SCM secrets to Auth Service","createdAt":"2023-02-07T22:58:15Z"}
{"state":"Merged","mergedAt":"2023-02-08T22:53:01Z","number":4756,"mergeCommitSha":"03cfc9a604237d68d69b937e4d9ae9e0414ed317","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4756","title":"Validate the refresh token chain","createdAt":"2023-02-07T23:44:54Z"}
{"state":"Merged","mergedAt":"2023-02-08T00:17:06Z","number":4757,"mergeCommitSha":"50261ef9b8e98d94b4b92af76a94a60f304a1f50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4757","title":"Fix db locks","createdAt":"2023-02-08T00:16:48Z"}
{"state":"Merged","mergedAt":"2023-02-08T00:21:37Z","number":4758,"mergeCommitSha":"4abc1385b9b360fafb5e10c6c77df1fbd52b569f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4758","title":"Clean up powerml scripts","createdAt":"2023-02-08T00:21:00Z"}
{"state":"Closed","mergedAt":null,"number":4759,"mergeCommitSha":"2dff912d8159b5d52bd927154f84aa6cac4447a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4759","title":"Open source 2","createdAt":"2023-02-08T00:29:27Z"}
{"state":"Merged","mergedAt":"2022-03-04T20:11:24Z","number":476,"body":"`teamMember` is a type, not a descriptive property name. This will result in a DB drop","mergeCommitSha":"ab7fcb506aa0c35c871ea76c5214d5cdfc6e0463","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/476","title":"Rename ambiguous model and api 'teamMember' property to 'author'","createdAt":"2022-03-04T16:43:28Z"}
{"state":"Open","mergedAt":null,"number":4760,"body":"* This replaces the hack code that compares team member display names\r\n* Note that this is also temporary code (i.e. to be removed once identity is fixed), just a less hack-y implementation than what was there before\r\n* Leverages a fuzzy string match to find user matches, and maps the id to a single user given a match\r\n    * i.e. Given users `{ id: 1, name: \"Ben\" }` and `{ id: 2, name: \"Ben Ng\" }`, the map will read `{ 1: { id: 2, name: \"Ben Ng\" }, 2: { id: 2, name: \"Ben Ng\" } } ` (the logic skews bias towards users that already have Unblocked accounts) \r\n* This is attached to the new `activeMembersStream` which is used sparingly and primarily in Topic related UI code and mentions  ","mergeCommitSha":"18d26654ae7e6088a67e447aad5c807808fb4f35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4760","title":"Add deduped map to TeamMemberStore","createdAt":"2023-02-08T01:17:33Z"}
{"state":"Merged","mergedAt":"2023-02-08T09:11:13Z","number":4762,"body":"- GitHub secrets no longer available globally\r\n- Now accessible in Auth, SCM, and Admin services","mergeCommitSha":"4e2b6455fe344587ccae6054beec0d9abc62a3fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4762","title":"Move GitHub config and secrets to SCM","createdAt":"2023-02-08T01:36:59Z"}
{"state":"Merged","mergedAt":"2023-02-08T02:09:24Z","number":4763,"mergeCommitSha":"d6e2d6693b86812883ab858ff3e90dcf3165ab6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4763","title":"try agian","createdAt":"2023-02-08T02:09:18Z"}
{"state":"Merged","mergedAt":"2023-02-08T18:35:11Z","number":4765,"body":"\r\n<img width=\"1298\" alt=\"Screenshot 2023-02-08 at 10 34 32\" src=\"https://user-images.githubusercontent.com/1798345/217620971-741ae4ce-5062-4669-a148-32132895c7b8.png\">\r\n\r\nfixes https://linear.app/unblocked/issue/UNB-674/engagement-metrics-should-take-hub-and-vscode-sidebar-interactions","mergeCommitSha":"6fabefd01790b6372e42e5dcd2f5e6c5717ac6f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4765","title":"Explain what last active is in admin web","createdAt":"2023-02-08T18:23:51Z"}
{"state":"Merged","mergedAt":"2023-02-08T22:06:39Z","number":4766,"body":"* Update assets\r\n* Update content\r\n* Aggregate status bar counts to be a single insights count (similar logic used in the last slide of onboarding)\r\n\r\n* Note: this flow is still feature flagged ","mergeCommitSha":"d0430f980861b68276d44d3123c11e2f06300ce7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4766","title":"Onboarding update 2023","createdAt":"2023-02-08T18:36:17Z"}
{"state":"Merged","mergedAt":"2023-02-08T19:41:00Z","number":4767,"body":"* also, newly created threads in vscode should open in the beside view column","mergeCommitSha":"a0efb0c64e27aada720b4bcfcd03ba54b47e92c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4767","title":"UNB-962 Fix topic table height","createdAt":"2023-02-08T18:59:54Z"}
{"state":"Merged","mergedAt":"2023-02-08T20:19:39Z","number":4768,"body":"Safari annoyingly thinks the admin web is not in English.\r\n\r\n\r\n<img width=\"785\" alt=\"Screenshot 2023-02-08 at 12 16 42\" src=\"https://user-images.githubusercontent.com/1798345/217641928-f5ddb592-e232-47cf-ae89-cf0b85985ce3.png\">\r\n\r\n\r\n<img width=\"832\" alt=\"Screenshot 2023-02-08 at 12 16 50\" src=\"https://user-images.githubusercontent.com/1798345/217641935-708bae4e-2c60-4733-a8f6-15a8659b953e.png\">\r\n","mergeCommitSha":"66c410bcf3baf49382b38936233d7eca23e70a0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4768","title":"Adjust admin language","createdAt":"2023-02-08T20:18:27Z"}
{"state":"Merged","mergedAt":"2023-02-15T18:22:06Z","number":4769,"body":"[This](https://codepen.io/jimratliff/pen/BaBzzbq) codepen explains the problem quite well. We use the `line-clamp` css property to cut off the text in the insight cards:\r\n<img width=\"846\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/217654022-0693a43c-d52a-4089-abd2-5a7ec68b0332.png\">\r\n(Chrome)\r\n\r\nThere are issues with this css property in Safari browsers, where in the case of multiple paragraphs, the 'clamped' paragraph blocks end up overlapping the main block. \r\n<img width=\"905\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/217653962-2424cee3-317b-4ed6-afe1-6dc152791391.png\">\r\n(Safari)\r\n\r\nAs a workaround, only display the first block of the PR description in the insight cards. This gets around the overlapping blocks.\r\n<img width=\"904\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/217654337-c7862641-558a-4bc8-9f26-99f8a7f5dfe5.png\">\r\n(Safari)\r\n\r\nA knockoff consequence here is that if the first block of a description is only one line long, it would only ever display one line:\r\n<img width=\"895\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/217654748-2a6a4435-82e7-4ba6-b335-c54fe4e1bb77.png\">\r\n\r\n\r\n","mergeCommitSha":"ae20f0583e6015096dc3b0914c25ea1fad4f1fd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4769","title":"UNB-969 - Workaround for safari lineclamp bug","createdAt":"2023-02-08T21:25:06Z"}
{"state":"Merged","mergedAt":"2022-03-04T22:46:50Z","number":477,"body":"- Update CloudFront ID for Dev\r\n- Fix import path in index.html\r\n- Add /dashboard/ subdir to S3 sync command","mergeCommitSha":"c46775c8f11cde4812b54af0e24c5a3abca3f7d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/477","title":"A couple of Dashboard infra fixes","createdAt":"2022-03-04T22:40:26Z"}
{"state":"Merged","mergedAt":"2023-02-08T22:57:26Z","number":4770,"mergeCommitSha":"6914b7a788f9de9988c35132107fc269e7822a34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4770","title":"Delete Agora","createdAt":"2023-02-08T22:46:55Z"}
{"state":"Merged","mergedAt":"2023-02-08T23:29:27Z","number":4771,"mergeCommitSha":"ee0f2b3dfe31ba73435b0f80269a1af873fdbdca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4771","title":"Unblock Jeff -- write out JAR resources to local filesystem for now","createdAt":"2023-02-08T23:12:45Z"}
{"state":"Merged","mergedAt":"2023-02-09T23:09:43Z","number":4772,"body":"Logging for potential race condition outlined here: https://linear.app/unblocked/issue/UNB-952/additional-hardening-on-initial-reference-in-walkthrough","mergeCommitSha":"39efe5bf1a7edf0f747c05e4c161a1e0e1da9021","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4772","title":"Initial reference ingestion logging","createdAt":"2023-02-08T23:26:56Z"}
{"state":"Merged","mergedAt":"2023-02-08T23:49:28Z","number":4773,"mergeCommitSha":"598dea5ce543430fd90de5189f5db062cc15aeed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4773","title":"Replace Author with ScmUser","createdAt":"2023-02-08T23:29:41Z"}
{"state":"Merged","mergedAt":"2023-02-09T01:01:24Z","number":4774,"body":"- Added a flag to force using absolute bucket names instead of qualified bucket names\r\n- Set the correct name for prod alb logs buckets.\r\nI have manually deployed this to prod and all is working fine now.","mergeCommitSha":"d82f92d12f38cf723359bb09814f424c2025b415","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4774","title":"fix for ALB log buckets in prod","createdAt":"2023-02-09T00:05:43Z"}
{"state":"Merged","mergedAt":"2023-02-09T00:18:07Z","number":4775,"body":"Enabling delete protection on all ALBs. Our ALBs are managed by a Kubernetes controller if a faulty configuration goes out causing it to be deleted we would need up to 48 hours to have the new DNS being updated everywhere. \r\n\r\nThis should prevent that","mergeCommitSha":"83a19cef3b8d19023f760d770d60e49e6eca3e51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4775","title":"enable termination protection for internet facing ALBs","createdAt":"2023-02-09T00:11:28Z"}
{"state":"Merged","mergedAt":"2023-02-09T02:00:24Z","number":4776,"body":"Changing liveness and startup probe (__deepcheck) timeouts to 7 seconds instead of 4. (I will update grafana probes to 10)\r\n\r\nI felt 7 is more reasonable and would like to avoid making these checks too forgiving which could mask a lot of issues. \r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1675752896101139","mergeCommitSha":"817d1c6c916e27e26696df0ac79facf0e56776c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4776","title":"make liveness a bit less agrressive","createdAt":"2023-02-09T00:31:38Z"}
{"state":"Open","mergedAt":null,"number":4779,"body":"This pr does several things for VSCode extension:\r\n1. It adds a dependency on our own forked module to allow for HAR generation of all HTTP requests using node-fetch. It instruments the low-level http.Agent used by node-fetch.\r\nhttps://www.npmjs.com/package/@nextchaptersoftware/node-fetch-har\r\n2. Adds a rolling file writer for the HAR (similar to how we do logs). It uses debouncing to reduce thrashing the disk too much.\r\n3. It adds a setting to control HAR generation. By default, HAR generation is DISABLED, and is simply a pass through to node-fetch.\r\n\r\n![CleanShot 2023-02-09 at 11 10 51](https://user-images.githubusercontent.com/3806658/217914099-e024550f-7deb-4c46-9bf6-09957b979d72.gif)\r\n","mergeCommitSha":"35e0d4d675cf2100cdcaa61c91a4a876867b3ab3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4779","title":"Add HAR support to VSCode to record all node-fetch requests","createdAt":"2023-02-09T07:04:30Z"}
{"state":"Merged","mergedAt":"2022-03-07T22:45:22Z","number":478,"body":"Add team fetching and team member fetching to vscode.\r\n\r\nCurrently integrated workarounds due to missing team context. Should be fixed in the near future with updated API specs.\r\n\r\n<img width=\"1050\" alt=\"CleanShot 2022-03-04 at 16 09 46@2x\" src=\"https://user-images.githubusercontent.com/1553313/156858302-53573462-6f72-4038-bc1c-c74e4d1dd45b.png\">\r\n","mergeCommitSha":"e2f0c38e17324640eacfa20e7e8f8a848b213330","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/478","title":"Setup Team & TeamMember integration to VSCode","createdAt":"2022-03-05T00:10:26Z"}
{"state":"Merged","mergedAt":"2023-02-09T07:32:34Z","number":4780,"body":"Missed some from https://github.com/NextChapterSoftware/unblocked/pull/4768","mergeCommitSha":"290f8c62df3b47c38c27f006de71f62696e7f337","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4780","title":"Fix admin web language properly","createdAt":"2023-02-09T07:30:25Z"}
{"state":"Merged","mergedAt":"2023-02-09T17:50:59Z","number":4781,"body":"Will be used in next change.\n\nAlso minor ScmUser changes\n- remove provider. provider is insufficient in enterprise cases.\n- rename name to displayName\n- id must be String, since Bitbucket ids are not numbers","mergeCommitSha":"ab7ad257a4cadade79cf338f476038efc458e05e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4781","title":"Add ScmOrg abstract class and tweak ScmUser","createdAt":"2023-02-09T07:30:28Z"}
{"state":"Merged","mergedAt":"2023-02-09T17:52:29Z","number":4782,"mergeCommitSha":"ad6ff63384176e2a53e0b5ffdb7cef61685ed461","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4782","title":"Encapsulate GitLab classes in client-SCM model","createdAt":"2023-02-09T08:05:06Z"}
{"state":"Merged","mergedAt":"2023-02-14T07:38:39Z","number":4783,"body":"We need to check the contents of the pull request description in addition to the comment counts.\r\n\r\nThe logic here will restore pull requests if a conversation is later opened up for it.","mergeCommitSha":"fc66143393b2f5cccd9e1d5d760b7472a40f3cab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4783","title":"Archive low relevance pull requests","createdAt":"2023-02-09T16:49:37Z"}
{"state":"Merged","mergedAt":"2023-02-09T23:29:28Z","number":4784,"body":"Replaces references to `GitHubPullRequestReviewComment` with `ScmPullRequestComment` and `GitHubPullRequestReviewFile` with `ScmPullRequestFile`.","mergeCommitSha":"befbdc8bc4d1f3eb932ee7d98e6d82767e1c8caa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4784","title":"Introduce ScmPullRequestComment and ScmPullRequestFile classes","createdAt":"2023-02-09T17:45:11Z"}
{"state":"Merged","mergedAt":"2023-02-09T19:30:54Z","number":4785,"mergeCommitSha":"de70ff425b270e427c460ea609e73f78b4256025","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4785","title":"Add topic experts for curated topics","createdAt":"2023-02-09T18:35:25Z"}
{"state":"Merged","mergedAt":"2023-02-09T20:24:41Z","number":4786,"body":"Not needed afaict. Can't remember why we added. Spam.","mergeCommitSha":"f3be12baa270a7fa309e08629236c93bd7701250","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4786","title":"Remove trace logging from ktor/auth","createdAt":"2023-02-09T18:36:02Z"}
{"state":"Closed","mergedAt":null,"number":4787,"mergeCommitSha":"8583111f8a0845a1d4e3d6931a9775c4c566824a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4787","title":"Fix race condition in VSCode RepoStore","createdAt":"2023-02-09T18:51:52Z"}
{"state":"Merged","mergedAt":"2023-02-10T18:56:01Z","number":4788,"body":"Refactor VSCode Repo Store to be completely stream based.","mergeCommitSha":"6454b00a168469c481c316b1f126901f1d5db958","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4788","title":"Update Repo Store","createdAt":"2023-02-09T18:52:08Z"}
{"state":"Merged","mergedAt":"2023-02-10T00:49:11Z","number":4789,"mergeCommitSha":"7c7ff66cc644512fc46aeaf234d1abe2c688c04c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4789","title":"Putting the chat app in the bin, and refactored the project file to only reference relevant projects and schemes","createdAt":"2023-02-09T20:23:44Z"}
{"state":"Merged","mergedAt":"2022-03-05T00:39:02Z","number":479,"mergeCommitSha":"f3d7e4a97e25ac4c4a92d0a8e237be5d99c96cad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/479","title":"INFO logging for exposed","createdAt":"2022-03-05T00:32:43Z"}
{"state":"Merged","mergedAt":"2023-02-10T00:16:40Z","number":4793,"mergeCommitSha":"d91f886db6c31309667b7aa98a14b33a833c203a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4793","title":"Enable model creation in prod","createdAt":"2023-02-10T00:13:00Z"}
{"state":"Merged","mergedAt":"2023-02-10T05:40:39Z","number":4794,"mergeCommitSha":"c364908e33026b961ad3fce90de54530084504b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4794","title":"Introduce ScmPullRequestReview","createdAt":"2023-02-10T00:22:42Z"}
{"state":"Merged","mergedAt":"2023-02-10T01:05:52Z","number":4795,"mergeCommitSha":"be4d11430e66d273cdbf4d2f91042ab48c8c5350","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4795","title":"Refactor some code","createdAt":"2023-02-10T00:37:43Z"}
{"state":"Merged","mergedAt":"2023-02-10T00:53:28Z","number":4796,"body":"* Remove unused videos (and references)\r\n* Replace current videos with smaller assets\r\n* Fix layout issue","mergeCommitSha":"3901517f91c8208e0e57d2676a7459a8c3d3e878","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4796","title":"Update onboarding assets","createdAt":"2023-02-10T00:45:18Z"}
{"state":"Merged","mergedAt":"2023-02-15T20:54:05Z","number":4797,"body":"<img width=\"338\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/217974364-ed1bd807-34f4-4912-9ec6-0b2e7b23f0b2.png\">\r\n\r\n* Fixes bug in the Explorer Insights panel selected state persisting on context click\r\n* Don't allow users to 'delete' Slack thread insights (this never did anything, just remove the option) \r\n* Refactor InsightStreamStore and helper fns\r\n* Removed the 'show unreads only' menu from the navigation bar (doesn't make as much sense in a search interface)\r\n* Make thread ordering consistent (i.e. code <--> unblocked)\r\n<img width=\"1102\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/218206964-ef891570-727f-44d5-a853-c5fd48047289.png\">\r\n","mergeCommitSha":"f4055eef051af3a9d82040ee3bbca300cee9a7d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4797","title":"Add searchV2 to vscode sidebar","createdAt":"2023-02-10T01:07:04Z"}
{"state":"Merged","mergedAt":"2023-02-10T06:44:32Z","number":4798,"body":"API service doesn't really depend on SCM config, it just depends on SCM web URLs parts\nand must not consume any secrets. Introduces ScmWeb, ScmWebConfig, ScmNoAuth, and\nfriends.\n\nThis un-fucks the API-service initialization.","mergeCommitSha":"a55caaa6ad4830b69bc798c602865b0bd44c2315","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4798","title":"Introduce ScmWeb abstraction to eliminate API-service dependency on SCM config loading","createdAt":"2023-02-10T01:38:44Z"}
{"state":"Merged","mergedAt":"2022-01-18T17:26:39Z","number":48,"body":"Setup basic API client interface for web.\r\n\r\nGenerated API code *not* checked in.\r\nRunning start or build will automatically try generating code. Currently depending on gradle caching capabilities.\r\n\r\nStill need to setup client configs (differentiate dev/prod) that will provide base urls, keys, headers, etc...\r\nAlso potentially custom templates.","mergeCommitSha":"184fabce529e41c0848a0efbb7e6f082b39d706b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/48","title":"Setup API client for Web","createdAt":"2022-01-17T23:10:11Z"}
{"state":"Merged","mergedAt":"2022-03-05T01:01:04Z","number":480,"body":"We updated publicPath to relative for deployment purposes https://github.com/NextChapterSoftware/unblocked/pull/477\r\n\r\nThis broke local dev (somewhat expected) Here's the fix.","mergeCommitSha":"c26f354d0164dd94c6a778d2ca3299d362cd7ce6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/480","title":"Point local publicpath to absolute","createdAt":"2022-03-05T00:38:45Z"}
{"state":"Merged","mergedAt":"2023-02-11T01:12:02Z","number":4800,"body":"\r\n<img width=\"895\" alt=\"Screenshot 2023-02-10 at 00 34 53\" src=\"https://user-images.githubusercontent.com/1798345/218042807-bd663060-4233-465a-b684-04a7bfd8ba02.png\">\r\n\r\n\r\nGitLab OAuth applications\r\nhttps://www.notion.so/nextchaptersoftware/GitLab-eac211b094c24b39bb43aac611b31037","mergeCommitSha":"0c0ad8f86f9cacfa6ea56d9d4333d9c4af1ddd5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4800","title":"GitLab Cloud Sign-in","createdAt":"2023-02-10T08:33:42Z"}
{"state":"Merged","mergedAt":"2023-02-10T18:39:21Z","number":4801,"body":"Remove feature flag for new onboarding","mergeCommitSha":"33457d51608eae0e7d8380283e703f658b00ae4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4801","title":"Flag on new onboarding","createdAt":"2023-02-10T18:04:36Z"}
{"state":"Merged","mergedAt":"2023-02-10T19:22:58Z","number":4802,"body":"And add new flag for @matthewjamesadam ","mergeCommitSha":"e869819d560d9209c7750c8aec141e83d95bf5b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4802","title":"Deprecate old onboarding feature flag","createdAt":"2023-02-10T19:06:29Z"}
{"state":"Merged","mergedAt":"2023-02-10T22:43:56Z","number":4803,"body":"Add stream operator that logs events of stream as things change.\r\n\r\nCan be manually added to any stream with compose or as part of any ValueStream / ValueCacheStream.","mergeCommitSha":"6055322ba30727a6af40dd6ede4cdaa8b7f4a289","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4803","title":"Add logger to streams","createdAt":"2023-02-10T20:01:58Z"}
{"state":"Closed","mergedAt":null,"number":4804,"mergeCommitSha":"f183fff3c9dd06d090e483de727f73c8b9d200b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4804","title":"Fix build issues due to lint","createdAt":"2023-02-10T20:47:56Z"}
{"state":"Merged","mergedAt":"2023-02-10T22:21:43Z","number":4807,"body":"The intended behaviour is that in regular mode, we log warnings and errors only into console, file, and logz.io, but when the client debug flag is enabled, we log *all* flags to all of these targets, with higher throttling amounts.\r\n\r\nSome practical results of this:\r\n* We can't use `log.debug(blah)` to do temporary debugging -- but that's probably fine\r\n* We can feel free to be much more verbose with debug/info logs, as by default nobody will see them and they won't negatively impact system performance\r\n\r\nThis code is a bit messy -- this is a pretty quick effort to get this in today, we can improve this later.","mergeCommitSha":"d2f98b5cb9736607508e97ad2f51da1a20e3075d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4807","title":"Respect client debug flag in VSCode","createdAt":"2023-02-10T21:29:41Z"}
{"state":"Merged","mergedAt":"2023-02-10T21:48:12Z","number":4808,"body":"\r\nhttps://user-images.githubusercontent.com/858772/218203073-c9b527fd-97cb-48bb-bfbe-295973e8baa0.mp4\r\n\r\n","mergeCommitSha":"ed88992cdcf2e3ce0a449882c769717cd59a37cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4808","title":"Fix onboarding notification step behaviour","createdAt":"2023-02-10T21:32:49Z"}
{"state":"Merged","mergedAt":"2022-03-05T01:05:16Z","number":481,"body":"Add teamID to thread API model.","mergeCommitSha":"6fbdc94f7450821a6f631b798a3f1d11cc33ced3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/481","title":"Add teamID to thread model","createdAt":"2022-03-05T00:58:03Z"}
{"state":"Merged","mergedAt":"2023-02-10T23:33:03Z","number":4810,"body":"There are scenarios where there are multiple possible debug ports, this will display all of them.","mergeCommitSha":"1b9f61c1232279c9f9368dbee30a57612e1208d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4810","title":"Show all VSCode prod debug ports","createdAt":"2023-02-10T23:09:43Z"}
{"state":"Merged","mergedAt":"2023-02-11T00:51:15Z","number":4811,"mergeCommitSha":"24d278b05d44b35440dc4eb80c38a2b31697ee45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4811","title":"Add BitBucketPullRequest and BitBucketUser models","createdAt":"2023-02-10T23:30:34Z"}
{"state":"Merged","mergedAt":"2023-02-11T00:15:00Z","number":4812,"body":"Not really an error","mergeCommitSha":"9b452efa04e00293e99dcde9160947002b36b141","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4812","title":"Lower severity of Sourcemark Snippet issue","createdAt":"2023-02-10T23:45:53Z"}
{"state":"Merged","mergedAt":"2023-02-12T20:07:40Z","number":4813,"body":"Add to MDC logging context, API status pages, and tracing (Honeycomb).\n\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1676058583580359?thread_ts=1676058315.461229&cid=C02HEVCCJA3","mergeCommitSha":"3c665def6a7a7b0e78a11570bd1180faab1de3bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4813","title":"Add 'isInsider' field to monitoring for excluding internal traffic","createdAt":"2023-02-10T23:46:48Z"}
{"state":"Merged","mergedAt":"2023-02-11T00:22:53Z","number":4814,"mergeCommitSha":"32399b1e32235a983eee19dcfa2b2473b4efa56f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4814","title":"Add initial value to webviewEventHandler stream","createdAt":"2023-02-11T00:11:15Z"}
{"state":"Merged","mergedAt":"2023-02-11T01:11:07Z","number":4815,"mergeCommitSha":"b4426f0fd34886499952ef954d019bd8703683da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4815","title":"Jetbrains CI runs lint","createdAt":"2023-02-11T01:02:09Z"}
{"state":"Merged","mergedAt":"2023-02-15T19:42:23Z","number":4817,"body":"* Was trying to use `useCapability` in shared code, which didn't work for vscode. Have to pass the flag value into each client view\r\n* NOTE: Will need to do the same for jetbrains code if/when we release it and the flag is still active","mergeCommitSha":"d460fe37e4449c39db86e92afc216fb789596cc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4817","title":"Fix topics tags in vscode","createdAt":"2023-02-11T01:28:59Z"}
{"state":"Merged","mergedAt":"2023-02-11T03:11:46Z","number":4818,"body":"- Email followup\n- Add email followup logic\n","mergeCommitSha":"ec9dda1e53d096e93a449779268edd15b014e382","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4818","title":"EmailFollowup","createdAt":"2023-02-11T02:45:57Z"}
{"state":"Merged","mergedAt":"2023-02-11T03:47:40Z","number":4819,"mergeCommitSha":"64cf56df768bb56daa7c696e650857f8d0ce6a78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4819","title":"Fix template","createdAt":"2023-02-11T03:47:30Z"}
{"state":"Merged","mergedAt":"2022-03-05T05:36:44Z","number":482,"body":"Expected to fail, because we haven't addressed these issues yet.","mergeCommitSha":"42eabcd9ddf184453caa37aaf0aade79bafce0b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/482","title":"Linter prevents using DAO/SQL models outside the DB package","createdAt":"2022-03-05T03:29:55Z"}
{"state":"Merged","mergedAt":"2023-02-11T05:07:58Z","number":4820,"mergeCommitSha":"ebeef9cffd385e3a3cafe731a07227cb63e00882","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4820","title":"Fix identity processing","createdAt":"2023-02-11T05:00:55Z"}
{"state":"Merged","mergedAt":"2023-02-11T06:43:52Z","number":4821,"mergeCommitSha":"36c0e4be8913aa9ebef5f7ae5a9624e3a058d54e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4821","title":"Update ses template","createdAt":"2023-02-11T06:43:30Z"}
{"state":"Merged","mergedAt":"2023-02-11T19:03:40Z","number":4822,"mergeCommitSha":"74387557b99dd7849b2b0fc397a5307b34d9e616","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4822","title":"Reduce service counts in prod. ","createdAt":"2023-02-11T18:52:00Z"}
{"state":"Merged","mergedAt":"2023-02-11T20:45:12Z","number":4823,"body":" - Dev and Prod have been configured to use c5a.2xlarge nodes with 16GB ram. We are still at the same capacity as before node resize\r\n - Update falco rules to exclude some false positives that showed up during deployment\r\n - Reduced pod counts in Dev\r\n \r\n All EKS changes have been deployed to both Dev and Prod\r\n All Falco rule changes have been deployed to both Dev and Prod","mergeCommitSha":"3ade7e0230d7ce87fd853e5c3400812d8a4c5d5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4823","title":"Resize eks nodes ","createdAt":"2023-02-11T20:43:31Z"}
{"state":"Closed","mergedAt":null,"number":4824,"body":"Prevents CI actions from timing out.\n\nCan use @Timeout annotation to override on individual tests where necessary:\n```kotlin\n\n@Timeout(60) // timeout in seconds\n@Test\nfun `slow test`() {  }\n```\n\nOr annotate the test class to override on every test in the class.","mergeCommitSha":"72e04eb7e467973988b56eb4cf52e17edbca7f40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4824","title":"Add default JUnit test timeout","createdAt":"2023-02-12T20:00:40Z"}
{"state":"Merged","mergedAt":"2023-02-12T20:24:29Z","number":4825,"mergeCommitSha":"4c7eea5c07b58bbca4e959dcd58df0a63c756d6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4825","title":"Bitbucket classes are internal","createdAt":"2023-02-12T20:05:58Z"}
{"state":"Merged","mergedAt":"2023-02-13T03:50:47Z","number":4826,"body":"Prevents CI actions from timing out.\n\nCan use @Timeout annotation to override on individual tests where necessary:\n```kotlin\n\n@Timeout(60) // timeout in seconds\n@Test\nfun `slow test`() {  }\n```\n\nOr annotate the test class to override on every test in the class.","mergeCommitSha":"8125cc54793333573e23b50211ab3b4dfc2dee9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4826","title":"Add default JUnit test timeout","createdAt":"2023-02-12T20:55:34Z"}
{"state":"Merged","mergedAt":"2023-02-13T05:56:55Z","number":4827,"body":"Add to MDC logging context and tracing (Honeycomb).\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1676058583580359?thread_ts=1676058315.461229&cid=C02HEVCCJA3","mergeCommitSha":"7d59d58828b309ca0f1f5b558b46e95d9f5cca23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4827","title":"Add 'isInsider' field to monitoring for excluding internal traffic","createdAt":"2023-02-12T20:55:37Z"}
{"state":"Merged","mergedAt":"2023-02-13T03:49:58Z","number":4828,"mergeCommitSha":"e3f8101981cd6bf265c834e2619c474238f63e5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4828","title":"Fix GitLab OAuth","createdAt":"2023-02-13T03:02:30Z"}
{"state":"Merged","mergedAt":"2023-02-13T19:17:32Z","number":4829,"body":"Code duplication :(","mergeCommitSha":"3463d384c610ddc377d253d8dd1895dacf305e33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4829","title":"Update tracing for isInsider across all frontend services","createdAt":"2023-02-13T07:02:55Z"}
{"state":"Merged","mergedAt":"2022-03-06T01:16:42Z","number":483,"body":"Basically move DB stuff from `SourceMarkService` to `SourceMarkStore`","mergeCommitSha":"1d6442e171431a1da2fbc7539cb7d10e46f3ed8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/483","title":"SourcePoint data model","createdAt":"2022-03-05T06:53:58Z"}
{"state":"Merged","mergedAt":"2023-02-13T18:01:50Z","number":4830,"body":"Notion Doc with all the info about this work: https://www.notion.so/nextchaptersoftware/CloudTrail-CloudWatch-Security-Alarms-36617499a4804fbaa0e745bd4b5d9f98?pvs=4\r\n\r\n- Added config file for standard region (us-east-1) under management account\r\n- New config file includes a list of CloudWatch alarms required by CIS benchmark 1.4\r\n- Modified IAM and DNS stacks to be optional\r\n- Added a new stack to create CloudWatch metric filters and CloudWatch alarms\r\n- Added new config data structures to support above changes\r\n\r\nChanges have been deployed and tested. We now receive security pages for events defined in this code change using both emails and on-call escalations ","mergeCommitSha":"8e67bbaf25ef355f8d8adfe2428b18cb49cd4253","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4830","title":"Adding CloudTrail alarms for SecOps","createdAt":"2023-02-13T09:05:09Z"}
{"state":"Merged","mergedAt":"2023-02-13T18:25:21Z","number":4831,"body":"Add additional logs to debug empty files issue in the final step of the tour.\r\n\r\n","mergeCommitSha":"8154fc0ff8830f091f78c65ad4c275d91dd08b37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4831","title":"Additional logging for tour","createdAt":"2023-02-13T17:19:08Z"}
{"state":"Merged","mergedAt":"2023-02-13T19:10:04Z","number":4832,"mergeCommitSha":"61cd258f88af313044405ab5c6acaab03708c5a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4832","title":"Minor email improvements","createdAt":"2023-02-13T18:57:44Z"}
{"state":"Merged","mergedAt":"2023-02-13T19:21:11Z","number":4833,"mergeCommitSha":"7f1c16ec954e2043b4d065b7ace139008d31d389","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4833","title":"Use AbortController instead of AbortSignal","createdAt":"2023-02-13T18:57:59Z"}
{"state":"Closed","mergedAt":null,"number":4834,"mergeCommitSha":"208c8e8826a489bcd31b5927ae1061928f6f4206","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4834","title":"Add logging and refactor onboarding return typing","createdAt":"2023-02-13T19:00:35Z"}
{"state":"Merged","mergedAt":"2023-02-13T20:00:08Z","number":4835,"body":"PowerML was just taking the first approved topic.\r\nThis doesn’t really work well when there are multiople source types.\r\n","mergeCommitSha":"512a058d1a7d1c2af023964b0ff55936bfce5fee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4835","title":"Fix powerml topic mapping across sources","createdAt":"2023-02-13T19:49:54Z"}
{"state":"Merged","mergedAt":"2023-02-13T20:13:13Z","number":4836,"body":"Title is maybe a little misleading. This mostly adds some additional logging to help track down what's happening on Ben's machine","mergeCommitSha":"e3caf32709b654f23c07441d143482b5d3c84efa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4836","title":"Second attempt to fix notifications onboarding flow...","createdAt":"2023-02-13T20:04:38Z"}
{"state":"Merged","mergedAt":"2023-02-13T20:29:50Z","number":4838,"body":"https://chapter2global.slack.com/archives/C03NVHV37EF/p1676311065853919?thread_ts=1676310785.235869&cid=C03NVHV37EF","mergeCommitSha":"440a15411044e8e8d2cf4400ffe995bd0e73bf51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4838","title":"Do not send Slack announcements for internal teams or users","createdAt":"2023-02-13T20:12:40Z"}
{"state":"Merged","mergedAt":"2023-02-13T21:00:46Z","number":4839,"body":"Applications are registered here:\r\nhttps://bitbucket.org/getunblocked/workspace/settings/api\r\n\r\nMore info on our workspace:\r\nhttps://www.notion.so/nextchaptersoftware/Bitbucket-2d547f0fa979416d87d970e2c8da1e76","mergeCommitSha":"6a0aba44376b6a2624739e49722804526be90a70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4839","title":"Add Bitbucket OAuth secrets","createdAt":"2023-02-13T20:35:31Z"}
{"state":"Merged","mergedAt":"2022-03-07T17:30:26Z","number":484,"mergeCommitSha":"aca2ecf9e000e5ed640509e6e1af25bdd75ce8dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/484","title":"Remove internal -- doesn't do anthing","createdAt":"2022-03-06T04:37:33Z"}
{"state":"Merged","mergedAt":"2023-02-13T21:12:30Z","number":4840,"mergeCommitSha":"fda26a104aa4457d80b375c7b9419f185700866b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4840","title":"benedict-bb is an internal user","createdAt":"2023-02-13T20:57:15Z"}
{"state":"Merged","mergedAt":"2023-02-13T21:20:19Z","number":4841,"body":"Added ability to see topics for a given thread and to have them link back to the topic page.","mergeCommitSha":"e3de9035f9178e05ee424841280efbeb676a05cd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4841","title":"Add ability to view topics for a single thread","createdAt":"2023-02-13T21:09:44Z"}
{"state":"Merged","mergedAt":"2023-02-13T21:30:54Z","number":4843,"body":"The async `requestAuthorization` function will block until the user interacts with the notification permissions toast. Very strange, but we can ignore that behaviour since we always dump the user to system settings.","mergeCommitSha":"cb73937bb40a7f13f3617c223d54aa3aab1c2042","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4843","title":"Fixes system settings launch bug","createdAt":"2023-02-13T21:19:59Z"}
{"state":"Open","mergedAt":null,"number":4845,"body":"Because of this issue: https://github.com/microsoft/vscode/issues/174279 -- whenever a proxy (VPN, Proxyman, etc) shuts down or changes configuration, any open VSCode extensions start to fail.\r\n\r\nThis could affect customers, but definitely seems to be affecting Dennis, so to reduce noise this PR tries to detect this scenario and displays a UI to restart VSCode.\r\n\r\n<img width=\"1552\" alt=\"Screen Shot 2023-02-13 at 1 39 44 PM\" src=\"https://user-images.githubusercontent.com/2133518/218581539-08119838-fdcb-45e1-9981-bc7907f0a937.png\">\r\n","mergeCommitSha":"bb7404d99cb6a8751bedb3de97c310c2e4d86f7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4845","title":"Offer to restart VSCode when we detect a dead proxy","createdAt":"2023-02-13T21:45:14Z"}
{"state":"Merged","mergedAt":"2023-02-13T22:31:32Z","number":4846,"mergeCommitSha":"ba36026557ad4638026845565b93146e52612f5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4846","title":"Move MockObjects to parent package","createdAt":"2023-02-13T22:17:57Z"}
{"state":"Merged","mergedAt":"2023-02-28T21:19:02Z","number":4847,"body":"When a log request fails, we should not retry as it causes an large loop of requests.","mergeCommitSha":"fa8993ba3c2baed1a0194bded616ef5f27d7ee0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4847","title":"Stop retry logs","createdAt":"2023-02-13T22:19:15Z"}
{"state":"Merged","mergedAt":"2023-02-14T01:20:40Z","number":4849,"body":"When going through the onboarding tutorial for the first time as the first user, there was a chance that no top files would appear in the final step.\r\n\r\nThis is due to a race condition on when `getTopFiles` is called & PR ingestion starts.\r\n\r\n`getTopFiles` depends on the initialization of SMStore. If SMStore is initialized *before* PR ingestion occurs (expected behaviour), this leads to `getTopFiles` returning empty.\r\n\r\nSince we call `getTopFiles` eagerly in the tutorial, which is immediately after repo installation, there's a chance that getTopFiles returns empty when it could potentially return files once PR ingestion has ingested some files.\r\n\r\nThis is ultimately a design problem as there's no guarantee that PR ingestion has pulled in data before we get to the end of tutorial. In the meantime, the workaround is to try calling `getTopFiles` again right before we display the top files page if the initial request returned empty.","mergeCommitSha":"0b154279cc85be048ba4667ea2d51896d33a1ccc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4849","title":"Potential Fix for missing files at end of tutorial","createdAt":"2023-02-13T22:32:31Z"}
{"state":"Merged","mergedAt":"2022-03-06T05:03:46Z","number":485,"mergeCommitSha":"b9f94c7279cacac7d0f62372adb947d013a388e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/485","title":"Move clearCalculatedSourcePoints to SourceMarkStore","createdAt":"2022-03-06T04:37:43Z"}
{"state":"Merged","mergedAt":"2023-02-14T00:40:27Z","number":4850,"body":"Bitbucket pull requests only include a truncated version of the merge commit SHA, so when a pull request is merged we'll need to hit the [get commit](https://developer.atlassian.com/cloud/bitbucket/rest/api-group-commits/#api-repositories-workspace-repo-slug-commit-commit-get) operation to get the merged-at date and full commit SHA which we'll need for the sourcemark.","mergeCommitSha":"05fee8224cd39931be21c46f033af91b331c1463","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4850","title":"Add BitbucketCommit to allow getting the full commit sha for a merged pull request","createdAt":"2023-02-13T22:49:14Z"}
{"state":"Merged","mergedAt":"2023-02-13T23:41:59Z","number":4851,"body":"PowerML endpoint can be flaky.\r\nWe need to make it more resilient.","mergeCommitSha":"2bac540b81394040d85399e663e559f420d7c65f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4851","title":"Use a fallback service for generating insight to topics mapping","createdAt":"2023-02-13T23:29:14Z"}
{"state":"Merged","mergedAt":"2023-02-14T01:20:34Z","number":4852,"body":"https://linear.app/unblocked/issue/UNB-996/poller-shuts-down-prematurely-leading-to-unexpected-infinite-spinner","mergeCommitSha":"34777f5472a9efbddc1fbaf3b743c2dfa8e330fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4852","title":"Shut down poller unless it has been recently focused","createdAt":"2023-02-13T23:31:23Z"}
{"state":"Merged","mergedAt":"2023-02-14T00:25:06Z","number":4853,"body":"Remove dead code.","mergeCommitSha":"ca0ecbc021c80b71edad18e76bb675d3aad40749","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4853","title":"Remove DropdownButton","createdAt":"2023-02-13T23:36:54Z"}
{"state":"Merged","mergedAt":"2023-02-14T00:29:58Z","number":4854,"mergeCommitSha":"7a6034b5a4a5710f19be000365c8c7880ae34583","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4854","title":"Add ability to send invite emails from admin console","createdAt":"2023-02-14T00:13:06Z"}
{"state":"Merged","mergedAt":"2023-02-15T19:40:38Z","number":4857,"mergeCommitSha":"1636f4998a259592b2b08d1a9592f4850374c9a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4857","title":"Refactor GitHub and GitLab into ScmAuthApiFactory","createdAt":"2023-02-14T07:30:37Z"}
{"state":"Merged","mergedAt":"2023-02-14T20:25:09Z","number":4858,"body":"This PR breaks down CloudWatch alarms per account so we could narrow down the event source easier. ","mergeCommitSha":"0df631c4535bf56f217d8d9e8da4b044d1674a7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4858","title":"Add metric dimension cloudwatch","createdAt":"2023-02-14T20:24:16Z"}
{"state":"Merged","mergedAt":"2022-03-06T05:00:37Z","number":486,"body":"https://foresight.docs.thundra.io/integrations/github-actions/test-monitoring/monitoring-java-tests-using-github-actions#starting-with-a-gradle-workflow\r\n\r\n---\r\n\r\n<img width=\"1705\" alt=\"Screen Shot 2022-03-05 at 21 09 22\" src=\"https://user-images.githubusercontent.com/1798345/*********-0d89692d-8929-4cfe-a059-99e1eb169e7e.png\">\r\n","mergeCommitSha":"00099edccec889f1646d5fa4133447a6dc929590","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/486","title":"Instrumented tests with Thundra","createdAt":"2022-03-06T04:55:01Z"}
{"state":"Closed","mergedAt":null,"number":4860,"body":"<img width=\"1109\" alt=\"CleanShot 2023-02-15 at 09 11 05@2x\" src=\"https://user-images.githubusercontent.com/1553313/219102256-610f8274-f21b-4da0-8aa0-7e192b5d54d1.png\">\r\n\r\nBasic setup to render gutter icons.\r\nCurrently hard-coded until we have models and store to back it.","mergeCommitSha":"913446477ce9d896a29cca4f14b57db57ffa1c7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4860","title":"Basic gutter icon","createdAt":"2023-02-15T17:11:52Z"}
{"state":"Merged","mergedAt":"2023-02-15T19:45:09Z","number":4861,"body":"Replaces:\n- GitHubUserApiFactory\n- GitLabUserApiFactory","mergeCommitSha":"a4b47f6845a3ba7a631afb4c114242d4c2c66fca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4861","title":"Introduce ScmUserApiFactory and ScmUserApi","createdAt":"2023-02-15T17:29:46Z"}
{"state":"Merged","mergedAt":"2023-02-22T00:29:29Z","number":4862,"body":"<img width=\"690\" alt=\"Screenshot 2023-02-15 at 10 56 47\" src=\"https://user-images.githubusercontent.com/1798345/219125907-71c8b911-a01d-4d1f-82fc-1fc9908bb49b.png\">\r\n\r\n\r\n<img width=\"1015\" alt=\"Screenshot 2023-02-15 at 10 57 34\" src=\"https://user-images.githubusercontent.com/1798345/219126066-6ed23f04-3bd9-4808-a918-f73330e0b3f3.png\">\r\n","mergeCommitSha":"d907020952c564934955dad215d5613c7ba651b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4862","title":"Bitbucket auth flow","createdAt":"2023-02-15T17:29:50Z"}
{"state":"Merged","mergedAt":"2023-02-15T18:28:40Z","number":4863,"body":"Renovate auto-updated our version of React, but not the types.  This updates the types and brings react and react-dom up to parity.\r\n\r\nThis fixes a bunch of warnings displayed when running `npm install`","mergeCommitSha":"e1a39ed8d98e5be280fb20b3cd19e99877408c17","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4863","title":"Fix up React dependencies","createdAt":"2023-02-15T17:50:33Z"}
{"state":"Merged","mergedAt":"2023-02-15T21:22:03Z","number":4865,"body":"Address this exception\r\n\r\nhttps://app.logz.io/#/dashboard/osd/?_a=(columns%3A!(message)%2Cfilters%3A!(('%24state'%3A(store%3AappState)%2Cbool%3A(minimum_should_match%3A1%2Cshould%3A!((match_phrase%3A(_logzio_logceptions%3Aa90f2e68be07a395aae24b9ad5e05d39))))%2Cmeta%3A(alias%3AIllegalStateException%2Cdisabled%3A!f%2Ckey%3Abool%2ClogzInsights%3A!t%2Cnegate%3A!f%2Ctype%3Acustom%2Cvalue%3A'%7B%22minimum_should_match%22%3A1%2C%22should%22%3A%5B%7B%22match_phrase%22%3A%7B%22_logzio_logceptions%22%3A%22a90f2e68be07a395aae24b9ad5e05d39%22%7D%7D%5D%7D')))%2Cindex%3A'logzioCustomerIndex*'%2Cinterval%3Aauto%2Cquery%3A(language%3Alucene%2Cquery%3A'')%2Csort%3A!())&_g=(filters%3A!()%2CrefreshInterval%3A(pause%3A!t%2Cvalue%3A0)%2Ctime%3A(from%3Anow-1d%2Cto%3A'2023-02-15T09%3A10%3A00.000Z'))&accountIds=411850&switchToAccountId=411850&discoverTab=logz-exceptions-tab","mergeCommitSha":"af6ca0f5d45a416889d9ca0ab0a33a520727e767","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4865","title":"Fix bug with restoring archive pull requests","createdAt":"2023-02-15T19:07:25Z"}
{"state":"Merged","mergedAt":"2023-02-15T21:11:26Z","number":4866,"body":"Added stream for visible files.\r\n\r\nWhenever one refocuses VSCode, it will recalculate source marks for visible editors.\r\n\r\nActive File:\r\n\r\nhttps://user-images.githubusercontent.com/1553313/*********-9d3ff404-629f-4653-9536-3366220753a5.mp4\r\n\r\n\r\nVisible File:\r\n\r\nhttps://user-images.githubusercontent.com/1553313/*********-edcebfec-6f4f-4ec6-bb67-7deb6c503cb6.mp4\r\n\r\n","mergeCommitSha":"888e6f6afd9569e74aa32faccb8b5cadd0d5a1ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4866","title":"Focus refresh file source mark","createdAt":"2023-02-15T19:13:29Z"}
{"state":"Merged","mergedAt":"2023-02-15T19:46:14Z","number":4867,"body":"Needs to be done for all the other GitHub* types.","mergeCommitSha":"1a17f628762c667ed60bb473be8b56e80c3af4c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4867","title":"Use generic ScmPullRequestReview instead of GitHubPullRequestReview","createdAt":"2023-02-15T19:16:32Z"}
{"state":"Merged","mergedAt":"2023-02-17T18:45:49Z","number":4868,"body":"React 18 updated the `react-dom` API and deprecated the old one.  I made a small wrapper to log failures and allow simple one-method rendering.  This will get rid of all the errors/warnings in the console.","mergeCommitSha":"4505742e1a8e9e3e520d1abb96f8091daf86c392","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4868","title":"Use new React DOM API","createdAt":"2023-02-15T19:48:41Z"}
{"state":"Merged","mergedAt":"2023-02-15T21:07:25Z","number":4869,"body":"Minor refactor before making additional changes","mergeCommitSha":"374e2c98ab9c5a16fd1d80f2c5a5ecea06dfeb33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4869","title":"Refactor team member pages","createdAt":"2023-02-15T20:54:43Z"}
{"state":"Merged","mergedAt":"2022-03-07T17:57:11Z","number":487,"body":"Presume this was a bug. Since the threadId is already required in the path, no need to duplicate in the body.","mergeCommitSha":"5cafdac1eb82a20790c41fffa4743ecbfa04783e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/487","title":"Removes duplicate thread ID in create/update APIs","createdAt":"2022-03-07T07:33:20Z"}
{"state":"Merged","mergedAt":"2023-02-22T00:55:45Z","number":4870,"mergeCommitSha":"c6d06b6737f2251a3be9dfd82a899afae1938a49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4870","title":"Persist user OAuth refresh token on Identity model","createdAt":"2023-02-15T21:14:58Z"}
{"state":"Closed","mergedAt":null,"number":4871,"body":"Move jet brains extension into `projects/apps/ide` to follow existing pattern.\r\n\r\nMotivation: shared config, shared code reuse, consistency, shared CI.","mergeCommitSha":"a17ad9872aca049239c07cc5e1c1cfef702b20e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4871","title":"Move JB Plugin","createdAt":"2023-02-15T21:59:23Z"}
{"state":"Merged","mergedAt":"2023-02-15T23:53:40Z","number":4872,"body":"- Enabled bucket versioning for all customer asset buckets\r\n- Enabled bucket versioning for our software releases bucket","mergeCommitSha":"721b2f3ff32653d68d4436be9db0c21152c22c26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4872","title":"enable S3 bucket version","createdAt":"2023-02-15T22:52:23Z"}
{"state":"Merged","mergedAt":"2023-02-15T23:15:01Z","number":4873,"mergeCommitSha":"8394400e2743ece82d4d5b7b031ac61ae99460a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4873","title":"Fix archivePullRequests logic","createdAt":"2023-02-15T23:03:24Z"}
{"state":"Merged","mergedAt":"2023-02-15T23:21:47Z","number":4874,"mergeCommitSha":"2231d1a03b5ba4f6b9ecb10fe13575786e88fe40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4874","title":"Show the remaining pull requests yet to be fully ingested","createdAt":"2023-02-15T23:15:25Z"}
{"state":"Merged","mergedAt":"2023-02-15T23:42:45Z","number":4875,"body":"Log clietn debug as info until I can fix this properly via custom threshold filter.\r\n","mergeCommitSha":"21b82b0fc52c05736dc647b5711d4165162f472b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4875","title":"Do not have time to address this properly, so log debug as info","createdAt":"2023-02-15T23:42:12Z"}
{"state":"Merged","mergedAt":"2023-02-16T00:37:23Z","number":4876,"mergeCommitSha":"edee73397e68fbd92574124a7b10270d498e1448","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4876","title":"Add ability to store s3 metadata into data assets","createdAt":"2023-02-16T00:15:23Z"}
{"state":"Merged","mergedAt":"2023-02-16T00:42:10Z","number":4877,"body":"This reverts commit 2231d1a03b5ba4f6b9ecb10fe13575786e88fe40.","mergeCommitSha":"6f353cfd4abbf290c1afb26393cb4483ffdd1086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4877","title":"Revert \"Show the remaining pull requests yet to be fully ingested (#4874)\"","createdAt":"2023-02-16T00:22:53Z"}
{"state":"Merged","mergedAt":"2023-02-16T01:06:36Z","number":4878,"mergeCommitSha":"95dc7119dbb91c1ae5c0b2edfb99f17a6f35dad3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4878","title":"Add s3 metadata during glue run for topics","createdAt":"2023-02-16T01:06:04Z"}
{"state":"Merged","mergedAt":"2023-02-16T17:32:15Z","number":4879,"body":"Turns out the only way to get the (short) commit sha for a comment is to parse it from the code link.\r\n\r\nNotes\r\n- the endpoint to get pull request comments includes both code comments and top-level comments. The only difference between the two is the presence of the inline property, which specifies the file and line the code refers to\r\n- every comment (TLC or code) can have a reply. More over even code comment replies can have nested replies, which differs from GitHub where all threads are flat.","mergeCommitSha":"7dfe8dc3357a6cb81264c4b47b6486b5826baef2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4879","title":"Add Bitbucket comment class","createdAt":"2023-02-16T05:22:02Z"}
{"state":"Merged","mergedAt":"2022-03-07T18:09:53Z","number":488,"body":"Also fix an Exposed warning about SHA-1 column types","mergeCommitSha":"53fc09c13ac5b8c81a8c0a5ea3a2a63b24d6675c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/488","title":"Verbosely log only our own packages","createdAt":"2022-03-07T07:49:36Z"}
{"state":"Closed","mergedAt":null,"number":4880,"body":"Introduce a spec file where one can introduce client side models.\r\n\r\nHaving these specced out will allow for a common language used across multiple languages.\r\n\r\nE.g. communication between IntelliJ (Kotlin) + Agent (TS)","mergeCommitSha":"6bef484c7e982a8b531b5fe0eec1943a3d8acbf7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4880","title":"Extra Spec File","createdAt":"2023-02-16T17:08:26Z"}
{"state":"Merged","mergedAt":"2023-02-16T18:49:58Z","number":4881,"body":"We'll need this for pagination when bulk ingesting pull requests from Bitbucket.","mergeCommitSha":"331c31d8931493c49d995d812796b94517908928","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4881","title":"Deserialize next field for paginated Bitbucket responses","createdAt":"2023-02-16T18:33:39Z"}
{"state":"Merged","mergedAt":"2023-02-16T19:08:22Z","number":4882,"mergeCommitSha":"3c0c8aeffaa855492ebb179d1f7c14d365eb91f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4882","title":"Add prod s3 metadata","createdAt":"2023-02-16T19:05:42Z"}
{"state":"Merged","mergedAt":"2023-02-16T22:33:22Z","number":4883,"body":"<img width=\"934\" alt=\"CleanShot 2023-02-16 at 11 27 09@2x\" src=\"https://user-images.githubusercontent.com/858772/219467544-cd482ce8-e8c2-456b-bd8b-5351cca16f03.png\">\r\n\r\n<img width=\"488\" alt=\"CleanShot 2023-02-16 at 11 29 13@2x\" src=\"https://user-images.githubusercontent.com/858772/219467644-347b367b-1ad2-4c5e-953e-34fcc2e418a8.png\">\r\n\r\n","mergeCommitSha":"ad12f2a70927335a4bd55f16e9e73d5b77e75c0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4883","title":"Add web titles as subtitles for threads in the Hub","createdAt":"2023-02-16T19:28:21Z"}
{"state":"Merged","mergedAt":"2023-02-22T18:31:09Z","number":4884,"body":"https://user-images.githubusercontent.com/13431372/219820159-764e86c8-683c-4e4f-aafa-49d2ef0c7709.mp4\r\n\r\n\r\nAlso:\r\n* Fix a couple of bugs in the current search/filter UI \r\n* Fix some small viewport layout issues in PR views","mergeCommitSha":"45de6b58c9f1260d84a43a954582c989481067e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4884","title":"Add search to mobile","createdAt":"2023-02-16T20:40:55Z"}
{"state":"Merged","mergedAt":"2023-02-16T21:00:58Z","number":4885,"mergeCommitSha":"5eb987f77d782565b9e091d34462f4def6eb5ba2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4885","title":"Add minimum histogram threshold for slack","createdAt":"2023-02-16T20:43:15Z"}
{"state":"Merged","mergedAt":"2023-02-16T22:50:30Z","number":4886,"body":"Bitbucket pull request comments allow for replies to have replies:\r\n\r\n<img width=\"1344\" alt=\"CleanShot 2023-02-16 at 13 04 51@2x\" src=\"https://user-images.githubusercontent.com/1924615/219486604-0997d422-905b-4011-bc2b-e2e20ec753d0.png\">\r\n\r\nUnblocked doesn't show comments like this (yet) but let's add a couple of properties to store these nested reply details. If/when we update our UI to support this type of nesting we'll have all the data we need to recreate it.","mergeCommitSha":"122ab709781ea65c1838dc592365d4d43f278081","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4886","title":"Add prCommentParentId property to track nested replies","createdAt":"2023-02-16T21:10:00Z"}
{"state":"Closed","mergedAt":null,"number":4887,"body":"- Add minimum histogram threshold for slack\r\n- Add more logging during slack ingestion for topics\r\n","mergeCommitSha":"8e66f6b5c4936ad2a20f0c81d75e230827dc0f24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4887","title":"AddMoreLogging","createdAt":"2023-02-16T23:00:01Z"}
{"state":"Merged","mergedAt":"2023-02-16T23:01:55Z","number":4888,"mergeCommitSha":"0256b1eecc92a94017a7264c0fbe5f7f3c83b050","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4888","title":"Add more logging for slack ingestion fo topics","createdAt":"2023-02-16T23:01:44Z"}
{"state":"Merged","mergedAt":"2023-02-17T17:24:55Z","number":4889,"body":"This will let us transform Bitbucket and GitHub pull request comments to the three main types of comments (top-level, file, and code). All comments are pretty much the same, with file and code comments having additional properties to specify the file and lines.","mergeCommitSha":"d16a4064200b240e264663239ff8837fa701a97d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4889","title":"Refactor ScmPullRequestComment for top level comments and file comments","createdAt":"2023-02-17T00:07:21Z"}
{"state":"Merged","mergedAt":"2022-03-07T21:54:31Z","number":489,"body":"Stores for video channel models","mergeCommitSha":"cd4ecd42f6195a1e22c1bce145cfc3d8063f462d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/489","title":"Video Channel Stores","createdAt":"2022-03-07T16:12:50Z"}
{"state":"Merged","mergedAt":"2023-02-17T01:10:16Z","number":4890,"mergeCommitSha":"3397aa4b19e5a1a0f772788ea87060de5e363e73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4890","title":"Make jetbrains webview build inremental","createdAt":"2023-02-17T00:47:32Z"}
{"state":"Merged","mergedAt":"2023-02-21T18:17:53Z","number":4891,"body":"Refactored model definitions out of Private.yml and into individual files. \r\n\r\n1. Reduces size of 7k LOC file to 4k\r\n2. Allows for better composition of models. Will allow for composition of API models for client side models (Aggregate models)\r\n\r\nAPIs & API specific models (parameters, security schemas) will still remain in Private.yml\r\n\r\n","mergeCommitSha":"477868284eb6beea8232f64d9a11c7b8a4f45624","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4891","title":"Private.yml Refactor + Support for Extra Aggregate Models","createdAt":"2023-02-17T01:21:42Z"}
{"state":"Merged","mergedAt":"2023-02-21T18:39:50Z","number":4892,"body":"download vm files:\r\n- [s3://macos-virtual-machines/13.2.1/13.2.1_aux.img](https://macos-virtual-machines.s3-us-west-2.amazonaws.com/13.2.1/13.2.1_aux.img)\r\n- [s3://macos-virtual-machines/13.2.1/13.2.1_disk.img](https://macos-virtual-machines.s3-us-west-2.amazonaws.com/13.2.1/13.2.1_disk.img)\r\n- [s3://macos-virtual-machines/13.2.1/13.2.1_config.img](https://macos-virtual-machines.s3-us-west-2.amazonaws.com/13.2.1/13.2.1_config.json)\r\n\r\nrun the virtual machine\r\n./macosvm -g ./13.2.1_config.json","mergeCommitSha":"e839dfef73a1bdd00aa5f31f6309fb58c239328a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4892","title":"adding macosvm + readme for unblocked macos testing","createdAt":"2023-02-17T01:48:27Z"}
{"state":"Merged","mergedAt":"2023-02-17T04:57:52Z","number":4893,"mergeCommitSha":"f4d285be58a3997a8f176b56ffa5fdbb05ef4010","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4893","title":"Refactor OAuth client config","createdAt":"2023-02-17T02:59:12Z"}
{"state":"Merged","mergedAt":"2023-02-21T22:18:02Z","number":4894,"body":"Added basic hook to generate client data models (No APIs atm)\r\nCurrently being used for IntelliJ plugin\r\n","mergeCommitSha":"e6243d6fb69b42917c85ab284dbf7f8227b3b8b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4894","title":"Basic Kotlin client codegen","createdAt":"2023-02-17T16:41:07Z"}
{"state":"Merged","mergedAt":"2023-02-17T18:45:40Z","number":4895,"body":"https://github.com/NextChapterSoftware/machine-learning\r\n\r\nMoved to above repository.","mergeCommitSha":"58d35bc669d50a90652ef5df9d398f51b3aa0a1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4895","title":"Removing sandboxed ml files","createdAt":"2023-02-17T18:30:46Z"}
{"state":"Merged","mergedAt":"2023-02-21T18:27:42Z","number":4896,"mergeCommitSha":"8d61d0916119ef4795919e58ad7c15cdc60b1238","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4896","title":"JB agent protobuf generation and call","createdAt":"2023-02-17T19:45:01Z"}
{"state":"Merged","mergedAt":"2023-02-17T23:44:57Z","number":4897,"body":"Also increased polling interval to 5 seconds","mergeCommitSha":"4eb10d4e3abf56f53df6b43110bf84012f847872","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4897","title":"Stop polling from Hub when laptop is locked or asleep","createdAt":"2023-02-17T19:59:02Z"}
{"state":"Merged","mergedAt":"2023-02-17T20:20:03Z","number":4898,"mergeCommitSha":"468359fb4defbc765f770a17351b9393042c6bd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4898","title":"Do not remove pr links from text","createdAt":"2023-02-17T20:15:03Z"}
{"state":"Merged","mergedAt":"2023-02-17T21:03:35Z","number":4899,"mergeCommitSha":"bb446916f9c7aba8e88270436f26b713279b1056","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4899","title":"Optimize sanitizer script for powerml","createdAt":"2023-02-17T21:03:30Z"}
{"state":"Merged","mergedAt":"2022-01-17T23:44:31Z","number":49,"body":"Remove bs files and move makefile down one level.","mergeCommitSha":"a09f6e6c1e704f91db8e6502fcefda16ea8d91a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/49","title":"Clean up files","createdAt":"2022-01-17T23:34:06Z"}
{"state":"Merged","mergedAt":"2022-03-07T22:08:35Z","number":490,"body":"Refactor out message editor button.\r\nAllow for Command + Enter to send message.","mergeCommitSha":"c9e5ca1806c8438daf0b83b05c39bcc6d654c537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/490","title":"Command + Enter to send message","createdAt":"2022-03-07T17:05:31Z"}
{"state":"Merged","mergedAt":"2023-02-21T23:46:04Z","number":4900,"mergeCommitSha":"aced899615c8a153ab122f6fadaf6d8d31c0b44b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4900","title":"Add Bitbucket comment transformers","createdAt":"2023-02-17T21:57:26Z"}
{"state":"Merged","mergedAt":"2023-03-07T04:35:02Z","number":4901,"body":"I've moved the large client assets (the VSCode onboarding videos) into a submodule here: https://github.com/NextChapterSoftware/unblocked-client-assets.\r\n\r\nThis PR adds that repo as a submodule at `/shared/clientAssets` and uses the assets from there.  The point of doing this now is to stop the bleeding and ensure any future videos are added in a submodule, which can be swapped out etc in the future if it gets too big.","mergeCommitSha":"8551732168858c862fe217d6b55334c8cdd93268","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4901","title":"Use client asset repo submodule","createdAt":"2023-02-17T23:09:34Z"}
{"state":"Merged","mergedAt":"2023-02-18T01:40:45Z","number":4902,"body":"Request:\n```\ncurl -X GET 'https://getunblocked.com/api/login/options' -i\n```\n\nResponse:\n```\nserver: Ktor/debug\ndate: Sat, 18 Feb 2023 01:10:49 GMT\n```","mergeCommitSha":"9487f60cce4c45e042e622deb7c2f219121915f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4902","title":"Remove Server and Date default headers","createdAt":"2023-02-18T01:12:43Z"}
{"state":"Merged","mergedAt":"2023-02-18T01:51:23Z","number":4903,"body":"Manually for now, will be background job.","mergeCommitSha":"6e4c621050037557480730ddc5c05c34957b16ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4903","title":"Ability to refresh user profiles","createdAt":"2023-02-18T01:32:28Z"}
{"state":"Closed","mergedAt":null,"number":4904,"body":"Note: must run migration before this.","mergeCommitSha":"6a5e971c10689686508d2716f10d466ef483bb18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4904","title":"Use RSA-0496 by default","createdAt":"2023-02-18T01:59:53Z"}
{"state":"Merged","mergedAt":"2023-02-19T04:18:04Z","number":4905,"mergeCommitSha":"0416e41951559991751029a53afad3bdf6522617","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4905","title":"Adds user secret to admin web so that it can run user secret migration and profile refresh","createdAt":"2023-02-18T05:48:08Z"}
{"state":"Merged","mergedAt":"2023-02-21T22:05:29Z","number":4906,"body":"- Created new configuration files for dev and prod us-east-2\r\n- Bootstraped new regions for CDK deployments\r\n- Modified our core environment deployment function to avoid deploying expensive compoents to cold-site\r\n- Created replica S3 buckets in cold sites\r\n- Added functionality to create replication rules for customer asset buckets\r\n- Updated a couple of outdated functions that CDK was complaining about\r\n- Enabled bucket versioning on customer asset buckets (Required for SOC2)\r\n\r\nNote: I have applied the user data retention lifecycle rule only to us-east-2 (replica buckets) to make sure they work as expected before applying them to our main buckets. ","mergeCommitSha":"3f5d6f452d908071f0d507858be8dc054fba0537","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4906","title":"Adding support for a cold region","createdAt":"2023-02-21T08:45:50Z"}
{"state":"Closed","mergedAt":null,"number":4907,"body":"Bitbucket only provides the short sha for comments. I'm thinking we store this as a separate property on SourcePointModel and set `commitHash` to a sentinel value, then have the API return the short sha for `SourcePoint.commitHash` (API model) whenever this sentinel value is detected.\r\n\r\nAlternatively, we could rename this property to `commitHashString` and store either the full or short sha in this property depending on what we're given, and then deprecate `commitHash`.\r\n\r\nWe would need to update the sourcemark engine to handle short shas, but only before we onboard a bitbucket customer.  ","mergeCommitSha":"27956c7e736ccbcb01dc272a08f4cec28397d622","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4907","title":"[RFC] Add SourcePointModel.commitShortSha","createdAt":"2023-02-21T17:21:50Z"}
{"state":"Merged","mergedAt":"2023-02-22T00:40:22Z","number":4908,"body":"Limitation is that odd length hashes cannot be faithfully persisted as binary,\nso we coerce the odd length hash to even length by dropping one character first.","mergeCommitSha":"ef5a84e581acdea2ae9b5bc04d1d94915f2a4ea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4908","title":"Model short hashes in DB","createdAt":"2023-02-21T18:18:10Z"}
{"state":"Merged","mergedAt":"2023-02-21T18:35:45Z","number":4909,"mergeCommitSha":"5761f90e1a1c6c4e3b2c45c0ac4fdc09efe4823f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4909","title":"Minor decryption script","createdAt":"2023-02-21T18:30:57Z"}
{"state":"Merged","mergedAt":"2022-03-07T19:27:39Z","number":491,"body":"cleanup ","mergeCommitSha":"cde9eea589e5e59763da6b27b7a87f14d9a37586","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/491","title":"All DAO models extend EntityExtensions","createdAt":"2022-03-07T18:41:33Z"}
{"state":"Merged","mergedAt":"2023-02-21T19:07:01Z","number":4910,"mergeCommitSha":"ed6cb77de91b9ae3688337954a40501bad8ef881","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4910","title":"Rename to ScmPRComment","createdAt":"2023-02-21T18:45:27Z"}
{"state":"Merged","mergedAt":"2023-02-21T21:58:01Z","number":4911,"body":"This will add the following to all responses:\r\n```\r\nX-Content-Type-Options: nosniff\r\nStrict-Transport-Security: max-age=15724800; includeSubdomains\r\nX-Frame-Options: SAMEORIGIN\r\nReferrer-Policy: strict-origin\r\n```\r\n\r\nThis also REMOVES the `Server` header from all responses.\r\n\r\nIn a follow-up PR, we're going to introduce:\r\n\r\n`Permissions-Policy: fullscreen=(self)`\r\n`Content-Security-Policy: <fml>`","mergeCommitSha":"aeb54b53fbe4fc7aef3f98bd9fb478f07cc255c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4911","title":"Add default security headers to CloudFront","createdAt":"2023-02-21T21:41:17Z"}
{"state":"Merged","mergedAt":"2023-02-21T22:11:24Z","number":4912,"body":"Zally lint has an older version of SwaggerParser which breaks parsing relative ref (https://github.com/swagger-api/swagger-parser/issues/1865)\r\n\r\nWill disable until Zally updates versions.\r\nhttps://github.com/zalando/zally/issues/1445","mergeCommitSha":"86092b84d11b33f176a2405dd39e0b8f1177affe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4912","title":"Temp disable zally","createdAt":"2023-02-21T21:52:32Z"}
{"state":"Merged","mergedAt":"2023-02-21T23:48:46Z","number":4913,"body":"We used to briefly display the sidebar UI, and hop back to the explorer UI, so that the sidebar badge would display.  But we removed sidebar badges, so this is not needed anymore.","mergeCommitSha":"6e6536594a4efa959e7bcf8c75822a31504a3d9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4913","title":"VSCode: remove hop to display sidebar UI","createdAt":"2023-02-21T21:57:18Z"}
{"state":"Merged","mergedAt":"2023-02-21T23:45:09Z","number":4914,"body":"#### Changes\n- DB model changes\n   - added binary `rawAccessToken`\n- admin web\n   - show encrypted access tokens\n   - migration to re-encrypt secrets for a person's identities\n   - migration ro Re-encrypt secrets for all identities\n\n#### Migration plan\n- [x] run in LOCAL\n- [x] run in DEV\n- [x] run in PROD on people in our team\n- [x] run in PROD for all people\n\n#### Rollback plan\n- the legacy `accessToken` field values are untouched, so rollback plan is simply revert this change","mergeCommitSha":"8a0781179e8c14e8b54d7e4706f41aa319184b68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4914","title":"Re-encrypt user SCM secrets","createdAt":"2023-02-21T22:14:29Z"}
{"state":"Merged","mergedAt":"2023-02-21T23:52:03Z","number":4915,"body":"The linter picked up a bunch of non-linted changes. \r\n\r\nMy changes are all in cloudfront-stack.ts","mergeCommitSha":"5c848c23d08b3815201f30a4c9e183a67f54c19f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4915","title":"Apply security policy to all paths","createdAt":"2023-02-21T22:31:00Z"}
{"state":"Merged","mergedAt":"2023-02-22T01:14:06Z","number":4916,"body":"TIL: permissions-policy is a deny list, which is possibly the biggest design error in all of web security history: https://github.com/w3c/webappsec-permissions-policy/issues/189\r\n\r\n","mergeCommitSha":"c4692a2248ca37515c7335585b6bcb98c858b8e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4916","title":"Adds permissions-policy to security headers","createdAt":"2023-02-21T23:01:49Z"}
{"state":"Merged","mergedAt":"2023-02-22T00:43:06Z","number":4917,"body":"Fixes https://linear.app/unblocked/issue/UNB-1014/vscode-extension-spinsdoesnt-load-on-new-machine-learning-repo\r\nFixes the issue a customer was running into here: https://app.intercom.com/a/apps/crhakcyc/inbox/inbox/5472711/conversations/304\r\n\r\nThe problem seems to be the following:  VSCode publishes a command for every view labelled `MySpecialView.focus`, which causes that view to be opened and focused.  This *does not work* correctly when the extension is starting up, at least not with our extension insight view.\r\n\r\nWe had previously done this by running a second (manually-defined) command first, which runs that predefined command.  That seems to work fine, for some reason.  I refactored/simplified this code and removed the manual command (because it did not appear to do anything useful before).","mergeCommitSha":"f456cb4b41acf2c89c7c2f551fcaac321a522604","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4917","title":"Prevent VSCode sidebar from wedging on startup","createdAt":"2023-02-21T23:44:11Z"}
{"state":"Merged","mergedAt":"2023-02-22T00:23:38Z","number":4918,"mergeCommitSha":"a9a4d892a59c341ffa629dc813abe3171d933d7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4918","title":"Add powerml with summaries","createdAt":"2023-02-21T23:45:10Z"}
{"state":"Closed","mergedAt":null,"number":4919,"body":"Setup generic auth classes which should allow us to trigger auth operations (refreshAuth) in other stores without passing around client specific abstractions (e.g. TokenProvider)\r\n\r\nThis was specifically done so that we can refactor RepoStore from VSCode into shared/ide. RepoStore in VSCode currently calls the `refreshAuth` implementation within VSCode which handles injecting the token provider.","mergeCommitSha":"40fe41a4f3984c00067ced1e76917664656b8dc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4919","title":"Setup Generic Auth Classes","createdAt":"2023-02-21T23:49:15Z"}
{"state":"Merged","mergedAt":"2022-03-08T20:09:18Z","number":492,"body":"Instead of an identity, include team memberships instead of identity for a `person`\r\n\r\nCan easily map from team memberships -> Identity if necessary\r\n\r\nOn the clients, we typically reference individuals based on their TeamMembership ID.\r\nFor example, if we wanted to associate a message with the current user, we would need a teamMemberID which a person does not contain.\r\n\r\nAlso added reference to the team a teamMember belongs to.","mergeCommitSha":"34c2a623c20bbc586912608c7d9459783db0832a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/492","title":"Update Person model to include memberships","createdAt":"2022-03-07T18:45:08Z"}
{"state":"Merged","mergedAt":"2023-02-22T00:26:17Z","number":4920,"mergeCommitSha":"0a7ccf163f67fab96b56478895402cdb876b988e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4920","title":"update powerml docker file","createdAt":"2023-02-22T00:25:44Z"}
{"state":"Merged","mergedAt":"2023-02-22T17:00:03Z","number":4921,"body":"Keep a reference of TokenProvider in AuthStore.\r\n\r\nThis enables the usage of RefreshAuth within shared code without handling TokenProviders.","mergeCommitSha":"fcd59119f1a21434053f8d880eafe95877f5a187","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4921","title":"Remove TokenProvider input from RefreshAuth","createdAt":"2023-02-22T00:30:20Z"}
{"state":"Merged","mergedAt":"2023-02-22T01:37:20Z","number":4922,"body":"This should do nothing except dump some stuff into the web console when there are CSP errors","mergeCommitSha":"4eee4f1f6fc5555e495ac4c0950a555ec3993d32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4922","title":"Add csp in report only mode to test dashboard","createdAt":"2023-02-22T00:46:17Z"}
{"state":"Merged","mergedAt":"2023-02-22T23:53:39Z","number":4923,"mergeCommitSha":"85221038f453269b3c2bca7e68485702f2d545b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4923","title":"AES crypto system","createdAt":"2023-02-22T01:13:29Z"}
{"state":"Merged","mergedAt":"2023-02-22T02:41:59Z","number":4924,"body":"Cleanup user token encryption\n\nFollow up from #4914.\n\nremove legacy accessToken","mergeCommitSha":"997ecb67243331a22b04aa26d12a4fcd5dcbd6e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4924","title":"Cleanup user token encryption","createdAt":"2023-02-22T01:44:05Z"}
{"state":"Merged","mergedAt":"2023-02-22T02:02:09Z","number":4925,"body":"Still in report only mode so this is just for documentation purposes while we iterate towards a tight policy","mergeCommitSha":"0e1ea3a8aba74e1374daa390fdcb721394872127","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4925","title":"Overshoot CSP tightening to get error reporting","createdAt":"2023-02-22T01:56:16Z"}
{"state":"Merged","mergedAt":"2023-02-22T02:26:21Z","number":4926,"mergeCommitSha":"013635f7bf29436dca77e0a10ed56322cadf2a96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4926","title":"Whack-a-mole time","createdAt":"2023-02-22T02:23:04Z"}
{"state":"Closed","mergedAt":null,"number":4927,"mergeCommitSha":"91d0aad52c404eb9318c6f0989a9a310dc2c9f6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4927","title":"Apply CSP for realz","createdAt":"2023-02-22T02:46:51Z"}
{"state":"Merged","mergedAt":"2023-02-22T03:04:29Z","number":4928,"mergeCommitSha":"40458105e93653d332b21bbef5fe8c6501d15454","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4928","title":"include subdomains in CSP","createdAt":"2023-02-22T03:01:27Z"}
{"state":"Merged","mergedAt":"2023-02-22T03:38:51Z","number":4929,"mergeCommitSha":"0d70b41154551910b921de68ab95f43f5aa99e72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4929","title":"Add img blob and wss connect directives to CSP","createdAt":"2023-02-22T03:36:08Z"}
{"state":"Merged","mergedAt":"2022-03-08T21:06:27Z","number":493,"body":"This change would dashboard to prod. We still need to figure out how to introduce testing but for now this should help with my work. \r\n\r\nBoth Dev and Prod Dashbaords are available at `/dashboard`. we currently limit access to them via WAF IP filters. ","mergeCommitSha":"cb6aaf575badb51196fac370c65c6a00b1b6d44a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/493","title":"Deploy web dashboard to prod","createdAt":"2022-03-07T19:50:49Z"}
{"state":"Merged","mergedAt":"2023-02-22T05:25:56Z","number":4930,"body":"This adds the CSP. Running in report only mode shows no errors for all dashboard pages using the following policy:\r\n\r\n```\r\ndefault-src 'self'; img-src blob: data: https:; style-src 'unsafe-inline' https:; script-src 'self' *.intercom.io *.intercomcdn.com; connect-src 'self' *.intercom.io *.sentry.io wss://*.intercom.io; font-src *\r\n```\r\n\r\nWill revert immediately if this breaks anything","mergeCommitSha":"ed92a3b6353e417d6d6d2afe003241242714e874","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4930","title":"Add CSP for realz","createdAt":"2023-02-22T04:26:41Z"}
{"state":"Merged","mergedAt":"2023-02-22T04:34:48Z","number":4931,"mergeCommitSha":"1f596ed30d94112fdd6de2a726a7c2f2b57b9526","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4931","title":"Remove restrictions for img and font srcs","createdAt":"2023-02-22T04:31:31Z"}
{"state":"Merged","mergedAt":"2023-02-22T17:43:16Z","number":4932,"mergeCommitSha":"f08c4b10d7dc81d26373c318e3d6879840180ebe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4932","title":"powerml wordnet","createdAt":"2023-02-22T04:39:27Z"}
{"state":"Merged","mergedAt":"2023-02-22T04:54:33Z","number":4933,"mergeCommitSha":"b1b8b31e34baff0bfbfe52550eb74122a3eddf8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4933","title":"img-src * apparently doesn't include blob: and data: schemes","createdAt":"2023-02-22T04:49:56Z"}
{"state":"Merged","mergedAt":"2023-02-22T04:52:33Z","number":4934,"mergeCommitSha":"f83e7174d60da6f9eff547aac6b5b6a9b2338c2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4934","title":"Reduce topic summaries","createdAt":"2023-02-22T04:52:22Z"}
{"state":"Merged","mergedAt":"2023-02-22T05:25:25Z","number":4935,"body":"The problem is that `createValueStream` makes an assumption that `undefined` items should never be sent by default.\r\n\r\nThis needs to be cleaned up -- I think the behaviour should be more explicit -- but changing `createValueStream`'s default behaviour is risky as it's used everywhere and I don't think the contract in this area is super obvious to all code using it.  So this is a bit of a hacky way to fix this -- use `null` to imply \"unset\" and then map immediately.","mergeCommitSha":"608d0060177e86cb1f249ed9f9a4d22ec123fba2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4935","title":"Fix VSCode sidebar rendering","createdAt":"2023-02-22T05:10:34Z"}
{"state":"Closed","mergedAt":null,"number":4936,"body":"We have the backend changes necessary for adding topic descriptions.\r\nWe now want to display it on the frontend.\r\nThis one addresses the topic description on the topic page.\r\nThe remaining work is to add a hover description in the topics list.\r\n\r\n<img width=\"1337\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/220528621-fce4c80b-fbec-4fee-912e-f63641c2dcc7.png\">\r\n","mergeCommitSha":"3194312af8d4cd45b46d861309ef65bfc658ad16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4936","title":"Include topic summary on topics page","createdAt":"2023-02-22T05:12:29Z"}