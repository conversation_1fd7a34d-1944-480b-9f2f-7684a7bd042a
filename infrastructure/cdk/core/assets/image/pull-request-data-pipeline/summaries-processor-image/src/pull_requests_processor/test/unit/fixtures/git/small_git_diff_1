commit 916b3df89f68e1c0efe5b36fcea3ab5c426df110
Author: <PERSON> <<EMAIL>>
Date:   Thu Apr 28 10:17:45 2022 -0700

    Legacy proto generation cleanup (#1050)

diff --git a/common/Makefile b/common/Makefile
index 8bc1a98ce..e3ae8b11c 100755
--- a/common/Makefile
+++ b/common/Makefile
@@ -1,9 +1,5 @@
 MAKEFILE_DIR := $(dir $(abspath $(firstword $(MAKEFILE_LIST))))

-generate-message-api-legacy:
-	./proto_message.sh; ./proto_sourcemark.sh
-.PHONY: generate-message-api-legacy
-
 generate-common-protos:
 	$(MAKEFILE_DIR)/../gradlew generateProto
 .PHONY: generate-common-protos
diff --git a/common/proto_message.sh b/common/proto_message.sh
deleted file mode 100755
index c684db10c..000000000
--- a/common/proto_message.sh
+++ /dev/null
@@ -1,15 +0,0 @@
-#!/usr/bin/env bash
-
-OUT_DIR="../shared/api/generatedProtobuf"
-IN_DIR="./src/main/resources/protos"
-PROTOC_GEN_TS_PATH="./node_modules/ts-proto/protoc-gen-ts_proto"
-
-mkdir -p -- "$OUT_DIR"
-
-protoc \
-    --plugin=${PROTOC_GEN_TS_PATH} \
-    --ts_proto_out="${OUT_DIR}" \
-    --ts_proto_opt=esModuleInterop=true \
-    --ts_proto_opt=useOptionals=messages \
-    --ts_proto_opt=oneof=unions \
-    "$IN_DIR"/Message.proto
diff --git a/common/proto_sourcemark.sh b/common/proto_sourcemark.sh
deleted file mode 100755
index bb86ddf6e..000000000
--- a/common/proto_sourcemark.sh
+++ /dev/null
@@ -1,16 +0,0 @@
-#!/usr/bin/env bash
-
-OUT_DIR="../shared/sourcemark/generated"
-IN_DIR="./src/main/resources/protos"
-PROTOC_GEN_TS_PATH="./node_modules/ts-proto/protoc-gen-ts_proto"
-
-mkdir -p -- "$OUT_DIR"
-
-protoc \
-    --plugin=${PROTOC_GEN_TS_PATH} \
-    --ts_proto_out="${OUT_DIR}" \
-    --ts_proto_opt=esModuleInterop=true \
-    --ts_proto_opt=useOptionals=messages \
-    --ts_proto_opt=oneof=unions \
-    --ts_proto_opt=outputServices=grpc-js \
-     "$IN_DIR"/SourceMark.proto
