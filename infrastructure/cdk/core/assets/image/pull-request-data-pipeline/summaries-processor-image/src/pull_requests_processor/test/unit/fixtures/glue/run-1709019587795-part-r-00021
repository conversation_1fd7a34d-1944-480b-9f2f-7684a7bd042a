{"state":"Merged","mergedAt":"2022-12-19T21:06:27Z","number":4142,"body":"We were sending 2GB of logs from Kube to CloudWatch because of audit logs. This dripped it back to 100MB/day ","mergeCommitSha":"ab3d6020cfb067ea8c171f05622784a9846f2973","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4142","title":"reduce kube logging","createdAt":"2022-12-19T19:29:00Z"}
{"state":"Merged","mergedAt":"2022-12-20T19:33:22Z","number":4143,"body":"Utilize Video Preview Image whenever possible in thread listing.\r\n\r\n<img width=\"1212\" alt=\"CleanShot 2022-12-19 at 12 16 59@2x\" src=\"https://user-images.githubusercontent.com/1553313/208513426-e6fdd238-61ed-497e-a4c6-e23ab210ee38.png\">\r\n","mergeCommitSha":"66cbb71ffa14edbaa5d98f9c1db1bf5e1dc23c24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4143","title":"Thread video preview container","createdAt":"2022-12-19T20:17:37Z"}
{"state":"Merged","mergedAt":"2022-12-19T21:47:13Z","number":4144,"mergeCommitSha":"92fae745add51711aff816e849539f0d593dfab9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4144","title":"Disable topic mapping job","createdAt":"2022-12-19T21:39:59Z"}
{"state":"Merged","mergedAt":"2022-12-19T22:43:19Z","number":4145,"mergeCommitSha":"57aede3a1f9560ea2e50c024bb906ea41900f40e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4145","title":"Do topic mappings during topic creation/ingestion","createdAt":"2022-12-19T22:10:05Z"}
{"state":"Merged","mergedAt":"2022-12-20T17:18:34Z","number":4146,"body":"1. Add query property to `getTopics` API that allows fetching only \"recommended\" and \"relevant\" topics.  \"relevant\" topics are ones that are user-approved, and are the topics we generally show throughout the UI.  \"recommended\" topics are ones that are not marked as relevant, and are only used as recommendations in this UI:\r\n\r\n<img width=\"847\" alt=\"Screen Shot 2022-12-19 at 2 10 09 PM\" src=\"https://user-images.githubusercontent.com/2133518/208536253-90d86c7a-d501-4824-9298-18e0f098d6d4.png\">\r\n\r\n2. Add new `updateTopic` API method, used to add a new topic or update an existing topic.  This will be used to add new topics (whether those topics are \"recommended\" topics generated by the system, or whether they are brand new topics):\r\n<img width=\"846\" alt=\"Screen Shot 2022-12-19 at 2 12 26 PM\" src=\"https://user-images.githubusercontent.com/2133518/208536869-18e3dae9-ee84-4f1e-bd0e-fa51d059f44f.png\">\r\n<img width=\"844\" alt=\"Screen Shot 2022-12-19 at 2 12 36 PM\" src=\"https://user-images.githubusercontent.com/2133518/208536875-323b24da-1052-4b4f-ba3f-51999f3ba3aa.png\">\r\n\r\nThis doesn't handle the training UI.  I'm leaving that for now.","mergeCommitSha":"3da41094d71a55d0e11cde0df921662b7914aaaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4146","title":"Add API methods for topic maintenance","createdAt":"2022-12-19T22:13:57Z"}
{"state":"Merged","mergedAt":"2022-12-19T23:38:03Z","number":4147,"mergeCommitSha":"f97ab6dfa6085bb4d1ac2efc1b58bb445602ea3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4147","title":"Video drafts are available in read-only mode","createdAt":"2022-12-19T23:12:12Z"}
{"state":"Merged","mergedAt":"2022-12-20T23:55:18Z","number":4148,"mergeCommitSha":"543e6df1beabf7dff8af99ac8601a105393e2e2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4148","title":"Replace ThreadTopicModel and PullRequestTopicModel with TopicInsightModel","createdAt":"2022-12-20T00:46:42Z"}
{"state":"Merged","mergedAt":"2022-12-20T21:41:56Z","number":4149,"mergeCommitSha":"d22d80235603cdf01830cc4ca4c47b7b3037bb47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4149","title":"[BREAKS API ON MAIN] updateTopic Request body should just be a single object","createdAt":"2022-12-20T18:03:06Z"}
{"state":"Merged","mergedAt":"2022-02-25T20:21:59Z","number":415,"body":"Ran the `npm run fix-pretty` and it made the file messy. Here are the changes I have made:\r\n- Added multi-part upload perms to IAM user \r\n- Added a couple of flags to completely disable public hosting and public objects in the bucket\r\nThis is already deployed and to Dev to help with Demo stuff","mergeCommitSha":"1c8093c82d9c1a2850e6d3c50d4aadd8a52f97b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/415","title":"add more perms to iam user for agora","createdAt":"2022-02-25T19:07:49Z"}
{"state":"Merged","mergedAt":"2022-12-20T21:17:17Z","number":4150,"mergeCommitSha":"1e2c14e968f109ed5e87be566457eecc4100fdb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4150","title":"Add API for delete all Video Drafts","createdAt":"2022-12-20T18:54:54Z"}
{"state":"Merged","mergedAt":"2022-12-21T19:10:33Z","number":4151,"mergeCommitSha":"64408c66a7cfd2be7dd3a9d9b9f6e270e7597f8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4151","title":"Video reliability improvements","createdAt":"2022-12-20T19:34:23Z"}
{"state":"Merged","mergedAt":"2022-12-20T23:57:36Z","number":4152,"body":"This is just a pure refactor:\r\n\r\n* Change the key for TopicStore to allow querying for relevant or recommended topics\r\n* Change all existing uses of TopicStore to get the stream of relevant topics (the \"current user approved\" topics)\r\n\r\nAlso, fixed the dashboard storybook","mergeCommitSha":"fee3aebbcb7d276bfeaaaee6a66456cddfc4fbc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4152","title":"Refactor TopicStore to allow querying recommended topics","createdAt":"2022-12-20T19:38:38Z"}
{"state":"Merged","mergedAt":"2022-12-21T07:52:29Z","number":4153,"mergeCommitSha":"0f927254001d16eee18cf58e586a468892a55d01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4153","title":"Persist GitHub Enterprise apps","createdAt":"2022-12-20T21:06:13Z"}
{"state":"Merged","mergedAt":"2022-12-20T21:34:23Z","number":4154,"mergeCommitSha":"1d47e231fc5d7db1f400407de919770a55b01429","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4154","title":"Add pull request count to repo page","createdAt":"2022-12-20T21:18:17Z"}
{"state":"Merged","mergedAt":"2022-12-20T21:45:20Z","number":4155,"mergeCommitSha":"be626114ffe387915469a707281da9523876249c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4155","title":"Sort topics descending by score","createdAt":"2022-12-20T21:25:18Z"}
{"state":"Merged","mergedAt":"2022-12-20T22:12:27Z","number":4156,"body":"Motivation\n- https://chapter2global.slack.com/archives/C02HEVCCJA3/p1671573792238419?thread_ts=1671485514.066699&cid=C02HEVCCJA3\n\nFeel free to revert this change.","mergeCommitSha":"10058fd3995c644c47982e06c25f9517088f50a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4156","title":"Reduce Slack polling by 60X","createdAt":"2022-12-20T22:07:12Z"}
{"state":"Merged","mergedAt":"2022-12-21T01:12:54Z","number":4157,"body":"<img width=\"1306\" alt=\"CleanShot 2022-12-20 at 14 19 14@2x\" src=\"https://user-images.githubusercontent.com/1553313/208777876-db4827f8-197d-47f2-be1e-5d3565c6696c.png\">\r\n<img width=\"519\" alt=\"CleanShot 2022-12-20 at 14 19 17@2x\" src=\"https://user-images.githubusercontent.com/1553313/208777881-fc06eb51-7f54-4196-ab24-c7d34ba0810f.png\">\r\n","mergeCommitSha":"ce724a45c4f3fd74b50d77797df1b968a420d3cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4157","title":"Delete all video drafts","createdAt":"2022-12-20T22:20:09Z"}
{"state":"Closed","mergedAt":null,"number":4158,"mergeCommitSha":"ea416380c92e911ea7ff6605e479078a221ba1f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4158","title":"[DO NOT MERGE] Add message body to SearchInsightsResponse","createdAt":"2022-12-20T22:31:33Z"}
{"state":"Merged","mergedAt":"2022-12-21T01:12:44Z","number":4159,"body":"Test flow to make post request to GHE instance.\r\n\r\nSends a post with form as tested here: https://github.com/NextChapterSoftware/unblocked/blob/b951767435d6d5638cebf8ccc1d93c09928a11e7/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/OperationsPage.kt#L111-L145","mergeCommitSha":"49b79c29e13c49427e5b6ad3de12a806b9e239b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4159","title":"Test Entry point for manifest","createdAt":"2022-12-20T22:37:12Z"}
{"state":"Merged","mergedAt":"2022-02-25T22:40:28Z","number":416,"mergeCommitSha":"c2de158c26ec2c09e71503e7ce404b49fc3e570b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/416","title":"Enable loading runtime fixtures in dev","createdAt":"2022-02-25T19:28:13Z"}
{"state":"Merged","mergedAt":"2023-01-10T00:41:51Z","number":4160,"body":"Add generic mobile menu component (has a side animation for now but we can customize when more is necessary)\r\n\r\nhttps://user-images.githubusercontent.com/13431372/208785262-34e10f64-0120-4374-9dc9-f58dde5360af.mp4\r\n\r\nAdd filter menu for the mobile viewport that replaces the filter dropdowns in the larger viewports\r\nAdd a couple of generic header and row components for reuse","mergeCommitSha":"698bd477614552a36cf323eef2b6910d8a5b9b48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4160","title":"Add MobileMenu component for dashboard","createdAt":"2022-12-20T23:25:39Z"}
{"state":"Closed","mergedAt":null,"number":4161,"body":"…InsightModel (#4148)\"\r\n\r\nThis reverts commit 543e6df1beabf7dff8af99ac8601a105393e2e2c.","mergeCommitSha":"a5fea5a1b49803dec55775dd74296c2be704fba4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4161","title":"[DO NOT MERGE] Revert \"Replace ThreadTopicModel and PullRequestTopicModel with Topic…","createdAt":"2022-12-20T23:56:00Z"}
{"state":"Merged","mergedAt":"2022-12-21T19:27:03Z","number":4162,"body":"\r\n<img width=\"305\" alt=\"CleanShot 2022-12-20 at 20 28 03@2x\" src=\"https://user-images.githubusercontent.com/858772/208821449-a4dbf554-6e52-433f-b57f-476805a6b1a6.png\">\r\n<img width=\"289\" alt=\"CleanShot 2022-12-20 at 20 28 57@2x\" src=\"https://user-images.githubusercontent.com/858772/208821455-62566208-dd14-4897-b1c0-1e3035a31c1e.png\">\r\n\r\n","mergeCommitSha":"24e88bd97647b90d0c87659c237e570fb64c64c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4162","title":"Add retry dialog for video upload","createdAt":"2022-12-21T00:31:21Z"}
{"state":"Merged","mergedAt":"2023-01-13T00:48:07Z","number":4163,"mergeCommitSha":"2a589dddc4d39c8a7eb7a4c58b6a1dc8e5a4888a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4163","title":"Drop ThreadTopicModel and PullRequestTopicModel tables","createdAt":"2022-12-21T00:32:15Z"}
{"state":"Closed","mergedAt":null,"number":4164,"body":"Merge references list and move contributors list to the side.\r\n\r\n<img width=\"1340\" alt=\"CleanShot 2022-12-20 at 16 55 22@2x\" src=\"https://user-images.githubusercontent.com/1553313/208795875-4a4f64ed-9053-4280-949b-65650add4884.png\">\r\n\r\n","mergeCommitSha":"52fa95881233d9554c95be7397f1dcceda5f2c57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4164","title":"Unify references list","createdAt":"2022-12-21T01:00:51Z"}
{"state":"Merged","mergedAt":"2023-01-10T00:04:52Z","number":4165,"body":"Merge references list and move contributors list to the side.\r\n\r\n<img width=\"1340\" alt=\"CleanShot 2022-12-20 at 16 55 22@2x\" src=\"https://user-images.githubusercontent.com/1553313/208795875-4a4f64ed-9053-4280-949b-65650add4884.png\">\r\n","mergeCommitSha":"08dd1e27ccba031774ae70a82861481cae2ca268","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4165","title":"Unify References List","createdAt":"2022-12-21T01:02:31Z"}
{"state":"Merged","mergedAt":"2022-12-21T18:27:53Z","number":4166,"body":"Ben calls this a ContextRow.  It's basically a large block-display button with styling half way between a button and a link.  It's primarily used in the right-hand sidebar in the dashboard.\r\n\r\n<img width=\"1165\" alt=\"Screen Shot 2022-12-21 at 9 41 02 AM\" src=\"https://user-images.githubusercontent.com/2133518/208969630-94d45f26-2502-4a76-b538-acf429c95fdf.png\">\r\n\r\n<img width=\"1165\" alt=\"Screen Shot 2022-12-21 at 9 40 44 AM\" src=\"https://user-images.githubusercontent.com/2133518/208969621-95357151-33a1-4a91-a380-a6d21e6241b3.png\">\r\n\r\nContextRow can have an action (`onClick`) or not.  It has two variants, regular and deemphasized (looks somewhat faded out, until hovered).\r\n\r\nWe're currently using this in one place (the dashboard topic view, on the right side).\r\n","mergeCommitSha":"d94181be41871759a0238e79111bddbad9194a62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4166","title":"Add ContextRow component","createdAt":"2022-12-21T17:42:35Z"}
{"state":"Closed","mergedAt":null,"number":4167,"mergeCommitSha":"eb52032dde832530215d061da1079afd1c2dfe82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4167","title":"Enable GH Enterprise in dev","createdAt":"2022-12-21T18:08:18Z"}
{"state":"Merged","mergedAt":"2022-12-22T22:49:18Z","number":4168,"body":"Parsed through https://fontawesome.com/search?f=brands&o=r for relevant brands.\r\n\r\n<img width=\"1617\" alt=\"CleanShot 2022-12-21 at 10 57 35@2x\" src=\"https://user-images.githubusercontent.com/1553313/208983962-08414149-ec19-40b7-9703-7eec458475c0.png\">\r\n\r\nTODO: FileIcons next.","mergeCommitSha":"a3729a42fbc7ab6cd974f5d8eea3dd21190c155e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4168","title":"Add brand icons to references","createdAt":"2022-12-21T19:07:42Z"}
{"state":"Closed","mergedAt":null,"number":4169,"body":"## Changes\r\n1. client adds a new `enterpriseHostAndPort` query param\r\n2. server adds enterprise provider \"OAuth\" Url to response\r\n    - url is /login/{provider}/manifest, when enterprise provider app does not exist\r\n    - url is /login/{provider}, when enterprise provider app does exist\r\n\r\n\r\n## Error Handing\r\nIf enterprise provider app does not exist, and the enterpriseHostAndPort is not network accessible then we need to encode that information in the response. There are two approaches: (i) use the default ApiError response, or (ii) add some custom errors to this API.\r\n\r\n#### Using defaultApiError response\r\nThe downside is that these responses cannot be versioned; for example, the `type` field below cannot be an enum.\r\n\r\nFirewall\r\n```\r\n{\r\n  \"type\": \"firewall\",\r\n  \"title\": \"Connection to your enterprise server timed out\",\r\n  \"status\": 400,\r\n  \"detail\": \"Unblocked cannot connect to your enterprise server. You may need to allow inbound https access.\",\r\n  \"instance\": \"https://git.nike.com\"\r\n}\r\n```\r\n\r\nDNS\r\n```\r\n{\r\n  \"type\": \"dns\",\r\n  \"title\": \"Your enterprise server was not found\",\r\n  \"status\": 400,\r\n  \"detail\": \"Unblocked cannot find your enterpise server. You may need to make your internal enterprise server DNS public.\",\r\n  \"instance\": \"https://git.nike.com\"\r\n}\r\n```","mergeCommitSha":"cdfae954b5eeeded4d592070dfe1176a1b1512d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4169","title":"[WIP] Adds enterpise providers to login options","createdAt":"2022-12-21T20:40:36Z"}
{"state":"Merged","mergedAt":"2022-02-25T19:42:01Z","number":417,"mergeCommitSha":"85ec669d121746e52b5f3f7bc42781f7384f9fa6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/417","title":"Having problems with prettier","createdAt":"2022-02-25T19:36:01Z"}
{"state":"Merged","mergedAt":"2022-12-22T22:23:26Z","number":4170,"body":"There's no reliability here, just fire and forget. If this doesn't serve our needs then I will implement a reliable queue.","mergeCommitSha":"77d8ba142dd230ede33088b6e6eca9cdd19a45ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4170","title":"Send error level logs to the logging API","createdAt":"2022-12-21T20:50:03Z"}
{"state":"Merged","mergedAt":"2022-12-23T01:40:02Z","number":4171,"body":"Starting a walkthrough should not be possible if already running.\r\n\r\n<img width=\"401\" alt=\"CleanShot 2022-12-21 at 15 41 45@2x\" src=\"https://user-images.githubusercontent.com/1553313/209024596-581595e0-0641-48f7-bb8c-aaaae06e5b91.png\">\r\n","mergeCommitSha":"7f7d3f768f9587ec93d38918bea221bef0278230","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4171","title":"Disable start walkthrough if running in VSCode","createdAt":"2022-12-21T23:49:12Z"}
{"state":"Merged","mergedAt":"2022-12-23T17:27:24Z","number":4172,"body":"Used for topic management. ","mergeCommitSha":"5d0a6e70a577157348bc3ceaee134db73c8d9f5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4172","title":"Update getTopics operation and implement updateTopic operation","createdAt":"2022-12-21T23:58:22Z"}
{"state":"Merged","mergedAt":"2022-12-23T00:59:05Z","number":4173,"body":"while moving the scoring into something that we can share with powerml too. no changes to the data pipeline or the app, just the science-like notebooks.","mergeCommitSha":"dd278f7b1b2896dfd54a9cb24fe3ae0ec2771de5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4173","title":"Move pipeline from dev (unblocked only) to prod (all partners)","createdAt":"2022-12-22T00:48:01Z"}
{"state":"Merged","mergedAt":"2022-12-23T19:33:47Z","number":4174,"body":"Refactored preview duration to left.\r\n\r\nAdded to thread listing as well.\r\n\r\n<img width=\"657\" alt=\"CleanShot 2022-12-22 at 09 06 54@2x\" src=\"https://user-images.githubusercontent.com/1553313/209188637-6078f1a1-54af-4ae7-a224-65f1cf6289f4.png\">\r\n<img width=\"1130\" alt=\"CleanShot 2022-12-22 at 09 15 02@2x\" src=\"https://user-images.githubusercontent.com/1553313/209189971-e6b24458-03f5-4b67-9acc-c9872850e269.png\">\r\n\r\n","mergeCommitSha":"b7cba235ab195effa0306253ff7c7d7c13672d7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4174","title":"Updated preview duration","createdAt":"2022-12-22T17:07:32Z"}
{"state":"Merged","mergedAt":"2022-12-23T00:58:15Z","number":4175,"body":"1: improve the url removal.\r\n2: general emoji removal function.\r\n3: general username removal.\r\n4: remove tokenization. seems like Bert/PowerML can do casing, which we are going to test.","mergeCommitSha":"8cf1b2c5ba1ff29324f875b9dea87659a3f5d145","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4175","title":"Bring over some text processing functions from the data science pipeline","createdAt":"2022-12-22T20:34:09Z"}
{"state":"Merged","mergedAt":"2022-12-22T22:44:00Z","number":4176,"mergeCommitSha":"17275ded0813e59397db8b81ffe12379a2500790","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4176","title":"Fixes slurp bugs","createdAt":"2022-12-22T22:00:05Z"}
{"state":"Merged","mergedAt":"2022-12-23T00:01:47Z","number":4177,"body":"Fixing a bug where Steve was missing video content.\r\n\r\nWas able to reproduce by editing the video's title. Editing the title === updating the message content. MessageEditor currently does *not* handle videos and drops them... (TO FIX)\r\n\r\nThis hacky PR will try inserting a video that exists within the video metadata *back* into the message content if missing.\r\nThis is technically okay right now since we don't support deleting videos without deleting entire threads...\r\n\r\n","mergeCommitSha":"d5f1ee5c5d6ab83b5e2f1340f307dae33f68d3c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4177","title":"Fix deleted videos","createdAt":"2022-12-22T23:51:03Z"}
{"state":"Merged","mergedAt":"2023-01-20T23:15:07Z","number":4178,"body":"We're replacing SearchService with SearchInsightService for the search V2 experience.","mergeCommitSha":"65caeca5e6e4a48e6d1ffdc9803b68ad94c29c0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4178","title":"Replace SearchService with SearchInsightService","createdAt":"2022-12-23T00:06:10Z"}
{"state":"Closed","mergedAt":null,"number":4179,"mergeCommitSha":"d119d4e6c88046557cc559bb6829170aae961a0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4179","title":"Try to fix installer build","createdAt":"2022-12-23T06:09:27Z"}
{"state":"Merged","mergedAt":"2022-02-25T20:22:03Z","number":418,"mergeCommitSha":"9fc28d0d40d6ae4be77dec7b8288569df74f39b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/418","title":"Add readme for databse","createdAt":"2022-02-25T19:56:20Z"}
{"state":"Merged","mergedAt":"2022-12-23T18:37:27Z","number":4180,"body":"Two changes I'm committing separately from the topic maintenance UI:\r\n\r\n1) Add a footer to the detail layout view, this will be used for the previous/next buttons in this design:\r\n\r\n<img width=\"700\" alt=\"Screen Shot 2022-12-23 at 9 44 47 AM\" src=\"https://user-images.githubusercontent.com/2133518/209380780-9aba3b17-b4b2-4699-a7b3-779f6479036c.png\">\r\n\r\n2) Add a CardCheckbox component.  This is a stylized checkbox with a large panel area:\r\n\r\n<img width=\"1109\" alt=\"Screen Shot 2022-12-23 at 9 41 18 AM\" src=\"https://user-images.githubusercontent.com/2133518/209381012-a87b1fea-259d-43ba-a296-5c158dd453c0.png\">\r\n","mergeCommitSha":"f7575d1398333d888e27b89e7a93cc3d133dfeec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4180","title":"Add helper components","createdAt":"2022-12-23T17:45:44Z"}
{"state":"Merged","mergedAt":"2023-01-11T22:35:58Z","number":4181,"body":"Update MessageEditor to properly handle Video Elements\r\nEditing a message with a video should no longer be completely destructive... \r\n\r\nUnable to show preview image at this time... If we wanted to do this, would require injecting video metadata into the MessageEditor which I'd like to avoid unless necessary.\r\n\r\n<img width=\"1024\" alt=\"CleanShot 2022-12-23 at 11 12 42@2x\" src=\"https://user-images.githubusercontent.com/1553313/209396060-5af69242-4619-4250-b978-b3a93d9cc6aa.png\">\r\n","mergeCommitSha":"84e11d39e4c79bc57f167e1de0cc12d7e6e3416b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4181","title":"Translate video element in message editor","createdAt":"2022-12-23T19:13:35Z"}
{"state":"Merged","mergedAt":"2022-12-23T19:36:46Z","number":4182,"mergeCommitSha":"372b6f26b81002d475c3af8c160a393e2bf66e09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4182","title":"Set correct topic source when created","createdAt":"2022-12-23T19:15:55Z"}
{"state":"Merged","mergedAt":"2022-12-23T20:30:39Z","number":4183,"body":"Built the maven module from https://github.com/NextChapterSoftware/emoji-data-java\r\n\r\nFork was required because it hasn't been touched since 2020","mergeCommitSha":"a25e27bc5b77739459c89106ef6cd71ecea101a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4183","title":"NO","createdAt":"2022-12-23T19:52:02Z"}
{"state":"Merged","mergedAt":"2022-12-23T22:00:50Z","number":4184,"body":"Looks the changes that made things better in dev, made things worst for the usercloud team. This change removes the two new text processing rules we introduced yesterday and the start of a notebook to debug prod.","mergeCommitSha":"278ac6d7483490730eff728d6b7ec9bdce074815","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4184","title":"Prod text sanitization changes","createdAt":"2022-12-23T20:18:03Z"}
{"state":"Merged","mergedAt":"2023-01-09T23:53:03Z","number":4185,"body":"## API Design\n\nhttps://www.notion.so/nextchaptersoftware/Enterprise-login-flow-87508b383a05429d8a2c4b8f388b7aeb\n\n## Example errors\n\n- `endpointNotResolved`\n  - DNS issue.\n  - \"Your enterprise server was not found\"\n  - \"Unblocked cannot find your enterpise server. You may need to make your internal enterprise server DNS public.\"\n\n- `endpointNotReachable`\n  - Firewall issue.\n  - \"Connection to your enterprise server timed out\"\n  - \"Unblocked cannot connect to your enterprise server. You may need to allow inbound https access.\"","mergeCommitSha":"b87a892acc0f4bd9e8f3049cb5032fe1742eee0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4185","title":"Introduce Enterpise Provider flow to drive enterprise login","createdAt":"2022-12-23T22:45:06Z"}
{"state":"Merged","mergedAt":"2022-12-27T18:58:00Z","number":4186,"body":"The intent of this was to roll back a change, but decided to just go all the way. Gets usercloud back to the previous state (which doesn't appear to be related to folders as this investigation demostrated) and improves everyone else.","mergeCommitSha":"5f59e94245bffad0c5c32d629b06b4c1dd79c1d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4186","title":"Improve text sanitizer and model config","createdAt":"2022-12-25T05:40:32Z"}
{"state":"Closed","mergedAt":null,"number":4187,"body":"1. Need to remove the key before checking in, adopting whatever approach @pwerry is using for things like this.\r\n2. Need to include the previous messages in the current prompt.\r\n3. Need to refactor the OpenAI code into a client.swift file.\r\n4. Fix whatever config option that is doubling the size of the input area.￼\r\n5. Adding our embeddings (the thing that is really interesting here?)\r\n\r\n![63A5A968-BA02-4A0D-A028-CFEE72B9C686_1_102_o](https://user-images.githubusercontent.com/332509/210105775-fdcd7761-bc88-4f15-923f-0b987cc6f7a9.jpeg)\r\n","mergeCommitSha":"216fc0280633f141826f62ac2c4595914beaef66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4187","title":"Initial demo: Still refactoring this, but it works.","createdAt":"2022-12-30T19:28:51Z"}
{"state":"Merged","mergedAt":"2023-01-01T03:42:58Z","number":4188,"body":"Finally got a chance to run the bert data science notebook over the prod usercloud data. Below is what we get with nearly ~1k messages. Right now the prod data pipeline only generates the default topic cluster, which is 3 terms. It is possible that all the messages aren't making into the prod bert job. will look at that next.\r\n\r\nTopic\tCount\tName\r\n0\t-1\t268\t-1_authz_test_state\r\n1\t0\t85\t0_pagination_server_storage\r\n2\t1\t84\t1_policy_tokenizer_idp\r\n3\t2\t80\t2_invite_profile_public\r\n4\t3\t47\t3_debug_devbox_log\r\n5\t4\t47\t4_events_log_audit\r\n6\t5\t47\t5_tenants_status_auth\r\n7\t6\t33\t6_config_userstore_paths\r\n8\t7\t29\t7_reducers_return_users\r\n9\t8\t29\t8_jsonclient_jsonapi_underlying\r\n10\t9\t28\t9_staging_prod_provisioning\r\n11\t10\t27\t10_golang_samples_sdk\r\n12\t11\t25\t11_aws_region_userclouds\r\n13\t12\t25\t12_git_dev_code\r\n14\t13\t22\t13_azcli_namespace_storage\r\n15\t14\t21\t14_component_ui_lib\r\n16\t15\t19\t15_test__\r\n17\t16\t18\t16_tests_test_cache\r\n18\t17\t18\t17_policy_user_code\r\n19\t18\t17\t18___\r\n20\t19\t16\t19_table_tenantdb_logdb","mergeCommitSha":"761a6972dd742b57e23d4686416cc8d5e6e02c2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4188","title":"usercloud testing","createdAt":"2022-12-31T21:14:30Z"}
{"state":"Merged","mergedAt":"2023-01-09T19:50:50Z","number":4189,"body":"Used a lot of network traffic to drag in daos for updates.\r\nWe should be using models...","mergeCommitSha":"c422b4155e0e6a5d4ede67f659e915587809d85f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4189","title":"Daos should not be used for status updates","createdAt":"2023-01-09T19:34:50Z"}
{"state":"Merged","mergedAt":"2022-02-25T21:14:43Z","number":419,"body":"- Scaling up dev cluster\r\n- Removing old unused node group \r\nDev cluster was hitting number of pod limits. T3 instances have a low pod count so I added a second node. \r\nWe used a lot of our pod count for Controllers and Monitoring pods.\r\n\r\nNote: Cluster auto-scaler seems to work on CPU and Memory only and not pod limits. Pod limits are due to Network drivers which Kube doesn't know how to handle when it comes to scaling\r\n\r\nI intentionally haven't configured CI/CD for Kubernetes because of `eksctl` limitations. It's not smart enough to diff a config file and only deploy changes. Most changes require explicit CLI invocation to update resources. \r\n\r\nCommand used to update cluster scale:\r\n`eksctl scale nodegroup --cluster=dev --name=eks-managed-t3-medium --nodes-min=2 --nodes-max=3 --nodes=2`","mergeCommitSha":"a7db6756ac6588fe655f3e3ddfbd71f943721d84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/419","title":"Scaling up dev cluster","createdAt":"2022-02-25T20:24:55Z"}
{"state":"Merged","mergedAt":"2023-01-10T01:09:57Z","number":4190,"body":"Per https://github.com/NextChapterSoftware/unblocked/pull/4029, atttach pr participant team members to the client model and display if they exist\r\n\r\nAlso added a generic component to display a string list of items with a helper title attribute ","mergeCommitSha":"50bbf590c882c036e45a9a04ff2e5025ba995e6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4190","title":"Get and display pull request team members","createdAt":"2023-01-09T19:46:52Z"}
{"state":"Closed","mergedAt":null,"number":4191,"mergeCommitSha":"ffd4ab852e7bff4f7294f50a68c82b012d52d3c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4191","title":"Enterprise options UI","createdAt":"2023-01-09T20:59:55Z"}
{"state":"Merged","mergedAt":"2023-01-10T01:06:27Z","number":4192,"body":"Added functional support for Enterprise login / registration.\r\n\r\nNon web clients should not be affected and only display existing public providers (aka GH).","mergeCommitSha":"7fa4d1087a9c54841b98e5b9dffd6329510d9681","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4192","title":"Base enterprise options UI","createdAt":"2023-01-09T21:02:45Z"}
{"state":"Merged","mergedAt":"2023-01-09T23:53:56Z","number":4193,"body":"## Steps\r\n1. Ask the AssetService to initiate a multipart upload with S3, and generate the pre-signed urls for each part\r\n2. Client uploads the parts, and receives an `eTag` in each response\r\n3. Client sends completion request to AssetService with a map of `{ partNumber : eTag }`","mergeCommitSha":"f445c58b23eff7ba69d1ea5822cd2422ba9bb407","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4193","title":"Multipart Asset Upload Api","createdAt":"2023-01-09T21:12:44Z"}
{"state":"Merged","mergedAt":"2023-01-10T18:24:48Z","number":4194,"mergeCommitSha":"11c1c9e49a8da14c83a1a8e7764bb8785b0eb2e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4194","title":"Multipart upload service implementation","createdAt":"2023-01-09T21:18:45Z"}
{"state":"Merged","mergedAt":"2023-01-10T01:03:10Z","number":4195,"mergeCommitSha":"b8808c98ddeddaa704757c530fadb13a0baa92b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4195","title":"Include pull request participants","createdAt":"2023-01-09T22:12:18Z"}
{"state":"Merged","mergedAt":"2023-01-10T00:09:03Z","number":4196,"body":"Moving away from outdated library and updated to the latest emoji+unicode mappings as found here.\r\nhttps://github.com/iamcal/emoji-data/blob/master/emoji_pretty.json\r\n\r\nCustom implementation for emoji parsing which should take skin tones into account.","mergeCommitSha":"2b99e21576b0fe16f229dbe9af9ad197d9a396f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4196","title":"Use custom emoji library","createdAt":"2023-01-09T23:34:11Z"}
{"state":"Merged","mergedAt":"2023-01-10T18:17:13Z","number":4197,"body":"* Add sidebar to the topics page that shows recommended new topics, and allows creating a new topic\r\n* Add 'new topic' wizard -- this can be used to update an existing topic too\r\n\r\nStill needs a bit of polish (will be added in a followup PR):\r\n* We need to refresh the topic stores after creating the new topic (since we don't have a push channel).  Still figuring this out\r\n* A couple things don't work 100% right in mobile view\r\n* A few other tiny UI bits and pieces that probably aren't urgent\r\n \r\n<img width=\"1291\" alt=\"Screen Shot 2023-01-09 at 3 31 59 PM\" src=\"https://user-images.githubusercontent.com/2133518/*********-84b9a15f-df05-4f91-aa91-5236ef6e7fda.png\">\r\n<img width=\"1291\" alt=\"Screen Shot 2023-01-09 at 3 32 03 PM\" src=\"https://user-images.githubusercontent.com/2133518/211429133-ac8b4e79-5d98-46cf-9a12-2c3111f4e332.png\">\r\n<img width=\"1291\" alt=\"Screen Shot 2023-01-09 at 3 32 06 PM\" src=\"https://user-images.githubusercontent.com/2133518/211429139-0eef82e7-304e-43a2-bc49-24da4b8f8529.png\">\r\n<img width=\"1291\" alt=\"Screen Shot 2023-01-09 at 3 32 10 PM\" src=\"https://user-images.githubusercontent.com/2133518/211429141-cbeeabe6-5b57-4f39-8ca2-d3f5577d9e97.png\">\r\n","mergeCommitSha":"cab7490d1db58755d4d5f41d018b3d7344f35922","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4197","title":"'Add topic' dashboard UI","createdAt":"2023-01-09T23:34:59Z"}
{"state":"Merged","mergedAt":"2023-01-10T18:37:27Z","number":4198,"body":"1. Fixes icon styling to have proper background colours (background color overrides existed in VSCode but not web)\r\n2. GitContributors from FileReferences were not going through team member resolution. Updated.\r\n\r\nBefore:\r\n<img width=\"1471\" alt=\"CleanShot 2023-01-09 at 15 45 08@2x\" src=\"https://user-images.githubusercontent.com/1553313/211430497-fe15b7d7-49e1-4703-b2e4-1156b895f0a1.png\">\r\n\r\nAfter:\r\n<img width=\"1140\" alt=\"CleanShot 2023-01-09 at 15 45 28@2x\" src=\"https://user-images.githubusercontent.com/1553313/211430503-81dd2f53-6b24-47ce-85b6-b3717fc660ea.png\">\r\n","mergeCommitSha":"3e30bd645183b79cbbedebe5152eef0d50ff7574","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4198","title":"Updates to web contributor list","createdAt":"2023-01-09T23:47:02Z"}
{"state":"Merged","mergedAt":"2023-01-10T00:37:55Z","number":4199,"mergeCommitSha":"951e30439ddf584283ed39d1a6839e4a799b9a0a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4199","title":"Most to latest gradle build cache node","createdAt":"2023-01-10T00:35:11Z"}
{"state":"Merged","mergedAt":"2022-01-18T19:32:08Z","number":42,"body":"![image](https://user-images.githubusercontent.com/13431372/149825681-7fbbd9ee-c4fe-4151-99d9-2eab3bf5706b.png)\r\n![image](https://user-images.githubusercontent.com/13431372/149825695-ac3dc6bc-f515-4f03-907c-63ad2a11dbb3.png)\r\n![image](https://user-images.githubusercontent.com/13431372/149825713-f9ba7779-37e0-4d19-8264-12c21e217490.png)\r\n\r\n\r\n*Note: will update some of this code with theme colors once I get theming set up (i.e. the background of the indicator)","mergeCommitSha":"a7f850278764f3ec24cc79f3b4396b6c0d53156e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/42","title":"Add user status wrapper to icon ","createdAt":"2022-01-17T19:12:57Z"}
{"state":"Merged","mergedAt":"2022-03-01T19:10:00Z","number":420,"body":"For Web, setup message creation.\r\n* Does not include live updates as that depends on Message channel. TODO\r\n\r\nFor VSCode, setup thread creation.\r\n\r\nIntroduces new UUID library (copied from VSCode) since VSCode does not support crypto.getRandomValue which the npm uuid lib depends on.","mergeCommitSha":"2ad942f28e3004a1be789b3f303e48aac4ad3602","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/420","title":"Hookup Thread & Message Creation","createdAt":"2022-02-25T20:53:28Z"}
{"state":"Merged","mergedAt":"2023-01-10T00:54:04Z","number":4200,"mergeCommitSha":"06173822bf3bb93575d426172ecc9a0d9a62ea4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4200","title":"The /enterpriseProviders endpoint routes to auth service","createdAt":"2023-01-10T00:40:08Z"}
{"state":"Merged","mergedAt":"2023-01-10T01:07:39Z","number":4201,"body":"We need this for better debugging with aurora.","mergeCommitSha":"69b3a20bf32908a3ce2746ef81e5ea24eb7fe9c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4201","title":"Add application name to jdbc driver","createdAt":"2023-01-10T00:51:49Z"}
{"state":"Merged","mergedAt":"2023-01-10T17:55:21Z","number":4202,"mergeCommitSha":"7edd35bea04b0356a9c1e269eec37aa29f50995c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4202","title":"Prevent default for anchors within Link container","createdAt":"2023-01-10T01:22:31Z"}
{"state":"Closed","mergedAt":null,"number":4203,"body":"examples of the powerml topic gen and the openai summarization/qa","mergeCommitSha":"8a29e469bfa576d9214653fd17e0947327850945","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4203","title":"PowerML + UnblockedChat notebooks","createdAt":"2023-01-10T08:25:19Z"}
{"state":"Merged","mergedAt":"2023-01-10T18:32:13Z","number":4204,"body":"Ensure core set of properties are standarized across various connection types.","mergeCommitSha":"d49c190b081062741dbe6be8924a9118f323ffd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4204","title":"Standardize db connection properties","createdAt":"2023-01-10T18:05:00Z"}
{"state":"Merged","mergedAt":"2023-01-11T00:36:52Z","number":4205,"body":"There's quite a bit of resilience built into this:\r\n- Every API call is now auth gated so that we're not falling over when there's no auth token\r\n- Every API call retries 3 times for >= 500 errors\r\n- Uploads are now chunked to 5MB parts. Each part is retried up to 3 times with a backoff before giving up\r\n- Parallelized to 3 simultaneous part uploads. This parallelization is not dynamic, meaning it doesn't currently adjust according to network conditions. We could do that if needed through. The idea is to allow parts to complete while still maintaining some semblance of network efficiency. \r\n- Upload attempts are also auth gated as a hint about network reachability\r\n- Uploads will fall back to single single-shot if the file size is <= 20MB","mergeCommitSha":"c079474ea1f231ec1c326b16ce21b0e40d33fdd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4205","title":"Multipart upload client implementation","createdAt":"2023-01-10T18:24:20Z"}
{"state":"Closed","mergedAt":null,"number":4206,"mergeCommitSha":"80acb9a26f443d23f80a025552787ee08424da3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4206","title":"Guard against multiple Hub launch attempts","createdAt":"2023-01-10T18:30:23Z"}
{"state":"Merged","mergedAt":"2023-01-10T19:19:27Z","number":4207,"mergeCommitSha":"a761f601b322598efe4e1037f69e636751dc9ef3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4207","title":"Demo hack","createdAt":"2023-01-10T19:14:07Z"}
{"state":"Merged","mergedAt":"2023-01-10T21:36:24Z","number":4208,"body":"Undo demo hack.  Also add text clipping to context rows so very long topic names clip properly.\r\n\r\n<img width=\"1344\" alt=\"Screen Shot 2023-01-10 at 11 41 55 AM\" src=\"https://user-images.githubusercontent.com/2133518/211647267-98cfb9ad-6d76-4776-addc-8179d85105a4.png\">\r\n","mergeCommitSha":"e9d50c139b0b6d59d9c49cff9dba318d37e11e82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4208","title":"Undo demo hack","createdAt":"2023-01-10T19:43:54Z"}
{"state":"Merged","mergedAt":"2023-01-10T22:44:09Z","number":4209,"body":"https://linear.app/unblocked/issue/UNB-812/video-app-not-slurping-initial-window\r\n\r\nIssue Dennis was running into where slurps were sometimes missing.\r\n\r\nThe issue is that the frontmostApplication when AppUrlMonitor launches is always the UnblockedVideo App, not the application before.\r\n\r\nOur original approach was the activate the previous application but the activation event isn't perfect. Current theory is if that the menubarOwningApplication === target application, activating the target application doesn't actually work...\r\n\r\nTo work around this, the initial foreground application will be the menuBarOwningApplication (which will never be the VideoApp since it's an accessory app). \r\nOn application did change, we still want to go observe the frontmostApplication","mergeCommitSha":"7bd2274e28a3e53ab695e382cfa0c73eb016d82b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4209","title":"Use MenuBarOwningApplication on initializtion","createdAt":"2023-01-10T20:47:18Z"}
{"state":"Merged","mergedAt":"2022-02-25T21:28:35Z","number":421,"body":"- Adding service account for a new pusher service (for now same perms as API service)\r\n- Adding a new ECR repo for pusher service images (for now we build API service code and push it there)\r\n","mergeCommitSha":"50ff51e1c7e2fed4f6ff2e7a198620296eff74f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/421","title":"Prep infra for pusherservice","createdAt":"2022-02-25T21:22:31Z"}
{"state":"Merged","mergedAt":"2023-01-10T22:33:41Z","number":4210,"mergeCommitSha":"f647a0ec558c52a8329ec48bd7281a15230c5db3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4210","title":"Fix search for empty arrays","createdAt":"2023-01-10T22:18:19Z"}
{"state":"Merged","mergedAt":"2023-01-11T22:29:35Z","number":4211,"body":"Add API support for app types references\r\n\r\nEDIT:\r\n\r\nDue to issues with this being a breaking API change, decided to encode the app type into the FilePath instead for webFileReferences.","mergeCommitSha":"9a8127ed850316fa1c8b592e457688e7dd8dabbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4211","title":"Add support for app types","createdAt":"2023-01-10T22:22:01Z"}
{"state":"Merged","mergedAt":"2023-01-10T22:53:33Z","number":4212,"body":"Enable StyleLint processing in VSCode -- this gives red error underlines for CSS/SASS style bugs, so you don't have to wait for linting to run in CI or commandline.\r\n\r\nAt some point the StyleLint people changed the default settings to *not* process SASS files.  This re-enables it.\r\n\r\n<img width=\"1028\" alt=\"Screen Shot 2023-01-10 at 2 38 57 PM\" src=\"https://user-images.githubusercontent.com/2133518/211677363-7d877d75-4518-4472-869d-af965b44e0d7.png\">\r\n","mergeCommitSha":"a7cfd39feaa317a676972d167119a682829cf118","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4212","title":"Enable StyleLint in VSCode","createdAt":"2023-01-10T22:40:25Z"}
{"state":"Merged","mergedAt":"2023-01-10T22:51:35Z","number":4213,"mergeCommitSha":"60776ef9b803b3a713078421d60ab4d197ef80b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4213","title":"Add resiliency to processing stages","createdAt":"2023-01-10T22:51:27Z"}
{"state":"Merged","mergedAt":"2023-01-11T21:14:28Z","number":4214,"body":"Breaking API change:\r\n- Added values to EnterpriseProviderResult.Status enum\r\n- Not breaking customers, since this API is not yet used in PROD.\r\n\r\n\r\nTODO\r\n\r\n- [ ] tests\r\n\r\n\r\nDEFER\r\n\r\n- [ ] encrypt secrets\r\n- [ ] persist session and encode key in state parameter","mergeCommitSha":"5bc49bc109d1a39a43654ee45e2510216b120406","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4214","title":"[BREAKS API ON MAIN] Enterprise providers api implementation","createdAt":"2023-01-10T22:52:48Z"}
{"state":"Merged","mergedAt":"2023-01-10T23:06:46Z","number":4215,"mergeCommitSha":"53a4322ef621c37449d7fc5287b9676e3149f521","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4215","title":"Remove ses kinesis stack","createdAt":"2023-01-10T23:06:37Z"}
{"state":"Merged","mergedAt":"2023-01-11T22:45:08Z","number":4216,"body":"Removes Walkthrough entrypoint and code from VSCode","mergeCommitSha":"6abb52a9ee42997a6deb5f393de5a9f4a5670173","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4216","title":"Remove video walkthrough from VSCode","createdAt":"2023-01-10T23:22:05Z"}
{"state":"Merged","mergedAt":"2023-01-10T23:34:27Z","number":4217,"mergeCommitSha":"e2c9aa83727ea3bd0846d899ebe6e206f1cc0d46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4217","title":"Dont index open pull requests","createdAt":"2023-01-10T23:23:45Z"}
{"state":"Merged","mergedAt":"2023-01-11T00:16:12Z","number":4218,"mergeCommitSha":"37abe15ff6790c2f1709930389018482ebf0a575","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4218","title":"Use new Ventura API for login item registration","createdAt":"2023-01-11T00:03:37Z"}
{"state":"Merged","mergedAt":"2023-01-11T00:25:59Z","number":4219,"mergeCommitSha":"cbe872400a820cb25ebac9bbc1aadaa47216ceef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4219","title":"Use github auth token for protoc clone step","createdAt":"2023-01-11T00:25:34Z"}
{"state":"Merged","mergedAt":"2022-02-25T22:07:02Z","number":422,"body":"- Created new helm chart thingi for new pusher service\r\n- Modified Github actions workflows to deploy a copy of APIservice as pusher service\r\n- Modified service deployment workflow to take ECR repo name as an input param\r\n\r\nAs we discussed, the plan is to bring up a copy of API service as pusher until we separate those services.\r\n\r\nInfra prep PR: https://github.com/NextChapterSoftware/unblocked/pull/421","mergeCommitSha":"e1b4d82fa35a0354a09008bcf8db0160517181e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/422","title":"Setup helm charts and CI/CD for new pusher service","createdAt":"2022-02-25T21:36:28Z"}
{"state":"Merged","mergedAt":"2023-01-11T05:16:07Z","number":4220,"body":"Matt switched us over to Bert generated topics and approved a subset of them. We shouldn't delete these approved topics when regenerating topics.","mergeCommitSha":"4370f51b6b3ef735f1f3635add49675bf988b71f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4220","title":"Don't delete approved (or rejected) topics when regenerating topics","createdAt":"2023-01-11T00:30:32Z"}
{"state":"Merged","mergedAt":"2023-01-11T00:59:48Z","number":4221,"mergeCommitSha":"d43052faa535726a0956a227a8ce8ababf239d2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4221","title":"powerml topics","createdAt":"2023-01-11T00:35:57Z"}
{"state":"Merged","mergedAt":"2023-01-11T21:58:52Z","number":4222,"body":"* Refresh topic store listing after creating a new topic\r\n* Show Close button in Add Topic view\r\n* Fix bug where navigating back and forth in the wizard would clear the selected set of experts","mergeCommitSha":"da822d2c6f1310bff58130c8fe9683f67e5eb20a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4222","title":"Fix bugs in Add Topic dashboard UI","createdAt":"2023-01-11T00:39:07Z"}
{"state":"Merged","mergedAt":"2023-01-11T01:55:44Z","number":4223,"mergeCommitSha":"b9dc955a017ed062cb9aae74d4d336f8d1a06799","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4223","title":"Remove code dupe","createdAt":"2023-01-11T01:48:34Z"}
{"state":"Closed","mergedAt":null,"number":4224,"body":"Adds back some of the text cleaning logic that was removed from the preprocessing step\r\n","mergeCommitSha":"bf2bd52fac14b22e45df48938eff5e4fd55f2613","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4224","title":"Clean up inputs to histogram pipeline","createdAt":"2023-01-11T06:24:47Z"}
{"state":"Merged","mergedAt":"2023-01-11T19:59:18Z","number":4225,"body":"Customer request. Motivation: under terrible network conditions the dialog gets in the way\r\n\r\n<img width=\"383\" alt=\"CleanShot 2023-01-11 at 10 24 32@2x\" src=\"https://user-images.githubusercontent.com/858772/211887497-d7b60d99-7ffc-43a0-95a2-211bd5e86878.png\">\r\n","mergeCommitSha":"3a4ce8855e515d169ef56ca2d77df2ecaebcb869","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4225","title":"Make progress dialog backgroundable","createdAt":"2023-01-11T17:59:51Z"}
{"state":"Merged","mergedAt":"2023-01-11T18:51:15Z","number":4226,"mergeCommitSha":"7b4513a94102e4059b3725c292f4f02a082b33ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4226","title":"Add a maximum connection lifetime duration for db","createdAt":"2023-01-11T18:26:29Z"}
{"state":"Merged","mergedAt":"2023-01-11T20:47:44Z","number":4227,"mergeCommitSha":"c37d4aadcef72fd96fb2cdb67838008569b4a298","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4227","title":"[BREAKS API ON MAIN] Remove counts from search req and add new counts endpoint","createdAt":"2023-01-11T18:59:48Z"}
{"state":"Merged","mergedAt":"2023-01-11T19:35:59Z","number":4228,"body":"- Try new bert\r\n- Update\r\n- Try again\r\n","mergeCommitSha":"d726c1a8cf66208c02ea4702fbf7d66106d4c5f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4228","title":"TryNewBert","createdAt":"2023-01-11T19:35:43Z"}
{"state":"Merged","mergedAt":"2023-01-11T21:14:39Z","number":4229,"mergeCommitSha":"98d08ce169aa8d3c43aba8230822483e8a3a0f16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4229","title":"Fix folder filter","createdAt":"2023-01-11T21:14:17Z"}
{"state":"Merged","mergedAt":"2022-02-25T23:46:02Z","number":423,"mergeCommitSha":"a4f32f15b79b0dc4d91632858ca8f121daf24068","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/423","title":"Fix vpn concurrency issue for now","createdAt":"2022-02-25T22:23:54Z"}
{"state":"Merged","mergedAt":"2023-01-11T21:17:20Z","number":4230,"mergeCommitSha":"4725d6da8d00234eba935e866e14c32d37778cf6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4230","title":"Fix post processing","createdAt":"2023-01-11T21:15:59Z"}
{"state":"Merged","mergedAt":"2023-01-11T21:59:24Z","number":4231,"body":"Confirmed that dealloc occurs immediately after `uploaders` is set to empty","mergeCommitSha":"b99ffc1018452311da53fbe6600dc939cdf1490a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4231","title":"Fixes upload dealloc crash","createdAt":"2023-01-11T21:30:24Z"}
{"state":"Merged","mergedAt":"2023-01-21T00:03:32Z","number":4232,"mergeCommitSha":"ca97c563f99b9bdf66d0597eee2b68af93c77f62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4232","title":"Use hevc and aac","createdAt":"2023-01-11T21:38:28Z"}
{"state":"Merged","mergedAt":"2023-01-11T22:00:18Z","number":4233,"mergeCommitSha":"2a7e3006f481833009cc63b535e94c761b9c05a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4233","title":"trying to set a static version while I work on a fix to add retries","createdAt":"2023-01-11T21:48:13Z"}
{"state":"Merged","mergedAt":"2023-01-11T21:54:16Z","number":4234,"mergeCommitSha":"3467c248a7cb3650ee5628d240f4b11d72509725","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4234","title":"Cleanup post process lambda","createdAt":"2023-01-11T21:54:08Z"}
{"state":"Merged","mergedAt":"2023-01-11T23:04:53Z","number":4235,"mergeCommitSha":"94bb98b911fdd0c1c39f8a0d1cb6bdd99ce093b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4235","title":"Add logging to getRelatedTopics","createdAt":"2023-01-11T22:01:59Z"}
{"state":"Merged","mergedAt":"2023-01-20T22:39:02Z","number":4236,"body":"Whenever threads are added or archived locally, use the StreamOverlay to overlay a new/deleted thread into the stream.\r\n\r\nFixes UNB-815\r\n\r\nThis does a few things:\r\n\r\n* We had a very hacky overlay for this before, which only worked when creating threads (not deleting).  This PR switches this over to use the StreamOverlay, which is what we use elsewhere for overlaying data into streams.\r\n* Move the code that triggers the overlay from the discussion thread UI into ThreadStore, so it will trigger when any thread is added or removed anywhere.\r\n* This handles deletion correctly by overlaying a thread removal.\r\n\r\nThere is still some hackyness: because there is no way to have a push channel for this data, we instead temporarily poll for new sourcemarks.  This is just a local operation though, so it's OK to do.","mergeCommitSha":"08b32b2e54bf5febfd7d2b1a19dd9900faa8029b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4236","title":"Overlay file sourcemark addition/deletion in VSCode","createdAt":"2023-01-11T22:22:42Z"}
{"state":"Merged","mergedAt":"2023-01-12T01:06:01Z","number":4237,"body":"1. Reduces size of preview image for smaller transfers\r\n2. Update Video Draft viewport based on this: https://chapter2global.slack.com/archives/C02US6PHTHR/p1673462522627569\r\n\r\n3. Update VideoPreview images to have a fixed size with skeleton UI.\r\n\r\nhttps://user-images.githubusercontent.com/1553313/211932283-70c4f75c-7e9c-45cd-a84c-222ba1397633.mp4\r\n\r\n","mergeCommitSha":"ffce7d83f9be8bfb75ee2e6353389f0b3c7b4d60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4237","title":"Polish video loading states on dashboard","createdAt":"2023-01-11T22:33:34Z"}
{"state":"Merged","mergedAt":"2023-01-11T23:07:58Z","number":4238,"body":"Trying to help with deployment speed and consistency. We are wasting over 60% of our resources","mergeCommitSha":"311ded4274eeeac3346178a7c508fcb5074547eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4238","title":"reduce cpu request to help with deployments","createdAt":"2023-01-11T22:55:53Z"}
{"state":"Merged","mergedAt":"2023-01-11T23:25:40Z","number":4239,"body":"Instead of using fetch POST, we want to use form post so that the browser follows.","mergeCommitSha":"a636a960ba7b4ee728dcd0dad87b02e5d6396972","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4239","title":"Fix GHE Registration POST","createdAt":"2023-01-11T23:13:15Z"}
{"state":"Merged","mergedAt":"2022-02-25T22:29:44Z","number":424,"mergeCommitSha":"e338d40fc9f7be2697f44702805384ffc05c7bdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/424","title":"update readme","createdAt":"2022-02-25T22:27:08Z"}
{"state":"Merged","mergedAt":"2023-01-11T23:25:28Z","number":4240,"mergeCommitSha":"64bd9349aa85e767c4f8cec9d8c17c0e062ef82c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4240","title":"Only return approved topics","createdAt":"2023-01-11T23:16:41Z"}
{"state":"Merged","mergedAt":"2023-01-11T23:51:21Z","number":4241,"mergeCommitSha":"c6aa6be71c1e885e75d8403298d86a175be5fa62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4241","title":"score powerml","createdAt":"2023-01-11T23:49:49Z"}
{"state":"Merged","mergedAt":"2023-01-12T00:17:21Z","number":4242,"body":"First attempt at fixing returning topics for a file","mergeCommitSha":"042f181a38f7be2fe0d41e9650ece69fb169ce39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4242","title":"Get topics by source","createdAt":"2023-01-12T00:00:14Z"}
{"state":"Merged","mergedAt":"2023-01-16T23:01:55Z","number":4243,"body":"No significant UI changes, just replace the API providing the data. The dropdown filtering is now done via the API (instead of clientside filtering):\r\n<img width=\"1439\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/211946817-4023d1ff-b45b-4b1e-986a-f7d1a87ed6bb.png\">\r\n<img width=\"682\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/211946830-ac774484-ff19-48a7-9d49-7563c41a8bf5.png\">\r\n\r\n* Refactored logic from the ThreadSearchStore into its own base store for reuse (kept the old code in the Mine view, can refactor that out separately since it's unrelate to topics work) \r\n* Added ~InsightSearchStore~ InsightSearchStream to query the new search API \r\n","mergeCommitSha":"5137b2383e106adb217af39ac2b38c01a3e764ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4243","title":"Integrate search API with TopicView","createdAt":"2023-01-12T00:25:27Z"}
{"state":"Merged","mergedAt":"2023-01-13T17:22:27Z","number":4244,"body":"<img width=\"686\" alt=\"Screen Shot 2023-01-11 at 5 07 35 PM\" src=\"https://user-images.githubusercontent.com/2133518/211951669-b43353fd-6230-44ed-b953-b4ed56d830ce.png\">\r\n","mergeCommitSha":"c5247685c876cc6b4e582e70c084fc3a3414804b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4244","title":"Responsive 'Add Topic' button in mobile dashboard","createdAt":"2023-01-12T01:07:20Z"}
{"state":"Merged","mergedAt":"2023-01-12T01:31:44Z","number":4245,"body":"There was a discrepancy between 'large' breakpoint definitions causing some display bugs.","mergeCommitSha":"df5fbf5f9ddaadd971f4c00093ee6cbf52c0ce40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4245","title":"Make breakpoints consistent","createdAt":"2023-01-12T01:24:04Z"}
{"state":"Merged","mergedAt":"2023-01-12T05:26:28Z","number":4246,"body":"Actions were referencing the wrong postMessage :(\r\n\r\nUnfortunately there's a \"standard\" postMessage API in typescript so we didn't catch the type issue.","mergeCommitSha":"39804d0dcfecddb9233f592f3367be2fc401142b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4246","title":"Fix discussion thread header post message","createdAt":"2023-01-12T01:42:29Z"}
{"state":"Merged","mergedAt":"2023-01-12T02:02:48Z","number":4247,"mergeCommitSha":"bfacbf6b62a6f98282938175e1968b20acf8bc93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4247","title":"Fix sort ordering","createdAt":"2023-01-12T02:02:14Z"}
{"state":"Merged","mergedAt":"2023-01-12T17:31:11Z","number":4248,"body":"Missing max height on video","mergeCommitSha":"17a1ba78f37abfc2ae77e4cff86f35f57bab991e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4248","title":"Add maxHeight to video","createdAt":"2023-01-12T05:36:59Z"}
{"state":"Merged","mergedAt":"2023-01-12T20:03:19Z","number":4249,"body":"Update browser icon in URL Icon","mergeCommitSha":"cb1a1f78bbcb9b069c4a2ed684651b6107bcf489","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4249","title":"Update Browser Icon","createdAt":"2023-01-12T06:37:17Z"}
{"state":"Merged","mergedAt":"2022-02-25T22:41:24Z","number":425,"mergeCommitSha":"456dfbdf5606f7c3d7a6d041302036bc4cdb6ce9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/425","title":"update","createdAt":"2022-02-25T22:39:34Z"}
{"state":"Merged","mergedAt":"2023-01-12T20:13:05Z","number":4250,"body":"Deleting references were problematic for webFileReferences as filePaths can be identical.\r\nThis typically occurs when there are empty paths `/` if we slurped websites at their root path.","mergeCommitSha":"b2738d1d6917b0cdb52c9b557f6b725ee60f82a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4250","title":"Fix Reference Delete","createdAt":"2023-01-12T06:59:06Z"}
{"state":"Merged","mergedAt":"2023-01-12T18:06:43Z","number":4251,"mergeCommitSha":"dad5d39053d96f45ee5a0c6a5b2ad1a588e3788a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4251","title":"increase timeout","createdAt":"2023-01-12T18:06:36Z"}
{"state":"Merged","mergedAt":"2023-01-12T23:14:47Z","number":4252,"mergeCommitSha":"8f626a0ab1189f6495ca7fd3e1cc3be3754c26d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4252","title":"Clean up TopicService","createdAt":"2023-01-12T19:44:43Z"}
{"state":"Merged","mergedAt":"2023-01-12T20:09:49Z","number":4253,"body":"Cluster and order scm and slack messages.\r\n","mergeCommitSha":"d9a16af0cdb2a7e24c73124ab4615506aaee98ef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4253","title":"Improve PowerML spatial ordering","createdAt":"2023-01-12T19:45:42Z"}
{"state":"Merged","mergedAt":"2023-01-12T20:52:59Z","number":4254,"body":"gettopics returned from service were not respecting topic score.","mergeCommitSha":"20666b8d1465b52f3a334a478adf20a0c6429e4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4254","title":"Returned topics need to be sorted","createdAt":"2023-01-12T20:43:39Z"}
{"state":"Merged","mergedAt":"2023-01-13T00:13:53Z","number":4255,"mergeCommitSha":"adce0f415a9a1c24105725df60d56ecc6ea48729","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4255","title":"Remove stanford lib from API service","createdAt":"2023-01-12T21:14:03Z"}
{"state":"Merged","mergedAt":"2023-01-18T22:07:29Z","number":4256,"body":"There were some tricky bits here, specifically because the dropdown header is only displayed when the user is hovering over the associated row, and the button \"background\" only displays when the mouse is over the button.\r\n\r\nI couldn't find a UX for the confirmation dialog -- @benedict-jw is this OK?\r\n\r\n<img width=\"1311\" alt=\"Screen Shot 2023-01-12 at 2 07 14 PM\" src=\"https://user-images.githubusercontent.com/2133518/212191562-ac1347c8-4a9d-4fda-9471-3ca4e4ac128e.png\">\r\n<img width=\"1311\" alt=\"Screen Shot 2023-01-12 at 2 07 19 PM\" src=\"https://user-images.githubusercontent.com/2133518/212191579-3348ee01-16cf-4404-89f9-42da07c6d9aa.png\">\r\n","mergeCommitSha":"b15d7df9612fcf31e8df17d9d8c385ef5e6ad0a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4256","title":"Allow topic deletion in dashboard topic list UI","createdAt":"2023-01-12T22:07:58Z"}
{"state":"Merged","mergedAt":"2023-01-12T22:40:14Z","number":4257,"body":"Opt in to 1:1 sample rate.","mergeCommitSha":"358fa820bd0855fc44c2ebad215119060db53a55","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4257","title":"Fix sampling","createdAt":"2023-01-12T22:37:11Z"}
{"state":"Merged","mergedAt":"2023-01-12T23:53:45Z","number":4258,"mergeCommitSha":"fcf578fd0cb165c88f53f7b8406becfb8c7bb6bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4258","title":"Up polling interval","createdAt":"2023-01-12T23:52:31Z"}
{"state":"Merged","mergedAt":"2023-01-13T03:09:32Z","number":4259,"mergeCommitSha":"4687e43be2b7d8746ce6cbf6e94aa0aacc84b767","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4259","title":"Auth provider vends enterprise API clients","createdAt":"2023-01-13T00:03:29Z"}
{"state":"Merged","mergedAt":"2022-02-25T23:40:38Z","number":426,"body":"We've two API services now so we'd be creating dupes. Need to think of a better approach.","mergeCommitSha":"955fdd813c71f4f9d3918cd38f68f1293d81c049","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/426","title":"Disable ingesting PRs in dev","createdAt":"2022-02-25T23:09:32Z"}
{"state":"Merged","mergedAt":"2023-01-13T00:12:47Z","number":4260,"mergeCommitSha":"0fa4f659549627419e6324eba884388916b89cc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4260","title":"Add diagnostics","createdAt":"2023-01-13T00:04:59Z"}
{"state":"Merged","mergedAt":"2023-01-13T00:24:16Z","number":4261,"body":"Since we have a real topic UI now, we don't need this anymore.  Removing this also lets us remove the API it uses.","mergeCommitSha":"003efd4ef68a2813deefedb31c3629fd36f4d6fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4261","title":"Remove old topic extraction UI","createdAt":"2023-01-13T00:06:07Z"}
{"state":"Merged","mergedAt":"2023-01-13T17:18:56Z","number":4262,"body":"Fixes an issue where partial matches on strings containing `/`s don't return search results. \r\n\r\nThe fix is to split such strings and append the result to the string to be indexed.","mergeCommitSha":"459a03589dc3c67918e0c926a64f3f63ccf72c37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4262","title":"Index file path components","createdAt":"2023-01-13T01:10:57Z"}
{"state":"Merged","mergedAt":"2023-01-13T19:00:23Z","number":4263,"body":"This reverts commit 2a589dddc4d39c8a7eb7a4c58b6a1dc8e5a4888a.","mergeCommitSha":"b8cad7fa4717ffde858b7c984fac97fb9263af3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4263","title":"Revert \"Drop ThreadTopicModel and PullRequestTopicModel tables (#4163)\"","createdAt":"2023-01-13T01:16:52Z"}
{"state":"Closed","mergedAt":null,"number":4264,"body":"Deprecated operation that's no longer used by the client.","mergeCommitSha":"cde4bf53e6153b75acdfd9d54817f87d09c1a4a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4264","title":"[BREAKS API ON MAIN] Remove getRecommendedTopics operation","createdAt":"2023-01-13T01:22:41Z"}
{"state":"Merged","mergedAt":"2023-01-13T06:58:47Z","number":4265,"mergeCommitSha":"3340ec8195f307569a5e66e1bf3de15454c377d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4265","title":"Vend enterprise version of GitHub User client","createdAt":"2023-01-13T01:25:20Z"}
{"state":"Merged","mergedAt":"2023-01-13T04:44:52Z","number":4266,"body":"First parts of manifest flow styled\r\n\r\n<img width=\"1170\" alt=\"CleanShot 2023-01-12 at 18 14 28@2x\" src=\"https://user-images.githubusercontent.com/1553313/212221531-50b41c6d-cbbd-4cd2-a61b-e00a0d01caf1.png\">\r\n<img width=\"1188\" alt=\"CleanShot 2023-01-12 at 18 14 32@2x\" src=\"https://user-images.githubusercontent.com/1553313/212221535-69e328b6-10f9-4501-9a1f-f9504a1529b3.png\">\r\n","mergeCommitSha":"9230d5a24048b13bf4683d7f9f0be531a0ba6b77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4266","title":"V0.1 Manifest flow style","createdAt":"2023-01-13T02:15:32Z"}
{"state":"Merged","mergedAt":"2023-01-13T05:09:09Z","number":4267,"body":"Adding spinner to manifest page","mergeCommitSha":"57c9e12cb321461d066a9e9e762cf0821ae9e6f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4267","title":"Update spinner","createdAt":"2023-01-13T05:01:07Z"}
{"state":"Merged","mergedAt":"2023-01-13T07:05:44Z","number":4268,"body":"Design\r\nhttps://www.figma.com/file/qViiyIpROqSPQF5hspSM7P/Unblocked---Onboarding\r\n\r\nExample\r\n<img width=\"1153\" alt=\"Screenshot 2023-01-12 at 22 05 04\" src=\"https://user-images.githubusercontent.com/1798345/212249111-33558d26-7cbf-4a6a-b40d-e52988d1f387.png\">\r\n","mergeCommitSha":"616601cd7c752520111433b13b5377e45a23ee58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4268","title":"Update Github Enteprise error text to match designs","createdAt":"2023-01-13T06:03:49Z"}
{"state":"Merged","mergedAt":"2023-01-13T07:14:45Z","number":4269,"body":"Manifest Apps created in GHE instances are now eligible to be installed on any GitHub Org on the private-cloud instance.","mergeCommitSha":"5b395ebce15d870a4b71394f6502f321a27800c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4269","title":"GitHub Enterprise Apps are public now","createdAt":"2023-01-13T06:59:59Z"}
{"state":"Merged","mergedAt":"2022-02-25T23:35:22Z","number":427,"body":"Github actions do not have a global timeout.\r\nSo we set it per job\r\n","mergeCommitSha":"57592aa648300816cab75eeb16b96667b2b3c95e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/427","title":"Add timeout for all github jobs","createdAt":"2022-02-25T23:18:21Z"}
{"state":"Merged","mergedAt":"2023-01-13T17:37:50Z","number":4270,"mergeCommitSha":"715e5e230f87426e920bcc97ec0a9f411c9660ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4270","title":"Show/hide topic tags under feature flag","createdAt":"2023-01-13T16:07:37Z"}
{"state":"Merged","mergedAt":"2023-01-13T19:41:30Z","number":4271,"mergeCommitSha":"100e8197c7efc48c315b1605c54964f87dce9fb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4271","title":"Slack webhooks need to handle workspaces shared across teams","createdAt":"2023-01-13T19:21:56Z"}
{"state":"Merged","mergedAt":"2023-01-17T00:11:00Z","number":4272,"mergeCommitSha":"d5d7e6d3fe3799d5b0056e66c206a98a9dea075c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4272","title":"SearchV2 filtering by author should be an OR operation (not AND)","createdAt":"2023-01-13T19:40:02Z"}
{"state":"Merged","mergedAt":"2023-01-14T00:16:24Z","number":4273,"body":"The underlying cause for the crash was asynchronous access on an array. Should have spotted this earlier. Moved those shared properties to an `actor`.\r\n\r\nThere was another crash that happened due to early deallocations, which I _think_ was due to a swift bug with incorrectly tagged mutable arrays. \r\n\r\nAnd finally I spotted early deallocations when the uploader instances weren't captured in properties and instead relied on `@escaping` closure capture. There seems to be some weird swift behaviour there too","mergeCommitSha":"ccd17cab0369f42fd707910f542646256ea3023e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4273","title":"Fix video upload crash take 2","createdAt":"2023-01-13T20:08:10Z"}
{"state":"Merged","mergedAt":"2023-01-13T20:43:35Z","number":4274,"body":"Right now this just filters out anyone with any of the same names.\r\n\r\nprivate.yml:\r\n<img width=\"255\" alt=\"Screen Shot 2023-01-13 at 12 25 07 PM\" src=\"https://user-images.githubusercontent.com/2133518/212412656-5527d439-91c2-4ccd-9209-06bc35a65f1f.png\">\r\n\r\nSourceMarkEngine.ts\r\n<img width=\"305\" alt=\"Screen Shot 2023-01-13 at 12 25 27 PM\" src=\"https://user-images.githubusercontent.com/2133518/212412703-f7877de2-feca-4934-8749-daefd42ea9b1.png\">\r\n","mergeCommitSha":"d6bd4ad8f1a5edff824c03a91bd5ce39f3bb2baa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4274","title":"Hack to filter duplicate users out of the tooltips","createdAt":"2023-01-13T20:25:37Z"}
{"state":"Merged","mergedAt":"2023-01-20T18:11:17Z","number":4275,"body":"Refactored parts of HomeHeader to HomeHeaderContainer.\r\n\r\nApplied HomeHeaderContainer to login pages\r\n\r\n![CleanShot 2023-01-13 at 12 31 36@2x](https://user-images.githubusercontent.com/1553313/212414070-6bf3a150-087e-45dd-a2d5-263dda078126.png)\r\n\r\n<img width=\"1467\" alt=\"CleanShot 2023-01-13 at 12 40 28@2x\" src=\"https://user-images.githubusercontent.com/1553313/212415158-114bc182-c99a-4008-9004-08b547285ce0.png\">\r\n\r\n","mergeCommitSha":"bd5e5e5e136dfd14e58f8d5ad73d9b8c0ff466cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4275","title":"Updated Login routes to include HomeHeader","createdAt":"2023-01-13T20:40:48Z"}
{"state":"Merged","mergedAt":"2023-01-13T23:43:48Z","number":4276,"body":"We were not adding contributors to the actual thread...\r\n\r\n<img width=\"1180\" alt=\"CleanShot 2023-01-13 at 12 48 05@2x\" src=\"https://user-images.githubusercontent.com/1553313/212416398-b1560c87-bb7b-4bc7-aa16-f464170f061f.png\">\r\n","mergeCommitSha":"3ac2e93c9829c9661310944b4c4263e02a20c522","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4276","title":"Add Contributor to walkthrough","createdAt":"2023-01-13T20:48:40Z"}
{"state":"Merged","mergedAt":"2023-01-19T03:37:13Z","number":4277,"body":"This introduces the search notebook with some embedding, search, summarization, prompting examples for topics/systems/people/experts.","mergeCommitSha":"e1fd3dcb73b7849400a5a0a19d3f2b1af2ca44f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4277","title":"Search","createdAt":"2023-01-13T21:47:30Z"}
{"state":"Merged","mergedAt":"2023-01-14T00:43:19Z","number":4278,"mergeCommitSha":"d14759fb6f581fcec84863813e41acccb4dc1ac5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4278","title":"Fix message layout breakpoint","createdAt":"2023-01-13T21:50:40Z"}
{"state":"Merged","mergedAt":"2023-01-13T22:49:50Z","number":4279,"body":"As per @kaych 's great ideas","mergeCommitSha":"b6917488f02ce4882a7eae0d12769ac56cc23e65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4279","title":"Better hack for deducing users","createdAt":"2023-01-13T21:54:35Z"}
{"state":"Merged","mergedAt":"2022-02-26T00:15:24Z","number":428,"mergeCommitSha":"5b6f382bbc223026a97f64d15df7dccfa999bc7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/428","title":"update","createdAt":"2022-02-26T00:11:54Z"}
{"state":"Merged","mergedAt":"2023-01-16T19:27:10Z","number":4280,"body":"![CleanShot 2023-01-13 at 14 32 09](https://user-images.githubusercontent.com/13431372/212431351-e1825d0a-8b53-4ce7-b848-009129869b2f.gif)\r\n","mergeCommitSha":"10e1b8376b6257e9614561b3ea80e7e21381020f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4280","title":"Scroll icon stack and fix maxIcons logic","createdAt":"2023-01-13T22:33:16Z"}
{"state":"Merged","mergedAt":"2023-01-17T19:35:57Z","number":4281,"body":"Add Linear, Notion & Unblocked icons for code references\r\n\r\nAlso added yml to list of file icons.\r\n\r\n<img width=\"546\" alt=\"CleanShot 2023-01-13 at 15 35 09@2x\" src=\"https://user-images.githubusercontent.com/1553313/212437469-103b2780-56cc-47bb-97b3-1e4492970c70.png\">\r\n","mergeCommitSha":"74c35bf2eff9e3e018dd5121ee61a57767292d8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4281","title":"Add Custom Icons","createdAt":"2023-01-13T23:34:47Z"}
{"state":"Merged","mergedAt":"2023-01-14T00:06:25Z","number":4282,"body":"Slack has its own markup language. Converting it to proper markdown is too heavy of a lift right now, but we can do something simple to preserve bullet point formatting in ingested slack threads.","mergeCommitSha":"e4bfcf80678fe89566a0e47276c918ca4c94c821","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4282","title":"Transform slack bullet points to preserve formatting","createdAt":"2023-01-13T23:49:17Z"}
{"state":"Merged","mergedAt":"2023-01-14T00:41:09Z","number":4283,"mergeCommitSha":"392f41948cad7d4f73cba0ab11ea566828ce1cf5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4283","title":"Determine enterprise provider endpoint from state","createdAt":"2023-01-14T00:23:50Z"}
{"state":"Merged","mergedAt":"2023-01-16T04:55:06Z","number":4284,"body":"This will allow returning insights by recency in descending order so that the newest insights appear at the top.","mergeCommitSha":"ab316e8bd33c7ee183346100358c799726c0572d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4284","title":"Use SearchInsightService for getTopicRelatedInsights","createdAt":"2023-01-16T04:44:59Z"}
{"state":"Closed","mergedAt":null,"number":4285,"body":"This reverts commit ab316e8bd33c7ee183346100358c799726c0572d.","mergeCommitSha":"16d2bfa541ae1c0d6ba9cf87ae353f72eb527442","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4285","title":"Revert \"Use SearchInsightService for getTopicRelatedInsights (#4284)\"","createdAt":"2023-01-16T04:57:53Z"}
{"state":"Merged","mergedAt":"2023-01-16T05:28:29Z","number":4286,"body":"Just for now so that they don't appear in the Topics overview page","mergeCommitSha":"5d6a3c335529b5edd68798b2f155dfa1f26b8355","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4286","title":"Dont index archived threads","createdAt":"2023-01-16T05:19:40Z"}
{"state":"Merged","mergedAt":"2023-01-19T17:24:14Z","number":4287,"body":"Support VSCode GHE.\r\n\r\nUpdated LoginManifest with Basic ComboBox implementation to allow multiple repos.\r\n\r\n<img width=\"1202\" alt=\"CleanShot 2023-01-15 at 21 50 47@2x\" src=\"https://user-images.githubusercontent.com/1553313/212607541-72e2e32e-0ac6-4fa2-9dcd-21bd99add573.png\">\r\n","mergeCommitSha":"1a80a127c373c45f3d910580befbb09f46fec0cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4287","title":"VScode enterprise auth","createdAt":"2023-01-16T05:52:17Z"}
{"state":"Merged","mergedAt":"2023-01-16T06:37:29Z","number":4288,"mergeCommitSha":"8bd4a9249b192bc2fafc770f1ed0f7af609681b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4288","title":"Include archived threads when triggering reindexing","createdAt":"2023-01-16T06:23:09Z"}
{"state":"Merged","mergedAt":"2023-01-16T07:35:10Z","number":4289,"mergeCommitSha":"87d2b3077f1fbffb274a8cf6d67af27092b6b5a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4289","title":"Map insight to topics after creating or editing","createdAt":"2023-01-16T06:51:20Z"}
{"state":"Merged","mergedAt":"2022-02-28T02:54:46Z","number":429,"body":"refactor ","mergeCommitSha":"0ad81f48ee1b690a44a8fea3449c22c569a84d34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/429","title":"Utility to get test resource","createdAt":"2022-02-26T23:11:38Z"}
{"state":"Merged","mergedAt":"2023-01-16T08:35:48Z","number":4290,"mergeCommitSha":"7b0c18cccf472b5d87df84b9803e848ece1f5329","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4290","title":"Send search index event on thread archive or restoration","createdAt":"2023-01-16T08:26:56Z"}
{"state":"Merged","mergedAt":"2023-01-16T20:07:30Z","number":4291,"mergeCommitSha":"0c2aa3d120e42e51e4bbe4aafd3caad2358f40db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4291","title":"findEnterpriseProvider now returns error message and org name if possible","createdAt":"2023-01-16T18:40:34Z"}
{"state":"Merged","mergedAt":"2023-01-16T21:04:05Z","number":4292,"body":"The handler just logs for now. Once I've confirmed its all wired up correctly, I'll move the logic from the search service to the topic service.","mergeCommitSha":"bc907c39d21129096ccda005797f3db0a3e93f03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4292","title":"Send topic insight mapping event after insight indexing","createdAt":"2023-01-16T18:57:48Z"}
{"state":"Merged","mergedAt":"2023-01-16T21:37:42Z","number":4293,"body":"To support local client development against DEV backend stack.\n\nThis change only applies to DEV.","mergeCommitSha":"075ad716e5e436a15525668181b2239b4288baf0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4293","title":"Allowlist GitHub Enterprise callback URLs","createdAt":"2023-01-16T21:06:01Z"}
{"state":"Merged","mergedAt":"2023-01-16T22:16:22Z","number":4294,"mergeCommitSha":"b5f74447afa4a0762ca2770c45b9516b7125458e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4294","title":"Move topic insight mapping logic to topic service","createdAt":"2023-01-16T21:16:27Z"}
{"state":"Merged","mergedAt":"2023-01-16T21:54:22Z","number":4295,"mergeCommitSha":"f91cbca73a65f6ea4e11fe9f16cd59f8482a10f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4295","title":"Make TopicInsightMappingEventPayload serializable","createdAt":"2023-01-16T21:42:12Z"}
{"state":"Merged","mergedAt":"2023-01-16T23:37:12Z","number":4296,"body":"Update LoginOptions to include `AvailableEnterpriseProvider`\r\n\r\nAdds dashboard URL to manifest creation page to prevent hard-coded links in VSCode + hub.","mergeCommitSha":"48aeb613550da1e9b56a39f32c4e35aca42d126d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4296","title":"Login Options V3","createdAt":"2023-01-16T22:24:41Z"}
{"state":"Merged","mergedAt":"2023-01-16T22:56:12Z","number":4297,"body":"GitHub Enterprise App creation will take more time than a simple OAuth dance,\r\nso we're extending the expiry to accomodate.\r\n\r\nRisk is that an potential attacker who used another exploit to steal an exchange\r\ntoken now has 50 more minutes to pull off the attack.\r\n\r\n### Future alternatives\r\n\r\n- Setup client-request-signing using asymmetric key-pair exchange before pre-auth,\r\n  so that the server can trust the origin of the request.\r\n- Make the exchange token single-use. This would allow us to _detect_ (but not prevent)\r\n  token stealing. Downside is that this is not resilient to dropped network responses.\r\n  So this is not a great option.","mergeCommitSha":"7f7c1c5e5ed53a604caf2f31f3e6f40d7871f938","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4297","title":"Extend the auth exchange window from 10m to 1h to allow for GHE","createdAt":"2023-01-16T22:37:13Z"}
{"state":"Merged","mergedAt":"2023-01-17T00:18:21Z","number":4298,"mergeCommitSha":"86e5b73e8815bdd785dabb44583a349065dc24f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4298","title":"Refactor search query with debouncer","createdAt":"2023-01-17T00:05:01Z"}
{"state":"Merged","mergedAt":"2023-01-17T03:35:11Z","number":4299,"body":"GitHub Enterprise hooks are exactly the same shape as GitHub Cloud hooks.\r\n\r\nWe route the GHE hooks to the exact same webhook-service endpoint as GitHub Cloud (`/api/hooks/github`).\r\n\r\nThe only difference is that we now validate the source of the hook using the\r\nGitHub App ID (from the `X-GitHub-Hook-Installation-Target-ID` request header)\r\nagainst known cloud (configured) and server (persisted) GitHub Apps.\r\nIf the source of the hook is unknown, then we throw it away.","mergeCommitSha":"b848def4366a5944a9385a307f9c86bbc412e9f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4299","title":"Support for GitHub Enterprise hooks","createdAt":"2023-01-17T00:08:01Z"}
{"state":"Merged","mergedAt":"2022-01-17T19:43:31Z","number":43,"body":"We are now moving api package to being a gradle project.\r\nWe are defining the private spec api generator as an openapi task.\r\nWe also fix up makefile to simplify code gen.","mergeCommitSha":"60abc5f3f04e8a381f66151f28565f16cd024f5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/43","title":"Make api a gradle project","createdAt":"2022-01-17T19:35:10Z"}
{"state":"Merged","mergedAt":"2022-02-28T00:25:58Z","number":430,"body":"To use, create a new file with your `$USER` here:\r\n```\r\ntouch apiservice/src/main/resources/config/local/$USER.conf\r\n```","mergeCommitSha":"318375e5ccb3247d0e47e828c71df04de8df6826","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/430","title":"User-specific local config overrides","createdAt":"2022-02-26T23:12:22Z"}
{"state":"Merged","mergedAt":"2023-01-20T20:05:25Z","number":4300,"body":"Supports Enterprise Auth from Hub.\r\n\r\nUpdated so that login items reflect provided login options. Still partially hardcoded due to UI (temporary as there will be a large revamp with new hub)\r\n\r\n<img width=\"632\" alt=\"CleanShot 2023-01-16 at 16 30 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/212784832-a0f260eb-72be-454b-b9b9-f4240180e5c4.png\">\r\n\r\n<img width=\"619\" alt=\"CleanShot 2023-01-16 at 16 30 07@2x\" src=\"https://user-images.githubusercontent.com/1553313/212784839-c1d10dca-81b4-43a9-a5ea-3e5f1d7d0324.png\">","mergeCommitSha":"bbccbf6d35505a958da60d00e0ea715a1b68a58f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4300","title":"Hub Enterprise Auth","createdAt":"2023-01-17T00:31:30Z"}
{"state":"Merged","mergedAt":"2023-01-17T01:08:31Z","number":4301,"body":"This will be used in the explorer insights panel, to allow searching for insights within a particular file.  The \"file\" is represented by the set of commits (PRs) and threads.","mergeCommitSha":"e032e28ebd8a1e6d471d66d4d4eb32023e624d50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4301","title":"Add commit/thread IDs to insight search","createdAt":"2023-01-17T00:41:16Z"}
{"state":"Merged","mergedAt":"2023-01-17T06:09:53Z","number":4302,"mergeCommitSha":"cf958f8c5ef7dcbd8668e49b372b2ab63acc9244","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4302","title":"Fix webhook validation typo","createdAt":"2023-01-17T05:41:13Z"}
{"state":"Merged","mergedAt":"2023-01-17T21:04:05Z","number":4303,"body":"Elastic Transcoder does not have any CloudFormation resources. This means we can't automate the creation of pipelines and presets so they need to be created via AWS console as described here https://www.notion.so/nextchaptersoftware/Video-Transcoding-Pipeline-Setup-5e53b9675e754716b22c0d22d36eb92b\r\n\r\n- Added build config to support multiple jobs and pipelines if needed \r\n- Added stack to create IAM role, Lambda and S3 Lambda trigger for transcoding jobs \r\n- Modified Lambda construct to support pre-made roles \r\n- Updated Dev and prod configs with IDs and configuration for each environment's respective pipeline\r\n\r\nI have tested it in Dev and it works fine. We need to decide whether to remove input_suffix or make a change to asset service ?\r\n\r\n\r\n\r\nhttps://linear.app/unblocked/issue/UNB-825/create-video-transcoding-lambda-to-trigger-mediaconvert-jobs","mergeCommitSha":"2d4d49c88973cf341e60122e9d6cb6daaf5173f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4303","title":"add lambda function for video asset transcoding","createdAt":"2023-01-17T12:04:40Z"}
{"state":"Merged","mergedAt":"2023-01-17T18:33:33Z","number":4304,"mergeCommitSha":"011cc4028dfb715324f931c64af7ce8810850b4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4304","title":"Implement insight search with ID filter","createdAt":"2023-01-17T18:15:16Z"}
{"state":"Merged","mergedAt":"2023-01-17T19:16:24Z","number":4305,"body":"Hook events sourced from GitHub Cloud are allowed only if their IP source matches\nGitHub's published hook IPs.\n\nAs a result we need to separate the GHE hooks, since they can come from anywhere.","mergeCommitSha":"644d5187a66819d26af5311133854b8a99902f63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4305","title":"[BREAKS API ON MAIN] Separate endpoint for GHE hooks","createdAt":"2023-01-17T18:38:18Z"}
{"state":"Merged","mergedAt":"2023-01-17T18:57:19Z","number":4306,"body":"https://chapter2global.slack.com/archives/C02GEN8LFGT/p1673980290159429","mergeCommitSha":"4907b2b9fd89a2ccdbfbec2dd0d246ed78a50ae1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4306","title":"Fix null filemark line range in emails","createdAt":"2023-01-17T18:39:37Z"}
{"state":"Merged","mergedAt":"2023-01-18T20:31:35Z","number":4307,"body":"First cut of a team member view in the dashboard:\r\n<img width=\"1284\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/212994593-941ae62c-db96-47ae-920e-9d761112b153.png\">\r\n\r\n* Refactored out a bit of the topic view components for reuse here and in the search views\r\n* Missing topics and related team members in this user's network (API work needed for this)","mergeCommitSha":"d908e48377f8cb00c18f5a61f8897f16190ad607","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4307","title":"[Dashboard] Add team member view ","createdAt":"2023-01-17T19:32:27Z"}
{"state":"Merged","mergedAt":"2023-01-17T19:49:16Z","number":4308,"mergeCommitSha":"327172595090dc22e8d7aa7f726223285f96b3b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4308","title":"use s3 construct","createdAt":"2023-01-17T19:41:26Z"}
{"state":"Merged","mergedAt":"2023-01-17T19:57:52Z","number":4309,"mergeCommitSha":"e69aec64597655c6513526f3abc7c2019d3602d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4309","title":"Rename TopicInsightMappingEventService function","createdAt":"2023-01-17T19:48:11Z"}
{"state":"Merged","mergedAt":"2022-02-28T22:27:39Z","number":431,"body":"### Changes\r\n\r\n- Move toward a more \"event-based\" logger, where varying properies are confined to MDC\r\n  and the message are simply events. This allows us to create nice property based filters\r\n  in Logz.io, rather than parsing strings.\r\n\r\n-  Dropping the Ktor HttpClient loggers, which are totally useless in a distributed\r\n   system. For example, look at the data from this Logz.io query:\r\n   https://app.logz.io/#/goto/a0ed2f609033e3142f4774afdea826a2?switchToAccountId=411850\r\n\r\n-  Drop Exposed logger for everything except tests. Can be enabled locally with the\r\n   `enableSqlLogging` config instead.\r\n\r\n- Coloured logging.\r\n\r\n### Patterns\r\n- Logger construction; KotlinLogging has a very simple one-liner:\r\n\r\n       private val logger: KLogger = KotlinLogging.logger {}\r\n\r\n- Regular usage:\r\n\r\n       logger.debug(\"message\")\r\n       logger.info(\"message\")\r\n       logger.error(\"message\")\r\n\r\n- Lazy eval; most useful for debug messages that may be disabled in PROD, or\r\n  for message payloads that are expensive to compute:\r\n\r\n       logger.debug { \"lazy evaluated $hello message\" }\r\n\r\n- Exceptions; pass to logger:\r\n\r\n       logger.error(exception) { \"something went wrong\" }\r\n\r\nMore info:\r\nhttps://github.com/MicroUtils/kotlin-logging/wiki#usage","mergeCommitSha":"52b725529ecb31976596474e1a68c10eb4567f4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/431","title":"Use KotlinLogging","createdAt":"2022-02-28T21:56:55Z"}
{"state":"Merged","mergedAt":"2023-01-17T20:06:26Z","number":4310,"mergeCommitSha":"fd4a5700b5a1bd537bed62835b65dbeb289d23e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4310","title":"Asset bucket changes","createdAt":"2023-01-17T19:53:57Z"}
{"state":"Merged","mergedAt":"2023-01-17T20:16:02Z","number":4311,"mergeCommitSha":"26059d47fcd4b986fa7db1de299358aea244ae19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4311","title":"update iam permissions for asset service","createdAt":"2023-01-17T20:15:49Z"}
{"state":"Merged","mergedAt":"2023-01-19T18:03:39Z","number":4312,"body":"Fix transcription segment highlighting in web\r\n\r\n<img width=\"1081\" alt=\"CleanShot 2023-01-17 at 13 04 41@2x\" src=\"https://user-images.githubusercontent.com/1553313/213012083-b0fdf613-7947-4fcd-8137-ffe008e4d633.png\">\r\n","mergeCommitSha":"68aed889119764d90fde575f3ea489d7363b460a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4312","title":"Update highlighted styling for transcription","createdAt":"2023-01-17T21:05:12Z"}
{"state":"Merged","mergedAt":"2023-01-18T23:30:22Z","number":4313,"body":"Hidden behind a default: `EnableSearch`\r\n\r\nSets up search bar component and system hotkey. \r\n\r\nDoesn't actually do any searching yet. ","mergeCommitSha":"cd9b4fd6ea1e5dcc78d45fead7d6fcbe5316eb45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4313","title":"Add search bar to hub","createdAt":"2023-01-17T21:30:42Z"}
{"state":"Merged","mergedAt":"2023-01-18T17:54:13Z","number":4314,"mergeCommitSha":"58015d2a2bf525f367aa2bece48dc746b7b51777","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4314","title":"Trigger topic insight mapping after topic is updated or created","createdAt":"2023-01-17T21:39:14Z"}
{"state":"Merged","mergedAt":"2023-01-17T22:49:32Z","number":4315,"body":"- update video\r\n- Update\r\n","mergeCommitSha":"f31cf866f7da745ba06678e752b3b0f367f53797","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4315","title":"Assset videos should have extensions","createdAt":"2023-01-17T22:13:41Z"}
{"state":"Merged","mergedAt":"2023-01-17T22:49:00Z","number":4316,"body":"If a user has created / connected to an enterprise provider, populate `getLoginProviders` with enterpriseID on subsequent logins.","mergeCommitSha":"0c99c10d9413151bffb6001fc143c646c5098195","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4316","title":"Keep reference to enterprise ID","createdAt":"2023-01-17T22:40:19Z"}
{"state":"Merged","mergedAt":"2023-01-17T23:01:29Z","number":4317,"mergeCommitSha":"bcbf6ead117b14bdf48e8622fdcc9fbdf3e397bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4317","title":"update templates","createdAt":"2023-01-17T23:01:17Z"}
{"state":"Merged","mergedAt":"2023-01-19T21:54:16Z","number":4318,"body":"Focusing down to the result list isn't implemented yet. The full search implementation will use autocomplete tokens, which will necessitate tabbing between views as well. This may be pretty complicated to implement in SwiftUI...\r\n\r\nhttps://user-images.githubusercontent.com/858772/213321095-cb0c5a02-d46b-401c-92a4-fd3a33cc7842.mp4\r\n\r\n","mergeCommitSha":"5894a3b6137bb47f1b699eefc2a43093811ab0ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4318","title":"Display search results","createdAt":"2023-01-17T23:10:37Z"}
{"state":"Merged","mergedAt":"2023-01-17T23:15:13Z","number":4319,"body":"We only want to restrict traffic to the /github endpoint.\n\nTraffic to the /githubEnterprise endpoint cannot be restricted.","mergeCommitSha":"bd9f6b3d08c3d6a6626f7843b61501b410329468","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4319","title":"Fix IP rule for webhooks","createdAt":"2023-01-17T23:11:40Z"}
{"state":"Merged","mergedAt":"2022-02-28T23:49:39Z","number":432,"mergeCommitSha":"cf3b7a052f397d9e9ba1095a834a6964e1edf2fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/432","title":"Add channel token endpoint for video chats. Implementation to follow","createdAt":"2022-02-28T22:44:42Z"}
{"state":"Merged","mergedAt":"2023-01-17T23:40:37Z","number":4320,"mergeCommitSha":"2fea501b5bf12c0571be950e672c6db7eb8f5366","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4320","title":"update buckets","createdAt":"2023-01-17T23:40:07Z"}
{"state":"Merged","mergedAt":"2023-01-18T00:02:01Z","number":4321,"body":"Forgot to pass the prefix.","mergeCommitSha":"36efd231a711720219739f5da08eb155e2f3c599","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4321","title":"Fix HMAC valdiation of GHE webhooks","createdAt":"2023-01-17T23:47:55Z"}
{"state":"Merged","mergedAt":"2023-01-18T00:54:53Z","number":4322,"mergeCommitSha":"348ff075ed30457c4906615134f15360705045de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4322","title":"Take counts out of the topic store","createdAt":"2023-01-18T00:46:43Z"}
{"state":"Merged","mergedAt":"2023-01-18T01:12:54Z","number":4323,"mergeCommitSha":"c75244b21d0137edfd4d40937cebb2fb63efbc1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4323","title":"Fix build","createdAt":"2023-01-18T01:12:47Z"}
{"state":"Merged","mergedAt":"2023-01-18T02:32:56Z","number":4324,"body":"- Fix extensions\r\n- Try again\r\n- Try again\r\n","mergeCommitSha":"f2d8c5bc24c3d373d191c69e62d2664464970472","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4324","title":"FixExtensions","createdAt":"2023-01-18T02:30:38Z"}
{"state":"Merged","mergedAt":"2023-01-18T02:41:52Z","number":4325,"mergeCommitSha":"20efd635b5357cca8a5cb1bff01b4857e2cd69cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4325","title":"Transition to old buckets","createdAt":"2023-01-18T02:41:45Z"}
{"state":"Merged","mergedAt":"2023-01-18T23:49:56Z","number":4326,"body":"This is implemented using a server API, so that we get the same stemming and search capabilities as we have in the service.\r\n\r\nThe way this is implemented is that I create four different streams:\r\n1) File-scoped, no filter\r\n2) View-scoped, no filter\r\n3) File-scoped, with filter\r\n4) View-scoped, with filter\r\n\r\nand we switch between the streams as the filter state changes.  This seems to work OK and let us compose the streams.\r\n\r\nThe layout here is a little bit funny, the item alignment is now more-obviously off because the new show/hide button makes the right-alignment more obvious.  I'll look into this, but since the scrollbar can throw the alignment off there's not a ton we can do here.\r\n\r\n<img width=\"401\" alt=\"Screen Shot 2023-01-17 at 11 02 35 PM\" src=\"https://user-images.githubusercontent.com/2133518/213105590-cb1303f0-9f44-4142-9a1f-f25803540b40.png\">\r\n","mergeCommitSha":"facb2d1abdb6deaf570ec69d6a7a2cd4ef95e151","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4326","title":"Add filter input to VSCode Explorer Insights panel","createdAt":"2023-01-18T07:03:37Z"}
{"state":"Merged","mergedAt":"2023-01-18T17:57:51Z","number":4327,"body":"- Removed S3 head object calls. We don't need them anymore. I'll add filters for events later on.\r\n- Modified video-transcoding config \r\n\r\nThe issue for not having jobs processed was caused by the manually created pipeline pointing at the wrong bucket (blob assets). I had to edit it through AWS console. We are already paying the price for one manual config. \r\n\r\nAlso there was a mistake in the IAM policy for prod where it was referencing Dev account ID. fixed that and I can now see my test assets being processed. ","mergeCommitSha":"de6e278afbea1288c66451d408fc3e28f2e0c9fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4327","title":"fix the suffix and remove s3.head_object call","createdAt":"2023-01-18T08:16:36Z"}
{"state":"Merged","mergedAt":"2023-01-18T21:15:57Z","number":4328,"body":"https://aws.github.io/aws-eks-best-practices/security/docs/runtime/\r\n\r\n\r\nhttps://linear.app/unblocked/issue/UNB-828/configure-security-context-on-application-pods","mergeCommitSha":"297441aae0d5e48ba832f20678ecbdecdba37a83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4328","title":"add default runtime seccompProfile","createdAt":"2023-01-18T11:10:00Z"}
{"state":"Merged","mergedAt":"2023-01-18T22:16:54Z","number":4329,"body":"This could be a breaking change. I will watch things closely and revert if needed. ","mergeCommitSha":"020d5ef60faa5dceab610a768601d4024d5aed1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/4329","title":"Pod security non root user","createdAt":"2023-01-18T11:27:36Z"}
{"state":"Merged","mergedAt":"2022-03-01T20:03:49Z","number":433,"body":"This has miles to go, so don't dig _too_ deep into the algorithm.\r\n\r\nNext step is testing, which will tease out issues.","mergeCommitSha":"fe136e292b0d1f5c12279601a2ce455e8465c433","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/433","title":"Initial SourceMark Calculator","createdAt":"2022-02-28T22:51:43Z"}