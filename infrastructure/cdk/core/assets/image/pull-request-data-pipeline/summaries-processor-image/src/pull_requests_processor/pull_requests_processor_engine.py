import asyncio
import concurrent
import logging
from abc import abstractmethod

from dataclasses import dataclass

from tqdm import tqdm
from typing import List, Callable, Optional, Awaitable

from pull_requests_processor.pull_request_types import PullRequest, PullRequestSummaryResult
from pull_requests_processor.pull_request_constants import DEFAULT_BATCH_SIZE


@dataclass(order=True)
class WorkerRequest:
    pull_request: PullRequest
    repo_dir: str


class PullRequestsProcessorEngine:
    @abstractmethod
    def get_pull_request_summary_results(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        generator: Callable[[[str, PullRequest]], Optional[PullRequestSummaryResult]],
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> List[PullRequestSummaryResult]:
        pass

    @abstractmethod
    async def async_get_pull_request_summary_results(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        generator: Callable[[[str, PullRequest]], Awaitable[Optional[PullRequestSummaryResult]]],
        batch_size: int = 5,
    ) -> List[PullRequestSummaryResult]:
        pass


class ThreadedPullRequestsProcessorEngine(PullRequestsProcessorEngine):
    async def __worker(
        self,
        pbar: tqdm,
        queue: asyncio.Queue,
        generator: Callable[[str, PullRequest], Awaitable[Optional[PullRequestSummaryResult]]],
        results: List[PullRequestSummaryResult],
    ):
        while True:
            worker_request: WorkerRequest = await queue.get()
            result = await generator(worker_request.repo_dir, worker_request.pull_request)
            if result:
                results.append(result)
            # Mark the item as processed, allowing queue.join() to keep
            # track of remaining work and know when everything is done.
            queue.task_done()
            pbar.update(1)

    async def async_get_pull_request_summary_results(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        generator: Callable[[[str, PullRequest]], Awaitable[Optional[PullRequestSummaryResult]]],
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> List[PullRequestSummaryResult]:
        with tqdm(total=len(pull_requests), desc="Summarizing pull requests...") as pbar:
            pull_request_summary_results: List[PullRequestSummaryResult] = []
            queue = asyncio.Queue(maxsize=batch_size)
            tasks: List[asyncio.Task] = []
            for _ in range(batch_size):
                task = asyncio.create_task(
                    self.__worker(
                        queue=queue,
                        pbar=pbar,
                        generator=generator,
                        results=pull_request_summary_results,
                    )
                )
                tasks.append(task)

            for pull_request in pull_requests:
                worker_request = WorkerRequest(pull_request=pull_request, repo_dir=repo_dir)
                await queue.put(item=worker_request)

            await queue.join()
            for task in tasks:
                task.cancel()

            await asyncio.gather(*tasks, return_exceptions=True)
            return pull_request_summary_results

    def get_pull_request_summary_results(
        self,
        repo_dir: str,
        pull_requests: List[PullRequest],
        generator: Callable[[[str, PullRequest]], Optional[PullRequestSummaryResult]],
        batch_size: int = DEFAULT_BATCH_SIZE,
    ) -> List[PullRequestSummaryResult]:
        pull_request_summary_results: List[PullRequestSummaryResult] = []
        with tqdm(total=len(pull_requests), desc="Summarizing pull requests...") as pbar:
            with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
                # Start the load operations and mark each future with its result
                future_to_pull_request = {
                    executor.submit(generator, repo_dir, pull_request): pull_request for pull_request in pull_requests
                }

                for future in concurrent.futures.as_completed(future_to_pull_request.keys()):
                    pull_request = future_to_pull_request[future]
                    try:
                        pull_request_summary_result = future.result()
                    except Exception as exc:
                        logging.error("%s generated an exception: %s" % (pull_request, exc))
                    else:
                        if pull_request_summary_result:
                            pull_request_summary_results.append(pull_request_summary_result)
                    finally:
                        pbar.update(1)

        return pull_request_summary_results
