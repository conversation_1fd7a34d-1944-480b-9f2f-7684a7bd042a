import logging
import re
import random

from typing import List
from unidiff import PatchSet

from pull_requests_processor.git_diff_types import GitDiffPartitions, GitDiffPartition
from pull_requests_processor.token_utils import num_tokens_from_string


class GitDiffPartitioner:
    _max_tokens: int
    _max_partitions: int

    def __init__(self, max_tokens: int, max_partitions: int = 25):
        self._max_tokens = max_tokens
        self._max_partitions = max_partitions

    def split_git_diff(self, git_diff: str) -> GitDiffPartitions:
        """
        Split the diff into segments.
        """

        # If the file is small enough, we don't need to split it.
        # This is a performance optimization.
        if num_tokens_from_string(git_diff) <= self._max_tokens:
            return self.__no_split_git_diff(git_diff=git_diff)

        try:
            return self.__split_git_diff(git_diff=git_diff)
        except Exception as e:
            logging.exception(f"Split failed '{git_diff}'")
            raise

    def __no_split_git_diff(self, git_diff: str) -> GitDiffPartitions:
        """
        Returns the entire file as a single partition.
        """
        return GitDiffPartitions(
            partitions=[
                GitDiffPartition(
                    git_diff=git_diff,
                )
            ],
            summary=self.__get_summary(diff=git_diff),
        )

    def __split_git_diff(self, git_diff: str) -> GitDiffPartitions:
        patch_set = PatchSet(git_diff)
        partitions: List[GitDiffPartition] = []
        for patched_file in patch_set:
            patched_file_git_diff = str(patched_file)
            if num_tokens_from_string(patched_file_git_diff) < self._max_tokens:
                partitions.append(GitDiffPartition(git_diff=patched_file_git_diff))
            else:
                for hunk in patched_file:
                    hunk_git_diff = f"{str(patched_file.patch_info)}\n\n{str(hunk)})"
                    partitions.append((GitDiffPartition(git_diff=hunk_git_diff)))

        for partition in partitions:
            partition.git_diff = self.__compresses_spaces(partition.git_diff)
        merged_partitions = self.__combine_small_neighbours(entries=partitions, max_tokens=self._max_tokens)
        culled_partitions = self.__cull_partitions(entries=merged_partitions, max_partitions=self._max_partitions)

        return GitDiffPartitions(partitions=culled_partitions, summary=self.__get_summary(diff=git_diff))

    @staticmethod
    def __get_summary(diff: str) -> str:
        splits = diff.split("diff --git")
        if len(splits) > 0:
            return splits[0]
        else:
            return ""

    @staticmethod
    def __cull_partitions(entries: List[GitDiffPartition], max_partitions: int) -> List[GitDiffPartition]:
        """
        Randomly samples partitions if it is past a max size
        """
        if len(entries) <= max_partitions:
            return entries

        # Randomly sample up to max_samples elements from the input list
        sampled_entries = random.sample(entries, max_partitions)
        return sampled_entries

    @staticmethod
    def __combine_small_neighbours(entries: List[GitDiffPartition], max_tokens: int) -> List[GitDiffPartition]:
        """
        Merges consecutive neighbours if their combined length is less than max_chars.
        """
        merged = []
        if not entries:
            return merged

        current = entries.pop(0)

        while entries:
            entry = entries.pop(0)
            if num_tokens_from_string(current.git_diff + entry.git_diff) <= max_tokens:
                current.git_diff += f"\n{entry.git_diff}"
            else:
                merged.append(current)
                current = entry

        if current:
            merged.append(current)

        return merged

    @staticmethod
    def __compresses_spaces(text: str) -> str:
        """
        Removes leading/trailing spaces from each line and
        compresses consecutive spaces/tabs within lines into a single space.
        """
        return "\n".join([re.sub(r"[ \t]+", " ", line) for line in text.split("\n")])
