{"state":"Merged","mergedAt":"2023-10-27T14:59:14Z","number":8950,"body":"Will be used for all families of summaries, but just RepoSummaries initally.","mergeCommitSha":"7329dc1b51e03479132f4667eb002aef1bb0a414","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8950","title":"Introduce new Summary document type","createdAt":"2023-10-27T00:24:38Z"}
{"state":"Merged","mergedAt":"2023-10-27T16:28:00Z","number":8951,"mergeCommitSha":"45d951ec1d813784bda605864727430c16e0b5b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8951","title":"Add summaries prompt template","createdAt":"2023-10-27T05:37:40Z"}
{"state":"Merged","mergedAt":"2023-10-27T22:31:18Z","number":8952,"mergeCommitSha":"dddcdf80a5ac702815df31322a3275a34d407afd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8952","title":"Generate repo summaries in PROD","createdAt":"2023-10-27T06:03:03Z"}
{"state":"Merged","mergedAt":"2023-10-28T05:11:11Z","number":8953,"mergeCommitSha":"aa8ebe13fabb5552c5f4ab5c6e38069f5d860130","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8953","title":"Cleanup rollout of repo summaries","createdAt":"2023-10-27T06:03:08Z"}
{"state":"Merged","mergedAt":"2023-10-27T17:01:35Z","number":8954,"mergeCommitSha":"991c617be009ab84644dd1036a0bc40e99a86c01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8954","title":"Repo summaries are persisted as Summary Document Types","createdAt":"2023-10-27T06:51:16Z"}
{"state":"Merged","mergedAt":"2023-10-27T17:23:32Z","number":8955,"body":"- Increased Webhook CPU. Webhooks uses guaranteed CPU which means it's not allowed to burst but at the same time it is protected from other services bursting.\r\n- Notification service has been very busy using most of the CPU given to it constantly\r\n- Linear service was under provisioned. ","mergeCommitSha":"d2a377e87c2c15ab0f5c18c0eba3603a41a17389","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8955","title":"Add more CPU to services that are under stress","createdAt":"2023-10-27T16:42:41Z"}
{"state":"Merged","mergedAt":"2023-10-27T17:44:01Z","number":8956,"mergeCommitSha":"ca5f87f57417731d203ed92aff1171fe85f6120d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8956","title":"[SKIP TESTS] Fix upserts","createdAt":"2023-10-27T17:14:45Z"}
{"state":"Merged","mergedAt":"2023-10-27T20:13:06Z","number":8957,"body":"Adding values.yaml file for all services in east-2 prod cluster. \r\n\r\nI have scaled all the services to the minimum scale of 1. \r\n\r\nAdminweb and monkey service are set to zero pods. ","mergeCommitSha":"0cf0040b9afde5e578f6e8523fe36dd8b0d8d886","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8957","title":"adding helm yaml file for services in east","createdAt":"2023-10-27T19:40:22Z"}
{"state":"Merged","mergedAt":"2023-10-27T20:36:31Z","number":8958,"mergeCommitSha":"1203bb3f731d8ad993ce757237330a209823c346","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8958","title":"Add hover delay to team selector","createdAt":"2023-10-27T20:10:04Z"}
{"state":"Closed","mergedAt":null,"number":8959,"body":"We also need to delete from dynamo","mergeCommitSha":"3959b723caa85bf8e64c48cc6c6b20020158e476","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8959","title":"Delete archived notion pages from embedding database","createdAt":"2023-10-27T20:13:17Z"}
{"state":"Merged","mergedAt":"2022-04-14T04:34:01Z","number":896,"body":"- https://www.notion.so/nextchaptersoftware/SourceMark-Open-Issues-3cdea5e5cd37494c94b2899d259408bf#6b56e804000842f19c91608e53157cc8","mergeCommitSha":"4a84294c56dfd50e968b83ccca9dca77d70e8947","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/896","title":"Fix point cache for uncommitted changes","createdAt":"2022-04-14T04:17:40Z"}
{"state":"Merged","mergedAt":"2023-10-30T22:37:36Z","number":8960,"mergeCommitSha":"4f2f263ec65a8c0148a2c50da5ff97f463951c09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8960","title":"Remove potentially sensitive message logging","createdAt":"2023-10-27T20:36:56Z"}
{"state":"Merged","mergedAt":"2023-10-27T21:53:21Z","number":8961,"mergeCommitSha":"f084aab79226d54a7c76f5ad56bd03b4984954ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8961","title":"Fix repo summaries prompt caused by nullable","createdAt":"2023-10-27T20:37:10Z"}
{"state":"Merged","mergedAt":"2023-10-27T21:13:08Z","number":8962,"mergeCommitSha":"f031736eba79ad95dbc938109bc251a48259f1ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8962","title":"enable deploys to us-east-2","createdAt":"2023-10-27T20:40:29Z"}
{"state":"Merged","mergedAt":"2023-10-27T21:35:58Z","number":8964,"body":"Reverts NextChapterSoftware/unblocked#8962","mergeCommitSha":"f46bf585b44869d573690c245737daffd283a3a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8964","title":"Revert \"enable deploys to us-east-2\"","createdAt":"2023-10-27T21:35:02Z"}
{"state":"Merged","mergedAt":"2023-10-27T22:27:05Z","number":8965,"mergeCommitSha":"ddc98e64d0f1b471dfed4c310b1bcff2f111e1a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8965","title":"Declare logContentCache table and make DynamoDB putItem support time-to-live","createdAt":"2023-10-27T21:50:50Z"}
{"state":"Merged","mergedAt":"2023-10-27T23:52:08Z","number":8966,"body":"Also delete archived notion pages to fix https://chapter2global.slack.com/archives/C062J3FLK8B/p1698413512473829","mergeCommitSha":"e44aad869aa254964b565c1ea30fa1e7de9a7b7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8966","title":"Add ability to delete embeddings","createdAt":"2023-10-27T22:48:43Z"}
{"state":"Merged","mergedAt":"2023-10-28T00:21:33Z","number":8967,"mergeCommitSha":"a0c1e49a7037a390ee1b254452ffaa311088f8a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8967","title":"[SKIP TESTS] Add message attributes extraction","createdAt":"2023-10-27T23:20:45Z"}
{"state":"Merged","mergedAt":"2023-10-30T21:25:30Z","number":8968,"body":"- Add 'References' header\r\n- In IDEs, revert to previous vertically-oriented link list\r\n\r\n![CleanShot 2023-10-30 at 14 09 27@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/38cdac4e-0f77-4cbd-9339-935378d6ae2a)\r\n![CleanShot 2023-10-30 at 14 09 59@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/8ed40398-6e13-4e85-b3ae-f2582f1b3515)\r\n![CleanShot 2023-10-30 at 14 11 57@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/e7fcc2c9-028f-46f4-88e4-672dde09f51e)\r\n","mergeCommitSha":"c9cf68280deddd07ef8a4db7194bf51cb7ddc8b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8968","title":"Tweak reference list UIs","createdAt":"2023-10-27T23:32:48Z"}
{"state":"Merged","mergedAt":"2023-10-30T19:15:41Z","number":8969,"body":"1. The `StepsLarge` and `StepsMedium` section UIs had no minimum width set for the video and content panels.  Our intention is that video has 60% width, text content has 40%, but for some reason Chrome doesn't fully enforce this (a bug, AFAICT).  Setting a min-width on both sides seems to fix this.\r\n2. The `AnimatingHeightSizer` was setting an explicit height on its first render, which is wrong -- it should size to `auto` or `0` based on whether the content is displayed or not.  Setting an explicit height means if the content width changes, the height won't respond.","mergeCommitSha":"cadea8d8cc1d0701c0eaeb14761f8dfa9699bb65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8969","title":"Fix a couple landing page bugs in chrome","createdAt":"2023-10-27T23:53:41Z"}
{"state":"Merged","mergedAt":"2022-04-14T17:27:47Z","number":897,"body":"Was removed in refactor","mergeCommitSha":"a108f135c325d48ac4fc57ebf24125f22ad5bc5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/897","title":"Fix command registration bug","createdAt":"2022-04-14T17:21:04Z"}
{"state":"Merged","mergedAt":"2023-10-28T00:32:58Z","number":8970,"mergeCommitSha":"8259b69be253a86774bb8b42625a79d4d9404157","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8970","title":"Update ktlint","createdAt":"2023-10-28T00:04:16Z"}
{"state":"Merged","mergedAt":"2023-10-28T00:43:26Z","number":8971,"body":"This is to dissuade the LLM from hallucinating urls that are not related to the repo.","mergeCommitSha":"ed1cc5aa9dda6e01c6e800ea1b31ef51e98206b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8971","title":"Repo summary use repo urls","createdAt":"2023-10-28T00:19:56Z"}
{"state":"Merged","mergedAt":"2023-10-28T00:54:23Z","number":8972,"mergeCommitSha":"9e0e29aaaf4c38922169389964617576a0005956","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8972","title":"[SKIP TESTS] Increase size","createdAt":"2023-10-28T00:54:17Z"}
{"state":"Merged","mergedAt":"2023-10-28T02:21:51Z","number":8973,"mergeCommitSha":"cbadc73cd52f1a98f8153b8125fc82178935e041","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8973","title":"Specify explicit types","createdAt":"2023-10-28T02:01:38Z"}
{"state":"Merged","mergedAt":"2023-10-28T03:35:54Z","number":8974,"mergeCommitSha":"6bd86468290d6559ba4d1c6d3a1c95a6af4bac48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8974","title":"Fix lock for NotionIngestionService","createdAt":"2023-10-28T03:20:59Z"}
{"state":"Merged","mergedAt":"2023-10-28T03:49:57Z","number":8975,"mergeCommitSha":"5e3bda1845c4f5dbc1a3a30492fa534881d855b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8975","title":"Force regenerate repo summaries","createdAt":"2023-10-28T03:35:20Z"}
{"state":"Merged","mergedAt":"2023-10-29T05:44:04Z","number":8976,"body":"If we HEAD /pages/{pageId} it will return the page...which we don't need since we just need a yes/no whether the page still exists.","mergeCommitSha":"fe43171f5cd1b02578c81cf3d2f82d57178de238","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8976","title":"Add ability to HEAD page children to check whether a notion page has been deleted","createdAt":"2023-10-29T00:31:53Z"}
{"state":"Merged","mergedAt":"2023-10-30T02:35:41Z","number":8977,"body":"So that we can clean-up deleted documents.","mergeCommitSha":"ab559205fa3521c3c4bb6198944a6c4dbdab4760","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8977","title":"Read-repair: Emit DocumentationValidationEvent for documents retrieved from pinecone","createdAt":"2023-10-29T17:57:11Z"}
{"state":"Merged","mergedAt":"2023-10-30T00:51:45Z","number":8978,"body":"On disk encryption for logContentCache.","mergeCommitSha":"47cc5369d8d1fdd8ed8ab219e3fa22efcb9f70d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8978","title":"Encrypt logContentCache DynamoDB table","createdAt":"2023-10-30T00:51:00Z"}
{"state":"Merged","mergedAt":"2023-10-30T05:44:28Z","number":8979,"mergeCommitSha":"af3d723b0cc4e517d15279d4b50e7666ac55fe4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8979","title":"Remove Notion page emvedding if it no longer exists","createdAt":"2023-10-30T03:21:49Z"}
{"state":"Merged","mergedAt":"2022-04-18T18:38:33Z","number":898,"body":"For VSCode we have been copying the entire `src/assets` folder into the `dist` folder, and referencing images manually by piecing together file paths.  This only worked in local builds, VSIX packages were missing the asset folder, and so images would be missing at runtime.\r\n\r\nThis PR changes how we reference images.  We now use `import MyImage from 'MyImage.svg'` to reference images in code, which causes the image to be referenced by webpack. This means webpack puts in the right place and returns the correct (relative) URL for it.  It also means the build will fail at compile time if we rename/move a file accidentally.\r\n\r\n(this is what we're currently doing in the dashboard and web extension)","mergeCommitSha":"706a7f58520f5919422edd92163f79457f750238","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/898","title":"Reference images in VSCode code directly","createdAt":"2022-04-14T17:49:09Z"}
{"state":"Merged","mergedAt":"2023-10-30T03:36:24Z","number":8980,"mergeCommitSha":"d62e3631dd7e92e1aa98d6d786e66fb601e538c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8980","title":"Debug message dequeue","createdAt":"2023-10-30T03:36:00Z"}
{"state":"Merged","mergedAt":"2023-10-30T16:15:39Z","number":8981,"body":"Intended usage:\r\n\r\n```kotlin\r\nLOGGER.debug(\r\n   \"nonSensitiveField\" to \"nonSensitiveValue\",\r\n   sensitive = mapOf(\r\n       \"sensitiveField\" to \"sensitiveValue\",\r\n   ),\r\n)\r\n```\r\n\r\nAppearance in logz.io:\r\n\r\n`sensitiveFieldsId: <UUID>`\r\n\r\nThen we expose a lookup page in Admin Console (coming soon), or a Chrome extension to do inline replacement (intern project).\r\n\r\nThe duplication is less than ideal but we can't introduce a `lib-aws` dependency to the main logging library. \r\n\r\nAnother possibility - add the sensitive field parameters to the `lib-log` functions, and inject a sensitive field store via object initialization at startup. I kind of prefer the explicit nature of calling a function with required fields to get a different behaviour.","mergeCommitSha":"517c95a9412de5d3f2a1b2bf93207f91aee165fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8981","title":"Adds sensitive field logging using DynamoDB","createdAt":"2023-10-30T03:50:06Z"}
{"state":"Merged","mergedAt":"2023-10-30T04:04:13Z","number":8982,"mergeCommitSha":"39d3fe12016c8c5129fe2db8d224467b82d6f086","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8982","title":"Check that values are not null","createdAt":"2023-10-30T03:50:07Z"}
{"state":"Merged","mergedAt":"2023-10-30T05:10:21Z","number":8983,"mergeCommitSha":"c6de39636b6f84125287ddd3f46a35381405fb83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8983","title":"Pull out source document ID during pinecone search","createdAt":"2023-10-30T04:44:45Z"}
{"state":"Merged","mergedAt":"2023-10-30T06:04:56Z","number":8984,"body":"Calling message.body() twice will lead to non-idempotent seek in underlying response byte array.\r\n","mergeCommitSha":"cd14a48042a0c0fa3598ec316b08716a930f0125","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8984","title":"[SKIP TESTS] Fix ActiveMQ byte messge pointers","createdAt":"2023-10-30T06:03:40Z"}
{"state":"Merged","mergedAt":"2023-10-30T17:01:53Z","number":8985,"mergeCommitSha":"867465c778d888939be0c2086fe3a95c0b2f89ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8985","title":"Setup ci for east 2 prod","createdAt":"2023-10-30T16:59:16Z"}
{"state":"Merged","mergedAt":"2023-10-30T17:51:34Z","number":8986,"mergeCommitSha":"85deca6dbbc0eeedaab28d6066aabcbb05129631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8986","title":"Spread out API calls for read-repair","createdAt":"2023-10-30T17:11:45Z"}
{"state":"Merged","mergedAt":"2023-10-30T17:18:13Z","number":8987,"body":"Reverts NextChapterSoftware/unblocked#8985","mergeCommitSha":"14968af2089059f88de60a3e8c96662cab478a93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8987","title":"Revert \"Setup ci for east 2 prod\"","createdAt":"2023-10-30T17:18:05Z"}
{"state":"Merged","mergedAt":"2023-10-30T17:36:32Z","number":8988,"mergeCommitSha":"2caed63bb59ffa4fbb820896478ae64a29cd7ce2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8988","title":"Setup ci for east 2 prod attempt 2","createdAt":"2023-10-30T17:33:43Z"}
{"state":"Merged","mergedAt":"2023-10-30T18:02:48Z","number":8989,"mergeCommitSha":"2ab3888e73ef045b515cada1fc0b54dd06ffd887","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8989","title":"Do not log secrets!!!","createdAt":"2023-10-30T18:02:42Z"}
{"state":"Merged","mergedAt":"2022-04-15T01:06:31Z","number":899,"body":"Currently, schema update fails not only when the new schema is invalid,\r\nbut also when the application cannot get a DB busy-lock.\r\n\r\nWe rollover all instances for all services around the same time,\r\nso it's pretty likely that some instances will fail to acquire the lock\r\non first attempt, which will terminate the instance.\r\n\r\nThis change reduces the churn in instance termination by retrying a few\r\ntimes before eventually giving up due to invalid schema.","mergeCommitSha":"20f5fc5c469f84c539576ab98cd96709454ad7d7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/899","title":"Schema manager retries actualization","createdAt":"2022-04-14T18:14:04Z"}
{"state":"Merged","mergedAt":"2023-10-30T18:26:08Z","number":8990,"mergeCommitSha":"032b765002e5cb2d79a8a9cf1f741e852a706d4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8990","title":"fix the context value for secret deployments","createdAt":"2023-10-30T18:25:19Z"}
{"state":"Merged","mergedAt":"2023-10-30T21:21:50Z","number":8991,"mergeCommitSha":"a9963ae43caf25d976b0a7555f423a44e929b3d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8991","title":"Bail out and return null on failure to parse response json","createdAt":"2023-10-30T18:37:50Z"}
{"state":"Merged","mergedAt":"2023-10-30T19:19:01Z","number":8992,"mergeCommitSha":"b46492902ef5e2f33a72ee7cdc33a66838f9479f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8992","title":"Pinecone query logging to debug Q&A chained responses","createdAt":"2023-10-30T18:51:58Z"}
{"state":"Closed","mergedAt":null,"number":8993,"mergeCommitSha":"327efa1cf87f710d55e1a31e6fe69005801a2498","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8993","title":"change set context action version","createdAt":"2023-10-30T19:45:27Z"}
{"state":"Merged","mergedAt":"2023-10-30T19:59:55Z","number":8994,"mergeCommitSha":"be5fc4f399f38fd194f0d84182b78351623c20f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8994","title":"revert back to using one kubeconfig per env-cluster","createdAt":"2023-10-30T19:59:47Z"}
{"state":"Merged","mergedAt":"2023-10-30T20:57:44Z","number":8995,"mergeCommitSha":"9b42b22b9a1abca7e69720278065848c86e5bca0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8995","title":"Extend delay range for document validation","createdAt":"2023-10-30T20:42:27Z"}
{"state":"Merged","mergedAt":"2023-10-30T21:26:24Z","number":8996,"mergeCommitSha":"cb614b40cf41e68caa57822fb8a5be2614b02aa3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8996","title":"Re-rank step should use the query we used during document retrieval","createdAt":"2023-10-30T21:04:21Z"}
{"state":"Merged","mergedAt":"2023-10-30T22:13:32Z","number":8997,"mergeCommitSha":"46017a9a979f49d0eff3948cfb12f3290cf9254b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8997","title":"disable east deploys while I figure out the DB connection problem","createdAt":"2023-10-30T22:13:16Z"}
{"state":"Merged","mergedAt":"2023-10-30T23:46:47Z","number":8998,"mergeCommitSha":"a7573e1c08f42c72ab64305d0d3e5fd27bfef9cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8998","title":"Render sensitive log fields in admin console","createdAt":"2023-10-30T22:35:25Z"}
{"state":"Merged","mergedAt":"2023-10-30T23:21:54Z","number":8999,"body":"[SKIP TESTS]","mergeCommitSha":"12a436007c18f9596cd5d5582535d08d9fa3356a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/8999","title":"[SKIP TESTS] Add queue management from admin console","createdAt":"2023-10-30T22:55:47Z"}
{"state":"Closed","mergedAt":null,"number":9,"mergeCommitSha":"b25c9419fa76742fef2d8681a1c55b073e11c76a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9","title":"Test branch7","createdAt":"2021-12-14T18:47:15Z"}
{"state":"Merged","mergedAt":"2022-01-20T22:21:33Z","number":90,"body":"![image](https://user-images.githubusercontent.com/13431372/150405362-16174334-aa0b-420e-97b1-528f5d03344c.png)\r\n![image](https://user-images.githubusercontent.com/13431372/150405384-cd796ce1-ab00-4b4f-91c4-82093b9fecdd.png)\r\n\r\nAdd base Button component and stories\r\nStorybook is running off of a test theme that's in `themes/test-theme.scss`\r\n\r\n### Issues\r\n* Not sure why the `@use` import isn't working, it compiles fine using the relative imports \r\n* I didn't install stylelint -- was wondering if this is better off as a shared config for both web and vscode ","mergeCommitSha":"03d8e4b3367de492dc5562613e4b7bf53cb22c4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/90","title":"Add Button component to vscode","createdAt":"2022-01-20T19:08:35Z"}
{"state":"Merged","mergedAt":"2022-04-14T18:55:10Z","number":900,"body":"Trying to address https://www.notion.so/nextchaptersoftware/SourceMark-Open-Issues-3cdea5e5cd37494c94b2899d259408bf#4aa96ee23496427c9d5c44f3e8ef2aba","mergeCommitSha":"09af980f1862ca5f03267c45c897bcf37c74e09a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/900","title":"Add logging","createdAt":"2022-04-14T18:39:43Z"}
{"state":"Merged","mergedAt":"2023-10-30T23:30:22Z","number":9000,"mergeCommitSha":"97550452147aacdef1d1e5eb21d0bbd0e9dfeaaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9000","title":"Protect against failed sensitive field storage","createdAt":"2023-10-30T23:27:02Z"}
{"state":"Merged","mergedAt":"2023-10-31T03:41:42Z","number":9001,"body":"The coroutine context for failsafe retry was wrong.","mergeCommitSha":"3b559b3d510dcd7a2b67454dcfb9f6100238cccf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9001","title":"Fix openai coroutine contexts","createdAt":"2023-10-31T00:50:24Z"}
{"state":"Merged","mergedAt":"2023-10-31T17:52:47Z","number":9002,"body":"Should we call it instead:\r\n\r\n`GoogleDocs`?\r\n`GoogleDrive`? (we'll have access to and can ingest PDFs, text files, docx, in addition to Google Docs)\r\n\r\nAnswer: No","mergeCommitSha":"112cb2fdc44b2940250ad82f4023877093e45590","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9002","title":"Add Provider.Google","createdAt":"2023-10-31T03:31:34Z"}
{"state":"Merged","mergedAt":"2023-10-31T04:57:36Z","number":9003,"mergeCommitSha":"5121db001fab513ca22a26ff4d5dbbe240e87880","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9003","title":"More cleanup of coroutines","createdAt":"2023-10-31T04:42:35Z"}
{"state":"Merged","mergedAt":"2023-10-31T06:51:21Z","number":9004,"mergeCommitSha":"73189bd3678bffd3d1d0b5172ee378f533bd7cd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9004","title":"Add baggage context","createdAt":"2023-10-31T06:34:28Z"}
{"state":"Merged","mergedAt":"2023-10-31T08:42:29Z","number":9005,"mergeCommitSha":"aaed587eb7695d40e145b5052883091fae881072","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9005","title":"allow east to west access and fix deployment order","createdAt":"2023-10-31T08:42:22Z"}
{"state":"Merged","mergedAt":"2023-10-31T09:33:28Z","number":9006,"mergeCommitSha":"d9dd260fb792a7bbbf1ca045b88db521449c96ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9006","title":"Re-enable east deploys","createdAt":"2023-10-31T09:28:00Z"}
{"state":"Merged","mergedAt":"2023-10-31T10:16:36Z","number":9007,"mergeCommitSha":"62a93800767747e237e1692bcb1334e1270fde68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9007","title":"disable east","createdAt":"2023-10-31T10:16:28Z"}
{"state":"Merged","mergedAt":"2023-10-31T16:13:37Z","number":9008,"mergeCommitSha":"1fbbcf03028b05f3a2f6a106683c46b3d0705eb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9008","title":"Add logContentCache access to prod us-west","createdAt":"2023-10-31T16:09:27Z"}
{"state":"Merged","mergedAt":"2023-10-31T16:37:13Z","number":9009,"mergeCommitSha":"d5fcc9d947a074ebe79c4a000b5708dfbb89bfce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9009","title":"Update alb endpoints in helm config for east services","createdAt":"2023-10-31T16:21:54Z"}
{"state":"Merged","mergedAt":"2022-04-15T01:06:00Z","number":901,"body":"Creates a source point for a commit even if the file hasn't changed from the previous commit returned by `git rev-list`\r\n\r\nFixes https://www.notion.so/nextchaptersoftware/SourceMark-Open-Issues-3cdea5e5cd37494c94b2899d259408bf#4aa96ee23496427c9d5c44f3e8ef2aba","mergeCommitSha":"2d0c925f87815b2c893dfa214cfe954fa89e5405","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/901","title":"Upstream sourcepoint even if the fileHash hasn't changed","createdAt":"2022-04-14T20:08:47Z"}
{"state":"Merged","mergedAt":"2023-10-31T17:54:24Z","number":9010,"body":"Image pull errors have been an issue with deployments to east due to a lag in replication. This PR sets the image pull policy to \"always\"\r\n\r\n\r\nFrom docs:\r\n```\r\nThe caching semantics of the underlying image provider make even imagePullPolicy: Always efficient, as long as the registry is reliably accessible. Your container runtime can notice that the image layers already exist on the node so that they don't need to be downloaded again.\r\n```","mergeCommitSha":"cc6bd426d934f7fbc01c387ad25feb313033379f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9010","title":"Set image pull policy to always","createdAt":"2023-10-31T17:20:04Z"}
{"state":"Closed","mergedAt":null,"number":9011,"body":"# Problem\r\nUsers are confused by our request to provide feedback back, taking the instruction literally as: if you provide feedback the answer will improve right now. Examples:\r\n\r\n<img width=\"786\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/5a4bc2ba-adb1-4fa8-9cc5-86916202c41b\">\r\n\r\n<img width=\"779\" alt=\"Screenshot 2023-10-31 at 10 55 33\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/f60266cd-d4f1-47cf-bc25-ec45a5dd5745\">\r\n\r\n# Proposal\r\n\r\nMake the test clearer:\r\n> How did we do? Provide feedback to help us improve:\r\n\r\n<img width=\"761\" alt=\"Screenshot 2023-10-31 at 10 42 01\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/f91004c3-1192-423d-a01c-a57d662a63c1\">\r\n\r\n\r\nAfter providing feedback, we thank the user:\r\n> Thanks for providing feedback!\r\n\r\n<img width=\"779\" alt=\"Screenshot 2023-10-31 at 10 42 55\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/24a7e6e9-e7cb-4de3-ba01-42256b0a1680\">\r\n","mergeCommitSha":"c988b688d8aadb06ff4bb4cbac722ae98d480374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9011","title":"Improve feedback message","createdAt":"2023-10-31T17:39:25Z"}
{"state":"Merged","mergedAt":"2023-10-31T20:27:19Z","number":9012,"body":"In QA threads, always @mention the bot, even when there are other participants in the thread, and even when the current user is not a participant in the thread.","mergeCommitSha":"7a29f37729b2cb95d052b9a1b55b89878d0a689c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9012","title":"Always mention bot","createdAt":"2023-10-31T17:40:24Z"}
{"state":"Merged","mergedAt":"2023-10-31T17:59:43Z","number":9013,"body":"The old PR https://github.com/NextChapterSoftware/unblocked/pull/9010","mergeCommitSha":"13e3d1508ea3584c8208a61b7142dcea288d6da6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9013","title":"forgot to commit this in my last PR","createdAt":"2023-10-31T17:55:51Z"}
{"state":"Merged","mergedAt":"2023-10-31T20:17:27Z","number":9014,"mergeCommitSha":"5758670ca3649d5eda1419f58c5df9ec07ee86c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9014","title":"Set all services to zero scale in east and disable autoscaler","createdAt":"2023-10-31T18:43:40Z"}
{"state":"Merged","mergedAt":"2023-10-31T19:44:49Z","number":9015,"mergeCommitSha":"b777601f3e94d853ebba70bfe689e1875156f40e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9015","title":"Fix slack team preferences upsert","createdAt":"2023-10-31T19:12:03Z"}
{"state":"Merged","mergedAt":"2023-10-31T20:33:36Z","number":9016,"mergeCommitSha":"92f46668ecfa6b427813fde71e35ecf73f45f0ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9016","title":"Move meta tags to landing page only","createdAt":"2023-10-31T20:16:07Z"}
{"state":"Merged","mergedAt":"2023-11-01T05:43:45Z","number":9017,"mergeCommitSha":"a995b2c7d269e0de67d7b65ab091a8e0e1579bfd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9017","title":"Move to landing config","createdAt":"2023-10-31T20:59:46Z"}
{"state":"Merged","mergedAt":"2023-10-31T21:19:17Z","number":9018,"body":"This can be a very slow API. Need to wait for it to complete server-side.","mergeCommitSha":"6acb7df1bb1b1e9de93c8bbbcccfac7d06e2323b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9018","title":"Increase getTeamStatus timeout","createdAt":"2023-10-31T21:18:01Z"}
{"state":"Merged","mergedAt":"2023-10-31T21:41:28Z","number":9019,"mergeCommitSha":"2446b3ab123e65ef871ed669e7bbababbd3bc22c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9019","title":"Add team pr ingestion pipeline settings","createdAt":"2023-10-31T21:19:26Z"}
{"state":"Merged","mergedAt":"2022-04-26T17:05:07Z","number":902,"body":"We should be using roles to specify various environment contexts\r\n","mergeCommitSha":"199d9d6781687e8ea656bea67b46225f1209795d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/902","title":"Move environments to roles","createdAt":"2022-04-14T20:19:52Z"}
{"state":"Merged","mergedAt":"2023-11-01T03:46:42Z","number":9020,"mergeCommitSha":"6b142166bd81e790ece46bcefc868c95dc339cf8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9020","title":"Cleaning up workflows and using repo var and secrets","createdAt":"2023-10-31T21:20:21Z"}
{"state":"Merged","mergedAt":"2023-10-31T21:57:27Z","number":9021,"mergeCommitSha":"1b4ef3bb846b8b74963ec2445ec4e8df3a2ecd14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9021","title":"Enable sensitive field logging in prod","createdAt":"2023-10-31T21:24:24Z"}
{"state":"Merged","mergedAt":"2023-10-31T21:26:08Z","number":9022,"mergeCommitSha":"a40bc4cef1eae2b46cd9bb6d8b74258cfbb6abc3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9022","title":"Add llama models","createdAt":"2023-10-31T21:25:23Z"}
{"state":"Merged","mergedAt":"2023-10-31T22:16:11Z","number":9023,"mergeCommitSha":"1d26c7ed88e2ae7731373b36540632567f78c11e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9023","title":"Missed a reference pattern to clean up","createdAt":"2023-10-31T21:36:12Z"}
{"state":"Merged","mergedAt":"2023-10-31T21:51:16Z","number":9024,"mergeCommitSha":"8907ec0ed2a436688ce2d66d87839f9237d47484","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9024","title":"Enable pr summary","createdAt":"2023-10-31T21:51:06Z"}
{"state":"Merged","mergedAt":"2023-10-31T22:05:26Z","number":9025,"mergeCommitSha":"4fac9d78a22e50080b0708b434d9642fb5430dd5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9025","title":"Maybe fix refreshTeamResources which is not completing for Liftoff","createdAt":"2023-10-31T21:59:00Z"}
{"state":"Merged","mergedAt":"2023-11-01T00:06:25Z","number":9026,"mergeCommitSha":"2c9e096469c7aa78375a3f2be57b1c39d9f9f266","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9026","title":"Add Google Oauth client secrets","createdAt":"2023-10-31T23:22:41Z"}
{"state":"Merged","mergedAt":"2023-11-01T15:38:32Z","number":9027,"mergeCommitSha":"8861b099f0309eaef9bb1ee7f3529b2b7aec3366","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9027","title":"Expose SCM provider API token for team in admin","createdAt":"2023-10-31T23:59:15Z"}
{"state":"Merged","mergedAt":"2023-11-01T19:08:32Z","number":9028,"mergeCommitSha":"c438de6186ef9b495da62389f6461cf97fcaa0bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9028","title":"Followup suggestions","createdAt":"2023-11-01T00:07:02Z"}
{"state":"Merged","mergedAt":"2023-11-01T00:31:09Z","number":9029,"mergeCommitSha":"9bfaca4c08c7fed2fc808e17c7794e1cb06314f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9029","title":"Skip repo summary embedding if no repos","createdAt":"2023-11-01T00:09:23Z"}
{"state":"Merged","mergedAt":"2022-04-20T23:36:02Z","number":903,"body":"<img width=\"863\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/163473061-530e347e-f9d3-43c6-b3e1-92302613f45d.png\">\r\n\r\n * Client-side for now (should be easy enough to abstract to an API property if necessary)\r\n * Current behaviour: all links go to the web dashboard instance of the message. Browser scrolls to the message given the url search query \r\n     * Will need to eventually add support to open message in any client ","mergeCommitSha":"1f0d208e0d8c3522f8657c5efda8a2dd0edff465","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/903","title":"Add link to the web dashboard message","createdAt":"2022-04-14T21:09:08Z"}
{"state":"Closed","mergedAt":null,"number":9030,"body":"Once we get a roped llama back online, want to finish testing this topic classifier. If nothing else, so I can get the type2type converter working with llama2 prompt syntax:\r\n\r\n```\r\ndef build_instruction(input_type: TypeSchema, output_type: TypeSchema, document: str) -> str:\r\n    base_instruction = \"\"\"\r\n    <<SYS>>\r\n    You are generating a JSON object for our API.\r\n    Transform the Input using the following JSON schemas.\r\n    Return a valid JSON instance of the Output Type.\r\n    If you cannot perform the transform, return an empty JSON object.\r\n    <</SYS>>\r\n    \"\"\"\r\n    instruction = base_instruction + \"\\n\\n**Input Type:**\\n\" + input_type.json()\r\n    instruction += \"\\n\\n**Output Type:**\\n\" + output_type.json()\r\n    instruction += \"\\n\\n**Input:**\\n\" + \"{text='\" + document + \"'}\"\r\n    return \"\"\"<s>[INST]\"\"\" + instruction + \"\"\"[/INST]\"\"\"\r\n```","mergeCommitSha":"45b2b91aea741aa3216d57ac52acc76104d1167b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9030","title":"Fix llama tokens + remove hanging print","createdAt":"2023-11-01T01:37:37Z"}
{"state":"Merged","mergedAt":"2023-11-01T03:21:34Z","number":9031,"body":"Reverts NextChapterSoftware/unblocked#9018\r\n\r\nwasn’t necessary","mergeCommitSha":"988a39f649db44cd15f8e7ef3bbd2b89c2cc4ce4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9031","title":"Revert \"Increase getTeamStatus timeout\"","createdAt":"2023-11-01T03:21:23Z"}
{"state":"Merged","mergedAt":"2023-11-01T04:18:51Z","number":9032,"mergeCommitSha":"16324d2f349f6f8eef0d2adb6c910cffe7f7261d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9032","title":"try east 2 deployment again","createdAt":"2023-11-01T04:18:43Z"}
{"state":"Merged","mergedAt":"2023-11-01T04:56:10Z","number":9033,"mergeCommitSha":"27d5278c23139b060808403a15fd78bd3e23e5d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9033","title":"adding code to cleanup ssh tunnel after each deployment","createdAt":"2023-11-01T04:56:03Z"}
{"state":"Merged","mergedAt":"2023-11-01T05:21:04Z","number":9034,"mergeCommitSha":"4729af754252478655b7cece38ecf321428b8c5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9034","title":"use pkill to kill ssh process","createdAt":"2023-11-01T05:20:51Z"}
{"state":"Merged","mergedAt":"2023-11-01T22:14:43Z","number":9035,"body":"<img width=\"1450\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/ae679b6e-6afd-4b2e-a051-8a96103db668\">\r\n<img width=\"515\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/c8649b03-25de-4451-b5c6-be5e876bcdd5\">\r\n","mergeCommitSha":"cf76586848f0f49d5739b2d7c7529253944fef0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9035","title":"Add followup suggestions to unbot response","createdAt":"2023-11-01T05:50:18Z"}
{"state":"Merged","mergedAt":"2023-11-01T06:25:59Z","number":9036,"mergeCommitSha":"8a9273037b0cc0aff945e54ca19058dc8106f89e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9036","title":"removed unused workflow and added sudo to pkill","createdAt":"2023-11-01T06:25:52Z"}
{"state":"Merged","mergedAt":"2023-11-01T07:03:39Z","number":9037,"mergeCommitSha":"df08a087e0fef3642e882505ba8ffbd015bea5f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9037","title":"last one for this night","createdAt":"2023-11-01T07:03:32Z"}
{"state":"Merged","mergedAt":"2023-11-01T18:49:46Z","number":9038,"mergeCommitSha":"7c5d2bde136a21c03a6840298aef0a5487d62ace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9038","title":"[SKIP TESTS] Add topic ingestion","createdAt":"2023-11-01T18:20:05Z"}
{"state":"Merged","mergedAt":"2023-11-01T19:54:17Z","number":9039,"body":"We had some oddball bugs here:\r\n\r\n* We were explicitly *not* loading intercom for the root landing page.  I don't know why.\r\n* For all other landing pages (blog, pricing, etc) we loaded intercom (via the `IntercomWrapper`), but never actually displayed the UI (`showDefaultUI={true}` was missing in `IntercomProvider`).  So you never saw the Intercom badge.\r\n\r\nIn this PR:\r\n* Load intercom via `IntercomProvider` in all landing pages\r\n* Remove `IntercomWrapper` as we no longer need it.","mergeCommitSha":"ec88b19e679a333f9bb9f632d453efc0c06acbd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9039","title":"Add intercom to landing pages","createdAt":"2023-11-01T18:22:59Z"}
{"state":"Merged","mergedAt":"2022-04-14T22:30:03Z","number":904,"body":"Setup packaging and deployment of ZIP for web extension\r\n\r\nFollow these instructions to install unpacked extension. https://developer.chrome.com/docs/extensions/mv3/getstarted/#unpacked\r\n","mergeCommitSha":"17533af504516ac911e586bcfa97c891cf94d431","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/904","title":"Setup web extension deployment","createdAt":"2022-04-14T21:22:07Z"}
{"state":"Merged","mergedAt":"2023-11-01T18:41:10Z","number":9040,"mergeCommitSha":"18dc3232a24c69f0c5f58aa5719f4e2fd28bb630","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9040","title":"[SKIP TESTS] Attempt to fix us-east database","createdAt":"2023-11-01T18:40:47Z"}
{"state":"Merged","mergedAt":"2023-11-01T23:38:53Z","number":9041,"mergeCommitSha":"84e97ad248fee5343f65c078f69889570c6c0f96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9041","title":"Implement Google Oauth","createdAt":"2023-11-01T18:52:31Z"}
{"state":"Merged","mergedAt":"2023-11-01T19:01:53Z","number":9042,"mergeCommitSha":"cc5abd77b6a7b56fad588e1249c35b085cd00c8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9042","title":"Add database region override to all east services","createdAt":"2023-11-01T19:01:29Z"}
{"state":"Merged","mergedAt":"2023-11-01T19:07:45Z","number":9043,"mergeCommitSha":"4fa40656093551f74cc238f3093d698174833eaf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9043","title":"Start deploying EKS changes to east-2 prod cluster using CI","createdAt":"2023-11-01T19:07:36Z"}
{"state":"Merged","mergedAt":"2023-11-01T19:15:25Z","number":9044,"mergeCommitSha":"9221d918aaf8fb6cd33bd80804ba3a58c4b26d4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9044","title":"forgot to add the ssh kill command","createdAt":"2023-11-01T19:15:17Z"}
{"state":"Merged","mergedAt":"2023-11-01T19:44:47Z","number":9045,"mergeCommitSha":"b1f98643aa83287944ab6054a3708cd97b7de5c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9045","title":"Moving ssh kill task to another action","createdAt":"2023-11-01T19:35:04Z"}
{"state":"Merged","mergedAt":"2023-11-01T20:39:43Z","number":9046,"mergeCommitSha":"4f85c88f8835912e72e919bc33afda9a8338c944","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9046","title":"[SKIP TESTS] - use correct template loader","createdAt":"2023-11-01T20:16:43Z"}
{"state":"Merged","mergedAt":"2023-11-01T23:05:39Z","number":9047,"body":"The recommended approach to use is a sub-query model using Rank.\r\nRank will associated a monotonically increasing number based off ordering of rows.\r\nFollowing the rank sub-query, we do the actual query.\r\n","mergeCommitSha":"be746526dfee20cd29383d59a4c8e6099fa575ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9047","title":"Add ability to query repos by lastActiveAt and createdAt","createdAt":"2023-11-01T20:31:08Z"}
{"state":"Merged","mergedAt":"2023-11-03T00:23:48Z","number":9048,"body":"Will be used to auto-select repos during onboarding.","mergeCommitSha":"90e18ddd52f4dfd26037fbd6b3f466c225f37c5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9048","title":"Recently active repos","createdAt":"2023-11-01T20:36:47Z"}
{"state":"Merged","mergedAt":"2023-11-01T21:00:30Z","number":9049,"mergeCommitSha":"3e0f38fab40bc13684bbe2c826c76b5e565ff2cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9049","title":"[SKIP TESTS] Database issues","createdAt":"2023-11-01T21:00:07Z"}
{"state":"Merged","mergedAt":"2022-04-14T22:27:33Z","number":905,"body":"We might consider doing this periodically rather than just at the end","mergeCommitSha":"e4aaf852ae6be343249b94675cb2494c0aec7e1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/905","title":"Defer upstreaming sourcepoints until after calculation","createdAt":"2022-04-14T21:49:01Z"}
{"state":"Merged","mergedAt":"2023-11-01T23:23:53Z","number":9050,"mergeCommitSha":"4f6622e9212a3fed690778ff8ca17397ca49ac71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9050","title":"Enable suggestion generation in prod","createdAt":"2023-11-01T22:14:55Z"}
{"state":"Merged","mergedAt":"2023-11-01T22:58:32Z","number":9051,"mergeCommitSha":"8f4ff0e8b9d615fdbd7dbb7eb9474f0545075941","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9051","title":"Do not restrict search service honeycomb","createdAt":"2023-11-01T22:58:17Z"}
{"state":"Merged","mergedAt":"2023-11-02T17:47:48Z","number":9052,"body":"<img width=\"1505\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/942b5b1d-aa78-460d-ac6f-a5c1742ae8ad\">\r\n","mergeCommitSha":"1996114e695c1432d4dcf7cb65aae3596d7e468a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9052","title":"Add landing slack page","createdAt":"2023-11-02T00:36:55Z"}
{"state":"Merged","mergedAt":"2023-11-02T16:01:17Z","number":9053,"body":"Rerun when fixed:\r\n- cribl/sandbox-gogen\r\n- https://us-west-2.console.aws.amazon.com/states/home?region=us-west-2#/v2/executions/details/arn:aws:states:us-west-2:029574882031:execution:CodeIngestionDataPipelineStateMachine:codeIngestion-08999a9c-a204-487e-9663-90f71b303b58","mergeCommitSha":"882770499fb4ca6c12b7169e8f3047324aeb577b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9053","title":"Code ingestion handles case where readme is a directory","createdAt":"2023-11-02T00:52:59Z"}
{"state":"Merged","mergedAt":"2023-11-02T03:11:38Z","number":9054,"mergeCommitSha":"3327e893f39e6dee2355c351c84b9efce42852a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9054","title":"Drop dynamo entries when dropping confluence spaces","createdAt":"2023-11-02T03:00:25Z"}
{"state":"Merged","mergedAt":"2023-11-02T15:14:29Z","number":9055,"body":"For now it'll just delete the threads and embeddings. I'll update it later to drop related models (JiraSite, JiraProject)","mergeCommitSha":"ecbf5a408f6f33560a4605acf66715936e1adde8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9055","title":"Add ability to drop Jira site from admin console","createdAt":"2023-11-02T05:51:36Z"}
{"state":"Merged","mergedAt":"2023-11-02T23:03:03Z","number":9056,"mergeCommitSha":"c5dd1784f4a335f1be0830456d5007bf5470d208","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9056","title":"Log query on rerank failures","createdAt":"2023-11-02T16:04:16Z"}
{"state":"Merged","mergedAt":"2023-11-02T22:02:21Z","number":9057,"mergeCommitSha":"8d4b541a1e7fa3cd8474aee699747665d78bf6de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9057","title":"Add isSuggestion field to CreateMessageV3","createdAt":"2023-11-02T16:08:16Z"}
{"state":"Merged","mergedAt":"2023-11-02T18:08:39Z","number":9058,"mergeCommitSha":"6b00004ef7c7fc588275f8aa534ac23d39da67e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9058","title":"Add GoogleIngestionService","createdAt":"2023-11-02T17:21:43Z"}
{"state":"Merged","mergedAt":"2023-11-02T17:44:49Z","number":9059,"mergeCommitSha":"8fcc7ec1f21df80929236cad93ad7f630a047824","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9059","title":"Update startup probe and global network policy","createdAt":"2023-11-02T17:34:32Z"}
{"state":"Merged","mergedAt":"2022-04-19T21:01:25Z","number":906,"body":"Only need to do full calculation once per calculator instance, which is basically once per repo per VSCode session.","mergeCommitSha":"aef8e09f841ae995307050affba9308fcd4bb5d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/906","title":"Unsubscribe from sourcemark stream once it has been initialized","createdAt":"2022-04-15T01:05:21Z"}
{"state":"Merged","mergedAt":"2023-11-02T18:46:11Z","number":9060,"mergeCommitSha":"d254920e4c684257ef7166de27981fe2d8c15a02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9060","title":"Adding us-east-2 prod public IP addresses","createdAt":"2023-11-02T17:51:27Z"}
{"state":"Merged","mergedAt":"2023-11-02T18:04:31Z","number":9061,"mergeCommitSha":"d8d899eeb34328fd537fb3d49cb9fa92a5482e00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9061","title":"slack join","createdAt":"2023-11-02T18:04:24Z"}
{"state":"Merged","mergedAt":"2023-11-02T18:22:32Z","number":9062,"body":"TBH I'm not even sure we need this UI?  It seems like a bit of a random point to bug the user to download the app @benedict-jw ","mergeCommitSha":"f7fd3b82ad4a058ffef2ddb605ba255fd28ae0b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9062","title":"When giving feedback, only display download message on mac","createdAt":"2023-11-02T18:11:42Z"}
{"state":"Merged","mergedAt":"2023-11-02T18:48:10Z","number":9063,"mergeCommitSha":"34542a80d713580e9585a9eedeafe21492330d01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9063","title":"Add service resource","createdAt":"2023-11-02T18:25:39Z"}
{"state":"Merged","mergedAt":"2023-11-02T19:22:13Z","number":9064,"mergeCommitSha":"2a431564cf497a5635f12bf3461854877531afd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9064","title":"[SKIP TESTS] Include modifiedTime when asking for files from Google","createdAt":"2023-11-02T19:05:36Z"}
{"state":"Merged","mergedAt":"2023-11-02T20:23:41Z","number":9065,"mergeCommitSha":"732cd30709904af62b5c1f74ddda5a332db58c9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9065","title":"Use query instead of scan for more efficient cleanup of DynamoDB content","createdAt":"2023-11-02T20:06:31Z"}
{"state":"Merged","mergedAt":"2023-11-02T20:54:02Z","number":9066,"mergeCommitSha":"5e72ec9ad6bff9e41683b11e76ef0e3e87f29cfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9066","title":"Only retrieve Google files modified since the last ingestion","createdAt":"2023-11-02T20:22:27Z"}
{"state":"Closed","mergedAt":null,"number":9067,"body":"Useful when the source defines an explicit width and height.","mergeCommitSha":"b7b8acf8789f83999584e162eb117d806dd3a825","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9067","title":"Image block add width and height","createdAt":"2023-11-02T20:42:51Z"}
{"state":"Merged","mergedAt":"2023-11-02T21:17:28Z","number":9068,"mergeCommitSha":"fc4f8bbff487f7d69fd151e9c6bbe659223f2869","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9068","title":"Bias towards enabled teams for dashboard redirect","createdAt":"2023-11-02T20:50:08Z"}
{"state":"Merged","mergedAt":"2023-11-02T21:16:45Z","number":9069,"body":"Next one will add logic to check for autogenerated messages (like from Graphite)","mergeCommitSha":"9edcbb8d3b20b19ddef964e0cee2349540114a65","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9069","title":"Clean up thread migration logic","createdAt":"2023-11-02T21:04:19Z"}
{"state":"Closed","mergedAt":null,"number":907,"body":"Handles inserts:\r\n- [x] whole line insert\r\n\r\nHandles removals:\r\n- [x] whole line remove\r\n- [x] whole line remove inside range that is insignificant\r\n- [x] whole range remove\r\n- [ ] whole line move within the file\r\n- [ ] whole line move to any file\r\n- [ ] whole line move to any file with modifications\r\n\r\nHandles modifications:\r\n- [x] modification outside range\r\n- [ ] modification inside range that is insignificant\r\n- [ ] modification inside range that matches an insert in the file\r\n- [ ] modification inside range that matches an insert in any file","mergeCommitSha":"fd2868045ea95de09651018a28a66e0e75d94d7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/907","title":"Proper range adjustment","createdAt":"2022-04-15T01:08:03Z"}
{"state":"Merged","mergedAt":"2023-11-03T03:02:14Z","number":9070,"mergeCommitSha":"6bccbedd5b56306e0442b6cb0ba51439b715409c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9070","title":"Implement isSuggestedFollowon","createdAt":"2023-11-02T21:40:16Z"}
{"state":"Merged","mergedAt":"2023-11-03T18:00:57Z","number":9071,"body":"Slack token queries should specify permissions such that we can intelligently pick out the token we need and error if it’s not good enough.\r\n\r\nIn addition, we are planning to soon reverse the order whereby bot tokens are favoured over user tokens.\r\n","mergeCommitSha":"b5a627748662025ca9f9027fad1046b6491a173c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9071","title":"Move to smart slack tokens","createdAt":"2023-11-02T22:08:34Z"}
{"state":"Merged","mergedAt":"2023-11-02T22:36:38Z","number":9072,"mergeCommitSha":"65f424874a1b368b26b24e9df5184804126e8d85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9072","title":"Dont ingest Graphite comments","createdAt":"2023-11-02T22:21:09Z"}
{"state":"Merged","mergedAt":"2023-11-03T21:28:05Z","number":9073,"body":"We store two files in the `/tmp/unblocked-hub` folder:\r\n\r\n* The `env` file, which specifies which environment IDEs should use, for developer builds.  `make use-dev` modifies this file.\r\n* The `ipc.port` file, which the hub publishes to tell IDEs what port to connect for IPC.  This is written out by the helper app on startup, after the IPC port is opened.\r\n\r\nUnfortunate `/tmp` is a terrible place for this, because macOS deletes files in it.  This PR moves the files into the unsandboxed app config folder: `~/Library/Application Support/com.nextchaptersoftware.UnblockedHub`.","mergeCommitSha":"61455de0460ab7430d71e6c5c00c6ea52b84c36b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9073","title":"Use better app configuration folder","createdAt":"2023-11-02T22:39:50Z"}
{"state":"Merged","mergedAt":"2023-11-03T03:16:14Z","number":9074,"mergeCommitSha":"bc1eb623c521281e57a478b11ba250c8676c93e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9074","title":"Repo summary generation lock and run when repos are installed","createdAt":"2023-11-02T23:13:19Z"}
{"state":"Merged","mergedAt":"2023-11-03T15:31:50Z","number":9075,"mergeCommitSha":"e05ffeda208d422568627dd51b42e0b004b7b185","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9075","title":"Update thread migrator to remove Graphite messages","createdAt":"2023-11-03T00:00:03Z"}
{"state":"Merged","mergedAt":"2023-11-03T01:10:56Z","number":9076,"body":"- query uses keyConditionExpression\n\n- scan uses filterExpression","mergeCommitSha":"dcd1fd45a1cac9575d467b294203a66277ef3493","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9076","title":"Fix dynamo query","createdAt":"2023-11-03T00:10:37Z"}
{"state":"Merged","mergedAt":"2023-11-03T14:50:53Z","number":9077,"body":"Another benefit - reduced query is now stored on inference example, which makes it easy to look up in the admin console and replicate in the retriever","mergeCommitSha":"ebf784e768747d574d5d7f9ded324f3121bc3020","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9077","title":"Progressively compress chat for document retrieval","createdAt":"2023-11-03T05:51:03Z"}
{"state":"Merged","mergedAt":"2023-11-03T15:57:06Z","number":9078,"mergeCommitSha":"cad15b62abe9c00584aaffef5ddcced944cbcf67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9078","title":"[SKIP TESTS] Skip threads without repos","createdAt":"2023-11-03T15:43:38Z"}
{"state":"Merged","mergedAt":"2023-11-03T18:27:51Z","number":9079,"body":"Using query not scan now","mergeCommitSha":"ac424db0a502fea3bc7e2b997d434ee07560c06f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9079","title":"Fix dynamodb EKS access","createdAt":"2023-11-03T16:48:56Z"}
{"state":"Merged","mergedAt":"2022-04-18T04:15:53Z","number":908,"body":"Given a SourceMark, retrieve the SourcePoint history relevant to the current branch.\r\n\r\nThis is intended to be used when we can no longer track a point (due to either code\r\nremoval or significant code modification beyond what we can reliably track). By\r\nproviding the history of points on this branch the user can at least browse previous\r\npoints at earlier revisions.","mergeCommitSha":"16b747dc9ab0bb8182834411f5d24025aaee2ba3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/908","title":"Interface for getting SourceMark history","createdAt":"2022-04-18T03:07:58Z"}
{"state":"Merged","mergedAt":"2023-11-03T18:16:34Z","number":9080,"mergeCommitSha":"73f1e59015923669ddeb0d82a1988f35547a0196","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9080","title":"Add isSuggestion arg to createMessage client call","createdAt":"2023-11-03T18:03:25Z"}
{"state":"Merged","mergedAt":"2023-11-03T21:56:22Z","number":9081,"mergeCommitSha":"bbcef792e5342e7b2f82f2c4ed7ec314d86ca38e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9081","title":"Add helper for suggestions","createdAt":"2023-11-03T18:39:10Z"}
{"state":"Merged","mergedAt":"2023-11-03T21:10:21Z","number":9082,"mergeCommitSha":"edf1078aebbec63a9f3695d084e712e3b3cccd9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9082","title":"Slack wants validation on webhooks","createdAt":"2023-11-03T20:10:59Z"}
{"state":"Merged","mergedAt":"2023-11-03T22:22:03Z","number":9083,"body":"Slack has asked for this:\r\n\r\n“Add specific information to your privacy policy on how users can request to have their data deleted.”\r\n","mergeCommitSha":"3e40dcf07d5fe31b6e95af6e206021b248bc20d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9083","title":"Update privacy docs for slack","createdAt":"2023-11-03T20:27:38Z"}
{"state":"Merged","mergedAt":"2023-11-04T10:46:27Z","number":9084,"mergeCommitSha":"116176d14746f63cb7e346667bb4a67a724fd8c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9084","title":"goodbye user scopes","createdAt":"2023-11-03T22:16:57Z"}
{"state":"Merged","mergedAt":"2023-11-04T04:19:13Z","number":9085,"mergeCommitSha":"7dc0afdae260b4e8d153f3461c3acf8af49af7da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9085","title":"Fix up thread titles where Graphite comments have been removed","createdAt":"2023-11-04T03:46:30Z"}
{"state":"Merged","mergedAt":"2023-11-04T06:12:32Z","number":9086,"mergeCommitSha":"230423229f58a158a55ffdc16bc52b6e3c7c5632","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9086","title":"[SKIP TESTS] Just ignore anything with a Graphite link because no human would add one","createdAt":"2023-11-04T05:16:28Z"}
{"state":"Merged","mergedAt":"2023-11-04T07:00:58Z","number":9087,"mergeCommitSha":"d5e2beed7a5b86cb5d7b85893067c3aa858ca22d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9087","title":"More EKS dynamo fixes","createdAt":"2023-11-04T06:59:59Z"}
{"state":"Merged","mergedAt":"2023-11-06T06:53:30Z","number":9088,"mergeCommitSha":"f0a9acbba2f72cce496dee67c7cd544f627040e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9088","title":"Delete other Graphite comments","createdAt":"2023-11-04T08:30:10Z"}
{"state":"Merged","mergedAt":"2023-11-04T12:39:01Z","number":9089,"body":"Slack seems to want this...","mergeCommitSha":"1d132914c630b5e06e420092e6d59251b5df6a25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9089","title":"Add ability to show chat bubble for slack landing","createdAt":"2023-11-04T12:20:38Z"}
{"state":"Merged","mergedAt":"2022-04-28T02:59:12Z","number":909,"body":"https://blog.jetbrains.com/ktor/2022/04/11/ktor-2-0-released/","mergeCommitSha":"452464a1c2772d492e5a9d8d4ad25614a9d929e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/909","title":"Use ktor 2.0.0","createdAt":"2022-04-18T06:25:31Z"}
{"state":"Merged","mergedAt":"2023-11-04T21:49:33Z","number":9090,"mergeCommitSha":"56f87fe3b0ed41ec5baec9eccfeb92560db03ce4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9090","title":"Make MLTypedDocument.documentId a required field","createdAt":"2023-11-04T17:39:37Z"}
{"state":"Merged","mergedAt":"2023-11-05T05:49:19Z","number":9091,"body":"Can be null in the `findInstallationsAndRepos` case.","mergeCommitSha":"c4033ba31b6be3dcec11e5cf325b39bd9eaf0e6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9091","title":"Add createdAt and lastActiveAt to ScmRepo model","createdAt":"2023-11-05T05:27:53Z"}
{"state":"Merged","mergedAt":"2023-11-05T18:13:44Z","number":9092,"mergeCommitSha":"df15b2cc9ad2a5da7fc7039e536d8847d32cd857","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9092","title":"Try again","createdAt":"2023-11-05T18:13:05Z"}
{"state":"Merged","mergedAt":"2023-11-05T21:17:08Z","number":9093,"mergeCommitSha":"ceb747924f7dbbe2007f407f48dadf4d407b8948","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9093","title":"Move to standard intercom functionality","createdAt":"2023-11-05T20:43:19Z"}
{"state":"Merged","mergedAt":"2023-11-05T23:02:54Z","number":9094,"body":"[SKIP TESTS]","mergeCommitSha":"030629f017797ce123f91edc8ab15c4451376258","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9094","title":"[SKIP TESTS] Move to cleaner implementaiton for slack notifications","createdAt":"2023-11-05T22:30:03Z"}
{"state":"Merged","mergedAt":"2023-11-05T23:52:28Z","number":9095,"mergeCommitSha":"24c562318fa9e777e941eae494b435529d9e9dd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9095","title":"[SKIP TESTS] Fix slack notifications part 2","createdAt":"2023-11-05T23:52:02Z"}
{"state":"Merged","mergedAt":"2023-11-06T01:15:41Z","number":9096,"mergeCommitSha":"db23db11e03d8d93c7a4f1e93ec37edecc859f5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9096","title":"upgrade Dev to pg 15.4 to support active-active replication","createdAt":"2023-11-06T01:15:18Z"}
{"state":"Merged","mergedAt":"2023-11-06T03:28:50Z","number":9097,"body":"Refactor document merging and rerank\n\nPull non-active files into document section of prompt","mergeCommitSha":"23a7cc1cea80295b95deb2839593b40d35de5a1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9097","title":"Refactor document prompt, merging, and re-ranking","createdAt":"2023-11-06T02:48:53Z"}
{"state":"Merged","mergedAt":"2023-11-06T04:58:01Z","number":9098,"mergeCommitSha":"31a4243579a909cc5138afacb6aa9ede792a0cf5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9098","title":"Start cerr rank from 0","createdAt":"2023-11-06T03:37:32Z"}
{"state":"Merged","mergedAt":"2023-11-06T03:41:46Z","number":9099,"body":"- Added replication related parameters (for v15.4 or higher)\r\n- Added a way to enable Postgres deployments in cold-site\r\n- Updated us-east-2 Postgres configuration to prepare for deployment\r\n","mergeCommitSha":"e116997f32acfa80c474ec04d6cacdff7e050e49","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9099","title":"Enable supoort for active-active postgres replication","createdAt":"2023-11-06T03:38:23Z"}
{"state":"Merged","mergedAt":"2022-01-20T21:27:35Z","number":91,"body":"Or it will throw errors. ","mergeCommitSha":"525941a3883e49f0861d82d4735ecd20ad1555d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/91","title":"Security plugin needs to be installed before routes","createdAt":"2022-01-20T20:25:58Z"}
{"state":"Merged","mergedAt":"2022-04-18T17:24:26Z","number":910,"body":"https://chapter2global.slack.com/archives/C036YH3QF7T/p1650267666831929","mergeCommitSha":"6c6ddf928f14c2ce0ddc5cf6a444e798d9ebd535","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/910","title":"Disable log line in recalculateMarkForCommit","createdAt":"2022-04-18T17:07:56Z"}
{"state":"Merged","mergedAt":"2023-11-06T03:57:50Z","number":9100,"body":"Reverts NextChapterSoftware/unblocked#9099","mergeCommitSha":"ba96004fa89335ae31983c85f3216514f0a1ae0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9100","title":"Revert \"Enable supoort for active-active postgres replication\"","createdAt":"2023-11-06T03:57:42Z"}
{"state":"Merged","mergedAt":"2023-11-06T04:53:53Z","number":9101,"mergeCommitSha":"a08667f040bf31371a1d846302393a12621cc87d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9101","title":"Propagate local files through followup messages","createdAt":"2023-11-06T04:35:06Z"}
{"state":"Merged","mergedAt":"2023-11-06T04:58:32Z","number":9102,"mergeCommitSha":"beb15c43a98617796cf184efa930368cfc1cd78f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9102","title":"Update unblocked-file-utils to 0.6.0","createdAt":"2023-11-06T04:37:13Z"}
{"state":"Merged","mergedAt":"2023-11-06T05:50:16Z","number":9103,"mergeCommitSha":"ffaed6f0d9900ee25af12a26f8f163268e687263","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9103","title":"Show document query in admin console","createdAt":"2023-11-06T05:40:50Z"}
{"state":"Merged","mergedAt":"2023-11-06T20:39:01Z","number":9104,"mergeCommitSha":"1d1351ced9685a11540cfa08cb635c78bc37d2af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9104","title":"Embed Google Docs and text files","createdAt":"2023-11-06T06:40:15Z"}
{"state":"Merged","mergedAt":"2023-11-06T08:20:46Z","number":9106,"mergeCommitSha":"c0ffb3715dab32c98d178703d3f549e0e5e07957","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9106","title":"Fix llama index versionoing","createdAt":"2023-11-06T08:20:32Z"}
{"state":"Merged","mergedAt":"2023-11-06T17:02:44Z","number":9107,"mergeCommitSha":"e8c83d9fc766312af31d6d66160a0ba4a1778b4c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9107","title":"Fix logging","createdAt":"2023-11-06T17:02:37Z"}
{"state":"Merged","mergedAt":"2023-11-06T17:40:31Z","number":9108,"mergeCommitSha":"cad1ded29cc2e6c80b2275f3a8372c1caa990288","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9108","title":"[SKIP TESTS] Disable TopicMetricsUtilsTest","createdAt":"2023-11-06T17:26:21Z"}
{"state":"Merged","mergedAt":"2023-11-06T18:55:58Z","number":9109,"mergeCommitSha":"46fc74443f09ea42ddb56a87d1191f67b6903a6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9109","title":"[SKIP TESTS] - fix local document formatting","createdAt":"2023-11-06T18:52:38Z"}
{"state":"Merged","mergedAt":"2023-11-06T19:49:28Z","number":9110,"mergeCommitSha":"5127279519f9b13efc61a163a5565c6c47164d27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9110","title":"Adds new GPT 4 Turbo and improved GPT 3.5 Turbo models to template options","createdAt":"2023-11-06T19:28:22Z"}
{"state":"Merged","mergedAt":"2023-11-08T00:34:28Z","number":9111,"body":"We don't need this anymore.","mergeCommitSha":"6b65abf8919ddce8dd4e35629ee0e04124705add","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9111","title":"Stop prefixing @Unblocked in message input","createdAt":"2023-11-06T20:59:14Z"}
{"state":"Merged","mergedAt":"2023-11-06T21:39:21Z","number":9112,"mergeCommitSha":"38ee1528dd9cbdfce3b8fe38bb97eabad1b93c24","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9112","title":"Run migration on remaining Graphite threads","createdAt":"2023-11-06T21:19:00Z"}
{"state":"Merged","mergedAt":"2023-11-06T21:35:03Z","number":9113,"mergeCommitSha":"3fb0bf679c6c1341156b9fa5a137eb826f421549","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9113","title":"respect slack webhook timestmamps","createdAt":"2023-11-06T21:20:48Z"}
{"state":"Merged","mergedAt":"2023-11-06T22:15:27Z","number":9114,"mergeCommitSha":"f476dbe17272aee18dee5d98ce8dcaa141e712c8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9114","title":"Notify #growth channel when team is switched to Full mode","createdAt":"2023-11-06T21:31:31Z"}
{"state":"Merged","mergedAt":"2023-11-06T23:11:26Z","number":9115,"mergeCommitSha":"708440ac9c4f7329bb577e53e0c5ff133641c9c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9115","title":"Pusher drifted out of sync with how unread latest modified is being computed","createdAt":"2023-11-06T22:01:09Z"}
{"state":"Merged","mergedAt":"2023-11-06T22:57:18Z","number":9116,"mergeCommitSha":"5942474a0c2ca5c272af20bd09458916fceaec05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9116","title":"Fixes notifications appearing for old threads and first time load","createdAt":"2023-11-06T22:39:02Z"}
{"state":"Merged","mergedAt":"2023-11-06T22:55:35Z","number":9117,"mergeCommitSha":"b7ca6da9d63e78607657ee766f5c1be04c519a03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9117","title":"[SKIP TESTS] - Fix bad formatting on active IDE doc","createdAt":"2023-11-06T22:54:41Z"}
{"state":"Merged","mergedAt":"2023-11-06T23:21:20Z","number":9118,"mergeCommitSha":"4f0122c74ce8a242b3890e324448d21221044760","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9118","title":"Pass local IDE context with new messages","createdAt":"2023-11-06T23:07:15Z"}
{"state":"Merged","mergedAt":"2023-11-06T23:07:47Z","number":9119,"mergeCommitSha":"40e9bd09f3abd82e649e84ba3471251bfea75883","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9119","title":"increase dev rate limits because they were getting annoying","createdAt":"2023-11-06T23:07:39Z"}
{"state":"Merged","mergedAt":"2023-11-07T17:59:13Z","number":9120,"body":"A bot cannot join an archived channel, so it can't peruse messages","mergeCommitSha":"50868ee1bb101cdbc31be429435a94061a297fa2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9120","title":"Do not ingest archived slack channels","createdAt":"2023-11-06T23:30:42Z"}
{"state":"Merged","mergedAt":"2023-11-07T00:12:47Z","number":9121,"mergeCommitSha":"e1457ddc8d7df60aba9125c4885b79c58e2cc27b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9121","title":"[SKIP TESTS] I have no idea what I'm doing does this even work","createdAt":"2023-11-07T00:12:01Z"}
{"state":"Merged","mergedAt":"2023-11-07T21:33:30Z","number":9122,"body":"Adds a new table-based RepoConnector UI.  This only affects Bitbucket for now, but will be reused in Github once the API is ready.\r\n\r\n![CleanShot 2023-11-06 at 16 32 33@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b5df8048-ab04-4c1b-86c6-b4e73cedff1d)\r\n\r\n* Update `InstallationRepoStreams` to allow fetching installation information both before install (using `listInstallationRepos`) and after install (using `getTeamScmInstallationRepos`).  This means the UI can be driven off the same store and data\r\n* Add `Table`, `SortableHeaderCell`, `TableGroup`, and `TableHeader` components, to make building tables a bit easier.  Some parts of these components are factored out of the topics table code.","mergeCommitSha":"6efd1787b82f3b7268f26baff150cf5cbe4cbfe5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9122","title":"New repo connector UI","createdAt":"2023-11-07T00:35:02Z"}
{"state":"Merged","mergedAt":"2023-11-07T20:25:08Z","number":9123,"body":"Update styling, organize discussions list by date (this gets dashboard to parity with the hub):\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13431372/44b140e1-eb02-4ebe-a941-3860ca59450b\r\n\r\n(Part 1 of the dashboard update)","mergeCommitSha":"2c362d553125380ac1349c6aaca58de0c2568618","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9123","title":"Reorganize dashboard discussion views","createdAt":"2023-11-07T00:50:55Z"}
{"state":"Merged","mergedAt":"2023-11-07T03:44:06Z","number":9124,"body":"Add maxSize ML template constraint to any template\r\n\r\nUse as:\r\n```\r\n{template|maxSize=1000}\r\n```\r\n\r\nAlso fix several bugs calculating the constrained size.","mergeCommitSha":"a81dd891fde17aef22d829bc8b33810a65e1723e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9124","title":"Add maxSize ML template constraint to any template","createdAt":"2023-11-07T01:30:38Z"}
{"state":"Merged","mergedAt":"2023-11-08T18:56:54Z","number":9125,"body":"To enable selecting the folders and files to be ingested","mergeCommitSha":"c6de6aa88498769646c3ee81bb18918577ce73a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9125","title":"[RFC] Add Google configuration endpoints","createdAt":"2023-11-07T17:20:53Z"}
{"state":"Merged","mergedAt":"2023-11-07T21:47:38Z","number":9126,"mergeCommitSha":"1c1eef0e449da4fcc2b1145264f8d296cd61fcbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9126","title":"Bot should answer if the only participant in the thread is the user and the bot regardless of a bot mention","createdAt":"2023-11-07T18:45:05Z"}
{"state":"Merged","mergedAt":"2023-11-07T19:40:14Z","number":9127,"body":"Allows us to write:\n```\n{summaryDocuments|maxSize=1000}\n```","mergeCommitSha":"200c26b617d826f44f21da05fdf166e50d6d4f96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9127","title":"Summaries are now constrained","createdAt":"2023-11-07T19:03:33Z"}
{"state":"Merged","mergedAt":"2023-11-07T19:43:25Z","number":9128,"body":"So that we can enable for our team in PROD when recording the video for Google verification.","mergeCommitSha":"571f8dbc256936fecc05b68706425ffcb92530fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9128","title":"Add feature flag for Google","createdAt":"2023-11-07T19:20:25Z"}
{"state":"Merged","mergedAt":"2023-11-07T20:06:13Z","number":9129,"mergeCommitSha":"2b4ac2de1f13acf357fc4b529ef7bff11d583b20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9129","title":"Document retrieval defaults match latest prompt defaults","createdAt":"2023-11-07T19:44:56Z"}
{"state":"Merged","mergedAt":"2023-11-07T20:25:59Z","number":9130,"mergeCommitSha":"0fa0b0524f6113a4bcf1208711df21959945e8aa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9130","title":"Scaling up Data and Slack services in us-east-2 prod","createdAt":"2023-11-07T20:16:14Z"}
{"state":"Merged","mergedAt":"2023-11-07T21:39:21Z","number":9131,"body":"Also bumps the submodule commit.","mergeCommitSha":"642d1baaf7b4cb4be2a9b8a64a17431998e3717a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9131","title":"Import google drive icon to client","createdAt":"2023-11-07T20:37:56Z"}
{"state":"Merged","mergedAt":"2023-11-07T22:28:25Z","number":9132,"mergeCommitSha":"830e0da2dcf1f80a889c994cebc61c1892695b3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9132","title":"Career job postings","createdAt":"2023-11-07T21:40:13Z"}
{"state":"Merged","mergedAt":"2023-11-07T23:14:01Z","number":9133,"mergeCommitSha":"9d6bedc7588cf42d3b0a4e3e31440d8fb8f0ac97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9133","title":"Add Google admin console pages","createdAt":"2023-11-07T22:59:40Z"}
{"state":"Merged","mergedAt":"2023-11-08T20:08:27Z","number":9134,"body":"Add a gradle plugin task to parse our spec and validate it against used operations in last 60 days.\r\nThis currently just marks unused operation as deprecated.\r\nWe will soon be removing the operaitons using the plugin.\r\n\r\nOne minor downside is that this overwrites the api spec with an in-memory copy, which is fine.\r\nValidated that the spec is correct afterwards.","mergeCommitSha":"95f4ddafb56c283581b347922581d56fa5e17f58","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9134","title":"OpenAPIDeprecation","createdAt":"2023-11-07T23:59:29Z"}
{"state":"Merged","mergedAt":"2023-11-08T00:57:54Z","number":9135,"mergeCommitSha":"e2c5bd23a66f8c7623b3ba57dfb8a1f839dbb6fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9135","title":"Fix Use CERR checkbox on document retrieval page","createdAt":"2023-11-08T00:13:05Z"}
{"state":"Merged","mergedAt":"2023-11-09T23:41:38Z","number":9136,"body":"https://www.notion.so/nextchaptersoftware/New-Repo-Selection-APIs-df89af5404b8413bbb393354fd87935f","mergeCommitSha":"7f0980b90501c60f54f7474a469bb0356f165cef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9136","title":"New SCM select repo APIs","createdAt":"2023-11-08T00:20:53Z"}
{"state":"Merged","mergedAt":"2023-11-08T05:16:45Z","number":9137,"body":"- background image to header\r\n- adding email buttons\r\n![CleanShot 2023-11-07 at 17 02 23@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/47890078-3c99-41f6-8665-ccd9ba29610d)\r\n\r\n![CleanShot 2023-11-07 at 17 02 44@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/e57e4f86-25ee-4419-acc1-6f9feb158c75)\r\n","mergeCommitSha":"5e0fa2317a7dc86d224e5b8267c91b18237cf7ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9137","title":"Adding background image to header","createdAt":"2023-11-08T01:03:03Z"}
{"state":"Merged","mergedAt":"2023-11-08T05:39:40Z","number":9138,"mergeCommitSha":"c309b5aa2803072dd71a81f0160ab9eb8b004964","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9138","title":"Rm hover date","createdAt":"2023-11-08T05:28:20Z"}
{"state":"Merged","mergedAt":"2023-11-08T16:17:21Z","number":9139,"mergeCommitSha":"47b4cb4ad82b492c072e6e4defd8bf7af82fbe7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9139","title":"Fix centre alignment on career posting","createdAt":"2023-11-08T16:04:18Z"}
{"state":"Merged","mergedAt":"2022-04-19T17:39:08Z","number":914,"body":"This is better, but only slightly.\r\n\r\n- We need to audit all instances of `window.showErrorMessage` as we're dumping `Error`s in here right now.\r\n\r\n    <img width=\"646\" alt=\"Screen Shot 2022-04-18 at 11 40 37\" src=\"https://user-images.githubusercontent.com/1798345/163857802-3c103eeb-0826-4d55-bb94-bb5693788a23.png\">\r\n\r\n\r\n- Also the repo resolution and associated error messaging needs to consolidated so that we don't repeat the error handling whenever we need a repo instance.\r\n","mergeCommitSha":"755577d7931237ac1e4c7916cabb91ed476dbcbc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/914","title":"Better error message when failing to create discussion for moved/renamed repo","createdAt":"2022-04-18T18:41:39Z"}
{"state":"Merged","mergedAt":"2023-11-08T17:22:54Z","number":9140,"mergeCommitSha":"7b7a3327d87a2d741b7bbbe37c242bf98b2975e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9140","title":"Add JSON output format instructions to template","createdAt":"2023-11-08T16:40:36Z"}
{"state":"Merged","mergedAt":"2023-11-08T18:34:58Z","number":9141,"mergeCommitSha":"675d554ff5fee48239c015fe311eac89045e3544","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9141","title":"Add response_format argument to OpenAI chat completion calls","createdAt":"2023-11-08T17:08:20Z"}
{"state":"Merged","mergedAt":"2023-11-08T17:52:40Z","number":9142,"body":"Fixes a bug where IntelliJ wasn't using the existing hub refresh token on startup.  There was a dangling promise on startup -- we weren't waiting for the initial refresh token (from the hub) to be stored before setting up the auth system.  At some point this API changed from being synchronous to async, and this call wasn't changed to await.\r\n\r\nI'm going to look into whether eslint can help us avoid this kind of thing in the future.","mergeCommitSha":"ebd17d62416eef794cdbce4f797e05fb381e5ebc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9142","title":"Fix IntelliJ auth on startup","createdAt":"2023-11-08T17:25:49Z"}
{"state":"Merged","mergedAt":"2023-11-08T17:52:49Z","number":9143,"mergeCommitSha":"742d8c61d2d2b5cba6fef2976a0294e260bd7f85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9143","title":"Scaling up Topic, Linear and Jira services in Prod us-east-2","createdAt":"2023-11-08T17:43:36Z"}
{"state":"Open","mergedAt":null,"number":9144,"body":"* Use `AbortSignal.timeout` instead of custom signal -- now that VSCode is using a slightly newer node, we can use this directly.  This also allows us to customize timeouts at the call site.\r\n* Add error handler middleware to log all HTTP timeouts","mergeCommitSha":"086dd0278ce73b54a41e13877a482f3b78f7aa2c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9144","title":"Improve TS HTTP timeout handling","createdAt":"2023-11-08T19:10:55Z"}
{"state":"Open","mergedAt":null,"number":9145,"body":"![CleanShot 2023-11-08 at 14 03 43@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/f8de1b3f-fa58-4da4-8014-d6236489044e)\r\n","mergeCommitSha":"b7ffee69ee12b101d3251297013fac777c128480","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9145","title":"Add slack channel invite warning","createdAt":"2023-11-08T20:04:43Z"}
{"state":"Merged","mergedAt":"2023-11-08T20:50:55Z","number":9146,"body":"There are multipole api operations that were incorrectly referencing pullRequestId as a query parameter","mergeCommitSha":"0b499df114ec744bec32274aa103e41a936ece6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9146","title":"Generalize pull requset id","createdAt":"2023-11-08T20:16:26Z"}
{"state":"Merged","mergedAt":"2023-11-08T20:52:02Z","number":9147,"mergeCommitSha":"a95733edb3efe11cf9af65dcb10a63f6a3c4503d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9147","title":"Implement Google drive search","createdAt":"2023-11-08T20:35:13Z"}
{"state":"Merged","mergedAt":"2023-11-08T21:09:36Z","number":9148,"mergeCommitSha":"40d607fc42ae5c400737e9668fbe7a041bc09e47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9148","title":"Linear comment user ID could be null","createdAt":"2023-11-08T20:45:53Z"}
{"state":"Closed","mergedAt":null,"number":9149,"mergeCommitSha":"df86d505e62441211b4626805d0af3480984df78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9149","title":"Standardize private yaml","createdAt":"2023-11-08T20:53:16Z"}
{"state":"Merged","mergedAt":"2022-04-18T19:28:43Z","number":915,"body":"see task to remove allowlist\r\nhttps://github.com/NextChapterSoftware/unblocked/issues/916","mergeCommitSha":"596f4fe86ba537f2ddbd4a1dcf8fee7ffa49c4ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/915","title":"Allow the CodeSwellDemo account","createdAt":"2022-04-18T19:12:01Z"}
{"state":"Merged","mergedAt":"2023-11-08T22:20:28Z","number":9150,"body":"Hacky hack!  Content TBD.","mergeCommitSha":"5f29447c9ba006306c95c42735700aed981dc043","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9150","title":"Add meta for career landing pages","createdAt":"2023-11-08T21:38:48Z"}
{"state":"Merged","mergedAt":"2023-11-08T22:36:51Z","number":9151,"mergeCommitSha":"ed1e519068cd171177309d7a3e5a1ebe639e179e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9151","title":"Add support for ingesting files from shared Google Drives","createdAt":"2023-11-08T22:12:11Z"}
{"state":"Merged","mergedAt":"2023-11-08T23:06:48Z","number":9152,"body":"We get shared drives in a separate call so this is not necessary.","mergeCommitSha":"b0ac249157a96f11de4f9bf8b28bcf3ea8bea6a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9152","title":"Exclude folders and files from shared drives for search","createdAt":"2023-11-08T22:52:52Z"}
{"state":"Closed","mergedAt":null,"number":9153,"mergeCommitSha":"4a6501b51627fda450c31ad7de280386def36c51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9153","title":"Add PDF enum","createdAt":"2023-11-08T23:28:59Z"}
{"state":"Merged","mergedAt":"2023-11-08T23:57:22Z","number":9154,"mergeCommitSha":"5a7b7f3d008777dfb1c7146a44d002f79cb5fa88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9154","title":"Only emit #customer-integrations notification for new installation","createdAt":"2023-11-08T23:44:52Z"}
{"state":"Merged","mergedAt":"2023-11-09T00:44:02Z","number":9155,"mergeCommitSha":"017ba972b33f0eea7c27bc18fb33635203acb86d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9155","title":"Update hiring meta","createdAt":"2023-11-09T00:08:27Z"}
{"state":"Merged","mergedAt":"2023-11-09T00:55:27Z","number":9156,"body":"Move most of the content from `RepoConnector` into `RepoTable`, which will be used anywhere we want to display the repo table UI (in GitHub config, most notably).\r\n\r\n`RepoConnector` itself didn't do much outside of the table, so I moved its remaining code into `ConnectRepo`.","mergeCommitSha":"2eda9e281531b5f38d4d27a22dea57e8bcb73531","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9156","title":"Refactor RepoTable a bit","createdAt":"2023-11-09T00:43:58Z"}
{"state":"Merged","mergedAt":"2023-11-09T03:16:27Z","number":9157,"body":"Standardize api spec format to Swagger Yaml writer output.","mergeCommitSha":"f5ada1199f82711c59d61365b9eef0eacf3a7ba3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9157","title":"Standardize api specs","createdAt":"2023-11-09T02:51:24Z"}
{"state":"Merged","mergedAt":"2023-11-09T17:59:53Z","number":9159,"body":"Planning on removing from apis next","mergeCommitSha":"10afd54f38280da1dea63cf5b6c83ffbe4348357","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9159","title":"Delete agora and related dead code","createdAt":"2023-11-09T03:38:41Z"}
{"state":"Merged","mergedAt":"2023-11-09T04:19:48Z","number":9162,"body":"This reverts commit f5ada1199f82711c59d61365b9eef0eacf3a7ba3.\r\n","mergeCommitSha":"3c59ede913ee8e489f72692500dad5f558217f5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9162","title":"Revert \"Standardize styles (#9157)\"","createdAt":"2023-11-09T04:19:39Z"}
{"state":"Merged","mergedAt":"2023-11-09T05:03:28Z","number":9163,"mergeCommitSha":"b1d5a11589b0fb86e4b9f93c52d7a570d037fcc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9163","title":"Add operation overrides","createdAt":"2023-11-09T04:58:40Z"}
{"state":"Merged","mergedAt":"2023-11-09T05:05:47Z","number":9164,"body":"- Add operation overrides\r\n- Update\r\n- Try again\r\n- Revert \"Revert \"Standardize styles (#9157)\" (#9162)\"\r\n","mergeCommitSha":"7d793899fca1a69073ec141c5921c544f2c3bc53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9164","title":"RevertTheRevert","createdAt":"2023-11-09T05:05:35Z"}
{"state":"Merged","mergedAt":"2023-11-09T17:24:45Z","number":9166,"mergeCommitSha":"14f6497afb3e36e220457a1f99d8228033a6bc6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9166","title":"Add PDF enum","createdAt":"2023-11-09T07:10:09Z"}
{"state":"Merged","mergedAt":"2023-11-09T18:42:09Z","number":9168,"mergeCommitSha":"1fb0f4f82a3494aa2da72e75f297cfd764477a4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9168","title":"Add back being able to add folders from shared drives","createdAt":"2023-11-09T18:17:13Z"}
{"state":"Merged","mergedAt":"2023-11-09T19:33:50Z","number":9169,"body":"Removing:\r\n               /teams/{teamId}/videoChannels/mine\r\n               /teams/{teamId}/videoChannels/{videoChannelId}\r\n               /teams/{teamId}/videoChannels/{videoChannelId}\r\n               /teams/{teamId}/videoChannels/{videoChannelId}/invite\r\n               /teams/{teamId}/videoChannels/{videoChannelId}/join\r\n               /teams/{teamId}/videoChannels/{videoChannelId}/leave\r\n               /teams/{teamId}/videoChannels/{videoChannelId}/heartbeat\r\n               /teams/{teamId}/videoChannels/{videoChannelId}/startScreenSharing\r\n               /teams/{teamId}/videoChannels/{videoChannelId}/stopScreenSharing\r\n               /teams/{teamId}/videoRecordings/{recordingId}\r\n               /teams/{teamId}/videoRecordings/{recordingId}\r\n               /teams/{teamId}/videoRecordings/{recordingId}/stop\r\n               \r\n               \r\nConfirmed via honeycomb these guys have not been called.","mergeCommitSha":"fda7c4af8002bed8cb083eb4462a32b03841095d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9169","title":"Remove dead video apis","createdAt":"2023-11-09T18:44:29Z"}
{"state":"Merged","mergedAt":"2022-04-18T21:00:27Z","number":917,"mergeCommitSha":"a64d7903ed00a19a8a92b4fbd1338e08e56b1a32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/917","title":"Lint","createdAt":"2022-04-18T20:52:21Z"}
{"state":"Merged","mergedAt":"2023-11-09T19:48:56Z","number":9170,"mergeCommitSha":"dbb75b49feddf2e57afed188e7e283e39134628c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9170","title":"[BREAKS API ON MAIN] Add a generated uuid to GoogleDriveFile","createdAt":"2023-11-09T19:08:01Z"}
{"state":"Merged","mergedAt":"2023-11-09T22:00:36Z","number":9171,"body":"Problem was that SCM OAuth _always_ modified the `Identity.person` field, even when the identity already had a Person relationship.\r\n\r\nChanged so that we only set the `person` value to a new or exiting person when there is no existing related person.\r\n\r\nfixes: https://linear.app/unblocked/issue/UNB-1062/people-service-fix-the-issue-with-creating-leaking-person-objects","mergeCommitSha":"c45c25127f5b7873b6c64afbef86f2ac4f726dd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9171","title":"Prevent SCM login from modifying existing identity-to-person relationship","createdAt":"2023-11-09T19:34:37Z"}
{"state":"Merged","mergedAt":"2023-11-09T21:38:45Z","number":9172,"mergeCommitSha":"0bee89b20b6809f4c5d512571a85801ef5cc4521","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9172","title":"Clamp onboarding step indices (crash fix)","createdAt":"2023-11-09T19:43:02Z"}
{"state":"Merged","mergedAt":"2023-11-09T20:11:50Z","number":9173,"body":"Otherwise a refresh token won't be returned during code exchange","mergeCommitSha":"27dcc98f1876b54f456130948d70e023fa685f03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9173","title":"Add prompt paramenter to oauth link","createdAt":"2023-11-09T19:54:25Z"}
{"state":"Merged","mergedAt":"2023-11-09T20:58:35Z","number":9174,"mergeCommitSha":"a7095fea75cfbf6774e655b0d9f6e7132db2e31a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9174","title":"Scaling more background worker services in east","createdAt":"2023-11-09T20:49:37Z"}
{"state":"Merged","mergedAt":"2023-11-09T21:18:12Z","number":9175,"mergeCommitSha":"315111fb71009e7940b3f1325d33db1e36dc54d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9175","title":"Return default list if query string is empty","createdAt":"2023-11-09T20:57:02Z"}
{"state":"Merged","mergedAt":"2023-11-09T21:43:42Z","number":9176,"mergeCommitSha":"6b0bb41638c5dda2924afd4e1c287d48dd4bcbd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9176","title":"Upserting TeamMemberModels for non-SCM integrations should set isCurrentMember to false","createdAt":"2023-11-09T21:27:34Z"}
{"state":"Merged","mergedAt":"2023-11-09T22:07:32Z","number":9177,"body":"So basically we don't even use the `scmUrl`... at all. GitHub urls are now (apparently) derived entirely from the `prCommentUrl` from the latest thread message. This confused the heck out of me because the other insight types remain but are not in use (I guess?). Anyways.... we need to clean this up at some point","mergeCommitSha":"dc9e5ca9eea363e4305423c0a4854742e1364866","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9177","title":"Fixes the 'Open in GitHub' menu actions in the hub","createdAt":"2023-11-09T21:36:24Z"}
{"state":"Merged","mergedAt":"2023-11-09T22:20:05Z","number":9178,"mergeCommitSha":"83265ca2c2aa77e88b187b43aadcc092019ff51a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9178","title":"Discovered yet another reference pattern to clean up","createdAt":"2023-11-09T22:06:35Z"}
{"state":"Merged","mergedAt":"2023-11-10T19:03:29Z","number":9179,"body":"Note that this UI is hidden behind a feature flag so it should be safe to merge.\r\n\r\nWithout access:\r\n<img width=\"1508\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/1e9daf72-0926-462f-8b36-ad92ddc21f84\">\r\n\r\n<img width=\"1497\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/2209bdc6-fbc1-4419-935d-7210b15fd0d5\">\r\n\r\nWith access:\r\n<img width=\"875\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/ad7b96c5-168c-4110-b654-2e3c16ef48f1\">\r\n\r\n<img width=\"1489\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/2f5bebde-ddf7-4d05-ab3b-d7499e96a260\">\r\n","mergeCommitSha":"8cf1246b8ebd7fbce0b48791f3f1db808b715c3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9179","title":"Google drive UI","createdAt":"2023-11-09T22:29:36Z"}
{"state":"Merged","mergedAt":"2022-04-18T22:44:48Z","number":918,"body":"Display a tooltip and link to the thread on all sourcemark gutter icons.\r\n\r\n<img width=\"258\" alt=\"Screen Shot 2022-04-18 at 2 22 10 PM\" src=\"https://user-images.githubusercontent.com/2133518/163880101-67d20e78-c875-4514-9790-8b13b687368e.png\">\r\n\r\nThis required joining thread data into the sourcemark data for each file.  This is not particularly efficient, but isn't adding any extra network overhead since we're loading this data anyways for the sidebar.","mergeCommitSha":"fde19c6753d64ee92e0b25da8876cd00fe508d51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/918","title":"Simple gutter icon tooltips","createdAt":"2022-04-18T21:03:36Z"}
{"state":"Merged","mergedAt":"2023-11-10T00:38:45Z","number":9180,"mergeCommitSha":"772b6fd6012cb5a0c6c5a19a6d16c6d22678e69a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9180","title":"Update Provider.Google display name","createdAt":"2023-11-09T22:38:10Z"}
{"state":"Merged","mergedAt":"2023-11-09T23:15:07Z","number":9181,"body":"# Problem\r\nUnblocked can't currently answer questions like \"what does this do?\" or \"write a test for this function\" or \"explain this line of code\", or \"explain how this variable is used in my codebase\".\r\n\r\nThe underlying issue is that Unblocked doesn't know what \"this\" refers to. It has most commonly manifested as an issue when a customer highlights a piece of code that they want an answer to.\r\n\r\n# Proposal\r\nAt a minimum, we need two things to address the problem:\r\n- The visible file content\r\n- The specific line content or range of selected lines content\r\n\r\nThere are a few techniques we can use to solve the class of questions above:\r\n- Query substitution/expansion. This requires a language model, where the selected line(s), and possibly whole file content are fed into the model with some very tight criteria for expansion so that a question like \"where is this property used in my code\" becomes \"where is the <ClassName.propertyName> property used in my code\", resulting in a highly accurate retrieval. \r\n- Selected line annotation in the prompt, which draws the model's focus to those specific lines while still preserving the surrounding code context\r\n- Separate prompt callout for the snippet","mergeCommitSha":"80808f19add6ffe2ee5177085b72e996e454ece1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9181","title":"Add selected line range to FileContext","createdAt":"2023-11-09T22:38:54Z"}
{"state":"Merged","mergedAt":"2023-11-10T00:22:32Z","number":9182,"body":"- Goodbye dead slack apis\r\n- Goodbye dead files\r\n","mergeCommitSha":"c3008d16e512a13b597fcd497f9fced393c0530c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9182","title":"DeleteDeprecatedSlackApis","createdAt":"2023-11-09T23:09:29Z"}
{"state":"Merged","mergedAt":"2023-11-10T00:13:43Z","number":9183,"mergeCommitSha":"693c0349309bfa585af661c4f8c6008b11e81e9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9183","title":"Adds a selectedColumnRage property to FileContext","createdAt":"2023-11-09T23:41:06Z"}
{"state":"Open","mergedAt":null,"number":9184,"body":"Also emit an event (handler to come later) to clean up any threads/embeddings/models associated with the uninstalled integration.","mergeCommitSha":"cac9b64d904fb0d88f326efffe6751ee998e5501","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9184","title":"Revoke OAuth tokens when uninstalling an integration","createdAt":"2023-11-10T00:04:57Z"}
{"state":"Merged","mergedAt":"2023-11-10T00:55:06Z","number":9185,"mergeCommitSha":"c1c0bf6ee6d4c8efa5f575ca8cd10881243be374","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9185","title":"Delete semantic search dead apis","createdAt":"2023-11-10T00:29:08Z"}
{"state":"Merged","mergedAt":"2023-11-10T04:44:18Z","number":9186,"mergeCommitSha":"7e2bba297b3374a90f2166b0cad60a4053543a07","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9186","title":"Refactor selected file content API","createdAt":"2023-11-10T01:23:17Z"}
{"state":"Merged","mergedAt":"2023-11-10T03:36:10Z","number":9187,"body":"- Remove all api endpoints that have been already marked as deprecated in API spec and deemed unused via Honeycomb.","mergeCommitSha":"7c121386405dfb0b015591830176c1989f8ff75a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9187","title":"RemoveDeprecatedAndUnusedApis","createdAt":"2023-11-10T02:09:23Z"}
{"state":"Merged","mergedAt":"2023-11-10T08:22:36Z","number":9188,"mergeCommitSha":"83df3f559e01d15b5fb24ededd6b26ce37977802","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9188","title":"Fix statsig transitive vulnerability","createdAt":"2023-11-10T08:09:28Z"}
{"state":"Merged","mergedAt":"2023-11-10T12:48:10Z","number":9189,"body":"- Delete legacy person api\r\n- api compat\r\n","mergeCommitSha":"87c4792c987bd404dce7dfd47d5f19c65413cd20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9189","title":"Delete deprecated person apis","createdAt":"2023-11-10T11:56:03Z"}
{"state":"Merged","mergedAt":"2022-04-19T23:45:06Z","number":919,"body":"Hook up sourcemark store to web extension UI.\r\n\r\nTODO: Need to integrate thread and message preview.\r\nBetter UI.\r\n\r\n<img width=\"1485\" alt=\"CleanShot 2022-04-18 at 14 40 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/163882183-4a67afac-bc2a-4169-bbcb-f4c66b5a9cc2.png\">\r\n\r\nhttps://user-images.githubusercontent.com/1553313/163884820-baa757fc-464a-4599-ae31-ccee8db7c775.mp4\r\n\r\n\r\n","mergeCommitSha":"be7f2b1cef2ba806d395b51c64bc399b7a8acaa7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/919","title":"Basic source mark rendering","createdAt":"2022-04-18T21:40:38Z"}
{"state":"Merged","mergedAt":"2023-11-10T13:20:19Z","number":9190,"mergeCommitSha":"5b08edf7645a0df2399506e34011618820796424","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9190","title":"Remove dead person api","createdAt":"2023-11-10T13:20:13Z"}
{"state":"Merged","mergedAt":"2023-11-10T20:32:54Z","number":9191,"body":"VSCode only for now, will add IntelliJ next","mergeCommitSha":"dbd3de08b293ef1723a9d4cb1a23e02b0d827abf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9191","title":"Add selection to IDE context","createdAt":"2023-11-10T17:01:16Z"}
{"state":"Merged","mergedAt":"2023-11-10T17:30:41Z","number":9192,"mergeCommitSha":"6d0547c3a49d32b63fce88c509ece3af060def59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9192","title":"[SKIP TESTS] Fix sourceDocumentId for Google document","createdAt":"2023-11-10T17:17:24Z"}
{"state":"Merged","mergedAt":"2023-11-10T18:53:34Z","number":9193,"mergeCommitSha":"c08ba9747b867ff60162b923093fd311c4987b26","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9193","title":"Scale up the rest of our services in prod us-east-2","createdAt":"2023-11-10T18:45:31Z"}
{"state":"Merged","mergedAt":"2023-11-10T21:33:22Z","number":9194,"body":"This is step one, which is to annotate the document with a selected section for the active document in the prompt. This will require a couple prompt changes to get it to work correctly. A couple more advanced techniques could follow:\r\n- Separate prompt call out for the selected code.\r\n- Query expansion for retrieval and context in the prompt (I suspect this will deliver massive value)","mergeCommitSha":"f9ce30e18e5afbf232fe33c7175d19b8f0d8cf8c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9194","title":"Add selected line content to prompt","createdAt":"2023-11-10T19:09:45Z"}
{"state":"Merged","mergedAt":"2023-11-10T23:09:41Z","number":9195,"body":"When we Oauth, there's a lag between when the new identity is aligned with the primary member because that happens during in MembershipMaintenance which happens on a regular interval.\r\n\r\nBut we have the person ID when we Oauth, so we can immediately associate the new team member with the existing primary team member linked to that person and team.","mergeCommitSha":"a05cec34c00e675e103075ba3d7277226a0df09c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9195","title":"Align team member for person during Google and Notion oauth","createdAt":"2023-11-10T19:11:45Z"}
{"state":"Merged","mergedAt":"2023-11-10T19:33:17Z","number":9196,"mergeCommitSha":"04c9bbad61268838c5c133769f26faf595665449","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9196","title":"Fix broken scrolling","createdAt":"2023-11-10T19:17:34Z"}
{"state":"Merged","mergedAt":"2023-11-10T19:40:39Z","number":9197,"mergeCommitSha":"6f6543b00789ad5afd4d263eb2d5a3bc9551388e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9197","title":"Increase startup probe timeout","createdAt":"2023-11-10T19:31:39Z"}
{"state":"Merged","mergedAt":"2023-11-10T20:39:27Z","number":9198,"mergeCommitSha":"e1a19d217d116c129ca4f9fa4def0e1fec6848cb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9198","title":"Fix onboarding redirect","createdAt":"2023-11-10T19:35:02Z"}
{"state":"Merged","mergedAt":"2023-11-10T22:13:51Z","number":9199,"mergeCommitSha":"cbbebd59a25e1a1dbdda2bb02733166679cfce6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9199","title":"Stop bleeding backend video transcription errors to the user","createdAt":"2023-11-10T19:40:25Z"}
{"state":"Merged","mergedAt":"2022-01-21T00:07:20Z","number":92,"body":"Part of https://github.com/Chapter2Inc/codeswell/issues/89\r\n\r\nMakes `transactionWithLogger` suspending and adds ability to specify tests as suspending.\r\n\r\nFuture PR will establish a pattern for making suspending database calls in prod.","mergeCommitSha":"dd469cbfb61867885141939e3e816aaa8f67727d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/92","title":"Make database calls in tests suspending","createdAt":"2022-01-20T22:32:45Z"}
{"state":"Merged","mergedAt":"2022-04-19T05:38:35Z","number":920,"body":"Currently, new PRs that are merged are never ingested after the initial ingestion for a repo happens. To fix this, we need to poll the API periodically to get new comments (ultimately we want webhooks but for now we'll poll). \r\n\r\nTo do this, we need to update our initial ingestion logic to grab the `updatedAt` for the latest PR ingested. Then after the initial ingestion is complete, we can poll the rest API for pull requests that have been updated since that date. The `updatedAt` field changes whenever a PR is changed, including when comments are created, updated, or deleted. We can use this with the rest API `etag` to efficiently poll the rest API for new comments.\r\n\r\nThis PR updates the GraphQL query to sort PRs by updatedAt (descending). Next PR will add the query that polls for newly updated PRs.","mergeCommitSha":"a0fb8eb2a099011ef88d33af9bb9d3b3207d31c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/920","title":"Order pull requests in GraphQL query by updated_at desc","createdAt":"2022-04-18T23:21:00Z"}
{"state":"Merged","mergedAt":"2023-11-10T19:55:47Z","number":9200,"mergeCommitSha":"6248b2c7c07f0617fec9e9e5f190cabbf364769e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9200","title":"[SKIP TESTS] scale down video services in east","createdAt":"2023-11-10T19:41:56Z"}
{"state":"Merged","mergedAt":"2023-11-10T20:29:52Z","number":9201,"mergeCommitSha":"4478126fec435c458a71e54f93f14da847571590","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9201","title":"[SKIP TESTS]   Fix a mistake in video service config","createdAt":"2023-11-10T20:21:25Z"}
{"state":"Merged","mergedAt":"2023-11-10T21:06:49Z","number":9202,"mergeCommitSha":"d38709d98c476c7b6dff822f194f7320bdbd30be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9202","title":"Ingest Google subfolders","createdAt":"2023-11-10T20:32:46Z"}
{"state":"Merged","mergedAt":"2023-11-10T21:30:30Z","number":9203,"mergeCommitSha":"01c87cbd27911143f9c190ce273fc666ca70c0c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9203","title":"[SKIP TESTS] Fix SerialName for GoogleFolderIngestionEvent","createdAt":"2023-11-10T21:30:21Z"}
{"state":"Merged","mergedAt":"2023-11-10T21:46:49Z","number":9204,"body":"This should have failed on the previous PR, I'm not sure why it didn't.","mergeCommitSha":"b9c7159f865374277009182a97facd3c71ad2d89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9204","title":"Fix broken build","createdAt":"2023-11-10T21:35:26Z"}
{"state":"Merged","mergedAt":"2023-11-10T21:54:02Z","number":9205,"body":"also fix notion integration deleting\r\n\r\nLogging to be removed after testing.","mergeCommitSha":"587e5398eee207c697e7d86204f9864c33fb0da8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9205","title":"Add wider support and logging for debugging","createdAt":"2023-11-10T21:38:44Z"}
{"state":"Merged","mergedAt":"2024-01-17T05:42:49Z","number":9206,"body":"I am surprised this never bit us before","mergeCommitSha":"c38667b1aebbc0092f81b336eb9c447ad22c3467","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9206","title":"Run jetbrains plugin CI build on `/shared` or `package.json` change","createdAt":"2023-11-10T21:42:55Z"}
{"state":"Merged","mergedAt":"2023-11-12T07:19:26Z","number":9207,"mergeCommitSha":"000a00f8a0fcd0a9884423f3d4dfe982eaa38943","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9207","title":"Refactor list teams","createdAt":"2023-11-10T21:48:41Z"}
{"state":"Merged","mergedAt":"2023-11-15T07:06:00Z","number":9208,"mergeCommitSha":"e9c8af76ca8a154b631532ac0846affbcf05ac23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9208","title":"Backfill the isUserSelected field on repo by copying the value from isScmConnected","createdAt":"2023-11-10T21:53:52Z"}
{"state":"Merged","mergedAt":"2023-11-21T05:28:35Z","number":9209,"body":"- Removes this migration which has been run already\r\n  https://github.com/NextChapterSoftware/unblocked/pull/9208\r\n","mergeCommitSha":"acd8db2629d3e949e36f08347c8438134877deab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9209","title":"Remove isUserSelected migration","createdAt":"2023-11-10T22:04:01Z"}
{"state":"Merged","mergedAt":"2022-04-19T16:28:15Z","number":921,"body":"Turns out that columns are completely independent. They cannot (and should not) be\r\nrepresented as a range, since a range of [100, 1] is impossible.\r\n\r\nThey can also be independently null. This happens when the starting lines\r\nof a point range are deleted by a user; in this case we need to nullify the start\r\ncolumn, but not the end column. Similar when the ending lines of a point range\r\nare deleted by a user.","mergeCommitSha":"c7c7abb7c9341d5170fb292df5ac4a7e46f8e3f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/921","title":"Column ranges can be independently null","createdAt":"2022-04-19T00:25:42Z"}
{"state":"Merged","mergedAt":"2023-11-10T22:12:08Z","number":9210,"mergeCommitSha":"54d3a3761c9c1a1667319a73f207f3232fb858c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9210","title":"Remove logging in GoogleFileIngestionService","createdAt":"2023-11-10T22:04:02Z"}
{"state":"Open","mergedAt":null,"number":9211,"mergeCommitSha":"ee9fcc0fa5cd4fdd9d6f1854b41e0cae147af047","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9211","title":"Skip topics selection if there are no topics","createdAt":"2023-11-10T22:06:31Z"}
{"state":"Merged","mergedAt":"2023-11-10T22:38:04Z","number":9212,"mergeCommitSha":"71d5c60bdbd6b63f20e1224af4c209745b017366","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9212","title":"Fix onboarding redirect and rm logs","createdAt":"2023-11-10T22:26:46Z"}
{"state":"Merged","mergedAt":"2023-11-10T23:20:04Z","number":9213,"mergeCommitSha":"de5d092dded7e871e9a81ab361624581f700407f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9213","title":"Filter google files at source","createdAt":"2023-11-10T22:34:22Z"}
{"state":"Merged","mergedAt":"2023-11-11T00:48:53Z","number":9214,"body":"\r\n<img width=\"1301\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/91a30beb-0650-409a-b8a8-27445c2d9b4e\">\r\n\r\n\r\n","mergeCommitSha":"acf1681b5779b50cbe6aec775ab0b5dbebb81117","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9214","title":"Clean up notion settings to match other integrations","createdAt":"2023-11-10T23:36:06Z"}
{"state":"Merged","mergedAt":"2023-11-10T23:51:45Z","number":9215,"mergeCommitSha":"addc101a37c68b3fcce905ef94208d4a76c4568a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9215","title":"Provide selection context for all visible files","createdAt":"2023-11-10T23:41:00Z"}
{"state":"Merged","mergedAt":"2023-11-11T00:40:07Z","number":9216,"mergeCommitSha":"f9fb065eeeed6a93f9b4324b7172f88fb5cecb66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9216","title":"Set last synced to null when Google Drive folders list is updated","createdAt":"2023-11-10T23:43:38Z"}
{"state":"Merged","mergedAt":"2023-11-11T00:05:06Z","number":9217,"mergeCommitSha":"5bac50f7ced93d9036bb91c0ab8be842f371c8de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9217","title":"Assume the latest visible local file is the active file","createdAt":"2023-11-10T23:46:11Z"}
{"state":"Merged","mergedAt":"2023-11-11T01:29:46Z","number":9218,"mergeCommitSha":"d1290dcb5fd0aab8b9502ef205fd9cc796aa3180","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9218","title":"Fix loading team members for google drive member","createdAt":"2023-11-11T00:48:45Z"}
{"state":"Merged","mergedAt":"2023-11-12T02:16:07Z","number":9219,"mergeCommitSha":"c60dae0f704253d73b95b2aea1a52805686a7682","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9219","title":"Consume GitHub installation repository selection","createdAt":"2023-11-11T00:58:24Z"}
{"state":"Merged","mergedAt":"2022-04-19T19:42:46Z","number":922,"body":"Handles inserts:\r\n- [x] whole line insert\r\n\r\nHandles removals:\r\n- [x] whole line remove\r\n- [x] whole line remove inside range that is insignificant\r\n- [x] whole range remove\r\n- [ ] DEFER: whole line move within the file\r\n- [ ] DEFER: whole line move to any file\r\n- [ ] DEFER: whole line move to any file with modifications\r\n\r\nHandles modifications:\r\n- [x] modification outside range\r\n- [ ] DEFER: modification inside range that is insignificant\r\n- [ ] DEFER: modification inside range that matches an insert in the file\r\n- [ ] DEFER: modification inside range that matches an insert in any file","mergeCommitSha":"a5dbe94a491a3fecbff8f85d17787b002a1bb049","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/922","title":"Proper range adjustment","createdAt":"2022-04-19T01:18:07Z"}
{"state":"Merged","mergedAt":"2023-11-11T01:41:24Z","number":9220,"mergeCommitSha":"a25b72755bf6f9fea97750c46c440d4b63e4ad9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9220","title":"[SKIP TESTS] Fix slack notification for google drive installation","createdAt":"2023-11-11T01:22:30Z"}
{"state":"Merged","mergedAt":"2023-11-11T03:05:55Z","number":9221,"body":"Fix totally unnecessary team and repo lookups when constructing CodeInsightIndexContentModel.","mergeCommitSha":"00d8befc8754166ca6b2a3ecb1b3da564fe5afb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9221","title":"Fix unnecessary team and repo lookups in doc retrieval marshalling","createdAt":"2023-11-11T01:28:07Z"}
{"state":"Merged","mergedAt":"2023-11-11T01:41:40Z","number":9222,"mergeCommitSha":"cc084b724786a634ede630c4b3649d7704046781","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9222","title":"Fix bad compare","createdAt":"2023-11-11T01:34:49Z"}
{"state":"Merged","mergedAt":"2023-11-11T23:09:11Z","number":9223,"body":"Adding a panel for details on how you install, use, and configure our Slack bot. Because we don't have a direct \"Add to Slack\" button, we have to be super detailed.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/13353189/8793c03b-fb6a-41fd-bcb1-5f2ec06a2b54\r\n\r\n","mergeCommitSha":"d7b6789df47807ffdd5ee7b13c4d29183c7e428a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9223","title":"Adding setup information to the slack landing page","createdAt":"2023-11-11T01:48:26Z"}
{"state":"Merged","mergedAt":"2023-11-11T03:50:28Z","number":9224,"mergeCommitSha":"4de5c5a81f9719cd185e9902156669b496448eb4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9224","title":"Fix GoogleIngestionService to correctly set the last synced time","createdAt":"2023-11-11T03:37:04Z"}
{"state":"Merged","mergedAt":"2023-11-11T06:03:09Z","number":9225,"body":"Person accounts are still the right approach for general active user calculations (unlike team members) as they're team agnostic.\r\n\r\nWe add additional fields to UserEngagementMetrics for calculations of events that have no person acccount associated, because at the time the event was created, the underlying member did not have a person account associated with it.\r\n\r\nIn those instances, we attempt to calculate distinct team members disambiguated by the associated primary member id if it exists. This is a best-effort approach (in that we might get dupes in the numbers across teams), but whatever.\r\n\r\nWe then allow for summing the distinct persons to distinct team members in Metrics page.. There's no cleaner way of doing this that I can think of.\r\n\r\nI do not want to mess around with the existing metrics graphs as well, hence the additive approach.","mergeCommitSha":"fd425a04d9e5c4a315b03393ddca7415cbcb745b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9225","title":"First stab no-account user engagement","createdAt":"2023-11-11T04:39:53Z"}
{"state":"Merged","mergedAt":"2023-11-11T05:38:09Z","number":9226,"mergeCommitSha":"599f72add87c4ac67b5988bdf96b16cf0e32889f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9226","title":"Use us-west-2 for assets","createdAt":"2023-11-11T05:34:55Z"}
{"state":"Merged","mergedAt":"2023-11-11T18:04:47Z","number":9227,"mergeCommitSha":"f9c411229932ca857d41e782f5f6825fa309806c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9227","title":"Merge metrics","createdAt":"2023-11-11T17:37:19Z"}
{"state":"Merged","mergedAt":"2023-11-12T08:15:29Z","number":9228,"body":"Greedy or look-ahead/look-behind exclusion regex's are inherently inconsistent and non-performant due to underlying implementations. So let's dump those and scan procedurally.","mergeCommitSha":"2164358d0c30e2d76759f5bc417b9ac7994089bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9228","title":"Fix bad ref matching","createdAt":"2023-11-12T06:05:51Z"}
{"state":"Merged","mergedAt":"2023-11-12T09:34:08Z","number":9229,"mergeCommitSha":"2f98c5551be9ea740d937aa3031c0feeb306889a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9229","title":"[SKIP TESTS] Fix admin web cert","createdAt":"2023-11-12T09:25:13Z"}
{"state":"Merged","mergedAt":"2022-04-19T20:48:13Z","number":923,"body":"## Summary\r\n\r\nImplemented with an `NSPanel` subclass with SwiftUI content. The tricky parts of this is getting the thing to \"float\" above all other content on the screen. The notifications stuff here will be pulled out into the menu bar app very soon.\r\n\r\nThe colours are off, but this is mostly just to get the mechanics working. Polish will come later. Parking the video app after this for now...\r\n\r\n![CleanShot 2022-04-18 at 17 31 47](https://user-images.githubusercontent.com/858772/163908789-d7d01c7e-31b6-4a59-aec8-7d788ab6531b.gif)\r\n\r\n","mergeCommitSha":"2c8ccfd2bc0f2e65bb81b4e6d16b691afd47fa2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/923","title":"Custom notifications for calls","createdAt":"2022-04-19T02:34:49Z"}
{"state":"Merged","mergedAt":"2023-11-12T19:34:53Z","number":9230,"mergeCommitSha":"fb82b78a5824eb1780357ec9ba0a94fbf0641900","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9230","title":"enabling ALB security group creation in east-2","createdAt":"2023-11-12T19:34:05Z"}
{"state":"Merged","mergedAt":"2023-11-12T23:58:23Z","number":9231,"body":"Temp fix while we work on this https://github.com/NextChapterSoftware/unblocked/pull/9184","mergeCommitSha":"8522681453267abbc31e3982e14e68c7855d5f11","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9231","title":"Get latest identity for searchGoogleDriveFiles operation","createdAt":"2023-11-12T23:36:19Z"}
{"state":"Merged","mergedAt":"2023-11-13T07:43:17Z","number":9232,"mergeCommitSha":"06213c0304ab8d79bbd6e475dcab68d07811b850","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9232","title":"Scale up Video and Transcription services in us-east-2","createdAt":"2023-11-13T00:01:28Z"}
{"state":"Merged","mergedAt":"2023-11-14T18:39:38Z","number":9233,"body":"The icon pushes down the text, making it feel like it's not optically centred on the page. Adding a `bottom-margin` to offset.\r\n\r\nNot sure if these are shared elsewhere?\r\n\r\nBreakpoint: S\r\n![375x812](https://github.com/NextChapterSoftware/unblocked/assets/13353189/d9bd0ef6-d7d3-4308-892f-b4d0138ae151)\r\n\r\nBreakpoint: Md\r\n![768x1024](https://github.com/NextChapterSoftware/unblocked/assets/13353189/ec44ffa0-5e14-4cf5-b43a-a28534b5b5c6)\r\n\r\nBreakpoint: Lg\r\n![1024x768](https://github.com/NextChapterSoftware/unblocked/assets/13353189/ab6b9b2e-c148-40f8-8cc9-5320f8d14233)\r\n\r\nBreakpoint: XL\r\n![1280x800](https://github.com/NextChapterSoftware/unblocked/assets/13353189/c815b81c-491f-4f74-8250-3e5164135deb)\r\n","mergeCommitSha":"f3410531dad2ae8c3f40798a820a3d38f382b0c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9233","title":"Optically centre PendingProcessing page.","createdAt":"2023-11-13T00:06:26Z"}
{"state":"Merged","mergedAt":"2023-11-13T03:40:23Z","number":9234,"mergeCommitSha":"ec6cfe2f939bbf1fdd7d4342f6c1e9facb9e190b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9234","title":"fix s3 logging bucket","createdAt":"2023-11-13T03:31:18Z"}
{"state":"Merged","mergedAt":"2023-11-13T08:35:10Z","number":9235,"body":"This reverts commit 06213c0304ab8d79bbd6e475dcab68d07811b850.","mergeCommitSha":"2e4fe05eebb4a3bc8325e8705a405504aa2642f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9235","title":"Revert \"Scale up Video and Transcription services in us-east-2 (#9232)\"","createdAt":"2023-11-13T08:24:24Z"}
{"state":"Merged","mergedAt":"2023-11-13T21:40:12Z","number":9236,"mergeCommitSha":"a7a2f3c9dc0aaf0477edc61ae4783a10adfc2bbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9236","title":"[SKIP TESTS] disable deploys to us-east-2","createdAt":"2023-11-13T21:28:23Z"}
{"state":"Merged","mergedAt":"2023-11-14T19:24:06Z","number":9237,"body":"- Fix admin page for user engagement veents\r\n- Update\r\n","mergeCommitSha":"20f0523a09867c097b773b9dfe1e62a75f5e3f5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9237","title":"FixAdminPage","createdAt":"2023-11-14T19:13:17Z"}
{"state":"Merged","mergedAt":"2023-11-14T20:03:58Z","number":9238,"mergeCommitSha":"4a3ab2184a367a33861f9fe601c5bf0e05bf0011","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9238","title":"[SKIP TESTS] Remove east-2 prod cluster deployment from eksctl jobs","createdAt":"2023-11-14T19:52:18Z"}
{"state":"Merged","mergedAt":"2023-11-14T20:57:47Z","number":9239,"mergeCommitSha":"d4920e18eabaa58c6a03b1cf83f5eb98efc19dd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9239","title":"Move minheight to larger breakpoints","createdAt":"2023-11-14T20:03:23Z"}
{"state":"Merged","mergedAt":"2022-04-19T22:05:30Z","number":924,"body":"Part two of the work to ingest new pull request comments after the initial ingestion during onboarding. \r\n\r\nThis uses the RepoModel to store a cursor so that if the SCM service is restarted during ingestion, the process can pick up where it left off.\r\n\r\nOne more PR to come after this that will ingest comments from PRs as they're merged.","mergeCommitSha":"2405d0187c299e1f271c676d4d66694204573e83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/924","title":"Make pull request ingestion resilient","createdAt":"2022-04-19T15:24:13Z"}
{"state":"Merged","mergedAt":"2023-11-17T04:54:26Z","number":9240,"body":"# Summary\r\nThis PR focuses on regression testing.\r\n\r\nThere are two types of evals we're pursuing:\r\n1. Regression tests\r\n2. A/B tests\r\n\r\nIt's turns out that one is _not_ a perfect subset of the other when it comes to data projection.\r\n\r\n# Regression Tests Definition\r\nRegression tests can be defined as follows:\r\n- For a given template T\r\n- And a set of previous examples S(e)\r\n- Re-run the Query for each example in S(e) with the new template T to produce a set of old/new tuples S(e, e`)\r\n- Then run evals on all example tuples to get an eval result set S(r, r`)\r\n- Then compare and reduce the result set of old/new evals to check for regressions and produce set S(R)\r\n- Then reduce the set to a single regression result C(R)\r\n\r\n# A/B Tests Definition\r\nA/B tests look like a subset of regression but are slightly different:\r\n- Given a Query Q\r\n- And a set of templates S(T)\r\n- Run the query for each template to get a set of examples S(e)\r\n- Then run evals on the set of examples to get a set of eval results S(r)\r\n- Then reduce the set of eval results to pick a winner W\r\n\r\n# Details\r\n\r\nGoing to focus squarely on the regression test case first, where `Eval` is the generally applicable data model that spans both A/B tests and regression tests. It represents a unit of eval execution, and thus has an execution state and an optional result that is filled in once the eval completes. \r\n\r\nThe `RegressionEval` represents a regression execution unit from a a previously known inference example. It has a `template` and `regressionExample` fields, which are used to generate a new inference example needed for the `newEval` execution. The `oldEval` is an eval execution that simply runs an eval on the `regressionExample` to get a baseline to compare to the `newEval`. \r\n\r\nA `RegressionEval` can be performed on its own, or as a Set. A set of regression evals is handy to group them together as a single execution, where all must pass for an overall regression test to pass. We represent  in the DB using a `RegressionTest`. `RegressionEval`'s have an optional associated `RegressionTest`","mergeCommitSha":"73a061f4ebfa328688dadf4116fba7d75cbaae74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9240","title":"Eval data models","createdAt":"2023-11-14T20:04:15Z"}
{"state":"Merged","mergedAt":"2023-11-23T20:45:56Z","number":9241,"body":"These endpoints can have the following pusher channels if we want to keep these lists live:\r\n\r\n* `/questions`\r\n* `/questions/mine` \r\n","mergeCommitSha":"0011bd897f4bd5e928f195df333b3b5bc594dd50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9241","title":"Add Team Questions and My Questions endpoints","createdAt":"2023-11-14T20:10:51Z"}
{"state":"Merged","mergedAt":"2023-11-14T21:01:04Z","number":9242,"mergeCommitSha":"a56cd69c6d06b69795035e499b11064f81404604","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9242","title":"First stab at changes","createdAt":"2023-11-14T20:34:29Z"}
{"state":"Merged","mergedAt":"2023-11-16T00:23:25Z","number":9243,"body":"Refactor the \"enabled repo\" store.  This used to be called `InstallationRepoStreams`, and was a read-only cache of the latest repos selected by the user.  The actual logic for changing state, saving, etc, was separated out into each view.\r\n\r\nIn this PR I refactored this so the logic is now held in the new `EnabledRepoStore` class.  This class fetches the selected repos, tracks selected/unselected, and applies changes to the service.  There are two variants, one using the installation ID (pre-team-creation) and one using the team ID (post-team-creation).\r\n\r\nThe point of this is to factor all the code so that repo maintenance is done by `EnabledRepoStore` and `RepoTable`, so when we add this to GitHub, we don't have to duplicate all the logic in another place.","mergeCommitSha":"98baa73a19170019dfb69e4531ac739a632381fc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9243","title":"Refactor EnabledRepoStore","createdAt":"2023-11-14T21:45:19Z"}
{"state":"Merged","mergedAt":"2023-11-14T23:50:04Z","number":9244,"body":"<img width=\"669\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/b80a0821-8f76-4b25-b646-2b73c252e0fa\">\r\n\r\nOnly styled for web right now; unless specified, tabs otherwise default to the `primary` variant.","mergeCommitSha":"1ad672174647e9dcf34d53fdeef6d96e425cedec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9244","title":"Add secondary tab variant","createdAt":"2023-11-14T22:11:24Z"}
{"state":"Merged","mergedAt":"2023-11-17T05:51:23Z","number":9245,"mergeCommitSha":"0011463467c49ff50512d733b8e4b926562d52d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9245","title":"Eval stores","createdAt":"2023-11-14T22:15:38Z"}
{"state":"Merged","mergedAt":"2023-11-15T01:53:11Z","number":9246,"body":"Given a signed in user, we used to return all team memberships associated with all SCMs for that user.\n\nThis has been changed so that we only return team memberships associated with the SCM that user is logged in with.","mergeCommitSha":"dbea92bc2d1313601bdcdcd114f9ed07ccf38efc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9246","title":"API GET person returns memberships only for the current auth session","createdAt":"2023-11-14T23:24:10Z"}
{"state":"Merged","mergedAt":"2023-11-14T23:57:08Z","number":9247,"mergeCommitSha":"993fa206e88409c0daa5faf3767c1209fb4e7a15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9247","title":"Add user details to integration slack message","createdAt":"2023-11-14T23:25:38Z"}
{"state":"Merged","mergedAt":"2023-11-15T00:00:05Z","number":9248,"body":"This allows the header and footer to behave as expected without any hacks.","mergeCommitSha":"cd533cf3f224dcd7e3c86b9d846f0b96ba4625e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9248","title":"Fix LoginNavigator scrolling","createdAt":"2023-11-14T23:48:20Z"}
{"state":"Merged","mergedAt":"2023-11-15T08:50:17Z","number":9249,"mergeCommitSha":"64899676cf5a6bae5246f1ee3d35582af790b6b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9249","title":"Slack auth","createdAt":"2023-11-15T00:37:58Z"}
{"state":"Merged","mergedAt":"2022-04-19T15:51:18Z","number":925,"body":"Removed old office IP addresses and added the new one. ","mergeCommitSha":"15715037ce04a79278c621de968fb6547405d0fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/925","title":"update office IP in waf whitelist","createdAt":"2022-04-19T15:50:08Z"}
{"state":"Merged","mergedAt":"2023-11-15T01:57:27Z","number":9250,"mergeCommitSha":"b9974e244ba8d1f21eb748fc0d1b2c5c29f7c063","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9250","title":"Fix listInstallations isPersonalAccount, which was always false","createdAt":"2023-11-15T01:45:18Z"}
{"state":"Merged","mergedAt":"2023-11-15T02:40:39Z","number":9251,"mergeCommitSha":"541d6b90b92e694a211dfb8d5b1523db4b403a04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9251","title":"Update privacy policy","createdAt":"2023-11-15T02:38:07Z"}
{"state":"Merged","mergedAt":"2023-11-15T02:44:22Z","number":9253,"mergeCommitSha":"79af40ddd0753f5b96a9939b27408a4496fc08e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9253","title":"Fix prettier","createdAt":"2023-11-15T02:44:08Z"}
{"state":"Merged","mergedAt":"2023-11-15T04:24:38Z","number":9254,"mergeCommitSha":"bc7f1c13a04b9cdac3dd19a9c34951bec4a29a63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9254","title":"Fix web onboarding when no Bitbucket Workspaces","createdAt":"2023-11-15T03:18:42Z"}
{"state":"Merged","mergedAt":"2023-11-17T06:23:21Z","number":9255,"mergeCommitSha":"7c16a9c3115a774ee6945a1d563eff8de7092c79","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9255","title":"Add global regression test flag to inference example model","createdAt":"2023-11-15T04:54:49Z"}
{"state":"Merged","mergedAt":"2023-11-17T06:48:38Z","number":9256,"mergeCommitSha":"456846af920d7a84963cbc2b3a89c7a331e84ed1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9256","title":"Add eval template","createdAt":"2023-11-15T05:14:44Z"}
{"state":"Merged","mergedAt":"2023-11-17T08:33:06Z","number":9257,"mergeCommitSha":"69cf4ba827d0e5b3521acdcd743d2a1cab860bbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9257","title":"Regression test service to create global regression test models in one shot","createdAt":"2023-11-15T05:49:49Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:41:19Z","number":9258,"mergeCommitSha":"5c466df57a484bf8a26b6241a2e07937e1de59ce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9258","title":"Update usages of isUserSelected and isScmConnected","createdAt":"2023-11-15T06:24:29Z"}
{"state":"Merged","mergedAt":"2023-11-15T10:05:35Z","number":9259,"mergeCommitSha":"5c18883cdf7f9b6d4a28adcc96c5efc9d4ceca1c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9259","title":"Fix casing","createdAt":"2023-11-15T08:54:59Z"}
{"state":"Merged","mergedAt":"2022-04-19T17:52:22Z","number":926,"body":"The `recalculateMarkForCommit` method was not reentrant, because concurrent\r\nexecutions would mutate the unsafe shared `mark.sourcePoints` array.\r\n\r\nThis addresses the error:\r\n```\r\nfromPoint does not exist, should never happen\r\n```","mergeCommitSha":"e386765b48a490886540dd420ade3c37ecf46dad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/926","title":"Fix concurrent race condition in sourcepoint calc","createdAt":"2022-04-19T16:00:04Z"}
{"state":"Merged","mergedAt":"2023-11-15T17:24:59Z","number":9260,"mergeCommitSha":"461a7649d51deedbc58a1494c3eb2f4caafa3a91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9260","title":"Repo pages shows both isUserSelected and isScmConnected","createdAt":"2023-11-15T17:17:48Z"}
{"state":"Merged","mergedAt":"2023-11-15T19:49:20Z","number":9261,"body":"(Safari) bug: https://chapter2global.slack.com/archives/C02US6PHTHR/p1700022108546989","mergeCommitSha":"f0597c302bdb49e4d5a5d40ea886e258fad256ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9261","title":"Update threads list key","createdAt":"2023-11-15T18:56:38Z"}
{"state":"Merged","mergedAt":"2023-11-15T20:55:28Z","number":9262,"mergeCommitSha":"86f5146f565c58773f397879a2b43a8d969e76f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9262","title":"Add tests","createdAt":"2023-11-15T19:53:14Z"}
{"state":"Merged","mergedAt":"2023-11-15T20:06:11Z","number":9263,"mergeCommitSha":"fcb44bae34ad0557d792e82f15416f1608466215","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9263","title":"Always pull latest local context through, and preserve all active files","createdAt":"2023-11-15T19:54:01Z"}
{"state":"Merged","mergedAt":"2023-11-16T05:06:17Z","number":9264,"mergeCommitSha":"fe62c96f63ac42c61e371e9150d247e2004614d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9264","title":"SCM events that are cancelled or fail should be retried","createdAt":"2023-11-15T20:09:55Z"}
{"state":"Merged","mergedAt":"2023-11-15T23:11:14Z","number":9265,"body":"REF IDs for all active local docs were (REF-0), which confused the reference extractor resulting in indeterministic selection of documents. ","mergeCommitSha":"187807bf3da31d60d0d37a2e2077ad0ee73af4c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9265","title":"Fix duplicate ref ids for active docs","createdAt":"2023-11-15T20:48:33Z"}
{"state":"Merged","mergedAt":"2023-11-15T22:48:10Z","number":9266,"mergeCommitSha":"578448abd44dc06ca72c23c40093c420dc7f2e09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9266","title":"Add logging to patchInstallation to debug repo selection race","createdAt":"2023-11-15T22:15:25Z"}
{"state":"Merged","mergedAt":"2023-11-15T23:10:41Z","number":9267,"mergeCommitSha":"18b30e9623a4677cef85096d143e346161a33c97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9267","title":"Center LoginNavigator children that require it","createdAt":"2023-11-15T22:42:30Z"}
{"state":"Merged","mergedAt":"2023-11-15T23:23:34Z","number":9268,"mergeCommitSha":"5c21d59f0c5d90fede0c2a7e6a6c4106d2bf34b5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9268","title":"Add Matt's test BB org to dev","createdAt":"2023-11-15T22:54:37Z"}
{"state":"Merged","mergedAt":"2023-11-15T23:56:14Z","number":9269,"mergeCommitSha":"f6195139c33f8155721e1562ddb2c902fcde74b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9269","title":"Fix de-select / re-select repo race","createdAt":"2023-11-15T23:52:50Z"}
{"state":"Merged","mergedAt":"2022-04-19T19:13:24Z","number":927,"body":"Right now this is needed for the VSCode SourceMark store, but I think more stores will use this eventually, as it's more efficient when the end targets are able to deal with diffs.","mergeCommitSha":"06aa264cf086bf33820e40e506aadaeb2fdb8dc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/927","title":"Add option for DataCacheStream to only send diffs","createdAt":"2022-04-19T18:37:49Z"}
{"state":"Merged","mergedAt":"2023-11-16T00:12:43Z","number":9270,"mergeCommitSha":"42708d508f7aa89f2f66b029a1469e4cc8a59326","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9270","title":"Missed these two","createdAt":"2023-11-16T00:00:29Z"}
{"state":"Merged","mergedAt":"2023-11-16T01:51:24Z","number":9271,"body":"The logs are hard to interrpret right now due to the progress information, but it appears (I think) that clone failures are not retried.","mergeCommitSha":"3d5b319e120b973d0d9384f2e3b94e4a9f5fa4f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9271","title":"Code ingestion should retry Git clone","createdAt":"2023-11-16T00:38:42Z"}
{"state":"Merged","mergedAt":"2023-11-16T01:22:59Z","number":9272,"mergeCommitSha":"6f5bdad8ea6f06558ce67bcd5e465627e900ceee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9272","title":"Slack channel changes","createdAt":"2023-11-16T00:56:56Z"}
{"state":"Merged","mergedAt":"2023-11-16T19:52:40Z","number":9273,"body":"Update RepoTable to match new designs:\r\n* Remove grouping\r\n* Add checkbox on/off designs\r\n* Add `Select All` / `Clear All` header item\r\n* When all repos are selected, send 'undefined' for the repo connection list.  This will ensure new repos are selected in the future.\r\n* Default sort is now by selection\r\n\r\n![CleanShot 2023-11-15 at 16 56 44@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/a3ab54d7-8446-40fa-b569-1713b8699af4)\r\n","mergeCommitSha":"b41339a13c33317529a30e6523c665a65027ba5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9273","title":"Update RepoTable UI","createdAt":"2023-11-16T00:59:08Z"}
{"state":"Merged","mergedAt":"2023-11-16T01:55:27Z","number":9274,"mergeCommitSha":"ec2f5c104e872910ed72fcb76b801e73261c6cbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9274","title":"Fix previous active docs being filtered","createdAt":"2023-11-16T01:32:18Z"}
{"state":"Merged","mergedAt":"2023-11-16T04:06:14Z","number":9275,"mergeCommitSha":"437212a5753eeb1e4498ab1eefcf14e313119a6f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9275","title":"ADd oauth exceptions","createdAt":"2023-11-16T02:38:36Z"}
{"state":"Merged","mergedAt":"2023-11-16T17:49:40Z","number":9276,"mergeCommitSha":"601b3b334cab743ce3abd43b13c3e33cd00561b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9276","title":"Try metrics change","createdAt":"2023-11-16T17:43:53Z"}
{"state":"Merged","mergedAt":"2023-11-16T20:42:55Z","number":9277,"mergeCommitSha":"0c479496eb90f56ad3f3dff84adb3d3965795cca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9277","title":"Show slack auth error (required)","createdAt":"2023-11-16T17:54:21Z"}
{"state":"Merged","mergedAt":"2023-11-16T21:07:15Z","number":9278,"mergeCommitSha":"aadfe79a6a35cbb68343b5fb92c9b2ef553eaeb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9278","title":"Add environment name to admin web tabs etc","createdAt":"2023-11-16T18:32:54Z"}
{"state":"Merged","mergedAt":"2023-11-16T19:59:24Z","number":9279,"mergeCommitSha":"d1b6f7d4d0decac198cf20d363a6a1c8298b4c67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9279","title":"Fix build due to OpenAI kotlin","createdAt":"2023-11-16T19:37:07Z"}
{"state":"Merged","mergedAt":"2022-04-19T21:26:39Z","number":928,"body":"Will be used for getting new pull request comments after the initial ingestion","mergeCommitSha":"4896033e964c36cd257385756526f70264e67a2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/928","title":"Add ability to get pull requests with etag","createdAt":"2022-04-19T18:55:49Z"}
{"state":"Merged","mergedAt":"2023-11-16T21:04:02Z","number":9280,"mergeCommitSha":"0c378b0bf9dd29625efcb982712655012ff3dfdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9280","title":"Bye for now","createdAt":"2023-11-16T21:03:56Z"}
{"state":"Merged","mergedAt":"2023-11-16T21:45:38Z","number":9281,"mergeCommitSha":"b936f3d8caac08cd9a8b8fd5f4be19faaff1c247","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9281","title":"Make env name uppercase","createdAt":"2023-11-16T21:30:27Z"}
{"state":"Merged","mergedAt":"2023-11-17T00:56:13Z","number":9282,"mergeCommitSha":"90e80c5c2fa60b8937142a4b0f5914f9a742cdf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9282","title":"Error on Slck Auth Install","createdAt":"2023-11-16T21:33:10Z"}
{"state":"Merged","mergedAt":"2023-11-17T08:55:56Z","number":9283,"mergeCommitSha":"a02c80b7e1c0acf14788877842db195498d37c15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9283","title":"Eval service","createdAt":"2023-11-16T21:34:04Z"}
{"state":"Merged","mergedAt":"2023-11-16T23:29:38Z","number":9284,"mergeCommitSha":"d8bb6d41fe93a47be5b40b72b8bdb2a4ce030074","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9284","title":"Repo selection during onboarding is stateful by using getTeamStatus repoSelectionNeeded","createdAt":"2023-11-16T23:07:29Z"}
{"state":"Closed","mergedAt":null,"number":9285,"mergeCommitSha":"e0639160e794944697a27c3f2d625de20810d3b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9285","title":"Add FVT endpoint to API service","createdAt":"2023-11-17T00:12:10Z"}
{"state":"Merged","mergedAt":"2023-11-17T01:12:46Z","number":9286,"body":"adds a call to the topic summary service when topic is moved to recommended.","mergeCommitSha":"5eeb2c8e3afaae7bd69f72290334c166bf1c3db9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9286","title":"Generate summaries automatically for recommended topics","createdAt":"2023-11-17T00:56:12Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:42:04Z","number":9287,"mergeCommitSha":"fd6259aebd49680a76ec397eae84dc23cc521e89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9287","title":"Implement repoSelectionNeeded in getTeamStatus","createdAt":"2023-11-17T01:38:42Z"}
{"state":"Merged","mergedAt":"2023-11-17T08:06:50Z","number":9288,"body":"Ticking time bomb, every day an hour before midnight after standard time shift :) ","mergeCommitSha":"6631a17fd22f5f0ffefef0e65d49e12030f87411","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9288","title":"Fix time dependent test","createdAt":"2023-11-17T07:44:44Z"}
{"state":"Merged","mergedAt":"2023-11-17T16:27:45Z","number":9289,"mergeCommitSha":"8747cc967239ac20df9699f8e5165642f625ca8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9289","title":"Forgot the code","createdAt":"2023-11-17T16:02:44Z"}
{"state":"Merged","mergedAt":"2022-04-21T18:18:59Z","number":929,"mergeCommitSha":"316c19bf249027c7f2f906c10df43f3f261d9fbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/929","title":"Add hub app","createdAt":"2022-04-19T18:58:23Z"}
{"state":"Merged","mergedAt":"2023-11-17T18:44:04Z","number":9290,"mergeCommitSha":"a95b5fa4d64a40a17e87d614600decba899da450","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9290","title":"Submit when checkbox changes on global example setting","createdAt":"2023-11-17T18:43:38Z"}
{"state":"Merged","mergedAt":"2023-11-17T18:57:57Z","number":9291,"mergeCommitSha":"0b754d7fd33c83421c8e95bde8114aa7c40f8663","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9291","title":"Fix impersonation for user that does not have unblocked account","createdAt":"2023-11-17T18:48:09Z"}
{"state":"Merged","mergedAt":"2023-11-17T19:52:32Z","number":9292,"body":"- More slack impersonation fixes\r\n- Fix richie comments\r\n","mergeCommitSha":"c55d7dad7a07f46669297b06ec5fe876e7efd8e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9292","title":"FixTypo","createdAt":"2023-11-17T19:41:10Z"}
{"state":"Merged","mergedAt":"2023-11-18T09:01:27Z","number":9293,"mergeCommitSha":"6d9a569e225f45a1c432da9b37fc05b4deb045b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9293","title":"Fix slack customer integration message","createdAt":"2023-11-17T20:01:48Z"}
{"state":"Merged","mergedAt":"2023-11-18T00:23:44Z","number":9294,"mergeCommitSha":"18439d831eb71989c53f504932a81e962bd65358","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9294","title":"Utils for eval rendering","createdAt":"2023-11-18T00:00:38Z"}
{"state":"Merged","mergedAt":"2023-11-20T18:59:12Z","number":9295,"body":"* Needed to update some typing and integrate the new `useBlocker` hook for compatibility with the new version (removed our old custom hook as a consequence)\r\n* Updated main routes back to `/discussions/` routing and added backwards compatibility/redirects","mergeCommitSha":"dcba6144daead7d930c87b16577035522d6e73f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9295","title":"Update react router to 6.19.0","createdAt":"2023-11-18T00:33:10Z"}