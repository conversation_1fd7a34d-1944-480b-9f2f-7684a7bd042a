{"state":"Closed","mergedAt":null,"number":7432,"mergeCommitSha":"427809a101c0b77f72f39f1e8af4d4c42ebbe0fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7432","title":"Prompt metadata plumbing","createdAt":"2023-08-08T16:58:59Z"}
{"state":"Merged","mergedAt":"2023-08-14T20:26:54Z","number":7433,"body":"This mirrors Jira configuration apis, except instead of projects we have spaces.","mergeCommitSha":"cf5e4b7604a7b2da6b384aa1c61a93ee268b2fba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7433","title":"Add Confluence configuration apis","createdAt":"2023-08-08T18:06:54Z"}
{"state":"Merged","mergedAt":"2023-08-08T19:50:58Z","number":7434,"mergeCommitSha":"388440cf6ca62094fd3b546b78dc0136b9c85cdb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7434","title":"Add Confluence blog post apis","createdAt":"2023-08-08T19:02:03Z"}
{"state":"Merged","mergedAt":"2023-08-08T19:36:01Z","number":7435,"body":"Fix issue with QA on empty file page.\r\n\r\n1. Was using the repoID instead of the teamID when missing teamID at QA init.\r\n2. TeamMember streams may not be fully initialized at time of QA. Fallback to directly fetching team members.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/b3293a91-ff07-4789-8eed-faa68f58e6f6\r\n\r\n","mergeCommitSha":"57eeaf8bce6a7fcbafc187348a9969d8dc7aa286","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7435","title":"Handle asking QA on empty file page","createdAt":"2023-08-08T19:06:50Z"}
{"state":"Merged","mergedAt":"2023-08-08T21:21:28Z","number":7436,"mergeCommitSha":"78aef50320ecd7f9dbd4e87662d4f7127e62cd0e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7436","title":"Add Confluence store functions to configure spaces for ingestion","createdAt":"2023-08-08T20:45:11Z"}
{"state":"Merged","mergedAt":"2023-08-08T21:12:35Z","number":7437,"mergeCommitSha":"53814d3e618c981966a7f24752a3c6218426bfdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7437","title":"Thread block should update thread unread","createdAt":"2023-08-08T21:01:18Z"}
{"state":"Merged","mergedAt":"2023-08-08T23:23:35Z","number":7438,"body":"This pr adds a few things:\r\n1. Infrastructure setup for confluence documentation, including GLUE jobs to ingest confluence data and produce streamlined output that we can distribute on.\r\n2. Basic skeleton for consuming confluence documentation and generating embeddings.","mergeCommitSha":"c8f5f8effef4c70cda2d1c26c9e021d0557c57e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7438","title":"Adding confluence skeelton","createdAt":"2023-08-08T22:51:28Z"}
{"state":"Merged","mergedAt":"2022-04-01T18:41:36Z","number":744,"body":"Adds a new bidirectional streaming rpm endpoint called `token`. The approach:\r\n\r\n1) Client will open a stream to `token` by sending an initial `Token` message to the server\r\n2) `SourceMarkServer` receives the request and then \"saves\" the stream by registering it as a token provider in `TokenManager`\r\n3) When the source mark API client needs a token, it will request one from the `TokenManager` and the `TokenManager` will iterate through each provider and return the first one that gives back a token.\r\n\r\nNot the nicest code, suggestions welcome.","mergeCommitSha":"2caba1bbf7052cf5f8af67d5195279aff2d04a67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/744","title":"Add ability to get the auth token from one of the extensions","createdAt":"2022-03-31T23:11:39Z"}
{"state":"Merged","mergedAt":"2023-08-09T17:03:14Z","number":7440,"body":"Confluence state machine provider and other contents.","mergeCommitSha":"0d120fd8ad8726155b3e610d517ed63125b7d396","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7440","title":"Add ability to trigger confluence from admin console","createdAt":"2023-08-09T01:12:40Z"}
{"state":"Open","mergedAt":null,"number":7441,"body":"Let's make the search service do less work","mergeCommitSha":"bcad381c157c3ce154f61016773cdb1ff9221278","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7441","title":"Run search indexing at the end of bulk ingestion","createdAt":"2023-08-09T04:10:59Z"}
{"state":"Merged","mergedAt":"2023-08-09T18:27:14Z","number":7442,"mergeCommitSha":"960657192e9b875192fb426219749780ad155c7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7442","title":"reduce max scale on search service to avoid overloading db","createdAt":"2023-08-09T08:50:54Z"}
{"state":"Merged","mergedAt":"2023-08-09T17:41:48Z","number":7443,"body":"This is a _cure_ for the problem.\n\nThe _prevention_ is to nuke repo embeddings on repo uninstall, which I will do next.","mergeCommitSha":"cd54a9d69a8742cd6c5e4ff38d698279a5b414f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7443","title":"Do not crash when semantic retrieval returns an uninstalled repo","createdAt":"2023-08-09T17:08:45Z"}
{"state":"Merged","mergedAt":"2023-08-09T19:43:19Z","number":7444,"mergeCommitSha":"a313aff42b595a9d4aed8f4019ffaf46db84951a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7444","title":"Fix build","createdAt":"2023-08-09T18:28:28Z"}
{"state":"Merged","mergedAt":"2023-08-09T19:15:52Z","number":7445,"mergeCommitSha":"eab7db1e967e57f2ab3f09929aa8565913cb8eef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7445","title":"Add logging to PullRequestBulkIngestionService","createdAt":"2023-08-09T18:39:37Z"}
{"state":"Merged","mergedAt":"2023-08-09T22:36:43Z","number":7446,"body":"onboarding:\r\n<img width=\"1346\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/3fce99b7-1316-439f-af17-02e4cd218f8c\">\r\n<img width=\"1483\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/05da9562-2696-403d-b88b-5d3fcf9e1860\">\r\n\r\nsettings:\r\n<img width=\"1486\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/b932eeca-2179-4899-8c53-b5244cdb8d23\">\r\n","mergeCommitSha":"ac6c13419a5df1d9fd65b5e04fe66d518ad4c605","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7446","title":"Add GitHub config UI","createdAt":"2023-08-09T20:25:33Z"}
{"state":"Merged","mergedAt":"2023-08-09T21:03:33Z","number":7447,"mergeCommitSha":"e05a5090a921edfdb870ea49364d4ab6ad18c049","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7447","title":"Extend bulk ingestion lock expiration to 5 minutes","createdAt":"2023-08-09T20:29:40Z"}
{"state":"Merged","mergedAt":"2023-08-09T21:34:55Z","number":7448,"mergeCommitSha":"daf37fa7dd88a89bb7779e982aa090115ff3f5e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7448","title":"Hard delete uninstalled repos","createdAt":"2023-08-09T20:48:28Z"}
{"state":"Merged","mergedAt":"2023-08-09T20:55:07Z","number":7449,"mergeCommitSha":"95f004ed04284603cc8683e695afc47c3944dbd2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7449","title":"Update slack configuration","createdAt":"2023-08-09T20:54:59Z"}
{"state":"Merged","mergedAt":"2022-03-31T23:17:43Z","number":745,"mergeCommitSha":"ab12c4dcf38db45b3ae9ed45ba549a5e3395b750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/745","title":"Add ability to generate auth token for post requests","createdAt":"2022-03-31T23:14:15Z"}
{"state":"Merged","mergedAt":"2023-08-09T22:22:14Z","number":7451,"mergeCommitSha":"8e90980288c1ca91d0a0abc965b2a655bdfa57bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7451","title":"Fix Kotlin enum entries warnings in 1.9","createdAt":"2023-08-09T20:57:34Z"}
{"state":"Merged","mergedAt":"2023-08-10T03:19:38Z","number":7452,"body":"* Topic suggestion rows should link to the populated topic view\r\n* Enable adding a component as long as there is a `name`\r\n* Rename `topic` to `component` (some missed instances)","mergeCommitSha":"ad007419e5a380e4b8091ed15975793bb5913475","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7452","title":"Fix adding topics on dashboard","createdAt":"2023-08-09T21:20:08Z"}
{"state":"Merged","mergedAt":"2023-08-10T18:09:03Z","number":7454,"mergeCommitSha":"1e4311f5ecf8e860eb989ac64e3ea322db6c5194","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7454","title":"Refresh the thread list after marking read status","createdAt":"2023-08-09T22:28:14Z"}
{"state":"Merged","mergedAt":"2023-08-09T22:38:05Z","number":7455,"mergeCommitSha":"cd00ae4610f86221bc88ad5412be0fc66a3694dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7455","title":"Update image for confluence","createdAt":"2023-08-09T22:37:55Z"}
{"state":"Merged","mergedAt":"2023-08-09T23:15:30Z","number":7456,"mergeCommitSha":"0d5ce8b5a31e431becb62a91d5a07f0a019f8f57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7456","title":"Stop running dry-run refernce generation","createdAt":"2023-08-09T22:50:38Z"}
{"state":"Merged","mergedAt":"2023-08-09T23:24:38Z","number":7457,"body":"Address this ingestion error https://app.logz.io/#/goto/825110b87cc96efed3549323fdbffab9?switchToAccountId=411850","mergeCommitSha":"112e8573c2f598dd8068c1fb0124b7ac3f853096","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7457","title":"Handle missing author for Jira issue comment","createdAt":"2023-08-09T22:58:49Z"}
{"state":"Merged","mergedAt":"2023-08-09T23:07:40Z","number":7458,"mergeCommitSha":"4afea0af8aaa781a8819004dfd5e5656693a341d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7458","title":"Fix topic ingestion priority","createdAt":"2023-08-09T23:07:32Z"}
{"state":"Merged","mergedAt":"2023-08-09T23:22:20Z","number":7459,"mergeCommitSha":"b30001ab2f20787ea2c962b110df29aee62e6bb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7459","title":"Fix confluence payload generation","createdAt":"2023-08-09T23:20:55Z"}
{"state":"Merged","mergedAt":"2022-04-01T18:41:46Z","number":746,"body":"The current unread implementation works well for the my discussions mode, and it was largely not a huge problem to implement it for pusher.\r\n\r\nTESTING:\r\n1. Local stack testing with Kay’s stuff. Works well for my discussions. Used a series of curl commands to add messages as Mahdi. :)\r\n\r\n![output-onlinegiftools](https://user-images.githubusercontent.com/3806658/161199948-91a4761e-ebab-4df5-9126-810515b22c37.gif)\r\n\r\n\r\n","mergeCommitSha":"c3fab6c9b29c2b9f2ee6ee8201682b0fac51f5c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/746","title":"Add basic unread pusher support","createdAt":"2022-04-01T05:09:45Z"}
{"state":"Merged","mergedAt":"2023-08-10T01:24:30Z","number":7460,"mergeCommitSha":"7d25be133935d7565e6b2dcdfb647e2c04ed0f34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7460","title":"Add slack channel attributes we’re interested in","createdAt":"2023-08-09T23:31:26Z"}
{"state":"Merged","mergedAt":"2023-08-10T00:11:31Z","number":7462,"mergeCommitSha":"ae96ac50c8a0fa16f20aba795f7ddc2e76e28cfa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7462","title":"reduce worker max scales","createdAt":"2023-08-10T00:02:33Z"}
{"state":"Merged","mergedAt":"2023-08-10T01:26:16Z","number":7463,"mergeCommitSha":"a4c542b035aa39f6bcdd54f8bf7218e9aa657a56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7463","title":"Add topic experts mapping","createdAt":"2023-08-10T01:25:41Z"}
{"state":"Merged","mergedAt":"2023-08-10T20:06:42Z","number":7464,"body":"During ingestion we create both dense (semantic) and now sparse (lexical) embeddings and upload both to Pinecone.\r\n\r\nhttps://www.notion.so/nextchaptersoftware/Document-retrieval-problem-afa91e6c2feb4d10a113f786f837020a?pvs=4#a8639e75c0004f2bac5d5a5b3df468fc","mergeCommitSha":"4e9f11582e1d70e6faf625b14cd9fe7073676770","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7464","title":"Generate sparse Pinecone embeddings for source code","createdAt":"2023-08-10T04:38:51Z"}
{"state":"Closed","mergedAt":null,"number":7465,"body":"Add feature flag to bypass team pending state.\r\n\r\nWill allow us to impersonate users with teams in a pending state w/o sending notification.","mergeCommitSha":"2b5d5d0f82b52add438fafa12ec61c4902125832","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7465","title":"Bypass pending team check","createdAt":"2023-08-10T04:54:03Z"}
{"state":"Merged","mergedAt":"2023-08-10T05:34:00Z","number":7466,"body":"SCM service has been OOMing for a couple of days.\r\nhttps://getunblocked.grafana.net/d/eIvzXO_7k/service-pod-resources-overview?orgId=1&var-datasource=grafanacloud-getunblocked-prom&var-cluster=prod-us-west-2&var-namespace=default&var-service=scmservice","mergeCommitSha":"ed0190b0d01a0a0ddca92b66f1209a6e5483b737","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7466","title":"add more memory to prod scm service","createdAt":"2023-08-10T05:24:17Z"}
{"state":"Merged","mergedAt":"2023-08-10T07:18:43Z","number":7467,"body":"I am force merging this change to avoid an accidental rollback.","mergeCommitSha":"fa8fe54be6755fd2d841804fffd66ebd2ef7c993","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7467","title":"scale up prod db to xlarge instances","createdAt":"2023-08-10T07:18:35Z"}
{"state":"Merged","mergedAt":"2023-08-10T17:02:46Z","number":7468,"body":"Linux scheduler was killing SCM pods for extended bursting above CPU limit. Increased the CPU limit to 1 core per pod","mergeCommitSha":"c90803df08115fdb5dcb283f012b874f98e7ac99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7468","title":"increase SCM service cpu","createdAt":"2023-08-10T09:30:01Z"}
{"state":"Merged","mergedAt":"2023-08-10T18:01:33Z","number":7469,"mergeCommitSha":"0d5ce06f6a0d4ec84f8403e5c03019caa7bb7724","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7469","title":"Add output format instruction to topics extractor","createdAt":"2023-08-10T15:25:56Z"}
{"state":"Closed","mergedAt":null,"number":747,"body":"not working yet.\r\n\r\nremaining work:\r\n- [ ] gRPC\r\n- [x] ProcessBuilder","mergeCommitSha":"ee93a9958e6da72c449a0cb75b798e644fe54019","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/747","title":"Generate native macOS SM agent binaries","createdAt":"2022-04-01T06:00:51Z"}
{"state":"Merged","mergedAt":"2023-08-10T22:42:14Z","number":7470,"mergeCommitSha":"69de20353367876271639fbb219358271033669d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7470","title":"Sanitize json array output from topics extractor","createdAt":"2023-08-10T15:50:30Z"}
{"state":"Merged","mergedAt":"2023-08-10T19:35:25Z","number":7471,"body":"Remove all from new slack configuration","mergeCommitSha":"4772d5e8f2ad3162b9382fcc0794dc5028aae734","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7471","title":"[BREAKS API ON MAIN] Fix slack API","createdAt":"2023-08-10T16:27:16Z"}
{"state":"Closed","mergedAt":null,"number":7472,"mergeCommitSha":"9d14e9b0aff70939042aa0164e80f820a678fd7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7472","title":"Add delete to ExplainStatementInterceptor","createdAt":"2023-08-10T18:19:54Z"}
{"state":"Merged","mergedAt":"2023-08-10T18:36:02Z","number":7473,"body":"https://app.logz.io/#/goto/4fa29450399ef46ec2936ce3f7398c0e?switchToAccountId=411850","mergeCommitSha":"818aff45126f16dfca0a82dd7184274c517edb3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7473","title":"SlackChannelPreferencesService.shouldIngest should just return false if no preferences found","createdAt":"2023-08-10T18:22:33Z"}
{"state":"Merged","mergedAt":"2023-08-10T18:32:20Z","number":7474,"mergeCommitSha":"0a13b3b320d40dfaaa200e96b6ca677f21485ec9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7474","title":"Increase polling interval","createdAt":"2023-08-10T18:23:40Z"}
{"state":"Merged","mergedAt":"2023-08-10T19:39:05Z","number":7475,"body":"https://chapter2global.slack.com/archives/C05MZR4420Y/p1691689324149319","mergeCommitSha":"a89c54306fe541c911aaf125f96c216e99a14f9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7475","title":"Only generate recommendations for team members that have accounts","createdAt":"2023-08-10T18:44:32Z"}
{"state":"Merged","mergedAt":"2023-08-22T22:42:18Z","number":7476,"mergeCommitSha":"70b6e45bfaf2ab894b3db42e77acc2e478a1871d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7476","title":"Sample questions should emit slack message","createdAt":"2023-08-10T18:58:11Z"}
{"state":"Merged","mergedAt":"2023-08-11T05:41:55Z","number":7477,"mergeCommitSha":"d5b3fbf5be8c41c5193ec6e97235a1268590b2f0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7477","title":"Slack Channel webhooks","createdAt":"2023-08-10T19:34:43Z"}
{"state":"Merged","mergedAt":"2023-08-11T02:06:19Z","number":7478,"body":"- Modified Keda scaler resource to support multiple queue based triggers. \r\n- Modified all values.yaml files to support the new format","mergeCommitSha":"dd3f3f85f9f82faf51cedb15f99ad372652dca90","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7478","title":"Adding support for multiple queue triggers","createdAt":"2023-08-10T19:50:59Z"}
{"state":"Merged","mergedAt":"2023-08-10T21:11:06Z","number":7479,"body":"https://chapter2global.slack.com/archives/C02T1N1LK19/p1691696042825189\r\n\r\nProd admin web keeps getting killed because of exceeding CPU limit. Added more CPU to it.","mergeCommitSha":"1fa1e20d375f20d75bb252c6b5ade3f46764ee52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7479","title":"add more cpu to prod admin web","createdAt":"2023-08-10T19:54:07Z"}
{"state":"Merged","mergedAt":"2022-04-01T19:24:34Z","number":748,"body":"Remove invalid references to id.\r\nCleanup threadId usage.","mergeCommitSha":"67ee6dcd682929f7e152c64c65880fb7be8624c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/748","title":"Clean up yaml specs","createdAt":"2022-04-01T18:50:50Z"}
{"state":"Merged","mergedAt":"2023-08-10T21:10:46Z","number":7480,"body":"This should give us more in depth info about DB performance. \r\n\r\nhttps://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_Monitoring.OS.overview.html","mergeCommitSha":"a7a4cffa800b19890a6649d7fd1be57b6b6d1790","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7480","title":"enable enhanced monitoring in prod RDS","createdAt":"2023-08-10T20:20:43Z"}
{"state":"Merged","mergedAt":"2023-08-10T23:37:53Z","number":7481,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/83248bd0-f955-4a54-a94b-6c7b6be7ffe8\r\n\r\n* Also add support for custom fontawesome kit icons","mergeCommitSha":"4636b63d5dc2faced90fe880c3f23b4498c0d471","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7481","title":"Link to topic insights from Topics table","createdAt":"2023-08-10T21:10:02Z"}
{"state":"Merged","mergedAt":"2023-08-10T23:41:39Z","number":7482,"mergeCommitSha":"8ca4f58167db84ae0fa408a5c271903fddb950e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7482","title":"Make sure that in-prompt references are correctly filled in","createdAt":"2023-08-10T22:31:38Z"}
{"state":"Merged","mergedAt":"2023-08-11T00:31:06Z","number":7483,"body":"We want to send slack shadow answers to a specified customer channel so that they can validate it.","mergeCommitSha":"520f5784e2c481cc0cd26eec5bf416d52c0b7a3a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7483","title":"Slack unbot shadow answers validation","createdAt":"2023-08-10T23:51:55Z"}
{"state":"Merged","mergedAt":"2023-08-11T00:50:03Z","number":7484,"body":"Checks to see if the primary associated member is a current member when determining whether a member is an expert.","mergeCommitSha":"ebd542373578cc8f7ff5d39f2d939f77922a8ec8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7484","title":"Only choose experts from current members","createdAt":"2023-08-11T00:25:16Z"}
{"state":"Merged","mergedAt":"2023-08-11T00:46:11Z","number":7485,"mergeCommitSha":"ab125e42d7bf28912f911b29d3c848927ee48055","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7485","title":"Fix bug in source code partitioning that created empty partitions","createdAt":"2023-08-11T00:41:57Z"}
{"state":"Merged","mergedAt":"2023-08-11T02:48:05Z","number":7486,"mergeCommitSha":"bd6ebf800babb8857cd67ca1957698028857de10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7486","title":"Ignore experts marked as ignoreThreads","createdAt":"2023-08-11T01:38:28Z"}
{"state":"Merged","mergedAt":"2023-08-11T02:54:42Z","number":7487,"mergeCommitSha":"3d10e83f5fa77ca2aa6bce06fc4c7762fd5e5859","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7487","title":"Discard producer flow control","createdAt":"2023-08-11T02:54:36Z"}
{"state":"Merged","mergedAt":"2023-08-11T03:17:53Z","number":7488,"mergeCommitSha":"2c4e29c72092ce8e6ecf2fda1655d1a321430429","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7488","title":"Show number of pull requests authored by team member","createdAt":"2023-08-11T03:06:48Z"}
{"state":"Merged","mergedAt":"2023-08-11T05:14:44Z","number":7489,"mergeCommitSha":"635b0735ee7b242c5b2880ebe307dcb1d8ec3ec4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7489","title":"Remove admin input box autofocus","createdAt":"2023-08-11T05:14:23Z"}
{"state":"Merged","mergedAt":"2022-04-01T21:27:57Z","number":749,"body":"This pr does two things:\r\n1. Makes sure all DAO classes are properly named.\r\n2. Add abiltity to ktlint rules to ignore files/packages (i.e. test)\r\n\r\nPretty much all boilerplate outside of ktlint custom rules changes...","mergeCommitSha":"a9cb99cf842300f0f25fc9d986920814ea402d8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/749","title":"Fix up dao naming and lint rules","createdAt":"2022-04-01T21:18:35Z"}
{"state":"Merged","mergedAt":"2023-08-11T05:39:56Z","number":7490,"body":"The intention of the rule was to focus on slow API operations mainly.\r\n\r\nBut background jobs — which are expected to be slow — ended up dominating:\r\nhttps://ui.honeycomb.io/unblocked/environments/production/result/AQfp5yHiVMM?useStackedGraphs","mergeCommitSha":"fa52c3162470bf15a39730c6b02cc11be85e8e66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7490","title":"Adjust refinery slow sampler to run for http requests only","createdAt":"2023-08-11T05:36:30Z"}
{"state":"Merged","mergedAt":"2023-08-11T05:40:18Z","number":7491,"mergeCommitSha":"86da7d696dad7456327eb7f7004a487797f89e72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7491","title":"Adding clustered protoytpe","createdAt":"2023-08-11T05:39:59Z"}
{"state":"Merged","mergedAt":"2023-08-11T06:30:47Z","number":7492,"mergeCommitSha":"42fb09306e7bdb17e42d2acdccef21dd397b5571","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7492","title":"Trigger repo delete on uninstall + button to backfill","createdAt":"2023-08-11T05:50:43Z"}
{"state":"Merged","mergedAt":"2023-08-22T18:39:57Z","number":7493,"body":"Setup work for new Slack UI Base.\r\nContains some shared components that should be used with new settings.\r\n\r\nAPI still using mock data while backend implemented. DO NOT MERGE.\r\n\r\n<img width=\"898\" alt=\"CleanShot 2023-08-10 at 22 47 54@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/ef0fbfc1-1f5a-43fc-85e4-06e56afe8a37\">\r\n","mergeCommitSha":"70378d2fad10f6e98b6efc3190afaf705d06584c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7493","title":"[DO NOT MERGE] Slack UI base","createdAt":"2023-08-11T05:54:51Z"}
{"state":"Merged","mergedAt":"2023-08-11T15:28:04Z","number":7494,"body":"So that counts for Jira team member are combined with the primary (SCM) team member.","mergeCommitSha":"e63c40927261ba2cd4377ca503454100f4f34fe9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7494","title":"Combine associated member counts with primary member when calculating experts","createdAt":"2023-08-11T05:58:20Z"}
{"state":"Merged","mergedAt":"2023-08-11T06:19:48Z","number":7495,"body":"This could take a while for a user that has many threads.","mergeCommitSha":"d9e0deaf523f286bb8c7dd56fe9484f5719db7b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7495","title":"Launch admin console function in a coroutine","createdAt":"2023-08-11T06:05:10Z"}
{"state":"Merged","mergedAt":"2023-08-11T06:25:18Z","number":7496,"mergeCommitSha":"083ded51f8717a109841548fc31b62fd4b96508c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7496","title":"increas esize","createdAt":"2023-08-11T06:23:54Z"}
{"state":"Merged","mergedAt":"2023-08-11T06:53:11Z","number":7497,"mergeCommitSha":"f0ae92ab8d0a733376aa64ec08519dbf8bb25d39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7497","title":"Alter scaling","createdAt":"2023-08-11T06:53:05Z"}
{"state":"Merged","mergedAt":"2023-08-11T07:03:56Z","number":7498,"mergeCommitSha":"83b934f8b148767f5b93701420a38ab86b40d928","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7498","title":"Change priorities","createdAt":"2023-08-11T07:03:05Z"}
{"state":"Merged","mergedAt":"2023-08-11T07:18:18Z","number":7499,"body":"Going to force merge this change to avoid accidental rollbacks","mergeCommitSha":"7dce04c89a892328264c901f35fcd9d53c8ea4ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7499","title":"resize prod activemq instance","createdAt":"2023-08-11T07:18:11Z"}
{"state":"Merged","mergedAt":"2022-01-19T17:58:37Z","number":75,"body":"These don't come with the Exposed framework, probably because they're Postgres-specific. Adding these now so that we can drop them in later when we create the models.\r\n\r\nhttps://www.postgresql.org/docs/9.6/textsearch.html","mergeCommitSha":"853660351ce1c6f7f81247f10f4ad8a26c3f22ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/75","title":"Add custom type, function, and op for full text search","createdAt":"2022-01-19T05:04:37Z"}
{"state":"Merged","mergedAt":"2022-04-01T21:31:51Z","number":750,"mergeCommitSha":"29f91416d020b973ea84f2707432a39ea83b400b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/750","title":"Fix scheduler bug and remove unnecessary fields","createdAt":"2022-04-01T21:21:38Z"}
{"state":"Merged","mergedAt":"2023-08-11T07:37:11Z","number":7500,"mergeCommitSha":"baad1dbf4a819d02194fc17d57ce30c929eef9c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7500","title":"Use aws recommendations fo rslow consumers","createdAt":"2023-08-11T07:37:05Z"}
{"state":"Merged","mergedAt":"2023-08-11T08:05:04Z","number":7501,"mergeCommitSha":"02d33e16fa176b9bcc4789e1267fec9c633ca19f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7501","title":"Search bot events","createdAt":"2023-08-11T08:03:21Z"}
{"state":"Merged","mergedAt":"2023-08-11T08:33:48Z","number":7502,"mergeCommitSha":"e3261e05eeec6deb3042e50da66e35fbb409621a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7502","title":"Serializer","createdAt":"2023-08-11T08:32:59Z"}
{"state":"Merged","mergedAt":"2023-08-11T18:17:34Z","number":7503,"mergeCommitSha":"f0cdc291684488c11e6bef172ea475386cc3dfef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7503","title":"Whitelist fontawesome kit domain","createdAt":"2023-08-11T17:25:41Z"}
{"state":"Merged","mergedAt":"2023-08-11T17:44:10Z","number":7504,"mergeCommitSha":"558d6507aff004071d1c80fa7113f89f05358f93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7504","title":"Update refinery","createdAt":"2023-08-11T17:43:30Z"}
{"state":"Merged","mergedAt":"2023-08-12T19:25:04Z","number":7505,"mergeCommitSha":"4f7ad8e7b3f4b478e95fe1653dd419d2cd57bf77","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7505","title":"Added timer to topic classifier","createdAt":"2023-08-11T17:43:36Z"}
{"state":"Merged","mergedAt":"2023-08-11T19:17:29Z","number":7506,"body":"Fixes issue where @mentions in Jira were not being displayed properly\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1691766588868659\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1691766626477799","mergeCommitSha":"aebc0f078ee247634fccfa4d8744b95366ad34df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7506","title":"Parse mentions in Jira issues","createdAt":"2023-08-11T18:32:40Z"}
{"state":"Merged","mergedAt":"2023-08-11T19:14:29Z","number":7507,"mergeCommitSha":"af98843706eb3795f30e17a6088f26c18de5043e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7507","title":"Make slack events processing sequential","createdAt":"2023-08-11T19:14:10Z"}
{"state":"Merged","mergedAt":"2023-08-11T19:17:50Z","number":7508,"mergeCommitSha":"d434a66561f6b6a3b5baa2373a8ed87d3a90636c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7508","title":"Remove ocncurrent batch","createdAt":"2023-08-11T19:17:29Z"}
{"state":"Merged","mergedAt":"2023-08-14T16:27:41Z","number":7509,"body":"Previous implementation only applied response reference extraction to cases where in-prompt referencing yielded results. \r\n\r\nBut it doesn't make sense to couple these things. A \"Response Reference Resolver\" is just another resolver.","mergeCommitSha":"20efc681ae0d1d4cc01a63be69449aa4dc22aec2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7509","title":"Always scrape the response for references","createdAt":"2023-08-11T19:48:38Z"}
{"state":"Merged","mergedAt":"2022-04-01T21:34:00Z","number":751,"body":"Merge issue. Missed required delete handler","mergeCommitSha":"69f52a0d5a016aec87069a430ce6100590115547","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/751","title":"Fix Extension build","createdAt":"2022-04-01T21:24:21Z"}
{"state":"Merged","mergedAt":"2023-08-11T20:17:31Z","number":7510,"mergeCommitSha":"8ebedf37d046c48bf219af0d937a86d9470a433f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7510","title":"Add ability to reset jira ingestion from admin console","createdAt":"2023-08-11T19:52:09Z"}
{"state":"Merged","mergedAt":"2023-08-11T20:17:24Z","number":7511,"mergeCommitSha":"9602619236862e13e31c84abccd44fd0bcb9b426","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7511","title":"Fix domain","createdAt":"2023-08-11T20:14:49Z"}
{"state":"Merged","mergedAt":"2023-08-11T20:39:48Z","number":7512,"body":"- Topic mapping should have a fallback if thread is not indexed yet\r\n- Fix topic mapping\r\n","mergeCommitSha":"73c9654962a84727da6f19d5bcd3cea75b1f4c12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7512","title":"TopicMappingShouldNotBeDependentOnSearch","createdAt":"2023-08-11T20:36:01Z"}
{"state":"Merged","mergedAt":"2023-08-14T18:23:21Z","number":7513,"mergeCommitSha":"c9368fa962449e420a5af164e928d24a5282aaae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7513","title":"Add overlays to unread APIs","createdAt":"2023-08-11T20:39:44Z"}
{"state":"Merged","mergedAt":"2023-08-11T20:43:39Z","number":7514,"mergeCommitSha":"d2442b6f65b5fba7303fed323c2a94e2290086a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7514","title":"Change consumer counts","createdAt":"2023-08-11T20:43:19Z"}
{"state":"Merged","mergedAt":"2023-08-11T21:34:24Z","number":7516,"mergeCommitSha":"42efbee2dd614ec58e0d99b40b19a2f579fabd88","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7516","title":"Update","createdAt":"2023-08-11T21:33:05Z"}
{"state":"Merged","mergedAt":"2023-08-11T21:35:48Z","number":7517,"body":"This reverts commit baad1dbf4a819d02194fc17d57ce30c929eef9c3.\r\n","mergeCommitSha":"dc2d0e0a5bb653bbebfeb40c7722fe06a8d178db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7517","title":"Revert \"Use aws recommendations fo rslow consumers (#7500)\"","createdAt":"2023-08-11T21:35:42Z"}
{"state":"Merged","mergedAt":"2023-08-11T22:36:19Z","number":7518,"mergeCommitSha":"c9667086874326c83a6e0c31f6d0fa341e94d459","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7518","title":"Set ThreadModel.jiraProject where null","createdAt":"2023-08-11T21:58:46Z"}
{"state":"Merged","mergedAt":"2023-08-11T23:02:06Z","number":7519,"body":"Referencing the index pulls *all* the FA icons into our bundle.  This reduces the dashboard bundle size about 30%.  I'll look into making a lint rule for this...","mergeCommitSha":"88ada818b597918118307d2d46ea7b337c17ab41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7519","title":"Dont use FA index","createdAt":"2023-08-11T22:22:55Z"}
{"state":"Merged","mergedAt":"2022-04-01T21:52:39Z","number":752,"body":"* My recent changes slightly broke the discussion creation flow (as in the form webview panel wasn't closing after creation)","mergeCommitSha":"0f81d0aee144f9f3fef14493b149a9e4150a7a91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/752","title":"Fix creating knowledge form","createdAt":"2022-04-01T21:44:51Z"}
{"state":"Merged","mergedAt":"2023-08-11T23:12:13Z","number":7520,"mergeCommitSha":"14b98baaafecd56fd473e90afd8d6390c1b8d4a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7520","title":"Faster people page","createdAt":"2023-08-11T22:35:49Z"}
{"state":"Merged","mergedAt":"2023-08-14T23:01:13Z","number":7521,"mergeCommitSha":"7c29ef019631b5e07450f615ba03b7b38193d3cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7521","title":"Add recommended single instance stream","createdAt":"2023-08-11T22:38:26Z"}
{"state":"Merged","mergedAt":"2023-08-11T23:11:44Z","number":7522,"body":"Mostly these are just previous Q&As.","mergeCommitSha":"726bfbd0f09ee1d2b6afe7800f6c8ea6417e6e95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7522","title":"Don't add notes to document for semantic search","createdAt":"2023-08-11T22:39:15Z"}
{"state":"Merged","mergedAt":"2023-08-12T03:10:46Z","number":7523,"body":"The other clauses on team/site/issueId is enough because of the unique index.","mergeCommitSha":"26c6154c0b8eeff0dd0e51569fb6b48f89afd5f3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7523","title":"Remove ThreadModel.jiraProject clause","createdAt":"2023-08-11T22:40:28Z"}
{"state":"Merged","mergedAt":"2023-08-11T23:36:32Z","number":7524,"body":"The reason we have to add this is to support a crazy scenario where a Slack installation was added to a team, the bot was aligned correctly, Q&A threads were added, the installation was removed, the bot was un-aligned (because the matching was based on strict ExternalId equality), a re-embedding of the thread occurred, which now ingested the slack thread since it no longer has an author with a primary bot association.\r\n\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1691792420443449","mergeCommitSha":"66fa876f976fdec350e50d75ef9f05601b427809","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7524","title":"More aggressive Slack bot member alignment","createdAt":"2023-08-11T23:11:49Z"}
{"state":"Merged","mergedAt":"2023-08-12T00:59:45Z","number":7525,"mergeCommitSha":"9c33a727aab29d91d838e90b15120df344253475","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7525","title":"Increase scm consumer count","createdAt":"2023-08-11T23:41:56Z"}
{"state":"Merged","mergedAt":"2023-08-14T22:49:18Z","number":7526,"body":"Doesn't actually make use of the local context (yet) outside of the repo list. Legacy support for existing events on the queue.","mergeCommitSha":"6a8e85b0f72885b8526517871030ce0b092a853a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7526","title":"Push localcontext info to search service","createdAt":"2023-08-11T23:43:13Z"}
{"state":"Merged","mergedAt":"2023-08-12T00:28:59Z","number":7527,"mergeCommitSha":"b6f85f63ef2b0787ed4f19e9a6aae8afa64ae9cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7527","title":"Add migration to backfill Jira issue mentions","createdAt":"2023-08-11T23:51:51Z"}
{"state":"Merged","mergedAt":"2023-08-12T00:54:28Z","number":7528,"body":"We ran into CORS issues using the CDN script for the kit icons; remove for now. Matt is coming up with a way to import the custom icons but manually render svg for now to unblock.","mergeCommitSha":"2ff5ea3abdf99d8e8a5d3dfa191efecc991ed9b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7528","title":"Fix list icon","createdAt":"2023-08-11T23:59:35Z"}
{"state":"Merged","mergedAt":"2023-08-12T01:58:42Z","number":7529,"mergeCommitSha":"e07a0d151427537831eb99d958920a2407ad5cee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7529","title":"Priority search","createdAt":"2023-08-12T01:32:04Z"}
{"state":"Merged","mergedAt":"2022-04-04T22:39:54Z","number":753,"body":"![image](https://user-images.githubusercontent.com/13431372/161347126-f12450e5-a69b-4773-9e27-1c3863d202a2.png)\r\n![image](https://user-images.githubusercontent.com/13431372/161347144-cd7ef415-1d70-41e8-beff-87b384f031dc.png)\r\n![image](https://user-images.githubusercontent.com/13431372/161347175-b86514e6-cffc-45d9-a057-016f5b618d48.png)\r\n\r\nNOTES:\r\n* Needed to add a MockMessage body that doesn't include an image because we can't parse those right now and our stories were breaking because of this\r\n* ~Right now everyone can edit and delete every message -- this will need to be gated by comparing the currentPerson.id to the message author id (and can be layered on in another PR) -- but I'm unsure of how exactly to grab this data properly in the two clients~ Editing and deleting is now gated by a boolean calculated onto the MessageAndAuthor (needs renaming) model -- will refactor this logic out of the store once we have the currentPerson available in the webviews","mergeCommitSha":"549a3dc9f90170f499d19aa7144c9f23661ab4d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/753","title":"Add editing messages on client","createdAt":"2022-04-01T22:09:13Z"}
{"state":"Merged","mergedAt":"2023-08-12T02:49:47Z","number":7531,"mergeCommitSha":"b0e928e2ea52d6ab30a1036fc2edfb4a67371691","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7531","title":"Fix concurrency in scm","createdAt":"2023-08-12T02:49:26Z"}
{"state":"Merged","mergedAt":"2023-08-12T03:59:59Z","number":7532,"body":"We need to create all users before we can resolve all @mentions ","mergeCommitSha":"08f7741e16b3c4379ddbe0456ff2c66fe1200087","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7532","title":"Add Jira users api","createdAt":"2023-08-12T03:11:18Z"}
{"state":"Merged","mergedAt":"2023-08-12T06:34:47Z","number":7533,"mergeCommitSha":"71bf5c4dc55a3ec6e41ac2a8d8bf83629fa7d6db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7533","title":"Trigger event to ingest jira users on install","createdAt":"2023-08-12T06:07:16Z"}
{"state":"Merged","mergedAt":"2023-08-12T10:47:24Z","number":7534,"mergeCommitSha":"e7edd9725bbdea8581d857e7e832f7df6a06db29","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7534","title":"Do not do unnecessary retries","createdAt":"2023-08-12T10:02:51Z"}
{"state":"Merged","mergedAt":"2023-08-12T11:14:43Z","number":7535,"mergeCommitSha":"ff5c9ae0f2f7535f911e6dd96bf8c56f9d301e4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7535","title":"Remove timer","createdAt":"2023-08-12T11:14:38Z"}
{"state":"Merged","mergedAt":"2023-08-12T20:01:57Z","number":7537,"mergeCommitSha":"9929cdf1b45887cc037c4c2ae830d2b47e6fb246","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7537","title":"fix small mispelling","createdAt":"2023-08-12T19:45:29Z"}
{"state":"Merged","mergedAt":"2023-08-12T22:34:32Z","number":7538,"mergeCommitSha":"f583e34d8c80d66c10f3d5cc57696a4d5cafd4d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7538","title":"increasing mem and cpu on scm service","createdAt":"2023-08-12T22:24:32Z"}
{"state":"Merged","mergedAt":"2023-08-14T07:25:00Z","number":7539,"body":"Why Atlassian why","mergeCommitSha":"2531ab9dcc04facd9e9258050da8f8c274dc2083","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7539","title":"Convert Jira links to proper markdown","createdAt":"2023-08-14T06:47:11Z"}
{"state":"Merged","mergedAt":"2022-04-01T22:42:01Z","number":754,"body":"https://stackoverflow.com/questions/59368838/difference-between-coroutinescope-and-coroutinescope-in-kotlin\r\n\r\nSeems like the use of `coroutineScope` was not returning so any call to `registerRepoPath` would never never return.","mergeCommitSha":"70ff0698f06313a07d00fbc0919da56ab3c02499","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/754","title":"Fix bug where SourceMarkScheduler.startTask was not returning","createdAt":"2022-04-01T22:30:31Z"}
{"state":"Merged","mergedAt":"2023-08-14T21:08:06Z","number":7540,"mergeCommitSha":"8414ed70cbd0dd2dae70bc83af46bd41e3d48f4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7540","title":"Added matched keywords to a/b test metrics","createdAt":"2023-08-14T07:03:08Z"}
{"state":"Closed","mergedAt":null,"number":7541,"body":"- Remove timer\r\n- Fix issues with consumer transactions for activemq not being rolled back on cancellation exceptions\r\n","mergeCommitSha":"ce8864723803638e765181ff17063b986200b959","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7541","title":"RemoveTimer","createdAt":"2023-08-14T17:14:49Z"}
{"state":"Merged","mergedAt":"2023-08-14T17:39:40Z","number":7542,"body":"…ack on cancellation exceptions","mergeCommitSha":"0468a90225874f08a787a8ac6797dec88ab90bb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7542","title":"Fix issues with consumer transactions for activemq not being rolled back on cancellation exceptions","createdAt":"2023-08-14T17:16:24Z"}
{"state":"Merged","mergedAt":"2023-08-14T21:15:30Z","number":7543,"mergeCommitSha":"f40e97f344e5962251b1f4f07d6a051e8af850bb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7543","title":"Use custom FA icons","createdAt":"2023-08-14T17:23:00Z"}
{"state":"Merged","mergedAt":"2023-08-14T17:40:44Z","number":7544,"mergeCommitSha":"2938f63702a5e16d4eed5d6339ac7b55b5a1ac4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7544","title":"Change activemq config","createdAt":"2023-08-14T17:40:31Z"}
{"state":"Merged","mergedAt":"2023-08-14T18:43:21Z","number":7545,"body":"On Login Exchange error, redirect user back to login.\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/f979d0b0-a32f-4931-ad2e-53d7e5abc809\r\n\r\n","mergeCommitSha":"64488044bb64c9c280acfd080cf747e9386d38eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7545","title":"Redirect user to login after exchange error","createdAt":"2023-08-14T18:23:39Z"}
{"state":"Merged","mergedAt":"2023-08-14T18:25:12Z","number":7546,"mergeCommitSha":"2c09ae80a0b22db7b670cb2395bec7238c6d156e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7546","title":"Add more diagnostic logging","createdAt":"2023-08-14T18:23:55Z"}
{"state":"Merged","mergedAt":"2023-08-14T18:57:51Z","number":7547,"mergeCommitSha":"8058ce421dffbcc7fdc37b7de766c4ab89a87e12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7547","title":"Update maximum for dataservice","createdAt":"2023-08-14T18:57:40Z"}
{"state":"Merged","mergedAt":"2023-08-14T20:41:54Z","number":7548,"mergeCommitSha":"5f6c1ab02abd331a7dce60dd71cbbe2234372bbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7548","title":"Set MessageModel.jiraIssueId","createdAt":"2023-08-14T18:59:59Z"}
{"state":"Merged","mergedAt":"2023-08-14T19:58:56Z","number":7549,"mergeCommitSha":"b5b88612e3b98f3de26d6cdecf1f67fafc3ebd03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7549","title":"Increase parallelization","createdAt":"2023-08-14T19:58:49Z"}
{"state":"Merged","mergedAt":"2022-04-05T22:55:49Z","number":755,"body":"## Summary\r\n\r\nThis is a background job that \"cleans\" video channels in the following way:\r\n\r\n- If a channel's max time is reached (30 minutes for example), the participants are kicked from the channel\r\n- If a participant has stopped heart-beating the channel beyond a max interval, the participant is kicked\r\n- If a participant has left the channel for some period of time, the participant object is deleted. We have to keep the participant object around for a while in order for push updates to work (clients need to know that a participant has left)\r\n- Additionally if the participant that left is the host, there's some additional cleanup that happens for active video recordings, and host status is handed off to the first available participant. ","mergeCommitSha":"e9495de927adff2bc12568cb2146293ac0cb080a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/755","title":"Video channel maintenance job","createdAt":"2022-04-01T23:14:14Z"}
{"state":"Merged","mergedAt":"2023-08-14T21:21:31Z","number":7550,"body":"Add Util to parse errors for APIError, which is the default error response to all API requests.\r\n\r\nUsing this, attempt to grab error title and display in loginExchange for 3 seconds before redirecting back to base login page.\r\n\r\nExample:\r\n<img width=\"1309\" alt=\"CleanShot 2023-08-14 at 12 59 40@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/b58d1e0c-6def-471b-bbae-e16aa635b1ec\">\r\n","mergeCommitSha":"f56ed268b2d2e8a576e6dac456320f16baadeddc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7550","title":"Handle API Error during LoginExchange","createdAt":"2023-08-14T20:02:01Z"}
{"state":"Merged","mergedAt":"2023-08-14T20:16:09Z","number":7551,"mergeCommitSha":"370936a17caa21fe2204064a2c1b166925d9771b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7551","title":"Add logging for question completion service","createdAt":"2023-08-14T20:15:54Z"}
{"state":"Merged","mergedAt":"2023-08-14T20:34:49Z","number":7552,"body":"When installing from Github from dashboard, set Install Location.\r\n\r\nWithout this, we render the following:\r\n<img width=\"772\" alt=\"CleanShot 2023-08-14 at 13 18 46@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/37e65c8a-ebee-4ac7-8191-7825b69c3686\">\r\n\r\nInstead, we should be redirecting them to the identities page.\r\n<img width=\"1279\" alt=\"CleanShot 2023-08-14 at 13 19 13@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/cc62e57b-ecb3-4683-b0b6-778eef4cf923\">\r\n\r\nThe logic is dictated by a sessionStorage value:\r\n\r\n```\r\n    const redirectLocation = useMemo(() => {\r\n        return InstallStorage.getStorageRedirect();\r\n    }, []);\r\n\r\n    const isDashboardInstall = useMemo(() => {\r\n        return InstallStorage.isDashboardInstall();\r\n    }, []);\r\n\r\n    if (redirectLocation) {\r\n        return <VSCodeInstallRedirect redirectLocation={redirectLocation} />;\r\n    }\r\n\r\n    if (isDashboardInstall) {\r\n        return <DashboardInstallRedirect />;\r\n    }\r\n    ```","mergeCommitSha":"b99c811f02b398c6c31e432ac8b734ade9843982","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7552","title":"Add install location to dashboard","createdAt":"2023-08-14T20:20:01Z"}
{"state":"Merged","mergedAt":"2023-08-14T21:36:46Z","number":7553,"mergeCommitSha":"0ee976ddc55c4f0391a8c28fecdcc7efa11b69b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7553","title":"Enable Confluence provider for dev only","createdAt":"2023-08-14T20:43:08Z"}
{"state":"Merged","mergedAt":"2023-08-14T20:50:36Z","number":7554,"body":"This is so that Peter doesn't yell at me anymore.","mergeCommitSha":"746d0df2511e8429389517061027a9cd4dded395","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7554","title":"Ignore generated direxctories","createdAt":"2023-08-14T20:48:02Z"}
{"state":"Closed","mergedAt":null,"number":7555,"body":"Reverts NextChapterSoftware/unblocked#7530","mergeCommitSha":"b28e2f64675adb56bbd5560156aa07a7a4c5af63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7555","title":"Revert \"chore(deps): update dependency flight-school/anycodable to from: \"0.6.7\"\"","createdAt":"2023-08-14T20:48:08Z"}
{"state":"Merged","mergedAt":"2023-08-14T20:58:37Z","number":7556,"mergeCommitSha":"9ccc6de3692e6e5ea21015ec2b9291e3b89500eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7556","title":"Revert AnyCodable version for CI builds","createdAt":"2023-08-14T20:57:05Z"}
{"state":"Merged","mergedAt":"2023-08-15T17:08:07Z","number":7557,"body":"First half of the algorithm: weighted document retrieval. We hit up pinecone with and without the repo filter and then zip the results in a weighted fashion. The weight is controllable from the template, although we could just move this to code if that makes more sense.","mergeCommitSha":"4e551ca75f36299432a6fad55e7cd31388d6ef91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7557","title":"Perform weighted context aware document retrieval","createdAt":"2023-08-14T21:03:02Z"}
{"state":"Merged","mergedAt":"2023-08-14T21:49:36Z","number":7558,"body":"AWS has a pretty stringent throttling for SES updates, just add retry semantics.","mergeCommitSha":"07ced22659e96684f91b528f3c5906c0f14ea14d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7558","title":"Fix aws throttling ses updates","createdAt":"2023-08-14T21:31:34Z"}
{"state":"Open","mergedAt":null,"number":7559,"mergeCommitSha":"024336866dcea2e472377597e7f2e9c96011c461","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7559","title":"Better Answers Email Campaign email","createdAt":"2023-08-14T21:48:33Z"}
{"state":"Merged","mergedAt":"2022-04-02T00:07:41Z","number":756,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p164*************","mergeCommitSha":"812318bce0dbd34349f66db71a510b879b0a7b0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/756","title":"Use proto2","createdAt":"2022-04-01T23:58:51Z"}
{"state":"Merged","mergedAt":"2023-08-15T00:06:09Z","number":7560,"body":"Responds with:\n```json\n{\n  \"status\": \"400\",\n  \"title\": \"We're sorry, but to use Unblocked, a verified email is required.\",\n  \"detail\": \"A verified email provides a reliable communication and verification method for your account. Please verify your email address in GitHub and try again.\",\n  \"url\": \"https://docs.github.com/en/get-started/signing-up-for-github/verifying-your-email-address\"\n}\n```\n\nNote that _all_ failed API requests now return an API response body `ApiError` which miminally has:\n```json\n{\n  \"status\": <int>\n}\n```","mergeCommitSha":"32daeb855803f2318d473b7a657ba85d1a420d1a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7560","title":"Login will fail with 400 and an error message if no verified email exists","createdAt":"2023-08-14T21:49:08Z"}
{"state":"Merged","mergedAt":"2023-08-14T22:56:59Z","number":7561,"body":"still learning statsig","mergeCommitSha":"a7bc5ffdf819c828546efdb038bc733a32ee28a8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7561","title":"still learning statsig, need to add the model name to the metrics","createdAt":"2023-08-14T22:02:33Z"}
{"state":"Merged","mergedAt":"2023-08-15T00:38:05Z","number":7562,"body":"Currently we only keep 7 days of insights. This change will extend that to 31 days","mergeCommitSha":"1db08e62bedb75d62cc1166918884b9964b5f945","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7562","title":"Increase db insights retention","createdAt":"2023-08-14T23:00:42Z"}
{"state":"Merged","mergedAt":"2023-08-14T23:19:45Z","number":7563,"mergeCommitSha":"47c14c940b5589d9a614171fcc911b9bd5357cbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7563","title":"Add ConfluenceInstallationFactory","createdAt":"2023-08-14T23:07:36Z"}
{"state":"Closed","mergedAt":null,"number":7564,"body":"Revert \"Handle API Error during LoginExchange (#7550)\"\n\nThis reverts commit f56ed268b2d2e8a576e6dac456320f16baadeddc.\n\nRevert \"Redirect user to login after exchange error (#7545)\"\n\nThis reverts commit 64488044bb64c9c280acfd080cf747e9386d38eb.","mergeCommitSha":"03bfe437d16209c5a08f1c07260027f07ecb17a6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7564","title":"Revert User Auth Redirect","createdAt":"2023-08-15T00:00:39Z"}
{"state":"Merged","mergedAt":"2023-08-15T18:42:05Z","number":7565,"mergeCommitSha":"216017eafbbfbc27892a90b28755e8440fc6aa80","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7565","title":"Fix pr tab provider typing","createdAt":"2023-08-15T00:01:53Z"}
{"state":"Merged","mergedAt":"2023-08-15T00:29:00Z","number":7566,"mergeCommitSha":"a35b5b8ca0485d1c4da034cfea25914e097f0ff7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7566","title":"Fix duplicate jira message migrator","createdAt":"2023-08-15T00:03:37Z"}
{"state":"Merged","mergedAt":"2023-08-15T00:35:26Z","number":7567,"mergeCommitSha":"90881789850d63ea07210874964a0b431110eebb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7567","title":"Streamline slack responses for question validation","createdAt":"2023-08-15T00:13:44Z"}
{"state":"Merged","mergedAt":"2023-08-15T00:35:16Z","number":7568,"body":"- Up data service\r\n- Up data service consumers\r\n","mergeCommitSha":"a6ef3ef2d43e33bb2b1f95a326f3fec11b8f1f9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7568","title":"UpDataServic2","createdAt":"2023-08-15T00:35:01Z"}
{"state":"Merged","mergedAt":"2023-08-15T17:10:21Z","number":7569,"body":"* JetBrains IDEs send open file context when creating a QA thread\r\n* JetBrains IDEs send open file listing to SourceMark engine, which should pre-load marks for all open files.","mergeCommitSha":"8c7d920e77f4a4e06fb15abfe2e271049b4698a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7569","title":"JetBrains IDE sends open file context","createdAt":"2023-08-15T03:38:06Z"}
{"state":"Merged","mergedAt":"2022-04-03T03:34:20Z","number":757,"body":"This was already approved but I'm resurrecting it to make reviews of the base branch a little easier","mergeCommitSha":"dfa464712aebef88f16729f2ac249a05d5e3e70b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/757","title":"Add video channel push","createdAt":"2022-04-02T00:21:02Z"}
{"state":"Merged","mergedAt":"2023-08-15T04:05:55Z","number":7570,"body":"This reverts commit 4e9f11582e1d70e6faf625b14cd9fe7073676770.\r\n\r\nThis is temporary; I have a feeling that sparse vectors are too large and maybe crashing/hanging the process somehow.","mergeCommitSha":"14c4b7215673477d6191840e91db8c74a9d88627","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7570","title":"Revert \"Generate sparse Pinecone embeddings for source code (#7464)\"","createdAt":"2023-08-15T04:03:44Z"}
{"state":"Merged","mergedAt":"2023-08-15T04:06:29Z","number":7571,"body":"Give _N_ seconds to process a file.","mergeCommitSha":"e3cf79afba770a193fd66d2957c59c1a8f2362b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7571","title":"timeout source code file processing","createdAt":"2023-08-15T04:03:47Z"}
{"state":"Merged","mergedAt":"2023-08-15T05:01:56Z","number":7572,"body":"Never safe to lookup an identity by external ID, without also passing the provider in the predicate. And if the provider is an enterprise provider, then the external team ID is also required.\r\n\r\n## Example\r\n\r\nBoth of these identities share the exact same Atlassian _External ID_; however, they have different providers. In this scenario, Unblocked would sometimes lookup the Confluence identity at login, attempt to update it by changing the provider type from Confluence to Bitbucket, which would hit a DB constraint (thankfully!) and conflict because there already was a Bitbucket identity with the same external ID.\r\n\r\n- Person with Bitbucket identity:\r\n  https://admin.dev.getunblocked.com/people/0fb64633-8923-4192-a683-e7684bece058\r\n\r\n- Person with Confluence identity:\r\n  https://admin.dev.getunblocked.com/people/02b3cc8c-d7e1-496b-a656-af26e5752e62\r\n\r\n## Trace\r\n\r\n```json\r\n{\r\n  \"level\": \"WARN\",\r\n  \"message\": \"Transaction attempt #1 failed: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint \\\"identitymodel_provider_externalid_externalteamid_unique\\\"\\n  Detail: Key (provider, \\\"externalId\\\", \\\"externalTeamId\\\")=(2, 712020:2dd18615-f7c0-4d6c-8d69-435c149d116c, :2:) already exists.. Statement(s): UPDATE identitymodel SET \\\"accessTokenExpiresAt\\\"=?, emails=?, \\\"externalTeamId\\\"=?, \\\"htmlUrl\\\"=?, person=?, \\\"primaryEmail\\\"=?, provider=?, \\\"rawAccessToken\\\"=?, \\\"rawRefreshToken\\\"=? WHERE id = ?\",\r\n  \"platform\": {\r\n    \"version\": \"90881789850d63ea07210874964a0b431110eebb\",\r\n    \"buildNumber\": \"27136\"\r\n  },\r\n  \"environment\": \"dev\",\r\n  \"@timestamp\": \"2023-08-15T02:40:59.725+0000\",\r\n  \"service\": \"authservice\",\r\n  \"thread_name\": \"DefaultDispatcher-worker-1\",\r\n  \"logger_name\": \"Exposed\",\r\n  \"stack_trace\": \"o.p.u.PSQLException: ERROR: duplicate key value violates unique constraint \\\"identitymodel_provider_externalid_externalteamid_unique\\\"\r\n  Detail: Key (provider, \"externalId\", \"externalTeamId\")=(2, 712020:2dd18615-f7c0-4d6c-8d69-435c149d116c, :2:) already exists.\r\n\tat o.p.c.v.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2676)\r\n\tat o.p.c.v.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2366)\r\n\tat o.p.c.v.QueryExecutorImpl.execute(QueryExecutorImpl.java:356)\r\n\tat o.p.jdbc.PgStatement.executeInternal(PgStatement.java:496)\r\n\tat o.p.jdbc.PgStatement.execute(PgStatement.java:413)\r\n\tat o.p.j.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)\r\n\tat o.p.j.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)\r\n\tat i.o.i.j.i.OpenTelemetryStatement.wrapCall(OpenTelemetryStatement.java:294)\r\n\tat i.o.i.j.i.OpenTelemetryPreparedStatement.executeUpdate(OpenTelemetryPreparedStatement.java:65)\r\n\tat o.a.c.d.DelegatingPreparedStatement.executeUpdate(DelegatingPreparedStatement.java:136)\r\n\tat o.a.c.d.DelegatingPreparedStatement.executeUpdate(DelegatingPreparedStatement.java:136)\r\n\tat o.j.e.s.s.j.JdbcPreparedStatementImpl.executeUpdate(JdbcPreparedStatementImpl.kt:26)\r\n\tat o.j.e.s.s.BatchUpdateStatement.executeInternal(BatchUpdateStatement.kt:41)\r\n\tat o.j.e.s.s.BatchUpdateStatement.executeInternal(BatchUpdateStatement.kt:12)\r\n\tat o.j.e.s.s.Statement.executeIn(Statement.kt:62)\r\n\t... 23 common frames omitted\r\nWrapped by: o.j.e.e.ExposedSQLException: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint \"identitymodel_provider_externalid_externalteamid_unique\"\r\n  Detail: Key (provider, \"externalId\", \"externalTeamId\")=(2, 712020:2dd18615-f7c0-4d6c-8d69-435c149d116c, :2:) already exists.\r\n\tat o.j.e.s.s.Statement.executeIn(Statement.kt:64)\r\n\tat o.j.e.s.Transaction.exec(Transaction.kt:141)\r\n\tat o.j.e.s.Transaction.exec(Transaction.kt:127)\r\n\tat o.j.e.s.s.Statement.execute(Statement.kt:28)\r\n\tat o.j.e.d.EntityBatchUpdate.execute(EntityBatchUpdate.kt:33)\r\n\tat...\"\r\n}\r\n```","mergeCommitSha":"fd245b0fff8ec95976fd8b697312b22d8773131d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7572","title":"Fix conflict between external IDs from different providers","createdAt":"2023-08-15T04:37:00Z"}
{"state":"Merged","mergedAt":"2023-08-15T04:54:28Z","number":7573,"body":"Has been deprecated for some time, and responsible for random build failures.\nhttps://github.com/scikit-learn/sklearn-pypi-package#brownout-schedule","mergeCommitSha":"f72bd99f73b013edb49cd486599117e14f453769","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7573","title":"Update sklearn to scikit-learn","createdAt":"2023-08-15T04:53:28Z"}
{"state":"Merged","mergedAt":"2023-08-15T05:22:47Z","number":7574,"mergeCommitSha":"b71f2b3d1cf8253c370ef7156708b983bfb487cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7574","title":"Replace no longer needed Jira migration logic","createdAt":"2023-08-15T04:58:39Z"}
{"state":"Merged","mergedAt":"2023-08-15T21:32:51Z","number":7575,"body":"Background job skeleton that is config controlled.\r\nhttps://www.notion.so/nextchaptersoftware/Auto-Source-Code-Ingest-42102fc2c91a4826a72c93d6ed754571?pvs=4","mergeCommitSha":"c586a99a7394cc8377ac3c6d2e132d930155857b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7575","title":"Config to manage source code automation rollout","createdAt":"2023-08-15T05:22:38Z"}
{"state":"Merged","mergedAt":"2023-08-15T22:21:56Z","number":7576,"body":"https://www.notion.so/nextchaptersoftware/Auto-Source-Code-Ingest-42102fc2c91a4826a72c93d6ed754571?pvs=4","mergeCommitSha":"7cb84461fdaa2c08c603b47276dfca236f941c53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7576","title":"Repo model fields to manage automated source code ingestion","createdAt":"2023-08-15T05:36:56Z"}
{"state":"Merged","mergedAt":"2023-08-15T19:44:03Z","number":7577,"mergeCommitSha":"d3346215d3f502c20e076de9ee38f447b152c142","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7577","title":"Dedup the merged retrieved documents","createdAt":"2023-08-15T18:28:03Z"}
{"state":"Merged","mergedAt":"2023-08-15T19:46:58Z","number":7578,"body":"This will let us render the repo name in the references UI without having to fetch and aggregate all the team's repos in all clients.  We probably want to get away from fetching and aggregating potentially large data sources, and we're already doing this in this model to some extent (the Provider property could be aggregated, but is provided here)","mergeCommitSha":"a9319c7aab4127234f1acc7a419597f65494aa9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7578","title":"Add repo name to source code references","createdAt":"2023-08-15T18:30:16Z"}
{"state":"Merged","mergedAt":"2023-08-15T20:42:47Z","number":7579,"body":"onboarding:\r\n<img width=\"1494\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/c03e0388-2c7b-4492-908a-b3e77bd75e0f\">\r\n<img width=\"1497\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/7146f86a-cab6-4c2a-835c-e8f582c7d9dc\">\r\n<img width=\"1499\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8e840684-7bd0-4e32-be82-32f71556f3c8\">\r\n\r\nsettings:\r\n<img width=\"1499\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/7ff1f4c6-0ab1-4167-a99f-71a8ec10d69f\">\r\n<img width=\"1492\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/cec9636e-00c5-4343-8807-ce6671b5b632\">\r\n","mergeCommitSha":"b40e62af1685fd84b25ae1eeea08e7077be002de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7579","title":"Add confluence onboarding/configuration","createdAt":"2023-08-15T18:36:56Z"}
{"state":"Merged","mergedAt":"2022-04-02T23:22:02Z","number":758,"body":"## Summary\r\n\r\nTo create an allow list we need to use a stable identifier. The only such ID in our system is the external SCM identifier. \r\n\r\nTo facilitate this I had to add the `externalId` (GitHub identity) as a claim in the token. No harm to this, except that anyone with this token will be able to identity exactly who owns it.","mergeCommitSha":"31249d073da6f88e2e09e05f39491c8b9cc1df05","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/758","title":"Add auth allow list","createdAt":"2022-04-02T23:12:37Z"}
{"state":"Merged","mergedAt":"2023-08-15T22:47:38Z","number":7580,"body":"- Repos will have `codeIngestEnabled = true` and `codeIngestLastStartAt = null` set on upsert.\r\n- Accounts for legacy GitHub installations that do not authorize source code access;\r\n  repos from those teams will not have `codeIngestEnabled = true`.\r\n\r\nhttps://www.notion.so/nextchaptersoftware/Auto-Source-Code-Ingest-42102fc2c91a4826a72c93d6ed754571","mergeCommitSha":"dcf7d7da7ed4e8c440eefc1d1ba08f872fef1851","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7580","title":"Programmatically control enabling code ingestion on repos","createdAt":"2023-08-15T19:39:31Z"}
{"state":"Merged","mergedAt":"2023-08-15T19:58:25Z","number":7581,"body":"- sort\n- display tooltip (useful for Atlassian logos that are hard to distinguish)","mergeCommitSha":"765be83e444079d2072dc5c757c98936c7b2ab95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7581","title":"Fix provider icons in admin","createdAt":"2023-08-15T19:39:34Z"}
{"state":"Merged","mergedAt":"2023-08-15T21:32:24Z","number":7582,"body":"We were swallowing the UserVisibleException \uD83E\uDD26‍♂️\n\nAddresses bug from #7560.\n\nSee also\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1692124983644169?thread_ts=**********.799379&cid=C02HEVCCJA3","mergeCommitSha":"27b50a219099a5fd31d5da89e7c8a874aa8a2bd9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7582","title":"User visible errors should be handled correctly by exchangeAuthCode","createdAt":"2023-08-15T19:46:56Z"}
{"state":"Merged","mergedAt":"2023-08-15T21:16:03Z","number":7583,"mergeCommitSha":"fb775aeac5a7a4e831bdb423ea191708f6ca2ffc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7583","title":"Fix dialog width","createdAt":"2023-08-15T20:58:13Z"}
{"state":"Merged","mergedAt":"2023-08-15T22:30:57Z","number":7584,"body":"open:\r\n<img width=\"791\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/581ce8b9-faa2-4ed8-91f0-d4db4c0fa91b\">\r\n\r\nmerged:\r\n<img width=\"782\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/c943ab54-1ee7-43a4-8294-75abcf078540\">\r\n\r\nclosed:\r\n<img width=\"809\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/36d20786-01ca-432d-a2e1-190c42fd55bb\">\r\n","mergeCommitSha":"d2dfd558fa4dc3bb6b76d5cffbf804b10df8f123","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7584","title":"Add pr state tag to dashboard pr view","createdAt":"2023-08-15T21:17:32Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:39:04Z","number":7585,"body":"Update Github connect messaging in attempt to prevent personal orgs.\r\n\r\n<img width=\"1016\" alt=\"CleanShot 2023-08-15 at 15 32 02@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1553313/1270138f-7923-4884-ac00-b9d5a9bfa075\">\r\n","mergeCommitSha":"83fb68a9814ba2b7d0ddc2387666d6f4085ca831","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7585","title":"Update GH connect messaging","createdAt":"2023-08-15T21:32:59Z"}
{"state":"Closed","mergedAt":null,"number":7586,"body":"Now that we have more nodes in prod it makes sense to run more web hook service instances. This helps with deployment stability. We have already set anti-affinity to spread them out.  ","mergeCommitSha":"f83f891a37e0c4344ac4421c8daee5710dbae0fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7586","title":"add more instances to webhook service","createdAt":"2023-08-15T22:33:10Z"}
{"state":"Merged","mergedAt":"2023-08-15T23:05:20Z","number":7587,"mergeCommitSha":"c4c591a9d257feb7691b3d303fb31511ec5613dd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7587","title":"Move ConfluenceIngestionJob out of the data service","createdAt":"2023-08-15T22:46:34Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:34:33Z","number":7589,"mergeCommitSha":"2d1310b3e1a821c9625e8f94bdeeb209653ac78f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7589","title":"Plumb through local file context to prompt","createdAt":"2023-08-15T23:41:48Z"}
{"state":"Closed","mergedAt":null,"number":759,"body":"There was.a request to get per-repo thread push notification.\r\nThis pr adds support for that.\r\n\r\nThe channel uri would be in the format:\r\n\"/threads?repoIds={repo1}&repoIds={repo2}...\"","mergeCommitSha":"cf2f14ddfcc979d500894e799e2e9c4d794c7baa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/759","title":"AddSupportForPerRepoThreadChannels","createdAt":"2022-04-03T02:09:15Z"}
{"state":"Merged","mergedAt":"2023-08-17T19:22:38Z","number":7590,"body":"Moving away from SendInBlue to SendGrid for email campaigns.\r\n\r\nWhen onboarding event occurs, we add the user's email to a list in SendGrid. This list is part of an automated marketing campaign which starts the moment a new user is added to the targeted list.\r\n\r\nSendInBlue will be removed in a subsequent PR.\r\nProd / Dev API token needs to be added. Only local token exists atm.","mergeCommitSha":"8fc7c7af1cca37a632aed2659248930ca3e9e828","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7590","title":"Integrate SendGrid for Onboarding Campaign","createdAt":"2023-08-15T23:41:59Z"}
{"state":"Merged","mergedAt":"2023-08-16T03:17:31Z","number":7591,"mergeCommitSha":"0e7b0958aa9ed6bb611cb14c60bb1a73dbd81a6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7591","title":"Faster repos page load in admin web","createdAt":"2023-08-16T00:16:54Z"}
{"state":"Merged","mergedAt":"2023-08-16T04:10:16Z","number":7592,"body":"Missed this path from #7580.\r\n\r\nhttps://www.notion.so/nextchaptersoftware/Auto-Source-Code-Ingest-42102fc2c91a4826a72c93d6ed754571","mergeCommitSha":"d6df7d1211841f4191c52462881d078f3e9a3388","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7592","title":"Backfill RepoModel codeIngestEnabled","createdAt":"2023-08-16T00:29:23Z"}
{"state":"Merged","mergedAt":"2023-08-16T00:45:23Z","number":7593,"body":"- Move to latest cdk\r\n- Upate\r\n","mergeCommitSha":"a1729daeaae45fcb4dc6e72dedb1f8e73f2e1d1b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7593","title":"MoveToLatestCDK","createdAt":"2023-08-16T00:36:43Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:57:19Z","number":7594,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/9694780c-22ee-4f7f-a7d8-2f8d30965acc\r\n\r\n","mergeCommitSha":"66da7d93cfecd4aa26c2d65c5eefd2889f502a5a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7594","title":"Pin tabs to detail header","createdAt":"2023-08-16T00:42:01Z"}
{"state":"Merged","mergedAt":"2023-08-16T01:36:17Z","number":7595,"mergeCommitSha":"4078f32dd4d6d3906499ed4412145e9b0948ce39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7595","title":"Fix infra build","createdAt":"2023-08-16T01:36:11Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:52:48Z","number":7596,"body":"https://www.notion.so/nextchaptersoftware/Auto-Source-Code-Ingest-42102fc2c91a4826a72c93d6ed754571","mergeCommitSha":"e1b52453bb78e29149f829b3c66fabb034441728","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7596","title":"Source code ingestion job","createdAt":"2023-08-16T03:28:21Z"}
{"state":"Merged","mergedAt":"2023-08-16T03:31:12Z","number":7597,"mergeCommitSha":"eeb45d531b965f390a678d70fcb4c5b82b658044","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7597","title":"Fix endpoint config","createdAt":"2023-08-16T03:28:39Z"}
{"state":"Merged","mergedAt":"2023-08-16T04:17:37Z","number":7598,"mergeCommitSha":"6e91b597ba146a431b75a7798bd26598f5ee6bea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7598","title":"Fix model environment for llama2","createdAt":"2023-08-16T04:17:26Z"}
{"state":"Closed","mergedAt":null,"number":7599,"body":"Temp comment confluence.","mergeCommitSha":"e066c3dd6370ff08dcc9ab7a7aedbefdd414b804","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7599","title":"Comment out jira & conflunce","createdAt":"2023-08-16T05:08:13Z"}
{"state":"Merged","mergedAt":"2022-01-20T15:53:50Z","number":76,"body":"## Summary\r\n\r\nThis is the start of what we need to do to wire up token based authorization. I've added the Authentication installer, although at the moment it doesn't do anything because there are no endpoints that use it. \r\n\r\n## Testing\r\nThe validator is missing a test - will add \r\n\r\n## Extras\r\n\r\nI slightly refactored the base to a \"module\" layout. In Server.kt the idea is that we can pass multiple modules and chain them like the good old days. Not sure if anyone has looked at `EngineMain` layout? Also - why `Netty` over `CIO`?\r\n","mergeCommitSha":"3d188a50ea4c2ebf2b778640231c02c17f206e78","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/76","title":"Wire up ktor auth dsl","createdAt":"2022-01-19T06:05:21Z"}
{"state":"Merged","mergedAt":"2022-04-04T16:07:17Z","number":760,"body":"There was.a request to get per-repo thread push notification.\r\nThis pr adds support for that.\r\n\r\nThe channel uri would be in the format:\r\n\"/threads?repoIds={repo1}&repoIds={repo2}...\"\r\n\r\nOther Changes:\r\n1. Clean up some parsing of channel crap.","mergeCommitSha":"8d5412a44f79f6fb99d1544b341820803869cc73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/760","title":"AddSupportForPerRepoThreadChannels","createdAt":"2022-04-03T03:55:38Z"}
{"state":"Merged","mergedAt":"2023-08-16T05:17:59Z","number":7600,"mergeCommitSha":"4dc4f4d08b2ba8a40c5b275a43333ed92fc24bc5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7600","title":"Hide confluence","createdAt":"2023-08-16T05:08:15Z"}
{"state":"Merged","mergedAt":"2023-08-16T14:37:23Z","number":7601,"body":"- 12 hours ingestion\r\n  This is a very stupid change.\r\n  Motivation is to get a SiMa repo to complete.\r\n  See #7410 for correct future approach instead.\r\n\r\n- Pinecone 30 file batchsize\r\n  Hitting 2MB upload limit with 50 files.\r\n  This change will produce more requests, but they will be smaller.","mergeCommitSha":"44eac20a177220c3a2830d922b9a57e981ffa44f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7601","title":"Source code ingestion 12 hour stopping condition and lower Pinecone batch size","createdAt":"2023-08-16T14:25:25Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:04:03Z","number":7602,"body":"I think this was broken when we split up WebviewContentController into a hierarchy for IntelliJ.  Basically, `_isVisible` was not being set correctly on editor on webviews.  This didn't seem to cause many bugs (visibility would generally be set to true always), but Pete noticed an error in the following workflow:\r\n\r\n* Ask unblocked a question in the IDE -> thread created\r\n* Focus the IDE on another text editor, such that the QA thread tab is now not focused and its editor is not visible\r\n* Ask unblocked another question\r\n\r\nThe QA thread tab re-foregrounds, but the UI doesn't update, because we think the webview is not visible, so we queue up updates instead of sending them to the webview.","mergeCommitSha":"c89b3a92d5bef92bc0fa0b63e86d22db3916308b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7602","title":"Fix VSCode webview visibility","createdAt":"2023-08-16T16:27:14Z"}
{"state":"Merged","mergedAt":"2023-08-16T16:59:28Z","number":7603,"body":"Thank you @matthewjamesadam for inspiring this change!!!","mergeCommitSha":"6cef6d700f94d7221401a738e3be33a273fe3a34","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7603","title":"Remove slack files","createdAt":"2023-08-16T16:59:22Z"}
{"state":"Merged","mergedAt":"2023-08-16T19:45:06Z","number":7604,"body":"This is to allow documents (Confluence pages, Notion docs, Quip, other wikis) to appear in the references list for a Q&A thread.\r\n\r\n![CleanShot 2023-08-16 at 10 02 32@2x](https://github.com/NextChapterSoftware/unblocked/assets/1924615/70632808-599a-4240-9e12-34a8b110c2d7)\r\n\r\nThe icon will be the provider, the display string will be the document title, and the link will be the external url.","mergeCommitSha":"f862be997155e6580cdf38877567c47add89330a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7604","title":"Add ReferenceArtifact.Document","createdAt":"2023-08-16T17:05:52Z"}
{"state":"Merged","mergedAt":"2023-09-05T18:45:41Z","number":7605,"body":"Blocked on all teams having the field specified.\r\nMust run migration in #7895 first.","mergeCommitSha":"c2167090cd13b516bdb32254e339da2f87beead0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7605","title":"Make providerInstallationValid Team column non-null","createdAt":"2023-08-16T17:41:44Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:11:07Z","number":7606,"mergeCommitSha":"a98111c59d9a8c81ca55c3c4eb7b1a9f84cd8837","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7606","title":"Fix force repo delete bug","createdAt":"2023-08-16T17:51:45Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:40:28Z","number":7607,"mergeCommitSha":"341fbf45c2aa3c0d4897c8a4772c2775b9f0bf63","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7607","title":"Sort and clip QA context files","createdAt":"2023-08-16T18:20:13Z"}
{"state":"Merged","mergedAt":"2023-08-16T18:57:46Z","number":7608,"mergeCommitSha":"a0c586e160067dd061f0db73326ad97f917fbcb8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7608","title":"Lint refactor build fix","createdAt":"2023-08-16T18:56:21Z"}
{"state":"Merged","mergedAt":"2023-08-16T20:40:12Z","number":7609,"body":"* The HeadlessUI Dialog (what we use for our ModalDialog component) by default focuses the first element inside the dialog content area. In most cases, our buttons are laid out so that the secondary action is rendered first (leftmost) and the primary action is rendered second (rightmost). Hence, hitting ENTER would almost always trigger the secondary action \r\n* Fix is to explicitly set a ref and pass it into the `initialFocus` prop ([per headless docs](https://headlessui.com/react/dialog#managing-initial-focus)) which will set the initial focus to the intended element  ","mergeCommitSha":"eca519494953c15ba945d7e762f6cc2223233394","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7609","title":"Always focus the primary button in modals","createdAt":"2023-08-16T19:01:15Z"}
{"state":"Merged","mergedAt":"2022-04-03T19:59:59Z","number":761,"mergeCommitSha":"bb707b4278df18d8def85ba31157f72a5bbd4e7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/761","title":"Update makefile and readme","createdAt":"2022-04-03T19:57:39Z"}
{"state":"Merged","mergedAt":"2023-08-16T20:25:26Z","number":7610,"body":"Clicking on a document reference will open its external URL.\r\n\r\nI added a new ClientWorkspace action for this.","mergeCommitSha":"5b39c474644425ce64090746f3834b79d0afb6d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7610","title":"Doc refs open external URLs","createdAt":"2023-08-16T19:59:14Z"}
{"state":"Merged","mergedAt":"2023-08-16T20:17:31Z","number":7611,"mergeCommitSha":"ecc1a59c4515d0c6a6e9e46db4938473111be63c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7611","title":"Fix local file decompression","createdAt":"2023-08-16T20:00:00Z"}
{"state":"Merged","mergedAt":"2023-08-16T20:10:13Z","number":7612,"body":"These changes are deployed to both Dev and Prod","mergeCommitSha":"baf89f6870c01a701bbab2ed7fa1d11f2f50f1cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7612","title":"adding statemachine perms to scm service","createdAt":"2023-08-16T20:06:44Z"}
{"state":"Merged","mergedAt":"2023-08-17T17:28:37Z","number":7613,"body":"For retrieving embedded documents like Confluence. Also adds a new InsightType (`InsightType.Document`), a new DocumentType (`DocumentType.Document`), and two new EmbeddingMetadata fields (`title` and `externalUrl`).","mergeCommitSha":"796d8871b4cf2e4620fef5e8c7a1238e6f69e697","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7613","title":"Add DocumentSearchResult","createdAt":"2023-08-16T20:16:26Z"}
{"state":"Closed","mergedAt":null,"number":7614,"body":"Add basic debug logs to partially inspect installation logs.","mergeCommitSha":"2064234017c217ef4ba44763656212b0f83fc2cc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7614","title":"Add logs to debug findInstallationsAndRepos","createdAt":"2023-08-16T20:30:04Z"}
{"state":"Merged","mergedAt":"2023-08-16T21:20:27Z","number":7615,"mergeCommitSha":"ecae0952c746b636ccfbe110b6d474c0e56f9c2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7615","title":"new topic_classifier_experiment with topic_count","createdAt":"2023-08-16T20:38:41Z"}
{"state":"Merged","mergedAt":"2023-08-16T22:14:49Z","number":7616,"body":"* Sticky vscode tabs in PR views\r\n* Move PR state badge to inline \r\n* Fix other small bugs","mergeCommitSha":"5c003459e129cf6347ee6b7d19a2bf640927613c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7616","title":"UI cleanup","createdAt":"2023-08-16T21:07:09Z"}
{"state":"Merged","mergedAt":"2023-08-16T22:10:35Z","number":7617,"mergeCommitSha":"36154781bcb8dd215fb33547537874820da2817c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7617","title":"Enabled code ingestion in PROD and configure service quota","createdAt":"2023-08-16T21:36:49Z"}
{"state":"Merged","mergedAt":"2023-08-16T22:11:39Z","number":7618,"mergeCommitSha":"2fb67eac6f7de67d004a69f8b06ee4a7b10eff37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7618","title":"Fix Code Ingest column in Repos Page","createdAt":"2023-08-16T21:42:52Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:22:20Z","number":7619,"body":"Update to handle array of rootCommitShas.","mergeCommitSha":"bc2a38a056d0efeb956f52b79936e67078e4b1d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7619","title":"Update API to handle repoCommitShas","createdAt":"2023-08-16T21:52:48Z"}
{"state":"Closed","mergedAt":null,"number":762,"body":"Apparently, performance of yaml parsing is superior to json.\r\n","mergeCommitSha":"ec80b28684266bd7730143a68e6ff06a3c5a40a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/762","title":"Move to using Yaml Serialization for Kotlin and Typescript","createdAt":"2022-04-03T22:20:45Z"}
{"state":"Merged","mergedAt":"2023-08-16T22:28:05Z","number":7620,"mergeCommitSha":"d055f3047ba8d53e1e72f423a71e9f0685fa1bf9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7620","title":"Add 70b instance","createdAt":"2023-08-16T22:28:00Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:26:30Z","number":7621,"body":"Add konami code bypass that allows us to inspect the state of the dashboard.\r\n\r\nThis bypasses the processing state as well as `hasCompletedOnboardingIntegrations`\r\n\r\nSet `KONAMI_KEY` to true in local storage to trigger bypass. May require refresh after setting value.","mergeCommitSha":"f0adc27a3536327b6bf3db33d389f56d7f353b35","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7621","title":"Add konami bypass to dashboard","createdAt":"2023-08-16T22:38:48Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:22:28Z","number":7622,"mergeCommitSha":"2c0634ab6a9f424f5bfbd19338b5ce1f62dd0e43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7622","title":"Search results should open PR views where possible","createdAt":"2023-08-16T22:46:25Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:39:55Z","number":7623,"mergeCommitSha":"203b5ef426a2a8c88027ba2536d085f46a2abfdc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7623","title":"Add topic priority","createdAt":"2023-08-16T23:11:17Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:22:09Z","number":7624,"mergeCommitSha":"489d2212270d976347c6ac3c198c6b051f4ac36c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7624","title":"Prioritize newer repos for code ingestion","createdAt":"2023-08-16T23:11:51Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:29:13Z","number":7625,"mergeCommitSha":"052b653bbe043aba0056881227add9e116760073","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7625","title":"Deserialize confluence links","createdAt":"2023-08-16T23:15:44Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:27:02Z","number":7626,"body":"JIra service is being throttled. Extended throttling results in Linux scheduler killing the pod with exit code 137.\r\n\r\nSetting a higher limit allows the pod to burst without risks of throttling. \r\n\r\nhttps://getunblocked.grafana.net/d/6581e46e4e5c7ba40a07646395ef7b23/kubernetes-compute-resources-pod?orgId=1&var-datasource=default&var-cluster=prod-us-west-2&var-namespace=default&var-pod=jiraservice-bb68bb854-8cwcf&var-lokids=All&from=1692225999139&to=1692226778652","mergeCommitSha":"1290fb6ee0bdb5ca4d3b315b5c1e065acdbbf899","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7626","title":"increase cpu limit for JIRA service","createdAt":"2023-08-16T23:18:43Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:37:59Z","number":7627,"mergeCommitSha":"7e4ea2e0f4f9528736c6ac4937cd2705b30b7811","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7627","title":"Fix looping","createdAt":"2023-08-16T23:28:37Z"}
{"state":"Merged","mergedAt":"2023-08-16T23:57:23Z","number":7628,"mergeCommitSha":"588e65725e8eee1196b215e30317a2992326ee9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7628","title":"Fix padding","createdAt":"2023-08-16T23:45:59Z"}
{"state":"Merged","mergedAt":"2023-08-17T18:04:00Z","number":7629,"body":"Update so clients send remotes & all root commit shas to API service when resolving repos.\r\n\r\n","mergeCommitSha":"112461aead50d230e6a1a8aa7092836e7a4dec4f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7629","title":"Update to use root commit shas","createdAt":"2023-08-17T00:02:46Z"}
{"state":"Merged","mergedAt":"2022-04-05T23:09:20Z","number":763,"body":"## Summary\r\nWill stop and process video recordings that exceed the maximum recording duration (currently 60 seconds)","mergeCommitSha":"9c3d803a9899632bbcdbc2092295746c2b685198","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/763","title":"Video recording background maintenance job","createdAt":"2022-04-04T15:54:37Z"}
{"state":"Merged","mergedAt":"2023-08-17T00:38:28Z","number":7630,"body":"We are looking into the issue. This reduces the scale to 1 and caps it at max 2.","mergeCommitSha":"6f66eaa6a61c4ce4635e084edbbf7d92019a979b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7630","title":"this is temp while we troubleshoot","createdAt":"2023-08-17T00:28:50Z"}
{"state":"Merged","mergedAt":"2023-08-17T01:14:00Z","number":7631,"body":"For better topic generation","mergeCommitSha":"6fb493ff474dc05bf47dd2d956e0aee701beecdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7631","title":"Push all Jira issues to S3","createdAt":"2023-08-17T01:00:58Z"}
{"state":"Merged","mergedAt":"2023-08-17T01:56:38Z","number":7632,"mergeCommitSha":"6a81fb58f9eba0a5a2bb5dad97a0c440fc3ebd3d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7632","title":"Include Jira issues for topic pipeline","createdAt":"2023-08-17T01:38:54Z"}
{"state":"Merged","mergedAt":"2023-08-17T02:47:24Z","number":7633,"mergeCommitSha":"90b275d328737c907861dfb0c966261a1dbb30e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7633","title":"remove llama2","createdAt":"2023-08-17T02:47:19Z"}
{"state":"Merged","mergedAt":"2023-08-17T04:07:29Z","number":7634,"body":"https://www.notion.so/nextchaptersoftware/Auto-Source-Code-Ingest-42102fc2c91a4826a72c93d6ed754571\r\n","mergeCommitSha":"0d5b60f24d4303bcb316d70bb921680031e596a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7634","title":"Controls to Backfill and Re-ingest Source Code Ingestion","createdAt":"2023-08-17T03:22:29Z"}
{"state":"Merged","mergedAt":"2023-08-17T08:06:24Z","number":7635,"mergeCommitSha":"7d95d53642dd408058ae3b767266c424d41e01ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7635","title":"Update cdk","createdAt":"2023-08-17T08:06:10Z"}
{"state":"Merged","mergedAt":"2023-08-17T08:55:50Z","number":7636,"mergeCommitSha":"be3f0a256c330a0a7a727d190c616e1243ca4445","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7636","title":"Add jira processor openai pipeline","createdAt":"2023-08-17T08:49:03Z"}
{"state":"Merged","mergedAt":"2023-08-17T18:29:52Z","number":7637,"body":"For now we won't partition because we need to figure out how to do incremental pinecone db updates for a file that spread across multiple embeddings.\r\n\r\nThe plan is to eventually create a separate service that will handle take over handling all `SearchEvent.Embed*` events.","mergeCommitSha":"d4645b58186b4d784cba0e8b2fa82ca0d5e4198d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7637","title":"Embed confluence pages and blog posts","createdAt":"2023-08-17T17:48:18Z"}
{"state":"Merged","mergedAt":"2023-08-23T21:21:06Z","number":7638,"body":"Revert \"Revert \"Generate sparse Pinecone embeddings for source code (#7464)\" (#7570)\"\n\nThis reverts commit 14c4b7215673477d6191840e91db8c74a9d88627.","mergeCommitSha":"0ab325315bff1033f54905bb381d0b76ffd09332","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7638","title":"[Revive] Generate sparse Pinecone embeddings for source code","createdAt":"2023-08-17T17:48:51Z"}
{"state":"Merged","mergedAt":"2023-08-21T18:04:04Z","number":7639,"body":"Clear the id from the context when the highlighting state clears. This prevents the UI from re-scrolling.","mergeCommitSha":"132ce903de0d9c12702ce05cde3201c9c9bbe183","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7639","title":"Clear thread id from context","createdAt":"2023-08-17T17:53:58Z"}
{"state":"Merged","mergedAt":"2022-04-04T22:31:26Z","number":764,"body":"Very basic stream for local values.","mergeCommitSha":"12822c65b1434d5fef6668251c70d1984cc7134a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/764","title":"Value Cache Stream","createdAt":"2022-04-04T17:02:23Z"}
{"state":"Merged","mergedAt":"2023-08-17T18:38:38Z","number":7640,"mergeCommitSha":"489d8815899369b612f0752c8211fe6c763f0b9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7640","title":"Redirect includes confluence provider type","createdAt":"2023-08-17T18:27:42Z"}
{"state":"Merged","mergedAt":"2023-08-17T19:06:43Z","number":7641,"mergeCommitSha":"50975dd73b566dca2f15db76ca33df05258f84a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7641","title":"Confluence pages and blog posts author can be null","createdAt":"2023-08-17T18:37:47Z"}
{"state":"Merged","mergedAt":"2023-08-29T18:07:28Z","number":7642,"body":"Show repo name in tooltips and the disambiguation dialog and tooltips\r\n\r\n<img width=\"1582\" alt=\"Screenshot 2023-08-29 at 11 04 46 AM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/b817f79e-7f4b-4177-bb26-5084614f93cf\">\r\n","mergeCommitSha":"7ea7f215f322d83db2f550568c8a9f6dfa718104","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7642","title":"Render repo name in reference UIs","createdAt":"2023-08-17T18:47:05Z"}
{"state":"Merged","mergedAt":"2023-08-17T19:14:27Z","number":7643,"mergeCommitSha":"29d7f9f794a9d51d1e4a7b89909662243612f453","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7643","title":"Fix redirect url","createdAt":"2023-08-17T19:04:14Z"}
{"state":"Merged","mergedAt":"2023-08-17T19:58:24Z","number":7644,"mergeCommitSha":"a3954808e09e8c6f884dc4cee15718c3b01ec362","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7644","title":"Enable Confluence","createdAt":"2023-08-17T19:23:12Z"}
{"state":"Merged","mergedAt":"2023-08-17T19:31:24Z","number":7645,"body":"We have a configurable way of adding transaction/lock timeouts.\r\nOf the two, idle transaction timeouts is the safest, while lock_timeout can be hairy.\r\n\r\nWe noticed a long running execution of a statement (more than an hour yesterday).\r\nWe should be killing these guys.","mergeCommitSha":"b48cba135b93dc1c43e6c06ceb2e6e12534dbd46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7645","title":"Add database transaction timeouts","createdAt":"2023-08-17T19:25:20Z"}
{"state":"Merged","mergedAt":"2023-08-17T20:48:44Z","number":7646,"mergeCommitSha":"cd067fede0599f1b9c9d946905ad58891c8c950e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7646","title":"Revert \"Hide confluence (#7600)\"","createdAt":"2023-08-17T19:27:04Z"}
{"state":"Merged","mergedAt":"2023-10-06T06:00:51Z","number":7647,"mergeCommitSha":"73e25eaae99bcc32457da428928f1041f87e70f8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7647","title":"More realistic delete repo unit test to exercise DB slow query","createdAt":"2023-08-17T19:43:58Z"}
{"state":"Merged","mergedAt":"2023-08-17T20:06:32Z","number":7648,"mergeCommitSha":"39ba0da5926afca57ce32adca4492d7d31a7bfe1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7648","title":"Statement timeout","createdAt":"2023-08-17T20:06:26Z"}
{"state":"Merged","mergedAt":"2023-08-17T20:33:41Z","number":7649,"mergeCommitSha":"c3e18cc904b37855c892a9f4fadee226e2a00612","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7649","title":"Include compressed content as metadata for confluence embedding","createdAt":"2023-08-17T20:07:28Z"}
{"state":"Merged","mergedAt":"2022-04-04T17:19:27Z","number":765,"body":"only in safari:\r\n<img width=\"209\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/161595553-90d82fe8-cab6-4431-9eb5-d43b41d1aa28.png\">\r\n\r\n\r\n","mergeCommitSha":"2b36ab22c9d4c87db42b296c4f859fa9b9cd0845","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/765","title":"Fix context menu display bug","createdAt":"2022-04-04T17:07:04Z"}
{"state":"Closed","mergedAt":null,"number":7650,"body":"Push dev and prod secrets","mergeCommitSha":"110f10e2cb36b3ffbf30ac6fcd311b976751b7d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7650","title":"Update sendgrid","createdAt":"2023-08-17T20:23:13Z"}
{"state":"Closed","mergedAt":null,"number":7651,"mergeCommitSha":"418ec7a540edbe2f3d0c949becd74d6fc67a0cb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7651","title":"Update sendgrid secrets","createdAt":"2023-08-17T20:26:25Z"}
{"state":"Merged","mergedAt":"2023-08-17T20:42:20Z","number":7652,"mergeCommitSha":"6cb6c1b3817c1fd9a29229be6c6414189635fde7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7652","title":"updated sendgrid secrets","createdAt":"2023-08-17T20:30:17Z"}
{"state":"Merged","mergedAt":"2023-08-23T19:55:34Z","number":7653,"mergeCommitSha":"c5384f648663255d065c4685991708e3dd4659b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7653","title":"Add foreign key indices where it makes sense","createdAt":"2023-08-17T20:47:22Z"}
{"state":"Merged","mergedAt":"2023-08-17T21:53:14Z","number":7654,"mergeCommitSha":"4a3471da732f9d4b9893c46238928f4ff5e24498","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7654","title":"Include local documents in references","createdAt":"2023-08-17T21:01:07Z"}
{"state":"Merged","mergedAt":"2023-08-17T21:49:58Z","number":7655,"mergeCommitSha":"37b7d01cbb9037c0a70d08dbc9d2a8dea53f05e9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7655","title":"Set fileContent for EmbeddingMetadata.metadata","createdAt":"2023-08-17T21:28:31Z"}
{"state":"Merged","mergedAt":"2023-08-18T19:43:05Z","number":7656,"body":"Previous default polling interval was way too aggressive. I have set it to 120 for services that are allowed to scale up to high numbers. This should help reduce some of the shock on our DB during scaling operations","mergeCommitSha":"96584b2286462343e3050e3f29d4bbf7e91ab899","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7656","title":"change the polling interval for bg services","createdAt":"2023-08-17T21:43:37Z"}
{"state":"Merged","mergedAt":"2023-08-17T22:39:59Z","number":7657,"body":"Skip this repo and the 1000s of forks/copies like `SiMa-ai/simaai-linux`.","mergeCommitSha":"b5ea0a938c990088901a19e420d113891e59fb89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7657","title":"Do not ingest the Linux Kernel","createdAt":"2023-08-17T22:22:04Z"}
{"state":"Merged","mergedAt":"2023-08-18T02:28:33Z","number":7658,"mergeCommitSha":"02fd14e68b81141b4ac3310818ac9c9e60450544","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7658","title":"Enable incremental confluence sync","createdAt":"2023-08-17T22:24:57Z"}
{"state":"Merged","mergedAt":"2023-08-17T22:58:42Z","number":7659,"mergeCommitSha":"24548406b452ee602df0765a0d8f3c99979a913a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7659","title":"Semantic search includes InsightType.Document","createdAt":"2023-08-17T22:44:09Z"}
{"state":"Merged","mergedAt":"2022-04-04T18:20:52Z","number":766,"body":"This pr adds channels for determining team member updates.\r\n\r\nAlso augments team member logic for modified at.","mergeCommitSha":"bb66519be56ae5d4b260df4092f95dbbc27d823c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/766","title":"AddTeamMembersPushChannels","createdAt":"2022-04-04T17:46:24Z"}
{"state":"Merged","mergedAt":"2023-08-21T18:03:50Z","number":7660,"mergeCommitSha":"a05ee721ee2f126dba08e14eeda63dbdf8adcf2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7660","title":"useAsyncOperation should run on dependency list","createdAt":"2023-08-17T23:03:59Z"}
{"state":"Merged","mergedAt":"2023-08-18T00:13:58Z","number":7661,"body":"Slack at mentions can be non-space delimited.\r\n","mergeCommitSha":"17b3f7cf396912eb0f8060ba46ef9d388b1f0c51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7661","title":"Fix duplicat slack bot message due to buggy mention parsing","createdAt":"2023-08-17T23:50:23Z"}
{"state":"Merged","mergedAt":"2023-08-18T02:03:56Z","number":7662,"body":"Fixes\r\n\r\n<img width=\"1340\" alt=\"CleanShot 2023-08-17 at 17 29 40@2x\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1924615/ef992869-8c31-4b54-8b73-5d7c958a8e0e\">\r\n","mergeCommitSha":"647e37f9df70689bb429b292f5dac04b222a93bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7662","title":"Fix provider for confluence references","createdAt":"2023-08-18T00:30:38Z"}
{"state":"Merged","mergedAt":"2023-08-18T04:49:23Z","number":7663,"mergeCommitSha":"3e275bfc798651d90b6ff069b8998c21ddf49d27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7663","title":"Include provider in DocumentInsightIndexContentModel.contentsWithMetadata","createdAt":"2023-08-18T04:17:44Z"}
{"state":"Closed","mergedAt":null,"number":7664,"body":"`groupId` can represent anything, depending on the provider","mergeCommitSha":"d9a81fac05543273f50e8182b0b454f6186577c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7664","title":"Rename confluenceSpaceId to groupId","createdAt":"2023-08-18T05:14:15Z"}
{"state":"Merged","mergedAt":"2023-08-18T18:09:18Z","number":7665,"mergeCommitSha":"4d87bf668a274b2b7e57c0441ff0528f74430210","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7665","title":"Do a reingest when a customer updates confluence configuration","createdAt":"2023-08-18T05:35:08Z"}
{"state":"Merged","mergedAt":"2023-08-18T10:48:35Z","number":7666,"mergeCommitSha":"418aeb098f5225ae2cd574df05220a50936894c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7666","title":"Refinery api keys","createdAt":"2023-08-18T10:48:18Z"}
{"state":"Merged","mergedAt":"2023-08-18T17:23:58Z","number":7667,"body":"There is no point in having logs if we’re not going to act on them, especially erroring.\r\nWe are flooding logz.io with this stuff.\r\n\r\nIf we need to log this in the future we can revert this change.\r\nAlso the change introduces an implicit dependency on logging library, which I'd rather not have.","mergeCommitSha":"8c3ff0235576ffb5eb02231525024d501dc5001c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7667","title":"Remove API constraint logs","createdAt":"2023-08-18T11:52:41Z"}
{"state":"Merged","mergedAt":"2023-08-18T18:08:12Z","number":7668,"body":"No need for subtitle in PRs because the title already shows the PR title + #: \r\n![image](https://github.com/NextChapterSoftware/unblocked/assets/13431372/d2da6d31-597e-49b6-9c2b-e82ea8f2cf78)\r\n","mergeCommitSha":"ccfc9c140b816964ef93baadee272662aac43389","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7668","title":"Remove dupe PR titles","createdAt":"2023-08-18T17:57:14Z"}
{"state":"Merged","mergedAt":"2023-08-18T23:27:09Z","number":7669,"body":"Note that the patterns will not be respected yet, that will come in a later PR.","mergeCommitSha":"03e6064abd0ccf083d4341f3605096d13bcf3c31","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7669","title":"Implement getSlackConfigurationV2 and postSlackConfigurationV2","createdAt":"2023-08-18T18:54:24Z"}
{"state":"Merged","mergedAt":"2022-04-04T19:13:25Z","number":767,"mergeCommitSha":"3d2828f0f7a2d6cdd790aa566f9da1a44c1e3e3c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/767","title":"Add asset service skeleton","createdAt":"2022-04-04T18:55:14Z"}
{"state":"Merged","mergedAt":"2023-08-18T19:05:09Z","number":7670,"mergeCommitSha":"6b9fbe411a279cac5aa1858559593cbac2f53c72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7670","title":"Log propmt","createdAt":"2023-08-18T19:02:58Z"}
{"state":"Merged","mergedAt":"2023-08-18T20:12:54Z","number":7671,"mergeCommitSha":"07eb61522bf943a7c3e2d4b65b05fe64ecc3167b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7671","title":"Re-write the OpenAI prompt parser and add tests","createdAt":"2023-08-18T19:53:27Z"}
{"state":"Merged","mergedAt":"2023-08-18T20:47:14Z","number":7672,"mergeCommitSha":"76b8d1f2d946f3271834e2507a1108f6f664ac5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7672","title":"Add logging to ConfluenceEmbeddingService","createdAt":"2023-08-18T20:27:24Z"}
{"state":"Merged","mergedAt":"2023-08-18T21:47:48Z","number":7673,"body":"`ChatRole` is not ours so decided to just contain is to where it's actually used. It's also a value class with companion defined instances, so can't do usual enum tricks. ","mergeCommitSha":"b653a617647a50ea559db70a44b8b37cd93dd7d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7673","title":"Sup Rashin","createdAt":"2023-08-18T21:26:59Z"}
{"state":"Merged","mergedAt":"2023-08-18T23:04:55Z","number":7674,"mergeCommitSha":"9e3f8423e475915cd218990a363a90fb9cb13369","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7674","title":"Fix ingestion of Confluence blog posts","createdAt":"2023-08-18T22:47:32Z"}
{"state":"Merged","mergedAt":"2023-08-23T20:33:51Z","number":7675,"mergeCommitSha":"b55a93f6e7b6db6365aa956febb15f88d87aed19","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7675","title":"Use Sendgrid for team invite followup","createdAt":"2023-08-18T23:04:47Z"}
{"state":"Merged","mergedAt":"2023-08-21T18:03:41Z","number":7676,"body":"We should not show the skip link if everything has already been connected\r\n\r\nBefore:\r\n<img width=\"1269\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/456700c3-2665-49b2-8b78-833e5aaf589c\">\r\n\r\nAfter:\r\n<img width=\"1241\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/cbd1ccad-6ed1-4c38-b793-aab00aa3b962\">\r\n","mergeCommitSha":"d17a622663300d33452ef05db97069166d955357","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7676","title":"Fix onboarding skip logic","createdAt":"2023-08-18T23:13:00Z"}
{"state":"Merged","mergedAt":"2023-08-31T22:25:21Z","number":7677,"body":"Add banner to prompt users to download the hub:\r\n<img width=\"1504\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/ac732ca4-c331-407b-9703-5e60af964d86\">\r\n\r\nChange entails changing a bit of how we set the top level banner and where the logic lives. Given how we have sticky positioned elements in several areas of the dashboard, we have to change the height offset of the header depending on the presence of a banner, relative to the content. it seemed best fitting to consolidate the layout logic to the Navigator level. Open to alternative suggestions re: implementation.\r\n","mergeCommitSha":"3c7bc888f7d8f2841a47941527f50252918a0c3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7677","title":"Download hub banner in dashboard","createdAt":"2023-08-18T23:23:54Z"}
{"state":"Merged","mergedAt":"2023-08-22T16:37:15Z","number":7678,"body":"With this change, the `AllChannels` flag will be ignored and ingestion will only proceed on channels that are specifically selected and/or match a pattern.","mergeCommitSha":"5468d4e5dc5a8c401771d9dcb46dcb2c30008c45","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7678","title":"Update SlackChannelPreferencesService to support patterns","createdAt":"2023-08-18T23:46:24Z"}
{"state":"Merged","mergedAt":"2023-08-21T17:49:05Z","number":7679,"mergeCommitSha":"a0ea58d3f793251a70876ea9d4aa522d4ae999a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7679","title":"Update database libraries and versions","createdAt":"2023-08-19T01:35:01Z"}
{"state":"Merged","mergedAt":"2022-04-04T19:40:11Z","number":768,"body":"- Added new stack to create customer assets' buckets\r\n- Added placeholder lambda edge funtions\r\n- Added CF behavior for `/assets/*`\r\n- Added public site bucket to hold error images and assets\r\n- Cleaned up some old unused dependencies \r\n- Exported bucket object in Agora streaming stack ","mergeCommitSha":"65ec6e6c03431454446872ce88e27b85c22e7bd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/768","title":"Added S3 bucket and CF behavior for static assets","createdAt":"2022-04-04T19:30:29Z"}
{"state":"Merged","mergedAt":"2023-08-19T17:00:14Z","number":7680,"body":"Underlying cause was a change in how Notion (or electron in general) constructs their Accessibility tree. The web views were no longer accessible from the root window element. Solution was to pick the centre of the app, find the element at the centre, and then walk up the tree until the web view is discovered. Works in all cases for electron apps since they are rendered using a web view. ","mergeCommitSha":"7aa6defcab3988800b693993230e682f5f9438e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7680","title":"Fixes slurping by using bi-directional search for web elements","createdAt":"2023-08-19T15:48:50Z"}
{"state":"Merged","mergedAt":"2023-08-21T03:46:43Z","number":7681,"body":"Dennis has been periodically experiencing an installer issue where the bits get properly installed but the app. never comes back up. The cause of this seems to be sleep mode, and only on some devices or system configurations. The current installer implementation will give up after some retries if it can't launch the app. In this new implementation, the installer will just retry for infinity to launch the app. ","mergeCommitSha":"baaa029e4d6e70360a7b9d6045911f8fc1f1aad2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7681","title":"Thor hammer the installer","createdAt":"2023-08-20T00:13:51Z"}
{"state":"Merged","mergedAt":"2023-08-21T20:20:13Z","number":7682,"body":"The idea here is to extract all channels where at least one PR-linked slack thread exists and enable those channels for ingestion, to allow migrating away from `AllChannel` ingestion","mergeCommitSha":"4de5951401b88a2f2606e8c62c9cf19e4fc512c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7682","title":"Migrate slack team preferences with SlackChannelIngestionType.AllChannels","createdAt":"2023-08-21T19:58:10Z"}
{"state":"Merged","mergedAt":"2023-08-21T21:21:40Z","number":7683,"body":"- Update ktlint\r\n- More ktlint changes\r\n","mergeCommitSha":"5d5479711eb1d6e2dbcb74d9172ab2bbf2a04ff1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7683","title":"UpdateKtlint","createdAt":"2023-08-21T20:29:55Z"}
{"state":"Merged","mergedAt":"2023-08-21T21:03:41Z","number":7684,"mergeCommitSha":"39f318470b22247b7b037a6870e66b6a1766e937","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7684","title":"Fix getSlackChannelRelatedObjects","createdAt":"2023-08-21T20:49:23Z"}
{"state":"Merged","mergedAt":"2023-08-22T17:25:42Z","number":7685,"mergeCommitSha":"5bcbf88ab3a26750409eb4d8e785b088964aaad9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7685","title":"Enable slack bot at mention for all teams with our slack app installed","createdAt":"2023-08-21T21:01:25Z"}
{"state":"Merged","mergedAt":"2023-08-21T21:34:51Z","number":7686,"mergeCommitSha":"178537501045fa8d929a51362097496e230461e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7686","title":"Update gradle","createdAt":"2023-08-21T21:11:13Z"}
{"state":"Merged","mergedAt":"2023-08-22T03:38:54Z","number":7687,"mergeCommitSha":"96343d30ac1e19db0bb7231ccc8211ac034cf35f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7687","title":"Fix dropEmbeddingsConfluenceSite","createdAt":"2023-08-21T21:18:20Z"}
{"state":"Merged","mergedAt":"2023-08-21T22:58:16Z","number":7688,"mergeCommitSha":"b848761cacf0ceaec715e967c710e5deceb35326","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7688","title":"Fix SlackMigrator","createdAt":"2023-08-21T21:51:47Z"}
{"state":"Merged","mergedAt":"2023-08-21T23:03:35Z","number":7689,"mergeCommitSha":"4eed05cbe3ed11b3b2c2dbad405455ae9d779f43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7689","title":"Parse isQuestionAnswered response for CLASSIFICATION: token","createdAt":"2023-08-21T22:42:49Z"}
{"state":"Merged","mergedAt":"2022-04-04T22:05:51Z","number":769,"body":"When generating sourcemark TS code, use the same TS generator we use for messages.  It generates more idiomatic typescript and handles optionals.\r\n\r\nAs part of this, I switched the token response message to use the google wrapper type, as we do in messages.  This allows the optional scalar value to work correctly.\r\n\r\nNote that all required properties that refer to other messages are generated as optionals!  I'm not sure why.","mergeCommitSha":"46416964b81a0e88b0d854767d318aa2f18d3a53","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/769","title":"Switch TS proto lib","createdAt":"2022-04-04T19:44:41Z"}
{"state":"Closed","mergedAt":null,"number":7690,"mergeCommitSha":"cf7e0c806d1d6dd24de221275a72dcaca22eab7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7690","title":"Make migration transaction smaller","createdAt":"2023-08-21T23:35:14Z"}
{"state":"Merged","mergedAt":"2023-08-22T00:32:38Z","number":7691,"mergeCommitSha":"c233feb00c36c5aa775c9156b636a96a0e535785","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7691","title":"Add slack validation channel","createdAt":"2023-08-22T00:01:12Z"}
{"state":"Merged","mergedAt":"2023-08-22T22:42:35Z","number":7692,"body":"* VSCode will now re-notify when there is a pending update, every 4 hours\r\n* The hub will notify when any IDE has a pending update, daily.  Clicking on the notification restarts the IDE\r\n* VSCode-only for now -- IntelliJ coming in the next PR\r\n\r\n<img width=\"370\" alt=\"Screenshot 2023-08-21 at 5 52 27 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/64e3f705-9f2d-4fca-9f6a-24e7c4cf9201\">\r\n<img width=\"375\" alt=\"Screenshot 2023-08-21 at 5 52 31 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/e1b9f02c-7b0a-43b5-8ab4-371468518b20\">\r\n\r\nImplementation notes:\r\n* IDE plugins now tell the hub their build number in the configure streaming call\r\n* Hub has a new class (`IDEPluginNotifier`) that takes in the latest-installed hub version, and the plugin build versions, and manages when to send notifications\r\n* There is a new hub API action for the hub to tell the IDEs that they have a pending update, and when to restart","mergeCommitSha":"5c01cceab8439c4da89ef9fb0628b4870e471711","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7692","title":"Hub-controlled IDE plugin updates","createdAt":"2023-08-22T00:38:05Z"}
{"state":"Merged","mergedAt":"2023-08-22T02:01:11Z","number":7693,"mergeCommitSha":"6b6e0a6bd3015a22312d8edefd2df7bd25749d4b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7693","title":"Add test","createdAt":"2023-08-22T01:36:53Z"}
{"state":"Merged","mergedAt":"2023-08-22T02:32:47Z","number":7694,"mergeCommitSha":"9c4a24beded6af13645837661342c43247d577c5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7694","title":"Fix slack channel ingestion","createdAt":"2023-08-22T02:19:35Z"}
{"state":"Merged","mergedAt":"2023-08-22T03:57:18Z","number":7695,"mergeCommitSha":"7baafd87b1fe721523e94b42b271fcc89b0f8e81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7695","title":"Upgradeslack","createdAt":"2023-08-22T03:10:57Z"}
{"state":"Merged","mergedAt":"2023-08-22T07:31:07Z","number":7696,"body":"- Created ACM certs for email.getunblocked.com as our branded link on SendGrid\r\n- Created a new stack to provision CloudFront distro with Sendgid as the origin\r\n- Updated prod config in standard region to deploy the new stack.\r\n\r\nNote: The new CloudFront distro is only deployed to prod. We are trying to figure out how to separate branded link in Dev from Prod. Until then we will use email.getunblocked.com as the branded link for both environments.","mergeCommitSha":"1860050b2c7adba9f75de19fad6076e3e5d4b3db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7696","title":"Adding support for branded links using CloudFront","createdAt":"2023-08-22T07:18:44Z"}
{"state":"Merged","mergedAt":"2023-08-22T17:04:31Z","number":7697,"mergeCommitSha":"524d2db4d0ddc9b0ef84475dde66527ca868dbbf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7697","title":"Database timeouts clenaup","createdAt":"2023-08-22T17:04:11Z"}
{"state":"Merged","mergedAt":"2023-08-22T17:11:27Z","number":7698,"mergeCommitSha":"142ca2576d0d28e28f3354e31070599db7a1d521","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7698","title":"Database timeouts for prod","createdAt":"2023-08-22T17:05:24Z"}
{"state":"Closed","mergedAt":null,"number":7699,"body":"This reverts commit 142ca2576d0d28e28f3354e31070599db7a1d521.\r\n","mergeCommitSha":"48dc4d18e90642f4d7002092d388e6c2c0769796","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7699","title":"[DO NOT MERGE] Revert \"Database timeouts for prod (#7698)\"","createdAt":"2023-08-22T17:15:20Z"}
{"state":"Merged","mergedAt":"2022-01-19T18:20:25Z","number":77,"body":"It was complaining:\r\n\r\n```\r\nw: /Users/<USER>/Code/codeswell/apiservice/src/main/kotlin/com/codeswell/db/common/OperatorExtensions.kt: (19, 20): 'TypeExtensions.TSVectorType' is a final type, and thus a value of the type parameter is predetermined\r\n```","mergeCommitSha":"021ca454c4f7c1cc39aa41a8af7d4d77ee3b8401","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/77","title":"Fix warning","createdAt":"2022-01-19T18:17:53Z"}
{"state":"Merged","mergedAt":"2022-04-04T20:31:29Z","number":770,"body":"Service account has been created. EKS deploys are currently manual. ","mergeCommitSha":"e37037afd1c07dde81f3184a93288635db01559d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/770","title":"add service account for assetservice","createdAt":"2022-04-04T20:01:52Z"}
{"state":"Merged","mergedAt":"2023-08-22T17:33:47Z","number":7700,"body":"- Fix bad logic\r\n- Fix bad logic\r\n","mergeCommitSha":"5e1321bd427c4c35b288e40839eb8f563edb6cc1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7700","title":"FixBadLogic","createdAt":"2023-08-22T17:33:34Z"}
{"state":"Merged","mergedAt":"2023-08-22T17:54:35Z","number":7701,"mergeCommitSha":"de8aebb3755bd90eb5de10752cd2e8e5c13c2394","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7701","title":"Reduce slack limit to recommended","createdAt":"2023-08-22T17:52:56Z"}
{"state":"Merged","mergedAt":"2023-08-22T21:15:05Z","number":7702,"mergeCommitSha":"11c8b23cec436a022061d52098fe6b6b7b2459c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7702","title":"Add team setting for sparse vector generation","createdAt":"2023-08-22T18:03:20Z"}
{"state":"Merged","mergedAt":"2023-08-22T21:20:05Z","number":7703,"mergeCommitSha":"f753a2ba664a88c186a7f941703c05f762ac378d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7703","title":"Plumb through sparse vector generation flag to generator","createdAt":"2023-08-22T18:29:04Z"}
{"state":"Merged","mergedAt":"2023-08-22T20:05:13Z","number":7704,"mergeCommitSha":"b5f19c9ed9749df9386cd30f5120daf7d92a7026","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7704","title":"Exclude private channels from slack channel search","createdAt":"2023-08-22T19:33:56Z"}
{"state":"Merged","mergedAt":"2023-08-22T21:08:16Z","number":7705,"body":"Construct Slack integration URLs based on app IDs","mergeCommitSha":"89660b3a0362a33fcaeb7dc456c21a1b5b6b5ee8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7705","title":"Fix slack integration url","createdAt":"2023-08-22T20:03:12Z"}
{"state":"Merged","mergedAt":"2023-08-22T20:39:44Z","number":7706,"mergeCommitSha":"0343ababca9237b5c417eb8d01b83b04c318e21d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7706","title":"disable caching on sendgrid cloudfront distro","createdAt":"2023-08-22T20:38:24Z"}
{"state":"Merged","mergedAt":"2023-08-22T21:22:30Z","number":7707,"mergeCommitSha":"1d188ff3c28941e451cc749a62e3a2fa45cac44f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7707","title":"Add more slack channel event handlers","createdAt":"2023-08-22T20:54:16Z"}
{"state":"Merged","mergedAt":"2023-08-23T16:51:36Z","number":7708,"body":"https://github.com/NextChapterSoftware/unblocked/assets/13431372/f3df6f0e-b5d9-4dcd-8d68-ee3ae37bf927\r\n\r\n* By default, a Toast component is a styled banner that triggers for a given action and fades away on its own\r\n* Only added to the web/ directory for now; unclear if a custom component like this makes sense on any of the other clients\r\n* Added to settings UIs on save (replacing the old banner which was styled wrong)\r\n* Will add to other areas in the next PR (i.e. when a topic is created/edited, when slack has been connected, etc)","mergeCommitSha":"9c4497de856232d7e9c60d8bd3f536d87acd1682","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7708","title":"Add toast component","createdAt":"2023-08-22T21:11:24Z"}
{"state":"Merged","mergedAt":"2023-08-22T21:28:02Z","number":7709,"mergeCommitSha":"e68fc99cfbd86c25dbb2ddef87e89452e14fda0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7709","title":"adding new bucket for the docs site","createdAt":"2023-08-22T21:27:08Z"}
{"state":"Merged","mergedAt":"2022-04-04T20:43:14Z","number":771,"mergeCommitSha":"3da9e67c54a78ea9720a0e98e6062faa72c7462b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/771","title":"Add helm charts ofr assetservice","createdAt":"2022-04-04T20:16:17Z"}
{"state":"Merged","mergedAt":"2023-08-22T22:54:50Z","number":7710,"body":"* Add a new reverse-stream agent API (PluginCommand) -- this is a one-way set of commands the agent can call in the plugin (analogous to AgentCommand, just the other direction).  This replaces OpenBrowserCOmmand, and I added two new commands: NotifyPluginUpdate and Restart\r\n* Agent uses this channel to notify the plugin and restart as needed (as required by the hub)\r\n* Remove JB-specific update checking code","mergeCommitSha":"6b603a6f66fcb5243c1fcc36843076ae7548f5ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7710","title":"Hub-managed plugin update notifications in JB IDEs","createdAt":"2023-08-22T22:23:38Z"}
{"state":"Merged","mergedAt":"2023-08-22T23:54:42Z","number":7711,"body":"Every minute we poll to see if any clients need to be updated, and every minute the logic was wrong -- the result would always be true, so we would always pop up the notification on startup.  This fixes the bug.","mergeCommitSha":"c37bb935922d26fd85ccff0e82e7b3b37b77e440","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7711","title":"Fix bad client update polling logic","createdAt":"2023-08-22T23:44:02Z"}
{"state":"Merged","mergedAt":"2023-08-29T17:38:06Z","number":7712,"body":"Remove message editor chin.  Also, remove the options to have the cancel/post buttons outside of the editor, as we never used this option anymore (buttons are now always inline).\r\n\r\n<img width=\"1062\" alt=\"Screenshot 2023-08-22 at 5 10 05 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/3fa5bac9-176b-4dd3-bbb3-8a41987872fb\">\r\n<img width=\"1062\" alt=\"Screenshot 2023-08-22 at 5 10 23 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/b280bbeb-943d-450b-875d-5d95b631f4fa\">\r\n<img width=\"1244\" alt=\"Screenshot 2023-08-22 at 5 11 44 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/aa91bdf4-84f4-4646-95c2-328cb9342bbe\">\r\n<img width=\"1439\" alt=\"Screenshot 2023-08-22 at 5 12 40 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/7c149cc2-ce41-4aae-ab50-5173f7e65a17\">\r\n<img width=\"1496\" alt=\"Screenshot 2023-08-22 at 5 14 22 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/4e432d38-9173-481f-a67f-17bee4f298dd\">\r\n<img width=\"1496\" alt=\"Screenshot 2023-08-22 at 5 14 33 PM\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/2133518/489ff9a2-be07-40a5-aadd-8221be71cbd3\">\r\n","mergeCommitSha":"8f44c03a15913e4d893f4b7f43c2ede630094476","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7712","title":"Remove message editor chin","createdAt":"2023-08-23T00:15:20Z"}
{"state":"Merged","mergedAt":"2023-08-23T00:50:09Z","number":7713,"body":"This change enables all global edge locations on our primary CloudFront distribution. This way user traffic would enter Amazon fabric as early as possible. \r\n\r\nHopefully this will also improve our global average access time. ","mergeCommitSha":"2018f34a45cfe58f9b0989ff8bcf9995207da21c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7713","title":"enable all global edges for our CloudFront","createdAt":"2023-08-23T00:43:47Z"}
{"state":"Merged","mergedAt":"2023-08-23T01:52:13Z","number":7714,"body":"Save cursors and use cursors if ingestion is stopped midway.\r\n","mergeCommitSha":"746ee0a45696c920464a661ef3c9df3fb3990673","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7714","title":"OptimizeSlackIngestion","createdAt":"2023-08-23T01:28:02Z"}
{"state":"Merged","mergedAt":"2023-08-23T01:54:37Z","number":7715,"mergeCommitSha":"1f4ecc2a7b8cc58284c35439f3952c8789d1f7f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7715","title":"Slack instances","createdAt":"2023-08-23T01:54:28Z"}
{"state":"Merged","mergedAt":"2023-08-23T16:22:32Z","number":7716,"body":"This doesn't affect production builds, just local developer builds.\r\n\r\nI don't love how protobufs work here -- the client sends a nil build number, which is deserialized on the hub to a zero.  Thanks.","mergeCommitSha":"09ed73a18a740595c81ada95be0bfe0f57247fe8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7716","title":"Developer builds shouldn't pop up upgrade dialog","createdAt":"2023-08-23T03:45:41Z"}
{"state":"Merged","mergedAt":"2023-08-23T06:18:06Z","number":7717,"mergeCommitSha":"643bf806190b6fecbb424caf7eed8aa337bfeb25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7717","title":"Update deps","createdAt":"2023-08-23T04:26:04Z"}
{"state":"Merged","mergedAt":"2023-08-23T19:40:51Z","number":7718,"mergeCommitSha":"edf9a14f7a41a46d200801047a22137cefc9f45f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7718","title":"Add StackOverflowTeamsQuestionsApi","createdAt":"2023-08-23T04:40:53Z"}
{"state":"Merged","mergedAt":"2023-08-23T22:32:34Z","number":7719,"mergeCommitSha":"b8c8456db487c8662c8d2361352ef3cc10cf03ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7719","title":"Remove no longer needed Confluence flag","createdAt":"2023-08-23T04:57:13Z"}
{"state":"Merged","mergedAt":"2022-04-04T21:13:38Z","number":772,"mergeCommitSha":"eb54b4fc2a304f292185955d5925ec11863bd178","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/772","title":"Deploy asset service","createdAt":"2022-04-04T20:50:04Z"}
{"state":"Merged","mergedAt":"2023-08-23T15:37:39Z","number":7720,"mergeCommitSha":"c731e468667f77cf05e3873910c6282b46204372","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7720","title":"Fix slack channel ingestion","createdAt":"2023-08-23T07:53:40Z"}
{"state":"Merged","mergedAt":"2023-08-23T15:01:38Z","number":7721,"body":"I have fixed the publish job in Documentation repo. Assets are now being uploaded to S3 bucket. \r\n\r\nThis would enable the following:\r\n\r\n- https://dev.getunblocked.com/docs\r\n- https://getunblocked.com/docs","mergeCommitSha":"ef5dce9915449085aa78d0d5fa06a73a4508c721","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7721","title":"Enable docs site on cf","createdAt":"2023-08-23T09:54:49Z"}
{"state":"Merged","mergedAt":"2023-08-23T17:10:53Z","number":7722,"mergeCommitSha":"f70007d54a68177907dba4e01ad64b03d63f351e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7722","title":"Change slack channel","createdAt":"2023-08-23T17:08:11Z"}
{"state":"Merged","mergedAt":"2023-08-23T19:18:17Z","number":7723,"body":"index.ts's are my nemesis, this happened again.  I added an eslint rules that prevents using base index imports for FA.\r\n\r\nReduces bundle by about 20%","mergeCommitSha":"d952523be75905eff54ec21bc39f4e4b4332bfae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7723","title":"Don't include all FA icons in web bundle","createdAt":"2023-08-23T17:28:41Z"}
{"state":"Merged","mergedAt":"2023-08-23T21:03:05Z","number":7724,"body":"<img width=\"1115\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/996c177b-fe4f-4ef2-86fa-ac56a7f44cf8\">\r\n\r\n* Add reusable components for settings headers/footers\r\n* Slack setting buttons are in the footer section instead of the content\r\n* Will refactor further with the new configuration container for the other integration UIs","mergeCommitSha":"5f69f225cea074e995051e9208e07fb413fb956c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7724","title":"Refactor settings header and footer","createdAt":"2023-08-23T18:10:36Z"}
{"state":"Merged","mergedAt":"2023-08-23T19:05:53Z","number":7725,"body":"- Add slack user engagement\r\n- New enum\r\n","mergeCommitSha":"45452c433a06ce7a379e0cadf63db8d7d443ae7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7725","title":"slackUserEngagment","createdAt":"2023-08-23T18:21:15Z"}
{"state":"Merged","mergedAt":"2023-08-23T19:29:05Z","number":7726,"mergeCommitSha":"f9de1405096e69ba85d0a2adc0a9880209c68d5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7726","title":"Update openai client","createdAt":"2023-08-23T19:04:02Z"}
{"state":"Merged","mergedAt":"2023-08-23T20:10:36Z","number":7729,"mergeCommitSha":"e15984a5d572cd72c67670c835b6a3f0d33627c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7729","title":"Delete dead code","createdAt":"2023-08-23T19:51:39Z"}
{"state":"Merged","mergedAt":"2022-04-04T22:29:36Z","number":773,"body":"<img width=\"272\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/161636294-40cdbb28-7590-493b-9b04-a276137fe25e.png\">\r\n\r\nfor reference: https://code.visualstudio.com/api/advanced-topics/using-proposed-api#sharing-extensions-using-the-proposed-api for packaging this extension with this proposed api\r\n\r\nNOTES:\r\n- Will need to use an [Insiders](https://code.visualstudio.com/insiders/) build of vscode to be able to see the badges\r\n- there is a [bug](https://github.com/microsoft/vscode/issues/146330) where badge doesn't show up if the user hasn't clicked the Unblocked icon to initialize the extension (and create the webview)","mergeCommitSha":"fbb0e5e3e58a2041f3ebb23d2840f6887159ecd0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/773","title":"Implement badges ","createdAt":"2022-04-04T21:40:57Z"}
{"state":"Merged","mergedAt":"2023-08-23T23:20:07Z","number":7730,"mergeCommitSha":"d36339aee3706b663e38f3ea0b9c92873f4293c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7730","title":"Add Provider.StackOverflowTeams","createdAt":"2023-08-23T19:56:04Z"}
{"state":"Merged","mergedAt":"2023-08-23T21:43:08Z","number":7731,"body":"Add loading and empty state to Slack settings\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/c48395d4-325a-489b-9eea-d000d25b4b2e\r\n\r\n","mergeCommitSha":"a33dacc17526b1fb2f65ba226db139334e3d076b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7731","title":"Add Slack Loading and Empty State","createdAt":"2023-08-23T19:56:05Z"}
{"state":"Merged","mergedAt":"2023-08-23T21:03:17Z","number":7735,"body":"Milvus is a horrible embedding service.\r\n","mergeCommitSha":"0f45b97d73fd728e20dd74bb48f2fac3fd5badd8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7735","title":"Remove milvus","createdAt":"2023-08-23T20:26:05Z"}
{"state":"Merged","mergedAt":"2023-08-23T20:58:19Z","number":7736,"body":"Export into a separate `dashboard.css` file that lives alongside `dashboard.js`.\r\n\r\nThis had a much bigger impact on the bundle size than I was expecting:\r\n* JS bundle shrinks from 5.5MB -> 1.8MB\r\n* Added CSS file of 340kb\r\n\r\nI'm going to guess that the zipped bundle size won't differ as much percentage-wise, as the inline CSS should have zipped pretty efficiently, but this is going to help a bit regardless.\r\n\r\nLesson learned: `style-loader` inline CSS is not very efficient.","mergeCommitSha":"1b25c4f9e6007c15cf7bd70a0356c995d0b52830","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7736","title":"Export dashboard styles into separate CSS file","createdAt":"2023-08-23T20:32:43Z"}
{"state":"Merged","mergedAt":"2023-08-23T21:31:14Z","number":7737,"body":"- Delete cdk assets\r\n- Remove data pipeline code\r\n- More deletions\r\n","mergeCommitSha":"f7fffc102603ced5657cc4c2bf24d50e7d9a0a27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7737","title":"RemoveConfluenceDataPipeline","createdAt":"2023-08-23T21:10:18Z"}
{"state":"Merged","mergedAt":"2023-08-23T23:38:43Z","number":7739,"mergeCommitSha":"a119094157fc9894d051fdb97996a1045baf71fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7739","title":"Fix TopicStore.upsertTopic","createdAt":"2023-08-23T21:13:39Z"}
{"state":"Merged","mergedAt":"2022-04-04T21:51:26Z","number":774,"mergeCommitSha":"8a3a4a7ac570e06cdb7946b36e085647cc01b3df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/774","title":"Oops","createdAt":"2022-04-04T21:51:01Z"}
{"state":"Merged","mergedAt":"2023-08-24T16:34:52Z","number":7741,"body":"New toasts:\r\n\r\nTopics:\r\n* create topic \r\n* edit topic\r\n* delete topic\r\n\r\nThreads:\r\n* delete thread\r\n* restore thread","mergeCommitSha":"c4a2e2b84e0956accdf0754883fc1e9b169e322a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7741","title":"Add toasts to thread/topic CRUD operations","createdAt":"2023-08-23T21:47:59Z"}
{"state":"Merged","mergedAt":"2023-08-23T22:07:19Z","number":7742,"mergeCommitSha":"e098539cca2db27f969b6873409a48d963a5ea10","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7742","title":"Fix error message","createdAt":"2023-08-23T22:01:37Z"}
{"state":"Merged","mergedAt":"2023-08-23T22:10:35Z","number":7743,"mergeCommitSha":"a29d00f62faed7417ef85dfacc197638cbab661d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7743","title":"Fix source code","createdAt":"2023-08-23T22:10:29Z"}
{"state":"Merged","mergedAt":"2023-08-23T22:18:45Z","number":7744,"mergeCommitSha":"00f5ab9f03982fd018b3c562058df4127c20e1d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7744","title":"Fix build","createdAt":"2023-08-23T22:18:40Z"}
{"state":"Merged","mergedAt":"2023-08-29T17:37:51Z","number":7745,"mergeCommitSha":"6826622b256f0bf641c7204082b1cd08fe74769f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7745","title":"Webpack bundle analyzer shows gzipped size","createdAt":"2023-08-23T22:37:34Z"}
{"state":"Merged","mergedAt":"2023-08-23T22:56:34Z","number":7746,"mergeCommitSha":"7c6c3eee7805923db9f9e9d989ce173a34f0356c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7746","title":"Fix entrypoint path for source code processor","createdAt":"2023-08-23T22:50:59Z"}
{"state":"Merged","mergedAt":"2023-08-24T20:48:47Z","number":7748,"body":"special cases pdfs as a \"supported binary file\" which means it is an file that we can create some textual representation of -- like a pdf or an image or video, etc. -- and then embedded during source code ingestion.\r\n\r\nthis should be enough to test and then i'll change file_traversal.py to support binary files in a more general way.","mergeCommitSha":"529fb50140873e399c1d8edbe68588083105a7cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7748","title":"add min. support for pdfs","createdAt":"2023-08-23T23:15:06Z"}
{"state":"Closed","mergedAt":null,"number":7749,"mergeCommitSha":"32c6f6d934110638a4c8056a060e329aa82b6be9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7749","title":"Convert embeddings stuff to pylib","createdAt":"2023-08-24T05:04:16Z"}
{"state":"Merged","mergedAt":"2022-04-05T16:54:09Z","number":775,"body":"Setting up code highlighting for web extension\r\n\r\n<img width=\"643\" alt=\"CleanShot 2022-04-04 at 14 50 00@2x\" src=\"https://user-images.githubusercontent.com/1553313/161638264-b01c20da-36bf-4c48-8ccc-d9cde2300360.png\">\r\n\r\n","mergeCommitSha":"0aff3bf9a09ef2ae1087d15fcf818247a0674eb3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/775","title":"Web extension code highlighting","createdAt":"2022-04-04T21:52:06Z"}
{"state":"Merged","mergedAt":"2023-08-24T08:59:25Z","number":7750,"body":"- Added a new resource file for docs cloudfront deployment\r\n- Added new certs for new CloudFront distro\r\n- Modified config file to accept configuration for new distro\r\n\r\nI had to deploy all of these changes from my local machine to speed things up. All changes have been deployed.\r\n\r\nNext step is to remove the docs path from our main CloudFront distro. \r\n\r\nI will be force merging this change to avoid accidental reverts. ","mergeCommitSha":"688d25a1489d825a6275be40aa11c5ad24e2fbf3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7750","title":"Adding CloudFront distro dedicated to Docs site","createdAt":"2023-08-24T08:59:16Z"}
{"state":"Merged","mergedAt":"2023-08-25T19:16:01Z","number":7751,"mergeCommitSha":"80c99ebe07efe35627fc7dc9060e4b10348b0c18","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7751","title":"Create StackOverflowForTeams api operations","createdAt":"2023-08-24T16:03:41Z"}
{"state":"Merged","mergedAt":"2023-08-24T17:20:21Z","number":7752,"mergeCommitSha":"5c496aeda00910f298fb35a921f3e4deb4045672","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7752","title":"Add logging","createdAt":"2023-08-24T17:20:16Z"}
{"state":"Merged","mergedAt":"2023-08-24T18:13:53Z","number":7753,"mergeCommitSha":"66e1c2d40a687f4a55bc8b418f06b5a2df2195a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7753","title":"Fix admin console page typos","createdAt":"2023-08-24T18:04:24Z"}
{"state":"Merged","mergedAt":"2023-08-24T19:26:26Z","number":7754,"body":"For support channels we should not prevent a QA from being posted if the boolean is false.","mergeCommitSha":"fc6bba8a9f0b03f00ec17706b80a240ccfe3f8ed","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7754","title":"Add boolean to control slack bot filtering for shadow validation","createdAt":"2023-08-24T18:09:51Z"}
{"state":"Merged","mergedAt":"2023-08-24T19:53:41Z","number":7755,"mergeCommitSha":"21f7f3038d9b1d24b765620fc648a17675e0f626","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7755","title":"Use dotproduct pinecone index in dev","createdAt":"2023-08-24T18:30:10Z"}
{"state":"Merged","mergedAt":"2023-08-24T19:23:49Z","number":7756,"mergeCommitSha":"03b71cec0811b08e5ecfa079da0b586d3d6091bc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7756","title":"Create StackOverflowTeamsIngestionJob","createdAt":"2023-08-24T18:57:27Z"}
{"state":"Merged","mergedAt":"2023-08-24T20:54:23Z","number":7757,"mergeCommitSha":"8d72fbdbc4fc672d750781dcb37181db1cfaf26b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7757","title":"Create StackOverflowTeamsApiProvider and StackOverflowTeamsLockProvider","createdAt":"2023-08-24T19:42:58Z"}
{"state":"Merged","mergedAt":"2023-08-24T21:28:36Z","number":7758,"body":"This should work around the ad blocker issues in the dashboard.","mergeCommitSha":"9dab592fe657e5f1f313aa69307984144d8361fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7758","title":"Use local fonts","createdAt":"2023-08-24T20:44:28Z"}
{"state":"Merged","mergedAt":"2023-08-24T21:57:41Z","number":7759,"mergeCommitSha":"b936cd0843ed237e76a69e23b95f03f59ae4dae4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7759","title":"Add StackOverflowTeamsEmbeddingService","createdAt":"2023-08-24T21:20:17Z"}
{"state":"Merged","mergedAt":"2022-04-05T22:14:21Z","number":776,"mergeCommitSha":"1d0bbc72517ba75156609b78cbaf5356d0140749","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/776","title":"Ability to archive and delete threads","createdAt":"2022-04-04T23:04:14Z"}
{"state":"Merged","mergedAt":"2023-08-24T22:29:48Z","number":7760,"body":"The timeout here is just too aggressive","mergeCommitSha":"efac32b83b1e2e46476abe43aa530dee81052cc6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7760","title":"Quick fix for IJ timeout exception on startup","createdAt":"2023-08-24T21:45:17Z"}
{"state":"Merged","mergedAt":"2023-08-24T22:53:01Z","number":7761,"body":"Don't use `dashboard.css` for both the dashboard and landing page css, use the default webpack naming.  This results in `dashboard.css` and `landing.css`.","mergeCommitSha":"bcd7d8aa398c5cb9a1f124c31d02193aaf2b17ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7761","title":"Use better css file names","createdAt":"2023-08-24T22:42:19Z"}
{"state":"Merged","mergedAt":"2023-08-25T20:55:22Z","number":7762,"body":"<img width=\"1496\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/54ca6998-6ece-4a57-b74e-9d627e7a72f5\">\r\n<img width=\"1498\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/a26267f4-3b75-4323-9ca2-94dc6bc54adc\">\r\n\r\nThis change only leverages the existing APIs.\r\n\r\nTo get to full parity with Slack, we would need API changes for the following:\r\n- [ ] New search endpoints for jira projects and confluence spaces\r\n- [ ]  A rev on the JiraConfig/ConfluenceConfig models:\r\n    *  selected `project`s/`space`s are full models instead of ids\r\n    * Add `pattern` field for pattern matching\r\n- [ ]  An `integrationUrl` or `siteUrl` field on the JiraSite and ConfluenceSite models\r\n","mergeCommitSha":"c2ae2a248eb710f978985f4619a76141d4959e56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7762","title":"Refactor settings UIs","createdAt":"2023-08-24T22:46:42Z"}
{"state":"Merged","mergedAt":"2023-08-25T00:04:52Z","number":7763,"mergeCommitSha":"2e1044824de92a5c2a8eb4843319a8431193878b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7763","title":"Migrate instructor image to build with poetry and slim-bullseye","createdAt":"2023-08-24T22:54:51Z"}
{"state":"Merged","mergedAt":"2023-08-25T00:55:40Z","number":7764,"mergeCommitSha":"aff075d3d9d4ef7e2f165b2f461aaef2cc2fc9d6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7764","title":"Delete stale images","createdAt":"2023-08-25T00:55:30Z"}
{"state":"Merged","mergedAt":"2023-08-25T02:36:35Z","number":7765,"body":"- Add auto scaler\r\n- Update\r\n","mergeCommitSha":"b560da5fbf81b7f6d5ee6a1f4fe389e9e1dbdc4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7765","title":"UpdateHFAutoScaling","createdAt":"2023-08-25T02:36:04Z"}
{"state":"Merged","mergedAt":"2023-08-25T05:01:09Z","number":7766,"body":"Added compatibility for a v2 request. Shame that we aren't using some kind of API generator but meh. New request looks like this:\r\n\r\n**Request**\r\n```\r\n{\"v2\": {\"inputs\": [str]}}\r\n```\r\n\r\n**Response**\r\n```\r\n{\"dense_vectors\":[float], \"sparse_vectors\":[{\"indices\":[int], \"values\":[float]}]}\r\n```","mergeCommitSha":"2d8d41f7e8d5e7094b8de26672c2108d5da31598","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7766","title":"Add instructor v2 request","createdAt":"2023-08-25T04:31:18Z"}
{"state":"Merged","mergedAt":"2023-08-25T06:05:18Z","number":7767,"mergeCommitSha":"7fded0ff44293ce651f996ecfd5de482d0a23468","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7767","title":"comment out unused behaviors","createdAt":"2023-08-25T06:04:35Z"}
{"state":"Merged","mergedAt":"2023-08-25T18:20:44Z","number":7768,"mergeCommitSha":"8f4bb46acf98f9d696eef33595e30c729a9dc189","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7768","title":"Add sparse vectors to kotlin objects","createdAt":"2023-08-25T07:15:33Z"}
{"state":"Merged","mergedAt":"2023-08-25T07:35:21Z","number":7769,"body":"Reverts NextChapterSoftware/unblocked#7766","mergeCommitSha":"d4c350012eff9762f1f017f01d12f32e180401e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7769","title":"Revert \"Add instructor v2 request\"","createdAt":"2023-08-25T07:35:11Z"}
{"state":"Merged","mergedAt":"2022-04-05T23:22:08Z","number":777,"body":"## Summary\r\n\r\nImplements Agora API calls for channel and recording management","mergeCommitSha":"9d672afac8615a879c0f930a0367981057ee7b14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/777","title":"Agora Client","createdAt":"2022-04-04T23:33:32Z"}
{"state":"Merged","mergedAt":"2023-08-25T07:48:09Z","number":7770,"body":"Reverts NextChapterSoftware/unblocked#7767","mergeCommitSha":"90be7a987c98379132ccf5e649d0d64a5201bc5d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7770","title":"Revert \"comment out unused behaviors\"","createdAt":"2023-08-25T07:39:02Z"}
{"state":"Merged","mergedAt":"2023-08-25T08:12:03Z","number":7771,"body":"Reverts NextChapterSoftware/unblocked#7769","mergeCommitSha":"32c71b324fcaee9e41589f95401ad525212200f4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7771","title":"Revert \"Revert \"Add instructor v2 request\"\"","createdAt":"2023-08-25T08:03:27Z"}
{"state":"Merged","mergedAt":"2023-08-27T03:49:01Z","number":7772,"mergeCommitSha":"16c0a61c69109fb17c90925457f199aa7a7448ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7772","title":"Update StackOverflowTeamsQuestionsApi to support paging","createdAt":"2023-08-25T16:23:23Z"}
{"state":"Merged","mergedAt":"2023-08-25T18:47:10Z","number":7773,"body":"I'm sure we'll find more as we encounter them...","mergeCommitSha":"8e43baf5a8d1d410a0738b72dcdcfaf6832b6090","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7773","title":"Pass at removing offset hacks for Sofia Pro","createdAt":"2023-08-25T18:14:30Z"}
{"state":"Merged","mergedAt":"2023-08-25T20:16:51Z","number":7774,"mergeCommitSha":"ef3096c429edf35212cb9c8c225ef4beb3b4ada8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7774","title":"Add sparse vector weight to template","createdAt":"2023-08-25T18:42:05Z"}
{"state":"Merged","mergedAt":"2023-08-25T19:49:46Z","number":7775,"mergeCommitSha":"d0a426567a073fa016f9c3fb371b00da322bc10b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7775","title":"Fix me NOW","createdAt":"2023-08-25T19:47:02Z"}
{"state":"Merged","mergedAt":"2023-08-25T22:11:31Z","number":7776,"body":"There are a few levers that need to be pulled to \"activate\" hybrid search:\r\n1. Team setting for sparse vectors needs to be turned on\r\n2. Source code must be re-ingested with sparse vectors team flag on to generate the sparse vectors\r\n3. ML template should have the weights set appropriately","mergeCommitSha":"a748e2b8c53b59a96e856e51ff0323ef942f6cbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7776","title":"Integrate sparse vectors into semantic search","createdAt":"2023-08-25T19:57:04Z"}
{"state":"Merged","mergedAt":"2023-08-25T22:17:04Z","number":7777,"mergeCommitSha":"9243a0cfea9c4ffe87236bbb87b20ab771daa169","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7777","title":"Implement uninstallStackOverflowTeam","createdAt":"2023-08-25T20:06:16Z"}
{"state":"Merged","mergedAt":"2023-08-28T17:23:02Z","number":7778,"mergeCommitSha":"383d783b00813d54cc7157c9a075502e7bb49b99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7778","title":"Add llama2 support for pr summaries","createdAt":"2023-08-25T20:32:50Z"}
{"state":"Merged","mergedAt":"2023-08-25T22:18:22Z","number":7779,"body":"…meout","mergeCommitSha":"92093359514468709d6d091a2927dd2c545edaf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7779","title":"Fixes: older pdfs failing the binary check + larger pdfs exceeding timeout","createdAt":"2023-08-25T20:53:06Z"}
{"state":"Merged","mergedAt":"2022-04-05T00:24:05Z","number":778,"mergeCommitSha":"65fdd501f7a41eb249a3ee3fae41fd891132a39c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/778","title":"Fix up readmes for secrets deployment","createdAt":"2022-04-04T23:58:53Z"}
{"state":"Merged","mergedAt":"2023-08-25T21:55:31Z","number":7780,"mergeCommitSha":"77d9914aa99d0dbca6ecc1bdf5b55ee82ed476b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7780","title":"Validate Stack Overflow for Teams hostURL and PAT","createdAt":"2023-08-25T20:59:53Z"}
{"state":"Merged","mergedAt":"2023-08-25T21:12:50Z","number":7781,"mergeCommitSha":"464cd5656edbaed307c0a3c8ba672055357c4ed2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7781","title":"minor typo that has been annoying me","createdAt":"2023-08-25T21:01:07Z"}
{"state":"Merged","mergedAt":"2023-08-25T23:20:09Z","number":7782,"mergeCommitSha":"0c2b327c3727342aba4e13cd900ad78389ff0576","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7782","title":"[BREAKS API ON MAIN] Remove operation thats not needed","createdAt":"2023-08-25T22:42:51Z"}
{"state":"Merged","mergedAt":"2023-08-29T00:15:54Z","number":7783,"mergeCommitSha":"daaec5335c0707a825482b19df1eeeee27b351d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7783","title":"Inactive person campaign","createdAt":"2023-08-25T23:19:02Z"}
{"state":"Merged","mergedAt":"2023-08-27T10:28:46Z","number":7784,"mergeCommitSha":"4811730d170d4b72eeca69872d4f124963a9cf0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7784","title":"Mosaic test","createdAt":"2023-08-27T10:28:07Z"}
{"state":"Merged","mergedAt":"2023-08-28T04:32:32Z","number":7785,"mergeCommitSha":"eb2cd08524f872b3eef3fa2c855aef565430b65e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7785","title":"Emit embedding event for stack overflow questions","createdAt":"2023-08-27T21:14:50Z"}
{"state":"Merged","mergedAt":"2023-08-28T17:57:12Z","number":7786,"mergeCommitSha":"6915bb23b649b48fa605cfed01a72905948b75e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7786","title":"Remove Stack Overflow Teams from team members page","createdAt":"2023-08-28T04:23:12Z"}
{"state":"Merged","mergedAt":"2023-08-28T18:05:04Z","number":7787,"body":"Working on alternatives to unsafe-inline but adding this to unblock develoment / release of docs.","mergeCommitSha":"90a02c243d3f2cebe44565cf764e57d7211d698f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7787","title":"Use unsafe inline temporarily for docs","createdAt":"2023-08-28T15:31:54Z"}
{"state":"Merged","mergedAt":"2023-08-28T17:55:03Z","number":7788,"mergeCommitSha":"f3a9d7b36e1fee5020971a9b4b9fcb08885d2650","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7788","title":"Fix patchStackOverflowTeam so that 400s return a body with an error message","createdAt":"2023-08-28T17:16:32Z"}
{"state":"Merged","mergedAt":"2023-08-28T17:39:28Z","number":7789,"body":"This pr is to remove the optionality of the set of columsn to update during an upsert.\r\n","mergeCommitSha":"0e7fe99e75708d2afee7cbc7d19257528a2fa894","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7789","title":"This pr was inspired by David Lam","createdAt":"2023-08-28T17:17:07Z"}
{"state":"Merged","mergedAt":"2022-04-05T05:01:41Z","number":779,"body":"Standardize the java set of arguments used to run our services.\r\nFuture plan is to be able to configure memory etc. per service via arguments if we have to, but I want to control stuff like gc etc. via a global jar runner.\r\n\r\nFor example, I\"m planning on moving us to ZGC.\r\nhttps://openjdk.java.net/projects/zgc/\r\n\r\nTesting:\r\nConfirmed successful deployment:\r\nhttps://github.com/NextChapterSoftware/unblocked/runs/**********?check_suite_focus=true","mergeCommitSha":"18ca8d21a9eca48693fd21fa9333060d2173000d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/779","title":"Add java runner","createdAt":"2022-04-05T01:32:43Z"}
{"state":"Merged","mergedAt":"2023-08-28T18:37:54Z","number":7790,"body":"Fixes https://app.logz.io/#/goto/2d20267f1d5a99276551e87914141dfb?switchToAccountId=411850","mergeCommitSha":"22ecbd4ad30661227261b73fc86cc14700c7cc56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7790","title":"Confluence pages could have a null createdAt. Not sure why but that's Atlassian for ya.","createdAt":"2023-08-28T17:45:45Z"}
{"state":"Merged","mergedAt":"2023-08-28T18:23:36Z","number":7791,"mergeCommitSha":"1979959a7d6d72d2792e247d5ea39304b4d51e7b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7791","title":"Fix display name for Stack Overflow teams installation","createdAt":"2023-08-28T18:12:47Z"}
{"state":"Merged","mergedAt":"2023-08-28T18:57:38Z","number":7792,"mergeCommitSha":"8c8907c09c7909f82a6afd8546d9b1f27e4c5a68","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7792","title":"Throw UserVisibleException instead of BadRequest to surface error in UI","createdAt":"2023-08-28T18:44:57Z"}
{"state":"Merged","mergedAt":"2023-08-28T22:49:48Z","number":7793,"body":"onboarding:\r\n<img width=\"1153\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/a2fbf3c7-017b-44a3-9205-20bd0ae2ad98\">\r\n<img width=\"885\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8598bbd8-9b5b-448b-8ff9-886e01d213e5\">\r\n\r\n\r\nsettings:\r\n<img width=\"1144\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/51369129-c720-44e9-8cbe-2b7ab736ca67\">\r\n<img width=\"1142\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/5d66dc76-9d1b-42b9-8ccb-f86950a70273\">\r\n","mergeCommitSha":"6751e706356855e42d72d798e53934da0f6d4d60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/7793","title":"Stack overflow settings","createdAt":"2023-08-28T19:58:22Z"}