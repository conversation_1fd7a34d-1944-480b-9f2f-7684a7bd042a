commit 3b0450a6f4c0cebe25b357e61eebf684feacc13b
Author: <PERSON> <<EMAIL>>
Date:   Mon May 16 13:59:14 2022 -0700

    Delay PR ingestion until rate limit resets (#1306)

    * Delay pr ingestion until rate limit resets

    * Add PullRequestIngestionJobTest

diff --git a/projects/clients/client-scm/src/main/kotlin/com/nextchaptersoftware/scm/github/Models.kt b/projects/clients/client-scm/src/main/kotlin/com/nextchaptersoftware/scm/github/Models.kt
index 823c7985c..722f6c5f3 100644
--- a/projects/clients/client-scm/src/main/kotlin/com/nextchaptersoftware/scm/github/Models.kt
+++ b/projects/clients/client-scm/src/main/kotlin/com/nextchaptersoftware/scm/github/Models.kt
@@ -143,7 +143,9 @@ data class RateLimit(
         @SerialName("used") val used: Int,
         @SerialName("remaining") val remaining: Int,
         @SerialName("reset") val reset: Long,
-    )
+    ) {
+        val resetInstant = Instant.fromEpochSeconds(reset)
+    }
 }

 /**
diff --git a/projects/libs/lib-aws/src/main/kotlin/com/nextchaptersoftware/aws/sqs/SqsProvider.kt b/projects/libs/lib-aws/src/main/kotlin/com/nextchaptersoftware/aws/sqs/SqsProvider.kt
index 53e794890..6646ed9d9 100644
--- a/projects/libs/lib-aws/src/main/kotlin/com/nextchaptersoftware/aws/sqs/SqsProvider.kt
+++ b/projects/libs/lib-aws/src/main/kotlin/com/nextchaptersoftware/aws/sqs/SqsProvider.kt
@@ -1,6 +1,8 @@
 package com.nextchaptersoftware.aws.sqs

 import com.nextchaptersoftware.aws.client.AWSClientProvider
+import kotlin.time.Duration
+import software.amazon.awssdk.services.sqs.model.ChangeMessageVisibilityRequest
 import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
 import software.amazon.awssdk.services.sqs.model.DeleteMessageResponse
 import software.amazon.awssdk.services.sqs.model.GetQueueAttributesRequest
@@ -16,6 +18,7 @@ interface SqsProvider {
     fun receiveMessages(maxNumberOfMessages: Int = 1, waitTimeSeconds: Int? = null): ReceiveMessageResponse
     fun sendMessage(message: String): SendMessageResponse
     fun deleteMessage(message: Message): DeleteMessageResponse
+    fun setVisibilityTimeout(message: Message, timeout: Duration)
     fun getApproximateNumberOfMessages(): Int
 }

@@ -60,6 +63,16 @@ class StandardSqsProvider(private val awsClientProvider: AWSClientProvider, queu
         return awsClientProvider.sqsClient.deleteMessage(deleteMessageRequest)
     }

+    override fun setVisibilityTimeout(message: Message, timeout: Duration) {
+        val changeMessageVisibilityRequest = ChangeMessageVisibilityRequest.builder()
+            .queueUrl(queueUrl)
+            .receiptHandle(message.receiptHandle())
+            .visibilityTimeout(timeout.inWholeSeconds.toInt())
+            .build()
+
+        awsClientProvider.sqsClient.changeMessageVisibility(changeMessageVisibilityRequest)
+    }
+
     override fun getApproximateNumberOfMessages(): Int {
         val getQueueAttributesRequest = GetQueueAttributesRequest.builder()
             .queueUrl(queueUrl)
diff --git a/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/Exceptions.kt b/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/Exceptions.kt
index 07c94bc95..9c5c28587 100644
--- a/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/Exceptions.kt
+++ b/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/Exceptions.kt
@@ -1,4 +1,7 @@
 package com.nextchaptersoftware.pringestion

+import kotlin.time.Duration
+
 class MissingCommentAuthorException : Exception()
 class MissingCommitIdException : Exception()
+class DelayDueToRateLimitingException(val delay: Duration) : Exception()
diff --git a/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProvider.kt b/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProvider.kt
index f5e29e451..fe653f615 100644
--- a/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProvider.kt
+++ b/projects/libs/lib-pringestion/src/main/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProvider.kt
@@ -1,23 +1,25 @@
 package com.nextchaptersoftware.pringestion

 /* ktlint-disable nextchaptersoftware:restrict-database-model-import */
-import com.nextchaptersoftware.config.GitHubConfig
 import com.nextchaptersoftware.db.common.Database.suspendedTransaction
 import com.nextchaptersoftware.db.models.RepoDAO
 import com.nextchaptersoftware.db.models.TeamDAO
 import com.nextchaptersoftware.scm.github.GitHubAppClient
 import com.nextchaptersoftware.scm.github.GitHubPullRequestService
 import com.nextchaptersoftware.scm.github.PullRequestReviewThread
-import com.nextchaptersoftware.security.Jwt
 import java.util.UUID
+import kotlin.time.Duration.Companion.seconds
 import mu.KotlinLogging

 private val logger = KotlinLogging.logger {}

 class PullRequestReviewThreadProvider(
-    private val gitHubConfig: GitHubConfig,
-    private val jwt: Jwt,
+    private val gitHubAppClient: GitHubAppClient,
 ) {
+    companion object {
+        const val rateLimitThreshold = 1000
+    }
+
     suspend fun get(
         repoId: UUID,
         prNumber: Int,
@@ -30,7 +32,14 @@ class PullRequestReviewThreadProvider(
             return null
         }

-        val threads = GitHubAppClient(config = gitHubConfig, jwt = jwt).v3Org(installationId = installationId).use {
+        val threads = gitHubAppClient.v3Org(installationId = installationId).use {
+            // Throw an exception if we're close to the limit to make the ingestion job backoff
+            val rateLimitInfo = it.rateLimit().rate
+            if (rateLimitInfo.remaining <= rateLimitThreshold) {
+                val delay = it.rateLimit().rate.reset - kotlinx.datetime.Clock.System.now().epochSeconds
+                throw DelayDueToRateLimitingException(delay = delay.seconds)
+            }
+
             GitHubPullRequestService(
                 gitHubClient = it,
                 owner = repo.externalOwner,
diff --git a/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/MockObjects.kt b/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/MockObjects.kt
index facfb2525..82f90eff8 100644
--- a/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/MockObjects.kt
+++ b/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/MockObjects.kt
@@ -2,10 +2,12 @@ package com.nextchaptersoftware.pringestion

 import com.nextchaptersoftware.db.models.Provider
 import com.nextchaptersoftware.scm.github.Author
+import com.nextchaptersoftware.scm.github.GitHubPullRequest
 import com.nextchaptersoftware.scm.github.GitHubPullRequestReviewComment
 import com.nextchaptersoftware.scm.github.GitHubPullRequestReviewFile
 import com.nextchaptersoftware.scm.github.GitHubUser
 import com.nextchaptersoftware.scm.github.PullRequestReviewThread
+import com.nextchaptersoftware.utils.UrlExtensions.asUrl
 import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
 import io.ktor.http.Url
 import java.util.UUID
@@ -84,7 +86,7 @@ object MockObjects {

     fun prFile(
         sha: String = "2f9b516f92f791cdce89d55d5fc3b6d2c0bc2298",
-        filename: String = "path/to/file",
+        filename: String = "path/to/my/file",
         patch: String = "@@ -1,5 +1,5 @@\n import '",
     ) = GitHubPullRequestReviewFile(
         sha = sha,
@@ -107,4 +109,28 @@ object MockObjects {
         provider = provider,
         displayName = displayName,
     )
+
+    fun gitHubPullRequest(
+        id: Int = 1,
+        number: Int = 99,
+        title: String = "This is my pull request",
+        mergeCommitSha: String? = "9e632d4dfd795a9c2e5b5d1cbd4fb20d9c698581",
+        user: GitHubUser? = prCommentAuthor(),
+        closedAt: Instant? = Instant.nowWithMicrosecondPrecision(),
+        mergedAt: Instant? = closedAt,
+        updatedAt: Instant = Instant.nowWithMicrosecondPrecision(),
+        reviewComments: Int? = 10,
+        htmlUrl: String = "https://github.com/owner/repo/pull/$number",
+    ) = GitHubPullRequest(
+        id = id,
+        number = number,
+        title = title,
+        mergeCommitSha = mergeCommitSha,
+        user = user,
+        closedAt = closedAt,
+        mergedAt = mergedAt,
+        updatedAt = updatedAt,
+        reviewComments = reviewComments,
+        htmlUrl = htmlUrl.asUrl(),
+    )
 }
diff --git a/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestIngestionServiceV2Test.kt b/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestIngestionServiceV2Test.kt
index 5bb97d08a..81e4ab51e 100644
--- a/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestIngestionServiceV2Test.kt
+++ b/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestIngestionServiceV2Test.kt
@@ -3,6 +3,7 @@ package com.nextchaptersoftware.pringestion
 import com.nextchaptersoftware.db.ModelBuilders.makeRepo
 import com.nextchaptersoftware.db.ModelBuilders.makeTeam
 import com.nextchaptersoftware.db.common.Database.suspendedTransaction
+import com.nextchaptersoftware.db.models.IdentityDAO
 import com.nextchaptersoftware.db.models.MessageDAO
 import com.nextchaptersoftware.db.models.RepoDAO
 import com.nextchaptersoftware.db.models.TeamDAO
@@ -24,8 +25,10 @@ class PullRequestIngestionServiceV2Test : DatabaseTestsBase() {
     private lateinit var team: TeamDAO
     private lateinit var repo: RepoDAO

-    private val firstComment = MockObjects.prReviewThreadComment(id = 1)
-    private val replyComment = MockObjects.prReviewThreadComment(id = 2, inReplyToId = firstComment.id)
+    private val firstCommentAuthor = MockObjects.prCommentAuthor(id = 99)
+    private val firstComment = MockObjects.prReviewThreadComment(id = 1, user = firstCommentAuthor)
+    private val replyCommentAuthor = MockObjects.prCommentAuthor(id = 100)
+    private val replyComment = MockObjects.prReviewThreadComment(id = 2, user = replyCommentAuthor, inReplyToId = firstComment.id)
     private val prThread = MockObjects.pullRequestReviewThread(comments = listOf(firstComment, replyComment))

     fun setUp() = suspendingTest {
@@ -43,6 +46,9 @@ class PullRequestIngestionServiceV2Test : DatabaseTestsBase() {
         service.ingest(repo.id.value, prThread.pullRequestNumber)

         suspendedTransaction {
+            val identities = IdentityDAO.all().map { it.asDataModel() }
+            assertThat(identities.map { it.externalId }).containsExactlyInAnyOrder("99", "100")
+
             val messages = MessageDAO.all().toList()
             assertThat(messages.map { it.prCommentId }).containsExactlyInAnyOrderElementsOf(
                 prThread.comments.map { it.id.toString() },
@@ -67,7 +73,8 @@ class PullRequestIngestionServiceV2Test : DatabaseTestsBase() {
             messages.first().thread.asDataModel()
         }

-        val secondReply = MockObjects.prReviewThreadComment(id = 3, inReplyToId = firstComment.id)
+        val secondReplyAuthor = MockObjects.prCommentAuthor(id = 101)
+        val secondReply = MockObjects.prReviewThreadComment(id = 3, user = secondReplyAuthor, inReplyToId = firstComment.id)
         val prThreadWithAdditionComment = prThread.copy(comments = prThread.comments + listOf(secondReply))

         `when`(threadProvider.get(repo.id.value, prThread.pullRequestNumber)).thenReturn(
@@ -77,6 +84,9 @@ class PullRequestIngestionServiceV2Test : DatabaseTestsBase() {
         service.ingest(repo.id.value, prThread.pullRequestNumber)

         val existingThread = suspendedTransaction {
+            val identities = IdentityDAO.all().map { it.asDataModel() }
+            assertThat(identities.map { it.externalId }).containsExactlyInAnyOrder("99", "100", "101")
+
             val messages = MessageDAO.all().toList()
             assertThat(messages.map { it.prCommentId }).containsExactlyInAnyOrder("1", "2", "3")
             messages.first().thread.asDataModel()
diff --git a/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProviderTest.kt b/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProviderTest.kt
new file mode 100644
index 000000000..44dfed00e
--- /dev/null
+++ b/projects/libs/lib-pringestion/src/test/kotlin/com/nextchaptersoftware/pringestion/PullRequestReviewThreadProviderTest.kt
@@ -0,0 +1,78 @@
+package com.nextchaptersoftware.pringestion
+
+import com.nextchaptersoftware.db.ModelBuilders
+import com.nextchaptersoftware.db.models.RepoDAO
+import com.nextchaptersoftware.db.models.TeamDAO
+import com.nextchaptersoftware.db.utils.DBTestUtils.suspendingDatabaseTest
+import com.nextchaptersoftware.db.utils.DatabaseTestsBase
+import com.nextchaptersoftware.scm.github.GitHubAppClient
+import com.nextchaptersoftware.scm.github.RateLimit
+import com.nextchaptersoftware.test.utils.TestUtils.suspendingTest
+import org.assertj.core.api.Assertions.assertThat
+import org.junit.jupiter.api.Test
+import org.junit.jupiter.api.assertThrows
+import org.mockito.Mockito.`when`
+import org.mockito.kotlin.any
+import org.mockito.kotlin.eq
+import org.mockito.kotlin.mock
+
+class PullRequestReviewThreadProviderTest : DatabaseTestsBase() {
+    private lateinit var team: TeamDAO
+    private lateinit var repo: RepoDAO
+    private val mockGitHubAppClient = mock<GitHubAppClient>()
+    private val mockV3Org = mock<GitHubAppClient.V3Org>()
+    private val rateLimit = RateLimit(
+        rate = RateLimit.Rate(
+            limit = 5000,
+            remaining = 1001,
+            used = 3999,
+            reset = **********,
+        ),
+    )
+
+    fun setUp() = suspendingTest {
+        val installationId = "12351345"
+        team = ModelBuilders.makeTeam(providerExternalInstallationId = installationId)
+        repo = ModelBuilders.makeRepo(team = team)
+        `when`(mockGitHubAppClient.v3Org(installationId)).thenReturn(mockV3Org)
+        `when`(mockV3Org.rateLimit()).thenReturn(rateLimit)
+    }
+
+    @Test
+    fun `returns threads`() = suspendingDatabaseTest {
+        setUp()
+
+        `when`(mockV3Org.pullRequest(repo.externalOwner, repo.externalName, 100)).thenReturn(
+            MockObjects.gitHubPullRequest(reviewComments = 1),
+        )
+        `when`(mockV3Org.pullRequestComments(eq(repo.externalOwner), eq(repo.externalName), eq(100), any())).thenReturn(
+            listOf(MockObjects.prReviewThreadComment()),
+        )
+        `when`(mockV3Org.pullRequestFiles(eq(repo.externalOwner), eq(repo.externalName), eq(100), any())).thenReturn(
+            listOf(MockObjects.prFile()),
+        )
+
+        val results = PullRequestReviewThreadProvider(mockGitHubAppClient).get(repo.id.value, 100)
+        assertThat(results?.repo?.id).isEqualTo(repo.id)
+        assertThat(results?.team?.id).isEqualTo(team.id)
+        assertThat(results?.threads).hasSize(1)
+    }
+
+    @Test
+    fun `throws DelayDueToRateLimitingException`() = suspendingDatabaseTest {
+        setUp()
+
+        `when`(mockV3Org.rateLimit()).thenReturn(
+            rateLimit.copy(
+                rate = rateLimit.rate.copy(
+                    remaining = 999,
+                    used = 4001,
+                ),
+            ),
+        )
+
+        assertThrows<DelayDueToRateLimitingException> {
+            PullRequestReviewThreadProvider(mockGitHubAppClient).get(repo.id.value, 100)
+        }
+    }
+}
diff --git a/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/TeamPage.kt b/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/TeamPage.kt
index edc80dacb..b7a09e496 100644
--- a/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/TeamPage.kt
+++ b/projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/adminweb/page/TeamPage.kt
@@ -24,7 +24,6 @@ import io.ktor.server.html.respondHtmlTemplate
 import io.ktor.server.request.uri
 import io.ktor.util.pipeline.PipelineContext
 import kotlinx.datetime.Clock
-import kotlinx.datetime.Instant
 import kotlinx.html.h1
 import kotlinx.html.h3
 import kotlinx.html.span
@@ -73,7 +72,7 @@ suspend fun PipelineContext<Unit, ApplicationCall>.renderTeamPage(page: AdminPag
                     property("HTML Url", Url(team.providerHtmlUrl))
                     rateLimitInfo.getOrNull()?.let {
                         property("Rate Limit") {
-                            val reset = Instant.fromEpochSeconds(it.reset).minus(Clock.System.now()).inWholeMinutes
+                            val reset = it.resetInstant.minus(Clock.System.now()).inWholeMinutes
                             +"${it.remaining} remaining / ${it.limit} total (reset in ${reset}m)"
                             span { progressBar(fraction = it.remaining.toDouble() / it.limit) }
                         }
diff --git a/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/Module.kt b/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/Module.kt
index f65cc781e..6272dc6d6 100644
--- a/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/Module.kt
+++ b/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/Module.kt
@@ -83,8 +83,7 @@ fun Application.module(
             PullRequestIngestionJob(
                 pullRequestIngestionService = PullRequestIngestionServiceV2(
                     prReviewThreadProvider = PullRequestReviewThreadProvider(
-                        gitHubConfig = config.scmProviders.github,
-                        jwt = jwt,
+                        gitHubAppClient = GitHubAppClient(config = config.scmProviders.github, jwt = jwt),
                     ),
                     threadSearchEventService = ThreadSearchEventService(
                         sqsProvider = sqsProviderFactory.generate(awsClientProvider, config.queue.searchIndexingQueueName),
diff --git a/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJob.kt b/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJob.kt
index 3f76a3ff1..ad21d4faf 100644
--- a/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJob.kt
+++ b/projects/services/scmservice/src/main/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJob.kt
@@ -1,6 +1,7 @@
 package com.nextchaptersoftware.scmservice.jobs

 import com.nextchaptersoftware.aws.sqs.SqsProvider
+import com.nextchaptersoftware.pringestion.DelayDueToRateLimitingException
 import com.nextchaptersoftware.pringestion.PullRequestIngestionEvent
 import com.nextchaptersoftware.pringestion.PullRequestIngestionServiceV2
 import com.nextchaptersoftware.service.BackgroundJob
@@ -23,6 +24,7 @@ import kotlinx.serialization.decodeFromString
 import kotlinx.serialization.json.Json
 import mu.KotlinLogging
 import mu.withLoggingContext
+import software.amazon.awssdk.services.sqs.model.Message

 private val logger = KotlinLogging.logger {}

@@ -53,35 +55,7 @@ class PullRequestIngestionJob(

                     response.messages().mapNotNull { message ->
                         launch {
-                            runCatching {
-                                Json.decodeFromString<PullRequestIngestionEvent>(message.body())
-                            }.getOrElse {
-                                logger.error(it) { "Failed to deserialize PullRequestIngestionEvent" }
-                                sqsProvider.deleteMessage(message)
-                                null
-                            }?.let { event ->
-                                withLoggingContext(
-                                    "repoId" to event.repoId,
-                                    "prNumber" to event.prNumber.toString(),
-                                ) {
-                                    runCatching {
-                                        process(event)
-                                    }.onSuccess {
-                                        sqsProvider.deleteMessage(message)
-                                    }.onFailure {
-                                        when (it) {
-                                            is TimeoutCancellationException -> {
-                                                // Just log and don't delete message to allow retry
-                                                logger.error(it) { "Pull request ingestion timed out" }
-                                            }
-                                            else -> {
-                                                // Just log and don't delete message to allow retry
-                                                logger.error(it) { "Pull request ingestion failed" }
-                                            }
-                                        }
-                                    }
-                                }
-                            }
+                            process(message)
                         }
                     }.joinAll()
                 }
@@ -90,6 +64,43 @@ class PullRequestIngestionJob(
         }
     }

+    internal suspend fun process(message: Message) = withTimeout(itemProcessingTimeout) {
+        val event = runCatching {
+            Json.decodeFromString<PullRequestIngestionEvent>(message.body())
+        }.getOrElse {
+            logger.error(it) { "Failed to deserialize PullRequestIngestionEvent" }
+            sqsProvider.deleteMessage(message)
+            null
+        } ?: return@withTimeout
+
+        withLoggingContext(
+            "repoId" to event.repoId,
+            "prNumber" to event.prNumber.toString(),
+        ) {
+            runCatching {
+                process(event)
+            }.onSuccess {
+                sqsProvider.deleteMessage(message)
+            }.onFailure {
+                when (it) {
+                    is DelayDueToRateLimitingException -> {
+                        logger.debug(it) { "Re-attempting pull request ingestion in ${it.delay.inWholeMinutes}m" }
+                        val delay = it.delay.plus(5.seconds) // Jitter
+                        sqsProvider.setVisibilityTimeout(message, delay)
+                    }
+                    is TimeoutCancellationException -> {
+                        // Just log and don't delete message to allow retry
+                        logger.error(it) { "Pull request ingestion timed out" }
+                    }
+                    else -> {
+                        // Just log and don't delete message to allow retry
+                        logger.error(it) { "Pull request ingestion failed" }
+                    }
+                }
+            }
+        }
+    }
+
     private suspend fun process(event: PullRequestIngestionEvent) = withTimeout(itemProcessingTimeout) {
         pullRequestIngestionService.ingest(repoId = event.repoId.asUUID(), prNumber = event.prNumber)
     }
diff --git a/projects/services/scmservice/src/test/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJobTest.kt b/projects/services/scmservice/src/test/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJobTest.kt
new file mode 100644
index 000000000..7560d6c34
--- /dev/null
+++ b/projects/services/scmservice/src/test/kotlin/com/nextchaptersoftware/scmservice/jobs/PullRequestIngestionJobTest.kt
@@ -0,0 +1,79 @@
+package com.nextchaptersoftware.scmservice.jobs
+
+import com.nextchaptersoftware.aws.sqs.SqsProvider
+import com.nextchaptersoftware.pringestion.DelayDueToRateLimitingException
+import com.nextchaptersoftware.pringestion.PullRequestIngestionEvent
+import com.nextchaptersoftware.pringestion.PullRequestIngestionServiceV2
+import com.nextchaptersoftware.test.utils.TestUtils.suspendingTest
+import java.util.UUID
+import kotlin.time.Duration.Companion.seconds
+import kotlinx.serialization.encodeToString
+import kotlinx.serialization.json.Json
+import org.junit.jupiter.api.Test
+import org.mockito.Mockito.never
+import org.mockito.Mockito.`when`
+import org.mockito.kotlin.any
+import org.mockito.kotlin.doAnswer
+import org.mockito.kotlin.mock
+import org.mockito.kotlin.times
+import org.mockito.kotlin.verify
+import org.mockito.kotlin.verifyNoInteractions
+import software.amazon.awssdk.services.sqs.model.Message
+
+class PullRequestIngestionJobTest {
+    private val pullRequestIngestionService: PullRequestIngestionServiceV2 = mock()
+    private val sqsProvider: SqsProvider = mock()
+
+    private val repoId = UUID.fromString("03ef58b2-c529-4b17-b04a-23eb75c4aa17")
+    private val prNumber = 21
+    private val event = PullRequestIngestionEvent(repoId = repoId.toString(), prNumber = prNumber)
+    private val eventAsJson = Json.encodeToString(event)
+
+    private val job = PullRequestIngestionJob(
+        pullRequestIngestionService = pullRequestIngestionService,
+        sqsProvider = sqsProvider,
+    )
+
+    @Test
+    fun `process`() = suspendingTest {
+        val message = Message.builder().let {
+            it.body(eventAsJson)
+            it.build()
+        }
+
+        job.process(message)
+
+        verify(pullRequestIngestionService, times(1)).ingest(repoId = repoId, prNumber = prNumber)
+        verify(sqsProvider, times(1)).deleteMessage(message)
+    }
+
+    @Test
+    fun `process handles deserialization error`() = suspendingTest {
+        val message: Message = Message.builder().let {
+            it.body("blah blah blah")
+            it.build()
+        }
+
+        job.process(message)
+
+        verifyNoInteractions(pullRequestIngestionService)
+        verify(sqsProvider, times(1)).deleteMessage(message)
+    }
+
+    @Test
+    fun `process handles DelayDueToRateLimitingException`() = suspendingTest {
+        val message: Message = Message.builder().let {
+            it.body(eventAsJson)
+            it.build()
+        }
+
+        `when`(pullRequestIngestionService.ingest(repoId = repoId, prNumber = prNumber)).doAnswer {
+            throw DelayDueToRateLimitingException(60.seconds)
+        }
+
+        job.process(message)
+
+        verify(sqsProvider, times(1)).setVisibilityTimeout(message, 65.seconds)
+        verify(sqsProvider, never()).deleteMessage(any())
+    }
+}
