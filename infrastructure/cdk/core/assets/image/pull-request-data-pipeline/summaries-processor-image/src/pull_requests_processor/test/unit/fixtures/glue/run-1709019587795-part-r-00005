{"state":"Merged","mergedAt":"2022-05-17T23:09:42Z","number":1329,"body":"We want to store teamId etc. in api queries for honeycomb.\r\n","mergeCommitSha":"e5ebefcc8b6dd573ad07a3591e971eef252545fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1329","title":"Add ability to store our service specific fields in api queries","createdAt":"2022-05-17T22:56:52Z"}
{"state":"Merged","mergedAt":"2022-01-27T23:47:23Z","number":133,"body":"Apologies for this doozy...\r\n\r\n","mergeCommitSha":"e4088676edcafd9f2f86d5bd6456025ffe93e2e6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/133","title":"Auth implementation against stub provider","createdAt":"2022-01-26T06:53:12Z"}
{"state":"Merged","mergedAt":"2022-05-18T00:51:01Z","number":1330,"body":"## Dark Mode\r\n<img width=\"532\" alt=\"CleanShot 2022-05-17 at 15 38 01@2x\" src=\"https://user-images.githubusercontent.com/858772/168925907-180873e5-892c-438f-a753-d9d620878395.png\">\r\n<img width=\"512\" alt=\"CleanShot 2022-05-17 at 15 37 20@2x\" src=\"https://user-images.githubusercontent.com/858772/168925917-acf41197-26e9-4712-90e5-db471df9d163.png\">\r\n<img width=\"531\" alt=\"CleanShot 2022-05-17 at 15 36 01@2x\" src=\"https://user-images.githubusercontent.com/858772/168925922-3d7ef65c-65db-40b1-881d-e4174585ff0b.png\">\r\n<img width=\"563\" alt=\"CleanShot 2022-05-17 at 15 36 10@2x\" src=\"https://user-images.githubusercontent.com/858772/168925925-0326e76e-aa9f-42bf-8f5a-098a2e56e4ef.png\">\r\n<img width=\"533\" alt=\"CleanShot 2022-05-17 at 15 35 38@2x\" src=\"https://user-images.githubusercontent.com/858772/168925929-e7f31290-2fc6-450a-9653-aa219ffb5726.png\">\r\n<img width=\"523\" alt=\"CleanShot 2022-05-17 at 15 58 12@2x\" src=\"https://user-images.githubusercontent.com/858772/168925932-24b0eb5c-ac89-4db4-913e-178617f97820.png\">\r\n<img width=\"531\" alt=\"CleanShot 2022-05-17 at 15 58 47@2x\" src=\"https://user-images.githubusercontent.com/858772/168925937-f2504bae-db91-4ad3-ae97-94c8c75b5a4a.png\">\r\n\r\n## Light Mode\r\n<img width=\"507\" alt=\"CleanShot 2022-05-17 at 15 38 08@2x\" src=\"https://user-images.githubusercontent.com/858772/168925992-093f880a-5489-4627-b026-027ef882f0c5.png\">\r\n<img width=\"552\" alt=\"CleanShot 2022-05-17 at 15 37 11@2x\" src=\"https://user-images.githubusercontent.com/858772/168925999-4cb7cf9b-8a6b-4340-8961-306bb803f2ce.png\">\r\n<img width=\"506\" alt=\"CleanShot 2022-05-17 at 15 36 26@2x\" src=\"https://user-images.githubusercontent.com/858772/168926004-01ee41a6-faa3-4aca-bad1-1d8f857cc347.png\">\r\n<img width=\"516\" alt=\"CleanShot 2022-05-17 at 15 36 31@2x\" src=\"https://user-images.githubusercontent.com/858772/168926007-89b41abe-0f72-40ea-ad21-2ce6fb123695.png\">\r\n<img width=\"514\" alt=\"CleanShot 2022-05-17 at 15 36 48@2x\" src=\"https://user-images.githubusercontent.com/858772/168926012-6c94a8d0-f141-42cf-b7b2-4ba5b84db778.png\">\r\n<img width=\"504\" alt=\"CleanShot 2022-05-17 at 15 58 28@2x\" src=\"https://user-images.githubusercontent.com/858772/168926018-b6dbb3d9-94c5-49cc-9039-8a5727724a1b.png\">\r\n<img width=\"525\" alt=\"CleanShot 2022-05-17 at 15 58 34@2x\" src=\"https://user-images.githubusercontent.com/858772/168926023-5dd64994-d3a4-4c99-bcb1-0c28df4d67d4.png\">\r\n\r\n","mergeCommitSha":"4b7715c6fcf32a46dc6e19a2a5cedf86825f69be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1330","title":"Add sofia pro fonts","createdAt":"2022-05-17T23:02:08Z"}
{"state":"Merged","mergedAt":"2022-05-18T00:12:20Z","number":1331,"mergeCommitSha":"1324cd986fb957c4857b3485b426a214a571af12","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1331","title":"Add operationId","createdAt":"2022-05-17T23:53:29Z"}
{"state":"Merged","mergedAt":"2022-05-18T01:09:39Z","number":1332,"body":"https://ui.honeycomb.io/unblocked/environments/test/trace/Byxb8k1F16f?span=35c6e52a5b053707","mergeCommitSha":"1f1f82b496a93a3016b19efbdf14bb435f21eed4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1332","title":"Remove useless transaction","createdAt":"2022-05-18T00:58:34Z"}
{"state":"Closed","mergedAt":null,"number":1334,"mergeCommitSha":"b359625c9835fbe9361802f2b832918cc6a865db","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1334","title":"Persist PR Open related fields on thread","createdAt":"2022-05-18T03:04:06Z"}
{"state":"Merged","mergedAt":"2022-05-18T21:19:57Z","number":1335,"body":"Running into issue where multiple background listeners were being registered.\r\nThis meant that whenever the client send a message to the background, events were being doubled/tripled/etc...\r\n\r\nThis was causing multiple auth windows to appear and rate limiting our auth: Fixes https://github.com/NextChapterSoftware/unblocked/issues/1333\r\n\r\nWe should only ever have a single background listener per port type.","mergeCommitSha":"c2e67315b95ec85a832f5074b3c7cb235ede4b57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1335","title":"Fix multiple background listeners","createdAt":"2022-05-18T04:00:45Z"}
{"state":"Merged","mergedAt":"2022-05-25T15:41:12Z","number":1336,"body":"Setup basic outline for installation wizard.\r\n\r\nHave not spent enough time into thinking about wizard architecture to make this generic. My hope was to keep these wizards simple for now and then refactor out a generic wizard once we have a better understanding of functionality we need.\r\n\r\n","mergeCommitSha":"6c32167612d9bc73d3675e3710f8d70440546f89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1336","title":"Setup outline for installation wizard","createdAt":"2022-05-18T15:16:57Z"}
{"state":"Merged","mergedAt":"2022-05-18T16:23:12Z","number":1337,"mergeCommitSha":"1dad491ad9ab42df54a20990499fb48d66b3e2ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1337","title":"Add new office IP to allow list","createdAt":"2022-05-18T16:22:57Z"}
{"state":"Closed","mergedAt":null,"number":1338,"body":"Not working...","mergeCommitSha":"2e1a6f1dfb6653f9415cce10d40fbcb01fc43a52","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1338","title":"DatabaseTests should catch and rerun only PSQLException","createdAt":"2022-05-18T18:00:55Z"}
{"state":"Merged","mergedAt":"2022-05-18T19:08:06Z","number":1339,"mergeCommitSha":"75e0c8147ab7f9130586021dc047f9fb5edf4a7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1339","title":"Cleanup code","createdAt":"2022-05-18T18:13:56Z"}
{"state":"Merged","mergedAt":"2022-01-26T22:54:37Z","number":134,"body":"Adds\r\n- ability to put/get a source mark\r\n- ability to get chats for a source mark\r\n\r\nRemoves\r\n- ability to list all chats (sourcemarkId query param is required)\r\n- ability to list all messages (chatId query param is now required)\r\n\r\nStill TBD:\r\n- Sourcemark or Annotation? Let's discuss in person","mergeCommitSha":"3acb44eb556d013208f5ce1ce981df99f8bf39ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/134","title":"Create sourcemarks/annotations api stubs","createdAt":"2022-01-26T08:54:02Z"}
{"state":"Merged","mergedAt":"2022-05-18T19:11:17Z","number":1340,"mergeCommitSha":"d11578986a7d95e3e0bf1648ece47a496cc876bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1340","title":"Update secrets","createdAt":"2022-05-18T19:10:53Z"}
{"state":"Merged","mergedAt":"2022-05-18T19:15:51Z","number":1341,"mergeCommitSha":"bd5b349635ff08ed3347585100815bf028ca8fc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1341","title":"update","createdAt":"2022-05-18T19:15:46Z"}
{"state":"Merged","mergedAt":"2022-05-18T19:35:59Z","number":1342,"mergeCommitSha":"24bb78ce7c8702e2f4e0d4657fff8755408faa0d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1342","title":"Fix up secrets config","createdAt":"2022-05-18T19:23:07Z"}
{"state":"Merged","mergedAt":"2022-05-18T20:05:43Z","number":1343,"body":"How did this pass CI?","mergeCommitSha":"83cc6355db22a9d1210bf787b2f51ba8840c6a7e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1343","title":"Fix build","createdAt":"2022-05-18T19:57:29Z"}
{"state":"Merged","mergedAt":"2022-05-18T20:02:23Z","number":1344,"mergeCommitSha":"9a6f9601fd6c6ea53cf888fad972b0e4d2e20e67","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1344","title":"Lock to version","createdAt":"2022-05-18T20:02:06Z"}
{"state":"Merged","mergedAt":"2022-05-18T20:28:43Z","number":1345,"mergeCommitSha":"93819a06ad7f6a8f211e6a8fc3bd5198ad9bbed0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1345","title":"update","createdAt":"2022-05-18T20:28:36Z"}
{"state":"Merged","mergedAt":"2022-05-20T00:06:10Z","number":1346,"body":"Saves the PR author.\r\n\r\nWill be needed for:\r\n1. recommendation engine\r\n2. making the PR author a participant(https://chapter2global.slack.com/archives/C02GEN8LFGT/p1652989775801499)","mergeCommitSha":"d54217bb1464094d9ca9cc065ccd2ed9172d5fa0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1346","title":"Persist PR creator on thread","createdAt":"2022-05-18T20:31:41Z"}
{"state":"Merged","mergedAt":"2022-05-20T02:51:36Z","number":1347,"body":"(This builds on top of https://github.com/NextChapterSoftware/unblocked/pull/1309)\r\n\r\nUse ThreadInfo APIs in VSCode.\r\n\r\nSome caveats:\r\n* Similar to the dashboard, this breaks search.\r\n* For the editor/gutter integration, the behaviour isn't ideal: when we open the file, we fetch all threads for sourcemarks in the file, and then we begin monitoring those threads.  I think we can do this, but should maybe crank the polling frequency in this case way down (once every 20/30 seconds or something) -- the only data we pull from this is the title and participants, which change very rarely.","mergeCommitSha":"62c6fa1288cc924d1b59b9d459df38235a1ddc1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1347","title":"Use ThreadInfo API for VSCode","createdAt":"2022-05-18T20:38:39Z"}
{"state":"Open","mergedAt":null,"number":1348,"mergeCommitSha":"d17d8768061b4b4f0cdc50485bc4d424af4a7430","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1348","title":"WIP testing docker based workers. do NOT merge ","createdAt":"2022-05-18T20:43:51Z"}
{"state":"Merged","mergedAt":"2022-05-18T21:17:27Z","number":1349,"mergeCommitSha":"e16326b2490a7d40c6c459cf64e1b2c6c9b25b98","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1349","title":"Add traces for toher services","createdAt":"2022-05-18T20:57:26Z"}
{"state":"Merged","mergedAt":"2022-01-26T20:07:46Z","number":135,"body":"- Try adding network policy for API service again\r\n- Changed EKS cluster node type because `t2` instances are not available in some regions like `us-west-2d`\r\n- Added DNS allow policy \r\n- Modified deny all policy to allow cluster egress traffic \r\nAll of these changes except the ` apiservice/.helm/service/templates/network-policy.yaml` have been verified, ","mergeCommitSha":"8dcdcaac1a9f112f31a7e7e01ca15cf0b78382f6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/135","title":"Eks install calico","createdAt":"2022-01-26T19:02:18Z"}
{"state":"Merged","mergedAt":"2022-05-19T00:02:14Z","number":1350,"mergeCommitSha":"d9b4a2ac1d36d32926a1b353a0c3eb1cd041c3d5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1350","title":"Implements fat thread push channels","createdAt":"2022-05-18T21:50:56Z"}
{"state":"Merged","mergedAt":"2022-05-18T22:25:02Z","number":1351,"body":"Deployments are timing out in prod. This should give a little more time to helm so the atomic rollover could finish.","mergeCommitSha":"4d6e15e49eef2a1f970b026cf93d6f021e84aa2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1351","title":"increase helm deployment timeout","createdAt":"2022-05-18T21:53:42Z"}
{"state":"Merged","mergedAt":"2022-05-18T22:33:18Z","number":1352,"mergeCommitSha":"aa2854cc73cb9b9eb7401de78d5794c69cbd6938","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1352","title":"Add onboarding templates and assets folder","createdAt":"2022-05-18T22:14:55Z"}
{"state":"Merged","mergedAt":"2022-05-20T23:35:25Z","number":1353,"body":"Waiting on hub app to implement pushing to fully test this out.\r\n\r\nSets up new grpc stream with Hub app whenever repos are updated in VSCode.","mergeCommitSha":"a30ff0944908eef45b505cdaaaa1e7a671070476","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1353","title":"Open discussion in VSCode from Hub","createdAt":"2022-05-18T22:42:48Z"}
{"state":"Merged","mergedAt":"2022-05-19T16:58:35Z","number":1354,"mergeCommitSha":"f73ae7b0be2055039a026ac27662e6bcbacfd9fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1354","title":"Update font to sofia-pro and update reply count","createdAt":"2022-05-18T23:05:15Z"}
{"state":"Merged","mergedAt":"2022-05-19T00:09:45Z","number":1355,"mergeCommitSha":"cf1d908c52afa303f7d83c08d94cad0356719cf3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1355","title":"Order participants by createdAt in API","createdAt":"2022-05-18T23:20:39Z"}
{"state":"Merged","mergedAt":"2022-05-19T21:42:00Z","number":1356,"body":"Basic Installation Store.\r\n\r\nShould be used to populate \r\n<img width=\"1004\" alt=\"CleanShot 2022-05-18 at 16 50 23@2x\" src=\"https://user-images.githubusercontent.com/1553313/169173532-66e44c04-ba56-4670-a9b6-4b10caa7b8d4.png\">\r\n","mergeCommitSha":"69f9aee876c7946f92f3ae3fdde6229a3f12ed2a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1356","title":"VSCode Installation Store","createdAt":"2022-05-18T23:50:50Z"}
{"state":"Merged","mergedAt":"2022-05-19T00:45:43Z","number":1357,"mergeCommitSha":"6d7614f991e84f9ca18fa6737939c808f99e7558","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1357","title":"Add identity id to traces","createdAt":"2022-05-19T00:22:26Z"}
{"state":"Merged","mergedAt":"2022-05-19T04:43:36Z","number":1358,"body":"Seems solid afaict...","mergeCommitSha":"2fd0ee81e5bcec44433ce0edd28b52bed66cbc81","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1358","title":"Test for /mine lastModified","createdAt":"2022-05-19T00:43:55Z"}
{"state":"Merged","mergedAt":"2022-05-19T01:09:20Z","number":1359,"mergeCommitSha":"aed6a4f751f501de2903b1cd31de4b95df54eff1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1359","title":"Fix sampling","createdAt":"2022-05-19T00:55:48Z"}
{"state":"Merged","mergedAt":"2022-01-26T22:30:17Z","number":136,"body":"- Added docs for KMS key generation\r\n- Added KMS key to Dev cluster for Kube secrets encryption \r\n- Enabled KMS backed secret encryption in Dev ","mergeCommitSha":"27aa9524d112e51921667d0e1589678794bef40e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/136","title":"configured dev cluster to use KMS for secret encryption","createdAt":"2022-01-26T22:28:02Z"}
{"state":"Merged","mergedAt":"2022-05-19T03:05:42Z","number":1360,"mergeCommitSha":"2e180d5a305a7e4caea3e9400b6fcbe23f193215","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1360","title":"Revert main panel to use macOS default fonts","createdAt":"2022-05-19T03:03:45Z"}
{"state":"Merged","mergedAt":"2022-05-19T19:18:29Z","number":1361,"body":"Currently the \"dumb\" version that works like this:\r\n\r\n1. Fetch `/threads/mine` with limit 25\r\n2. Subscribe to `/threads/mine` with latest modified at\r\n3. Subscribe to every `/thread/:id` with latest modified at\r\n4. If _any_ of those fire, repeat from step 1\r\n\r\nThis works fine and takes care of the deleted/archivedAt case, but obviously does not allow for any scrolling past 25 items, and is not as efficient as if we called `/threads/mine` with a latest modified value. \r\n\r\nIt's still an improvement over what we had previously though.\r\n\r\nNext iteration will add infinite scrolling and do things the \"right\" way:\r\n- `GET /threads/mine&limit=N`\r\n- subscribe to each of the N threads by id, using modifiedSince\r\n- subscribe to `/threads/mine` (no limit) using modifiedSince\r\n- fetch more from `threads.last().cursor` when the end of the list is visible to user","mergeCommitSha":"9cd5faab4ddc1845635d68bbe16ff41e6fecf0b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1361","title":"Integrates with fat threads","createdAt":"2022-05-19T05:01:03Z"}
{"state":"Merged","mergedAt":"2022-05-19T07:47:13Z","number":1362,"body":"Changes here are just plumbing really:\r\n- Annotate threads, when created from API and PRInjestion, with ThreadRank.\r\n  The ThreadRank is random gibberish for now.\r\n- Moved event notifications outside the DB transaction to prevent sending\r\n  retries and prevent sending events if transaction rolled-back.\r\n\r\nNext:\r\n- use ingested PR data to generate recommendation model\r\n- calculate ThreadRank from recommendation model\r\n\r\nFuture:\r\n- decouple libraries, creating separately deployed recommendation service\r\n- decouple ThreadRank calculation from thread creation runtime by sending async event to SQS instead of recommending inline","mergeCommitSha":"b82448e3a31abb814de2078910e75638cf5dd7ab","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1362","title":"Recommend thread on creation","createdAt":"2022-05-19T07:30:09Z"}
{"state":"Merged","mergedAt":"2022-05-20T18:58:31Z","number":1363,"mergeCommitSha":"88b3dd66c978c66fafa51897d6dfc412bbd1a7c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1363","title":"Unblocked installer","createdAt":"2022-05-19T07:35:06Z"}
{"state":"Merged","mergedAt":"2022-05-19T17:41:32Z","number":1364,"body":"TODO in follow up:\r\n- [ ] API tests\r\n- [ ] move modifiedAt concatenation logic down to the ThreadInfoStore","mergeCommitSha":"45ef537a579ad39c0fa8df853b33407ede65d089","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1364","title":"Quick fix for thread pusher issue","createdAt":"2022-05-19T17:30:01Z"}
{"state":"Merged","mergedAt":"2022-05-19T21:22:45Z","number":1365,"body":"This change causes Github action to request a spot instance with the same size as the current on-demand instance type specified in our config. This is just a temporary fix while I address a bug","mergeCommitSha":"0502c3f44bbc0bc2f1921a251f2daf50c8635366","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1365","title":"temp fix while I address a bug","createdAt":"2022-05-19T17:37:51Z"}
{"state":"Merged","mergedAt":"2022-05-19T17:45:42Z","number":1366,"body":"Probably needs a test...","mergeCommitSha":"ee2d824a1349d9a7809b6d1b3c69a3e7c9cde354","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1366","title":"Fix unread sorting bug","createdAt":"2022-05-19T17:37:54Z"}
{"state":"Merged","mergedAt":"2022-05-19T18:09:01Z","number":1367,"mergeCommitSha":"5d9ae998c33efb50be9028f996c0f2b1a4533c9b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1367","title":"Fix up transaction contexts","createdAt":"2022-05-19T17:42:00Z"}
{"state":"Merged","mergedAt":"2022-05-19T18:18:31Z","number":1368,"mergeCommitSha":"0c2a29592f3634aa6e5e1754e92955c197dffac4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1368","title":"Reduce pusher events","createdAt":"2022-05-19T18:18:24Z"}
{"state":"Merged","mergedAt":"2022-05-19T19:17:37Z","number":1369,"body":"I had logic completely backwards.","mergeCommitSha":"c9ab232c342a66f317313c3c585e98ed31bd6df9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1369","title":"Only recommended threads for non-thread participants","createdAt":"2022-05-19T18:58:29Z"}
{"state":"Merged","mergedAt":"2022-01-26T22:55:20Z","number":137,"body":"Removing GH integration led to issues transferring accepted baselines from branches to main.\r\n\r\nNow, auto accept changes as new baselines for the main branch.\r\n\r\nWe are making this decision with the assumption that reviewers have already approved baselines in the PRs before merging to main.\r\n\r\nMore info: https://www.chromatic.com/docs/branching-and-baselines#squash-and-rebase-merging","mergeCommitSha":"b0b625b4d9f41374b935e86e0c3ad6ed28b81713","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/137","title":"Auto accept baselines in main","createdAt":"2022-01-26T22:41:23Z"}
{"state":"Merged","mergedAt":"2022-05-19T19:21:38Z","number":1370,"body":"We could probably have a nicer UI (i.e. show an error state on submit instead of just disabling) but this should do for now","mergeCommitSha":"ffcd52107d2ab9d972a094356928dd361fbc6acb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1370","title":"Dont allow thread creation without title","createdAt":"2022-05-19T19:11:03Z"}
{"state":"Merged","mergedAt":"2022-05-19T21:08:25Z","number":1372,"body":"Small visual change.\r\n\r\n## Has Unreads\r\n<img width=\"520\" alt=\"CleanShot 2022-05-19 at 12 41 32@2x\" src=\"https://user-images.githubusercontent.com/858772/169389413-3db561c0-9596-4ba4-8c3f-b414a2c9fbe1.png\">\r\n\r\n\r\n## No Unreads\r\n<img width=\"502\" alt=\"CleanShot 2022-05-19 at 12 41 05@2x\" src=\"https://user-images.githubusercontent.com/858772/169389431-1065955f-6ca1-437c-807e-9e7304c5c30a.png\">\r\n\r\n","mergeCommitSha":"9f913e1f86bd4512fed5a0178670e35840dba3c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1372","title":"Only indent the list if there is at least one unread","createdAt":"2022-05-19T19:40:54Z"}
{"state":"Merged","mergedAt":"2022-05-19T20:12:39Z","number":1373,"body":"One liner","mergeCommitSha":"e05581eb3c0bee6400a4c388842078489f87508b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1373","title":"Accidentally inverted merge logic for notifications","createdAt":"2022-05-19T20:01:26Z"}
{"state":"Merged","mergedAt":"2022-05-19T21:06:07Z","number":1374,"body":"Hub popover anchor point is now centered\r\n\r\n<img width=\"499\" alt=\"CleanShot 2022-05-19 at 13 17 05@2x\" src=\"https://user-images.githubusercontent.com/858772/169397142-55e6e536-c472-4fff-a3cd-ed300d77ffab.png\">\r\n","mergeCommitSha":"63766a1f5f71ab49ff5ae5afb30f2a16312e8ad6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1374","title":"Fix hub anchor point","createdAt":"2022-05-19T20:17:18Z"}
{"state":"Merged","mergedAt":"2022-05-19T21:02:38Z","number":1375,"mergeCommitSha":"2a8ca4a265a02adbfd258a62b51162a8e12102d1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1375","title":"Cleanup notificaiton classes","createdAt":"2022-05-19T20:19:50Z"}
{"state":"Merged","mergedAt":"2022-05-20T23:36:01Z","number":1376,"body":"This ended up being a little bit more involved then you might guess.  The Archived UI works differently then the other thread UIs: the archived list is loaded only when you navigate to it, and is not a live UI (ie it does not have a push channel).  So I couldn't reuse the same data loading machinery.\r\n\r\nI ended up making a separate store for the archived threads, that loads the threads, joins in the team members, and produces a stream that is compatible with the rest of the UI.","mergeCommitSha":"2b7c68cbd685ac786012c5d77ae396e3cdbf93e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1376","title":"Re-add Archived Threads UI to dashboard","createdAt":"2022-05-19T20:49:01Z"}
{"state":"Merged","mergedAt":"2022-05-19T21:05:48Z","number":1377,"body":"Lifts it up a tad and capitalizes the thread title","mergeCommitSha":"65225c0a45e6edfb010651c8eecffe8cb60d9859","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1377","title":"Fix thread bubble alignment","createdAt":"2022-05-19T21:05:05Z"}
{"state":"Merged","mergedAt":"2022-05-19T22:45:28Z","number":1378,"body":"I don't generally get the opportunity to get reviewed by @richiebres \r\nThe honor is all mine!!!1","mergeCommitSha":"6c3e850b588a62d134a258fe268848330dae8da7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1378","title":"AddInvitesEndpoint","createdAt":"2022-05-19T21:20:59Z"}
{"state":"Merged","mergedAt":"2022-05-20T00:24:50Z","number":1379,"mergeCommitSha":"2a1621ee2b805b81cd143f28a2e2be99bad9b008","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1379","title":"Rename team to recommended in dashboard","createdAt":"2022-05-19T23:21:07Z"}
{"state":"Merged","mergedAt":"2022-01-31T19:13:17Z","number":138,"body":"vscode:\r\n![image](https://user-images.githubusercontent.com/13431372/151259736-d5a145e8-b44f-4d7e-a53a-c1ace36b0374.png)\r\n\r\nstorybook:\r\n![image](https://user-images.githubusercontent.com/13431372/151269011-d1813ae4-b0cf-4dd6-baeb-a89ac173ebaa.png)\r\n* NOTE: built in the `test-theme.scss` into the vscode storybook \r\n\r\nThis is only the UI form, with mocked data, no backend implementation yet.\r\n\r\nStill TODO (in ensuing PRs):\r\n* Fix state management so state of the form persists when webview reloads (i.e. on tab change)\r\n* Add generate-api to generate API models\r\n* Layer 'find team members' logic to populate list of collaborators \r\n* Add user icons to UI (will need to revise existing `web/` component per designs and move it to the `shared/` folder once that's set up) \r\n* Add fontawesome icons to vscode and to UI (move to `shared/` etc)","mergeCommitSha":"d58e5ed5af5e7f1af28f105b953d18371e256e7a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/138","title":"Start discussion form UI","createdAt":"2022-01-27T00:23:25Z"}
{"state":"Merged","mergedAt":"2022-05-20T00:47:42Z","number":1380,"mergeCommitSha":"ed5ad77e2a6f8d3474806e13802c5197fb0e03ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1380","title":"Admin web PR re-ingestion button","createdAt":"2022-05-20T00:24:44Z"}
{"state":"Merged","mergedAt":"2022-05-20T01:13:56Z","number":1381,"body":"Fixes this:\r\nhttps://admin.dev.getunblocked.com/teams/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/repos/cecea018-fdc0-46aa-a160-8c35ba197164/threads\r\n\r\nBreaking change was here:\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/1346","mergeCommitSha":"b8ed8a7f363ed3842be2b91f3c809ca78e8c4c9a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1381","title":"Fix Thread-to-TeamMember joins","createdAt":"2022-05-20T00:57:58Z"}
{"state":"Merged","mergedAt":"2022-05-20T02:00:35Z","number":1382,"mergeCommitSha":"26c6b777888f331eb0da2bc223bca0665c92dfdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1382","title":"Hook up invite email to temp UI","createdAt":"2022-05-20T01:30:35Z"}
{"state":"Merged","mergedAt":"2022-05-20T02:07:58Z","number":1383,"body":"<img width=\"961\" alt=\"Screen Shot 2022-05-19 at 18 51 15\" src=\"https://user-images.githubusercontent.com/1798345/169432615-7710b86c-add0-4330-b8f2-fb48d2abedd0.png\">\r\n","mergeCommitSha":"6263a8be23e32611297c80f29040dbab035bd8ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1383","title":"Show PR author in admin web","createdAt":"2022-05-20T01:51:25Z"}
{"state":"Merged","mergedAt":"2022-05-20T03:23:55Z","number":1384,"body":"Because the operations here are asynchronous, it's hard to deterministically tell which push channels will be called in which order -- they may be polled at the same time, they may be polled one after another.  As long as both channels are actually polled, this should pass.\r\n\r\nLonger-term, the plan is to define these async tests using fake timers, which would allow fully deterministic behaviour, but we need to upgrade to Jest 28 before we can do that, and that will take a fair bit of effort.","mergeCommitSha":"48c3d5446dd0fef4d77cc9182906dc1a4bc633be","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1384","title":"Stabilize flaky test","createdAt":"2022-05-20T03:17:25Z"}
{"state":"Merged","mergedAt":"2022-05-20T17:55:53Z","number":1385,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1652990002219319\r\n\r\nThis is causing unnecessary pushes when an author posts a reply to a PR thread from unblocked.","mergeCommitSha":"9174eeab33e294aad2d88d8f7ec6c52ab09300a4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1385","title":"Correctly set lastMessageCreatedAt for PR ignested threads","createdAt":"2022-05-20T04:34:18Z"}
{"state":"Merged","mergedAt":"2022-05-20T19:18:40Z","number":1386,"body":"- generates recommendation model from admin web for now\r\n\r\nThe social comment network is the first of at least 8 signals that we'll use for recommendation purposes. See for context:\r\nhttps://www.notion.so/nextchaptersoftware/Recommendation-Engine-18047c5f07844b9d9bef343add8a7f0f#9c78c6a94cd64cceacfce755ee93329c","mergeCommitSha":"f37844eb04aa274d767dc630076e5ce62a95087d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1386","title":"Recommendation engine with Social Comment Network model","createdAt":"2022-05-20T17:19:27Z"}
{"state":"Merged","mergedAt":"2022-05-21T01:09:39Z","number":1387,"body":"### Motivation\r\n1. allows for paging forwards and backwards from any thread list item\r\n2. facilitates very efficient polling of an existing list view using combination of `If-Modfied-Since` and `before`. only one subscription is necessary\r\n\r\n### See also\r\n- https://www.notion.so/nextchaptersoftware/Thread-List-Polling-36544355f402456aa784337ff6e94c94","mergeCommitSha":"3b9314fda8e2bf6a4f28be8569a96882e23e07ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1387","title":"Before and After cursors for more efficient polling and paging","createdAt":"2022-05-20T17:55:46Z"}
{"state":"Merged","mergedAt":"2022-05-20T18:16:02Z","number":1388,"body":"Makes things appear faster","mergeCommitSha":"f16e7fe02d6ab556429c6bec5aaf5324516a5627","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1388","title":"Hold content view in memory instead of recreating on popup. ","createdAt":"2022-05-20T18:15:45Z"}
{"state":"Merged","mergedAt":"2022-05-20T23:27:39Z","number":1389,"body":"Enqueuing from ApiService should be a simple event, there should be zero processing on the apiservice side.\r\n\r\nThe NotificationService should now hold sole responsibility of generating the actual template that is pushed to SES.\r\n\r\nTo that end:\r\n1. We have Polymorphic Payload types that are sent to noficiation service.\r\n2. We have Payload handlers in notification service that manage the actual pushing to ses.\r\n","mergeCommitSha":"ff0bf90c977fb7ee7ee244b3a9e5ade939ac6bf2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1389","title":"Simplify Notification Service","createdAt":"2022-05-20T19:20:14Z"}
{"state":"Merged","mergedAt":"2022-01-27T00:38:30Z","number":139,"body":"This pr solves the annoying problem of inconsistency in api specs.\r\n\r\nWe are now going to use zally:\r\nhttps://github.com/zalando/zally\r\n\r\nThe nice thing about Zally is that they use kotlin, so I've done the following:\r\n1. Piggy back on gradle buildSrc to generate Codeswell specific gradle plugin that we can use to lint our openapi spec via our own tasks (zallyLint)\r\n2. Allow for custom configuration of ruleset for Zally.\r\n\r\nTo run: ./gradlew :api:zallyLint\r\n\r\nI'm going to in a followup pr fix our openapi spec and code and reduce the maximum lint errors allowed before a gradle build will fail.\r\n\r\n","mergeCommitSha":"d2d0e7155d1c5e37096bb5255c5e703ed06468c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/139","title":"Add openapi linter","createdAt":"2022-01-27T00:23:43Z"}
{"state":"Merged","mergedAt":"2022-05-20T23:04:05Z","number":1390,"body":"This will break the searchThreads operations","mergeCommitSha":"812c8bdf904c11dcbee10d32f10a95653a16a796","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1390","title":"Update search threads operation to return ThreadInfos","createdAt":"2022-05-20T19:44:27Z"}
{"state":"Merged","mergedAt":"2022-05-20T22:38:09Z","number":1391,"body":"Just refactoring to introduce the new component. No new functionality.\r\n\r\n<img width=\"300\" alt=\"Screen Shot 2022-05-20 at 15 04 57\" src=\"https://user-images.githubusercontent.com/1798345/169618426-0fc635e4-7d72-4858-90b1-707e4f994d10.png\">\r\n","mergeCommitSha":"69dcad6d25d904802447f570451085c4e6dc7ce1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1391","title":"Admin web actions menu","createdAt":"2022-05-20T22:04:37Z"}
{"state":"Merged","mergedAt":"2022-05-21T00:12:06Z","number":1392,"body":"As with any resilience stuff, this can be a bit tricky to follow. There are two central ideas here:\r\n\r\n- Everything is gated on auth, which blocks\r\n- Failures are retried with backoff until they succeed\r\n\r\nThe fact that everything is gated on auth and that auth will put the breaks on when there's a 401 will prevent runaway retries. I've added \"fetching\" flags to all the stores so that attempts don't stack, and there is only ever 1 \"retrying\" task in flight for a store at one time. \r\n\r\nThere is currently no special handling for 400 range errors. Arguably the request should abort, but doing so will leave the client is an invalid state since there is no error state UX at the moment. \r\n\r\nTechnically none of the requests the client makes should result in a 400 though. If that occurs, then it's programmer error. ","mergeCommitSha":"8d7ca91f356f0ff3432dca26fd2fd778e6e3a2ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1392","title":"Add defensive networking","createdAt":"2022-05-20T22:37:41Z"}
{"state":"Merged","mergedAt":"2022-05-27T16:25:38Z","number":1393,"mergeCommitSha":"ae63873360d2dd43d70e9493389f68ad830092e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1393","title":"Build hub and extension for each installer build","createdAt":"2022-05-20T22:45:19Z"}
{"state":"Merged","mergedAt":"2022-05-21T00:03:40Z","number":1394,"body":"Replaces random ThreadRank with value from SocialCommentNetwork.","mergeCommitSha":"55742162edff1981287f0759a05133a05899835a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1394","title":"Lookup ThreadRank from SocialNetworkModel on thread creation","createdAt":"2022-05-20T23:46:53Z"}
{"state":"Merged","mergedAt":"2022-05-24T16:17:30Z","number":1395,"mergeCommitSha":"7121be83f0d16c7e5255c746f1c5545378293d02","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1395","title":"update","createdAt":"2022-05-21T00:28:35Z"}
{"state":"Merged","mergedAt":"2022-05-21T01:12:38Z","number":1396,"body":"<img width=\"1141\" alt=\"image\" src=\"https://user-images.githubusercontent.com/1798345/169628533-9fa76223-fcf3-4afb-ab6a-37768410e68c.png\">\r\n","mergeCommitSha":"a9998d271368590046e2f54024eaf12c22d4136c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1396","title":"Grid visualization of social comment network","createdAt":"2022-05-21T01:05:45Z"}
{"state":"Merged","mergedAt":"2022-05-21T01:49:01Z","number":1397,"body":"Very very inefficient, but I don't care -- just for backfilling once off.\r\n\r\nPG still handles it in a few seconds.\r\n\r\nAfter this lands, people will see recommended threads in dashboard.","mergeCommitSha":"8a644eec7cb43fbc5ea9759b0e9cb6cad9311d70","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1397","title":"Admin web triggers ThreadRank backfill","createdAt":"2022-05-21T01:36:09Z"}
{"state":"Merged","mergedAt":"2022-05-24T19:45:58Z","number":1398,"mergeCommitSha":"e1a68cc7a40b140f9bb7f40e9cd833a7078b171f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1398","title":"Re-add search to dashboard and VSCode","createdAt":"2022-05-21T03:40:32Z"}
{"state":"Merged","mergedAt":"2022-05-21T04:02:50Z","number":1399,"mergeCommitSha":"eb1d58e022882c1d7ea2a03bc59643179bcc232d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1399","title":"Pop open hub on first load","createdAt":"2022-05-21T03:52:02Z"}
{"state":"Merged","mergedAt":"2021-12-15T18:52:57Z","number":14,"mergeCommitSha":"0a96fef00a5ac9c0b2174e3ebe28f9f7c41163f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/14","title":"Update Stories","createdAt":"2021-12-15T17:51:13Z"}
{"state":"Merged","mergedAt":"2022-01-27T06:27:30Z","number":140,"body":"Lets simplify the code here","mergeCommitSha":"08251f694fbb7097a6fc0aa201a8a0ea5d2b87e5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/140","title":"Successful responses dont require setting status code 200","createdAt":"2022-01-27T01:20:35Z"}
{"state":"Merged","mergedAt":"2022-05-21T14:56:06Z","number":1400,"mergeCommitSha":"c9f5354df58e0642ecff17e5b873e8f9c89aa15d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1400","title":"Do not expose internal exceptions in API responses","createdAt":"2022-05-21T14:33:11Z"}
{"state":"Merged","mergedAt":"2022-05-21T15:42:43Z","number":1401,"mergeCommitSha":"50a5c54cc7e8486735e0e0410354a4c610161a0c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1401","title":"StatusPages cleanup","createdAt":"2022-05-21T15:00:20Z"}
{"state":"Merged","mergedAt":"2022-05-21T15:32:54Z","number":1402,"mergeCommitSha":"138918703561dde7dca0d705e046356a80bcafb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1402","title":"Relabel Team to Recommended in WebExt and VSCode","createdAt":"2022-05-21T15:26:37Z"}
{"state":"Merged","mergedAt":"2022-05-21T16:23:00Z","number":1403,"body":"I've often asked myself what life would be like without @matthewjamesadam @mahdi-torabi @pwerry @davidkwlam @kaych @dennispi @richiebres @jeffrey-ng @benedict-jw @kaych\r\n\r\nIt would not be as fun, that's for sure.","mergeCommitSha":"6bf47309b18a5d1717648a9bfeec1340e5cabcb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1403","title":"Add contentBasedDeduplication option","createdAt":"2022-05-21T16:07:47Z"}
{"state":"Merged","mergedAt":"2022-05-23T17:05:48Z","number":1404,"body":"Comments made on a PR should include the PR creator as a thread participant.\r\nThe comment is made on the creator's PR so it is relevant to the creator,\r\nnot just the user who started the comment.\r\n\r\nFrom https://chapter2global.slack.com/archives/C02GEN8LFGT/p1652989775801499.","mergeCommitSha":"4567f7ccd1ae3a2bd9b9300617fcba600f14ece0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1404","title":"PR creator is a PR thread participant","createdAt":"2022-05-22T04:40:28Z"}
{"state":"Merged","mergedAt":"2022-05-24T04:49:27Z","number":1405,"body":"Using 4 month (16w) decay constant, because 1 month (4w) decay was much too aggressive.\r\n\r\nAfter _N_ Months | 4 Month Decay    | 1 Month Decay\r\n-----------------|------------------|----------------\r\n0                | 1.000            | 1.000\r\n1                | 0.939            | 0.368\r\n2                | 0.779            | 0.018\r\n3                | 0.570            | 0.000\r\n4                | 0.368            | 0.000\r\n5                | 0.210            | 0.000\r\n6                | 0.105            | 0.000\r\n\r\nFrom https://chapter2global.slack.com/archives/C02HEVCCJA3/p1653090142396089?thread_ts=1653075978.167919&cid=C02HEVCCJA3.","mergeCommitSha":"fbe8200dcbcc0021af26c6079e47a12c1aa2209b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1405","title":"Recommendations use 16 week decay constant","createdAt":"2022-05-22T05:32:17Z"}
{"state":"Closed","mergedAt":null,"number":1406,"body":"Downside is that ThreadRankModel is now dense and scales with number of\r\nthreads and team members, so the storage complexity is now O(N*T),\r\nwhere N is thread count and T is team size.\r\n\r\nAlso takes longer to run recommendation engine because it has to\r\npersist a lot more data (mostly rows with weight 0.0) to DB.\r\n\r\nRecommending all threads is conceptually inconsistent. An alternative solution\r\nwould be to incorporate signals other than SocialCommentNetwork. For\r\nexample, recommend threads for files that you have created insight bubbles\r\nfor, or recommend threads from people that you have interacted with in\r\nprevious Unblocked conversations.\r\n\r\nFrom https://chapter2global.slack.com/archives/C02HEVCCJA3/p1653107358086729?thread_ts=1653099239.071839&cid=C02HEVCCJA3.","mergeCommitSha":"664ca581bf311c374274cffc083bf54cce621c93","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1406","title":"[RFC] Apply ThreadRank to every thread and remove minimum filter","createdAt":"2022-05-22T05:38:32Z"}
{"state":"Merged","mergedAt":"2022-05-31T00:03:05Z","number":1407,"body":"Currently working for thread view invites. Adding to onboarding next","mergeCommitSha":"b5b9abead464a7bceabdb4b6c1997f8e9c3a5be1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1407","title":"Implements team invites, missing error states","createdAt":"2022-05-23T20:26:49Z"}
{"state":"Merged","mergedAt":"2022-05-24T05:23:16Z","number":1408,"body":"Attempt to debug this:\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1653365615171469","mergeCommitSha":"1d87f058dac405ba51a3fe10730453a889f8aef0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1408","title":"Debug pusher when threads change","createdAt":"2022-05-24T05:06:34Z"}
{"state":"Merged","mergedAt":"2022-05-25T17:01:33Z","number":1409,"body":"And allow pull request ingestion to renew at the end of each loop.\r\n\r\nThis is to prevent PR ingestion from being locked out for 10 minutes if the SCM service is killed for whatever reason.","mergeCommitSha":"0ea445a3616f20430de248b6bd48ba1ab12c70c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1409","title":"Drop lock to 1 minute expiration","createdAt":"2022-05-24T08:30:34Z"}
{"state":"Merged","mergedAt":"2022-01-27T21:55:34Z","number":141,"body":"Add api codegen to `vscode/` directory ","mergeCommitSha":"13e2991aa90ffff1977290cf0dd701aa71adc002","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/141","title":"Add codegen to vscode","createdAt":"2022-01-27T01:22:05Z"}
{"state":"Merged","mergedAt":"2022-05-24T16:38:54Z","number":1410,"body":"- help with debugging time-related issues, like pusher updates\n- also fix this page, which currently fails to load;\n  https://admin.dev.getunblocked.com/teams/f72bb2e8-61d0-4fb2-abb4-b406253e4e22/members/624c613b-876f-4160-8744-04394c976a48/unreads","mergeCommitSha":"18aef2e5d2d6553fc891334fdb4ff342db69df28","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1410","title":"Show absolute time in admin web","createdAt":"2022-05-24T16:22:51Z"}
{"state":"Merged","mergedAt":"2022-05-24T17:55:34Z","number":1413,"body":"Move specific sqs models out of aws library","mergeCommitSha":"cc42c4ca20c3ad7c18a2d476382ff4027780c9cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1413","title":"Clean up stuff","createdAt":"2022-05-24T17:36:32Z"}
{"state":"Merged","mergedAt":"2022-05-24T21:21:23Z","number":1414,"body":"Also adds an isOutdated property to ThreadModel","mergeCommitSha":"f976c5de7e1de443415ae658d91f2bbf2e10f50d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1414","title":"Archive outdated threads for closed PRs","createdAt":"2022-05-24T17:58:34Z"}
{"state":"Merged","mergedAt":"2022-05-24T20:13:37Z","number":1415,"body":"We’ve been using two types of serializers:\r\n1. The standard Json.calls\r\n2. The custom .encode/.decode calls that add support for UUID etc. serialization\r\n\r\nWe’re now standardizing to use the latter and moving away from string conversions to/from UUID which is not a great practice.\r\n\r\nAlso adding custom lint rule to prevent this.","mergeCommitSha":"271c0025066d2da8509f16a8252113cdeef60780","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1415","title":"Cleanup serializer usages","createdAt":"2022-05-24T19:14:14Z"}
{"state":"Merged","mergedAt":"2022-05-24T21:09:07Z","number":1416,"mergeCommitSha":"e86045d745e046add2faf46201709e9ed5eb3e32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1416","title":"Use fat threads in web extension","createdAt":"2022-05-24T20:18:59Z"}
{"state":"Merged","mergedAt":"2022-05-25T00:20:55Z","number":1417,"body":"<img width=\"617\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170123998-895f3a4a-5996-4fdb-8736-5985b3138515.png\">\r\n<img width=\"690\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170124580-bc595b88-858a-4cc6-8136-96fbf4dcff95.png\">\r\n<img width=\"686\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170124601-3829a735-0436-4a4a-814d-8f68a9d7a799.png\">\r\n","mergeCommitSha":"c625209db84ffe3ba0f203d7e943c0c0f62c6c39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1417","title":"[Onboarding] Stepper component","createdAt":"2022-05-24T20:21:01Z"}
{"state":"Merged","mergedAt":"2022-05-25T04:55:21Z","number":1418,"body":"First part of a multi-part change. This change:\r\n- adds repo reference as nullable to thread\r\n- sets the repo reference on thread during API thread creation\r\n- sets the repo reference on thread during PR ingest thread creation\r\n\r\nNext changes:\r\n- add migration to backfill repoID on thread from SourceMark\r\n- make repo reference non-nullable on Thread\r\n- remove unnecessary joins on SourceMarkModel for all thread-related SQL queries\r\n- remove migration\r\n\r\nMotivation\r\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1652290573953709","mergeCommitSha":"3152714a0da3030ee983cc8cf8079171e739b750","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1418","title":"Add Repo reference to Thread","createdAt":"2022-05-24T20:29:38Z"}
{"state":"Merged","mergedAt":"2022-05-24T21:09:02Z","number":1419,"body":"This is temp while I get things working under a docker environment","mergeCommitSha":"d2ef5a2c0455388c1478ce7bdbed544bd0396f0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1419","title":"I need to trigger a whole bunch of test builds with workflow changes. ","createdAt":"2022-05-24T20:51:38Z"}
{"state":"Merged","mergedAt":"2022-01-27T02:25:45Z","number":142,"body":"This pr fixes all the high severity issues with our openspec api and actually caught a bug!\r\nWe will eventually fix the lower priority ones as well.\r\n\r\nIt also does two things:\r\n1. Makes sure the task is always run (regardless of whether gradle input has not changed)\r\n2. Make sure compile task is dependent on lint task.","mergeCommitSha":"fa6b9b0a3d095fd9b83e7671e92e68ae10488380","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/142","title":"Fix up openapi","createdAt":"2022-01-27T01:55:30Z"}
{"state":"Merged","mergedAt":"2022-05-24T21:48:30Z","number":1420,"mergeCommitSha":"8aa4c315e173858984fae2dcf66a66710e32fb37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1420","title":"cleanup","createdAt":"2022-05-24T20:58:02Z"}
{"state":"Merged","mergedAt":"2022-05-25T05:25:48Z","number":1421,"body":"Ran it locally, and worked well.","mergeCommitSha":"2ba8119775e06abbb25dc8bbdd7c92e971e78700","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1421","title":"Migration to backfill repoID on thread from SourceMark","createdAt":"2022-05-24T20:58:19Z"}
{"state":"Merged","mergedAt":"2022-05-25T05:17:50Z","number":1422,"body":"First pass at creating an endpoint for the clients to call during onboarding. Next PR will take a look at the team member calling this endpoint and trigger ingestion for their PRs first.\r\n\r\nThis endpoint is safe to retry.","mergeCommitSha":"85dacc3e465b02b8d02bce8831b2d7d832731449","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1422","title":"Create API endpoint for triggering PR ingestion at onboarding","createdAt":"2022-05-24T21:18:42Z"}
{"state":"Closed","mergedAt":null,"number":1423,"body":"https://linear.app/unblocked/issue/UNB-171/recommendation-system-takes-approvals-into-account","mergeCommitSha":"057c39ac1e9337da3493ca80bdc518dc8675627c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1423","title":"GitHub PR reviews API","createdAt":"2022-05-24T21:46:22Z"}
{"state":"Merged","mergedAt":"2022-05-25T01:36:33Z","number":1424,"mergeCommitSha":"98cead11a7d27b0e45a62a69a93893a9aa9feeb1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1424","title":"Add centralized utility for generating dashboard urls","createdAt":"2022-05-24T22:16:16Z"}
{"state":"Merged","mergedAt":"2022-05-24T23:52:29Z","number":1425,"body":"We have a combination of plural vs singular on dashboard endpoints.\r\nWe should choose one or the other.\r\nSingular for dashboard.\r\n\r\nStrangely, we've chosen plural for apiservice. :)","mergeCommitSha":"12c99bd35e8ef4ea53f9eca27e7df5bcf964c2f2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1425","title":"Singularize endpoint","createdAt":"2022-05-24T22:59:23Z"}
{"state":"Closed","mergedAt":null,"number":1426,"body":"This will be used during onboarding where we want to ingest the onboarding team member's PRs first. \r\n\r\nNote that we need to use the user-to-server token for this request because the team token doesn't work unless we grant read-only access to `contents` to our GitHub app:\r\n\r\nhttps://stackoverflow.com/questions/68280756/validation-failed-error-on-searching-in-private-repository-with-installation-o\r\n\r\nI don't think we want to do that because if we do I think we'll get access to source code:\r\n\r\nhttps://docs.github.com/en/rest/overview/permissions-required-for-github-apps#permission-on-contents","mergeCommitSha":"244603b532a410aad667a638631ef5e0d472417f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1426","title":"Add search pull requests","createdAt":"2022-05-24T23:12:24Z"}
{"state":"Merged","mergedAt":"2022-05-25T16:30:03Z","number":1427,"body":"Add the following headers to all TS client requests:\r\n* `X-Unblocked-Client-Version`: the client version (right now, the commit SHA)\r\n* `X-Unblocked-Client-Agent`: the client app (`hub`, `web-extension`, `web-dashboard`, `vscode`)\r\n\r\nThis was a bit more involved then I thought it would be:\r\n* For some reason all of the runtime Environment code and types were in `/sharedConfigs`, which makes no sense.  I moved them into `/shared/config` and mapped `@config` to that folder.\r\n* Added DefinePlugin definitions for each app to define app type and commit SHA.\r\n* Removed `mergeWithCustomize` usage in VSCode as it seemed to break DefinePlugin's behaviour\r\n* Add hooks into BaseAPI, adding a middleware step that adds the headers from the build config","mergeCommitSha":"4432cd05b64a8244751d6cffa89a4cfe896b6dfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1427","title":"Add client version and agent headers","createdAt":"2022-05-24T23:30:40Z"}
{"state":"Merged","mergedAt":"2022-05-25T03:41:17Z","number":1428,"body":"AppKit and personal pride are juxtaposed to create a solution worthy of Apple's greatest hits.\r\n\r\nThere are two menus in the app: \r\n\r\n## 1. The main app menu\r\n<img width=\"526\" alt=\"CleanShot 2022-05-24 at 16 52 55@2x\" src=\"https://user-images.githubusercontent.com/858772/170150463-44aaf04b-25c8-4680-a741-3a1f7ff1aa1d.png\">\r\n\r\nThis is a pretty gross hack. It has to be rendered as a popover because a regular NSMenu is totally broken within a popover. Additionally it's not possible to get a very custom feel using SwiftUI and NSMenu together without a ton of extra work. But it works, so meh.\r\n\r\n--\r\n\r\n## 2. The menu bar right-click menu \r\n<img width=\"301\" alt=\"CleanShot 2022-05-24 at 16 53 06@2x\" src=\"https://user-images.githubusercontent.com/858772/170150502-bada7827-ea73-4bb2-ae0a-d5293cebf265.png\">\r\n\r\nMore filth. To get this to work properly we have to detect the right click event on the status bar, then quickly swap in the menu and trigger another click. The power of christ compels you!\r\n\r\n\r\n## Remaining\r\nNone of the items except logout and quit are wired up. We need to add install an install verification check for the VSCode extension, then link out to the dashboard to do the extension detections dance.\r\n\r\nAdditionally, I need to implement the actual settings window:\r\n<img width=\"632\" alt=\"CleanShot 2022-05-24 at 17 12 32@2x\" src=\"https://user-images.githubusercontent.com/858772/170151990-6e665fa6-74b7-496f-be82-909159b4354b.png\">\r\n\r\n","mergeCommitSha":"5caba1b4ef5d17a8465f4ea984340831660e1df2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1428","title":"Implements hub menu display","createdAt":"2022-05-24T23:33:07Z"}
{"state":"Merged","mergedAt":"2022-05-25T03:11:45Z","number":1429,"mergeCommitSha":"a72798d660ff45c4a7ac59c32caf7f24d331d79d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1429","title":"Add welcome email","createdAt":"2022-05-25T02:23:29Z"}
{"state":"Merged","mergedAt":"2022-01-27T04:41:34Z","number":143,"body":"We were using fullNameOverride with the name having gitsha.\r\nYou can't do that, as that will cause the kube controllers to delete and recreate controllers etc.\r\n","mergeCommitSha":"e898e91f07f5a21bffc691bc717165cd67d71119","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/143","title":"Fix up naming of pods which was resulting in network controllers etc. being created on every deployemnt","createdAt":"2022-01-27T03:31:00Z"}
{"state":"Merged","mergedAt":"2022-05-25T04:25:42Z","number":1430,"body":"This is pretty much dead code now.  Use the existing thread listing to show the unread number, instead of loading all the Unread data.","mergeCommitSha":"16cdb3f517fdd8574a484c44dd9bb42ef4fc4a15","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1430","title":"Remove UnreadStore","createdAt":"2022-05-25T03:11:23Z"}
{"state":"Merged","mergedAt":"2022-05-25T06:02:14Z","number":1431,"body":"- make repo reference non-nullable on Thread\n- remove unnecessary joins on SourceMarkModel for all thread-related SQL queries\n- remove migration","mergeCommitSha":"b37d7bc9392a3f891c3a2043bfdbf2722d02e583","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1431","title":"remove unnecessary joins on SourceMarkModel for all thread-related SQL queries","createdAt":"2022-05-25T03:44:21Z"}
{"state":"Merged","mergedAt":"2022-05-25T18:01:25Z","number":1432,"body":"This is the last place that uses the old thread stores for data fetching.  Once this is in I will start cutting out the dead code.","mergeCommitSha":"2aa41bbd4737265c7edb28ed97a03da90084641d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1432","title":"Use fat threads API for web extension source mark renderer","createdAt":"2022-05-25T05:47:04Z"}
{"state":"Merged","mergedAt":"2022-05-25T06:11:24Z","number":1433,"mergeCommitSha":"97fe0c0c777b2bf523f7064c703db0d119185f97","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1433","title":"Fix notification","createdAt":"2022-05-25T05:57:41Z"}
{"state":"Merged","mergedAt":"2022-05-25T06:55:35Z","number":1434,"body":"Fixes this:\r\nhttps://admin.prod.getunblocked.com/teams/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/repos/f8e94478-7548-4000-a625-1a43267e0fc5/threads","mergeCommitSha":"9e1a569a2aa2c3dafafc21eeaf512a706f0064f5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1434","title":"Fix admin web Repo>Threads page","createdAt":"2022-05-25T06:48:40Z"}
{"state":"Merged","mergedAt":"2022-05-25T17:45:43Z","number":1435,"mergeCommitSha":"5acd60b98da5f79078179cecb510fbe8a7b16745","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1435","title":"Fix emails","createdAt":"2022-05-25T14:58:41Z"}
{"state":"Merged","mergedAt":"2022-05-25T21:08:14Z","number":1436,"body":"Add skeleton architecture for the onboarding tutorial steps, with the Import Knowledge steps built out as a proof of concept\r\n* NOTE: This is barely styled without additional functionality, just bare bones architecture to walk through the first few steps -- will go back over and style properly after\r\n\r\n<img width=\"889\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170337259-5d4466a4-5234-48f6-a993-d29014fc0a4f.png\">\r\n<img width=\"857\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170337523-1843a44b-7643-47f2-a44f-eddfbdc3f8e9.png\">\r\n<img width=\"896\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170337289-2019709e-dd17-4537-8a60-1a421aa988eb.png\">\r\n<img width=\"869\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170337311-83fe6460-3378-40f4-9713-1efd694a13f3.png\">\r\n","mergeCommitSha":"a0cc9b45ab7a016a2d8e4a001303c2379989acbb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1436","title":"[Onboarding] Tutorial skeleton and first stage","createdAt":"2022-05-25T18:15:34Z"}
{"state":"Merged","mergedAt":"2022-05-25T18:57:39Z","number":1437,"body":"A wonderful man by the name of @matthewjamesadam added some very helpful headers that we want to include as part of our honeycomb traces!\r\n\r\n<img width=\"362\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/170342603-c9cba523-6329-4650-a17b-720bf24e69eb.png\">\r\n\r\nhttps://ui.honeycomb.io/unblocked/environments/local/trace/3y3JC756kh1?span=be65ea29cc5f70bc","mergeCommitSha":"48b286b463c57e81c507ecce0a0b6ad0f93ce602","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1437","title":"Add call headers to honeycomb","createdAt":"2022-05-25T18:36:35Z"}
{"state":"Merged","mergedAt":"2022-05-25T21:51:34Z","number":1439,"body":"First step towards prioritizing ingesting PRs for the onboarding customer + repo","mergeCommitSha":"f0874b29f2f0908196e4943af3d3859266a24a14","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1439","title":"Split PR ingestion into separate jobs","createdAt":"2022-05-25T18:46:44Z"}
{"state":"Closed","mergedAt":null,"number":144,"mergeCommitSha":"07d16fa5adbb5cded5f7d777849b2dee937410e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/144","title":"Jeff/integrate auth","createdAt":"2022-01-27T03:31:03Z"}
{"state":"Merged","mergedAt":"2022-05-25T20:06:14Z","number":1440,"body":"Remove the old ThreadStore and RepoThreadStore.\r\n\r\nNote that thread mutation operations are still in `ThreadStore.ts`.  We can move them later if we want.","mergeCommitSha":"992e04ca3a1a7c67b02a89a3504ecb1d53daf08d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1440","title":"Remove ThreadStore","createdAt":"2022-05-25T19:01:31Z"}
{"state":"Merged","mergedAt":"2022-05-27T06:38:28Z","number":1441,"body":"Called by the client as early as possible during onboarding so that PR ingestion can begin.","mergeCommitSha":"16b387f1b7dc1bd3218951a8ddfbbf299d803986","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1441","title":"Create API endpoint to allow client to give a hint to the server about which repo to ingest PRs first","createdAt":"2022-05-25T19:22:38Z"}
{"state":"Merged","mergedAt":"2022-05-26T23:00:58Z","number":1442,"body":"Motivation: to support https://github.com/NextChapterSoftware/unblocked/issues/1438.\r\n\r\nAlso remove the includeDeactivated query parameter (aka isCurrentMember); there really is no valid\r\nuse case for excluding deactived team members, and some clients (eg: Hub) were not including these\r\nwhich would cause a bug when rendering past discussions with deactivated team members.","mergeCommitSha":"a63c159712549f0e6b7952f37bbf289717971969","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1442","title":"Add hasAccount to TeamMember API","createdAt":"2022-05-25T19:39:23Z"}
{"state":"Merged","mergedAt":"2022-05-26T21:24:12Z","number":1443,"body":"Conditionally render project selector based on installation state.\r\n\r\n<img width=\"943\" alt=\"CleanShot 2022-05-25 at 13 14 12@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-e84d874d-e2de-47f9-bf97-96d39c435d29.png\">\r\n\r\nPoll for installation data and populate view\r\n\r\n<img width=\"1104\" alt=\"CleanShot 2022-05-26 at 12 02 43@2x\" src=\"https://user-images.githubusercontent.com/1553313/*********-********-edd6-48f4-9d58-a51fad1e698e.png\">\r\n\r\n\r\nFinal styling is still TODO. Should come soon as we consolidate onboarding css.\r\n","mergeCommitSha":"54dce36b92d21de4358892fee5412bdc4ec6a610","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1443","title":"Setup code for Installations Wizard","createdAt":"2022-05-25T20:20:53Z"}
{"state":"Merged","mergedAt":"2022-05-25T23:24:59Z","number":1444,"mergeCommitSha":"beba15de40dd60de906b92667a848d0089a10ff0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1444","title":"Add web urls to API","createdAt":"2022-05-25T21:12:29Z"}
{"state":"Merged","mergedAt":"2022-05-26T18:40:48Z","number":1445,"body":"For VSCode, channel polling will stop for a given workspace if that workspace is not the focused window (ie, you focus another window, or you minimize the workspace window).\r\n\r\nFor the dashboard, channel polling will stop whenever the dashboard tab loses focus, or whenever the browser window loses focus (ie you focus another window, or minimize the browser).\r\n\r\n@jeffrey-ng I don't know how to handle this in the web extension, off the top of my head.  Any suggestions?\r\n\r\nIt's unclear to me if the browser behaviour is actually what we want.  It's pretty common to keep browser windows visible by tiling windows or having them side-by-side, while you work on other things, and if those windows don't update it might be surprising.  If that's the case, we should be able to use the page visibility API instead of the focus API: https://developer.mozilla.org/en-US/docs/Web/API/Page_Visibility_API -- this will tell us if the window is completely invisible (viewing another tab, or the window is minimized).","mergeCommitSha":"144f27f8303c35eed314dd3d2daea6ce803c5be7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1445","title":"Stop channel polling when focus lost in dashboard/VSCode","createdAt":"2022-05-25T21:57:46Z"}
{"state":"Merged","mergedAt":"2022-05-25T23:31:04Z","number":1446,"body":"Fewer GitHub client instances in this case, so maybe it'll help with memory issues. Does clean up the code though.","mergeCommitSha":"80b54d8a982da88df3a4438ba8d36d8d672f86dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1446","title":"Single git hub client for all background jobs","createdAt":"2022-05-25T22:13:25Z"}
{"state":"Merged","mergedAt":"2022-05-26T17:49:08Z","number":1447,"body":"Add `hasSeenTutorial` flag in person model.\r\nAdd `updatePersonTutorial` PUT request.","mergeCommitSha":"15286672871b19ba84bf91d59fbaa4520303b617","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1447","title":"Update Person Spec for Tutorial","createdAt":"2022-05-25T22:18:19Z"}
{"state":"Merged","mergedAt":"2022-05-26T17:24:13Z","number":1448,"mergeCommitSha":"55b2e7230b322829d9e8325bec6e054ea33d4ba3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1448","title":"Record who archived a thread","createdAt":"2022-05-25T22:19:31Z"}
{"state":"Merged","mergedAt":"2022-05-26T02:28:27Z","number":1449,"body":"Clients are now free to pass `before` query parameter to pusher for the `/threads/mine` and `/threads/recommended` channels; in addition to if-modified-since as before.\r\n\r\nDesign here: https://www.notion.so/nextchaptersoftware/Thread-List-Polling-36544355f402456aa784337ff6e94c94\r\n\r\nFuture (hopefully before launch \uD83D\uDE2C), we should structure the poller APIs so that they take require arguments, as the `channel` part of the API is totally unstructured — it's just a string.","mergeCommitSha":"0bc57e27bf06d93cf63f7d4c8ee50c2b7762cc75","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1449","title":"Hook up poller \"before\" cursor to threadBundleStore","createdAt":"2022-05-25T22:46:24Z"}
{"state":"Merged","mergedAt":"2022-01-27T06:19:11Z","number":145,"body":"https://github.com/Chapter2Inc/codeswell/pull/122#discussion_r791938298","mergeCommitSha":"2ada67c6b5a387aaff95990d9b7dc0c30749ef2e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/145","title":"Add title property to chat API model","createdAt":"2022-01-27T04:14:24Z"}
{"state":"Merged","mergedAt":"2022-05-26T03:59:48Z","number":1450,"mergeCommitSha":"2c44eca075de91b9a71a7e2802196a10e56002e2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1450","title":"Hub preferences","createdAt":"2022-05-25T23:35:51Z"}
{"state":"Merged","mergedAt":"2022-05-26T05:27:34Z","number":1451,"mergeCommitSha":"ed27929101cee5972ae716ed3ff6c7bae883048a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1451","title":"Clean up PR ingestion logic","createdAt":"2022-05-25T23:54:32Z"}
{"state":"Merged","mergedAt":"2022-05-26T04:04:22Z","number":1452,"mergeCommitSha":"af524e092f07266f1d061b1060fbb22317a08fc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1452","title":"Use fat thread links","createdAt":"2022-05-25T23:59:05Z"}
{"state":"Merged","mergedAt":"2022-05-26T00:29:59Z","number":1453,"body":"Follow on from https://github.com/NextChapterSoftware/unblocked/pull/1444","mergeCommitSha":"e6cd06aa25c82ba70d85f1abf104953ca82b6204","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1453","title":"Use server urls","createdAt":"2022-05-26T00:05:12Z"}
{"state":"Merged","mergedAt":"2022-05-26T04:06:03Z","number":1454,"mergeCommitSha":"d1322d677bde26cc97d79f2576b2a2aec3f14d06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1454","title":"Make app open less obnoxious","createdAt":"2022-05-26T00:06:29Z"}
{"state":"Merged","mergedAt":"2022-05-26T04:17:48Z","number":1455,"body":"In most cases, we already have the thread data available:\r\n* Sidebar already has fat threads loaded\r\n* Text editor gutter items already have fat threads loaded\r\n\r\nso we can view whatever data we have, while loading and channel polling in the background.  The result is *very* quick thread views.  The first view can still take a few seconds, after that they're all sub-second.\r\n\r\n@jeffrey-ng @kaych for the sidebar I'm doing what we were doing before, and funneling the ThreadInfoAggregate data through the sidebar webview.  This is a pretty sad way of doing this.  LMK if you think this is an immediate problem, otherwise I think we should refactor this some other time.\r\n\r\nNext PR will warm up the sourcemark engine for each repo on startup, which should remove the remaining initial lag.","mergeCommitSha":"7b77244e3752869c319dd9af78ea76d303f55e96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1455","title":"Use pre-loaded thread data when opening discussion views in VSCode","createdAt":"2022-05-26T03:27:43Z"}
{"state":"Merged","mergedAt":"2022-05-26T16:17:26Z","number":1456,"mergeCommitSha":"ec87d76fd4f6b1670a44a36dccd6a5bf1c650e25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1456","title":"Hi Matt :)","createdAt":"2022-05-26T04:46:16Z"}
{"state":"Merged","mergedAt":"2022-05-26T16:28:21Z","number":1457,"body":"PRs ingested during onboarding should use their own queue so that we can prioritize the onboarding customer's PRs over others.\r\n\r\nNeeds the queue to be created first.","mergeCommitSha":"aa54701015d647c9c099437d43c94e50cd8f58b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1457","title":"Pull request ingestion during onboarding uses its own queue","createdAt":"2022-05-26T05:37:39Z"}
{"state":"Merged","mergedAt":"2022-05-26T14:01:20Z","number":1458,"body":"Logic when dealing with **_UN_**-read is head-wrecking. Tests prove it works now.","mergeCommitSha":"8570e5face02558daceeeacdf93dbe70af714a8e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1458","title":"Fix bug where before cursor only returned unread threads","createdAt":"2022-05-26T06:03:22Z"}
{"state":"Merged","mergedAt":"2022-05-26T16:30:11Z","number":1459,"body":"Reverts NextChapterSoftware/unblocked#1365","mergeCommitSha":"5a58a3c64840a2d0393120c667a893735c4f7768","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1459","title":"Revert \"temp fix while I address a bug\"","createdAt":"2022-05-26T16:29:37Z"}
{"state":"Merged","mergedAt":"2022-01-27T04:17:05Z","number":146,"mergeCommitSha":"35960385b51bed27021bdebaf94ba92f1aea9797","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/146","title":"Add rabc rules for deployer","createdAt":"2022-01-27T04:16:27Z"}
{"state":"Merged","mergedAt":"2022-05-26T16:51:29Z","number":1460,"mergeCommitSha":"424f7a3e3167693d9566f4509da75c27775fa9cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1460","title":"Reduce parallelization","createdAt":"2022-05-26T16:51:18Z"}
{"state":"Merged","mergedAt":"2022-05-26T16:54:18Z","number":1461,"body":"This reverts commit 5a58a3c64840a2d0393120c667a893735c4f7768.\r\n","mergeCommitSha":"1cbccc5e0a54c531dfa909ca19e3e07c1f019e76","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1461","title":"Revert \"Revert \"temp fix while I address a bug (#1365)\" (#1459)\"","createdAt":"2022-05-26T16:54:14Z"}
{"state":"Merged","mergedAt":"2022-05-26T18:13:32Z","number":1463,"mergeCommitSha":"d683e83b601e8e2520b179ebc06f06cfa3332841","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1463","title":"Disabling flaky tests","createdAt":"2022-05-26T18:12:57Z"}
{"state":"Merged","mergedAt":"2022-05-26T19:46:14Z","number":1464,"body":"I introduced a bug yesterday where we're not returning the right line range for the commit. Problem is that the call to get commit and line range are separate, when they are really a package. \r\n\r\nIf the PR is merged, then we want the sha of the merge commit plus the final line range (`comment.line`).\r\n\r\nIf the PR is open, then we want the sha of the original commit plus the original line range (`comment.originalLine`).","mergeCommitSha":"a1bf4cc01b2a270c87c5495136945979e91c1132","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1464","title":"Fix logic for getting commit and line range","createdAt":"2022-05-26T18:42:32Z"}
{"state":"Merged","mergedAt":"2022-05-26T19:17:17Z","number":1465,"body":"My bad. Didn't review properly.","mergeCommitSha":"3dadb83104658e4f940695a6444611006a496f44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1465","title":"Schema fix","createdAt":"2022-05-26T18:46:05Z"}
{"state":"Merged","mergedAt":"2022-05-26T20:00:27Z","number":1466,"mergeCommitSha":"8a585a5ebde574bb147f8af6e017fef190e44121","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1466","title":"Change default version for hub builds","createdAt":"2022-05-26T18:59:54Z"}
{"state":"Merged","mergedAt":"2022-05-26T19:53:00Z","number":1467,"mergeCommitSha":"680f9550edfdbdb6a49d5fa109dad4c772580ae9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1467","title":"Lint out delays in tests","createdAt":"2022-05-26T19:00:20Z"}
{"state":"Merged","mergedAt":"2022-05-26T19:23:07Z","number":1468,"mergeCommitSha":"9037f711a46eae06b464fc9fbe02ca54be3e2791","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1468","title":"Fix delay style tests and un-disable","createdAt":"2022-05-26T19:06:02Z"}
{"state":"Merged","mergedAt":"2022-05-26T19:35:15Z","number":1469,"body":"This fixes the permission error in Notification service. Prod deployments should start working after this. ","mergeCommitSha":"ae86cec567eb2c886412856466aaa67c422243fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1469","title":"fix missing permissions for notification service","createdAt":"2022-05-26T19:07:38Z"}
{"state":"Merged","mergedAt":"2022-01-27T04:42:42Z","number":147,"body":"https://github.com/Chapter2Inc/codeswell/pull/122/files#r791938511","mergeCommitSha":"8e83d2c3f0dfbf8ed9ac7fd506550a5d058ea21e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/147","title":"Add body property to message API model","createdAt":"2022-01-27T04:23:44Z"}
{"state":"Merged","mergedAt":"2022-05-26T22:58:54Z","number":1470,"body":"Whenever the repo list changes, tell the SME to process that repo, by referencing the calculator for that repo.  This makes the first view for a thread pretty quick.","mergeCommitSha":"f23e9b3f01514b4d41aabcef9bfe41d04d49add2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1470","title":"Warm up sourcemark engine on startup","createdAt":"2022-05-26T19:08:11Z"}
{"state":"Merged","mergedAt":"2022-05-26T21:26:24Z","number":1471,"mergeCommitSha":"895c7ed79b9e4d04ca9f1820a0f046dd1d1b82af","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1471","title":"Use fat thread cursors","createdAt":"2022-05-26T20:10:43Z"}
{"state":"Merged","mergedAt":"2022-05-27T17:41:38Z","number":1472,"body":"Debugging VSCode against dev (my normal workflow) was getting annoying, because on each run it would connect to the installed hub app (running against prod), and try to use its token against the dev env, which would fail.  So I'd have to log in.\r\n\r\nThis PR adds a per-environment user defaults key for the port, so VSCode will only use tokens for the right env.","mergeCommitSha":"b8d02247b42dc9d60d29a1bed8a83d10f4a0ea47","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1472","title":"Separate IPC port user defaults for each env","createdAt":"2022-05-26T21:41:01Z"}
{"state":"Merged","mergedAt":"2022-05-26T22:26:23Z","number":1473,"mergeCommitSha":"28658fe9e3ff202e6ba7b00f58df02534bbfb814","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1473","title":"Bump marketing version to experiment with actions","createdAt":"2022-05-26T22:12:30Z"}
{"state":"Merged","mergedAt":"2022-05-26T22:42:21Z","number":1474,"body":"As per https://chapter2global.slack.com/archives/C02HEVCCJA3/p1653589342940129?thread_ts=1653502477.712799&cid=C02HEVCCJA3","mergeCommitSha":"73c9d2ce8a30f755e0ddcde1ec314d148ac21e4a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1474","title":"Change coment label","createdAt":"2022-05-26T22:33:57Z"}
{"state":"Merged","mergedAt":"2022-05-27T16:15:47Z","number":1475,"body":"This makes the preferences window stay on top of all other windows until it is closed or minimized","mergeCommitSha":"c287d10a00f0e5fad6ccdc8ae5df996d5cd76940","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1475","title":"Make preferences window floating","createdAt":"2022-05-26T23:07:09Z"}
{"state":"Merged","mergedAt":"2022-05-27T18:53:58Z","number":1476,"body":"<img width=\"945\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170590768-f28e2cd3-3ef3-4433-a7ab-5f88569aa64c.png\">\r\n<img width=\"1078\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170603124-d81fae35-41e4-4964-bbaf-a9974e1a4c7e.png\">\r\n<img width=\"1076\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170590822-15535c75-79ed-4a5f-bdf4-1ebcee9ea151.png\">\r\n<img width=\"1068\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/170590844-d32c8dc8-e669-4c02-aaff-feb8d0a7e9e2.png\">\r\n\r\n* Funnel in data for the first tutorial section (Import Knowledge)\r\n* Is happy path (clicking through the flow using the buttons) opens the first thread in the sidebar)\r\n* Refactor `openFileEditor` functionality out of the DiscussionThreadCommand to a common place to be reused during the tutorial\r\n* Add condition to turn off opening the file editor when opening a discussion thread (we don't want to do this during the tutorial)\r\n* Handle events in the sidebar triggering the next tutorial view\r\n\r\nNOTE:\r\n* The tutorial currently doesn't handle the sourcemark tooltip click (i.e. opening the thread via the gutter tooltip won't trigger the next tutorial view) -- will layer on support for this since implementing this will be a bit more involved. The flow works fine clicking the tutorial button ","mergeCommitSha":"bbf09e7bdc33c475101d6f8d4b64a5c5608084ea","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1476","title":"[Onboarding] Import knowledge steps","createdAt":"2022-05-27T00:04:58Z"}
{"state":"Merged","mergedAt":"2022-05-31T04:28:33Z","number":1477,"body":"Don't create any ThreadUnreads during the initial PR ingestion. After the initial ingestion, when a new message is created for:\r\n\r\nA new thread (no other messages): \r\n- Create a ThreadUnread for the new message author, with lastReadMessage set to the new message\r\n- Create a ThreadUnread for the PR author, with lastReadMessage set to null (if creator != message author)\r\n\r\nAn existing thread (with existing messages)\r\n- Upsert a ThreadUnread for each thread participant, with lastReadMessage set to\r\n  - the new message, for the new message's author\r\n  - the last message in the thread before new message creating, for all other thread participants\r\n\r\nThe goal here is to the have all participants (minus the new message author) get a notification that the thread has a message that is unread.","mergeCommitSha":"f2490336118600b5e1c5bdce5444a437a57d4418","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1477","title":"Create ThreadUnreads for PR ingested messages after the initial ingestion","createdAt":"2022-05-27T00:05:27Z"}
{"state":"Merged","mergedAt":"2022-05-27T01:07:30Z","number":1478,"mergeCommitSha":"196fa5e805ff3a551b0d8ec4233999d3588f2ea7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1478","title":"use on-demand instances to see if builds get better","createdAt":"2022-05-27T00:15:42Z"}
{"state":"Merged","mergedAt":"2022-05-27T19:17:05Z","number":1479,"body":"This is going to involve a few more pull requests, but this is the basic UI for the team invite page.\r\n\r\nWhat this pr does is the following:\r\n1. Add basic Landing page navigation (this is up in the air, I can possibly modify the existing Home Navigation, but it looks quite different)\r\n2. Add TeamInvite page with optional users parameter (for storybooks)\r\n3. Modify UserIconStack component to take in custom maxSize.\r\n\r\nTODO:\r\n1. Figure out how to get actual team members for an unauthorized user. (no idea)\r\n2. Integrate some sort of expiry token to Team Invite page.\r\n3. Make this stuff responsive.\r\n\r\nSTORYBOOK:\r\n<img width=\"1210\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/170604165-520268a7-bfac-4d6c-b322-f84c84238352.png\">\r\n\r\nLIVE WEBPAGE (WITH NAVIGATION BUT NO USERS YET):\r\n<img width=\"1711\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/170604234-37659069-6274-4411-b8f0-9446851a161c.png\">\r\n\r\nFIGMA:\r\n<img width=\"1118\" alt=\"image\" src=\"https://user-images.githubusercontent.com/3806658/170604331-103a7509-f8d8-4eb5-b958-3c1f197a5bd3.png\">","mergeCommitSha":"ee147f1377e44d935deeb0aa898fa0b67399b5e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1479","title":"Add initial team invite page","createdAt":"2022-05-27T00:17:38Z"}
{"state":"Merged","mergedAt":"2022-01-27T06:44:07Z","number":148,"body":"Will wire up `putSourcemark` to the database in a later PR.","mergeCommitSha":"7c62830e3d10e62bb4f3a5d0f3ebff2274d81c51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/148","title":"getSourcemark operation hits database","createdAt":"2022-01-27T05:51:01Z"}
{"state":"Merged","mergedAt":"2022-05-27T19:24:13Z","number":1480,"mergeCommitSha":"93454a945e794bda989c5d1ad08eb1881268aca8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1480","title":"Ad hoc signing for PR builds","createdAt":"2022-05-27T17:34:56Z"}
{"state":"Merged","mergedAt":"2022-05-27T18:56:55Z","number":1481,"body":"* Add abs date to each message timestamp (on hover)\r\n* Update sidebar icons per new designs\r\n* Add missing placeholders to search inputs","mergeCommitSha":"62d35a01824ba474c5cf030f7da6c7ae615eca6d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1481","title":"Add abs date to messages and touch up placeholders and icons","createdAt":"2022-05-27T17:38:57Z"}
{"state":"Merged","mergedAt":"2022-05-27T17:56:00Z","number":1482,"body":"I had scaled API service up during our last incident. It's causing annoying alarms in Dev due to capacity so I am scaling it back to 2. \r\n\r\nI don't want to waste money adding another Kube instance in Dev until we really need it. Right now this is happening because during deployments we 3 active API instances due to the 50% rollover config","mergeCommitSha":"19d7647deb256f6cabdece9a5f52a9fb4ce49e4e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1482","title":"Reverting API service back to 1 instance","createdAt":"2022-05-27T17:39:57Z"}
{"state":"Merged","mergedAt":"2022-05-27T18:36:13Z","number":1483,"body":"Noticed it in local stack.","mergeCommitSha":"dca7e253b50f581f0f5a2b4c46c2fab9b40aede0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1483","title":"Preflight requests were failing because these headers were not being allowed","createdAt":"2022-05-27T18:20:17Z"}
{"state":"Merged","mergedAt":"2022-05-30T16:08:23Z","number":1484,"body":"Updated styling for Project Selector & org installation.\r\n<img width=\"852\" alt=\"CleanShot 2022-05-27 at 11 26 31@2x\" src=\"https://user-images.githubusercontent.com/1553313/170769509-a1c77e91-7358-43ee-aab4-737f67e363c4.png\">\r\n\r\n<img width=\"888\" alt=\"CleanShot 2022-05-27 at 12 16 53@2x\" src=\"https://user-images.githubusercontent.com/1553313/170776464-ff5c72f9-adbc-4896-be85-fe236d9ee868.png\">\r\n\r\nOn ProjectSelection, new workspace should automatically bring up org installation flow on launch. There could be some delay as we wait for the extension to initialize...\r\n\r\nAt the end of installation, we will conditionally start tutorial (currently hard coded to always start tutorial)\r\n\r\n","mergeCommitSha":"ef99428fb7fe451b42bff74319d2243e65ee3791","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1484","title":"Installation styles and functionality","createdAt":"2022-05-27T18:26:47Z"}
{"state":"Merged","mergedAt":"2022-05-27T19:42:46Z","number":1485,"body":"Kay's suggesiton. :)","mergeCommitSha":"69e9342b91bad47a22ecee69359ffa96ffb56905","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1485","title":"Did not know about typescript predicates","createdAt":"2022-05-27T19:26:44Z"}
{"state":"Merged","mergedAt":"2022-05-27T20:21:28Z","number":1486,"mergeCommitSha":"93c0d0b8a10987e8087148af0634ffad547a1733","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1486","title":"Set marketing version","createdAt":"2022-05-27T19:56:15Z"}
{"state":"Merged","mergedAt":"2022-05-27T21:24:46Z","number":1487,"mergeCommitSha":"852d08e189c344e7663d17374b6bb54887c69fef","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1487","title":"Admin web can clear busy lock","createdAt":"2022-05-27T20:11:32Z"}
{"state":"Merged","mergedAt":"2022-06-03T20:37:36Z","number":1488,"body":"Add a new class that acts as a proxy for WebviewPanels.  This is used when panels are shared and reused (by using WebviewPanelTracker).\r\n\r\nThe key problem is that when you use WebviewPanelTracker, multiple clients can end up with access to a WebviewPanel.  They may all call methods on the panel and be in conflict.\r\n\r\nI noticed this was a problem because I noticed that when you viewed a number of threads in a row, without closing the discussion thread UI, we would continue to poll for *all* the threads that you had ever viewed in that editor.  Only once the editor was closed, would polling for those threads stop.  The problem is that every DiscussionThreadCommand invocation was handling `WebviewPanel.onDidDispose`, and so would only be notified once the editor was finally closed.","mergeCommitSha":"cf14ca83f5dc30822e08be7d13a0dfa955eb255e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1488","title":"Add WebviewPanelProxy","createdAt":"2022-05-27T20:15:41Z"}
{"state":"Merged","mergedAt":"2022-05-28T00:08:40Z","number":1489,"body":"Adding icon to start of footer will come in a later PR (requires figuring out where to host the icon).","mergeCommitSha":"b8d628a9f6c748c5db2bf1d81f4f7bf21878c474","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1489","title":"Add link to comment footer","createdAt":"2022-05-27T20:15:50Z"}
{"state":"Merged","mergedAt":"2022-01-27T07:58:33Z","number":149,"body":"Addresses comments in https://github.com/Chapter2Inc/codeswell/pull/140","mergeCommitSha":"ebe8e1d93949f12938283ea50bd8fbe6beabfa83","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/149","title":"Fix status codes in putSourcemark","createdAt":"2022-01-27T06:37:16Z"}
{"state":"Merged","mergedAt":"2022-05-27T21:46:37Z","number":1490,"body":"https://github.com/NextChapterSoftware/ec2-action-builder/pull/2\r\n\r\nThis is to address InsufficientCapacity errors thrown by AWS when using Spot instances. I will not merge the PR above until we are confident these new changes are stable.","mergeCommitSha":"693f47abad490352755e1861e2be12427a8ca7bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1490","title":"change ec2 runner to a new version with fallback support","createdAt":"2022-05-27T20:58:32Z"}
{"state":"Merged","mergedAt":"2022-05-27T21:45:15Z","number":1491,"body":"Not sure how the last one merged. Must have set to auto :) ","mergeCommitSha":"d1f05b223581416982235329f7333ef1053383ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1491","title":"Set marketing version","createdAt":"2022-05-27T21:23:24Z"}
{"state":"Merged","mergedAt":"2022-05-27T22:06:36Z","number":1492,"body":"This disables IP filters on Dashboard in prod and dev. I am keeping the WAF because we will add DDoS protection rules to it soon. ","mergeCommitSha":"87f8c0a605631875367f275374a45052d760190e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1492","title":"disable ip filter on dashboard","createdAt":"2022-05-27T21:57:24Z"}
{"state":"Merged","mergedAt":"2022-05-31T14:56:26Z","number":1493,"body":"This pr segregates web builds into two bundles:\r\n1. Dashboard\r\n2. Landing \r\n\r\nThis pr also deploys landing page separately from dashboard.\r\nBasically, this has set up the framework by which we can split landing page into its own package.\r\n\r\nTESTED:\r\nhttps://dev.getunblocked.com/team/blah/invite\r\nhttps://dev.getunblocked.com","mergeCommitSha":"90334ae4ff0b8b4102d99b6ca4d42bffb42a48e3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1493","title":"Landing Page Deployment","createdAt":"2022-05-27T21:59:13Z"}
{"state":"Merged","mergedAt":"2022-05-27T23:37:30Z","number":1494,"body":"When VSCode plugin initializes, it reaches out to the hub app and check if onboarding should occur.\r\n\r\nGeneric getConfiguration API to allow for changes in future","mergeCommitSha":"a71edbd2520cc20a5b6f5282ea8dca131408fa40","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1494","title":"Setup configuration Hub API","createdAt":"2022-05-27T22:00:02Z"}
{"state":"Merged","mergedAt":"2022-05-27T22:49:05Z","number":1495,"mergeCommitSha":"bd12ff119d6ab307a12540a276b2d2f5b3eb3ad5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1495","title":"Fix workflow notifications","createdAt":"2022-05-27T22:02:06Z"}
{"state":"Merged","mergedAt":"2022-05-27T22:44:59Z","number":1496,"body":"I have deployed this change from my machine. Deploybot user doesn't have access to management namespace by design to prevent it from altering its own permissions. ","mergeCommitSha":"84678feb1de223faf85dfb0e5ce70691a78aec0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1496","title":"add landing page deployment permissions to deploybot","createdAt":"2022-05-27T22:34:31Z"}
{"state":"Merged","mergedAt":"2022-05-27T23:25:32Z","number":1497,"mergeCommitSha":"7c7876b85b1de5f62037e6ea8f68b7bacf33177b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1497","title":"Adds swift GRPC configuration endpoint","createdAt":"2022-05-27T23:18:02Z"}
{"state":"Merged","mergedAt":"2022-05-28T00:12:24Z","number":1498,"body":"Use enum instead of boolean","mergeCommitSha":"dfdf9043504cf4c72aac3cf6e820f05d4d9e8b00","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1498","title":"Update action instead of bool","createdAt":"2022-05-27T23:59:24Z"}
{"state":"Merged","mergedAt":"2022-05-30T21:33:11Z","number":1499,"mergeCommitSha":"2e701d7fabad6a19226ec2e213df858bf0baf54f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/1499","title":"[RFC] Links proposal for team and threads","createdAt":"2022-05-28T00:18:23Z"}
{"state":"Merged","mergedAt":"2021-12-20T21:55:29Z","number":15,"mergeCommitSha":"86f142318894fbb324c175586784a25482ff219c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/15","title":"Introduce Side Navigator and Frame","createdAt":"2021-12-17T19:45:26Z"}