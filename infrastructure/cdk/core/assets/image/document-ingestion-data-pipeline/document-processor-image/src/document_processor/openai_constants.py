import os
from aws_utils.aws_secrets_utils import get_aws_secret


def get_secret_with_fallback(secret_name, env_var_name):
    try:
        return get_aws_secret(
            secret_name=secret_name, default=os.environ.get(env_var_name)
        )
    except Exception:
        # Fall back to environment variable if AWS call fails for any reason
        return os.environ.get(env_var_name)


OPENAI_API_KEY = get_secret_with_fallback(
    secret_name="openai-unblocked-token", env_var_name="OPENAI_API_KEY"
)
