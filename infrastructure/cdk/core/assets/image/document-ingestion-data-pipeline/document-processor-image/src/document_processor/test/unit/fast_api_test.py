import pytest
from fastapi.testclient import TestClient
from document_processor.fast_api import app
import os
import boto3
import uuid
from botocore.exceptions import ClientError
import logging
import sys

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


@pytest.fixture(scope="session", autouse=True)
def setup_aws_credentials():
    logger.info("Setting up AWS credentials")

    secrets_path = os.path.expanduser("~/.secrets/unblocked/local/secrets.properties")
    if os.path.exists(secrets_path):
        _load_aws_credentials(secrets_path)
        logger.info(f"Loaded AWS credentials from {secrets_path}")
    else:
        logger.warning(f"Secrets file not found: {secrets_path}")

    region = os.environ.get("AWS_DEFAULT_REGION", "us-west-2")
    os.environ["AWS_DEFAULT_REGION"] = region
    logger.info(f"AWS region set to {region}")

    try:
        logger.info("Checking AWS identity...")
        sts_client = boto3.client("sts")
        identity = sts_client.get_caller_identity()
        logger.info(f"Using AWS identity: {identity['Arn']}")
    except Exception as e:
        logger.error(f"Could not fetch AWS identity: {str(e)}")

    sys.stdout.flush()


def _load_aws_credentials(file_path):
    logger.info("Loading AWS credentials from properties file")
    with open(file_path, "r") as f:
        for line in f:
            if "=" in line and not line.startswith("#"):
                key, value = line.strip().split("=", 1)
                if key.startswith("aws."):
                    env_key = key.replace("aws.", "AWS_").upper()
                    os.environ[env_key] = value
                    logger.info(f"Set environment variable {env_key}")


@pytest.fixture(scope="session")
def bucket_name():
    return os.environ.get(
        "TEST_S3_BUCKET_NAME", "ncs-unblocked-document-processor-test-files"
    )


@pytest.fixture(autouse=True)
def fastapi_client():
    # Ensures startup/shutdown run for every test needing the client
    with TestClient(app) as c:
        yield c


@pytest.fixture(autouse=True)
def ensure_openai_env():
    os.environ.setdefault("OPENAI_API_KEY", "test-key")
    yield


class TestDocumentProcessorFastAPI:
    @pytest.fixture(autouse=True)
    def setup_method(self, fastapi_client, bucket_name):
        self.test_client = fastapi_client
        self.fixtures_dir = os.path.join(os.path.dirname(__file__), "fixtures")
        self.bucket_name = bucket_name

        logger.info(f"Using S3 bucket: {self.bucket_name}")
        try:
            self.s3_client = boto3.client("s3")
            # Test if we can list the bucket to confirm permissions
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"Successfully connected to bucket {self.bucket_name}")
            try:
                loc = self.s3_client.get_bucket_location(Bucket=self.bucket_name).get(
                    "LocationConstraint"
                )
                logger.info(
                    f"Bucket location: {loc or 'us-east-1 (implicit)'} | Client region: {self.s3_client.meta.region_name}"
                )
            except Exception as e:
                logger.warning(f"Could not fetch bucket location: {e}")
        except ClientError as e:
            logger.error(f"Error connecting to S3 bucket: {str(e)}")
            logger.error(
                f"AWS credentials: ACCESS_KEY_ID={os.environ.get('AWS_ACCESS_KEY_ID', 'not set')[:4]}..."
            )
            raise

        self.uploaded_files = []

        yield

        if self.uploaded_files:
            logger.info(f"Cleaning up {len(self.uploaded_files)} files from S3")
            for key in self.uploaded_files:
                try:
                    self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
                    logger.info(f"Deleted {key} from S3")
                except ClientError as e:
                    logger.error(f"Error deleting {key}: {e}")

    def upload_fixture(self, filename):
        file_key = f"test-fixtures/{uuid.uuid4()}-{filename}"
        file_path = os.path.join(self.fixtures_dir, filename)

        if not os.path.exists(file_path):
            logger.error(f"Fixture file missing: {file_path}")
            raise FileNotFoundError(file_path)

        size = os.path.getsize(file_path)
        logger.info(
            f"Preparing upload: local_path={file_path} size_bytes={size} s3://{self.bucket_name}/{file_key}"
        )

        try:
            with open(file_path, "rb") as f:
                self.s3_client.upload_fileobj(f, self.bucket_name, file_key)
            logger.info(f"Upload completed: {file_key}")
            # Verify object exists & log metadata
            try:
                head = self.s3_client.head_object(Bucket=self.bucket_name, Key=file_key)
                logger.info(
                    f"Verified S3 object: key={file_key} etag={head.get('ETag')} size={head.get('ContentLength')} "
                    f"last_modified={head.get('LastModified')}"
                )
            except ClientError as he:
                logger.error(f"Post-upload head_object failed for {file_key}: {he}")
        except ClientError as ce:
            logger.error(f"S3 upload failed for {file_key}: {ce}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during upload for {file_key}: {e}")
            raise

        self.uploaded_files.append(file_key)
        return file_key

    def generate_signed_url(self, file_key):
        logger.debug(f"Requesting presigned URL for key={file_key}")
        signed_url = self.s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": self.bucket_name, "Key": file_key},
            ExpiresIn=120,
        )
        logger.info(f"Generated presigned URL (expires 120s) for key={file_key}")
        return signed_url

    def map_document(self, url, filename):
        logger.info(
            f"Invoking /invocations for filename={filename} ext={os.path.splitext(filename)[1]} url_trunc={url[:60]}"
        )
        response = self.test_client.post(
            "/invocations", json={"url": url, "name": filename}
        )
        logger.info(
            f"/invocations response status={response.status_code} length={len(response.text or '')}"
        )
        if response.status_code != 200:
            logger.error(
                f"Invocation failed: status={response.status_code} body={response.text[:500]}"
            )
        return response

    def test_convert_md(self):
        filename = "example.md"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_txt(self):
        filename = "example.txt"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_log(self):
        filename = "example.txt"
        filealias = (
            "example.log"  # Cannot do a .log fixture, so use .txt but alias as .log'
        )
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filealias)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_csv(self):
        filename = "example.csv"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_pdf(self):
        filename = "example.pdf"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_docx(self):
        filename = "example.docx"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_xlsx(self):
        filename = "example.xlsx"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_pptx(self):
        filename = "example.pptx"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_html(self):
        filename = "example.html"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        assert response.text.strip(), "The response should not be empty."

    def test_convert_jpg(self):
        filename = "example.jpg"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        result = response.json()
        assert isinstance(result, str)
        assert len(result.strip()) > 0
        assert "error" not in result.lower()
        assert "failed" not in result.lower()

    def test_convert_png(self):
        filename = "example.png"
        file_key = self.upload_fixture(filename)
        signed_url = self.generate_signed_url(file_key)

        response = self.map_document(signed_url, filename)

        assert response.status_code == 200
        result = response.json()
        assert isinstance(result, str)
        assert len(result.strip()) > 0
        assert "error" not in result.lower()

    def test_unsupported_file_extension(self):
        filename = "example.xyz"
        response = self.test_client.post(
            "/invocations",
            json={"url": "https://example.com/test.xyz", "name": filename},
        )

        assert response.status_code == 400
        assert "Unsupported file extension" in response.json()["detail"]
        assert ".xyz" in response.json()["detail"]

    def test_ping_endpoint(self):
        response = self.test_client.get("/ping")
        assert response.status_code == 200
        assert response.json() == {"message": "ok"}

    if __name__ == "__main__":
        import pytest, sys

        sys.exit(pytest.main([__file__]))
