version: 1
disable_existing_loggers: False

formatters:
  default:
    "()": uvicorn.logging.DefaultFormatter
    fmt: "%(asctime)s | worker=%(process)d | %(levelprefix)s %(name)s - %(message)s"
    use_colors: null
  access:
    "()": uvicorn.logging.AccessFormatter
    fmt: "%(asctime)s | worker=%(process)d | %(levelprefix)s %(client_addr)s - \"%(request_line)s\" %(status_code)s"
    use_colors: null

handlers:
  default:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stderr
  access:
    class: logging.StreamHandler
    formatter: access
    stream: ext://sys.stdout

loggers:
  uvicorn.error:
    level: INFO
    handlers: [default]
    propagate: no
  uvicorn.access:
    level: INFO
    handlers: [access]
    propagate: no

root:
  level: WARNING
  handlers: [default]
