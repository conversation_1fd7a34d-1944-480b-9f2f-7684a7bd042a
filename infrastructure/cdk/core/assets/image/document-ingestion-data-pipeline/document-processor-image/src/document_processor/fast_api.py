from fastapi import FastAPI, HTTPException
from markitdown import MarkItD<PERSON>
from io import BytesIO
import uvicorn
import os
import httpx
from typing import Binary<PERSON>
from pydantic import BaseModel
import time
from httpx import HTTPStatusError
import logging
import asyncio
from urllib.parse import urlparse
from contextlib import asynccontextmanager
from pathlib import Path

from document_processor.llm_constants import DEFAULT_LLM_ENDPOINT_URL
from document_processor.openai_constants import OPENAI_API_KEY  # optional convenience

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

IMAGE_EXTENSIONS = {".png", ".jpg", ".jpeg"}
DOCUMENT_EXTENSIONS = {".docx", ".xlsx", ".html", ".htm", ".pdf", ".csv", ".pptx"}
TEXT_EXTENSIONS = {".txt", ".log", ".md"}


class DocumentMappingRequest(BaseModel):
    url: str
    name: str | None = None


def _filename_from_url(url: str) -> str:
    base = os.path.basename(urlparse(url).path)
    return base or "document"


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Per-worker resources (created after fork/spawn)
    app.state.markitdown = MarkItDown()
    # app.state.mkd_lock = asyncio.Lock()  # <-- intentionally commented out
    app.state.doc_client = httpx.AsyncClient(
        timeout=httpx.Timeout(60, connect=10), http2=True
    )
    app.state.img_client = httpx.AsyncClient(
        timeout=httpx.Timeout(120, connect=10), http2=True
    )
    logger.info(f"🚀 Worker started. PID={os.getpid()}")
    try:
        yield
    finally:
        await app.state.doc_client.aclose()
        await app.state.img_client.aclose()
        logger.info(f"💥 Worker stopped. PID={os.getpid()}")


app = FastAPI(lifespan=lifespan)


@app.post("/")
@app.post("/invocations")
async def map(request: DocumentMappingRequest) -> str:
    start = time.perf_counter()
    name = request.name or _filename_from_url(request.url)
    url = request.url

    try:
        _, ext = os.path.splitext(name)
        ext = ext.lower().strip()

        if ext in IMAGE_EXTENSIONS:
            result = await convert_image(url=url, name=name)
        elif ext in DOCUMENT_EXTENSIONS:
            result = await convert_document(url=url, ext=ext)
        elif ext in TEXT_EXTENSIONS:
            result = await convert_text(url=url)

        else:
            supported = ", ".join(sorted(DOCUMENT_EXTENSIONS | IMAGE_EXTENSIONS | TEXT_EXTENSIONS))
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file extension: {ext or '(none)'}."
                f" Supported extensions are: {supported}",
            )

        duration = time.perf_counter() - start
        logger.info(f"✅ mapping {name} in {duration:.2f}s (PID={os.getpid()})")
        return result

    except HTTPException:
        duration = time.perf_counter() - start
        logger.warning(f"❌ mapping {name} in {duration:.2f}s (PID={os.getpid()})")
        raise
    except Exception as e:
        duration = time.perf_counter() - start
        logger.warning(f"❌ mapping {name} in {duration:.2f}s (PID={os.getpid()})")
        raise HTTPException(status_code=500, detail=str(e))


async def convert_document(url: str, ext: str) -> str:
    response = await app.state.doc_client.get(url)
    response.raise_for_status()
    stream: BinaryIO = BytesIO(response.content)

    loop = asyncio.get_running_loop()
    # async with app.state.mkd_lock:  # <-- re-enable if needed
    result = await loop.run_in_executor(
        None,
        lambda: app.state.markitdown.convert_stream(stream=stream, file_extension=ext),
    )
    if result.text_content is None:
        raise HTTPException(
            status_code=500,
            detail="No text content",
        )
    if result.text_content.strip() == "This is not a valid Office Open XML file.":
        raise HTTPException(
            status_code=422,
            detail="This is not a valid Office Open XML file.",
        )
    return result.text_content


async def convert_text(url: str) -> str:
    response = await app.state.doc_client.get(url)
    response.raise_for_status()
    stream: BinaryIO = BytesIO(response.content)
    text_content = stream.read().decode("utf-8", errors="ignore")
    return text_content


async def convert_image(url: str, name: str) -> str:
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        raise RuntimeError("Missing OPENAI_API_KEY in environment variables.")

    prompt_text = (
        f"Describe this image in plain Markdown format for use in text search. "
        f"If the image includes a diagram, provide a mermaid description in plain text. "
        f"Do not use triple backtick markers except when including a mermaid diagram. "
        f"If there is no diagram, omit the '## Diagram:' section entirely. "
        f"Use the following format:\n"
        f"# {name} Image\n"
        f"## Description:\n<detailed description here>\n\n"
        f"## Diagram:\n```mermaid\n<mermaid diagram here>\n```"
    )

    payload = {
        "model": "gpt-4o",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt_text},
                    {"type": "image_url", "image_url": {"url": url}},
                ],
            }
        ],
        "stream": False,
        "max_tokens": 1024,
    }

    headers = {
        "Authorization": f"Bearer {openai_api_key}",
        "Content-Type": "application/json",
    }

    try:
        response = await app.state.img_client.post(
            DEFAULT_LLM_ENDPOINT_URL, json=payload, headers=headers
        )
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]
    except HTTPStatusError as e:
        logger.warning(
            f"OpenAI request failed: {e.response.status_code} - {e.response.text}"
        )
        raise HTTPException(
            status_code=500,
            detail=f"OpenAI request failed: {e.response.status_code} - {e.response.text}",
        )


@app.get("/ping")
async def ping() -> dict[str, str]:
    return {"message": "ok"}


def _available_cores() -> int:
    """Count CPU cores available to this process (affinity-aware on Linux)."""
    try:
        return len(os.sched_getaffinity(0))  # type: ignore[attr-defined]
    except Exception:
        return os.cpu_count() or 1


def _compute_workers() -> int:
    """Workers default to available cores; allow env override but clamp to max."""
    max_workers = min(_available_cores(), 1)
    override = os.getenv("WEB_CONCURRENCY") or os.getenv("UVICORN_WORKERS")
    if override:
        try:
            requested = int(override)
        except ValueError:
            requested = max_workers
        return max(1, min(requested, max_workers))
    return max_workers


def _resolve_app_import(env_var: str = "APP_MODULE", app_attr: str = "app") -> str:
    """
    Return an import string for uvicorn, e.g. 'document_processor.fast_api:app'.
    Priority:
      1) ENV APP_MODULE (e.g., 'document_processor.fast_api')
      2) Derived from __package__ and this file name
    """
    mod = os.getenv(env_var)
    if not mod:
        stem = Path(__file__).stem  # e.g., 'fast_api'
        mod = f"{__package__}.{stem}" if __package__ else stem
    return f"{mod}:{app_attr}"


if __name__ == "__main__":
    # Convenience: export OPENAI_API_KEY for child workers if provided via constant.
    if OPENAI_API_KEY and not os.getenv("OPENAI_API_KEY"):
        os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY

    port = int(os.getenv("PORT", "8080"))
    current_dir = os.path.dirname(os.path.abspath(__file__))
    log_config_path = f"{current_dir}/log_conf.yaml"

    workers = _compute_workers()
    app_import = _resolve_app_import()

    logger.warning(
        f"Starting uvicorn app='{app_import}' with workers={workers} "
        f"(max={_available_cores()}), PID={os.getpid()}"
    )

    # NOTE: pass the import string (not the app object) so workers are enabled.
    uvicorn.run(
        app_import,
        host="0.0.0.0",
        port=port,
        log_level="warning",
        workers=workers,
        log_config=log_config_path,
    )
