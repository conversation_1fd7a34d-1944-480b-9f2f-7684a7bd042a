check: format test

run:
	PORT=8091 poetry run python ./src/document_processor/fast_api.py

format:
	poetry run black src

test:
	PYTHONPATH=./src poetry run pytest ./src/document_processor/test/unit -s -v --log-cli-level=INFO

docker-build:
	$(eval JFROG_PASSWORD := $(shell security find-generic-password -a "<EMAIL>" -l "poetry-repository-jfrog-server" -w 2>/dev/null))
	@JFROG_PASSWORD=$(JFROG_PASSWORD) docker compose build

docker-run:
	OPENAI_API_KEY=$$OPENAI_API_KEY docker compose up

.PHONY: check format test
