[tool.poetry]
name = "document-processor"
version = "0.1.0"
description = "Maps binary documents + images to Markdown"
authors = ["cancelself <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
fastapi = "^0"
httpx = { version = "^0", extras = ["http2"] }
markitdown = {extras = ["all"], version = "^0"}
python = ">=3.10.12,<3.12"
python-multipart = "^0"
uvicorn = "^0"
unblocked-aws-utils = "^0"

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"
fastapi = "^0"
unblocked-aws-utils = "^0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.packages]]
from = 'src'
include = 'document_processor'

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
