import concurrent
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Optional

import pandas as pd
from embedding_utils.embedding_generator import EmbeddingGenerator
from integration_utils.message import MessageInterface
from pandas import DataFrame
from pinecone import QueryResponse
from pinecone_utils.pinecone_client import PineconeClient
from text_utils.compression import Compression
from tqdm import tqdm

from semantic_match_processor_image.dataframe_constants import REPO_ID_COLUMN, FILE_CONTENT_COLUMN, FILE_PATH_COLUMN, \
    FILE_NAME_COLUMN, START_LINE_COLUMN, END_LINE_COLUMN, QA_MATCHES_COLUMN, PR_MATCHES_COLUMN, SLACK_MATCHES_COLUMN, \
    DOC_MATCHES_COLUMN, ALPHA_COLUMN, SCORE_CUTOFF_COLUMN, DOC_ID_COLUMN, ISSUE_MATCHES_COLUMN, \
    FILTERED_PR_MATCHES_COLUMN, FILTERED_SLACK_MATCHES_COLUMN, FILTERED_ISSUE_MATCHES_COLUMN, \
    FILTER_LENGTH_CUTOFF_COLUMN
from semantic_match_processor_image.pinecone_enum_types import DocumentType, InsightType
from semantic_match_processor_image.source_code_provider import SourceCodeMessage


class SemanticMatchProcessor:
    __min_size: int
    __max_size: int

    def process_semantic_match(self, messages: [MessageInterface]) -> DataFrame:
        pass


class PineconeSemanticMatchProcessor(SemanticMatchProcessor):
    __embedder: EmbeddingGenerator
    __pinecone_client: PineconeClient
    __repo_id: str

    def __init__(self,
                 repo_id: str,
                 embedder: EmbeddingGenerator,
                 pinecone_client: PineconeClient
                 ):
        self.__embedder = embedder
        self.__pinecone_client = pinecone_client
        self.__repo_id = repo_id

    def process_message(
            self,
            messages: [SourceCodeMessage],
            alpha: float = 0.8,
            top_k: int = 15,
            score_cutoff: float = 0.7,
            filter_length_cutoff: int = 1024,
    ) -> dict:
        qa_query_matches = []
        pr_query_matches = []
        issue_query_matches = []
        slack_query_matches = []
        doc_query_matches = []

        filtered_pr_query_matches = []
        filtered_issue_query_matches = []
        filtered_slack_query_matches = []

        qa_query_filter = {
            "$and": [
                {"type": DocumentType.Thread.value},
                {"insightType": InsightType.Answer.value}
            ]
        }

        pr_query_filter = {
            "$and": [
                {"group": {"$in": [self.__repo_id]}},
                {"type": {"$in": [DocumentType.PullRequest.value, DocumentType.Thread.value]}},
                {"insightType": {
                    "$in": [InsightType.PullRequest.value,
                            InsightType.PullRequestComment.value]}}
            ]
        }

        issue_query_filter = {
            "$and": [
                {"type": DocumentType.Thread.value},
                {"insightType": InsightType.Issue.value}
            ]
        }

        slack_query_filter = {
            "$and": [
                {"type": DocumentType.Thread.value},
                {"insightType": InsightType.Slack.value}
            ]
        }

        doc_query_filter = {
            "$and": [
                {"type": DocumentType.Documentation.value},
                {"insightType": InsightType.Documentation.value}
            ]
        }

        for m in messages:
            content = m.get_text()
            dense_vec = self.__embedder.get_query_embedding(query=content)
            sparse_vec = self.__embedder.safely_get_sparse_query_embedding(query=content)

            def invoke_query(query_filter: dict) -> Optional[QueryResponse]:
                try:
                    return self.__pinecone_client.query_embeddings(
                        dense_vec=dense_vec,
                        sparse_vec=sparse_vec,
                        alpha=alpha,
                        top_k=top_k,
                        query_filter=query_filter,
                    )
                except Exception as e:
                    print(f"Error during query: {e}")
                    return None

            def get_match_count(
                    query_response: Optional[QueryResponse],
                    sub_filter_length_cutoff: int = 0,
            ) -> int:
                if not query_response:
                    return 0

                match_count = 0
                for match in query_response['matches']:
                    file_content = ""
                    try:
                        file_content = match['metadata'].get('fileContent', "")
                        if file_content:
                            file_content = Compression.decompress(file_content)
                    except Exception as e:
                        print(f"Error during metadata retrieval: {e}")

                    if match['score'] >= score_cutoff and len(file_content) >= sub_filter_length_cutoff:
                        match_count += 1

                return match_count

            qa_query_results = invoke_query(query_filter=qa_query_filter)
            pr_query_results = invoke_query(query_filter=pr_query_filter)
            issue_query_results = invoke_query(query_filter=issue_query_filter)
            slack_query_results = invoke_query(query_filter=slack_query_filter)
            doc_query_results = invoke_query(query_filter=doc_query_filter)

            qa_query_matches.append(get_match_count(query_response=qa_query_results))
            pr_query_matches.append(get_match_count(query_response=pr_query_results))
            slack_query_matches.append(get_match_count(query_response=slack_query_results))
            doc_query_matches.append(get_match_count(query_response=doc_query_results))
            issue_query_matches.append(get_match_count(query_response=issue_query_results))

            filtered_pr_query_matches.append(
                get_match_count(query_response=pr_query_results, sub_filter_length_cutoff=filter_length_cutoff))
            filtered_slack_query_matches.append(
                get_match_count(query_response=slack_query_results, sub_filter_length_cutoff=filter_length_cutoff))
            filtered_issue_query_matches.append(
                get_match_count(query_response=issue_query_results, sub_filter_length_cutoff=filter_length_cutoff))

        return {
            REPO_ID_COLUMN: [msg.repo_id for msg in messages],
            DOC_ID_COLUMN: [msg.get_id() for msg in messages],
            FILE_CONTENT_COLUMN: [msg.file_content for msg in messages],
            FILE_PATH_COLUMN: [msg.file_path for msg in messages],
            FILE_NAME_COLUMN: [msg.file_name for msg in messages],
            START_LINE_COLUMN: [msg.start_line for msg in messages],
            END_LINE_COLUMN: [msg.end_line for msg in messages],
            QA_MATCHES_COLUMN: [qa_query_match for qa_query_match in qa_query_matches],
            PR_MATCHES_COLUMN: [pr_query_match for pr_query_match in pr_query_matches],
            SLACK_MATCHES_COLUMN: [slack_query_match for slack_query_match in slack_query_matches],
            DOC_MATCHES_COLUMN: [doc_query_match for doc_query_match in doc_query_matches],
            ISSUE_MATCHES_COLUMN: [issue_query_match for issue_query_match in issue_query_matches],
            FILTERED_PR_MATCHES_COLUMN: [filtered_pr_query_match for filtered_pr_query_match in
                                         filtered_pr_query_matches],
            FILTERED_SLACK_MATCHES_COLUMN: [filtered_slack_query_match for filtered_slack_query_match in
                                            filtered_slack_query_matches],
            FILTERED_ISSUE_MATCHES_COLUMN: [filtered_issue_query_match for filtered_issue_query_match in
                                            filtered_issue_query_matches],
            FILTER_LENGTH_CUTOFF_COLUMN: [filter_length_cutoff],
            ALPHA_COLUMN: [alpha],
            SCORE_CUTOFF_COLUMN: [score_cutoff],
        }

    def process_semantic_match(self, messages: [SourceCodeMessage], alpha=0.8, top_k=15, score_cutoff=0.7):
        results = []
        batch_size = 50

        with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor, tqdm(
                total=len(messages)) as pbar:
            for i in range(0, len(messages), batch_size):
                chunk = messages[i:i + batch_size]
                chunk_results = list(
                    executor.map(lambda m: self.process_message([m], alpha, top_k, score_cutoff), chunk))
                results.extend(chunk_results)
                pbar.update(len(chunk))

        def merge_lists(*dictionaries):
            result_dict = {}

            # Iterate over the keys
            for key in dictionaries[0].keys():
                # Use zip and sum to add corresponding lists together
                result_dict[key] = [item for sublist in [d[key] for d in dictionaries] for item in sublist]

            return result_dict

        merged_results = merge_lists(*results)
        return pd.DataFrame(merged_results)
