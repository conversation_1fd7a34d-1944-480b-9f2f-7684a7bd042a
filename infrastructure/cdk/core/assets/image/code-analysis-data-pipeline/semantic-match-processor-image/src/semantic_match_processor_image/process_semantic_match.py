import logging
import tempfile

import git_utils.git_utils as git
from pandas import DataFrame

from pinecone_utils.index_config import IndexConfig
from pinecone_utils.pinecone_client import Pinecone<PERSON>lient

from embedding_utils.embedding_generator import EmbeddingGenerator
from embedding_utils.instructor.instructor_bm25 import Instructor<PERSON>25EmbeddingGenerator

from semantic_match_processor_image.config_loader import ConfigLoader
from semantic_match_processor_image.pinecone_constants import PINECONE_API_KEY
from semantic_match_processor_image.process_constants import PROCESS_DOCUMENT_MIN_LENGTH, PROCESS_DOCUMENT_MAX_LENGTH, \
    PROCESS_MIN_DOCUMENTS, PROCESS_MAX_DOCUMENTS
from semantic_match_processor_image.semantic_match_processor import PineconeSemanticMatchProcessor
from semantic_match_processor_image.semantic_match_writer import AggregateSemanticMatchWriter, \
    SummarySemanticMatchWriter, GranularSemanticMatchWriter
from semantic_match_processor_image.source_code_provider import SourceCodeProvider, SourceCodeMessage

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)


def load_config() -> ConfigLoader:
    """Load task config from environment variables."""
    return ConfigLoader()


def create_pinecone_client(config: ConfigLoader) -> PineconeClient:
    pinecone_client = PineconeClient(
        api_key=PINECONE_API_KEY,
        namespace=config.pinecone_namespace,
        index_config=IndexConfig(
            name=config.pinecone_hybrid_index,
            metadata_config={"indexed": ["source", "type", "insightType", "group", "date"]},
        ),
    )
    pinecone_client.create_hybrid_index()
    return pinecone_client


def create_embedder() -> EmbeddingGenerator:
    return InstructorBM25EmbeddingGenerator()


def clone_repository(config: ConfigLoader) -> str:
    if config.optional_repo_dir:
        return config.optional_repo_dir

    local_dir = tempfile.mkdtemp()
    options = ["--no-tags", "--single-branch"]

    git.clone_with_config(
        repo_http_clone_url=config.repo_http_clone_url,
        local_dir=local_dir,
        multi_options=options,
    )

    return local_dir


def get_source_code_messages(config: ConfigLoader, repo_dir: str) -> [SourceCodeMessage]:
    source_code_provider = SourceCodeProvider(
        repo_id=config.repo_id,
        repo_dir=repo_dir,
    )

    source_code_messages = source_code_provider.load()
    logging.info(f"Number of source documents: {len(source_code_messages)}")
    source_code_messages = [
        s
        for s in source_code_messages
        if PROCESS_DOCUMENT_MIN_LENGTH <= len(s.get_text()) <= PROCESS_DOCUMENT_MAX_LENGTH
    ]

    if len(source_code_messages) < PROCESS_MIN_DOCUMENTS:
        logging.error(
            f"Number of source documents: {len(source_code_messages)} does not meet minimum requirement: {PROCESS_MIN_DOCUMENTS}"
        )
        exit(0)

    source_code_messages = source_code_messages[:PROCESS_MAX_DOCUMENTS]
    return source_code_messages


def process_semantic_matches(
        config: ConfigLoader,
        pinecone_client: PineconeClient,
        embedder: EmbeddingGenerator,
        messages: [SourceCodeMessage]
) -> DataFrame:
    semantic_match_processor = PineconeSemanticMatchProcessor(
        repo_id=config.repo_id,
        embedder=embedder,
        pinecone_client=pinecone_client,
    )

    return semantic_match_processor.process_semantic_match(
        messages=messages,
    )


def write_semantic_matches(
        config: ConfigLoader,
        df: DataFrame,
):
    aggregate_semantic_match_writer = AggregateSemanticMatchWriter(repo_id=config.repo_id)
    aggregate_semantic_match_writer.write_semantic_matches(df=df)

    summary_semantic_match_writer = SummarySemanticMatchWriter(repo_id=config.repo_id)
    summary_semantic_match_writer.write_semantic_matches(df=df)

    granular_semantic_match_writer = GranularSemanticMatchWriter(repo_id=config.repo_id)
    granular_semantic_match_writer.write_semantic_matches(df=df)


def main():
    logging.info("Loading task config ...")
    config = load_config()

    logging.info("Creating Pinecone client ...")
    pinecone_client = create_pinecone_client(config=config)

    logging.info("Creating Embedder ...")
    embedder = create_embedder()

    logging.info(f"Cloning repository {config.repo_id} ...")
    repo_dir = clone_repository(config=config)

    logging.info("Get source code messages ...")
    source_code_messages = get_source_code_messages(config=config, repo_dir=repo_dir)

    logging.info("Processing semantic matches ...")
    df = process_semantic_matches(
        config=config,
        pinecone_client=pinecone_client,
        embedder=embedder,
        messages=source_code_messages,
    )

    logging.info("Writing semantic matches ...")
    write_semantic_matches(config=config, df=df)


if __name__ == "__main__":
    main()
