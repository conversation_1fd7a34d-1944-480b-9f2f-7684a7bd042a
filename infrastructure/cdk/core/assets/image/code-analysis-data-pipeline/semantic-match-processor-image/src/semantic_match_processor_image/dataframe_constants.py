DOC_ID_COLUMN = "doc_id"
FILE_CONTENT_COLUMN = "file_content"
REPO_ID_COLUMN = "repo_id"
FILE_PATH_COLUMN = "file_path"
FILE_NAME_COLUMN = "file_name"
START_LINE_COLUMN = "start_line"
END_LINE_COLUMN = "end_line"

FILTERED_ISSUE_MATCHES_COLUMN = "filtered_issue_matches"
FILTERED_SLACK_MATCHES_COLUMN = "filtered_slack_matches"
FILTERED_PR_MATCHES_COLUMN = "filtered_pr_matches"

PR_MATCHES_COLUMN = "pr_matches"
DOC_MATCHES_COLUMN = "doc_matches"
SLACK_MATCHES_COLUMN = "slack_matches"
ISSUE_MATCHES_COLUMN = "issue_matches"
QA_MATCHES_COLUMN = "qa_matches"

ALPHA_COLUMN = "alpha"
SCORE_CUTOFF_COLUMN = "score_cutoff"
FILTER_LENGTH_CUTOFF_COLUMN = "filter_length_cutoff"

TOTAL_ROWS_COUNT_COLUMN = "total_rows_count"
FILTERED_ISSUE_MATCHES_ROWS_COUNT_COLUMN = "filtered_issue_matches_rows_count"
FILTERED_SLACK_MATCHES_ROWS_COUNT_COLUMN = "filtered_slack_matches_rows_count"
FILTERED_PR_MATCHES_ROWS_COUNT_COLUMN = "filtered_pr_matches_rows_count"
QA_MATCHES_ROWS_COUNT_COLUMN = "qa_matches_rows_count"
DOC_MATCHES_ROWS_COUNT_COLUMN = "doc_matches_rows_count"
ISSUE_MATCHES_ROWS_COUNT_COLUMN = "issue_matches_rows_count"
SLACK_MATCHES_ROWS_COUNT_COLUMN = "slack_matches_rows_count"
PR_MATCHES_ROWS_COUNT_COLUMN = "pr_matches_rows_count"
