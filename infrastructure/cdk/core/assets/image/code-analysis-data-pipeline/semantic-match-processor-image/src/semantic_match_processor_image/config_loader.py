import os


class ConfigLoader:
    def __init__(self):
        # required values
        self.repo_http_clone_url = os.environ.get("PROCESS_REPO_HTTP_CLONE_URL")
        self.repo_id = os.environ.get("PROCESS_REPO_ID")
        self.org_id = os.environ.get("PROCESS_ORG_ID")
        self.optional_repo_dir = os.environ.get("PROCESS_REPO_DIR")
        self.pinecone_hybrid_index = os.environ.get(
            "PROCESS_PINECONE_HYBRID_INDEX", default="local-unblocked-semantic-semantic"
        )
        self.pinecone_namespace = os.environ.get("PROCESS_PINECONE_NAMESPACE", default=self.org_id)

        # verify required values
        self._verify_loaded_correctly()

    def _verify_loaded_correctly(self):
        missing_vars = [
            f"PROCESS_{key.upper()}" for key, value in self.__dict__.items() if "optional" not in key and not value
        ]

        if missing_vars:
            raise ValueError(f"Expected the following environment variables to be set: {', '.join(missing_vars)}")

    @staticmethod
    def str_to_bool(value):
        return str(value).lower() in ["true", "1", "yes", "t", "y"]
