import os

# AWS
PROCESS_INPUT_DIRECTORY = "/opt/ml/processing/inputs/s3/input"
if os.getenv("PROCESS_ENV") == "local":
    # Local
    PROCESS_INPUT_DIRECTORY = os.path.join(os.path.dirname(__file__), "fixtures", "scm")

PROCESS_INPUT_GLOB = "{}/run*".format(PROCESS_INPUT_DIRECTORY)

# In AWS
PROCESS_OUTPUT_DIRECTORY = "/opt/ml/processing/outputs/s3/output"
if os.getenv("PROCESS_ENV") == "local":
    # Local
    PROCESS_OUTPUT_DIRECTORY = "/tmp/unzipped"

PROCESS_OUTPUT_GRANULAR_BASE_NAME = "semantic_match_granular"
PROCESS_OUTPUT_AGGREGATE_BASE_NAME = "semantic_match_aggregate"
PROCESS_OUTPUT_SUMMARY_BASE_NAME = "semantic_match_summary"
