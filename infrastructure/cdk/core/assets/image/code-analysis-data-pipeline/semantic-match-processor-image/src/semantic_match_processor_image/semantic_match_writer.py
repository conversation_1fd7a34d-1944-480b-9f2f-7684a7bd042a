import logging
import os

import pandas as pd
from pandas import DataFrame

from semantic_match_processor_image.dataframe_constants import REPO_ID_COLUMN, FILE_NAME_COLUMN, FILE_PATH_COLUMN, \
    DOC_ID_COLUMN, ALPHA_COLUMN, SCORE_CUTOFF_COLUMN, QA_MATCHES_COLUMN, PR_MATCHES_COLUMN, DOC_MATCHES_COLUMN, \
    SLACK_MATCHES_COLUMN, PR_MATCHES_ROWS_COUNT_COLUMN, SLACK_MATCHES_ROWS_COUNT_COLUMN, QA_MATCHES_ROWS_COUNT_COLUMN, \
    DOC_MATCHES_ROWS_COUNT_COLUMN, TOTAL_ROWS_COUNT_COLUMN, ISSUE_MATCHES_COLUMN, ISSUE_MATCHES_ROWS_COUNT_COLUMN, \
    FILTER_LENGTH_CUTOFF_COLUMN, FILTERED_SLACK_MATCHES_COLUMN, FILTERED_PR_MATCHES_COLUMN, \
    FILTERED_ISSUE_MATCHES_COLUMN, FILTERED_PR_MATCHES_ROWS_COUNT_COLUMN, FILTERED_SLACK_MATCHES_ROWS_COUNT_COLUMN, \
    FILTERED_ISSUE_MATCHES_ROWS_COUNT_COLUMN
from semantic_match_processor_image.path_constants import PROCESS_OUTPUT_DIRECTORY, PROCESS_OUTPUT_AGGREGATE_BASE_NAME, \
    PROCESS_OUTPUT_SUMMARY_BASE_NAME, PROCESS_OUTPUT_GRANULAR_BASE_NAME


class SemanticMatchWriter:
    def write_semantic_matches(self, df: DataFrame):
        pass


class GranularSemanticMatchWriter(SemanticMatchWriter):
    __output_directory: str
    __output_base_name: str
    __repo_id: str

    def __init__(
            self,
            repo_id: str,
            output_directory: str = PROCESS_OUTPUT_DIRECTORY,
            output_base_name: str = PROCESS_OUTPUT_GRANULAR_BASE_NAME
    ):
        self.__repo_id = repo_id
        self.__output_directory = output_directory
        self.__output_base_name = output_base_name

    def write_semantic_matches(self, df: DataFrame):
        try:
            output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}-{self.__repo_id}.csv")
            df.to_csv(path_or_buf=output_file, index=False)
        except Exception as e:
            logging.exception(f"Failed to write semantic match granular. Exception: {str(e)}")


class AggregateSemanticMatchWriter(SemanticMatchWriter):
    __output_directory: str
    __output_base_name: str
    __repo_id: str

    def __init__(
            self,
            repo_id: str,
            output_directory: str = PROCESS_OUTPUT_DIRECTORY,
            output_base_name: str = PROCESS_OUTPUT_AGGREGATE_BASE_NAME
    ):
        self.__repo_id = repo_id
        self.__output_directory = output_directory
        self.__output_base_name = output_base_name

    def write_semantic_matches(self, df: DataFrame):
        try:
            output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}-{self.__repo_id}.csv")
            # Define a custom aggregation dictionary to get the first value of repo_id
            agg_dict = {
                QA_MATCHES_COLUMN: "sum",
                DOC_MATCHES_COLUMN: "sum",
                SLACK_MATCHES_COLUMN: "sum",
                PR_MATCHES_COLUMN: "sum",
                ISSUE_MATCHES_COLUMN: "sum",
                FILTERED_SLACK_MATCHES_COLUMN: "sum",
                FILTERED_PR_MATCHES_COLUMN: "sum",
                FILTERED_ISSUE_MATCHES_COLUMN: "sum",
                REPO_ID_COLUMN: "first",
                ALPHA_COLUMN: "first",
                SCORE_CUTOFF_COLUMN: "first",
                FILE_NAME_COLUMN: "first",
                FILE_PATH_COLUMN: "first",
                FILTER_LENGTH_CUTOFF_COLUMN: "first",
            }
            aggregated_df = df.groupby([DOC_ID_COLUMN]).agg(agg_dict).reset_index()
            aggregated_df.to_csv(path_or_buf=output_file, index=False)
        except Exception as e:
            logging.exception(f"Failed to write semantic match aggregate. Exception: {str(e)}")


class SummarySemanticMatchWriter(SemanticMatchWriter):
    __output_directory: str
    __output_base_name: str
    __repo_id: str

    def __init__(
            self,
            repo_id: str,
            output_directory: str = PROCESS_OUTPUT_DIRECTORY,
            output_base_name: str = PROCESS_OUTPUT_SUMMARY_BASE_NAME,
    ):
        self.__repo_id = repo_id
        self.__output_directory = output_directory
        self.__output_base_name = output_base_name

    def write_semantic_matches(self, df: DataFrame):
        try:
            output_file = os.path.join(self.__output_directory, f"{self.__output_base_name}-{self.__repo_id}.csv")
            agg_dict = {
                QA_MATCHES_COLUMN: "sum",
                DOC_MATCHES_COLUMN: "sum",
                SLACK_MATCHES_COLUMN: "sum",
                PR_MATCHES_COLUMN: "sum",
                ISSUE_MATCHES_COLUMN: "sum",
                FILTERED_SLACK_MATCHES_COLUMN: "sum",
                FILTERED_PR_MATCHES_COLUMN: "sum",
                FILTERED_ISSUE_MATCHES_COLUMN: "sum",
                REPO_ID_COLUMN: "first",
                ALPHA_COLUMN: "first",
                SCORE_CUTOFF_COLUMN: "first",
                FILTER_LENGTH_CUTOFF_COLUMN: "first",
            }
            aggregated_df = df.groupby([DOC_ID_COLUMN]).agg(agg_dict).reset_index()
            summary_df = pd.DataFrame({
                PR_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[PR_MATCHES_COLUMN] > 0).sum(),
                SLACK_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[SLACK_MATCHES_COLUMN] > 0).sum(),
                QA_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[QA_MATCHES_COLUMN] > 0).sum(),
                DOC_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[DOC_MATCHES_COLUMN] > 0).sum(),
                ISSUE_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[ISSUE_MATCHES_COLUMN] > 0).sum(),
                FILTERED_PR_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[FILTERED_PR_MATCHES_COLUMN] > 0).sum(),
                FILTERED_SLACK_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[FILTERED_SLACK_MATCHES_COLUMN] > 0).sum(),
                FILTERED_ISSUE_MATCHES_ROWS_COUNT_COLUMN: (aggregated_df[FILTERED_ISSUE_MATCHES_COLUMN] > 0).sum(),
                TOTAL_ROWS_COUNT_COLUMN: len(aggregated_df),
                REPO_ID_COLUMN: self.__repo_id,
                ALPHA_COLUMN: aggregated_df[ALPHA_COLUMN].iloc[0],
                SCORE_CUTOFF_COLUMN: aggregated_df[SCORE_CUTOFF_COLUMN].iloc[0],
                FILTER_LENGTH_CUTOFF_COLUMN: aggregated_df[FILTER_LENGTH_CUTOFF_COLUMN].iloc[0],
            }, index=[0])
            summary_df.to_csv(path_or_buf=output_file, index=False)
        except Exception as e:
            logging.exception(f"Failed to write semantic match summary. Exception: {str(e)}")
