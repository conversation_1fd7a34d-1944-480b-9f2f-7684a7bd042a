FROM --platform="linux/amd64" nvidia/cuda:12.4.1-devel-ubuntu22.04

# Necessary for HDBSCAN
RUN apt-get update \
  && apt-get -y install gcc  \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Install Git
RUN apt-get install -y git

RUN apt-get -y install python3
RUN apt-get -y install python3-pip
RUN apt-get -y install python3-pip
RUN apt-get -y install python-is-python3

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==1.8.2

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

WORKDIR /code

# Install dependencies
COPY . /code

# The install of torch is required for using CUDA (problem with poetry installation)
RUN POETRY_VIRTUALENVS_CREATE=false poetry install --no-cache --no-interaction --without dev \
    && pip3 install torch==2.7.0 --upgrade --no-cache-dirRUN poetry config virtualenvs.create false
RUN poetry config virtualenvs.create false

# Add a Python script and configure Docker to run it
ENTRYPOINT ["poetry", "run", "python", "/code/src/semantic_match_processor_image/process_topics.py"]
