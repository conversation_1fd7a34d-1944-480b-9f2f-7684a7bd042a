check: format test

# To run locally replace these variables from your own local stack repo and team as necessary.
# You can get these values from your local Admin Web.
repoUrl = 'https://github.com/NextChapterSoftware/unblocked.git'
repoId = 'f8e94478-7548-4000-a625-1a43267e0fc5'
orgId = '2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417'
env = "local"
aws_profile="prod"
pinecone_hybrid_index="prod-unblocked-instructor-dotproduct-n40"

# other customer
repoUrl = 'https://github.com/NextChapterSoftware/unblocked.git'
repoId = '6399820c-d768-4487-b1c7-3acafbebb9c2'
orgId = 'e0c510e2-6e19-4947-a19a-98f45478d171'
repoDir = '/Users/<USER>/semantic/cribl'
env = "local"
aws_profile="prod"
pinecone_hybrid_index="prod-unblocked-instructor-dotproduct-n40"

run:
	export PROCESS_ENV=$(env); \
	export AWS_PROFILE=$(aws_profile); \
	export PROCESS_REPO_HTTP_CLONE_URL=$(repoUrl); \
	export PROCESS_REPO_DIR=$(repoDir); \
	export PROCESS_REPO_ID=$(repoId); \
	export PROCESS_ORG_ID=$(orgId); \
	export PROCESS_PINECONE_HYBRID_INDEX=$(pinecone_hybrid_index); \
	poetry run python ./src/semantic_match_processor_image/process_semantic_match.py

format:
	poetry run black src

test:
	poetry run pytest src

.PHONY: check format run test
