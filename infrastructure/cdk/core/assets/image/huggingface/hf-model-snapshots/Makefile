check: format test

# To run locally replace these variables from your own local stack repo and team as necessary.
# You can get these values from your local Admin Web.
model_id="bigscience/mt0-small"
aws_profile="dev"

ENV_VARS=\
	export AWS_PROFILE=$(aws_profile); \
	export MODEL_IDS="$(model_id)";

run:
	$(ENV_VARS) poetry run python ./src/hf_model_snapshots/generate_model_snapshots.py

format:
	poetry run black src

unit-test:
	poetry run pytest ./src/hf_model_snapshot/test/unit

.PHONY: check format run test
