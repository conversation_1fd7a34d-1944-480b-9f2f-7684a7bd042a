FROM --platform="linux/amd64" python:3.11-slim-bookworm

# Necessary for bertopic
RUN apt-get update \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==1.8.2

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

WORKDIR /code

# Install dependencies
COPY . /code

RUN POETRY_VIRTUALENVS_CREATE=false poetry install
RUN poetry config virtualenvs.create false

# Add a Python script and configure Docker to run it
ENTRYPOINT ["poetry", "run", "python", "/code/src/hf_model_snapshots/generate_model_snapshots.py"]
