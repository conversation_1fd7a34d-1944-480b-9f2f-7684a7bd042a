from typing import Optional
from huggingface_hub import snapshot_download


class HFHubUtils:
    @staticmethod
    def download_model(
        model_name: str,
        cache_dir: str,
        max_workers: int = 30,
        resume_download: bool = True,
        library_name: Optional[str] = None,
        library_version: Optional[str] = None,
        revision: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> str:
        """
        Downloads a huggingface model and saves it to a specified directory.

        Parameters:
        model_name (str): Name of the huggingface model.
        cache_dir (str): Directory to save the downloaded model.
        library_name (str, optional): Specific library name to use for saving model files.
        library_version (str, optional): The library version to use.
        revision (str, optional): The specific model version to use.
        user_agent (str, optional): User agent to use for downloading.

        Returns:
        path (str): Path of the downloaded model.
        """
        return snapshot_download(
            model_name,
            cache_dir=cache_dir,
            library_name=library_name,
            library_version=library_version,
            revision=revision,
            user_agent=user_agent,
            max_workers=max_workers,
            resume_download=resume_download,
        )
