from typing import Set

from pydantic import (
    Field, validator, field_validator,
)
from pydantic_settings import BaseSettings

from hf_model_snapshots.hf_constants import DEFAULT_CACHE_DIR, DEFAULT_MODEL_ID


class ConfigLoader(BaseSettings):
    model_ids: [str] = Field(
        default=[DEFAULT_MODEL_ID],
        alias="MODEL_IDS",
    )
    cache_dir: str = Field(
        default=DEFAULT_CACHE_DIR,
        alias="CACHE_DIR",
    )

    # Validator to parse comma-separated values into a list
    @field_validator('model_ids')
    @classmethod
    def parse_list(cls, v):
        if isinstance(v, str):
            return v.split(',')
        return v
