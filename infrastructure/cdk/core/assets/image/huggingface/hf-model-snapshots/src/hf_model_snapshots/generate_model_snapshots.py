import logging

from hf_model_snapshots.config_loader import Config<PERSON>oader
from hf_model_snapshots.hf_hub_utils import HFHubUtils

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)


def load_config() -> ConfigLoader:
    """Load task config from environment variables."""
    return ConfigLoader()


def generate_model_snapshots(config: ConfigLoader):
    for model_id in config.model_ids:
        HFHubUtils.download_model(
            model_name=model_id,
            cache_dir=config.cache_dir,
        )


def main() -> None:
    logging.info("Loading task config ...")
    config = load_config()

    print(config)

    logging.info("Downloading model ...")
    generate_model_snapshots(config=config)


if __name__ == "__main__":
    main()
