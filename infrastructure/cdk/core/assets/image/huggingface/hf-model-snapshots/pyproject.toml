[tool.poetry]
name = "hf-model-snapshots"
version = "1.0.0"
description = ""
authors = []
readme = "README.md"
packages = [
    { include = "hf_model_snapshots", from = "src" },
]

[tool.poetry.dependencies]
brotli = "^1"
python = ">=3.10.12,<3.14"
unblocked-aws-utils = "^0"
huggingface-hub = "^0.22.2"
pydantic-settings = "^2.2.1"

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
