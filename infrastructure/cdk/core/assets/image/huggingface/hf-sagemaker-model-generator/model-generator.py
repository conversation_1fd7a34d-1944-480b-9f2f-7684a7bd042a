# Use this to download and generate and augment a HuggingFace model if you want to do custom inference
# https://gist.github.com/timesler/4b244a6b73d6e02d17fd220fd92dfaec
# https://github.com/dhawalkp/dolly-12b/blob/main/dolly-12b-deepspeed-sagemaker.ipynb
# Make sure to install the following using brew: gnu-tar, pigz

import os
import shutil
from pathlib import Path
import tempfile

import sagemaker
from huggingface_hub import snapshot_download
from sagemaker.s3 import S3Uploader

# Set HF cache location and ensure that hf-transfer is used for faster download
os.environ["HF_HUB_ENABLE_HF_TRANSFER"] = "1"
os.environ["AWS_PROFILE"] = "dev"

sess = sagemaker.Session()
role = sagemaker.get_execution_role()

print(f"SageMaker role arn: {role}")
print(f"SageMaker bucket: {sess.default_bucket()}")
print(f"SageMaker session region: {sess.boto_region_name}")

MODEL_ID = "mosaicml/mpt-30b-chat"
MODEL_OVERRIDE_DIR = os.path.abspath("mosaic-mpt-chat-transformer-model-override")
MODEL_SNAPSHOT_DIR = os.path.expanduser("~/models/snapshots")

MODEL_BASE_DIR = Path(MODEL_ID.split("/")[-1])
MODEL_SRC_DIR = Path(MODEL_OVERRIDE_DIR)
MODEL_TAR_DIR = Path(os.path.join(MODEL_SNAPSHOT_DIR, str(MODEL_BASE_DIR)))
MODEL_TAR_FILE = Path(os.path.join(MODEL_SNAPSHOT_DIR, "model.tar.gz"))
MODEL_CODE_DIR = MODEL_TAR_DIR.joinpath("code")

print(f"Model Id: {MODEL_ID}")
print(f"Model Snapshot Dir: {str(MODEL_SNAPSHOT_DIR)}")
print(f"Model Base Dir: {str(MODEL_BASE_DIR)}")
print(f"Model Src Dir: {str(MODEL_SRC_DIR)}")
print(f"Model Tar Dir: {str(MODEL_TAR_DIR)}")
print(f"Model Tar File: {str(MODEL_TAR_FILE)}")
print(f"Model Code File: {str(MODEL_CODE_DIR)}")


def create_model_dir():
    MODEL_TAR_DIR.mkdir(exist_ok=True, parents=True)


def download_hugging_face_model():
    # Download model from Hugging Face into model_dir
    snapshot_download(repo_id=MODEL_ID,
                      local_dir=str(MODEL_TAR_DIR),
                      local_dir_use_symlinks=False,
                      resume_download=True,
                      max_workers=16,
                      )


def build_model_artifact():
    parent_dir = os.getcwd()

    # change to model dir
    os.chdir(str(MODEL_TAR_DIR))

    os.system(f"mkdir -p {str(MODEL_CODE_DIR)}")
    shutil.copytree(src=str(MODEL_SRC_DIR), dst=str(MODEL_CODE_DIR), dirs_exist_ok=True)

    # use pigz for faster and parallel compression
    os.system(f"gtar --use-compress-program=pigz -cvf {str(MODEL_TAR_FILE)}  -C {str(MODEL_TAR_DIR)} .")

    # change back to parent dir
    os.chdir(parent_dir)


def upload_model_artifact():
    s3_model_uri = S3Uploader.upload(
        local_path=str(MODEL_TAR_FILE),
        desired_s3_uri=f"s3://{sess.default_bucket()}/{MODEL_BASE_DIR}",
    )

    print(f"Model uploaded to: {s3_model_uri}")


create_model_dir()
download_hugging_face_model()
build_model_artifact()
upload_model_artifact()
