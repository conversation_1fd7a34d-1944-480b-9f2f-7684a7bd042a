'''
This code enforces a maximum image count per ECR repository.
'''
from __future__ import print_function

import argparse
import os
import boto3

REGION = None
DRYRUN = None
IMAGES_TO_KEEP = None


def initialize():
    global REGION
    global DRYRUN
    global IMAGES_TO_KEEP

    REGION = os.environ.get('REGION', "None")
    DRYRUN = os.environ.get('DRYRUN', "false").lower()
    if DRYRUN == "false":
        DRYRUN = False
    else:
        DRYRUN = True
    IMAGES_TO_KEEP = int(os.environ.get('IMAGES_TO_KEEP', 100))


def handler(event, context):
    initialize()
    if REGION == "None":
        ec2_client = boto3.client('ec2')
        available_regions = ec2_client.describe_regions()['Regions']
        for region in available_regions:
            clean_tagged_repos(region['RegionName'])
    else:
        clean_tagged_repos(REGION)


def list_all_tagged_repos(ecr_client):
    repositories = []
    describe_repo_paginator = ecr_client.get_paginator('describe_repositories')
    for response in describe_repo_paginator.paginate():
        for repo in response['repositories']:
            # Skip cdk repositories
            if 'cdk' not in repo.get('repositoryName', ''):
                repositories.append(repo)

    return repositories


def clean_images_max_count(ecr_client, repositories):
    for repo in repositories:
        print("Checking image retention for: " + repo['repositoryName'])
        images = []
        describe_images_repo_paginator = ecr_client.get_paginator('describe_images')
        for response_listImagepaginator in describe_images_repo_paginator.paginate(
                repositoryName=repo['repositoryName']):
            images.extend(response_listImagepaginator["imageDetails"])

        if len(images) > IMAGES_TO_KEEP:
            images.sort(key=lambda img: img['imagePushedAt'], reverse=True)
            images = images[IMAGES_TO_KEEP:]
            images = [{"imageDigest": img["imageDigest"]} for img in images]
            if not DRYRUN:
                for i in range(0, len(images) - 1, 100):
                    endOffset = i + 100
                    if endOffset > len(images):
                        endOffset = len(images) - 1

                    response = ecr_client.batch_delete_image(
                        repositoryName=repo["repositoryName"],
                        imageIds=images[i:endOffset]
                    )
                    if len(response.get("failures", [])) > 0:
                        print("Failed to delete following images from: " + repo["repositoryName"])
                        print(response["failures"])
            else:
                print("following list of images would have been deleted from: " + repo["repositoryName"])
                for image in images:
                    print(image)


def clean_tagged_repos(regionname):
    ecr_client = boto3.client('ecr', region_name=regionname)

    print("Discovering list of tagged repositories in " + regionname)
    repositories = list_all_tagged_repos(ecr_client)
    if len(repositories) < 1:
        print("failed to list any repos. Something is wrong!")
        exit(1)
    clean_images_max_count(ecr_client, repositories)


# Below is the test harness
if __name__ == '__main__':
    REQUEST = {"None": "None"}
    PARSER = argparse.ArgumentParser(description='Deletes stale ECR images')
    PARSER.add_argument('-dryrun', help='Prints the repository to be deleted without deleting them', default='true',
                        action='store', dest='dryrun')
    PARSER.add_argument('-imagestokeep', help='Number of image tags to keep', default='20', action='store',
                        dest='imagestokeep')
    PARSER.add_argument('-region', help='ECR/ECS region', default=None, action='store', dest='region')

    ARGS = PARSER.parse_args()
    if ARGS.region:
        os.environ["REGION"] = ARGS.region
    else:
        os.environ["REGION"] = "None"
    os.environ["DRYRUN"] = ARGS.dryrun.lower()
    os.environ["IMAGES_TO_KEEP"] = ARGS.imagestokeep
    handler(REQUEST, None)
