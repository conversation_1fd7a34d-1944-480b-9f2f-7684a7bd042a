import os
import boto3

from botocore.exceptions import ClientError
from jsonpath_ng.ext import parse

AWS_REGION = os.environ.get("AWS_REGION", "us-west-2")

print(os.environ)

sagemaker_client = boto3.Session().client("sagemaker", region_name=AWS_REGION)

MODEL_ENDPOINT_NAME_JSON_PATH = os.environ.get(
    'MODEL_ENDPOINT_NAME_JSON_PATH',
    "$.PowerMLSubPipeline.PowerMLModelEndpoint.ModelEndpointName"
)


def get_model_endpoint_name(event):
    jsonpath_expression = parse(MODEL_ENDPOINT_NAME_JSON_PATH)
    for match in jsonpath_expression.find(event):
        return match.value


def get_team_id(event):
    return event['TeamId']


def invoke_endpoint(event):
    endpoint_name = get_model_endpoint_name(event)
    try:
        response = sagemaker_client.describe_endpoint(
            EndpointName=endpoint_name
        )
        print('response', response)
        return {'Exists': 'TRUE'}
    except ClientError as e:
        print('{} is not existent in endpoint list'.format(endpoint_name))
        return {'Exists': 'FALSE'}
    return result


# When invoked in a step function state machine the event will
# include the entire state machine input.
def handler(event, context):
    print(MODEL_ENDPOINT_NAME_JSON_PATH)

    return invoke_endpoint(event)
