import os
import boto3
import json

from jsonpath_ng.ext import parse

print(os.environ)

AWS_REGION = os.environ.get("AWS_REGION", "us-west-2")

sagemaker_client = boto3.Session().client("sagemaker-runtime", region_name=AWS_REGION)

MODEL_ENDPOINT_NAME_JSON_PATH = os.environ.get(
    'MODEL_ENDPOINT_NAME_JSON_PATH',
    "$.PowerMLSubPipeline.PowerMLModelEndpoint.ModelEndpointName"
)

MODEL_ENDPOINT_DATA_JSON_PATH = os.environ.get(
    'MODEL_ENDPOINT_DATA_JSON_PATH',
    "$.PowerMLSubPipeline.PowerMLModelEndpoint.ModelEndpointData"
)


def get_model_endpoint_name(event):
    jsonpath_expression = parse(MODEL_ENDPOINT_NAME_JSON_PATH)
    for match in jsonpath_expression.find(event):
        return match.value


def get_team_id(event):
    return event['TeamId']


def get_model_endpoint_data(event):
    jsonpath_expression = parse(MODEL_ENDPOINT_DATA_JSON_PATH)
    for match in jsonpath_expression.find(event):
        return match.value


def generate_data_payload(data):
    return json.dumps(data).encode()


def invoke_endpoint(event):
    endpoint_name = get_model_endpoint_name(event)
    data = get_model_endpoint_data(event)
    data_payload = generate_data_payload(data)
    response = sagemaker_client.invoke_endpoint(
        EndpointName=endpoint_name,
        ContentType='application/json',
        Body=data_payload
    )
    print('response', response)
    result = json.loads(response['Body'].read().decode())
    print('result', result)
    return result


# When invoked in a step function state machine the event will
# include the entire state machine input.
def handler(event, context):
    print(MODEL_ENDPOINT_NAME_JSON_PATH)

    return invoke_endpoint(event)
