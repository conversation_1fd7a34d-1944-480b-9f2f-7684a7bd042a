FROM --platform="linux/amd64" public.ecr.aws/lambda/python:3.12

# Install dependencies
RUN dnf update -y && \
    dnf install -y python3 && \
    pip3 install --upgrade pip && \
    pip install awscli boto3 stomp.py jsonpath-ng

# Add a Python script and configure Docker to run it
ADD app.py ${LAMBDA_TASK_ROOT}/

# self-signed certificate needed for stomp ssl communication
# openssl req -newkey rsa:2048 -nodes -keyout privateKey.key -x509 -days 365 -out cert.pem
ADD ssl/cert.pem  ${LAMBDA_TASK_ROOT}/ssl/
ADD ssl/privateKey.key  ${LAMBDA_TASK_ROOT}/ssl/

CMD ["app.handler"]
