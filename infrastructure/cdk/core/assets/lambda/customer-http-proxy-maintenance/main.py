import boto3
import json
import os
import logging
from typing import Dict, List, Optional

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
asg_client = boto3.client('autoscaling')
ec2_client = boto3.client('ec2')
route53_client = boto3.client('route53')
sns_client = boto3.client('sns')

def lambda_handler(event, context):
    """
    Handle ASG lifecycle events and manage EIP assignments + DNS records
    """
    try:
        logger.info(f"Processing event: {json.dumps(event)}")

        # Validate required environment variables
        required_env_vars = [
            'TARGET_ASG_NAME', 'SNS_TOPIC_ARN', 'HOSTED_ZONE_ID',
            'HOSTED_ZONE_NAME', 'CUSTOMER_NAME', 'ELASTIC_IPS', 'ELASTIC_IP_IDS'
        ]
        missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

        if missing_vars:
            error_msg = f"Missing required environment variables: {', '.join(missing_vars)}"
            logger.error(error_msg)
            send_notification(error_msg)
            raise ValueError(error_msg)

        target_asg_name = os.environ['TARGET_ASG_NAME']
        customer_name = os.environ['CUSTOMER_NAME']

        # Check if this is an ASG lifecycle event for our target ASG
        if 'source' in event and event['source'] == 'aws.autoscaling':
            detail = event.get('detail', {})
            event_asg_name = detail.get('AutoScalingGroupName', '')

            if event_asg_name == target_asg_name:
                handle_asg_event(event, customer_name)
            else:
                logger.info(f"ASG event for {event_asg_name}, but we're managing {target_asg_name} - ignoring")
        else:
            # This is a scheduled/manual invocation - process our target ASG
            logger.info(f"Processing scheduled maintenance for ASG: {target_asg_name}")
            process_target_asg(target_asg_name, customer_name)

        return {
            'statusCode': 200,
            'body': json.dumps(f'Processing completed successfully for ASG: {target_asg_name}')
        }

    except Exception as e:
        error_msg = f"Lambda execution failed: {str(e)}"
        logger.error(error_msg)
        send_notification(error_msg)
        raise

def handle_asg_event(event, customer_name: str):
    """
    Handle specific ASG lifecycle events
    """
    detail = event.get('detail', {})
    event_type = event.get('detail-type', '')
    asg_name = detail.get('AutoScalingGroupName', '')
    instance_id = detail.get('EC2InstanceId', '')

    logger.info(f"Handling {event_type} for ASG: {asg_name}, Instance: {instance_id}")

    if 'Launch Successful' in event_type:
        handle_instance_launch(instance_id, customer_name)
    elif 'Terminate' in event_type:
        handle_instance_termination(instance_id, customer_name)

    # Update DNS record after any change
    update_dns_for_customer(customer_name)

def process_target_asg(asg_name: str, customer_name: str):
    """
    Process the target ASG for periodic maintenance
    """
    try:
        logger.info(f"Processing ASG: {asg_name} for customer: {customer_name}")

        # Get healthy instances
        healthy_instances = get_healthy_asg_instances(asg_name)

        # Get customer EIPs from environment variables
        customer_eips = get_customer_eips_from_env()

        # Assign EIPs to instances that don't have them
        assign_eips_to_instances(healthy_instances, customer_eips, customer_name)

        # Update DNS record
        update_dns_for_customer(customer_name)

    except Exception as e:
        error_msg = f"Failed to process target ASG {asg_name}: {str(e)}"
        logger.error(error_msg)
        send_notification(error_msg)

def handle_instance_launch(instance_id: str, customer_name: str):
    """
    Handle instance launch - assign available EIP
    """
    logger.info(f"Handling instance launch: {instance_id}")

    try:
        # Wait for instance to be ready first
        import time
        time.sleep(30)

        # Retry logic for finding available EIP (4 minutes with 30-second intervals)
        max_retries = 8  # 8 * 30 seconds = 4 minutes
        retry_count = 0

        while retry_count < max_retries:
            available_eip = find_available_eip_for_customer()

            if available_eip:
                # Associate EIP to instance
                ec2_client.associate_address(
                    AllocationId=available_eip['AllocationId'],
                    InstanceId=instance_id
                )
                logger.info(f"Associated EIP {available_eip['PublicIp']} ({available_eip['AllocationId']}) to instance {instance_id}")
                return

            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"No available EIP found (attempt {retry_count}/{max_retries}), waiting 30 seconds...")
                time.sleep(30)

        error_msg = f"No available EIP found for customer {customer_name} after {max_retries} attempts (4 minutes)"
        logger.error(error_msg)
        send_notification(error_msg)

    except Exception as e:
        error_msg = f"Failed to handle instance launch {instance_id}: {str(e)}"
        logger.error(error_msg)
        send_notification(error_msg)

def handle_instance_termination(instance_id: str, customer_name: str):
    """
    Handle instance termination - EIP will be automatically disassociated
    """
    logger.info(f"Handling instance termination: {instance_id}")

    try:
        # Find EIP that was associated with this instance
        customer_eips = get_customer_eips_from_env()
        for eip in customer_eips:
            if eip.get('InstanceId') == instance_id:
                logger.info(f"EIP {eip['PublicIp']} ({eip['AllocationId']}) was associated with terminated instance {instance_id}")
                # EIP will be automatically disassociated when instance terminates
                break

    except Exception as e:
        error_msg = f"Failed to handle instance termination {instance_id}: {str(e)}"
        logger.error(error_msg)
        send_notification(error_msg)

def get_customer_eips_from_env() -> List[Dict]:
    """
    Get customer EIPs from environment variables
    """
    try:
        eip_ids = os.environ['ELASTIC_IP_IDS'].split(',')
        eip_addresses = os.environ['ELASTIC_IPS'].split(',')

        if len(eip_ids) != len(eip_addresses):
            raise ValueError("Mismatch between ELASTIC_IP_IDS and ELASTIC_IPS counts")

        # Get current EIP status from AWS
        response = ec2_client.describe_addresses(AllocationIds=eip_ids)

        # Return the EIP data
        return response['Addresses']

    except Exception as e:
        logger.error(f"Failed to get customer EIPs from environment: {str(e)}")
        return []

def find_available_eip_for_customer() -> Optional[Dict]:
    """
    Find an available (unassociated) EIP from the configured EIPs
    """
    customer_eips = get_customer_eips_from_env()

    for eip in customer_eips:
        if 'InstanceId' not in eip:  # EIP is not associated
            return eip

    return None

def get_healthy_asg_instances(asg_name: str) -> List[Dict]:
    """
    Get healthy running instances from ASG
    """
    try:
        # Get ASG instances
        asg_response = asg_client.describe_auto_scaling_instances()
        asg_instance_ids = []

        for instance in asg_response['AutoScalingInstances']:
            if (instance['AutoScalingGroupName'] == asg_name and
                instance['LifecycleState'] == 'InService'):
                asg_instance_ids.append(instance['InstanceId'])

        if not asg_instance_ids:
            logger.info(f"No InService instances found in ASG {asg_name}")
            return []

        # Get instance details and health
        instances_response = ec2_client.describe_instances(InstanceIds=asg_instance_ids)

        # Also get instance status (if instances are running)
        running_instance_ids = []
        for reservation in instances_response['Reservations']:
            for instance in reservation['Instances']:
                if instance['State']['Name'] == 'running':
                    running_instance_ids.append(instance['InstanceId'])

        if not running_instance_ids:
            logger.info(f"No running instances found in ASG {asg_name}")
            return []

        # Get health status for running instances
        status_response = ec2_client.describe_instance_status(InstanceIds=running_instance_ids)

        # Build healthy instance IDs set
        healthy_instance_ids = set()
        for status in status_response['InstanceStatuses']:
            if (status['InstanceStatus']['Status'] == 'ok' and
                status['SystemStatus']['Status'] == 'ok'):
                healthy_instance_ids.add(status['InstanceId'])

        # Return healthy running instances
        healthy_instances = []
        for reservation in instances_response['Reservations']:
            for instance in reservation['Instances']:
                if (instance['InstanceId'] in healthy_instance_ids and
                    instance['State']['Name'] == 'running'):
                    healthy_instances.append(instance)

        logger.info(f"Found {len(healthy_instances)} healthy instances in ASG {asg_name}")
        return healthy_instances

    except Exception as e:
        logger.error(f"Failed to get healthy instances for ASG {asg_name}: {str(e)}")
        return []

def assign_eips_to_instances(healthy_instances: List[Dict], customer_eips: List[Dict], customer_name: str):
    """
    Assign EIPs to healthy instances that don't have them
    """
    try:
        import time

        # Find instances without EIPs
        instances_needing_eips = []
        for instance in healthy_instances:
            has_eip = False
            for eip in customer_eips:
                if eip.get('InstanceId') == instance['InstanceId']:
                    has_eip = True
                    break
            if not has_eip:
                instances_needing_eips.append(instance)

        logger.info(f"Found {len(instances_needing_eips)} instances needing EIPs")

        # Try to assign EIPs with retry for each instance
        for instance in instances_needing_eips:
            max_retries = 16  # 16 * 15 seconds = 4 minutes per instance
            retry_count = 0
            instance_assigned = False

            while retry_count < max_retries and not instance_assigned:
                # Refresh EIP status
                current_eips = get_customer_eips_from_env()
                available_eips = [eip for eip in current_eips if 'InstanceId' not in eip]

                if available_eips:
                    try:
                        ec2_client.associate_address(
                            AllocationId=available_eips[0]['AllocationId'],
                            InstanceId=instance['InstanceId']
                        )
                        logger.info(f"Associated EIP {available_eips[0]['PublicIp']} ({available_eips[0]['AllocationId']}) to instance {instance['InstanceId']}")
                        instance_assigned = True
                    except Exception as e:
                        logger.warning(f"Failed to associate EIP {available_eips[0]['AllocationId']} to {instance['InstanceId']}: {str(e)}")

                if not instance_assigned:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.info(f"No available EIPs for {instance['InstanceId']}, waiting 15 seconds... (attempt {retry_count}/{max_retries})")
                        time.sleep(15)

            if not instance_assigned:
                error_msg = f"Failed to assign EIP to instance {instance['InstanceId']} after {max_retries} attempts"
                logger.error(error_msg)
                send_notification(error_msg)

    except Exception as e:
        error_msg = f"Failed to assign EIPs for customer {customer_name}: {str(e)}"
        logger.error(error_msg)
        send_notification(error_msg)

def update_dns_for_customer(customer_name: str):
    """
    Update DNS record with private IPs of healthy instances with EIPs
    """
    try:
        asg_name = os.environ['TARGET_ASG_NAME']
        record_name = f"{customer_name}.proxy.{os.environ['HOSTED_ZONE_NAME']}"

        # Get healthy instances
        healthy_instances = get_healthy_asg_instances(asg_name)

        # Get customer EIPs
        customer_eips = get_customer_eips_from_env()

        # Find private IPs of instances that have EIPs assigned
        private_ips_with_eips = []
        for instance in healthy_instances:
            for eip in customer_eips:
                if eip.get('InstanceId') == instance['InstanceId']:
                    private_ips_with_eips.append(instance['PrivateIpAddress'])
                    break

        if not private_ips_with_eips:
            error_msg = f"No healthy instances with EIPs found for customer {customer_name}"
            logger.warning(error_msg)
            send_notification(error_msg)
            return

        # Update Route53 record
        route53_client.change_resource_record_sets(
            HostedZoneId=os.environ['HOSTED_ZONE_ID'],
            ChangeBatch={
                'Changes': [{
                    'Action': 'UPSERT',
                    'ResourceRecordSet': {
                        'Name': record_name,
                        'Type': 'A',
                        'TTL': 60,
                        'ResourceRecords': [{'Value': ip} for ip in private_ips_with_eips]
                    }
                }]
            }
        )
        logger.info(f"Updated DNS record {record_name} with private IPs: {private_ips_with_eips}")

    except Exception as e:
        error_msg = f"Failed to update DNS for customer {customer_name}: {str(e)}"
        logger.error(error_msg)
        send_notification(error_msg)

def send_notification(message: str):
    """
    Send notification to SNS topic
    """
    try:
        if os.environ.get('SNS_TOPIC_ARN'):
            sns_client.publish(
                TopicArn=os.environ['SNS_TOPIC_ARN'],
                Subject='Customer HTTP Proxy EIP/DNS Management Alert',
                Message=message
            )
            logger.info(f"Sent notification: {message}")
        else:
            logger.warning("SNS_TOPIC_ARN not configured, skipping notification")
    except Exception as e:
        logger.error(f"Failed to send notification: {str(e)}")
