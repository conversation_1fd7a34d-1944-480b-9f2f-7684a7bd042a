import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import os
import sys

class TestLambdaFunction(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        """Set up test environment before importing lambda function"""
        # Set up AWS region to avoid boto3 errors
        os.environ['AWS_DEFAULT_REGION'] = 'us-west-2'
        os.environ['AWS_ACCESS_KEY_ID'] = 'testing'
        os.environ['AWS_SECRET_ACCESS_KEY'] = 'testing'

        # Add the lambda function to the path
        lambda_path = os.path.join(os.path.dirname(__file__), '..', 'lambda', 'customer-http-proxy-maintenance')
        if lambda_path not in sys.path:
            sys.path.insert(0, lambda_path)

    def setUp(self):
        """Set up test environment variables and mocks"""
        self.env_vars = {
            'TARGET_ASG_NAME': 'workdayHttpProxyASG',
            'SNS_TOPIC_ARN': 'arn:aws:sns:us-west-2:123456789012:test-topic',
            'HOSTED_ZONE_ID': 'Z1234567890123',
            'HOSTED_ZONE_NAME': 'example.com',
            'CUSTOMER_NAME': 'workday',
            'ELASTIC_IPS': '************,************',
            'ELASTIC_IP_IDS': 'eipalloc-0b6fdb4b05ed3f05a,eipalloc-02a1c919825b18232',
            'AWS_DEFAULT_REGION': 'us-west-2'
        }

        # Patch environment variables
        self.env_patcher = patch.dict(os.environ, self.env_vars)
        self.env_patcher.start()

        # Import main function after setting up environment
        if 'main' in sys.modules:
            # Reload the module to pick up new environment
            import importlib
            self.main_module = importlib.reload(sys.modules['main'])
        else:
            import main
            self.main_module = main

        # Mock AWS clients
        self.asg_client_patcher = patch.object(self.main_module, 'asg_client')
        self.ec2_client_patcher = patch.object(self.main_module, 'ec2_client')
        self.route53_client_patcher = patch.object(self.main_module, 'route53_client')
        self.sns_client_patcher = patch.object(self.main_module, 'sns_client')

        self.mock_asg_client = self.asg_client_patcher.start()
        self.mock_ec2_client = self.ec2_client_patcher.start()
        self.mock_route53_client = self.route53_client_patcher.start()
        self.mock_sns_client = self.sns_client_patcher.start()

    def tearDown(self):
        """Clean up patches"""
        self.env_patcher.stop()
        self.asg_client_patcher.stop()
        self.ec2_client_patcher.stop()
        self.route53_client_patcher.stop()
        self.sns_client_patcher.stop()

    def test_lambda_handler_missing_env_vars(self):
        """Test lambda handler with missing environment variables"""
        with patch.dict(os.environ, {}, clear=True):
            with patch.object(self.main_module, 'send_notification') as mock_send:
                with self.assertRaises(ValueError):
                    self.main_module.lambda_handler({}, {})
                # Should be called twice - once for validation error, once for lambda execution error
                self.assertEqual(mock_send.call_count, 2)
                # Check that both calls were made with error messages
                calls = mock_send.call_args_list
                self.assertIn('Missing required environment variables', str(calls[0]))
                self.assertIn('Lambda execution failed', str(calls[1]))

    def test_lambda_handler_asg_event_success(self):
        """Test lambda handler with ASG event"""
        event = {
            'source': 'aws.autoscaling',
            'detail-type': 'EC2 Instance Launch Successful',
            'detail': {
                'AutoScalingGroupName': 'workdayHttpProxyASG',
                'EC2InstanceId': 'i-1234567890abcdef0'
            }
        }

        with patch.object(self.main_module, 'handle_asg_event') as mock_handle:
            result = self.main_module.lambda_handler(event, {})
            mock_handle.assert_called_once_with(event, 'workday')
            self.assertEqual(result['statusCode'], 200)

    def test_lambda_handler_wrong_asg_event(self):
        """Test lambda handler ignores events from other ASGs"""
        event = {
            'source': 'aws.autoscaling',
            'detail-type': 'EC2 Instance Launch Successful',
            'detail': {
                'AutoScalingGroupName': 'otherASG',
                'EC2InstanceId': 'i-1234567890abcdef0'
            }
        }

        with patch.object(self.main_module, 'handle_asg_event') as mock_handle:
            result = self.main_module.lambda_handler(event, {})
            mock_handle.assert_not_called()
            self.assertEqual(result['statusCode'], 200)

    def test_lambda_handler_scheduled_event(self):
        """Test lambda handler with scheduled event"""
        event = {'source': 'aws.events'}

        with patch.object(self.main_module, 'process_target_asg') as mock_process:
            result = self.main_module.lambda_handler(event, {})
            mock_process.assert_called_once_with('workdayHttpProxyASG', 'workday')
            self.assertEqual(result['statusCode'], 200)

    def test_get_customer_eips_from_env(self):
        """Test getting EIPs from environment variables"""
        mock_response = {
            'Addresses': [
                {
                    'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
                    'PublicIp': '************'
                },
                {
                    'AllocationId': 'eipalloc-02a1c919825b18232',
                    'PublicIp': '************',
                    'InstanceId': 'i-1234567890abcdef0'
                }
            ]
        }

        self.mock_ec2_client.describe_addresses.return_value = mock_response

        eips = self.main_module.get_customer_eips_from_env()

        self.assertEqual(len(eips), 2)
        self.mock_ec2_client.describe_addresses.assert_called_once_with(
            AllocationIds=['eipalloc-0b6fdb4b05ed3f05a', 'eipalloc-02a1c919825b18232']
        )

    def test_get_customer_eips_from_env_mismatch(self):
        """Test EIP environment variable mismatch"""
        with patch.dict(os.environ, {'ELASTIC_IP_IDS': 'eip1', 'ELASTIC_IPS': 'ip1,ip2'}):
            eips = self.main_module.get_customer_eips_from_env()
            self.assertEqual(eips, [])

    def test_find_available_eip_for_customer(self):
        """Test finding available EIP"""
        mock_eips = [
            {
                'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
                'PublicIp': '************'
            },
            {
                'AllocationId': 'eipalloc-02a1c919825b18232',
                'PublicIp': '************',
                'InstanceId': 'i-1234567890abcdef0'
            }
        ]

        with patch.object(self.main_module, 'get_customer_eips_from_env', return_value=mock_eips):
            available_eip = self.main_module.find_available_eip_for_customer()

            self.assertIsNotNone(available_eip)
            self.assertEqual(available_eip['AllocationId'], 'eipalloc-0b6fdb4b05ed3f05a')
            self.assertNotIn('InstanceId', available_eip)

    def test_find_available_eip_none_available(self):
        """Test finding available EIP when none are available"""
        mock_eips = [
            {
                'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
                'PublicIp': '************',
                'InstanceId': 'i-1234567890abcdef0'
            }
        ]

        with patch.object(self.main_module, 'get_customer_eips_from_env', return_value=mock_eips):
            available_eip = self.main_module.find_available_eip_for_customer()
            self.assertIsNone(available_eip)

    def test_get_healthy_asg_instances(self):
        """Test getting healthy ASG instances"""
        # Mock ASG instances
        mock_asg_response = {
            'AutoScalingInstances': [
                {
                    'InstanceId': 'i-1234567890abcdef0',
                    'AutoScalingGroupName': 'workdayHttpProxyASG',
                    'LifecycleState': 'InService'
                },
                {
                    'InstanceId': 'i-1234567890abcdef1',
                    'AutoScalingGroupName': 'workdayHttpProxyASG',
                    'LifecycleState': 'Pending'
                }
            ]
        }

        # Mock instance details
        mock_instances_response = {
            'Reservations': [
                {
                    'Instances': [
                        {
                            'InstanceId': 'i-1234567890abcdef0',
                            'State': {'Name': 'running'},
                            'PrivateIpAddress': '*********'
                        }
                    ]
                }
            ]
        }

        # Mock instance status
        mock_status_response = {
            'InstanceStatuses': [
                {
                    'InstanceId': 'i-1234567890abcdef0',
                    'InstanceStatus': {'Status': 'ok'},
                    'SystemStatus': {'Status': 'ok'}
                }
            ]
        }

        self.mock_asg_client.describe_auto_scaling_instances.return_value = mock_asg_response
        self.mock_ec2_client.describe_instances.return_value = mock_instances_response
        self.mock_ec2_client.describe_instance_status.return_value = mock_status_response

        healthy_instances = self.main_module.get_healthy_asg_instances('workdayHttpProxyASG')

        self.assertEqual(len(healthy_instances), 1)
        self.assertEqual(healthy_instances[0]['InstanceId'], 'i-1234567890abcdef0')

    def test_handle_instance_launch(self):
        """Test handling instance launch"""
        mock_available_eip = {
            'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
            'PublicIp': '************'
        }

        with patch.object(self.main_module, 'find_available_eip_for_customer', return_value=mock_available_eip):
            with patch('time.sleep'):  # Mock sleep to prevent delays
                self.main_module.handle_instance_launch('i-1234567890abcdef0', 'workday')

                self.mock_ec2_client.associate_address.assert_called_once_with(
                    AllocationId='eipalloc-0b6fdb4b05ed3f05a',
                    InstanceId='i-1234567890abcdef0'
                )

    def test_handle_instance_launch_no_available_eip(self):
        """Test handling instance launch with no available EIP"""
        with patch.object(self.main_module, 'find_available_eip_for_customer', return_value=None):
            with patch.object(self.main_module, 'send_notification') as mock_send:
                with patch('time.sleep'):  # Mock sleep to prevent delays during retry
                    self.main_module.handle_instance_launch('i-1234567890abcdef0', 'workday')

                    mock_send.assert_called_once()
                    self.mock_ec2_client.associate_address.assert_not_called()

    def test_assign_eips_to_instances(self):
        """Test assigning EIPs to instances"""
        healthy_instances = [
            {
                'InstanceId': 'i-1234567890abcdef0',
                'PrivateIpAddress': '*********'
            },
            {
                'InstanceId': 'i-1234567890abcdef1',
                'PrivateIpAddress': '*********'
            }
        ]

        customer_eips = [
            {
                'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
                'PublicIp': '************'
            },
            {
                'AllocationId': 'eipalloc-02a1c919825b18232',
                'PublicIp': '************',
                'InstanceId': 'i-1234567890abcdef1'  # Already assigned
            }
        ]

        # Mock time.sleep to prevent actual delays
        with patch('time.sleep'):
            # Mock get_customer_eips_from_env to return available EIPs
            with patch.object(self.main_module, 'get_customer_eips_from_env', return_value=customer_eips):
                self.main_module.assign_eips_to_instances(healthy_instances, customer_eips, 'workday')

        # Should only associate one EIP (the unassigned one to the first instance)
        self.mock_ec2_client.associate_address.assert_called_once_with(
            AllocationId='eipalloc-0b6fdb4b05ed3f05a',
            InstanceId='i-1234567890abcdef0'
        )

    def test_update_dns_for_customer(self):
        """Test updating DNS for customer"""
        healthy_instances = [
            {
                'InstanceId': 'i-1234567890abcdef0',
                'PrivateIpAddress': '*********'
            },
            {
                'InstanceId': 'i-1234567890abcdef1',
                'PrivateIpAddress': '*********'
            }
        ]

        customer_eips = [
            {
                'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
                'PublicIp': '************',
                'InstanceId': 'i-1234567890abcdef0'
            },
            {
                'AllocationId': 'eipalloc-02a1c919825b18232',
                'PublicIp': '************',
                'InstanceId': 'i-1234567890abcdef1'
            }
        ]

        with patch.object(self.main_module, 'get_healthy_asg_instances', return_value=healthy_instances):
            with patch.object(self.main_module, 'get_customer_eips_from_env', return_value=customer_eips):
                self.main_module.update_dns_for_customer('workday')

                expected_change_batch = {
                    'Changes': [{
                        'Action': 'UPSERT',
                        'ResourceRecordSet': {
                            'Name': 'workday.proxy.example.com',
                            'Type': 'A',
                            'TTL': 60,
                            'ResourceRecords': [
                                {'Value': '*********'},
                                {'Value': '*********'}
                            ]
                        }
                    }]
                }

                self.mock_route53_client.change_resource_record_sets.assert_called_once_with(
                    HostedZoneId='Z1234567890123',
                    ChangeBatch=expected_change_batch
                )

    def test_update_dns_no_healthy_instances_with_eips(self):
        """Test updating DNS with no healthy instances with EIPs"""
        healthy_instances = [
            {
                'InstanceId': 'i-1234567890abcdef0',
                'PrivateIpAddress': '*********'
            }
        ]

        customer_eips = [
            {
                'AllocationId': 'eipalloc-0b6fdb4b05ed3f05a',
                'PublicIp': '************'
                # No InstanceId - not assigned
            }
        ]

        with patch.object(self.main_module, 'get_healthy_asg_instances', return_value=healthy_instances):
            with patch.object(self.main_module, 'get_customer_eips_from_env', return_value=customer_eips):
                with patch.object(self.main_module, 'send_notification') as mock_send:
                    self.main_module.update_dns_for_customer('workday')

                    mock_send.assert_called_once()
                    self.mock_route53_client.change_resource_record_sets.assert_not_called()

    def test_send_notification(self):
        """Test sending SNS notification"""
        self.main_module.send_notification('Test message')

        self.mock_sns_client.publish.assert_called_once_with(
            TopicArn='arn:aws:sns:us-west-2:123456789012:test-topic',
            Subject='Customer HTTP Proxy EIP/DNS Management Alert',
            Message='Test message'
        )

    def test_handle_asg_event_launch(self):
        """Test handling ASG launch event"""
        event = {
            'detail-type': 'EC2 Instance Launch Successful',
            'detail': {
                'AutoScalingGroupName': 'workdayHttpProxyASG',
                'EC2InstanceId': 'i-1234567890abcdef0'
            }
        }

        with patch.object(self.main_module, 'handle_instance_launch') as mock_launch:
            with patch.object(self.main_module, 'update_dns_for_customer') as mock_dns:
                self.main_module.handle_asg_event(event, 'workday')

                mock_launch.assert_called_once_with('i-1234567890abcdef0', 'workday')
                mock_dns.assert_called_once_with('workday')

    def test_handle_asg_event_terminate(self):
        """Test handling ASG terminate event"""
        event = {
            'detail-type': 'EC2 Instance Terminate Successful',
            'detail': {
                'AutoScalingGroupName': 'workdayHttpProxyASG',
                'EC2InstanceId': 'i-1234567890abcdef0'
            }
        }

        with patch.object(self.main_module, 'handle_instance_termination') as mock_terminate:
            with patch.object(self.main_module, 'update_dns_for_customer') as mock_dns:
                self.main_module.handle_asg_event(event, 'workday')

                mock_terminate.assert_called_once_with('i-1234567890abcdef0', 'workday')
                mock_dns.assert_called_once_with('workday')


if __name__ == '__main__':
    unittest.main()
