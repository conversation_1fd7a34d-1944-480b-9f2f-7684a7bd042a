# Makefile for Customer HTTP Proxy Lambda Testing

# Variables
VENV_NAME = venv
PYTHON = python3
PIP = $(VENV_NAME)/bin/pip
PYTHON_VENV = $(VENV_NAME)/bin/python
TEST_PATH = ./
LAMBDA_PATH = lambda/customer-http-proxy-maintenance/
REQUIREMENTS_FILE = requirements.txt

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[0;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

.PHONY: help setup clean test test-verbose coverage format lint check install-deps create-structure all

# Default target
all: setup test

help: ## Show this help message
	@echo "$(BLUE)Customer HTTP Proxy Lambda Testing$(NC)"
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Create virtual environment and install dependencies
	@echo "$(BLUE)Setting up virtual environment...$(NC)"
	$(PYTHON) -m venv $(VENV_NAME)
	$(PIP) install --upgrade pip
	$(PIP) install -r $(REQUIREMENTS_FILE)
	@echo "$(GREEN)Virtual environment setup complete!$(NC)"

create-structure: ## Create project directory structure
	@echo "$(BLUE)Creating project structure...$(NC)"
	mkdir -p $(LAMBDA_PATH)
	mkdir -p $(TEST_PATH)
	mkdir -p lambda/customer-http-proxy-maintenance
	@echo "$(GREEN)Project structure created!$(NC)"

install-deps: ## Install Python dependencies
	@echo "$(BLUE)Installing dependencies...$(NC)"
	$(PIP) install boto3 pytest pytest-cov pytest-mock flake8 black
	@echo "$(GREEN)Dependencies installed!$(NC)"

test: ## Run unit tests
	@echo "$(BLUE)Running unit tests...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) -v
	@echo "$(GREEN)Tests completed!$(NC)"

test-verbose: ## Run unit tests with verbose output
	@echo "$(BLUE)Running unit tests (verbose)...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) -v -s

test-specific: ## Run specific test (usage: make test-specific TEST=test_name)
	@echo "$(BLUE)Running specific test: $(TEST)$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) -v -k "$(TEST)"

coverage: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) --cov=$(LAMBDA_PATH) --cov-report=html --cov-report=term-missing
	@echo "$(GREEN)Coverage report generated in htmlcov/$(NC)"

coverage-xml: ## Generate XML coverage report
	@echo "$(BLUE)Generating XML coverage report...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) --cov=$(LAMBDA_PATH) --cov-report=xml

lint: ## Run code linting with flake8
	@echo "$(BLUE)Running code linting...$(NC)"
	$(PYTHON_VENV) -m flake8 $(LAMBDA_PATH) $(TEST_PATH) --max-line-length=120 --ignore=E203,W503

format: ## Format code with black
	@echo "$(BLUE)Formatting code with black...$(NC)"
	$(PYTHON_VENV) -m black $(LAMBDA_PATH) $(TEST_PATH) --line-length=120

format-check: ## Check code formatting without changing files
	@echo "$(BLUE)Checking code formatting...$(NC)"
	$(PYTHON_VENV) -m black $(LAMBDA_PATH) $(TEST_PATH) --line-length=120 --check

check: lint format-check ## Run all code quality checks

test-ci: ## Run tests suitable for CI/CD
	@echo "$(BLUE)Running CI tests...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) --cov=$(LAMBDA_PATH) --cov-report=xml --cov-fail-under=80

watch: ## Run tests in watch mode (requires pytest-watch)
	@echo "$(BLUE)Running tests in watch mode...$(NC)"
	$(PIP) install pytest-watch
	$(PYTHON_VENV) -m ptw $(TEST_PATH) $(LAMBDA_PATH)

clean: ## Clean up generated files and virtual environment
	@echo "$(YELLOW)Cleaning up...$(NC)"
	rm -rf $(VENV_NAME)
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	rm -rf coverage.xml
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	@echo "$(GREEN)Cleanup complete!$(NC)"

rebuild: clean setup ## Clean and rebuild environment
	@echo "$(GREEN)Environment rebuilt!$(NC)"

# Development helpers
dev-setup: setup install-deps ## Complete development setup
	@echo "$(GREEN)Development environment ready!$(NC)"

quick-test: ## Quick test run (no coverage)
	@echo "$(BLUE)Running quick tests...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) -x -v

# Lambda-specific targets
package-lambda: ## Package lambda function for deployment
	@echo "$(BLUE)Packaging lambda function...$(NC)"
	cd $(LAMBDA_PATH) && zip -r ../../../lambda-package.zip .
	@echo "$(GREEN)Lambda package created: lambda-package.zip$(NC)"

validate-lambda: ## Validate lambda function syntax
	@echo "$(BLUE)Validating lambda function...$(NC)"
	$(PYTHON_VENV) -m py_compile $(LAMBDA_PATH)/lambda_function.py
	@echo "$(GREEN)Lambda function syntax is valid!$(NC)"

# Show current environment info
info: ## Show environment information
	@echo "$(BLUE)Environment Information:$(NC)"
	@echo "Python version: $$($(PYTHON) --version)"
	@echo "Virtual environment: $(VENV_NAME)"
	@echo "Lambda path: $(LAMBDA_PATH)"
	@echo "Test path: $(TEST_PATH)"
	@if [ -d "$(VENV_NAME)" ]; then \
		echo "$(GREEN)Virtual environment exists$(NC)"; \
		echo "Installed packages:"; \
		$(PIP) list | head -10; \
	else \
		echo "$(RED)Virtual environment not found$(NC)"; \
	fi
