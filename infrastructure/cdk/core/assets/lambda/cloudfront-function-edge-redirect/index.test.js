const sut = require('./index');
const assert = require('assert').strict;
const testEventTemplate = require('./app-event.json');

test('ignore paths with file extension', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/_next/static/media/landing-hero.d494c262.png';
    const result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/_next/static/media/landing-hero.d494c262.png');
});

test('ignore paths with trailing slash', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/';
    let result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/');

    testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/blog/';
    result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/blog/');
});

test('invalid path without trailing slash and query params - expect 301', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.querystring = {
        test: { value: 'true' },
        arg: { value: 'val1', multivalue: [{ value: 'val1' }, { value: 'val2' }] },
    };
    testEvent.request.uri = '/_next/static/media';
    let result = sut(testEvent);
    let location = result.headers.location.value;
    console.log(location);
    assert.equal(result.statusCode, 301);
    assert.equal(location, '/_next/static/media/?test=true&arg=val1&arg=val2');

    testEvent = clone(testEventTemplate);
    testEvent.request.querystring = {
        test: { value: 'true' },
        arg: { value: 'val1', multivalue: [{ value: 'val3' }, { value: 'val4' }] },
    };
    testEvent.request.uri = '/blog';
    result = sut(testEvent);
    assert.equal(result.statusCode, 301);
    location = result.headers.location.value;
    assert.equal(location, '/blog/?test=true&arg=val3&arg=val4');
});

test('www redirect to domain apex', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/blog';

    testEvent.request.headers.host.value = 'www.getunblocked.com';
    let result = sut(testEvent);
    let location = result.headers.location.value;
    assert.equal(result.statusCode, 301);
    assert.equal(location, 'https://getunblocked.com/blog');
});

test('www redirect to domain apex with query params', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/blog';
    testEvent.request.querystring = {
        test: { value: 'true' },
        arg: { value: 'val1', multivalue: [{ value: 'val1' }, { value: 'val2' }] },
    };

    testEvent.request.headers.host.value = 'www.getunblocked.com';
    let result = sut(testEvent);
    let location = result.headers.location.value;
    console.log(result);
    assert.equal(result.statusCode, 301);
    assert.equal(location, 'https://getunblocked.com/blog?test=true&arg=val1&arg=val2');
});

test('do not redirect .well-known OAuth paths', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/.well-known/oauth-authorization-server';
    let result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/.well-known/oauth-authorization-server');

    testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/.well-known/oauth-protected-resource';
    result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/.well-known/oauth-protected-resource');
});

test('normalize .well-known OAuth paths with trailing segments', async () => {
    let testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/.well-known/oauth-authorization-server/api/mcpsse';
    let result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/.well-known/oauth-authorization-server');

    testEvent = clone(testEventTemplate);
    testEvent.request.uri = '/.well-known/oauth-protected-resource/some/extra/path';
    result = sut(testEvent);
    assert.equal(result.statusCode, undefined);
    assert.equal(result.uri, '/.well-known/oauth-protected-resource');
});

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
