function handler(event) {
    // Extract the request from the CloudFront event that is sent to the function
    const request = event.request;

    // Construct the query string
    const queryString = constructQueryString(request.querystring);

    // Redirect invite urls
    if (request.uri.startsWith('/team/') && request.uri.includes('/invite/')) {
        const pathSegments = request.uri.split('/');
        if (pathSegments.length >= 5 && pathSegments.length <= 6 && pathSegments[3] === 'invite') {
            const teamId = pathSegments[2];
            const inviteId = pathSegments[4];
            const redirectTo = `/invite?teamId=${teamId}&invite=${inviteId}`;

            const response = {
                statusCode: 301,
                statusDescription: 'Redirecting to invite endpoint',
                headers: {
                    location: { value: `https://${request.headers.host.value}${redirectTo}` },
                },
            };
            return response;
        }
    }

    // Redirect to domain apex
    if (request.headers.host && request.headers.host.value.startsWith('www.')) {
        let domain = request.headers.host.value.replace('www.', '');
        const response = {
            statusCode: 301,
            statusDescription: 'Redirecting to apex domain',
            headers: {
                location: { value: `https://${domain}${request.uri}${queryString}` },
            },
        };
        return response;
    }

    // If requested url does not have an extension or trailing slash, return 301!
    // Exception: .well-known OAuth paths should not be redirected as they are meant to be served as-is
    const oldUri = request.uri;
    if (!hasFileExtension(oldUri) && !oldUri.endsWith('/') && !isWellKnownOAuthPath(oldUri)) {
        const response = {
            statusCode: 301,
            statusDescription: 'Moved Permanently',
            headers: {
                location: { value: `${oldUri}/${queryString}` },
            },
        };
        return response;
    }

    if (isWellKnownOAuthPath(oldUri)) {
        // Normalize OAuth well-known paths by removing any trailing segments
        request.uri = normalizeWellKnownOAuthPath(oldUri);
        request.headers['content-type'] = { value: 'application/json' };
    }

    // Return to CloudFront
    return request;
}

function constructQueryString(querystring) {
    var queryParams = [];
    for (var key in querystring) {
        if (querystring.hasOwnProperty(key)) {
            var valueObject = querystring[key];
            if (valueObject.multivalue) {
                for (var i = 0; i < valueObject.multivalue.length; i++) {
                    queryParams.push(
                        encodeURIComponent(key) + '=' + encodeURIComponent(valueObject.multivalue[i].value)
                    );
                }
            } else {
                queryParams.push(encodeURIComponent(key) + '=' + encodeURIComponent(valueObject.value));
            }
        }
    }
    return queryParams.length > 0 ? '?' + queryParams.join('&') : '';
}

function hasFileExtension(fileName) {
    var pattern = /\.[0-9a-z]+$/i;
    var hasExtension = pattern.test(fileName);
    return hasExtension;
}

function isWellKnownOAuthPath(uri) {
    return (
        uri.includes('/.well-known/oauth-authorization-server') || uri.includes('/.well-known/oauth-protected-resource')
    );
}

function normalizeWellKnownOAuthPath(uri) {
    if (uri.includes('/.well-known/oauth-authorization-server')) {
        const index = uri.indexOf('/.well-known/oauth-authorization-server');
        return uri.substring(0, index + '/.well-known/oauth-authorization-server'.length);
    }
    if (uri.includes('/.well-known/oauth-protected-resource')) {
        const index = uri.indexOf('/.well-known/oauth-protected-resource');
        return uri.substring(0, index + '/.well-known/oauth-protected-resource'.length);
    }
    return uri;
}

// Needed so local tests could run!
if (console.error) {
    module.exports = handler;
}
