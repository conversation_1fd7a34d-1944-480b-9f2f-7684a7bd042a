'use strict';
/*
 How to run:
    1- install dependencies `npm install`
    2- run local tests `npm test`
 */
const assert = require('assert').strict;
const https = require('https');
const lambdaLocal = require('lambda-local');
const path = require('path');
const testEventTemplate = require('./app-event.json');

describe('Test', function () {
    it('valid domain apex url', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'getunblocked.com';
        assert.equal((await runLambda(testEvent)).headers.host[0].value, 'getunblocked.com');
    });

    it('domain with www prefix - expects 301 status', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'www.getunblocked.com';
        testEvent.Records[0].cf.request.uri = '';
        assert.equal((await run<PERSON>ambda(testEvent)).status, '301');
        assert.equal((await runLambda(testEvent)).headers.location[0].value, 'https://getunblocked.com');
    });

    it('valid domain with www in path', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'getunblocked.com/www';
        assert.equal((await runLambda(testEvent)).headers.host[0].value, 'getunblocked.com/www');
    });

    it('domain with www prefix and in path - expects 301 status', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'www.getunblocked.com';
        testEvent.Records[0].cf.request.uri = '/www';
        assert.equal((await runLambda(testEvent)).status, '301');
        assert.equal((await runLambda(testEvent)).headers.location[0].value, 'https://getunblocked.com/www');
    });

    it('valid subdomain apex url', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'dev.getunblocked.com';
        assert.equal((await runLambda(testEvent)).headers.host[0].value, 'dev.getunblocked.com');
    });

    it('subdomain with www prefix and in path - expects 301 status', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'www.dev.getunblocked.com';
        testEvent.Records[0].cf.request.uri = '/www';
        assert.equal((await runLambda(testEvent)).status, '301');
        assert.equal((await runLambda(testEvent)).headers.location[0].value, 'https://dev.getunblocked.com/www');
    });

    it('subdomain with wwww (four)', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.headers.host[0].value = 'wwww.dev.getunblocked.com';
        assert.equal((await runLambda(testEvent)).headers.host[0].value, 'wwww.dev.getunblocked.com');
    });
});

// Runs lambda function with provided event object
async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
