'use strict';

exports.handler = (event, context, callback) => {
    // Extract the request from the CloudFront event that is sent to Lambda@Edge
    const request = event.Records[0].cf.request;

    console.log('event: ' + JSON.stringify(event));
    console.log('request: ' + JSON.stringify(request));

    // Redirect to domain apex
    if (request.headers.host[0].value.startsWith('www.')) {
        let domain = request.headers.host[0].value.replace('www.', '');
        const response = {
            status: '301',
            statusDescription: `Redirecting to apex domain`,
            headers: {
                location: [
                    {
                        key: 'Location',
                        value: `https://${domain}${request.uri}`,
                    },
                ],
            },
        };
        callback(null, response);
    }

    // Return to CloudFront
    return callback(null, request);
};
