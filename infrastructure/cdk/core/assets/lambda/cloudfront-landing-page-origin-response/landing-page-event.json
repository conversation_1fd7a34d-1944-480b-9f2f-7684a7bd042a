{"Records": [{"cf": {"config": {"distributionDomainName": "d1gz1415gsret9.cloudfront.net", "distributionId": "E1U394B5JV5R7Z", "eventType": "origin-response", "requestId": "WpnSvX5u1ZXlSh3gi8UcRITNAoMtgCBmQSs99wY5PkcIGpUoU1X4AA=="}, "request": {"clientIp": "*************", "headers": {"x-forwarded-for": [{"key": "X-Forwarded-For", "value": "*************"}], "user-agent": [{"key": "User-Agent", "value": "Amazon CloudFront"}], "via": [{"key": "Via", "value": "2.0 1d000d0dfe9d69b4983f619fdc5499d6.cloudfront.net (CloudFront)"}], "accept-encoding": [{"key": "Accept-Encoding", "value": "br,gzip"}], "host": [{"key": "Host", "value": "landing-page.dev.getunblocked.com.s3.us-east-1.amazonaws.com"}]}, "method": "GET", "origin": {"s3": {"authMethod": "origin-access-identity", "customHeaders": {}, "domainName": "landing-page.dev.getunblocked.com.s3.us-east-1.amazonaws.com", "path": "", "region": "us-east-1"}}, "querystring": "", "uri": "/team/blah/invite"}, "response": {"headers": {"x-amz-request-id": [{"key": "x-amz-request-id", "value": "K74SHJYD63NXGX59"}], "x-amz-id-2": [{"key": "x-amz-id-2", "value": "X0Qe8uIAEgG5GsGiZJ/sziLrtTArLvHbc52avzs0XRwpMgZC6eWxx2XLLVSfdDVXTcfZdcTkkkw="}], "date": [{"key": "Date", "value": "Mon, 30 May 2022 19:45:25 GMT"}], "server": [{"key": "Server", "value": "AmazonS3"}], "content-type": [{"key": "Content-Type", "value": "application/xml"}], "transfer-encoding": [{"key": "Transfer-Encoding", "value": "chunked"}]}, "status": "403", "statusDescription": "Forbidden"}}}]}