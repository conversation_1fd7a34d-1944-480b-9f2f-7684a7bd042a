const assert = require('assert').strict;
const testEventTemplate = require('./app-event.json');
const lambdaLocal = require('lambda-local');
const landingPageEventTemplate = require('./landing-page-event.json');
const path = require('path');
const nonceTestEventTemplate = require('./nonce-test-event.json');

describe('Test', function () {
    it('reroutes to dashboard index file', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dashboard/discussions/mine';
        assert.equal((await runLambda(testEvent)).headers['x-lambda-path'][0].value, '/dashboard/index.html');
    });

    it('reroutes to landing page index file', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/team/blah/invite';
        assert.equal((await runLambda(testEvent)).headers['x-lambda-path'][0].value, '/index.html');
    });

    it('Valid http response with valid /index.html - expects 200', async function () {
        let testEvent = clone(nonceTestEventTemplate);
        assert.equal((await runLambda(testEvent)).status, '200');
        assert.equal((await runLambda(testEvent)).body.includes('nonce'), true);
        assert.ok((await runLambda(testEvent)).body.match(/nonce="[^"]*"/));
    });

    it('Valid http response with valid root valid html - expects 200', async function () {
        let testEvent = clone(nonceTestEventTemplate);
        testEvent.Records[0].cf.request.uri = '/';
        assert.equal((await runLambda(testEvent)).status, '200');
        assert.equal((await runLambda(testEvent)).body.includes('nonce'), true);
        assert.ok((await runLambda(testEvent)).body.match(/nonce="[^"]*"/));
        assert.ok(
            (await runLambda(testEvent)).body.includes('<link rel="preload" href="dummylink" as="script" nonce="')
        );
        assert.ok((await runLambda(testEvent)).body.includes('<link rel="preload" href="dummylink2"/>'));
        assert.ok((await runLambda(testEvent)).body.includes('<script id="data-google-tag-manager" data-nonce="'));
    });

    it('Valid http response with valid root valid html - expects 200', async function () {
        let testEvent = clone(nonceTestEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dummyNonHTML';
        assert.equal((await runLambda(testEvent)).status, '200');
        assert.equal((await runLambda(testEvent)).body.includes('nonce'), false);
        assert.equal((await runLambda(testEvent)).body, 'NonHTMLResponseBody');
    });
});

async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
