{"Records": [{"cf": {"config": {"distributionId": "EXAMPLEDISTRIBUTIONID"}, "request": {"method": "GET", "uri": "/index.html", "querystring": "", "headers": {"host": [{"key": "Host", "value": "localhost"}], "user-agent": [{"key": "User-Agent", "value": "Mozilla/5.0 ..."}]}}, "response": {"status": "200", "statusDescription": "OK", "headers": {"content-type": [{"key": "Content-Type", "value": "text/html"}], "content-length": [{"key": "Content-Length", "value": "1234"}]}, "body": "NonHTMLResponseBody"}}}]}