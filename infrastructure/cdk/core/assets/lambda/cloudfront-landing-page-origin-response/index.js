'use strict';

// Source - https://andrewlock.net/using-lambda-at-edge-to-handle-angular-client-side-routing-with-s3-and-cloudfront/

const http = require('https');
const path = require('path');
const fs = require('fs');
const { GetObjectCommand, S3Client } = require('@aws-sdk/client-s3');
const config = require('./config.json');

const indexPage = 'index.html';

exports.handler = async (event, context, callback) => {
    const cf = event.Records[0].cf;
    const request = cf.request;
    const response = cf.response;
    const statusCode = response.status;

    //console.log('event: ' + JSON.stringify(event));
    //console.log('request: ' + JSON.stringify(request));

    // Do not manipulate API responses
    const isApiRequest = request.uri.startsWith('/api');

    // Only replace 404 requests typically received
    // when loading a page for a SPA that uses client-side routing
    const doReplace = request.method === 'GET' && statusCode === '404' && !isApiRequest;

    const result = doReplace
        ? await generateResponseAndLog(cf, request, indexPage, response)
        : await addCspNonceToContent(event);

    //console.log('response: ' + JSON.stringify(result));
    callback(null, result);
};

function isLandingPageRequest(request) {
    if (request.headers && request.headers.host && request.headers.host.length && request.headers.host.length > 0) {
        if (request.headers.host[0].value.includes('landing-page')) {
            return true;
        }
    }

    return false;
}

async function generateResponseAndLog(cf, request, indexPage, originalResponse) {
    const domain = cf.config.distributionDomainName;
    let indexPath;
    if (isLandingPageRequest(request)) {
        indexPath = `/${indexPage}`;
    } else {
        const appPath = getAppPath(request.uri);
        indexPath = `/${appPath}/${indexPage}`;
    }

    const response = await generateResponse(domain, indexPath, request, originalResponse);
    console.log(
        `For request: ${JSON.stringify(request)}, the resolved indexPath: ${indexPath}, the response: ${JSON.stringify(
            response
        )}`
    );
    return response;
}

async function generateResponse(domain, path, request, originalResponse) {
    try {
        // Load HTML index from the CloudFront cache
        const s3Response = await httpGet({
            hostname: domain,
            path: path,
            timeout: 5000,
            headers: {
                'x-secret-sauce': 'rashinisawesome',
            },
        });

        const headers = s3Response.headers || {
            'content-type': [{ value: 'text/html;charset=UTF-8' }],
        };

        const responseHeaders = wrapAndFilterHeaders(headers, originalResponse.headers || {});

        // Debug headers to see the original requested URL vs the index file request.
        responseHeaders['x-lambda-request-uri'] = [{ value: request.uri }];
        responseHeaders['x-lambda-hostname'] = [{ value: domain }];
        responseHeaders['x-lambda-path'] = [{ value: path }];
        responseHeaders['x-lambda-response-status'] = [{ value: String(s3Response.status) }];

        return {
            status: '200',
            headers: responseHeaders,
            body: s3Response.body,
        };
    } catch (error) {
        console.log(
            `Failed to generate response for request: ${JSON.stringify(
                request
            )} and path: ${path} with originalResponse: ${JSON.stringify(originalResponse)}`,
            error
        );
        return {
            status: '500',
            headers: {
                'content-type': [{ value: 'text/plain' }],
            },
            body: 'An error occurred loading the page',
        };
    }
}

function httpGet(params) {
    return new Promise((resolve, reject) => {
        http.get(params, (resp) => {
            console.log(`Fetching ${params.hostname}${params.path}, status code : ${resp.statusCode}`);
            let result = {
                status: resp.statusCode,
                headers: resp.headers,
                body: '',
            };
            resp.on('data', (chunk) => {
                result.body += chunk;
                console.log(result);
            });
            resp.on('end', () => {
                resolve(result);
            });
        })
            .on('error', (err) => {
                console.log(`Couldn't fetch ${params.hostname}${params.path} : ${err.message}`);
                reject(err, null);
            })
            .on('timeout', () => {
                console.log(`Couldn't fetch ${params.hostname}${params.path} due to timeout`);
                reject(new Error('Request timed out'), null);
            });
    });
}

// Get the app path segment e.g. candidates.app, employers.client etc
function getAppPath(path) {
    if (!path) {
        return '';
    }

    if (path[0] === '/') {
        path = path.slice(1);
    }

    const segments = path.split('/');

    // will always have at least one segment (may be empty)
    return segments[0];
}

// Cloudfront requires header values to be wrapped in an array
function wrapAndFilterHeaders(headers, originalHeaders) {
    const allowedHeaders = ['content-type', 'content-length', 'last-modified', 'date', 'etag'];

    const responseHeaders = originalHeaders;

    if (!headers) {
        return responseHeaders;
    }

    for (var propName in headers) {
        // only include allowed headers
        if (allowedHeaders.includes(propName.toLowerCase())) {
            var header = headers[propName];

            if (Array.isArray(header)) {
                // assume already 'wrapped' format
                responseHeaders[propName] = header;
            } else {
                // fix to required format
                responseHeaders[propName] = [{ value: header }];
            }
        }
    }

    return responseHeaders;
}

function addNoncesToHtml(html, nonce) {
    // Add nonce attribute to script tags
    const modifiedHtmlWithNonces = html.replace(/<script[^>]*(?:nonce="[^"]*")?[^>]*>/g, (match) => {
        if (!match.includes('nonce=')) {
            // Special case to handle GTM
            // Adds data-nonce and regular nonce
            if (match.includes('id="data-google-tag-manager"')) {
                return match.replace('>', ` data-nonce="${nonce}" nonce="${nonce}">`);
            }

            return match.replace('>', ` nonce="${nonce}">`);
        } else {
            return match;
        }
    });

    // Add nonce attribute to script tags link tags
    const finalResult = modifiedHtmlWithNonces.replace(/<link[^>]*(?:nonce="[^"]*")?[^>]*>/g, (match) => {
        if (!match.includes('nonce=') && match.includes('as="script"')) {
            // Special case to handle GTM
            // Adds data-nonce and regular nonce
            if (match.includes('id="data-google-tag-manager"')) {
                return match.replace('>', ` data-nonce="${nonce}" nonce="${nonce}">`);
            }

            return match.replace('/>', ` nonce="${nonce}"/>`);
        } else {
            return match;
        }
    });

    return finalResult.toString();
}

function generateRandomNonce() {
    // Generate a random nonce (you can implement your own random string generation logic here)
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const nonceLength = 16;
    let nonce = '';
    for (let i = 0; i < nonceLength; i++) {
        nonce += characters[Math.floor(Math.random() * characters.length)];
    }
    return nonce;
}

async function addCspNonceToContent(event) {
    const request = event.Records[0].cf.request;
    const response = event.Records[0].cf.response;
    const host = request.headers['host'][0].value;
    const uri = request.uri;

    const bucket = config[host]['bucket'];
    const region = config[host]['region'];
    const cacheTtl = config[host]['cache_ttl_seconds'];

    // Check if the request path is root directory or ends with .html
    if (uri === '/' || uri.endsWith('.html')) {
        // Generate a random nonce
        const nonce = generateRandomNonce();

        const streamToString = (stream) =>
            new Promise((resolve, reject) => {
                const chunks = [];
                stream.on('data', (chunk) => chunks.push(chunk));
                stream.on('error', reject);
                stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
            });

        try {
            let objectContent = '';
            if (host.includes('localhost')) {
                // Read html test file
                objectContent = fs.readFileSync('./test.html', 'utf-8');
            } else {
                const s3Client = new S3Client({
                    region: region,
                });

                const getObjectCommand = new GetObjectCommand({
                    Bucket: bucket,
                    Key: uri.substring(1),
                });

                const objectResponse = await s3Client.send(getObjectCommand);
                objectContent = await objectResponse.Body.transformToString('utf-8');
            }

            // Apply nonce changes to the HTML content
            const modifiedHtml = addNoncesToHtml(objectContent, nonce);
            const modifiedHtmlString = typeof modifiedHtml === 'string' ? modifiedHtml : modifiedHtml.toString();
            // Set the modified HTML content as the response body
            response.status = '200';
            response.statusDescription = 'OK';
            response.body = modifiedHtmlString;
            response.headers['content-length'] = [{ key: 'Content-Length', value: modifiedHtml.length.toString() }];
            response.headers['content-type'] = [{ key: 'Content-Type', value: 'text/html' }];
            response.headers['x-custom-header'] = [{ key: 'X-Custom-Header', value: `${nonce}` }];

            // Set CORS headers
            response.headers['access-control-allow-origin'] = [{ key: 'Access-Control-Allow-Origin', value: '*' }];
            response.headers['access-control-allow-headers'] = [{ key: 'Access-Control-Allow-Headers', value: '*' }];

            // Set a short TTL of 10 minutes in the cache
            response.headers['cache-control'] = [{ key: 'Cache-Control', value: `max-age=${cacheTtl}` }];
        } catch (error) {
            // Handle any errors that occur during the process
            console.error('Error:', error);
            response.status = 500;
            response.statusDescription = 'Internal Server Error';
            response.body = 'An error occurred while processing the request.';
            response.headers['content-length'] = [{ key: 'Content-Length', value: response.body.length.toString() }];
            response.headers['content-type'] = [{ key: 'Content-Type', value: 'text/plain' }];
        }
    }

    return response;
}
