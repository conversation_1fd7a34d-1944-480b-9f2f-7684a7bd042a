import boto3
import os
import dateutil.parser
from datetime import datetime, timedelta
from dateutil import tz

RETENTION_DAYS = int(os.getenv('RETENTION_DAYS'))
def lambda_handler(event, context):

    # Set up RDS client
    rds = boto3.client('rds')

    # Get all RDS cluster snapshots
    snapshots = rds.describe_db_cluster_snapshots(SnapshotType='manual')['DBClusterSnapshots']

    # Get current timestamp with timezone information
    now = datetime.now(tz.tzutc())

    # Iterate through snapshots
    for snapshot in snapshots:

        # Get snapshot creation time and age in days
        snapshot_time_str = str(snapshot['SnapshotCreateTime'])
        snapshot_time = dateutil.parser.parse(snapshot_time_str).replace(microsecond=0, tzinfo=tz.tzutc())
        age = (now - snapshot_time).days

        # Check if snapshot is older than 30 days and does not have retain=true tag
        if age > 30 and not any(tag['Key'] == 'retain' and tag['Value'] == 'true' for tag in snapshot['TagList']):

            # Delete snapshot
            print(f"Snapshot {snapshot['DBClusterSnapshotIdentifier']} will be deleted")
            rds.delete_db_cluster_snapshot(DBClusterSnapshotIdentifier=snapshot['DBClusterSnapshotIdentifier'])
