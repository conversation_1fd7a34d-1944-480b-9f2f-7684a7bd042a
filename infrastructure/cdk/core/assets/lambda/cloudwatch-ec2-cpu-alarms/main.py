"""
We are required to configure CPUUtilization for all EC2 resources by SOC2 framework. Failing to do so causes Drata
monitoring checks to fail. Additionally, some CDK elements such as Managed Compute Environment (for batch jobs) do not
provide necessary resource access to create CPU alarms within CDK code. This is a workaround to deal with such cases.

- This script creates CPUUtilization alarms for ECS clusters with the tag "AWSBatchServiceTag" in the AWS/ECS namespace.
- It also creates CPUUtilization for autoscaling groups that do not have "AWSBatchServiceTag" or "AmazonECSManaged" tags.
- Each alarm is configured with a timestamp tag that is updated every time lambda function runs (every hour).
- Alarms created by this code which have timestamps exceeding 24 hours (e.g an alarm for deleted ECS cluster) are deleted.

Local testing:
    python3 main.py -region us-west-2 -sns-topic-arn arn:aws:sns:us-west-2:************:DummyTopic -aws-account-id ************

"""

from __future__ import print_function

import argparse
import boto3
import os
from datetime import datetime, timezone

def handler(event, context):

    # Get the region from environment variable
    region = os.environ.get('AWS_REGION')

    DRYRUN = True
    if os.environ.get('DRYRUN', "false").lower() == "false":
        DRYRUN = False

    # Get the SNS topic ARN from environment variable
    sns_topic_arn = os.environ.get('SNS_TOPIC_ARN')

    aws_account_id = os.environ.get('AWS_ACCOUNT_ID')

    # Current timestamp in Unix format
    current_timestamp = datetime.now(timezone.utc).timestamp()

    # Create/Delete alarms for non-ECS managed autoscaling groups
    nonEcsAutoscalingGroupCpuAlarms(region, sns_topic_arn, aws_account_id, current_timestamp, DRYRUN)

    # Create/Delete alarms for batch ECS clusters for managed compute environments
    ecsBatchClusterAlarms(region, sns_topic_arn, aws_account_id, current_timestamp, DRYRUN)

    deleteOldAlarms(region, DRYRUN)

def updateTimestamp(alarm_name, region, aws_account_id, current_timestamp, cloudwatch_client, DRYRUN):
    # Update timestamp tag of the existing alarm
    print(f"Updating timestamp tag for alarm: {alarm_name}")
    if not DRYRUN:
        cloudwatch_client.tag_resource(
            ResourceARN=f"arn:aws:cloudwatch:{region}:{aws_account_id}:alarm:{alarm_name}",
            Tags=[
                {'Key': 'timestamp', 'Value': str(current_timestamp)}
            ]
        )

def deleteOldAlarms(region, DRYRUN):
    # Initialize AWS client
    cloudwatch_client = boto3.client('cloudwatch', region_name=region)

    # Current timestamp in Unix format
    current_timestamp = datetime.now(timezone.utc).timestamp()

    # Get all alarms
    paginator = cloudwatch_client.get_paginator('describe_alarms')
    for page in paginator.paginate():
        for alarm in page['MetricAlarms']:
            # Check if the alarm has the 'CreateByAsgAlarmLambda' tag
            tags = cloudwatch_client.list_tags_for_resource(ResourceARN=alarm['AlarmArn'])['Tags']
            has_create_tag = any(tag['Key'] == 'CreateByAsgAlarmLambda' for tag in tags)

            if has_create_tag:
                # Get the timestamp value from the tags
                timestamp_tag = next((tag['Value'] for tag in tags if tag['Key'] == 'timestamp'), None)

                # Delete the alarm if the timestamp is more than a day old
                if timestamp_tag:
                    timestamp = float(timestamp_tag)
                    if current_timestamp - timestamp >= 24 * 3600:
                        print(f"Deleting alarm: {alarm['AlarmName']}")
                        if not DRYRUN:
                            cloudwatch_client.delete_alarms(AlarmNames=[alarm['AlarmName']])


def ecsBatchClusterAlarms(region, sns_topic_arn, aws_account_id, current_timestamp, DRYRUN):
    # Initialize AWS clients
    cloudwatch_client = boto3.client('cloudwatch', region_name=region)
    ecs_client = boto3.client('ecs', region_name=region)

    #### Create CPU alarm for batch ECS cluster created by
    # List all ECS clusters
    clusters = ecs_client.list_clusters()['clusterArns']

    for cluster_arn in clusters:
        cluster_name = cluster_arn.split('/')[-1]

        # Check if the cluster has the required tag
        response = ecs_client.list_tags_for_resource(resourceArn=cluster_arn)
        tags = response['tags']
        has_batch_tag = False
        for tag in tags:
            if tag['key'] == 'AWSBatchServiceTag':
                has_batch_tag = True
                break

        if has_batch_tag:
            # Create CPUUtilization alarm for the ECS cluster
            alarm_name = f"{cluster_name}-CPUUtilization-Alarm"

            existing_alarms = cloudwatch_client.describe_alarms(AlarmNames=[alarm_name])['MetricAlarms']

            if existing_alarms:
                updateTimestamp(alarm_name, region, aws_account_id, current_timestamp, cloudwatch_client, DRYRUN)
            else:
                print(f"Creating {alarm_name}")
                if not DRYRUN:
                    cloudwatch_client.put_metric_alarm(
                        AlarmName=alarm_name,
                        ComparisonOperator='GreaterThanThreshold',
                        EvaluationPeriods=2,
                        MetricName='CPUUtilization',
                        Namespace='AWS/ECS',
                        Dimensions=[{'Name': 'ClusterName', 'Value': cluster_name}],
                        Period=300,  # 5 minutes
                        Statistic='Average',
                        Threshold=80,
                        ActionsEnabled=True,
                        AlarmActions=[sns_topic_arn],
                        AlarmDescription=f"Alarm for CPU utilization above 80% in ECS cluster {cluster_name}",
                        TreatMissingData='missing',
                        Tags=[
                            {'Key': 'ClusterName', 'Value': cluster_name},
                            {'Key': 'AWSBatchServiceTag', 'Value': 'batch'},
                            {'Key': 'Service', 'Value': 'ECS'},
                            {'Key': 'CreateByAsgAlarmLambda', 'Value': 'ASG_ALARMS_LAMBDA'},
                            {'Key': 'timestamp', 'Value': str(current_timestamp)}
                        ]
                    )



def nonEcsAutoscalingGroupCpuAlarms(region, sns_topic_arn, aws_account_id, current_timestamp, DRYRUN):
    # Initialize AWS clients
    autoscaling_client = boto3.client('autoscaling', region_name=region)
    cloudwatch_client = boto3.client('cloudwatch', region_name=region)

    # List all Auto Scaling Groups
    response = autoscaling_client.describe_auto_scaling_groups()
    for asg in response['AutoScalingGroups']:
        asg_name = asg['AutoScalingGroupName']

        # Check if the ASG has tags to ignore
        ignore_asg = False
        for tag in asg.get('Tags', []):
            if tag['Key'] in ["AWSBatchServiceTag", "AmazonECSManaged"]:
                ignore_asg = True
                break

        if ignore_asg:
            print(f"Ignoring {asg_name} due to tags")
            continue

        # Create CPUUtilization alarm for each Auto Scaling Group
        alarm_name = f"{asg_name}-CPUUtilization-Alarm"

        existing_alarms = cloudwatch_client.describe_alarms(AlarmNames=[alarm_name])['MetricAlarms']

        if existing_alarms:
            updateTimestamp(alarm_name, region, aws_account_id, current_timestamp, cloudwatch_client, DRYRUN)
        else:
            print(f"Creating {alarm_name}")
            if not DRYRUN:
                cloudwatch_client.put_metric_alarm(
                    AlarmName=alarm_name,
                    ComparisonOperator='GreaterThanThreshold',
                    EvaluationPeriods=2,
                    MetricName='CPUUtilization',
                    Namespace='AWS/EC2',
                    Dimensions=[{'Name': 'AutoScalingGroupName', 'Value': asg_name}],
                    Period=300,  # 5 minutes
                    Statistic='Average',
                    Threshold=80,
                    ActionsEnabled=True,
                    AlarmActions=[sns_topic_arn],
                    AlarmDescription=f"Alarm for CPU utilization above 80% in Auto Scaling Group {asg_name}",
                    TreatMissingData='missing',
                    Tags=[
                        {'Key': 'AutoScalingGroupName', 'Value': asg_name},
                        {'Key': 'Service', 'Value': 'EC2'},
                        {'Key': 'CreateByAsgAlarmLambda', 'Value': 'ASG_ALARMS_LAMBDA'},
                        {'Key': 'timestamp', 'Value': str(current_timestamp)}
                    ]
                )


# Test harness
if __name__ == '__main__':
    PARSER = argparse.ArgumentParser(description='Adds cpu alarm for each ASG')
    PARSER.add_argument('-dryrun', help='Show list of alarms to be created or deleted', default='true',
                        action='store', dest='dryrun')
    PARSER.add_argument('-region', help='EC2 region', default=None, action='store', dest='region')
    PARSER.add_argument('-sns-topic-arn', help='EC2 region', default=None, action='store', dest='sns_topic_arn')
    PARSER.add_argument('-aws-account-id', help='EC2 region', default=None, action='store', dest='aws_account_id')

    ARGS = PARSER.parse_args()
    if 'AWS_REGION' not in  os.environ:
        os.environ["AWS_REGION"] = ARGS.region or "us-west-2"
    os.environ["SNS_TOPIC_ARN"] = ARGS.sns_topic_arn
    os.environ["AWS_ACCOUNT_ID"] = ARGS.aws_account_id
    os.environ["DRYRUN"] = ARGS.dryrun.lower()
    handler(None, None)
