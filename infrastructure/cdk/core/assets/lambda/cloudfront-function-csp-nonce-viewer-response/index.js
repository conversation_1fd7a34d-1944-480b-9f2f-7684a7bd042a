async function handler(event) {
    const response = event.response;

    // Check if the custom header X-Custom-Header exists
    if (response.headers['x-custom-header']) {
        const nonce = response.headers['x-custom-header'].value;

        // Check if the response has a CSP header
        if (response.headers['content-security-policy']) {
            const cspHeaderValue = response.headers['content-security-policy'].value;

            // Modify the CSP header to include the nonce
            const modifiedCspHeaderValue = cspHeaderValue.replace(/script-src/i, `script-src 'nonce-${nonce}'`);

            // Update the response's CSP header with the modified value
            response.headers['content-security-policy'].value = modifiedCspHeaderValue;
        }

        // Add Cache-Control header to prevent caching
        response.headers['cache-control'] = { value: 'no-store, no-cache, must-revalidate, max-age=0' };
    }

    return response;
}
