'use strict';
/*
 - Simple asset upload/download integration test to make sure lambda functions
   are working correctly after deployment.
 - These tests run against Dev only
 - They are used as part of infra CI/CD to validate Lambda@Edge changes before
   allowing promotion to production

 How to run:
    1- install dependencies `npm install`
    2- run local tests `npm test`
 */
const fs = require('fs');
const crypto = require('crypto');
const assert = require('assert').strict;
const https = require('https');
const path = require('path');
const url = require('url');
const godModeTokenRequestHost = 'dev.getunblocked.com';
const godModeTokenRequestPath = '/api/godmode/00110e87-abd5-46cb-a8d1-e576f52d0d03/3806658';
const testTeamId = 'c9c47779-235f-4ca9-aefb-96758fa96650';
const assetId = crypto.randomUUID();
const assetContent = randomString(600);

describe('Test', function () {
    it('Upload text asset - expected to pass', async function () {
        // Get Auth token (might move it to request methods)
        const authToken = await getToken();
        assert.equal(authToken.status, 200);

        // Create asset object
        const assetCreateBody = JSON.stringify({
            asset: {
                id: `${assetId}`,
                name: `${assetId}`,
                contentLength: 600,
                contentType: 'text/plain',
            },
        });
        const assetCreateResponseRaw = await httpRequest(
            {
                hostname: godModeTokenRequestHost,
                path: `/api/assets/teams/${testTeamId}/${assetId}`,
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${JSON.parse(authToken['body']).token}`,
                },
                port: 443,
                method: 'POST',
                timeout: 1000,
            },
            assetCreateBody
        );

        assert.equal(assetCreateResponseRaw.status, 200);
        const assetCreateRespBody = JSON.parse(assetCreateResponseRaw['body']);

        // Upload a file content
        const parsedS3Url = url.parse(assetCreateRespBody.uploadUrl.url);
        const assetUploadResponseRaw = await httpRequest(
            {
                hostname: parsedS3Url.host,
                path: parsedS3Url.path,
                headers: {
                    'Content-Type': 'text/plain',
                    'Content-Length': assetContent.length,
                },
                port: 443,
                method: 'PUT',
                timeout: 1000,
            },
            assetContent
        );
        assert.equal(assetUploadResponseRaw.status, 200);
    });

    it('Download text asset - expected to pass', async function () {
        // Get Auth token (might move it to request methods)
        const authToken = await getToken();
        assert.equal(authToken.status, 200);

        const dlResponse = await httpGet({
            hostname: godModeTokenRequestHost,
            path: `/assets/${testTeamId}/${assetId}`,
            headers: {
                Accept: 'application/json',
                Authorization: `Bearer ${JSON.parse(authToken['body']).token}`,
            },
            port: 443,
            timeout: 2000,
        });

        assert.equal(dlResponse.status, 200);
        assert.equal(dlResponse.body.length, assetContent.length);
    });

    it('Download streaming playlist - expected to pass', async function () {
        // Get Auth token (might move it to request methods)
        const authToken = await getToken();
        assert.equal(authToken.status, 200);

        const dlResponse = await httpGet({
            hostname: godModeTokenRequestHost,
            path: `/assets/${testTeamId}/48ad03ab6e4aec392caba9921c0e1517_testChannel.m3u8`,
            headers: {
                Accept: 'application/json',
                Authorization: `Bearer ${JSON.parse(authToken['body']).token}`,
            },
            port: 443,
            timeout: 2000,
        });

        assert.equal(dlResponse.status, 200);
        assert.equal(dlResponse.body.includes('#EXTM3U'), true);
        assert.equal(dlResponse.body.includes('X-Amz-SignedHeaders=host&x-id=GetObject'), true);
    });
});

// Runs lambda function with provided event object
async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
function httpRequest(params, body) {
    return new Promise((resolve, reject) => {
        const req = https.request(params, (resp) => {
            const chunks = [];
            let result = {
                status: resp.statusCode,
                headers: resp.headers,
                body: '',
            };
            resp.on('data', (chunk) => {
                result.body += chunk;
            });
            resp.on('end', () => {
                resolve(result);
            });
        });
        req.on('error', reject);
        //console.log(body)
        if (body) {
            req.write(body);
        }
        req.end();
    });
}

function httpGet(params) {
    return new Promise((resolve, reject) => {
        https
            .get(params, (resp) => {
                let result = {
                    status: resp.statusCode,
                    headers: resp.headers,
                    body: '',
                };
                resp.on('data', (chunk) => {
                    result.body += chunk;
                });
                resp.on('end', () => {
                    resolve(result);
                });
            })
            .on('error', (err) => {
                console.log(`Couldn't fetch ${params.hostname}${params.path} : ${err.message}`);
                reject(err, null);
            });
    });
}

function getToken() {
    const params = {
        hostname: godModeTokenRequestHost,
        path: godModeTokenRequestPath,
        headers: {
            Accept: 'application/json',
        },
        port: 443,
        timeout: 1000,
    };

    return new Promise((resolve, reject) => {
        https
            .get(params, (resp) => {
                let result = {
                    status: resp.statusCode,
                    headers: resp.headers,
                    body: '',
                };
                resp.on('data', (chunk) => {
                    result.body += chunk;
                });
                resp.on('end', () => {
                    resolve(result);
                });
            })
            .on('error', (err) => {
                console.log(`Couldn't fetch ${params.hostname}${params.path} : ${err.message}`);
                reject(err, null);
            });
    });
}

function createMockTxtFile(tmpDirPath = '/tmp') {
    const fileName = randomString(20);
    const filePath = path.join(tmpDirPath, fileName);
    const fileContent = randomString(600);

    try {
        fs.writeFileSync(filePath, fileContent);
        return filePath;
    } catch (err) {
        console.error(err);
    }

    return false;
}

function randomString(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}
