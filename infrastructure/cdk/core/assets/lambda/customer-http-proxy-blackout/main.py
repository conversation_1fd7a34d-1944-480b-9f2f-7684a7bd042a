#!/usr/bin/env python3
"""
Fixed AWS Lambda to block/allow inbound traffic during maintenance windows.
Resolves rule comparison issues that caused unnecessary remove/add cycles.
"""

import datetime
import json
import os
import boto3
import urllib.request
import urllib.parse
import ipaddress


class MaintenanceController:
    def __init__(self):
        self.maintenance_windows = os.environ.get('MAINTENANCE_WINDOWS', '')
        self.security_group_name = os.environ.get('SECURITY_GROUP_NAME', '')
        self.allowed_inbound_cidrs = os.environ.get('ALLOWED_INBOUND_CIDR_LIST', '').split(',') if os.environ.get('ALLOWED_INBOUND_CIDR_LIST') else []
        self.allowed_inbound_port = int(os.environ.get('ALLOWED_INBOUND_PORT', '80'))
        self.slack_webhook = os.environ.get('SLACK_WEBHOOK_URL', '')
        self.grafana_webhook = os.environ.get('GRAFANA_WEBHOOK_URL', '')
        self.ec2 = boto3.client('ec2')

    def validate_and_analyze_cidrs(self):
        """Validate CIDR blocks and check for overlaps."""
        print(f"Analyzing CIDRs: {self.allowed_inbound_cidrs}")

        valid_cidrs = []
        for cidr_str in self.allowed_inbound_cidrs:
            cidr_str = cidr_str.strip()
            if cidr_str:
                try:
                    cidr = ipaddress.IPv4Network(cidr_str, strict=False)
                    valid_cidrs.append(cidr)
                    print(f"Valid CIDR: {cidr}")
                except ValueError as e:
                    print(f"Invalid CIDR '{cidr_str}': {e}")
                    return False

        # Check for overlaps
        for i, cidr1 in enumerate(valid_cidrs):
            for j, cidr2 in enumerate(valid_cidrs):
                if i != j:
                    if cidr1.overlaps(cidr2):
                        print(f"WARNING: CIDR overlap detected: {cidr1} overlaps with {cidr2}")
                    if cidr1.subnet_of(cidr2):
                        print(f"INFO: {cidr1} is a subnet of {cidr2}")
                    if cidr1.supernet_of(cidr2):
                        print(f"INFO: {cidr1} is a supernet of {cidr2}")

        return True

    def get_security_group_id(self):
        """Get security group ID from name."""
        try:
            response = self.ec2.describe_security_groups(
                Filters=[
                    {
                        'Name': 'group-name',
                        'Values': [self.security_group_name]
                    }
                ]
            )

            if response['SecurityGroups']:
                sg_id = response['SecurityGroups'][0]['GroupId']
                print(f"Found security group: {self.security_group_name} -> {sg_id}")
                return sg_id
            else:
                print(f"Security group '{self.security_group_name}' not found")
                return None

        except Exception as e:
            print(f"Error looking up security group name: {e}")
            return None

    def get_current_cidrs_for_port(self, sg_id):
        """Get all CIDRs currently allowed for the target port."""
        try:
            response = self.ec2.describe_security_groups(GroupIds=[sg_id])
            sg = response['SecurityGroups'][0]
            rules = sg.get('IpPermissions', [])

            current_cidrs = set()
            for rule in rules:
                # Only consider rules for our target port and protocol
                if (rule.get('IpProtocol') == 'tcp' and
                    rule.get('FromPort') == self.allowed_inbound_port and
                    rule.get('ToPort') == self.allowed_inbound_port):

                    for ip_range in rule.get('IpRanges', []):
                        current_cidrs.add(ip_range.get('CidrIp'))

            return current_cidrs
        except Exception as e:
            print(f"Error getting current CIDRs: {e}")
            return set()

    def get_desired_cidrs(self):
        """Get the set of CIDRs we want to allow."""
        desired_cidrs = set()
        for cidr in self.allowed_inbound_cidrs:
            cidr = cidr.strip()
            if cidr:
                desired_cidrs.add(cidr)
        return desired_cidrs

    def rules_are_equivalent(self, sg_id):
        """Check if current rules are equivalent to desired rules (regardless of AWS rule structure)."""
        current_cidrs = self.get_current_cidrs_for_port(sg_id)
        desired_cidrs = self.get_desired_cidrs()

        print(f"DEBUG: Current CIDRs for port {self.allowed_inbound_port}: {sorted(current_cidrs)}")
        print(f"DEBUG: Desired CIDRs: {sorted(desired_cidrs)}")

        return current_cidrs == desired_cidrs

    def block_inbound_traffic(self):
        """Remove all inbound rules to block traffic."""
        sg_id = self.get_security_group_id()
        if not sg_id:
            return {'sg_name': self.security_group_name, 'status': 'error', 'error': 'Security group not found'}

        try:
            # Check current CIDRs
            current_cidrs = self.get_current_cidrs_for_port(sg_id)

            # Check if already blocked (no inbound rules for our port)
            if not current_cidrs:
                print("Security group already has no inbound rules for target port - already blocked")
                return {
                    'sg_name': self.security_group_name,
                    'sg_id': sg_id,
                    'status': 'already_blocked',
                    'rules_removed': 0,
                    'rules_changed': False
                }

            # Get raw AWS rules for the actual removal operations
            response = self.ec2.describe_security_groups(GroupIds=[sg_id])
            sg = response['SecurityGroups'][0]
            inbound_rules = sg.get('IpPermissions', [])

            # Remove all inbound rules for our port
            api_calls_attempted = 0
            api_calls_successful = 0
            failed_rules = []
            rules_removed_count = 0

            print(f"Found {len(inbound_rules)} total AWS rule(s)")

            for rule in inbound_rules:
                # Only remove rules for our target port
                if (rule.get('IpProtocol') == 'tcp' and
                    rule.get('FromPort') == self.allowed_inbound_port and
                    rule.get('ToPort') == self.allowed_inbound_port):

                    api_calls_attempted += 1
                    try:
                        self.ec2.revoke_security_group_ingress(
                            GroupId=sg_id,
                            IpPermissions=[rule]
                        )
                        api_calls_successful += 1
                        cidr_count = len(rule.get('IpRanges', []))
                        rules_removed_count += cidr_count
                        print(f"Successfully removed AWS rule with {cidr_count} CIDR(s): {rule}")
                    except Exception as rule_error:
                        error_code = getattr(rule_error, 'response', {}).get('Error', {}).get('Code', '')
                        if error_code == 'InvalidPermission.NotFound':
                            print(f"Rule already removed: {rule}")
                            api_calls_successful += 1  # Count as successful since it's gone
                        else:
                            print(f"Failed to remove rule {rule}: {rule_error}")
                            failed_rules.append(str(rule_error))

            # Check final state
            final_cidrs = self.get_current_cidrs_for_port(sg_id)
            rules_changed = len(current_cidrs) != len(final_cidrs)

            print(f"CIDRs before: {len(current_cidrs)}")
            print(f"CIDRs after: {len(final_cidrs)}")
            print(f"AWS rule API calls attempted: {api_calls_attempted}")
            print(f"AWS rule API calls successful: {api_calls_successful}")
            print(f"Rules actually changed: {rules_changed}")

            # Determine final status
            if failed_rules:
                status = 'partial_blocked' if rules_changed else 'error'
            elif rules_changed:
                status = 'blocked'
            else:
                status = 'already_blocked'

            return {
                'sg_name': self.security_group_name,
                'sg_id': sg_id,
                'status': status,
                'rules_removed': rules_removed_count,
                'failed_rules': failed_rules,
                'rules_changed': rules_changed
            }

        except Exception as e:
            return {'sg_name': self.security_group_name, 'sg_id': sg_id, 'status': 'error', 'error': str(e)}

    def allow_inbound_traffic(self):
        """Add inbound rules for allowed CIDRs and port."""
        sg_id = self.get_security_group_id()
        if not sg_id:
            return {'sg_name': self.security_group_name, 'status': 'error', 'error': 'Security group not found'}

        if not self.allowed_inbound_cidrs:
            return {'sg_name': self.security_group_name, 'sg_id': sg_id, 'status': 'error', 'error': 'No allowed CIDRs configured'}

        try:
            # Check if current rules already have the right CIDRs
            if self.rules_are_equivalent(sg_id):
                print("Security group already has correct CIDRs - no changes needed")
                return {
                    'sg_name': self.security_group_name,
                    'sg_id': sg_id,
                    'status': 'already_allowed',
                    'rules_added': 0,
                    'rules_removed': 0,
                    'rules_changed': False
                }

            # Get current state
            current_cidrs = self.get_current_cidrs_for_port(sg_id)
            desired_cidrs = self.get_desired_cidrs()

            print(f"Current CIDRs: {sorted(current_cidrs)}")
            print(f"Desired CIDRs: {sorted(desired_cidrs)}")

            rules_added = 0
            rules_removed = 0
            failed_rules = []

            # Calculate what needs to be added/removed
            cidrs_to_add = desired_cidrs - current_cidrs
            cidrs_to_remove = current_cidrs - desired_cidrs

            print(f"CIDRs to add: {sorted(cidrs_to_add)}")
            print(f"CIDRs to remove: {sorted(cidrs_to_remove)}")

            # Remove unwanted CIDRs
            if cidrs_to_remove:
                response = self.ec2.describe_security_groups(GroupIds=[sg_id])
                sg = response['SecurityGroups'][0]
                existing_rules = sg.get('IpPermissions', [])

                for rule in existing_rules:
                    if (rule.get('IpProtocol') == 'tcp' and
                        rule.get('FromPort') == self.allowed_inbound_port and
                        rule.get('ToPort') == self.allowed_inbound_port):

                        # Check if this rule contains any CIDRs we want to remove
                        rule_cidrs = {ip_range.get('CidrIp') for ip_range in rule.get('IpRanges', [])}
                        cidrs_in_rule_to_remove = rule_cidrs.intersection(cidrs_to_remove)

                        if cidrs_in_rule_to_remove:
                            try:
                                print(f"DEBUG: Removing rule containing unwanted CIDRs: {rule}")
                                self.ec2.revoke_security_group_ingress(
                                    GroupId=sg_id,
                                    IpPermissions=[rule]
                                )
                                rules_removed += len(rule_cidrs)
                                print(f"Successfully removed rule with CIDRs: {sorted(rule_cidrs)}")

                                # If the rule contained both wanted and unwanted CIDRs, re-add the wanted ones
                                cidrs_in_rule_to_keep = rule_cidrs - cidrs_to_remove
                                for cidr_to_keep in cidrs_in_rule_to_keep:
                                    if cidr_to_keep in desired_cidrs:
                                        new_rule = {
                                            'IpProtocol': 'tcp',
                                            'FromPort': self.allowed_inbound_port,
                                            'ToPort': self.allowed_inbound_port,
                                            'IpRanges': [{'CidrIp': cidr_to_keep}]
                                        }
                                        try:
                                            self.ec2.authorize_security_group_ingress(
                                                GroupId=sg_id,
                                                IpPermissions=[new_rule]
                                            )
                                            print(f"Re-added kept CIDR: {cidr_to_keep}")
                                        except Exception as e:
                                            print(f"Failed to re-add kept CIDR {cidr_to_keep}: {e}")
                                            failed_rules.append(f"Re-add: {str(e)}")

                            except Exception as e:
                                error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', '')
                                if error_code == 'InvalidPermission.NotFound':
                                    print(f"Rule already removed: {rule}")
                                else:
                                    print(f"Failed to remove rule {rule}: {e}")
                                    failed_rules.append(f"Remove: {str(e)}")

            # Add missing CIDRs
            if cidrs_to_add:
                for cidr in cidrs_to_add:
                    new_rule = {
                        'IpProtocol': 'tcp',
                        'FromPort': self.allowed_inbound_port,
                        'ToPort': self.allowed_inbound_port,
                        'IpRanges': [{'CidrIp': cidr}]
                    }
                    try:
                        print(f"DEBUG: Adding new rule for CIDR: {cidr}")
                        self.ec2.authorize_security_group_ingress(
                            GroupId=sg_id,
                            IpPermissions=[new_rule]
                        )
                        rules_added += 1
                        print(f"Successfully added rule for CIDR: {cidr}")
                    except Exception as e:
                        error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', '')
                        error_message = getattr(e, 'response', {}).get('Error', {}).get('Message', '')
                        print(f"DEBUG: Add rule error - Code: {error_code}, Message: {error_message}")

                        if error_code == 'InvalidPermission.Duplicate':
                            print(f"Rule already exists for CIDR: {cidr}")
                            rules_added += 1  # Count as successful since it exists
                        else:
                            print(f"Failed to add rule for CIDR {cidr}: {e}")
                            failed_rules.append(f"Add {cidr}: {str(e)}")

            # Check final state
            final_cidrs = self.get_current_cidrs_for_port(sg_id)
            rules_changed = current_cidrs != final_cidrs

            print(f"CIDRs before: {sorted(current_cidrs)}")
            print(f"CIDRs after: {sorted(final_cidrs)}")
            print(f"Rules removed (CIDRs): {rules_removed}")
            print(f"Rules added (CIDRs): {rules_added}")
            print(f"Rules actually changed: {rules_changed}")
            print(f"Failed rules: {failed_rules}")

            # Determine final status
            if failed_rules:
                status = 'partial_allowed' if rules_changed else 'error'
            elif rules_changed:
                status = 'allowed'
            else:
                status = 'already_allowed'

            return {
                'sg_name': self.security_group_name,
                'sg_id': sg_id,
                'status': status,
                'rules_added': rules_added,
                'rules_removed': rules_removed,
                'failed_rules': failed_rules,
                'rules_changed': rules_changed
            }

        except Exception as e:
            print(f"DEBUG: Exception in allow_inbound_traffic: {e}")
            print(f"DEBUG: Exception type: {type(e)}")
            return {'sg_name': self.security_group_name, 'sg_id': sg_id, 'status': 'error', 'error': str(e)}

    def get_current_cidrs_for_port(self, sg_id):
        """Get all CIDRs currently allowed for the target port."""
        try:
            response = self.ec2.describe_security_groups(GroupIds=[sg_id])
            sg = response['SecurityGroups'][0]
            rules = sg.get('IpPermissions', [])

            current_cidrs = set()
            for rule in rules:
                # Only consider rules for our target port and protocol
                if (rule.get('IpProtocol') == 'tcp' and
                    rule.get('FromPort') == self.allowed_inbound_port and
                    rule.get('ToPort') == self.allowed_inbound_port):

                    for ip_range in rule.get('IpRanges', []):
                        current_cidrs.add(ip_range.get('CidrIp'))

            return current_cidrs
        except Exception as e:
            print(f"Error getting current CIDRs: {e}")
            return set()

    def is_maintenance_window(self) -> bool:
        """Check if current UTC time is within maintenance window."""
        if not self.maintenance_windows:
            return False

        current_utc = datetime.datetime.now(datetime.timezone.utc)
        patterns = [p.strip() for p in self.maintenance_windows.split(',')]

        for pattern in patterns:
            if self._is_in_window(pattern, current_utc):
                return True
        return False

    def _is_in_window(self, pattern: str, current_time: datetime.datetime) -> bool:
        """Check if current time is in window. Format: SUN:02:00-04:00"""
        try:
            # Split on first colon to separate day from time range
            colon_index = pattern.find(':')
            if colon_index == -1:
                raise ValueError("Invalid pattern format")

            day_part = pattern[:colon_index]
            time_part = pattern[colon_index + 1:]
            start_time, end_time = time_part.split('-')

            days = {'SUN': 6, 'MON': 0, 'TUE': 1, 'WED': 2, 'THU': 3, 'FRI': 4, 'SAT': 5}
            target_weekday = days[day_part.upper()]

            start_hour, start_min = map(int, start_time.split(':'))
            end_hour, end_min = map(int, end_time.split(':'))

            if current_time.weekday() == target_weekday:
                current_minutes = current_time.hour * 60 + current_time.minute
                start_minutes = start_hour * 60 + start_min
                end_minutes = end_hour * 60 + end_min

                if end_minutes <= start_minutes:  # Overnight window
                    return current_minutes >= start_minutes or current_minutes <= end_minutes
                else:
                    return start_minutes <= current_minutes <= end_minutes

            # Check for overnight window that spans to next day
            if end_hour < start_hour:  # This is an overnight window
                # Check if we're on the day after the target day
                next_day = (target_weekday + 1) % 7
                if current_time.weekday() == next_day:
                    current_minutes = current_time.hour * 60 + current_time.minute
                    end_minutes = end_hour * 60 + end_min
                    return current_minutes <= end_minutes

        except Exception as e:
            print(f"Error parsing pattern '{pattern}': {e}")

        return False

    def send_notifications(self, action: str, result: dict, is_success: bool, state_changed: bool = False):
        """Send webhook notifications."""
        status_code = result.get('status', 'unknown')

        # Always send failure notifications
        if not is_success:
            # Slack notification for failure
            if self.slack_webhook:
                status = "❌ FAILURE"
                message = {
                    "text": f"{status}: Inbound traffic {action}",
                    "attachments": [{
                        "color": "danger",
                        "fields": [
                            {"title": "Action", "value": action, "short": True},
                            {"title": "Security Group", "value": self.security_group_name, "short": True},
                            {"title": "Error", "value": result.get('error', 'Unknown error'), "short": False}
                        ]
                    }]
                }
                self._send_webhook(self.slack_webhook, message)

            # Grafana notification for failure
            if self.grafana_webhook:
                grafana_message = {
                    "title": f"Inbound traffic {action} failed",
                    "message": f"Failed to {action} inbound traffic for {self.security_group_name}",
                    "details": result
                }
                self._send_webhook(self.grafana_webhook, grafana_message)

        # Send success notifications when state actually changed
        elif is_success and state_changed:
            if self.slack_webhook:
                # Determine status based on result
                if status_code in ['partial_blocked', 'partial_allowed']:
                    status = "⚠️ PARTIAL SUCCESS"
                    color = "warning"
                else:
                    status = "✅ SUCCESS"
                    color = "good"

                fields = [
                    {"title": "Action", "value": action, "short": True},
                    {"title": "Security Group", "value": self.security_group_name, "short": True}
                ]

                # Add details for changes
                if result.get('rules_added', 0) > 0:
                    fields.append({"title": "Rules Added", "value": str(result.get('rules_added', 0)), "short": True})
                if result.get('rules_removed', 0) > 0:
                    fields.append({"title": "Rules Removed", "value": str(result.get('rules_removed', 0)), "short": True})

                if result.get('failed_rules'):
                    failed_count = len(result['failed_rules'])
                    fields.append({"title": "Failed Rules", "value": str(failed_count), "short": True})

                message = {
                    "text": f"{status}: Inbound traffic {action}",
                    "attachments": [{
                        "color": color,
                        "fields": fields
                    }]
                }
                self._send_webhook(self.slack_webhook, message)

            # Send Grafana notification for partial success (warning level)
            if status_code in ['partial_blocked', 'partial_allowed'] and self.grafana_webhook:
                grafana_message = {
                    "title": f"Inbound traffic {action} partially successful",
                    "message": f"Partially {action} inbound traffic for {self.security_group_name}",
                    "details": result
                }
                self._send_webhook(self.grafana_webhook, grafana_message)

    def _send_webhook(self, webhook_url: str, payload: dict):
        """Send webhook notification."""
        try:
            data = json.dumps(payload).encode('utf-8')
            req = urllib.request.Request(
                webhook_url,
                data=data,
                headers={'Content-Type': 'application/json'}
            )
            with urllib.request.urlopen(req, timeout=10) as response:
                pass  # Successfully sent
        except Exception as e:
            print(f"Webhook failed: {e}")

    def execute(self):
        """Main execution method."""
        try:
            # Add CIDR validation
            if not self.validate_and_analyze_cidrs():
                return {
                    'statusCode': 400,
                    'body': json.dumps({'error': 'Invalid CIDR configuration'})
                }

            is_maintenance = self.is_maintenance_window()
            print(f"Maintenance window active: {is_maintenance}")

            if is_maintenance:
                print("🚫 MAINTENANCE MODE: Blocking inbound traffic")
                result = self.block_inbound_traffic()
                action = "blocked"
            else:
                print("✅ NORMAL MODE: Allowing inbound traffic")
                result = self.allow_inbound_traffic()
                action = "allowed"

            is_success = result.get('status') not in ['error']

            # State changed is now based on actual CIDR comparison
            state_changed = result.get('rules_changed', False)

            print(f"Result status: {result.get('status')}")
            print(f"Rules changed: {state_changed}")
            print(f"Is success: {is_success}")
            print(f"Will send notification: {is_success and state_changed}")

            # Treat partial success as success for notifications, but log warnings
            if result.get('status') in ['partial_blocked', 'partial_allowed']:
                print(f"Warning: Partial {action} - some rules failed")
                if result.get('failed_rules'):
                    print(f"Failed rules: {result['failed_rules']}")

            self.send_notifications(action, result, is_success, state_changed)

            return {
                'statusCode': 200,
                'body': json.dumps({
                    'maintenance_active': is_maintenance,
                    'action': action,
                    'success': is_success,
                    'state_changed': state_changed,
                    'result': result
                })
            }

        except Exception as e:
            print(f"Error in execute: {e}")
            return {
                'statusCode': 500,
                'body': json.dumps({'error': str(e)})
            }


def lambda_handler(event, context):
    """AWS Lambda entry point."""
    controller = MaintenanceController()
    return controller.execute()


# Local testing
if __name__ == "__main__":
    os.environ['MAINTENANCE_WINDOWS'] = 'SUN:02:00-04:00'
    os.environ['SECURITY_GROUP_NAME'] = 'web-servers'
    os.environ['ALLOWED_INBOUND_CIDR_LIST'] = '10.0.0.0/8,172.16.0.0/12'
    os.environ['ALLOWED_INBOUND_PORT'] = '80'

    controller = MaintenanceController()
    result = controller.execute()
    print(json.dumps(result, indent=2))
