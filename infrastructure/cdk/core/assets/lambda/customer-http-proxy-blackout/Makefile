# Makefile for Customer HTTP Proxy Blackout Lambda

VENV_NAME = venv
PYTHON = python3
PIP = $(VENV_NAME)/bin/pip
PYTHON_VENV = $(VENV_NAME)/bin/python
LAMBDA_PATH = lambda/customer-http-proxy-blackout/
TEST_PATH = ./

# Colors
GREEN = \033[0;32m
BLUE = \033[0;34m
NC = \033[0m

.PHONY: help setup clean test lint format all

all: setup test

help: ## Show available commands
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Create virtual environment and install dependencies
	@echo "$(BLUE)Setting up environment...$(NC)"
	$(PYTHON) -m venv $(VENV_NAME)
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements.txt
	@echo "$(GREEN)Setup complete!$(NC)"

test: ## Run tests
	@echo "$(BLUE)Running tests...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) -v

test-coverage: ## Run tests with coverage
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	$(PYTHON_VENV) -m pytest $(TEST_PATH) --cov=$(LAMBDA_PATH) --cov-report=term-missing

lint: ## Run code linting
	@echo "$(BLUE)Running linting...$(NC)"
	$(PYTHON_VENV) -m flake8 $(LAMBDA_PATH) $(TEST_PATH) --max-line-length=120

format: ## Format code
	@echo "$(BLUE)Formatting code...$(NC)"
	$(PYTHON_VENV) -m black $(LAMBDA_PATH) $(TEST_PATH) --line-length=120

clean: ## Clean up
	@echo "$(BLUE)Cleaning up...$(NC)"
	rm -rf $(VENV_NAME) .pytest_cache htmlcov .coverage
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -delete
	@echo "$(GREEN)Cleanup complete!$(NC)"

rebuild: clean setup ## Clean and rebuild

dev: setup test lint format ## Full development cycle
