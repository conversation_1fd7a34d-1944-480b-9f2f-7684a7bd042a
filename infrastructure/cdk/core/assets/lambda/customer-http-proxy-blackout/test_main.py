"""
Tests for customer HTTP proxy blackout lambda function.
Updated for the fixed version with CIDR-based comparison.
"""

import json
import os
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone
import boto3
from moto import mock_ec2

# Import the lambda function from same directory
from main import MaintenanceController, lambda_handler

class TestMaintenanceController:

    def setup_method(self):
        os.environ['MAINTENANCE_WINDOWS'] = 'SUN:02:00-04:00,WED:01:00-03:00'
        os.environ['SECURITY_GROUP_NAME'] = 'test-security-group'
        os.environ['ALLOWED_INBOUND_CIDR_LIST'] = '10.0.0.0/8,**********/12'
        os.environ['ALLOWED_INBOUND_PORT'] = '80'
        os.environ['SLACK_WEBHOOK_URL'] = 'https://hooks.slack.com/test'
        os.environ['GRAFANA_WEBHOOK_URL'] = 'https://grafana.test/webhook'
        os.environ['AWS_DEFAULT_REGION'] = 'us-west-2'

    def teardown_method(self):
        for key in ['MAINTENANCE_WINDOWS', 'SECURITY_GROUP_NAME', 'ALLOWED_INBOUND_CIDR_LIST', 'ALLOWED_INBOUND_PORT', 'SLACK_WEBHOOK_URL', 'GRAFANA_WEBHOOK_URL', 'AWS_DEFAULT_REGION']:
            os.environ.pop(key, None)

    def test_init(self):
        controller = MaintenanceController()
        assert controller.maintenance_windows == 'SUN:02:00-04:00,WED:01:00-03:00'
        assert controller.security_group_name == 'test-security-group'
        assert controller.allowed_inbound_cidrs == ['10.0.0.0/8', '**********/12']
        assert controller.allowed_inbound_port == 80
        assert controller.slack_webhook == 'https://hooks.slack.com/test'
        assert controller.grafana_webhook == 'https://grafana.test/webhook'

    def test_validate_and_analyze_cidrs_valid(self):
        controller = MaintenanceController()
        result = controller.validate_and_analyze_cidrs()
        assert result is True

    def test_validate_and_analyze_cidrs_invalid(self):
        os.environ['ALLOWED_INBOUND_CIDR_LIST'] = '10.0.0.0/8,invalid-cidr'
        controller = MaintenanceController()
        result = controller.validate_and_analyze_cidrs()
        assert result is False

    def test_get_desired_cidrs(self):
        controller = MaintenanceController()
        expected = {'10.0.0.0/8', '**********/12'}
        result = controller.get_desired_cidrs()
        assert result == expected

    def test_is_in_window_sunday_active(self):
        controller = MaintenanceController()
        sunday_3am = datetime(2024, 1, 7, 3, 0, tzinfo=timezone.utc)
        result = controller._is_in_window('SUN:02:00-04:00', sunday_3am)
        assert result is True

    def test_is_in_window_sunday_inactive(self):
        controller = MaintenanceController()
        sunday_5am = datetime(2024, 1, 7, 5, 0, tzinfo=timezone.utc)
        result = controller._is_in_window('SUN:02:00-04:00', sunday_5am)
        assert result is False

    def test_is_in_window_overnight(self):
        controller = MaintenanceController()
        friday_2330 = datetime(2024, 1, 5, 23, 30, tzinfo=timezone.utc)
        result = controller._is_in_window('FRI:23:00-01:00', friday_2330)
        assert result is True

        saturday_0030 = datetime(2024, 1, 6, 0, 30, tzinfo=timezone.utc)
        result = controller._is_in_window('FRI:23:00-01:00', saturday_0030)
        assert result is True

    @patch('main.datetime')
    def test_is_maintenance_window_active(self, mock_datetime):
        controller = MaintenanceController()
        sunday_3am = datetime(2024, 1, 7, 3, 0, tzinfo=timezone.utc)
        mock_datetime.datetime.now.return_value = sunday_3am
        mock_datetime.timezone.utc = timezone.utc
        result = controller.is_maintenance_window()
        assert result is True

    @patch('main.datetime')
    def test_is_maintenance_window_inactive(self, mock_datetime):
        controller = MaintenanceController()
        monday_3am = datetime(2024, 1, 8, 3, 0, tzinfo=timezone.utc)
        mock_datetime.datetime.now.return_value = monday_3am
        mock_datetime.timezone.utc = timezone.utc
        result = controller.is_maintenance_window()
        assert result is False

    @mock_ec2
    def test_get_security_group_id_success(self):
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )
        controller = MaintenanceController()
        result = controller.get_security_group_id()
        assert result == sg['GroupId']

    @mock_ec2
    def test_get_security_group_id_not_found(self):
        controller = MaintenanceController()
        result = controller.get_security_group_id()
        assert result is None

    @mock_ec2
    def test_get_current_cidrs_for_port(self):
        """Test that get_current_cidrs_for_port correctly extracts CIDRs."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add rules for different ports
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[
                {
                    'IpProtocol': 'tcp',
                    'FromPort': 80,  # Target port
                    'ToPort': 80,
                    'IpRanges': [
                        {'CidrIp': '10.0.0.0/8'},
                        {'CidrIp': '**********/12'}
                    ]
                },
                {
                    'IpProtocol': 'tcp',
                    'FromPort': 443,  # Different port
                    'ToPort': 443,
                    'IpRanges': [{'CidrIp': '***********/24'}]
                }
            ]
        )

        controller = MaintenanceController()
        cidrs_for_port_80 = controller.get_current_cidrs_for_port(sg['GroupId'])

        # Should only return CIDRs for port 80, not 443
        expected_cidrs = {'10.0.0.0/8', '**********/12'}
        assert cidrs_for_port_80 == expected_cidrs

    @mock_ec2
    def test_rules_are_equivalent_true(self):
        """Test that rules_are_equivalent correctly identifies when CIDRs match."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add the exact CIDRs we want (AWS may consolidate into one rule)
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[{
                'IpProtocol': 'tcp',
                'FromPort': 80,
                'ToPort': 80,
                'IpRanges': [
                    {'CidrIp': '10.0.0.0/8'},
                    {'CidrIp': '**********/12'}
                ]
            }]
        )

        controller = MaintenanceController()
        result = controller.rules_are_equivalent(sg['GroupId'])
        assert result is True

    @mock_ec2
    def test_rules_are_equivalent_false(self):
        """Test that rules_are_equivalent correctly identifies when CIDRs don't match."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add different CIDRs than what we want
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[{
                'IpProtocol': 'tcp',
                'FromPort': 80,
                'ToPort': 80,
                'IpRanges': [{'CidrIp': '***********/24'}]  # Different CIDR
            }]
        )

        controller = MaintenanceController()
        result = controller.rules_are_equivalent(sg['GroupId'])
        assert result is False

    @mock_ec2
    def test_block_inbound_traffic_success(self):
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )
        # Add some inbound rules
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[{
                'IpProtocol': 'tcp',
                'FromPort': 80,
                'ToPort': 80,
                'IpRanges': [{'CidrIp': '10.0.0.0/8'}]
            }]
        )
        controller = MaintenanceController()
        result = controller.block_inbound_traffic()
        assert result['status'] == 'blocked'
        assert result['sg_name'] == 'test-security-group'
        assert result['sg_id'] == sg['GroupId']
        assert result['rules_removed'] == 1
        assert result['rules_changed'] is True

    @mock_ec2
    def test_block_inbound_traffic_already_blocked(self):
        """Test that blocking returns already_blocked when no rules exist."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )
        # Don't add any rules - should already be blocked
        controller = MaintenanceController()
        result = controller.block_inbound_traffic()
        assert result['status'] == 'already_blocked'
        assert result['rules_changed'] is False

    @mock_ec2
    def test_allow_inbound_traffic_success(self):
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )
        controller = MaintenanceController()
        result = controller.allow_inbound_traffic()
        assert result['status'] == 'allowed'
        assert result['sg_name'] == 'test-security-group'
        assert result['sg_id'] == sg['GroupId']
        assert result['rules_added'] == 2  # Two CIDRs configured
        assert result['rules_changed'] is True

    @mock_ec2
    def test_allow_inbound_traffic_already_allowed(self):
        """Test that allowing returns already_allowed when correct rules exist."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add the exact CIDRs we want (let AWS consolidate them)
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[{
                'IpProtocol': 'tcp',
                'FromPort': 80,
                'ToPort': 80,
                'IpRanges': [
                    {'CidrIp': '10.0.0.0/8'},
                    {'CidrIp': '**********/12'}
                ]
            }]
        )

        controller = MaintenanceController()
        result = controller.allow_inbound_traffic()
        assert result['status'] == 'already_allowed'
        assert result['rules_changed'] is False

    @mock_ec2
    def test_allow_inbound_traffic_with_cleanup(self):
        """Test that allow operation only affects the target port."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add rules for different ports
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[
                {
                    'IpProtocol': 'tcp',
                    'FromPort': 443,  # Different port - should be left alone
                    'ToPort': 443,
                    'IpRanges': [{'CidrIp': '10.0.0.0/8'}]
                },
                {
                    'IpProtocol': 'tcp',
                    'FromPort': 80,  # Target port with wrong CIDR
                    'ToPort': 80,
                    'IpRanges': [{'CidrIp': '***********/24'}]
                }
            ]
        )

        controller = MaintenanceController()
        result = controller.allow_inbound_traffic()

        # Should add correct rules and clean up wrong ones for target port
        assert result['status'] == 'allowed'
        assert result['rules_added'] == 2  # Two desired CIDRs
        assert result['rules_changed'] is True

        # Verify final state
        response = ec2.describe_security_groups(GroupIds=[sg['GroupId']])
        rules = response['SecurityGroups'][0]['IpPermissions']

        # Should have rules for both ports
        port_80_cidrs = set()
        port_443_cidrs = set()

        for rule in rules:
            if rule['FromPort'] == 80:
                for ip_range in rule['IpRanges']:
                    port_80_cidrs.add(ip_range['CidrIp'])
            elif rule['FromPort'] == 443:
                for ip_range in rule['IpRanges']:
                    port_443_cidrs.add(ip_range['CidrIp'])

        # Port 80 should have our desired CIDRs
        assert port_80_cidrs == {'10.0.0.0/8', '**********/12'}
        # Port 443 should still have its original CIDR (untouched)
        assert port_443_cidrs == {'10.0.0.0/8'}

    @mock_ec2
    def test_block_inbound_traffic_only_target_port(self):
        """Test that blocking only affects the target port."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add rules for different ports
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[
                {
                    'IpProtocol': 'tcp',
                    'FromPort': 80,  # Target port
                    'ToPort': 80,
                    'IpRanges': [{'CidrIp': '10.0.0.0/8'}]
                },
                {
                    'IpProtocol': 'tcp',
                    'FromPort': 443,  # Different port - should be left alone
                    'ToPort': 443,
                    'IpRanges': [{'CidrIp': '10.0.0.0/8'}]
                }
            ]
        )

        controller = MaintenanceController()
        result = controller.block_inbound_traffic()

        assert result['status'] == 'blocked'
        assert result['rules_removed'] == 1  # Only port 80 rule removed
        assert result['rules_changed'] is True

        # Verify final state - port 443 should still exist
        response = ec2.describe_security_groups(GroupIds=[sg['GroupId']])
        rules = response['SecurityGroups'][0]['IpPermissions']

        # Should only have the port 443 rule remaining
        assert len(rules) == 1
        assert rules[0]['FromPort'] == 443
        assert rules[0]['ToPort'] == 443

    @mock_ec2
    def test_block_allow_cycle_consistent_counts(self):
        """Test that allow→block→allow cycle gives consistent rule counts."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        controller = MaintenanceController()

        # Start with allow - should add 2 CIDRs
        allow_result1 = controller.allow_inbound_traffic()
        assert allow_result1['rules_added'] == 2, f"Expected 2 rules added, got {allow_result1['rules_added']}"
        assert allow_result1['status'] == 'allowed'

        # Block - should remove 2 CIDRs (same count)
        block_result = controller.block_inbound_traffic()
        assert block_result['rules_removed'] == 2, f"Expected 2 rules removed, got {block_result['rules_removed']}"
        assert block_result['status'] == 'blocked'

        # Allow again - should add 2 CIDRs again
        allow_result2 = controller.allow_inbound_traffic()
        assert allow_result2['rules_added'] == 2, f"Expected 2 rules added again, got {allow_result2['rules_added']}"
        assert allow_result2['status'] == 'allowed'

        print(f"Cycle results: Allow1={allow_result1['rules_added']}, Block={block_result['rules_removed']}, Allow2={allow_result2['rules_added']}")

        # All counts should be consistent
        assert allow_result1['rules_added'] == block_result['rules_removed'] == allow_result2['rules_added'] == 2

    @mock_ec2
    def test_idempotent_behavior(self):
        """Test that multiple consecutive calls with same state don't make changes."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        controller = MaintenanceController()

        # First allow - should make changes
        result1 = controller.allow_inbound_traffic()
        assert result1['status'] == 'allowed'
        assert result1['rules_changed'] is True

        # Second allow - should detect no changes needed
        result2 = controller.allow_inbound_traffic()
        assert result2['status'] == 'already_allowed'
        assert result2['rules_changed'] is False
        assert result2['rules_added'] == 0

        # Third allow - should still detect no changes needed
        result3 = controller.allow_inbound_traffic()
        assert result3['status'] == 'already_allowed'
        assert result3['rules_changed'] is False
        assert result3['rules_added'] == 0

    @patch('main.urllib.request.urlopen')
    def test_send_notifications_success(self, mock_urlopen):
        mock_response = MagicMock()
        mock_urlopen.return_value.__enter__.return_value = mock_response
        controller = MaintenanceController()

        # Test success notification with state change
        result = {'sg_name': 'test-sg', 'status': 'blocked', 'rules_changed': True}
        controller.send_notifications('blocked', result, True, True)
        assert mock_urlopen.call_count == 1
        mock_urlopen.reset_mock()

        # Test partial success notification
        result = {'sg_name': 'test-sg', 'status': 'partial_blocked', 'rules_removed': 2, 'failed_rules': ['error1'], 'rules_changed': True}
        controller.send_notifications('blocked', result, True, True)
        assert mock_urlopen.call_count == 2  # Slack + Grafana for partial success
        mock_urlopen.reset_mock()

        # Test success notification without state change (should not send)
        result = {'sg_name': 'test-sg', 'status': 'blocked', 'rules_changed': False}
        controller.send_notifications('blocked', result, True, False)
        assert mock_urlopen.call_count == 0
        mock_urlopen.reset_mock()

        # Test failure notification (should always send)
        result = {'sg_name': 'test-sg', 'status': 'error', 'error': 'Test error', 'rules_changed': False}
        controller.send_notifications('blocked', result, False, False)
        assert mock_urlopen.call_count == 2  # Slack + Grafana

    @patch('main.MaintenanceController.is_maintenance_window')
    @patch('main.MaintenanceController.block_inbound_traffic')
    @patch('main.MaintenanceController.send_notifications')
    def test_execute_maintenance_mode(self, mock_notifications, mock_block, mock_maintenance):
        mock_maintenance.return_value = True
        mock_block.return_value = {'sg_name': 'test-sg', 'status': 'blocked', 'rules_changed': True}
        controller = MaintenanceController()
        result = controller.execute()
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['maintenance_active'] is True
        assert body['action'] == 'blocked'
        assert body['success'] is True
        assert body['state_changed'] is True
        mock_block.assert_called_once()
        mock_notifications.assert_called_once_with('blocked', {'sg_name': 'test-sg', 'status': 'blocked', 'rules_changed': True}, True, True)

    @patch('main.MaintenanceController.is_maintenance_window')
    @patch('main.MaintenanceController.allow_inbound_traffic')
    @patch('main.MaintenanceController.send_notifications')
    def test_execute_normal_mode(self, mock_notifications, mock_allow, mock_maintenance):
        mock_maintenance.return_value = False
        mock_allow.return_value = {'sg_name': 'test-sg', 'status': 'allowed', 'rules_changed': True}
        controller = MaintenanceController()
        result = controller.execute()
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['maintenance_active'] is False
        assert body['action'] == 'allowed'
        assert body['success'] is True
        assert body['state_changed'] is True
        mock_allow.assert_called_once()
        mock_notifications.assert_called_once_with('allowed', {'sg_name': 'test-sg', 'status': 'allowed', 'rules_changed': True}, True, True)

    @patch('main.MaintenanceController.is_maintenance_window')
    @patch('main.MaintenanceController.allow_inbound_traffic')
    @patch('main.MaintenanceController.send_notifications')
    def test_execute_no_state_change(self, mock_notifications, mock_allow, mock_maintenance):
        mock_maintenance.return_value = False
        mock_allow.return_value = {'sg_name': 'test-sg', 'status': 'already_allowed', 'rules_changed': False}
        controller = MaintenanceController()
        result = controller.execute()
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['maintenance_active'] is False
        assert body['action'] == 'allowed'
        assert body['success'] is True
        assert body['state_changed'] is False
        mock_allow.assert_called_once()
        mock_notifications.assert_called_once_with('allowed', {'sg_name': 'test-sg', 'status': 'already_allowed', 'rules_changed': False}, True, False)

    @patch('main.MaintenanceController.is_maintenance_window')
    @patch('main.MaintenanceController.block_inbound_traffic')
    @patch('main.MaintenanceController.send_notifications')
    def test_execute_already_blocked_no_notification(self, mock_notifications, mock_block, mock_maintenance):
        mock_maintenance.return_value = True
        mock_block.return_value = {'sg_name': 'test-sg', 'status': 'already_blocked', 'rules_changed': False}
        controller = MaintenanceController()
        result = controller.execute()
        assert result['statusCode'] == 200
        body = json.loads(result['body'])
        assert body['maintenance_active'] is True
        assert body['action'] == 'blocked'
        assert body['success'] is True
        assert body['state_changed'] is False
        mock_block.assert_called_once()
        mock_notifications.assert_called_once_with('blocked', {'sg_name': 'test-sg', 'status': 'already_blocked', 'rules_changed': False}, True, False)

    @mock_ec2
    def test_partial_rule_matching(self):
        """Test handling of rules where some CIDRs match and others don't."""
        ec2 = boto3.client('ec2', region_name='us-west-2')
        vpc = ec2.create_vpc(CidrBlock='10.0.0.0/16')
        sg = ec2.create_security_group(
            GroupName='test-security-group',
            Description='Test security group',
            VpcId=vpc['Vpc']['VpcId']
        )

        # Add a rule with one matching CIDR and one extra CIDR
        ec2.authorize_security_group_ingress(
            GroupId=sg['GroupId'],
            IpPermissions=[{
                'IpProtocol': 'tcp',
                'FromPort': 80,
                'ToPort': 80,
                'IpRanges': [
                    {'CidrIp': '10.0.0.0/8'},      # This matches our desired
                    {'CidrIp': '***********/24'}   # This doesn't match our desired
                ]
            }]
        )

        controller = MaintenanceController()

        # Should detect that rules are not equivalent
        assert controller.rules_are_equivalent(sg['GroupId']) is False

        # Should fix the rules
        result = controller.allow_inbound_traffic()
        assert result['status'] == 'allowed'
        assert result['rules_changed'] is True

        # Verify final state has only our desired CIDRs
        final_cidrs = controller.get_current_cidrs_for_port(sg['GroupId'])
        expected_cidrs = {'10.0.0.0/8', '**********/12'}
        assert final_cidrs == expected_cidrs


class TestLambdaHandler:

    def setup_method(self):
        os.environ['MAINTENANCE_WINDOWS'] = 'SUN:02:00-04:00'
        os.environ['SECURITY_GROUP_NAME'] = 'test-security-group'
        os.environ['ALLOWED_INBOUND_CIDR_LIST'] = '10.0.0.0/8,**********/12'
        os.environ['ALLOWED_INBOUND_PORT'] = '80'
        os.environ['SLACK_WEBHOOK_URL'] = 'https://hooks.slack.com/test'
        os.environ['GRAFANA_WEBHOOK_URL'] = 'https://grafana.test/webhook'
        os.environ['AWS_DEFAULT_REGION'] = 'us-west-2'

    def teardown_method(self):
        for key in ['MAINTENANCE_WINDOWS', 'SECURITY_GROUP_NAME', 'ALLOWED_INBOUND_CIDR_LIST', 'ALLOWED_INBOUND_PORT', 'SLACK_WEBHOOK_URL', 'GRAFANA_WEBHOOK_URL', 'AWS_DEFAULT_REGION']:
            os.environ.pop(key, None)

    @patch('main.MaintenanceController.execute')
    def test_lambda_handler(self, mock_execute):
        mock_execute.return_value = {
            'statusCode': 200,
            'body': json.dumps({'maintenance_active': False, 'action': 'allowed', 'success': True, 'state_changed': True})
        }
        event = {}
        context = {}
        result = lambda_handler(event, context)
        assert result['statusCode'] == 200
        mock_execute.assert_called_once()

    @patch('main.MaintenanceController.validate_and_analyze_cidrs')
    def test_lambda_handler_invalid_cidrs(self, mock_validate):
        mock_validate.return_value = False
        event = {}
        context = {}
        controller = MaintenanceController()
        result = controller.execute()
        assert result['statusCode'] == 400
        body = json.loads(result['body'])
        assert 'Invalid CIDR configuration' in body['error']


if __name__ == '__main__':
    pytest.main(['-v', __file__])
