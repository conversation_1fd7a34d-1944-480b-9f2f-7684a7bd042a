'use strict';
/**
 * This lambda function is used to redirect
 * subdomains on our main sites e.g getunblocked.com or dev.getunblocked.com
 * such as dashboard.getunblocked.com to the correct path https://getunblocked.com/dashboard
 *
 * CloudFront distros are configured with wildcard certs. DNS records pointing to CloudFront
 * are created manually for each subdomain we wish to redirect
 */
exports.handler = (event, context, callback) => {
    const request = event.Records[0].cf.request;
    const host = request.headers['host'][0].value;
    const subdomain = host.split('.')[0];
    const domain = host.replace(`${subdomain}.`, '');
    const response = {
        status: '301',
        statusDescription: `Redirecting to apex domain`,
        headers: {
            location: [
                {
                    key: 'Location',
                    value: `https://${domain}/${subdomain}${request.uri}`,
                },
            ],
        },
    };
    callback(null, response);
};
