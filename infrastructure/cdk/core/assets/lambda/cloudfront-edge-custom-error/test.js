const assert = require('assert').strict;
const testEventTemplate = require('./app-event.json');
const lambdaLocal = require('lambda-local');
const landingPageEventTemplate = require('./landing-page-event.json');
const path = require('path');

describe('Test', function () {
    it('reroutes to dashboard index file', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dashboard/discussions/mine';
        assert.equal((await runLambda(testEvent)).headers['x-lambda-path'][0].value, '/dashboard/index.html');
    });

    it('reroutes to landing page index file', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/team/blah/invite';
        assert.equal((await runLambda(testEvent)).headers['x-lambda-path'][0].value, '/index.html');
    });
});

async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
