{"Records": [{"cf": {"config": {"distributionDomainName": "d1gz1415gsret9.cloudfront.net", "distributionId": "E1U394B5JV5R7Z", "eventType": "origin-response", "requestId": "Tz-LhucRuWnTd5xHyDgaooJGSYCRzfxVuC52gS0WpcuMWIeH4pRj2A=="}, "request": {"clientIp": "*************", "headers": {"x-forwarded-for": [{"key": "X-Forwarded-For", "value": "*************"}], "user-agent": [{"key": "User-Agent", "value": "Amazon CloudFront"}], "via": [{"key": "Via", "value": "2.0 1d000d0dfe9d69b4983f619fdc5499d6.cloudfront.net (CloudFront)"}], "accept-encoding": [{"key": "Accept-Encoding", "value": "br,gzip"}], "if-modified-since": [{"key": "If-Modified-Since", "value": "Mon, 30 May 2022 16:11:16 GMT"}], "if-none-match": [{"key": "If-None-Match", "value": "\"3d751148c07fe6246e5078dfb7d16249\""}], "host": [{"key": "Host", "value": "dashboard.dev.getunblocked.com.s3.us-east-1.amazonaws.com"}], "cache-control": [{"key": "Cache-Control", "value": "max-age=0"}]}, "method": "GET", "origin": {"s3": {"authMethod": "origin-access-identity", "customHeaders": {}, "domainName": "dashboard.dev.getunblocked.com.s3.us-east-1.amazonaws.com", "path": "", "region": "us-east-1"}}, "querystring": "", "uri": "/dashboard/discussions/mine"}, "response": {"headers": {"x-amz-request-id": [{"key": "x-amz-request-id", "value": "9RZERP1RXNS18DQG"}], "x-amz-id-2": [{"key": "x-amz-id-2", "value": "8RJq6Uy9YcPRPFVPan7igNpsDOcNi2MZskre7ftbuEt1yqgoWNWfdBuoGux0yIkEgd5lkaUvr/k="}], "date": [{"key": "Date", "value": "Mon, 30 May 2022 19:35:01 GMT"}], "server": [{"key": "Server", "value": "AmazonS3"}], "content-type": [{"key": "Content-Type", "value": "application/xml"}], "transfer-encoding": [{"key": "Transfer-Encoding", "value": "chunked"}]}, "status": "403", "statusDescription": "Forbidden"}}}]}