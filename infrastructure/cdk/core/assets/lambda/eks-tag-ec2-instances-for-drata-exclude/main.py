'''
This code DrataExclude:true to EC2 instances with tag eks:cluster-name if not already present
Local testing:
 - Switch your AWS cli to Dev (Never run local against prod!)
 - Run 'python3 main.py -region us-west-2'
 - By default script runs in dryrun mode!
'''
from __future__ import print_function

import argparse
import os
import boto3

REGION = None
DRYRUN = None


def initialize():
    global REGION
    global DRYRUN

    REGION = os.environ.get('REGION', "None")
    DRYRUN = os.environ.get('DRYRUN', "false").lower()
    if DRYRUN == "false":
        DRYRUN = False
    else:
        DRYRUN = True


def handler(event, context):
    initialize()
    tagInstances()

def tagInstances():
    # Initialize EC2 client
    #ec2 = boto3.client('ec2')
    ec2 = boto3.client('ec2', region_name=REGION)

    # Get all EC2 instances
    instances = ec2.describe_instances(Filters=[{'Name': 'instance-state-name', 'Values': ['running']}])

    # Iterate through instances
    for reservation in instances['Reservations']:
        for instance in reservation['Instances']:
            instance_id = instance['InstanceId']
            eks_cluster_name_tag_exists = False
            # Check if 'eks:cluster-name' tag exists
            for tag in instance.get('Tags', []):
                if tag['Key'] == 'eks:cluster-name':
                    eks_cluster_name_tag_exists = True
                    break

            # If 'eks:cluster-name' tag exists and 'DrataExclude' tag does not exist, add it
            if eks_cluster_name_tag_exists:
                drata_exclude_tag_exists = False
                for tag in instance.get('Tags', []):
                    if tag['Key'] == 'DrataExclude':
                        drata_exclude_tag_exists = True
                        break
                if not drata_exclude_tag_exists:
                    print(f"Add DrataExclude tag to {instance_id}")
                    if not DRYRUN:
                        ec2.create_tags(Resources=[instance_id], Tags=[{'Key': 'DrataExclude', 'Value': 'Monitored by Grafana Cloud'}])
                        exit(0)

    return {
        'statusCode': 200,
        'body': 'Successfully added tag DrataExclude:true to EC2 instances with tag eks:cluster-name if not already present'
    }

# Below is the test harness
if __name__ == '__main__':
    PARSER = argparse.ArgumentParser(description='Adds DrataExclude tag to EKS nodes')
    PARSER.add_argument('-dryrun', help='Show list of instances to be tagged but do not tag them', default='true',
                        action='store', dest='dryrun')
    PARSER.add_argument('-region', help='EC2 region', default=None, action='store', dest='region')

    ARGS = PARSER.parse_args()
    if ARGS.region:
        os.environ["REGION"] = ARGS.region
    else:
        os.environ["REGION"] = "None"
    os.environ["DRYRUN"] = ARGS.dryrun.lower()
    handler(None, None)
