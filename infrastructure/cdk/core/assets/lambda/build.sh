#!/usr/bin/env bash
#
# Script to install npm packages and run tests for each
# Lambda function.
#
#  Note: CDK cannot install npms dependencies.
#        We have to run this script before deployments
#
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
set -e
for dir in $(ls $SCRIPT_DIR | grep -v integration-tests)
do
  if [ -f "$SCRIPT_DIR/$dir/package.json" ]; then
      echo "-> BUILDING $dir "
      (
        cd $SCRIPT_DIR/$dir/ &&\
        pwd &&\
        rm -rf node_modules&&\
        echo "Installing DEV dependencies" &&\
        npm install &&\
        #echo "Running tests at $(pwd)" &&\
        #npm test &&\
        echo "Removing DEV dependencies and keeping prod ones!" &&\
        npm prune --production &&\
        echo "Finished building and testing $dir"
      )
  fi
done