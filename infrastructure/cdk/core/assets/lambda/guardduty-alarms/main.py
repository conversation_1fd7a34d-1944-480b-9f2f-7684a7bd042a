import json
import boto3
import urllib.request
import os

# Initialize Boto3 client for Secrets Manager
secrets_client = boto3.client('secretsmanager')

def get_secret(secret_name, key):
    response = secrets_client.get_secret_value(SecretId=secret_name)
    secret = json.loads(response['SecretString'])
    return secret[key]

def process_guardduty_finding(detail):
    created_at = detail.get('createdAt')
    updated_at = detail.get('updatedAt')

    # Extract relevant details for the message
    severity = float(detail.get('severity', 0))
    title = detail.get('title', 'N/A')
    region = detail.get('region', 'N/A')
    account_id = detail.get('accountId', 'N/A')
    resource_type = detail.get('resource', {}).get('resourceType', 'N/A')
    action_type = detail.get('service', {}).get('action', {}).get('actionType', 'N/A')

    # Convert the JSON to a pretty-printed string
    full_json = json.dumps(detail, indent=8)

    # Severity to color and string mapping
    if severity <= 3.9:
        severity_level = "LOW"
        severity_color = "#36a64f"  # Green
    elif 4.0 <= severity <= 6.9:
        severity_level = "MEDIUM"
        severity_color = "#f1c40f"  # Yellow
    elif 7.0 <= severity <= 8.9:
        severity_level = "HIGH"
        severity_color = "#e74c3c"  # Red
    else:
        severity_level = "UNKNOWN"
        severity_color = "#808080"  # Grey

    # Get the channel name from environment variable (default to #aws-alarms)
    channel_name = os.getenv('SLACK_CHANNEL', '#aws-alarms')

    # Construct Slack message
    slack_message = {
        "channel": channel_name,
        "text": f"GuardDuty Alert: {title}",
        "blocks": [
            {
                "type": "header",
                "text": {"type": "plain_text", "text": "GuardDuty Alert"}
            },
            {
                "type": "section",
                "fields": [
                    {"type": "mrkdwn", "text": f"*Title:*\n{title}"},
                    {"type": "mrkdwn", "text": f"*Severity:*\n{severity} ({severity_level})"},
                    {"type": "mrkdwn", "text": f"*Created At:*\n{created_at}"},
                    {"type": "mrkdwn", "text": f"*Region:*\n{region}"},
                    {"type": "mrkdwn", "text": f"*Account ID:*\n{account_id}"},
                    {"type": "mrkdwn", "text": f"*Resource Type:*\n{resource_type}"},
                    {"type": "mrkdwn", "text": f"*Action Type:*\n{action_type}"}
                ]
            },
            {
                "type": "context",
                "elements": [{"type": "mrkdwn", "text": "Full JSON details are included below."}]
            }
        ],
        "attachments": [{"color": severity_color, "text": full_json}]
    }

    print("Constructed Slack Message:", json.dumps(slack_message, indent=4))

    # Send to Slack
    slack_webhook_url = get_secret("SlackAlarmWebhookURL", "SlackAlarmWebhookURL")

    slack_req = urllib.request.Request(
        slack_webhook_url,
        data=json.dumps(slack_message).encode('utf-8'),
        headers={'Content-Type': 'application/json'},
        method='POST'
    )

    try:
        with urllib.request.urlopen(slack_req) as response:
            print(f"Response from Slack: {response.read().decode('utf-8')}")
    except urllib.error.HTTPError as e:
        print(f"HTTPError: {e.code} - {e.reason}")
        print(e.read().decode())
        raise

    # If severity is HIGH, send to Grafana
    if severity_level == "HIGH":
        grafana_webhook_url = get_secret("GrafanaWebhookUrl", "GrafanaWebhookUrl")

        grafana_message = {
            "title": f"GuardDuty High Alert: {title}",
            "message": f"A high-severity GuardDuty alert detected. See full details in Slack: <slack_channel_placeholder>",
            "tags": ["guardduty", "high", region, account_id],
            "annotations": {
                "severity": severity_level,
                "region": region,
                "accountId": account_id,
                "resourceType": resource_type,
                "actionType": action_type
            }
        }

        grafana_req = urllib.request.Request(
            grafana_webhook_url,
            data=json.dumps(grafana_message).encode('utf-8'),
            headers={'Content-Type': 'application/json'},
            method='POST'
        )

        try:
            with urllib.request.urlopen(grafana_req) as response:
                print(f"Response from Grafana: {response.read().decode('utf-8')}")
        except urllib.error.HTTPError as e:
            print(f"HTTPError: {e.code} - {e.reason}")
            print(e.read().decode())
            raise


def lambda_handler(event, context):
    """
    Handles multiple records from an SNS-triggered Lambda function.
    """
    print("Incoming Event:", json.dumps(event, indent=4))

    for record in event.get('Records', []):
        sns_message = record.get('Sns', {}).get('Message')

        if not sns_message:
            print("Skipping record, no SNS message found.")
            continue

        try:
            message_data = json.loads(sns_message)  # Parse SNS Message JSON
            detail = message_data.get("detail", {})

            if detail:
                process_guardduty_finding(detail)
            else:
                print("Skipping record, no GuardDuty detail found.")

        except json.JSONDecodeError as e:
            print(f"JSON decoding failed: {e}")
            continue

    return {
        'statusCode': 200,
        'body': json.dumps('Processed all records.')
    }
