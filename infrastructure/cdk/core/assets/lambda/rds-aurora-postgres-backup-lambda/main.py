import os
import boto3


target_region = 'us-east-2'  # replace with your target region
target_account = '************' # replace with the target AWS account ID
rds_target = boto3.client('rds', region_name=target_region)
rds = boto3.client('rds')
s3 = boto3.client('s3')

region = os.getenv('REGION')
account = os.getenv('ACCOUNT')
cluster_name = os.getenv('CLUSTER_NAME')
bucket_name = os.getenv('BUCKET_NAME')
iam_role_arn = os.getenv('IAM_ROLE_ARN')
s3_kms_key_arn = os.getenv('S3_KMS_KEY_ARN')
rds_kms_key_arn = os.getenv('RDS_KMS_KEY_ARN')
snapshot_identifier_prefix = f'rds:{cluster_name}'
target_identifier_prefix = f'rds:{cluster_name}'

def exportNotFoundOnS3(bucket_name, s3_key):
    try:
        # Check if the snapshot already exists on S3
        s3.head_object(Bucket=bucket_name, Key=s3_key)
        print(f'Snapshot {snapshot_name} already exists on S3')
        return False
    except Exception as e:
        return True

def snapshotHasNoExportJob(dbClusterSnapshotArn):
    # Check if there are any export tasks in progress for this snapshot
    export_tasks = rds.describe_export_tasks(
        SourceArn=dbClusterSnapshotArn
    )['ExportTasks']

    if len(export_tasks) > 0:
        print(f'Export task for snapshot {dbClusterSnapshotArn} already in progress..skipping!')
        return False
    return True

def snapshotExistsInTargetRegion(target_snapshot_identifier):
    try:
        # Check if the snapshot already exists in the target region
        return (len(rds_target.describe_db_cluster_snapshots(DBClusterSnapshotIdentifier=target_snapshot_identifier)['DBClusterSnapshots']) > 0)
    except Exception as e:
        return False

def lambda_handler(event, context):
    try:

        # Get a list of all RDS cluster snapshots with the specified prefix
        print("here")
        snapshots = rds.describe_db_cluster_snapshots(
            DBClusterIdentifier=cluster_name,
            SnapshotType='automated',
            IncludeShared=False
        )['DBClusterSnapshots']
        print(snapshots)
        filtered_snapshots = [snapshot for snapshot in snapshots if snapshot['DBClusterSnapshotIdentifier'].startswith(snapshot_identifier_prefix)]

        # Export each snapshot to S3 and copy it to the target region if it doesn't already exist there
        for snapshot in filtered_snapshots:
            snapshot_full_name = str(snapshot['DBClusterSnapshotIdentifier'])
            snapshot_name = snapshot['DBClusterSnapshotIdentifier'].replace(snapshot_identifier_prefix + "-", cluster_name[:30]).replace("--", "-")
            print(f"Working on {snapshot_name}")
            s3_key = f'rds-backups/{snapshot_name}.bak'

            if (exportNotFoundOnS3(bucket_name, s3_key) and snapshotHasNoExportJob(snapshot['DBClusterSnapshotArn'])):
                # Export the snapshot to S3
                print(f"creating export job for {snapshot_name}")
                export_snapshot_response = rds.start_export_task(
                    ExportTaskIdentifier=f'export-{snapshot_name}',
                    SourceArn=snapshot['DBClusterSnapshotArn'],
                    S3BucketName=bucket_name,
                    IamRoleArn=iam_role_arn,
                    KmsKeyId=s3_kms_key_arn,
                    S3Prefix=s3_key
                )

                export_task_identifier = export_snapshot_response['ExportTaskIdentifier']
                print(f'Started exporting snapshot {snapshot_full_name} to S3 with identifier {export_task_identifier}')

            # Check if the snapshot already exists in the target region
            target_snapshot_name = snapshot_name.replace(region, target_region)
            #target_snapshot_identifier = f'arn:aws:rds:{target_region}:{target_account}:cluster-snapshot:{target_snapshot_name}'

            if not snapshotExistsInTargetRegion(snapshot_name):
                # Copy the snapshot to the target region
                print(f'Copying snapshot {snapshot_full_name} to {target_region}')
                copy_snapshot_response = rds_target.copy_db_cluster_snapshot(
                    SourceDBClusterSnapshotIdentifier=snapshot['DBClusterSnapshotArn'],
                    TargetDBClusterSnapshotIdentifier=snapshot_name,
                    KmsKeyId=rds_kms_key_arn,
                    CopyTags=True,
                    SourceRegion=region
                )
                print(f'Started copying snapshot {snapshot_full_name} to {target_region} with identifier {copy_snapshot_response["DBClusterSnapshot"]["DBClusterSnapshotIdentifier"]}')

        return 0

    except Exception as e:
        print(e)
        exit(1)

