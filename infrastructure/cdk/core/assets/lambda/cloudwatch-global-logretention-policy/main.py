import boto3
import os

def lambda_handler(event, context):
    RETENTION_DAYS = int(os.getenv('RETENTION_DAYS'))
    client = boto3.client('logs')

    next_token = None
    while True:
        kwargs = {}
        if next_token:
            kwargs['nextToken'] = next_token

        log_groups = client.describe_log_groups(**kwargs)
        for group in log_groups['logGroups']:
            print(f"Processing log group {group['logGroupName']}")
            client.put_retention_policy(logGroupName=group['logGroupName'], retentionInDays=RETENTION_DAYS)

        next_token = log_groups.get('nextToken')
        if not next_token:
            break
