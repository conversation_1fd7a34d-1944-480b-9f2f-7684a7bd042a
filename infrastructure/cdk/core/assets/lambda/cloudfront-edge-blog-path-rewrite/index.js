'use strict';
const https = require('https');

// Helper function to generate a nonce for CSP
function generateRandomNonce() {
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const nonceLength = 16;
    let nonce = '';
    for (let i = 0; i < nonceLength; i++) {
        nonce += characters[Math.floor(Math.random() * characters.length)];
    }
    return nonce;
}

// Helper function to add nonce to script and link tags
function addNoncesToHtml(html, nonce) {
    // Add nonce attribute to script tags
    const modifiedHtmlWithNonces = html.replace(/<script[^>]*(?:nonce="[^"]*")?[^>]*>/g, (match) => {
        if (!match.includes('nonce=')) {
            return match.replace('>', ` nonce="${nonce}">`);
        } else {
            return match;
        }
    });

    // Add nonce attribute to link tags (for scripts)
    const finalResult = modifiedHtmlWithNonces.replace(/<link[^>]*(?:nonce="[^"]*")?[^>]*>/g, (match) => {
        if (!match.includes('nonce=') && match.includes('as="script"')) {
            return match.replace('/>', ` nonce="${nonce}"/>`);
        } else {
            return match;
        }
    });

    return finalResult;
}

exports.handler = async (event) => {
    const request = event.Records[0].cf.request;
    const response = event.Records[0].cf.response;

    let newUri = request.uri;

    // Remove trailing slash unless the URI is just "/"
    if (newUri.length > 1 && newUri.endsWith('/')) {
        newUri = newUri.slice(0, -1);
    }

    // Get the origin domain from the CloudFront request object
    let originDomain = '';
    if (request.origin && request.origin.custom && request.origin.custom.domainName) {
        originDomain = request.origin.custom.domainName;
    } else if (request.origin && request.origin.s3 && request.origin.s3.domainName) {
        originDomain = request.origin.s3.domainName;
    } else {
        throw new Error('No valid origin domain found in request');
    }

    // Options for the request to the origin server
    const options = {
        hostname: originDomain,
        port: 443,
        path: newUri,
        method: 'GET',
        rejectUnauthorized: false, // Bypass SSL certificate validation
    };

    // Create a promise that will handle the HTTPS request and response
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let body = '';

            // Collect the data from the response
            res.on('data', (chunk) => {
                body += chunk;
            });

            // When the response ends, modify the HTML with nonce and resolve the function
            res.on('end', () => {
                const nonce = generateRandomNonce();
                // Check if the content type is HTML and add nonce
                const contentType = res.headers['content-type'] || '';
                if (contentType.includes('text/html')) {
                    body = addNoncesToHtml(body, nonce);
                    res.headers['x-custom-nonce'] = [{ key: 'X-Custom-Nonce', value: nonce }];
                    res.headers['x-custom-header'] = [{ key: 'X-Custom-Nonce', value: nonce }];
                }

                const response = {
                    status: res.statusCode.toString(),
                    body: body,
                    headers: {
                        'x-custom-header': [{ key: 'X-Custom-Header', value: `${nonce}` }],
                        'content-type': [{ key: 'Content-Type', value: res.headers['content-type'] }],
                    },
                };

                // Pass along any custom headers (e.g., nonce or other security headers)
                resolve(response);
            });
        });

        // Handle any errors with the request
        req.on('error', (e) => reject(e));

        // End the request
        req.end();
    });
};
