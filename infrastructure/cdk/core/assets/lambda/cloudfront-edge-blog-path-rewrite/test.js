const assert = require('assert').strict;
const testEventTemplate = require('./app-event.json');
const lambdaLocal = require('lambda-local');
const path = require('path');

describe('Lambda Function Tests', function () {
    it('removes /blog prefix from URI', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/blog/';

        const result = await runLambda(testEvent);

        assert.equal(result.status, '200');
    });

    it('correctly proxies request to origin', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/blog/connecting-your-code-no-matter-where-it-lives';

        const result = await runLambda(testEvent);

        assert.equal(result.status, '200');
        assert.ok(result.body.includes('Webflow'), 'Expected body to contain content from Webflow');
    });
});

async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
