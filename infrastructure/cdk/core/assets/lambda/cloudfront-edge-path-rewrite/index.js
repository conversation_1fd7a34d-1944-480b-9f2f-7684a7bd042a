'use strict';

// Source: https://aws.amazon.com/blogs/compute/implementing-default-directory-indexes-in-amazon-s3-backed-amazon-cloudfront-origins-using-lambdaedge/
//
// Handles requests like www.myapp.com/app/ (with trailing slash) and adds index.html to them
// By default this request returns 200 OK with application/x-directory content type
exports.handler = (event, context, callback) => {
    // Extract the request from the CloudFront event that is sent to Lambda@Edge
    const request = event.Records[0].cf.request;

    console.log('event: ' + JSON.stringify(event));

    // Extract the URI from the request
    const oldUri = request.uri;
    let newUri = request.uri;

    if (oldUri.endsWith('/')) {
        newUri = oldUri.replace(/\/$/, '/index.html');
    } else if (!hasFileExtension(oldUri) && !isWellKnownOAuthPath(oldUri)) {
        // This is an ugly hack but works! We treat anything without an extension
        // as a directory to allow for hosting both SPAs and regular html pages
        // Exception: .well-known OAuth paths should be served as-is
        newUri = oldUri + '/index.html';
    } else if (isWellKnownOAuthPath(oldUri)) {
        // Normalize OAuth well-known paths by removing any trailing segments
        newUri = normalizeWellKnownOAuthPath(oldUri);
    }

    // Replace the received URI with the URI that includes the index page
    request.uri = newUri;

    console.log(`Response: ${JSON.stringify(request)}`);

    // Return to CloudFront
    return callback(null, request);
};

function hasFileExtension(fileName) {
    var pattern = /\.[0-9a-z]+$/i;
    var hasExtension = pattern.test(fileName);
    return hasExtension;
}

function isWellKnownOAuthPath(uri) {
    return (
        uri.includes('/.well-known/oauth-authorization-server') || uri.includes('/.well-known/oauth-protected-resource')
    );
}

function normalizeWellKnownOAuthPath(uri) {
    if (uri.includes('/.well-known/oauth-authorization-server')) {
        const index = uri.indexOf('/.well-known/oauth-authorization-server');
        return uri.substring(0, index + '/.well-known/oauth-authorization-server'.length);
    }
    if (uri.includes('/.well-known/oauth-protected-resource')) {
        const index = uri.indexOf('/.well-known/oauth-protected-resource');
        return uri.substring(0, index + '/.well-known/oauth-protected-resource'.length);
    }
    return uri;
}
