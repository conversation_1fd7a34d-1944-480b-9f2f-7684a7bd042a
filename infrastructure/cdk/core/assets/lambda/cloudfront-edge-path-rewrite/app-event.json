{"Records": [{"cf": {"config": {"distributionDomainName": "d1gz1415gsret9.cloudfront.net", "distributionId": "E1U394B5JV5R7Z", "eventType": "origin-request", "requestId": "dinaOnk5Pj-Z_9ClQLwNtsUiEPS9JP7Vh8mx9-aJsKlx4RzUY_Lwlw=="}, "request": {"clientIp": "*************", "headers": {"x-forwarded-for": [{"key": "X-Forwarded-For", "value": "*************"}], "user-agent": [{"key": "User-Agent", "value": "Amazon CloudFront"}], "via": [{"key": "Via", "value": "2.0 da4fa914888b330b3e8a08632b8e41be.cloudfront.net (CloudFront)"}], "accept-encoding": [{"key": "Accept-Encoding", "value": "br,gzip"}], "if-modified-since": [{"key": "If-Modified-Since", "value": "Mon, 30 May 2022 16:11:16 GMT"}], "if-none-match": [{"key": "If-None-Match", "value": "\"3d751148c07fe6246e5078dfb7d16249\""}], "host": [{"key": "Host", "value": "dashboard.dev.getunblocked.com.s3.us-east-1.amazonaws.com"}]}, "method": "GET", "origin": {"s3": {"authMethod": "origin-access-identity", "customHeaders": {}, "domainName": "dashboard.dev.getunblocked.com.s3.us-east-1.amazonaws.com", "path": "", "region": "us-east-1"}}, "querystring": "", "uri": "/dashboard/"}}}]}