import os
import pytest
import datetime
from unittest.mock import patch, MagicMock
from main import lambda_handler, get_latest_request_count, put_relabeled_metric, clean_service_name

@pytest.fixture(autouse=True)
def set_env():
    os.environ['TG_TAG_KEY'] = 'ingress.k8s.aws/resource'
    os.environ['CUSTOM_NAMESPACE'] = 'Custom/ALB'
    yield
    # Clean up
    for key in ['TG_TAG_KEY', 'CUSTOM_NAMESPACE']:
        if key in os.environ:
            del os.environ[key]

@patch("main.put_relabeled_metric")
@patch("main.get_latest_request_count")
@patch("main.elbv2")
@patch("main.get_all_target_groups")
def test_lambda_handler_basic(
    mock_get_all_tgs,
    mock_elbv2,
    mock_get_count,
    mock_put_metric
):
    # Mock target groups
    mock_get_all_tgs.return_value = [
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg-1/abc123',
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg-2/def456'
    ]

    # Mock describe_tags responses
    mock_elbv2.describe_tags.side_effect = [
        # First TG has no matching tag
        {
            "TagDescriptions": [{
                "Tags": [
                    {"Key": "Name", "Value": "some-other-tag"},
                    {"Key": "Environment", "Value": "prod"}
                ]
            }]
        },
        # Second TG has the matching tag
        {
            "TagDescriptions": [{
                "Tags": [
                    {"Key": "ingress.k8s.aws/resource", "Value": "default/my-service:80"},
                    {"Key": "Environment", "Value": "prod"}
                ]
            }]
        }
    ]

    # Mock request count
    mock_get_count.return_value = 150.0

    # Execute
    result = lambda_handler({}, {})

    # Assertions
    assert result['statusCode'] == 200
    assert 'Processed 1 target groups' in result['body']

    # Verify get_latest_request_count was called once for the TG with the tag
    mock_get_count.assert_called_once_with(
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg-2/def456'
    )

    # Verify put_relabeled_metric was called with the tag value and count
    mock_put_metric.assert_called_once_with('default/my-service:80', 150.0)

@patch("main.put_relabeled_metric")
@patch("main.get_latest_request_count")
@patch("main.elbv2")
@patch("main.get_all_target_groups")
def test_lambda_handler_no_matching_tags(
    mock_get_all_tgs,
    mock_elbv2,
    mock_get_count,
    mock_put_metric
):
    # Mock target groups
    mock_get_all_tgs.return_value = [
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg-1/abc123'
    ]

    # Mock describe_tags with no matching tag
    mock_elbv2.describe_tags.return_value = {
        "TagDescriptions": [{
            "Tags": [
                {"Key": "Name", "Value": "some-other-tag"},
                {"Key": "Environment", "Value": "prod"}
            ]
        }]
    }

    # Execute
    result = lambda_handler({}, {})

    # Assertions
    assert result['statusCode'] == 200
    assert 'Processed 0 target groups' in result['body']

    # Verify no metrics were processed
    mock_get_count.assert_not_called()
    mock_put_metric.assert_not_called()

@patch("main.cloudwatch")
@patch("main.elbv2")
def test_get_latest_request_count_success(mock_elbv2, mock_cloudwatch):
    # Mock target group description
    mock_elbv2.describe_target_groups.return_value = {
        "TargetGroups": [{
            "LoadBalancerArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-lb/xyz789"]
        }]
    }

    # Mock CloudWatch response
    mock_cloudwatch.get_metric_statistics.return_value = {
        "Datapoints": [
            {
                "Timestamp": datetime.datetime(2023, 1, 1, 12, 0),
                "Sum": 100.0
            },
            {
                "Timestamp": datetime.datetime(2023, 1, 1, 12, 1),
                "Sum": 150.0  # Most recent
            }
        ]
    }

    # Execute
    result = get_latest_request_count(
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg/abc123'
    )

    # Should return the most recent datapoint (150.0)
    assert result == 150.0

    # Verify CloudWatch was called with correct parameters
    mock_cloudwatch.get_metric_statistics.assert_called_once()
    call_args = mock_cloudwatch.get_metric_statistics.call_args

    assert call_args[1]['Namespace'] == 'AWS/ApplicationELB'
    assert call_args[1]['MetricName'] == 'RequestCount'
    assert call_args[1]['Period'] == 60
    assert call_args[1]['Statistics'] == ['Sum']

    # Check dimensions
    dimensions = call_args[1]['Dimensions']
    assert len(dimensions) == 2
    assert {"Name": "TargetGroup", "Value": "targetgroup/my-tg/abc123"} in dimensions
    assert {"Name": "LoadBalancer", "Value": "app/my-lb/xyz789"} in dimensions

@patch("main.cloudwatch")
@patch("main.elbv2")
def test_get_latest_request_count_no_data(mock_elbv2, mock_cloudwatch):
    # Mock target group description
    mock_elbv2.describe_target_groups.return_value = {
        "TargetGroups": [{
            "LoadBalancerArns": ["arn:aws:elasticloadbalancing:us-west-2:123456789012:loadbalancer/app/my-lb/xyz789"]
        }]
    }

    # Mock CloudWatch response with no datapoints
    mock_cloudwatch.get_metric_statistics.return_value = {
        "Datapoints": []
    }

    # Execute
    result = get_latest_request_count(
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg/abc123'
    )

    # Should return None when no data
    assert result is None

@patch("main.cloudwatch")
def test_put_relabeled_metric_success(mock_cloudwatch):
    # Execute
    put_relabeled_metric("default/my-service:80", 200.0)

    # Verify CloudWatch put_metric_data was called
    mock_cloudwatch.put_metric_data.assert_called_once()
    call_args = mock_cloudwatch.put_metric_data.call_args

    assert call_args[1]['Namespace'] == 'Custom/ALB'

    metric_data = call_args[1]['MetricData'][0]
    assert metric_data['MetricName'] == 'RequestCount'
    assert metric_data['Value'] == 200.0
    assert metric_data['Unit'] == 'Count'

    # Check dimension - ServiceName as dimension name, cleaned service name as value
    dimensions = metric_data['Dimensions']
    assert len(dimensions) == 1
    assert dimensions[0]['Name'] == 'ServiceName'
    assert dimensions[0]['Value'] == 'default/my-service:80'

def test_clean_service_name():
    # Test normal service name
    assert clean_service_name("default/my-service:80") == "default/my-service:80"

    # Test service name with invalid characters
    assert clean_service_name("default/my@service$name!") == "default/my@service_name_"

    # Test empty service name
    assert clean_service_name("") == "unknown_service"

    # Test very long service name
    long_name = "a" * 300
    result = clean_service_name(long_name)
    assert len(result) == 255

    # Test service name with valid special characters
    assert clean_service_name("default/my-service_v2:80#prod") == "default/my-service_v2:80#prod"

@patch("main.put_relabeled_metric")
@patch("main.get_latest_request_count")
@patch("main.elbv2")
@patch("main.get_all_target_groups")
def test_lambda_handler_with_error(
    mock_get_all_tgs,
    mock_elbv2,
    mock_get_count,
    mock_put_metric
):
    # Mock target groups
    mock_get_all_tgs.return_value = [
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg-1/abc123'
    ]

    # Mock describe_tags to raise an exception
    mock_elbv2.describe_tags.side_effect = Exception("AWS API Error")

    # Execute - should not raise exception
    result = lambda_handler({}, {})

    # Should still return success but process 0 target groups
    assert result['statusCode'] == 200
    assert 'Processed 0 target groups' in result['body']

    # Verify no metrics were processed due to error
    mock_get_count.assert_not_called()
    mock_put_metric.assert_not_called()

@patch("main.put_relabeled_metric")
@patch("main.get_latest_request_count")
@patch("main.elbv2")
@patch("main.get_all_target_groups")
def test_lambda_handler_no_data(
    mock_get_all_tgs,
    mock_elbv2,
    mock_get_count,
    mock_put_metric
):
    # Mock target groups
    mock_get_all_tgs.return_value = [
        'arn:aws:elasticloadbalancing:us-west-2:123456789012:targetgroup/my-tg-1/abc123'
    ]

    # Mock describe_tags with matching tag
    mock_elbv2.describe_tags.return_value = {
        "TagDescriptions": [{
            "Tags": [
                {"Key": "ingress.k8s.aws/resource", "Value": "default/my-service:80"}
            ]
        }]
    }

    # Mock get_latest_request_count to return None (no data)
    mock_get_count.return_value = None

    # Execute
    result = lambda_handler({}, {})

    # Should process 0 target groups since no data was available
    assert result['statusCode'] == 200
    assert 'Processed 0 target groups' in result['body']

    # Verify get_latest_request_count was called but put_relabeled_metric was not
    mock_get_count.assert_called_once()
    mock_put_metric.assert_not_called()
