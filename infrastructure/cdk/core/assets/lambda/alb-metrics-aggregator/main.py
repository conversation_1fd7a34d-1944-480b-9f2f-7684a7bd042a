import boto3
import os
import datetime

elbv2 = boto3.client("elbv2")
cloudwatch = boto3.client("cloudwatch")

TAG_KEY = os.environ.get("TG_TAG_KEY", "ingress.k8s.aws/resource")
CUSTOM_NAMESPACE = os.environ.get("CUSTOM_NAMESPACE", "Custom/ALB")

def lambda_handler(event, context):
    tg_arns = get_all_target_groups()
    processed_count = 0

    for tg_arn in tg_arns:
        try:
            tags = elbv2.describe_tags(ResourceArns=[tg_arn])["TagDescriptions"][0]["Tags"]
            tag_map = {tag["Key"]: tag["Value"] for tag in tags}
            service_name = tag_map.get(TAG_KEY)

            if not service_name:
                continue

            request_count = get_latest_request_count(tg_arn)

            if request_count is not None:
                put_relabeled_metric(service_name, request_count)
                processed_count += 1

        except Exception as e:
            print(f"Error processing TG {tg_arn}: {e}")

    return {
        'statusCode': 200,
        'body': f'Processed {processed_count} target groups'
    }

def get_all_target_groups():
    tg_arns = []
    paginator = elbv2.get_paginator("describe_target_groups")

    for page in paginator.paginate():
        for tg in page["TargetGroups"]:
            tg_arns.append(tg["TargetGroupArn"])

    return tg_arns

def get_latest_request_count(tg_arn):
    """Get the most recent RequestCount value - exact same metric, just relabeled"""
    try:
        tg_desc = elbv2.describe_target_groups(TargetGroupArns=[tg_arn])["TargetGroups"][0]
        tg_id = tg_arn.split("targetgroup/")[-1]
        lb_arn = tg_desc["LoadBalancerArns"][0]
        lb_name_and_id = lb_arn.split("loadbalancer/app/")[-1]

        dimensions = [
            {"Name": "TargetGroup", "Value": f"targetgroup/{tg_id}"},
            {"Name": "LoadBalancer", "Value": f"app/{lb_name_and_id}"}
        ]

        end_time = datetime.datetime.utcnow()
        start_time = end_time - datetime.timedelta(minutes=5)

        response = cloudwatch.get_metric_statistics(
            Namespace="AWS/ApplicationELB",
            MetricName="RequestCount",
            Dimensions=dimensions,
            StartTime=start_time,
            EndTime=end_time,
            Period=60,
            Statistics=["Sum"],
            Unit="Count"
        )

        data_points = response.get("Datapoints", [])

        if data_points:
            # Just get the most recent value - no averaging, no per-target calculation
            latest_datapoint = max(data_points, key=lambda x: x["Timestamp"])
            return latest_datapoint["Sum"]
        else:
            return None

    except Exception as e:
        print(f"Error querying CloudWatch for {tg_arn}: {e}")
        return None

def put_relabeled_metric(service_name, request_count):
    """Put the same metric with a predictable dimension name for KEDA"""
    clean_name = clean_service_name(service_name)

    cloudwatch.put_metric_data(
        Namespace=CUSTOM_NAMESPACE,
        MetricData=[
            {
                "MetricName": "RequestCount",
                "Dimensions": [
                    {"Name": "ServiceName", "Value": clean_name}
                ],
                "Timestamp": datetime.datetime.utcnow(),
                "Value": request_count,
                "Unit": "Count"
            }
        ]
    )

def clean_service_name(service_name):
    """Clean service name for CloudWatch dimension"""
    import re
    clean_name = re.sub(r'[^a-zA-Z0-9\.\-_:#/+=@]', '_', service_name)

    if len(clean_name) > 255:
        clean_name = clean_name[:255]

    if not clean_name:
        clean_name = "unknown_service"

    return clean_name
