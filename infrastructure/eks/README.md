<!-- TOC -->
* [Elastic Kubernetes Service (EKS)](#elastic-kubernetes-service-eks)
  * [Overview](#overview)
  * [Environments Networks and Regions](#environments-networks-and-regions)
    * [Dev](#dev)
    * [Prod](#prod)
  * [DNS and Certificates](#dns-and-certificates)
    * [DNS](#dns)
    * [Certificates](#certificates)
  * [Configuration Management](#configuration-management)
  * [Cluster Add-ons and Customizations](#cluster-add-ons-and-customizations)
  * [Service Accounts](#service-accounts)
  * [End-to-End Validation](#end-to-end-validation)
  * [AWS Secret Manager Secrets and Service Account](#aws-secret-manager-secrets-and-service-account)
    * [Exposing secret to EKS](#exposing-secret-to-eks)
  * [Kubectl Authentication and Authorization](#kubectl-authentication-and-authorization)
    * [Access Levels](#access-levels)
    * [IAM Access Groups for EKS](#iam-access-groups-for-eks)
    * [Generating Kubeconfig for users](#generating-kubeconfig-for-users)
    * [IAM Role ARNs](#iam-role-arns)
  * [Useful Links](#useful-links)
<!-- TOC -->

# Elastic Kubernetes Service (EKS)
This document provides in depth description of how we deploy, configure and manage our EKS clusters.

**NOTE:** If you are looking for instructions on how to setup AWS or Kubernetes access please refer to this document:

[AWS IAM/EKS User Access Setup ](https://www.notion.so/nextchaptersoftware/AWS-IAM-User-Access-Setup-9df153f43d6b43228d7973df5aa26d40?pvs=4)

## Overview

We have migrated EKS cluster deployment from `eksctl` to CDK code. We use EKS Blueprints along with several custom in-house add-ons to deploy EKS clusters.

- It now supports multi-cluster deployment for Blue-Green deployments
- All clusters are deployed to the same VPC (keeps NAT addresses consistent across deployments)
- Changes to EKS clusters are deployed as part of our regular CI/CD for CDK

## Environments Networks and Regions

### Dev

| Region    | CIDR          | Name  | Overlay CIDR  | Dedicated Hosted Zone (public) | Status     | Notes                                   |
| --------- |---------------|-------|---------------|--------------------------------|------------|-----------------------------------------|
| us-west-2 | **********/16 | dev   | **********/16 | us-west-2.dev.getunblocked.com | Deprecated | Old eksctl subnet - free for future use |
| us-west-2 | **********/16 | dev-2 | **********/16 | us-west-2.dev.getunblocked.com | Deployed   |                                         |
| us-east-2 | **********/16 | dev   | **********/16 | us-east-2.dev.getunblocked.com | DR         |                                         |

### Prod

| Region    | CIDR         | Name   | Overlay CIDR  | Dedicated Hosted Zone (public)  | Status      | Notes                                    |
| --------- |--------------|--------|---------------|---------------------------------|-------------|------------------------------------------|
| us-west-2 | ********/16  | prod   | **********/16 | us-west-2.prod.getunblocked.com | Deprecated  | Old eksctl subnet - free for future use  |
| us-west-2 | ********/16  | prod-2 | **********/16 | us-west-2.prod.getunblocked.com | Deployed    |                                          |
| us-east-2 | *********/16 | prod   | **********/16 | us-east-2.prod.getunblocked.com | DR          |                                          |

## DNS and Certificates

### DNS

- **Shared hosted zones**
  - Clusters in the same region share the same hosted zone `{AWS_REGION}.{ENV}.getunblocked.com`
    - e.g `us-west-2.prod.getunblocked.com` for all clusters in US-WEST-2 Prod
    - e.g `us-east-2.dev.getunblocked.com` for all **DR** clusters in US-EAST-2 Dev
- **Alb and cluster names**
  - Clusters MUST have unique names in all lowercase (e.g prod-2, prod-2 etc)
  - Cluster names MUST be passed to helm deployments to ensure correct Application Load Balancer names and to avoid DNS clash
  - Each ALB can belong to one and only one cluster!
  - Examples of ALB names:
    - `alb.prod-2.us-west-2.prod.getunblocked.com` (`prod-2` cluster external ALB)
    - `adminweb.prod-2.us-west-2.prod.getunblocked.com` (`prod-2` cluster internal AdminWeb ALB)
- **DNS record structure**
  - CloudFront references the global ALB CNAME record `alb.{ENV}.getunblocked.com` **(Manually Updated)**
    - e.g `alb.prod.getunblocked.com`
  - Each global ALB CNAME references one or more cluster ALB records
  - e.g `alb.prod.getunblocked.com` references `alb.dev-1.us-west-2.dev.getunblocked.com`
  - Internal only ALBs do NOT use CloudFront! They are accessed directly via a global CNAME
    - e.g `admin.prod.getunblocked.com`

### Certificates

  - Regions CANNOT share certificates (AWS ACM limitation)
  - All clusters in the **same region** share the same certificates
  - Certificates are issued for a global ALB CNAME
  - Certificates are applied to ALBs by AWS Loadbalancer Controller running on each cluster
  - ALBs with different names/addresses CANNOT share a certificate

## Configuration Management

- All cluster configurations are managed by CDK
  - EKS VPC and network is created via `infrastructure/cdk/core/lib/vpc/eks-network-stack.ts`
  - EKS cluster, add-ons, manifests and other automation are configured via `infrastructure/cdk/core/lib/eks/eks-clusters-stack.ts`
  - You can modify cluster attributes by updating config files under `infrastructure/cdk/core/config/{ENV}-{REGIOM}.json`
  - CDK automation supports multi-cluster deployments (useful for Blue-Green upgrades)
    - `eksClusters` configuration is an array of clusters
    - All clusters are deployed to the same VPC so no further network changes should be required
    - Each cluster MUST HAVE A UNIQUE NAME

## Cluster Add-ons and Customizations

Add-ons and third-party controllers are used to provide extra functionality and integration with other AWS services such as Route53, NLB, ELB etc. Access permissions for these components are provided using IAM roles/policies attached to service account.

Add-on types:
- [Managed add-ons](https://aws-quickstart.github.io/cdk-eks-blueprints/addons/) maintained by EKS Blueprint (e.g `blueprints.addons.CalicoOperatorAddOn()`)
- [Custom Helm add-on](https://aws-quickstart.github.io/cdk-eks-blueprints/extensibility/#helm-add-ons) (e.g `infrastructure/cdk/core/lib/eks/addons/cert-manager-csi-driver`)
- [Custom non-Helm add-on](https://aws-quickstart.github.io/cdk-eks-blueprints/extensibility/#non-helm-add-ons) (e.g `infrastructure/cdk/core/lib/eks/addons/external-users-rbac`)

List of active add-ons can be found [here](infrastructure/cdk/core/lib/eks/addons/addons.ts)

Other customizations and configurations that cannot be made into an add-on are kept under `infrastructure/cdk/core/lib/eks/utils`

## Service Accounts
All service accounts that require access to AWS resources (e.g Services that need access to RDS) are managed via CDK.
- All IAM policies are defined in `infrastructure/cdk/core/assets/eks/service-accounts/policies.json`
- Each service account can reference one or more IAM policy using the policy name (JSON key)
- All service accounts are defined in `infrastructure/cdk/core/config/{ENV}-{REGIOM}.json` under cluster config

## Upgrade procedure for A/B deployments
### Prepare cluster configuration

```json
{
    "enabled": true,
    "name": "prod-2",
    "version": "V1_31",
    "kubeProxyAddOnVersion": "v1.31.2-eksbuild.3",
    "enableAlbController": true,
    "createDefaultDenyPolicy": false,
    "hostedZoneId": "Z046326532BV8FIH2UJE2",
    "enable_addon_falco": false,
    "enable_addon_grafana": false,
    "enable_addon_refinery": false,
    "enable_addon_priority_class": true,
    "enable_addon_cert_manager_csi": false,
    "enable_addon_kms_issuer": false,
    "enable_addon_external_rbac": false,
    "enable_addon_prefect_server": false,
    "enable_addon_prefect_workers": false,
    "enable_addon_prefect_external_rbac": false,
    "managedNodeGroup": [
        {
            "id": "prod-2-group1-c5a-4xlarge",
            "minSize": 15,
            "maxSize": 25,
            "maxUnavailablePercentage": 30,
            "instanceType": "c5a.4xlarge"
        },
        {
            "id": "prod-2-workers-c5a-xlarge",
            "minSize": 3,
            "maxSize": 10,
            "maxUnavailablePercentage": 30,
            "instanceType": "c5a.2xlarge",
            "diskSize": 250,
            "taints": [
                {
                    "effect": "NO_SCHEDULE",
                    "key": "node-class",
                    "value": "worker"
                }
            ]
        }
    ]
}
```
1. Copy the existing cluster configuration
2. Give the cluster a unique `name` (e.g if prod-1 is actively use prod-2 instead)
3. Update `version` field to one of the values listed [here](https://docs.aws.amazon.com/cdk/api/v2/docs/aws-cdk-lib.aws_eks.KubernetesVersion.html)
4. Get the correct version for a compatible `kubeProxyAddOnVersion` by running the following command:
    ```bash
    # Replate ${VERSION} with the correct value (e.g 1.31)
    aws eks describe-addon-versions --addon-name kube-proxy  --kubernetes-version ${VERSION} \
                  --query "addons[].addonVersions[].[addonVersion, compatibilities[].defaultVersion]" \
                 --output text

    # Important: Copy the entire build number e.g 'v1.31.2-eksbuild.3'
    ```
5. Except `enableAlbController` and `enable_addon_priority_class`, set all Addons to `false`
6. Set `enableAlbController`, and `enable_addon_prefect_external_rbac` to `false`
7. Rename all node groups according to your new cluster name
8. Deploy the base cluster e.g `cdk deploy EksClustersStack  -c config=prod-us-west-2`
9. Now set the following to `true` and re-deploy
   10. `enable_addon_external_rbac`
   11. `createDefaultDenyPolicy`
12. Now set the following to `true` and re-deploy
    13. `enable_addon_grafana`
    14. `enable_addon_refinery`
    15. `enable_addon_cert_manager_csi`
    16. `enable_addon_kms_issuer`
17. Now set the following to `true` and re-deploy
    18. `enable_addon_prefect_server`
    19. `enable_addon_prefect_workers`
    20. `enable_addon_falco`
21. Finally set `enable_addon_prefect_external_rbac` and `enable_addon_falco` to `true` and redeploy

### Generate Kubeconfig for CI

Instructions provided in 1Password under `deploybot kubeconfigs` secret

### Temporary CI deploy target setup

- Get Kube API host url from the kubeconfig file from last step
- Make sure to remove the scheme from Kube API host env var in GH actions
- Make sure to set the correct cluster name and enviroment names ()

```yaml
  deploy-prod2:
    needs: [ package, deploy-dev ]
    if: ${{ (github.ref == 'refs/heads/main') && !contains(vars.SUSPEND_PROD_DEPLOYMENTS, 'true') }}
    uses: ./.github/workflows/ci-services-deploy.yml
    permissions:
      contents: read
      actions: read
    with:
      deploy-env: "prod"
      gh-environment: "production"
      image-tag: ${{ needs.package.outputs.image-tag }}
      annotate-deployment: 'true'
      kube-cluster-name: "prod-2"                            <----- must match your new cluster name
      kubeconfig: ${{ vars.TEMP_PROD2_KUBECONFIG }}
      kube-api-host: "C6F76E55F0113245D27CA850964A5ECC.gr7.us-west-2.eks.amazonaws.com"  <----- no scheme in url
    secrets: inherit
```

### Don't forget to update ALB addresses (Look at ALB section)

## End-to-End Validation

1. Use a DNS name following the format  `${SRV_NAME}${CLUSTER_NAME}.${EKS_DEPLOY_REGION}.${EKS_DEPLOY_ENV}.getunblocked.com` (e.g `test.us-west-2.dev.getunblocked.com`)
2. Substitute the name from previous step as `FQDN_GOES_HERE` in following spec

```YAML
apiVersion: v1
kind: Service
metadata:
  name: nginx
  annotations:
    external-dns.alpha.kubernetes.io/hostname: FQDN_GOES_HERE
spec:
  type: LoadBalancer
  ports:
  - port: 80
    name: http
    targetPort: 80
  - port: 443
    name: https
    targetPort: 80
  selector:
    app: nginx
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx
spec:
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - image: nginx
        name: nginx
        ports:
        - containerPort: 80
          name: http

```

3. Save to a file, switch kubectl context to new cluster and deploy `kubectl apply -f PATH_TO_YAML_FILE.yaml`
4. Open http://test.us-west-2.dev.getunblocked.com in browser (nginx homepage)
5. If all is good, run `kubectl delete -f PATH_TO_YAML_FILE.yaml` to cleanup test deployment

## AWS Secret Manager Secrets and Service Account
The AWS provider for the Secrets Store CSI Driver allows you to make secrets stored in Secrets Manager and parameters stored in Parameter Store appear as files mounted in Kubernetes pods.

### Exposing secret to EKS

1. Add your secret to secret manager and note its ARN
2. Add a new service account with inline policy to EKS config yaml file (e.g look at how we create postgres service account)
3. In the policy grant `"secretsmanager:GetSecretValue` and `"secretsmanager:DescribeSecret"` on resource ARN for the target secret

```yaml
iam:
  withOIDC: true
  serviceAccounts:
    ...
    - metadata:
        name: redis-unblocked
        namespace: default
      attachPolicy:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - "secretsmanager:GetSecretValue"
          - "secretsmanager:DescribeSecret"
          Resource: 'arn:aws:secretsmanager:us-west-2:************:secret:redis-unblocked-password-*'
```

4. Create service account by running `eksctl create iamserviceaccount --config-file=PATH_TO_CLUSTER_YAML.yaml --approve` and note service account name (e.g for in our example that would be `redis-unblocked`)
5. Add secret resource to your Kubernetes helm chart, replace `objectName` value with name of your secret in **AWS Secret Manager**, give the provider class a user friendly name and then deploy it!

```yaml
apiVersion: secrets-store.csi.x-k8s.io/v1alpha1
kind: SecretProviderClass
metadata:
  name: redis-unblocked-user-aws-secret
spec:
  provider: aws
  parameters:
    objects: |
        - objectName: "redis-unblocked-password-1"
          objectType: "secretsmanager"
```

6. Add secret mount with custom CSI driver to your deployment under `spec` key (`secretProviderClass` is set to `name` field from previous step)

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      serviceAccountName: ">>>> SERVICE_ACCOUNT_NAME_GOES_HERE <<<<<"
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: ">>>> SECRET PROVIDER NAME GOES HERE <<<<<"
      containers:
      - name: nginx-deployment
        image: nginx
        ports:
        - containerPort: 80
        volumeMounts:
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"
          readOnly: true
```

7. for more info check out this page: [AWS Docs](https://github.com/aws/secrets-store-csi-driver-provider-aws)


## Kubectl Authentication and Authorization
Our EKS clusters are configured to use IAM as primary identity provider. Access to each EKS cluster is gated using IAM roles attached to IAM users/groups. Each IAM role is bound to a Kubernetes role/clusterrole via  `iamidentitymapping` provided through IODC controller.

- **roles/rolebindings** are scoped to a single k8s **namespace**
- **clusterrole/clusterrolebinding** are scoped to an entire k8s **cluster**
- IAM roles are **NOT** the same as k8s roles/clusterrole
- `cdk` takes care of setting up some default IAM/K8s resources to grant AWS account admins full access to EKS cluster.
- **All AWS account admins have full EKS access by default**

Here is the full picture for namespace scoped access:

- A K8s role defines what `apiGroups/resources/verbs` can be accessed
- A K8s rolebinding defines what user (within k8s) to associated with a role
- An IAM Identity Role is used to facilitate mapping between K8s user and an IAM role.
- `cdk` with the help of IAM `IODC` integration associates the K8s user to the IAM identity role
- Users and AWS entities can now assume the IAM role in order to gain access to apiGroups/resources/verbs defined in the k8s role
- In our multi-account setup, a cross-account IAM role under management account is used to grant groups (also defined under management account) access to the original IAM role. This allows for federated access to EKS clusters using IAM

### Access Levels

- **Full Admin**: Full access to AWS account as well as any EKS clusters in the account
- **Deploy Access([RBAC Spec](RBAC/deployer-access.yaml))** Grants necessary permissions required for deploying Kubernetes resources under `default` namespace.
- **Read Access ([RBAC Spec](RBAC/reader-access.yaml))**: Provides read only access to a subset or resources uder `default` namespace.

### IAM Access Groups for EKS

Following IAM groups under management AWS account provide full administrator access to EKS clusters in their respective environments:

| Name                                         | Target Environment | Access Level  | Notes                                      |
| -------------------------------------------- | ------------------ | ------------- | ------------------------------------------ |
| all-pre-prod-full-access                     | Dev                | Full Admin    | Admin access to Dev AWS account            |
| all-prod-full-access                         | Prod               | Full Admin    | Admin access to Prod AWS account           |
| all-envs-k8s-default-namespace-deploy-access | Prod & Dev         | Deploy Access | Used by CI/CD `deploybot` user             |
| all-envs-k8s-default-namespace-read-access   | Prod & Dev         | Read Access   | RO access for devs (mainly meant for prod) |

### Generating Kubeconfig for users

Note: Members of groups with `deploy` and `read` access CANNOT generate Kubernetes config files. A user with Admin level access can generate the `kubeconfig` file for any non-admin users.

```bash
# Where the new config will be saved
export KUBECONFIG=/tmp/kubeconfig

# CDK output of the EKS stack provides the necessary commands to update Kube config
# Here are the commands for our existing clusters

# Dev-1 Cluster
aws eks update-kubeconfig --name dev-1 --region us-west-2 --role-arn arn:aws:iam::************:role/dev-1-dev1AccessRole6278B1BE-RmCyuakNwCcL

# Dev-1 Cluster
aws eks update-kubeconfig --name prod-2 --region us-west-2 --role-arn arn:aws:iam::************:role/prod-2-prod1AccessRoleCDAFF533-4I1bfq8MvC8G

# Cleanup
unset KUBECONFIG

# Edit the config file at /tmp/kubeconfi if necessary and change `v1alpha1` to `v1beta1`
# Done, your Kube config file is ready!
# You can now copy it to ~/.kube/config or send it to the users
```

### IAM Role ARNs

Role ARN must be set as part of your AWS CLI profile setup in order for Kubeconfig files to work. More info can be found here:[AWS CLI role switching setup](https://www.notion.so/nextchaptersoftware/AWS-IAM-User-Access-Setup-9df153f43d6b43228d7973df5aa26d40#cd16c9d5d780477a895cae4be1b27252)

| Name                                         | Environment | ARN                                            | Notes |
| -------------------------------------------- | ----------- | ---------------------------------------------- | ----- |
| all-envs-k8s-default-namespace-deploy-access | Prod        | arn:aws:iam::************:role/K8sDeployerRole | -     |
| all-envs-k8s-default-namespace-read-access   | Prod        | arn:aws:iam::************:role/K8sReaderRole   | -     |
| all-envs-k8s-default-namespace-deploy-access | Dev         | arn:aws:iam::************:role/K8sDeployerRole | -     |
| all-envs-k8s-default-namespace-read-access   | Dev         | arn:aws:iam::************:role/K8sReaderRole   | -     |

## Useful Links

- [EKS Blueprints Docs](https://aws-quickstart.github.io/cdk-eks-blueprints/)
- https://www.eksworkshop.com
- [Intro to RBAC (Kubernetes)](https://www.eksworkshop.com/beginner/090_rbac/)
- [EKS Workshop](https://www.eksworkshop.com/beginner/091_iam-groups/intro/)
