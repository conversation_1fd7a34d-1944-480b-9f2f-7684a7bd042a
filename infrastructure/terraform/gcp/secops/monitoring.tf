# Email channel
resource "google_monitoring_notification_channel" "email" {
  display_name = "generic email for alerts"
  type         = "email"
  labels = {
    email_address = "<EMAIL>"
  }
}

resource "google_monitoring_alert_policy" "vm_cpu_high" {
  display_name = "High VM CPU utilization"
  combiner     = "OR"
  enabled      = true

  notification_channels = [
    google_monitoring_notification_channel.email.name
  ]

  # --- Condition 1: VM CPU utilization ---
  conditions {
    display_name = "CPU > 99% for 30m on any VM"

    condition_threshold {
      filter = <<-EOT
        resource.type = "gce_instance"
        AND metric.type = "compute.googleapis.com/instance/cpu/utilization"
        EOT

      # Align raw samples to 60s and average per series
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }

      comparison      = "COMPARISON_GT"
      threshold_value = 0.99
      duration        = "1800s"
      trigger {
        count = 1
      }
    }
  }

  # --- Condition 2: GKE node CPU utilization ---
  conditions {
    display_name = "GKE Node CPU > 99% for 30m"
    condition_threshold {
      filter = <<-EOT
        resource.type = "k8s_node"
        AND metric.type = "kubernetes.io/node/cpu/allocatable_utilization"
      EOT
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
      comparison      = "COMPARISON_GT"
      threshold_value = 0.99
      duration        = "1800s"
      trigger {
        count = 1
      }
    }
  }

  alert_strategy {
    auto_close = "86400s" # autoclose after 1 day if no data
  }

  documentation {
    content   = "CPU > 99% for 30 minutes on a VM. Investigate noisy neighbors, right-size, or scale out."
    mime_type = "text/markdown"
  }
}