# Allow SSH to the VPN VM with specific source ranges
resource "google_compute_firewall" "allow_ssh_vpn" {
  name    = "allow-ssh-${local.vpn_vm_name}"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["${local.vpn_ssh_access}"]
  target_tags   = ["${local.vpn_vm_name}"]
}

# Allow ICMP within the VPC
resource "google_compute_firewall" "allow_internal_icmp" {
  name    = "allow-internal-icmp"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "icmp"
  }

  source_ranges = [
    var.public_subnet_cidr,
    var.private_subnet_cidr
  ]
}

# Allow SSH to the temp VM with specific source ranges
resource "google_compute_firewall" "allow_ssh_temp" {
  name    = "allow-ssh-secops-temp"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  source_ranges = ["${local.ghe_ssh_access}"]
  target_tags   = ["temp"]
}

# Allow SSH to the tunnel VM with specific source ranges
resource "google_compute_firewall" "allow_ssh_tunnel" {
  name    = "allow-ssh-secops-tunnel"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["${local.ghe_ssh_access}","172.25.0.0/16"]
  target_tags   = ["tunnel"]
}

# Allow SSH to the tunnel VM with specific source ranges
resource "google_compute_firewall" "allow_all_tunnel" {
  name    = "allow-all-secops-tunnel"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["23-65535"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["tunnel"]
}


# Allow SSH to the GHE VM with specific source ranges
resource "google_compute_firewall" "allow_ssh_ghe" {
  name    = "allow-ssh-${local.ghe_vm_name}"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["22", "122"]
  }

  source_ranges = ["${local.ghe_ssh_access}","172.25.0.0/16"]
  target_tags   = ["${local.ghe_vm_name}"]
}

# Allow 8443 to the GHE VM with specific source ranges
resource "google_compute_firewall" "allow_8443_ghe" {
  name    = "allow-8443-${local.ghe_vm_name}"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["8443"]
  }

  source_ranges = ["172.25.0.0/16"]
  target_tags   = ["${local.ghe_vm_name}"]
}

# Allow HTTP(S) to the GHE VM
resource "google_compute_firewall" "allow_http_ghe" {
  name    = "allow-http-${local.ghe_vm_name}"
  network = module.vpc.network_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["443", "80"]
  }

  source_ranges = ["${local.ghe_ssh_access}","172.25.0.0/16"]
  target_tags   = ["${local.ghe_vm_name}"]
}