module "gke" {
  source  = "terraform-google-modules/kubernetes-engine/google//modules/private-cluster"
  version = "38.0.0"

  project_id             = var.project_id
  name                   = var.gke_name
  region                 = var.region
  zones                  = data.google_compute_zones.available.names
  network                = module.vpc.network_name
  subnetwork             = local.private_subnet_name
  ip_range_pods          = "${local.private_subnet_name}-${var.gke_name}-pods"
  ip_range_services      = "${local.private_subnet_name}-${var.gke_name}-services"
  create_service_account = true
  deletion_protection    = true

  #addons
  http_load_balancing        = true
  horizontal_pod_autoscaling = true
  filestore_csi_driver       = true
  dns_cache                  = true
  gateway_api_channel        = "CHANNEL_STANDARD"

  #networking
  network_policy           = true
  network_policy_provider  = "CALICO"
  enable_l4_ilb_subsetting = true

  #control plane access
  gcp_public_cidrs_access_enabled = false
  enable_private_endpoint         = true
  enable_private_nodes            = true
  master_authorized_networks      = var.master_authorized_networks

  #Node pools
  release_channel          = "STABLE" #UNSPECIFIED, RAPID, R<PERSON><PERSON><PERSON><PERSON>, ST<PERSON>LE
  remove_default_node_pool = true
  node_pools               = local.node_pools

  node_pools_oauth_scopes = {
    all = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
    ]
  }

  node_pools_labels = local.node_pools_labels

  node_pools_metadata = merge(
    { all = {} },
    { for pool in local.node_pools :
      pool.name => { "node-pool-metadata-custom-value" = pool.name }
    }
  )

  node_pools_taints = local.node_pools_taints

  node_pools_tags = merge(
    { all = [] },
    { for pool in local.node_pools :
      pool.name => [pool.name]
    }
  )

  depends_on = [module.vpc]

}
/*
resource "kubernetes_manifest" "gateway" {
  manifest = {
    apiVersion = "gateway.networking.k8s.io/v1"
    kind       = "Gateway"
    metadata = {
      name      = var.gke_gateway_name
      namespace = local.gateway_namespace
      annotations = {
        "networking.gke.io/certmap" = var.gke_test_certmap_name
      }
    }
    spec = {
      gatewayClassName = local.gateway_class_name
      listeners        = local.gateway_listener
      addresses        = local.gateway_addresses
    }
  }

  depends_on = [google_compute_global_address.gke_gateway, module.global_dns_cert]
}
*/