terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.46.0"
    }

    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.38"
    }

  }

  required_version = "~> 1.12.0"

  # Store the state in a GCS bucket
  backend "gcs" {
    bucket = "secops-467621_tfstate"
    prefix = "terraform.tfstate"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "kubernetes" {
  host                   = "https://${module.gke.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(module.gke.ca_certificate)
}