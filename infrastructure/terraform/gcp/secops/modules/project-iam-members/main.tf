variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "role" {
  description = "IAM role to assign (e.g., roles/owner, roles/editor)"
  type        = string
}

variable "user_emails" {
  description = "List of user emails to grant the role"
  type        = list(string)
}

resource "google_project_iam_member" "members" {
  for_each = toset(var.user_emails)

  project = var.project_id
  role    = var.role
  member  = "user:${each.value}"
}
