variable "subdomains" {
  description = "List of subdomains for DNS records"
  type        = list(string)
}

variable "managed_zone" {
  description = "Name of the managed zone"
  type        = string
}

variable "ttl" {
  description = "TTL value for DNS record"
  type        = number
  default     = 300
}

variable "ip_address" {
  description = "IP address for the DNS record"
  type        = string
}

resource "google_dns_record_set" "records" {
  for_each     = toset(var.subdomains)
  name         = "${each.key}."
  managed_zone = var.managed_zone
  type         = "A"
  ttl          = var.ttl

  rrdatas = [var.ip_address]
}
