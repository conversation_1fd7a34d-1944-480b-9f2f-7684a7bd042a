terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 5.10.0"
    }
  }
}

locals {
  # Domains that will be on the cert (wildcards OK)
  cert_domains = toset(var.domains)

  # DNS authorizations must be at the apex (strip leading "*.")
  auth_domains = toset([
    for d in local.cert_domains :
    (startswith(d, "*.") ? substr(d, 2, length(d) - 2) : d)
  ])
}

# --- (Optional) look up the Cloud DNS managed zone ---
data "google_dns_managed_zone" "zone" {
  count   = var.manage_dns ? 1 : 0
  project = coalesce(var.dns_project, var.project)
  name    = var.dns_zone
}

# --- DNS AUTHORIZATIONS (global) ---
resource "google_certificate_manager_dns_authorization" "this" {
  for_each = local.auth_domains

  name     = "${var.name_prefix}-${replace(lower(each.value), "/[^a-z0-9-]/", "-")}"
  domain   = each.value
  location = "global"
  labels   = var.labels
}

# --- CERTIFICATE (global, Google-managed) ---
resource "google_certificate_manager_certificate" "this" {
  name     = var.certificate_name
  location = "global"
  labels   = var.labels

  managed {
    domains = tolist(local.cert_domains)
    dns_authorizations = [
      for a in google_certificate_manager_dns_authorization.this : a.id
    ]
  }
}

# --- (Optional) Cloud DNS TXT records for ACME DNS-01 ---
# One TXT per apex in `auth_domains`, created only if manage_dns = true.
resource "google_dns_record_set" "acme_txt" {
  for_each = var.manage_dns ? google_certificate_manager_dns_authorization.this : {}

  project      = coalesce(var.dns_project, var.project)
  managed_zone = var.dns_zone

  # Use the FQDN the API gives us (already ends with a dot)
  name    = each.value.dns_resource_record[0].name
  type    = each.value.dns_resource_record[0].type # "TXT"
  ttl     = var.dns_ttl
  rrdatas = [each.value.dns_resource_record[0].data]

  # Make sure the zone actually matches the record name to avoid silent misroutes
  depends_on = [data.google_dns_managed_zone.zone]
}

# --- CERTIFICATE MAP (global) ---
resource "google_certificate_manager_certificate_map" "this" {
  count  = var.create_certificate_map ? 1 : 0
  name   = var.certificate_map_name
  labels = var.labels
}

# --- MAP ENTRIES (global, exact hostnames only) ---
resource "google_certificate_manager_certificate_map_entry" "this" {
  for_each = toset(var.map_hostnames)

  name         = "${var.name_prefix}-${replace(lower(each.value), "/[^a-z0-9-]/", "-")}"
  map          = var.certificate_map_name
  hostname     = each.value
  certificates = [google_certificate_manager_certificate.this.id]
  labels       = var.labels

  depends_on = [
    google_certificate_manager_certificate.this,
    google_certificate_manager_certificate_map.this
  ]
}

