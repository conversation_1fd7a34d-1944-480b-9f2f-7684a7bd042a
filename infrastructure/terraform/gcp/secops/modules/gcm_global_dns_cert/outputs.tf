output "dns_txt_records" {
  description = "ACME TXT records you need (if you manage DNS elsewhere)."
  value = {
    for d, a in google_certificate_manager_dns_authorization.this :
    d => {
      name  = a.dns_resource_record[0].name
      type  = a.dns_resource_record[0].type
      value = a.dns_resource_record[0].data
      ttl   = var.dns_ttl
    }
  }
}

output "certificate_id" {
  description = "Full ID of the Certificate Manager certificate."
  value       = google_certificate_manager_certificate.this.id
}

output "certificate_map" {
  description = "Certificate map name."
  value       = var.certificate_map_name
}

output "certificate_map_entries" {
  description = "Map of hostname -> entry name."
  value = {
    for k, v in google_certificate_manager_certificate_map_entry.this :
    k => v.name
  }
}

