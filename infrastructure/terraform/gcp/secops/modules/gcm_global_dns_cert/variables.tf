variable "project" {
  description = "Default project for Certificate Manager resources."
  type        = string
}

variable "certificate_name" {
  description = "Name of the Certificate Manager certificate (global)."
  type        = string
}

variable "domains" {
  description = "FQDNs for the cert. Wildcards OK (e.g., *.example.com). Include the base domain separately if needed."
  type        = list(string)
}

variable "certificate_map_name" {
  description = "Name of the global certificate map to attach entries to."
  type        = string
}

variable "create_certificate_map" {
  description = "Create the certificate map in this module?"
  type        = bool
  default     = true
}

variable "map_hostnames" {
  description = "Exact hostnames (no wildcards) to bind in the certificate map (e.g., jira.example.com)."
  type        = list(string)
}

variable "name_prefix" {
  description = "Prefix for per-domain resources (DNS auths, map entries)."
  type        = string
  default     = "cm"
}

variable "labels" {
  description = "Labels to apply to created resources."
  type        = map(string)
  default     = {}
}

# ----- Cloud DNS integration -----

variable "manage_dns" {
  description = "If true, create ACME TXT records in Cloud DNS."
  type        = bool
  default     = false
}

variable "dns_zone" {
  description = "Cloud DNS managed zone name (required if manage_dns = true)."
  type        = string
  default     = null
}

variable "dns_project" {
  description = "Project that holds the Cloud DNS zone (defaults to var.project)."
  type        = string
  default     = null
}

variable "dns_ttl" {
  description = "TTL for ACME TXT records."
  type        = number
  default     = 300
}

