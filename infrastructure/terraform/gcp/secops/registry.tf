# Enable required APIs
resource "google_project_service" "artifact_registry" {
  project            = var.project_id
  service            = "artifactregistry.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "iam_credentials" {
  project            = var.project_id
  service            = "iamcredentials.googleapis.com"
  disable_on_destroy = false
}

# Private by default (access is IAM-controlled)
resource "google_artifact_registry_repository" "docker_repo" {
  location      = var.region
  repository_id = "secops"
  description   = "Private Docker repo"
  format        = "DOCKER"

  labels = {
    env = "secops"
  }
  depends_on = [google_project_service.artifact_registry]
}