# VPC
module "vpc" {
  source  = "terraform-google-modules/network/google"
  version = "11.1.1"

  project_id   = var.project_id
  network_name = var.vpc_name
  routing_mode = "REGIONAL"

  subnets = [
    {
      subnet_name   = local.public_subnet_name
      subnet_ip     = var.public_subnet_cidr
      subnet_region = var.region
      description   = "Public subnet with internet access"
    },
    {
      subnet_name           = local.private_subnet_name
      subnet_ip             = var.private_subnet_cidr
      subnet_region         = var.region
      description           = "Private subnet with NAT"
      subnet_private_access = "true"
    }
  ]

  secondary_ranges = {
    (local.private_subnet_name) = [
      {
        range_name    = "${local.private_subnet_name}-${var.gke_name}-pods"
        ip_cidr_range = var.gke_pods_cidr
      },
      {
        range_name    = "${local.private_subnet_name}-${var.gke_name}-services"
        ip_cidr_range = var.gke_services_cidr
      }
    ]
  }

  routes = [
    {
      name              = "egress-internet"
      description       = "Default route to Internet"
      destination_range = "0.0.0.0/0"
      tags              = "public"
      next_hop_internet = "true"
    }
  ]
}

# Cloud NAT
module "cloud_nat" {
  source  = "terraform-google-modules/cloud-nat/google"
  version = "5.3.0"

  project_id = var.project_id
  region     = var.region
  router     = "${var.vpc_name}-router"
  network    = module.vpc.network_name

  create_router = true
  nat_ips       = [google_compute_address.nat_ip_[2].self_link]
  #nat_ips                            = concat(
  #[google_compute_address.nat_ip.self_link],   # this is already a string, wrap in []
  #google_compute_address.nat_ip_[*].self_link  # this is list(string)
  #)
  source_subnetwork_ip_ranges_to_nat = "LIST_OF_SUBNETWORKS"

  # NAT logs help confirm SNAT pressure/errors
  log_config_enable = true
  log_config_filter = "ERRORS_ONLY"

  subnetworks = [
    {
      name                     = local.private_subnet_name
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = []
    }
  ]

  # ---- DYNAMIC PORT ALLOCATION ----
  enable_dynamic_port_allocation = true

  # Per-VM floors/ceilings (must be powers of two; min >= 32)
  # Tune these to your workload; examples shown:
  min_ports_per_vm = 1024 # good starting floor for busy nodes
  #max_ports_per_vm = 65536  # allow burst without over-allocating to all VMs

  depends_on = [module.vpc, google_compute_address.nat_ip]
}

resource "google_compute_address" "nat_ip" {
  name   = "nat-external-ip"
  region = var.region
}

# static IP for the NAT
resource "google_compute_address" "nat_ip_" {
  count  = 3
  name   = "nat-external-ip-${count.index}"
  region = var.region
}

# static IP for the VPN
resource "google_compute_address" "vpn_ip" {
  name   = "vpn-ip"
  region = var.region
}

# static IP for the TEMP
resource "google_compute_address" "temp_ip" {
  name   = "temp-ip"
  region = var.region
}

# static IP for GHE
resource "google_compute_address" "ghe_ip" {
  name   = "ghe-ip"
  region = var.region
}

# static IP for tunnel
resource "google_compute_address" "tunnel_ip" {
  name   = "tunnel-ip"
  region = var.region
}

# static IP for the GKE Gateway
resource "google_compute_global_address" "gke_gateway" {
  name    = "gke-secops-gateway"
  project = var.project_id
}

# Global Cloud Armor Security Policy
resource "google_compute_security_policy" "waf_global" {
  name = "waf-global-policy"

  # --- Rule 1: ALLOW ALL (keeps services reachable from anywhere)
  # NOTE: This rule precedes the default deny and permits traffic.
  # Remove or narrow this later (e.g., to ip:inRange(...) ) when you want to lock down.
  rule {
    priority = 1000
    action   = "allow"
    match {
      versioned_expr = "SRC_IPS_V1"
      # All IPv4 + IPv6 — use specific CIDRs here when you’re ready to restrict.
      config {
        src_ip_ranges = ["0.0.0.0/0", "::/0"]
      }
    }
    description = "allow all traffic from anywhere"
  }

  # (Optional) You can add more specific allow rules above (lower priority numbers)
  # and keep the broad allow-all below them if you want a phased hardening.

  # --- Default (implicit) action: deny(403)
  # Cloud Armor uses the highest-priority matching rule; if none match,
  # the policy's default action applies. In Terraform this is modeled as:
  # a final low-priority catch-all deny rule. Priority must be unique.
  rule {
    priority = 2147483647
    action   = "deny(403)"
    match {
      versioned_expr = "SRC_IPS_V1"
      config {
        src_ip_ranges = ["*"]
      }
    }
    description = "Default deny with 403"
  }

  # Enable Adaptive Protection (Layer7 DDoS)
  adaptive_protection_config {
    layer_7_ddos_defense_config {
      enable = true
      # (Optional) rule_visibility = "STANDARD" | "PREMIUM"
    }
  }
}