# GCP Infrastructure with Terraform

This repository contains Terraform configurations to manage Google Cloud Platform (GCP) infrastructure.  
It provisions core resources such as **VPC networking, IAM, DNS, certificates, firewall rules, VM instances, and GKE clusters** (with node pools).

---

## 🚀 Running Terraform Locally

From the root of the repo:

1. **Initialize providers & backend**
   ```
   terraform init
   ```

2. **Check planned changes**
   ```
   terraform plan
   ```

3. **Apply changes**
   ```
   terraform apply
   ```

   Add `-auto-approve` if you want to skip manual confirmation (not recommended locally).
---

## 🧩 Managing GKE Node Pools

Node pools are defined in **`main.tf`** (root)

### Create a New Node Pool
1. Open `main.tf`.
2. append a new map into locals.node_pools list:
  ```
  locals {
    node_pools = [
      {
        name        = "existing-pool"
        machine_type = "e2-medium"
        # ...
      },

      {
        name                 = "new-node-pool"
        machine_type         = "e2-standard-4"
        node_locations       = join(",", data.google_compute_zones.available.names)
        total_min_count      = 2
        total_max_count      = 5
        local_ssd_count      = 0
        spot                 = false
        disk_size_gb         = 30
        disk_type            = "pd-standard"
        image_type           = "COS_CONTAINERD"
        enable_gcfs          = false
        enable_gvnic         = false
        logging_variant      = "DEFAULT"
        auto_repair          = true
        auto_upgrade         = true
        service_account_name = "service-account@${var.project_id}.iam.gserviceaccount.com"
        preemptible          = false
        initial_node_count   = 1
        enable_private_nodes = true
      }
    ]
  }
  ```

3. Add labels for the new node pool
  ```
  locals {
    ...

    node_pools_labels = {
      all = {}

      existing labels...

      new-node-pool = {
        workload = "your-new-workload"
      }
    }
  }
  ```

4. Add taints for the new node pool
  ```
  locals {
    ...

    node_pools_taints = {
      all = []

      existing taints...

      new-node-pool = [
        {
          key = "new-node-pool"
          value = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }
  ```

5. Run:
   ```
   terraform plan
   terraform apply
   ```

### Modify a Node Pool
- Adjust fields in `locals.node_pools` (e.g. `machine_type`, `disk_type`, `disk_size_gb`).

### Delete a Node Pool
- Remove the node pool map and it corresponding labels and taints

---
### Common changes
- **Machine type**: `e2-medium`, `n2-standard-4`, etc.
- **Disk type**: `pd-standard`, `pd-balanced`, `pd-ssd`.
- **Labels**: key-value metadata for scheduling.
- **Taints**: restrict workloads that can run on the pool.
- **Disk size**: increase when workloads need more storage.

---
## ✅ Best Practices

- Always run `terraform plan` before applying to confirm changes.
- Avoid `-auto-approve` outside CI/CD pipelines.
- Be cautious with GKE changes: altering machine type, disk, or taints may force **node pool recreation** and cause workload disruption.
- Review IAM (`iam.tf`) and firewall (`firewall.tf`) changes carefully for least-privilege and security posture.

---
