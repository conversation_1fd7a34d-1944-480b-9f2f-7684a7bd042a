# Module to issue certificates on Certificate Manager (with Certificates and Certificate maps)
module "global_dns_cert" {
  source = "./modules/gcm_global_dns_cert"

  project                = var.project_id
  certificate_name       = "gke-secops-cert-global"
  certificate_map_name   = "gke-secops-test"
  create_certificate_map = true

  domains = var.test_wildcard_domain

  # Bind exact hosts
  map_hostnames = var.test_hostnames

  # Create the ACME TXT in this Cloud DNS zone
  manage_dns  = true
  dns_zone    = google_dns_managed_zone.secops.name
  dns_project = var.project_id
  dns_ttl     = 300

  labels = {
    env = "test"
  }
}
