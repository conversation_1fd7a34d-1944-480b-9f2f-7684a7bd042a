module "project_owners" {
  source     = "./modules/project-iam-members"
  project_id = var.project_id
  role       = "roles/owner"

  user_emails = var.project_owner_emails
}

# 1) Service Account for GitHub Actions
resource "google_service_account" "gha" {
  account_id   = local.gha_sa_name
  display_name = "${local.gha_sa_name} SA for GKE ${module.gke.name} cluster"
}

# 2) Grant minimal IAM on the project so the SA can run `gcloud container clusters get-credentials`
#    This lets it discover the cluster & fetch an access token; actual kubectl permissions are via K8s RBAC.
resource "google_project_iam_member" "gha_cluster_view" {
  project = var.project_id
  role    = "roles/container.clusterViewer"
  member  = "serviceAccount:${google_service_account.gha.email}"
}

# (Optional) If you want it to manage node pools/etc. via API, grant admin (broader):
#   role    = "roles/container.admin"

# 3) Create a JSON key for the SA (store securely; rotate!)
resource "google_service_account_key" "gha_key" {
  service_account_id = google_service_account.gha.name
  keepers = {
    # change this to rotate the key intentionally
    purpose = local.gha_sa_name
  }
}

resource "google_project_service" "required" {
  for_each           = toset(local.services)
  project            = var.project_id
  service            = each.key
  disable_on_destroy = false
}

# Grant minimal read so gcloud can look up the project/org tree cleanly
# roles/browser ⊇ resourcemanager.projects.get and basic metadata listing
resource "google_project_iam_member" "ci_browser" {
  project = var.project_id
  role    = "roles/browser"
  member  = "serviceAccount:${google_service_account.gha.email}"

  depends_on = [google_project_service.required]
}

resource "google_project_iam_member" "gke_nodes_ar_reader" {
  project = var.project_id
  role    = "roles/artifactregistry.reader"
  member  = "serviceAccount:${module.gke.service_account}"
}
/*
# The Role
resource "kubernetes_role" "deployer" {
  metadata {
    name      = "deployer-role"
    namespace = "default"
  }

  rule {
    api_groups = [
      "",
      "apps",
      "batch",
      "extensions",
      "networking.k8s.io",
      "secrets-store.csi.x-k8s.io",
      "crd.projectcalico.org",
      "keda.sh",
      "autoscaling",
      "external-secrets.io",
      "rbac.authorization.k8s.io",
      "gateway.networking.k8s.io",
      "networking.gke.io",
      "policy",
      "actions.github.com"
    ]
    resources = [
      "configmaps",
      "cronjobs",
      "deployments",
      "events",
      "ingresses",
      "jobs",
      "pods",
      "pods/attach",
      "pods/exec",
      "pods/log",
      "pods/portforward",
      "secrets",
      "services",
      "replicasets",
      "networkpolicies",
      "secretproviderclasses",
      "scaledobjects",
      "horizontalpodautoscalers",
      "externalsecrets",
      "roles",
      "rolebindings",
      "serviceaccounts",
      "persistentvolumeclaims",
      "statefulsets",
      "httproutes",
      "healthcheckpolicies",
      "poddisruptionbudgets",
      "autoscalingrunnersets"
    ]
    verbs = [
      "create",
      "delete",
      "describe",
      "get",
      "list",
      "patch",
      "update"
    ]
  }
}

resource "kubernetes_role_binding" "gha_ns_edit" {
  metadata {
    name      = local.gha_sa_name
    namespace = "default"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role.deployer.metadata[0].name
  }
  subject {
    kind      = "User"
    name      = google_service_account.gha.email
    api_group = "rbac.authorization.k8s.io"
  }

  depends_on = [module.gke]
}

resource "kubernetes_cluster_role" "deploybot_clusterrole" {
  metadata {
    name = "deploybot-clusterrole"
  }

  rule {
    api_groups = [
      "rbac.authorization.k8s.io"
    ]
    resources = [
      "clusterroles",
      "clusterrolebindings"
    ]
    verbs = [
      "get",
      "list",
      "watch"
    ]
  }
}

resource "kubernetes_cluster_role_binding" "deploybot_binding" {
  metadata {
    name = "deploybot-clusterrolebinding"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = kubernetes_cluster_role.deploybot_clusterrole.metadata[0].name
  }

  subject {
    kind      = "User"
    name      = google_service_account.gha.email
    api_group = "rbac.authorization.k8s.io"
  }
}
*/
resource "google_project_service" "iam" {
  project = var.project_id
  service = "iam.googleapis.com"
}

resource "google_project_service" "sts" {
  project = var.project_id
  service = "sts.googleapis.com"
}

resource "google_project_service" "iamcredentials" {
  project = var.project_id
  service = "iamcredentials.googleapis.com"
}

resource "google_service_account" "ci" {
  project      = var.project_id
  account_id   = var.ci_service_acct
  display_name = "GitHub Actions CI Infra GCP"
}

resource "google_project_iam_member" "ci_project_roles" {
  for_each = toset(local.ci_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.ci.email}"
}

resource "google_iam_workload_identity_pool" "github" {
  project                   = var.project_id
  workload_identity_pool_id = var.wif_pool_id # e.g., "github-pool"
  display_name              = "GitHub Actions Pool"
  description               = "OIDC federation for GitHub Actions"

  depends_on = [
    google_project_service.iam,
    google_project_service.sts,
  ]
}

resource "google_iam_workload_identity_pool_provider" "github" {
  project                            = var.project_id
  workload_identity_pool_id          = google_iam_workload_identity_pool.github.workload_identity_pool_id
  workload_identity_pool_provider_id = var.wif_provider_id # e.g., "github"
  display_name                       = "GitHub OIDC"
  description                        = "OIDC provider for GitHub Actions"
  attribute_condition                = "attribute.repository==\"${var.github_repository}\""

  oidc {
    issuer_uri = "https://token.actions.githubusercontent.com"
  }

  attribute_mapping = {
    "google.subject"       = "assertion.sub"
    "attribute.repository" = "assertion.repository"
    "attribute.workflow"   = "assertion.workflow"
    "attribute.ref"        = "assertion.ref"
  }

  depends_on = [google_iam_workload_identity_pool.github]
}

resource "google_service_account_iam_member" "ci_wif_bind" {
  service_account_id = google_service_account.ci.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "principalSet://iam.googleapis.com/${google_iam_workload_identity_pool.github.name}/attribute.repository/${var.github_repository}"
}