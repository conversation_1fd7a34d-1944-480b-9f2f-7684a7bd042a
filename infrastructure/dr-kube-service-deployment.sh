#!/bin/bash
#
# This script is used to deploy Unblocked core
# services to Kubernetes during an emergency DR
# event
#
# Usage:
#   - Follow instructions provided in DR playbook https://www.notion.so/nextchaptersoftware/Disaster-Recovery-Playbook-807fd27cd4604ed5ba2ce02218a92dde?pvs=4
#   - Once ready to deploy, retrieve both the image name, deployment SHA and build number from github actions (You can get those values from any service deploy jobs)
#   - Switch kubectl context to your target environment/cluster
#   - Retrieve the latest list of micro-services from github actions job specs
#   - Update the following variables with information from previous steps and run `bash dr-kube-service-deployment.sh`
#
IMAGE_TAG="1-75157b22fc57258ebd0b6797fdf65258f02cd4a1"
IMAGE_REPO_BASE_URL="129540529571.dkr.ecr.us-east-2.amazonaws.com"
VERSION="75157b22fc57258ebd0b6797fdf65258f02cd4a1"
BUILD_NUMBER=19325
REGION=us-east-2

# List of services
SERVICES="adminweb \
api \
asset \
auth \
data
notification \
pusher \
scm \
search \
slack \
telemetry \
topic \
transcription \
video \
webhook"

# Switch to git repo root directory
cd $(git rev-parse --show-toplevel)

# Deploy each service
for service in $SERVICES
do
   helm upgrade --atomic --timeout 15m --install "${service}service" ./projects/services/"${service}service"/.helm/"${service}service" \
       -f ./projects/services/"${service}service"/.helm/"${service}service"/values.yaml \
       -f ./projects/services/"${service}service"/.helm/"${service}service"/values-dev-us-east-2.yaml \
       --kube-context dev-dr \
       --set-string baseservice.image.repository="${IMAGE_REPO_BASE_URL}/${service}" \
       --set-string baseservice.image.tag=${IMAGE_TAG} \
       --set-string baseservice.service.environment=dev \
       --set-string baseservice.service.region=${REGION} \
       --set-string baseservice.nameOverride="${service}service" \
       --set-string baseservice.podAnnotations.version=${VERSION} \
       --set-string baseservice.podAnnotations.buildNumber=${BUILD_NUMBER}
done
