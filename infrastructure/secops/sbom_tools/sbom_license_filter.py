#!/usr/bin/env python3
"""
SBOM License Filter Script

This script reads an SPDX JSON SBOM file and filters packages by license types.
It supports multiple license types and partial matching.

Usage:
    python sbom_license_filter.py <sbom_file> <license1> [license2] [license3] ...

Examples:
    python sbom_license_filter.py unblocked-generate-sbom.spdx.json MIT
    python sbom_license_filter.py unblocked-generate-sbom.spdx.json MIT LGPL Apache
    python sbom_license_filter.py unblocked-generate-sbom.spdx.json GPL --exclude
"""

import json
import sys
import argparse
from typing import List, Dict, Any, Set


def load_sbom(file_path: str) -> Dict[str, Any]:
    """Load SPDX JSON SBOM file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{file_path}': {e}")
        sys.exit(1)


def extract_license_info(package: Dict[str, Any]) -> Set[str]:
    """Extract license information from a package."""
    licenses = set()

    # Check licenseConcluded field
    if 'licenseConcluded' in package and package['licenseConcluded'] != 'NOASSERTION':
        licenses.add(package['licenseConcluded'])

    # Check licenseDeclared field
    if 'licenseDeclared' in package and package['licenseDeclared'] != 'NOASSERTION':
        licenses.add(package['licenseDeclared'])

    # Check licenseInfoFromFiles field
    if 'licenseInfoFromFiles' in package:
        for license_info in package['licenseInfoFromFiles']:
            if license_info != 'NOASSERTION':
                licenses.add(license_info)

    return licenses


def normalize_license(license_str: str) -> str:
    """Normalize license string for comparison."""
    # Handle common license expressions and variations
    license_str = license_str.upper().strip()

    # Handle license expressions with OR/AND operators
    # For expressions like "(MIT OR GPL-3.0-or-later)", we need to extract individual licenses
    if ' OR ' in license_str or ' AND ' in license_str:
        # For compound expressions, return as-is for now - will be handled in matching
        return license_str

    # Remove common suffixes and prefixes
    license_str = license_str.replace('LICENSE', '').replace('LICENCE', '').strip()

    # Common exact mappings
    exact_mappings = {
        'APACHE-2.0': 'APACHE-2.0',
        'APACHE 2.0': 'APACHE-2.0',
        'MIT': 'MIT',
        'GPL-2.0': 'GPL-2.0',
        'GPL-2.0-OR-LATER': 'GPL-2.0',
        'GPL-3.0': 'GPL-3.0',
        'GPL-3.0-OR-LATER': 'GPL-3.0',
        'LGPL-2.1': 'LGPL-2.1',
        'LGPL-2.1-OR-LATER': 'LGPL-2.1',
        'LGPL-3.0': 'LGPL-3.0',
        'LGPL-3.0-OR-LATER': 'LGPL-3.0',
        'AGPL-3.0': 'AGPL-3.0',
        'AGPL-3.0-OR-LATER': 'AGPL-3.0',
        'AGPL-1.0': 'AGPL-1.0',
        'BSD-2-CLAUSE': 'BSD-2-CLAUSE',
        'BSD-3-CLAUSE': 'BSD-3-CLAUSE',
    }

    return exact_mappings.get(license_str, license_str)


def matches_license_filter(package_licenses: Set[str], target_licenses: List[str]) -> bool:
    """Check if package licenses match any of the target licenses."""

    for pkg_license in package_licenses:
        normalized_pkg = normalize_license(pkg_license)

        # Handle compound license expressions like "(MIT OR GPL-3.0-or-later)"
        if ' OR ' in normalized_pkg or ' AND ' in normalized_pkg:
            # Extract individual licenses from compound expressions
            # Remove parentheses and split on OR/AND
            clean_expr = normalized_pkg.replace('(', '').replace(')', '')
            individual_licenses = []

            for part in clean_expr.replace(' AND ', ' OR ').split(' OR '):
                individual_licenses.append(normalize_license(part.strip()))

            # Check if any of the individual licenses match our targets
            for individual_license in individual_licenses:
                if matches_single_license(individual_license, target_licenses):
                    return True
        else:
            # Single license - direct comparison
            if matches_single_license(normalized_pkg, target_licenses):
                return True

    return False


def matches_single_license(license_str: str, target_licenses: List[str]) -> bool:
    """Check if a single license matches any target license."""
    for target in target_licenses:
        target_upper = target.upper()

        # Handle different specificity levels
        if target_upper == 'GPL':
            if license_str.startswith('GPL-'):
                return True
        elif target_upper == 'LGPL':
            if license_str.startswith('LGPL-'):
                return True
        elif target_upper == 'AGPL':
            if license_str.startswith('AGPL-'):
                return True
        elif target_upper == 'APACHE':
            if license_str.startswith('APACHE-') or license_str == 'APACHE-2.0':
                return True
        elif target_upper == 'BSD':
            if license_str.startswith('BSD-'):
                return True
        else:
            # Exact match or starts with target
            if license_str == target_upper or license_str.startswith(target_upper + '-'):
                return True

    return False


def filter_packages_by_license(sbom: Dict[str, Any],
                             target_licenses: List[str],
                             exclude: bool = False) -> List[Dict[str, Any]]:
    """Filter packages based on license types."""
    matching_packages = []

    if 'packages' not in sbom:
        print("Warning: No 'packages' field found in SBOM")
        return matching_packages

    for package in sbom['packages']:
        package_licenses = extract_license_info(package)

        if not package_licenses:
            # Handle packages with no license information
            if exclude and 'UNKNOWN' in [lic.upper() for lic in target_licenses]:
                continue
            elif not exclude and 'UNKNOWN' in [lic.upper() for lic in target_licenses]:
                matching_packages.append(package)
            continue

        has_matching_license = matches_license_filter(package_licenses, target_licenses)

        if (has_matching_license and not exclude) or (not has_matching_license and exclude):
            package['_detected_licenses'] = list(package_licenses)
            matching_packages.append(package)

    return matching_packages


def print_packages(packages: List[Dict[str, Any]], target_licenses: List[str], exclude: bool = False):
    """Print filtered packages in a readable format."""
    action = "excluding" if exclude else "matching"
    print(f"\nPackages {action} license types: {', '.join(target_licenses)}")
    print("=" * 80)

    if not packages:
        print("No packages found.")
        return

    print(f"Found {len(packages)} package(s):\n")

    for i, package in enumerate(packages, 1):
        name = package.get('name', 'Unknown')
        version = package.get('versionInfo', 'Unknown')
        download_location = package.get('downloadLocation', 'Unknown')
        detected_licenses = package.get('_detected_licenses', ['No license info'])

        print(f"{i:3d}. {name}")
        print(f"     Version: {version}")
        print(f"     Licenses: {', '.join(detected_licenses)}")
        print(f"     Download: {download_location}")
        print()


def main():
    parser = argparse.ArgumentParser(
        description='Filter SBOM packages by license types',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s sbom.spdx.json MIT
  %(prog)s sbom.spdx.json MIT LGPL Apache AGPL
  %(prog)s sbom.spdx.json GPL AGPL --exclude
  %(prog)s sbom.spdx.json UNKNOWN  # Find packages with no license info
        """
    )

    parser.add_argument('sbom_file', help='Path to SPDX JSON SBOM file')
    parser.add_argument('licenses', nargs='+', help='License types to filter (e.g., MIT, GPL, Apache)')
    parser.add_argument('--exclude', action='store_true',
                       help='Exclude packages with specified licenses instead of including them')
    parser.add_argument('--json', action='store_true',
                       help='Output results as JSON instead of human-readable format')
    parser.add_argument('--count-only', action='store_true',
                       help='Only show the count of matching packages')

    args = parser.parse_args()

    # Load SBOM
    sbom = load_sbom(args.sbom_file)

    # Filter packages
    filtered_packages = filter_packages_by_license(sbom, args.licenses, args.exclude)

    # Output results
    if args.count_only:
        action = "excluding" if args.exclude else "matching"
        print(f"Count of packages {action} license types {', '.join(args.licenses)}: {len(filtered_packages)}")
    elif args.json:
        # Clean up the temporary field before JSON output
        for package in filtered_packages:
            if '_detected_licenses' in package:
                del package['_detected_licenses']
        print(json.dumps(filtered_packages, indent=2))
    else:
        print_packages(filtered_packages, args.licenses, args.exclude)


if __name__ == '__main__':
    main()
