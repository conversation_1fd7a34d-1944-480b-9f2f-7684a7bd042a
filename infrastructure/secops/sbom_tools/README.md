# SBOM License Filter

A Python script to filter packages in SBOM files by license type.

## Generate SBOM

**[Run SBOM Generator](https://github.com/NextChapterSoftware/unblocked/actions/workflows/ci-sbom.yaml)** - Click to manually trigger SBOM generation
Once finieshed you can download the artifact from the same workflow run.

Or use Syft locally:
```bash
syft . -o spdx-json --file sbom.spdx.json
```

## Usage

```bash
python3 sbom_license_filter.py <sbom_file> <license_types...>
```

### Examples

```bash
# Find MIT licensed packages
python3 sbom_license_filter.py sbom.spdx.json MIT

# Find multiple license types
python3 sbom_license_filter.py sbom.spdx.json MIT Apache GPL

# Exclude GPL/AGPL packages
python3 sbom_license_filter.py sbom.spdx.json GPL AGPL --exclude

# JSON output
python3 sbom_license_filter.py sbom.spdx.json MIT --json

# Count only
python3 sbom_license_filter.py sbom.spdx.json GPL --count-only
```

## Supported Licenses

MIT, Apache, GPL, LGPL, AGPL, BSD, UNKNOWN

## Options

- `--exclude` - Exclude packages with specified licenses
- `--json` - Output as JSON
- `--count-only` - Show count only
