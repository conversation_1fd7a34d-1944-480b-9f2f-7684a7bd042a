#!/bin/bash
set -e
trap ctrl_c INT

function ctrl_c() {
        echo "Ctrl + C happened"
        exit
}

RED='\033[0;31m'
NC='\033[0m' # No Color
printf "${RED}%s\n" \
  "=======================================================================" \
  "==========================>   DANGER ZONE   <==========================" \
  "==========================>   DANGER ZONE   <==========================" \
  "==========================>   DANGER ZONE   <==========================" \
  "======================================================================="

printf "\n"
printf "%s\n" \
    "========== DANGER =========="\
    "Running this script will:"   \
    "     -> Shutdown the database!"\
    "     -> Take down all Kubernetes services!"\
    "     -> Block all user traffic using AWS WAF!"

echo -e "${NC}"

read -p "Enter AWS cli profile name for your target environment: " aws_profile_name
read -p "Enter AWS region for your target environment: " aws_region_name
read -p "Enter AWS account ID for your target environment: " aws_account_id
read -p "Enter Kube context name based on your local Kubeconfig: " kube_context_name


# Convert variables to lowercase
aws_profile_name=$(echo "$aws_profile_name" | tr '[:upper:]' '[:lower:]')
kube_context_name=$(echo "$kube_context_name" | tr '[:upper:]' '[:lower:]')

# Ensure neither variable contains both "dev" and "prod"
if [[ "$aws_profile_name" == *"dev"* && "$kube_context_name" == *"prod"* ]] || [[ "$aws_profile_name" == *"prod"* && "$kube_context_name" == *"dev"* ]]; then
  echo "Error: provided information contains both 'dev' and 'prod'."
  exit 1
fi

###################################
# Verify AWS access
###################################
AWS_COMMAND="aws --profile $aws_profile_name --region $aws_region_name"
# Verify AWS CLI configuration using sts get-caller-identity
echo
echo "Verifying AWS cli access..."
if ! $AWS_COMMAND sts get-caller-identity > /dev/null 2>&1; then
  echo "Error: Failed to verify AWS CLI with profile '$aws_profile_name' and region '$aws_region_name'."
  exit 1
else
  echo "AWS CLI verification successful with profile '$aws_profile_name' and region '$aws_region_name'."
fi


# Verify AWS CLI configuration and get account ID
echo
echo "Verifying AWS account ID access..."
account_id=$($AWS_COMMAND sts get-caller-identity --query "Account" --output text 2>/dev/null)

# Check if the retrieved account ID matches the expected account ID
if [ "$account_id" != "$aws_account_id" ]; then
  echo "Error: Retrieved AWS account ID '$account_id' does not match expected account ID '$aws_account_id'."
  exit 1
fi
# Success message
echo "AWS account id verification successful! Expected id matches retrieved value $account_id"


###################################
# Check user has RDS permissions
###################################
check_rds_permissions() {
    local AWS_COMMAND="$1"

    echo
    echo "Verify user has access to manage RDS"
    # Get the current IAM user name
    user_name=$($AWS_COMMAND iam get-user --query 'User.UserName' --output text)

    # List attached policies
    attached_policies=$($AWS_COMMAND iam list-attached-user-policies --user-name "$user_name" --query 'AttachedPolicies[*].PolicyName' --output text)

    # List inline policies
    inline_policies=$($AWS_COMMAND iam list-user-policies --user-name "$user_name" --query 'PolicyNames[*]' --output text)

    # Check if there are any policies that grant full access to RDS
    if echo "$attached_policies$inline_policies" | grep -q "AdministratorAccess"; then
        echo "User has full IAM permissions to manage RDS."
    else
        echo "User does not have full IAM permissions to manage RDS."
    fi
}

# Example usage:
check_rds_permissions $AWS_COMMAND


###################################
#    Verify Kubernetes access
###################################
# Verify Kubernetes access
namespace="default"
KUBECTL_COMMAND="kubectl --context=$kube_context_name -n $namespace"

# Verify Kubernetes access
echo
echo "Verify user has access to Kubernetes in target environment"
if $KUBECTL_COMMAND get pods &> /dev/null; then
  echo "Verified Kubernetes access for '$namespace' namespace using the '$kube_context_name' context."
else
  echo "Error: Failed to verify Kubernetes access for '$namespace' namespace using the '$kube_context_name' context."
  exit 1
fi


#############################
#   Final confirmation
#############################
# Prompt the user to input the AWS account ID
echo
echo "Final confirmation before applying changes!"
read -p "Please enter the AWS account ID for confirmation: " user_input_account_id

# Check if the user input matches the expected AWS account ID
if [ "$user_input_account_id" = "$aws_account_id" ]; then
    echo "AWS account ID confirmed. Proceeding with the script."
else
    echo "Error: The entered AWS account ID does not match the expected value."
    exit 1
fi

###################################
# Delete Kubernetes pods & deployments
###################################
delete_kubernetes_resources() {
    local KUBECTL_COMMAND="$1"
    local namespace="$2"

    # Run kubectl delete deployments command
    echo "$KUBECTL_COMMAND delete deployments --all --grace-period=0 --force &> /dev/null"
    if $KUBECTL_COMMAND delete deployments --all --grace-period=0 --force &> /dev/null; then
        echo "Successfully deleted all deployments in the '$namespace' namespace."
    else
        echo "Error: Failed to delete deployments in the '$namespace' namespace."
        exit 1
    fi

    # Run kubectl delete pods command to be sure
    if $KUBECTL_COMMAND delete pods --all --grace-period=0 --force --namespace="$namespace" &> /dev/null; then
        echo "Successfully deleted all pods in the '$namespace' namespace."
    else
        echo "Error: Failed to delete pods in the '$namespace' namespace."
        exit 1
    fi
}

delete_kubernetes_resources "$KUBECTL_COMMAND" $namespace


###################################
#     Shutdown RDS database
###################################
# List RDS clusters and extract their identifiers
clusters=$($AWS_COMMAND rds describe-db-clusters --query 'DBClusters[*].DBClusterIdentifier' --output text)
echo
echo "Attempt to shutdown RDS database"
# Loop through each cluster
while IFS= read -r cluster; do
    echo "Cluster Identifier: $cluster"

    # Shut down the cluster
    echo "Shutting down cluster..."
    $AWS_COMMAND rds stop-db-cluster --db-cluster-identifier "$cluster"

    # Check if the shutdown command was successful
    if [ $? -eq 0 ]; then
        echo "Cluster '$cluster' was successfully shut down."
    else
        echo "Failed to shut down cluster '$cluster'."
    fi
done <<< "$clusters"
