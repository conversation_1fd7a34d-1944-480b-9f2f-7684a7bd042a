# DANGER: The Big Red Button!
Use this script only if you are absolutely confident you know what are doing!

## Overview
This document describes the procedures to completely isolate our database and shutdown all services in case of an emergency!
Running the [danger_zone__kill_everything.sh](./danger_zone__kill_everything.sh) script will:
- Shutdown the database!
- Take down all Kubernetes services!
- Block all user traffic using AWS WAF!"

## Shutdown procedures

### SUSPEND prod deploys if you are targeting production

https://www.notion.so/nextchaptersoftware/Disable-Prod-Deployments-9aa0dda13e7c4ffe91f05e055e41b8d2?pvs=4

### Collect the following information:

- **AWS Account ID** for the target environment
- **AWS Region** for the target environment
- **Name of the AWS profile** in your local AWS CLI setup for the target environment
- **Name of the Kubernetes** cluster (context) in your target environment

### Block traffic using WAF2

- Switch to AWS profile for your target environemnt
- List the WAF2 resources relating to CloudFront

```bash
aws wafv2 list-web-acls --scope=CLOUDFRONT --region=us-east-1


## Example output
{
    "NextMarker": "waf-cloudfront",
    "WebACLs": [
        {
            "Name": "waf-cloudfront",
            "Id": "bc119484-d14b-4f2b-acbe-a4e3882c10be",
            "Description": "WAFv2 ACL for CloudFront",
            "LockToken": "23b020c6-32e0-41de-8724-1e35b7daea5f",
            "ARN": "arn:aws:wafv2:us-east-1:************:global/webacl/waf-cloudfront/bc119484-d14b-4f2b-acbe-a4e3882c10be"
        }
    ]
}
```

- Note the `NextMarker` (it is the WAF name), `Id` and `LockToken` from last step
- Block all traffic

```bash
WAF_NAME="waf-cloudfront"
WAF_ID="GET_ID_FROM_LAST_STEP!"
WAF_LOCK_TOKEN="GET_LOCK_TOKEN_FROM_LAST_STEP"
aws wafv2 update-web-acl \
    --scope CLOUDFRONT \
    --region us-east-1 \
    --id "$WAF_ID" \
    --name "$WAF_NAME" \
    --default-action Block={} \
    --lock-token "$WAF_LOCK_TOKEN" \
    --visibility-config SampledRequestsEnabled=true,CloudWatchMetricsEnabled=true,MetricName=exampleMetric \
    --rules "$(aws wafv2 get-web-acl --scope CLOUDFRONT --region us-east-1 --id "$WAF_ID"  --name "$WAF_NAME" --query 'WebACL.Rules' --output json)"



```

### Kill all services and stop the database

**IMPORTANT NOTE:** If you are using AWS CLI profile switching tools like `awsp`, make sure to switch out of the target profile and into the root account's profile!

Run the `./danger_zone__kill_everything.sh` and follow onscreen instructions!

---
### Recovery: Reverting WAF rule to restore traffic flow
The WAF rule above can be reverted as follows:
```bash
# Get latest Lock Token
aws wafv2 list-web-acls --scope=CLOUDFRONT --region=us-east-1

WAF_NAME="waf-cloudfront"
WAF_ID="GET_ID_FROM_LAST_STEP!"
WAF_LOCK_TOKEN="GET_LOCK_TOKEN_FROM_LAST_STEP"
aws wafv2 update-web-acl \
    --scope CLOUDFRONT \
    --region us-east-1 \
    --id "$WAF_ID" \
    --name "$WAF_NAME" \
    --default-action Allow={} \
    --lock-token "$WAF_LOCK_TOKEN" \
    --visibility-config SampledRequestsEnabled=true,CloudWatchMetricsEnabled=true,MetricName=exampleMetric \
    --rules "$(aws wafv2 get-web-acl --scope CLOUDFRONT --region us-east-1 --id "$WAF_ID"  --name "$WAF_NAME" --query 'WebACL.Rules' --output json)"

```

**IMPORTANT NOTE** CDK code can also restore all waf rules and set everything back as they were before!
