# Node.js S3 Sync Makefile

VENV := venv
PYTHON := $(VENV)/bin/python
PIP := $(VENV)/bin/pip
SCRIPT := latest-node-binary-to-s3-sync.py

# Required variables (set these when running make)
BUCKET ?= releases.dev.getunblocked.com
ROLE ?= arn:aws:iam::************:role/OrganizationAccountAccessRole

.PHONY: help
help:
	@echo "Usage:"
	@echo "  make setup           - Create venv and install dependencies"
	@echo "  make dry-run         - Test without uploading (no S3 check)"
	@echo "  make dry-run-s3      - Test with S3 check (shows what exists)"
	@echo "  make sync            - Upload to S3"
	@echo ""
	@echo "Set AWS credentials first:"
	@echo "  export AWS_ACCESS_KEY_ID=your_key"
	@echo "  export AWS_SECRET_ACCESS_KEY=your_secret"

$(VENV):
	python3 -m venv $(VENV)
	$(PIP) install --upgrade pip
	$(PIP) install boto3 packaging

.PHONY: setup
setup: $(VENV)
	@echo "Setup complete!"

.PHONY: dry-run
dry-run: $(VENV)
	$(PYTHON) $(SCRIPT) --bucket $(BUCKET) --role $(ROLE) --inputfile versions.json --dry-run

.PHONY: dry-run-s3
dry-run-s3: $(VENV)
	$(PYTHON) $(SCRIPT) --bucket $(BUCKET) --role $(ROLE) --inputfile versions.json --dry-run-with-s3-check

.PHONY: sync
sync: $(VENV)
	$(PYTHON) $(SCRIPT) --bucket $(BUCKET) --role $(ROLE) --inputfile versions.json

.PHONY: clean
clean:
	rm -rf $(VENV)

.DEFAULT_GOAL := help
