#!/usr/bin/env python3
"""
This script uploads Node.js binaries from https://nodejs.org/dist/ to S3.

Features:
- Downloads latest or specified Node.js versions
- Uploads to S3 with organized folder structure
- Supports dry-run mode for testing
- Supports dry-run with S3 check to see what exists
- Only uploads files that don't already exist in S3
- Supports multiple platforms and architectures

Usage:
  export AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY_ID
  export AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS_KEY

  python3 latest-node-binary-to-s3-sync.py --bucket TARGET_BUCKET_NAME --role AWS_ROLE_ARN [OPTIONS]

Examples:
  # Dry run with latest version (no S3 connection)
  python3 latest-node-binary-to-s3-sync.py --bucket releases.dev.getunblocked.com \
    --role arn:aws:iam::************:role/OrganizationAccountAccessRole \
    --dry-run

  # Dry run with S3 check (shows what actually exists)
  python3 latest-node-binary-to-s3-sync.py --bucket releases.dev.getunblocked.com \
    --role arn:aws:iam::************:role/OrganizationAccountAccessRole \
    --dry-run-with-s3-check

  # Upload specific versions from file
  python3 latest-node-binary-to-s3-sync.py --bucket releases.dev.getunblocked.com \
    --role arn:aws:iam::************:role/OrganizationAccountAccessRole \
    --inputfile ./versions.json
"""

import argparse
import json
import os
import re
import sys
import time
import urllib.request
from typing import List, Dict, Tuple, Optional

try:
    import boto3
except ImportError:
    print("Error: boto3 is not installed or not accessible.")
    print("Please install it with: pip3 install boto3")
    print("Or create a virtual environment:")
    print("  python3 -m venv venv")
    print("  source venv/bin/activate")
    print("  pip install boto3 packaging")
    sys.exit(1)

try:
    from packaging import version
except ImportError:
    print("Error: packaging module is not installed.")
    print("Please install it with: pip3 install packaging")
    sys.exit(1)


class NodeJSS3Syncer:
    """Handles downloading and uploading Node.js binaries to S3."""

    # Platform and architecture mappings
    PLATFORMS = {
        "darwin": ["arm64", "x64"],
        "linux": ["x64", "arm64", "armv7l"],
        "win": ["x64", "x86", "arm64"]
    }

    # Name mappings for S3 paths - ONLY CHANGE: Added x86 -> intel mapping
    PLATFORM_MAPPING = {"darwin": "macos"}
    ARCH_MAPPING = {"arm64": "arm", "armv7l": "arm", "x64": "intel", "x86": "intel"}

    def __init__(self, bucket: str, role_arn: str, dry_run: bool = False, dry_run_with_s3_check: bool = False, region: str = "us-west-2"):
        """Initialize the syncer with AWS credentials and configuration."""
        self.bucket = bucket
        self.role_arn = role_arn
        self.dry_run = dry_run
        self.dry_run_with_s3_check = dry_run_with_s3_check
        self.region = region
        self.s3_client = None
        self.baseurl = "https://nodejs.org/dist"

        # Setup S3 client if we need it (not for basic dry-run)
        if not dry_run or dry_run_with_s3_check:
            self._setup_s3_client()

    def _setup_s3_client(self) -> None:
        """Set up S3 client with assumed role credentials."""
        aws_access_key_id = os.environ.get('AWS_ACCESS_KEY_ID')
        aws_secret_access_key = os.environ.get('AWS_SECRET_ACCESS_KEY')

        if not aws_access_key_id or not aws_secret_access_key:
            raise ValueError("AWS credentials not found in environment variables")

        # Assume role for S3 access
        sts = boto3.client(
            'sts',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=self.region
        )

        assumed_role = sts.assume_role(
            RoleArn=self.role_arn,
            RoleSessionName='NodeJSSyncSession'
        )

        credentials = assumed_role['Credentials']
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=credentials['AccessKeyId'],
            aws_secret_access_key=credentials['SecretAccessKey'],
            aws_session_token=credentials['SessionToken'],
            region_name=self.region
        )

    def _get_s3_key(self, platform: str, arch: str, filename: str) -> str:
        """Generate S3 key path for a given platform, architecture, and filename."""
        platform_name = self.PLATFORM_MAPPING.get(platform, platform)
        arch_name = self.ARCH_MAPPING.get(arch, arch)
        return f"releases/node/{platform_name}/{arch_name}/{filename}"

    def _s3_object_exists(self, key: str) -> bool:
        """Check if an object exists in S3."""
        if self.dry_run and not self.dry_run_with_s3_check:
            print(f"[DRY RUN] Would check if {key} exists in {self.bucket}")
            return False  # Assume it doesn't exist for basic dry run

        try:
            self.s3_client.head_object(Bucket=self.bucket, Key=key)
            if self.dry_run_with_s3_check:
                print(f"[S3 CHECK] ✓ {key} already exists in {self.bucket}")
            else:
                print(f"✓ {key} already exists in {self.bucket}")
            return True
        except Exception:
            if self.dry_run_with_s3_check:
                print(f"[S3 CHECK] ✗ {key} does not exist in {self.bucket}")
            return False

    def _download_file(self, url: str, filename: str) -> bool:
        """Download a file from URL with retry logic."""
        max_retries = 3
        retry_wait_time = 5
        buffer_size = 8192  # Increased buffer size for better performance

        if self.dry_run or self.dry_run_with_s3_check:
            print(f"[DRY RUN] Would download {url} to {filename}")
            return True

        for retry_count in range(max_retries):
            try:
                # Clean up any existing partial file
                if os.path.exists(filename):
                    os.remove(filename)

                print(f"Downloading {url} (attempt {retry_count + 1}/{max_retries})...")

                with urllib.request.urlopen(url) as response:
                    total_size = int(response.headers.get('Content-Length', 0))

                    with open(filename, 'wb') as f:
                        downloaded_size = 0
                        while True:
                            buffer = response.read(buffer_size)
                            if not buffer:
                                break

                            f.write(buffer)
                            downloaded_size += len(buffer)

                            # Simple progress indicator for large files
                            if total_size > 0 and downloaded_size % (1024 * 1024) == 0:
                                progress = (downloaded_size / total_size) * 100
                                print(f"Progress: {progress:.1f}%")

                print(f"✓ Successfully downloaded {filename}")
                return True

            except Exception as e:
                print(f"✗ Download attempt {retry_count + 1} failed: {e}")
                if os.path.exists(filename):
                    os.remove(filename)

                if retry_count < max_retries - 1:
                    print(f"Retrying in {retry_wait_time} seconds...")
                    time.sleep(retry_wait_time)

        print(f"✗ Failed to download {url} after {max_retries} attempts")
        return False

    def _upload_file_to_s3(self, filename: str, s3_key: str) -> bool:
        """Upload a file to S3."""
        if self.dry_run or self.dry_run_with_s3_check:
            print(f"[DRY RUN] Would upload {filename} to s3://{self.bucket}/{s3_key}")
            return True

        try:
            self.s3_client.upload_file(filename, self.bucket, s3_key)
            print(f"✓ Uploaded {filename} to s3://{self.bucket}/{s3_key}")
            return True
        except Exception as e:
            print(f"✗ Failed to upload {filename}: {e}")
            return False
        finally:
            # Clean up local file after upload attempt
            if os.path.exists(filename):
                os.remove(filename)

    def _get_available_files(self, version_url: str) -> List[str]:
        """Get list of available files for a Node.js version."""
        try:
            with urllib.request.urlopen(version_url) as response:
                html = response.read().decode('utf-8')

            # Extract all href links
            urls = re.findall(r'href="([^"]*)"', html)
            return urls
        except Exception as e:
            print(f"✗ Failed to fetch file list from {version_url}: {e}")
            return []

    def _is_supported_combination(self, platform: str, arch: str, node_version_str: str) -> bool:
        """Check if platform/arch combination is supported for the given Node.js version."""
        # Windows ARM64 support was added in Node.js 19.9.0
        if platform == "win" and arch == "arm64":
            try:
                # Handle both "latest" and version strings like "v20.11.1"
                if node_version_str == "latest":
                    # Assume latest is always supported
                    return True

                # Strip 'v' prefix and parse version
                version_str = node_version_str.lstrip('v')
                current_version = version.parse(version_str)
                min_version = version.parse("19.9.0")

                if current_version < min_version:
                    print(f"Skipping unsupported combination: {platform}/{arch} for Node.js {node_version_str}")
                    return False

            except Exception as e:
                print(f"Warning: Could not parse version '{node_version_str}', assuming supported: {e}")
                # If we can't parse the version, assume it's supported to be safe
                pass

        return True

    def sync_version_to_s3(self, node_version: str) -> None:
        """Sync a specific Node.js version to S3."""
        version_url = f"{self.baseurl}/{node_version}/"
        print(f"\n=== Syncing Node.js {node_version} ===")

        available_files = self._get_available_files(version_url)
        if not available_files:
            print(f"✗ No files found for version {node_version}")
            return

        files_processed = 0
        files_uploaded = 0

        for platform in self.PLATFORMS:
            for arch in self.PLATFORMS[platform]:
                if not self._is_supported_combination(platform, arch, node_version):
                    continue

                # Find matching files for this platform/arch combination
                matching_files = [
                    filename for filename in available_files
                    if (f"-{platform}-" in filename and
                        f"-{arch}" in filename and
                        (filename.endswith(".tar.gz") or filename.endswith(".zip")))
                ]

                for filename in matching_files:
                    files_processed += 1
                    # Extract just the filename, not the full path
                    basename = os.path.basename(filename)
                    print(f"\nProcessing: {basename}")

                    s3_key = self._get_s3_key(platform, arch, basename)

                    if self._s3_object_exists(s3_key):
                        if self.dry_run_with_s3_check:
                            print(f"[DRY RUN] Skipping {basename} - already exists in S3")
                        else:
                            print(f"Skipping {basename} - already exists in S3")
                        continue

                    file_url = f"{version_url}{basename}"

                    if self._download_file(file_url, basename):
                        if self._upload_file_to_s3(basename, s3_key):
                            files_uploaded += 1

        print(f"\n=== Summary for {node_version} ===")
        print(f"Files processed: {files_processed}")
        print(f"Files uploaded: {files_uploaded}")
        if self.dry_run or self.dry_run_with_s3_check:
            print("[DRY RUN] No actual uploads performed")

    def sync_versions(self, versions: List[str]) -> None:
        """Sync multiple Node.js versions to S3."""
        print(f"Starting sync for {len(versions)} version(s)")
        if self.dry_run:
            print("🔍 DRY RUN MODE - No actual downloads or uploads will be performed")
        elif self.dry_run_with_s3_check:
            print("🔍 DRY RUN WITH S3 CHECK - Checking S3 but no actual downloads or uploads")

        for node_version in versions:
            try:
                self.sync_version_to_s3(node_version)
            except Exception as e:
                print(f"✗ Error syncing version {node_version}: {e}")
                continue

        print(f"\n🎉 Sync completed for all versions")


def load_versions_from_file(filepath: str) -> List[str]:
    """Load versions from a JSON file."""
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
            return data.get("versions", [])
    except Exception as e:
        raise ValueError(f"Error reading versions file {filepath}: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Upload Node.js binaries to S3 bucket',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Dry run with latest version (no S3 connection)
  %(prog)s --bucket my-bucket --role arn:aws:iam::123:role/MyRole --dry-run

  # Dry run with S3 check (shows what actually exists)
  %(prog)s --bucket my-bucket --role arn:aws:iam::123:role/MyRole --dry-run-with-s3-check

  # Upload specific versions
  %(prog)s --bucket my-bucket --role arn:aws:iam::123:role/MyRole --inputfile versions.json
        """
    )

    parser.add_argument('--role', required=True,
                       help='ARN of the IAM role to assume')
    parser.add_argument('--bucket', required=True,
                       help='Name of the S3 bucket')
    parser.add_argument('--inputfile',
                       help='JSON file containing versions to sync')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform a dry run without actual downloads/uploads')
    parser.add_argument('--dry-run-with-s3-check', action='store_true',
                       help='Dry run that connects to S3 to check if files exist')
    parser.add_argument('--region', default='us-west-2',
                       help='AWS region (default: us-west-2)')

    args = parser.parse_args()

    # Determine versions to sync
    if args.inputfile:
        versions = load_versions_from_file(args.inputfile)
        if not versions:
            print("✗ No versions found in input file")
            return 1
    else:
        versions = ["latest"]

    # Validate mutually exclusive dry run options
    if args.dry_run and args.dry_run_with_s3_check:
        print("✗ Error: Cannot use both --dry-run and --dry-run-with-s3-check together")
        return 1

    # Create syncer and run
    try:
        syncer = NodeJSS3Syncer(
            bucket=args.bucket,
            role_arn=args.role,
            dry_run=args.dry_run,
            dry_run_with_s3_check=args.dry_run_with_s3_check,
            region=args.region
        )
        syncer.sync_versions(versions)
        return 0

    except Exception as e:
        print(f"✗ Error: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
