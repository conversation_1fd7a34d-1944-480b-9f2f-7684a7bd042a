global:
  namespace: default  # overrides all resources' namespaces

# Bitbucket
nameOverride: bitbucket

# managedcertificates.networking.gke.io
managedCertificate:
  enabled: false

# ingress controller
ingress:
  enabled: false
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "gke-ingress"
    kubernetes.io/ingress.allow-http: "false"
  httpRedirect:
    enabled: false

# for ingress only
backendConfig:
  healthCheck:
    type: HTTP
    requestPath: /login
    port: 7990
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3

# config for gateway
gateway:
  enabled: true
  name: gke-secops-edge-global
httproute:
  name: bitbucket-route
  hostnames:
    - bitbucket.test.secops.gcp.getunblocked.com
  rules:
    - backendRefs:
        - name: bitbucket
          port: 7990
healthcheckpolicy:
  name: bitbucket
  default:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    config:
      type: HTTP
      httpHealthCheck:
        port: 7990
        requestPath: /login
  targetRef:
    group: ""            # core
    kind: Service
    name: bitbucket

image: atlassian/bitbucket:latest
replicaCount: 1
containerPort: 7990
debugPort: 5005
sharedVolumePath: /var/atlassian/application-data/bitbucket/shared
waitForPostgres:
  enabled: true
  image: "postgres:16.2-bullseye"
  host: "postgresql-bitbucket"
  port: 5432
  user: bb
env:
  SERVER_SCHEME: "https"
  SERVER_SECURE: "true"
  SERVER_PROXY_NAME: "bitbucket.test.secops.gcp.getunblocked.com"
  SERVER_PROXY_PORT: "443"
  JDBC_URL: "************************************************"
  JDBC_USER: "bb"
  JDBC_PASSWORD: "jellyfish"
  JDBC_DRIVER: "org.postgresql.Driver"
  JVM_MINIMUM_MEMORY: "2048m"
  JVM_MAXIMUM_MEMORY: "4096m"
  JVM_SUPPORT_RECOMMENDED_ARGS: "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
  HAZELCAST_GROUP_NAME: "bb_group"
  HAZELCAST_GROUP_PASSWORD: "1234"
  HAZELCAST_NETWORK_MULTICAST: "true"

podSecurityContext:
  runAsUser: 2003
  runAsGroup: 2003
  fsGroup: 2003
  fsGroupChangePolicy: "OnRootMismatch"

resources:
  requests:
    memory: "8Gi"
  limits:
    memory: "8Gi"

pvc:
  keep: true #pvc wont delete if do helm uninstall
  accessModes:
    - ReadWriteOnce
  storage: 10Gi

# PostgreSQL for Bitbucket
postgresql:
  enabled: true
  fullnameOverride: postgresql-bitbucket
  image:
    tag: "16.2.0"
  commonLabels:
    app.kubernetes.io/instance: "postgresql-bitbucket"
    app.kubernetes.io/name: "postgresql-bitbucket"
    app.kubernetes.io/version: "16.2.0"
  auth:
    username: bb
    password: jellyfish
    database: bbdb
  primary:
    persistence:
      enabled: true
      size: 50Gi
    resources:
      requests:
        cpu: "200m"
        memory: "128Mi"
        ephemeral-storage: "50Mi"
      limits:
        cpu: "500m"
        memory: "512Mi"
        ephemeral-storage: "2Gi"
