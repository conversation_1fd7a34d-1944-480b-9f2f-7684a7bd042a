jenkins:
  controller:
    image:
      repository: "jenkins/jenkins"
      tag: "2.516.2-lts-jdk21"
    jenkinsUrl: https://jenkins.test.secops.gcp.getunblocked.com
    admin:
      username: admin
      password: "ChangeMe123!"   # demo only; prefer a Secret in prod
    serviceType: ClusterIP       # Or ClusterIP with an Ingress
    resources:
      requests:
        cpu: "200m"
        memory: "512Mi"
      limits:
        cpu: "1"
        memory: "2Gi"
    persistence:
      enabled: true
      size: 20Gi
      storageClass: standard-rwo  # uncomment/set for your cluster

    # Optional: basic plugins to start with (pin versions later)
    installPlugins:
      - kubernetes
      - workflow-aggregator
      - git
      - configuration-as-code
      - job-dsl

    ingress:
      enabled: false

# config for gateway
gateway:
  enabled: true
  name: gke-secops-edge-global
httproute:
  name: jenkins-route
  hostnames:
    - jenkins.test.secops.gcp.getunblocked.com
  rules:
    - backendRefs:
        - name: jenkins
          port: 8080
healthcheckpolicy:
  name: jenkins
  default:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    config:
      type: HTTP
      httpHealthCheck:
        port: 8080
        requestPath: /login
  targetRef:
    group: ""            # core
    kind: Service
    name: jenkins
