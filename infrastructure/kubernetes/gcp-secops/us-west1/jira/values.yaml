global:
  namespace: default  # overrides all resources' namespaces

# Jira
nameOverride: jira

# managedcertificates.networking.gke.io
managedCertificate:
  enabled: false

# ingress controller
ingress:
  enabled: false
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "gke-ingress"
    kubernetes.io/ingress.allow-http: "false"
  httpRedirect:
    enabled: false

# for ingress only
backendConfig:
  healthCheck:
    type: HTTP
    requestPath: /login.jsp
    port: 8080
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3

# config for gateway
gateway:
  enabled: true
  name: gke-secops-edge-global
httproute:
  name: jira-route
  hostnames:
    - jira.test.secops.gcp.getunblocked.com
  rules:
    - backendRefs:
        - name: jira
          port: 8080
healthcheckpolicy:
  name: jira
  default:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    config:
      type: HTTP
      httpHealthCheck:
        port: 8080
        requestPath: /login.jsp
  targetRef:
    group: ""            # core
    kind: Service
    name: jira

image: atlassian/jira-software:10.7.3
replicaCount: 1
containerPort: 8080
debugPort: 8000
sharedVolumePath: /var/atlassian/application-data/jira/shared
waitForPostgres:
  enabled: true
  image: "postgres:15.12-bullseye"
  host: "postgresql-jira"
  port: 5432
  user: jira
env:
  ATL_JDBC_URL: "*********************************************"
  ATL_JDBC_USER: "jira"
  ATL_JDBC_PASSWORD: "jellyfish"
  ATL_DB_DRIVER: "org.postgresql.Driver"
  ATL_DB_TYPE: "postgres72"
  ATL_PROXY_NAME: "jira.test.secops.gcp.getunblocked.com"
  ATL_PROXY_PORT: "443"
  ATL_TOMCAT_SCHEME: "https"
  ATL_TOMCAT_SECURE: "true"
  JVM_MINIMUM_MEMORY: "2048m"
  JVM_MAXIMUM_MEMORY: "4096m"
  JVM_SUPPORT_RECOMMENDED_ARGS: "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000"
  CLUSTERED: "true"
  JIRA_NODE_ID: "node_1"

podSecurityContext:
  runAsUser: 2001
  runAsGroup: 2001
  fsGroup: 2001
  fsGroupChangePolicy: "OnRootMismatch"

resources:
  requests:
    memory: "5Gi"
    ephemeral-storage: "1Gi"
  limits:
    memory: "5Gi"
    ephemeral-storage: "2Gi"

pvc:
  keep: true #pvc wont delete if do helm uninstall
  accessModes:
    - ReadWriteOnce
  storage: 10Gi

# PostgreSQL for Jira
postgresql:
  enabled: true
  fullnameOverride: postgresql-jira
  image:
    tag: "15.10.0"
  commonLabels:
    app.kubernetes.io/instance: "postgresql-jira"
    app.kubernetes.io/name: "postgresql-jira"
    app.kubernetes.io/version: "15.10.0"
  auth:
    username: jira
    password: jellyfish
    database: jiradb
  primary:
    persistence:
      enabled: true
      size: 50Gi
    resources:
      requests:
        cpu: "500m"
        memory: "128Mi"
        ephemeral-storage: "50Mi"
      limits:
        cpu: "1"
        memory: "512Mi"
        ephemeral-storage: "2Gi"
