global:
  namespace: default  # overrides all resources' namespaces

# Confluence
nameOverride: confluence

# managedcertificates.networking.gke.io
managedCertificate:
  enabled: false

# ingress controller
ingress:
  enabled: false
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "gke-ingress"
    kubernetes.io/ingress.allow-http: "false"
  httpRedirect:
    enabled: false

# for ingress only
backendConfig:
  healthCheck:
    type: HTTP
    requestPath: /login.action
    port: 8090
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3

# config for gateway
gateway:
  enabled: true
  name: gke-secops-edge-global
httproute:
  name: confluence-route
  hostnames:
    - confluence.test.secops.gcp.getunblocked.com
  rules:
    - backendRefs:
        - name: confluence
          port: 8090
healthcheckpolicy:
  name: confluence
  default:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    config:
      type: HTTP
      httpHealthCheck:
        port: 8090
        requestPath: /login.action
  targetRef:
    group: ""            # core
    kind: Service
    name: confluence

image: atlassian/confluence:latest
replicaCount: 1
containerPort: 8090
debugPort: 5006
sharedVolumePath: /var/atlassian/application-data/confluence/shared-home
waitForPostgres:
  enabled: true
  image: "postgres:16.2-bullseye"
  host: "postgresql-confluence"
  port: 5432
  user: confluence
env:
  ATL_PROXY_NAME: "confluence.test.secops.gcp.getunblocked.com"
  ATL_PROXY_PORT: "443"
  ATL_TOMCAT_SCHEME: "https"
  ATL_TOMCAT_SECURE: "true"
  ATL_TOMCAT_PORT: "8090"
  ATL_JDBC_URL: "*********************************************************"
  ATL_JDBC_USER: "confluence"
  ATL_JDBC_PASSWORD: "jellyfish"
  ATL_DB_DRIVER: "org.postgresql.Driver"
  ATL_DB_TYPE: "postgresql"
  JVM_MINIMUM_MEMORY: "2048m"
  JVM_MAXIMUM_MEMORY: "4096m"
  JVM_SUPPORT_RECOMMENDED_ARGS: "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006"
  ATL_CLUSTER_NAME: "confluence_cluster"
  ATL_PRODUCT_HOME_SHARED: "/var/atlassian/application-data/confluence/shared-home"
  ATL_CLUSTER: "false"
  ATL_CLUSTER_TYPE: "multicast"
  ATL_CLUSTER_TTL: "1"
  ATL_CLUSTER_ADDRESS: "***************"
  ATL_CLUSTER_INTERFACE: "eth0"

podSecurityContext:
  runAsUser: 2002
  runAsGroup: 2002
  fsGroup: 2002
  fsGroupChangePolicy: "OnRootMismatch"

resources:
  requests:
    memory: "4Gi"
  limits:
    memory: "5Gi"

pvc:
  keep: true #pvc wont delete if do helm uninstall
  accessModes:
    - ReadWriteOnce
  storage: 10Gi

# PostgreSQL for Confluence
postgresql:
  enabled: true
  fullnameOverride: postgresql-confluence
  image:
    tag: "16.2.0"
  commonLabels:
    app.kubernetes.io/instance: "postgresql-confluence"
    app.kubernetes.io/name: "postgresql-confluence"
    app.kubernetes.io/version: "16.2.0"
  auth:
    username: confluence
    password: jellyfish
    database: confluencedb
  primary:
    persistence:
      enabled: true
      size: 50Gi
    resources:
      requests:
        cpu: "500m"
        memory: "128Mi"
        ephemeral-storage: "50Mi"
      limits:
        cpu: "1"
        memory: "512Mi"
        ephemeral-storage: "2Gi"
