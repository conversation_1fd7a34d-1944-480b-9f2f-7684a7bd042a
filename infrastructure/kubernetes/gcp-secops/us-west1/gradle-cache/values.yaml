image:
  tag: "21.2"

config:
  #  hashedPassword: "REPLACE_WITH_YOUR_HASHED_PASSWORD"  # Provided via helm command
  cache:
    targetSize:
      type: maxAvailable
    freeSpaceBufferSize: 2000
    maxArtifactSize: 150
    maxEntryAgeInHours: null
  uiAccess:
    type: "secure"
    username: "unblocked"

storage:
  size: "80Gi"

resources:
  limits:
    cpu: 1500m
    memory: 5Gi
  requests:
    cpu: 1000m
    memory: 4Gi

nodeSelector:
  workload: gradlecache

tolerations:
  - key: "gradlecache"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

podAnnotations:
  description: "Gradle Build Cache for production CI/CD"

podSecurityContext:
  fsGroup: 1000
  runAsNonRoot: true
  seccompProfile:
    type: RuntimeDefault

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000
