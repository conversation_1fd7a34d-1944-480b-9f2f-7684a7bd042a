fullnameOverride: "llama3211bvision"

replicaCount: 0

# Container images configuration
image:
  tgi:
    repository: "877923746456.dkr.ecr.us-west-2.amazonaws.com/onprem-huggingface"
    tag: "1-d0cd9155dcf7737120216e6b93d764ca2eba595c"
    pullPolicy: IfNotPresent

# Environment variables for llama-3.1-8B
env:
  - name: NVIDIA_DRIVER_CAPABILITIES
    value: "compute,utility"
  - name: DISABLE_CUSTOM_KERNELS
    value: "true"
  - name: NVIDIA_REQUIRE_CUDA
    value: "cuda>=11.0"
  - name: HF_HUB_CACHE
    value: "/hf-snapshots"
  - name: HUGGINGFACE_HUB_CACHE
    value: "/hf-snapshots"
  # Model-specific configuration
  - name: MODEL_ID
    value: "meta-llama/Llama-3.2-11B-Vision-Instruct"
  - name: NUM_SHARD
    value: "1"
  - name: MAX_INPUT_LENGTH
    value: "30000"  # Reduced context window for stability
  - name: MAX_BATCH_PREFILL_TOKENS
    value: "30000"
  - name: MAX_BATCH_TOTAL_TOKENS
    value: "60000"
  - name: MAX_CONCURRENT_REQUESTS
    value: "30"
  - name: MAX_TOTAL_TOKENS
    value: "32000"
  - name: PORT
    value: "8080"
  - name: ROPE_SCALING
    value: "dynamic"
  - name: ROPE_FACTOR
    value: "1"
  - name: METRICS_ENABLED
    value: "true"
  - name: ENABLE_FLASH_ATTENTION
    value: "true"
  - name: PAYLOAD_LIMIT
    value: "20000000"
  - name: PRECISION
    value: "fp16"  # Optimized for L40S

# Resource configuration for llama-3.1-8B (4 GPUs)
resources:
  nginx:
    requests:
      cpu: "300m"
      memory: "1Gi"
    limits:
      cpu: "1"
      memory: "1Gi"
  tgi:
    requests:
      cpu: "3"
      memory: "28Gi"
      gpu: "1"
    limits:
      cpu: "4"
      memory: "28Gi"
      gpu: "1"
    shm:
      size: 2Gi

# Health check probes for llama-3.1-8B (different path and longer startup)
startupProbe:
  httpGet:
    path: /
    port: 8080
  failureThreshold: 40
  periodSeconds: 10
  initialDelaySeconds: 180

readinessProbe:
  httpGet:
    path: /
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 180
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 60
  successThreshold: 1

# Ingress configuration for llama-3.1-8B
ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/group.order: "210"
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: instance
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/group.name: "ml-internal"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz"
    external-dns.alpha.kubernetes.io/hostname: ml.alb.secops-2.us-west-2.secops.getunblocked.com
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:877923746456:certificate/08dcc91a-2501-4676-a004-714001a2c97f
  paths:
    - host: ml.alb.secops-2.us-west-2.secops.getunblocked.com
      path: /api/ml/transformers/llama-32-11B-Vision
      pathType: Prefix
    - host: ml.alb.secops.getunblocked.com
      path: /api/ml/transformers/llama-32-11B-Vision
      pathType: Prefix

# Node scheduling
tolerations:
  - key: "nvidia.com/gpu"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

nodeSelector:
  "node.kubernetes.io/instance-type": g6e.xlarge
  #"nvidia.com/gpu.product": NVIDIA-L40S

# Persistent volume configuration
pvc:
  enabled: true
  accessModes:
    - ReadWriteMany
  storage: 100Gi
  storageClassName: efs-sc


