components:
  schemas:
    Artifact:
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        data:
          anyOf:
          - type: object
          - {}
          - type: 'null'
          description: Data associated with the artifact, e.g. a result.; structure
            depends on the artifact type.
          title: Data
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A markdown-enabled description of the artifact.
          title: Description
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The flow run associated with the artifact.
          title: Flow Run Id
        id:
          format: uuid
          title: Id
          type: string
        key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional unique reference key for this artifact.
          title: Key
        metadata_:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          description: User-defined artifact metadata. Content must be string key
            and value pairs.
          title: Metadata
        task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The task run associated with the artifact.
          title: Task Run Id
        type:
          anyOf:
          - type: string
          - type: 'null'
          description: An identifier that describes the shape of the data field. e.g.
            'result', 'table', 'markdown'
          title: Type
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required: []
      title: Artifact
      type: object
    ArtifactCollection:
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        data:
          anyOf:
          - type: object
          - {}
          - type: 'null'
          description: Data associated with the artifact, e.g. a result.; structure
            depends on the artifact type.
          title: Data
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A markdown-enabled description of the artifact.
          title: Description
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The flow run associated with the artifact.
          title: Flow Run Id
        id:
          format: uuid
          title: Id
          type: string
        key:
          description: An optional unique reference key for this artifact.
          title: Key
          type: string
        latest_id:
          description: The latest artifact ID associated with the key.
          format: uuid
          title: Latest Id
          type: string
        metadata_:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          description: User-defined artifact metadata. Content must be string key
            and value pairs.
          title: Metadata
        task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The task run associated with the artifact.
          title: Task Run Id
        type:
          anyOf:
          - type: string
          - type: 'null'
          description: An identifier that describes the shape of the data field. e.g.
            'result', 'table', 'markdown'
          title: Type
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - key
      - latest_id
      - id
      - created
      - updated
      title: ArtifactCollection
      type: object
    ArtifactCollectionFilter:
      additionalProperties: false
      description: Filter artifact collections. Only artifact collections matching
        all criteria will be returned
      properties:
        flow_run_id:
          anyOf:
          - $ref: '#/components/schemas/ArtifactCollectionFilterFlowRunId'
          - type: 'null'
          description: Filter criteria for `Artifact.flow_run_id`
        key:
          anyOf:
          - $ref: '#/components/schemas/ArtifactCollectionFilterKey'
          - type: 'null'
          description: Filter criteria for `Artifact.key`
        latest_id:
          anyOf:
          - $ref: '#/components/schemas/ArtifactCollectionFilterLatestId'
          - type: 'null'
          description: Filter criteria for `Artifact.id`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        task_run_id:
          anyOf:
          - $ref: '#/components/schemas/ArtifactCollectionFilterTaskRunId'
          - type: 'null'
          description: Filter criteria for `Artifact.task_run_id`
        type:
          anyOf:
          - $ref: '#/components/schemas/ArtifactCollectionFilterType'
          - type: 'null'
          description: Filter criteria for `Artifact.type`
      title: ArtifactCollectionFilter
      type: object
    ArtifactCollectionFilterFlowRunId:
      additionalProperties: false
      description: Filter by `ArtifactCollection.flow_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run IDs to include
          title: Any
      title: ArtifactCollectionFilterFlowRunId
      type: object
    ArtifactCollectionFilterKey:
      additionalProperties: false
      description: Filter by `ArtifactCollection.key`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of artifact keys to include
          title: Any
        exists_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If `true`, only include artifacts with a non-null key. If `false`,
            only include artifacts with a null key. Should return all rows in the
            ArtifactCollection table if specified.
          title: Exists
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A string to match artifact keys against. This can include SQL
            wildcard characters like `%` and `_`.
          examples:
          - my-artifact-%
          title: Like
      title: ArtifactCollectionFilterKey
      type: object
    ArtifactCollectionFilterLatestId:
      additionalProperties: false
      description: Filter by `ArtifactCollection.latest_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of artifact ids to include
          title: Any
      title: ArtifactCollectionFilterLatestId
      type: object
    ArtifactCollectionFilterTaskRunId:
      additionalProperties: false
      description: Filter by `ArtifactCollection.task_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of task run IDs to include
          title: Any
      title: ArtifactCollectionFilterTaskRunId
      type: object
    ArtifactCollectionFilterType:
      additionalProperties: false
      description: Filter by `ArtifactCollection.type`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of artifact types to include
          title: Any
        not_any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of artifact types to exclude
          title: Not Any
      title: ArtifactCollectionFilterType
      type: object
    ArtifactCollectionSort:
      description: Defines artifact collection sorting options.
      enum:
      - CREATED_DESC
      - UPDATED_DESC
      - ID_DESC
      - KEY_DESC
      - KEY_ASC
      title: ArtifactCollectionSort
      type: string
    ArtifactCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create an artifact.
      properties:
        data:
          anyOf:
          - type: object
          - {}
          - type: 'null'
          description: Data associated with the artifact, e.g. a result.; structure
            depends on the artifact type.
          title: Data
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A markdown-enabled description of the artifact.
          title: Description
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The flow run associated with the artifact.
          title: Flow Run Id
        key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional unique reference key for this artifact.
          title: Key
        metadata_:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          description: User-defined artifact metadata. Content must be string key
            and value pairs.
          title: Metadata
        task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The task run associated with the artifact.
          title: Task Run Id
        type:
          anyOf:
          - type: string
          - type: 'null'
          description: An identifier that describes the shape of the data field. e.g.
            'result', 'table', 'markdown'
          title: Type
      title: ArtifactCreate
      type: object
    ArtifactFilter:
      additionalProperties: false
      description: Filter artifacts. Only artifacts matching all criteria will be
        returned
      properties:
        flow_run_id:
          anyOf:
          - $ref: '#/components/schemas/ArtifactFilterFlowRunId'
          - type: 'null'
          description: Filter criteria for `Artifact.flow_run_id`
        id:
          anyOf:
          - $ref: '#/components/schemas/ArtifactFilterId'
          - type: 'null'
          description: Filter criteria for `Artifact.id`
        key:
          anyOf:
          - $ref: '#/components/schemas/ArtifactFilterKey'
          - type: 'null'
          description: Filter criteria for `Artifact.key`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        task_run_id:
          anyOf:
          - $ref: '#/components/schemas/ArtifactFilterTaskRunId'
          - type: 'null'
          description: Filter criteria for `Artifact.task_run_id`
        type:
          anyOf:
          - $ref: '#/components/schemas/ArtifactFilterType'
          - type: 'null'
          description: Filter criteria for `Artifact.type`
      title: ArtifactFilter
      type: object
    ArtifactFilterFlowRunId:
      additionalProperties: false
      description: Filter by `Artifact.flow_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run IDs to include
          title: Any
      title: ArtifactFilterFlowRunId
      type: object
    ArtifactFilterId:
      additionalProperties: false
      description: Filter by `Artifact.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of artifact ids to include
          title: Any
      title: ArtifactFilterId
      type: object
    ArtifactFilterKey:
      additionalProperties: false
      description: Filter by `Artifact.key`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of artifact keys to include
          title: Any
        exists_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If `true`, only include artifacts with a non-null key. If `false`,
            only include artifacts with a null key.
          title: Exists
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A string to match artifact keys against. This can include SQL
            wildcard characters like `%` and `_`.
          examples:
          - my-artifact-%
          title: Like
      title: ArtifactFilterKey
      type: object
    ArtifactFilterTaskRunId:
      additionalProperties: false
      description: Filter by `Artifact.task_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of task run IDs to include
          title: Any
      title: ArtifactFilterTaskRunId
      type: object
    ArtifactFilterType:
      additionalProperties: false
      description: Filter by `Artifact.type`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of artifact types to include
          title: Any
        not_any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of artifact types to exclude
          title: Not Any
      title: ArtifactFilterType
      type: object
    ArtifactSort:
      description: Defines artifact sorting options.
      enum:
      - CREATED_DESC
      - UPDATED_DESC
      - ID_DESC
      - KEY_DESC
      - KEY_ASC
      title: ArtifactSort
      type: string
    ArtifactUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update an artifact.
      properties:
        data:
          anyOf:
          - type: object
          - {}
          - type: 'null'
          title: Data
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        metadata_:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Metadata
      title: ArtifactUpdate
      type: object
    Automation:
      properties:
        actions:
          description: The actions to perform when this Automation triggers
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions
          type: array
        actions_on_resolve:
          description: The actions to perform when an Automation goes into a resolving
            state
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions On Resolve
          type: array
        actions_on_trigger:
          description: The actions to perform when an Automation goes into a triggered
            state
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions On Trigger
          type: array
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        description:
          default: ''
          description: A longer description of this automation
          title: Description
          type: string
        enabled:
          default: true
          description: Whether this automation will be evaluated
          title: Enabled
          type: boolean
        id:
          format: uuid
          title: Id
          type: string
        name:
          description: The name of this automation
          title: Name
          type: string
        trigger:
          anyOf:
          - $ref: '#/components/schemas/EventTrigger'
          - $ref: '#/components/schemas/CompoundTrigger-Output'
          - $ref: '#/components/schemas/SequenceTrigger-Output'
          description: The criteria for which events this Automation covers and how
            it will respond to the presence or absence of those events
          title: Trigger
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - trigger
      - actions
      - id
      - created
      - updated
      title: Automation
      type: object
    AutomationCreate:
      additionalProperties: false
      properties:
        actions:
          description: The actions to perform when this Automation triggers
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions
          type: array
        actions_on_resolve:
          description: The actions to perform when an Automation goes into a resolving
            state
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions On Resolve
          type: array
        actions_on_trigger:
          description: The actions to perform when an Automation goes into a triggered
            state
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions On Trigger
          type: array
        description:
          default: ''
          description: A longer description of this automation
          title: Description
          type: string
        enabled:
          default: true
          description: Whether this automation will be evaluated
          title: Enabled
          type: boolean
        name:
          description: The name of this automation
          title: Name
          type: string
        owner_resource:
          anyOf:
          - type: string
          - type: 'null'
          description: The resource to which this automation belongs
          title: Owner Resource
        trigger:
          anyOf:
          - $ref: '#/components/schemas/EventTrigger'
          - $ref: '#/components/schemas/CompoundTrigger-Input'
          - $ref: '#/components/schemas/SequenceTrigger-Input'
          description: The criteria for which events this Automation covers and how
            it will respond to the presence or absence of those events
          title: Trigger
      required:
      - name
      - trigger
      - actions
      title: AutomationCreate
      type: object
    AutomationFilter:
      additionalProperties: false
      properties:
        created:
          anyOf:
          - $ref: '#/components/schemas/AutomationFilterCreated'
          - type: 'null'
          description: Filter criteria for `Automation.created`
        name:
          anyOf:
          - $ref: '#/components/schemas/AutomationFilterName'
          - type: 'null'
          description: Filter criteria for `Automation.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: AutomationFilter
      type: object
    AutomationFilterCreated:
      additionalProperties: false
      description: Filter by `Automation.created`.
      properties:
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include automations created before this datetime
          title: Before
      title: AutomationFilterCreated
      type: object
    AutomationFilterName:
      additionalProperties: false
      description: Filter by `Automation.created`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include automations with names that match any of these
            strings
          title: Any
      title: AutomationFilterName
      type: object
    AutomationPartialUpdate:
      additionalProperties: false
      properties:
        enabled:
          default: true
          description: Whether this automation will be evaluated
          title: Enabled
          type: boolean
      title: AutomationPartialUpdate
      type: object
    AutomationSort:
      description: Defines automations sorting options.
      enum:
      - CREATED_DESC
      - UPDATED_DESC
      - NAME_ASC
      - NAME_DESC
      title: AutomationSort
      type: string
    AutomationUpdate:
      additionalProperties: false
      properties:
        actions:
          description: The actions to perform when this Automation triggers
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions
          type: array
        actions_on_resolve:
          description: The actions to perform when an Automation goes into a resolving
            state
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions On Resolve
          type: array
        actions_on_trigger:
          description: The actions to perform when an Automation goes into a triggered
            state
          items:
            anyOf:
            - $ref: '#/components/schemas/DoNothing'
            - $ref: '#/components/schemas/RunDeployment'
            - $ref: '#/components/schemas/PauseDeployment'
            - $ref: '#/components/schemas/ResumeDeployment'
            - $ref: '#/components/schemas/CancelFlowRun'
            - $ref: '#/components/schemas/ChangeFlowRunState'
            - $ref: '#/components/schemas/PauseWorkQueue'
            - $ref: '#/components/schemas/ResumeWorkQueue'
            - $ref: '#/components/schemas/SendNotification'
            - $ref: '#/components/schemas/CallWebhook'
            - $ref: '#/components/schemas/PauseAutomation'
            - $ref: '#/components/schemas/ResumeAutomation'
            - $ref: '#/components/schemas/SuspendFlowRun'
            - $ref: '#/components/schemas/ResumeFlowRun'
            - $ref: '#/components/schemas/PauseWorkPool'
            - $ref: '#/components/schemas/ResumeWorkPool'
          title: Actions On Trigger
          type: array
        description:
          default: ''
          description: A longer description of this automation
          title: Description
          type: string
        enabled:
          default: true
          description: Whether this automation will be evaluated
          title: Enabled
          type: boolean
        name:
          description: The name of this automation
          title: Name
          type: string
        trigger:
          anyOf:
          - $ref: '#/components/schemas/EventTrigger'
          - $ref: '#/components/schemas/CompoundTrigger-Input'
          - $ref: '#/components/schemas/SequenceTrigger-Input'
          description: The criteria for which events this Automation covers and how
            it will respond to the presence or absence of those events
          title: Trigger
      required:
      - name
      - trigger
      - actions
      title: AutomationUpdate
      type: object
    BlockDocument:
      description: An ORM representation of a block document.
      properties:
        block_document_references:
          additionalProperties:
            type: object
          description: Record of the block document's references
          title: Block Document References
          type: object
        block_schema:
          anyOf:
          - $ref: '#/components/schemas/BlockSchema'
          - type: 'null'
          description: The associated block schema
        block_schema_id:
          description: A block schema ID
          format: uuid
          title: Block Schema Id
          type: string
        block_type:
          anyOf:
          - $ref: '#/components/schemas/BlockType'
          - type: 'null'
          description: The associated block type
        block_type_id:
          description: A block type ID
          format: uuid
          title: Block Type Id
          type: string
        block_type_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The associated block type's name
          title: Block Type Name
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        data:
          description: The block document's data
          title: Data
          type: object
        id:
          format: uuid
          title: Id
          type: string
        is_anonymous:
          default: false
          description: Whether the block is anonymous (anonymous blocks are usually
            created by Prefect automatically)
          title: Is Anonymous
          type: boolean
        name:
          anyOf:
          - pattern: ^[^/%&><]+$
            type: string
          - type: 'null'
          description: The block document's name. Not required for anonymous block
            documents.
          title: Name
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - block_schema_id
      - block_type_id
      - id
      - created
      - updated
      title: BlockDocument
      type: object
    BlockDocumentCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a block document.
      properties:
        block_schema_id:
          description: A block schema ID
          format: uuid
          title: Block Schema Id
          type: string
        block_type_id:
          description: A block type ID
          format: uuid
          title: Block Type Id
          type: string
        data:
          description: The block document's data
          title: Data
          type: object
        is_anonymous:
          default: false
          description: Whether the block is anonymous (anonymous blocks are usually
            created by Prefect automatically)
          title: Is Anonymous
          type: boolean
        name:
          anyOf:
          - type: string
          - type: 'null'
          description: The block document's name. Not required for anonymous block
            documents.
          title: Name
      required:
      - block_schema_id
      - block_type_id
      title: BlockDocumentCreate
      type: object
    BlockDocumentFilter:
      additionalProperties: false
      description: Filter BlockDocuments. Only BlockDocuments matching all criteria
        will be returned
      properties:
        block_type_id:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentFilterBlockTypeId'
          - type: 'null'
          description: Filter criteria for `BlockDocument.block_type_id`
        id:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentFilterId'
          - type: 'null'
          description: Filter criteria for `BlockDocument.id`
        is_anonymous:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentFilterIsAnonymous'
          - type: 'null'
          default:
            eq_: false
          description: Filter criteria for `BlockDocument.is_anonymous`. Defaults
            to excluding anonymous blocks.
        name:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentFilterName'
          - type: 'null'
          description: Filter criteria for `BlockDocument.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: BlockDocumentFilter
      type: object
    BlockDocumentFilterBlockTypeId:
      additionalProperties: false
      description: Filter by `BlockDocument.block_type_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of block type ids to include
          title: Any
      title: BlockDocumentFilterBlockTypeId
      type: object
    BlockDocumentFilterId:
      additionalProperties: false
      description: Filter by `BlockDocument.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of block ids to include
          title: Any
      title: BlockDocumentFilterId
      type: object
    BlockDocumentFilterIsAnonymous:
      additionalProperties: false
      description: Filter by `BlockDocument.is_anonymous`.
      properties:
        eq_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Filter block documents for only those that are or are not anonymous.
          title: Eq
      title: BlockDocumentFilterIsAnonymous
      type: object
    BlockDocumentFilterName:
      additionalProperties: false
      description: Filter by `BlockDocument.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of block names to include
          title: Any
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A string to match block names against. This can include SQL
            wildcard characters like `%` and `_`.
          examples:
          - my-block%
          title: Like
      title: BlockDocumentFilterName
      type: object
    BlockDocumentSort:
      description: Defines block document sorting options.
      enum:
      - NAME_DESC
      - NAME_ASC
      - BLOCK_TYPE_AND_NAME_ASC
      title: BlockDocumentSort
      type: string
    BlockDocumentUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a block document.
      properties:
        block_schema_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: A block schema ID
          title: Block Schema Id
        data:
          description: The block document's data
          title: Data
          type: object
        merge_existing_data:
          default: true
          title: Merge Existing Data
          type: boolean
      title: BlockDocumentUpdate
      type: object
    BlockSchema:
      description: An ORM representation of a block schema.
      properties:
        block_type:
          anyOf:
          - $ref: '#/components/schemas/BlockType'
          - type: 'null'
          description: The associated block type
        block_type_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: A block type ID
          title: Block Type Id
        capabilities:
          description: A list of Block capabilities
          items:
            type: string
          title: Capabilities
          type: array
        checksum:
          description: The block schema's unique checksum
          title: Checksum
          type: string
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        fields:
          additionalProperties: true
          description: The block schema's field schema
          title: Fields
          type: object
        id:
          format: uuid
          title: Id
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        version:
          default: non-versioned
          description: Human readable identifier for the block schema
          title: Version
          type: string
      required:
      - checksum
      - block_type_id
      - id
      - created
      - updated
      title: BlockSchema
      type: object
    BlockSchemaCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a block schema.
      properties:
        block_type_id:
          description: A block type ID
          format: uuid
          title: Block Type Id
          type: string
        capabilities:
          description: A list of Block capabilities
          items:
            type: string
          title: Capabilities
          type: array
        fields:
          description: The block schema's field schema
          title: Fields
          type: object
        version:
          default: non-versioned
          description: Human readable identifier for the block schema
          title: Version
          type: string
      required:
      - block_type_id
      title: BlockSchemaCreate
      type: object
    BlockSchemaFilter:
      additionalProperties: false
      description: Filter BlockSchemas
      properties:
        block_capabilities:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilterCapabilities'
          - type: 'null'
          description: Filter criteria for `BlockSchema.capabilities`
        block_type_id:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilterBlockTypeId'
          - type: 'null'
          description: Filter criteria for `BlockSchema.block_type_id`
        id:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilterId'
          - type: 'null'
          description: Filter criteria for `BlockSchema.id`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        version:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilterVersion'
          - type: 'null'
          description: Filter criteria for `BlockSchema.version`
      title: BlockSchemaFilter
      type: object
    BlockSchemaFilterBlockTypeId:
      additionalProperties: false
      description: Filter by `BlockSchema.block_type_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of block type ids to include
          title: Any
      title: BlockSchemaFilterBlockTypeId
      type: object
    BlockSchemaFilterCapabilities:
      additionalProperties: false
      description: Filter by `BlockSchema.capabilities`
      properties:
        all_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of block capabilities. Block entities will be returned
            only if an associated block schema has a superset of the defined capabilities.
          examples:
          - - write-storage
            - read-storage
          title: All
      title: BlockSchemaFilterCapabilities
      type: object
    BlockSchemaFilterId:
      additionalProperties: false
      description: Filter by BlockSchema.id
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of IDs to include
          title: Any
      title: BlockSchemaFilterId
      type: object
    BlockSchemaFilterVersion:
      additionalProperties: false
      description: Filter by `BlockSchema.capabilities`
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of block schema versions.
          examples:
          - - 2.0.0
            - 2.1.0
          title: Any
      title: BlockSchemaFilterVersion
      type: object
    BlockType:
      description: An ORM representation of a block type
      properties:
        code_example:
          anyOf:
          - type: string
          - type: 'null'
          description: A code snippet demonstrating use of the corresponding block
          title: Code Example
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A short blurb about the corresponding block's intended use
          title: Description
        documentation_url:
          anyOf:
          - type: string
          - type: 'null'
          description: Web URL for the block type's documentation
          title: Documentation Url
        id:
          format: uuid
          title: Id
          type: string
        is_protected:
          default: false
          description: Protected block types cannot be modified via API.
          title: Is Protected
          type: boolean
        logo_url:
          anyOf:
          - type: string
          - type: 'null'
          description: Web URL for the block type's logo
          title: Logo Url
        name:
          description: A block type's name
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        slug:
          description: A block type's slug
          title: Slug
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - slug
      - id
      - created
      - updated
      title: BlockType
      type: object
    BlockTypeCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a block type.
      properties:
        code_example:
          anyOf:
          - type: string
          - type: 'null'
          description: A code snippet demonstrating use of the corresponding block
          title: Code Example
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A short blurb about the corresponding block's intended use
          title: Description
        documentation_url:
          anyOf:
          - type: string
          - type: 'null'
          description: Web URL for the block type's documentation
          title: Documentation Url
        logo_url:
          anyOf:
          - type: string
          - type: 'null'
          description: Web URL for the block type's logo
          title: Logo Url
        name:
          description: A block type's name
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        slug:
          description: A block type's slug
          title: Slug
          type: string
      required:
      - name
      - slug
      title: BlockTypeCreate
      type: object
    BlockTypeFilter:
      additionalProperties: false
      description: Filter BlockTypes
      properties:
        name:
          anyOf:
          - $ref: '#/components/schemas/BlockTypeFilterName'
          - type: 'null'
          description: Filter criteria for `BlockType.name`
        slug:
          anyOf:
          - $ref: '#/components/schemas/BlockTypeFilterSlug'
          - type: 'null'
          description: Filter criteria for `BlockType.slug`
      title: BlockTypeFilter
      type: object
    BlockTypeFilterName:
      additionalProperties: false
      description: Filter by `BlockType.name`
      properties:
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A case-insensitive partial match. For example,  passing 'marvin'
            will match 'marvin', 'sad-Marvin', and 'marvin-robot'.
          examples:
          - marvin
          title: Like
      title: BlockTypeFilterName
      type: object
    BlockTypeFilterSlug:
      additionalProperties: false
      description: Filter by `BlockType.slug`
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of slugs to match
          title: Any
      title: BlockTypeFilterSlug
      type: object
    BlockTypeUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a block type.
      properties:
        code_example:
          anyOf:
          - type: string
          - type: 'null'
          title: Code Example
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        documentation_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Documentation Url
        logo_url:
          anyOf:
          - type: string
          - type: 'null'
          title: Logo Url
      title: BlockTypeUpdate
      type: object
    Body_average_flow_run_lateness_flow_runs_lateness_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        task_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilter'
          - type: 'null'
        work_pool_queues:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueFilter'
          - type: 'null'
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
      title: Body_average_flow_run_lateness_flow_runs_lateness_post
      type: object
    Body_bulk_decrement_active_slots_v2_concurrency_limits_decrement_post:
      properties:
        create_if_missing:
          default: true
          title: Create If Missing
          type: boolean
        names:
          items:
            type: string
          min_items: 1
          title: Names
          type: array
        occupancy_seconds:
          anyOf:
          - exclusiveMinimum: 0.0
            type: number
          - type: 'null'
          title: Occupancy Seconds
        slots:
          exclusiveMinimum: 0.0
          title: Slots
          type: integer
      required:
      - slots
      - names
      title: Body_bulk_decrement_active_slots_v2_concurrency_limits_decrement_post
      type: object
    Body_bulk_increment_active_slots_v2_concurrency_limits_increment_post:
      properties:
        create_if_missing:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Create If Missing
        mode:
          default: concurrency
          enum:
          - concurrency
          - rate_limit
          title: Mode
          type: string
        names:
          items:
            type: string
          min_items: 1
          title: Names
          type: array
        slots:
          exclusiveMinimum: 0.0
          title: Slots
          type: integer
      required:
      - slots
      - names
      title: Body_bulk_increment_active_slots_v2_concurrency_limits_increment_post
      type: object
    Body_clear_database_admin_database_clear_post:
      properties:
        confirm:
          default: false
          description: Pass confirm=True to confirm you want to modify the database.
          title: Confirm
          type: boolean
      title: Body_clear_database_admin_database_clear_post
      type: object
    Body_count_account_events_events_count_by__countable__post:
      properties:
        filter:
          $ref: '#/components/schemas/EventFilter'
        time_interval:
          default: 1.0
          minimum: 0.01
          title: Time Interval
          type: number
        time_unit:
          $ref: '#/components/schemas/TimeUnit'
          default: day
      required:
      - filter
      title: Body_count_account_events_events_count_by__countable__post
      type: object
    Body_count_artifacts_artifacts_count_post:
      properties:
        artifacts:
          $ref: '#/components/schemas/ArtifactFilter'
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
      title: Body_count_artifacts_artifacts_count_post
      type: object
    Body_count_block_documents_block_documents_count_post:
      properties:
        block_documents:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentFilter'
          - type: 'null'
        block_schemas:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilter'
          - type: 'null'
        block_types:
          anyOf:
          - $ref: '#/components/schemas/BlockTypeFilter'
          - type: 'null'
      title: Body_count_block_documents_block_documents_count_post
      type: object
    Body_count_deployments_by_flow_ui_flows_count_deployments_post:
      properties:
        flow_ids:
          items:
            format: uuid
            type: string
          max_items: 200
          title: Flow Ids
          type: array
      required:
      - flow_ids
      title: Body_count_deployments_by_flow_ui_flows_count_deployments_post
      type: object
    Body_count_deployments_deployments_count_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pool_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_count_deployments_deployments_count_post
      type: object
    Body_count_flow_runs_flow_runs_count_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pool_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_count_flow_runs_flow_runs_count_post
      type: object
    Body_count_flows_flows_count_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_count_flows_flows_count_post
      type: object
    Body_count_latest_artifacts_artifacts_latest_count_post:
      properties:
        artifacts:
          $ref: '#/components/schemas/ArtifactCollectionFilter'
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
      title: Body_count_latest_artifacts_artifacts_latest_count_post
      type: object
    Body_count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post:
      properties:
        flow_run_ids:
          items:
            format: uuid
            type: string
          max_items: 200
          title: Flow Run Ids
          type: array
      required:
      - flow_run_ids
      title: Body_count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post
      type: object
    Body_count_task_runs_task_runs_count_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
      title: Body_count_task_runs_task_runs_count_post
      type: object
    Body_count_variables_variables_count_post:
      properties:
        variables:
          anyOf:
          - $ref: '#/components/schemas/VariableFilter'
          - type: 'null'
      title: Body_count_variables_variables_count_post
      type: object
    Body_count_work_pools_work_pools_count_post:
      properties:
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
      title: Body_count_work_pools_work_pools_count_post
      type: object
    Body_create_database_admin_database_create_post:
      properties:
        confirm:
          default: false
          description: Pass confirm=True to confirm you want to modify the database.
          title: Confirm
          type: boolean
      title: Body_create_database_admin_database_create_post
      type: object
    Body_create_flow_run_input_flow_runs__id__input_post:
      properties:
        key:
          description: The input key
          title: Key
          type: string
        sender:
          anyOf:
          - type: string
          - type: 'null'
          description: The sender of the input
          title: Sender
        value:
          description: The value of the input
          format: binary
          title: Value
          type: string
      required:
      - key
      - value
      title: Body_create_flow_run_input_flow_runs__id__input_post
      type: object
    Body_decrement_concurrency_limits_v1_concurrency_limits_decrement_post:
      properties:
        names:
          description: The tags to release a slot for
          items:
            type: string
          title: Names
          type: array
        task_run_id:
          description: The ID of the task run releasing the slot
          format: uuid
          title: Task Run Id
          type: string
      required:
      - names
      - task_run_id
      title: Body_decrement_concurrency_limits_v1_concurrency_limits_decrement_post
      type: object
    Body_drop_database_admin_database_drop_post:
      properties:
        confirm:
          default: false
          description: Pass confirm=True to confirm you want to modify the database.
          title: Confirm
          type: boolean
      title: Body_drop_database_admin_database_drop_post
      type: object
    Body_filter_flow_run_input_flow_runs__id__input_filter_post:
      properties:
        exclude_keys:
          default: []
          description: Exclude inputs with these keys
          items:
            type: string
          title: Exclude Keys
          type: array
        limit:
          default: 1
          description: The maximum number of results to return
          title: Limit
          type: integer
        prefix:
          description: The input key prefix
          title: Prefix
          type: string
      required:
      - prefix
      title: Body_filter_flow_run_input_flow_runs__id__input_filter_post
      type: object
    Body_flow_run_history_flow_runs_history_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        history_end:
          description: The history's end time.
          format: date-time
          title: History End
          type: string
        history_interval:
          description: The size of each history interval, in seconds. Must be at least
            1 second.
          format: time-delta
          title: History Interval
          type: number
        history_start:
          description: The history's start time.
          format: date-time
          title: History Start
          type: string
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
        work_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
      required:
      - history_start
      - history_end
      - history_interval
      title: Body_flow_run_history_flow_runs_history_post
      type: object
    Body_get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post:
      properties:
        deployment_ids:
          description: The deployment IDs to get scheduled runs for
          items:
            format: uuid
            type: string
          title: Deployment Ids
          type: array
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        scheduled_before:
          description: The maximum time to look for scheduled flow runs
          format: date-time
          title: Scheduled Before
          type: string
      required:
      - deployment_ids
      title: Body_get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post
      type: object
    Body_get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        scheduled_after:
          description: The minimum time to look for scheduled flow runs
          format: date-time
          title: Scheduled After
          type: string
        scheduled_before:
          description: The maximum time to look for scheduled flow runs
          format: date-time
          title: Scheduled Before
          type: string
        work_queue_names:
          description: The names of work pool queues
          items:
            type: string
          title: Work Queue Names
          type: array
      title: Body_get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post
      type: object
    Body_increment_concurrency_limits_v1_concurrency_limits_increment_post:
      properties:
        names:
          description: The tags to acquire a slot for
          items:
            type: string
          title: Names
          type: array
        task_run_id:
          description: The ID of the task run acquiring the slot
          format: uuid
          title: Task Run Id
          type: string
      required:
      - names
      - task_run_id
      title: Body_increment_concurrency_limits_v1_concurrency_limits_increment_post
      type: object
    Body_next_runs_by_flow_ui_flows_next_runs_post:
      properties:
        flow_ids:
          items:
            format: uuid
            type: string
          max_items: 200
          title: Flow Ids
          type: array
      required:
      - flow_ids
      title: Body_next_runs_by_flow_ui_flows_next_runs_post
      type: object
    Body_paginate_deployments_deployments_paginate_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        page:
          default: 1
          minimum: 1.0
          title: Page
          type: integer
        sort:
          $ref: '#/components/schemas/DeploymentSort'
          default: NAME_ASC
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pool_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_paginate_deployments_deployments_paginate_post
      type: object
    Body_paginate_flow_runs_flow_runs_paginate_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        page:
          default: 1
          minimum: 1.0
          title: Page
          type: integer
        sort:
          $ref: '#/components/schemas/FlowRunSort'
          default: ID_DESC
        task_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilter'
          - type: 'null'
        work_pool_queues:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueFilter'
          - type: 'null'
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
      title: Body_paginate_flow_runs_flow_runs_paginate_post
      type: object
    Body_paginate_flows_flows_paginate_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        page:
          default: 1
          minimum: 1.0
          title: Page
          type: integer
        sort:
          $ref: '#/components/schemas/FlowSort'
          default: NAME_ASC
        task_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilter'
          - type: 'null'
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
      title: Body_paginate_flows_flows_paginate_post
      type: object
    Body_read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
      title: Body_read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post
      type: object
    Body_read_artifacts_artifacts_filter_post:
      properties:
        artifacts:
          $ref: '#/components/schemas/ArtifactFilter'
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/ArtifactSort'
          default: ID_DESC
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
      title: Body_read_artifacts_artifacts_filter_post
      type: object
    Body_read_automations_automations_filter_post:
      properties:
        automations:
          anyOf:
          - $ref: '#/components/schemas/AutomationFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/AutomationSort'
          default: NAME_ASC
      title: Body_read_automations_automations_filter_post
      type: object
    Body_read_block_documents_block_documents_filter_post:
      properties:
        block_documents:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentFilter'
          - type: 'null'
        block_schemas:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilter'
          - type: 'null'
        block_types:
          anyOf:
          - $ref: '#/components/schemas/BlockTypeFilter'
          - type: 'null'
        include_secrets:
          default: false
          description: Whether to include sensitive values in the block document.
          title: Include Secrets
          type: boolean
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          anyOf:
          - $ref: '#/components/schemas/BlockDocumentSort'
          - type: 'null'
          default: NAME_ASC
      title: Body_read_block_documents_block_documents_filter_post
      type: object
    Body_read_block_schemas_block_schemas_filter_post:
      properties:
        block_schemas:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
      title: Body_read_block_schemas_block_schemas_filter_post
      type: object
    Body_read_block_types_block_types_filter_post:
      properties:
        block_schemas:
          anyOf:
          - $ref: '#/components/schemas/BlockSchemaFilter'
          - type: 'null'
        block_types:
          anyOf:
          - $ref: '#/components/schemas/BlockTypeFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
      title: Body_read_block_types_block_types_filter_post
      type: object
    Body_read_concurrency_limits_concurrency_limits_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
      title: Body_read_concurrency_limits_concurrency_limits_filter_post
      type: object
    Body_read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
        work_queues:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueFilter'
          - type: 'null'
      required:
      - task_runs
      title: Body_read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post
      type: object
    Body_read_deployments_deployments_filter_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/DeploymentSort'
          default: NAME_ASC
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pool_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_read_deployments_deployments_filter_post
      type: object
    Body_read_events_events_filter_post:
      properties:
        filter:
          anyOf:
          - $ref: '#/components/schemas/EventFilter'
          - type: 'null'
          description: Additional optional filter criteria to narrow down the set
            of Events
        limit:
          default: 50
          description: The number of events to return with each page
          maximum: 50.0
          minimum: 0.0
          title: Limit
          type: integer
      title: Body_read_events_events_filter_post
      type: object
    Body_read_flow_run_history_ui_flow_runs_history_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        limit:
          default: 1000
          maximum: 1000.0
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/FlowRunSort'
          default: EXPECTED_START_TIME_DESC
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_read_flow_run_history_ui_flow_runs_history_post
      type: object
    Body_read_flow_run_notification_policies_flow_run_notification_policies_filter_post:
      properties:
        flow_run_notification_policy_filter:
          $ref: '#/components/schemas/FlowRunNotificationPolicyFilter'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
      title: Body_read_flow_run_notification_policies_flow_run_notification_policies_filter_post
      type: object
    Body_read_flow_runs_flow_runs_filter_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/FlowRunSort'
          default: ID_DESC
        task_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilter'
          - type: 'null'
        work_pool_queues:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueFilter'
          - type: 'null'
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
      title: Body_read_flow_runs_flow_runs_filter_post
      type: object
    Body_read_flows_flows_filter_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/FlowSort'
          default: NAME_ASC
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
        work_pools:
          $ref: '#/components/schemas/WorkPoolFilter'
      title: Body_read_flows_flows_filter_post
      type: object
    Body_read_latest_artifacts_artifacts_latest_filter_post:
      properties:
        artifacts:
          $ref: '#/components/schemas/ArtifactCollectionFilter'
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/ArtifactCollectionSort'
          default: ID_DESC
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
      title: Body_read_latest_artifacts_artifacts_latest_filter_post
      type: object
    Body_read_logs_logs_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        logs:
          $ref: '#/components/schemas/LogFilter'
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/LogSort'
          default: TIMESTAMP_ASC
      title: Body_read_logs_logs_filter_post
      type: object
    Body_read_saved_searches_saved_searches_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
      title: Body_read_saved_searches_saved_searches_filter_post
      type: object
    Body_read_task_run_counts_by_state_ui_task_runs_count_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        task_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilter'
          - type: 'null'
      title: Body_read_task_run_counts_by_state_ui_task_runs_count_post
      type: object
    Body_read_task_runs_task_runs_filter_post:
      properties:
        deployments:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilter'
          - type: 'null'
        flow_runs:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilter'
          - type: 'null'
        flows:
          anyOf:
          - $ref: '#/components/schemas/FlowFilter'
          - type: 'null'
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/TaskRunSort'
          default: ID_DESC
        task_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilter'
          - type: 'null'
      title: Body_read_task_runs_task_runs_filter_post
      type: object
    Body_read_task_workers_task_workers_filter_post:
      properties:
        task_worker_filter:
          anyOf:
          - $ref: '#/components/schemas/TaskWorkerFilter'
          - type: 'null'
          description: The task worker filter
      title: Body_read_task_workers_task_workers_filter_post
      type: object
    Body_read_variables_variables_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        sort:
          $ref: '#/components/schemas/VariableSort'
          default: NAME_ASC
        variables:
          anyOf:
          - $ref: '#/components/schemas/VariableFilter'
          - type: 'null'
      title: Body_read_variables_variables_filter_post
      type: object
    Body_read_work_pools_work_pools_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        work_pools:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilter'
          - type: 'null'
      title: Body_read_work_pools_work_pools_filter_post
      type: object
    Body_read_work_queue_runs_work_queues__id__get_runs_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        scheduled_before:
          description: Only flow runs scheduled to start before this time will be
            returned.
          format: date-time
          title: Scheduled Before
          type: string
      title: Body_read_work_queue_runs_work_queues__id__get_runs_post
      type: object
    Body_read_work_queues_work_pools__work_pool_name__queues_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        work_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
      title: Body_read_work_queues_work_pools__work_pool_name__queues_filter_post
      type: object
    Body_read_work_queues_work_queues_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        work_queues:
          $ref: '#/components/schemas/WorkQueueFilter'
      title: Body_read_work_queues_work_queues_filter_post
      type: object
    Body_read_workers_work_pools__work_pool_name__workers_filter_post:
      properties:
        limit:
          description: Defaults to PREFECT_API_DEFAULT_LIMIT if not provided.
          title: Limit
          type: integer
        offset:
          default: 0
          minimum: 0.0
          title: Offset
          type: integer
        workers:
          anyOf:
          - $ref: '#/components/schemas/WorkerFilter'
          - type: 'null'
      title: Body_read_workers_work_pools__work_pool_name__workers_filter_post
      type: object
    Body_reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post:
      properties:
        slot_override:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: Manual override for active concurrency limit slots.
          title: Slot Override
      title: Body_reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post
      type: object
    Body_resume_flow_run_flow_runs__id__resume_post:
      properties:
        run_input:
          anyOf:
          - type: object
          - type: 'null'
          title: Run Input
      title: Body_resume_flow_run_flow_runs__id__resume_post
      type: object
    Body_schedule_deployment_deployments__id__schedule_post:
      properties:
        end_time:
          description: The latest date to schedule
          format: date-time
          title: End Time
          type: string
        max_runs:
          description: The maximum number of runs to schedule
          title: Max Runs
          type: integer
        min_runs:
          description: The minimum number of runs to schedule
          title: Min Runs
          type: integer
        min_time:
          description: Runs will be scheduled until at least this long after the `start_time`
          format: time-delta
          title: Min Time
          type: number
        start_time:
          description: The earliest date to schedule
          format: date-time
          title: Start Time
          type: string
      title: Body_schedule_deployment_deployments__id__schedule_post
      type: object
    Body_set_flow_run_state_flow_runs__id__set_state_post:
      properties:
        force:
          default: false
          description: If false, orchestration rules will be applied that may alter
            or prevent the state transition. If True, orchestration rules are not
            applied.
          title: Force
          type: boolean
        state:
          $ref: '#/components/schemas/StateCreate'
          description: The intended state.
      required:
      - state
      title: Body_set_flow_run_state_flow_runs__id__set_state_post
      type: object
    Body_set_task_run_state_task_runs__id__set_state_post:
      properties:
        force:
          default: false
          description: If false, orchestration rules will be applied that may alter
            or prevent the state transition. If True, orchestration rules are not
            applied.
          title: Force
          type: boolean
        state:
          $ref: '#/components/schemas/StateCreate'
          description: The intended state.
      required:
      - state
      title: Body_set_task_run_state_task_runs__id__set_state_post
      type: object
    Body_task_run_history_task_runs_history_post:
      properties:
        deployments:
          $ref: '#/components/schemas/DeploymentFilter'
        flow_runs:
          $ref: '#/components/schemas/FlowRunFilter'
        flows:
          $ref: '#/components/schemas/FlowFilter'
        history_end:
          description: The history's end time.
          format: date-time
          title: History End
          type: string
        history_interval:
          description: The size of each history interval, in seconds. Must be at least
            1 second.
          format: time-delta
          title: History Interval
          type: number
        history_start:
          description: The history's start time.
          format: date-time
          title: History Start
          type: string
        task_runs:
          $ref: '#/components/schemas/TaskRunFilter'
      required:
      - history_start
      - history_end
      - history_interval
      title: Body_task_run_history_task_runs_history_post
      type: object
    Body_validate_obj_ui_schemas_validate_post:
      properties:
        json_schema:
          additionalProperties: true
          title: Json Schema
          type: object
        values:
          additionalProperties: true
          title: Values
          type: object
      required:
      - json_schema
      - values
      title: Body_validate_obj_ui_schemas_validate_post
      type: object
    Body_worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post:
      properties:
        heartbeat_interval_seconds:
          anyOf:
          - type: integer
          - type: 'null'
          description: The worker's heartbeat interval in seconds
          title: Heartbeat Interval Seconds
        name:
          description: The worker process name
          title: Name
          type: string
      required:
      - name
      title: Body_worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post
      type: object
    CallWebhook:
      description: Call a webhook when an Automation is triggered.
      properties:
        block_document_id:
          description: The identifier of the webhook block to use
          format: uuid
          title: Block Document Id
          type: string
        payload:
          default: ''
          description: An optional templatable payload to send when calling the webhook.
          title: Payload
          type: string
        type:
          const: call-webhook
          default: call-webhook
          title: Type
          type: string
      required:
      - block_document_id
      title: CallWebhook
      type: object
    CancelFlowRun:
      description: Cancels a flow run associated with the trigger
      properties:
        type:
          const: cancel-flow-run
          default: cancel-flow-run
          title: Type
          type: string
      title: CancelFlowRun
      type: object
    ChangeFlowRunState:
      description: Changes the state of a flow run associated with the trigger
      properties:
        message:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional message to associate with the state change
          title: Message
        name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the state to change the flow run to
          title: Name
        state:
          $ref: '#/components/schemas/StateType'
          description: The type of the state to change the flow run to
        type:
          const: change-flow-run-state
          default: change-flow-run-state
          title: Type
          type: string
      required:
      - state
      title: ChangeFlowRunState
      type: object
    CompoundTrigger-Input:
      description: 'A composite trigger that requires some number of triggers to have

        fired within the given time period'
      properties:
        id:
          description: The unique ID of this trigger
          format: uuid
          title: Id
          type: string
        require:
          anyOf:
          - type: integer
          - enum:
            - any
            - all
            type: string
          title: Require
        triggers:
          items:
            anyOf:
            - $ref: '#/components/schemas/EventTrigger'
            - $ref: '#/components/schemas/CompoundTrigger-Input'
            - $ref: '#/components/schemas/SequenceTrigger-Input'
          title: Triggers
          type: array
        type:
          const: compound
          default: compound
          title: Type
          type: string
        within:
          anyOf:
          - type: number
          - type: 'null'
          title: Within
      required:
      - triggers
      - within
      - require
      title: CompoundTrigger
      type: object
    CompoundTrigger-Output:
      description: 'A composite trigger that requires some number of triggers to have

        fired within the given time period'
      properties:
        id:
          description: The unique ID of this trigger
          format: uuid
          title: Id
          type: string
        require:
          anyOf:
          - type: integer
          - enum:
            - any
            - all
            type: string
          title: Require
        triggers:
          items:
            anyOf:
            - $ref: '#/components/schemas/EventTrigger'
            - $ref: '#/components/schemas/CompoundTrigger-Output'
            - $ref: '#/components/schemas/SequenceTrigger-Output'
          title: Triggers
          type: array
        type:
          const: compound
          default: compound
          title: Type
          type: string
        within:
          anyOf:
          - type: number
          - type: 'null'
          title: Within
      required:
      - triggers
      - within
      - require
      title: CompoundTrigger
      type: object
    ConcurrencyLimit:
      description: An ORM representation of a concurrency limit.
      properties:
        active_slots:
          description: A list of active run ids using a concurrency slot
          items:
            format: uuid
            type: string
          title: Active Slots
          type: array
        concurrency_limit:
          description: The concurrency limit.
          title: Concurrency Limit
          type: integer
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        id:
          format: uuid
          title: Id
          type: string
        tag:
          description: A tag the concurrency limit is applied to.
          title: Tag
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - tag
      - concurrency_limit
      - id
      - created
      - updated
      title: ConcurrencyLimit
      type: object
    ConcurrencyLimitCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a concurrency limit.
      properties:
        concurrency_limit:
          description: The concurrency limit.
          title: Concurrency Limit
          type: integer
        tag:
          description: A tag the concurrency limit is applied to.
          title: Tag
          type: string
      required:
      - tag
      - concurrency_limit
      title: ConcurrencyLimitCreate
      type: object
    ConcurrencyLimitStrategy:
      description: Enumeration of concurrency collision strategies.
      enum:
      - ENQUEUE
      - CANCEL_NEW
      title: ConcurrencyLimitStrategy
      type: string
    ConcurrencyLimitV2:
      description: An ORM representation of a v2 concurrency limit.
      properties:
        active:
          default: true
          description: Whether the concurrency limit is active.
          title: Active
          type: boolean
        active_slots:
          default: 0
          description: The number of active slots.
          title: Active Slots
          type: integer
        avg_slot_occupancy_seconds:
          default: 2.0
          description: The average amount of time a slot is occupied.
          title: Avg Slot Occupancy Seconds
          type: number
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        denied_slots:
          default: 0
          description: The number of denied slots.
          title: Denied Slots
          type: integer
        id:
          format: uuid
          title: Id
          type: string
        limit:
          description: The concurrency limit.
          title: Limit
          type: integer
        name:
          description: The name of the concurrency limit.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        slot_decay_per_second:
          default: 0
          description: The decay rate for active slots when used as a rate limit.
          title: Slot Decay Per Second
          type: number
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - limit
      - id
      - created
      - updated
      title: ConcurrencyLimitV2
      type: object
    ConcurrencyLimitV2Create:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a v2 concurrency limit.
      properties:
        active:
          default: true
          description: Whether the concurrency limit is active.
          title: Active
          type: boolean
        active_slots:
          default: 0
          description: The number of active slots.
          minimum: 0.0
          title: Active Slots
          type: integer
        denied_slots:
          default: 0
          description: The number of denied slots.
          minimum: 0.0
          title: Denied Slots
          type: integer
        limit:
          description: The concurrency limit.
          minimum: 0.0
          title: Limit
          type: integer
        name:
          description: The name of the concurrency limit.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        slot_decay_per_second:
          default: 0
          description: The decay rate for active slots when used as a rate limit.
          minimum: 0.0
          title: Slot Decay Per Second
          type: number
      required:
      - name
      - limit
      title: ConcurrencyLimitV2Create
      type: object
    ConcurrencyLimitV2Update:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a v2 concurrency limit.
      properties:
        active:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Active
        active_slots:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          title: Active Slots
        denied_slots:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          title: Denied Slots
        limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          title: Limit
        name:
          anyOf:
          - pattern: ^[^/%&><]+$
            type: string
          - type: 'null'
          title: Name
        slot_decay_per_second:
          anyOf:
          - minimum: 0.0
            type: number
          - type: 'null'
          title: Slot Decay Per Second
      title: ConcurrencyLimitV2Update
      type: object
    ConcurrencyOptions:
      description: Class for storing the concurrency config in database.
      properties:
        collision_strategy:
          $ref: '#/components/schemas/ConcurrencyLimitStrategy'
      required:
      - collision_strategy
      title: ConcurrencyOptions
      type: object
    Constant:
      description: Represents constant input value to a task run.
      properties:
        input_type:
          const: constant
          default: constant
          title: Input Type
          type: string
        type:
          title: Type
          type: string
      required:
      - type
      title: Constant
      type: object
    CountByState:
      properties:
        CANCELLED:
          default: 0
          title: Cancelled
          type: integer
        CANCELLING:
          default: 0
          title: Cancelling
          type: integer
        COMPLETED:
          default: 0
          title: Completed
          type: integer
        CRASHED:
          default: 0
          title: Crashed
          type: integer
        FAILED:
          default: 0
          title: Failed
          type: integer
        PAUSED:
          default: 0
          title: Paused
          type: integer
        PENDING:
          default: 0
          title: Pending
          type: integer
        RUNNING:
          default: 0
          title: Running
          type: integer
        SCHEDULED:
          default: 0
          title: Scheduled
          type: integer
      title: CountByState
      type: object
    Countable:
      enum:
      - day
      - time
      - event
      - resource
      title: Countable
      type: string
    CreatedBy:
      properties:
        display_value:
          anyOf:
          - type: string
          - type: 'null'
          description: The display value for the creator.
          title: Display Value
        id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the creator of the object.
          title: Id
        type:
          anyOf:
          - type: string
          - type: 'null'
          description: The type of the creator of the object.
          title: Type
      title: CreatedBy
      type: object
    CronSchedule:
      additionalProperties: false
      description: "Cron schedule\n\nNOTE: If the timezone is a DST-observing one,\
        \ then the schedule will adjust\nitself appropriately. Cron's rules for DST\
        \ are based on schedule times, not\nintervals. This means that an hourly cron\
        \ schedule will fire on every new\nschedule hour, not every elapsed hour;\
        \ for example, when clocks are set back\nthis will result in a two-hour pause\
        \ as the schedule will fire *the first\ntime* 1am is reached and *the first\
        \ time* 2am is reached, 120 minutes later.\nLonger schedules, such as one\
        \ that fires at 9am every morning, will\nautomatically adjust for DST.\n\n\
        Args:\n    cron (str): a valid cron string\n    timezone (str): a valid timezone\
        \ string in IANA tzdata format (for example,\n        America/New_York).\n\
        \    day_or (bool, optional): Control how croniter handles `day` and `day_of_week`\n\
        \        entries. Defaults to True, matching cron which connects those values\
        \ using\n        OR. If the switch is set to False, the values are connected\
        \ using AND. This\n        behaves like fcron and enables you to e.g. define\
        \ a job that executes each\n        2nd friday of a month by setting the days\
        \ of month and the weekday."
      properties:
        cron:
          examples:
          - 0 0 * * *
          title: Cron
          type: string
        day_or:
          default: true
          description: Control croniter behavior for handling day and day_of_week
            entries.
          title: Day Or
          type: boolean
        timezone:
          anyOf:
          - type: string
          - type: 'null'
          examples:
          - America/New_York
          title: Timezone
      required:
      - cron
      title: CronSchedule
      type: object
    CsrfToken:
      properties:
        client:
          description: The client id associated with the CSRF token
          title: Client
          type: string
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        expiration:
          description: The expiration time of the CSRF token
          format: date-time
          title: Expiration
          type: string
        id:
          format: uuid
          title: Id
          type: string
        token:
          description: The CSRF token
          title: Token
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - token
      - client
      - expiration
      - id
      - created
      - updated
      title: CsrfToken
      type: object
    DependencyResult:
      properties:
        end_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: End Time
        estimated_run_time:
          anyOf:
          - type: number
          - type: 'null'
          title: Estimated Run Time
        expected_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Expected Start Time
        id:
          format: uuid
          title: Id
          type: string
        name:
          title: Name
          type: string
        start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Start Time
        state:
          anyOf:
          - $ref: '#/components/schemas/State'
          - type: 'null'
        total_run_time:
          anyOf:
          - type: number
          - type: 'null'
          title: Total Run Time
        untrackable_result:
          title: Untrackable Result
          type: boolean
        upstream_dependencies:
          items:
            $ref: '#/components/schemas/TaskRunResult'
          title: Upstream Dependencies
          type: array
      required:
      - id
      - name
      - upstream_dependencies
      - state
      - expected_start_time
      - start_time
      - end_time
      - total_run_time
      - estimated_run_time
      - untrackable_result
      title: DependencyResult
      type: object
    DeploymentCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a deployment.
      properties:
        concurrency_limit:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The deployment's concurrency limit.
          title: Concurrency Limit
        concurrency_options:
          anyOf:
          - $ref: '#/components/schemas/ConcurrencyOptions'
          - type: 'null'
          description: The deployment's concurrency options.
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        enforce_parameter_schema:
          default: true
          description: Whether or not the deployment should enforce the parameter
            schema.
          title: Enforce Parameter Schema
          type: boolean
        entrypoint:
          anyOf:
          - type: string
          - type: 'null'
          title: Entrypoint
        flow_id:
          description: The ID of the flow associated with the deployment.
          format: uuid
          title: Flow Id
          type: string
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Infrastructure Document Id
        job_variables:
          description: Overrides for the flow's infrastructure configuration.
          title: Job Variables
          type: object
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          description: The name of the deployment.
          examples:
          - my-deployment
          title: Name
          type: string
        parameter_openapi_schema:
          additionalProperties: true
          anyOf:
          - type: object
          - type: 'null'
          description: The parameter schema of the flow, including defaults.
          title: Parameter Openapi Schema
        parameters:
          description: Parameters for flow runs scheduled by the deployment.
          title: Parameters
          type: object
        path:
          anyOf:
          - type: string
          - type: 'null'
          title: Path
        paused:
          default: false
          description: Whether or not the deployment is paused.
          title: Paused
          type: boolean
        pull_steps:
          anyOf:
          - items:
              type: object
            type: array
          - type: 'null'
          title: Pull Steps
        schedules:
          description: A list of schedules for the deployment.
          items:
            $ref: '#/components/schemas/DeploymentScheduleCreate'
          title: Schedules
          type: array
        storage_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Storage Document Id
        tags:
          description: A list of deployment tags.
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        version:
          anyOf:
          - type: string
          - type: 'null'
          title: Version
        work_pool_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the deployment's work pool.
          examples:
          - my-work-pool
          title: Work Pool Name
        work_queue_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Work Queue Name
      required:
      - name
      - flow_id
      title: DeploymentCreate
      type: object
    DeploymentFilter:
      additionalProperties: false
      description: Filter for deployments. Only deployments matching all criteria
        will be returned.
      properties:
        concurrency_limit:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilterConcurrencyLimit'
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: Prefer `Deployment.concurrency_limit_id` over
            `Deployment.concurrency_limit`. If provided, will be ignored for backwards-compatibility.
            Will be removed after December 2024.'
        flow_or_deployment_name:
          anyOf:
          - $ref: '#/components/schemas/DeploymentOrFlowNameFilter'
          - type: 'null'
          description: Filter criteria for `Deployment.name` or `Flow.name`
        id:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilterId'
          - type: 'null'
          description: Filter criteria for `Deployment.id`
        name:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilterName'
          - type: 'null'
          description: Filter criteria for `Deployment.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        paused:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilterPaused'
          - type: 'null'
          description: Filter criteria for `Deployment.paused`
        tags:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilterTags'
          - type: 'null'
          description: Filter criteria for `Deployment.tags`
        work_queue_name:
          anyOf:
          - $ref: '#/components/schemas/DeploymentFilterWorkQueueName'
          - type: 'null'
          description: Filter criteria for `Deployment.work_queue_name`
      title: DeploymentFilter
      type: object
    DeploymentFilterConcurrencyLimit:
      additionalProperties: false
      description: 'DEPRECATED: Prefer `Deployment.concurrency_limit_id` over `Deployment.concurrency_limit`.'
      properties:
        ge_:
          anyOf:
          - type: integer
          - type: 'null'
          description: Only include deployments with a concurrency limit greater than
            or equal to this value
          title: Ge
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include deployments without a concurrency limit
          title: Is Null
        le_:
          anyOf:
          - type: integer
          - type: 'null'
          description: Only include deployments with a concurrency limit less than
            or equal to this value
          title: Le
      title: DeploymentFilterConcurrencyLimit
      type: object
    DeploymentFilterId:
      additionalProperties: false
      description: Filter by `Deployment.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of deployment ids to include
          title: Any
      title: DeploymentFilterId
      type: object
    DeploymentFilterName:
      additionalProperties: false
      description: Filter by `Deployment.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of deployment names to include
          examples:
          - - my-deployment-1
            - my-deployment-2
          title: Any
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A case-insensitive partial match. For example,  passing 'marvin'
            will match 'marvin', 'sad-Marvin', and 'marvin-robot'.
          examples:
          - marvin
          title: Like
      title: DeploymentFilterName
      type: object
    DeploymentFilterPaused:
      additionalProperties: false
      description: Filter by `Deployment.paused`.
      properties:
        eq_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Only returns where deployment is/is not paused
          title: Eq
      title: DeploymentFilterPaused
      type: object
    DeploymentFilterTags:
      additionalProperties: false
      description: Filter by `Deployment.tags`.
      properties:
        all_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags. Deployments will be returned only if their
            tags are a superset of the list
          examples:
          - - tag-1
            - tag-2
          title: All
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags to include
          examples:
          - - tag-1
            - tag-2
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include deployments without tags
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: DeploymentFilterTags
      type: object
    DeploymentFilterWorkQueueName:
      additionalProperties: false
      description: Filter by `Deployment.work_queue_name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of work queue names to include
          examples:
          - - work_queue_1
            - work_queue_2
          title: Any
      title: DeploymentFilterWorkQueueName
      type: object
    DeploymentFlowRunCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a flow run from a deployment.
      properties:
        context:
          title: Context
          type: object
        empirical_policy:
          $ref: '#/components/schemas/FlowRunPolicy'
          description: The empirical policy for the flow run.
        enforce_parameter_schema:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Whether or not to enforce the parameter schema on this run.
          title: Enforce Parameter Schema
        idempotency_key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional idempotency key. If a flow run with the same idempotency
            key has already been created, the existing flow run will be returned.
          title: Idempotency Key
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Infrastructure Document Id
        job_variables:
          anyOf:
          - type: object
          - type: 'null'
          title: Job Variables
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          description: The name of the flow run. Defaults to a random slug if not
            specified.
          examples:
          - my-flow-run
          title: Name
          type: string
        parameters:
          title: Parameters
          type: object
        parent_task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Parent Task Run Id
        state:
          anyOf:
          - $ref: '#/components/schemas/StateCreate'
          - type: 'null'
          description: The state of the flow run to create
        tags:
          description: A list of tags for the flow run.
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        work_queue_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Work Queue Name
      title: DeploymentFlowRunCreate
      type: object
    DeploymentOrFlowNameFilter:
      additionalProperties: false
      description: Filter by `Deployment.name` or `Flow.name` with a single input
        string for ilike filtering.
      properties:
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A case-insensitive partial match on deployment or flow names.
            For example, passing 'example' might match deployments or flows with 'example'
            in their names.
          title: Like
      title: DeploymentOrFlowNameFilter
      type: object
    DeploymentPaginationResponse:
      properties:
        count:
          title: Count
          type: integer
        limit:
          title: Limit
          type: integer
        page:
          title: Page
          type: integer
        pages:
          title: Pages
          type: integer
        results:
          items:
            $ref: '#/components/schemas/DeploymentResponse'
          title: Results
          type: array
      required:
      - results
      - count
      - limit
      - pages
      - page
      title: DeploymentPaginationResponse
      type: object
    DeploymentResponse:
      properties:
        concurrency_limit:
          anyOf:
          - type: integer
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: Prefer `global_concurrency_limit`. Will always
            be None for backwards compatibility. Will be removed after December 2024.'
          title: Concurrency Limit
        concurrency_options:
          anyOf:
          - $ref: '#/components/schemas/ConcurrencyOptions'
          - type: 'null'
          description: The concurrency options for the deployment.
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        created_by:
          anyOf:
          - $ref: '#/components/schemas/CreatedBy'
          - type: 'null'
          description: Optional information about the creator of this deployment.
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A description for the deployment.
          title: Description
        enforce_parameter_schema:
          default: true
          description: Whether or not the deployment should enforce the parameter
            schema.
          title: Enforce Parameter Schema
          type: boolean
        entrypoint:
          anyOf:
          - type: string
          - type: 'null'
          description: The path to the entrypoint for the workflow, relative to the
            `path`.
          title: Entrypoint
        flow_id:
          description: The flow id associated with the deployment.
          format: uuid
          title: Flow Id
          type: string
        global_concurrency_limit:
          anyOf:
          - $ref: '#/components/schemas/GlobalConcurrencyLimitResponse'
          - type: 'null'
          description: The global concurrency limit object for enforcing the maximum
            number of flow runs that can be active at once.
        id:
          format: uuid
          title: Id
          type: string
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The block document defining infrastructure to use for flow
            runs.
          title: Infrastructure Document Id
        job_variables:
          description: Overrides to apply to the base infrastructure block at runtime.
          title: Job Variables
          type: object
        labels:
          additionalProperties:
            anyOf:
            - type: boolean
            - type: integer
            - type: number
            - type: string
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
          type: object
        last_polled:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The last time the deployment was polled for status updates.
          title: Last Polled
        name:
          description: The name of the deployment.
          title: Name
          type: string
        parameter_openapi_schema:
          additionalProperties: true
          anyOf:
          - type: object
          - type: 'null'
          description: The parameter schema of the flow, including defaults.
          title: Parameter Openapi Schema
        parameters:
          description: Parameters for flow runs scheduled by the deployment.
          title: Parameters
          type: object
        path:
          anyOf:
          - type: string
          - type: 'null'
          description: The path to the working directory for the workflow, relative
            to remote storage or an absolute path.
          title: Path
        paused:
          default: false
          description: Whether or not the deployment is paused.
          title: Paused
          type: boolean
        pull_steps:
          anyOf:
          - items:
              type: object
            type: array
          - type: 'null'
          description: Pull steps for cloning and running this deployment.
          title: Pull Steps
        schedules:
          description: A list of schedules for the deployment.
          items:
            $ref: '#/components/schemas/DeploymentSchedule'
          title: Schedules
          type: array
        status:
          anyOf:
          - $ref: '#/components/schemas/DeploymentStatus'
          - type: 'null'
          default: NOT_READY
          description: Whether the deployment is ready to run flows.
        storage_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The block document defining storage used for this flow.
          title: Storage Document Id
        tags:
          description: A list of tags for the deployment
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        updated_by:
          anyOf:
          - $ref: '#/components/schemas/UpdatedBy'
          - type: 'null'
          description: Optional information about the updater of this deployment.
        version:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional version for the deployment.
          title: Version
        work_pool_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the deployment's work pool.
          title: Work Pool Name
        work_queue_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The work queue for the deployment. If no work queue is set,
            work will not be scheduled.
          title: Work Queue Name
      required:
      - name
      - flow_id
      - id
      - created
      - updated
      title: DeploymentResponse
      type: object
    DeploymentSchedule:
      properties:
        active:
          default: true
          description: Whether or not the schedule is active.
          title: Active
          type: boolean
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The deployment id associated with this schedule.
          title: Deployment Id
        id:
          format: uuid
          title: Id
          type: string
        max_scheduled_runs:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The maximum number of scheduled runs for the schedule.
          title: Max Scheduled Runs
        parameters:
          description: A dictionary of parameter value overrides.
          title: Parameters
          type: object
        schedule:
          anyOf:
          - $ref: '#/components/schemas/IntervalSchedule'
          - $ref: '#/components/schemas/CronSchedule'
          - $ref: '#/components/schemas/RRuleSchedule'
          description: The schedule for the deployment.
          title: Schedule
        slug:
          anyOf:
          - type: string
          - type: 'null'
          description: A unique slug for the schedule.
          title: Slug
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - schedule
      - id
      - created
      - updated
      title: DeploymentSchedule
      type: object
    DeploymentScheduleCreate:
      additionalProperties: false
      properties:
        active:
          default: true
          description: Whether or not the schedule is active.
          title: Active
          type: boolean
        max_scheduled_runs:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The maximum number of scheduled runs for the schedule.
          title: Max Scheduled Runs
        parameters:
          description: A dictionary of parameter value overrides.
          title: Parameters
          type: object
        schedule:
          anyOf:
          - $ref: '#/components/schemas/IntervalSchedule'
          - $ref: '#/components/schemas/CronSchedule'
          - $ref: '#/components/schemas/RRuleSchedule'
          description: The schedule for the deployment.
          title: Schedule
        slug:
          anyOf:
          - type: string
          - type: 'null'
          description: A unique identifier for the schedule.
          title: Slug
      required:
      - schedule
      title: DeploymentScheduleCreate
      type: object
    DeploymentScheduleUpdate:
      additionalProperties: false
      properties:
        active:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Whether or not the schedule is active.
          title: Active
        max_scheduled_runs:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The maximum number of scheduled runs for the schedule.
          title: Max Scheduled Runs
        parameters:
          description: A dictionary of parameter value overrides.
          title: Parameters
          type: object
        schedule:
          anyOf:
          - $ref: '#/components/schemas/IntervalSchedule'
          - $ref: '#/components/schemas/CronSchedule'
          - $ref: '#/components/schemas/RRuleSchedule'
          - type: 'null'
          description: The schedule for the deployment.
          title: Schedule
        slug:
          anyOf:
          - type: string
          - type: 'null'
          description: A unique identifier for the schedule.
          title: Slug
      title: DeploymentScheduleUpdate
      type: object
    DeploymentSort:
      description: Defines deployment sorting options.
      enum:
      - CREATED_DESC
      - UPDATED_DESC
      - NAME_ASC
      - NAME_DESC
      title: DeploymentSort
      type: string
    DeploymentStatus:
      description: Enumeration of deployment statuses.
      enum:
      - READY
      - NOT_READY
      title: DeploymentStatus
      type: string
    DeploymentUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a deployment.
      properties:
        concurrency_limit:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The deployment's concurrency limit.
          title: Concurrency Limit
        concurrency_options:
          anyOf:
          - $ref: '#/components/schemas/ConcurrencyOptions'
          - type: 'null'
          description: The deployment's concurrency options.
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        enforce_parameter_schema:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Whether or not the deployment should enforce the parameter
            schema.
          title: Enforce Parameter Schema
        entrypoint:
          anyOf:
          - type: string
          - type: 'null'
          title: Entrypoint
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Infrastructure Document Id
        job_variables:
          anyOf:
          - type: object
          - type: 'null'
          description: Overrides for the flow's infrastructure configuration.
          title: Job Variables
        parameter_openapi_schema:
          anyOf:
          - type: object
          - type: 'null'
          description: The parameter schema of the flow, including defaults.
          title: Parameter Openapi Schema
        parameters:
          anyOf:
          - type: object
          - type: 'null'
          description: Parameters for flow runs scheduled by the deployment.
          title: Parameters
        path:
          anyOf:
          - type: string
          - type: 'null'
          title: Path
        paused:
          default: false
          description: Whether or not the deployment is paused.
          title: Paused
          type: boolean
        pull_steps:
          anyOf:
          - items:
              type: object
            type: array
          - type: 'null'
          title: Pull Steps
        schedules:
          description: A list of schedules for the deployment.
          items:
            $ref: '#/components/schemas/DeploymentScheduleUpdate'
          title: Schedules
          type: array
        storage_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Storage Document Id
        tags:
          description: A list of deployment tags.
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        version:
          anyOf:
          - type: string
          - type: 'null'
          title: Version
        work_pool_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the deployment's work pool.
          examples:
          - my-work-pool
          title: Work Pool Name
        work_queue_name:
          anyOf:
          - type: string
          - type: 'null'
          title: Work Queue Name
      title: DeploymentUpdate
      type: object
    DoNothing:
      description: Do nothing when an Automation is triggered
      properties:
        type:
          const: do-nothing
          default: do-nothing
          title: Type
          type: string
      title: DoNothing
      type: object
    Edge:
      properties:
        id:
          format: uuid
          title: Id
          type: string
      required:
      - id
      title: Edge
      type: object
    Event:
      description: The client-side view of an event that has happened to a Resource
      properties:
        event:
          description: The name of the event that happened
          title: Event
          type: string
        follows:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The ID of an event that is known to have occurred prior to
            this one. If set, this may be used to establish a more precise ordering
            of causally-related events when they occur close enough together in time
            that the system may receive them out-of-order.
          title: Follows
        id:
          description: The client-provided identifier of this event
          format: uuid
          title: Id
          type: string
        occurred:
          description: When the event happened from the sender's perspective
          format: date-time
          title: Occurred
          type: string
        payload:
          description: An open-ended set of data describing what happened
          title: Payload
          type: object
        related:
          description: A list of additional Resources involved in this event
          items:
            $ref: '#/components/schemas/RelatedResource'
          title: Related
          type: array
        resource:
          $ref: '#/components/schemas/Resource'
          description: The primary Resource this event concerns
      required:
      - occurred
      - event
      - resource
      - id
      title: Event
      type: object
    EventAnyResourceFilter:
      additionalProperties: false
      properties:
        id:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events for resources with these IDs
          title: Id
        id_prefix:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events for resources with IDs starting with these
            prefixes
          title: Id Prefix
        labels:
          anyOf:
          - $ref: '#/components/schemas/ResourceSpecification'
          - type: 'null'
          description: Only include events for related resources with these labels
      title: EventAnyResourceFilter
      type: object
    EventCount:
      description: The count of events with the given filter value
      properties:
        count:
          description: The count of matching events
          title: Count
          type: integer
        end_time:
          description: The end time of this group of events
          format: date-time
          title: End Time
          type: string
        label:
          description: The value to display for this count
          title: Label
          type: string
        start_time:
          description: The start time of this group of events
          format: date-time
          title: Start Time
          type: string
        value:
          description: The value to use for filtering
          title: Value
          type: string
      required:
      - value
      - label
      - count
      - start_time
      - end_time
      title: EventCount
      type: object
    EventFilter:
      additionalProperties: false
      properties:
        any_resource:
          anyOf:
          - $ref: '#/components/schemas/EventAnyResourceFilter'
          - type: 'null'
          description: Filter criteria for any resource involved in the event
        event:
          anyOf:
          - $ref: '#/components/schemas/EventNameFilter'
          - type: 'null'
          description: Filter criteria for the event name
        id:
          $ref: '#/components/schemas/EventIDFilter'
          description: Filter criteria for the events' ID
        occurred:
          $ref: '#/components/schemas/EventOccurredFilter'
          description: Filter criteria for when the events occurred
        order:
          $ref: '#/components/schemas/EventOrder'
          default: DESC
          description: The order to return filtered events
        related:
          anyOf:
          - $ref: '#/components/schemas/EventRelatedFilter'
          - type: 'null'
          description: Filter criteria for the related resources of the event
        resource:
          anyOf:
          - $ref: '#/components/schemas/EventResourceFilter'
          - type: 'null'
          description: Filter criteria for the resource of the event
      title: EventFilter
      type: object
    EventIDFilter:
      additionalProperties: false
      properties:
        id:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: Only include events with one of these IDs
          title: Id
      title: EventIDFilter
      type: object
    EventNameFilter:
      additionalProperties: false
      properties:
        exclude_name:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Exclude events matching one of these names exactly
          title: Exclude Name
        exclude_prefix:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Exclude events matching one of these prefixes
          title: Exclude Prefix
        name:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events matching one of these names exactly
          title: Name
        prefix:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events matching one of these prefixes
          title: Prefix
      title: EventNameFilter
      type: object
    EventOccurredFilter:
      additionalProperties: false
      properties:
        since:
          description: Only include events after this time (inclusive)
          format: date-time
          title: Since
          type: string
        until:
          description: Only include events prior to this time (inclusive)
          format: date-time
          title: Until
          type: string
      title: EventOccurredFilter
      type: object
    EventOrder:
      enum:
      - ASC
      - DESC
      title: EventOrder
      type: string
    EventPage:
      description: 'A single page of events returned from the API, with an optional
        link to the

        next page of results'
      properties:
        events:
          description: The Events matching the query
          items:
            $ref: '#/components/schemas/ReceivedEvent'
          title: Events
          type: array
        next_page:
          anyOf:
          - format: uri
            minLength: 1
            type: string
          - type: 'null'
          description: The URL for the next page of results, if there are more
          title: Next Page
        total:
          description: The total number of matching Events
          title: Total
          type: integer
      required:
      - events
      - total
      - next_page
      title: EventPage
      type: object
    EventRelatedFilter:
      additionalProperties: false
      properties:
        id:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events for related resources with these IDs
          title: Id
        labels:
          anyOf:
          - $ref: '#/components/schemas/ResourceSpecification'
          - type: 'null'
          description: Only include events for related resources with these labels
        resources_in_roles:
          anyOf:
          - items:
              maxItems: 2
              minItems: 2
              prefixItems:
              - type: string
              - type: string
              type: array
            type: array
          - type: 'null'
          description: Only include events with specific related resources in specific
            roles
          title: Resources In Roles
        role:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events for related resources in these roles
          title: Role
      title: EventRelatedFilter
      type: object
    EventResourceFilter:
      additionalProperties: false
      properties:
        distinct:
          default: false
          description: Only include events for distinct resources
          title: Distinct
          type: boolean
        id:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events for resources with these IDs
          title: Id
        id_prefix:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include events for resources with IDs starting with these
            prefixes.
          title: Id Prefix
        labels:
          anyOf:
          - $ref: '#/components/schemas/ResourceSpecification'
          - type: 'null'
          description: Only include events for resources with these labels
      title: EventResourceFilter
      type: object
    EventTrigger:
      description: 'A trigger that fires based on the presence or absence of events
        within a given

        period of time.'
      properties:
        after:
          description: The event(s) which must first been seen to fire this trigger.  If
            empty, then fire this trigger immediately.  Events may include trailing
            wildcards, like `prefect.flow-run.*`
          items:
            type: string
          title: After
          type: array
          uniqueItems: true
        expect:
          description: The event(s) this trigger is expecting to see.  If empty, this
            trigger will match any event.  Events may include trailing wildcards,
            like `prefect.flow-run.*`
          items:
            type: string
          title: Expect
          type: array
          uniqueItems: true
        for_each:
          description: 'Evaluate the trigger separately for each distinct value of
            these labels on the resource.  By default, labels refer to the primary
            resource of the triggering event.  You may also refer to labels from related
            resources by specifying `related:<role>:<label>`.  This will use the value
            of that label for the first related resource in that role.  For example,
            `"for_each": ["related:flow:prefect.resource.id"]` would evaluate the
            trigger for each flow.'
          items:
            type: string
          title: For Each
          type: array
          uniqueItems: true
        id:
          description: The unique ID of this trigger
          format: uuid
          title: Id
          type: string
        match:
          $ref: '#/components/schemas/ResourceSpecification'
          description: Labels for resources which this trigger will match.
        match_related:
          $ref: '#/components/schemas/ResourceSpecification'
          description: Labels for related resources which this trigger will match.
        posture:
          description: The posture of this trigger, either Reactive or Proactive.  Reactive
            triggers respond to the _presence_ of the expected events, while Proactive
            triggers respond to the _absence_ of those expected events.
          enum:
          - Reactive
          - Proactive
          title: Posture
          type: string
        threshold:
          default: 1
          description: The number of events required for this trigger to fire (for
            Reactive triggers), or the number of events expected (for Proactive triggers)
          title: Threshold
          type: integer
        type:
          const: event
          default: event
          title: Type
          type: string
        within:
          default: 0.0
          description: The time period over which the events must occur.  For Reactive
            triggers, this may be as low as 0 seconds, but must be at least 10 seconds
            for Proactive triggers
          title: Within
          type: number
      required:
      - posture
      title: EventTrigger
      type: object
    Flow:
      description: An ORM representation of flow data.
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        id:
          format: uuid
          title: Id
          type: string
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          description: The name of the flow
          examples:
          - my-flow
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        tags:
          description: A list of flow tags
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - id
      - created
      - updated
      title: Flow
      type: object
    FlowCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a flow.
      properties:
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          description: The name of the flow
          examples:
          - my-flow
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        tags:
          description: A list of flow tags
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
      required:
      - name
      title: FlowCreate
      type: object
    FlowFilter:
      additionalProperties: false
      description: Filter for flows. Only flows matching all criteria will be returned.
      properties:
        deployment:
          anyOf:
          - $ref: '#/components/schemas/FlowFilterDeployment'
          - type: 'null'
          description: Filter criteria for Flow deployments
        id:
          anyOf:
          - $ref: '#/components/schemas/FlowFilterId'
          - type: 'null'
          description: Filter criteria for `Flow.id`
        name:
          anyOf:
          - $ref: '#/components/schemas/FlowFilterName'
          - type: 'null'
          description: Filter criteria for `Flow.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        tags:
          anyOf:
          - $ref: '#/components/schemas/FlowFilterTags'
          - type: 'null'
          description: Filter criteria for `Flow.tags`
      title: FlowFilter
      type: object
    FlowFilterDeployment:
      additionalProperties: false
      description: Filter by flows by deployment
      properties:
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include flows without deployments
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowFilterDeployment
      type: object
    FlowFilterId:
      additionalProperties: false
      description: Filter by `Flow.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow ids to include
          title: Any
      title: FlowFilterId
      type: object
    FlowFilterName:
      additionalProperties: false
      description: Filter by `Flow.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow names to include
          examples:
          - - my-flow-1
            - my-flow-2
          title: Any
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A case-insensitive partial match. For example,  passing 'marvin'
            will match 'marvin', 'sad-Marvin', and 'marvin-robot'.
          examples:
          - marvin
          title: Like
      title: FlowFilterName
      type: object
    FlowFilterTags:
      additionalProperties: false
      description: Filter by `Flow.tags`.
      properties:
        all_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags. Flows will be returned only if their tags are
            a superset of the list
          examples:
          - - tag-1
            - tag-2
          title: All
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include flows without tags
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowFilterTags
      type: object
    FlowPaginationResponse:
      properties:
        count:
          title: Count
          type: integer
        limit:
          title: Limit
          type: integer
        page:
          title: Page
          type: integer
        pages:
          title: Pages
          type: integer
        results:
          items:
            $ref: '#/components/schemas/Flow'
          title: Results
          type: array
      required:
      - results
      - count
      - limit
      - pages
      - page
      title: FlowPaginationResponse
      type: object
    FlowRun:
      description: An ORM representation of flow run data.
      properties:
        auto_scheduled:
          default: false
          description: Whether or not the flow run was automatically scheduled.
          title: Auto Scheduled
          type: boolean
        context:
          description: Additional context for the flow run.
          examples:
          - my_var: my_value
          title: Context
          type: object
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        created_by:
          anyOf:
          - $ref: '#/components/schemas/CreatedBy'
          - type: 'null'
          description: Optional information about the creator of this flow run.
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the deployment associated with this flow run, if
            available.
          title: Deployment Id
        deployment_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the deployment associated with this flow run.
          examples:
          - '1.0'
          title: Deployment Version
        empirical_policy:
          $ref: '#/components/schemas/FlowRunPolicy'
        end_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The actual end time.
          title: End Time
        estimated_run_time:
          default: 0.0
          description: A real-time estimate of the total run time.
          title: Estimated Run Time
          type: number
        estimated_start_time_delta:
          default: 0.0
          description: The difference between actual and expected start time.
          title: Estimated Start Time Delta
          type: number
        expected_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The flow run's expected start time.
          title: Expected Start Time
        flow_id:
          description: The id of the flow being run.
          format: uuid
          title: Flow Id
          type: string
        flow_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the flow executed in this flow run.
          examples:
          - '1.0'
          title: Flow Version
        id:
          format: uuid
          title: Id
          type: string
        idempotency_key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional idempotency key for the flow run. Used to ensure
            the same flow run is not created multiple times.
          title: Idempotency Key
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The block document defining infrastructure to use this flow
            run.
          title: Infrastructure Document Id
        infrastructure_pid:
          anyOf:
          - type: string
          - type: 'null'
          description: The id of the flow run as returned by an infrastructure block.
          title: Infrastructure Pid
        job_variables:
          anyOf:
          - type: object
          - type: 'null'
          description: Variables used as overrides in the base job template
          title: Job Variables
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          description: The name of the flow run. Defaults to a random slug if not
            specified.
          examples:
          - my-flow-run
          title: Name
          type: string
        next_scheduled_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The next time the flow run is scheduled to start.
          title: Next Scheduled Start Time
        parameters:
          description: Parameters for the flow run.
          title: Parameters
          type: object
        parent_task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: If the flow run is a subflow, the id of the 'dummy' task in
            the parent flow used to track subflow state.
          title: Parent Task Run Id
        run_count:
          default: 0
          description: The number of times the flow run was executed.
          title: Run Count
          type: integer
        start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The actual start time.
          title: Start Time
        state:
          anyOf:
          - $ref: '#/components/schemas/State'
          - type: 'null'
          description: The current state of the flow run.
        state_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the flow run's current state.
          title: State Id
        state_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the current flow run state.
          title: State Name
        state_type:
          anyOf:
          - $ref: '#/components/schemas/StateType'
          - type: 'null'
          description: The type of the current flow run state.
        tags:
          description: A list of tags on the flow run
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        total_run_time:
          default: 0.0
          description: Total run time. If the flow run was executed multiple times,
            the time of each run will be summed.
          title: Total Run Time
          type: number
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        work_queue_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the run's work pool queue.
          title: Work Queue Id
        work_queue_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The work queue that handled this flow run.
          title: Work Queue Name
      required:
      - flow_id
      - id
      - created
      - updated
      title: FlowRun
      type: object
    FlowRunCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a flow run.
      properties:
        context:
          description: The context of the flow run.
          title: Context
          type: object
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: The id of the deployment associated with this
            flow run, if available.'
          title: Deployment Id
        empirical_policy:
          $ref: '#/components/schemas/FlowRunPolicy'
          description: The empirical policy for the flow run.
        flow_id:
          description: The id of the flow being run.
          format: uuid
          title: Flow Id
          type: string
        flow_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the flow being run.
          title: Flow Version
        idempotency_key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional idempotency key. If a flow run with the same idempotency
            key has already been created, the existing flow run will be returned.
          title: Idempotency Key
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Infrastructure Document Id
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          description: The name of the flow run. Defaults to a random slug if not
            specified.
          examples:
          - my-flow-run
          title: Name
          type: string
        parameters:
          title: Parameters
          type: object
        parent_task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Parent Task Run Id
        state:
          anyOf:
          - $ref: '#/components/schemas/StateCreate'
          - type: 'null'
          description: The state of the flow run to create
        tags:
          description: A list of tags for the flow run.
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
      required:
      - flow_id
      title: FlowRunCreate
      type: object
    FlowRunFilter:
      additionalProperties: false
      description: Filter flow runs. Only flow runs matching all criteria will be
        returned
      properties:
        deployment_id:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterDeploymentId'
          - type: 'null'
          description: Filter criteria for `FlowRun.deployment_id`
        end_time:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterEndTime'
          - type: 'null'
          description: Filter criteria for `FlowRun.end_time`
        expected_start_time:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterExpectedStartTime'
          - type: 'null'
          description: Filter criteria for `FlowRun.expected_start_time`
        flow_version:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterFlowVersion'
          - type: 'null'
          description: Filter criteria for `FlowRun.flow_version`
        id:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterId'
          - type: 'null'
          description: Filter criteria for `FlowRun.id`
        idempotency_key:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterIdempotencyKey'
          - type: 'null'
          description: Filter criteria for `FlowRun.idempotency_key`
        name:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterName'
          - type: 'null'
          description: Filter criteria for `FlowRun.name`
        next_scheduled_start_time:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterNextScheduledStartTime'
          - type: 'null'
          description: Filter criteria for `FlowRun.next_scheduled_start_time`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        parent_flow_run_id:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterParentFlowRunId'
          - type: 'null'
          description: Filter criteria for subflows of the given flow runs
        parent_task_run_id:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterParentTaskRunId'
          - type: 'null'
          description: Filter criteria for `FlowRun.parent_task_run_id`
        start_time:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterStartTime'
          - type: 'null'
          description: Filter criteria for `FlowRun.start_time`
        state:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterState'
          - type: 'null'
          description: Filter criteria for `FlowRun.state`
        tags:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterTags'
          - type: 'null'
          description: Filter criteria for `FlowRun.tags`
        work_queue_name:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterWorkQueueName'
          - type: 'null'
          description: Filter criteria for `FlowRun.work_queue_name
      title: FlowRunFilter
      type: object
    FlowRunFilterDeploymentId:
      additionalProperties: false
      description: Filter by `FlowRun.deployment_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run deployment ids to include
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include flow runs without deployment ids
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowRunFilterDeploymentId
      type: object
    FlowRunFilterEndTime:
      additionalProperties: false
      description: Filter by `FlowRun.end_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs ending at or after this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs ending at or before this time
          title: Before
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only return flow runs without an end time
          title: Is Null
      title: FlowRunFilterEndTime
      type: object
    FlowRunFilterExpectedStartTime:
      additionalProperties: false
      description: Filter by `FlowRun.expected_start_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs scheduled to start at or after this
            time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs scheduled to start at or before this
            time
          title: Before
      title: FlowRunFilterExpectedStartTime
      type: object
    FlowRunFilterFlowVersion:
      additionalProperties: false
      description: Filter by `FlowRun.flow_version`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow run flow_versions to include
          title: Any
      title: FlowRunFilterFlowVersion
      type: object
    FlowRunFilterId:
      additionalProperties: false
      description: Filter by `FlowRun.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run ids to include
          title: Any
        not_any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run ids to exclude
          title: Not Any
      title: FlowRunFilterId
      type: object
    FlowRunFilterIdempotencyKey:
      additionalProperties: false
      description: Filter by FlowRun.idempotency_key.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow run idempotency keys to include
          title: Any
        not_any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow run idempotency keys to exclude
          title: Not Any
      title: FlowRunFilterIdempotencyKey
      type: object
    FlowRunFilterName:
      additionalProperties: false
      description: Filter by `FlowRun.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow run names to include
          examples:
          - - my-flow-run-1
            - my-flow-run-2
          title: Any
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A case-insensitive partial match. For example,  passing 'marvin'
            will match 'marvin', 'sad-Marvin', and 'marvin-robot'.
          examples:
          - marvin
          title: Like
      title: FlowRunFilterName
      type: object
    FlowRunFilterNextScheduledStartTime:
      additionalProperties: false
      description: Filter by `FlowRun.next_scheduled_start_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs with a next_scheduled_start_time at
            or after this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs with a next_scheduled_start_time or
            before this time
          title: Before
      title: FlowRunFilterNextScheduledStartTime
      type: object
    FlowRunFilterParentFlowRunId:
      additionalProperties: false
      description: Filter for subflows of a given flow run
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of parent flow run ids to include
          title: Any
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowRunFilterParentFlowRunId
      type: object
    FlowRunFilterParentTaskRunId:
      additionalProperties: false
      description: Filter by `FlowRun.parent_task_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run parent_task_run_ids to include
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include flow runs without parent_task_run_id
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowRunFilterParentTaskRunId
      type: object
    FlowRunFilterStartTime:
      additionalProperties: false
      description: Filter by `FlowRun.start_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs starting at or after this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include flow runs starting at or before this time
          title: Before
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only return flow runs without a start time
          title: Is Null
      title: FlowRunFilterStartTime
      type: object
    FlowRunFilterState:
      additionalProperties: false
      description: Filter by `FlowRun.state_type` and `FlowRun.state_name`.
      properties:
        name:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterStateName'
          - type: 'null'
          description: Filter criteria for `FlowRun.state_name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        type:
          anyOf:
          - $ref: '#/components/schemas/FlowRunFilterStateType'
          - type: 'null'
          description: Filter criteria for `FlowRun.state_type`
      title: FlowRunFilterState
      type: object
    FlowRunFilterStateName:
      additionalProperties: false
      description: Filter by `FlowRun.state_name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow run state names to include
          title: Any
        not_any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of flow run state names to exclude
          title: Not Any
      title: FlowRunFilterStateName
      type: object
    FlowRunFilterStateType:
      additionalProperties: false
      description: Filter by `FlowRun.state_type`.
      properties:
        any_:
          anyOf:
          - items:
              $ref: '#/components/schemas/StateType'
            type: array
          - type: 'null'
          description: A list of flow run state types to include
          title: Any
        not_any_:
          anyOf:
          - items:
              $ref: '#/components/schemas/StateType'
            type: array
          - type: 'null'
          description: A list of flow run state types to exclude
          title: Not Any
      title: FlowRunFilterStateType
      type: object
    FlowRunFilterTags:
      additionalProperties: false
      description: Filter by `FlowRun.tags`.
      properties:
        all_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags. Flow runs will be returned only if their tags
            are a superset of the list
          examples:
          - - tag-1
            - tag-2
          title: All
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags to include
          examples:
          - - tag-1
            - tag-2
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include flow runs without tags
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowRunFilterTags
      type: object
    FlowRunFilterWorkQueueName:
      additionalProperties: false
      description: Filter by `FlowRun.work_queue_name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of work queue names to include
          examples:
          - - work_queue_1
            - work_queue_2
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include flow runs without work queue names
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: FlowRunFilterWorkQueueName
      type: object
    FlowRunInput:
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        flow_run_id:
          description: The flow run ID associated with the input.
          format: uuid
          title: Flow Run Id
          type: string
        id:
          format: uuid
          title: Id
          type: string
        key:
          description: The key of the input.
          title: Key
          type: string
        sender:
          anyOf:
          - type: string
          - type: 'null'
          description: The sender of the input.
          title: Sender
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        value:
          description: The value of the input.
          title: Value
          type: string
      required:
      - flow_run_id
      - key
      - value
      - id
      - created
      - updated
      title: FlowRunInput
      type: object
    FlowRunNotificationPolicy:
      description: An ORM representation of a flow run notification.
      properties:
        block_document_id:
          description: The block document ID used for sending notifications
          format: uuid
          title: Block Document Id
          type: string
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        id:
          format: uuid
          title: Id
          type: string
        is_active:
          default: true
          description: Whether the policy is currently active
          title: Is Active
          type: boolean
        message_template:
          anyOf:
          - type: string
          - type: 'null'
          description: 'A templatable notification message. Use {braces} to add variables.
            Valid variables include: ''flow_id'', ''flow_name'', ''flow_run_id'',
            ''flow_run_name'', ''flow_run_notification_policy_id'', ''flow_run_parameters'',
            ''flow_run_state_message'', ''flow_run_state_name'', ''flow_run_state_timestamp'',
            ''flow_run_state_type'', ''flow_run_url'''
          examples:
          - Flow run {flow_run_name} with id {flow_run_id} entered state {flow_run_state_name}.
          title: Message Template
        state_names:
          description: The flow run states that trigger notifications
          items:
            type: string
          title: State Names
          type: array
        tags:
          description: The flow run tags that trigger notifications (set [] to disable)
          items:
            type: string
          title: Tags
          type: array
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - state_names
      - tags
      - block_document_id
      - id
      - created
      - updated
      title: FlowRunNotificationPolicy
      type: object
    FlowRunNotificationPolicyCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a flow run notification
        policy.
      properties:
        block_document_id:
          description: The block document ID used for sending notifications
          format: uuid
          title: Block Document Id
          type: string
        is_active:
          default: true
          description: Whether the policy is currently active
          title: Is Active
          type: boolean
        message_template:
          anyOf:
          - type: string
          - type: 'null'
          description: 'A templatable notification message. Use {braces} to add variables.
            Valid variables include: ''flow_id'', ''flow_name'', ''flow_run_id'',
            ''flow_run_name'', ''flow_run_notification_policy_id'', ''flow_run_parameters'',
            ''flow_run_state_message'', ''flow_run_state_name'', ''flow_run_state_timestamp'',
            ''flow_run_state_type'', ''flow_run_url'''
          examples:
          - Flow run {flow_run_name} with id {flow_run_id} entered state {flow_run_state_name}.
          title: Message Template
        state_names:
          description: The flow run states that trigger notifications
          items:
            type: string
          title: State Names
          type: array
        tags:
          description: The flow run tags that trigger notifications (set [] to disable)
          items:
            type: string
          title: Tags
          type: array
      required:
      - state_names
      - tags
      - block_document_id
      title: FlowRunNotificationPolicyCreate
      type: object
    FlowRunNotificationPolicyFilter:
      additionalProperties: false
      description: Filter FlowRunNotificationPolicies.
      properties:
        is_active:
          anyOf:
          - $ref: '#/components/schemas/FlowRunNotificationPolicyFilterIsActive'
          - type: 'null'
          default:
            eq_: false
          description: 'Filter criteria for `FlowRunNotificationPolicy.is_active`. '
      title: FlowRunNotificationPolicyFilter
      type: object
    FlowRunNotificationPolicyFilterIsActive:
      additionalProperties: false
      description: Filter by `FlowRunNotificationPolicy.is_active`.
      properties:
        eq_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: Filter notification policies for only those that are or are
            not active.
          title: Eq
      title: FlowRunNotificationPolicyFilterIsActive
      type: object
    FlowRunNotificationPolicyUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a flow run notification
        policy.
      properties:
        block_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Block Document Id
        is_active:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Active
        message_template:
          anyOf:
          - type: string
          - type: 'null'
          title: Message Template
        state_names:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: State Names
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          title: Tags
      title: FlowRunNotificationPolicyUpdate
      type: object
    FlowRunPaginationResponse:
      properties:
        count:
          title: Count
          type: integer
        limit:
          title: Limit
          type: integer
        page:
          title: Page
          type: integer
        pages:
          title: Pages
          type: integer
        results:
          items:
            $ref: '#/components/schemas/FlowRunResponse'
          title: Results
          type: array
      required:
      - results
      - count
      - limit
      - pages
      - page
      title: FlowRunPaginationResponse
      type: object
    FlowRunPolicy:
      description: Defines of how a flow run should retry.
      properties:
        max_retries:
          default: 0
          deprecated: true
          description: The maximum number of retries. Field is not used. Please use
            `retries` instead.
          title: Max Retries
          type: integer
        pause_keys:
          anyOf:
          - items:
              type: string
            type: array
            uniqueItems: true
          - type: 'null'
          description: Tracks pauses this run has observed.
          title: Pause Keys
        resuming:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Indicates if this run is resuming from a pause.
          title: Resuming
        retries:
          anyOf:
          - type: integer
          - type: 'null'
          description: The number of retries.
          title: Retries
        retry_delay:
          anyOf:
          - type: integer
          - type: 'null'
          description: The delay time between retries, in seconds.
          title: Retry Delay
        retry_delay_seconds:
          default: 0
          deprecated: true
          description: The delay between retries. Field is not used. Please use `retry_delay`
            instead.
          title: Retry Delay Seconds
          type: number
        retry_type:
          anyOf:
          - enum:
            - in_process
            - reschedule
            type: string
          - type: 'null'
          description: The type of retry this run is undergoing.
          title: Retry Type
      title: FlowRunPolicy
      type: object
    FlowRunResponse:
      properties:
        auto_scheduled:
          default: false
          description: Whether or not the flow run was automatically scheduled.
          title: Auto Scheduled
          type: boolean
        context:
          description: Additional context for the flow run.
          examples:
          - my_var: my_val
          title: Context
          type: object
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        created_by:
          anyOf:
          - $ref: '#/components/schemas/CreatedBy'
          - type: 'null'
          description: Optional information about the creator of this flow run.
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the deployment associated with this flow run, if
            available.
          title: Deployment Id
        deployment_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the deployment associated with this flow run.
          examples:
          - '1.0'
          title: Deployment Version
        empirical_policy:
          $ref: '#/components/schemas/FlowRunPolicy'
        end_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The actual end time.
          title: End Time
        estimated_run_time:
          default: 0.0
          description: A real-time estimate of the total run time.
          title: Estimated Run Time
          type: number
        estimated_start_time_delta:
          default: 0.0
          description: The difference between actual and expected start time.
          title: Estimated Start Time Delta
          type: number
        expected_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The flow run's expected start time.
          title: Expected Start Time
        flow_id:
          description: The id of the flow being run.
          format: uuid
          title: Flow Id
          type: string
        flow_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the flow executed in this flow run.
          examples:
          - '1.0'
          title: Flow Version
        id:
          format: uuid
          title: Id
          type: string
        idempotency_key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional idempotency key for the flow run. Used to ensure
            the same flow run is not created multiple times.
          title: Idempotency Key
        infrastructure_document_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The block document defining infrastructure to use this flow
            run.
          title: Infrastructure Document Id
        infrastructure_pid:
          anyOf:
          - type: string
          - type: 'null'
          description: The id of the flow run as returned by an infrastructure block.
          title: Infrastructure Pid
        job_variables:
          anyOf:
          - type: object
          - type: 'null'
          description: Variables used as overrides in the base job template
          title: Job Variables
        labels:
          additionalProperties:
            anyOf:
            - type: boolean
            - type: integer
            - type: number
            - type: string
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
          type: object
        name:
          description: The name of the flow run. Defaults to a random slug if not
            specified.
          examples:
          - my-flow-run
          title: Name
          type: string
        next_scheduled_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The next time the flow run is scheduled to start.
          title: Next Scheduled Start Time
        parameters:
          description: Parameters for the flow run.
          title: Parameters
          type: object
        parent_task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: If the flow run is a subflow, the id of the 'dummy' task in
            the parent flow used to track subflow state.
          title: Parent Task Run Id
        run_count:
          default: 0
          description: The number of times the flow run was executed.
          title: Run Count
          type: integer
        start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The actual start time.
          title: Start Time
        state:
          anyOf:
          - $ref: '#/components/schemas/State'
          - type: 'null'
          description: The current state of the flow run.
        state_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the flow run's current state.
          title: State Id
        state_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the current flow run state.
          title: State Name
        state_type:
          anyOf:
          - $ref: '#/components/schemas/StateType'
          - type: 'null'
          description: The type of the current flow run state.
        tags:
          description: A list of tags on the flow run
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        total_run_time:
          default: 0.0
          description: Total run time. If the flow run was executed multiple times,
            the time of each run will be summed.
          title: Total Run Time
          type: number
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        work_pool_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the flow run's work pool.
          title: Work Pool Id
        work_pool_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the flow run's work pool.
          examples:
          - my-work-pool
          title: Work Pool Name
        work_queue_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the run's work pool queue.
          title: Work Queue Id
        work_queue_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The work queue that handled this flow run.
          title: Work Queue Name
      required:
      - flow_id
      - id
      - created
      - updated
      title: FlowRunResponse
      type: object
    FlowRunSort:
      description: Defines flow run sorting options.
      enum:
      - ID_DESC
      - START_TIME_ASC
      - START_TIME_DESC
      - EXPECTED_START_TIME_ASC
      - EXPECTED_START_TIME_DESC
      - NAME_ASC
      - NAME_DESC
      - NEXT_SCHEDULED_START_TIME_ASC
      - END_TIME_DESC
      title: FlowRunSort
      type: string
    FlowRunUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a flow run.
      properties:
        empirical_policy:
          $ref: '#/components/schemas/FlowRunPolicy'
        flow_version:
          anyOf:
          - type: string
          - type: 'null'
          title: Flow Version
        infrastructure_pid:
          anyOf:
          - type: string
          - type: 'null'
          title: Infrastructure Pid
        job_variables:
          anyOf:
          - type: object
          - type: 'null'
          title: Job Variables
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
        parameters:
          title: Parameters
          type: object
        tags:
          items:
            type: string
          title: Tags
          type: array
      title: FlowRunUpdate
      type: object
    FlowSort:
      description: Defines flow sorting options.
      enum:
      - CREATED_DESC
      - UPDATED_DESC
      - NAME_ASC
      - NAME_DESC
      title: FlowSort
      type: string
    FlowUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a flow.
      properties:
        tags:
          description: A list of flow tags
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
      title: FlowUpdate
      type: object
    GlobalConcurrencyLimitResponse:
      description: A response object for global concurrency limits.
      properties:
        active:
          default: true
          description: Whether the global concurrency limit is active.
          title: Active
          type: boolean
        active_slots:
          description: The number of active slots.
          title: Active Slots
          type: integer
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        id:
          format: uuid
          title: Id
          type: string
        limit:
          description: The concurrency limit.
          title: Limit
          type: integer
        name:
          description: The name of the global concurrency limit.
          title: Name
          type: string
        slot_decay_per_second:
          default: 2.0
          description: The decay rate for active slots when used as a rate limit.
          title: Slot Decay Per Second
          type: number
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - limit
      - active_slots
      - id
      - created
      - updated
      title: GlobalConcurrencyLimitResponse
      type: object
    Graph:
      properties:
        artifacts:
          items:
            $ref: '#/components/schemas/GraphArtifact'
          title: Artifacts
          type: array
        end_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: End Time
        nodes:
          items:
            maxItems: 2
            minItems: 2
            prefixItems:
            - format: uuid
              type: string
            - $ref: '#/components/schemas/Node'
            type: array
          title: Nodes
          type: array
        root_node_ids:
          items:
            format: uuid
            type: string
          title: Root Node Ids
          type: array
        start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Start Time
        states:
          items:
            $ref: '#/components/schemas/GraphState'
          title: States
          type: array
      required:
      - start_time
      - end_time
      - root_node_ids
      - nodes
      - artifacts
      - states
      title: Graph
      type: object
    GraphArtifact:
      properties:
        created:
          format: date-time
          title: Created
          type: string
        data:
          anyOf:
          - {}
          - type: 'null'
          title: Data
        id:
          format: uuid
          title: Id
          type: string
        is_latest:
          title: Is Latest
          type: boolean
        key:
          anyOf:
          - type: string
          - type: 'null'
          title: Key
        type:
          anyOf:
          - type: string
          - type: 'null'
          title: Type
      required:
      - id
      - created
      - key
      - type
      - is_latest
      - data
      title: GraphArtifact
      type: object
    GraphState:
      properties:
        id:
          format: uuid
          title: Id
          type: string
        name:
          title: Name
          type: string
        timestamp:
          format: date-time
          title: Timestamp
          type: string
        type:
          $ref: '#/components/schemas/StateType'
      required:
      - id
      - timestamp
      - type
      - name
      title: GraphState
      type: object
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          title: Detail
          type: array
      title: HTTPValidationError
      type: object
    HistoryResponse:
      description: Represents a history of aggregation states over an interval
      properties:
        interval_end:
          description: The end date of the interval.
          format: date-time
          title: Interval End
          type: string
        interval_start:
          description: The start date of the interval.
          format: date-time
          title: Interval Start
          type: string
        states:
          description: A list of state histories during the interval.
          items:
            $ref: '#/components/schemas/HistoryResponseState'
          title: States
          type: array
      required:
      - interval_start
      - interval_end
      - states
      title: HistoryResponse
      type: object
    HistoryResponseState:
      description: Represents a single state's history over an interval.
      properties:
        count_runs:
          description: The number of runs in the specified state during the interval.
          title: Count Runs
          type: integer
        state_name:
          description: The state name.
          title: State Name
          type: string
        state_type:
          $ref: '#/components/schemas/StateType'
          description: The state type.
        sum_estimated_lateness:
          description: The sum of differences between actual and expected start time
            during the interval.
          title: Sum Estimated Lateness
          type: number
        sum_estimated_run_time:
          description: The total estimated run time of all runs during the interval.
          title: Sum Estimated Run Time
          type: number
      required:
      - state_type
      - state_name
      - count_runs
      - sum_estimated_run_time
      - sum_estimated_lateness
      title: HistoryResponseState
      type: object
    IntervalSchedule:
      additionalProperties: false
      description: "A schedule formed by adding `interval` increments to an `anchor_date`.\
        \ If no\n`anchor_date` is supplied, the current UTC time is used.  If a\n\
        timezone-naive datetime is provided for `anchor_date`, it is assumed to be\n\
        in the schedule's timezone (or UTC). Even if supplied with an IANA timezone,\n\
        anchor dates are always stored as UTC offsets, so a `timezone` can be\nprovided\
        \ to determine localization behaviors like DST boundary handling. If\nnone\
        \ is provided it will be inferred from the anchor date.\n\nNOTE: If the `IntervalSchedule`\
        \ `anchor_date` or `timezone` is provided in a\nDST-observing timezone, then\
        \ the schedule will adjust itself appropriately.\nIntervals greater than 24\
        \ hours will follow DST conventions, while intervals\nof less than 24 hours\
        \ will follow UTC intervals. For example, an hourly\nschedule will fire every\
        \ UTC hour, even across DST boundaries. When clocks\nare set back, this will\
        \ result in two runs that *appear* to both be\nscheduled for 1am local time,\
        \ even though they are an hour apart in UTC\ntime. For longer intervals, like\
        \ a daily schedule, the interval schedule\nwill adjust for DST boundaries\
        \ so that the clock-hour remains constant. This\nmeans that a daily schedule\
        \ that always fires at 9am will observe DST and\ncontinue to fire at 9am in\
        \ the local time zone.\n\nArgs:\n    interval (datetime.timedelta): an interval\
        \ to schedule on.\n    anchor_date (DateTime, optional): an anchor date to\
        \ schedule increments against;\n        if not provided, the current timestamp\
        \ will be used.\n    timezone (str, optional): a valid timezone string."
      properties:
        anchor_date:
          examples:
          - '2020-01-01T00:00:00Z'
          format: date-time
          title: Anchor Date
          type: string
        interval:
          title: Interval
          type: number
        timezone:
          anyOf:
          - type: string
          - type: 'null'
          examples:
          - America/New_York
          title: Timezone
      required:
      - interval
      title: IntervalSchedule
      type: object
    Log:
      description: An ORM representation of log data.
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The flow run ID associated with the log.
          title: Flow Run Id
        id:
          format: uuid
          title: Id
          type: string
        level:
          description: The log level.
          title: Level
          type: integer
        message:
          description: The log message.
          title: Message
          type: string
        name:
          description: The logger name.
          title: Name
          type: string
        task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The task run ID associated with the log.
          title: Task Run Id
        timestamp:
          description: The log timestamp.
          format: date-time
          title: Timestamp
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - level
      - message
      - timestamp
      - id
      - created
      - updated
      title: Log
      type: object
    LogCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a log.
      properties:
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Flow Run Id
        level:
          description: The log level.
          title: Level
          type: integer
        message:
          description: The log message.
          title: Message
          type: string
        name:
          description: The logger name.
          title: Name
          type: string
        task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Task Run Id
        timestamp:
          description: The log timestamp.
          format: date-time
          title: Timestamp
          type: string
      required:
      - name
      - level
      - message
      - timestamp
      title: LogCreate
      type: object
    LogFilter:
      additionalProperties: false
      description: Filter logs. Only logs matching all criteria will be returned
      properties:
        flow_run_id:
          anyOf:
          - $ref: '#/components/schemas/LogFilterFlowRunId'
          - type: 'null'
          description: Filter criteria for `Log.flow_run_id`
        level:
          anyOf:
          - $ref: '#/components/schemas/LogFilterLevel'
          - type: 'null'
          description: Filter criteria for `Log.level`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        task_run_id:
          anyOf:
          - $ref: '#/components/schemas/LogFilterTaskRunId'
          - type: 'null'
          description: Filter criteria for `Log.task_run_id`
        timestamp:
          anyOf:
          - $ref: '#/components/schemas/LogFilterTimestamp'
          - type: 'null'
          description: Filter criteria for `Log.timestamp`
      title: LogFilter
      type: object
    LogFilterFlowRunId:
      additionalProperties: false
      description: Filter by `Log.flow_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of flow run IDs to include
          title: Any
      title: LogFilterFlowRunId
      type: object
    LogFilterLevel:
      additionalProperties: false
      description: Filter by `Log.level`.
      properties:
        ge_:
          anyOf:
          - type: integer
          - type: 'null'
          description: Include logs with a level greater than or equal to this level
          examples:
          - 20
          title: Ge
        le_:
          anyOf:
          - type: integer
          - type: 'null'
          description: Include logs with a level less than or equal to this level
          examples:
          - 50
          title: Le
      title: LogFilterLevel
      type: object
    LogFilterTaskRunId:
      additionalProperties: false
      description: Filter by `Log.task_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of task run IDs to include
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include logs without a task run id
          title: Is Null
      title: LogFilterTaskRunId
      type: object
    LogFilterTimestamp:
      additionalProperties: false
      description: Filter by `Log.timestamp`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include logs with a timestamp at or after this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include logs with a timestamp at or before this time
          title: Before
      title: LogFilterTimestamp
      type: object
    LogSort:
      description: Defines log sorting options.
      enum:
      - TIMESTAMP_ASC
      - TIMESTAMP_DESC
      title: LogSort
      type: string
    MinimalConcurrencyLimitResponse:
      properties:
        id:
          format: uuid
          title: Id
          type: string
        limit:
          title: Limit
          type: integer
        name:
          title: Name
          type: string
      required:
      - id
      - name
      - limit
      title: MinimalConcurrencyLimitResponse
      type: object
    Node:
      properties:
        artifacts:
          items:
            $ref: '#/components/schemas/GraphArtifact'
          title: Artifacts
          type: array
        children:
          items:
            $ref: '#/components/schemas/Edge'
          title: Children
          type: array
        encapsulating:
          items:
            $ref: '#/components/schemas/Edge'
          title: Encapsulating
          type: array
        end_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: End Time
        id:
          format: uuid
          title: Id
          type: string
        kind:
          enum:
          - flow-run
          - task-run
          title: Kind
          type: string
        label:
          title: Label
          type: string
        parents:
          items:
            $ref: '#/components/schemas/Edge'
          title: Parents
          type: array
        start_time:
          format: date-time
          title: Start Time
          type: string
        state_type:
          $ref: '#/components/schemas/StateType'
      required:
      - kind
      - id
      - label
      - state_type
      - start_time
      - end_time
      - parents
      - children
      - encapsulating
      - artifacts
      title: Node
      type: object
    Operator:
      description: Operators for combining filter criteria.
      enum:
      - and_
      - or_
      title: Operator
      type: string
    OrchestrationResult:
      description: A container for the output of state orchestration.
      properties:
        details:
          anyOf:
          - $ref: '#/components/schemas/StateAcceptDetails'
          - $ref: '#/components/schemas/StateWaitDetails'
          - $ref: '#/components/schemas/StateRejectDetails'
          - $ref: '#/components/schemas/StateAbortDetails'
          title: Details
        state:
          anyOf:
          - $ref: '#/components/schemas/State'
          - type: 'null'
        status:
          $ref: '#/components/schemas/SetStateStatus'
      required:
      - state
      - status
      - details
      title: OrchestrationResult
      type: object
    Parameter:
      description: Represents a parameter input to a task run.
      properties:
        input_type:
          const: parameter
          default: parameter
          title: Input Type
          type: string
        name:
          title: Name
          type: string
      required:
      - name
      title: Parameter
      type: object
    PauseAutomation:
      description: Pauses a Work Queue
      properties:
        automation_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the automation to act on
          title: Automation Id
        source:
          default: selected
          description: Whether this Action applies to a specific selected automation
            (given by `automation_id`), or to an automation that is inferred from
            the triggering event.  If the source is 'inferred', the `automation_id`
            may not be set.  If the source is 'selected', the `automation_id` must
            be set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: pause-automation
          default: pause-automation
          title: Type
          type: string
      title: PauseAutomation
      type: object
    PauseDeployment:
      description: Pauses the given Deployment
      properties:
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the deployment
          title: Deployment Id
        source:
          default: selected
          description: Whether this Action applies to a specific selected deployment
            (given by `deployment_id`), or to a deployment that is inferred from the
            triggering event.  If the source is 'inferred', the `deployment_id` may
            not be set.  If the source is 'selected', the `deployment_id` must be
            set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: pause-deployment
          default: pause-deployment
          title: Type
          type: string
      title: PauseDeployment
      type: object
    PauseWorkPool:
      description: Pauses a Work Pool
      properties:
        source:
          default: selected
          description: Whether this Action applies to a specific selected work pool
            (given by `work_pool_id`), or to a work pool that is inferred from the
            triggering event.  If the source is 'inferred', the `work_pool_id` may
            not be set.  If the source is 'selected', the `work_pool_id` must be set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: pause-work-pool
          default: pause-work-pool
          title: Type
          type: string
        work_pool_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the work pool to pause
          title: Work Pool Id
      title: PauseWorkPool
      type: object
    PauseWorkQueue:
      description: Pauses a Work Queue
      properties:
        source:
          default: selected
          description: Whether this Action applies to a specific selected work queue
            (given by `work_queue_id`), or to a work queue that is inferred from the
            triggering event.  If the source is 'inferred', the `work_queue_id` may
            not be set.  If the source is 'selected', the `work_queue_id` must be
            set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: pause-work-queue
          default: pause-work-queue
          title: Type
          type: string
        work_queue_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the work queue to pause
          title: Work Queue Id
      title: PauseWorkQueue
      type: object
    QueueFilter:
      description: Filter criteria definition for a work queue.
      properties:
        deployment_ids:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: Only include flow runs from these deployments in the work queue.
          title: Deployment Ids
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: Only include flow runs with these tags in the work queue.
          title: Tags
      title: QueueFilter
      type: object
    RRuleSchedule:
      additionalProperties: false
      description: "RRule schedule, based on the iCalendar standard\n([RFC 5545](https://datatracker.ietf.org/doc/html/rfc5545))\
        \ as\nimplemented in `dateutils.rrule`.\n\nRRules are appropriate for any\
        \ kind of calendar-date manipulation, including\nirregular intervals, repetition,\
        \ exclusions, week day or day-of-month\nadjustments, and more.\n\nNote that\
        \ as a calendar-oriented standard, `RRuleSchedules` are sensitive to\nto the\
        \ initial timezone provided. A 9am daily schedule with a daylight saving\n\
        time-aware start date will maintain a local 9am time through DST boundaries;\n\
        a 9am daily schedule with a UTC start date will maintain a 9am UTC time.\n\
        \nArgs:\n    rrule (str): a valid RRule string\n    timezone (str, optional):\
        \ a valid timezone string"
      properties:
        rrule:
          title: Rrule
          type: string
        timezone:
          anyOf:
          - default: UTC
            pattern: Africa/Abidjan|Africa/Accra|Africa/Addis_Ababa|Africa/Algiers|Africa/Asmara|Africa/Asmera|Africa/Bamako|Africa/Bangui|Africa/Banjul|Africa/Bissau|Africa/Blantyre|Africa/Brazzaville|Africa/Bujumbura|Africa/Cairo|Africa/Casablanca|Africa/Ceuta|Africa/Conakry|Africa/Dakar|Africa/Dar_es_Salaam|Africa/Djibouti|Africa/Douala|Africa/El_Aaiun|Africa/Freetown|Africa/Gaborone|Africa/Harare|Africa/Johannesburg|Africa/Juba|Africa/Kampala|Africa/Khartoum|Africa/Kigali|Africa/Kinshasa|Africa/Lagos|Africa/Libreville|Africa/Lome|Africa/Luanda|Africa/Lubumbashi|Africa/Lusaka|Africa/Malabo|Africa/Maputo|Africa/Maseru|Africa/Mbabane|Africa/Mogadishu|Africa/Monrovia|Africa/Nairobi|Africa/Ndjamena|Africa/Niamey|Africa/Nouakchott|Africa/Ouagadougou|Africa/Porto-Novo|Africa/Sao_Tome|Africa/Timbuktu|Africa/Tripoli|Africa/Tunis|Africa/Windhoek|America/Adak|America/Anchorage|America/Anguilla|America/Antigua|America/Araguaina|America/Argentina/Buenos_Aires|America/Argentina/Catamarca|America/Argentina/ComodRivadavia|America/Argentina/Cordoba|America/Argentina/Jujuy|America/Argentina/La_Rioja|America/Argentina/Mendoza|America/Argentina/Rio_Gallegos|America/Argentina/Salta|America/Argentina/San_Juan|America/Argentina/San_Luis|America/Argentina/Tucuman|America/Argentina/Ushuaia|America/Aruba|America/Asuncion|America/Atikokan|America/Atka|America/Bahia|America/Bahia_Banderas|America/Barbados|America/Belem|America/Belize|America/Blanc-Sablon|America/Boa_Vista|America/Bogota|America/Boise|America/Buenos_Aires|America/Cambridge_Bay|America/Campo_Grande|America/Cancun|America/Caracas|America/Catamarca|America/Cayenne|America/Cayman|America/Chicago|America/Chihuahua|America/Ciudad_Juarez|America/Coral_Harbour|America/Cordoba|America/Costa_Rica|America/Creston|America/Cuiaba|America/Curacao|America/Danmarkshavn|America/Dawson|America/Dawson_Creek|America/Denver|America/Detroit|America/Dominica|America/Edmonton|America/Eirunepe|America/El_Salvador|America/Ensenada|America/Fort_Nelson|America/Fort_Wayne|America/Fortaleza|America/Glace_Bay|America/Godthab|America/Goose_Bay|America/Grand_Turk|America/Grenada|America/Guadeloupe|America/Guatemala|America/Guayaquil|America/Guyana|America/Halifax|America/Havana|America/Hermosillo|America/Indiana/Indianapolis|America/Indiana/Knox|America/Indiana/Marengo|America/Indiana/Petersburg|America/Indiana/Tell_City|America/Indiana/Vevay|America/Indiana/Vincennes|America/Indiana/Winamac|America/Indianapolis|America/Inuvik|America/Iqaluit|America/Jamaica|America/Jujuy|America/Juneau|America/Kentucky/Louisville|America/Kentucky/Monticello|America/Knox_IN|America/Kralendijk|America/La_Paz|America/Lima|America/Los_Angeles|America/Louisville|America/Lower_Princes|America/Maceio|America/Managua|America/Manaus|America/Marigot|America/Martinique|America/Matamoros|America/Mazatlan|America/Mendoza|America/Menominee|America/Merida|America/Metlakatla|America/Mexico_City|America/Miquelon|America/Moncton|America/Monterrey|America/Montevideo|America/Montreal|America/Montserrat|America/Nassau|America/New_York|America/Nipigon|America/Nome|America/Noronha|America/North_Dakota/Beulah|America/North_Dakota/Center|America/North_Dakota/New_Salem|America/Nuuk|America/Ojinaga|America/Panama|America/Pangnirtung|America/Paramaribo|America/Phoenix|America/Port-au-Prince|America/Port_of_Spain|America/Porto_Acre|America/Porto_Velho|America/Puerto_Rico|America/Punta_Arenas|America/Rainy_River|America/Rankin_Inlet|America/Recife|America/Regina|America/Resolute|America/Rio_Branco|America/Rosario|America/Santa_Isabel|America/Santarem|America/Santiago|America/Santo_Domingo|America/Sao_Paulo|America/Scoresbysund|America/Shiprock|America/Sitka|America/St_Barthelemy|America/St_Johns|America/St_Kitts|America/St_Lucia|America/St_Thomas|America/St_Vincent|America/Swift_Current|America/Tegucigalpa|America/Thule|America/Thunder_Bay|America/Tijuana|America/Toronto|America/Tortola|America/Vancouver|America/Virgin|America/Whitehorse|America/Winnipeg|America/Yakutat|America/Yellowknife|Antarctica/Casey|Antarctica/Davis|Antarctica/DumontDUrville|Antarctica/Macquarie|Antarctica/Mawson|Antarctica/McMurdo|Antarctica/Palmer|Antarctica/Rothera|Antarctica/South_Pole|Antarctica/Syowa|Antarctica/Troll|Antarctica/Vostok|Arctic/Longyearbyen|Asia/Aden|Asia/Almaty|Asia/Amman|Asia/Anadyr|Asia/Aqtau|Asia/Aqtobe|Asia/Ashgabat|Asia/Ashkhabad|Asia/Atyrau|Asia/Baghdad|Asia/Bahrain|Asia/Baku|Asia/Bangkok|Asia/Barnaul|Asia/Beirut|Asia/Bishkek|Asia/Brunei|Asia/Calcutta|Asia/Chita|Asia/Choibalsan|Asia/Chongqing|Asia/Chungking|Asia/Colombo|Asia/Dacca|Asia/Damascus|Asia/Dhaka|Asia/Dili|Asia/Dubai|Asia/Dushanbe|Asia/Famagusta|Asia/Gaza|Asia/Harbin|Asia/Hebron|Asia/Ho_Chi_Minh|Asia/Hong_Kong|Asia/Hovd|Asia/Irkutsk|Asia/Istanbul|Asia/Jakarta|Asia/Jayapura|Asia/Jerusalem|Asia/Kabul|Asia/Kamchatka|Asia/Karachi|Asia/Kashgar|Asia/Kathmandu|Asia/Katmandu|Asia/Khandyga|Asia/Kolkata|Asia/Krasnoyarsk|Asia/Kuala_Lumpur|Asia/Kuching|Asia/Kuwait|Asia/Macao|Asia/Macau|Asia/Magadan|Asia/Makassar|Asia/Manila|Asia/Muscat|Asia/Nicosia|Asia/Novokuznetsk|Asia/Novosibirsk|Asia/Omsk|Asia/Oral|Asia/Phnom_Penh|Asia/Pontianak|Asia/Pyongyang|Asia/Qatar|Asia/Qostanay|Asia/Qyzylorda|Asia/Rangoon|Asia/Riyadh|Asia/Saigon|Asia/Sakhalin|Asia/Samarkand|Asia/Seoul|Asia/Shanghai|Asia/Singapore|Asia/Srednekolymsk|Asia/Taipei|Asia/Tashkent|Asia/Tbilisi|Asia/Tehran|Asia/Tel_Aviv|Asia/Thimbu|Asia/Thimphu|Asia/Tokyo|Asia/Tomsk|Asia/Ujung_Pandang|Asia/Ulaanbaatar|Asia/Ulan_Bator|Asia/Urumqi|Asia/Ust-Nera|Asia/Vientiane|Asia/Vladivostok|Asia/Yakutsk|Asia/Yangon|Asia/Yekaterinburg|Asia/Yerevan|Atlantic/Azores|Atlantic/Bermuda|Atlantic/Canary|Atlantic/Cape_Verde|Atlantic/Faeroe|Atlantic/Faroe|Atlantic/Jan_Mayen|Atlantic/Madeira|Atlantic/Reykjavik|Atlantic/South_Georgia|Atlantic/St_Helena|Atlantic/Stanley|Australia/ACT|Australia/Adelaide|Australia/Brisbane|Australia/Broken_Hill|Australia/Canberra|Australia/Currie|Australia/Darwin|Australia/Eucla|Australia/Hobart|Australia/LHI|Australia/Lindeman|Australia/Lord_Howe|Australia/Melbourne|Australia/NSW|Australia/North|Australia/Perth|Australia/Queensland|Australia/South|Australia/Sydney|Australia/Tasmania|Australia/Victoria|Australia/West|Australia/Yancowinna|Brazil/Acre|Brazil/DeNoronha|Brazil/East|Brazil/West|CET|CST6CDT|Canada/Atlantic|Canada/Central|Canada/Eastern|Canada/Mountain|Canada/Newfoundland|Canada/Pacific|Canada/Saskatchewan|Canada/Yukon|Chile/Continental|Chile/EasterIsland|Cuba|EET|EST|EST5EDT|Egypt|Eire|Etc/GMT|Etc/GMT+0|Etc/GMT+1|Etc/GMT+10|Etc/GMT+11|Etc/GMT+12|Etc/GMT+2|Etc/GMT+3|Etc/GMT+4|Etc/GMT+5|Etc/GMT+6|Etc/GMT+7|Etc/GMT+8|Etc/GMT+9|Etc/GMT-0|Etc/GMT-1|Etc/GMT-10|Etc/GMT-11|Etc/GMT-12|Etc/GMT-13|Etc/GMT-14|Etc/GMT-2|Etc/GMT-3|Etc/GMT-4|Etc/GMT-5|Etc/GMT-6|Etc/GMT-7|Etc/GMT-8|Etc/GMT-9|Etc/GMT0|Etc/Greenwich|Etc/UCT|Etc/UTC|Etc/Universal|Etc/Zulu|Europe/Amsterdam|Europe/Andorra|Europe/Astrakhan|Europe/Athens|Europe/Belfast|Europe/Belgrade|Europe/Berlin|Europe/Bratislava|Europe/Brussels|Europe/Bucharest|Europe/Budapest|Europe/Busingen|Europe/Chisinau|Europe/Copenhagen|Europe/Dublin|Europe/Gibraltar|Europe/Guernsey|Europe/Helsinki|Europe/Isle_of_Man|Europe/Istanbul|Europe/Jersey|Europe/Kaliningrad|Europe/Kiev|Europe/Kirov|Europe/Kyiv|Europe/Lisbon|Europe/Ljubljana|Europe/London|Europe/Luxembourg|Europe/Madrid|Europe/Malta|Europe/Mariehamn|Europe/Minsk|Europe/Monaco|Europe/Moscow|Europe/Nicosia|Europe/Oslo|Europe/Paris|Europe/Podgorica|Europe/Prague|Europe/Riga|Europe/Rome|Europe/Samara|Europe/San_Marino|Europe/Sarajevo|Europe/Saratov|Europe/Simferopol|Europe/Skopje|Europe/Sofia|Europe/Stockholm|Europe/Tallinn|Europe/Tirane|Europe/Tiraspol|Europe/Ulyanovsk|Europe/Uzhgorod|Europe/Vaduz|Europe/Vatican|Europe/Vienna|Europe/Vilnius|Europe/Volgograd|Europe/Warsaw|Europe/Zagreb|Europe/Zaporozhye|Europe/Zurich|Factory|GB|GB-Eire|GMT|GMT+0|GMT-0|GMT0|Greenwich|HST|Hongkong|Iceland|Indian/Antananarivo|Indian/Chagos|Indian/Christmas|Indian/Cocos|Indian/Comoro|Indian/Kerguelen|Indian/Mahe|Indian/Maldives|Indian/Mauritius|Indian/Mayotte|Indian/Reunion|Iran|Israel|Jamaica|Japan|Kwajalein|Libya|MET|MST|MST7MDT|Mexico/BajaNorte|Mexico/BajaSur|Mexico/General|NZ|NZ-CHAT|Navajo|PRC|PST8PDT|Pacific/Apia|Pacific/Auckland|Pacific/Bougainville|Pacific/Chatham|Pacific/Chuuk|Pacific/Easter|Pacific/Efate|Pacific/Enderbury|Pacific/Fakaofo|Pacific/Fiji|Pacific/Funafuti|Pacific/Galapagos|Pacific/Gambier|Pacific/Guadalcanal|Pacific/Guam|Pacific/Honolulu|Pacific/Johnston|Pacific/Kanton|Pacific/Kiritimati|Pacific/Kosrae|Pacific/Kwajalein|Pacific/Majuro|Pacific/Marquesas|Pacific/Midway|Pacific/Nauru|Pacific/Niue|Pacific/Norfolk|Pacific/Noumea|Pacific/Pago_Pago|Pacific/Palau|Pacific/Pitcairn|Pacific/Pohnpei|Pacific/Ponape|Pacific/Port_Moresby|Pacific/Rarotonga|Pacific/Saipan|Pacific/Samoa|Pacific/Tahiti|Pacific/Tarawa|Pacific/Tongatapu|Pacific/Truk|Pacific/Wake|Pacific/Wallis|Pacific/Yap|Poland|Portugal|ROC|ROK|Singapore|Turkey|UCT|US/Alaska|US/Aleutian|US/Arizona|US/Central|US/East-Indiana|US/Eastern|US/Hawaii|US/Indiana-Starke|US/Michigan|US/Mountain|US/Pacific|US/Samoa|UTC|Universal|W-SU|WET|Zulu
            type: string
          - type: 'null'
          default: UTC
          examples:
          - America/New_York
          title: Timezone
      required:
      - rrule
      title: RRuleSchedule
      type: object
    ReceivedEvent:
      description: 'The server-side view of an event that has happened to a Resource
        after it has

        been received by the server'
      properties:
        event:
          description: The name of the event that happened
          title: Event
          type: string
        follows:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The ID of an event that is known to have occurred prior to
            this one. If set, this may be used to establish a more precise ordering
            of causally-related events when they occur close enough together in time
            that the system may receive them out-of-order.
          title: Follows
        id:
          description: The client-provided identifier of this event
          format: uuid
          title: Id
          type: string
        occurred:
          description: When the event happened from the sender's perspective
          format: date-time
          title: Occurred
          type: string
        payload:
          description: An open-ended set of data describing what happened
          title: Payload
          type: object
        received:
          description: When the event was received by Prefect Cloud
          format: date-time
          title: Received
          type: string
        related:
          description: A list of additional Resources involved in this event
          items:
            $ref: '#/components/schemas/RelatedResource'
          title: Related
          type: array
        resource:
          $ref: '#/components/schemas/Resource'
          description: The primary Resource this event concerns
      required:
      - occurred
      - event
      - resource
      - id
      title: ReceivedEvent
      type: object
    RelatedResource:
      additionalProperties:
        type: string
      description: A Resource with a specific role in an Event
      title: RelatedResource
      type: object
    Resource:
      additionalProperties:
        type: string
      description: An observable business object of interest to the user
      title: Resource
      type: object
    ResourceSpecification:
      additionalProperties:
        anyOf:
        - type: string
        - items:
            type: string
          type: array
      title: ResourceSpecification
      type: object
    ResumeAutomation:
      description: Resumes a Work Queue
      properties:
        automation_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the automation to act on
          title: Automation Id
        source:
          default: selected
          description: Whether this Action applies to a specific selected automation
            (given by `automation_id`), or to an automation that is inferred from
            the triggering event.  If the source is 'inferred', the `automation_id`
            may not be set.  If the source is 'selected', the `automation_id` must
            be set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: resume-automation
          default: resume-automation
          title: Type
          type: string
      title: ResumeAutomation
      type: object
    ResumeDeployment:
      description: Resumes the given Deployment
      properties:
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the deployment
          title: Deployment Id
        source:
          default: selected
          description: Whether this Action applies to a specific selected deployment
            (given by `deployment_id`), or to a deployment that is inferred from the
            triggering event.  If the source is 'inferred', the `deployment_id` may
            not be set.  If the source is 'selected', the `deployment_id` must be
            set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: resume-deployment
          default: resume-deployment
          title: Type
          type: string
      title: ResumeDeployment
      type: object
    ResumeFlowRun:
      description: Resumes a paused or suspended flow run associated with the trigger
      properties:
        type:
          const: resume-flow-run
          default: resume-flow-run
          title: Type
          type: string
      title: ResumeFlowRun
      type: object
    ResumeWorkPool:
      description: Resumes a Work Pool
      properties:
        source:
          default: selected
          description: Whether this Action applies to a specific selected work pool
            (given by `work_pool_id`), or to a work pool that is inferred from the
            triggering event.  If the source is 'inferred', the `work_pool_id` may
            not be set.  If the source is 'selected', the `work_pool_id` must be set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: resume-work-pool
          default: resume-work-pool
          title: Type
          type: string
        work_pool_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the work pool to pause
          title: Work Pool Id
      title: ResumeWorkPool
      type: object
    ResumeWorkQueue:
      description: Resumes a Work Queue
      properties:
        source:
          default: selected
          description: Whether this Action applies to a specific selected work queue
            (given by `work_queue_id`), or to a work queue that is inferred from the
            triggering event.  If the source is 'inferred', the `work_queue_id` may
            not be set.  If the source is 'selected', the `work_queue_id` must be
            set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: resume-work-queue
          default: resume-work-queue
          title: Type
          type: string
        work_queue_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the work queue to pause
          title: Work Queue Id
      title: ResumeWorkQueue
      type: object
    RunDeployment:
      description: Runs the given deployment with the given parameters
      properties:
        deployment_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The identifier of the deployment
          title: Deployment Id
        job_variables:
          anyOf:
          - type: object
          - type: 'null'
          description: The job variables to pass to the created flow run, or None
            to use the deployment's default job variables
          title: Job Variables
        parameters:
          anyOf:
          - type: object
          - type: 'null'
          description: The parameters to pass to the deployment, or None to use the
            deployment's default parameters
          title: Parameters
        source:
          default: selected
          description: Whether this Action applies to a specific selected deployment
            (given by `deployment_id`), or to a deployment that is inferred from the
            triggering event.  If the source is 'inferred', the `deployment_id` may
            not be set.  If the source is 'selected', the `deployment_id` must be
            set.
          enum:
          - selected
          - inferred
          title: Source
          type: string
        type:
          const: run-deployment
          default: run-deployment
          title: Type
          type: string
      title: RunDeployment
      type: object
    SavedSearch:
      description: An ORM representation of saved search data. Represents a set of
        filter criteria.
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        filters:
          description: The filter set for the saved search.
          items:
            $ref: '#/components/schemas/SavedSearchFilter'
          title: Filters
          type: array
        id:
          format: uuid
          title: Id
          type: string
        name:
          description: The name of the saved search.
          title: Name
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - id
      - created
      - updated
      title: SavedSearch
      type: object
    SavedSearchCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a saved search.
      properties:
        filters:
          description: The filter set for the saved search.
          items:
            $ref: '#/components/schemas/SavedSearchFilter'
          title: Filters
          type: array
        name:
          description: The name of the saved search.
          title: Name
          type: string
      required:
      - name
      title: SavedSearchCreate
      type: object
    SavedSearchFilter:
      description: A filter for a saved search model. Intended for use by the Prefect
        UI.
      properties:
        object:
          description: The object over which to filter.
          title: Object
          type: string
        operation:
          description: The operator to apply to the object. For example, `equals`.
          title: Operation
          type: string
        property:
          description: The property of the object on which to filter.
          title: Property
          type: string
        type:
          description: The type of the property.
          title: Type
          type: string
        value:
          description: A JSON-compatible value for the filter.
          title: Value
      required:
      - object
      - property
      - type
      - operation
      - value
      title: SavedSearchFilter
      type: object
    SchemaValueIndexError:
      properties:
        errors:
          items:
            anyOf:
            - type: string
            - $ref: '#/components/schemas/SchemaValuePropertyError'
            - $ref: '#/components/schemas/SchemaValueIndexError'
          title: Errors
          type: array
        index:
          title: Index
          type: integer
      required:
      - index
      - errors
      title: SchemaValueIndexError
      type: object
    SchemaValuePropertyError:
      properties:
        errors:
          items:
            anyOf:
            - type: string
            - $ref: '#/components/schemas/SchemaValuePropertyError'
            - $ref: '#/components/schemas/SchemaValueIndexError'
          title: Errors
          type: array
        property:
          title: Property
          type: string
      required:
      - property
      - errors
      title: SchemaValuePropertyError
      type: object
    SchemaValuesValidationResponse:
      properties:
        errors:
          items:
            anyOf:
            - type: string
            - $ref: '#/components/schemas/SchemaValuePropertyError'
            - $ref: '#/components/schemas/SchemaValueIndexError'
          title: Errors
          type: array
        valid:
          title: Valid
          type: boolean
      required:
      - errors
      - valid
      title: SchemaValuesValidationResponse
      type: object
    SendNotification:
      description: Send a notification when an Automation is triggered
      properties:
        block_document_id:
          description: The identifier of the notification block to use
          format: uuid
          title: Block Document Id
          type: string
        body:
          description: The text of the notification to send
          title: Body
          type: string
        subject:
          default: Prefect automated notification
          title: Subject
          type: string
        type:
          const: send-notification
          default: send-notification
          title: Type
          type: string
      required:
      - block_document_id
      - body
      title: SendNotification
      type: object
    SequenceTrigger-Input:
      description: 'A composite trigger that requires some number of triggers to have
        fired

        within the given time period in a specific order'
      properties:
        id:
          description: The unique ID of this trigger
          format: uuid
          title: Id
          type: string
        triggers:
          items:
            anyOf:
            - $ref: '#/components/schemas/EventTrigger'
            - $ref: '#/components/schemas/CompoundTrigger-Input'
            - $ref: '#/components/schemas/SequenceTrigger-Input'
          title: Triggers
          type: array
        type:
          const: sequence
          default: sequence
          title: Type
          type: string
        within:
          anyOf:
          - type: number
          - type: 'null'
          title: Within
      required:
      - triggers
      - within
      title: SequenceTrigger
      type: object
    SequenceTrigger-Output:
      description: 'A composite trigger that requires some number of triggers to have
        fired

        within the given time period in a specific order'
      properties:
        id:
          description: The unique ID of this trigger
          format: uuid
          title: Id
          type: string
        triggers:
          items:
            anyOf:
            - $ref: '#/components/schemas/EventTrigger'
            - $ref: '#/components/schemas/CompoundTrigger-Output'
            - $ref: '#/components/schemas/SequenceTrigger-Output'
          title: Triggers
          type: array
        type:
          const: sequence
          default: sequence
          title: Type
          type: string
        within:
          anyOf:
          - type: number
          - type: 'null'
          title: Within
      required:
      - triggers
      - within
      title: SequenceTrigger
      type: object
    SetStateStatus:
      description: Enumerates return statuses for setting run states.
      enum:
      - ACCEPT
      - REJECT
      - ABORT
      - WAIT
      title: SetStateStatus
      type: string
    Settings: {}
    SimpleFlowRun:
      properties:
        duration:
          description: The total run time of the run.
          title: Duration
          type: number
        id:
          description: The flow run id.
          format: uuid
          title: Id
          type: string
        lateness:
          description: The delay between the expected and actual start time.
          title: Lateness
          type: number
        state_type:
          $ref: '#/components/schemas/StateType'
          description: The state type.
        timestamp:
          description: The start time of the run, or the expected start time if it
            hasn't run yet.
          format: date-time
          title: Timestamp
          type: string
      required:
      - id
      - state_type
      - timestamp
      - duration
      - lateness
      title: SimpleFlowRun
      type: object
    SimpleNextFlowRun:
      properties:
        flow_id:
          description: The flow id.
          format: uuid
          title: Flow Id
          type: string
        id:
          description: The flow run id.
          format: uuid
          title: Id
          type: string
        name:
          description: The flow run name
          title: Name
          type: string
        next_scheduled_start_time:
          description: The next scheduled start time
          format: date-time
          title: Next Scheduled Start Time
          type: string
        state_name:
          description: The state name.
          title: State Name
          type: string
        state_type:
          $ref: '#/components/schemas/StateType'
          description: The state type.
      required:
      - id
      - flow_id
      - name
      - state_name
      - state_type
      - next_scheduled_start_time
      title: SimpleNextFlowRun
      type: object
    State:
      description: Represents the state of a run.
      properties:
        data:
          anyOf:
          - {}
          - type: 'null'
          description: Data associated with the state, e.g. a result. Content must
            be storable as JSON.
          title: Data
        id:
          format: uuid
          title: Id
          type: string
        message:
          anyOf:
          - type: string
          - type: 'null'
          examples:
          - Run started
          title: Message
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
        state_details:
          $ref: '#/components/schemas/StateDetails'
        timestamp:
          format: date-time
          title: Timestamp
          type: string
        type:
          $ref: '#/components/schemas/StateType'
      required:
      - type
      - id
      title: State
      type: object
    StateAbortDetails:
      description: Details associated with an ABORT state transition.
      properties:
        reason:
          anyOf:
          - type: string
          - type: 'null'
          description: The reason why the state transition was aborted.
          title: Reason
        type:
          const: abort_details
          default: abort_details
          description: The type of state transition detail. Used to ensure pydantic
            does not coerce into a different type.
          title: Type
          type: string
      title: StateAbortDetails
      type: object
    StateAcceptDetails:
      description: Details associated with an ACCEPT state transition.
      properties:
        type:
          const: accept_details
          default: accept_details
          description: The type of state transition detail. Used to ensure pydantic
            does not coerce into a different type.
          title: Type
          type: string
      title: StateAcceptDetails
      type: object
    StateCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a new state.
      properties:
        data:
          anyOf:
          - {}
          - type: 'null'
          description: The data of the state to create
          title: Data
        message:
          anyOf:
          - type: string
          - type: 'null'
          description: The message of the state to create
          title: Message
        name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the state to create
          title: Name
        state_details:
          $ref: '#/components/schemas/StateDetails'
          description: The details of the state to create
        type:
          $ref: '#/components/schemas/StateType'
          description: The type of the state to create
      required:
      - type
      title: StateCreate
      type: object
    StateDetails:
      properties:
        cache_expiration:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Cache Expiration
        cache_key:
          anyOf:
          - type: string
          - type: 'null'
          title: Cache Key
        child_flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Child Flow Run Id
        deferred:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          title: Deferred
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Flow Run Id
        pause_key:
          anyOf:
          - type: string
          - type: 'null'
          title: Pause Key
        pause_reschedule:
          default: false
          title: Pause Reschedule
          type: boolean
        pause_timeout:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Pause Timeout
        refresh_cache:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Refresh Cache
        retriable:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Retriable
        run_input_keyset:
          anyOf:
          - additionalProperties:
              type: string
            type: object
          - type: 'null'
          title: Run Input Keyset
        scheduled_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Scheduled Time
        task_parameters_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Task Parameters Id
        task_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Task Run Id
        traceparent:
          anyOf:
          - type: string
          - type: 'null'
          title: Traceparent
        transition_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          title: Transition Id
        untrackable_result:
          default: false
          title: Untrackable Result
          type: boolean
      title: StateDetails
      type: object
    StateRejectDetails:
      description: Details associated with a REJECT state transition.
      properties:
        reason:
          anyOf:
          - type: string
          - type: 'null'
          description: The reason why the state transition was rejected.
          title: Reason
        type:
          const: reject_details
          default: reject_details
          description: The type of state transition detail. Used to ensure pydantic
            does not coerce into a different type.
          title: Type
          type: string
      title: StateRejectDetails
      type: object
    StateType:
      description: Enumeration of state types.
      enum:
      - SCHEDULED
      - PENDING
      - RUNNING
      - COMPLETED
      - FAILED
      - CANCELLED
      - CRASHED
      - PAUSED
      - CANCELLING
      title: StateType
      type: string
    StateWaitDetails:
      description: Details associated with a WAIT state transition.
      properties:
        delay_seconds:
          description: The length of time in seconds the client should wait before
            transitioning states.
          title: Delay Seconds
          type: integer
        reason:
          anyOf:
          - type: string
          - type: 'null'
          description: The reason why the state transition should wait.
          title: Reason
        type:
          const: wait_details
          default: wait_details
          description: The type of state transition detail. Used to ensure pydantic
            does not coerce into a different type.
          title: Type
          type: string
      required:
      - delay_seconds
      title: StateWaitDetails
      type: object
    SuspendFlowRun:
      description: Suspends a flow run associated with the trigger
      properties:
        type:
          const: suspend-flow-run
          default: suspend-flow-run
          title: Type
          type: string
      title: SuspendFlowRun
      type: object
    TaskRun:
      description: An ORM representation of task run data.
      properties:
        cache_expiration:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Specifies when the cached state should expire.
          title: Cache Expiration
        cache_key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional cache key. If a COMPLETED state associated with
            this cache key is found, the cached COMPLETED state will be used instead
            of executing the task run.
          title: Cache Key
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        dynamic_key:
          description: A dynamic key used to differentiate between multiple runs of
            the same task within the same flow run.
          title: Dynamic Key
          type: string
        empirical_policy:
          $ref: '#/components/schemas/TaskRunPolicy'
        end_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The actual end time.
          title: End Time
        estimated_run_time:
          default: 0.0
          description: A real-time estimate of total run time.
          title: Estimated Run Time
          type: number
        estimated_start_time_delta:
          default: 0.0
          description: The difference between actual and expected start time.
          title: Estimated Start Time Delta
          type: number
        expected_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The task run's expected start time.
          title: Expected Start Time
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The flow run id of the task run.
          title: Flow Run Id
        flow_run_run_count:
          default: 0
          description: If the parent flow has retried, this indicates the flow retry
            this run is associated with.
          title: Flow Run Run Count
          type: integer
        id:
          format: uuid
          title: Id
          type: string
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          examples:
          - my-task-run
          title: Name
          type: string
        next_scheduled_start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The next time the task run is scheduled to start.
          title: Next Scheduled Start Time
        run_count:
          default: 0
          description: The number of times the task run has been executed.
          title: Run Count
          type: integer
        start_time:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The actual start time.
          title: Start Time
        state:
          anyOf:
          - $ref: '#/components/schemas/State'
          - type: 'null'
          description: The current task run state.
        state_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the current task run state.
          title: State Id
        state_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the current task run state.
          title: State Name
        state_type:
          anyOf:
          - $ref: '#/components/schemas/StateType'
          - type: 'null'
          description: The type of the current task run state.
        tags:
          description: A list of tags for the task run.
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        task_inputs:
          additionalProperties:
            items:
              anyOf:
              - $ref: '#/components/schemas/TaskRunResult'
              - $ref: '#/components/schemas/Parameter'
              - $ref: '#/components/schemas/Constant'
            type: array
          description: Tracks the source of inputs to a task run. Used for internal
            bookkeeping.
          title: Task Inputs
          type: object
        task_key:
          description: A unique identifier for the task being run.
          title: Task Key
          type: string
        task_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the task being run.
          title: Task Version
        total_run_time:
          default: 0.0
          description: Total run time. If the task run was executed multiple times,
            the time of each run will be summed.
          title: Total Run Time
          type: number
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - task_key
      - dynamic_key
      - id
      - created
      - updated
      title: TaskRun
      type: object
    TaskRunCount:
      additionalProperties:
        type: integer
      type: object
    TaskRunCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a task run
      properties:
        cache_expiration:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Specifies when the cached state should expire.
          title: Cache Expiration
        cache_key:
          anyOf:
          - type: string
          - type: 'null'
          description: An optional cache key. If a COMPLETED state associated with
            this cache key is found, the cached COMPLETED state will be used instead
            of executing the task run.
          title: Cache Key
        dynamic_key:
          description: A dynamic key used to differentiate between multiple runs of
            the same task within the same flow run.
          title: Dynamic Key
          type: string
        empirical_policy:
          $ref: '#/components/schemas/TaskRunPolicy'
        flow_run_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The flow run id of the task run.
          title: Flow Run Id
        id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The ID to assign to the task run. If not provided, a random
            UUID will be generated.
          title: Id
        labels:
          anyOf:
          - additionalProperties:
              anyOf:
              - type: boolean
              - type: integer
              - type: number
              - type: string
            type: object
          - type: 'null'
          description: A dictionary of key-value labels. Values can be strings, numbers,
            or booleans.
          examples:
          - key: value1
            key2: 42
          title: Labels
        name:
          examples:
          - my-task-run
          title: Name
          type: string
        state:
          anyOf:
          - $ref: '#/components/schemas/StateCreate'
          - type: 'null'
          description: The state of the task run to create
        tags:
          description: A list of tags for the task run.
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        task_inputs:
          additionalProperties:
            items:
              anyOf:
              - $ref: '#/components/schemas/TaskRunResult'
              - $ref: '#/components/schemas/Parameter'
              - $ref: '#/components/schemas/Constant'
            type: array
          description: The inputs to the task run.
          title: Task Inputs
          type: object
        task_key:
          description: A unique identifier for the task being run.
          title: Task Key
          type: string
        task_version:
          anyOf:
          - type: string
          - type: 'null'
          description: The version of the task being run.
          title: Task Version
      required:
      - task_key
      - dynamic_key
      title: TaskRunCreate
      type: object
    TaskRunFilter:
      additionalProperties: false
      description: Filter task runs. Only task runs matching all criteria will be
        returned
      properties:
        expected_start_time:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterExpectedStartTime'
          - type: 'null'
          description: Filter criteria for `TaskRun.expected_start_time`
        flow_run_id:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterFlowRunId'
          - type: 'null'
          description: Filter criteria for `TaskRun.flow_run_id`
        id:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterId'
          - type: 'null'
          description: Filter criteria for `TaskRun.id`
        name:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterName'
          - type: 'null'
          description: Filter criteria for `TaskRun.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        start_time:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterStartTime'
          - type: 'null'
          description: Filter criteria for `TaskRun.start_time`
        state:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterState'
          - type: 'null'
          description: Filter criteria for `TaskRun.state`
        subflow_runs:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterSubFlowRuns'
          - type: 'null'
          description: Filter criteria for `TaskRun.subflow_run`
        tags:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterTags'
          - type: 'null'
          description: Filter criteria for `TaskRun.tags`
      title: TaskRunFilter
      type: object
    TaskRunFilterExpectedStartTime:
      additionalProperties: false
      description: Filter by `TaskRun.expected_start_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include task runs expected to start at or after this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include task runs expected to start at or before this
            time
          title: Before
      title: TaskRunFilterExpectedStartTime
      type: object
    TaskRunFilterFlowRunId:
      additionalProperties: false
      description: Filter by `TaskRun.flow_run_id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of task run flow run ids to include
          title: Any
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Filter for task runs with None as their flow run id
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: TaskRunFilterFlowRunId
      type: object
    TaskRunFilterId:
      additionalProperties: false
      description: Filter by `TaskRun.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of task run ids to include
          title: Any
      title: TaskRunFilterId
      type: object
    TaskRunFilterName:
      additionalProperties: false
      description: Filter by `TaskRun.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of task run names to include
          examples:
          - - my-task-run-1
            - my-task-run-2
          title: Any
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A case-insensitive partial match. For example,  passing 'marvin'
            will match 'marvin', 'sad-Marvin', and 'marvin-robot'.
          examples:
          - marvin
          title: Like
      title: TaskRunFilterName
      type: object
    TaskRunFilterStartTime:
      additionalProperties: false
      description: Filter by `TaskRun.start_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include task runs starting at or after this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include task runs starting at or before this time
          title: Before
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only return task runs without a start time
          title: Is Null
      title: TaskRunFilterStartTime
      type: object
    TaskRunFilterState:
      additionalProperties: false
      description: Filter by `TaskRun.type` and `TaskRun.name`.
      properties:
        name:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterStateName'
          - type: 'null'
          description: Filter criteria for `TaskRun.state_name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        type:
          anyOf:
          - $ref: '#/components/schemas/TaskRunFilterStateType'
          - type: 'null'
          description: Filter criteria for `TaskRun.state_type`
      title: TaskRunFilterState
      type: object
    TaskRunFilterStateName:
      additionalProperties: false
      description: Filter by `TaskRun.state_name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of task run state names to include
          title: Any
      title: TaskRunFilterStateName
      type: object
    TaskRunFilterStateType:
      additionalProperties: false
      description: Filter by `TaskRun.state_type`.
      properties:
        any_:
          anyOf:
          - items:
              $ref: '#/components/schemas/StateType'
            type: array
          - type: 'null'
          description: A list of task run state types to include
          title: Any
      title: TaskRunFilterStateType
      type: object
    TaskRunFilterSubFlowRuns:
      additionalProperties: false
      description: Filter by `TaskRun.subflow_run`.
      properties:
        exists_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include task runs that are subflow run parents;
            if false, exclude parent task runs
          title: Exists
      title: TaskRunFilterSubFlowRuns
      type: object
    TaskRunFilterTags:
      additionalProperties: false
      description: Filter by `TaskRun.tags`.
      properties:
        all_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags. Task runs will be returned only if their tags
            are a superset of the list
          examples:
          - - tag-1
            - tag-2
          title: All
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include task runs without tags
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: TaskRunFilterTags
      type: object
    TaskRunPolicy:
      description: Defines of how a task run should retry.
      properties:
        max_retries:
          default: 0
          deprecated: true
          description: The maximum number of retries. Field is not used. Please use
            `retries` instead.
          title: Max Retries
          type: integer
        retries:
          anyOf:
          - type: integer
          - type: 'null'
          description: The number of retries.
          title: Retries
        retry_delay:
          anyOf:
          - type: integer
          - items:
              type: integer
            type: array
          - type: 'null'
          description: A delay time or list of delay times between retries, in seconds.
          title: Retry Delay
        retry_delay_seconds:
          default: 0
          deprecated: true
          description: The delay between retries. Field is not used. Please use `retry_delay`
            instead.
          title: Retry Delay Seconds
          type: number
        retry_jitter_factor:
          anyOf:
          - type: number
          - type: 'null'
          description: Determines the amount a retry should jitter
          title: Retry Jitter Factor
      title: TaskRunPolicy
      type: object
    TaskRunResult:
      description: Represents a task run result input to another task run.
      properties:
        id:
          format: uuid
          title: Id
          type: string
        input_type:
          const: task_run
          default: task_run
          title: Input Type
          type: string
      required:
      - id
      title: TaskRunResult
      type: object
    TaskRunSort:
      description: Defines task run sorting options.
      enum:
      - ID_DESC
      - EXPECTED_START_TIME_ASC
      - EXPECTED_START_TIME_DESC
      - NAME_ASC
      - NAME_DESC
      - NEXT_SCHEDULED_START_TIME_ASC
      - END_TIME_DESC
      title: TaskRunSort
      type: string
    TaskRunUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a task run
      properties:
        name:
          examples:
          - my-task-run
          title: Name
          type: string
      title: TaskRunUpdate
      type: object
    TaskWorkerFilter:
      properties:
        task_keys:
          items:
            type: string
          title: Task Keys
          type: array
      required:
      - task_keys
      title: TaskWorkerFilter
      type: object
    TaskWorkerResponse:
      properties:
        identifier:
          title: Identifier
          type: string
        task_keys:
          items:
            type: string
          title: Task Keys
          type: array
        timestamp:
          format: date-time
          title: Timestamp
          type: string
      required:
      - identifier
      - task_keys
      - timestamp
      title: TaskWorkerResponse
      type: object
    TimeUnit:
      enum:
      - week
      - day
      - hour
      - minute
      - second
      title: TimeUnit
      type: string
    UpdatedBy:
      properties:
        display_value:
          anyOf:
          - type: string
          - type: 'null'
          description: The display value for the updater.
          title: Display Value
        id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the updater of the object.
          title: Id
        type:
          anyOf:
          - type: string
          - type: 'null'
          description: The type of the updater of the object.
          title: Type
      title: UpdatedBy
      type: object
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          title: Location
          type: array
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
      required:
      - loc
      - msg
      - type
      title: ValidationError
      type: object
    Variable:
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        id:
          format: uuid
          title: Id
          type: string
        name:
          description: The name of the variable
          examples:
          - my-variable
          maxLength: 255
          title: Name
          type: string
        tags:
          description: A list of variable tags
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        value:
          anyOf:
          - type: string
          - type: integer
          - type: boolean
          - type: number
          - type: object
          - items: {}
            type: array
          - type: 'null'
          description: The value of the variable
          examples:
          - my-value
          title: Value
      required:
      - name
      - value
      - id
      - created
      - updated
      title: Variable
      type: object
    VariableCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a Variable.
      properties:
        name:
          description: The name of the variable
          examples:
          - my-variable
          maxLength: 255
          title: Name
          type: string
        tags:
          description: A list of variable tags
          examples:
          - - tag-1
            - tag-2
          items:
            type: string
          title: Tags
          type: array
        value:
          anyOf:
          - type: string
          - type: integer
          - type: boolean
          - type: number
          - type: object
          - items: {}
            type: array
          - type: 'null'
          description: The value of the variable
          examples:
          - my-value
          title: Value
      required:
      - name
      - value
      title: VariableCreate
      type: object
    VariableFilter:
      additionalProperties: false
      description: Filter variables. Only variables matching all criteria will be
        returned
      properties:
        id:
          anyOf:
          - $ref: '#/components/schemas/VariableFilterId'
          - type: 'null'
          description: Filter criteria for `Variable.id`
        name:
          anyOf:
          - $ref: '#/components/schemas/VariableFilterName'
          - type: 'null'
          description: Filter criteria for `Variable.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        tags:
          anyOf:
          - $ref: '#/components/schemas/VariableFilterTags'
          - type: 'null'
          description: Filter criteria for `Variable.tags`
      title: VariableFilter
      type: object
    VariableFilterId:
      additionalProperties: false
      description: Filter by `Variable.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of variable ids to include
          title: Any
      title: VariableFilterId
      type: object
    VariableFilterName:
      additionalProperties: false
      description: Filter by `Variable.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of variables names to include
          title: Any
        like_:
          anyOf:
          - type: string
          - type: 'null'
          description: A string to match variable names against. This can include
            SQL wildcard characters like `%` and `_`.
          examples:
          - my_variable_%
          title: Like
      title: VariableFilterName
      type: object
    VariableFilterTags:
      additionalProperties: false
      description: Filter by `Variable.tags`.
      properties:
        all_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of tags. Variables will be returned only if their tags
            are a superset of the list
          examples:
          - - tag-1
            - tag-2
          title: All
        is_null_:
          anyOf:
          - type: boolean
          - type: 'null'
          description: If true, only include Variables without tags
          title: Is Null
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: VariableFilterTags
      type: object
    VariableSort:
      description: Defines variables sorting options.
      enum:
      - CREATED_DESC
      - UPDATED_DESC
      - NAME_DESC
      - NAME_ASC
      title: VariableSort
      type: string
    VariableUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a Variable.
      properties:
        name:
          anyOf:
          - maxLength: 255
            type: string
          - type: 'null'
          description: The name of the variable
          examples:
          - my-variable
          title: Name
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of variable tags
          examples:
          - - tag-1
            - tag-2
          title: Tags
        value:
          anyOf:
          - type: string
          - type: integer
          - type: boolean
          - type: number
          - type: object
          - items: {}
            type: array
          - type: 'null'
          description: The value of the variable
          examples:
          - my-value
          title: Value
      title: VariableUpdate
      type: object
    WorkPool:
      description: An ORM representation of a work pool
      properties:
        base_job_template:
          description: The work pool's base job template.
          title: Base Job Template
          type: object
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          description: A concurrency limit for the work pool.
          title: Concurrency Limit
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        default_queue_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The id of the pool's default queue.
          title: Default Queue Id
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: A description of the work pool.
          title: Description
        id:
          format: uuid
          title: Id
          type: string
        is_paused:
          default: false
          description: Pausing the work pool stops the delivery of all work.
          title: Is Paused
          type: boolean
        name:
          description: The name of the work pool.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        status:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolStatus'
          - type: 'null'
          description: The current status of the work pool.
        type:
          description: The work pool type.
          title: Type
          type: string
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
      required:
      - name
      - type
      - id
      - created
      - updated
      title: WorkPool
      type: object
    WorkPoolCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a work pool.
      properties:
        base_job_template:
          description: The work pool's base job template.
          title: Base Job Template
          type: object
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          description: A concurrency limit for the work pool.
          title: Concurrency Limit
        description:
          anyOf:
          - type: string
          - type: 'null'
          description: The work pool description.
          title: Description
        is_paused:
          default: false
          description: Pausing the work pool stops the delivery of all work.
          title: Is Paused
          type: boolean
        name:
          description: The name of the work pool.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        type:
          default: prefect-agent
          description: The work pool type.
          title: Type
          type: string
      required:
      - name
      title: WorkPoolCreate
      type: object
    WorkPoolFilter:
      additionalProperties: false
      description: Filter work pools. Only work pools matching all criteria will be
        returned
      properties:
        id:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilterId'
          - type: 'null'
          description: Filter criteria for `WorkPool.id`
        name:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilterName'
          - type: 'null'
          description: Filter criteria for `WorkPool.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        type:
          anyOf:
          - $ref: '#/components/schemas/WorkPoolFilterType'
          - type: 'null'
          description: Filter criteria for `WorkPool.type`
      title: WorkPoolFilter
      type: object
    WorkPoolFilterId:
      additionalProperties: false
      description: Filter by `WorkPool.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of work pool ids to include
          title: Any
      title: WorkPoolFilterId
      type: object
    WorkPoolFilterName:
      additionalProperties: false
      description: Filter by `WorkPool.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of work pool names to include
          title: Any
      title: WorkPoolFilterName
      type: object
    WorkPoolFilterType:
      additionalProperties: false
      description: Filter by `WorkPool.type`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of work pool types to include
          title: Any
      title: WorkPoolFilterType
      type: object
    WorkPoolStatus:
      description: Enumeration of work pool statuses.
      enum:
      - READY
      - NOT_READY
      - PAUSED
      title: WorkPoolStatus
      type: string
    WorkPoolUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a work pool.
      properties:
        base_job_template:
          anyOf:
          - type: object
          - type: 'null'
          title: Base Job Template
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          title: Concurrency Limit
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        is_paused:
          anyOf:
          - type: boolean
          - type: 'null'
          title: Is Paused
      title: WorkPoolUpdate
      type: object
    WorkQueue:
      description: An ORM representation of a work queue
      properties:
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          description: An optional concurrency limit for the work queue.
          title: Concurrency Limit
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        description:
          anyOf:
          - type: string
          - type: 'null'
          default: ''
          description: An optional description for the work queue.
          title: Description
        filter:
          anyOf:
          - $ref: '#/components/schemas/QueueFilter'
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: Filter criteria for the work queue.'
        id:
          format: uuid
          title: Id
          type: string
        is_paused:
          default: false
          description: Whether or not the work queue is paused.
          title: Is Paused
          type: boolean
        last_polled:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The last time an agent polled this queue for work.
          title: Last Polled
        name:
          description: The name of the work queue.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        priority:
          default: 1
          description: The queue's priority. Lower values are higher priority (1 is
            the highest).
          exclusiveMinimum: 0.0
          title: Priority
          type: integer
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        work_pool_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The work pool with which the queue is associated.
          title: Work Pool Id
      required:
      - name
      - id
      - created
      - updated
      title: WorkQueue
      type: object
    WorkQueueCreate:
      additionalProperties: false
      description: Data used by the Prefect REST API to create a work queue.
      properties:
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          description: The work queue's concurrency limit.
          title: Concurrency Limit
        description:
          anyOf:
          - type: string
          - type: 'null'
          default: ''
          description: An optional description for the work queue.
          title: Description
        filter:
          anyOf:
          - $ref: '#/components/schemas/QueueFilter'
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: Filter criteria for the work queue.'
        is_paused:
          default: false
          description: Whether or not the work queue is paused.
          title: Is Paused
          type: boolean
        name:
          description: The name of the work queue.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        priority:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The queue's priority. Lower values are higher priority (1 is
            the highest).
          title: Priority
      required:
      - name
      title: WorkQueueCreate
      type: object
    WorkQueueFilter:
      additionalProperties: false
      description: 'Filter work queues. Only work queues matching all criteria will
        be

        returned'
      properties:
        id:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueFilterId'
          - type: 'null'
          description: Filter criteria for `WorkQueue.id`
        name:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueFilterName'
          - type: 'null'
          description: Filter criteria for `WorkQueue.name`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
      title: WorkQueueFilter
      type: object
    WorkQueueFilterId:
      additionalProperties: false
      description: Filter by `WorkQueue.id`.
      properties:
        any_:
          anyOf:
          - items:
              format: uuid
              type: string
            type: array
          - type: 'null'
          description: A list of work queue ids to include
          title: Any
      title: WorkQueueFilterId
      type: object
    WorkQueueFilterName:
      additionalProperties: false
      description: Filter by `WorkQueue.name`.
      properties:
        any_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of work queue names to include
          examples:
          - - wq-1
            - wq-2
          title: Any
        startswith_:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: A list of case-insensitive starts-with matches. For example,  passing
            'marvin' will match 'marvin', and 'Marvin-robot', but not 'sad-marvin'.
          examples:
          - - marvin
            - Marvin-robot
          title: Startswith
      title: WorkQueueFilterName
      type: object
    WorkQueueHealthPolicy:
      properties:
        maximum_late_runs:
          anyOf:
          - type: integer
          - type: 'null'
          default: 0
          description: The maximum number of late runs in the work queue before it
            is deemed unhealthy. Defaults to `0`.
          title: Maximum Late Runs
        maximum_seconds_since_last_polled:
          anyOf:
          - type: integer
          - type: 'null'
          default: 60
          description: The maximum number of time in seconds elapsed since work queue
            has been polled before it is deemed unhealthy. Defaults to `60`.
          title: Maximum Seconds Since Last Polled
      title: WorkQueueHealthPolicy
      type: object
    WorkQueueResponse:
      properties:
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          description: An optional concurrency limit for the work queue.
          title: Concurrency Limit
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        description:
          anyOf:
          - type: string
          - type: 'null'
          default: ''
          description: An optional description for the work queue.
          title: Description
        filter:
          anyOf:
          - $ref: '#/components/schemas/QueueFilter'
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: Filter criteria for the work queue.'
        id:
          format: uuid
          title: Id
          type: string
        is_paused:
          default: false
          description: Whether or not the work queue is paused.
          title: Is Paused
          type: boolean
        last_polled:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The last time an agent polled this queue for work.
          title: Last Polled
        name:
          description: The name of the work queue.
          pattern: ^[^/%&><]+$
          title: Name
          type: string
        priority:
          default: 1
          description: The queue's priority. Lower values are higher priority (1 is
            the highest).
          exclusiveMinimum: 0.0
          title: Priority
          type: integer
        status:
          anyOf:
          - $ref: '#/components/schemas/WorkQueueStatus'
          - type: 'null'
          description: The queue status.
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        work_pool_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: The work pool with which the queue is associated.
          title: Work Pool Id
        work_pool_name:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the work pool the work pool resides within.
          title: Work Pool Name
      required:
      - name
      - id
      - created
      - updated
      title: WorkQueueResponse
      type: object
    WorkQueueStatus:
      description: Enumeration of work queue statuses.
      enum:
      - READY
      - NOT_READY
      - PAUSED
      title: WorkQueueStatus
      type: string
    WorkQueueStatusDetail:
      properties:
        health_check_policy:
          $ref: '#/components/schemas/WorkQueueHealthPolicy'
          description: The policy used to determine whether or not the work queue
            is healthy.
        healthy:
          description: Whether or not the work queue is healthy.
          title: Healthy
          type: boolean
        last_polled:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: The last time an agent polled this queue for work.
          title: Last Polled
        late_runs_count:
          default: 0
          description: The number of late flow runs in the work queue.
          title: Late Runs Count
          type: integer
      required:
      - healthy
      - health_check_policy
      title: WorkQueueStatusDetail
      type: object
    WorkQueueUpdate:
      additionalProperties: false
      description: Data used by the Prefect REST API to update a work queue.
      properties:
        concurrency_limit:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          title: Concurrency Limit
        description:
          anyOf:
          - type: string
          - type: 'null'
          title: Description
        filter:
          anyOf:
          - $ref: '#/components/schemas/QueueFilter'
          - type: 'null'
          deprecated: true
          description: 'DEPRECATED: Filter criteria for the work queue.'
        is_paused:
          default: false
          description: Whether or not the work queue is paused.
          title: Is Paused
          type: boolean
        last_polled:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Last Polled
        name:
          anyOf:
          - type: string
          - type: 'null'
          title: Name
        priority:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          title: Priority
      title: WorkQueueUpdate
      type: object
    WorkerFilter:
      additionalProperties: false
      description: Filter by `Worker.last_heartbeat_time`.
      properties:
        last_heartbeat_time:
          anyOf:
          - $ref: '#/components/schemas/WorkerFilterLastHeartbeatTime'
          - type: 'null'
          description: Filter criteria for `Worker.last_heartbeat_time`
        operator:
          $ref: '#/components/schemas/Operator'
          default: and_
          description: Operator for combining filter criteria. Defaults to 'and_'.
        status:
          anyOf:
          - $ref: '#/components/schemas/WorkerFilterStatus'
          - type: 'null'
          description: Filter criteria for `Worker.status`
      title: WorkerFilter
      type: object
    WorkerFilterLastHeartbeatTime:
      additionalProperties: false
      description: Filter by `Worker.last_heartbeat_time`.
      properties:
        after_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include processes whose last heartbeat was at or after
            this time
          title: After
        before_:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          description: Only include processes whose last heartbeat was at or before
            this time
          title: Before
      title: WorkerFilterLastHeartbeatTime
      type: object
    WorkerFilterStatus:
      additionalProperties: false
      description: Filter by `Worker.status`.
      properties:
        any_:
          anyOf:
          - items:
              $ref: '#/components/schemas/WorkerStatus'
            type: array
          - type: 'null'
          description: A list of worker statuses to include
          title: Any
        not_any_:
          anyOf:
          - items:
              $ref: '#/components/schemas/WorkerStatus'
            type: array
          - type: 'null'
          description: A list of worker statuses to exclude
          title: Not Any
      title: WorkerFilterStatus
      type: object
    WorkerFlowRunResponse:
      properties:
        flow_run:
          $ref: '#/components/schemas/FlowRun'
        work_pool_id:
          format: uuid
          title: Work Pool Id
          type: string
        work_queue_id:
          format: uuid
          title: Work Queue Id
          type: string
      required:
      - work_pool_id
      - work_queue_id
      - flow_run
      title: WorkerFlowRunResponse
      type: object
    WorkerResponse:
      properties:
        created:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Created
        heartbeat_interval_seconds:
          anyOf:
          - type: integer
          - type: 'null'
          description: The number of seconds to expect between heartbeats sent by
            the worker.
          title: Heartbeat Interval Seconds
        id:
          format: uuid
          title: Id
          type: string
        last_heartbeat_time:
          description: The last time the worker process sent a heartbeat.
          format: date-time
          title: Last Heartbeat Time
          type: string
        name:
          description: The name of the worker.
          title: Name
          type: string
        status:
          $ref: '#/components/schemas/WorkerStatus'
          default: OFFLINE
          description: Current status of the worker.
        updated:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Updated
        work_pool_id:
          description: The work pool with which the queue is associated.
          format: uuid
          title: Work Pool Id
          type: string
      required:
      - name
      - work_pool_id
      - id
      - created
      - updated
      title: WorkerResponse
      type: object
    WorkerStatus:
      description: Enumeration of worker statuses.
      enum:
      - ONLINE
      - OFFLINE
      title: WorkerStatus
      type: string
info:
  title: Prefect Prefect REST API
  version: 3.2.9
  x-logo:
    url: static/prefect-logo-mark-gradient.png
openapi: 3.1.0
paths:
  /api/admin/database/clear:
    post:
      description: Clear all database tables without dropping them.
      operationId: clear_database_admin_database_clear_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_clear_database_admin_database_clear_post'
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Clear Database
      tags:
      - Admin
  /api/admin/database/create:
    post:
      description: Create all database objects.
      operationId: create_database_admin_database_create_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_create_database_admin_database_create_post'
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Database
      tags:
      - Admin
  /api/admin/database/drop:
    post:
      description: Drop all database objects.
      operationId: drop_database_admin_database_drop_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_drop_database_admin_database_drop_post'
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Drop Database
      tags:
      - Admin
  /api/admin/settings:
    get:
      description: 'Get the current Prefect REST API settings.


        Secret setting values will be obfuscated.'
      operationId: read_settings_admin_settings_get
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Settings'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Settings
      tags:
      - Admin
  /api/admin/version:
    get:
      description: Returns the Prefect version number
      operationId: read_version_admin_version_get
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Read Version Admin Version Get
                type: string
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Version
      tags:
      - Admin
  /api/artifacts/:
    post:
      description: 'Create an artifact.


        For more information, see https://docs.prefect.io/v3/develop/artifacts.'
      operationId: create_artifact_artifacts__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ArtifactCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Artifact'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Artifact
      tags:
      - Artifacts
  /api/artifacts/count:
    post:
      description: Count artifacts from the database.
      operationId: count_artifacts_artifacts_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_artifacts_artifacts_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Artifacts Artifacts Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Artifacts
      tags:
      - Artifacts
  /api/artifacts/filter:
    post:
      description: Retrieve artifacts from the database.
      operationId: read_artifacts_artifacts_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_artifacts_artifacts_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Artifact'
                title: Response Read Artifacts Artifacts Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Artifacts
      tags:
      - Artifacts
  /api/artifacts/latest/count:
    post:
      description: Count artifacts from the database.
      operationId: count_latest_artifacts_artifacts_latest_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_latest_artifacts_artifacts_latest_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Latest Artifacts Artifacts Latest Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Latest Artifacts
      tags:
      - Artifacts
  /api/artifacts/latest/filter:
    post:
      description: Retrieve artifacts from the database.
      operationId: read_latest_artifacts_artifacts_latest_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_latest_artifacts_artifacts_latest_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ArtifactCollection'
                title: Response Read Latest Artifacts Artifacts Latest Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Latest Artifacts
      tags:
      - Artifacts
  /api/artifacts/{id}:
    delete:
      description: Delete an artifact from the database.
      operationId: delete_artifact_artifacts__id__delete
      parameters:
      - description: The ID of the artifact to delete.
        in: path
        name: id
        required: true
        schema:
          description: The ID of the artifact to delete.
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Artifact
      tags:
      - Artifacts
    get:
      description: Retrieve an artifact from the database.
      operationId: read_artifact_artifacts__id__get
      parameters:
      - description: The ID of the artifact to retrieve.
        in: path
        name: id
        required: true
        schema:
          description: The ID of the artifact to retrieve.
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Artifact'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Artifact
      tags:
      - Artifacts
    patch:
      description: Update an artifact in the database.
      operationId: update_artifact_artifacts__id__patch
      parameters:
      - description: The ID of the artifact to update.
        in: path
        name: id
        required: true
        schema:
          description: The ID of the artifact to update.
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ArtifactUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Artifact
      tags:
      - Artifacts
  /api/artifacts/{key}/latest:
    get:
      description: Retrieve the latest artifact from the artifact table.
      operationId: read_latest_artifact_artifacts__key__latest_get
      parameters:
      - description: The key of the artifact to retrieve.
        in: path
        name: key
        required: true
        schema:
          description: The key of the artifact to retrieve.
          title: Key
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Artifact'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Latest Artifact
      tags:
      - Artifacts
  /api/automations/:
    post:
      description: 'Create an automation.


        For more information, see https://docs.prefect.io/v3/automate.'
      operationId: create_automation_automations__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutomationCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Automation'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Automation
      tags:
      - Automations
  /api/automations/count:
    post:
      operationId: count_automations_automations_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Automations Automations Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Automations
      tags:
      - Automations
  /api/automations/filter:
    post:
      operationId: read_automations_automations_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_automations_automations_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Automation'
                title: Response Read Automations Automations Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Automations
      tags:
      - Automations
  /api/automations/owned-by/{resource_id}:
    delete:
      operationId: delete_automations_owned_by_resource_automations_owned_by__resource_id__delete
      parameters:
      - in: path
        name: resource_id
        required: true
        schema:
          title: Resource Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '202':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Automations Owned By Resource
      tags:
      - Automations
  /api/automations/related-to/{resource_id}:
    get:
      operationId: read_automations_related_to_resource_automations_related_to__resource_id__get
      parameters:
      - in: path
        name: resource_id
        required: true
        schema:
          title: Resource Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Automation'
                title: Response Read Automations Related To Resource Automations Related
                  To  Resource Id  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Automations Related To Resource
      tags:
      - Automations
  /api/automations/{id}:
    delete:
      operationId: delete_automation_automations__id__delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Automation
      tags:
      - Automations
    get:
      operationId: read_automation_automations__id__get
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Automation'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Automation
      tags:
      - Automations
    patch:
      operationId: patch_automation_automations__id__patch
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutomationPartialUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Patch Automation
      tags:
      - Automations
    put:
      operationId: update_automation_automations__id__put
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutomationUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Automation
      tags:
      - Automations
  /api/block_capabilities/:
    get:
      description: 'Get available block capabilities.


        For more information, see https://docs.prefect.io/v3/develop/blocks.'
      operationId: read_available_block_capabilities_block_capabilities__get
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  type: string
                title: Response Read Available Block Capabilities Block Capabilities  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Available Block Capabilities
      tags:
      - Block capabilities
  /api/block_documents/:
    post:
      description: 'Create a new block document.


        For more information, see https://docs.prefect.io/v3/develop/blocks.'
      operationId: create_block_document_block_documents__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockDocumentCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockDocument'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Block Document
      tags:
      - Block documents
  /api/block_documents/count:
    post:
      description: Count block documents.
      operationId: count_block_documents_block_documents_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_block_documents_block_documents_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Block Documents Block Documents Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Block Documents
      tags:
      - Block documents
  /api/block_documents/filter:
    post:
      description: Query for block documents.
      operationId: read_block_documents_block_documents_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_block_documents_block_documents_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/BlockDocument'
                title: Response Read Block Documents Block Documents Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Documents
      tags:
      - Block documents
  /api/block_documents/{id}:
    delete:
      operationId: delete_block_document_block_documents__id__delete
      parameters:
      - description: The block document id
        in: path
        name: id
        required: true
        schema:
          description: The block document id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Block Document
      tags:
      - Block documents
    get:
      operationId: read_block_document_by_id_block_documents__id__get
      parameters:
      - description: The block document id
        in: path
        name: id
        required: true
        schema:
          description: The block document id
          format: uuid
          title: Id
          type: string
      - description: Whether to include sensitive values in the block document.
        in: query
        name: include_secrets
        required: false
        schema:
          default: false
          description: Whether to include sensitive values in the block document.
          title: Include Secrets
          type: boolean
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockDocument'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Document By Id
      tags:
      - Block documents
    patch:
      operationId: update_block_document_data_block_documents__id__patch
      parameters:
      - description: The block document id
        in: path
        name: id
        required: true
        schema:
          description: The block document id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockDocumentUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Block Document Data
      tags:
      - Block documents
  /api/block_schemas/:
    post:
      description: 'Create a block schema.


        For more information, see https://docs.prefect.io/v3/develop/blocks.'
      operationId: create_block_schema_block_schemas__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockSchemaCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockSchema'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Block Schema
      tags:
      - Block schemas
  /api/block_schemas/checksum/{checksum}:
    get:
      operationId: read_block_schema_by_checksum_block_schemas_checksum__checksum__get
      parameters:
      - description: The block schema checksum
        in: path
        name: checksum
        required: true
        schema:
          description: The block schema checksum
          title: Checksum
          type: string
      - description: Version of block schema. If not provided the most recently created
          block schema with the matching checksum will be returned.
        in: query
        name: version
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: Version of block schema. If not provided the most recently
            created block schema with the matching checksum will be returned.
          title: Version
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockSchema'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Schema By Checksum
      tags:
      - Block schemas
  /api/block_schemas/filter:
    post:
      description: Read all block schemas, optionally filtered by type
      operationId: read_block_schemas_block_schemas_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_block_schemas_block_schemas_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/BlockSchema'
                title: Response Read Block Schemas Block Schemas Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Schemas
      tags:
      - Block schemas
  /api/block_schemas/{id}:
    delete:
      description: Delete a block schema by id.
      operationId: delete_block_schema_block_schemas__id__delete
      parameters:
      - description: The block schema id
        in: path
        name: id
        required: true
        schema:
          description: The block schema id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Block Schema
      tags:
      - Block schemas
    get:
      description: Get a block schema by id.
      operationId: read_block_schema_by_id_block_schemas__id__get
      parameters:
      - description: The block schema id
        in: path
        name: id
        required: true
        schema:
          description: The block schema id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockSchema'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Schema By Id
      tags:
      - Block schemas
  /api/block_types/:
    post:
      description: 'Create a new block type.


        For more information, see https://docs.prefect.io/v3/develop/blocks.'
      operationId: create_block_type_block_types__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockTypeCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockType'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Block Type
      tags:
      - Block types
  /api/block_types/filter:
    post:
      description: Gets all block types. Optionally limit return with limit and offset.
      operationId: read_block_types_block_types_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_block_types_block_types_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/BlockType'
                title: Response Read Block Types Block Types Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Types
      tags:
      - Block types
  /api/block_types/install_system_block_types:
    post:
      operationId: install_system_block_types_block_types_install_system_block_types_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Install System Block Types
      tags:
      - Block types
  /api/block_types/slug/{slug}:
    get:
      description: Get a block type by name.
      operationId: read_block_type_by_slug_block_types_slug__slug__get
      parameters:
      - description: The block type name
        in: path
        name: slug
        required: true
        schema:
          description: The block type name
          title: Slug
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockType'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Type By Slug
      tags:
      - Block types
  /api/block_types/slug/{slug}/block_documents:
    get:
      operationId: read_block_documents_for_block_type_block_types_slug__slug__block_documents_get
      parameters:
      - description: The block type name
        in: path
        name: slug
        required: true
        schema:
          description: The block type name
          title: Slug
          type: string
      - description: Whether to include sensitive values in the block document.
        in: query
        name: include_secrets
        required: false
        schema:
          default: false
          description: Whether to include sensitive values in the block document.
          title: Include Secrets
          type: boolean
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/BlockDocument'
                title: Response Read Block Documents For Block Type Block Types Slug  Slug  Block
                  Documents Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Documents For Block Type
      tags:
      - Block types
      - Block types
      - Block documents
  /api/block_types/slug/{slug}/block_documents/name/{block_document_name}:
    get:
      operationId: read_block_document_by_name_for_block_type_block_types_slug__slug__block_documents_name__block_document_name__get
      parameters:
      - description: The block type name
        in: path
        name: slug
        required: true
        schema:
          description: The block type name
          title: Slug
          type: string
      - description: The block type name
        in: path
        name: block_document_name
        required: true
        schema:
          description: The block type name
          title: Block Document Name
          type: string
      - description: Whether to include sensitive values in the block document.
        in: query
        name: include_secrets
        required: false
        schema:
          default: false
          description: Whether to include sensitive values in the block document.
          title: Include Secrets
          type: boolean
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockDocument'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Document By Name For Block Type
      tags:
      - Block types
      - Block types
      - Block documents
  /api/block_types/{id}:
    delete:
      operationId: delete_block_type_block_types__id__delete
      parameters:
      - description: The block type ID
        in: path
        name: id
        required: true
        schema:
          description: The block type ID
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Block Type
      tags:
      - Block types
    get:
      description: Get a block type by ID.
      operationId: read_block_type_by_id_block_types__id__get
      parameters:
      - description: The block type ID
        in: path
        name: id
        required: true
        schema:
          description: The block type ID
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BlockType'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Block Type By Id
      tags:
      - Block types
    patch:
      description: Update a block type.
      operationId: update_block_type_block_types__id__patch
      parameters:
      - description: The block type ID
        in: path
        name: id
        required: true
        schema:
          description: The block type ID
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BlockTypeUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Block Type
      tags:
      - Block types
  /api/collections/views/{view}:
    get:
      description: Reads the content of a view from the prefect-collection-registry.
      operationId: read_view_content_collections_views__view__get
      parameters:
      - in: path
        name: view
        required: true
        schema:
          title: View
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Read View Content Collections Views  View  Get
                type: object
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read View Content
      tags:
      - Collections
  /api/concurrency_limits/:
    post:
      description: 'Create a task run concurrency limit.


        For more information, see https://docs.prefect.io/v3/develop/task-run-limits.'
      operationId: create_concurrency_limit_concurrency_limits__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConcurrencyLimitCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConcurrencyLimit'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Concurrency Limit
      tags:
      - Concurrency Limits
  /api/concurrency_limits/decrement:
    post:
      operationId: decrement_concurrency_limits_v1_concurrency_limits_decrement_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_decrement_concurrency_limits_v1_concurrency_limits_decrement_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Decrement Concurrency Limits V1
      tags:
      - Concurrency Limits
  /api/concurrency_limits/filter:
    post:
      description: 'Query for concurrency limits.


        For each concurrency limit the `active slots` field contains a list of TaskRun
        IDs

        currently using a concurrency slot for the specified tag.'
      operationId: read_concurrency_limits_concurrency_limits_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_concurrency_limits_concurrency_limits_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ConcurrencyLimit'
                title: Response Read Concurrency Limits Concurrency Limits Filter
                  Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Concurrency Limits
      tags:
      - Concurrency Limits
  /api/concurrency_limits/increment:
    post:
      operationId: increment_concurrency_limits_v1_concurrency_limits_increment_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_increment_concurrency_limits_v1_concurrency_limits_increment_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MinimalConcurrencyLimitResponse'
                title: Response Increment Concurrency Limits V1 Concurrency Limits
                  Increment Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Increment Concurrency Limits V1
      tags:
      - Concurrency Limits
  /api/concurrency_limits/tag/{tag}:
    delete:
      operationId: delete_concurrency_limit_by_tag_concurrency_limits_tag__tag__delete
      parameters:
      - description: The tag name
        in: path
        name: tag
        required: true
        schema:
          description: The tag name
          title: Tag
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Concurrency Limit By Tag
      tags:
      - Concurrency Limits
    get:
      description: 'Get a concurrency limit by tag.


        The `active slots` field contains a list of TaskRun IDs currently using a

        concurrency slot for the specified tag.'
      operationId: read_concurrency_limit_by_tag_concurrency_limits_tag__tag__get
      parameters:
      - description: The tag name
        in: path
        name: tag
        required: true
        schema:
          description: The tag name
          title: Tag
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConcurrencyLimit'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Concurrency Limit By Tag
      tags:
      - Concurrency Limits
  /api/concurrency_limits/tag/{tag}/reset:
    post:
      operationId: reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post
      parameters:
      - description: The tag name
        in: path
        name: tag
        required: true
        schema:
          description: The tag name
          title: Tag
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_reset_concurrency_limit_by_tag_concurrency_limits_tag__tag__reset_post'
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Reset Concurrency Limit By Tag
      tags:
      - Concurrency Limits
  /api/concurrency_limits/{id}:
    delete:
      operationId: delete_concurrency_limit_concurrency_limits__id__delete
      parameters:
      - description: The concurrency limit id
        in: path
        name: id
        required: true
        schema:
          description: The concurrency limit id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Concurrency Limit
      tags:
      - Concurrency Limits
    get:
      description: 'Get a concurrency limit by id.


        The `active slots` field contains a list of TaskRun IDs currently using a

        concurrency slot for the specified tag.'
      operationId: read_concurrency_limit_concurrency_limits__id__get
      parameters:
      - description: The concurrency limit id
        in: path
        name: id
        required: true
        schema:
          description: The concurrency limit id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConcurrencyLimit'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Concurrency Limit
      tags:
      - Concurrency Limits
  /api/csrf-token:
    get:
      description: Create or update a CSRF token for a client
      operationId: create_csrf_token_csrf_token_get
      parameters:
      - description: The client to create a CSRF token for
        in: query
        name: client
        required: true
        schema:
          description: The client to create a CSRF token for
          title: Client
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CsrfToken'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Csrf Token
  /api/deployments/:
    post:
      description: 'Gracefully creates a new deployment from the provided schema.
        If a deployment with

        the same name and flow_id already exists, the deployment is updated.


        If the deployment has an active schedule, flow runs will be scheduled.

        When upserting, any scheduled runs from the existing deployment will be deleted.


        For more information, see https://docs.prefect.io/v3/deploy.'
      operationId: create_deployment_deployments__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeploymentCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Deployment
      tags:
      - Deployments
  /api/deployments/count:
    post:
      description: Count deployments.
      operationId: count_deployments_deployments_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_deployments_deployments_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Deployments Deployments Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Deployments
      tags:
      - Deployments
  /api/deployments/filter:
    post:
      description: Query for deployments.
      operationId: read_deployments_deployments_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_deployments_deployments_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DeploymentResponse'
                title: Response Read Deployments Deployments Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Deployments
      tags:
      - Deployments
  /api/deployments/get_scheduled_flow_runs:
    post:
      description: Get scheduled runs for a set of deployments. Used by a runner to
        poll for work.
      operationId: get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_get_scheduled_flow_runs_for_deployments_deployments_get_scheduled_flow_runs_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FlowRunResponse'
                title: Response Get Scheduled Flow Runs For Deployments Deployments
                  Get Scheduled Flow Runs Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Get Scheduled Flow Runs For Deployments
      tags:
      - Deployments
  /api/deployments/name/{flow_name}/{deployment_name}:
    get:
      description: Get a deployment using the name of the flow and the deployment.
      operationId: read_deployment_by_name_deployments_name__flow_name___deployment_name__get
      parameters:
      - description: The name of the flow
        in: path
        name: flow_name
        required: true
        schema:
          description: The name of the flow
          title: Flow Name
          type: string
      - description: The name of the deployment
        in: path
        name: deployment_name
        required: true
        schema:
          description: The name of the deployment
          title: Deployment Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Deployment By Name
      tags:
      - Deployments
  /api/deployments/paginate:
    post:
      description: Pagination query for flow runs.
      operationId: paginate_deployments_deployments_paginate_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_paginate_deployments_deployments_paginate_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentPaginationResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Paginate Deployments
      tags:
      - Deployments
  /api/deployments/{id}:
    delete:
      description: Delete a deployment by id.
      operationId: delete_deployment_deployments__id__delete
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Deployment
      tags:
      - Deployments
    get:
      description: Get a deployment by id.
      operationId: read_deployment_deployments__id__get
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Deployment
      tags:
      - Deployments
    patch:
      operationId: update_deployment_deployments__id__patch
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeploymentUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Deployment
      tags:
      - Deployments
  /api/deployments/{id}/create_flow_run:
    post:
      description: 'Create a flow run from a deployment.


        Any parameters not provided will be inferred from the deployment''s parameters.

        If tags are not provided, the deployment''s tags will be used.


        If no state is provided, the flow run will be created in a SCHEDULED state.'
      operationId: create_flow_run_from_deployment_deployments__id__create_flow_run_post
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeploymentFlowRunCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowRunResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Flow Run From Deployment
      tags:
      - Deployments
  /api/deployments/{id}/pause_deployment:
    post:
      description: 'Set a deployment schedule to inactive. Any auto-scheduled runs
        still in a Scheduled

        state will be deleted.'
      operationId: pause_deployment_deployments__id__pause_deployment_post
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Pause Deployment
      tags:
      - Deployments
  /api/deployments/{id}/resume_deployment:
    post:
      description: Set a deployment schedule to active. Runs will be scheduled immediately.
      operationId: resume_deployment_deployments__id__resume_deployment_post
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Resume Deployment
      tags:
      - Deployments
  /api/deployments/{id}/schedule:
    post:
      description: "Schedule runs for a deployment. For backfills, provide start/end\
        \ times in the past.\n\nThis function will generate the minimum number of\
        \ runs that satisfy the min\nand max times, and the min and max counts. Specifically,\
        \ the following order\nwill be respected.\n\n    - Runs will be generated\
        \ starting on or after the `start_time`\n    - No more than `max_runs` runs\
        \ will be generated\n    - No runs will be generated after `end_time` is reached\n\
        \    - At least `min_runs` runs will be generated\n    - Runs will be generated\
        \ until at least `start_time + min_time` is reached"
      operationId: schedule_deployment_deployments__id__schedule_post
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_schedule_deployment_deployments__id__schedule_post'
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Schedule Deployment
      tags:
      - Deployments
  /api/deployments/{id}/schedules:
    get:
      operationId: read_deployment_schedules_deployments__id__schedules_get
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DeploymentSchedule'
                title: Response Read Deployment Schedules Deployments  Id  Schedules
                  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Deployment Schedules
      tags:
      - Deployments
    post:
      operationId: create_deployment_schedules_deployments__id__schedules_post
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              description: The schedules to create
              items:
                $ref: '#/components/schemas/DeploymentScheduleCreate'
              title: Schedules
              type: array
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DeploymentSchedule'
                title: Response Create Deployment Schedules Deployments  Id  Schedules
                  Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Deployment Schedules
      tags:
      - Deployments
  /api/deployments/{id}/schedules/{schedule_id}:
    delete:
      operationId: delete_deployment_schedule_deployments__id__schedules__schedule_id__delete
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - description: The schedule id
        in: path
        name: schedule_id
        required: true
        schema:
          description: The schedule id
          format: uuid
          title: Schedule Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Deployment Schedule
      tags:
      - Deployments
    patch:
      operationId: update_deployment_schedule_deployments__id__schedules__schedule_id__patch
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - description: The schedule id
        in: path
        name: schedule_id
        required: true
        schema:
          description: The schedule id
          format: uuid
          title: Schedule Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeploymentScheduleUpdate'
              description: The updated schedule
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Deployment Schedule
      tags:
      - Deployments
  /api/deployments/{id}/work_queue_check:
    get:
      deprecated: true
      description: 'Get list of work-queues that are able to pick up the specified
        deployment.


        This endpoint is intended to be used by the UI to provide users warnings

        about deployments that are unable to be executed because there are no work

        queues that will pick up their runs, based on existing filter criteria. It

        may be deprecated in the future because there is not a strict relationship

        between work queues and deployments.'
      operationId: work_queue_check_for_deployment_deployments__id__work_queue_check_get
      parameters:
      - description: The deployment id
        in: path
        name: id
        required: true
        schema:
          description: The deployment id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/WorkQueue'
                title: Response Work Queue Check For Deployment Deployments  Id  Work
                  Queue Check Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Work Queue Check For Deployment
      tags:
      - Deployments
  /api/events:
    post:
      description: 'Record a batch of Events.


        For more information, see https://docs.prefect.io/v3/automate/events/events.'
      operationId: create_events_events_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              items:
                $ref: '#/components/schemas/Event'
              title: Events
              type: array
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Events
      tags:
      - Events
  /api/events/count-by/{countable}:
    post:
      description: 'Returns distinct objects and the count of events associated with
        them.  Objects

        that can be counted include the day the event occurred, the type of event,
        or

        the IDs of the resources associated with the event.'
      operationId: count_account_events_events_count_by__countable__post
      parameters:
      - in: path
        name: countable
        required: true
        schema:
          $ref: '#/components/schemas/Countable'
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_account_events_events_count_by__countable__post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/EventCount'
                title: Response Count Account Events Events Count By  Countable  Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Account Events
      tags:
      - Events
  /api/events/filter:
    post:
      description: 'Queries for Events matching the given filter criteria in the given
        Account.  Returns

        the first page of results, and the URL to request the next page (if there
        are more

        results).'
      operationId: read_events_events_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_events_events_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventPage'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Events
      tags:
      - Events
  /api/events/filter/next:
    get:
      description: 'Returns the next page of Events for a previous query against the
        given Account, and

        the URL to request the next page (if there are more results).'
      operationId: read_account_events_page_events_filter_next_get
      parameters:
      - in: query
        name: page-token
        required: true
        schema:
          title: Page-Token
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventPage'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Account Events Page
      tags:
      - Events
  /api/flow_run_notification_policies/:
    post:
      description: 'Creates a new flow run notification policy.


        For more information, see https://docs.prefect.io/v3/automate/events/automations-triggers#sending-notifications-with-automations.'
      operationId: create_flow_run_notification_policy_flow_run_notification_policies__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlowRunNotificationPolicyCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowRunNotificationPolicy'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Flow Run Notification Policy
      tags:
      - Flow Run Notification Policies
  /api/flow_run_notification_policies/filter:
    post:
      description: Query for flow run notification policies.
      operationId: read_flow_run_notification_policies_flow_run_notification_policies_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_flow_run_notification_policies_flow_run_notification_policies_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FlowRunNotificationPolicy'
                title: Response Read Flow Run Notification Policies Flow Run Notification
                  Policies Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run Notification Policies
      tags:
      - Flow Run Notification Policies
  /api/flow_run_notification_policies/{id}:
    delete:
      description: Delete a flow run notification policy by id.
      operationId: delete_flow_run_notification_policy_flow_run_notification_policies__id__delete
      parameters:
      - description: The flow run notification policy id
        in: path
        name: id
        required: true
        schema:
          description: The flow run notification policy id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Flow Run Notification Policy
      tags:
      - Flow Run Notification Policies
    get:
      description: Get a flow run notification policy by id.
      operationId: read_flow_run_notification_policy_flow_run_notification_policies__id__get
      parameters:
      - description: The flow run notification policy id
        in: path
        name: id
        required: true
        schema:
          description: The flow run notification policy id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowRunNotificationPolicy'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run Notification Policy
      tags:
      - Flow Run Notification Policies
    patch:
      description: Updates an existing flow run notification policy.
      operationId: update_flow_run_notification_policy_flow_run_notification_policies__id__patch
      parameters:
      - description: The flow run notification policy id
        in: path
        name: id
        required: true
        schema:
          description: The flow run notification policy id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlowRunNotificationPolicyUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Flow Run Notification Policy
      tags:
      - Flow Run Notification Policies
  /api/flow_run_states/:
    get:
      description: Get states associated with a flow run.
      operationId: read_flow_run_states_flow_run_states__get
      parameters:
      - in: query
        name: flow_run_id
        required: true
        schema:
          format: uuid
          title: Flow Run Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/State'
                title: Response Read Flow Run States Flow Run States  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run States
      tags:
      - Flow Run States
  /api/flow_run_states/{id}:
    get:
      description: 'Get a flow run state by id.


        For more information, see https://docs.prefect.io/v3/develop/write-flows#final-state-determination.'
      operationId: read_flow_run_state_flow_run_states__id__get
      parameters:
      - description: The flow run state id
        in: path
        name: id
        required: true
        schema:
          description: The flow run state id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/State'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run State
      tags:
      - Flow Run States
  /api/flow_runs/:
    post:
      description: 'Create a flow run. If a flow run with the same flow_id and

        idempotency key already exists, the existing flow run will be returned.


        If no state is provided, the flow run will be created in a PENDING state.


        For more information, see https://docs.prefect.io/v3/develop/write-flows.'
      operationId: create_flow_run_flow_runs__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlowRunCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowRunResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Flow Run
      tags:
      - Flow Runs
  /api/flow_runs/count:
    post:
      description: Query for flow runs.
      operationId: count_flow_runs_flow_runs_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_flow_runs_flow_runs_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Flow Runs Flow Runs Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Flow Runs
      tags:
      - Flow Runs
  /api/flow_runs/filter:
    post:
      description: Query for flow runs.
      operationId: read_flow_runs_flow_runs_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_flow_runs_flow_runs_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FlowRunResponse'
                title: Response Read Flow Runs Flow Runs Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Runs
      tags:
      - Flow Runs
  /api/flow_runs/history:
    post:
      description: Query for flow run history data across a given range and interval.
      operationId: flow_run_history_flow_runs_history_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_flow_run_history_flow_runs_history_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/HistoryResponse'
                title: Response Flow Run History Flow Runs History Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Flow Run History
      tags:
      - Flow Runs
  /api/flow_runs/lateness:
    post:
      description: Query for average flow-run lateness in seconds.
      operationId: average_flow_run_lateness_flow_runs_lateness_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_average_flow_run_lateness_flow_runs_lateness_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - type: number
                - type: 'null'
                title: Response Average Flow Run Lateness Flow Runs Lateness Post
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Average Flow Run Lateness
      tags:
      - Flow Runs
  /api/flow_runs/paginate:
    post:
      description: Pagination query for flow runs.
      operationId: paginate_flow_runs_flow_runs_paginate_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_paginate_flow_runs_flow_runs_paginate_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowRunPaginationResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Paginate Flow Runs
      tags:
      - Flow Runs
  /api/flow_runs/{id}:
    delete:
      description: Delete a flow run by id.
      operationId: delete_flow_run_flow_runs__id__delete
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Flow Run
      tags:
      - Flow Runs
    get:
      description: Get a flow run by id.
      operationId: read_flow_run_flow_runs__id__get
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowRunResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run
      tags:
      - Flow Runs
    patch:
      description: Updates a flow run.
      operationId: update_flow_run_flow_runs__id__patch
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlowRunUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Flow Run
      tags:
      - Flow Runs
  /api/flow_runs/{id}/graph:
    get:
      description: Get a task run dependency map for a given flow run.
      operationId: read_flow_run_graph_v1_flow_runs__id__graph_get
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DependencyResult'
                title: Response Read Flow Run Graph V1 Flow Runs  Id  Graph Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run Graph V1
      tags:
      - Flow Runs
  /api/flow_runs/{id}/graph-v2:
    get:
      description: Get a graph of the tasks and subflow runs for the given flow run
      operationId: read_flow_run_graph_v2_flow_runs__id__graph_v2_get
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - description: Only include runs that start or end after this time.
        in: query
        name: since
        required: false
        schema:
          default: '0001-01-01T00:00:00+00:00'
          description: Only include runs that start or end after this time.
          format: date-time
          title: Since
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Graph'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run Graph V2
      tags:
      - Flow Runs
  /api/flow_runs/{id}/input:
    post:
      description: Create a key/value input for a flow run.
      operationId: create_flow_run_input_flow_runs__id__input_post
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_create_flow_run_input_flow_runs__id__input_post'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Flow Run Input
      tags:
      - Flow Runs
  /api/flow_runs/{id}/input/filter:
    post:
      description: Filter flow run inputs by key prefix
      operationId: filter_flow_run_input_flow_runs__id__input_filter_post
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_filter_flow_run_input_flow_runs__id__input_filter_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FlowRunInput'
                title: Response Filter Flow Run Input Flow Runs  Id  Input Filter
                  Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Filter Flow Run Input
      tags:
      - Flow Runs
  /api/flow_runs/{id}/input/{key}:
    delete:
      description: Delete a flow run input
      operationId: delete_flow_run_input_flow_runs__id__input__key__delete
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - description: The input key
        in: path
        name: key
        required: true
        schema:
          description: The input key
          title: Key
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Flow Run Input
      tags:
      - Flow Runs
    get:
      description: Create a value from a flow run input
      operationId: read_flow_run_input_flow_runs__id__input__key__get
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - description: The input key
        in: path
        name: key
        required: true
        schema:
          description: The input key
          title: Key
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run Input
      tags:
      - Flow Runs
  /api/flow_runs/{id}/labels:
    patch:
      description: Update the labels of a flow run.
      operationId: update_flow_run_labels_flow_runs__id__labels_patch
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              description: The labels to update
              title: Labels
              type: object
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Flow Run Labels
      tags:
      - Flow Runs
  /api/flow_runs/{id}/logs/download:
    get:
      description: Download all flow run logs as a CSV file, collecting all logs until
        there are no more logs to retrieve.
      operationId: download_logs_flow_runs__id__logs_download_get
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Download Logs
      tags:
      - Flow Runs
  /api/flow_runs/{id}/resume:
    post:
      description: Resume a paused flow run.
      operationId: resume_flow_run_flow_runs__id__resume_post
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_resume_flow_run_flow_runs__id__resume_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationResult'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Resume Flow Run
      tags:
      - Flow Runs
  /api/flow_runs/{id}/set_state:
    post:
      description: Set a flow run state, invoking any orchestration rules.
      operationId: set_flow_run_state_flow_runs__id__set_state_post
      parameters:
      - description: The flow run id
        in: path
        name: id
        required: true
        schema:
          description: The flow run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_set_flow_run_state_flow_runs__id__set_state_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationResult'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Set Flow Run State
      tags:
      - Flow Runs
  /api/flows/:
    post:
      description: 'Gracefully creates a new flow from the provided schema. If a flow
        with the

        same name already exists, the existing flow is returned.


        For more information, see https://docs.prefect.io/v3/develop/write-flows.'
      operationId: create_flow_flows__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlowCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Flow'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Flow
      tags:
      - Flows
  /api/flows/count:
    post:
      description: Count flows.
      operationId: count_flows_flows_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_flows_flows_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Flows Flows Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Flows
      tags:
      - Flows
  /api/flows/filter:
    post:
      description: Query for flows.
      operationId: read_flows_flows_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_flows_flows_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Flow'
                title: Response Read Flows Flows Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flows
      tags:
      - Flows
  /api/flows/name/{name}:
    get:
      description: Get a flow by name.
      operationId: read_flow_by_name_flows_name__name__get
      parameters:
      - description: The name of the flow
        in: path
        name: name
        required: true
        schema:
          description: The name of the flow
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Flow'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow By Name
      tags:
      - Flows
  /api/flows/paginate:
    post:
      description: Pagination query for flows.
      operationId: paginate_flows_flows_paginate_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_paginate_flows_flows_paginate_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlowPaginationResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Paginate Flows
      tags:
      - Flows
  /api/flows/{id}:
    delete:
      description: Delete a flow by id.
      operationId: delete_flow_flows__id__delete
      parameters:
      - description: The flow id
        in: path
        name: id
        required: true
        schema:
          description: The flow id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Flow
      tags:
      - Flows
    get:
      description: Get a flow by id.
      operationId: read_flow_flows__id__get
      parameters:
      - description: The flow id
        in: path
        name: id
        required: true
        schema:
          description: The flow id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Flow'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow
      tags:
      - Flows
    patch:
      description: Updates a flow.
      operationId: update_flow_flows__id__patch
      parameters:
      - description: The flow id
        in: path
        name: id
        required: true
        schema:
          description: The flow id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlowUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Flow
      tags:
      - Flows
  /api/health:
    get:
      operationId: health_check_health_get
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Health Check Health Get
                type: boolean
          description: Successful Response
      summary: Health Check
      tags:
      - Root
  /api/hello:
    get:
      description: Say hello!
      operationId: hello_hello_get
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Hello Hello Get
                type: string
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Hello
      tags:
      - Root
  /api/logs/:
    post:
      description: 'Create new logs from the provided schema.


        For more information, see https://docs.prefect.io/v3/develop/logging.'
      operationId: create_logs_logs__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              items:
                $ref: '#/components/schemas/LogCreate'
              title: Logs
              type: array
        required: true
      responses:
        '201':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Logs
      tags:
      - Logs
  /api/logs/filter:
    post:
      description: Query for logs.
      operationId: read_logs_logs_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_logs_logs_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Log'
                title: Response Read Logs Logs Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Logs
      tags:
      - Logs
  /api/ready:
    get:
      operationId: perform_readiness_check_ready_get
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema: {}
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Perform Readiness Check
      tags:
      - Root
  /api/saved_searches/:
    put:
      description: 'Gracefully creates a new saved search from the provided schema.


        If a saved search with the same name already exists, the saved search''s fields
        are

        replaced.'
      operationId: create_saved_search_saved_searches__put
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SavedSearchCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SavedSearch'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Saved Search
      tags:
      - SavedSearches
  /api/saved_searches/filter:
    post:
      description: Query for saved searches.
      operationId: read_saved_searches_saved_searches_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_saved_searches_saved_searches_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SavedSearch'
                title: Response Read Saved Searches Saved Searches Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Saved Searches
      tags:
      - SavedSearches
  /api/saved_searches/{id}:
    delete:
      description: Delete a saved search by id.
      operationId: delete_saved_search_saved_searches__id__delete
      parameters:
      - description: The saved search id
        in: path
        name: id
        required: true
        schema:
          description: The saved search id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Saved Search
      tags:
      - SavedSearches
    get:
      description: Get a saved search by id.
      operationId: read_saved_search_saved_searches__id__get
      parameters:
      - description: The saved search id
        in: path
        name: id
        required: true
        schema:
          description: The saved search id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SavedSearch'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Saved Search
      tags:
      - SavedSearches
  /api/task_run_states/:
    get:
      description: Get states associated with a task run.
      operationId: read_task_run_states_task_run_states__get
      parameters:
      - in: query
        name: task_run_id
        required: true
        schema:
          format: uuid
          title: Task Run Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/State'
                title: Response Read Task Run States Task Run States  Get
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Task Run States
      tags:
      - Task Run States
  /api/task_run_states/{id}:
    get:
      description: 'Get a task run state by id.


        For more information, see https://docs.prefect.io/v3/develop/write-tasks.'
      operationId: read_task_run_state_task_run_states__id__get
      parameters:
      - description: The task run state id
        in: path
        name: id
        required: true
        schema:
          description: The task run state id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/State'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Task Run State
      tags:
      - Task Run States
  /api/task_runs/:
    post:
      description: 'Create a task run. If a task run with the same flow_run_id,

        task_key, and dynamic_key already exists, the existing task

        run will be returned.


        If no state is provided, the task run will be created in a PENDING state.


        For more information, see https://docs.prefect.io/v3/develop/write-tasks.'
      operationId: create_task_run_task_runs__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskRunCreate'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskRun'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Task Run
      tags:
      - Task Runs
  /api/task_runs/count:
    post:
      description: Count task runs.
      operationId: count_task_runs_task_runs_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_task_runs_task_runs_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Task Runs Task Runs Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Task Runs
      tags:
      - Task Runs
  /api/task_runs/filter:
    post:
      description: Query for task runs.
      operationId: read_task_runs_task_runs_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_task_runs_task_runs_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TaskRun'
                title: Response Read Task Runs Task Runs Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Task Runs
      tags:
      - Task Runs
  /api/task_runs/history:
    post:
      description: Query for task run history data across a given range and interval.
      operationId: task_run_history_task_runs_history_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_task_run_history_task_runs_history_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/HistoryResponse'
                title: Response Task Run History Task Runs History Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Task Run History
      tags:
      - Task Runs
  /api/task_runs/{id}:
    delete:
      description: Delete a task run by id.
      operationId: delete_task_run_task_runs__id__delete
      parameters:
      - description: The task run id
        in: path
        name: id
        required: true
        schema:
          description: The task run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Task Run
      tags:
      - Task Runs
    get:
      description: Get a task run by id.
      operationId: read_task_run_task_runs__id__get
      parameters:
      - description: The task run id
        in: path
        name: id
        required: true
        schema:
          description: The task run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskRun'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Task Run
      tags:
      - Task Runs
    patch:
      description: Updates a task run.
      operationId: update_task_run_task_runs__id__patch
      parameters:
      - description: The task run id
        in: path
        name: id
        required: true
        schema:
          description: The task run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskRunUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Task Run
      tags:
      - Task Runs
  /api/task_runs/{id}/set_state:
    post:
      description: Set a task run state, invoking any orchestration rules.
      operationId: set_task_run_state_task_runs__id__set_state_post
      parameters:
      - description: The task run id
        in: path
        name: id
        required: true
        schema:
          description: The task run id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_set_task_run_state_task_runs__id__set_state_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrchestrationResult'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Set Task Run State
      tags:
      - Task Runs
  /api/task_workers/filter:
    post:
      description: 'Read active task workers. Optionally filter by task keys.


        For more information, see https://docs.prefect.io/v3/develop/deferred-tasks.'
      operationId: read_task_workers_task_workers_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_task_workers_task_workers_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TaskWorkerResponse'
                title: Response Read Task Workers Task Workers Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Task Workers
      tags:
      - Task Workers
  /api/templates/validate:
    post:
      operationId: validate_template_templates_validate_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              default: ''
              title: Template
              type: string
      responses:
        '200':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Validate Template
      tags:
      - Automations
  /api/ui/flow_runs/count-task-runs:
    post:
      description: Get task run counts by flow run id.
      operationId: count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_task_runs_by_flow_run_ui_flow_runs_count_task_runs_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                additionalProperties:
                  type: integer
                propertyNames:
                  format: uuid
                title: Response Count Task Runs By Flow Run Ui Flow Runs Count Task
                  Runs Post
                type: object
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Task Runs By Flow Run
      tags:
      - Flow Runs
      - UI
  /api/ui/flow_runs/history:
    post:
      operationId: read_flow_run_history_ui_flow_runs_history_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_flow_run_history_ui_flow_runs_history_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SimpleFlowRun'
                title: Response Read Flow Run History Ui Flow Runs History Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Flow Run History
      tags:
      - Flow Runs
      - UI
  /api/ui/flows/count-deployments:
    post:
      description: Get deployment counts by flow id.
      operationId: count_deployments_by_flow_ui_flows_count_deployments_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_deployments_by_flow_ui_flows_count_deployments_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                additionalProperties:
                  type: integer
                propertyNames:
                  format: uuid
                title: Response Count Deployments By Flow Ui Flows Count Deployments
                  Post
                type: object
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Deployments By Flow
      tags:
      - Flows
      - UI
  /api/ui/flows/next-runs:
    post:
      description: Get the next flow run by flow id.
      operationId: next_runs_by_flow_ui_flows_next_runs_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_next_runs_by_flow_ui_flows_next_runs_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                additionalProperties:
                  anyOf:
                  - $ref: '#/components/schemas/SimpleNextFlowRun'
                  - type: 'null'
                propertyNames:
                  format: uuid
                title: Response Next Runs By Flow Ui Flows Next Runs Post
                type: object
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Next Runs By Flow
      tags:
      - Flows
      - UI
  /api/ui/schemas/validate:
    post:
      operationId: validate_obj_ui_schemas_validate_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_validate_obj_ui_schemas_validate_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SchemaValuesValidationResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Validate Obj
      tags:
      - UI
      - Schemas
  /api/ui/task_runs/count:
    post:
      operationId: read_task_run_counts_by_state_ui_task_runs_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_task_run_counts_by_state_ui_task_runs_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountByState'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Task Run Counts By State
      tags:
      - Task Runs
      - UI
  /api/ui/task_runs/dashboard/counts:
    post:
      operationId: read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_dashboard_task_run_counts_ui_task_runs_dashboard_counts_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TaskRunCount'
                title: Response Read Dashboard Task Run Counts Ui Task Runs Dashboard
                  Counts Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Dashboard Task Run Counts
      tags:
      - Task Runs
      - UI
  /api/v2/concurrency_limits/:
    post:
      description: 'Create a task run concurrency limit.


        For more information, see https://docs.prefect.io/v3/develop/global-concurrency-limits.'
      operationId: create_concurrency_limit_v2_v2_concurrency_limits__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConcurrencyLimitV2Create'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConcurrencyLimitV2'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Concurrency Limit V2
      tags:
      - Concurrency Limits V2
  /api/v2/concurrency_limits/decrement:
    post:
      operationId: bulk_decrement_active_slots_v2_concurrency_limits_decrement_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_bulk_decrement_active_slots_v2_concurrency_limits_decrement_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MinimalConcurrencyLimitResponse'
                title: Response Bulk Decrement Active Slots V2 Concurrency Limits
                  Decrement Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Bulk Decrement Active Slots
      tags:
      - Concurrency Limits V2
  /api/v2/concurrency_limits/filter:
    post:
      operationId: read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_all_concurrency_limits_v2_v2_concurrency_limits_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/GlobalConcurrencyLimitResponse'
                title: Response Read All Concurrency Limits V2 V2 Concurrency Limits
                  Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read All Concurrency Limits V2
      tags:
      - Concurrency Limits V2
  /api/v2/concurrency_limits/increment:
    post:
      operationId: bulk_increment_active_slots_v2_concurrency_limits_increment_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_bulk_increment_active_slots_v2_concurrency_limits_increment_post'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MinimalConcurrencyLimitResponse'
                title: Response Bulk Increment Active Slots V2 Concurrency Limits
                  Increment Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Bulk Increment Active Slots
      tags:
      - Concurrency Limits V2
  /api/v2/concurrency_limits/{id_or_name}:
    delete:
      operationId: delete_concurrency_limit_v2_v2_concurrency_limits__id_or_name__delete
      parameters:
      - description: The ID or name of the concurrency limit
        in: path
        name: id_or_name
        required: true
        schema:
          anyOf:
          - format: uuid
            type: string
          - type: string
          description: The ID or name of the concurrency limit
          title: Id Or Name
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Concurrency Limit V2
      tags:
      - Concurrency Limits V2
    get:
      operationId: read_concurrency_limit_v2_v2_concurrency_limits__id_or_name__get
      parameters:
      - description: The ID or name of the concurrency limit
        in: path
        name: id_or_name
        required: true
        schema:
          anyOf:
          - format: uuid
            type: string
          - type: string
          description: The ID or name of the concurrency limit
          title: Id Or Name
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalConcurrencyLimitResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Concurrency Limit V2
      tags:
      - Concurrency Limits V2
    patch:
      operationId: update_concurrency_limit_v2_v2_concurrency_limits__id_or_name__patch
      parameters:
      - description: The ID or name of the concurrency limit
        in: path
        name: id_or_name
        required: true
        schema:
          anyOf:
          - format: uuid
            type: string
          - type: string
          description: The ID or name of the concurrency limit
          title: Id Or Name
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConcurrencyLimitV2Update'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Concurrency Limit V2
      tags:
      - Concurrency Limits V2
  /api/variables/:
    post:
      description: 'Create a variable.


        For more information, see https://docs.prefect.io/v3/develop/variables.'
      operationId: create_variable_variables__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VariableCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Variable'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Variable
      tags:
      - Variables
  /api/variables/count:
    post:
      operationId: count_variables_variables_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_variables_variables_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Variables Variables Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Variables
      tags:
      - Variables
  /api/variables/filter:
    post:
      operationId: read_variables_variables_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_variables_variables_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Variable'
                title: Response Read Variables Variables Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Variables
      tags:
      - Variables
  /api/variables/name/{name}:
    delete:
      operationId: delete_variable_by_name_variables_name__name__delete
      parameters:
      - in: path
        name: name
        required: true
        schema:
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Variable By Name
      tags:
      - Variables
    get:
      operationId: read_variable_by_name_variables_name__name__get
      parameters:
      - in: path
        name: name
        required: true
        schema:
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Variable'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Variable By Name
      tags:
      - Variables
    patch:
      operationId: update_variable_by_name_variables_name__name__patch
      parameters:
      - in: path
        name: name
        required: true
        schema:
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VariableUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Variable By Name
      tags:
      - Variables
  /api/variables/{id}:
    delete:
      operationId: delete_variable_variables__id__delete
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Variable
      tags:
      - Variables
    get:
      operationId: read_variable_variables__id__get
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Variable'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Variable
      tags:
      - Variables
    patch:
      operationId: update_variable_variables__id__patch
      parameters:
      - in: path
        name: id
        required: true
        schema:
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VariableUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Variable
      tags:
      - Variables
  /api/version:
    get:
      operationId: server_version_version_get
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Server Version Version Get
                type: string
          description: Successful Response
      summary: Server Version
      tags:
      - Root
  /api/work_pools/:
    post:
      description: 'Creates a new work pool. If a work pool with the same

        name already exists, an error will be raised.


        For more information, see https://docs.prefect.io/v3/deploy/infrastructure-concepts/work-pools.'
      operationId: create_work_pool_work_pools__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkPoolCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkPool'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Work Pool
      tags:
      - Work Pools
  /api/work_pools/count:
    post:
      description: Count work pools
      operationId: count_work_pools_work_pools_count_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_count_work_pools_work_pools_count_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Response Count Work Pools Work Pools Count Post
                type: integer
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Count Work Pools
      tags:
      - Work Pools
  /api/work_pools/filter:
    post:
      description: Read multiple work pools
      operationId: read_work_pools_work_pools_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_work_pools_work_pools_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/WorkPool'
                title: Response Read Work Pools Work Pools Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Pools
      tags:
      - Work Pools
  /api/work_pools/{name}:
    delete:
      description: Delete a work pool
      operationId: delete_work_pool_work_pools__name__delete
      parameters:
      - description: The work pool name
        in: path
        name: name
        required: true
        schema:
          description: The work pool name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Work Pool
      tags:
      - Work Pools
    get:
      description: Read a work pool by name
      operationId: read_work_pool_work_pools__name__get
      parameters:
      - description: The work pool name
        in: path
        name: name
        required: true
        schema:
          description: The work pool name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkPool'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Pool
      tags:
      - Work Pools
    patch:
      description: Update a work pool
      operationId: update_work_pool_work_pools__name__patch
      parameters:
      - description: The work pool name
        in: path
        name: name
        required: true
        schema:
          description: The work pool name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkPoolUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Work Pool
      tags:
      - Work Pools
  /api/work_pools/{name}/get_scheduled_flow_runs:
    post:
      description: Load scheduled runs for a worker
      operationId: get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post
      parameters:
      - description: The work pool name
        in: path
        name: name
        required: true
        schema:
          description: The work pool name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_get_scheduled_flow_runs_work_pools__name__get_scheduled_flow_runs_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/WorkerFlowRunResponse'
                title: Response Get Scheduled Flow Runs Work Pools  Name  Get Scheduled
                  Flow Runs Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Get Scheduled Flow Runs
      tags:
      - Work Pools
  /api/work_pools/{work_pool_name}/queues:
    post:
      description: 'Creates a new work pool queue. If a work pool queue with the same

        name already exists, an error will be raised.


        For more information, see https://docs.prefect.io/v3/deploy/infrastructure-concepts/work-pools#work-queues.'
      operationId: create_work_queue_work_pools__work_pool_name__queues_post
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkQueueCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkQueueResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Work Queue
      tags:
      - Work Pools
  /api/work_pools/{work_pool_name}/queues/filter:
    post:
      description: Read all work pool queues
      operationId: read_work_queues_work_pools__work_pool_name__queues_filter_post
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_work_queues_work_pools__work_pool_name__queues_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/WorkQueueResponse'
                title: Response Read Work Queues Work Pools  Work Pool Name  Queues
                  Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queues
      tags:
      - Work Pools
  /api/work_pools/{work_pool_name}/queues/{name}:
    delete:
      description: Delete a work pool queue
      operationId: delete_work_queue_work_pools__work_pool_name__queues__name__delete
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - description: The work pool queue name
        in: path
        name: name
        required: true
        schema:
          description: The work pool queue name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Work Queue
      tags:
      - Work Pools
    get:
      description: Read a work pool queue
      operationId: read_work_queue_work_pools__work_pool_name__queues__name__get
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - description: The work pool queue name
        in: path
        name: name
        required: true
        schema:
          description: The work pool queue name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkQueueResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queue
      tags:
      - Work Pools
    patch:
      description: Update a work pool queue
      operationId: update_work_queue_work_pools__work_pool_name__queues__name__patch
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - description: The work pool queue name
        in: path
        name: name
        required: true
        schema:
          description: The work pool queue name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkQueueUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Work Queue
      tags:
      - Work Pools
  /api/work_pools/{work_pool_name}/workers/filter:
    post:
      description: Read all worker processes
      operationId: read_workers_work_pools__work_pool_name__workers_filter_post
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_workers_work_pools__work_pool_name__workers_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/WorkerResponse'
                title: Response Read Workers Work Pools  Work Pool Name  Workers Filter
                  Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Workers
      tags:
      - Work Pools
  /api/work_pools/{work_pool_name}/workers/heartbeat:
    post:
      operationId: worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_worker_heartbeat_work_pools__work_pool_name__workers_heartbeat_post'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Worker Heartbeat
      tags:
      - Work Pools
  /api/work_pools/{work_pool_name}/workers/{name}:
    delete:
      description: Delete a work pool's worker
      operationId: delete_worker_work_pools__work_pool_name__workers__name__delete
      parameters:
      - description: The work pool name
        in: path
        name: work_pool_name
        required: true
        schema:
          description: The work pool name
          title: Work Pool Name
          type: string
      - description: The work pool's worker name
        in: path
        name: name
        required: true
        schema:
          description: The work pool's worker name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Worker
      tags:
      - Work Pools
  /api/work_queues/:
    post:
      description: 'Creates a new work queue.


        If a work queue with the same name already exists, an error

        will be raised.


        For more information, see https://docs.prefect.io/v3/deploy/infrastructure-concepts/work-pools#work-queues.'
      operationId: create_work_queue_work_queues__post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkQueueCreate'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkQueueResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Create Work Queue
      tags:
      - Work Queues
  /api/work_queues/filter:
    post:
      description: Query for work queues.
      operationId: read_work_queues_work_queues_filter_post
      parameters:
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_work_queues_work_queues_filter_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/WorkQueueResponse'
                title: Response Read Work Queues Work Queues Filter Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queues
      tags:
      - Work Queues
  /api/work_queues/name/{name}:
    get:
      description: Get a work queue by id.
      operationId: read_work_queue_by_name_work_queues_name__name__get
      parameters:
      - description: The work queue name
        in: path
        name: name
        required: true
        schema:
          description: The work queue name
          title: Name
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkQueueResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queue By Name
      tags:
      - Work Queues
  /api/work_queues/{id}:
    delete:
      description: Delete a work queue by id.
      operationId: delete_work_queue_work_queues__id__delete
      parameters:
      - description: The work queue id
        in: path
        name: id
        required: true
        schema:
          description: The work queue id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Delete Work Queue
      tags:
      - Work Queues
    get:
      description: Get a work queue by id.
      operationId: read_work_queue_work_queues__id__get
      parameters:
      - description: The work queue id
        in: path
        name: id
        required: true
        schema:
          description: The work queue id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkQueueResponse'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queue
      tags:
      - Work Queues
    patch:
      description: Updates an existing work queue.
      operationId: update_work_queue_work_queues__id__patch
      parameters:
      - description: The work queue id
        in: path
        name: id
        required: true
        schema:
          description: The work queue id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkQueueUpdate'
        required: true
      responses:
        '204':
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Update Work Queue
      tags:
      - Work Queues
  /api/work_queues/{id}/get_runs:
    post:
      description: Get flow runs from the work queue.
      operationId: read_work_queue_runs_work_queues__id__get_runs_post
      parameters:
      - description: The work queue id
        in: path
        name: id
        required: true
        schema:
          description: The work queue id
          format: uuid
          title: Id
          type: string
      - description: A header to indicate this request came from the Prefect UI.
        in: header
        name: x-prefect-ui
        required: false
        schema:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: A header to indicate this request came from the Prefect UI.
          title: X-Prefect-Ui
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Body_read_work_queue_runs_work_queues__id__get_runs_post'
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FlowRunResponse'
                title: Response Read Work Queue Runs Work Queues  Id  Get Runs Post
                type: array
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queue Runs
      tags:
      - Work Queues
  /api/work_queues/{id}/status:
    get:
      description: Get the status of a work queue.
      operationId: read_work_queue_status_work_queues__id__status_get
      parameters:
      - description: The work queue id
        in: path
        name: id
        required: true
        schema:
          description: The work queue id
          format: uuid
          title: Id
          type: string
      - in: header
        name: x-prefect-api-version
        required: false
        schema:
          title: X-Prefect-Api-Version
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkQueueStatusDetail'
          description: Successful Response
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
      summary: Read Work Queue Status
      tags:
      - Work Queues
