[project]
name = "prefect-openapi"
version = "0.1.0"
description = ""
authors = [
    { name = "Rashin Arab", email = "<EMAIL>" }
]
requires-python = ">=3.11"
dependencies = [
    "prefect[kubernetes] (>=3.2.9,<4.0.0)",
]

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
