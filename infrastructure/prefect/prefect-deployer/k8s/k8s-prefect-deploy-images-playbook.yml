---
- hosts: localhost
  roles:
    - common
  tasks:
    # Workaround for import_role bug
    # https://github.com/ansible/ansible/issues/80944
    - include_role:
        name: "{{ env }}"
        public: True

    - name: Set full path using ansible_env
      set_fact:
        resolvedPrefectSecretsDotEnv: "{{ ansible_env.HOME }}/{{ prefectSecretsDotEnv }}"

    # Set the Prefect API endpoint URL using a variable
    - name: Set Prefect API endpoint URL
      shell: prefect config set PREFECT_API_URL={{ prefectApiUrl }}

    - name: Set Prefect API authString
      shell: prefect config set PREFECT_API_AUTH_STRING={{ lookup('env', 'PREFECT_API_AUTH_STRING') | default(prefectApiAuthString, true) }}

    - name: Find directories containing prefect.yaml
      find:
        paths: "{{ playbook_dir }}"
        recurse: yes
        file_type: file
        patterns: 'prefect.yaml'
      register: find_result

    - name: Extract directory paths
      set_fact:
        prefect_dirs: "{{ find_result.files | map(attribute='path') | map('dirname') | unique }}"

    - name: Perform action in each prefect.yaml directory
      ansible.builtin.shell:
        cmd: |
          set -a
          source "{{ resolvedPrefectSecretsDotEnv }}"
          set +a
          prefect --no-prompt deploy --all
        chdir: "{{ item }}"
      loop: "{{ prefect_dirs }}"
      environment:
        JFROG_PASSWORD: "{{ lookup('env', 'JFROG_PASSWORD') | default(jfrogPassword, true) }}"
        PREFECT_ECR_REGISTRY: "{{ lookup('env', 'PREFECT_ECR_REGISTRY') | default(prefectEcrRegistry, true) }}"
        PREFECT_ECR_REPL_REGISTRY: "{{ lookup('env', '$PREFECT_ECR_REPL_REGISTRY') | default(prefectEcrReplRegistry, true) }}"
