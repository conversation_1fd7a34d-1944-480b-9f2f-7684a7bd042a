---
- hosts: localhost
  roles:
    - common
  tasks:
    # Workaround for import_role bug
    # https://github.com/ansible/ansible/issues/80944
    - include_role:
        name: "{{ env }}"
        public: True

    # Set the Prefect API endpoint URL using a variable
    - name: Set Prefect API endpoint URL
      shell: prefect config set PREFECT_API_URL={{ prefectApiUrl }}

    - name: Set Prefect API authString
      shell: prefect config set PREFECT_API_AUTH_STRING={{ lookup('env', 'PREFECT_API_AUTH_STRING') | default(prefectApiAuthString, true) }}

    # Create Kubernetes Work Pool using variables
    - name: Create Kubernetes Work Pool
      shell: >
        prefect work-pool create {{ prefectWorkPoolName }} --type kubernetes
        --base-job-template {{ playbook_dir }}/{{ prefectJobTemplateDir }}/{{ prefectWorkPoolName }}.json --overwrite

    # Update Kubernetes Work Pool using variables
    - name: Update Kubernetes Work Pool
      shell: >
        prefect work-pool update {{ prefectWorkPoolName }} --concurrency-limit {{ prefectConcurrencyLimit }}
