FROM --platform="linux/amd64" python:3.11-slim-bookworm

# Necessary for bertopic
RUN apt-get update \
  && apt-get clean  \
  && apt-get autoclean \
  && apt-get autoremove

# Install Git
RUN apt-get install -y git

# Tree-sitter workarounds
# - Install build tools (gcc) to build from source if necessary.
# - Install tree-sitter-languages using pip
RUN apt-get install -y build-essential
RUN pip3 install tree-sitter-language-pack==0.7.2

# Install lib-magic for file type detection
RUN apt-get install -y libmagic1

# Use Poetry to pin dependencies for sanity,
# and keep the development environment consistent with the production environment.
RUN pip3 install poetry==2.1.2

# Set up JFrog artifactory configuration such that we can pull from our private repositories
ARG JFROG_PASSWORD
RUN poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"


# No HTTPS proxy on kube
#RUN git config --global http.proxy  "http://proxy.$ENVIRONMENT.getunblocked.com:80" \
#    && git config --global https.proxy "http://proxy.$ENVIRONMENT.getunblocked.com:80"

WORKDIR /code

# Install dependencies
COPY . /code

## The install of torch is required for using CUDA (problem with poetry installation)
RUN POETRY_VIRTUALENVS_CREATE=false poetry install --no-cache --no-interaction --without dev
RUN poetry config virtualenvs.create false

# WARNING: DO NOT USE ENTRYPOINT - IT DOES NOT PLAY NICE WITH PREFECT FLOW PARAMETERS
# Add a Python script and configure Docker to run it
# ENTRYPOINT ["poetry", "run", "python", "/code/src/source_code_processor/process_source_code.py"]
