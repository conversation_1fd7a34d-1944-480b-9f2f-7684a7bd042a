[tool.poetry]
name = "source-code-processor"
version = "1.0.0"
description = ""
authors = []
readme = "README.md"
packages = [
    { include = "source_code_processor", from = "src" },
]

[tool.poetry.dependencies]
brotli = "^1"
huggingface_hub = "0.25.2"
langchain = '^0'
llama-index-core = '^0'
numpy = '<2'
openai = "^1"
prefect = "^3"
pydantic = ">=2.9,<3.0"
python = ">=3.10.12,<3.14"
sentence-transformers = '2.2.2'
unblocked-amq-utils = "^0"
unblocked-aws-utils = "^0"
unblocked-classifier-utils = "^0"
unblocked-crypto-utils = "^0"
unblocked-embedding-utils = "^0"
unblocked-file-utils = "^0"
unblocked-git-utils = "^0"
unblocked-logging-utils = "^0"
unblocked-opensearch-utils = "^0"
unblocked-partition-utils = "^0"
unblocked-pinecone-utils = "^0"
unblocked-text-utils = "^0"
unblocked-uuid-utils = "^0"

[tool.poetry.group.dev.dependencies]
black = "^24"
pytest = "^8"

[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
