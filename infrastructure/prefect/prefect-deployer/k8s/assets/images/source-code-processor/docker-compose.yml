services:
    embeddings-processor:
        platform: linux/amd64
        build:
            context: ./
            dockerfile: Dockerfile
            args:
                JFROG_PASSWORD: '${JFROG_PASSWORD}'
                ENVIRONMENT: 'dev'
        ports:
            - '8091:8080'
        volumes:
            - ~/.aws:/root/.aws:ro
        env_file:
            - ./local.env
        entrypoint:
          - poetry
          - run
          - python
          - /code/src/source_code_processor/process_source_code.py
