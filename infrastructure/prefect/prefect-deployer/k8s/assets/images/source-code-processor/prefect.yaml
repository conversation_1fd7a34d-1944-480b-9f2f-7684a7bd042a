# Generic metadata about this project
name: flows
prefect-version: 3.0.0

# build section allows you to manage and build docker images
build:
  - prefect.deployments.steps.run_shell_script:
      id: get-commit-hash
      script: git rev-parse --short HEAD
      stream_output: false
  - prefect_docker.deployments.steps.build_docker_image:
      id: build-image
      requires: prefect-docker>=0.4.0
      image_name: "{{ $PREFECT_ECR_REGISTRY }}/prefect-codeingestion"
      tag: "{{ get-commit-hash.stdout }}"
      additional_tags: [ "latest" ]
      dockerfile: Dockerfile
      platform: "linux/amd64"
      buildargs: { "JFROG_PASSWORD": "{{ $JFROG_PASSWORD }}" }

# push section allows you to manage if and how this project is uploaded to remote locations
push:
  - prefect_docker.deployments.steps.push_docker_image:
      requires: prefect-docker>=0.4.0
      image_name: "{{ build-image.image_name }}"
      tag: "{{ build-image.tag }}"
      additional_tags: [ "latest" ]

# pull section allows you to provide instructions for cloning this project in remote locations
pull:
  - prefect.deployments.steps.set_working_directory:
      directory: /code

# the definitions section allows you to define reusable components for your deployments
definitions:
  work_pool: &common_work_pool
    name: "k8s-work-pool"
    # https://docs.prefect.io/v3/deploy/infrastructure-concepts/customize
    # look at k8s-work-pool.json
    job_variables:
      image: "{{ $PREFECT_ECR_REPL_REGISTRY }}/prefect-codeingestion:{{ get-commit-hash.stdout }}"
      finished_job_ttl: 120
      cpu_limit: "3000m"
      cpu_request: "3000m"
      memory_limit: "10240Mi"
      memory_request: "6144Mi"
      service_account_name: "prefect-job"
      image_pull_policy: "Always"


# the deployments section allows you to provide configuration for deploying flows
deployments:
  - name: "source-code-processor"
    schedule: null
    entrypoint: "src/source_code_processor/process_source_code.py:process_source_code_flow"
    enforce_parameter_schema: true
    work_pool: *common_work_pool
