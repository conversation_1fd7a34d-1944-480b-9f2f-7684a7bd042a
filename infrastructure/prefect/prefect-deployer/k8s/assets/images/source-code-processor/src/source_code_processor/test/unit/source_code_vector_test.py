import os
import unittest
from unittest.mock import MagicMock

from embedding_utils.embedding_models import Embeddings
from partition_utils.code_partition import CodePartition

from source_code_processor.source_code_vector import SourceCodeVector


class TestSourceCodeVector(unittest.TestCase):
    def setUp(self):
        self.mock_embedder = MagicMock()
        self.mock_embedder.get_embeddings.return_value = Embeddings(
            dense_vectors=[[0.1, 0.2, 0.3]],
            sparse_vectors=[{"indices": [0.4, 0.5, 0.6]}],
        )
        self.mock_compressor = MagicMock()
        self.mock_compressor.compress_to_bytes.return_value = b"COMPRESSED"

    @staticmethod
    def fixture_path(name) -> str:
        return str(os.path.join(os.path.dirname(__file__), "fixtures", name))

    def test_source_metadata(self):
        filepath = self.fixture_path("hello.txt")
        vector = SourceCodeVector(
            rel_filepath=filepath,
            repo_full_name="org/repo",
            file_metadata={"hash": "95d09f2b10159347eece71399a7e2e907ea3df4f"},
            partition=CodePartition(
                start_line=5,
                end_line=24,
                code="hello  world",
                space_compressed_code="hello world",
            ),
            embedder=self.mock_embedder,
            compressor=self.mock_compressor,
            document_id="doc_id",
            document_source="doc_source",
            document_type="doc_type",
            insight_type="doc_insight_type",
            installation_id="installationId",
            repo_id="repoId",
        )

        self.mock_embedder.get_embeddings.assert_called_once()

        self.assertEqual(vector.file_path, self.fixture_path("hello.txt"))
        self.assertEqual(vector.file_sha, "95d09f2b10159347eece71399a7e2e907ea3df4f")
        self.assertEqual(vector.start_line, 5)
        self.assertEqual(vector.end_line, 24)
        self.assertEqual(vector.document_source, "doc_source")
        self.assertEqual(vector.document_type, "doc_type")
        self.assertEqual(vector.insight_type, "doc_insight_type")
        self.assertEqual(vector.installation_id, "installationId")
        self.assertEqual(vector.repo_id, "repoId")
        self.assertEqual(vector.dense_vector, [0.1, 0.2, 0.3])
        self.assertEqual(vector.sparse_vector, {"indices": [0.4, 0.5, 0.6]})
        self.assertNotEqual(vector.file_content, "hello  world")

        dictionary = vector.to_dict()

        self.mock_compressor.compress_to_base64.assert_not_called()
        self.assertEqual(self.mock_compressor.compress_to_bytes.call_count, 2)
        self.mock_compressor.compress_to_bytes.assert_any_call(f"Repo: org/repo\nFile path: {filepath}\n\nhello  world")
        self.mock_compressor.compress_to_bytes.assert_any_call(self.fixture_path("hello.txt"))

        self.assertEqual(dictionary["values"], [0.1, 0.2, 0.3])
        self.assertEqual(dictionary["sparse_values"], {"indices": [0.4, 0.5, 0.6]})
        self.assertEqual(dictionary["metadata"]["source"], "doc_source")
        self.assertEqual(dictionary["metadata"]["type"], "doc_type")
        self.assertEqual(dictionary["metadata"]["insightType"], "doc_insight_type")
        self.assertEqual(dictionary["metadata"]["installation"], "installationId")
        self.assertEqual(dictionary["metadata"]["group"], "repoId")
        self.assertIsNone(dictionary["metadata"].get("fileContent"))
        self.assertIsNone(dictionary["metadata"].get("filePath"))
        self.assertTrue(dictionary["metadata"]["contentEnc"].strip())
        self.assertTrue(dictionary["metadata"]["filePathEnc"].strip())

        self.assertEqual(vector.get_dense_vector(), [0.1, 0.2, 0.3])
        self.assertEqual(vector.get_sparse_vector(), {"indices": [0.4, 0.5, 0.6]})

    def test_source_metadata_empty_file(self):
        filepath = self.fixture_path("empty.txt")

        with self.assertRaises(AssertionError):
            SourceCodeVector(
                rel_filepath=filepath,
                repo_full_name="org/repo",
                file_metadata={"hash": "e69de29bb2d1d6434b8b29ae775ad8c2e48c5391"},
                partition=CodePartition(
                    start_line=5,
                    end_line=24,
                    code="",
                    space_compressed_code="",
                ),
                embedder=self.mock_embedder,
                compressor=self.mock_compressor,
                document_id="doc_id",
                document_source="doc_source",
                document_type="doc_type",
                insight_type="doc_insight_type",
                installation_id="installationId",
                repo_id="repoId",
            )
