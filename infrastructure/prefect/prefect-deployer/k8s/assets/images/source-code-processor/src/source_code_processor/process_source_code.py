"""
process_source_code.py (refactored)
----------------------------------
• Dataclass `PlatformStore` replaces dicts for store bundles.
• VectorStore + Embedder instantiated per `EmbeddingPlatformConfig`.
• Full Prefect pipeline (clone → embed → upsert → report).
• Includes `process_source_code_flow` and `main` entry‑point.
"""

# ───────── fixed-up import section ───────── #
from __future__ import annotations

import concurrent.futures as cf
import multiprocessing
import json
import os
import tempfile
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from amq_utils.amq_client import AMQClient
from amq_utils.amq_payload_utils import AMQPayloadUtils
from prefect import flow, task
from prefect.artifacts import create_progress_artifact, update_progress_artifact
from prefect.cache_policies import NO_CACHE

import git_utils.git_utils as git
from aws_utils.s3.s3_client import S3Client
from classifier_utils.source_classifier import SourceClassifier
from embedding_secrets import OPENSEARCH_PASSWORD, OPENSEARCH_USERNAME, PINECONE_API_KEY
from embedding_utils.embedding_endpoint.embedding_endpoint import EmbeddingEndpointGenerator
from embedding_utils.embedding_generator import EmbeddingGenerator
from embedding_utils.embedding_model_type import EmbeddingModelType
from file_utils.binary_extractor import BinaryExtractor
from file_utils.file_traversal import FileTraversal
from logging_utils.unblocked_logger import UnblockedLogger
from partition_utils.code_partition import CodePartition
from partition_utils.code_partitioner import CodePartitioner

# ----- OpenSearch helpers ------------------------------------------------ #
from opensearch_utils.opensearch_index_config import (
    FieldConfig,
    FieldType,
    IndexConfig as OpenSearchIndexConfig,
    KNNMethod,
    Engine,
    SpaceType,
    Metric as OSMetric,
)
from opensearch_utils.opensearch_vector_store import OpenSearchVectorStore

# ----- Pinecone helpers -------------------------------------------------- #
from pinecone_utils.pinecone_index_config import IndexConfig as PineconeIndexConfig, Metric as PineconeMetric
from pinecone_utils.pinecone_vector_store import PineconeVectorStore

# ----- Config / domain helpers ------------------------------------------ #
from source_code_processor.amq_constants import ACTIVEMQ_PASSWORD, ACTIVEMQ_PORT, ACTIVEMQ_USERNAME
from source_code_processor.config_loader import (
    ConfigLoader,
    EmbeddingPlatform,
    EmbeddingModel,
    EmbeddingPlatformConfig,
    IncrementalMode,
)
from source_code_processor.path_constants import PROCESS_OUTPUT_DIRECTORY
from source_code_processor.source_classifier import get_source_classifiers
from source_code_processor.source_code_vector import SourceCodeVector
from source_code_processor.timeout import Timeout
from uuid_utils.uuid_utils import UuidUtils

# ----- Vector-store typing ---------------------------------------------- #
from vector_store_utils.vector_store_types import BaseDocument, VectorStore

logger = UnblockedLogger(__name__)


# ───────────────────────────── dataclass ────────────────────────────── #


@dataclass(frozen=True, slots=True)
class PlatformStore:
    """Bundle a vector store with its embedder & metadata."""

    name: str
    platform: EmbeddingPlatform
    model: EmbeddingModel
    vector_store: VectorStore
    embedder: EmbeddingGenerator


# ─────────────────────────── global state ──────────────────────────── #

_partitioner: Optional[CodePartitioner] = None


# ─────────────────────────── helper builders ──────────────────────────── #


def _build_embedder(cfg: ConfigLoader, model_name: str) -> EmbeddingGenerator:
    if EmbeddingModelType.from_string(model_name) != EmbeddingModelType.E5MISTRAL:
        raise ValueError("Only E5Mistral supported right now")
    return EmbeddingEndpointGenerator(cfg.e5_mistral_endpoint_url, timeout=(1, 25))


# ---------------------- platform-specific factories --------------------- #


def _build_pinecone_store(cfg: ConfigLoader, epc: EmbeddingPlatformConfig) -> PineconeVectorStore:
    idx_cfg = PineconeIndexConfig(
        name=epc.index_name,
        dimension=epc.model.dimension,
        metric=PineconeMetric.DOTPRODUCT,
    )
    return PineconeVectorStore(api_key=PINECONE_API_KEY, index_config=idx_cfg)


def _build_opensearch_store(
    cfg: ConfigLoader,
    epc: EmbeddingPlatformConfig,
) -> OpenSearchVectorStore:
    """Factory for an OpenSearchVectorStore tuned for source-code documents."""

    # ---------- KNN method (HNSW / FAISS / cosine-sim) --------------------
    knn = KNNMethod(
        name="hnsw",
        engine=Engine.FAISS,
        space_type=SpaceType.COSINE,
        m=30,
        ef_construction=256,
    )

    # ---------- Explicit field list --------------------------------------
    fields: list[FieldConfig] = [
        # Dense vector & sparse rank-features
        FieldConfig(
            name="embedding",
            type=FieldType.VECTOR,
            dimension=epc.model.dimension,
            method=knn,
        ),
        FieldConfig(name="sparseEmbedding", type=FieldType.RANK_FEATURES),
        # Keyword identifiers / grouping
        FieldConfig(name="id", type=FieldType.KEYWORD),
        FieldConfig(name="namespace", type=FieldType.KEYWORD),
        FieldConfig(name="metadata.group", type=FieldType.KEYWORD),
        FieldConfig(name="metadata.installation", type=FieldType.KEYWORD),
        FieldConfig(name="metadata.org", type=FieldType.KEYWORD),
        # Text-searchable attributes
        FieldConfig(name="metadata.source", type=FieldType.TEXT),
        FieldConfig(name="metadata.type", type=FieldType.TEXT),
        FieldConfig(name="metadata.insightType", type=FieldType.TEXT),
        # Boolean flag
        FieldConfig(name="metadata.private", type=FieldType.BOOLEAN),
        # Non-indexed / large blobs
        FieldConfig(name="metadata.contentEnc", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.fileContent", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.supplementaryContentEnc", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.filePathEnc", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.filePath", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.titleEnc", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.title", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.externalUrlEnc", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.externalUrl", type=FieldType.TEXT, index=False),
        FieldConfig(name="metadata.sourceDocument", type=FieldType.TEXT, index=False),
        # Numeric timestamp
        FieldConfig(name="metadata.date", type=FieldType.INTEGER),
    ]

    # ---------- Index config & store -------------------------------------
    index_cfg = OpenSearchIndexConfig(
        name=epc.index_name,
        metric=OSMetric.COSINE,
        fields=fields,
    )

    return OpenSearchVectorStore(
        hosts=cfg.opensearch_endpoint_url,
        index_name=epc.index_name,
        auth=(OPENSEARCH_USERNAME, OPENSEARCH_PASSWORD),
        index_config=index_cfg,
    )


def _build_vector_store(cfg: ConfigLoader, epc: EmbeddingPlatformConfig) -> VectorStore:
    if epc.platform is EmbeddingPlatform.Pinecone:
        return _build_pinecone_store(cfg, epc)
    if epc.platform is EmbeddingPlatform.OpenSearch:
        return _build_opensearch_store(cfg, epc)
    raise ValueError(epc.platform)


@task(cache_policy=NO_CACHE)
def _init_platform_stores(config: ConfigLoader) -> List[PlatformStore]:
    stores: List[PlatformStore] = []
    for epc in config.embedding_platform_configs:
        emb = _build_embedder(config, epc.model.value)
        vs = _build_vector_store(config, epc)
        stores.append(
            PlatformStore(
                name=epc.index_name,
                platform=epc.platform.value,
                model=epc.model.value,
                vector_store=vs,
                embedder=emb,
            )
        )
    return stores


# ───────────────────────── Prefect tasks ──────────────────────────── #


@task(cache_policy=NO_CACHE)
def send_to_activemq(payload: dict, config: ConfigLoader):
    try:
        host_and_ports = [(host, ACTIVEMQ_PORT) for host in config.activemq_hosts]
        compressed_payload = AMQPayloadUtils.generate_compressed_payload(payload=payload)
        with AMQClient(
            host_and_ports=host_and_ports,
            user=ACTIVEMQ_USERNAME,
            password=ACTIVEMQ_PASSWORD,
            queue=config.activemq_queue,
        ) as client:
            client.send_message(payload=compressed_payload, headers={"persistence": True, "priority": 9})
        logger.info("Payload sent to ActiveMQ successfully.")
    except Exception as e:
        logger.exception(f"Failed to send payload to ActiveMQ: {str(e)}")


@task(cache_policy=NO_CACHE)
def output_results(
    config: ConfigLoader,
    commit_sha: str,
    commit_timestamp: str,
    elapsed_seconds: int,
    files_processed: int,
):
    if os.path.exists(PROCESS_OUTPUT_DIRECTORY):
        process_output_file = os.path.join(PROCESS_OUTPUT_DIRECTORY, "results.json")
        with open(process_output_file, "w") as f:
            f.write(
                json.dumps(
                    {
                        "completedSuccessfully": True,
                        "completedCommitSha": commit_sha,
                        "completedCommitTimestamp": commit_timestamp,
                        "filesProcessed": files_processed,
                        "wallClockRuntimeSeconds": elapsed_seconds,
                    }
                )
            )

    if config.s3_output:
        try:
            s3_client = S3Client()
            s3_client.upload_directory_to_s3_url(local_directory=PROCESS_OUTPUT_DIRECTORY, s3_url=config.s3_output)

            if config.activemq_hosts and config.activemq_queue and config.activemq_event_type:
                payload = {
                    "orgId": config.org_id,
                    "repoId": config.repo_id,
                    "s3OutputPrefix": config.s3_output,
                    "type": config.activemq_event_type,
                }
                send_to_activemq(payload=payload, config=config)
        except Exception as e:
            # Log the error
            logger.exception(f"An error occurred writing output to s3: {str(e)}")


@task(cache_policy=NO_CACHE)
def clone_repo(
    repo_http_clone_url: str,
    repo_http_proxy_url: Optional[str],
    repo_clone_auth: Optional[str],
    since_commit_timestamp: Optional[int],
) -> str:
    opts = ["--no-tags", "--single-branch"]
    if since_commit_timestamp is None:
        opts.append("--depth=1")

    tmp_dir = tempfile.mkdtemp()
    git.clone_with_config(
        repo_http_clone_url=repo_http_clone_url,
        local_dir=tmp_dir,
        multi_options=opts,
        token=repo_clone_auth,
        shallow_since=since_commit_timestamp,
        http_proxy=repo_http_proxy_url,
        https_proxy=repo_http_proxy_url,
    )
    return tmp_dir


def cleanup_file(
    config: ConfigLoader,
    platform_stores: List[PlatformStore],
    document_key_prefix: str,
    repo_id: str,
    rel_file: str,
):
    """Cleanup a single file."""

    for platform_store in platform_stores:
        try:
            with Timeout.seconds(20):
                rel_file_as_uuid = UuidUtils.get_uuid_from_file(repo_id=repo_id, file_path=rel_file)
                prefix = f"{document_key_prefix}{rel_file_as_uuid}|"
                platform_store.vector_store.delete_by_id_prefix(
                    prefix=prefix,
                    namespace=config.embedding_namespace,
                )
        except TimeoutError:
            logger.warning(f"TimeoutError while cleaning up file '{rel_file}'")
        except Exception as e:
            logger.exception(f"Failed to clean up file '{rel_file}'. Exception: {str(e)}")


def process_file(
    config: ConfigLoader,
    root_path: str,
    repo_full_name: str,
    rel_file: str,
    platform_stores: List[PlatformStore],
):
    """Partition a single file, embed once, and upsert to *every* platform store.

    We now create **one** list of `SourceCodeVector` objects (using the first
    platform's embedder) and convert that list to `BaseDocument` for each
    VectorStore implementation.  No per‑store duplication needed.
    VectorStore implementation.  No per‑store duplication needed.
    """
    abs_file = os.path.join(root_path, rel_file)

    try:
        embedder = platform_stores[0].embedder  # assume shared model across stores
        namespace = config.embedding_namespace

        # ── content & metadata ──────────────────────────────────────────── #
        if BinaryExtractor.is_supported(abs_file):
            return
        else:
            file_metadata = git.file_metadata(abs_file)

        # Cleanup existing partitions and vectors
        if config.incremental_mode == IncrementalMode.Incremental:
            for platform_store in platform_stores:
                rel_file_as_uuid = UuidUtils.get_uuid_from_file(repo_id=config.repo_id, file_path=rel_file)
                prefix = f"{config.document_key_prefix}{rel_file_as_uuid}|"
                document_keys = platform_store.vector_store.list_by_id_prefix(
                    prefix=prefix, namespace=namespace, limit=100
                )

                # Happy case: file is unchanged
                if document_keys and document_keys[0] and document_keys[0].startswith(prefix):
                    item_hash = document_keys[0][len(prefix) :].split("|")[0]
                    if item_hash == file_metadata.get("hash"):
                        logger.info(f"Skipping unchanged file: '{rel_file}' [hash={item_hash}]")
                        return

                # Unhappy case: file has changed, need to clean up
                platform_store.vector_store.delete_by_id_prefix(
                    namespace=config.embedding_namespace,
                    prefix=prefix,
                )

        # ── partition file ─────────────────────────────────────────────── #
        parts: List[CodePartition] = _partitioner.split_file(
            file_path=rel_file, file_content=file_metadata.get("content")
        )

        # ── build a single SCV list ────────────────────────────────────── #
        source_code_vectors: List[SourceCodeVector] = []
        for idx, part in enumerate(parts):
            doc_id = (
                f"{config.document_key_prefix}{UuidUtils.get_uuid_from_file(config.repo_id, rel_file)}|"
                f"{file_metadata.get('hash')}|{idx}"
            )
            source_code_vectors.append(
                SourceCodeVector(
                    rel_filepath=rel_file,
                    repo_full_name=repo_full_name,
                    file_metadata=file_metadata,
                    partition=part,
                    document_id=doc_id,
                    document_source=config.document_source,
                    document_type=config.document_type,
                    insight_type=config.insight_type,
                    installation_id=config.installation_id,
                    repo_id=config.repo_id,
                    embedder=embedder,
                )
            )

        # ── helper: convert SCV → BaseDocument once per store ──────────── #
        def _to_base_doc(scv: SourceCodeVector) -> BaseDocument:
            md = scv.to_dict()["metadata"]
            return BaseDocument(
                id=scv.document_id,
                namespace=namespace,
                metadata=md,
                embedding=scv.get_dense_vector(),
                sparse_embedding=scv.get_sparse_vector(),
            )

        base_docs = [_to_base_doc(scv) for scv in source_code_vectors]

        if len(source_code_vectors) > 0:
            # ── upsert the *same* BaseDocuments into every store ───────────── #
            for ps in platform_stores:
                logger.info(
                    f"Upserting {len(source_code_vectors)} vectors to {ps.platform} {ps.model} for file: '{rel_file}'"
                )
                ps.vector_store.add(documents=base_docs, namespace=config.embedding_namespace)
        else:
            logger.warning(f"Unable to create vector metadata for file: '{rel_file}'. Skipping pinecone upsert.")

    except Exception as exc:
        logger.exception("Error processing '%s': %s", rel_file, exc)


@task(cache_policy=NO_CACHE)
def process_repo(
    config: ConfigLoader,
    root_path: str,
    repo_full_name: str,
    platform_stores: list[PlatformStore],
    source_classifiers: dict[str, SourceClassifier],
) -> int:
    """
    Walk the repo, determine files to process (respecting incremental mode),
    then fan-out per-file work in batches of size `max_workers`. Each future
    is awaited with a per-result timeout; no use of Timeout.seconds.
    """
    # Gather candidate files
    rel_files = FileTraversal(
        root_path=root_path,
        min_file_size=256,
        ignore_file=".unblocked.ignore",
        source_classifiers=source_classifiers,
    ).list_text_files()
    logger.info("Found %d files in traversal.", len(rel_files))

    # Incremental-mode diff / cleanup
    if config.incremental_mode == IncrementalMode.Incremental:
        changed_rel_files = git.git_files_since_timestamp(
            repo_dir=root_path,
            since_unix_timestamp=str(int(config.incremental_since_timestamp) + 1),
        )

        # delete vectors for files removed since last run
        cleanup_rel_files = set(changed_rel_files).difference(rel_files)
        logger.info("Cleaning up %d removed files …", len(cleanup_rel_files))
        for rel_file in cleanup_rel_files:
            cleanup_file(
                config=config,
                platform_stores=platform_stores,
                document_key_prefix=config.document_key_prefix,
                repo_id=config.repo_id,
                rel_file=rel_file,
            )

        # process only files that changed
        rel_files = list(changed_rel_files.intersection(rel_files))

    total_files = len(rel_files)
    if not total_files:
        logger.info("Nothing to do — exiting early.")
        return 0
    else:
        logger.info("Processing %d files ...", total_files)

    # Progress artifact
    progress_artifact_id = create_progress_artifact(
        key="processing-progress",
        progress=0,
        description="Processing repository files",
    )
    completed = 0

    # Concurrency settings
    cpu_count = multiprocessing.cpu_count()
    threads_per_core = 5
    max_workers = min(config.max_threads, cpu_count * threads_per_core)

    # Per-file result timeout (seconds). Add a small cushion for Future.result.
    file_timeout = 30 * max(1, len(platform_stores))
    future_wait_timeout = file_timeout + 5

    logger.info(
        "Parallelizing in batches of %d worker threads. Per-result timeout = %d s.",
        max_workers,
        file_timeout,
    )

    def _batched(seq, size):
        for i in range(0, len(seq), size):
            yield seq[i : i + size]

    def _worker(worker_rel_file: str) -> tuple[str, float]:
        # No cooperative Timeout.seconds here—just do the work and measure.
        start_exec = time.perf_counter()
        try:
            process_file(
                config=config,
                root_path=root_path,
                repo_full_name=repo_full_name,
                rel_file=worker_rel_file,
                platform_stores=platform_stores,
            )
        except Exception as worker_exc:
            logger.exception("Error processing '%s': %s", worker_rel_file, worker_exc)
        finally:
            elapsed = time.perf_counter() - start_exec
        return worker_rel_file, elapsed

    for batch_idx, batch_files in enumerate(_batched(rel_files, max_workers), start=1):
        # Fresh executor each batch so timed-out tasks can't block future batches.
        with cf.ThreadPoolExecutor(
            max_workers=min(len(batch_files), max_workers),
            thread_name_prefix="proc-file",
        ) as pool:
            futures = [pool.submit(_worker, rf) for rf in batch_files]

            # Collect results with a per-result timeout
            for rf, fut in zip(batch_files, futures):
                try:
                    rel_file, run_s = fut.result(timeout=future_wait_timeout)
                except cf.TimeoutError:
                    logger.warning(
                        "Per-result timeout (>%ds) for '%s'. Skipping and continuing.",
                        future_wait_timeout,
                        rf,
                    )
                    continue
                except Exception as exc:
                    logger.exception("Worker raised: %s", exc)
                    continue

                completed += 1
                percentage = round(100 * completed / total_files, 1)
                logger.info("Processed file in %.3fs: '%s' (%s%%)", run_s, rel_file, percentage)
                update_progress_artifact(
                    artifact_id=progress_artifact_id,
                    progress=percentage,
                )

        logger.info("Finished batch %d containing %d file(s).", batch_idx, len(batch_files))

    logger.info("Processed %d / %d files.", completed, total_files)
    return total_files


# ───────────────────────── Main process task ───────────────────────── #


@task(cache_policy=NO_CACHE)
def process_source_code_data(config: ConfigLoader, platform_stores: List[PlatformStore]):
    start_time = time.time()

    since_ts = config.incremental_since_timestamp if config.incremental_mode == IncrementalMode.Incremental else None
    repo_dir = clone_repo(
        repo_http_clone_url=config.repo_http_clone_url,
        repo_http_proxy_url=config.repo_http_proxy_url,
        repo_clone_auth=config.repo_clone_auth,
        since_commit_timestamp=since_ts,
    )

    if git.git_is_repo_empty(repo_dir=repo_dir):
        logger.info(f"Repository is empty, exiting ...")
        elapsed_seconds = round(time.time() - start_time)
        output_results(
            config=config,
            commit_sha="0000000000000000000000000000000000000000",
            commit_timestamp="0",
            elapsed_seconds=elapsed_seconds,
            files_processed=0,
        )
        return

    commit_sha, commit_timestamp = git.git_head(repo_dir)
    files_processed = process_repo(
        config=config,
        root_path=repo_dir,
        repo_full_name=config.repo_full_name,
        platform_stores=platform_stores,
        source_classifiers=get_source_classifiers(llm_endpoint_url=config.llm_endpoint_url),
    )

    elapsed_seconds = round(time.time() - start_time)

    logger.info(f"Done, processed {files_processed} files.")
    output_results(
        config=config,
        commit_sha=commit_sha,
        commit_timestamp=commit_timestamp,
        elapsed_seconds=elapsed_seconds,
        files_processed=files_processed,
    )


# ───────────────────────────── Flow & main ─────────────────────────── #


@flow(name="process_source_code_flow", log_prints=True)
def process_source_code_flow(batch_job_environment: Dict[str, Any]):
    os.environ["BATCH_JOB_ENVIRONMENT"] = json.dumps(batch_job_environment)
    config = ConfigLoader()
    global _partitioner
    _partitioner = CodePartitioner()

    platform_stores = _init_platform_stores(config)
    process_source_code_data(config=config, platform_stores=platform_stores)


if __name__ == "__main__":
    process_source_code_flow(batch_job_environment={})
