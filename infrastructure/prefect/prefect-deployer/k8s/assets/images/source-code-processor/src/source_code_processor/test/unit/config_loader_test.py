import os
import json
import base64
import gzip
import unittest
from unittest.mock import patch

from source_code_processor.config_loader import (
    ConfigLoader,
    EmbeddingPlatform,
    EmbeddingModel,
    EmbeddingPlatformConfig,
)


# ------------------------------------------------------------------ #
# Helpers
# ------------------------------------------------------------------ #
def make_b64(obj) -> str:
    """
    Encode the list‑of‑dicts payload exactly the way
    `Compression.decompress_from_base64()` expects:
       gzip‑compress → base64‑encode.
    """
    gz_bytes = gzip.compress(json.dumps(obj).encode("utf-8"))
    return base64.b64encode(gz_bytes).decode()


# ------------------------------------------------------------------ #
class TestConfigLoader(unittest.TestCase):
    # -------------------------------------------------------------- #
    def test_init(self):
        """Loader populates all required + optional fields, including HTTP proxy and max_threads."""
        platform_cfg = [
            {
                "indexName": "documents-e5mistral",
                "model": "E5Mistral",
                "platform": "Pinecone",
            },
            {
                "indexName": "documents-e5mistral",
                "model": "E5Mistral",
                "platform": "OpenSearch",
            },
        ]

        with patch.dict(
            os.environ,
            {
                "PROCESS_DOCUMENT_SOURCE": "1",
                "PROCESS_DOCUMENT_TYPE": "1",
                "PROCESS_DOCUMENT_KEY_PREFIX": (
                    "c9f0f895-fb98-3b91-99f5-1fd0297e236d|11111111-1111-1111-1111-111111111111||"
                ),
                "PROCESS_EMBEDDING_PLATFORM_CONFIGS": make_b64(platform_cfg),
                "PROCESS_INCREMENTAL_MODE": "Incremental",
                "PROCESS_INCREMENTAL_SINCE_COMMIT_SHA": "f5adee1865f3de2c97471c78ca4423479e021129",
                "PROCESS_INCREMENTAL_SINCE_TIMESTAMP": "1704668900",
                "PROCESS_INSIGHT_TYPE": "8",
                "PROCESS_REPO_FULL_NAME": "Company-Name/repo_name",
                "PROCESS_REPO_HTTP_CLONE_URL": ("https://x-access-token:<EMAIL>/Company-Name/repo_name.git"),
                "PROCESS_REPO_ID": "62f82cb6-05f9-445c-a9d3-69253212ff30",
                "PROCESS_INSTALLATION_ID": "aa6ca0b3-81d2-42e1-95f6-58fed29c691b",
                "PROCESS_ORG_ID": "911d5b98-513c-46fa-b6aa-dd0bd32aca8d",
                "PROCESS_EMBEDDING_NAMESPACE": "911d5b98-513c-46fa-b6aa-dd0bd32aca8d",
                "PROCESS_ACTIVEMQ_HOSTS": "host1,host2,host3",
                "PROCESS_REPO_HTTP_PROXY_URL": "http://proxy.internal:8080",
                "PROCESS_MAX_THREADS": "16",  # NEW
            },
            clear=True,
        ):
            cfg = ConfigLoader()

            # Core assertions
            self.assertEqual(cfg.document_source, "1")
            self.assertEqual(cfg.document_type, "1")
            self.assertEqual(
                cfg.embedding_platform_configs,
                [
                    EmbeddingPlatformConfig(
                        platform=EmbeddingPlatform.Pinecone,
                        index_name="documents-e5mistral",
                        model=EmbeddingModel.E5MISTRAL,
                    ),
                    EmbeddingPlatformConfig(
                        platform=EmbeddingPlatform.OpenSearch,
                        index_name="documents-e5mistral",
                        model=EmbeddingModel.E5MISTRAL,
                    ),
                ],
            )
            self.assertEqual(cfg.activemq_hosts, ["host1", "host2", "host3"])
            self.assertEqual(cfg.incremental_mode, "Incremental")
            self.assertEqual(cfg.incremental_since_commit_sha, "f5adee1865f3de2c97471c78ca4423479e021129")
            self.assertEqual(cfg.incremental_since_timestamp, "1704668900")
            self.assertEqual(cfg.insight_type, "8")
            self.assertEqual(cfg.repo_full_name, "Company-Name/repo_name")
            self.assertEqual(
                cfg.repo_http_clone_url,
                "https://x-access-token:<EMAIL>/Company-Name/repo_name.git",
            )
            self.assertEqual(cfg.repo_id, "62f82cb6-05f9-445c-a9d3-69253212ff30")
            self.assertEqual(cfg.installation_id, "aa6ca0b3-81d2-42e1-95f6-58fed29c691b")
            self.assertEqual(cfg.org_id, "911d5b98-513c-46fa-b6aa-dd0bd32aca8d")
            self.assertEqual(cfg.embedding_namespace, "911d5b98-513c-46fa-b6aa-dd0bd32aca8d")

            # New assertions
            self.assertEqual(cfg.repo_http_proxy_url, "http://proxy.internal:8080")
            self.assertEqual(cfg.max_threads, 16)

    # -------------------------------------------------------------- #
    def test_missing_all_environment_variables(self):
        """Required‑field validation unaffected by optional HTTP proxy or max_threads."""
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError) as ctx:
                ConfigLoader()

        msg = str(ctx.exception)
        self.assertTrue(msg.startswith("Missing env vars"))

        expected_vars = {
            "PROCESS_DOCUMENT_KEY_PREFIX",
            "PROCESS_DOCUMENT_SOURCE",
            "PROCESS_DOCUMENT_TYPE",
            "PROCESS_EMBEDDING_PLATFORM_CONFIGS",
            "PROCESS_INSIGHT_TYPE",
            "PROCESS_REPO_FULL_NAME",
            "PROCESS_REPO_HTTP_CLONE_URL",
            "PROCESS_REPO_ID",
            "PROCESS_ORG_ID",
            "PROCESS_EMBEDDING_NAMESPACE",
        }
        for var in expected_vars:
            self.assertIn(var, msg)

        # Optional field should NOT trigger error
        self.assertNotIn("PROCESS_MAX_THREADS", msg)

    # -------------------------------------------------------------- #
    def test_blank_environment_variable(self):
        """Blank optional vars (incl. proxy and max_threads) are allowed; blank required → error."""
        with patch.dict(
            os.environ,
            {
                "PROCESS_DOCUMENT_KEY_PREFIX": "prefix",
                "PROCESS_DOCUMENT_SOURCE": "source",
                "PROCESS_DOCUMENT_TYPE": "type",
                "PROCESS_EMBEDDING_PLATFORM_CONFIGS": "",  # blank required → error
                "PROCESS_INCREMENTAL_MODE": "Full",
                "PROCESS_INSIGHT_TYPE": "insight-type",
                "PROCESS_REPO_FULL_NAME": "test/repo",
                "PROCESS_REPO_HTTP_CLONE_URL": "https://github.com/test/repo.git",
                "PROCESS_REPO_ID": "123",
                "PROCESS_ORG_ID": "",
                "PROCESS_EMBEDDING_NAMESPACE": "",
                "PROCESS_REPO_HTTP_PROXY_URL": "",
                "PROCESS_MAX_THREADS": "",
            },
            clear=True,
        ):
            with self.assertRaises(ValueError) as ctx:
                ConfigLoader()

        msg = str(ctx.exception)
        missing = {
            "PROCESS_EMBEDDING_PLATFORM_CONFIGS",
            "PROCESS_ORG_ID",
            "PROCESS_EMBEDDING_NAMESPACE",
        }
        for var in missing:
            self.assertIn(var, msg)


if __name__ == "__main__":
    unittest.main()
