import os

from aws_utils.aws_secrets_utils import get_aws_secret

EMBEDDING_CONTENT_ENCRYPTION_KEY = get_aws_secret(
    secret_name="EMBEDDING_CONTENT_ENCRYPTION_KEY", default=os.environ.get("EMBEDDING_CONTENT_ENCRYPTION_KEY")
)

PINECONE_API_KEY = get_aws_secret(
    secret_name="pinecone-serverless-api-key",
    default=os.environ.get("PINECONE_API_KEY", default=os.environ.get("PINECONE_API_ENV_KEY")),
)

OPENSEARCH_PASSWORD = get_aws_secret(
    secret_name="opensearch-master-password", default=os.environ.get("OPENSEARCH_PASSWORD"), field="password"
)

OPENSEARCH_USERNAME = get_aws_secret(
    secret_name="opensearch-master-password",
    default=os.environ.get("OPENSEARCH_USERNAME", default="unblocked"),
    field="username",
)
