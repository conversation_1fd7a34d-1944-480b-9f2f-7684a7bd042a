import os, json, base64, gzip, unittest
from unittest.mock import patch

from source_code_processor.config_loader import (
    ConfigLoader,
    EmbeddingPlatform,
    EmbeddingModel,
    EmbeddingPlatformConfig,
    IncrementalMode,
)


# ───────────────────────── helpers ────────────────────────── #
def make_b64(obj) -> str:
    gz_bytes = gzip.compress(json.dumps(obj).encode())
    return base64.b64encode(gz_bytes).decode()


def batch_json(**kv):
    """Convenience: build & json-dump the dict for BATCH_JOB_ENVIRONMENT."""
    return json.dumps(kv)


PLATFORM_CFG = [
    {"indexName": "documents-e5mistral", "model": "E5Mistral", "platform": "Pinecone"},
    {"indexName": "documents-e5mistral", "model": "E5Mistral", "platform": "OpenSearch"},
]


# ──────────────────────── test cases ──────────────────────── #
class TestConfigLoaderBatchEnv(unittest.TestCase):
    # -------------------------------------------------------- #
    def test_batch_env_happy_path(self):
        """Everything supplied via BATCH_JOB_ENVIRONMENT only, including max_threads."""
        payload = batch_json(
            PROCESS_DOCUMENT_KEY_PREFIX="key|prefix",
            PROCESS_DOCUMENT_SOURCE="git",
            PROCESS_DOCUMENT_TYPE="source-code",
            PROCESS_EMBEDDING_PLATFORM_CONFIGS=make_b64(PLATFORM_CFG),
            PROCESS_EMBEDDING_NAMESPACE="namespace-id",
            PROCESS_INCREMENTAL_MODE="Incremental",
            PROCESS_INSIGHT_TYPE="8",
            PROCESS_REPO_FULL_NAME="org/repo",
            PROCESS_REPO_HTTP_CLONE_URL="https://github.com/org/repo.git",
            PROCESS_REPO_ID="abc-123",
            PROCESS_ORG_ID="org-id",
            PROCESS_REPO_HTTP_PROXY_URL="http://proxy.internal:8080",
            PROCESS_MAX_THREADS="12",  # NEW: override default
        )

        with patch.dict(os.environ, {"BATCH_JOB_ENVIRONMENT": payload}, clear=True):
            cfg = ConfigLoader()

        # Required ones wired up
        self.assertEqual(cfg.document_source, "git")
        self.assertEqual(cfg.incremental_mode, IncrementalMode.Incremental)
        self.assertEqual(
            cfg.embedding_platform_configs,
            [
                EmbeddingPlatformConfig(EmbeddingPlatform.Pinecone, "documents-e5mistral", EmbeddingModel.E5MISTRAL),
                EmbeddingPlatformConfig(EmbeddingPlatform.OpenSearch, "documents-e5mistral", EmbeddingModel.E5MISTRAL),
            ],
        )
        self.assertEqual(cfg.repo_http_proxy_url, "http://proxy.internal:8080")
        self.assertEqual(cfg.max_threads, 12)

    # -------------------------------------------------------- #
    def test_batch_env_null_optionals(self):
        """Optional keys set to null → None or default."""
        payload = batch_json(
            PROCESS_DOCUMENT_KEY_PREFIX="key|prefix",
            PROCESS_DOCUMENT_SOURCE="git",
            PROCESS_DOCUMENT_TYPE="source-code",
            PROCESS_EMBEDDING_PLATFORM_CONFIGS=make_b64(PLATFORM_CFG),
            PROCESS_EMBEDDING_NAMESPACE="namespace-id",
            PROCESS_INCREMENTAL_MODE="Full",
            PROCESS_INSIGHT_TYPE="8",
            PROCESS_REPO_FULL_NAME="org/repo",
            PROCESS_REPO_HTTP_CLONE_URL="https://github.com/org/repo.git",
            PROCESS_REPO_ID="abc-123",
            PROCESS_ORG_ID="org-id",
            PROCESS_REPO_HTTP_PROXY_URL=None,
            PROCESS_ACTIVEMQ_HOSTS=None,
            PROCESS_MAX_THREADS=None,  # NEW: treated as default
        )

        with patch.dict(os.environ, {"BATCH_JOB_ENVIRONMENT": payload}, clear=True):
            cfg = ConfigLoader()

        self.assertIsNone(cfg.repo_http_proxy_url)
        self.assertIsNone(cfg.activemq_hosts)
        self.assertEqual(cfg.max_threads, 1)

    # -------------------------------------------------------- #
    def test_batch_env_null_required_raises(self):
        """A required key present but null still triggers validation."""
        payload = batch_json(
            PROCESS_DOCUMENT_KEY_PREFIX=None,  # required but null
            PROCESS_DOCUMENT_SOURCE="git",
            PROCESS_DOCUMENT_TYPE="source-code",
            PROCESS_EMBEDDING_PLATFORM_CONFIGS=make_b64(PLATFORM_CFG),
            PROCESS_EMBEDDING_NAMESPACE="namespace-id",
            PROCESS_INCREMENTAL_MODE="Full",
            PROCESS_INSIGHT_TYPE="8",
            PROCESS_REPO_FULL_NAME="org/repo",
            PROCESS_REPO_HTTP_CLONE_URL="https://github.com/org/repo.git",
            PROCESS_REPO_ID="abc-123",
            PROCESS_ORG_ID="org-id",
            PROCESS_MAX_THREADS="6",  # present, shouldn't interfere
        )

        with patch.dict(os.environ, {"BATCH_JOB_ENVIRONMENT": payload}, clear=True):
            with self.assertRaises(ValueError) as ctx:
                ConfigLoader()

        self.assertIn("PROCESS_DOCUMENT_KEY_PREFIX", str(ctx.exception))


if __name__ == "__main__":
    unittest.main()
