import signal


# NOTE: The signal-based timeout pattern only works reliably on Unix-like systems
# and ONLY in the **main thread** of the process. In CPython, installing signal
# handlers (signal.signal) is restricted to the main thread, and signals are
# delivered to the main thread only. Using this class in a worker thread will
# raise: ValueError("signal only works in main thread").
#
# On Windows, SIGALRM does not exist at all, so this approach will fail there.


class Timeout:
    class TimeoutError(Exception):
        pass

    def __init__(self, seconds=1):
        self.seconds = seconds
        # THREADS: No per-thread isolation. SIGALRM is process-wide, so even if
        # you set an alarm here, it affects the entire process (and is delivered
        # to the main thread), potentially interrupting unrelated code running
        # concurrently in other threads.

    @classmethod
    def seconds(cls, seconds: int):
        return cls(seconds=seconds)

    @classmethod
    def minutes(cls, minutes: int):
        return cls(seconds=minutes * 60)

    def handle_timeout(self, signum, frame):
        # This handler runs in the main thread when SIGALRM is delivered.
        # If the code you want to time out is running in a worker thread,
        # this exception won't be raised in that thread; it will be raised
        # in whatever code the main thread happens to be executing.
        raise self.TimeoutError("Timeout exceeded")

    def __enter__(self):
        # THREADS: The next line will raise in any thread other than the main thread:
        # ValueError: signal only works in main thread
        signal.signal(signal.SIGALRM, self.handle_timeout)

        # THREADS: signal.alarm() sets a **process-wide** real-time timer. It is not
        # thread-local. If multiple threads try to use this context manager at once,
        # they'll stomp on each other (last alarm wins) and the signal will still be
        # delivered to the main thread, not to the worker thread that started it.
        signal.alarm(self.seconds)

        # Robustness: This replaces any previous SIGALRM handler but does not save
        # and restore it. If other code in your process uses SIGALRM, this can break it.

    def __exit__(self, type, value, traceback):
        # Cancels any pending alarm, but there is a race: the alarm may have fired
        # just before this call, meaning the handler may already have run.
        signal.alarm(0)
