apiVersion: batch/v1
kind: Job
metadata:
  name: rashin-test
  namespace: prefect
spec:
  template:
    metadata:
      labels:
        job-name: rashin-test
    spec:
      containers:
        - name: prefect-job
          image: 129540529571.dkr.ecr.us-west-2.amazonaws.com/prefect:033ad6d
          imagePullPolicy: IfNotPresent
          args:
            - prefect
            - flow-run
            - execute
          env:
            - name: PREFECT_DEBUG_MODE
              value: "False"
            - name: PREFECT_API_URL
              value: http://prefect-server:4200/api
            - name: PREFECT_API_ENABLE_HTTP2
              value: "True"
            - name: PREFECT_SERVER_EPHEMERAL_ENABLED
              value: "True"
            - name: PREFECT_WORKER_QUERY_SECONDS
              value: "5.0"
            - name: PREFECT_WORKER_PREFETCH_SECONDS
              value: "10.0"
            - name: PREFECT__FLOW_RUN_ID
              value: d604ae49-9464-46c2-8090-51d7936106a8
          envFrom:
            - secretRef:
                name: prefect-secrets-env
                optional: true
          resources:
            limits:
              cpu: 3000m
              memory: 8192Mi
            requests:
              cpu: 3000m
              memory: 8192Mi
          volumeMounts:
            - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              name: kube-api-access-rnzfp
              readOnly: true
      restartPolicy: Never
      volumes:
        - name: kube-api-access-rnzfp
          projected:
            defaultMode: 420
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  items:
                    - key: ca.crt
                      path: ca.crt
                  name: kube-root-ca.crt
              - downwardAPI:
                  items:
                    - fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
                      path: namespace
  backoffLimit: 4
