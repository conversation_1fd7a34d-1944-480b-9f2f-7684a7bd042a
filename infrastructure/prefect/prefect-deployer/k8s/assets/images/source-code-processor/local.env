# ───────── execution environment ─────────
A<PERSON>_PROFILE=dev
ENVIRONMENT=prod

# ───────── ingestion job parameters ──────
PROCESS_DOCUMENT_SOURCE=1
PROCESS_DOCUMENT_TYPE=1
PROCESS_INSIGHT_TYPE=8

# Identifiers
PROCESS_REPO_HTTP_CLONE_URL=https://github.com/NextChapterSoftware/raycast.git
PROCESS_REPO_HTTP_PROXY_URL=http://workday.proxy.prod.getunblocked.com:3128
PROCESS_REPO_FULL_NAME=cancelself/cancelself.github.io
PROCESS_REPO_ID=36ad35ad-fb6c-46e5-9018-d8cf7c8b6c7d
PROCESS_ORG_ID=b0549615-71a0-4d50-a32e-7a863d862fd0

# ActiveMQ
PROCESS_ACTIVEMQ_HOSTS=b-3510bc32-72cb-4aee-9bf8-8d7a04f2f272-1.mq.us-west-2.amazonaws.com,b-3510bc32-72cb-4aee-9bf8-8d7a04f2f272-2.mq.us-west-2.amazonaws.com
PROCESS_ACTIVEMQ_QUEUE=source_code_events
PROCESS_ACTIVEMQ_EVENT_TYPE=CodeIngestionCompletionEvent

# Output
PROCESS_S3_OUTPUT=s3://code-ingestion-data-pipeline-sandbox-dev-us-west-2/009c8f4a-2a67-46e8-88f4-35b78157f38f

# Incremental / full mode
PROCESS_INCREMENTAL_MODE=Full
PROCESS_INCREMENTAL_SINCE_TIMESTAMP=1669337779
PROCESS_INCREMENTAL_SINCE_COMMIT_SHA=67c7281fa5518e3d5da9dbc0d71c871efc3e3f68

# Document-key prefix (repo|org|…||)
PROCESS_DOCUMENT_KEY_PREFIX=c9f0f895-fb98-3b91-99f5-1fd0297e236d|36ad35ad-fb6c-46e5-9018-d8cf7c8b6c7d||

# ───────── embedding back-ends ───────────
PROCESS_EMBEDDING_NAMESPACE=b0549615-71a0-4d50-a32e-7a863d862fd0
# Only Pinecone / E5-Mistral for now. Value is *Base-64* of a JSON list:
#   [{"platform":"Pinecone","indexName":"documents-e5mistral","model":"E5Mistral"}]
PROCESS_EMBEDDING_PLATFORM_CONFIGS=H4sIADLbGmgC/4uuVsrMS0mt8EvMTVWyUlBKyU8uzU3NKynWTTXNzSwuKUrMUdJRUMrNT0nNAcm7mvoiRAtyEkvS8otyQRIBmXmpyfl5qUq1sQDHOCYZVAAAAA==
#   [{"platform":"Pinecone","indexName":"documents-e5mistral","model":"E5Mistral"}, {"platform":"OpenSearch","indexName":"documents-e5mistral","model":"E5Mistral"}]
# PROCESS_EMBEDDING_PLATFORM_CONFIGS=H4sIAJedG2gC/4uuVsrMS0mt8EvMTVWyUlBKyU8uzU3NKynWTTXNzSwuKUrMUdJRUMrNT0nNAcm7mvoiRAtyEkvS8otyQRIBmXmpyfl5qUq1OgrUMtO/IDUvODWxKDlDqTYWAEu3+yqqAAAA

# (Optional) OpenSearch endpoint if you later add an OpenSearch platform config
PROCESS_OPENSEARCH_ENDPOINT_URL=https://vpc-unblocked-5ea7ebkg7ap253xp77hvtk3mkm.us-west-2.es.amazonaws.com

# LLM / embedding endpoints
PROCESS_LLM_ENDPOINT_URL=https://ml.alb.prod.gcp.getunblocked.com/api/ml/transformers/llama-31-8B
PROCESS_E5_MISTRAL_ENDPOINT_URL=https://ml.alb.prod.gcp.getunblocked.com/api/ml/embeddings/e5-mistral

# Max Threads
PROCESS_MAX_THREADS=15
