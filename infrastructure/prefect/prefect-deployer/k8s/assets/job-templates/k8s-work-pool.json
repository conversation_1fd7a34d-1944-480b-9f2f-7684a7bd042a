{"variables": {"type": "object", "properties": {"env": {"type": "object", "title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "additionalProperties": {"anyOf": [{"type": "string"}, {"type": "null"}]}}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "default": null, "description": "Name given to infrastructure created by a worker."}, "image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image", "default": null, "examples": ["docker.io/prefecthq/prefect:3-latest"], "description": "The image reference of a container image to use for created jobs. If not set, the latest Prefect image will be used."}, "labels": {"type": "object", "title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "additionalProperties": {"type": "string"}}, "command": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Command", "default": null, "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker."}, "namespace": {"type": "string", "title": "Namespace", "default": "prefect", "description": "The Kubernetes namespace to create jobs within."}, "stream_output": {"type": "boolean", "title": "Stream Output", "default": true, "description": "If set, output will be streamed from the job to local standard output."}, "cluster_config": {"anyOf": [{"$ref": "#/definitions/KubernetesClusterConfig"}, {"type": "null"}], "default": null, "description": "The Kubernetes cluster config to use for job creation."}, "finished_job_ttl": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Finished Job TTL", "default": null, "description": "The number of seconds to retain jobs after completion. If set, finished jobs will be cleaned up by Kubernetes after the given delay. If not set, jobs will be retained indefinitely."}, "image_pull_policy": {"enum": ["IfNotPresent", "Always", "Never"], "type": "string", "title": "Image Pull Policy", "default": "IfNotPresent", "description": "The Kubernetes image pull policy to use for job containers."}, "image_pull_secrets": {"type": "array", "title": "Image Pull Secrets", "description": "A list of Kubernetes secrets to use for pulling images.", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}, "default": []}, "service_account_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Service Account Name", "default": null, "description": "The Kubernetes service account to use for job creation."}, "job_watch_timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Job Watch Timeout Seconds", "default": null, "description": "Number of seconds to wait for each event emitted by a job before timing out. If not set, the worker will wait for each event indefinitely."}, "pod_watch_timeout_seconds": {"type": "integer", "title": "Pod Watch Timeout Seconds", "default": 500, "description": "Number of seconds to watch for pod creation before timing out."}, "cpu_limit": {"type": "string", "title": "CPU Limit", "default": "1000m", "description": "The maximum amount of CPU that the job can use."}, "memory_limit": {"type": "string", "title": "Memory Limit", "default": "1024Mi", "description": "The maximum amount of memory that the job can use."}, "cpu_request": {"type": "string", "title": "CPU Request", "default": "500m", "description": "The minimum amount of CPU that the job is guaranteed."}, "memory_request": {"type": "string", "title": "Memory Request", "default": "512Mi", "description": "The minimum amount of memory that the job is guaranteed."}, "cpu_type": {"type": "string", "title": "CPU Type", "default": "standard", "description": "The type of CPU to use for the job (e.g., 'standard', 'high-performance')."}, "env_secret_ref_name": {"type": "string", "title": "Environment Secret Reference Name", "default": "prefect-secrets-env", "description": "The name of the Kubernetes secret from which environment variables should be loaded."}}, "definitions": {"KubernetesClusterConfig": {"type": "object", "title": "KubernetesClusterConfig", "required": ["config", "context_name"], "properties": {"config": {"type": "object", "title": "Config", "description": "The entire contents of a kubectl config file."}, "context_name": {"type": "string", "title": "Context Name", "description": "The name of the kubectl context to use."}}, "description": "Stores configuration for interaction with Kubernetes clusters.\n\nSee `from_file` for creation.", "secret_fields": [], "block_type_slug": "kubernetes-cluster-config", "block_schema_references": {}}}, "description": "Default variables for the Kubernetes worker.\n\nThe schema for this class is used to populate the `variables` section of the default\nbase job template."}, "job_configuration": {"env": "{{ env }}", "name": "{{ name }}", "labels": "{{ labels }}", "command": "{{ command }}", "namespace": "{{ namespace }}", "job_manifest": {"apiVersion": "batch/v1", "kind": "Job", "metadata": {"labels": "{{ labels }}", "namespace": "{{ namespace }}", "generateName": "{{ name }}-"}, "spec": {"template": {"spec": {"containers": [{"env": "{{ env }}", "args": "{{ command }}", "name": "prefect-job", "image": "{{ image }}", "imagePullPolicy": "{{ image_pull_policy }}", "resources": {"limits": {"cpu": "{{ cpu_limit }}", "memory": "{{ memory_limit }}"}, "requests": {"cpu": "{{ cpu_request }}", "memory": "{{ memory_request }}"}}, "envFrom": [{"secretRef": {"name": "{{ env_secret_ref_name }}", "optional": true}}]}], "imagePullSecrets": "{{ image_pull_secrets }}", "completions": 1, "parallelism": 1, "restartPolicy": "Never", "serviceAccountName": "{{ service_account_name }}", "tolerations": [{"key": "node-class", "operator": "Equal", "value": "worker", "effect": "NoSchedule"}], "priorityClassName": "high-priority", "affinity": {"nodeAffinity": {"requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "node-class", "operator": "In", "values": ["worker"]}]}]}}}}}, "backoffLimit": 0, "ttlSecondsAfterFinished": "{{ finished_job_ttl }}"}}, "stream_output": "{{ stream_output }}", "cluster_config": "{{ cluster_config }}", "job_watch_timeout_seconds": "{{ job_watch_timeout_seconds }}", "pod_watch_timeout_seconds": "{{ pod_watch_timeout_seconds }}"}}