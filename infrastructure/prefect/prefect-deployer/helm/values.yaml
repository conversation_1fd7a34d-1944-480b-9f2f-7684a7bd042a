worker:
  # https://github.com/PrefectHQ/prefect-helm/blob/main/charts/prefect-worker/values.yaml
  apiConfig: server

  serverApiConfig:
    # If the prefect server is located external to this cluster, set a fully qualified domain name as the apiUrl
    # If the prefect server pod is deployed to this cluster, use the cluster DNS endpoint: http://<prefect-server-service-name>.<namespace>.svc.cluster.local:<prefect-server-port>/api
    # -- prefect API url (PREFECT_API_URL)
    apiUrl: http://prefect-server.prefect.svc.cluster.local:4200/api

  config:
    workPool: k8s-work-pool

  autoscaling:
    # -- enable autoscaling for the worker
    enabled: true
    # -- minimum number of replicas to scale down to
    minReplicas: 1
    # -- maximum number of replicas to scale up to
    maxReplicas: 5
    # -- target CPU utilization percentage for scaling the worker
    targetCPUUtilizationPercentage: 80
    # -- target memory utilization percentage for scaling the worker
    targetMemoryUtilizationPercentage: 80
