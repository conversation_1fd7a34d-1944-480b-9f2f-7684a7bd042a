deploy-prefect-k8s-dev:
	cd k8s && ansible-playbook k8s-prefect-deploy-playbook.yml -e "env=dev"
.PHONY: deploy-prefect-k8s-dev

deploy-prefect-k8s-prod:
	cd k8s && ansible-playbook k8s-prefect-deploy-playbook.yml -e "env=prod"
.PHONY: deploy-prefect-k8s-prod

deploy-prefect-k8s-images-dev:
	cd k8s && ansible-playbook k8s-prefect-deploy-images-playbook.yml -e "env=dev prefectEcrRegistry=877923746456.dkr.ecr.us-west-2.amazonaws.com prefectEcrReplRegistry=129540529571.dkr.ecr.us-west-2.amazonaws.com"
.PHONY: deploy-prefect-k8s-images-dev

deploy-prefect-k8s-images-prod:
	cd k8s && ansible-playbook k8s-prefect-deploy-images-playbook.yml -e "env=prod prefectEcrRegistry=877923746456.dkr.ecr.us-west-2.amazonaws.com prefectEcrReplRegistry=029574882031.dkr.ecr.us-west-2.amazonaws.com"
.PHONY: deploy-prefect-k8s-images-prod
