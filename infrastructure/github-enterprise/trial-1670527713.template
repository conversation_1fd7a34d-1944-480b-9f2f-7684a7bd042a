{
  "AWSTemplateFormatVersion" : "2010-09-09",

  "Description" : "GitHub Enterprise Server trial configuration",

  "Parameters" : {
    "Instance" : {
      "Description" : "Type of EC2 instance to launch",
      "Type" : "String",
      "Default" : "r6i.xlarge",
      "AllowedValues": [
        "r4.xlarge", "r4.2xlarge", "r4.4xlarge", "r4.8xlarge", "r4.16xlarge",
        "r5.xlarge", "r5.2xlarge", "r5.4xlarge", "r5.10xlarge", "r5.16xlarge",
        "r6i.xlarge", "r6i.2xlarge", "r6i.4xlarge", "r6i.10xlarge", "r6i.16xlarge",
        "m6i.32xlarge",
        "x1.16xlarge", "x1.32xlarge"
      ]
    },
    "Data" : {
      "Description" : "Size of attached EBS volumes (in GB)",
      "Type" : "Number",
      "Default" : 150,
      "MinValue" : 150,
      "MaxValue" : 16000
    },
    "KeyName" : {
      "Description" : "Name of an existing EC2 KeyPair to enable SSH access to the instances",
      "Type" : "String",
      "Default" : "ghe-ec2-instance"
    }
  },

  "Conditions" : {
    "HasKeyName" : { "Fn::Not" : [ { "Fn::Equals" : [ { "Ref" : "KeyName" }, ""] }] }
  },

  "Mappings" : {
    "AWSRegion2Ami" : {
      "ap-northeast-1" : { "AMI" : "ami-0ec63ee2246a7c74b" },
      "ap-northeast-2" : { "AMI" : "ami-0e5ec1d4f5c98f4da" },
      "ap-south-1" : { "AMI" : "ami-08d595e5aceedfd93" },
      "ap-southeast-1" : { "AMI" : "ami-0a1bd5aa0b5e78979" },
      "ap-southeast-2" : { "AMI" : "ami-0190468946dbb120f" },
      "ca-central-1" : { "AMI" : "ami-0ca2afd334010d839" },
      "eu-central-1" : { "AMI" : "ami-0b45ae034ec458401" },
      "eu-north-1" : { "AMI" : "ami-02a6fc3f34a7dcc41" },
      "eu-west-1" : { "AMI" : "ami-0a6a5a57073ed9dec" },
      "eu-west-2" : { "AMI" : "ami-07802791261c564bd" },
      "eu-west-3" : { "AMI" : "ami-055d28715f9f690a3" },
      "sa-east-1" : { "AMI" : "ami-0c70b8b90294e3ca2" },
      "us-east-1" : { "AMI" : "ami-0d82a07ece0123979" },
      "us-east-2" : { "AMI" : "ami-06297e661898b6775" },
      "us-west-1" : { "AMI" : "ami-0eabde70d769fa326" },
      "us-west-2" : { "AMI" : "ami-0ae70a87f1711dd97" },
      "us-gov-west-1" : { "AMI" : "ami-08087c7b0f674a8c4" },
      "us-gov-east-1" : { "AMI" : "ami-0889e06095b50d300" }
    }
  },

  "Resources" : {
    "GHSolo" : {
      "Type" : "AWS::EC2::Instance",      
      "Properties" : {
        
        "NetworkInterfaces": [
          {
            "SubnetId": "subnet-0ef97dd0c62b7874e",
            "GroupSet": [{ "Ref" : "GHInstanceSecurityGroup" }],
            "AssociatePublicIpAddress": true,
            "DeviceIndex": 0,
            "DeleteOnTermination": true,
          }
        ],
      
        "KeyName" : { "Fn::If" : [ "HasKeyName",
                                   { "Ref" : "KeyName" },
                                   { "Ref" : "AWS::NoValue" }
                                 ]},
        "ImageId" : { "Fn::FindInMap" : [ "AWSRegion2Ami", { "Ref" : "AWS::Region" }, "AMI" ] },
        "InstanceType" : { "Ref" : "Instance" },
        "EbsOptimized": true,
        "BlockDeviceMappings": [{
          "DeviceName" : "/dev/xvdf",
          "Ebs": {
            "VolumeType": "gp3",
            "VolumeSize" : { "Ref" : "Data" },
            "Encrypted": false,
            "DeleteOnTermination": false
          }
        }]
      }
    },

    "GHInstanceSecurityGroup" : {
      "Type" : "AWS::EC2::SecurityGroup",
      "Properties" : {
        "VpcId" : "vpc-052a14fb3aa70e776",
        "GroupDescription" : "Enable SSH access and HTTP access on the inbound port",
        "SecurityGroupIngress" : [ {
          "IpProtocol" : "tcp",
          "FromPort" : "22",
          "ToPort" : "22",
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 80,
          "ToPort" : 80,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 8080,
          "ToPort" : 8080,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 443,
          "ToPort" : 443,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 8443,
          "ToPort" : 8443,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 25,
          "ToPort" : 25,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 1194,
          "ToPort" : 1194,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "udp",
          "FromPort" : 1194,
          "ToPort" : 1194,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 122,
          "ToPort" : 122,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "tcp",
          "FromPort" : 9418,
          "ToPort" : 9418,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "udp",
          "FromPort" : 123,
          "ToPort" : 123,
          "CidrIp" : "0.0.0.0/0"
        },
        {
          "IpProtocol" : "udp",
          "FromPort" : 161,
          "ToPort" : 161,
          "CidrIp" : "0.0.0.0/0"
        } ]
      }
    }
  },

  "Outputs" : {
    "SoloURL" : {
      "Description" : "URL of the primary instance",
      "Value" :  { "Fn::Join" : [ "", [ "http://", { "Fn::GetAtt" : [ "GHSolo", "PublicIp" ]}]]}
    }
  }
}
