# Github Enterprise Demo Server 

## Deployment

### Info

- Environment: `non-prod`
- Account: `secops`
- Region: `us-west-2`
- Deployment method: `CloudFormation Stack`
- Elastic IP: `**************`
- DNS Name: `ghe.secops.getunblocked.com`
- Credentials: `stored in 1password` 

### Deployment command

```bash
TEMPLATE_PATH="Absolute path to the template file"

aws cloudformation create-stack \
  --region us-west-2 \
  --enable-termination-protection \
  --stack-name "GitHubTrial" \
  --template-body file://${TEMPLATE_PATH} \
  --parameters ParameterKey=Instance,ParameterValue=r6i.xlarge \
               ParameterKey=Data,ParameterValue=150

```

### Connecting to instance 
- HTTP: 
  - admin: https://ghe.secops.getunblocked.com:8443
  - User UI: https://ghe.secops.getunblocked.com/login
- SSH
  - `ssh -i "ghe-ec2-instance.pem" <EMAIL> -p 122`
  - SSH key can be found in 1password

### Starting and suspending instance
**Note**: Lambda functions processing actions provided below has been created manually via AWS console. If you need to update the code, modify `mgt.lambda.py` file and upload the new code to AWS through the management console.

```bash
# To start the ec2 instance:
curl https://uawecv2s6egm6om5rrho55pzs40frinf.lambda-url.us-west-2.on.aws/?action=start

# To stop ec2 instance:
curl https://uawecv2s6egm6om5rrho55pzs40frinf.lambda-url.us-west-2.on.aws/?action=stop

# To get the IP address for instance:
curl https://uawecv2s6egm6om5rrho55pzs40frinf.lambda-url.us-west-2.on.aws/


# You can make the requests above by specifying the action as part of JSON body
# Example JSON event:
# {
#   "body": "{\"action\": \"info\"}"
# }
# Note: The body is of type string and must be an escaped valid JSON string

```

### Automatic shutdown 
We have configured a rule on AWS EventBrdige to shutdown the GHE instance every two days at midnight. 

You can view/edit the event [here](https://us-west-2.console.aws.amazon.com/scheduler/home?region=us-west-2#schedules/default/GHE.Stopping.Event) under the secops account.

### Email whitelists

Using GSuite admin console you need to perform the following:

- Add an exception rule to exempt emails coming from the server address in SPAM and Fishing filter [here](https://admin.google.com/u/1/ac/apps/gmail/spam)
- Add routing rule to allow smtp traffic from the server's IP address from routing configuration [here](https://admin.google.com/u/1/ac/apps/gmail/routing)
- Add either `aspmx.l.google.com:25` or `smtp-relay.gmail.com:25` to SMTP configuration in GHE

**Note:** Only emails under our internal domain (getunblocked.com) can accept messages sent from this server. 