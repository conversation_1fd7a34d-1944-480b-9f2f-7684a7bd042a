# This code is manually deployed to AWS under sec-ops account
# I have added here just to keep track of it for future reference
import boto3
import json 
region = 'us-west-2'

# Id of EC2 instance to target
# We can't allow requesters specify IDs for security reasons
instances = ['i-03910418d995c02e0']


ec2 = boto3.client('ec2', region_name=region)
ec2_resource = boto3.resource('ec2', region_name=region)


def handler(event, context):
    # Retrieve the value of the `action` parameter
    if "queryStringParameters" in event:
        # `action` was passed as a query parameter
        action = event["queryStringParameters"]["action"]
    elif "body" in event:
        # `action` was passed as a parameter in the request body
        body = json.loads(event["body"])
        action = body["action"]
    else:
        # `action` was not provided
        return {
            "statusCode": 400,
            "body": "Please provide an `action` parameter with a value of either `start`, `stop`, or `info`."
        }

    print(event)
    instance = ec2_resource.Instance(instances[0])

    # Print the appropriate command based on the value of `action`
    if action == "start":
        if instance.state['Name'] != 'running':
            ec2.start_instances(InstanceIds=instances)
            return {
                "statusCode": 200,
                "body": "Instance is starting"
            }
        else:
            return {
                "statusCode": 200,
                "body": "Instance is running and can be reached via " + instance.private_ip_address
            }

    elif action == "stop":
        if instance.state['Name'] != 'stopped':
            # disabled while testing
            ec2.stop_instances(InstanceIds=instances)
            return {
                "statusCode": 200,
                "body": "Instance is stopping"
            }
        else:
            return {
                "statusCode": 200,
                "body": "Instance has stopped"
            }

    elif action == "info":
        if instance.state['Name'] == 'running':
            return {
                "statusCode": 200,
                "body": "Instance is running and can be reached via " + instance.private_ip_address
            }

        else:
            return {
                "statusCode": 200,
                "body": "Instance is not running"
            }

    else:
        return {
            "statusCode": 400,
            "body": "Invalid value for `action` parameter. Please provide a value of either `start`, `stop`, or `info`."
        }
        
