services:
    localstack:
        container_name: "${LOCALSTACK_DOCKER_NAME-localstack_main}"
        image: localstack/localstack:4.1.1
        ports:
            #- "127.0.0.1:4510-4559:4510-4559"  # external service port range
            - "127.0.0.1:4566:4566"            # LocalStack Edge Proxy
            - "127.0.0.1:4572:4572"            # S3
            - "127.0.0.1:4574:4574"            # SFN
            - "127.0.0.1:4576:4576"            # SQS
            - "127.0.0.1:4577:4577"            # STS
        environment:
            - DEBUG=${DEBUG-}
            - SERVICES=sqs,ses,s3,sfn,dynamodb,sts,iam,lambda
            - DEBUG=1
            - DATA_DIR=${DATA_DIR-}
            - LAMBDA_EXECUTOR=${LAMBDA_EXECUTOR-}
            - LOCALSTACK_API_KEY=${LOCALSTACK_API_KEY-}  # only required for Pro
            - DOCKER_HOST=unix:///var/run/docker.sock
        volumes:
            - "/var/lib/localstack"
            - "/var/run/docker.sock:/var/run/docker.sock"
            - "./docker/localstack:/var/localstack"
            - "./docker/localstack/localstackSetup.sh:/etc/localstack/init/ready.d/init-aws.sh"  # ready hook
        healthcheck:
            test: [ "CMD", "test", "-f", "/tmp/DONE_WITH_SETUP" ]
            interval: 1s
            timeout: 1s
            retries: 20
            start_period: 5s
