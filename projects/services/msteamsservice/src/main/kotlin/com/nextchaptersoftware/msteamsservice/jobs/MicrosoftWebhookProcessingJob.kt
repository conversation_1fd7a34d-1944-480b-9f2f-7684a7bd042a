package com.nextchaptersoftware.msteamsservice.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.service.BackgroundJob
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class MicrosoftWebhookProcessingJob(
    private val eventDequeueService: EventDequeueService,
    private val jobTimeout: Duration,
) : BackgroundJob {
    override val name: String
        get() = "Microsoft Webhook Processing Job"

    override suspend fun run() {
        withTimeout(jobTimeout) {
            runSuspendCatching {
                eventDequeueService.process()
            }.onFailure {
                LOGGER.errorAsync(it) { "Failed to process Microsoft webhook message" }
            }.getOrThrow()
        }
    }
}
