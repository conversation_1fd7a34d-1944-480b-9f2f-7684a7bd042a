package com.nextchaptersoftware.msteamsservice.api

import com.nextchaptersoftware.api.HealthApiDelegateInterface
import com.nextchaptersoftware.service.ServiceHealthApi
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class HealthApiDelegateImpl(private val serviceHealthApi: ServiceHealthApi) : HealthApiDelegateInterface {
    override suspend fun getDeepCheck(context: RoutingContext, input: Resources.getDeepCheck) {
        serviceHealthApi.getDeepCheck(context)
    }

    override suspend fun getShallowCheck(context: RoutingContext, input: Resources.getShallowCheck) {
        serviceHealthApi.getShallowCheck(context)
    }
}
