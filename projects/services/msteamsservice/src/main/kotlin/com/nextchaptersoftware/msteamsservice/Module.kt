package com.nextchaptersoftware.msteamsservice

import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.microsoft.bot.queue.dequeue.MicrosoftWebhookEventHandler
import com.nextchaptersoftware.microsoft.bot.queue.dequeue.MicrosoftWebhookEventMessageProcessor
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotContactRelationUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotContactRelationUpdateWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotConversationUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotConversationUpdateWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotDeleteUserDataWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotDeleteUserDataWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotEndOfConversationWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotEndOfConversationWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotEventWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotEventWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotHandoffWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotHandoffWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotInstallationUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotInstallationUpdateWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotInvokeWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotInvokeWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageDeleteWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageDeleteWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageReactionWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageReactionWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageUpdateWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotSuggestionWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotSuggestionWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotTraceWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotTraceWorkflowImpl
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotTypingWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotTypingWorkflowImpl
import com.nextchaptersoftware.msteamsservice.jobs.MicrosoftWebhookProcessingJob
import com.nextchaptersoftware.msteamsservice.plugins.configureRouting
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.service.plugins.configureTemporal
import com.nextchaptersoftware.temporal.annotations.Queue
import io.ktor.server.application.Application
import kotlin.time.Duration.Companion.seconds

private const val CONSUMER_COUNT = 3

@Suppress("LongMethod")
fun Application.module(
    serviceLifecycle: ServiceLifecycle,
    config: GlobalConfig = GlobalConfig.INSTANCE,
) {
    val microsoftWebhookEventHandler by lazy {
        MicrosoftWebhookEventHandler()
    }

    configureRouting(serviceLifecycle = serviceLifecycle)
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureJvmMetrics()
    configureBackgroundJobs(
        jobs = buildList {
            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = MicrosoftWebhookProcessingJob(
                            jobTimeout = 60.seconds,
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.hooksMicrosoftBotQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = MicrosoftWebhookEventMessageProcessor(
                                    handler = microsoftWebhookEventHandler,
                                ),
                            ),
                        ),
                    ),
                )
            }
        },
    )
    configureTemporal {
        worker(Queue.MSTeamsBot) {
            workflow<MicrosoftBotMessageWorkflow> { MicrosoftBotMessageWorkflowImpl() }
            workflow<MicrosoftBotConversationUpdateWorkflow> { MicrosoftBotConversationUpdateWorkflowImpl() }
            workflow<MicrosoftBotContactRelationUpdateWorkflow> { MicrosoftBotContactRelationUpdateWorkflowImpl() }
            workflow<MicrosoftBotTypingWorkflow> { MicrosoftBotTypingWorkflowImpl() }
            workflow<MicrosoftBotEndOfConversationWorkflow> { MicrosoftBotEndOfConversationWorkflowImpl() }
            workflow<MicrosoftBotEventWorkflow> { MicrosoftBotEventWorkflowImpl() }
            workflow<MicrosoftBotInvokeWorkflow> { MicrosoftBotInvokeWorkflowImpl() }
            workflow<MicrosoftBotDeleteUserDataWorkflow> { MicrosoftBotDeleteUserDataWorkflowImpl() }
            workflow<MicrosoftBotMessageUpdateWorkflow> { MicrosoftBotMessageUpdateWorkflowImpl() }
            workflow<MicrosoftBotMessageDeleteWorkflow> { MicrosoftBotMessageDeleteWorkflowImpl() }
            workflow<MicrosoftBotInstallationUpdateWorkflow> { MicrosoftBotInstallationUpdateWorkflowImpl() }
            workflow<MicrosoftBotMessageReactionWorkflow> { MicrosoftBotMessageReactionWorkflowImpl() }
            workflow<MicrosoftBotSuggestionWorkflow> { MicrosoftBotSuggestionWorkflowImpl() }
            workflow<MicrosoftBotTraceWorkflow> { MicrosoftBotTraceWorkflowImpl() }
            workflow<MicrosoftBotHandoffWorkflow> { MicrosoftBotHandoffWorkflowImpl() }
        }
    }
}
