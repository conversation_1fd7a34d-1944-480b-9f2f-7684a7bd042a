package com.nextchaptersoftware.review

import com.aallam.openai.client.OpenAIHost
import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.anthropic.api.AnthropicApiConfiguration
import com.nextchaptersoftware.anthropic.api.AnthropicApiProvider
import com.nextchaptersoftware.aws.bedrock.anthropic.api.BedrockAnthropicCompletionsApi
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.cohere.api.CohereApiConfiguration
import com.nextchaptersoftware.cohere.api.CohereApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.ServiceInitializer
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.gemini.api.GeminiApiProvider
import com.nextchaptersoftware.gemini.config.GeminiApiConfig
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.ml.api.delegate.MachineLearningApiProviderDelegate
import com.nextchaptersoftware.ml.completion.AnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockAnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockConverseCompletionService
import com.nextchaptersoftware.ml.completion.CohereCompletionService
import com.nextchaptersoftware.ml.completion.DecisionCompletionService
import com.nextchaptersoftware.ml.completion.GeminiCompletionService
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import com.nextchaptersoftware.ml.completion.OpenAICompletionService
import com.nextchaptersoftware.ml.completion.RoundRobinCompletionService
import com.nextchaptersoftware.openai.api.OpenAIApiConfiguration
import com.nextchaptersoftware.openai.api.OpenAIApiProvider
import com.nextchaptersoftware.openai.api.delegates.AzureOpenAIApiProviderDelegate
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.review.context.CodeReviewDiffContextService
import com.nextchaptersoftware.review.context.CodeReviewSourceProvider
import com.nextchaptersoftware.review.lifecycle.CodeReviewEventProcessor
import com.nextchaptersoftware.review.lifecycle.CodeReviewService
import com.nextchaptersoftware.review.plugins.configureRouting
import com.nextchaptersoftware.review.reporting.CodeReviewPublisher
import com.nextchaptersoftware.review.reporting.CodeReviewReporter
import com.nextchaptersoftware.review.reviewers.CodeReviewer
import com.nextchaptersoftware.review.services.CodeReviewControlService
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.utils.DefaultEnterpriseAppConfigOrgIdsResolver
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import io.ktor.server.application.Application
import kotlin.time.Duration.Companion.seconds

/**
 * The number of concurrent consumers for the review event queue.
 */
private const val CONSUMER_COUNT = 2

fun Application.module(
    serviceLifecycle: ServiceLifecycle,
    config: GlobalConfig = GlobalConfig.INSTANCE,
) {
    configureRouting(serviceLifecycle = serviceLifecycle)
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureJvmMetrics()

    val scmRepoApiFactory by lazy {
        makeScmRepoApiFactory(config)
    }

    val completionService by lazy {
        makeCompletionService(config)
    }

    val codeReviewer by lazy {
        CodeReviewer(
            completionService = completionService,
        )
    }

    val codeReviewControlService by lazy {
        CodeReviewControlService(
            capabilityValidation = CapabilityValidation(
                planCapabilitiesService = PlanCapabilitiesServiceProvider(config = config.billing).get(),
            ),
        )
    }

    val codeReviewService by lazy {
        CodeReviewService(
            codeReviewPublisher = CodeReviewPublisher(
                scmRepoApiFactory = scmRepoApiFactory,
            ),
            codeReviewReporter = CodeReviewReporter(),
            codeReviewer = codeReviewer,
            codeReviewDiffContextService = CodeReviewDiffContextService(
                codeReviewSourceProvider = CodeReviewSourceProvider(
                    scmRepoApiFactory = scmRepoApiFactory,
                ),
            ),
        )
    }

    configureBackgroundJobs(
        jobs = buildList {
            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = CodeReviewEventProcessingJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.codeReviewQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = CodeReviewEventProcessor(
                                    codeReviewControlService = codeReviewControlService,
                                    codeReviewService = codeReviewService,
                                ),
                            ),
                        ),
                    ),
                )
            }
        },
    )
}

private fun makeScmRepoApiFactory(config: GlobalConfig): ScmRepoApiFactory {
    val scmConfig = ScmConfig.INSTANCE

    val enterpriseAppConfigOrgIdsResolver by lazy {
        DefaultEnterpriseAppConfigOrgIdsResolver()
    }

    val scmAuthApiFactory by lazy {
        ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = ScmWebFactory(scmConfig),
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmAppApiFactory = ScmAppApiFactory(
        scmConfig = scmConfig,
        enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
    )

    val userSecretConfig = UserSecretConfig.INSTANCE

    val userSecretServiceRSA = UserSecretService(
        encryption = RSACryptoSystem.RSAEncryption(
            publicKey = config.encryption.userSecrets4096PublicKey,
            modulusBitLength = 4096,
        ),
        decryption = RSACryptoSystem.RSADecryption(
            privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
            modulusBitLength = 4096,
        ),
    )

    val userSecretServiceAES = run {
        val key = AESCryptoSystem.importKey(
            userSecretConfig.encryption.userSecretsAesKey.value,
        )
        UserSecretService(
            encryption = AESCryptoSystem.AESEncryption(key),
            decryption = AESCryptoSystem.AESDecryption(key),
        )
    }

    val userSecretServiceResolver = UserSecretServiceResolver(
        userSecretServiceRSA = userSecretServiceRSA,
        userSecretServiceAES = userSecretServiceAES,
    )

    return ScmRepoApiFactory(
        scmAppApiFactory = scmAppApiFactory,
        scmAuthApiFactory = scmAuthApiFactory,
        scmConfig = scmConfig,
        userSecretServiceResolver = userSecretServiceResolver,
    )
}

@Suppress("LongMethod")
private fun makeCompletionService(config: GlobalConfig): DecisionCompletionService {
    val machineLearningApiProviders by MachineLearningApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val machineLearningCompletionService = RoundRobinCompletionService(
        completionServices = machineLearningApiProviders.map { machineLearningApiProvider ->
            MachineLearningCompletionService(
                machineLearningApiProvider = machineLearningApiProvider,
            )
        },
    )
    val openAIApiProvider = OpenAIApiProvider(
        config = OpenAIApiConfiguration(
            timeout = config.openAI.defaultTimeout,
            token = config.openAI.apiKey,
        ),
    )
    val openRouterApiProvider = OpenAIApiProvider(
        config = OpenAIApiConfiguration(
            timeout = config.openRouter.defaultTimeout,
            token = config.openRouter.apiKey,
            host = OpenAIHost(baseUrl = config.openRouter.baseApiUri),
        ),
    )
    val azureGPT41OpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41DeploymentId,
    )

    val azureGPT41NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41NanoDeploymentId,
    )

    val cohereApiProvider = CohereApiProvider(
        config = CohereApiConfiguration(config),
    )
    val cohereCompletionService = CohereCompletionService(
        cohereApiProvider = cohereApiProvider,
    )
    val geminiConfig = GeminiApiConfig.INSTANCE

    val geminiApiProvider = GeminiApiProvider(
        config = geminiConfig,
    )
    val geminiCompletionService = GeminiCompletionService(
        geminiApiProvider = geminiApiProvider,
    )
    val bedrockRuntimeProviderFactory = StandardBedrockRuntimeProviderFactory()
    val bedrockRuntimeAsyncProviderFactory = StandardBedrockRuntimeAsyncProviderFactory()

    val awsClientProvider = AWSClientProvider.from(
        region = ServiceInitializer.REGION.toRegion(),
    )
    val bedrockRuntimeProvider = bedrockRuntimeProviderFactory.generate(
        awsClientProvider = awsClientProvider,
    )
    val bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProviderFactory.generate(
        awsClientProvider = awsClientProvider,
    )
    val bedrockAnthropicCompletionsApi = BedrockAnthropicCompletionsApi(
        bedrockRuntimeProvider = bedrockRuntimeProvider,
        bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
    )
    val bedrockAnthropicCompletionService = BedrockAnthropicCompletionService(
        bedrockAnthropicCompletionsApi = bedrockAnthropicCompletionsApi,
    )

    val anthropicApiConfiguration = AnthropicApiConfiguration(
        baseApiUri = config.anthropic.baseApiUri.asUrl,
        timeout = config.anthropic.defaultTimeout,
        token = config.anthropic.apiKey,
    )

    val anthropicApiProvider = AnthropicApiProvider(
        config = anthropicApiConfiguration,
    )

    val anthropicCompletionService = AnthropicCompletionService(
        anthropicApiProvider = anthropicApiProvider,
    )

    val roundRobinAnthropicCompletionService = RoundRobinCompletionService(
        completionServices = listOf(
            anthropicCompletionService,
            bedrockAnthropicCompletionService,
        ),
    )

    val bedrockConverseCompletionService = BedrockConverseCompletionService(
        bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
    )

    val azureGPT41MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41MiniDeploymentId,
    )

    return DecisionCompletionService(
        machineLearningCompletionService = machineLearningCompletionService,
        openAICompletionService = OpenAICompletionService(
            openAIApiProvider = openAIApiProvider,
        ),
        openRouterCompletionService = OpenAICompletionService(
            openAIApiProvider = openRouterApiProvider,
        ),
        bedrockAnthropicCompletionService = bedrockAnthropicCompletionService,
        anthropicCompletionService = anthropicCompletionService,
        roundRobinGPT41CompletionService = RoundRobinCompletionService(
            completionServices = azureGPT41OpenAIApiProviders.plus(openAIApiProvider).map {
                OpenAICompletionService(
                    openAIApiProvider = it,
                )
            },
        ),
        roundRobinGPT41NanoCompletionService = RoundRobinCompletionService(
            completionServices = azureGPT41NanoApiProviders.plus(openAIApiProvider).map {
                OpenAICompletionService(
                    openAIApiProvider = it,
                )
            },
        ),
        roundRobinAnthropicCompletionService = roundRobinAnthropicCompletionService,
        cohereCompletionService = cohereCompletionService,
        geminiCompletionService = geminiCompletionService,
        roundRobinGPT41MiniCompletionService = RoundRobinCompletionService(
            completionServices = azureGPT41MiniApiProviders.plus(openAIApiProvider).map {
                OpenAICompletionService(
                    openAIApiProvider = it,
                )
            },
        ),
        bedrockConverseCompletionService = bedrockConverseCompletionService,
    )
}
