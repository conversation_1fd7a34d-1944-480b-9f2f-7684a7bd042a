package com.nextchaptersoftware.review

import com.nextchaptersoftware.db.health.PostgresHealthChecker
import com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder

fun main() {
    ServiceBootstrapBuilder.bootstrap {
        withHealthCheckers(listOf(PostgresHealthChecker()))
        withLogs()
        withH<PERSON>comb()
        withDatabase()
    }.startHttpServer { serviceLifecycle ->
        module(
            serviceLifecycle = serviceLifecycle,
        )
    }
}
