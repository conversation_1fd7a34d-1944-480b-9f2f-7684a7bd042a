package com.nextchaptersoftware.review

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.BackgroundJob

class CodeReviewEventProcessingJob(
    private val eventDequeueService: EventDequeueService,
) : BackgroundJob {
    override val name: String
        get() = "Code Review Event Processing Job"

    override suspend fun run() {
        eventDequeueService.process()
    }
}
