package com.nextchaptersoftware.review.plugins

import com.nextchaptersoftware.api.HealthApi
import com.nextchaptersoftware.ktor.serialization.KtorSerialization.installSerializer
import com.nextchaptersoftware.review.api.HealthApiDelegateImpl
import com.nextchaptersoftware.service.ServiceHealthApi
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.resources.Resources
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configureRouting(serviceLifecycle: ServiceLifecycle) {
    install(Resources) {
        installSerializer()
    }

    val healthApiDelegateImpl by lazy {
        HealthApiDelegateImpl(ServiceHealthApi(serviceLifecycle = serviceLifecycle))
    }

    routing {
        route("/api") {
            HealthApi(healthApiDelegateImpl)
        }

        // Path used for exposing health endpoint for APM monitoring
        route("/api/health/review") {
            HealthApi(healthApiDelegateImpl)
        }
    }
}
