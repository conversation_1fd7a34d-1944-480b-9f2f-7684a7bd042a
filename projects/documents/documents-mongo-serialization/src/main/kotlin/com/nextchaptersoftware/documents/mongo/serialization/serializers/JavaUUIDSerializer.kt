package com.nextchaptersoftware.documents.mongo.serialization.serializers

import java.util.UUID
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import org.bson.BsonBinary
import org.bson.BsonInvalidOperationException
import org.bson.BsonValue
import org.bson.UuidRepresentation
import org.bson.codecs.kotlinx.BsonDecoder
import org.bson.codecs.kotlinx.BsonEncoder

/**
 * KSerializer for java.util.UUID.
 *
 * - BSON: stores as Binary subtype 4 (UUID Standard).
 * - Non-BSON: falls back to canonical string "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".
 *
 * Tip: also configure your MongoClient with UuidRepresentation.STANDARD for consistency:
 *   builder.uuidRepresentation(UuidRepresentation.STANDARD)
 */
object JavaUUIDSerializer : KSerializer<UUID> {
    override val descriptor =
        PrimitiveSerialDescriptor("JavaUUIDSerializer", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: UUID) {
        when (encoder) {
            is BsonEncoder -> {
                // Write as BSON Binary subtype 4 (UUID Standard)
                val bin = BsonBinary(value, UuidRepresentation.STANDARD)
                encoder.encodeBsonValue(bin)
            }

            else -> {
                // Non-BSON fallback (e.g., JSON): canonical UUID string
                encoder.encodeString(value.toString())
            }
        }
    }

    override fun deserialize(decoder: Decoder): UUID {
        return when (decoder) {
            is BsonDecoder -> {
                val v: BsonValue = decoder.decodeBsonValue()
                when {
                    v.isBinary -> v.asBinary().asUuid(UuidRepresentation.STANDARD)

                    v.isString -> UUID.fromString(v.asString().value)

                    else -> throw BsonInvalidOperationException(
                        "Cannot decode UUID from ${v.bsonType}; expected BINARY (UUID) or STRING.",
                    )
                }
            }

            else -> UUID.fromString(decoder.decodeString())
        }
    }
}
