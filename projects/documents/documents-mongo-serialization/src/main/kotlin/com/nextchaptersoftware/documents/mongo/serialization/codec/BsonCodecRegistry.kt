package com.nextchaptersoftware.documents.mongo.serialization.codec

import com.mongodb.MongoClientSettings
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry

object BsonCodecRegistry {
    val DEFAULT_BSON_CODEC_REGISTRY: CodecRegistry by lazy {
        CodecRegistries.fromRegistries(
            CodecRegistries.fromProviders(
                KotlinSerializationCodecProvider.DEFAULT_KOTLIN_SERIALIZATION_CODEC_PROVIDER,
                KotlinCustomCodecProvider.of(JavaUUIDCodec, KotlinInstantCodec, KtorUrlCodec),
            ),
            MongoClientSettings.getDefaultCodecRegistry(),
        )
    }
}
