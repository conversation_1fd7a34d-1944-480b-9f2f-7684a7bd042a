package com.nextchaptersoftware.documents.mongo.serialization.codec

import com.nextchaptersoftware.documents.mongo.serialization.serializers.BsonSerializersModule
import kotlinx.serialization.ExperimentalSerializationApi
import org.bson.codecs.kotlinx.KotlinSerializerCodecProvider

object KotlinSerializationCodecProvider {
    @OptIn(ExperimentalSerializationApi::class)
    val DEFAULT_KOTLIN_SERIALIZATION_CODEC_PROVIDER by lazy {
        // Create a provider that uses our module
        KotlinSerializerCodecProvider(
            serializersModule = BsonSerializersModule.DEFAULT_BSON_SERIALIZERS_MODULE,
        )
    }
}
