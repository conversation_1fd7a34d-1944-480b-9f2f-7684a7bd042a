package com.nextchaptersoftware.documents.mongo.serialization.serializers

import io.ktor.http.Url
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import org.bson.BsonString
import org.bson.codecs.kotlinx.BsonDecoder
import org.bson.codecs.kotlinx.BsonEncoder

/**
 * Serializer for [Url] values, encoding them as BSON strings (or plain strings for JSON).
 *
 * - In BSON contexts: stored as a [BsonString].
 * - In JSON / fallback contexts: stored as a plain string.
 */
object KtorUrlSerializer : KSerializer<Url> {
    override val descriptor = PrimitiveSerialDescriptor("KtorUrlAsBsonString", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: Url) {
        val urlString = value.toString()
        when (encoder) {
            is BsonEncoder -> encoder.encodeBsonValue(BsonString(urlString))
            else -> encoder.encodeString(urlString)
        }
    }

    override fun deserialize(decoder: Decoder): Url = when (decoder) {
        is BsonDecoder -> Url(decoder.decodeBsonValue().asString().value)
        else -> Url(decoder.decodeString())
    }
}
