package com.nextchaptersoftware.documents.mongo.serialization.codec

import org.bson.codecs.Codec
import org.bson.codecs.configuration.CodecProvider
import org.bson.codecs.configuration.CodecRegistry

/**
 * A simple, immutable [CodecProvider] implementation that lets you supply
 * custom BSON codecs for classes that are **not covered** by the standard
 * driver or the `KotlinSerializationCodecProvider`.
 *
 * Why this exists:
 * - The official MongoDB Kotlin driver delegates to `KotlinSerializationCodecProvider`
 *   for `@Serializable` data models.
 * - However, certain Kotlin types (e.g. [kotlin.time.Instant], [kotlin.time.Duration],
 *   or other domain primitives) are not `@Serializable` and therefore do not get
 *   codecs out of the box.
 * - Attempting to use these types in queries, updates, or nested models will otherwise
 *   result in a `CodecConfigurationException` because the driver has no idea how to
 *   encode/decode them.
 *
 * Usage:
 * - Create a [Codec] for each custom type (e.g. `KotlinInstantCodec` that maps
 *   [kotlin.time.Instant] ↔ `BsonDateTime`).
 * - Register it with this provider:
 *
 * ```kotlin
 * val registry = CodecRegistries.fromRegistries(
 *     CodecRegistries.fromProviders(
 *         KotlinSerializationCodecProvider.DEFAULT_KOTLIN_SERIALIZATION_CODEC_PROVIDER,
 *         KotlinCustomCodecProvider.of(KotlinInstantCodec),
 *     ),
 *     MongoClientSettings.getDefaultCodecRegistry()
 * )
 * ```
 *
 * Design:
 * - The provider is constructed with an initial map of `Class<*> → Codec<*>`.
 * - Lookups are O(1) and no new codecs can be added after initialization, making it safe
 *   to share across threads and Mongo clients.
 *
 * This makes it the recommended place to plug in codecs for *Kotlin-specific primitives*
 * that the driver does not support out of the box.
 */
class KotlinCustomCodecProvider private constructor(
    codecs: Collection<Codec<*>>,
) : CodecProvider {

    // Immutable, exact-class match map
    private val byClass: Map<Class<*>, Codec<*>> =
        codecs.associateBy { it.encoderClass }

    override fun <T : Any> get(clazz: Class<T>, registry: CodecRegistry): Codec<T>? {
        @Suppress("UNCHECKED_CAST")
        return byClass[clazz] as? Codec<T>
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        return other is KotlinCustomCodecProvider // match ValueCodecProvider semantics
    }

    override fun hashCode(): Int = 0

    override fun toString(): String = "KotlinCustomCodecProvider{}"

    companion object {
        /**
         * Create from an explicit set of codecs. If two codecs advertise the same encoderClass,
         * the last one wins (explicit preference).
         */
        fun of(vararg codecs: Codec<*>): KotlinCustomCodecProvider =
            KotlinCustomCodecProvider(codecs.toList())

        fun of(codecs: Collection<Codec<*>>): KotlinCustomCodecProvider =
            KotlinCustomCodecProvider(codecs)
    }
}
