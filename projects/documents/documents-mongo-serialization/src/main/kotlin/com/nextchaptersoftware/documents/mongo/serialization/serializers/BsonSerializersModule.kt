package com.nextchaptersoftware.documents.mongo.serialization.serializers

import io.ktor.http.Url
import java.util.UUID
import kotlin.time.Instant
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.modules.plus
import org.bson.codecs.kotlinx.defaultSerializersModule

object BsonSerializersModule {
    val DEFAULT_BSON_SERIALIZERS_MODULE by lazy {
        SerializersModule {
            contextual(Instant::class, KotlinInstantSerializer)
            contextual(Url::class, KtorUrlSerializer)
            contextual(UUID::class, JavaUUIDSerializer)
        } + defaultSerializersModule
    }
}
