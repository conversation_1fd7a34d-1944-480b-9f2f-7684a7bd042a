package com.nextchaptersoftware.documents.mongo.serialization.codec

import kotlin.time.Instant
import org.bson.BsonInvalidOperationException
import org.bson.BsonReader
import org.bson.BsonType
import org.bson.BsonWriter
import org.bson.codecs.Codec
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext

/**
 * Codec for kotlin.time.Instant <-> BSON DateTime (epoch millis).
 * Nullable fields are handled by Mongo automatically (this Codec is for non-null values).
 */
object KotlinInstantCodec : Codec<Instant> {
    override fun getEncoderClass(): Class<Instant> = Instant::class.java

    override fun encode(writer: BsonWriter, value: Instant, encoderContext: EncoderContext) {
        writer.writeDateTime(value.toEpochMilliseconds())
    }

    override fun decode(reader: BsonReader, decoderContext: DecoderContext): Instant {
        val t = reader.currentBsonType
        return when (t) {
            BsonType.DATE_TIME -> Instant.fromEpochMilliseconds(reader.readDateTime())

            // Some pipelines/drivers might surface epoch millis as INT64; support it as a convenience:
            BsonType.INT64 -> Instant.fromEpochMilliseconds(reader.readInt64())

            else -> throw BsonInvalidOperationException(
                "Cannot decode kotlin.time.Instant from $t; expected DATE_TIME (or INT64 epoch millis).",
            )
        }
    }
}
