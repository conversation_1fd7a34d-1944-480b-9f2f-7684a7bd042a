package com.nextchaptersoftware.documents.mongo.serialization.utils

import com.nextchaptersoftware.documents.mongo.serialization.serializers.BsonSerializersModule
import kotlinx.serialization.modules.SerializersModule
import org.bson.BsonDocument
import org.bson.BsonDocumentReader
import org.bson.BsonDocumentWriter
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext
import org.bson.codecs.kotlinx.BsonConfiguration
import org.bson.codecs.kotlinx.KotlinSerializerCodec

object BsonSerializationUtils {
    inline fun <reified T : Any> serialize(
        value: T,
        serializersModule: SerializersModule = BsonSerializersModule.DEFAULT_BSON_SERIALIZERS_MODULE,
        configuration: BsonConfiguration = BsonConfiguration(),
    ): BsonDocument {
        val document = BsonDocument()
        val writer = BsonDocumentWriter(document)
        val codec = checkNotNull(KotlinSerializerCodec.create(T::class, serializersModule, configuration))
        codec.encode(writer, value, EncoderContext.builder().build())
        writer.flush()
        return document
    }

    inline fun <reified T : Any> deserialize(
        document: BsonDocument,
        serializersModule: SerializersModule = BsonSerializersModule.DEFAULT_BSON_SERIALIZERS_MODULE,
        configuration: BsonConfiguration = BsonConfiguration(),
    ): T {
        val reader = BsonDocumentReader(document)
        val codec = checkNotNull(KotlinSerializerCodec.create(T::class, serializersModule, configuration))
        return codec.decode(reader, DecoderContext.builder().build())
    }
}
