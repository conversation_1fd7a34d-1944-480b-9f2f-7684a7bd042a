package com.nextchaptersoftware.documents.mongo.serialization.codec

import io.ktor.http.URLParserException
import io.ktor.http.Url
import org.bson.BsonInvalidOperationException
import org.bson.BsonReader
import org.bson.BsonType
import org.bson.BsonWriter
import org.bson.codecs.Codec
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext

/**
 * Codec for io.ktor.http.Url <-> BSON String.
 * Non-null only; Mongo handles nulls at the field level.
 */
object KtorUrlCodec : Codec<Url> {
    override fun getEncoderClass(): Class<Url> = Url::class.java

    override fun encode(writer: BsonWriter, value: Url, encoderContext: EncoderContext) {
        writer.writeString(value.toString())
    }

    override fun decode(reader: BsonReader, decoderContext: DecoderContext): Url {
        val t = reader.currentBsonType
        if (t != BsonType.STRING) {
            throw BsonInvalidOperationException("Cannot decode Url from $t; expected STRING")
        }
        val raw = reader.readString()
        return try {
            Url(raw)
        } catch (e: URLParserException) {
            throw BsonInvalidOperationException("Invalid URL string in BSON: $raw", e)
        }
    }
}
