package com.nextchaptersoftware.documents.mongo.serialization.codec

import java.util.UUID
import org.bson.BsonBinary
import org.bson.BsonInvalidOperationException
import org.bson.BsonReader
import org.bson.BsonType
import org.bson.BsonWriter
import org.bson.UuidRepresentation
import org.bson.codecs.Codec
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext

/**
 * Codec for java.util.UUID <-> BSON Binary (subtype 4, UUID Standard).
 * - Writes UUIDs using UuidRepresentation.STANDARD (subtype 4).
 * - Reads either Binary (any subtype; normalized to STANDARD) or String (canonical form).
 * - Null handling is done at the field level by the driver; this codec is for non-null UUIDs.
 */
object JavaUUIDCodec : Codec<UUID> {

    override fun getEncoderClass(): Class<UUID> = UUID::class.java

    override fun encode(writer: BsonWriter, value: UUID, encoderContext: EncoderContext) {
        // Always write as STANDARD (Binary subtype 4)
        writer.writeBinaryData(BsonBinary(value, UuidRepresentation.STANDARD))
    }

    override fun decode(reader: BsonReader, decoderContext: DecoderContext): UUID {
        return when (reader.currentBsonType) {
            BsonType.BINARY -> {
                val bin = reader.readBinaryData()
                // Normalize any incoming representation to STANDARD UUID
                try {
                    bin.asUuid(UuidRepresentation.STANDARD)
                } catch (e: IllegalArgumentException) {
                    throw BsonInvalidOperationException("Invalid UUID binary payload", e)
                }
            }

            BsonType.STRING -> {
                val s = reader.readString()
                try {
                    UUID.fromString(s)
                } catch (e: IllegalArgumentException) {
                    throw BsonInvalidOperationException("Invalid UUID string: $s", e)
                }
            }

            else -> throw BsonInvalidOperationException(
                "Cannot decode UUID from ${reader.currentBsonType}; expected BINARY or STRING.",
            )
        }
    }
}
