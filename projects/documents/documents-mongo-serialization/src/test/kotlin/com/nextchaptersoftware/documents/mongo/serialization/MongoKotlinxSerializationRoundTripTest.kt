package com.nextchaptersoftware.documents.mongo.serialization

import com.mongodb.client.model.Filters.eq
import com.mongodb.client.model.Updates
import com.mongodb.client.model.Updates.combine
import com.nextchaptersoftware.documents.mongo.serialization.utils.MongoTestUtils
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import io.ktor.http.Url
import java.util.UUID
import kotlin.time.Instant
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.assertj.core.api.Assertions.assertThat
import org.bson.BsonBinary
import org.bson.BsonBinarySubType
import org.bson.BsonDateTime
import org.bson.BsonDocument
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MongoKotlinxSerializationRoundTripTest {

    val client = MongoTestUtils.MONGO_API_CLIENT
    lateinit var dbName: String
    lateinit var collName: String

    // ---------- Basic (non-nested, non-polymorphic) model ----------

    @Serializable
    data class BasicDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
        @Contextual @SerialName("closed_at") val closedAt: Instant? = null,
    )

    // ---------- Ktor URL model ----------

    @Serializable
    data class UrlDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @SerialName("html_url") val htmlUrl: Url,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
    )

    // Polymorphic models
    @Serializable
    sealed interface ReviewEvent {
        val by: String
    }

    @Serializable
    @SerialName("approved")
    data class Approved(
        @Contextual val at: Instant,
        override val by: String,
    ) : ReviewEvent

    @Serializable
    @SerialName("changes_requested")
    data class ChangesRequested(
        val reason: String,
        @Contextual val at: Instant,
        override val by: String,
    ) : ReviewEvent

    @Serializable
    data class PolymorphicDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @SerialName("event") val event: ReviewEvent,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
    )

    // Models for nested tests
    @Serializable
    data class MiniUser(
        @SerialName("id") val id: Long,
        @SerialName("login") val login: String,
    )

    @Serializable
    data class MiniCommit(
        @SerialName("ref") val ref: String,
        @SerialName("sha") val sha: String,
        @SerialName("author") val author: MiniUser? = null,
        @Contextual @SerialName("authored_at") val authoredAt: Instant? = null,
    )

    @Serializable
    data class ParentDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @SerialName("owner") val owner: MiniUser,
        @SerialName("commits") val commits: List<MiniCommit>,
        @SerialName("latest") val latest: MiniCommit?,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
    )

    @Serializable
    data class UuidDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @Contextual @SerialName("correlation_id") val correlationId: UUID,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
    )

    // ---------- Lifecycle ----------

    @BeforeAll
    fun setUp() {
        dbName = MongoTestUtils.tempDbName("it_mongo_ser")
        collName = MongoTestUtils.tempCollectionName("rt")
    }

    @AfterAll
    fun tearDown() = runTest {
        runSuspendCatching { client.database(dbName).drop() }
        client.close()
    }

    // ---------- Basic round-trip test ----------

    @Test
    fun `basic round trip and raw BSON verification`() = runTest {
        val db = client.database(dbName)
        val coll = db.getCollection<BasicDoc>(collName)

        val created = Instant.fromEpochMilliseconds(1_800_000_000_000)
        val updated = Instant.fromEpochMilliseconds(1_800_000_123_456)

        val doc = BasicDoc(
            mongoId = ObjectId(),
            title = "mongo serialization basic",
            createdAt = created,
            updatedAt = updated,
            closedAt = null,
        )

        coll.insertOne(doc)
        val fetched = coll.find(eq("_id", doc.mongoId)).first()
        assertThat(fetched).isEqualTo(doc)

        val raw = db.getCollection<BsonDocument>(collName).find(eq("_id", doc.mongoId)).first()

        assertThat(raw["_id"]?.isObjectId).isTrue
        assertThat(raw["_id"]?.asObjectId()?.value).isEqualTo(doc.mongoId)

        assertThat(raw["created_at"]?.isDateTime).isTrue
        assertThat(raw["created_at"]?.asDateTime()).isInstanceOf(BsonDateTime::class.java)
        assertThat(raw["created_at"]?.asDateTime()?.value).isEqualTo(created.toEpochMilliseconds())

        assertThat(raw["updated_at"]?.isDateTime).isTrue
        assertThat(raw["updated_at"]?.asDateTime()?.value).isEqualTo(updated.toEpochMilliseconds())

        assertThat(raw.containsKey("closed_at")).isFalse

        coll.deleteOne(eq("_id", doc.mongoId))
    }

    // ---------- KTOR URL TESTS IN INNER CLASS ----------

    @Nested
    inner class KtorUrlTest {

        @Test
        fun `ktor url round trip and raw BSON verification`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<UrlDoc>(collName)

            val created = Instant.fromEpochMilliseconds(1_860_000_000_000)
            val updated = Instant.fromEpochMilliseconds(1_860_000_111_111)
            val url = Url("https://github.com/org/repo/pull/123")

            val doc = UrlDoc(
                title = "url round trip",
                htmlUrl = url,
                createdAt = created,
                updatedAt = updated,
            )

            coll.insertOne(doc)
            val fetched = coll.find(eq("_id", doc.mongoId)).first()
            assertThat(fetched).isEqualTo(doc)

            // Raw verification: stored as BSON String
            val raw = db.getCollection<BsonDocument>(collName).find(eq("_id", doc.mongoId)).first()
            val rawUrl = raw["html_url"]?.asString()?.value
            assertThat(rawUrl).isEqualTo(url.toString())

            coll.deleteOne(eq("_id", doc.mongoId))
        }

        @Test
        fun `partial update with ktor url value`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<UrlDoc>(collName)

            val created = Instant.fromEpochMilliseconds(1_870_000_000_000)
            val updated1 = Instant.fromEpochMilliseconds(1_870_000_111_111)

            val initial = UrlDoc(
                title = "url partial update",
                htmlUrl = Url("https://example.com/start"),
                createdAt = created,
                updatedAt = updated1,
            )

            coll.insertOne(initial)

            val newUrl = Url("https://example.com/updated/path?q=1")
            val updates = combine(
                Updates.set("html_url", newUrl),
                Updates.currentDate("touched_at"),
            )
            coll.updateOne(eq("_id", initial.mongoId), updates)

            val fetched = coll.find(eq("_id", initial.mongoId)).first()
            assertThat(fetched.htmlUrl).isEqualTo(newUrl)

            val raw = db.getCollection<BsonDocument>(collName).find(eq("_id", initial.mongoId)).first()
            assertThat(raw["html_url"]?.asString()?.value).isEqualTo(newUrl.toString())
            assertThat(raw["touched_at"]?.isDateTime).isTrue

            coll.deleteOne(eq("_id", initial.mongoId))
        }
    }

    // ---------- NESTED TESTS IN INNER CLASS ----------

    @Nested
    inner class NestedDocumentsTest {
        @Test
        fun `nested data class round trip`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<ParentDoc>(collName)

            val created = Instant.fromEpochMilliseconds(1_840_000_000_000)
            val updated = Instant.fromEpochMilliseconds(1_840_000_123_456)
            val authored = Instant.fromEpochMilliseconds(1_840_000_234_567)

            val parent = ParentDoc(
                title = "nested-docs",
                owner = MiniUser(id = 1001, login = "alice"),
                commits = listOf(
                    MiniCommit(ref = "main", sha = "a1", author = MiniUser(2, "bob"), authoredAt = authored),
                    MiniCommit(ref = "feature/x", sha = "b2", author = MiniUser(3, "cara")),
                ),
                latest = MiniCommit(ref = "main", sha = "a1", author = MiniUser(2, "bob"), authoredAt = authored),
                createdAt = created,
                updatedAt = updated,
            )

            coll.insertOne(parent)

            val fetched = coll.find(eq("_id", parent.mongoId)).first()
            assertThat(fetched).isEqualTo(parent)

            val raw = db.getCollection<BsonDocument>(collName).find(eq("_id", parent.mongoId)).first()

            val ownerDoc = raw["owner"]?.asDocument()
            assertThat(ownerDoc?.get("id")?.asInt64()?.value).isEqualTo(1001L)
            assertThat(ownerDoc?.get("login")?.asString()?.value).isEqualTo("alice")

            val commits = raw["commits"]?.asArray()
            assertThat(commits?.size).isEqualTo(2)
            val firstCommit = commits?.get(0)?.asDocument()
            assertThat(firstCommit?.get("ref")?.asString()?.value).isEqualTo("main")
            assertThat(firstCommit?.get("authored_at")?.isDateTime).isTrue

            val latest = raw["latest"]?.asDocument()
            assertThat(latest?.get("sha")?.asString()?.value).isEqualTo("a1")
            assertThat(latest?.get("author")?.asDocument()?.get("login")?.asString()?.value).isEqualTo("bob")

            coll.deleteOne(eq("_id", parent.mongoId))
        }

        @Test
        fun `partial updates with typed values on nested model`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<ParentDoc>(collName)

            val created = Instant.fromEpochMilliseconds(1_850_000_000_000)
            val updated1 = Instant.fromEpochMilliseconds(1_850_000_111_111)
            val updated2 = Instant.fromEpochMilliseconds(1_850_000_222_222)

            val base = ParentDoc(
                title = "partial-updates",
                owner = MiniUser(id = 5005, login = "delta"),
                commits = emptyList(),
                latest = null,
                createdAt = created,
                updatedAt = updated1,
            )
            coll.insertOne(base)

            // 1) $set a nested doc + typed Instant + $currentDate
            val newLatest = MiniCommit(ref = "hotfix", sha = "z9", author = MiniUser(9, "eve"))
            val updates1 = combine(
                Updates.set("latest", newLatest),
                Updates.set("updated_at", updated2),
                Updates.currentDate("server_touched_at"),
            )
            coll.updateOne(eq("_id", base.mongoId), updates1)

            val afterFirst = coll.find(eq("_id", base.mongoId)).first()
            assertThat(afterFirst.latest?.sha).isEqualTo("z9")
            assertThat(afterFirst.updatedAt).isEqualTo(updated2)

            val raw1 = db.getCollection<BsonDocument>(collName).find(eq("_id", base.mongoId)).first()
            assertThat(raw1["latest"]?.asDocument()?.get("ref")?.asString()?.value).isEqualTo("hotfix")
            assertThat(raw1["updated_at"]?.isDateTime).isTrue
            assertThat(raw1["updated_at"]?.asDateTime()?.value).isEqualTo(updated2.toEpochMilliseconds())
            assertThat(raw1["server_touched_at"]?.isDateTime).isTrue

            // 2) $unset latest, $set commits with typed list
            val updates2 = combine(
                Updates.unset("latest"),
                Updates.set(
                    "commits",
                    listOf(
                        MiniCommit(ref = "main", sha = "a1", author = MiniUser(2, "bob")),
                        MiniCommit(ref = "feature/y", sha = "c3", author = MiniUser(7, "gina")),
                    ),
                ),
            )
            coll.updateOne(eq("_id", base.mongoId), updates2)

            val afterSecond = coll.find(eq("_id", base.mongoId)).first()
            assertThat(afterSecond.latest).isNull()
            assertThat(afterSecond.commits.size).isEqualTo(2)

            val raw2 = db.getCollection<BsonDocument>(collName).find(eq("_id", base.mongoId)).first()
            assertThat(raw2.containsKey("latest")).isFalse
            val commits2 = raw2["commits"]?.asArray()
            assertThat(commits2?.size).isEqualTo(2)
            val c0 = commits2?.get(0)?.asDocument()
            assertThat(c0?.get("sha")?.asString()?.value).isEqualTo("a1")

            coll.deleteOne(eq("_id", base.mongoId))
        }
    }

    // ---------- POLYMORPHISM TESTS IN INNER CLASS ----------

    @Nested
    inner class PolymorphismTest {
        @Test
        fun `polymorphic round trip with sealed interface`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<PolymorphicDoc>(collName)

            val now = Instant.fromEpochMilliseconds(1_900_000_000_000)
            val later = Instant.fromEpochMilliseconds(1_900_000_111_111)

            val a = PolymorphicDoc(
                title = "poly-approved",
                event = Approved(at = now, by = "alice"),
                createdAt = now,
                updatedAt = later,
            )
            coll.insertOne(a)

            val b = PolymorphicDoc(
                title = "poly-changes",
                event = ChangesRequested(reason = "Add tests", at = later, by = "bob"),
                createdAt = now,
                updatedAt = later,
            )
            coll.insertOne(b)

            val fetchedA = coll.find(eq("_id", a.mongoId)).first()
            val fetchedB = coll.find(eq("_id", b.mongoId)).first()

            assertThat(fetchedA).isEqualTo(a)
            assertThat(fetchedB).isEqualTo(b)

            val rawA = db.getCollection<BsonDocument>(collName).find(eq("_id", a.mongoId)).first()
            val rawB = db.getCollection<BsonDocument>(collName).find(eq("_id", b.mongoId)).first()

            assertThat(rawA["event"]?.asDocument()).isNotNull
            assertThat(rawB["event"]?.asDocument()).isNotNull

            coll.deleteOne(eq("_id", a.mongoId))
            coll.deleteOne(eq("_id", b.mongoId))
        }
    }

    // ---------- UUID TESTS IN INNER CLASS ----------
    @Nested
    inner class UuidTest {

        @Test
        fun `uuid round trip and raw BSON verification`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<UuidDoc>(collName)

            val created = Instant.fromEpochMilliseconds(1_880_000_000_000)
            val updated = Instant.fromEpochMilliseconds(1_880_000_111_111)
            val uuid = UUID.fromString("12345678-90ab-cdef-1234-567890abcdef")

            val doc = UuidDoc(
                title = "uuid round trip",
                correlationId = uuid,
                createdAt = created,
                updatedAt = updated,
            )

            coll.insertOne(doc)
            val fetched = coll.find(eq("_id", doc.mongoId)).first()
            assertThat(fetched).isEqualTo(doc)

            // Raw BSON: UUID should be Binary subtype 4 (STANDARD)
            val raw = db.getCollection<BsonDocument>(collName).find(eq("_id", doc.mongoId)).first()
            val bin: BsonBinary? = raw["correlation_id"]?.asBinary()
            assertThat(bin).isNotNull
            assertThat(bin?.type).isEqualTo(BsonBinarySubType.UUID_STANDARD.value)

            // Cleanup
            coll.deleteOne(eq("_id", doc.mongoId))
        }

        @Test
        fun `partial update with bare UUID value`() = runTest {
            val db = client.database(dbName)
            val coll = db.getCollection<UuidDoc>(collName)

            val created = Instant.fromEpochMilliseconds(1_881_000_000_000)
            val updated = Instant.fromEpochMilliseconds(1_881_000_111_111)

            val initial = UuidDoc(
                title = "uuid partial update",
                correlationId = UUID.fromString("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                createdAt = created,
                updatedAt = updated,
            )
            coll.insertOne(initial)

            val newUuid = UUID.fromString("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb")
            coll.updateOne(
                eq("_id", initial.mongoId),
                Updates.set("correlation_id", newUuid), // bare UUID -> requires a UUID codec in registry
            )

            val fetched = coll.find(eq("_id", initial.mongoId)).first()
            assertThat(fetched.correlationId).isEqualTo(newUuid)

            val raw = db.getCollection<BsonDocument>(collName).find(eq("_id", initial.mongoId)).first()
            val bin: BsonBinary? = raw["correlation_id"]?.asBinary()
            assertThat(bin?.type).isEqualTo(BsonBinarySubType.UUID_STANDARD.value)

            coll.deleteOne(eq("_id", initial.mongoId))
        }
    }
}
