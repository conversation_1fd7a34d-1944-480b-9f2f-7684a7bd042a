package com.nextchaptersoftware.documents.mongo.serialization.utils

import com.mongodb.MongoClientSettings
import com.mongodb.kotlin.client.coroutine.MongoDatabase
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.MongoConfig
import com.nextchaptersoftware.documents.mongo.serialization.codec.BsonCodecRegistry
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.mongo.MongoApiClient
import io.ktor.http.Url
import kotlinx.coroutines.runBlocking
import org.bson.types.ObjectId

object MongoTestUtils {

    /** Shared client using GlobalConfig + your DEFAULT_CODEC_REGISTRY */
    val MONGO_API_CLIENT: MongoApiClient by lazy {
        createClient(config = GlobalConfig.Companion.INSTANCE.mongo) {
            codecRegistry(BsonCodecRegistry.DEFAULT_BSON_CODEC_REGISTRY)
        }
    }

    /** Create a client from a MongoConfig object (with optional extra settings). */
    fun createClient(
        config: MongoConfig,
        configureSettings: (MongoClientSettings.Builder.() -> Unit) = {
            codecRegistry(BsonCodecRegistry.DEFAULT_BSON_CODEC_REGISTRY)
        },
    ): MongoApiClient = MongoApiClient(
        baseUri = Url(config.baseApiUri),
        userName = config.userName,
        password = config.password,
        authDatabase = config.authDatabase,
        useTls = config.useTls,
        maxPoolSize = config.maxPoolSize,
        appName = config.appName,
        timeout = config.timeout,
        serverSelectionTimeout = config.serverSelectionTimeout,
        maxConnecting = config.maxConnecting,
        configureSettings = configureSettings,
    )

    // ---------- Optional test helpers (handy for integration tests) ----------

    fun tempDbName(prefix: String = "it_db"): String =
        "${prefix}_${System.currentTimeMillis()}_${ObjectId().toHexString().takeLast(6)}"

    fun tempCollectionName(prefix: String = "it_col"): String =
        "${prefix}_${System.currentTimeMillis()}_${ObjectId().toHexString().takeLast(6)}"

    /**
     * Execute a suspending [block] with a temporary database and drop it afterwards (best-effort).
     */
    fun <T> withTempDb(
        client: MongoApiClient = MONGO_API_CLIENT,
        dbName: String = tempDbName(),
        block: suspend (MongoDatabase) -> T,
    ): T = runBlocking {
        val db = client.database(dbName)
        try {
            block(db)
        } finally {
            runSuspendCatching { db.drop() }
        }
    }
}
