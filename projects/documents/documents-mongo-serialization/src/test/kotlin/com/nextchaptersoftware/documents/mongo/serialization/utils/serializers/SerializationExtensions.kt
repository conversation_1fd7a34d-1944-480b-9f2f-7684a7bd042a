package com.nextchaptersoftware.documents.mongo.serialization.utils.serializers

import io.ktor.http.Url
import java.util.UUID
import kotlin.time.Instant
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule

object SerializationExtensions {
    /**
     * Serializer for kotlin.time.Instant.
     * Encodes to ISO-8601 string (UTC) and decodes from the same.
     */
    object InstantIso8601Serializer : KSerializer<Instant> {
        override val descriptor: SerialDescriptor =
            PrimitiveSerialDescriptor("InstantIso8601", PrimitiveKind.STRING)

        override fun serialize(encoder: Encoder, value: Instant) {
            // Encode to ISO string
            encoder.encodeString(value.toString()) // Instant already uses ISO-8601
        }

        override fun deserialize(decoder: Decoder): Instant {
            val text = decoder.decodeString()
            return Instant.parse(text)
        }
    }

    /**
     * Serializer for java.util.UUID.
     * Encodes to standard 36-character UUID string, decodes from same.
     */
    object UuidSerializer : KSerializer<UUID> {
        override val descriptor: SerialDescriptor =
            PrimitiveSerialDescriptor("UuidAsString", PrimitiveKind.STRING)

        override fun serialize(encoder: Encoder, value: UUID) {
            encoder.encodeString(value.toString())
        }

        override fun deserialize(decoder: Decoder): UUID {
            return UUID.fromString(decoder.decodeString())
        }
    }

    /**
     * Serializer for ktor.http.Url.
     * Encodes to the URL string, decodes via Url() factory.
     */
    object KtorUrlSerializer : KSerializer<Url> {
        override val descriptor: SerialDescriptor =
            PrimitiveSerialDescriptor("KtorUrl", PrimitiveKind.STRING)

        override fun serialize(encoder: Encoder, value: Url) {
            encoder.encodeString(value.toString())
        }

        override fun deserialize(decoder: Decoder): Url {
            return Url(decoder.decodeString())
        }
    }

    val module = SerializersModule {
        contextual(Instant::class, InstantIso8601Serializer)
        contextual(UUID::class, UuidSerializer)
        contextual(Url::class, KtorUrlSerializer)
    }

    val json by lazy {
        Json {
            ignoreUnknownKeys = true
            explicitNulls = false
            encodeDefaults = false
            serializersModule = module
        }
    }
}
