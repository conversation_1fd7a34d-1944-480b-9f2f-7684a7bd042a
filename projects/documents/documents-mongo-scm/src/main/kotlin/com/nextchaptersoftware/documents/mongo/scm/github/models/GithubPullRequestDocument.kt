package com.nextchaptersoftware.documents.mongo.scm.github.models

import com.nextchaptersoftware.documents.mongo.core.models.MetadataDocument
import io.ktor.http.Url
import kotlin.time.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.bson.types.ObjectId

/**
 * Container document for storing GitHub PRs in MongoDB,
 * enriched with metadata, reviews, and comments.
 */
@Serializable
data class GitHubPullRequestContainerDocument(
    @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),

    // metadata
    @SerialName("metadata") val metadata: MetadataDocument,

    // The pull request itself (matches GitHub API response)
    @SerialName("pull_request") val pullRequest: GitHubPullRequestDocument,

    // All reviews associated with this pull request
    @SerialName("reviews") val reviews: List<GitHubPullRequestReviewDocument> = emptyList(),

    // All review comments (line-level or commit-level comments)
    @SerialName("review_comments") val reviewComments: List<GitHubPullRequestReviewCommentDocument> = emptyList(),

    // Optional: issue-style comments (general PR discussion, not attached to a file)
    @SerialName("issue_comments") val issueComments: List<GitHubIssueCommentDocument> = emptyList(),
)

/* =========================================================
 * Pull Request + Reviews
 * ========================================================= */

@Serializable
data class GitHubPullRequestDocument(
    @SerialName("id") val id: Long,
    @SerialName("number") val number: Int,
    @SerialName("title") val title: String,
    @SerialName("body") val body: String? = null,
    @SerialName("merge_commit_sha") val mergeCommitSha: String? = null,
    @SerialName("base") val base: GitHubBranchRefDocument? = null,
    @SerialName("head") val head: GitHubBranchRefDocument? = null,
    @SerialName("user") val user: GitHubUserDocument? = null,
    @Contextual @SerialName("closed_at") val closedAt: Instant? = null,
    @Contextual @SerialName("merged_at") val mergedAt: Instant? = null,
    @Contextual @SerialName("updated_at") val updatedAt: Instant,
    @Contextual @SerialName("created_at") val createdAt: Instant,
    @SerialName("assignees") val assignees: List<GitHubUserDocument> = emptyList(),
    @SerialName("requested_reviewers") val requestedReviewers: List<GitHubUserDocument> = emptyList(),
    @Contextual @SerialName("html_url") val htmlUrl: Url,
    @SerialName("state") val state: GitHubPrStateDocument, // "open" | "closed"
    @SerialName("draft") val draft: Boolean? = null,
)

@Serializable
enum class GitHubPrStateDocument {
    @SerialName("open")
    OPEN,

    @SerialName("closed")
    CLOSED,
}

@Serializable
data class GitHubPullRequestReviewDocument(
    @SerialName("id") val id: Long,
    @SerialName("body") val body: String? = null,
    @SerialName("user") val user: GitHubUserDocument,
    @SerialName("state") val state: GitHubReviewStateDocument,
    @SerialName("commit_id") val commitId: String? = null,
    @Contextual @SerialName("submitted_at") val submittedAt: Instant? = null,
    @Contextual @SerialName("html_url") val htmlUrl: Url,
    @SerialName("pull_request_url") val pullRequestUrl: String? = null,
)

@Serializable
enum class GitHubReviewStateDocument {
    @SerialName("COMMENTED")
    COMMENTED,

    @SerialName("APPROVED")
    APPROVED,

    @SerialName("CHANGES_REQUESTED")
    CHANGES_REQUESTED,

    @SerialName("DISMISSED")
    DISMISSED,

    @SerialName("PENDING")
    PENDING,
}

/* =========================================================
 * Review Comments (line/file-level comments)
 * ========================================================= */

@Serializable
data class GitHubPullRequestReviewCommentDocument(
    @SerialName("id") val id: Long,
    @SerialName("body") val body: String,
    @SerialName("user") val user: GitHubUserDocument,
    @SerialName("commit_id") val commitId: String,
    @SerialName("path") val path: String? = null, // file path in repo
    @SerialName("diff_hunk") val diffHunk: String? = null,
    @SerialName("original_position") val originalPosition: Int? = null,
    @SerialName("position") val position: Int? = null,
    @SerialName("side") val side: String? = null, // LEFT | RIGHT
    @Contextual @SerialName("created_at") val createdAt: Instant,
    @Contextual @SerialName("updated_at") val updatedAt: Instant,
    @Contextual @SerialName("html_url") val htmlUrl: Url,
)

/* =========================================================
 * Issue-style PR comments (general discussion, not review)
 * ========================================================= */

@Serializable
data class GitHubIssueCommentDocument(
    @SerialName("id") val id: Long,
    @SerialName("body") val body: String,
    @SerialName("user") val user: GitHubUserDocument,
    @Contextual @SerialName("created_at") val createdAt: Instant,
    @Contextual @SerialName("updated_at") val updatedAt: Instant,
    @Contextual @SerialName("html_url") val htmlUrl: Url,
)

/* =========================================================
 * Shared sub-docs
 * ========================================================= */

@Serializable
data class GitHubBranchRefDocument(
    @SerialName("label") val label: String? = null,
    @SerialName("ref") val ref: String? = null,
    @SerialName("sha") val sha: String,
    @SerialName("user") val user: GitHubUserDocument? = null,
    @SerialName("repo") val repo: GitHubRepoDocument? = null,
)

@Serializable
data class GitHubRepoDocument(
    @SerialName("id") val id: Long,
    @SerialName("name") val name: String,
    @SerialName("full_name") val fullName: String,
    @SerialName("private") val isPrivate: Boolean,
    @SerialName("owner") val owner: GitHubUserDocument? = null,
    @Contextual @SerialName("html_url") val htmlUrl: Url? = null,
    @Contextual @SerialName("url") val apiUrl: Url? = null,
)

@Serializable
data class GitHubUserDocument(
    @SerialName("id") val id: Long,
    @SerialName("login") val login: String,
    @SerialName("type") val type: String? = null, // "User" | "Bot" | "Organization"
    @SerialName("site_admin") val siteAdmin: Boolean? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("email") val email: String? = null,
    @SerialName("avatar_url") val avatarUrl: String? = null,
    @Contextual @SerialName("html_url") val htmlUrl: Url? = null,
)
