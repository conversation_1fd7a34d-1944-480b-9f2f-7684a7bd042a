package com.nextchaptersoftware.documents.mongo.scm.github.stores

import com.mongodb.client.model.Filters
import com.mongodb.client.model.IndexOptions
import com.mongodb.client.model.Indexes
import com.mongodb.client.model.Sorts
import com.mongodb.client.model.UpdateOptions
import com.mongodb.client.model.Updates
import com.nextchaptersoftware.documents.mongo.core.context.DatabaseContext
import com.nextchaptersoftware.documents.mongo.core.context.GlobalMongoDatabaseContexts
import com.nextchaptersoftware.documents.mongo.core.models.MetadataDocument
import com.nextchaptersoftware.documents.mongo.core.store.MongoDocumentStore
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubPullRequestContainerDocument
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.toList
import org.bson.conversions.Bson
import org.bson.types.ObjectId

class GitHubPullRequestDocumentStore(
    override val db: DatabaseContext = GlobalMongoDatabaseContexts.INSTANCE,
    override val collectionName: String = "github_pull_requests",
) : MongoDocumentStore<GitHubPullRequestContainerDocument> {

    override val entityClass: KClass<GitHubPullRequestContainerDocument> =
        GitHubPullRequestContainerDocument::class

    // -------------------------
    // Indexes
    // -------------------------
    override suspend fun ensureIndexes() {
        val keys = Indexes.compoundIndex(
            Indexes.ascending("metadata.tenantId"),
            Indexes.ascending("metadata.collectionId"),
            Indexes.ascending("metadata.groupId"),
            Indexes.ascending("pull_request.number"),
        )
        val options = IndexOptions().name("uniq_pr_by_meta_and_number").unique(true)
        collection.createIndex(keys, options)

        collection.createIndex(
            Indexes.ascending("pull_request.id"),
            IndexOptions().name("pull_request_id").unique(false),
        )

        collection.createIndex(
            Indexes.compoundIndex(
                Indexes.ascending("metadata.group_id"),
                Indexes.ascending("pull_request.number"),
            ),
            IndexOptions().unique(true).name("uniq_repo_pr_number"),
        )
    }

    // -------------------------
    // Queries
    // -------------------------

    suspend fun getByMongoId(id: ObjectId) =
        collection.find(Filters.eq("_id", id)).firstOrNull()

    suspend fun getByNumber(meta: MetadataDocument, number: Int) =
        collection.find(metaAndNumberFilter(meta, number)).firstOrNull()

    suspend fun listByRepo(meta: MetadataDocument, limit: Int = 100) =
        collection.find(metaFilter(meta))
            .sort(Sorts.descending("pull_request.updated_at"))
            .limit(limit)
            .toList()

    // -------------------------
    // Mutations
    // -------------------------

    suspend fun insert(doc: GitHubPullRequestContainerDocument) {
        collection.insertOne(doc)
    }

    suspend fun upsertByNumber(meta: MetadataDocument, doc: GitHubPullRequestContainerDocument) {
        val filter = metaAndNumberFilter(meta, doc.pullRequest.number)
        val update = Updates.combine(
            Updates.set("metadata", doc.metadata),
            Updates.set("pull_request", doc.pullRequest),
            Updates.set("reviews", doc.reviews),
            Updates.set("review_comments", doc.reviewComments),
            Updates.set("issue_comments", doc.issueComments),
            Updates.setOnInsert("_id", doc.mongoId),
        )
        collection.updateOne(filter, update, UpdateOptions().upsert(true))
    }

    // -------------------------
    // Filters
    // -------------------------

    private fun metaFilter(meta: MetadataDocument): Bson =
        Filters.and(
            Filters.eq("metadata.tenantId", meta.tenantId),
            Filters.eq("metadata.collectionId", meta.collectionId),
            Filters.eq("metadata.groupId", meta.groupId),
        )

    private fun metaAndNumberFilter(meta: MetadataDocument, number: Int): Bson =
        Filters.and(metaFilter(meta), Filters.eq("pull_request.number", number))
}
