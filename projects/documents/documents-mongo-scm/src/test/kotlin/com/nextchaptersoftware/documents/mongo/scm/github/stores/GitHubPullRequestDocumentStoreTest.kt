package com.nextchaptersoftware.documents.mongo.scm.github.stores

import com.mongodb.MongoWriteException
import com.mongodb.client.model.Filters.eq
import com.nextchaptersoftware.documents.mongo.core.context.DatabaseContext
import com.nextchaptersoftware.documents.mongo.core.context.MongoDatabaseContext
import com.nextchaptersoftware.documents.mongo.core.models.MetadataDocument
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubBranchRefDocument
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubPrStateDocument
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubPullRequestContainerDocument
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubPullRequestDocument
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubRepoDocument
import com.nextchaptersoftware.documents.mongo.scm.github.models.GitHubUserDocument
import com.nextchaptersoftware.documents.mongo.serialization.utils.MongoTestUtils
import io.ktor.http.Url
import java.util.UUID
import kotlin.time.Instant
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.bson.Document
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GitHubPullRequestDocumentStoreTest {

    private val client = MongoTestUtils.MONGO_API_CLIENT

    private lateinit var dbName: String
    private lateinit var dbCtx: DatabaseContext

    private lateinit var store: GitHubPullRequestDocumentStore

    @BeforeAll
    fun setup() {
        // isolate each run in its own database
        dbName = MongoTestUtils.tempDbName("it_github_pr_store")
        dbCtx = MongoDatabaseContext(dbName, client)
        store = GitHubPullRequestDocumentStore(dbCtx)
    }

    @AfterAll
    fun teardown() = runTest {
        // drop the whole temp DB and close the shared client
        client.database(dbName).drop()
        client.close()
    }

    @Test
    fun `ensureIndexes creates unique index on pull_request_id, round-trip insert works, and duplicate is rejected`() = runTest {
        // 1) ensure indexes
        store.ensureIndexes()

        // 2) prepare a sample container doc
        val prId = 1234567890L
        val metadata = MetadataDocument(
            tenantId = UUID.fromString("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
            collectionId = UUID.fromString("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
            groupId = UUID.fromString("cccccccc-cccc-cccc-cccc-cccccccccccc"),
            id = UUID.fromString("dddddddd-dddd-dddd-dddd-dddddddddddd"),
        )

        val user = GitHubUserDocument(
            id = 987654321L,
            login = "octocat",
            type = "User",
            siteAdmin = false,
            name = "The Octo Cat",
            email = "<EMAIL>",
            avatarUrl = "https://avatars.githubusercontent.com/u/1?v=4",
            htmlUrl = Url("https://github.com/octocat"),
        )

        val repo = GitHubRepoDocument(
            id = 54321L,
            name = "backend",
            fullName = "unblocked/backend",
            isPrivate = true,
            owner = user,
            htmlUrl = Url("https://github.com/unblocked/backend"),
            apiUrl = Url("https://api.github.com/repos/unblocked/backend"),
        )

        val base = GitHubBranchRefDocument(
            label = "unblocked:main",
            ref = "main",
            sha = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
            user = user,
            repo = repo,
        )

        val head = GitHubBranchRefDocument(
            label = "unblocked:feature/amazing",
            ref = "feature/amazing",
            sha = "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
            user = user,
            repo = repo,
        )

        val now = Instant.fromEpochMilliseconds(1_900_000_000_000)
        val pr = GitHubPullRequestDocument(
            id = prId,
            number = 42,
            title = "Add amazing feature",
            body = "This PR introduces an amazing feature with tests.",
            mergeCommitSha = null,
            base = base,
            head = head,
            user = user,
            closedAt = null,
            mergedAt = null,
            updatedAt = now,
            createdAt = now,
            assignees = listOf(user),
            requestedReviewers = emptyList(),
            htmlUrl = Url("https://github.com/unblocked/backend/pull/42"),
            state = GitHubPrStateDocument.OPEN,
            draft = false,
        )

        val container = GitHubPullRequestContainerDocument(
            mongoId = ObjectId(),
            metadata = metadata,
            pullRequest = pr,
            reviews = emptyList(),
        )

        // 3) insert & round-trip
        val typedColl = store.collection
        typedColl.insertOne(container)

        val fetched = typedColl.find(eq("_id", container.mongoId)).first()
        assertThat(fetched.mongoId).isEqualTo(container.mongoId)
        assertThat(fetched.metadata.tenantId).isEqualTo(metadata.tenantId)
        assertThat(fetched.pullRequest.id).isEqualTo(prId)
        assertThat(fetched.pullRequest.number).isEqualTo(42)
        assertThat(fetched.pullRequest.user?.login).isEqualTo("octocat")

        // 4) verify index exists (by name pattern and key)
        val indexDocs = typedColl.listIndexes().toList()
        // find the index that has key {"pull_request.id": 1} and unique true
        val prIdIndex = indexDocs.firstOrNull { idx ->
            val keyDoc = idx.get("key") as? Document
            keyDoc?.get("pull_request.id") == 1
        }
        assertThat(prIdIndex).isNotNull()

        // 5) duplicate insert should fail due to unique index on pull_request.id
        val dupContainer = container.copy(
            mongoId = ObjectId(), // different _id is fine
            pullRequest = container.pullRequest.copy(
                // same PR id → should violate the unique index
                id = prId,
            ),
        )

        assertThatThrownBy {
            // use blocking bridge for assertion brevity
            kotlinx.coroutines.runBlocking {
                typedColl.insertOne(dupContainer)
            }
        }
            .isInstanceOf(MongoWriteException::class.java)

        // cleanup the inserted doc
        typedColl.deleteOne(eq("_id", container.mongoId))
    }

    @Test
    fun `store exposes expected collection and name`() {
        assertThat(store.collectionName).isEqualTo("github_pull_requests")
        // 'collection' should be resolvable without NPE / exceptions
        val coll = store.collection
        assertThat(coll.namespace.collectionName).isEqualTo("github_pull_requests")
        assertThat(coll.namespace.databaseName).isEqualTo(dbName)
    }
}
