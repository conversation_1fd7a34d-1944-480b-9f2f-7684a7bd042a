@file:OptIn(kotlinx.serialization.ExperimentalSerializationApi::class)

package com.nextchaptersoftware.documents.mongo.scm.github.models

import com.nextchaptersoftware.documents.mongo.core.models.MetadataDocument
import com.nextchaptersoftware.documents.mongo.serialization.utils.MongoTestUtils
import com.nextchaptersoftware.documents.mongo.serialization.utils.serializers.SerializationExtensions.json
import java.nio.charset.StandardCharsets
import java.util.UUID
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.builtins.ListSerializer
import org.assertj.core.api.Assertions.assertThat
import org.bson.Document
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GitHubPullRequestDocumentTest {

    private val client = MongoTestUtils.MONGO_API_CLIENT
    private lateinit var dbName: String
    private lateinit var collName: String

    @BeforeAll
    fun setup() {
        dbName = MongoTestUtils.tempDbName("gh_pr_ingest_res")
        collName = MongoTestUtils.tempCollectionName("prs")
    }

    @AfterAll
    fun teardown() = runTest {
        client.database(dbName).drop()
        client.close()
    }

    @Test
    fun `parse resources to models to mongo roundtrip (with reviews & comments)`() = runTest {
        // --- Load resources ---
        val prPayload = readResource("/github/pull_request.json")
        val reviewsPayload = readResource("/github/reviews.json")
        val reviewCommentsPayload = readResource("/github/review_comments.json")
        val issueCommentsPayload = readResource("/github/issue_comments.json")

        // --- Deserialize to your document models ---
        val prDoc = json.decodeFromString(GitHubPullRequestDocument.serializer(), prPayload)
        val reviews = json.decodeFromString(
            ListSerializer(GitHubPullRequestReviewDocument.serializer()),
            reviewsPayload,
        )
        val reviewComments = json.decodeFromString(
            ListSerializer(GitHubPullRequestReviewCommentDocument.serializer()),
            reviewCommentsPayload,
        )
        val issueComments = json.decodeFromString(
            ListSerializer(GitHubIssueCommentDocument.serializer()),
            issueCommentsPayload,
        )

        // Quick sanity assertions on decoded models (unknown fields ignored)
        assertThat(prDoc.number).isEqualTo(42)
        assertThat(prDoc.user?.login).isEqualTo("rashin")
        assertThat(prDoc.state).isEqualTo(GitHubPrStateDocument.OPEN)
        assertThat(prDoc.base?.repo?.fullName).isEqualTo("unblocked/backend")
        assertThat(reviews.map { it.state }.toSet())
            .containsExactlyInAnyOrder(
                GitHubReviewStateDocument.APPROVED,
                GitHubReviewStateDocument.CHANGES_REQUESTED,
            )
        assertThat(reviewComments).isNotEmpty
        assertThat(issueComments).isNotEmpty

        // --- Build container with metadata & insert into Mongo ---
        val container = GitHubPullRequestContainerDocument(
            mongoId = ObjectId(),
            metadata = MetadataDocument(
                // Use your actual field names here; matching your original test
                tenantId = UUID.fromString("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                collectionId = UUID.fromString("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
                groupId = UUID.fromString("cccccccc-cccc-cccc-cccc-cccccccccccc"),
                id = UUID.fromString("dddddddd-dddd-dddd-dddd-dddddddddddd"),
            ),
            pullRequest = prDoc,
            reviews = reviews,
            reviewComments = reviewComments,
            issueComments = issueComments,
        )

        val db = client.database(dbName)
        val coll = db.getCollection<GitHubPullRequestContainerDocument>(collName)

        coll.insertOne(container)

        // --- Typed fetch roundtrip ---
        val fetched = coll.find(Document("_id", container.mongoId)).first()
        assertThat(fetched.mongoId).isEqualTo(container.mongoId)
        assertThat(fetched.metadata.tenantId).isEqualTo(container.metadata.tenantId)
        assertThat(fetched.pullRequest.number).isEqualTo(42)

        // Check a couple of PR fields
        assertThat(fetched.pullRequest.assignees.firstOrNull()?.login).isEqualTo("reviewer-a")
        assertThat(fetched.pullRequest.htmlUrl.toString()).contains("/pull/42")

        // Reviews + comments counts
        assertThat(fetched.reviews.size).isEqualTo(reviews.size)
        assertThat(fetched.reviewComments.size).isEqualTo(reviewComments.size)
        assertThat(fetched.issueComments.size).isEqualTo(issueComments.size)

        // Spot-check review comments (file/line comments)
        val firstRc = fetched.reviewComments.firstOrNull()
        assertThat(firstRc?.path).isEqualTo("src/main/App.kt")
        assertThat(firstRc?.user?.login).isEqualTo("reviewer1")
        assertThat(firstRc?.htmlUrl.toString()).contains("#discussion_r")

        // Spot-check issue comments (general PR discussion)
        val firstIc = fetched.issueComments.firstOrNull()
        assertThat(firstIc?.user?.login).isEqualTo("projectmanager")
        assertThat(firstIc?.htmlUrl.toString()).contains("#issuecomment-")

        // --- Raw BSON assertions (dates as DateTime; URLs as String) ---
        val raw = db.getCollection<org.bson.BsonDocument>(collName)
            .find(Document("_id", container.mongoId))
            .first()

        // Pull request block
        val prDocBson = raw?.get("pull_request")?.asDocument()
        assertThat(prDocBson?.get("updated_at")?.isDateTime).isTrue
        assertThat(prDocBson?.get("created_at")?.isDateTime).isTrue
        assertThat(prDocBson?.get("html_url")?.isString).isTrue

        // Review comments block
        val rcBson = raw?.get("review_comments")?.asArray()
        val rc0 = rcBson?.getOrNull(0)?.asDocument()
        assertThat(rcBson?.size).isEqualTo(reviewComments.size)
        assertThat(rc0?.get("created_at")?.isDateTime).isTrue
        assertThat(rc0?.get("updated_at")?.isDateTime).isTrue
        assertThat(rc0?.get("html_url")?.isString).isTrue

        // Issue comments block
        val icBson = raw?.get("issue_comments")?.asArray()
        val ic0 = icBson?.getOrNull(0)?.asDocument()
        assertThat(icBson?.size).isEqualTo(issueComments.size)
        assertThat(ic0?.get("created_at")?.isDateTime).isTrue
        assertThat(ic0?.get("updated_at")?.isDateTime).isTrue
        assertThat(ic0?.get("html_url")?.isString).isTrue

        // Cleanup
        coll.deleteOne(Document("_id", container.mongoId))
    }

    private fun readResource(path: String): String {
        val stream = this::class.java.getResourceAsStream(path)
        requireNotNull(stream) { "Missing test resource: $path" }
        return stream.readAllBytes().toString(StandardCharsets.UTF_8)
    }

    // Safe array accessor for BSON arrays
    private fun org.bson.BsonArray.getOrNull(index: Int) =
        if (index in 0 until size) get(index) else null
}
