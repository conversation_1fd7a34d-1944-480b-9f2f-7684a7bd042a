package com.nextchaptersoftware.documents.mongo.core.context

import com.mongodb.kotlin.client.coroutine.MongoCollection
import com.nextchaptersoftware.mongo.MongoApiClient
import kotlin.reflect.KClass

class MongoDatabaseContext(
    override val name: String,
    override val client: MongoApiClient,
) : DatabaseContext {
    override fun <T : Any> collection(name: String, kClass: KClass<T>): MongoCollection<T> =
        client.database(this.name).getCollection(name, kClass.java)
}
