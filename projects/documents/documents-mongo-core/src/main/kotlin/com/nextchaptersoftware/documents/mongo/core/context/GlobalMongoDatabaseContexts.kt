package com.nextchaptersoftware.documents.mongo.core.context

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.documents.mongo.serialization.codec.BsonCodecRegistry
import com.nextchaptersoftware.mongo.MongoApiClient

object GlobalMongoDatabaseContexts : AutoCloseable {
    private val mongoCfg = GlobalConfig.INSTANCE.mongo

    private val client: MongoApiClient by lazy {
        MongoApiClient.fromConfig(
            config = mongoCfg,
            configureSettings = {
                codecRegistry(BsonCodecRegistry.DEFAULT_BSON_CODEC_REGISTRY)
            },
        )
    }

    /** Singleton context for the default database from config. */
    val INSTANCE: DatabaseContext by lazy {
        MongoDatabaseContext(mongoCfg.dbName, client)
    }

    /** Context for any other database name. */
    fun context(dbName: String): DatabaseContext =
        MongoDatabaseContext(dbName, client)

    override fun close() {
        client.close()
    }
}
