package com.nextchaptersoftware.documents.mongo.core.models

import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Shared metadata container for documents ingested into the system.
 *
 * This document captures the *identity metadata* that uniquely identifies
 * an item within a tenant → collection → group hierarchy.
 *
 * It mirrors the mapping used in ingestion:
 *   - tenant_id      → org / organization identifier
 *   - collection_id  → installation / integration identifier
 *   - group_id       → logical grouping within the collection (e.g., project, repo)
 *   - id             → unique document identifier within the group
 */
@Serializable
data class MetadataDocument(
    /** The tenant/org this document belongs to. */
    @Contextual @SerialName("tenant_id") val tenantId: UUID,

    /** The collection (installation/integration) under which this document is stored. */
    @Contextual @SerialName("collection_id") val collectionId: UUID,

    /** The group identifier within the collection (e.g., Jira project ID, GitHub repo ID). */
    @Contextual @SerialName("group_id") val groupId: UUID,

    /** The unique document identifier within the group (e.g., issue ID, PR ID). */
    @Contextual @SerialName("id") val id: UUID? = null,
)
