// core/store/MongoDocumentStore.kt
package com.nextchaptersoftware.documents.mongo.core.store

import com.mongodb.kotlin.client.coroutine.MongoCollection
import com.nextchaptersoftware.documents.mongo.core.context.DatabaseContext
import kotlin.reflect.KClass

/** Base contract for typed Mongo stores. */
interface MongoDocumentStore<T : Any> {
    val db: DatabaseContext
    val entityClass: KClass<T>

    /** Canonical collection name for this entity. */
    val collectionName: String

    /** Lazily resolved typed collection (safe to share). */
    val collection: MongoCollection<T> get() = db.collection(collectionName, entityClass)

    /**
     * Ensure indexes required for this store exist.
     * Called at startup or migration time.
     */
    suspend fun ensureIndexes()
}
