// core/context/DatabaseContext.kt
package com.nextchaptersoftware.documents.mongo.core.context

import com.mongodb.kotlin.client.coroutine.MongoCollection
import com.nextchaptersoftware.mongo.MongoApiClient
import kotlin.reflect.KClass

/** Logical DB namespace + resolver for typed collections. */
interface DatabaseContext {
    val name: String
    val client: MongoApiClient

    fun <T : Any> collection(name: String, kClass: KClass<T>): MongoCollection<T>
}
