package com.nextchaptersoftware.ml.query.context

import com.nextchaptersoftware.data.preset.DataSourcePresetConfiguration
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.MLInferenceCategory
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberAndPerson
import com.nextchaptersoftware.db.models.OrgMemberBundle
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.stores.OrgMemberAndIdentity
import com.nextchaptersoftware.dsac.DsacContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class QueryContextTest {

    private fun createMockQuestioner(): OrgMemberBundle {
        return OrgMemberBundle(
            orgMemberAndPerson = OrgMemberAndPerson(
                orgMember = MockDataClasses.orgMember(),
                person = MockDataClasses.person(),
            ),
            orgMembersAndIdentities = listOf(
                OrgMemberAndIdentity(
                    member = MockDataClasses.member(),
                    identity = MockDataClasses.identity(),
                    org = MockDataClasses.org(),
                    orgMember = MockDataClasses.orgMember(),
                ),
            ),
        )
    }

    private fun createMockDsacContext(): DsacContext {
        return DsacContext.disabled(OrgId.random())
    }

    @Test
    fun `inferenceCategory defaults to null`() {
        val queryContext = QueryContext(
            org = MockDataClasses.org(),
            template = MockDataClasses.template(),
            questioner = createMockQuestioner(),
            dsacContext = createMockDsacContext(),
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
        )

        assertThat(queryContext.inferenceCategory).isNull()
    }

    @Test
    fun `inferenceCategory can be set to Api`() {
        val queryContext = QueryContext(
            org = MockDataClasses.org(),
            template = MockDataClasses.template(),
            questioner = createMockQuestioner(),
            dsacContext = createMockDsacContext(),
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
            inferenceCategory = MLInferenceCategory.Api,
        )

        assertThat(queryContext.inferenceCategory).isEqualTo(MLInferenceCategory.Api)
    }

    @Test
    fun `inferenceCategory can be set to Search`() {
        val queryContext = QueryContext(
            org = MockDataClasses.org(),
            template = MockDataClasses.template(),
            questioner = createMockQuestioner(),
            dsacContext = createMockDsacContext(),
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
            inferenceCategory = MLInferenceCategory.Search,
        )

        assertThat(queryContext.inferenceCategory).isEqualTo(MLInferenceCategory.Search)
    }

    @Test
    fun `inferenceCategory can be set to Mcp`() {
        val queryContext = QueryContext(
            org = MockDataClasses.org(),
            template = MockDataClasses.template(),
            questioner = createMockQuestioner(),
            dsacContext = createMockDsacContext(),
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
            inferenceCategory = MLInferenceCategory.Mcp,
        )

        assertThat(queryContext.inferenceCategory).isEqualTo(MLInferenceCategory.Mcp)
    }

    @Test
    fun `inferenceCategory does not affect other properties`() {
        val org = MockDataClasses.org()
        val template = MockDataClasses.template()
        val questioner = createMockQuestioner()
        val dsacContext = createMockDsacContext()
        val query = "test query"
        val productAgent = ProductAgentType.Api

        val queryContext = QueryContext(
            org = org,
            template = template,
            questioner = questioner,
            dsacContext = dsacContext,
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
            query = query,
            productAgent = productAgent,
            inferenceCategory = MLInferenceCategory.Api,
        )

        assertThat(queryContext.inferenceCategory).isEqualTo(MLInferenceCategory.Api)
        assertThat(queryContext.org).isEqualTo(org)
        assertThat(queryContext.questioner).isEqualTo(questioner)
        assertThat(queryContext.dsacContext).isEqualTo(dsacContext)
        assertThat(queryContext.query).isEqualTo(query)
        assertThat(queryContext.productAgent).isEqualTo(productAgent)
    }

    @Test
    fun `queryTemplate property works with inferenceCategory set`() {
        val template = MockDataClasses.template()
        val queryContext = QueryContext(
            org = MockDataClasses.org(),
            template = template,
            questioner = createMockQuestioner(),
            dsacContext = createMockDsacContext(),
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
            inferenceCategory = MLInferenceCategory.Api,
        )

        assertThat(queryContext.inferenceCategory).isEqualTo(MLInferenceCategory.Api)
        assertThat(queryContext.queryTemplate).isEqualTo(template)
    }

    @Test
    fun `answerPreferences property works with inferenceCategory set`() {
        val queryContext = QueryContext(
            org = MockDataClasses.org(),
            template = MockDataClasses.template(),
            questioner = createMockQuestioner(),
            dsacContext = createMockDsacContext(),
            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
            inferenceCategory = MLInferenceCategory.Api,
        )

        assertThat(queryContext.inferenceCategory).isEqualTo(MLInferenceCategory.Api)
        assertThat(queryContext.answerPreferences).isNull() // Default when not in demo mode
    }
}
