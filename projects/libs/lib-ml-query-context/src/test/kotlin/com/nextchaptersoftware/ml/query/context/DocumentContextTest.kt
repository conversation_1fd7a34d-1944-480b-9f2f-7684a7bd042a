package com.nextchaptersoftware.ml.query.context

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.LocalDocumentVisibility
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.ml.query.context.MLQuery
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class DocumentContextTest {

    @Test
    fun `ragSkipped defaults to false`() {
        val documentContext = DocumentContext(
            documentQueryContext = DocumentQueryContext(
                documentQueryText = "test query",
                documentQuery = MLQuery.MLTextQuery("test query"),
            ),
            documents = emptyList(),
            mlFunctionContent = emptySet(),
        )

        assertThat(documentContext.ragSkipped).isFalse()
    }

    @Test
    fun `ragSkipped can be set to true`() {
        val documentContext = DocumentContext(
            documentQueryContext = DocumentQueryContext(
                documentQueryText = "test query",
                documentQuery = MLQuery.MLTextQuery("test query"),
            ),
            documents = emptyList(),
            mlFunctionContent = emptySet(),
            ragSkipped = true,
        )

        assertThat(documentContext.ragSkipped).isTrue()
    }

    @Test
    fun `ragSkipped can be set to false explicitly`() {
        val documentContext = DocumentContext(
            documentQueryContext = DocumentQueryContext(
                documentQueryText = "test query",
                documentQuery = MLQuery.MLTextQuery("test query"),
            ),
            documents = emptyList(),
            mlFunctionContent = emptySet(),
            ragSkipped = false,
        )

        assertThat(documentContext.ragSkipped).isFalse()
    }

    @Test
    fun `documentPartitions works correctly with ragSkipped true`() {
        val activeDocument = MLTypedDocument(
            documentType = DocumentType.Code,
            sourceType = InsightType.SourceCode,
            sourceId = UUID.randomUUID(),
            installationId = InstallationId.random(),
            source = "test source",
            content = "test content",
            documentId = "test-doc-1",
            searchDocumentId = null,
            localDocumentVisibility = LocalDocumentVisibility.Active,
        )

        val otherDocument = MLTypedDocument(
            documentType = DocumentType.Documentation,
            sourceType = InsightType.Documentation,
            sourceId = UUID.randomUUID(),
            installationId = InstallationId.random(),
            source = "test source",
            content = "test content",
            documentId = "test-doc-2",
            searchDocumentId = null,
            localDocumentVisibility = LocalDocumentVisibility.Background,
        )

        val documentContext = DocumentContext(
            documentQueryContext = DocumentQueryContext(
                documentQueryText = "test query",
                documentQuery = MLQuery.MLTextQuery("test query"),
            ),
            documents = listOf(activeDocument, otherDocument),
            mlFunctionContent = emptySet(),
            ragSkipped = true,
        )

        assertThat(documentContext.ragSkipped).isTrue()
        assertThat(documentContext.activeLocalDocuments.documents).containsExactly(activeDocument)
        assertThat(documentContext.otherDocuments.documents).containsExactly(otherDocument)
    }

    @Test
    fun `ragSkipped does not affect other properties`() {
        val documentQueryContext = DocumentQueryContext(
            documentQueryText = "test query",
            documentQuery = MLQuery.MLTextQuery("test query"),
        )
        val documents = listOf(
            MLTypedDocument(
                documentType = DocumentType.Code,
                sourceType = InsightType.SourceCode,
                sourceId = UUID.randomUUID(),
                installationId = InstallationId.random(),
                source = "test source",
                content = "test content",
                documentId = "test-doc",
                searchDocumentId = null,
            ),
        )
        val mlFunctionContent = setOf("function1", "function2")
        val miscellaneousContent = listOf("misc1", "misc2")
        val templateOverride = MockDataClasses.template()

        val documentContext = DocumentContext(
            documentQueryContext = documentQueryContext,
            documents = documents,
            mlFunctionContent = mlFunctionContent,
            miscellaneousContent = miscellaneousContent,
            templateOverride = templateOverride,
            ragSkipped = true,
        )

        assertThat(documentContext.ragSkipped).isTrue()
        assertThat(documentContext.documentQueryContext).isEqualTo(documentQueryContext)
        assertThat(documentContext.documents).isEqualTo(documents)
        assertThat(documentContext.mlFunctionContent).isEqualTo(mlFunctionContent)
        assertThat(documentContext.miscellaneousContent).isEqualTo(miscellaneousContent)
        assertThat(documentContext.templateOverride).isEqualTo(templateOverride)
    }
}
