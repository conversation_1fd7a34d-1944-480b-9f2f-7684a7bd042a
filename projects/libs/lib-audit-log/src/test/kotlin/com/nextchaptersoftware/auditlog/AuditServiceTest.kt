package com.nextchaptersoftware.auditlog

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.models.AuditLogActorType
import com.nextchaptersoftware.db.models.AuditLogDetail
import com.nextchaptersoftware.db.models.AuditLogType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.types.EmailAddress
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AuditServiceTest : DatabaseTestsBase() {
    @Test
    fun create() = suspendingDatabaseTest {
        val installationName = "Installation Name"
        val provider = Provider.GitHub

        val org = makeOrg()
        val installation = makeInstallation(org = org, displayName = "Installation Name", provider = provider)
        val person = makePerson()

        val actor = Actor.Person(
            personId = person.idValue,
            name = "Richie",
            avatarUrl = "https://www.richie.com".asUrl,
            email = EmailAddress.of("<EMAIL>"),
        )

        val service = AuditService(
            orgId = org.idValue,
            actor = actor,
        )

        val detail = AuditLogDetail(
            added = listOf(
                AuditLogDetail.Resource(
                    id = RepoId.random().value,
                    name = "Repo Name",
                ),
            ),
        )

        service.dataSourceUpdated(
            installation = installation.asDataModel(),
            added = detail.added,
            block = { },
        )

        val result = Stores.auditLogStore.list(orgId = org.idValue).single()

        assertThat(result.orgId).isEqualTo(org.idValue)
        assertThat(result.type).isEqualTo(AuditLogType.DataSourceUpdated)
        assertThat(result.actorId).isEqualTo(person.idValue.value)
        assertThat(result.actorType).isEqualTo(AuditLogActorType.Person)
        assertThat(result.actorName).isEqualTo("Richie")
        assertThat(result.actorAvatarUrl).isEqualTo("https://www.richie.com".asUrl)
        assertThat(result.provider).isEqualTo(provider)
        assertThat(result.installationId).isEqualTo(installation.id.value)
        assertThat(result.installationName).isEqualTo(installationName)
        assertThat(result.detail).isEqualTo(detail)
    }
}
