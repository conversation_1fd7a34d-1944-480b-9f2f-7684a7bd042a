package com.nextchaptersoftware.auditlog

import com.nextchaptersoftware.auditlog.Extensions.asInstallationInfo
import com.nextchaptersoftware.auditlog.Extensions.description
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AnswerPreferences
import com.nextchaptersoftware.db.models.ApiKeyAudience
import com.nextchaptersoftware.db.models.ArchivedReferenceId
import com.nextchaptersoftware.db.models.AsanaProjectIngestionType
import com.nextchaptersoftware.db.models.AuditLogDetail
import com.nextchaptersoftware.db.models.AuditLogType
import com.nextchaptersoftware.db.models.CIProjectId
import com.nextchaptersoftware.db.models.Collection
import com.nextchaptersoftware.db.models.ConfluenceSpaceIngestionType
import com.nextchaptersoftware.db.models.DataSourcePreset
import com.nextchaptersoftware.db.models.DataSourcePresetId
import com.nextchaptersoftware.db.models.GoogleDriveFile
import com.nextchaptersoftware.db.models.GoogleWorkspaceDrive
import com.nextchaptersoftware.db.models.Group
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.JiraProjectIngestionType
import com.nextchaptersoftware.db.models.LinearTeam
import com.nextchaptersoftware.db.models.LinearTeamIngestionType
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.OrgApiKey
import com.nextchaptersoftware.db.models.OrgBillingSeatApprovalMode
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Plan
import com.nextchaptersoftware.db.models.PlanRate
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.SlackAutoAnswerMode
import com.nextchaptersoftware.db.models.SlackChannel
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.SlackChannelPatternPreferences
import com.nextchaptersoftware.db.models.SlackChannelPreferences
import com.nextchaptersoftware.db.models.SlackDefaultChannelPreferences
import com.nextchaptersoftware.db.models.UnblockedRole
import com.nextchaptersoftware.db.models.types.RepoSelectionMode
import com.nextchaptersoftware.db.stores.AuditLogStore
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.personId
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import io.ktor.server.routing.RoutingContext
import java.util.UUID
import org.jetbrains.exposed.sql.Transaction

suspend fun RoutingContext.auditService(): AuditService {
    return AuditService.forPerson(
        orgId = orgId,
        personId = personId(),
    )
}

@Suppress("LargeClass")
class AuditService internal constructor(
    val orgId: OrgId,
    val actor: Actor,
    private val auditLogStore: AuditLogStore = Stores.auditLogStore,
    private val identityStore: IdentityStore = Stores.identityStore,
) {
    companion object {
        suspend fun forPerson(
            orgId: OrgId,
            personId: PersonId,
        ): AuditService {
            val person = Stores.personStore.findById(id = personId)

            val actor = Actor.Person(
                personId = personId,
                name = person?.customDisplayName ?: "Person ${personId.value}",
                avatarUrl = person?.customAvatarUrl,
                email = person?.primaryEmail,
            )

            return AuditService(orgId = orgId, actor = actor)
        }

        suspend fun forSlackUser(
            orgId: OrgId,
            slackUserId: String,
            identity: Identity?,
        ): AuditService = when (val personId = identity?.person) {
            null -> {
                val actor = Actor.SlackUser(
                    slackUserId = slackUserId,
                    name = identity?.displayNameOrUsername ?: slackUserId,
                    avatarUrl = identity?.avatarUrl,
                )

                AuditService(orgId = orgId, actor = actor)
            }

            else -> {
                forPerson(orgId = orgId, personId = personId)
            }
        }

        fun forSystem(
            orgId: OrgId,
        ): AuditService {
            return AuditService(orgId = orgId, actor = Actor.System)
        }

        fun forDataSource(
            orgId: OrgId,
            provider: Provider,
        ): AuditService {
            return AuditService(orgId = orgId, actor = Actor.DataSource(provider))
        }

        fun forApiKey(
            orgId: OrgId,
            apiKey: OrgApiKey,
            provider: Provider? = null,
        ): AuditService {
            val actor = Actor.ApiKey(
                apiKeyId = apiKey.id,
                apiKeyName = apiKey.name,
                provider = provider,
            )

            return AuditService(orgId = orgId, actor = actor)
        }
    }

    suspend fun deleteTeam(
        orgName: String,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DeleteTeam,
            installationInfo = null,
            action = listOf("Deleted \"$orgName\""),
            block = block,
        )
    }

    suspend fun updateTeamName(
        updatedName: String,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.UpdateTeamName,
            installationInfo = null,
            action = listOf("Renamed team to \"$updatedName\""),
            detail = AuditLogDetail(value = updatedName),
            block = block,
        )
    }

    suspend fun memberRoleUpdated(
        orgMemberId: OrgMemberId,
        newRole: UnblockedRole?,
        block: suspend (Transaction) -> Unit,
    ) {
        val member = resolveResource(orgMemberId)

        executeAndAudit(
            type = AuditLogType.MemberRoleUpdated,
            installationInfo = null,
            action = when (newRole) {
                null -> "Reset role for \"${member.name}\""
                else -> "Assigned ${newRole.name} role to \"${member.name}\""
            }.let(::listOf),
            detail = AuditLogDetail(member = member, value = newRole?.name),
            block = block,
        )
    }

    suspend fun seatAssigned(
        orgMemberId: OrgMemberId,
    ) {
        val member = resolveResource(orgMemberId)

        executeAndAudit(
            type = AuditLogType.SeatAssigned,
            installationInfo = null,
            detail = AuditLogDetail(member = member),
            action = listOf("Assigned a license to \"${member.name}\""),
            block = { },
        )
    }

    suspend fun seatRemoved(
        orgMemberId: OrgMemberId,
    ) {
        val member = resolveResource(orgMemberId)

        executeAndAudit(
            type = AuditLogType.SeatRemoved,
            installationInfo = null,
            detail = AuditLogDetail(member = member),
            action = listOf("Removed a license from \"${member.name}\""),
            block = { },
        )
    }

    suspend fun rbacEnabled(
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.RbacEnabled,
            installationInfo = null,
            action = listOf("Enabled Role-based Access Control"),
            block = block,
        )
    }

    suspend fun rbacDisabled(
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.RbacDisabled,
            installationInfo = null,
            action = listOf("Disabled Role-based Access Control"),
            block = block,
        )
    }

    suspend fun dsacEnabledForAll(
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DsacEnabled,
            installationInfo = null,
            action = listOf("Data Shield enabled for all data sources"),
            block = block,
        )
    }

    suspend fun dsacDisabledForAll(
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DsacDisabled,
            installationInfo = null,
            action = listOf("Data Shield disabled for all data sources"),
            block = block,
        )
    }

    suspend fun dsacEnabledForInstallation(
        trx: Transaction? = null,
        installation: Installation,
        block: suspend (Transaction) -> Unit = {},
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.DsacEnabled,
            installationInfo = null,
            action = listOf("Data Shield enabled for ${installation.provider.displayName}: \"${installation.displayName}\""),
            detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = installation.id.value, name = installation.displayName))),
            block = block,
        )
    }

    suspend fun dsacEnabledForInstallations(
        trx: Transaction,
        installations: List<Installation>,
    ) {
        if (installations.isNotEmpty()) {
            val installationsStr = installations.joinToString { "${it.provider.displayName}: \"${it.displayName}\"" }

            executeAndAudit(
                trx = trx,
                type = AuditLogType.DsacEnabled,
                installationInfo = null,
                action = listOf("Data Shield enabled for $installationsStr"),
                detail = AuditLogDetail(modified = installations.map { AuditLogDetail.Resource(id = it.id.value, name = it.displayName) }),
                block = { },
            )
        }
    }

    suspend fun dsacDisabledForInstallation(
        trx: Transaction? = null,
        installation: Installation,
        block: suspend (Transaction) -> Unit = {},
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.DsacDisabled,
            installationInfo = null,
            action = listOf("Data Shield disabled for ${installation.provider.displayName}: \"${installation.displayName}\""),
            detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = installation.id.value, name = installation.displayName))),
            block = block,
        )
    }

    suspend fun dsacDisabledForInstallations(
        trx: Transaction,
        installations: List<Installation>,
    ) {
        if (installations.isNotEmpty()) {
            val installationsStr = installations.joinToString { "${it.provider.displayName}: \"${it.displayName}\"" }

            executeAndAudit(
                trx = trx,
                type = AuditLogType.DsacDisabled,
                installationInfo = null,
                action = listOf("Data Shield disabled for $installationsStr"),
                detail = AuditLogDetail(modified = installations.map { AuditLogDetail.Resource(id = it.id.value, name = it.displayName) }),
                block = { },
            )
        }
    }

    suspend fun dataSourceAdded(
        trx: Transaction? = null,
        installation: Installation,
    ) = dataSourceAdded(
        trx = trx,
        installationInfo = installation.asInstallationInfo(),
    )

    suspend fun dataSourceAdded(
        trx: Transaction? = null,
        scmTeam: ScmTeam,
    ) = dataSourceAdded(
        trx = trx,
        installationInfo = scmTeam.asInstallationInfo(),
    )

    private suspend fun dataSourceAdded(
        trx: Transaction? = null,
        installationInfo: InstallationInfo,
    ) {
        return executeAndAudit(
            trx = trx,
            type = AuditLogType.DataSourceAdded,
            installationInfo = installationInfo,
            action = listOf("Connected \"${installationInfo.name}\""),
            block = {},
        )
    }

    suspend fun dataSourceRemoved(
        installation: Installation,
        block: suspend (Transaction) -> Unit,
    ) = when (installation.provider.isCiProvider) {
        true -> deletedCiInstallation(
            installation = installation,
            block = block,
        )

        else -> dataSourceRemoved(
            installationInfo = installation.asInstallationInfo(),
            block = block,
        )
    }

    suspend fun dataSourceRemoved(
        scmTeam: ScmTeam,
    ) {
        dataSourceRemoved(
            installationInfo = scmTeam.asInstallationInfo(),
            block = {},
        )
    }

    private suspend fun dataSourceRemoved(
        installationInfo: InstallationInfo,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DataSourceRemoved,
            installationInfo = installationInfo,
            action = listOf("Deleted \"${installationInfo.name}\""),
            block = block,
        )
    }

    suspend fun dataSourceUpdated(
        trx: Transaction,
        installation: Installation,
        added: List<AuditLogDetail.Resource>? = null,
        removed: List<AuditLogDetail.Resource>? = null,
        modified: List<AuditLogDetail.Resource>? = null,
    ): Unit = dataSourceUpdated(
        trx = trx,
        installationInfo = installation.asInstallationInfo(),
        added = added,
        removed = removed,
        modified = modified,
        block = {},
    )

    suspend fun <T> dataSourceUpdated(
        installation: Installation,
        added: List<AuditLogDetail.Resource>? = null,
        removed: List<AuditLogDetail.Resource>? = null,
        modified: List<AuditLogDetail.Resource>? = null,
        block: suspend (Transaction) -> T,
    ): T = dataSourceUpdated(
        trx = null,
        installationInfo = installation.asInstallationInfo(),
        added = added,
        removed = removed,
        modified = modified,
        block = block,
    )

    suspend fun dataSourceUpdated(
        trx: Transaction,
        scmTeam: ScmTeam,
        added: List<AuditLogDetail.Resource>? = null,
        removed: List<AuditLogDetail.Resource>? = null,
        modified: List<AuditLogDetail.Resource>? = null,
    ): Unit = dataSourceUpdated(
        trx = trx,
        installationInfo = scmTeam.asInstallationInfo(),
        added = added,
        removed = removed,
        modified = modified,
        block = {},
    )

    suspend fun dataSourceUpdated(
        scmTeam: ScmTeam,
        added: List<AuditLogDetail.Resource>? = null,
        removed: List<AuditLogDetail.Resource>? = null,
        modified: List<AuditLogDetail.Resource>? = null,
        block: suspend (Transaction) -> Unit,
    ): Unit = dataSourceUpdated(
        trx = null,
        installationInfo = scmTeam.asInstallationInfo(),
        added = added,
        removed = removed,
        modified = modified,
        block = block,
    )

    private suspend fun <T> dataSourceUpdated(
        trx: Transaction?,
        installationInfo: InstallationInfo,
        added: List<AuditLogDetail.Resource>? = null,
        removed: List<AuditLogDetail.Resource>? = null,
        modified: List<AuditLogDetail.Resource>? = null,
        block: suspend (Transaction) -> T,
    ): T = when (added.isNullOrEmpty() && removed.isNullOrEmpty() && modified.isNullOrEmpty()) {
        true -> execute(trx, block)

        else -> {
            val displayName = installationInfo.name

            val addedStr = added?.nullIfEmpty()?.let { items -> "Added for \"$displayName\": ${items.joinToString { it.name }}" }
            val removedStr = removed?.nullIfEmpty()?.let { items -> "Removed for \"$displayName\": ${items.joinToString { it.name }}" }
            val modifiedStr = modified?.nullIfEmpty()?.let { items -> "Modified for \"$displayName\": ${items.joinToString { it.name }}" }

            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installationInfo,
                action = listOfNotNull(addedStr, removedStr, modifiedStr),
                detail = AuditLogDetail(added = added, removed = removed, modified = modified),
                block = block,
            )
        }
    }

    suspend fun confluenceIngestionTypeUpdated(
        installation: Installation,
        before: ConfluenceSpaceIngestionType?,
        after: ConfluenceSpaceIngestionType,
        block: suspend (Transaction) -> Unit,
    ) {
        when (before == after) {
            true -> {
                execute(block)
            }

            else -> {
                val afterString = when (after) {
                    ConfluenceSpaceIngestionType.AllSpaces -> "all spaces"
                    ConfluenceSpaceIngestionType.SelectedSpacesOnly -> "specific spaces"
                }

                val action = "Set \"${installation.displayName}\" to reference $afterString"

                dataSourceUpdated(installation = installation, action = action, block = block)
            }
        }
    }

    suspend fun jiraIngestionTypeUpdated(
        installation: Installation,
        before: JiraProjectIngestionType?,
        after: JiraProjectIngestionType,
        block: suspend (Transaction) -> Unit,
    ) {
        when (before == after) {
            true -> {
                execute(block)
            }

            else -> {
                val afterString = when (after) {
                    JiraProjectIngestionType.AllProjects -> "all projects"
                    JiraProjectIngestionType.SelectedProjectsOnly -> "specific projects"
                }

                val action = "Set \"${installation.displayName}\" to reference $afterString"

                dataSourceUpdated(installation = installation, action = action, block = block)
            }
        }
    }

    suspend fun asanaIngestionTypeUpdated(
        installation: Installation,
        before: AsanaProjectIngestionType?,
        after: AsanaProjectIngestionType,
        block: suspend (Transaction) -> Unit,
    ) {
        when (before == after) {
            true -> {
                execute(block)
            }

            else -> {
                val afterString = when (after) {
                    AsanaProjectIngestionType.AllNewProjects -> "currently selected and new projects"
                    AsanaProjectIngestionType.SelectedProjectsOnly -> "specific projects"
                }
                val action = "Set \"${installation.displayName}\" to reference $afterString"
                dataSourceUpdated(installation = installation, action = action, block = block)
            }
        }
    }

    suspend fun dataSourceUpdated(
        installation: Installation,
        action: String,
        block: suspend (Transaction) -> Unit,
    ) {
        return executeAndAudit(
            type = AuditLogType.DataSourceUpdated,
            installationInfo = installation.asInstallationInfo(),
            action = listOf(action),
            block = block,
        )
    }

    private suspend fun dataSourceUpdated(
        trx: Transaction,
        installation: Installation,
        action: String,
        detail: AuditLogDetail? = null,
    ) {
        return dataSourceUpdated(
            trx = trx,
            installation = installation,
            actions = listOf(action),
            detail = detail,
        )
    }

    private suspend fun dataSourceUpdated(
        trx: Transaction,
        installation: Installation,
        actions: List<String>,
        detail: AuditLogDetail? = null,
    ) {
        return executeAndAudit(
            trx = trx,
            type = AuditLogType.DataSourceUpdated,
            installationInfo = installation.asInstallationInfo(),
            action = actions,
            detail = detail,
            block = {},
        )
    }

    suspend fun slackChannelsAdded(
        trx: Transaction,
        installation: Installation,
        addedChannels: Map<SlackChannelId, String>,
    ) {
        if (addedChannels.isNotEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installation.asInstallationInfo(),
                action = listOf("Selected channels for \"${installation.displayName}\": ${addedChannels.values.joinToString { it }}"),
                detail = AuditLogDetail(added = addedChannels.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = {},
            )
        }
    }

    suspend fun slackChannelsRemoved(
        trx: Transaction,
        installation: Installation,
        removedChannels: Map<SlackChannelId, String>,
    ) {
        if (removedChannels.isNotEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installation.asInstallationInfo(),
                action = listOf("Removed channels for \"${installation.displayName}\": ${removedChannels.values.joinToString { it }}"),
                detail = AuditLogDetail(removed = removedChannels.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = {},
            )
        }
    }

    suspend fun slackChannelBotAdded(
        trx: Transaction,
        installation: Installation,
        slackChannel: SlackChannel,
    ) {
        dataSourceUpdated(
            trx = trx,
            installation = installation,
            action = "Added Unblocked to #${slackChannel.name} in \"${installation.displayName}\"",
            detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = slackChannel.id.value, name = slackChannel.name))),
        )
    }

    suspend fun slackChannelBotRemoved(
        trx: Transaction,
        installation: Installation,
        slackChannel: SlackChannel,
    ) {
        dataSourceUpdated(
            trx = trx,
            installation = installation,
            action = "Removed Unblocked from #${slackChannel.name} in \"${installation.displayName}\"",
            detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = slackChannel.id.value, name = slackChannel.name))),
        )
    }

    suspend fun slackChannelPatternsAdded(
        trx: Transaction,
        installation: Installation,
        addedPatterns: Set<String>,
    ) {
        if (addedPatterns.isNotEmpty()) {
            dataSourceUpdated(
                trx = trx,
                installation = installation,
                action = "Added channel patterns for \"${installation.displayName}\": ${addedPatterns.joinToString { it }}",
            )
        }
    }

    suspend fun slackChannelPatternsRemoved(
        trx: Transaction,
        installation: Installation,
        removedPatterns: Set<String>,
    ) {
        if (removedPatterns.isNotEmpty()) {
            dataSourceUpdated(
                trx = trx,
                installation = installation,
                action = "Removed channel patterns for \"${installation.displayName}\": ${removedPatterns.joinToString { it }}",
            )
        }
    }

    suspend fun slackChannelPreferencesUpdated(
        trx: Transaction,
        installation: Installation,
        slackChannelId: SlackChannelId,
        slackChannelName: String,
        before: SlackChannelPreferences?,
        after: SlackChannelPreferences,
    ) {
        val changed = when {
            before == null -> true
            before.autoAnswerMode != after.autoAnswerMode -> true
            before.dataSourcePresetId != after.dataSourcePresetId -> true
            before.answerPreferences != after.answerPreferences -> true
            else -> false
        }

        if (changed) {
            val dataSourcePresetName = after.dataSourcePresetId
                ?.let { Stores.dataSourcePresetStore.findName(trx = trx, id = it, orgId = installation.orgId) }
                ?: "All Data Sources"

            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installation.asInstallationInfo(),
                action = listOf(
                    "Channel preferences for #$slackChannelName were set to:",
                    "Data Sources: $dataSourcePresetName",
                    "Answer Length: ${after.answerPreferences.conciseness.label}",
                    "Answer Tone: ${after.answerPreferences.tone.label}",
                    "Research Depth: ${after.answerPreferences.depth.label}",
                    "Auto-Response Confidence: ${after.autoAnswerMode.label}",
                ),
                detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = slackChannelId.value, name = slackChannelName))),
                block = {},
            )
        }
    }

    suspend fun slackChannelPatternPreferencesUpdated(
        trx: Transaction,
        installation: Installation,
        slackChannelPattern: String,
        before: SlackChannelPatternPreferences?,
        after: SlackChannelPatternPreferences,
    ) {
        val changed = when {
            before == null -> true
            before.autoAnswerMode != after.autoAnswerMode -> true
            before.dataSourcePresetId != after.dataSourcePresetId -> true
            before.answerPreferences != after.answerPreferences -> true
            else -> false
        }

        if (changed) {
            val dataSourcePresetName = after.dataSourcePresetId
                ?.let { Stores.dataSourcePresetStore.findName(trx = trx, id = it, orgId = installation.orgId) }
                ?: "All Data Sources"

            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installation.asInstallationInfo(),
                action = listOf(
                    "Channel preferences for the pattern \"$slackChannelPattern\" were set to:",
                    "Data Sources: $dataSourcePresetName",
                    "Answer Length: ${after.answerPreferences.conciseness.label}",
                    "Answer Tone: ${after.answerPreferences.tone.label}",
                    "Research Depth: ${after.answerPreferences.depth.label}",
                    "Auto-Response Confidence: ${after.autoAnswerMode.label}",
                ),
                block = {},
            )
        }
    }

    suspend fun slackDefaultChannelPreferencesUpdated(
        trx: Transaction,
        installation: Installation,
        before: SlackDefaultChannelPreferences,
        after: SlackDefaultChannelPreferences,
    ) {
        val changed = when {
            before.autoAnswerMode != after.autoAnswerMode -> true
            before.dataSourcePresetId != after.dataSourcePresetId -> true
            before.answerPreferences != after.answerPreferences -> true
            else -> false
        }

        if (changed) {
            val dataSourcePresetName = after.dataSourcePresetId
                ?.let { Stores.dataSourcePresetStore.findName(trx = trx, id = it, orgId = installation.orgId) }
                ?: "All Data Sources"

            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installation.asInstallationInfo(),
                action = listOf(
                    "Default channel preferences were set to:",
                    "Data Sources: $dataSourcePresetName",
                    "Answer Length: ${after.answerPreferences.conciseness.label}",
                    "Answer Tone: ${after.answerPreferences.tone.label}",
                    "Research Depth: ${after.answerPreferences.depth.label}",
                    "Auto-Response Confidence: ${after.autoAnswerMode.label}",
                ),
                block = {},
            )
        }
    }

    suspend fun slackChannelAutoAnswerModeUpdated(
        trx: Transaction,
        installation: Installation,
        slackChannel: SlackChannel,
        before: SlackAutoAnswerMode?,
        after: SlackAutoAnswerMode,
    ) {
        if (before != after) {
            dataSourceUpdated(
                trx = trx,
                installation = installation,
                action = "Channel Auto-Response Confidence preference for #${slackChannel.name} was set to ${after.label}",
                detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = slackChannel.id.value, name = slackChannel.name))),
            )
        }
    }

    suspend fun slackChannelDataSourcePresetUpdated(
        trx: Transaction,
        installation: Installation,
        slackChannel: SlackChannel,
        before: DataSourcePresetId?,
        after: DataSourcePreset,
    ) {
        if (before != after.id) {
            dataSourceUpdated(
                trx = trx,
                installation = installation,
                action = "Channel Data Source preference for #${slackChannel.name} was set to ${after.name}",
                detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = slackChannel.id.value, name = slackChannel.name))),
            )
        }
    }

    suspend fun slackChannelAnswerPreferencesUpdated(
        trx: Transaction,
        installation: Installation,
        slackChannel: SlackChannel,
        before: AnswerPreferences?,
        after: AnswerPreferences,
    ) {
        if (before != after) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.DataSourceUpdated,
                installationInfo = installation.asInstallationInfo(),
                action = listOf(
                    "Channel answer preferences for #${slackChannel.name} were set to:",
                    "Answer Length: ${after.conciseness.label}",
                    "Answer Tone: ${after.tone.label}",
                    "Research Depth: ${after.depth.label}",
                ),
                detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = slackChannel.id.value, name = slackChannel.name))),
                block = {},
            )
        }
    }

    suspend fun googleDriveWorkspaceUpdated(
        trx: Transaction,
        installation: Installation,
        before: GoogleWorkspaceDrive?,
        after: GoogleWorkspaceDrive,
    ) {
        when (before == null) {
            true -> {
                val changes = listOf(
                    "admin email set to ${after.adminEmail}",
                    "service account key set",
                ).joinToString { it }

                dataSourceUpdated(
                    trx = trx,
                    installation = installation,
                    action = "Set Google Drive for Workspace \"${after.domain}\" configuration: $changes",
                )
            }

            else -> {
                val adminEmailChanged = before.adminEmail != after.adminEmail
                val serviceAccountKeyChanged = before.systemAccountKey.value.contentEquals(after.systemAccountKey.value).not()

                if (adminEmailChanged || serviceAccountKeyChanged) {
                    val changes = listOfNotNull(
                        after.adminEmail.takeIf { adminEmailChanged }?.let { "admin email set to $it" },
                        after.systemAccountKey.takeIf { serviceAccountKeyChanged }?.let { "service account key updated" },
                    ).joinToString { it }

                    dataSourceUpdated(
                        trx = trx,
                        installation = installation,
                        action = "Updated Google Drive for Workspace \"${after.domain}\" configuration: $changes",
                    )
                }
            }
        }
    }

    suspend fun googleDriveWorkspaceFileIngestionUpdated(
        installation: Installation,
        ingestAllFiles: Boolean,
        block: suspend (Transaction) -> Unit,
    ) {
        when (ingestAllFiles) {
            true -> dataSourceUpdated(
                installation = installation,
                action = "Selected all content from ${installation.displayName} to be referenced when answering questions.",
                block = block,
            )

            else -> execute(block)
        }
    }

    suspend fun googleDriveFilesAdded(
        trx: Transaction,
        installation: Installation,
        addedFiles: Set<GoogleDriveFile>,
    ) {
        if (addedFiles.isNotEmpty()) {
            dataSourceUpdated(
                trx = trx,
                installation = installation,
                action = "Files or folders selected for \"${installation.displayName}\": ${addedFiles.joinToString { it.name }}",
            )
        }
    }

    suspend fun googleDriveFilesRemoved(
        installation: Installation,
        removedFiles: Set<GoogleDriveFile>,
        block: suspend (Transaction) -> Unit,
    ) {
        when (removedFiles.isEmpty()) {
            true -> execute(block)

            else -> dataSourceUpdated(
                installation = installation,
                action = "Files or folders removed for \"${installation.displayName}\": ${removedFiles.joinToString { it.name }}",
                block = block,
            )
        }
    }

    suspend fun linearIngestionTypeUpdated(
        trx: Transaction,
        installation: Installation,
        before: LinearTeamIngestionType?,
        after: LinearTeamIngestionType,
    ) {
        if (before != after) {
            val label = when (after) {
                LinearTeamIngestionType.AllTeams -> "all teams"
                LinearTeamIngestionType.SelectedTeamsOnly -> "specific teams"
            }

            dataSourceUpdated(
                trx = trx,
                installation = installation,
                action = "Set \"${installation.displayName}\" to reference $label",
            )
        }
    }

    suspend fun linearTeamsSelected(
        trx: Transaction,
        installation: Installation,
        selectedTeams: Set<LinearTeam>,
        removedTeams: Set<LinearTeam>,
    ) {
        val selected = selectedTeams.nullIfEmpty()?.let { "selected ${it.joinToString { it.name }}" }
        val removed = removedTeams.nullIfEmpty()?.let { "removed ${it.joinToString { it.name }}" }

        if (selected != null || removed != null) {
            dataSourceUpdated(
                trx = trx,
                installation = installation,
                actions = listOfNotNull("Updated teams for \"${installation.displayName}\":", selected, removed),
                detail = AuditLogDetail(
                    added = selectedTeams.map { AuditLogDetail.Resource(it.id.value, it.name) }.nullIfEmpty(),
                    removed = removedTeams.map { AuditLogDetail.Resource(it.id.value, it.name) }.nullIfEmpty(),
                ),
            )
        }
    }

    suspend fun collectionUpdated(
        installation: Installation,
        collectionBeforeUpdate: Collection,
        newName: String?,
        newDescription: String?,
        newIconUrl: String?,
        block: suspend (Transaction) -> Unit,
    ) {
        val nameAfterUpdate = newName ?: collectionBeforeUpdate.name

        val changes = listOfNotNull(
            newName?.takeIf { it != collectionBeforeUpdate.name }?.let { "renamed to \"$it\"" },
            newDescription?.takeIf { it != collectionBeforeUpdate.description }?.let { "description edited" },
            newIconUrl?.takeIf { it != collectionBeforeUpdate.iconUrl.toString() }?.let { "icon changed" },
        )

        return executeAndAudit(
            type = AuditLogType.DataSourceUpdated,
            installationInfo = installation.asInstallationInfo().copy(name = nameAfterUpdate),
            action = when (changes.isNotEmpty()) {
                true -> listOf("Updated \"${collectionBeforeUpdate.name}\": ${changes.joinToString { it }}")
                else -> listOf("Updated \"${collectionBeforeUpdate.name}\"")
            },
            block = block,
        )
    }

    suspend fun dataSourceTokenUpdated(
        installation: Installation,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DataSourceTokenUpdated,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Updated token for \"${installation.displayName}\""),
            block = block,
        )
    }

    suspend fun updateSCMAutoSelectNewSourceRepos(
        autoSelectNewSourceRepos: Boolean,
        installation: Installation,
        block: suspend (Transaction) -> Unit,
    ) {
        val (type, action) = when (autoSelectNewSourceRepos) {
            true -> AuditLogType.EnableSCMAutoSelectNewSourceRepos to "Enabled"
            else -> AuditLogType.DisableSCMAutoSelectNewSourceRepos to "Disabled"
        }

        executeAndAudit(
            type = type,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("$action auto-selection of new repositories in \"${installation.displayName}\""),
            block = block,
        )
    }

    suspend fun updateSCMAutoEnableCi(
        autoEnableCi: Boolean,
        installation: Installation,
        block: suspend (Transaction) -> Unit,
    ) {
        val (type, action) = when (autoEnableCi) {
            true -> AuditLogType.EnableSCMAutoEnableCi to "Enabled"
            else -> AuditLogType.DisableSCMAutoEnableCi to "Disabled"
        }

        executeAndAudit(
            type = type,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("$action auto-enabling of CI for new repositories in \"${installation.displayName}\""),
            block = block,
        )
    }

    suspend fun createdEnterpriseApp(
        provider: Provider,
        displayName: String,
    ) {
        executeAndAudit(
            type = AuditLogType.CreatedEnterpriseApp,
            provider = provider,
            installationId = null,
            installationKey = null,
            installationName = null,
            action = listOf("Created ${provider.displayName} app for \"$displayName\""),
            detail = AuditLogDetail(value = displayName),
            block = { },
        )
    }

    suspend fun connectedSSOProvider(
        provider: Provider,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            provider = provider,
            installationKey = null,
            installationName = null,
            installationId = null,
            type = AuditLogType.ConnectedSSOProvider,
            action = listOf("Connected ${provider.displayName}"),
            block = block,
        )
    }

    suspend fun updatedSSOProvider(
        provider: Provider,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            provider = provider,
            installationKey = null,
            installationName = null,
            installationId = null,
            type = AuditLogType.UpdatedSSOProvider,
            action = listOf("Updated ${provider.displayName}"),
            block = block,
        )
    }

    suspend fun deletedSSOProvider(
        provider: Provider,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            provider = provider,
            installationKey = null,
            installationName = null,
            installationId = null,
            type = AuditLogType.DeletedSSOProvider,
            action = listOf("Deleted ${provider.displayName}"),
            block = block,
        )
    }

    suspend fun updatedSSOEnforcedSignIn(
        provider: Provider,
        enforceSignIn: Boolean,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            provider = provider,
            installationKey = null,
            installationName = null,
            installationId = null,
            type = AuditLogType.UpdatedSSOEnforcedSignIn,
            action = when (enforceSignIn) {
                true -> listOf("Changed enforcement to ${provider.displayName} only")
                else -> listOf("Changed SSO enforcement to all login providers")
            },
            block = block,
        )
    }

    suspend fun <T> registeredDomain(
        domain: String,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            type = AuditLogType.RegisteredDomain,
            installationInfo = null,
            action = listOf("Added email domain: $domain"),
            block = block,
        )
    }

    suspend fun deletedDomain(
        domain: String,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DeletedDomain,
            installationInfo = null,
            action = listOf("Deleted email domain: $domain"),
            block = block,
        )
    }

    suspend fun verifiedDomain(
        domain: String,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.VerifiedDomain,
            installationInfo = null,
            action = listOf("Added \"$domain\" as verified domain"),
            block = block,
        )
    }

    suspend fun <T> createdApiToken(
        keyName: String,
        audienceAndProvider: Pair<ApiKeyAudience, Provider?>,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            type = when (audienceAndProvider.first) {
                ApiKeyAudience.PublicApi -> AuditLogType.CreatedApiToken
                ApiKeyAudience.ScimApi -> AuditLogType.CreatedScimToken
            },
            provider = audienceAndProvider.second,
            installationId = null,
            installationKey = null,
            installationName = null,
            action = listOf("Created ${audienceAndProvider.first.description} token: \"$keyName\""),
            block = block,
        )
    }

    suspend fun deletedApiToken(
        keyName: String,
        audienceAndProvider: Pair<ApiKeyAudience, Provider?>,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = when (audienceAndProvider.first) {
                ApiKeyAudience.PublicApi -> AuditLogType.DeletedApiToken
                ApiKeyAudience.ScimApi -> AuditLogType.DeletedScimToken
            },
            provider = audienceAndProvider.second,
            installationId = null,
            installationKey = null,
            installationName = null,
            action = listOf("Deleted ${audienceAndProvider.first.description} token: $keyName"),
            block = block,
        )
    }

    suspend fun <T> createdDataSourcePreset(
        presetId: DataSourcePresetId,
        presetName: String,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            type = AuditLogType.CreatedDataSourcePreset,
            installationInfo = null,
            action = listOf("Created \"$presetName\" Preset"),
            detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = presetId.value, name = presetName))),
            block = block,
        )
    }

    suspend fun deletedDataSourcePreset(
        preset: DataSourcePreset,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DeletedDataSourcePreset,
            installationInfo = null,
            action = listOf("Deleted \"${preset.name}\" Preset"),
            detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = preset.id.value, name = preset.name))),
            block = block,
        )
    }

    // Creates an audit log if changes are present. If none are present, it will just execute the block.
    suspend fun <T> updatedDataSourcePreset(
        presetName: String,
        changes: List<String>?,
        detail: AuditLogDetail? = null,
        block: suspend (Transaction) -> T,
    ): T {
        return when (changes.isNullOrEmpty()) {
            true -> execute(block)

            else -> executeAndAudit(
                type = AuditLogType.UpdatedDataSourcePreset,
                installationInfo = null,
                action = listOf("Updated \"$presetName\" Preset: ${changes.joinToString { it }}"),
                detail = detail,
                block = block,
            )
        }
    }

    suspend fun updatedDataSourcePresetAddedInstallation(
        trx: Transaction,
        presetName: String,
        presetInstallation: Installation,
    ) {
        return executeAndAudit(
            trx = trx,
            type = AuditLogType.UpdatedDataSourcePreset,
            installationInfo = null,
            action = listOf("Updated \"$presetName\" Preset: added ${presetInstallation.provider.displayName} \"${presetInstallation.displayName}\""),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = presetInstallation.id.value, name = presetInstallation.displayName))),
            block = { },
        )
    }

    // Creates an audit log if group changes are present. If none are present, it will just execute the block.
    suspend fun <T> updatedDataSourcePresetInstallation(
        presetName: String,
        installationName: String,
        addedGroups: List<String>,
        removedGroups: List<String>,
        detail: AuditLogDetail? = null,
        block: suspend (Transaction) -> T,
    ): T {
        val changes = listOfNotNull(
            addedGroups.nullIfEmpty()?.let { added -> "added ${added.joinToString { it }}" },
            removedGroups.nullIfEmpty()?.let { removed -> "removed ${removed.joinToString { it }}" },
        )

        return when (addedGroups.isEmpty() && removedGroups.isEmpty()) {
            true -> execute(block)

            else -> executeAndAudit(
                type = AuditLogType.UpdatedDataSourcePreset,
                installationInfo = null,
                action = listOf("Updated \"$presetName\" Preset: edited $installationName (${changes.joinToString(" and ")})"),
                detail = detail,
                block = block,
            )
        }
    }

    suspend fun addedCiInstallation(
        trx: Transaction,
        installation: Installation,
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.CreatedCiInstallation,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Connected ${installation.provider.displayName}"),
            block = {},
        )
    }

    suspend fun deletedCiInstallation(
        installation: Installation,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DeletedCiInstallation,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Deleted ${installation.provider.displayName}"),
            block = block,
        )
    }

    suspend fun <T> createdCiToken(
        installation: Installation,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            type = AuditLogType.CreatedCiToken,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Added personal access token"),
            block = block,
        )
    }

    suspend fun <T> deletedCiToken(
        installation: Installation,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            type = AuditLogType.DeletedCiToken,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Deleted personal access token"),
            block = block,
        )
    }

    private fun projectString(count: Int) = when (count == 1) {
        true -> "project"
        else -> "projects"
    }

    suspend fun addedCiProjects(
        installation: Installation,
        ciProjects: Map<CIProjectId, String>,
        block: suspend (Transaction) -> Unit,
    ) {
        when (ciProjects.isEmpty()) {
            true -> execute(block)

            else -> executeAndAudit(
                type = AuditLogType.UpdatedCiProjectsOrRepos,
                installationInfo = installation.asInstallationInfo(),
                action = listOf("Selected ${projectString(ciProjects.size)}: ${ciProjects.values.joinToString { it }}"),
                detail = AuditLogDetail(added = ciProjects.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = block,
            )
        }
    }

    suspend fun removedCiProjects(
        installation: Installation,
        ciProjects: Map<CIProjectId, String>,
        block: suspend (Transaction) -> Unit,
    ) {
        when (ciProjects.isEmpty()) {
            true -> execute(block)

            else -> executeAndAudit(
                type = AuditLogType.UpdatedCiProjectsOrRepos,
                installationInfo = installation.asInstallationInfo(),
                action = listOf("Removed ${projectString(ciProjects.size)}: ${ciProjects.values.joinToString { it }}"),
                detail = AuditLogDetail(removed = ciProjects.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = block,
            )
        }
    }

    suspend fun updatedCiRepoSelectionMode(
        trx: Transaction,
        installation: Installation,
        previousMode: RepoSelectionMode?,
        updatedMode: RepoSelectionMode,
        block: suspend (Transaction) -> Unit,
    ) {
        when (previousMode == updatedMode) {
            true -> {
                execute(trx = trx, block = block)
            }

            else -> {
                executeAndAudit(
                    trx = trx,
                    type = AuditLogType.UpdatedCiProjectsOrRepos,
                    installationInfo = installation.asInstallationInfo(),
                    action = listOf("Set repository selection mode to $updatedMode"),
                    block = block,
                )
            }
        }
    }

    private fun repoString(count: Int) = when (count == 1) {
        true -> "repository"
        else -> "repositories"
    }

    suspend fun addedCiRepos(
        trx: Transaction,
        installation: Installation,
        ciRepos: Map<RepoId, String>,
    ) {
        if (!ciRepos.isEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.UpdatedCiProjectsOrRepos,
                installationInfo = installation.asInstallationInfo(),
                action = listOf("Selected ${repoString(ciRepos.size)}: ${ciRepos.values.joinToString { it }}"),
                detail = AuditLogDetail(added = ciRepos.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = {},
            )
        }
    }

    suspend fun removedCiRepos(
        trx: Transaction,
        installation: Installation,
        ciRepos: Map<RepoId, String>,
    ) {
        if (ciRepos.isNotEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.UpdatedCiProjectsOrRepos,
                installationInfo = installation.asInstallationInfo(),
                action = listOf("Removed ${repoString(ciRepos.size)}: ${ciRepos.values.joinToString { it }}"),
                detail = AuditLogDetail(removed = ciRepos.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = {},
            )
        }
    }

    suspend fun selectedBot(
        installation: Installation,
        identity: Identity,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.SelectedBot,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Set \"${identity.displayNameOrUsername}\" as bot commenter"),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = identity.id.value, name = identity.displayNameOrUsername))),
            block = block,
        )
    }

    suspend fun deselectedBot(
        installation: Installation,
        identity: Identity,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DeselectedBot,
            installationInfo = installation.asInstallationInfo(),
            action = listOf("Unset \"${identity.displayNameOrUsername}\" as bot commenter"),
            detail = AuditLogDetail(removed = listOf(AuditLogDetail.Resource(id = identity.id.value, name = identity.displayNameOrUsername))),
            block = block,
        )
    }

    suspend fun scimUserCreated(
        trx: Transaction,
        identity: Identity,
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.ScimUserCreated,
            installationInfo = null,
            action = listOf("Created user ${identity.displayNameOrUsername}"),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = identity.id.value, name = identity.displayNameOrUsername))),
            block = { },
        )
    }

    suspend fun scimUserRemoved(
        identity: Identity,
        member: Member,
        block: suspend (Transaction) -> Unit,
    ) {
        when (member.isCurrentMember) {
            true -> executeAndAudit(
                type = AuditLogType.ScimUserRemoved,
                installationInfo = null,
                action = listOf("Removed user ${identity.displayNameOrUsername}"),
                detail = AuditLogDetail(removed = listOf(AuditLogDetail.Resource(id = identity.id.value, name = identity.displayNameOrUsername))),
                block = block,
            )

            else -> execute(block)
        }
    }

    suspend fun scimUserUpdated(
        trx: Transaction,
        before: Pair<Identity, Member?>,
        after: Pair<Identity, Member>,
    ) {
        val changes = listOfNotNull(
            after.first.displayName.takeIf { it != before.first.displayName }?.let { "renamed to $it" },
            after.first.primaryEmail.takeIf { it != before.first.primaryEmail }?.let { "updated email to $it" },
            after.second.isCurrentMember.takeIf { it != before.second?.isCurrentMember }?.let { if (it) "activated user" else "deactivated user" },
        )

        if (changes.isNotEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.ScimUserUpdated,
                installationInfo = null,
                action = listOf("Updated user ${before.first.displayNameOrUsername}: ${changes.joinToString { it }}"),
                detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = after.first.id.value, name = after.first.username))),
                block = { },
            )
        }
    }

    suspend fun scimGroupCreated(
        trx: Transaction,
        group: Group,
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.ScimGroupCreated,
            installationInfo = null,
            action = listOf("Created group ${group.displayName}"),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = group.id.value, name = group.displayName))),
            block = { },
        )
    }

    suspend fun scimGroupDeleted(
        group: Group,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.ScimGroupDeleted,
            installationInfo = null,
            action = listOf("Deleted group ${group.displayName}"),
            detail = AuditLogDetail(removed = listOf(AuditLogDetail.Resource(id = group.id.value, name = group.displayName))),
            block = block,
        )
    }

    suspend fun scimGroupUpdated(
        trx: Transaction,
        before: Group,
        after: Group,
    ) {
        if (before.displayName != after.displayName) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.ScimGroupUpdated,
                installationInfo = null,
                action = listOf("Group ${before.displayName} renamed to ${after.displayName}"),
                detail = AuditLogDetail(modified = listOf(AuditLogDetail.Resource(id = after.id.value, name = after.displayName))),
                block = { },
            )
        }
    }

    suspend fun scimGroupMembershipsAdded(
        trx: Transaction,
        group: Group,
        added: Map<IdentityId, String>,
    ) {
        if (added.isNotEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.ScimGroupUpdated,
                installationInfo = null,
                action = listOf("Added members to group ${group.displayName}: ${added.values.joinToString { it }}"),
                detail = AuditLogDetail(added = added.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = { },
            )
        }
    }

    suspend fun scimGroupMembershipsRemoved(
        trx: Transaction,
        group: Group,
        removed: Map<IdentityId, String>,
    ) {
        if (removed.isNotEmpty()) {
            executeAndAudit(
                trx = trx,
                type = AuditLogType.ScimGroupUpdated,
                installationInfo = null,
                action = listOf("Removed members from group ${group.displayName}: ${removed.values.joinToString { it }}"),
                detail = AuditLogDetail(removed = removed.map { AuditLogDetail.Resource(it.key.value, it.value) }),
                block = { },
            )
        }
    }

    suspend fun archivedThreadReference(
        trx: Transaction,
        archivedReferenceId: ArchivedReferenceId,
        threadTitle: String,
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.ThreadReferenceArchived,
            installationInfo = null,
            action = listOf("Archived $threadTitle"),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = archivedReferenceId.value, name = threadTitle))),
            block = {},
        )
    }

    suspend fun restoredThreadReference(
        archivedReferenceId: ArchivedReferenceId,
        threadTitle: String,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.ThreadReferenceRestored,
            installationInfo = null,
            action = listOf("Restored $threadTitle"),
            detail = AuditLogDetail(removed = listOf(AuditLogDetail.Resource(id = archivedReferenceId.value, name = threadTitle))),
            block = block,
        )
    }

    suspend fun archivedPullRequestReference(
        trx: Transaction,
        archivedReferenceId: ArchivedReferenceId,
        pullRequestInstallation: Installation,
        pullRequestTitle: String,
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.PullRequestReferenceArchived,
            installationInfo = pullRequestInstallation.asInstallationInfo(),
            action = listOf("Archived $pullRequestTitle"),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = archivedReferenceId.value, name = pullRequestTitle))),
            block = {},
        )
    }

    suspend fun <T> restoredPullRequestReference(
        archivedReferenceId: ArchivedReferenceId,
        pullRequestTitle: String,
        pullRequestInstallation: Installation,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            type = AuditLogType.PullRequestReferenceRestored,
            installationInfo = pullRequestInstallation.asInstallationInfo(),
            action = listOf("Restored $pullRequestTitle"),
            detail = AuditLogDetail(removed = listOf(AuditLogDetail.Resource(id = archivedReferenceId.value, name = pullRequestTitle))),
            block = block,
        )
    }

    suspend fun archivedDocumentReference(
        trx: Transaction,
        archivedReferenceId: ArchivedReferenceId,
        referenceTitle: String,
        referenceProvider: Provider,
    ) {
        executeAndAudit(
            trx = trx,
            type = AuditLogType.DocumentationReferenceArchived,
            provider = referenceProvider,
            installationId = null,
            installationKey = null,
            installationName = null,
            action = listOf("Archived $referenceTitle"),
            detail = AuditLogDetail(added = listOf(AuditLogDetail.Resource(id = archivedReferenceId.value, name = referenceTitle))),
            block = {},
        )
    }

    suspend fun restoredDocumentReference(
        archivedReferenceId: ArchivedReferenceId,
        referenceTitle: String,
        referenceProvider: Provider,
        block: suspend (Transaction) -> Unit,
    ) {
        executeAndAudit(
            type = AuditLogType.DocumentationReferenceRestored,
            provider = referenceProvider,
            installationId = null,
            installationKey = null,
            installationName = null,
            action = listOf("Restored $referenceTitle"),
            detail = AuditLogDetail(removed = listOf(AuditLogDetail.Resource(id = archivedReferenceId.value, name = referenceTitle))),
            block = block,
        )
    }

    suspend fun selectedPlan(
        plan: Plan,
        rate: PlanRate?,
        seats: Int?,
    ) {
        val details = listOfNotNull(seats?.let { "$it licenses" }, rate?.let { "billed ${it.name.lowercase()}" })

        // example: "Selected Business plan (10 licenses, billed monthly)"
        val action = listOfNotNull(
            "Selected ${plan.name} plan",
            details.nullIfEmpty()?.joinToString()?.let { "($it)" },
        ).joinToString(" ")

        executeAndAudit(
            type = AuditLogType.SelectedPlan,
            installationInfo = null,
            action = listOf(action),
            block = { },
        )
    }

    suspend fun updatedPaidSeats(
        seats: Int,
    ) {
        executeAndAudit(
            type = AuditLogType.UpdatedPaidSeats,
            installationInfo = null,
            action = listOf("Paid licenses changed to $seats"),
            block = { },
        )
    }

    suspend fun setSeatApprovalMode(
        modeBefore: OrgBillingSeatApprovalMode,
        modeAfter: OrgBillingSeatApprovalMode,
        maxSeatsBefore: Int?,
        maxSeatsAfter: Int?,
    ) {
        if (modeBefore == modeAfter && maxSeatsBefore == maxSeatsAfter) {
            return
        }

        val modeString = when (modeAfter) {
            OrgBillingSeatApprovalMode.Unlimited -> "auto-approve"
            OrgBillingSeatApprovalMode.Limited -> "auto-approve up to ${maxSeatsAfter ?: "-"} licenses"
            OrgBillingSeatApprovalMode.Manual -> "manually approve"
        }

        executeAndAudit(
            type = AuditLogType.UpdatedSeatApprovalMode,
            installationInfo = null,
            action = listOf("License approval changed to $modeString"),
            block = { },
        )
    }

    suspend fun updatedBillingEmail(
        email: EmailAddress,
    ) {
        executeAndAudit(
            type = AuditLogType.UpdatedBillingEmail,
            installationInfo = null,
            action = listOf("Updated billing email to $email"),
            detail = AuditLogDetail(value = email.value),
            block = { },
        )
    }

    suspend fun trialExtensionRequested() {
        executeAndAudit(
            type = AuditLogType.TrialExtensionRequested,
            installationInfo = null,
            action = listOf("Requested a trial extension"),
            block = { },
        )
    }

    suspend fun trialExtended() {
        executeAndAudit(
            type = AuditLogType.TrialExtended,
            installationInfo = null,
            action = listOf("Trial extended"),
            block = { },
        )
    }

    inner class CodeReview {

        suspend fun toggleCodeReview(
            enabled: Boolean,
            block: suspend (Transaction) -> Unit,
        ) {
            when (enabled) {
                true -> executeAndAudit(
                    type = AuditLogType.CodeReviewEnabled,
                    installationInfo = null,
                    action = listOf("Enabled code review"),
                    block = block,
                )

                else -> executeAndAudit(
                    type = AuditLogType.CodeReviewDisabled,
                    installationInfo = null,
                    action = listOf("Disabled code review"),
                    block = block,
                )
            }
        }

        suspend fun addedCodeReviewRepos(
            trx: Transaction,
            scmInstallation: Installation,
            repos: Map<RepoId, String>,
        ) {
            if (repos.isEmpty()) return

            executeAndAudit(
                trx = trx,
                type = AuditLogType.CodeReviewReposAdded,
                installationInfo = scmInstallation.asInstallationInfo(),
                action = listOf("Enabled code review for ${countRepos(repos.size)}: ${repos.values.joinToString { it }}"),
                detail = AuditLogDetail(
                    added = repos.map { (repoId, repoName) ->
                        AuditLogDetail.Resource(id = repoId.value, name = repoName)
                    },
                ),
                block = {},
            )
        }

        suspend fun removedCodeReviewRepos(
            trx: Transaction,
            scmInstallation: Installation,
            repos: Map<RepoId, String>,
        ) {
            if (repos.isEmpty()) return

            executeAndAudit(
                trx = trx,
                type = AuditLogType.CodeReviewReposRemoved,
                installationInfo = scmInstallation.asInstallationInfo(),
                action = listOf("Disabled code review for ${countRepos(repos.size)}: ${repos.values.joinToString { it }}"),
                detail = AuditLogDetail(
                    removed = repos.map { (repoId, repoName) ->
                        AuditLogDetail.Resource(id = repoId.value, name = repoName)
                    },
                ),
                block = {},
            )
        }

        suspend fun updatedCodeReviewRepoSelectionMode(
            trx: Transaction,
            scmInstallation: Installation,
            updatedMode: RepoSelectionMode,
            block: suspend (Transaction) -> Unit,
        ) {
            when (updatedMode) {
                RepoSelectionMode.All -> executeAndAudit(
                    trx = trx,
                    type = AuditLogType.CodeReviewRepoSelectionModeUpdated,
                    installationInfo = scmInstallation.asInstallationInfo(),
                    action = listOf("Enabled code review for all repositories"),
                    block = block,
                )

                else -> execute(trx = trx, block = block)
            }
        }

        suspend fun removedCodeReviewExcludedPaths(
            trx: Transaction,
            repos: Map<RepoId, String>,
        ) {
            if (repos.isEmpty()) return

            executeAndAudit(
                trx = trx,
                type = AuditLogType.CodeReviewExcludedPathsRemoved,
                installationInfo = null,
                action = listOf("Removed code review excluded paths for ${countRepos(repos.size)}: ${repos.values.joinToString { it }}"),
                detail = AuditLogDetail(
                    removed = repos.map { (repoId, repoName) ->
                        AuditLogDetail.Resource(id = repoId.value, name = repoName)
                    },
                ),
                block = {},
            )
        }

        suspend fun changedCodeReviewExcludedPaths(
            trx: Transaction,
            repos: Map<RepoId, String>,
        ) {
            if (repos.isEmpty()) return

            executeAndAudit(
                trx = trx,
                type = AuditLogType.CodeReviewExcludedPathsUpdated,
                installationInfo = null,
                action = listOf("Changed code review excluded paths for ${countRepos(repos.size)}: ${repos.values.joinToString { it }}"),
                detail = AuditLogDetail(
                    modified = repos.map { (repoId, repoName) ->
                        AuditLogDetail.Resource(id = repoId.value, name = repoName)
                    },
                ),
                block = {},
            )
        }

        private fun countRepos(count: Int) = when (count == 1) {
            true -> "1 repository"
            else -> "$count repositories"
        }
    }

    private suspend fun <T> execute(
        block: suspend (Transaction) -> T,
    ): T {
        return execute(null, block)
    }

    private suspend fun <T> execute(
        trx: Transaction? = null,
        block: suspend (Transaction) -> T,
    ): T {
        return suspendedTransaction(trx) { block(this) }
    }

    private suspend fun <T> executeAndAudit(
        trx: Transaction? = null,
        type: AuditLogType,
        installationInfo: InstallationInfo?,
        action: List<String>,
        detail: AuditLogDetail? = null,
        block: suspend (Transaction) -> T,
    ): T {
        return executeAndAudit(
            trx = trx,
            type = type,
            provider = installationInfo?.provider,
            installationId = installationInfo?.id,
            installationKey = installationInfo?.installationKey,
            installationName = installationInfo?.name,
            action = action,
            detail = detail,
            block = block,
        )
    }

    private suspend fun <T> executeAndAudit(
        trx: Transaction? = null,
        type: AuditLogType,
        provider: Provider?,
        installationId: InstallationId?,
        installationKey: UUID?,
        installationName: String?,
        action: List<String>,
        detail: AuditLogDetail? = null,
        block: suspend (Transaction) -> T,
    ): T {
        return suspendedTransaction(trx) {
            val result = block(this)

            auditLogStore.create(
                trx = this,
                orgId = orgId,
                type = type,
                actorId = actor.id,
                actorType = actor.type,
                actorName = actor.name,
                actorAvatarUrl = actor.avatarUrl,
                actorEmail = when (actor) {
                    is Actor.Person,
                        -> actor.email

                    is Actor.SlackUser,
                    is Actor.ApiKey,
                    is Actor.DataSource,
                    Actor.System,
                        -> null
                },
                actorProvider = when (actor) {
                    is Actor.DataSource,
                        -> actor.provider

                    is Actor.ApiKey,
                        -> actor.provider

                    is Actor.System,
                        -> actor.provider

                    is Actor.SlackUser,
                        -> Provider.Slack

                    is Actor.Person,
                        -> null
                },
                provider = provider,
                installationId = installationId,
                installationKey = installationKey,
                installationName = installationName,
                action = action,
                detail = detail,
            )

            result
        }
    }

    suspend fun resolveResource(
        orgMemberId: OrgMemberId,
    ): AuditLogDetail.Resource {
        val displayName = identityStore.findBestIdentityDisplayNameAndAvatar(orgId = orgId, orgMemberId = orgMemberId)?.first

        return AuditLogDetail.Resource(
            id = orgMemberId.value,
            name = displayName ?: "Member ${orgMemberId.value}",
        )
    }
}
