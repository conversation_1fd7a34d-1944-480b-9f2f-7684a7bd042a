package com.nextchaptersoftware.auditlog

import com.nextchaptersoftware.db.models.ApiKeyAudience
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.ScmTeam

internal object Extensions {
    fun ScmTeam.asInstallationInfo(): InstallationInfo {
        return InstallationInfo(
            id = installationId,
            orgId = orgId,
            provider = provider,
            installationExternalId = providerExternalId,
            name = displayName,
        )
    }

    fun Installation.asInstallationInfo(): InstallationInfo {
        return InstallationInfo(
            id = id,
            orgId = orgId,
            provider = provider,
            installationExternalId = installationExternalId,
            name = displayName,
        )
    }

    val ApiKeyAudience.description: String get() = when (this) {
        ApiKeyAudience.PublicApi -> "API"
        ApiKeyAudience.ScimApi -> "SCIM API"
    }
}
