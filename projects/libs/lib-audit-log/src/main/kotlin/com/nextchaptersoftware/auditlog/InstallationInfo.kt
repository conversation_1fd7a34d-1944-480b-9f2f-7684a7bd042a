package com.nextchaptersoftware.auditlog

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.utils.StringUuidExtensions
import java.util.UUID

data class InstallationInfo(
    val id: InstallationId,
    val orgId: OrgId,
    val provider: Provider,
    val installationExternalId: String,
    val name: String,
) {
    val installationKey: UUID
        get() = StringUuidExtensions.uuidFromString("$orgId/${provider.dbOrdinal}/$installationExternalId")
}
