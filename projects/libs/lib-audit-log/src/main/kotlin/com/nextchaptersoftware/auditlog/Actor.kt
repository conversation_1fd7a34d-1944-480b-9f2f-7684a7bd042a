package com.nextchaptersoftware.auditlog

import com.nextchaptersoftware.db.models.AuditLog
import com.nextchaptersoftware.db.models.AuditLogActorType
import com.nextchaptersoftware.db.models.OrgApiKeyId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.StringUuidExtensions
import io.ktor.http.Url
import java.util.UUID

sealed class Actor {
    abstract val id: UUID
    abstract val type: AuditLogActorType
    abstract val name: String
    abstract val avatarUrl: Url?
    abstract val provider: Provider?

    data class Person(
        val personId: PersonId,
        override val name: String,
        override val avatarUrl: Url?,
        val email: EmailAddress?,
    ) : Actor() {
        override val id = personId.value
        override val type = AuditLogActorType.Person
        override val provider = null
    }

    data class SlackUser(
        val slackUserId: String,
        override val name: String,
        override val avatarUrl: Url?,
    ) : Actor() {
        override val id = StringUuidExtensions.uuidFromString(slackUserId)
        override val type = AuditLogActorType.Person
        override val provider = Provider.Slack
    }

    object System : Actor() {
        override val id = AuditLog.SYSTEM_ACTOR_ID
        override val type = AuditLogActorType.System
        override val name = "Unblocked"
        override val avatarUrl = null
        override val provider = Provider.Unblocked
    }

    data class ApiKey(
        private val apiKeyId: OrgApiKeyId,
        private val apiKeyName: String,
        override val provider: Provider?,
    ) : Actor() {
        override val id = apiKeyId.value
        override val type = AuditLogActorType.ApiKey
        override val name = apiKeyName
        override val avatarUrl = null
    }

    data class DataSource(
        override val provider: Provider,
    ) : Actor() {
        override val id = StringUuidExtensions.uuidFromString(provider.uniqueSignature)
        override val type = AuditLogActorType.DataSource
        override val name = provider.displayName
        override val avatarUrl = null
    }
}
