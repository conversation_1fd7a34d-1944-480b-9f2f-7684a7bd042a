package com.nextchaptersoftware.review

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.event.queue.enqueue.EventEnqueueService
import com.nextchaptersoftware.serialization.Serialization.encode
import com.nextchaptersoftware.types.Hash

class CodeReviewEventProducer(
    private val eventEnqueueService: EventEnqueueService,
) {
    fun enqueuePullRequestChangedEvent(
        orgId: OrgId,
        scmTeamId: ScmTeamId,
        repoId: RepoId,
        prId: PullRequestId,
        changeType: CodeReviewEventType,
        commit: Hash,
    ) {
        sendEvent(
            CodeReviewEvent.PullRequestChanged(
                orgId = orgId,
                scmTeamId = scmTeamId,
                repoId = repoId,
                prId = prId,
                eventType = changeType,
                commit = commit,
            ),
        )
    }

    fun sendEvent(event: CodeReviewEvent) {
        eventEnqueueService.enqueueEvent(event.encode())
    }
}
