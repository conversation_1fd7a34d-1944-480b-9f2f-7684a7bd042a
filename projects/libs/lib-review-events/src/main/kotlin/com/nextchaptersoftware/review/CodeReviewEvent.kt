package com.nextchaptersoftware.review

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.types.Hash
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class CodeReviewEvent {
    abstract val priority: MessagePriority
    abstract val orgId: OrgId

    @Serializable
    @SerialName("CodeReviewPullRequestChangedEvent")
    data class PullRequestChanged(
        override val orgId: OrgId,
        val scmTeamId: ScmTeamId,
        val repoId: RepoId,
        val prId: PullRequestId,
        val eventType: CodeReviewEventType,
        val commit: Hash,
    ) : CodeReviewEvent() {
        override val priority: MessagePriority = MessagePriority.DEFAULT
    }
}
