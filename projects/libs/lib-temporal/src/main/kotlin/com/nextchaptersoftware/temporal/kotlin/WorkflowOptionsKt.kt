package com.nextchaptersoftware.temporal.kotlin

import com.nextchaptersoftware.temporal.kotlin.WorkflowId.newWorkflowId
import io.temporal.client.WorkflowOptions
import kotlin.time.toJavaDuration

object WorkflowOptionsKt {

    /**
     * @see WorkflowId.newWorkflowId
     */
    inline fun <reified Workflow> WorkflowOptions.Builder.setWorkflowIdKt(
        vararg ids: Any,
    ) {
        val workflowId = newWorkflowId<Workflow>(*ids)
        setWorkflowId(workflowId)
    }

    /**
     * @see io.temporal.client.WorkflowOptions.Builder.setWorkflowRunTimeout
     */
    fun WorkflowOptions.Builder.setWorkflowRunTimeoutKt(
        duration: kotlin.time.Duration,
    ) {
        setWorkflowRunTimeout(duration.toJavaDuration())
    }
}
