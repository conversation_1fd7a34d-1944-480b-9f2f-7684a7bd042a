package com.nextchaptersoftware.temporal.kotlin

import io.temporal.client.schedules.Schedule
import io.temporal.client.schedules.ScheduleDescription
import io.temporal.client.schedules.ScheduleUpdate

class ScheduleDsl(
    val scheduleClientKt: ScheduleClientKt,
) {
    @Suppress(
        "TooGenericExceptionCaught",
        "SwallowedException",
    )
    inline fun schedule(
        scheduleId: String,
        configure: Schedule.Builder.() -> Unit,
    ): ScheduleDescription {
        val handle = scheduleClientKt.scheduleClient.getHandle(scheduleId)
        val schedule = Schedule.newBuilder()
            .apply(configure)
            .build()

        val scheduleDescription = try {
            handle.describe() // throws if schedule doesn't exist
        } catch (_: Exception) {
            scheduleClientKt.createSchedule(scheduleId, schedule).describe()
        }

        if (scheduleDescription.schedule != schedule) {
            handle.update { input ->
                ScheduleUpdate(schedule)
            }
        }

        return scheduleDescription
    }
}
