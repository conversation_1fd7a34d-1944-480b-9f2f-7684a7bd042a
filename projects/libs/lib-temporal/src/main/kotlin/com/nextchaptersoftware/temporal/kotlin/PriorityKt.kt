package com.nextchaptersoftware.temporal.kotlin

import io.temporal.common.Priority

@Suppress("MagicNumber")
object PriorityKt {

    val HIGHEST = priorityKey(1)
    val HIGH = priorityKey(3)
    val NORMAL = priorityKey(5)
    val LOW = priorityKey(7)
    val LOWEST = priorityKey(9)

    fun newBuilder(
        configure: Priority.Builder.() -> Unit,
    ): Priority {
        return Priority.newBuilder()
            .apply(configure)
            .build()
    }

    /**
     * @see io.temporal.common.Priority.Builder.setPriorityKey
     */
    fun priorityKey(priorityKey: Int) = newBuilder {
        setPriorityKey(priorityKey)
    }
}
