package com.nextchaptersoftware.temporal.ktor

import com.nextchaptersoftware.temporal.Temporal
import com.nextchaptersoftware.temporal.kotlin.WorkerFactoryKt
import com.nextchaptersoftware.temporal.kotlin.createWorkerFactoryKt
import io.ktor.server.application.Application
import io.ktor.server.application.BaseApplicationPlugin
import io.ktor.util.AttributeKey
import io.ktor.utils.io.core.Closeable

class KtorTemporal(
    private val factory: WorkerFactoryKt,
) : Closeable {

    companion object : BaseApplicationPlugin<Application, WorkerFactoryKt, KtorTemporal> {

        override val key = AttributeKey<KtorTemporal>("KtorTemporal")

        override fun install(
            pipeline: Application,
            configure: WorkerFactoryKt.() -> Unit,
        ): KtorTemporal {
            val temporal = Temporal.forLive()
            val factory = temporal.createWorkerFactoryKt()
            return KtorTemporal(
                factory = factory.apply(configure),
            )
        }
    }

    override fun close() {
        factory.close()
    }
}
