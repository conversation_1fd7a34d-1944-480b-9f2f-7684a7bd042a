package com.nextchaptersoftware.temporal.kotlin

import io.temporal.workflow.CancelExternalWorkflowException
import io.temporal.workflow.ExternalWorkflowStub

object ExternalWorkflowStubKt {

    /**
     * @see io.temporal.workflow.ExternalWorkflowStub.cancel
     */
    fun ExternalWorkflowStub.cancelIdempotently() {
        try {
            cancel()
        } catch (_: CancelExternalWorkflowException) {
            // workflow not found, it's either already cancelled or completed
        }
    }
}
