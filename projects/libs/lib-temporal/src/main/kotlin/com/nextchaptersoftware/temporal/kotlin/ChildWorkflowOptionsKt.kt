package com.nextchaptersoftware.temporal.kotlin

import com.nextchaptersoftware.temporal.kotlin.WorkflowId.newWorkflowId
import io.temporal.workflow.ChildWorkflowOptions

object ChildWorkflowOptionsKt {

    /**
     * @see WorkflowId.newWorkflowId
     */
    inline fun <reified Workflow> ChildWorkflowOptions.Builder.setWorkflowIdKt(
        vararg ids: Any,
    ) {
        val workflowId = newWorkflowId<Workflow>(*ids)
        setWorkflowId(workflowId)
    }
}
