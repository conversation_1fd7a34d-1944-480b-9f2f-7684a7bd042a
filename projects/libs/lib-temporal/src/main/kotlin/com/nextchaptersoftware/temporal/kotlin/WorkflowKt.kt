package com.nextchaptersoftware.temporal.kotlin

import io.temporal.activity.ActivityOptions
import io.temporal.workflow.ChildWorkflowOptions
import io.temporal.workflow.Workflow
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

object WorkflowKt {

    /**
     * @see io.temporal.workflow.Workflow.newActivityStub
     */
    inline fun <reified T> newActivityStub(
        configure: ActivityOptions.Builder.() -> Unit = {},
    ): T {
        val timeout = 5.minutes.toJavaDuration()

        return Workflow.newActivityStub(
            T::class.java,
            ActivityOptions.newBuilder()
                .setStartToCloseTimeout(timeout)
                .apply(configure)
                .build(),
        )
    }

    /**
     * @see io.temporal.workflow.Workflow.newChildWorkflowStub
     */
    inline fun <reified Children> newChildWorkflowStub(
        configure: ChildWorkflowOptions.Builder.() -> Unit = {},
    ): Children {
        return Workflow.newChildWorkflowStub(
            Children::class.java,
            ChildWorkflowOptions.newBuilder()
                .apply(configure)
                .build(),
        )
    }

    /**
     * @see io.temporal.workflow.Workflow.newExternalWorkflowStub
     */
    inline fun <reified Workflow> newExternalWorkflowStub(
        workflowId: String,
    ): Workflow {
        return Workflow.newExternalWorkflowStub<Workflow>(
            Workflow::class.java,
            workflowId,
        )
    }

    /**
     * @see io.temporal.workflow.Workflow.newDetachedCancellationScope
     */
    fun newDetachedCancellationScopeRun(
        block: () -> Unit,
    ) {
        Workflow.newDetachedCancellationScope(block).run()
    }

    fun sleep(duration: Duration) {
        Workflow.sleep(duration.toJavaDuration())
    }
}
