package com.nextchaptersoftware.temporal.kotlin

import io.temporal.client.schedules.ScheduleIntervalSpec
import io.temporal.client.schedules.ScheduleSpec
import java.time.Duration as JavaDuration
import java.time.temporal.ChronoUnit
import kotlin.time.Duration

object ScheduleSpecsKt {

    fun ScheduleSpec(
        configure: ScheduleSpec.Builder.() -> Unit,
    ): ScheduleSpec {
        return ScheduleSpec.newBuilder()
            .apply(configure)
            .build()
    }

    fun intervals(duration: Duration) = ScheduleSpec {
        val spec = ScheduleIntervalSpec(
           JavaDuration.of(duration.inWholeMilliseconds, ChronoUnit.MILLIS),
        )
        setIntervals(
            listOf(spec),
        )
    }
}
