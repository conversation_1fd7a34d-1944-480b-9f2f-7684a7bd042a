package com.nextchaptersoftware.temporal.kotlin

import io.temporal.client.WorkflowOptions
import io.temporal.client.schedules.Schedule
import io.temporal.client.schedules.ScheduleActionStartWorkflow
import io.temporal.client.schedules.ScheduleSpec

class WorkerDsl(
    val workerKt: WorkerKt,
    val scheduleDsl: ScheduleDsl,
) {
    inline fun <reified Workflow> workflow(
        noinline factory: () -> Workflow,
    ) {
        workerKt.registerWorkflowImplementationFactory<Workflow>(factory)
    }

    fun <Activity> activity(
        factory: () -> Activity,
    ) {
        val activity = factory()
        workerKt.registerActivitiesImplementations<Activity>(activity)
    }

    inline fun <reified Schedule> schedule(
        scheduleId: String,
        scheduleSpec: ScheduleSpec,
        workflowOptions: WorkflowOptions.Builder.() -> Unit = {},
        scheduleOptions: Schedule.Builder.() -> Unit = {},
    ) {
        val action = ScheduleActionStartWorkflow.newBuilder()
            .setWorkflowType(
                Schedule::class.java.simpleName,
            )
            .setOptions(
                WorkflowOptions.newBuilder()
                    .setTaskQueue(workerKt.worker.taskQueue)
                    .setWorkflowId(scheduleId)
                    .apply(workflowOptions)
                    .build(),
            )
            .build()

        scheduleDsl.schedule(
            scheduleId = scheduleId,
        ) {
            setAction(action)
            setSpec(scheduleSpec)
            apply(scheduleOptions)
        }
    }
}
