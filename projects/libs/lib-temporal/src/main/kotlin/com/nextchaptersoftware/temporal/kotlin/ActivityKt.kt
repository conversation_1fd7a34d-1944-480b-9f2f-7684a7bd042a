package com.nextchaptersoftware.temporal.kotlin

import io.temporal.activity.Activity
import io.temporal.activity.ActivityExecutionContext
import java.util.concurrent.Executors
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.runBlocking

object ActivityKt {

    private val dispatcher = Executors
        .newCachedThreadPool()
        .asCoroutineDispatcher()

    /**
     * Executes a suspendable activity [block] in a new thread
     */
    fun <T> suspendedActivity(
        context: CoroutineContext? = null,
        block: suspend () -> T,
    ): T {
        val context = context ?: EmptyCoroutineContext
        return runBlocking(context = dispatcher + context) {
            block()
        }
    }

    /**
     * Executes a suspendable activity [block] in a new thread, providing access to the underlying [ActivityContext].
     *
     * This method works only in the original Activity Execution thread
     *
     * @see [io.temporal.activity.Activity.getExecutionContext]
     */
    fun <T> suspendedActivityContext(
        context: CoroutineContext? = null,
        block: suspend ActivityScope.() -> T,
    ): T {
        val activityScope = ActivityScope(
            executionContext = Activity.getExecutionContext(),
        )
        return suspendedActivity(context = context) {
            activityScope.block()
        }
    }
}

class ActivityScope(
    val executionContext: ActivityExecutionContext,
)
