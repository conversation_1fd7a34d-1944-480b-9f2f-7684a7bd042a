package com.nextchaptersoftware.temporal.kotlin

import io.temporal.worker.Worker
import io.temporal.worker.WorkflowImplementationOptions

class WorkerKt(
    val worker: Worker,
) {
    /**
     * @see io.temporal.worker.Worker.registerWorkflowImplementationFactory
     */
    inline fun <reified Workflow> registerWorkflowImplementationFactory(
        noinline factory: () -> Workflow,
        configure: WorkflowImplementationOptions.Builder.() -> Unit = {},
    ) {
        val options = WorkflowImplementationOptions.newBuilder()
            .apply(configure)
            .build()

        worker.registerWorkflowImplementationFactory(
            Workflow::class.java,
            factory,
            options,
        )
    }

    /**
     * @see io.temporal.worker.Worker.registerActivitiesImplementations
     */
    fun <Activity> registerActivitiesImplementations(
        vararg activity: Activity,
    ) {
        worker.registerActivitiesImplementations(*activity)
    }
}
