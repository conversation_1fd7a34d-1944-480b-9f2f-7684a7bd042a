package com.nextchaptersoftware.temporal.kotlin

import java.util.UUID

object WorkflowId {

    /**
     * Create a deterministic workflow id for the given [Workflow].
     */
    inline fun <reified Workflow> newWorkflowId(vararg ids: Any): String {
        val type = Workflow::class.simpleName
        val value = ids.joinToString("/")
        return "$type:$value"
    }

    /**
     * Usage of random workflow ids is not recommended as they force new workflow executions.
     *
     * Instead, use [newWorkflowId] to create a deterministic workflow id derived from a long-lived identifier (ie. PG model id)
     */
    inline fun <reified T> randomWorkflowId(): String {
        val id = UUID.randomUUID()
        return newWorkflowId<T>(id)
    }
}
