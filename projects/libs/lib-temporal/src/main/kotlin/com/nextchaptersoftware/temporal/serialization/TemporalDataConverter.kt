package com.nextchaptersoftware.temporal.serialization

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.module.kotlin.kotlinModule
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.takeFrom
import io.temporal.common.converter.DataConverter
import io.temporal.common.converter.DefaultDataConverter
import io.temporal.common.converter.JacksonJsonPayloadConverter
import kotlin.time.Instant

/**
 * Jackson module for kotlin.time.Instant serialization/deserialization.
 */
class KotlinTimeModule : SimpleModule() {
    init {
        addSerializer(Instant::class.java, KotlinInstantSerializer())
        addDeserializer(Instant::class.java, KotlinInstantDeserializer())
    }
}

/**
 * Serializer for kotlin.time.Instant.
 * Uses string representation to preserve full precision.
 */
class KotlinInstantSerializer : JsonSerializer<Instant>() {
    override fun serialize(value: Instant, gen: JsonGenerator, serializers: SerializerProvider) {
        gen.writeString(value.toString())
    }
}

/**
 * Deserializer for kotlin.time.Instant.
 * Parses from string representation to preserve full precision.
 */
class KotlinInstantDeserializer : JsonDeserializer<Instant>() {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Instant {
        val text = p.text
        return Instant.parse(text)
    }
}

class KtorUrlModule : SimpleModule("KtorUrlModule") {
    init {
        addSerializer(Url::class.java, KtorUrlSerializer())
        addDeserializer(Url::class.java, KtorUrlDeserializer())
    }

    class KtorUrlSerializer : JsonSerializer<Url>() {
        override fun serialize(value: Url?, gen: JsonGenerator, serializers: SerializerProvider) {
            if (value == null) {
                gen.writeNull()
            } else {
                gen.writeString(value.toString())
            }
        }

        override fun handledType(): Class<Url> = Url::class.java
    }

    class KtorUrlDeserializer : JsonDeserializer<Url>() {
        override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Url {
            val node = p.codec.readTree<JsonNode>(p)
            check(node.isTextual) {
                "expected string, got ${node.nodeType}"
            }
            val raw = node.asText()
            return try {
                URLBuilder().takeFrom(raw).build()
            } catch (_: Throwable) {
                error("invalid url: $raw")
            }
        }
    }
}

/**
 * Custom DataConverter that supports kotlin.time.Instant serialization.
 */
object TemporalDataConverter {
    fun create(): DataConverter {
        val objectMapper = ObjectMapper()
        objectMapper
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .registerModule(kotlinModule())
            .registerModule(KotlinTimeModule())
            .registerModule(KotlinxJsonModule())
            .registerModule(KtorUrlModule())

        val customPayloadConverter = JacksonJsonPayloadConverter(objectMapper)

        return DefaultDataConverter.newDefaultInstance()
            .withPayloadConverterOverrides(customPayloadConverter)
    }
}
