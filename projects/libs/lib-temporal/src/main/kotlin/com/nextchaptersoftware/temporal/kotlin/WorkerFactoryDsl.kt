package com.nextchaptersoftware.temporal.kotlin

import com.nextchaptersoftware.temporal.annotations.Queue
import io.temporal.client.schedules.ScheduleClient
import java.io.Closeable

class WorkerFactoryDsl(
    val factoryKt: WorkerFactoryKt,
) : Closeable by factoryKt {

    fun worker(
        taskQueue: Queue,
        configure: WorkerDsl.() -> Unit,
    ): WorkerDsl {
        return worker(
            taskQueue = taskQueue.id,
            configure = configure,
        )
    }

    fun worker(
        taskQueue: String,
        configure: WorkerDsl.() -> Unit,
    ): WorkerDsl {
        val workerKt = factoryKt.newWorker(taskQueue)
        val workerDsl = WorkerDsl(
            workerKt = workerKt,
            scheduleDsl = ScheduleDsl(
                scheduleClientKt = ScheduleClientKt {
                    ScheduleClient.newInstance(factoryKt.factory.workflowClient.workflowServiceStubs)
                },
            ),
        )
        return workerDsl.apply(configure)
    }
}
