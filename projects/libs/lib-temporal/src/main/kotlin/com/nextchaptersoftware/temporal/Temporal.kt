package com.nextchaptersoftware.temporal

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.temporal.kotlin.WorkflowClientKt
import com.nextchaptersoftware.temporal.kotlin.WorkflowEnvironmentKt
import com.nextchaptersoftware.temporal.serialization.TemporalDataConverter
import io.temporal.client.WorkflowClientOptions
import io.temporal.serviceclient.WorkflowServiceStubs
import io.temporal.serviceclient.WorkflowServiceStubsOptions

private val config = GlobalConfig.INSTANCE.temporal

object Temporal {

    fun forLive(
        configure: WorkflowClientOptions.Builder.() -> Unit = {},
    ) = forEnvironment(
        environment = Live,
        configure = configure,
    )

    fun forEnvironment(
        environment: WorkflowEnvironmentKt,
        configure: WorkflowClientOptions.Builder.() -> Unit = {},
    ): WorkflowClientKt {
        val workflowClient = environment.newWorkflowClient {
            setDataConverter(TemporalDataConverter.create())
            apply(configure)
        }
        return workflowClient.let(::WorkflowClientKt)
    }

    val Live = WorkflowEnvironmentKt.Live(
        WorkflowServiceStubs.newServiceStubs(
            WorkflowServiceStubsOptions.newBuilder()
                .setTarget(config.baseApiUri)
                .build(),
        ),
    )

    val Local = WorkflowEnvironmentKt.Live(
        WorkflowServiceStubs.newLocalServiceStubs(),
    )

    val TestInMemory = WorkflowEnvironmentKt.Test()

    val TestInLive = WorkflowEnvironmentKt.Test {
        setUseExternalService(true)
        setWorkflowServiceStubsOptions(
            WorkflowServiceStubsOptions.newBuilder()
                .setTarget(config.baseApiUri)
                .build(),
        )
    }
}
