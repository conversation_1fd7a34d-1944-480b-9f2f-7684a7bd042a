package com.nextchaptersoftware.temporal.workflows

import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildTriageExecutionId
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.BuildTriageId
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.temporal.annotations.Queue
import com.nextchaptersoftware.temporal.annotations.RunsOn
import com.nextchaptersoftware.temporal.workflows.CITriageWorkflow.TriageRequest
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod

@RunsOn(Queue.CiTriages)
@WorkflowInterface
interface CITriagePipelineWorkflow {

    data class TriagePipelineRequest(
        val triageRequest: TriageRequest,
        val jobIds: List<BuildJobId>,
        val executionId: BuildTriageExecutionId? = null,
        val options: TriagePipelineOptions? = null,
    )

    data class TriagePipelineOptions(
        val isEphemeral: Boolean,
        val templateOverrides: Collection<MLInferenceTemplateId>?,
    )

    data class TriagePipelineResult(
        val triageId: BuildTriageId?,
        val isVisible: Boolean,
        val filter: BuildTriageFilterStage?,
        val triageModelResult: String? = null,
        val triageEvalResult: String? = null,
    )

    @WorkflowMethod
    fun execute(event: TriagePipelineRequest): TriagePipelineResult?
}
