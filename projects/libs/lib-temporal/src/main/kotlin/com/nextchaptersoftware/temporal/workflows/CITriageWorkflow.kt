package com.nextchaptersoftware.temporal.workflows

import com.nextchaptersoftware.db.models.BuildId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.temporal.annotations.Queue
import com.nextchaptersoftware.temporal.annotations.RunsOn
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod

@RunsOn(Queue.CiTriages)
@WorkflowInterface
interface CITriageWorkflow {

    data class TriageRequest(
        val orgId: OrgId,
        val ciInstallationId: InstallationId,
        val scmTeamId: ScmTeamId,
        val repoId: RepoId,
        val pullRequestId: PullRequestId,
        val buildId: BuildId,
        val options: TriageOptions? = null,
    )

    data class TriageOptions(
        val forceCommentUpdate: Boolean? = null,
        val disablePullRequestMuteCheck: Boolean? = null,
        val disableTriageExistsCheck: Boolean? = null,
    )

    @WorkflowMethod
    fun execute(triageRequest: TriageRequest)
}
