package com.nextchaptersoftware.temporal.kotlin

import io.ktor.utils.io.core.Closeable
import io.temporal.worker.WorkerFactory
import io.temporal.worker.WorkerOptions
import java.util.concurrent.TimeUnit
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

class WorkerFactoryKt(
    val factory: WorkerFactory,
    private val gracefulShutdownTimeout: Duration = 30.seconds,
) : Closeable {

    /**
     * @see io.temporal.worker.WorkerFactory.newWorker
     */
    fun newWorker(
        taskQueue: String,
        configure: WorkerOptions.Builder.() -> Unit = {},
    ): WorkerKt {
        val options = WorkerOptions.newBuilder()
            .apply(configure)
            .build()
        return factory.newWorker(taskQueue, options).let(::WorkerKt)
    }

    /**
     * @see io.temporal.worker.WorkerFactory.shutdown
     * @see io.temporal.worker.WorkerFactory.awaitTermination
     */
    override fun close() {
        factory.shutdown()
        factory.awaitTermination(gracefulShutdownTimeout.inWholeMilliseconds, TimeUnit.MILLISECONDS)
    }
}
