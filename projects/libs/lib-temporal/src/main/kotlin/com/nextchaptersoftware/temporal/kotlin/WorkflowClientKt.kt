package com.nextchaptersoftware.temporal.kotlin

import com.nextchaptersoftware.temporal.annotations.RunsOn
import io.temporal.api.common.v1.WorkflowExecution
import io.temporal.api.enums.v1.WorkflowIdConflictPolicy.WORKFLOW_ID_CONFLICT_POLICY_USE_EXISTING
import io.temporal.client.WorkflowClient
import io.temporal.client.WorkflowExecutionAlreadyStarted
import io.temporal.client.WorkflowOptions
import io.temporal.client.WorkflowStub
import io.temporal.worker.WorkerFactory
import kotlin.reflect.full.findAnnotation

class WorkflowClientKt(
    val client: WorkflowClient,
) {
    /**
     * @see io.temporal.client.WorkflowClient.newWorkflowStub
     */
    inline fun <reified Workflow> newWorkflowStub(
        configure: WorkflowOptions.Builder.() -> Unit = {},
    ): Workflow {
        val workflowOptions = WorkflowOptions.newBuilder()
            .setWorkflowIdConflictPolicy(WORKFLOW_ID_CONFLICT_POLICY_USE_EXISTING)
            .apply {
                Workflow::class.findAnnotation<RunsOn>()?.let {
                    setTaskQueue(it.queue.id)
                }
            }
            .apply(configure)
            .build()

        check(workflowOptions.workflowId != null) { "WorkflowId must be specified" }
        check(workflowOptions.taskQueue != null) { "TaskQueue or @RunsOn must be specified" }

        return client.newWorkflowStub(
            Workflow::class.java,
            workflowOptions,
        )
    }

    /**
     * @see io.temporal.client.WorkflowClient.newUntypedWorkflowStub
     */
    fun newUntypedWorkflowStub(
        workflowId: String,
    ): WorkflowStub {
        return client.newUntypedWorkflowStub(workflowId)
    }

    companion object {
        /**
         * @see io.temporal.client.WorkflowClient.start
         */
        fun startIdempotently(
            block: () -> Unit,
        ): WorkflowExecution {
            return try {
                WorkflowClient.start(block)
            } catch (e: WorkflowExecutionAlreadyStarted) {
                e.execution
            }
        }
    }
}

internal fun WorkflowClientKt.createWorkerFactory(): WorkerFactory {
    return WorkerFactory.newInstance(client)
}

internal fun WorkflowClientKt.createWorkerFactoryKt(): WorkerFactoryKt {
    return createWorkerFactory().let(::WorkerFactoryKt)
}
