package com.nextchaptersoftware.temporal.serialization

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.module.SimpleModule
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

private val json = Json {
    prettyPrint = false
    encodeDefaults = true
    ignoreUnknownKeys = true
}

abstract class BaseJsonElementSerializer<T : JsonElement> : JsonSerializer<T>() {
    override fun serialize(value: T, gen: JsonGenerator, serializers: SerializerProvider) {
        val rawJson = json.encodeToString(JsonElement.serializer(), value)
        gen.writeRawValue(rawJson)
    }
}

abstract class BaseJsonElementDeserializer<T : JsonElement>(
    private val cast: (JsonElement) -> T,
) : JsonDeserializer<T>() {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): T {
        val jsonNode = p.codec.readTree<JsonNode>(p)
        val element = json.decodeFromString(JsonElement.serializer(), jsonNode.toString())
        return cast(element)
    }
}

class JsonObjectJacksonSerializer : BaseJsonElementSerializer<JsonObject>()

class JsonArrayJacksonSerializer : BaseJsonElementSerializer<JsonArray>()

class JsonPrimitiveJacksonSerializer : BaseJsonElementSerializer<JsonPrimitive>()

class JsonNullJacksonSerializer : BaseJsonElementSerializer<JsonNull>()

class JsonElementJacksonSerializer : BaseJsonElementSerializer<JsonElement>()

class JsonObjectJacksonDeserializer : BaseJsonElementDeserializer<JsonObject>({ it.jsonObject })

class JsonArrayJacksonDeserializer : BaseJsonElementDeserializer<JsonArray>({ it.jsonArray })

class JsonPrimitiveJacksonDeserializer : BaseJsonElementDeserializer<JsonPrimitive>({ it.jsonPrimitive })

class JsonNullJacksonDeserializer : BaseJsonElementDeserializer<JsonNull>({ JsonNull })

class JsonElementJacksonDeserializer : BaseJsonElementDeserializer<JsonElement>({ it })

class KotlinxJsonModule : SimpleModule("KotlinxJsonModule") {
    init {
        addSerializer(JsonElement::class.java, JsonElementJacksonSerializer())
        addDeserializer(JsonElement::class.java, JsonElementJacksonDeserializer())

        addSerializer(JsonObject::class.java, JsonObjectJacksonSerializer())
        addDeserializer(JsonObject::class.java, JsonObjectJacksonDeserializer())

        addSerializer(JsonArray::class.java, JsonArrayJacksonSerializer())
        addDeserializer(JsonArray::class.java, JsonArrayJacksonDeserializer())

        addSerializer(JsonPrimitive::class.java, JsonPrimitiveJacksonSerializer())
        addDeserializer(JsonPrimitive::class.java, JsonPrimitiveJacksonDeserializer())

        addSerializer(JsonNull::class.java, JsonNullJacksonSerializer())
        addDeserializer(JsonNull::class.java, JsonNullJacksonDeserializer())
    }
}
