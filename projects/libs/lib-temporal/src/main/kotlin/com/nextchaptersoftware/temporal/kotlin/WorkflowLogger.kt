package com.nextchaptersoftware.temporal.kotlin

import io.temporal.workflow.Workflow
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty
import mu.KLogger
import mu.toKLogger

@Suppress("FunctionName")
fun WorkflowLogger(ctx: Any) = object : ReadOnlyProperty<Any?, KLogger> {

    val LOGGER by lazy {
        Workflow.getLogger(ctx::class.java).toKLogger()
    }

    override fun getValue(thisRef: Any?, property: KProperty<*>): KLogger {
        return LOGGER
    }
}
