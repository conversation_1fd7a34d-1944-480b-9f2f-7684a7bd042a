package com.nextchaptersoftware.temporal.kotlin

import com.google.protobuf.Duration
import io.grpc.Status
import io.grpc.StatusRuntimeException
import io.temporal.api.workflowservice.v1.RegisterNamespaceRequest
import io.temporal.client.WorkflowClient
import io.temporal.client.WorkflowClientOptions
import io.temporal.serviceclient.WorkflowServiceStubs
import io.temporal.testing.TestEnvironmentOptions
import io.temporal.testing.TestWorkflowEnvironment
import kotlin.time.Duration.Companion.days

sealed class WorkflowEnvironmentKt {

    class Live(
        val serviceStubs: WorkflowServiceStubs,
    ) : WorkflowEnvironmentKt() {

        fun newWorkflowClientInstance(
            configure: WorkflowClientOptions.Builder.() -> Unit = {},
        ): WorkflowClient {
            val options = WorkflowClientOptions.newBuilder()
                .apply(configure)
                .build()
            return WorkflowClient.newInstance(serviceStubs, options)
        }
    }

    class Test(
        private val workflowClientOptions: WorkflowClientOptions.Builder.() -> Unit = {},
        private val configure: TestEnvironmentOptions.Builder.() -> Unit = {},
    ) : WorkflowEnvironmentKt() {

        fun newTestEnvironment(
            options: WorkflowClientOptions.Builder.() -> Unit = {},
        ): TestWorkflowEnvironment {
            val testEnv = TestWorkflowEnvironment.newInstance(
                TestEnvironmentOptions.newBuilder()
                    .setWorkflowClientOptions(
                        WorkflowClientOptions.newBuilder()
                            .apply(workflowClientOptions)
                            .apply(options)
                            .build(),
                    )
                    .apply(configure)
                    .build(),
            )
            if (testEnv.namespace != null) {
                testEnv.registerNamespace()
            }
            return testEnv
        }

        private fun TestWorkflowEnvironment.registerNamespace() {
            try {
                val request = RegisterNamespaceRequest.newBuilder()
                    .setNamespace(namespace)
                    .setWorkflowExecutionRetentionPeriod(
                        Duration.newBuilder()
                            .setSeconds(1.days.inWholeSeconds)
                            .build(),
                    )
                    .build()

                workflowServiceStubs.blockingStub().registerNamespace(request)
            } catch (e: StatusRuntimeException) {
                if (e.status.code == Status.Code.UNIMPLEMENTED) {
                    return // in memory environment does not support namespaces
                }
                if (e.status.code == Status.Code.ALREADY_EXISTS) {
                    return
                }
                throw e
            }
        }
    }

    fun newWorkflowClient(
        configure: WorkflowClientOptions.Builder.() -> Unit = {},
    ): WorkflowClient {
        return when (this) {
            is Live -> newWorkflowClientInstance(configure)
            is Test -> newTestEnvironment(configure).workflowClient
        }
    }
}
