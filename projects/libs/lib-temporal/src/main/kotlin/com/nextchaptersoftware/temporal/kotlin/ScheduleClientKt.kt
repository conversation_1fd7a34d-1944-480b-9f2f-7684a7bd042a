package com.nextchaptersoftware.temporal.kotlin

import io.temporal.client.schedules.Schedule
import io.temporal.client.schedules.ScheduleClient
import io.temporal.client.schedules.ScheduleHandle
import io.temporal.client.schedules.ScheduleOptions

class ScheduleClientKt(
    withScheduleClient: () -> ScheduleClient,
) {
    val scheduleClient by lazy {
        withScheduleClient()
    }

    /**
     * @see io.temporal.client.schedules.ScheduleClient.createSchedule
     */
    fun createSchedule(
        scheduleId: String,
        schedule: Schedule,
        configure: ScheduleOptions.Builder.() -> Unit = {},
    ): ScheduleHandle {
        val options = ScheduleOptions.newBuilder()
            .apply(configure)
            .build()
        return scheduleClient.createSchedule(scheduleId, schedule, options)
    }
}
