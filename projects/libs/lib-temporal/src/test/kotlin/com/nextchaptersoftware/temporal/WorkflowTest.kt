package com.nextchaptersoftware.temporal

import com.nextchaptersoftware.temporal.kotlin.WorkflowKt.newActivityStub
import com.nextchaptersoftware.temporal.workflows.PingActivity
import com.nextchaptersoftware.temporal.workflows.PingActivityImpl
import io.temporal.client.WorkflowClient
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class WorkflowTest : TemporalTestsBase() {

    @WorkflowInterface
    interface OnConcurrentWorkflow {
        @WorkflowMethod
        fun execute(input: String)
    }

    @Nested
    inner class OnConcurrentWorkflowTest {

        @Test
        fun `concurrent workflows`() = temporalTest {
            val workflowId = "test-concurrent-workflows"

            val results = mutableListOf<String>()

            val temporal = configureTemporal {
                worker(taskQueue) {
                    workflow<OnConcurrentWorkflow> {
                        object : OnConcurrentWorkflow {
                            override fun execute(input: String) {
                                newActivityStub<PingActivity>().ping(input)
                            }
                        }
                    }
                    activity {
                        PingActivityImpl(results::add)
                    }
                }
            }

            // Start multiple workflows concurrently
            val executions = List(3) { i ->
                val task = "$workflowId/$i"
                val workflow = temporal.newWorkflowStub<OnConcurrentWorkflow> {
                    setTaskQueue(taskQueue)
                    setWorkflowId(task)
                }
                WorkflowClient.execute {
                    workflow.execute(task)
                }
            }

            // Wait for results
            executions.map { it.get() }

            // Verify all workflows completed successfully
            assertThat(results).containsExactlyInAnyOrder(
                "test-concurrent-workflows/0",
                "test-concurrent-workflows/1",
                "test-concurrent-workflows/2",
            )
        }
    }
}
