@file:Suppress("ktlint:nextchaptersoftware:no-test-delay-expression-rule")

package com.nextchaptersoftware.temporal.kotlin

import com.nextchaptersoftware.temporal.TemporalTestsBase
import com.nextchaptersoftware.temporal.kotlin.WorkflowKt.newActivityStub
import com.nextchaptersoftware.temporal.kotlin.WorkflowKt.newDetachedCancellationScopeRun
import com.nextchaptersoftware.temporal.workflows.PingActivity
import com.nextchaptersoftware.temporal.workflows.PingActivityImpl
import com.nextchaptersoftware.temporal.workflows.WaitActivity
import com.nextchaptersoftware.temporal.workflows.WaitActivityImpl
import io.temporal.client.WorkflowClient
import io.temporal.failure.CanceledFailure
import io.temporal.failure.TemporalFailure
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionException
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.delay
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class WorkflowKtTest : TemporalTestsBase() {

    @WorkflowInterface
    interface OnCancelWorkflow {
        @WorkflowMethod
        fun execute()
    }

    @Nested
    inner class OnCancelWorkflowTest {

        @Test
        fun `test cancellation workflow`() = temporalTest {
            val workflowId = "test-cancellation-workflow"
            val result = CompletableFuture<String>()

            val onCancelWorkflow = object : OnCancelWorkflow {
                override fun execute() {
                    val w = newActivityStub<WaitActivity>()
                    val p = newActivityStub<PingActivity>()
                    try {
                        w.wait(10.seconds)
                    } catch (e: TemporalFailure) {
                        if (e.cause is CanceledFailure) {
                            newDetachedCancellationScopeRun {
                                w.wait(3.seconds)
                                p.ping("cancellation")
                            }
                        }
                        throw e
                    }
                }
            }

            val temporal = configureTemporal {
                worker(taskQueue) {
                    workflow<OnCancelWorkflow> { onCancelWorkflow }
                    activity { WaitActivityImpl }
                    activity {
                        PingActivityImpl(result::complete)
                    }
                }
            }

            val stub = temporal.newWorkflowStub<OnCancelWorkflow> {
                setTaskQueue(taskQueue)
                setWorkflowId(workflowId)
            }
            val execution = WorkflowClient.execute {
                stub.execute()
            }

            delay(2.seconds)
            temporal.newUntypedWorkflowStub(workflowId).cancel()

            assertThrows<CompletionException> {
                execution.join()
            }

            assertThat(execution.isDone).isTrue
            assertThat(execution.isCompletedExceptionally).isTrue

            val value = result.get()
            assertThat(value).isEqualTo("cancellation")
        }
    }
}
