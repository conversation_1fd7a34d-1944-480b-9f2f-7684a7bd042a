@file:Suppress("ktlint:nextchaptersoftware:no-test-delay-expression-rule")

package com.nextchaptersoftware.temporal.workflows

import com.nextchaptersoftware.temporal.kotlin.ActivityKt.suspendedActivityContext
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration
import kotlin.time.Instant
import kotlinx.coroutines.delay

internal object WaitActivityImpl : WaitActivity {

    override fun wait(duration: Duration) = suspendedActivityContext {
        var stop = Instant.Companion.nowWithMicrosecondPrecision() + duration
        var i = 0
        while (Instant.Companion.nowWithMicrosecondPrecision() < stop) {
            executionContext.heartbeat(i)
            delay(duration / 100)
            i += 1
        }
    }
}
