package com.nextchaptersoftware.temporal.kotlin

import com.nextchaptersoftware.db.models.CIScm
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.temporal.kotlin.WorkflowId.newWorkflowId
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class WorkflowIdTest {

    @Test
    fun `newWorkflowId -- unique`() {
        assertThat(
            newWorkflowId<String>("123"),
        ).isEqualTo("String:123")

        val orgId = OrgId.random()

        assertThat(
            newWorkflowId<Org>(orgId),
        ).isEqualTo("Org:$orgId")
    }

    @Test
    fun `newWorkflowId -- multiple`() {
        val scmInstallationId = InstallationId.random()
        val ciInstallationId = InstallationId.random()

        assertThat(
            newWorkflowId<CIScm>(scmInstallationId, ciInstallationId),
        ).isEqualTo("CIScm:$scmInstallationId/$ciInstallationId")
    }
}
