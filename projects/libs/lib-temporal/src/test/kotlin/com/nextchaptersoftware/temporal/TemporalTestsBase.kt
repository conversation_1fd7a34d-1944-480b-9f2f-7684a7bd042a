package com.nextchaptersoftware.temporal

import com.nextchaptersoftware.temporal.kotlin.WorkerFactoryDsl
import com.nextchaptersoftware.temporal.kotlin.WorkerFactoryKt
import com.nextchaptersoftware.temporal.kotlin.WorkflowClientKt
import com.nextchaptersoftware.temporal.kotlin.WorkflowEnvironmentKt
import com.nextchaptersoftware.temporal.kotlin.createWorkerFactoryKt
import com.nextchaptersoftware.temporal.serialization.TemporalDataConverter
import io.temporal.testing.TestWorkflowEnvironment
import kotlin.jvm.javaClass
import kotlinx.coroutines.runBlocking

open class TemporalTestsBase(
    private val environment: WorkflowEnvironmentKt.Test? = null,
) {
    protected val taskQueue: String = javaClass.simpleName

    class TemporalTestDsl(
        private val workflowEnvironment: TestWorkflowEnvironment,
        private val workerFactoryKt: WorkerFactoryKt,
        private val workflowClientKt: WorkflowClientKt,
    ) {
        fun configureTemporal(
            configure: WorkerFactoryDsl.(TestWorkflowEnvironment) -> Unit,
        ): WorkflowClientKt {
            workerFactoryKt.let(::WorkerFactoryDsl).configure(workflowEnvironment)
            workerFactoryKt.factory.start()
            return workflowClientKt
        }
    }

    protected fun temporalTest(
        environment: WorkflowEnvironmentKt.Test? = null,
        block: suspend TemporalTestDsl.() -> Unit,
    ) {
        val environment = environment ?: this.environment ?: Temporal.TestInMemory

        val workflowTestEnv = environment.newTestEnvironment {
            setDataConverter(TemporalDataConverter.create())
            setNamespace("test")
        }

        val workflowClientKt = workflowTestEnv.workflowClient.let(::WorkflowClientKt)

        workflowClientKt.createWorkerFactoryKt().use { workerFactoryKt ->
            runBlocking {
                TemporalTestDsl(
                    workflowTestEnv,
                    workerFactoryKt,
                    workflowClientKt,
                ).block()
            }
        }
    }
}
