package com.nextchaptersoftware.temporal

import com.nextchaptersoftware.temporal.kotlin.WorkflowKt
import com.nextchaptersoftware.temporal.kotlin.WorkflowLogger
import io.temporal.activity.ActivityInterface
import io.temporal.activity.ActivityMethod
import io.temporal.workflow.QueryMethod
import io.temporal.workflow.SignalMethod
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

private val LOGGER = mu.KotlinLogging.logger {}

class WorkersTest : TemporalTestsBase() {

    @WorkflowInterface
    interface TestWorkflow {
        @WorkflowMethod
        fun processOrder(orderId: String): String

        @SignalMethod
        fun updateOrderStatus(status: String)

        @QueryMethod
        fun getCurrentStatus(): String
    }

    @WorkflowInterface
    interface ChildWorkflow {
        @WorkflowMethod
        fun processItem(itemId: String): String
    }

    @ActivityInterface
    interface OrderActivities {
        @ActivityMethod
        fun validateOrder(orderId: String): <PERSON><PERSON>an

        @ActivityMethod
        fun processPayment(orderId: String, amount: Double): String

        @ActivityMethod
        fun sendNotification(message: String): String
    }

    private class TestWorkflowImpl : TestWorkflow {
        private val logger by WorkflowLogger(this)
        private var currentStatus = "pending"

        override fun processOrder(orderId: String): String {
            val activities = WorkflowKt.newActivityStub<OrderActivities>()

            logger.info("Starting order processing for: $orderId")

            // Validate order
            val isValid = activities.validateOrder(orderId)
            if (!isValid) {
                return "Order validation failed"
            }

            currentStatus = "validated"
            logger.info("Order validated: $orderId")

            // Process payment
            val paymentResult = activities.processPayment(orderId, 99.99)
            currentStatus = "paid"
            logger.info("Payment processed: $paymentResult")

            // Send notification
            val notificationResult = activities.sendNotification("Order $orderId has been processed successfully")
            currentStatus = "completed"
            logger.info("Notification sent: $notificationResult")

            return "Order $orderId processed successfully"
        }

        override fun updateOrderStatus(status: String) {
            currentStatus = status
            logger.info("Order status updated to: $status")
        }

        override fun getCurrentStatus(): String = currentStatus
    }

    private class ChildWorkflowImpl : ChildWorkflow {

        private val logger by WorkflowLogger(this)

        override fun processItem(itemId: String): String {
            logger.info("Processing child workflow item: $itemId")
            return "Item $itemId processed by child workflow"
        }
    }

    private class OrderActivitiesImpl : OrderActivities {
        override fun validateOrder(orderId: String): Boolean {
            LOGGER.info("Validating order: $orderId")
            return orderId.isNotEmpty() && orderId.length > 3
        }

        override fun processPayment(orderId: String, amount: Double): String {
            LOGGER.info("Processing payment for order: $orderId, amount: $amount")
            return "Payment processed successfully for $orderId"
        }

        override fun sendNotification(message: String): String {
            LOGGER.info("Sending notification: $message")
            return "Notification sent: $message"
        }
    }

    @Test
    fun `can register and run a workflow using Workers against live environment`() = temporalTest {
        val workflowId = "live-test-queue"
        val temporal = configureTemporal {
            worker(taskQueue) {
                workflow<TestWorkflow> { TestWorkflowImpl() }
                workflow<ChildWorkflow> { ChildWorkflowImpl() }
                activity<OrderActivities> { OrderActivitiesImpl() }
            }
        }

        val stub = temporal.newWorkflowStub<TestWorkflow> {
            setTaskQueue(taskQueue)
            setWorkflowId(workflowId)
        }
        val result = stub.processOrder("LIVE-ORDER-789")
        assertThat(result).isEqualTo("Order LIVE-ORDER-789 processed successfully")
    }

    // Live environment tests - marked with @SkipInCI
    @Test
    fun `can register and run a workflow using Workers against live environment - live test`() = temporalTest {
        val workflowId = "live-test-queue"
        val client = configureTemporal {
            worker(taskQueue) {
                workflow<TestWorkflow> { TestWorkflowImpl() }
                workflow<ChildWorkflow> { ChildWorkflowImpl() }
                activity<OrderActivities> { OrderActivitiesImpl() }
            }
        }

        val stub = client.newWorkflowStub<TestWorkflow> {
            setTaskQueue(taskQueue)
            setWorkflowId(workflowId)
        }
        val result = stub.processOrder("LIVE-ORDER-123")
        assertThat(result).isEqualTo("Order LIVE-ORDER-123 processed successfully")
    }

    @Test
    fun `can use child workflows in live environment`() = temporalTest {
        val workflowId = "live-child-workflow-queue"
        val temporal = configureTemporal {
            worker(taskQueue) {
                workflow<TestWorkflow> { TestWorkflowImpl() }
                workflow<ChildWorkflow> { ChildWorkflowImpl() }
                activity<OrderActivities> { OrderActivitiesImpl() }
            }
        }

        val stub = temporal.newWorkflowStub<TestWorkflow> {
            setTaskQueue(taskQueue)
            setWorkflowId(workflowId)
        }
        val result = stub.processOrder("LIVE-CHILD-ORDER-999")

        // Verify the result indicates child workflows were processed
        assertThat(result).isEqualTo("Order LIVE-CHILD-ORDER-999 processed successfully")
    }

    @Test
    fun `can handle workflow validation failures in live environment`() = temporalTest {
        val workflowId = "live-failure-test-queue"
        val temporal = configureTemporal {
            worker(taskQueue) {
                workflow<TestWorkflow> { TestWorkflowImpl() }
                workflow<ChildWorkflow> { ChildWorkflowImpl() }
                activity<OrderActivities> { OrderActivitiesImpl() }
            }
        }

        val stub = temporal.newWorkflowStub<TestWorkflow> {
            setTaskQueue(taskQueue)
            setWorkflowId(workflowId)
        }

        // Test with invalid order ID (too short)
        val result = stub.processOrder("123")
        assertThat(result).isEqualTo("Order validation failed")
    }

    @Test
    fun `can verify search attributes are set on child workflow`() = temporalTest {
        val workflowId = "child-search-attributes-test-queue"
        val temporal = configureTemporal {
            worker(taskQueue) {
                workflow<TestWorkflow> { TestWorkflowImpl() }
                workflow<ChildWorkflow> { ChildWorkflowImpl() }
                activity<OrderActivities> { OrderActivitiesImpl() }
            }
        }

        val stub = temporal.newWorkflowStub<TestWorkflow> {
            setTaskQueue(taskQueue)
            setWorkflowId(workflowId)
        }
        val result = stub.processOrder("SEARCH-ATTR-ORDER-123")

        assertThat(result).isEqualTo("Order SEARCH-ATTR-ORDER-123 processed successfully")
    }

    @Test
    fun `can verify child workflow search attributes are properly set`() = temporalTest {
        val workflowId = "child-search-attributes-verify-queue"
        val temporal = configureTemporal {
            worker(taskQueue) {
                workflow<TestWorkflow> { TestWorkflowImpl() }
                workflow<ChildWorkflow> { ChildWorkflowImpl() }
                activity<OrderActivities> { OrderActivitiesImpl() }
            }
        }

        val stub = temporal.newWorkflowStub<TestWorkflow> {
            setTaskQueue(taskQueue)
            setWorkflowId(workflowId)
        }
        val result = stub.processOrder("VERIFY-ATTR-ORDER-456")

        assertThat(result).isEqualTo("Order VERIFY-ATTR-ORDER-456 processed successfully")
    }
}
