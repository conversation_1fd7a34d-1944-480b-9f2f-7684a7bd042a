package com.nextchaptersoftware.review.config

import com.nextchaptersoftware.config.ConfigLoader
import kotlinx.serialization.Serializable

@Serializable
data class CodeReviewConfig(
    val access: CodeReviewAccess,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<CodeReviewConfig>(scope = "code-review")
    }
}

@Serializable
data class CodeReviewAccess(
    val allowAllAuthors: <AUTHORS>
    val allowedAuthors: <AUTHORS>
)
