package com.nextchaptersoftware.review.config

import com.nextchaptersoftware.db.stores.MemberIdentityPair

/**
 * Service responsible for controlling code review access.
 *
 * This service encapsulates the business logic for determining whether a code review
 * should be processed based on access controls.
 */
class CodeReviewAccessService(
    private val config: CodeReviewConfig = CodeReviewConfig.INSTANCE,
) {
    fun isAllowed(
        prAuthor: MemberIdentityPair,
    ): <PERSON><PERSON><PERSON> {
        return when {
            prAuthor.identity.isBot -> false
            prAuthor.member.isCurrentMember.not() -> false
            config.access.allowAllAuthors -> true
            config.access.allowedAuthors.contains(prAuthor.identity.username) -> true
            else -> false
        }
    }
}
