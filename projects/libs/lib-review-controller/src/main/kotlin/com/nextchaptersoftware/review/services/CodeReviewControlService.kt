package com.nextchaptersoftware.review.services

import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.stores.Stores.memberStore
import com.nextchaptersoftware.db.stores.Stores.orgBillingStore
import com.nextchaptersoftware.db.stores.Stores.orgMemberStore
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.review.config.CodeReviewAccessService
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

/**
 * Service responsible for controlling code review eligibility and access.
 *
 * This service encapsulates the business logic for determining whether a code review
 * should be processed based on various conditions like org settings, repo enablement,
 * member permissions, and access controls.
 */
class CodeReviewControlService(
    private val capabilityValidation: CapabilityValidation,
    private val codeReviewAccessService: CodeReviewAccessService = CodeReviewAccessService(),
    private val codeReviewRepoControlService: CodeReviewRepoControlService = CodeReviewRepoControlService(),
) {

    /**
     * Determines if a code review should be processed for the given pull request.
     */
    suspend fun shouldProcessCodeReview(
        orgId: OrgId,
        scmTeamId: ScmTeamId,
        repoId: RepoId,
        prId: PullRequestId,
    ): Boolean {
        if (!orgSettingsStore.getEnableCodeReview(orgId)) {
            return false
        }

        if (!capabilityValidation.allowsCapability(orgId, PlanCapabilityType.CodeReview)) {
            return false
        }

        if (orgBillingStore.isTrialExpired(orgId = orgId)) {
            return false
        }

        val scmTeam = scmTeamStore.findById(teamId = scmTeamId)
            ?: return false

        val repo = repoStore.findById(repoId = repoId)
            ?: return false

        if (!codeReviewRepoControlService.isRepoEnabled(orgId = orgId, scmInstallationId = scmTeam.installationId, repo = repo)) {
            LOGGER.debugAsync { "skip: repo not enabled" }
            return false
        }

        val pr = pullRequestStore.findById(prId = prId)
            ?: return false

        if (pr.state != PullRequestState.Open) {
            LOGGER.debugAsync { "skip: pr is not open" }
            return false
        }

        if (pr.isDraft == true) {
            LOGGER.debugAsync { "skip: pr is draft" }
            return false
        }

        val prAuthor = memberStore.findAllMemberIdentityPairs(listOf(pr.creatorId))?.firstOrNull()
            ?: return false

        val orgMember = orgMemberStore.findById(id = prAuthor.member.orgMemberId)
            ?: return false

        if (!orgMember.enableCodeReview) {
            LOGGER.debugAsync { "skip: orgMember does not have code review enabled" }
            return false
        }

        if (!codeReviewAccessService.isAllowed(prAuthor = prAuthor)) {
            LOGGER.debugAsync("prAuthorId" to prAuthor.identity.id) { "skip: access denied" }
            return false
        }

        return true
    }
}
