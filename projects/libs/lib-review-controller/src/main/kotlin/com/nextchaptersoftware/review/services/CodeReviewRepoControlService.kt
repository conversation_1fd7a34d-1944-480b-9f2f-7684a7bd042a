package com.nextchaptersoftware.review.services

import com.nextchaptersoftware.auditlog.AuditService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.CodeReviewScm
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.types.RepoSelectionMode
import com.nextchaptersoftware.db.stores.AggregateCodeReviewRepo
import com.nextchaptersoftware.db.stores.CodeReviewRepoStore
import com.nextchaptersoftware.db.stores.CodeReviewScmStore
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.installationStore

/**
 * Responsible for managing code review repo enablement.
 *
 * This is non-trivial because repo enablement is derived from CodeReviewScm, CodeReviewRepo, and Repo models.
 */
class CodeReviewRepoControlService(
    private val codeReviewRepoStore: CodeReviewRepoStore = Stores.codeReviewRepoStore,
    private val codeReviewScmStore: CodeReviewScmStore = Stores.codeReviewScmStore,
    private val repoStore: RepoStore = Stores.repoStore,
) {

    suspend fun isRepoEnabled(
        orgId: OrgId,
        scmInstallationId: InstallationId,
        repo: Repo,
    ): Boolean {
        if (!repo.isActive) return false

        val codeReviewScm = codeReviewScmStore.getOrCreateScm(
            orgId = orgId,
            scmInstallationId = scmInstallationId,
        )

        return when (codeReviewScm.mode) {
            RepoSelectionMode.All -> true

            RepoSelectionMode.None -> false

            RepoSelectionMode.Selected -> codeReviewRepoStore.exists(
                orgId = orgId,
                scmInstallationId = scmInstallationId,
                repoId = repo.id,
            )
        }
    }

    suspend fun getScmRepos(
        orgId: OrgId,
        scmInstallationId: InstallationId,
    ): Pair<CodeReviewScm, List<AggregateCodeReviewRepo>> {
        return suspendedTransaction {
            val codeReviewScm = codeReviewScmStore.getOrCreateScm(
                trx = this,
                orgId = orgId,
                scmInstallationId = scmInstallationId,
            )
            val ciScmRepos = codeReviewRepoStore.getRepos(
                trx = this,
                orgId = orgId,
                scmInstallationId = scmInstallationId,
            ).let {
                when (codeReviewScm.mode) {
                    RepoSelectionMode.All -> {
                        it.map { repo -> repo.copy(isEnabled = true) }
                    }

                    RepoSelectionMode.Selected -> it

                    RepoSelectionMode.None -> {
                        check(it.none { repo -> repo.isEnabled }) { "No repos should be enabled in mode 'none'" }
                        it
                    }
                }
            }
            codeReviewScm to ciScmRepos
        }
    }

    suspend fun updateScmRepos(
        orgId: OrgId,
        scmInstallationId: InstallationId,
        updatedMode: RepoSelectionMode,
        updatedRepoIds: Set<RepoId>?,
        auditor: AuditService.CodeReview,
    ) {
        val scmInstallation = checkNotNull(installationStore.findById(installationId = scmInstallationId))

        suspendedTransaction {
            val previousMode = codeReviewScmStore.getRepoSelectionMode(
                trx = this,
                orgId = orgId,
                scmInstallationId = scmInstallationId,
            )
            if (previousMode != updatedMode) {
                auditor.updatedCodeReviewRepoSelectionMode(
                    trx = this,
                    scmInstallation = scmInstallation,
                    updatedMode = updatedMode,
                ) {
                    codeReviewScmStore.upsertScm(
                        trx = this,
                        orgId = orgId,
                        scmInstallationId = scmInstallationId,
                        mode = updatedMode,
                    )
                }
            }
            when (updatedMode) {
                RepoSelectionMode.None,
                RepoSelectionMode.All,
                    -> {
                    // No need to audit here, this is covered by the mode change audit
                    codeReviewRepoStore.removeScmRepos(
                        trx = this,
                        orgId = orgId,
                        scmInstallationId = scmInstallationId,
                    )
                }

                RepoSelectionMode.Selected,
                    -> {
                    val (added, removed) = codeReviewRepoStore.expectRepos(
                        trx = this,
                        orgId = orgId,
                        scmInstallationId = scmInstallationId,
                        expectedRepos = checkNotNull(updatedRepoIds),
                    )

                    val addedWithNames = repoStore.findIdAndExternalNamesByIds(
                        trx = this,
                        installationId = scmInstallationId,
                        repoIds = added,
                    )
                    val removedWithNames = repoStore.findIdAndExternalNamesByIds(
                        trx = this,
                        installationId = scmInstallationId,
                        repoIds = removed,
                    )

                    auditor.addedCodeReviewRepos(trx = this, scmInstallation = scmInstallation, repos = addedWithNames)
                    auditor.removedCodeReviewRepos(trx = this, scmInstallation = scmInstallation, repos = removedWithNames)
                }
            }
        }
    }
}
