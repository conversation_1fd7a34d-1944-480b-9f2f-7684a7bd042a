package com.nextchaptersoftware.review.services

import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgBilling
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePlan
import com.nextchaptersoftware.db.ModelBuilders.makePullRequest
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.stores.Stores.orgBillingStore
import com.nextchaptersoftware.db.stores.Stores.orgMemberStore
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.review.config.CodeReviewAccessService
import com.nextchaptersoftware.test.mokito.MockitoExtensions.eqValue
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlin.time.Instant
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.update
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever

class CodeReviewControlServiceTest : DatabaseTestsBase() {

    private val capabilityValidation = mock<CapabilityValidation>()
    private val codeReviewAccessService = mock<CodeReviewAccessService>()
    private val codeReviewRepoControlService = mock<CodeReviewRepoControlService>()

    private val service = CodeReviewControlService(
        capabilityValidation = capabilityValidation,
        codeReviewAccessService = codeReviewAccessService,
        codeReviewRepoControlService = codeReviewRepoControlService,
    )

    @Test
    fun `shouldProcessCodeReview is true when all conditions met`() = suspendingDatabaseTest {
        setupAndExpect(expected = true) { _, _, _, _, _, _ ->
            // No-op
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when org does not have code review enabled`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { orgId, _, _, _, _, _ ->
            orgSettingsStore.upsert(orgId = orgId, enableCodeReview = false)
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when org does not have code review capability`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { orgId, _, _, _, _, _ ->
            whenever(capabilityValidation.allowsCapability(orgId, PlanCapabilityType.CodeReview)).thenReturn(false)
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when org trial is expired`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { orgId, _, _, _, _, _ ->
            val trialPlan = makePlan(isTrialPlan = true)
            orgBillingStore.setTrialEnd(orgId = orgId, trialEnd = Instant.nowWithMicrosecondPrecision().minus(30.days))
            orgBillingStore.setPlan(orgId, planId = trialPlan.idValue, rate = null, seats = null)
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when scm team is not active`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { _, scmTeamId, _, _, _, _ ->
            scmTeamStore.markTeamForDeletion(teamId = scmTeamId)
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when repo is not active`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { _, scmTeamId, _, repoId, _, _ ->
            repoStore.deselectRepos(teamId = scmTeamId, repoIds = listOf(repoId))
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when repo is not enabled for code review`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { orgId, scmTeamId, scmInstallationId, repoId, _, _ ->
            whenever(
                codeReviewRepoControlService.isRepoEnabled(
                    orgId = eqValue(orgId),
                    scmInstallationId = eqValue(scmInstallationId),
                    repo = argThat { id == repoId },
                ),
            ).thenReturn(false)
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when pr is merged, closed or draft`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { _, _, _, _, prId, _ ->
            suspendedTransaction {
                PullRequestModel.update({ PullRequestModel.id eq prId }) {
                    it[state] = PullRequestState.Merged
                    it[mergedAt] = Instant.nowWithMicrosecondPrecision()
                    it[mergeCommitSha] = "1234567890abcdef"
                }
            }
        }
        setupAndExpect(expected = false) { _, _, _, _, prId, _ ->
            suspendedTransaction {
                PullRequestModel.update({ PullRequestModel.id eq prId }) {
                    it[state] = PullRequestState.Closed
                }
            }
        }
        setupAndExpect(expected = false) { _, _, _, _, prId, _ ->
            suspendedTransaction {
                PullRequestModel.update({ PullRequestModel.id eq prId }) {
                    it[isDraft] = true
                }
            }
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when pr author is not allowed`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { _, _, _, _, _, _ ->
            whenever(codeReviewAccessService.isAllowed(any())).thenReturn(false)
        }
    }

    @Test
    fun `shouldProcessCodeReview is false when org member does not have code review enabled`() = suspendingDatabaseTest {
        setupAndExpect(expected = false) { orgId, _, _, _, _, orgMemberId ->
            orgMemberStore.updatePreference(orgId = orgId, orgMemberId = orgMemberId, enableCodeReview = false)
        }
    }

    private suspend fun setupAndExpect(
        expected: Boolean,
        block: suspend (
            orgId: OrgId,
            scmTeamId: ScmTeamId,
            scmInstallationId: InstallationId,
            repoId: RepoId,
            prId: PullRequestId,
            orgMemberId: OrgMemberId,
        ) -> Unit,
    ) {
        val org = makeOrg()
        makeOrgBilling(org = org, trialEnd = Instant.nowWithMicrosecondPrecision().plus(30.days))
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val orgMember = makeOrgMember(org = org)
        val member = makeMember(orgMember = orgMember)
        val pr = makePullRequest(repo = repo, creator = member, creatorOrgMember = orgMember, state = PullRequestState.Open, isDraft = false)

        orgSettingsStore.upsert(orgId = org.idValue, enableCodeReview = true)
        whenever(capabilityValidation.allowsCapability(org.idValue, PlanCapabilityType.CodeReview)).thenReturn(true)
        whenever(codeReviewAccessService.isAllowed(any())).thenReturn(true)
        whenever(
            codeReviewRepoControlService.isRepoEnabled(
                orgId = eqValue(org.idValue),
                scmInstallationId = eqValue(scmTeam.installationId),
                repo = eq(repo.asDataModel()),
            ),
        ).thenReturn(true)

        block(org.idValue, scmTeam.idValue, scmTeam.installationId, repo.idValue, pr.idValue, orgMember.idValue)

        assertThat(
            service.shouldProcessCodeReview(orgId = org.idValue, scmTeamId = scmTeam.idValue, repoId = repo.idValue, prId = pr.idValue),
        ).isEqualTo(expected)
    }
}
