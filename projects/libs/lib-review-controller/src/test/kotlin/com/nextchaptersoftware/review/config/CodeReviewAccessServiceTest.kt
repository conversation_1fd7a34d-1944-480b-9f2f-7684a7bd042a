package com.nextchaptersoftware.review.config

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.stores.MemberIdentityPair
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CodeReviewAccessServiceTest {

    @Test
    fun `isAllowed returns false when identity is a bot`() {
        val service = CodeReviewAccessService(
            CodeReviewConfig(
                access = CodeReviewAccess(
                    allowAllAuthors = true,
                    allowedAuthors = setOf("user1", "user2"),
                ),
            ),
        )

        val memberIdentityPair = MemberIdentityPair(
            member = MockDataClasses.member(),
            identity = MockDataClasses.identity(username = "bot-user", isBot = true),
        )

        assertThat(service.isAllowed(memberIdentityPair)).isFalse()
    }

    @Test
    fun `isAllowed returns false when member is not a current member`() {
        val service = CodeReviewAccessService(
            CodeReviewConfig(
                access = CodeReviewAccess(
                    allowAllAuthors = true,
                    allowedAuthors = setOf(),
                ),
            ),
        )

        val memberIdentityPair = MemberIdentityPair(
            member = MockDataClasses.member(isCurrentMember = false),
            identity = MockDataClasses.identity(username = "human-user", isBot = false),
        )

        assertThat(service.isAllowed(memberIdentityPair)).isFalse()
    }

    @Test
    fun `isAllowed returns true when allowAllAuthors is true and identity is not a bot`() {
        val service = CodeReviewAccessService(
            CodeReviewConfig(
                access = CodeReviewAccess(
                    allowAllAuthors = true,
                    allowedAuthors = setOf(),
                ),
            ),
        )

        val memberIdentityPair = MemberIdentityPair(
            member = MockDataClasses.member(),
            identity = MockDataClasses.identity(username = "human-user", isBot = false),
        )

        assertThat(service.isAllowed(memberIdentityPair)).isTrue()
    }

    @Test
    fun `isAllowed returns true when username is in allowedAuthors list`() {
        val service = CodeReviewAccessService(
            CodeReviewConfig(
                access = CodeReviewAccess(
                    allowAllAuthors = false,
                    allowedAuthors = setOf("allowed-user", "another-user"),
                ),
            ),
        )

        val memberIdentityPair = MemberIdentityPair(
            member = MockDataClasses.member(),
            identity = MockDataClasses.identity(username = "allowed-user", isBot = false),
        )

        assertThat(service.isAllowed(memberIdentityPair)).isTrue()
    }

    @Test
    fun `isAllowed returns false when username is not in allowedAuthors list and allowAllAuthors is false`() {
        val service = CodeReviewAccessService(
            CodeReviewConfig(
                access = CodeReviewAccess(
                    allowAllAuthors = false,
                    allowedAuthors = setOf("allowed-user", "another-user"),
                ),
            ),
        )

        val memberIdentityPair = MemberIdentityPair(
            member = MockDataClasses.member(),
            identity = MockDataClasses.identity(username = "not-allowed-user", isBot = false),
        )

        assertThat(service.isAllowed(memberIdentityPair)).isFalse()
    }

    @Test
    fun `isAllowed returns false for bot even when username is in allowedAuthors list`() {
        val service = CodeReviewAccessService(
            CodeReviewConfig(
                access = CodeReviewAccess(
                    allowAllAuthors = false,
                    allowedAuthors = setOf("bot-user", "human-user"),
                ),
            ),
        )

        val memberIdentityPair = MemberIdentityPair(
            member = MockDataClasses.member(),
            identity = MockDataClasses.identity(username = "bot-user", isBot = true),
        )

        assertThat(service.isAllowed(memberIdentityPair)).isFalse()
    }
}
