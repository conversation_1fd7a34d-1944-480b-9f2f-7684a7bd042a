package com.nextchaptersoftware.review.services

import com.nextchaptersoftware.auditlog.AuditService
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.types.RepoSelectionMode
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CodeReviewRepoControlServiceTest : DatabaseTestsBase() {

    private val service = CodeReviewRepoControlService(
        codeReviewRepoStore = Stores.codeReviewRepoStore,
        codeReviewScmStore = Stores.codeReviewScmStore,
        repoStore = Stores.repoStore,
    )

    @Test
    fun `isRepoEnabled returns false when repo is not active`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val inactiveRepo = makeRepo(scmTeam = scmTeam, isScmConnected = false)

        val result = service.isRepoEnabled(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            repo = inactiveRepo.asDataModel(),
        )

        assertThat(result).isFalse()
    }

    @Test
    fun `isRepoEnabled returns true when mode is All and repo is active`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        // Set mode to All
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.All,
            updatedRepoIds = null,
            auditor = auditService,
        )

        val result = service.isRepoEnabled(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            repo = repo1.asDataModel(),
        )

        assertThat(result).isTrue()
    }

    @Test
    fun `isRepoEnabled returns false when mode is None`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")

        // Mode defaults to None for new SCM installations
        val result = service.isRepoEnabled(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            repo = repo1.asDataModel(),
        )

        assertThat(result).isFalse()
    }

    @Test
    fun `isRepoEnabled returns true when mode is Selected and repo is selected`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        // Set mode to Selected with repo1
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo1.idValue),
            auditor = auditService,
        )

        val result = service.isRepoEnabled(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            repo = repo1.asDataModel(),
        )

        assertThat(result).isTrue()
    }

    @Test
    fun `isRepoEnabled returns false when mode is Selected and repo is not selected`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val repo2 = makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        // Set mode to Selected with repo1 only
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo1.idValue),
            auditor = auditService,
        )

        val result = service.isRepoEnabled(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            repo = repo2.asDataModel(),
        )

        assertThat(result).isFalse()
    }

    @Test
    fun `getScmRepos returns correct data for None mode`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val repo2 = makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")

        val (scm, repos) = service.getScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
        )

        assertThat(scm.mode).isEqualTo(RepoSelectionMode.None)
        assertThat(scm.scmInstallation).isEqualTo(scmInstallation.idValue)
        assertThat(repos).hasSize(2)
        assertThat(repos.all { !it.isEnabled }).isTrue()
        assertThat(repos.map { it.id }).containsExactlyInAnyOrder(repo1.idValue, repo2.idValue)
    }

    @Test
    fun `getScmRepos returns all repos enabled for All mode`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.All,
            updatedRepoIds = null,
            auditor = auditService,
        )

        val (scm, repos) = service.getScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
        )

        assertThat(scm.mode).isEqualTo(RepoSelectionMode.All)
        assertThat(repos).hasSize(2)
        assertThat(repos.all { it.isEnabled }).isTrue()
    }

    @Test
    fun `getScmRepos returns only selected repos enabled for Selected mode`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val repo2 = makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo1.idValue),
            auditor = auditService,
        )

        val (scm, repos) = service.getScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
        )

        assertThat(scm.mode).isEqualTo(RepoSelectionMode.Selected)
        assertThat(repos).hasSize(2)

        val enabledRepos = repos.filter { it.isEnabled }
        val disabledRepos = repos.filter { !it.isEnabled }

        assertThat(enabledRepos).hasSize(1)
        assertThat(enabledRepos.first().id).isEqualTo(repo1.idValue)
        assertThat(disabledRepos).hasSize(1)
        assertThat(disabledRepos.first().id).isEqualTo(repo2.idValue)
    }

    @Test
    fun `updateScmRepos changes mode from None to Selected`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val repo2 = makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        // Initially None mode
        val (initialScm, _) = service.getScmRepos(org.idValue, scmInstallation.idValue)
        assertThat(initialScm.mode).isEqualTo(RepoSelectionMode.None)

        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo1.idValue, repo2.idValue),
            auditor = auditService,
        )

        val (updatedScm, repos) = service.getScmRepos(org.idValue, scmInstallation.idValue)
        assertThat(updatedScm.mode).isEqualTo(RepoSelectionMode.Selected)
        assertThat(repos.filter { it.isEnabled }).hasSize(2)
    }

    @Test
    fun `updateScmRepos changes mode from Selected to All and removes repo selections`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        // First set to Selected mode
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo1.idValue),
            auditor = auditService,
        )

        // Verify repo1 is selected
        val exists = Stores.codeReviewRepoStore.exists(org.idValue, scmInstallation.idValue, repo1.idValue)
        assertThat(exists).isTrue()

        // Change to All mode
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.All,
            updatedRepoIds = null,
            auditor = auditService,
        )

        val (updatedScm, repos) = service.getScmRepos(org.idValue, scmInstallation.idValue)
        assertThat(updatedScm.mode).isEqualTo(RepoSelectionMode.All)
        assertThat(repos.all { it.isEnabled }).isTrue()

        // Verify repo selections were removed
        val stillExists = Stores.codeReviewRepoStore.exists(org.idValue, scmInstallation.idValue, repo1.idValue)
        assertThat(stillExists).isFalse()
    }

    @Test
    fun `updateScmRepos adds and removes repos in Selected mode`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val scmTeam = makeScmTeam(org = org, installation = scmInstallation)
        val repo1 = makeRepo(scmTeam = scmTeam, externalName = "repo1", externalOwner = "owner1")
        val repo2 = makeRepo(scmTeam = scmTeam, externalName = "repo2", externalOwner = "owner2")
        val auditService = AuditService.forPerson(orgId = org.idValue, personId = PersonId.random()).CodeReview()

        // Start with repo1 selected
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo1.idValue),
            auditor = auditService,
        )

        // Change to repo2 selected
        service.updateScmRepos(
            orgId = org.idValue,
            scmInstallationId = scmInstallation.idValue,
            updatedMode = RepoSelectionMode.Selected,
            updatedRepoIds = setOf(repo2.idValue),
            auditor = auditService,
        )

        val (_, repos) = service.getScmRepos(org.idValue, scmInstallation.idValue)
        val enabledRepos = repos.filter { it.isEnabled }

        assertThat(enabledRepos).hasSize(1)
        assertThat(enabledRepos.first().id).isEqualTo(repo2.idValue)

        // Verify repo1 was removed and repo2 was added
        assertThat(Stores.codeReviewRepoStore.exists(org.idValue, scmInstallation.idValue, repo1.idValue)).isFalse()
        assertThat(Stores.codeReviewRepoStore.exists(org.idValue, scmInstallation.idValue, repo2.idValue)).isTrue()
    }
}
