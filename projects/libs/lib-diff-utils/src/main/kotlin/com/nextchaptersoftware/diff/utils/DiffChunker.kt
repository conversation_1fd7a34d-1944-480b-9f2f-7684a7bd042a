package com.nextchaptersoftware.diff.utils

import com.nextchaptersoftware.diff.DiffParsers
import com.nextchaptersoftware.diff.lite.LiteDiff
import com.nextchaptersoftware.diff.lite.LiteFile
import com.nextchaptersoftware.diff.lite.LiteHunk
import com.nextchaptersoftware.utils.kilobytes

/**
 * Utility for chunking large diffs into manageable pieces for processing.
 * Extracted from BuildTriageService to be shared across modules.
 */
class DiffChunker(
    private val maxHunkSize: Int = 3.kilobytes,
) {

    /**
     * Splits a diff into chunks first by files, and if files are too long, then chunk files too.
     * Then annotates the chunks with their IDs.
     *
     * @return ChunkDiffResult containing chunks and annotated diff, or null if diff cannot be parsed
     */
    fun chunkDiff(rawDiff: String): ChunkDiff? {
        val diff = DiffParsers.lite(rawDiff)
        if (diff.files.isEmpty()) {
            return null
        }

        val files = processDiff(diff)

        return ChunkDiff(
            files = files,
        )
    }

    private fun processDiff(
        diff: LiteDiff,
    ): List<ChunkFile> {
        return diff.files.mapIndexed { k, file ->
            val id = "D-$k".let(::ChunkId)
            when {
                file.isDeletion() -> processFileEmpty(file)

                file.size > maxHunkSize -> processFileLarge(
                    id,
                    file,
                )

                else -> processFileSmall(
                    id,
                    file,
                )
            }
        }
    }

    /**
     * Process a file diff that is too large and needs to be split into multiple chunks
     */
    private fun processFileLarge(
        id: ChunkId,
        file: LiteFile,
    ): ChunkFile {
        var remaining: MutableList<LiteHunk> = ArrayList(file.hunks)
        var i = 0

        val groups = mutableListOf<ChunkGroup>()

        while (remaining.isNotEmpty()) {
            // select hunks that fit into a chunk
            val index = findSplitPoint(remaining)
            val selected = List(index + 1) {
                remaining.removeFirst()
            }

            val group = ChunkGroup(
                id = id.createChild(i),
                hunks = selected.map { hunk ->
                    ChunkHunk(
                        head = hunk.info(),
                        body = hunk.body,
                    )
                },
            )

            groups.add(group)
            i += 1
        }

        return ChunkFile(
            head = file.head,
            groups = groups,
        )
    }

     private fun findSplitPoint(hunks: List<LiteHunk>): Int {
        var total = 0
        hunks.forEachIndexed { index, hunk ->
            total += hunk.size
            if (total >= maxHunkSize || index == hunks.size - 1) {
                return index
            }
        }
        return hunks.size
    }

    /**
     * Process a file diff that fits into a single chunk
     */
    private fun processFileSmall(
        id: ChunkId,
        file: LiteFile,
    ): ChunkFile {
        val group = ChunkGroup(
            id = id.createChild(0),
            hunks = file.hunks.map { hunk ->
                ChunkHunk(
                    head = hunk.info(),
                    body = hunk.body,
                )
            },
        )
        return ChunkFile(
            head = file.head,
            groups = group.let(::listOf),
        )
    }

    private fun processFileEmpty(
        file: LiteFile,
    ): ChunkFile {
        return ChunkFile(
            head = file.head,
            groups = emptyList(),
        )
    }
}

/**
 * Result of chunking a diff
 */
data class ChunkDiff(
    val files: List<ChunkFile>,
) {
    fun asString() = buildString {
        files.forEachIndexed { k, file ->
            if (k > 0) appendLine()
            append(file.asString())
        }
    }
}

data class ChunkFile(
    val head: String,
    val groups: List<ChunkGroup>,
) {
    fun asString() = buildString {
        append(head)
        groups.forEach { group ->
            appendLine()
            append(group.asString())
        }
    }
}

data class ChunkGroup(
    val id: ChunkId,
    val hunks: List<ChunkHunk>,
) {
    inline val size get() = hunks.sumOf { it.size }

    internal fun asString() = buildString {
        append(id.marker)
        hunks.forEach { hunk ->
            appendLine()
            append(hunk.asString())
        }
    }
}

data class ChunkHunk(
    val head: String,
    val body: String,
) {
    inline val size get() = head.length + body.length

    internal fun asString() = buildString {
        appendLine(head)
        append(body)
    }
}

data class ChunkId(val value: String) {

    override fun toString() = value

    inline val marker get() = "<!-- CHUNK_ID: $value -->"

    fun hunkIndex() = value
        .substringAfter('.', "0")
        .toIntOrNull()
        ?: 0
}

private fun ChunkId.createChild(child: Int) = "$value.$child".let(::ChunkId)
