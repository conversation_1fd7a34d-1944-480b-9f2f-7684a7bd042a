package com.nextchaptersoftware.diff.utils

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class DiffChunkerTest {
    private val diffChunker = DiffChunker()

    @Test
    fun `should return null for empty diff`() {
        val result = diffChunker.chunkDiff("")
        assertThat(result).isNull()
    }

    @Test
    fun `should return null for invalid diff`() {
        val result = diffChunker.chunkDiff("not a valid diff")
        assertThat(result).isNull()
    }

    @Test
    fun `should handle small diff as single chunk`() {
        val smallDiff = """
            |diff --git a/test.txt b/test.txt
            |index 1234567..abcdefg 100644
            |--- a/test.txt
            |+++ b/test.txt
            |@@ -1,3 +1,3 @@
            | line 1
            |-old line
            |+new line
            | line 3
        """.trimMargin()

        val result = diffChunker.chunkDiff(smallDiff)
        assertThat(result).isEqualTo(
            ChunkDiff(
                files = listOf(
                    ChunkFile(
                        head = """
                            |diff --git a/test.txt b/test.txt
                            |index 1234567..abcdefg 100644
                            |--- a/test.txt
                            |+++ b/test.txt
                        """.trimMargin(),
                        groups = listOf(
                            ChunkGroup(
                                id = "D-0.0".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -1,3 +1,3 @@",
                                        body = """
                                            | line 1
                                            |-old line
                                            |+new line
                                            | line 3
                                        """.trimMargin(),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        assertThat(result?.asString()).isEqualTo(
            """
                |diff --git a/test.txt b/test.txt
                |index 1234567..abcdefg 100644
                |--- a/test.txt
                |+++ b/test.txt
                |${createChunkIdMarker("D-0.0")}
                |@@ -1,3 +1,3 @@
                | line 1
                |-old line
                |+new line
                | line 3
            """.trimMargin(),
        )
    }

    @Test
    fun `should discard deletions diff chunks`() {
        val smallDiff = """
            |diff --git a/test.txt b/test.txt
            |deleted file mode 100644
            |index 997475775b8c..000000000000
            |--- a/test.txt
            |+++ /dev/null
            |@@ -1,3 +0,0 @@
            |-gone 1
            |-gone 2
            |-gone 3
        """.trimMargin()

        val result = diffChunker.chunkDiff(smallDiff)
        assertThat(result).isEqualTo(
            ChunkDiff(
                files = listOf(
                    ChunkFile(
                        head = """
                            |diff --git a/test.txt b/test.txt
                            |deleted file mode 100644
                            |index 997475775b8c..000000000000
                            |--- a/test.txt
                            |+++ /dev/null
                        """.trimMargin(),
                        groups = emptyList(),
                    ),
                ),
            ),
        )

        assertThat(result?.asString()).isEqualTo(
            """
                |diff --git a/test.txt b/test.txt
                |deleted file mode 100644
                |index 997475775b8c..000000000000
                |--- a/test.txt
                |+++ /dev/null
            """.trimMargin(),
        )
    }

    @Test
    fun `should handle multiple files`() {
        val file1 = """
            |diff --git a/file1.txt b/file1.txt
            |index 1234567..abcdefg 100644
            |--- a/file1.txt
            |+++ b/file1.txt
            |@@ -1,2 +1,2 @@
            |-old content 1
            |+new content 1
            """.trimMargin()
        val file2 = """
            |diff --git a/file2.txt b/file2.txt
            |index 2345678..bcdefgh 100644
            |--- a/file2.txt
            |+++ b/file2.txt
            |@@ -1,2 +1,2 @@
            |-old content 2
            |+new content 2
        """.trimMargin()

        val multiFileDiff = createDiff(
            file1,
            file2,
        )

        val result = diffChunker.chunkDiff(multiFileDiff)

        assertThat(result).isEqualTo(
            ChunkDiff(
                files = listOf(
                    ChunkFile(
                        head = """
                            |diff --git a/file1.txt b/file1.txt
                            |index 1234567..abcdefg 100644
                            |--- a/file1.txt
                            |+++ b/file1.txt
                        """.trimMargin(),
                        groups = listOf(
                            ChunkGroup(
                                id = "D-0.0".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -1,2 +1,2 @@",
                                        body = """
                                            |-old content 1
                                            |+new content 1
                                        """.trimMargin(),
                                    ),
                                ),
                            ),
                        ),
                    ),
                    ChunkFile(
                        head = """
                            |diff --git a/file2.txt b/file2.txt
                            |index 2345678..bcdefgh 100644
                            |--- a/file2.txt
                            |+++ b/file2.txt
                        """.trimMargin(),
                        groups = listOf(
                            ChunkGroup(
                                id = "D-1.0".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -1,2 +1,2 @@",
                                        body = """
                                            |-old content 2
                                            |+new content 2
                                        """.trimMargin(),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        assertThat(result?.asString()).isEqualTo(
            """
                |diff --git a/file1.txt b/file1.txt
                |index 1234567..abcdefg 100644
                |--- a/file1.txt
                |+++ b/file1.txt
                |${createChunkIdMarker("D-0.0")}
                |@@ -1,2 +1,2 @@
                |-old content 1
                |+new content 1
                |diff --git a/file2.txt b/file2.txt
                |index 2345678..bcdefgh 100644
                |--- a/file2.txt
                |+++ b/file2.txt
                |${createChunkIdMarker("D-1.0")}
                |@@ -1,2 +1,2 @@
                |-old content 2
                |+new content 2
            """.trimMargin(),
        )
    }

    @Test
    fun `should chunk large file diff`() {
        val largeContent = "x".repeat(4000) // Larger than DEFAULT_MAX_HUNK_SIZE (3000)
        val largeDiff = """
            |diff --git a/large.txt b/large.txt
            |index 1234567..abcdefg 100644
            |--- a/large.txt
            |+++ b/large.txt
            |@@ -1,1 +1,1 @@
            |+$largeContent
        """.trimMargin()

        val result = diffChunker.chunkDiff(largeDiff)

        assertThat(result).isEqualTo(
            ChunkDiff(
                files = listOf(
                    ChunkFile(
                        head = """
                            |diff --git a/large.txt b/large.txt
                            |index 1234567..abcdefg 100644
                            |--- a/large.txt
                            |+++ b/large.txt
                            """.trimMargin(),
                        groups = listOf(
                            ChunkGroup(
                                id = "D-0.0".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -1,1 +1,1 @@",
                                        body = """
                                            |+$largeContent
                                        """.trimMargin(),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        assertThat(result?.asString()).isEqualTo(
            """
                |diff --git a/large.txt b/large.txt
                |index 1234567..abcdefg 100644
                |--- a/large.txt
                |+++ b/large.txt
                |${createChunkIdMarker("D-0.0")}
                |@@ -1,1 +1,1 @@
                |+$largeContent
        """.trimMargin(),
        )
    }

    @Test
    fun `should use custom max hunk size`() {
        val customChunker = DiffChunker(maxHunkSize = 50)
        val content = createLines(5) {
            "x".repeat(5)
        }

        val diff = """
            |diff --git a/test.txt b/test.txt
            |index 1234567..abcdefg 100644
            |--- a/test.txt
            |+++ b/test.txt
            |@@ -10,10 +10,10 @@
            |$content
            |@@ -20,10 +20,10 @@
            |$content
            |@@ -30,10 +30,10 @@
            |$content
        """.trimMargin()

        val result = customChunker.chunkDiff(diff)

        assertThat(result).isEqualTo(
            ChunkDiff(
                files = listOf(
                    ChunkFile(
                        head = """
                            |diff --git a/test.txt b/test.txt
                            |index 1234567..abcdefg 100644
                            |--- a/test.txt
                            |+++ b/test.txt
                        """.trimMargin(),
                        groups = listOf(
                            ChunkGroup(
                                id = "D-0.0".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -10,10 +10,10 @@",
                                        body = content,
                                    ),
                                ),
                            ),
                            ChunkGroup(
                                id = "D-0.1".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -20,10 +20,10 @@",
                                        body = content,
                                    ),
                                ),
                            ),
                            ChunkGroup(
                                id = "D-0.2".let(::ChunkId),
                                hunks = listOf(
                                    ChunkHunk(
                                        head = "@@ -30,10 +30,10 @@",
                                        body = content,
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )

        assertThat(result?.asString()).isEqualTo(
            """
                |diff --git a/test.txt b/test.txt
                |index 1234567..abcdefg 100644
                |--- a/test.txt
                |+++ b/test.txt
                |${createChunkIdMarker("D-0.0")}
                |@@ -10,10 +10,10 @@
                |$content
                |${createChunkIdMarker("D-0.1")}
                |@@ -20,10 +20,10 @@
                |$content
                |${createChunkIdMarker("D-0.2")}
                |@@ -30,10 +30,10 @@
                |$content
            """.trimMargin(),
        )
    }

    @Test
    fun `should handle empty diffs`() {
        val diff = """
            |diff --git a/scripts/docker-ecr-login.sh b/pkg/model/scripts/docker-ecr-login.sh
            |similarity index 100%
            |rename from scripts/docker-ecr-login.sh
            |rename to pkg/model/scripts/docker-ecr-login.sh
        """.trimMargin()

        val result = diffChunker.chunkDiff(diff)

        assertThat(result).isEqualTo(
            ChunkDiff(
                files = listOf(
                    ChunkFile(
                        head = """
                            |diff --git a/scripts/docker-ecr-login.sh b/pkg/model/scripts/docker-ecr-login.sh
                            |similarity index 100%
                            |rename from scripts/docker-ecr-login.sh
                            |rename to pkg/model/scripts/docker-ecr-login.sh
                        """.trimMargin(),
                        groups = listOf(
                            ChunkGroup(
                                id = "D-0.0".let(::ChunkId),
                                hunks = emptyList(),
                            ),
                        ),
                    ),
                ),

            ),
        )

        assertThat(result?.asString()).isEqualTo(
            """
                |$diff
                |${createChunkIdMarker("D-0.0")}
            """.trimMargin(),
        )
    }
}

private fun createChunkIdMarker(id: String) = """<!-- CHUNK_ID: $id -->"""

private fun createDiff(vararg files: String) = files.joinToString("\n")

private fun createLines(lines: Int, content: () -> String): String = buildString {
    repeat(lines) { k ->
        content().also {
            if (k > 0) appendLine()
            append("+$it")
        }
    }
}
