package com.nextchaptersoftware.mongo

import com.aallam.openai.api.chat.ChatMessage
import com.aallam.openai.api.chat.ChatResponseFormat
import com.aallam.openai.api.chat.ChatRole
import com.aallam.openai.client.OpenAI
import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.client.model.Accumulators
import com.mongodb.client.model.Aggregates
import com.mongodb.client.model.Filters
import com.mongodb.client.model.Sorts
import com.mongodb.kotlin.client.coroutine.MongoClient
import com.mongodb.kotlin.client.coroutine.MongoCollection
import com.mongodb.kotlin.client.coroutine.MongoDatabase
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.openai.api.OpenAIApiConfiguration
import com.nextchaptersoftware.openai.api.OpenAICompletionsApi
import com.nextchaptersoftware.openai.api.models.OpenAITextModel
import java.time.Instant
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import org.assertj.core.api.Assertions.assertThat
import org.bson.Document
import org.bson.conversions.Bson
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assumptions
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PullRequestsQueryCoroutineTest {

    companion object {
        private lateinit var client: MongoClient
        private lateinit var db: MongoDatabase
        private lateinit var prs: MongoCollection<Document>

        @BeforeAll
        @JvmStatic
        fun connectAndSeed(): Unit = runBlocking {
            // Default to maindb with unblocked/unblocked auth; override via MONGODB_URI if needed.
            val uri = System.getenv("MONGODB_URI")
                ?: "*********************************************"

            client = MongoClient.create(
                MongoClientSettings.builder()
                    .applyConnectionString(ConnectionString(uri))
                    .build(),
            )

            try {
                client.getDatabase("admin").runCommand(Document("ping", 1))
            } catch (t: Throwable) {
                Assumptions.abort("Cannot connect to MongoDB at $uri: ${t.message}")
            }

            db = client.getDatabase("maindb")
            prs = db.getCollection("pull_requests", Document::class.java)
            prs.deleteMany(Document())

            // Load JSON from src/test/resources
            val resourceStream = this::class.java.classLoader.getResourceAsStream("github_pr_25.json")
                ?: error("Could not find github_pr_25.json in test resources")
            val text = resourceStream.bufferedReader().use { it.readText() }

            val root = Json.parseToJsonElement(text).jsonObject
            val prArray = root["pull_requests"]?.jsonArray
                ?: error("Missing 'pull_requests' array in JSON")

            val docs = prArray.map { prElem -> Document.parse(prElem.toString()) }
            if (docs.isNotEmpty()) prs.insertMany(docs)

            // Helpful indexes
            prs.createIndex(Document("state", 1))
            prs.createIndex(Document("updated_at", -1))
            prs.createIndex(Document("user.login", 1))
            prs.createIndex(Document("comments.user.login", 1))
            prs.createIndex(Document("files.filename", 1))
        }

        @AfterAll
        @JvmStatic
        fun close() = runBlocking { client.close() }
    }

    @Test
    fun `find all open PRs`() = runTest {
        val filter: Bson = Filters.eq("state", "open")
        val open = prs.find(filter).toList()
        assertThat(open).isNotEmpty
        assertThat(open).allSatisfy { assertThat(it.getString("state")).isEqualTo("open") }
    }

    @Test
    fun `PRs with more than 2 commits`() = runTest {
        val filter: Bson = Filters.expr(
            Document(mapOf("\$gt" to listOf(Document("\$size", "\$commits"), 2))),
        )
        val result = prs.find(filter).toList()
        assertThat(result).allSatisfy { pr ->
            val commitCount = (pr["commits"] as? List<*>)?.size ?: 0
            assertThat(commitCount).isGreaterThan(2)
        }
    }

    @Test
    fun `top 3 authors by PR count`() = runTest {
        val pipeline = listOf(
            Aggregates.group("\$user.login", Accumulators.sum("count", 1)),
            Aggregates.sort(Sorts.descending("count")),
            Aggregates.limit(3),
        )
        val top = prs.aggregate(pipeline).toList()
        assertThat(top).isNotEmpty
        assertThat(top).allSatisfy { doc -> assertThat(doc).containsKeys("_id", "count") }
    }

    @Test
    fun `PRs with comment by reviewer1`() = runTest {
        val inner: Bson = Filters.eq("user.login", "reviewer1") // disambiguates overload
        val filter: Bson = Filters.elemMatch("comments", inner)
        val result = prs.find(filter).toList()
        assertThat(result).isNotEmpty
    }

    @Test
    fun `latest updated PR`() = runTest {
        val latest = prs.find()
            .sort(Sorts.descending("updated_at"))
            .limit(1)
            .firstOrNull()
        assertThat(latest).isNotNull

        val max = prs.aggregate(
            listOf(Aggregates.group(null, Accumulators.max("maxUpdated", "\$updated_at"))),
        ).firstOrNull()
        assertThat(latest?.get("updated_at")).isEqualTo(max?.get("maxUpdated"))
    }

    @Test
    fun `PRs touching Python files`() = runTest {
        val filter: Bson = Filters.regex("files.filename", "\\.py$", "i")
        val docs = prs.find(filter).toList()
        assertThat(docs).isNotEmpty
    }

    @Test
    @Suppress("ktlint:nextchaptersoftware:no-nano-datetime")
    fun `PRs updated within last 7 days`() = runTest {
        val sevenDaysAgo = Instant.now().minusSeconds(7 * 24 * 3600)
        val filter: Bson = Filters.gte("updated_at", sevenDaysAgo.toString())
        val recent = prs.find(filter).toList()
        assertThat(recent).isNotNull
    }

    @Nested
    inner class OpenAIQueryGenerationTests {
        private val openaiApiConfiguration = OpenAIApiConfiguration(
            timeout = 10.seconds,
            token = GlobalConfig.INSTANCE.openAI.apiKey,
        )

        private val client = OpenAI(token = openaiApiConfiguration.token.value)
        private val openaiApi = OpenAICompletionsApi(client)

        // One generic system prompt for ALL user requests.
        @Suppress("MaxLineLength")
        private val genericSystemPrompt: String = $$"""
            You translate natural-language requests into ONE complete MongoDB COMMAND, returned as a strict JSON object.
            Rules:
            - Output ONLY a single JSON object, no code fences or commentary.
            - Use either:
                { "find": "pull_requests", "filter": {...}, "sort": {...}?, "limit": N?, "projection": {...}? }
              OR
                { "aggregate": "pull_requests", "pipeline": [ ... ], "cursor": {} }
            - Do NOT return partial filters/pipelines; return the full command object.
            - For relative time (“last N days/weeks”), use $NOW with $dateSubtract (e.g., { "$dateSubtract": { "startDate": "$NOW", "unit": "day", "amount": N } }).
            - Infer field paths from context; prefer robust operators: $regex (with "i" when appropriate), $exists, $expr, $size, $sum, $map, etc.
            - No placeholders, variables, or helper explanations.
            - Ensure the command is valid for db.runCommand().
        """

        // Schema hints as a separate system message (edit this list as your schema evolves)
        private val schemaHint: String = """
            Collection: pull_requests
            Field hints (nested paths allowed):
            - number: int
            - state: string ("open" | "closed" | "merged")
            - title: string
            - body: string
            - created_at: ISO date string
            - updated_at: ISO date string
            - user.login: string       // PR author
            - comments: array
            - comments[].user.login: string
            - comments[].body: string
            - comments[].created_at: ISO date string
            - files: array
            - files[].filename: string
            - files[].status: string ("modified" | "added" | "removed" | "renamed")
            - files[].additions: int
            - files[].deletions: int
            - commits: array
            - commits[].commit.message: string
        """.trimIndent()

        /** Helper that sends a natural-language request and returns the EXACT command as BSON Document. */
        private suspend fun commandFromNL(naturalLanguage: String): Document {
            val completion = openaiApi.chatCompletion(
                model = OpenAITextModel.GPT5,
                messages = listOf(
                    ChatMessage(role = ChatRole.System, content = genericSystemPrompt),
                    ChatMessage(role = ChatRole.System, content = schemaHint),
                    ChatMessage(role = ChatRole.User, content = naturalLanguage.trim()),
                ),
                temperature = 0.0,
                maxCompletionTokens = 5000,
                // Ask for strict JSON to minimize chances of prose.
                responseFormat = ChatResponseFormat.JsonObject,
            )
            val raw = completion.choices.first().message.content?.trim().orEmpty()
            // No extra processing: parse exactly what the LLM returned.
            return Document.parse(raw)
        }

        @Test
        fun `open PRs updated in last 14 days - generic prompt`() = runTest {
            val cmd = commandFromNL(
                """
            Show open pull requests updated in the last 14 days.
            """.trimIndent(),
            )

            val result = db.runCommand(cmd)
            assertThat(result).isNotNull

            val firstBatch = (result["cursor"] as? Document)?.get("firstBatch") as? List<*>
            if (firstBatch != null && firstBatch.isNotEmpty()) {
                assertThat(firstBatch).allSatisfy { anyDoc ->
                    val d = anyDoc as Document
                    assertThat(d.getString("state")).isEqualTo("open")
                }
            }
        }

        @Test
        fun `top 3 authors by pull request count - generic prompt`() = runTest {
            val cmd = commandFromNL(
                """
            Find the top 3 authors by number of pull requests; return documents with _id = author login and count.
            """.trimIndent(),
            )

            val result = db.runCommand(cmd)
            assertThat(result).isNotNull

            val firstBatch = (result["cursor"] as? Document)?.get("firstBatch") as? List<*>
            if (firstBatch != null && firstBatch.isNotEmpty()) {
                val first = firstBatch.first() as Document
                assertThat(first).containsKeys("_id", "count")
            }
        }

        @Test
        fun `PRs that touch Python files - generic prompt`() = runTest {
            val cmd = commandFromNL(
                """
            List pull requests that modify any .py file.
            """.trimIndent(),
            )

            val result = db.runCommand(cmd)
            assertThat(result).isNotNull

            val firstBatch = (result["cursor"] as? Document)?.get("firstBatch") as? List<*>
            // We don't enforce non-empty because dataset may vary, but we validate type.
            assertThat(firstBatch).isNotNull
        }
    }
}
