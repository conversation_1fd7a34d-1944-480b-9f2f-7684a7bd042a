package com.nextchaptersoftware.microsoft.bot.workflows

import com.nextchaptersoftware.microsoft.bot.models.HookPayload
import com.nextchaptersoftware.temporal.annotations.Queue
import com.nextchaptersoftware.temporal.annotations.RunsOn
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod

interface MicrosoftBotWorkflow {
    fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotMessageWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotConversationUpdateWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotContactRelationUpdateWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotTypingWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotEndOfConversationWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotEventWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotInvokeWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotDeleteUserDataWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotMessageUpdateWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotMessageDeleteWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotInstallationUpdateWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotMessageReactionWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotSuggestionWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotTraceWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}

@RunsOn(Queue.MSTeamsBot)
@WorkflowInterface
interface MicrosoftBotHandoffWorkflow : MicrosoftBotWorkflow {
    @WorkflowMethod
    override fun processHookEvent(webhookPayload: HookPayload)
}
