package com.nextchaptersoftware.microsoft.bot.models

import kotlinx.serialization.Serializable

@Serializable
enum class WebhookEventType(val value: String) {
    MESSAGE("message"),
    CONVERSATION_UPDATE("conversationUpdate"),
    CONTACT_RELATION_UPDATE("contactRelationUpdate"),
    TYPING("typing"),
    END_OF_CONVERSATION("endOfConversation"),
    EVENT("event"),
    INVOKE("invoke"),
    DELETE_USER_DATA("deleteUserData"),
    MESSAGE_UPDATE("messageUpdate"),
    MESSAGE_DELETE("messageDelete"),
    INSTALLATION_UPDATE("installationUpdate"),
    MESSAGE_REACTION("messageReaction"),
    SUGGESTION("suggestion"),
    TRACE("trace"),
    HANDOFF("handoff"),
    ;

    companion object {
        fun fromString(value: String): WebhookEventType? {
            return values().find { it.value == value }
        }
    }
}
