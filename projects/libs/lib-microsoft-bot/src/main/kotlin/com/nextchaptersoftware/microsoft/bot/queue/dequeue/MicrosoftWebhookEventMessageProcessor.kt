package com.nextchaptersoftware.microsoft.bot.queue.dequeue

import com.nextchaptersoftware.event.queue.dequeue.EventMessageProcessor
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.microsoft.bot.models.HookPayload
import com.nextchaptersoftware.microsoft.bot.models.WebhookEventType
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotContactRelationUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotConversationUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotDeleteUserDataWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotEndOfConversationWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotEventWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotHandoffWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotInstallationUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotInvokeWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageDeleteWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageReactionWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageUpdateWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotMessageWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotSuggestionWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotTraceWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotTypingWorkflow
import com.nextchaptersoftware.microsoft.bot.workflows.MicrosoftBotWorkflow
import com.nextchaptersoftware.serialization.Serialization.decode
import com.nextchaptersoftware.temporal.Temporal
import com.nextchaptersoftware.temporal.annotations.Queue
import com.nextchaptersoftware.temporal.kotlin.WorkflowOptionsKt.setWorkflowIdKt
import io.temporal.client.WorkflowClient
import jakarta.jms.Message
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class MicrosoftWebhookEventMessageProcessor(
    private val handler: EventHandler,
) : EventMessageProcessor {

    override suspend fun process(context: Message, body: String) {
        withLoggingContextAsync(
            "handler" to handler::class.java.simpleName,
            "messageId" to context.jmsMessageID,
            "isRedelivered" to context.jmsRedelivered,
        ) {
            handler.handle(event = body)
        }
    }
}

/**
 * Microsoft Teams webhook event handler that processes the webhook payload
 * and starts the appropriate Temporal workflows.
 */
@Suppress("CyclomaticComplexMethod")
class MicrosoftWebhookEventHandler : EventHandler {

    override suspend fun handle(event: String): Boolean {
        val webhookPayload = parseWebhookPayload(event) ?: run {
            LOGGER.errorSync { "Invalid microsoft webhook JSON payload body" }
            return true
        }

        when (webhookPayload.type) {
            WebhookEventType.MESSAGE -> startWorkflow<MicrosoftBotMessageWorkflow>(webhookPayload, "message")

            WebhookEventType.CONVERSATION_UPDATE -> startWorkflow<MicrosoftBotConversationUpdateWorkflow>(
                webhookPayload,
                "conversation-update",
            )

            WebhookEventType.CONTACT_RELATION_UPDATE -> startWorkflow<MicrosoftBotContactRelationUpdateWorkflow>(
                webhookPayload,
                "contact-relation-update",
                )

            WebhookEventType.TYPING -> startWorkflow<MicrosoftBotTypingWorkflow>(webhookPayload, "typing")

            WebhookEventType.END_OF_CONVERSATION -> startWorkflow<MicrosoftBotEndOfConversationWorkflow>(
                webhookPayload,
                "end-of-conversation",
            )

            WebhookEventType.EVENT -> startWorkflow<MicrosoftBotEventWorkflow>(webhookPayload, "event")

            WebhookEventType.INVOKE -> startWorkflow<MicrosoftBotInvokeWorkflow>(webhookPayload, "invoke")

            WebhookEventType.DELETE_USER_DATA -> startWorkflow<MicrosoftBotDeleteUserDataWorkflow>(
                webhookPayload,
                "delete-user-data",
            )

            WebhookEventType.MESSAGE_UPDATE -> startWorkflow<MicrosoftBotMessageUpdateWorkflow>(
                webhookPayload,
                "message-update",
            )

            WebhookEventType.MESSAGE_DELETE -> startWorkflow<MicrosoftBotMessageDeleteWorkflow>(
                webhookPayload,
                "message-delete",
            )

            WebhookEventType.INSTALLATION_UPDATE -> startWorkflow<MicrosoftBotInstallationUpdateWorkflow>(
                webhookPayload,
                "installation-update",
            )

            WebhookEventType.MESSAGE_REACTION -> startWorkflow<MicrosoftBotMessageReactionWorkflow>(
                webhookPayload,
                "message-reaction",
            )

            WebhookEventType.SUGGESTION -> startWorkflow<MicrosoftBotSuggestionWorkflow>(webhookPayload, "suggestion")

            WebhookEventType.TRACE -> startWorkflow<MicrosoftBotTraceWorkflow>(webhookPayload, "trace")

            WebhookEventType.HANDOFF -> startWorkflow<MicrosoftBotHandoffWorkflow>(webhookPayload, "handoff")
        }
        return true
    }

    @Suppress("TooGenericExceptionCaught")
    private fun parseWebhookPayload(body: String): HookPayload? {
        return try {
            body.decode<HookPayload>()
        } catch (e: Exception) {
            LOGGER.errorSync(e) { "Failed to parse webhook payload" }
            null
        }
    }

    private inline fun <reified T : MicrosoftBotWorkflow> startWorkflow(webhookPayload: HookPayload, workflowName: String) {
        val workflowStub = Temporal.forLive().newWorkflowStub<T> {
            setWorkflowIdKt<T>(workflowName, webhookPayload.id)
            setTaskQueue(Queue.MSTeamsBot.id)
        }
        WorkflowClient.start { workflowStub.processHookEvent(webhookPayload) }
    }
}
