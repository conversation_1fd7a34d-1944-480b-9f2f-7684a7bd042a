package com.nextchaptersoftware.microsoft.bot.queue.enqueue

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.event.queue.enqueue.EventEnqueueService
import com.nextchaptersoftware.microsoft.bot.queue.payloads.MicrosoftWebhookEvent
import com.nextchaptersoftware.serialization.Serialization.encode

class MicrosoftWebhookEventEnqueueService(
    private val eventEnqueueService: EventEnqueueService,
) {
    fun enqueueEvent(
        event: MicrosoftWebhookEvent,
        priority: MessagePriority = MessagePriority.DEFAULT,
    ) {
        eventEnqueueService.enqueueEvent(
            body = event.encode(),
            priority = priority,
        )
    }
}
