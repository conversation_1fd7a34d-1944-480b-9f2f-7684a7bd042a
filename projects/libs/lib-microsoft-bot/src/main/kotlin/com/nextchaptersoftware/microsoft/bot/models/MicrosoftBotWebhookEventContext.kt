package com.nextchaptersoftware.microsoft.bot.models

sealed class MicrosoftBotWebhookEventContext {
    data class BotMessage(
        val teamsThreadId: String?,
        val teamsMessageId: String,
        val text: String,
        val isBot: Boolean,
        val attachments: List<Attachment>?,
    ) : MicrosoftBotWebhookEventContext()

    data class ProcessorMessage(
        val teamsThreadId: String?,
        val teamsMessageId: String,
        val text: String,
        val teamsUserId: String,
        val teamsExternalTeamId: String,
        val teamsExternalChannelId: String,
    ) : MicrosoftBotWebhookEventContext()

    data class ResolvedProcessorMessage(
        val teamsThreadId: String?,
        val teamsMessageId: String,
        val text: String,
        val teamsUserId: String,
        val teamsExternalTeamId: String,
        val teamsExternalChannelId: String,
        // TODO: Add Teams team and channel DAOs when implemented
        // val teamsTeamDaos: TeamsTeamDaos,
        // val teamsChannelDaos: TeamsChannelDaos,
        // val memberInfo: MemberInfo?,
    ) : MicrosoftBotWebhookEventContext()

    data class MemberEventMessage(
        val teamsExternalTeamId: String,
        val teamsExternalChannelId: String,
        val teamsUserId: String,
        val isBotTokenEvent: Boolean,
    )

    sealed class MicrosoftBotFileEventContext : MicrosoftBotWebhookEventContext() {
        abstract val teamsExternalTeamId: String
        abstract val teamsFileId: String

        data class FileIngestMessage(
            override val teamsExternalTeamId: String,
            override val teamsFileId: String,
        ) : MicrosoftBotFileEventContext()

        data class FileDeletedMessage(
            override val teamsExternalTeamId: String,
            override val teamsFileId: String,
            val teamsChannelExternalIds: List<String>,
        ) : MicrosoftBotFileEventContext()
    }
}
