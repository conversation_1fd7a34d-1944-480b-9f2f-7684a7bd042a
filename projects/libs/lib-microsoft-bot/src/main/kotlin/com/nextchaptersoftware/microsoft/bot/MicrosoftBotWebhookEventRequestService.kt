package com.nextchaptersoftware.microsoft.bot

import com.nextchaptersoftware.ktor.UnauthorizedException
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.microsoft.bot.queue.enqueue.MicrosoftWebhookEventEnqueueService
import com.nextchaptersoftware.microsoft.bot.queue.payloads.MicrosoftWebhookEvent
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

/**
 * Microsoft Teams Bot Webhook Event Request Service
 *
 * Processes incoming webhook events from Microsoft Teams and enqueues them for processing.
 * The actual workflow processing is handled by MicrosoftWebhookEventMessageProcessor.
 */
class MicrosoftBotWebhookEventRequestService(
    private val microsoftWebhookEventEnqueueService: MicrosoftWebhookEventEnqueueService,
    private val microsoftBotEventVerifier: MicrosoftBotEventVerifier,
) {

    suspend fun process(
        body: String,
    ): String? {
        // Verify the webhook signature
        microsoftBotEventVerifier.isEventValid(
            eventBody = body,
        ).takeIf { it }?.let {
            LOGGER.errorSync { "Invalid microsoft webhook signature request" }
            throw UnauthorizedException("The request has invalid signature")
        }

        // Enqueue the webhook event for processing
        microsoftWebhookEventEnqueueService.enqueueEvent(
            event = MicrosoftWebhookEvent(body = body),
        )

        return null
    }
}
