package com.nextchaptersoftware.installation.services

import com.nextchaptersoftware.auditlog.Actor
import com.nextchaptersoftware.auditlog.AuditService
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import io.ktor.http.Url
import org.jetbrains.exposed.sql.Transaction

enum class InstallationType { DataSource, CI, }

class InstallationCreationService(
    private val installationStore: InstallationStore = Stores.installationStore,
    private val slackNotifier: SlackNotifier,
) {
    suspend fun create(
        trx: Transaction? = null,
        id: InstallationId,
        orgId: OrgId,
        provider: Provider,
        installationExternalId: String,
        displayName: String,
        htmlUrl: Url?,
        rawAccessToken: Ciphertext?,
        avatarUrl: Url? = null,
        installationType: InstallationType = InstallationType.DataSource,
        personId: PersonId,
    ): Installation {
        val auditService = AuditService.forPerson(
            orgId = orgId,
            personId = personId,
        )

        return create(
            trx = trx,
            id = id,
            orgId = orgId,
            provider = provider,
            installationExternalId = installationExternalId,
            displayName = displayName,
            htmlUrl = htmlUrl,
            rawAccessToken = rawAccessToken,
            avatarUrl = avatarUrl,
            installationType = installationType,
            auditService = auditService,
        )
    }

    suspend fun create(
        trx: Transaction? = null,
        id: InstallationId,
        orgId: OrgId,
        provider: Provider,
        installationExternalId: String,
        displayName: String,
        htmlUrl: Url?,
        rawAccessToken: Ciphertext?,
        avatarUrl: Url? = null,
        installationType: InstallationType = InstallationType.DataSource,
        auditService: AuditService,
    ): Installation {
        return suspendedTransaction(trx) {
            val installation = installationStore.insert(
                trx = this,
                id = id,
                orgId = orgId,
                installationExternalId = installationExternalId,
                provider = provider,
                htmlUrl = htmlUrl,
                avatarUrl = avatarUrl,
                rawAccessToken = rawAccessToken,
                displayName = displayName,
            )

            when (installationType) {
                InstallationType.DataSource -> {
                    auditService.dataSourceAdded(trx = this, installation = installation)
                }

                InstallationType.CI -> {
                    auditService.addedCiInstallation(trx = this, installation = installation)
                }
            }

            installation
        }.also {
            when (val actor = auditService.actor) {
                is Actor.Person -> {
                    slackNotifier.announceIntegrationAdded(installation = it, personId = actor.personId)
                }

                is Actor.ApiKey,
                is Actor.System,
                is Actor.DataSource,
                -> {
                    doNothing() // TODO create slack notification event for these actors
                }

                is Actor.SlackUser -> {
                    doNothing() // These users are associated with events from Slack and installations are not created from Slack
                }
            }
        }
    }
}
