package com.nextchaptersoftware.installation.services

import com.nextchaptersoftware.auditlog.AuditService
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores

class InstallationTokenService(
    private val installationStore: InstallationStore = Stores.installationStore,
) {
    suspend fun updateRawAccessToken(
        orgId: OrgId,
        personId: PersonId,
        installation: Installation,
        encryptedToken: Ciphertext,
    ) {
        require(installation.orgId == orgId) { "Installation does not belong to org" }

        AuditService.forPerson(orgId = orgId, personId = personId).dataSourceTokenUpdated(
            installation = installation,
        ) { trx ->
            installationStore.updateRawAccessToken(
                trx = trx,
                id = installation.id,
                orgId = orgId,
                rawAccessToken = encryptedToken,
            )
        }
    }
}
