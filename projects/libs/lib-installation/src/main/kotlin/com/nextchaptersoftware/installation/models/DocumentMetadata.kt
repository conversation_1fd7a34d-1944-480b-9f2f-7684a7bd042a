package com.nextchaptersoftware.installation.models

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.Provider
import io.ktor.http.Url
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@SerialName("DocumentMetadata")
@Serializable
data class DocumentMetadata(
    @Contextual val documentId: String,
    @Contextual val groupId: UUID?,
    @Contextual val sourceDocumentId: UUID,
    @Contextual val externalUrl: Url,
    val provider: Provider,
    val installationId: InstallationId?,
)
