package com.nextchaptersoftware.dashboard.extensions

import com.nextchaptersoftware.db.models.DataSourcePresetArtifact
import com.nextchaptersoftware.db.models.Message
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PaymentInvite
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.stores.SearchConfig
import com.nextchaptersoftware.environment.dsl.dashboardUrls
import io.ktor.http.Url

/**
 * Extension functions for domain models to generate dashboard URLs.
 * These functions are in a separate library to avoid circular dependencies.
 */

fun Thread.dashboardUrl(): Url {
    val dashboard = dashboardUrls
    return pullRequestId?.let { prId ->
        dashboard.team(orgId).pullRequest(prId).thread(id)
    } ?: dashboard.team(orgId).thread(id).toUrl()
}

fun Message.dashboardUrl(orgId: OrgId): Url {
    return dashboardUrls.team(orgId).thread(threadId).messageLegacy(id)
}

fun SearchConfig.dashboardUrl(orgId: OrgId): Url {
    // Note: Frontend doesn't have a dedicated search route, search is integrated into discussions/mine
    return dashboardUrls.team(orgId).discussions.mine(
        query = q,
        topicIds = topicIds,
        authorIds = authorIds,
        sort = sort,
    )
}

fun Org.dashboardUrl(): Url {
    return dashboardUrls.team(id).toUrl()
}

fun PullRequest.dashboardUrl(orgId: OrgId): Url {
    return dashboardUrls.team(orgId).pullRequest(id).toUrl()
}

fun DataSourcePresetArtifact.dashboardUrl(orgId: OrgId): Url =
    dashboardUrls.team(orgId).integrations.dataSourcePreset(presetId)

fun PaymentInvite.dashboardUrl(): Url {
    return dashboardUrls.paymentInvite(id.value)
}
