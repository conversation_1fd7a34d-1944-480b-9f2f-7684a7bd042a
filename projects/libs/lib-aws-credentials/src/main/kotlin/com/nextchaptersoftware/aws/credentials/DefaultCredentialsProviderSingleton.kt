@file:Suppress("ktlint:nextchaptersoftware:no-direct-defaultcredentialsprovider-constructor-rule")

package com.nextchaptersoftware.aws.credentials

import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider

/**
 * Singleton wrapper for [DefaultCredentialsProvider] to enable application-level caching
 * and prevent expensive repeated credential chain resolution.
 *
 * Even though AWS SDK v2 removed internal singleton instances in
 * [PR #6166](https://github.com/aws/aws-sdk-java-v2/pull/6166/files) to avoid
 * multiple clients closing the same instance, we still want global caching of credential resolution results.
 *
 * @see DefaultCredentialsProvider
 */
object DefaultCredentialsProviderSingleton {
    /**
     * The singleton [DefaultCredentialsProvider] instance, lazily initialized on first access.
     *
     * This prevents expensive operations like environment variable lookups, AWS profile parsing,
     * and metadata service calls from being repeated across the application.
     */
    val INSTANCE: DefaultCredentialsProvider by lazy {
        DefaultCredentialsProvider.builder().build()
    }
}
