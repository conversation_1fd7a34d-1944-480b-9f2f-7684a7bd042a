package com.nextchaptersoftware.aws.credentials

import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.Test

class DefaultCredentialsProviderSingletonTest {
    @Test
    fun `should return same instance on multiple calls`() {
        val instance1 = DefaultCredentialsProviderSingleton.INSTANCE
        val instance2 = DefaultCredentialsProviderSingleton.INSTANCE

        assertSame(instance1, instance2, "Should return the same instance")
    }

    @Test
    fun `should return valid DefaultCredentialsProvider instance`() {
        val instance = DefaultCredentialsProviderSingleton.INSTANCE

        assertNotNull(instance, "Instance should not be null")
        // Instance is guaranteed to be DefaultCredentialsProvider by the singleton implementation
    }
}
