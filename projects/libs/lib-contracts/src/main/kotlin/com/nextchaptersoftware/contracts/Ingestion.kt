package com.nextchaptersoftware.contracts

import com.nextchaptersoftware.db.models.InstallationId
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod

@WorkflowInterface
interface IngestionWorkflow {
    @WorkflowMethod
    fun process()
}

@WorkflowInterface
interface ProviderIngestionWorkflow {
    @WorkflowMethod
    fun ingest(installationId: InstallationId)
}
