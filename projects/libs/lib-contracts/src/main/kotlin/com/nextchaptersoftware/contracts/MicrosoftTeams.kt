package com.nextchaptersoftware.contracts

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MicrosoftTeamsChannel
import com.nextchaptersoftware.db.models.MicrosoftTeamsChannelId
import com.nextchaptersoftware.db.models.MicrosoftTeamsTeam
import com.nextchaptersoftware.db.models.MicrosoftTeamsTeamId
import com.nextchaptersoftware.db.models.OrgId
import io.temporal.activity.ActivityInterface
import io.temporal.activity.ActivityMethod
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod
import kotlinx.serialization.json.JsonObject

@WorkflowInterface
interface MSTeamsTeamsIngestionWorkflow {
    @WorkflowMethod
    fun ingest(installationId: InstallationId, ingestChannels: Boolean = true, ingestMessages: Boolean = false)
}

@WorkflowInterface
interface MSTeamsOnboardWorkflow {
    @WorkflowMethod
    fun onboard(installationId: InstallationId)
}

@WorkflowInterface
interface MSTeamsChannelIngestionWorkflow {
    @WorkflowMethod
    fun ingest(installationId: InstallationId, teamExternalId: String, ingestMessages: Boolean = false)
}

@WorkflowInterface
interface MSTeamsUserIngestionWorkflow {
    @WorkflowMethod
    fun ingest(installationId: InstallationId)
}

@WorkflowInterface
interface MSTeamsMessageIngestWorkflow {
    @WorkflowMethod
    fun ingest(installationId: InstallationId, teamExternalId: String, channelExternalId: String, replyToExternalId: String? = null)
}

data class TeamsIngestionContext(
    val installationId: InstallationId,
)

@ActivityInterface
interface MSTeamsTeamFetchUpsertActivity {
    @ActivityMethod
    fun fetchTeamPage(context: TeamsIngestionContext, pageState: PageState?): PageResult

    @ActivityMethod
    fun processTeamPage(context: TeamsIngestionContext, items: List<JsonObject>): List<String>
}

data class ChannelIngestionContext(
    val installationId: InstallationId,
    val teamExternalId: String,
)

@ActivityInterface
interface MSTeamsChannelFetchUpsertActivity {
    @ActivityMethod
    fun fetchChannelPage(context: ChannelIngestionContext, pageState: PageState?): PageResult

    @ActivityMethod
    fun processChannelPage(context: ChannelIngestionContext, items: List<JsonObject>): List<String>
}

@ActivityInterface
interface MSTeamsUserFetchUpsertActivity {
    @ActivityMethod
    fun fetchUserPage(context: TeamsIngestionContext, pageState: PageState?): PageResult

    @ActivityMethod
    fun processUserPage(context: TeamsIngestionContext, items: List<JsonObject>): List<IdentityId>
}

data class MessageIngestionContext(
    val orgId: OrgId,
    val installationId: InstallationId,
    val teamExternalId: String,
    val channelId: MicrosoftTeamsChannelId,
    val channelExternalId: String,
    val repliesToMessageId: String? = null,
)

@ActivityInterface
interface MSTeamsMessageFetchActivity {
    @ActivityMethod
    fun fetchMessagePage(context: MessageIngestionContext, pageState: PageState?): PageResult

    @ActivityMethod
    fun processMessagePage(context: MessageIngestionContext, items: List<JsonObject>)
}

@ActivityInterface
interface MSTeamsDataActivity {
    // Functions that were in workflows and need to be moved to activities
    @ActivityMethod
    fun getTeamsForInstallation(installationId: InstallationId): List<MicrosoftTeamsTeam>

    @ActivityMethod
    fun getChannelsForTeam(teamId: MicrosoftTeamsTeamId): List<MicrosoftTeamsChannel>

    @ActivityMethod
    fun getChannelByExternalId(channelExternalId: String): MicrosoftTeamsChannel

    @ActivityMethod
    fun getOrgIdForInstallation(installationId: InstallationId): OrgId
}

@ActivityInterface
interface MSTeamsChannelsIngestion {
    @ActivityMethod
    fun ingestTeamChannels(msTeamsTeamId: MicrosoftTeamsTeamId)
}
