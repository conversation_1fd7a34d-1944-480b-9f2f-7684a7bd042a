package com.nextchaptersoftware.review.reporting

import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.db.models.types.CodeReviewScope
import com.nextchaptersoftware.review.reviewers.ReviewComment
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.types.Hash
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CodeReviewReporterTest {

    private val reporter = CodeReviewReporter(skipReportOverview = false)

    @Test
    fun `generateReport includes review, debug, footer and encodes backticks`() {
        val commit = Hash.parse("0123456789abcdef0123456789abcdef01234567")
        val fileDiffs = listOf(
            ScmPullRequestFile(
                newFilePath = "src/Foo.kt",
                diff = "diff",
                commitSha = commit.asString(),
            ),
        )

        val trigger = CodeReviewEventType.PullRequestOpened
        val scope = CodeReviewScope.Full

        val (report, _) = reporter.generateReport(commit, scope, trigger, emptyList(), fileDiffs)

        // Review section basics
        assertThat(report).contains("## Review")

        // Debug info and footer
        assertThat(report).contains("Triggered a full review when pull request was opened on commit 0123456.")
        assertThat(report).contains("Reviewed by Unblocked.")
    }

    @Test
    fun `generateReport counts comments in debug table`() {
        val commit = Hash.parse("89abcdef0123456789abcdef0123456789abcdef")
        val fileDiffs = listOf(
            ScmPullRequestFile(
                newFilePath = "src/Foo.kt",
                diff = "diff",
                commitSha = commit.asString(),
            ),
        )
        val reviewComments = listOf(
            ReviewComment(
                filePath = "src/Foo.kt",
                content = ReviewComment.CommentContent(
                    title = "Test title",
                    description = "Test description",
                ),
                category = ReviewComment.Category.CRASH_OR_HANG,
                reasoning = "Test reasoning",
                severity = ReviewComment.Severity.MUST_FIX,
            ),
            ReviewComment(
                filePath = "src/Foo.kt",
                content = ReviewComment.CommentContent(
                    title = "Test title2",
                    description = "Test description2",
                ),
                category = ReviewComment.Category.BEST_PRACTICE,
                reasoning = "Test reasoning2",
                severity = ReviewComment.Severity.SHOULD_FIX,
            ),
        )

        val (report, _) = reporter.generateReport(
            commit = commit,
            scope = CodeReviewScope.Full,
            trigger = CodeReviewEventType.PullRequestCodeChanged,
            fileDiffs = fileDiffs,
            reviewComments = reviewComments,
        )

        assertThat(report).contains("2 comments")
        assertThat(report).contains("Foo.kt")
    }

    @Test
    fun `generateReport does not include report overview for incremental review`() {
        val commit = Hash.parse("89abcdef0123456789abcdef0123456789abcdef")
        val fileDiffs = listOf(
            ScmPullRequestFile(
                newFilePath = "src/Foo.kt",
                diff = "diff",
                commitSha = commit.asString(),
            ),
        )

        val reviewComments = listOf(
            ReviewComment(
                filePath = "src/Foo.kt",
                content = ReviewComment.CommentContent(
                    title = "Test title",
                    description = "Test description",
                ),
                category = ReviewComment.Category.LOGIC_ERROR,
                reasoning = "Test reasoning",
                severity = ReviewComment.Severity.MUST_FIX,
            ),
        )

        val (report, scmReviewComments) = reporter.generateReport(
            commit = commit,
            scope = CodeReviewScope.Incremental,
            trigger = CodeReviewEventType.PullRequestCodeChanged,
            fileDiffs = fileDiffs,
            reviewComments = reviewComments,
        )

        assertThat(report).isNull()
        assertThat(scmReviewComments).hasSize(1)
    }
}
