package com.nextchaptersoftware.review.lifecycle

import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePullRequest
import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.db.models.types.CodeReviewScope
import com.nextchaptersoftware.db.models.types.CodeReviewStatus
import com.nextchaptersoftware.db.stores.Stores.codeReviewStore
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.KotlinUtils.required
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CodeReviewManagerTest : DatabaseTestsBase() {

    private val codeReviewManager = CodeReviewManager()

    @Test
    fun `createReview creates full review when no published full review exists`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val commit = Hash.parse("abc123")
        val trigger = CodeReviewEventType.PullRequestOpened

        val result = codeReviewManager.createReview(orgId, pr.id, commit, trigger).required()
        assertThat(result.pr).isEqualTo(pr.id)
        assertThat(result.commit).isEqualTo(commit)
        assertThat(result.trigger).isEqualTo(trigger)
        assertThat(result.scope).isEqualTo(CodeReviewScope.Full)
        assertThat(result.status).isEqualTo(CodeReviewStatus.NotStarted)
        assertThat(result.publishedReviewId).isNull()
        assertThat(result.publishedReviewUrl).isNull()
    }

    @Test
    fun `createReview creates incremental review when published full review exists`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val firstCommit = Hash.parse("abc123")
        val secondCommit = Hash.parse("def456")
        val trigger = CodeReviewEventType.PullRequestCodeChanged

        val fullReview = codeReviewManager.createReview(orgId, pr.id, firstCommit, CodeReviewEventType.PullRequestOpened).required()
        codeReviewStore.publishReview(
            codeReviewId = fullReview.id,
            publishedReviewId = "review-123",
            publishedReviewUrl = io.ktor.http.Url("https://github.com/repo/pull/123#pullrequestreview-456"),
        )

        val result = codeReviewManager.createReview(orgId, pr.id, secondCommit, trigger).required()
        assertThat(result.pr).isEqualTo(pr.id)
        assertThat(result.commit).isEqualTo(secondCommit)
        assertThat(result.trigger).isEqualTo(trigger)
        assertThat(result.scope).isEqualTo(CodeReviewScope.Incremental)
        assertThat(result.status).isEqualTo(CodeReviewStatus.NotStarted)
        assertThat(result.publishedReviewId).isNull()
        assertThat(result.publishedReviewUrl).isNull()
    }

    @Test
    fun `createReview does not create incremental review when incremental is disabled`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val firstCommit = Hash.parse("abc123")
        val secondCommit = Hash.parse("def456")
        val trigger = CodeReviewEventType.PullRequestCodeChanged

        orgSettingsStore.upsert(orgId = orgId, codeReviewIncremental = false)

        val fullReview = codeReviewManager.createReview(orgId, pr.id, firstCommit, CodeReviewEventType.PullRequestOpened).required()
        codeReviewStore.publishReview(
            codeReviewId = fullReview.id,
            publishedReviewId = "review-123",
            publishedReviewUrl = io.ktor.http.Url("https://github.com/repo/pull/123#pullrequestreview-456"),
        )

        val result = codeReviewManager.createReview(orgId, pr.id, secondCommit, trigger)
        assertThat(result).isNull()
    }

    @Test
    fun `createReview cancels existing reviews before creating new one`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val firstCommit = Hash.parse("abc123")
        val secondCommit = Hash.parse("def456")
        val trigger = CodeReviewEventType.PullRequestReadyForReview

        val existingReview = codeReviewManager.createReview(orgId, pr.id, firstCommit, CodeReviewEventType.PullRequestOpened).required()

        val result = codeReviewManager.createReview(orgId, pr.id, secondCommit, trigger).required()
        assertThat(result.pr).isEqualTo(pr.id)
        assertThat(result.commit).isEqualTo(secondCommit)
        assertThat(result.trigger).isEqualTo(trigger)
        assertThat(result.scope).isEqualTo(CodeReviewScope.Full)

        val cancelledReview = codeReviewStore.findById(existingReview.id)
        assertThat(cancelledReview?.status).isEqualTo(CodeReviewStatus.Cancelled)
    }

    @Test
    fun `createReview returns null when review already exists for same commit`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val commit = Hash.parse("abc123")
        val trigger = CodeReviewEventType.PullRequestOpened

        // Create first review for the commit
        val firstReview = codeReviewManager.createReview(orgId, pr.id, commit, trigger).required()
        assertThat(firstReview.commit).isEqualTo(commit)

        // Attempt to create second review for the same commit should return null
        val secondReview = codeReviewManager.createReview(orgId, pr.id, commit, CodeReviewEventType.PullRequestCodeChanged)
        assertThat(secondReview).isNull()

        // Verify the original review still exists and is unchanged
        val existingReview = codeReviewStore.findById(firstReview.id)
        assertThat(existingReview).isNotNull
        assertThat(existingReview?.status).isEqualTo(CodeReviewStatus.NotStarted)
        assertThat(existingReview?.trigger).isEqualTo(trigger)
    }

    @Test
    fun `assertIsActive does not throw when review is active`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val commit = Hash.parse("abc123")
        val review = codeReviewManager.createReview(orgId, pr.id, commit, CodeReviewEventType.PullRequestOpened).required()

        codeReviewManager.assertIsActive(review.id)
    }

    @Test
    fun `assertIsActive throws CodeReviewCompletedException when review is not active`() = suspendingDatabaseTest {
        val orgId = makeOrg().asDataModel().id
        val pr = makePullRequest(isDraft = false).asDataModel()
        val commit = Hash.parse("abc123")
        val review = codeReviewManager.createReview(orgId, pr.id, commit, CodeReviewEventType.PullRequestOpened).required()

        codeReviewStore.doNotPublishReview(review.id)

        assertThrows<CodeReviewCompletedException> {
            codeReviewManager.assertIsActive(review.id)
        }.also {
            assertThat(it.message).isEqualTo("Code review has already completed")
        }
    }
}
