package com.nextchaptersoftware.review.reporting

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class FilePathTest {

    @Test
    fun `file path must be relative`() {
        assertThrows<IllegalArgumentException> {
            FilePath("/absolute/path/to/file.txt")
        }.also {
            assertThat(it.message).contains("Path must not start with a slash")
        }
    }

    @Test
    fun `file in root directory works correctly`() {
        val result = FilePath("README.md")

        assertThat(result.filePath).isEqualTo("README.md")
        assertThat(result.fileName).isEqualTo("README.md")
        assertThat(result.fileExtension).isEqualTo("md")
        assertThat(result.directory).isEqualTo("")
    }

    @Test
    fun `file without extension works correctly`() {
        val result = FilePath("scripts/build")

        assertThat(result.filePath).isEqualTo("scripts/build")
        assertThat(result.fileName).isEqualTo("build")
        assertThat(result.fileExtension).isNull()
        assertThat(result.directory).isEqualTo("scripts")
    }

    @Test
    fun `file with multiple dots in name works correctly`() {
        val result = FilePath("config/app.config.json")

        assertThat(result.filePath).isEqualTo("config/app.config.json")
        assertThat(result.fileName).isEqualTo("app.config.json")
        assertThat(result.fileExtension).isEqualTo("json")
        assertThat(result.directory).isEqualTo("config")
    }

    @Test
    fun `blank file path is rejected`() {
        assertThrows<IllegalArgumentException> {
            FilePath("")
        }.also {
            assertThat(it.message).contains("File path must not be blank")
        }
    }

    @Test
    fun `nested directory paths work correctly`() {
        val result = FilePath("src/main/kotlin/com/example/MyClass.kt")

        assertThat(result.filePath).isEqualTo("src/main/kotlin/com/example/MyClass.kt")
        assertThat(result.fileName).isEqualTo("MyClass.kt")
        assertThat(result.fileExtension).isEqualTo("kt")
        assertThat(result.directory).isEqualTo("src/main/kotlin/com/example")
    }

    @Test
    fun `file with dot at beginning works correctly`() {
        val result = FilePath("config/.gitignore")

        assertThat(result.filePath).isEqualTo("config/.gitignore")
        assertThat(result.fileName).isEqualTo(".gitignore")
        assertThat(result.fileExtension).isNull()
        assertThat(result.directory).isEqualTo("config")
    }

    @Test
    fun `file with only dot as extension works correctly`() {
        val result = FilePath("temp/file.")

        assertThat(result.filePath).isEqualTo("temp/file.")
        assertThat(result.fileName).isEqualTo("file.")
        assertThat(result.fileExtension).isNull()
        assertThat(result.directory).isEqualTo("temp")
    }
}
