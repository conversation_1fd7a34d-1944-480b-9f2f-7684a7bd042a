package com.nextchaptersoftware.review.context

import com.nextchaptersoftware.db.ModelBuilders.makeCodeReviewExclusion
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePullRequest
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.types.Hash
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class CodeReviewDiffContextServiceTest : DatabaseTestsBase() {
    private val codeReviewSourceProvider = mock<CodeReviewSourceProvider>()
    private val service = CodeReviewDiffContextService(codeReviewSourceProvider)

    @Test
    fun `getDiffContext returns all files when no exclusions exist`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "src/test/kotlin/ServiceTest.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), any())).thenReturn("")

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "src/test/kotlin/ServiceTest.kt",
        )
    }

    @Test
    fun `getDiffContext filters out files matching exclusion patterns`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        // Create exclusion patterns
        makeCodeReviewExclusion(
            repo = repo,
            excludedPaths = listOf("*.md", "src/test/**"),
        )

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "README.md",
                oldFilePath = "README.md",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "src/test/kotlin/ServiceTest.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "docs/guide.md",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), any())).thenReturn("")

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Only the Service.kt file should remain (not matching any exclusion patterns)
        assertThat(result).hasSize(1)
        assertThat(result[0].newFilePath).isEqualTo("src/main/kotlin/Service.kt")
    }

    @Test
    fun `getDiffContext filters files with null or empty diff`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/EmptyDiff.kt",
                oldFilePath = "src/main/kotlin/EmptyDiff.kt",
                diff = "",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/NullDiff.kt",
                oldFilePath = "src/main/kotlin/NullDiff.kt",
                diff = null,
                commitSha = "abc123",
            ),
        )

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), any())).thenReturn("")

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Only files with non-empty diff should be included
        assertThat(result).hasSize(1)
        assertThat(result[0].newFilePath).isEqualTo("src/main/kotlin/Service.kt")
    }

    @Test
    fun `getDiffContext handles files with only oldFilePath`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        // Create exclusion pattern that matches old file path
        makeCodeReviewExclusion(
            repo = repo,
            excludedPaths = listOf("old/**"),
        )

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "new/Service.kt",
                oldFilePath = "old/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "another/File.kt",
                oldFilePath = "old/File.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), any())).thenReturn("")

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Both files should be excluded because their oldFilePath matches the pattern
        assertThat(result).isEmpty()
    }

    @Test
    fun `getDiffContext returns empty list when no files have diff content`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = null,
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Another.kt",
                oldFilePath = "src/main/kotlin/Another.kt",
                diff = "",
                commitSha = "abc123",
            ),
        )

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), any())).thenReturn("")

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        assertThat(result).isEmpty()
    }

    @Test
    fun `getDiffContext filters files marked as linguist-generated in gitattributes`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/api/Client.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "assets/app.min.js",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Generated files
            generated/** linguist-generated
            *.min.js linguist-generated
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Only Service.kt should remain (not marked as generated)
        assertThat(result).hasSize(1)
        assertThat(result[0].newFilePath).isEqualTo("src/main/kotlin/Service.kt")
    }

    @Test
    fun `getDiffContext filters files marked as linguist-vendored in gitattributes`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "vendor/jquery.js",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "third-party/bootstrap.css",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Vendored dependencies
            vendor/** linguist-vendored
            third-party/** linguist-vendored
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Only Service.kt should remain (not marked as vendored)
        assertThat(result).hasSize(1)
        assertThat(result[0].newFilePath).isEqualTo("src/main/kotlin/Service.kt")
    }

    @Test
    fun `getDiffContext handles mixed gitattributes with both generated and vendored files`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/Models.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "vendor/library.js",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "docs/README.md",
                oldFilePath = "docs/README.md",
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Generated files
            generated/** linguist-generated
            # Vendored dependencies
            vendor/** linguist-vendored
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Only Service.kt and README.md should remain
        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "docs/README.md",
        )
    }

    @Test
    fun `getDiffContext handles gitattributes with explicit true and false values`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/Models.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "special/NotGenerated.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Generated files with explicit values
            generated/** linguist-generated=true
            special/** linguist-generated=false
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Service.kt and NotGenerated.kt should remain (NotGenerated.kt has linguist-generated=false)
        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "special/NotGenerated.kt",
        )
    }

    @Test
    fun `getDiffContext handles gitattributes with negated attributes`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/Models.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/special/NotGenerated.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Mark all generated files as generated
            generated/** linguist-generated
            # But exclude special subdirectory
            generated/special/** !linguist-generated
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Service.kt and NotGenerated.kt should remain (NotGenerated.kt has !linguist-generated)
        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "generated/special/NotGenerated.kt",
        )
    }

    @Test
    fun `getDiffContext handles empty gitattributes file`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/Models.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Just comments

            # No actual attributes
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // All files should remain since no attributes are defined
        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "generated/Models.kt",
        )
    }

    @Test
    fun `getDiffContext handles missing gitattributes file`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/Models.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(null) // No .gitattributes file exists

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // All files should remain since no .gitattributes file exists
        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "generated/Models.kt",
        )
    }

    @Test
    fun `getDiffContext applies both exclusion patterns and gitattributes filtering`() = suspendingDatabaseTest {
        val org = makeOrg()
        val scmTeam = makeScmTeam(org = org)
        val repo = makeRepo(scmTeam = scmTeam)
        val pr = makePullRequest(repo = repo)

        // Create exclusion patterns
        makeCodeReviewExclusion(
            repo = repo,
            excludedPaths = listOf("*.md"),
        )

        val files = listOf(
            ScmPullRequestFile(
                newFilePath = "src/main/kotlin/Service.kt",
                oldFilePath = "src/main/kotlin/Service.kt",
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "README.md", // Excluded by pattern
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "generated/Models.kt", // Excluded by gitattributes
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "docs/guide.md", // Excluded by pattern
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "vendor/library.js", // Excluded by gitattributes
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
            ScmPullRequestFile(
                newFilePath = "src/test/kotlin/ServiceTest.kt",
                oldFilePath = null,
                diff = "diff content",
                commitSha = "abc123",
            ),
        )

        val gitattributesContent = """
            # Generated files
            generated/** linguist-generated
            # Vendored dependencies
            vendor/** linguist-vendored
        """.trimIndent()

        whenever(codeReviewSourceProvider.getPrDiff(any(), any(), any(), any())).thenReturn(files)
        whenever(codeReviewSourceProvider.getFileContent(any(), any(), any(), any(), eq(".gitattributes")))
            .thenReturn(gitattributesContent)

        val (_, result) = service.getDiffContext(
            scmTeam = scmTeam.asDataModel(),
            repo = repo.asDataModel(),
            pr = pr.asDataModel(),
            scm = Scm.GitHub,
            commit = Hash.parse("abc123"),
        )

        // Only Service.kt and ServiceTest.kt should remain
        assertThat(result).hasSize(2)
        assertThat(result.map { it.newFilePath }).containsExactlyInAnyOrder(
            "src/main/kotlin/Service.kt",
            "src/test/kotlin/ServiceTest.kt",
        )
    }
}
