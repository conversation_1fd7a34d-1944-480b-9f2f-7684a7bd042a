package com.nextchaptersoftware.review.reviewers

import com.nextchaptersoftware.xml.SerializationExtensions.decodeXml
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ReviewCommentTest {

    @Test
    fun `decode single review comment`() {
        val xml = """
<reviewComments>
    <comment>
        <filePath>projects/models/src/main/kotlin/com/ncs/db/stores/CIRepoStore.kt</filePath>
        <lineRange>
            <side>right</side>
            <startLine>264</startLine>
            <endLine>275</endLine>
        </lineRange>
        <content>
            <title>Incorrect WHERE clause in selective mode query</title>
            <description>The query includes InstallationModel.id it not joined in this query.</description>
        </content>
        <category>CRASH_OR_HANG</category>
        <reasoning>References a table that is not joined in the query</reasoning>
        <severity>MUST_FIX</severity>
    </comment>
</reviewComments>
""".trimIndent()

        val actual = xml.decodeXml<ReviewComments>()
        assertThat(actual).isEqualTo(
            ReviewComments(
                comments = listOf(
                    ReviewComment(
                        filePath = "projects/models/src/main/kotlin/com/ncs/db/stores/CIRepoStore.kt",
                        lineRange = ReviewComment.LineRange(
                            side = ReviewComment.LineRange.Side.RIGHT,
                            startLine = 264,
                            endLine = 275,
                        ),
                        content = ReviewComment.CommentContent(
                            title = "Incorrect WHERE clause in selective mode query",
                            description = "The query includes InstallationModel.id it not joined in this query.",
                        ),
                        category = ReviewComment.Category.CRASH_OR_HANG,
                        reasoning = "References a table that is not joined in the query",
                        severity = ReviewComment.Severity.MUST_FIX,
                    ),
                ),
            ),
        )
    }

    @Test
    fun `decode missing endLine`() {
        val xml = """
<reviewComments>
    <comment>
        <filePath>projects/models/src/main/kotlin/com/ncs/db/stores/CIRepoStore.kt</filePath>
        <lineRange>
            <side>right</side>
            <startLine>264</startLine>
        </lineRange>
        <content>
            <title>title</title>
            <description>description</description>
        </content>
        <category>CRASH_OR_HANG</category>
        <reasoning>reasoning</reasoning>
        <severity>MUST_FIX</severity>
    </comment>
</reviewComments>
""".trimIndent()

        val actual = xml.decodeXml<ReviewComments>()
        assertThat(actual).isEqualTo(
            ReviewComments(
                comments = listOf(
                    ReviewComment(
                        filePath = "projects/models/src/main/kotlin/com/ncs/db/stores/CIRepoStore.kt",
                        lineRange = ReviewComment.LineRange(
                            side = ReviewComment.LineRange.Side.RIGHT,
                            startLine = 264,
                            endLine = 264,
                        ),
                        content = ReviewComment.CommentContent(
                            title = "title",
                            description = "description",
                        ),
                        category = ReviewComment.Category.CRASH_OR_HANG,
                        reasoning = "reasoning",
                        severity = ReviewComment.Severity.MUST_FIX,
                    ),
                ),
            ),
        )
    }

    @Test
    fun `decode single review comment with no line range`() {
        val xml = """
<reviewComments>
    <comment>
        <filePath>projects/models/src/main/kotlin/com/ncs/db/stores/CIRepoStore.kt</filePath>
        <content>
            <title>Incorrect WHERE clause in selective mode query</title>
            <description>The query includes InstallationModel.id it not joined in this query.</description>
        </content>
        <category>LOGIC_ERROR</category>
        <reasoning>References a table that is not joined in the query</reasoning>
        <severity>MUST_FIX</severity>
    </comment>
</reviewComments>
""".trimIndent()

        val actual = xml.decodeXml<ReviewComments>()
        assertThat(actual).isEqualTo(
            ReviewComments(
                comments = listOf(
                    ReviewComment(
                        filePath = "projects/models/src/main/kotlin/com/ncs/db/stores/CIRepoStore.kt",
                        content = ReviewComment.CommentContent(
                            title = "Incorrect WHERE clause in selective mode query",
                            description = "The query includes InstallationModel.id it not joined in this query.",
                        ),
                        category = ReviewComment.Category.LOGIC_ERROR,
                        reasoning = "References a table that is not joined in the query",
                        severity = ReviewComment.Severity.MUST_FIX,
                    ),
                ),
            ),
        )
    }

    @Test
    fun `decode multiple review comments`() {
        val xml = """
<reviewComments>
  <comment>
    <filePath>projects/models/src/test/kotlin/com/ncs/db/stores/RepoStoreTest.kt</filePath>
    <lineRange>
      <side>left</side>
      <startLine>1148</startLine>
      <endLine>1158</endLine>
    </lineRange>
    <content>
      <title>Test removal may leave functionality untested</title>
      <description>
        <![CDATA[
The `count CI repos` test is being removed as part of replacing `isCiEnabled` with `CIRepoModel`. Ensure that equivalent test coverage exists for the new `CIRepoModel` functionality, particularly for the `countCIRepos` method which appears to return a pair of counts.
        ]]>
      </description>
    </content>
    <category>TESTABILITY</category>
    <reasoning>Removing tests without replacement can reduce test coverage for critical functionality</reasoning>
    <severity>SHOULD_FIX</severity>
  </comment>
  <comment>
    <filePath>projects/services/adminwebservice/src/main/kotlin/com/ncs/adminwebservice/adminweb/page/RepoPage.kt</filePath>
    <lineRange>
      <side>right</side>
      <startLine>536</startLine>
      <endLine>536</endLine>
    </lineRange>
    <content>
      <title>Function name inconsistency</title>
      <description>
        <![CDATA[
The function name was changed from `togglebulkingestFlag` to `toggleBulkIngestFlag` (camelCase correction), but this appears to be an unrelated change to the PR's stated purpose of removing `isCiEnabled`. This naming fix should be in a separate commit or PR to maintain clear change history.
        ]]>
      </description>
    </content>
    <category>MAINTAINABILITY</category>
    <reasoning>Unrelated refactoring mixed with feature removal makes change history unclear</reasoning>
    <severity>FOLLOW_UP</severity>
  </comment>
</reviewComments>
""".trimIndent()

        val actual = xml.decodeXml<ReviewComments>()
        assertThat(actual.comments).hasSize(2)
        assertThat(actual).isEqualTo(
            ReviewComments(
                comments = listOf(
                    ReviewComment(
                        filePath = "projects/models/src/test/kotlin/com/ncs/db/stores/RepoStoreTest.kt",
                        lineRange = ReviewComment.LineRange(
                            side = ReviewComment.LineRange.Side.LEFT,
                            startLine = 1148,
                            endLine = 1158,
                        ),
                        content = ReviewComment.CommentContent(
                            title = "Test removal may leave functionality untested",
                            description = actual.comments[0].content.description,
                        ),
                        category = ReviewComment.Category.TESTABILITY,
                        reasoning = "Removing tests without replacement can reduce test coverage for critical functionality",
                        severity = ReviewComment.Severity.SHOULD_FIX,
                    ),
                    ReviewComment(
                        filePath = "projects/services/adminwebservice/src/main/kotlin/com/ncs/adminwebservice/adminweb/page/RepoPage.kt",
                        lineRange = ReviewComment.LineRange(
                            side = ReviewComment.LineRange.Side.RIGHT,
                            startLine = 536,
                            endLine = 536,
                        ),
                        content = ReviewComment.CommentContent(
                            title = "Function name inconsistency",
                            description = actual.comments[1].content.description,
                        ),
                        category = ReviewComment.Category.MAINTAINABILITY,
                        reasoning = "Unrelated refactoring mixed with feature removal makes change history unclear",
                        severity = ReviewComment.Severity.FOLLOW_UP,
                    ),
                ),
            ),
        )
    }
}
