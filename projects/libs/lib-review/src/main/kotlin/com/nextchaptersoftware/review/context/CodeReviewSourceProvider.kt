package com.nextchaptersoftware.review.context

import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.ktor.toHttpStatusCode
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.nullIfBlank
import io.ktor.http.HttpStatusCode
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CodeReviewSourceProvider(
    private val scmRepoApiFactory: ScmRepoApiFactory,
) {
    suspend fun getPrDiff(
        scmTeam: ScmTeam,
        repo: Repo,
        pr: PullRequest,
        scm: Scm,
    ): List<ScmPullRequestFile> {
        return scmRepoApiFactory.withApiFromRepo(scmTeam = scmTeam, repo = repo, scm = scm) { repoApi ->
            repoApi.pullRequestFiles(pullRequestNumber = pr.number).toList()
        }
    }

    suspend fun getFileContent(
        scmTeam: ScmTeam,
        repo: Repo,
        scm: Scm,
        commit: Hash,
        filePath: String,
    ): String? {
        @Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
        return runCatching {
            withTimeout(5.seconds) {
                scmRepoApiFactory.withApiFromRepo(scmTeam = scmTeam, repo = repo, scm = scm) { repoApi ->
                    repoApi.fileContents(path = filePath, ref = commit.asString())
                }.nullIfBlank()
            }
        }.onFailure { ex ->
            if (ex.toHttpStatusCode() != HttpStatusCode.NotFound) {
                LOGGER.warnAsync(ex, "filePath" to filePath) { "Failed to get file content" }
            }
        }.getOrNull()
    }
}
