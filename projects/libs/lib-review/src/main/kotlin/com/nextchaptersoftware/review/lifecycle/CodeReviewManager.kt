package com.nextchaptersoftware.review.lifecycle

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.CodeReview
import com.nextchaptersoftware.db.models.CodeReviewId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.db.models.types.CodeReviewScope
import com.nextchaptersoftware.db.stores.CodeReviewStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.types.Hash
import io.ktor.http.Url
import org.jetbrains.exposed.sql.Transaction

/**
 * Manages the lifecycle of code reviews.
 */
class CodeReviewManager(
    private val codeReviewStore: CodeReviewStore = Stores.codeReviewStore,
) {
    suspend fun createReview(
        orgId: OrgId,
        prId: PullRequestId,
        commit: Hash,
        trigger: CodeReviewEventType,
    ): CodeReview? {
        val incrementalEnabled = orgSettingsStore.getCodeReviewIncremental(orgId)

        return suspendedTransaction {
            // Skip reviews for the same commit.
            // This can happen when we receive multiple events for the same commit.
            if (codeReviewStore.exists(trx = this, prId = prId, commit = commit)) {
                return@suspendedTransaction null
            }

            // The first review is always a full review.
            // Subsequent reviews are incremental.
            val scope = when {
                codeReviewStore.hasPublishedFullReview(trx = this, prId = prId) -> CodeReviewScope.Incremental
                else -> CodeReviewScope.Full
            }

            // Skip incremental reviews unless incremental is enabled
            if (scope == CodeReviewScope.Incremental && !incrementalEnabled) {
                return@suspendedTransaction null
            }

            cancelReviews(trx = this, prId = prId, excludeCommit = commit)

            codeReviewStore.createReview(
                trx = this,
                prId = prId,
                commit = commit,
                trigger = trigger,
                scope = scope,
            )
        }
    }

    suspend fun cancelReviews(trx: Transaction? = null, prId: PullRequestId, excludeCommit: Hash?) {
        codeReviewStore.cancelReviews(trx = trx, prId = prId, excludeCommit = excludeCommit)
    }

    suspend fun assertIsActive(codeReviewId: CodeReviewId) {
        if (!codeReviewStore.isActiveReview(codeReviewId)) {
            throw CodeReviewCompletedException()
        }
    }

    suspend fun skip(codeReviewId: CodeReviewId) {
        codeReviewStore.doNotPublishReview(codeReviewId)
    }

    suspend fun publish(
        codeReviewId: CodeReviewId,
        publishedReviewId: String,
        publishedReviewUrl: Url,
    ) {
        codeReviewStore.publishReview(
            codeReviewId = codeReviewId,
            publishedReviewId = publishedReviewId,
            publishedReviewUrl = publishedReviewUrl,
        )
    }
}
