package com.nextchaptersoftware.review.reporting

import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmReviewComment
import com.nextchaptersoftware.types.Hash
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class CodeReviewPublisher(
    private val scmRepoApiFactory: ScmRepoApiFactory,
) {
    suspend fun publishReport(
        scmTeam: ScmTeam,
        repo: Repo,
        scm: Scm,
        pr: PullRequest,
        commit: Hash,
        markdown: String?,
        comments: List<ScmReviewComment>,
    ): ScmPullRequestReview {
        return runSuspendCatching {
            scmRepoApiFactory.withApiFromRepo(scmTeam = scmTeam, repo = repo, scm = scm) { repoApi ->
                repoApi.pullRequestReviewCreate(
                    pullRequestNumber = pr.number,
                    commitId = commit.asString(),
                    text = markdown,
                    comments = comments,
                )
            }
        }.onSuccess {
            LOGGER.debugAsync { "Successfully published code review report" }
        }.onFailure { exception ->
            LOGGER.errorAsync(exception, "reviewComments" to comments.map { it.copy(body = "REDACTED") }) { "Failed to publish code review report" }
        }.getOrThrow()
    }
}
