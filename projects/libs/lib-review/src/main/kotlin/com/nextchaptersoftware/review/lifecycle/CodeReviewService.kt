package com.nextchaptersoftware.review.lifecycle

import com.nextchaptersoftware.db.models.CodeReviewId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.db.models.types.CodeReviewScope
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.review.context.CodeReviewDiffContextService
import com.nextchaptersoftware.review.reporting.CodeReviewPublisher
import com.nextchaptersoftware.review.reporting.CodeReviewReporter
import com.nextchaptersoftware.review.reviewers.CodeReviewer
import com.nextchaptersoftware.review.reviewers.ReviewComment
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.trace.coroutine.withSpan
import com.nextchaptersoftware.types.Hash
import kotlinx.coroutines.CoroutineScope

class CodeReviewService(
    private val codeReviewDiffContextService: CodeReviewDiffContextService,
    private val codeReviewManager: CodeReviewManager = CodeReviewManager(),
    private val codeReviewPublisher: CodeReviewPublisher,
    private val codeReviewReporter: CodeReviewReporter,
    private val codeReviewer: CodeReviewer,
) {
    suspend fun reviewPullRequestCommit(
        codeReviewId: CodeReviewId,
        scmTeamId: ScmTeamId,
        repoId: RepoId,
        prId: PullRequestId,
        commit: Hash,
        trigger: CodeReviewEventType,
        scope: CodeReviewScope,
    ) {
        val scmTeam = Stores.scmTeamStore.findById(teamId = scmTeamId)
            ?: return codeReviewManager.skip(codeReviewId)

        val repo = Stores.repoStore.findById(repoId = repoId)
            ?: return codeReviewManager.skip(codeReviewId)

        val pr = Stores.pullRequestStore.findById(prId = prId)
            ?: return codeReviewManager.skip(codeReviewId)

        val scm = Scm.fromTeam(scmTeam)

        // diff
        val (actualCommit, fileDiffs) = step(codeReviewId, "diff") {
            codeReviewDiffContextService.getDiffContext(scmTeam, repo, scm, pr, commit)
        }.also { (_, files) ->
            files.skipIfEmpty(codeReviewId)
        }

        // review
        val reviewComments = step(codeReviewId, "review") {
            codeReviewer.reviewDiff(pr = pr, fileDiffs = fileDiffs)
        }.skipIfEmpty(codeReviewId)

        // judge
        val acceptedComments = step(codeReviewId, "judge") {
            reviewComments
                .filterNot { it.category == ReviewComment.Category.BEST_PRACTICE }
                .filterNot { it.category == ReviewComment.Category.CODE_STYLE }
                .filterNot { it.category == ReviewComment.Category.ERROR_HANDLING }
                .filterNot { it.category == ReviewComment.Category.MAINTAINABILITY }
                .filterNot { it.category == ReviewComment.Category.READABILITY }
                .filterNot { it.category == ReviewComment.Category.TESTABILITY }
                .filterNot { it.severity == ReviewComment.Severity.SHOULD_FIX }
                .filterNot { it.severity == ReviewComment.Severity.FOLLOW_UP }
        }.skipIfEmpty(codeReviewId)

        // report generation
        val (report, comments) = step(codeReviewId, "report") {
            codeReviewReporter.generateReport(
                commit = actualCommit,
                scope = scope,
                trigger = trigger,
                reviewComments = acceptedComments,
                fileDiffs = fileDiffs,
            )
        }.also { (_, comments) ->
            comments.skipIfEmpty(codeReviewId)
        }

        // publish
        step(codeReviewId, "publish") {
            codeReviewPublisher.publishReport(
                scmTeam = scmTeam,
                repo = repo,
                scm = scm,
                pr = pr,
                commit = actualCommit,
                markdown = report,
                comments = comments,
            ).also {
                codeReviewManager.publish(
                    codeReviewId = codeReviewId,
                    publishedReviewId = it.pullRequestNumber.toString(),
                    publishedReviewUrl = it.htmlUrl,
                )
            }
        }
    }

    /**
     * Wraps a block of code in a span with the given name.
     * Asserts that the code review is active before executing the block.
     */
    private suspend inline fun <T> step(
        codeReviewId: CodeReviewId,
        stepName: String,
        crossinline block: suspend CoroutineScope.() -> T,
    ): T {
        codeReviewManager.assertIsActive(codeReviewId)
        return withSpan("review.$stepName") {
            block()
        }
    }

    /**
     * Puts the review into a terminal state if the collection is empty.
     */
    private suspend fun <T : Collection<*>> T.skipIfEmpty(codeReviewId: CodeReviewId): T {
        if (isEmpty()) {
            codeReviewManager.skip(codeReviewId)
        }
        return this
    }
}
