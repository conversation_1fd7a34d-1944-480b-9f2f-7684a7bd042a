package com.nextchaptersoftware.review.reporting

internal data class FilePath(
    val filePath: String,
) {
    init {
        require(fileName.isNotBlank()) { "File path must not be blank" }
        require(directory.startsWith("/").not()) { "Path must not start with a slash" }
    }

    val directory: String
        get() = filePath.substringBeforeLast(delimiter = "/", missingDelimiterValue = "")

    val fileName: String
        get() = filePath.split("/").last()

    val fileExtension: String?
        get() {
            val name = fileName
            val idx = name.lastIndexOf('.')
            return when {
                // no dot
                idx < 0 -> null

                // dotfile like .gitignore implies no extension
                idx == 0 -> null

                // trailing dot: "file." -> empty extension
                idx == name.length - 1 -> null

                else -> name.substring(idx + 1)
            }
        }
}
