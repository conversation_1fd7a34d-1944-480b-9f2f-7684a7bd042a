package com.nextchaptersoftware.review.reporting

import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.db.models.types.CodeReviewScope
import com.nextchaptersoftware.review.reviewers.ReviewComment
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmReviewComment
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.truncateWithEllipsis
import kotlinx.html.DETAILS
import kotlinx.html.TBODY
import kotlinx.html.br
import kotlinx.html.details
import kotlinx.html.div
import kotlinx.html.h4
import kotlinx.html.h6
import kotlinx.html.li
import kotlinx.html.stream.createHTML
import kotlinx.html.summary
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.tr
import kotlinx.html.ul
import kotlinx.html.unsafe

class CodeReviewReporter(
    private val skipReportOverview: Boolean = true,
) {

    private companion object {
        private const val MAX_DIFF_SIZE = 10000
    }

    internal fun generateReport(
        commit: Hash,
        scope: CodeReviewScope,
        trigger: CodeReviewEventType,
        reviewComments: List<ReviewComment>,
        fileDiffs: List<ScmPullRequestFile>,
    ): Pair<String?, List<ScmReviewComment>> {
        val reviewedFilePaths = fileDiffs.mapNotNull { (it.newFilePath ?: it.oldFilePath)?.let(::FilePath) }

        val reportOverview = generateReportOverview(scope, trigger, commit, reviewedFilePaths, reviewComments)

        val comments = reviewComments.map { comment ->
            val relevantFileDiffs = fileDiffs
                .filter { it.newFilePath == comment.filePath }
                .mapNotNull { it.diff }
            comment.getAsScmReviewComment(commit, relevantFileDiffs)
        }

        return reportOverview to comments
    }

    private fun generateReportOverview(
        scope: CodeReviewScope,
        trigger: CodeReviewEventType,
        commit: Hash,
        reviewedFilePaths: List<FilePath>,
        reviewComments: List<ReviewComment>,
    ): String? {
        if (scope == CodeReviewScope.Incremental) {
            return null
        }

        if (skipReportOverview) {
            return null
        }

        val debug = generateDebugReport(trigger, scope, commit, reviewedFilePaths, reviewComments)
        val footer = generateFooterComponent()

        return """
            |## Review
            |
            |$debug
            |$footer
        """.trimMargin()
    }

    private fun generateDebugReport(
        trigger: CodeReviewEventType,
        scope: CodeReviewScope,
        commit: Hash,
        reviewedFilePaths: List<FilePath>,
        reviewComments: List<ReviewComment>,
    ): String {
        val commentCountByFilePath = reviewedFilePaths.associateWith { filePath -> reviewComments.count { it.filePath == filePath.filePath } }

        return createHTML(prettyPrint = false).details {
            summary { +"Information" }
            br
            div { +"Triggered a ${scope.name.lowercase()} review when ${trigger.displayedAs} on commit ${commit.asShortString()}." }
            br
            fileReviewsTable(commentCountByFilePath)
        }
    }

    private fun generateDebugComment(comment: ReviewComment, commit: Hash, relevantFileDiffs: List<String>): String {
        return createHTML(prettyPrint = false).details {
            summary { +"Information" }
            br
            ul {
                li { +"File: ${comment.filePath}" }
                li { +"Commit: ${commit.asShortString()}" }
                li { +"Line Range: ${comment.lineRange}" }
                li { +"Title: ${comment.content.title}" }
                li { +"Category: ${comment.category}" }
                li { +"Reasoning: ${comment.reasoning}" }
                li { +"Severity: ${comment.severity}" }
            }
            br
            relevantFileDiffs.forEach { diff ->
                unsafe {
                    +"""
                    |
                    |
                    |```diff
                    |${diff.truncateWithEllipsis(maxChars = MAX_DIFF_SIZE)}
                    |```
                    |
                    |
                """.trimMargin()
                }
            }
        }
    }

    private fun DETAILS.fileReviewsTable(commentCountByFilePath: Map<FilePath, Int>) {
        table {
            tr {
                th { +"File" }
                th { +"Status" }
            }
            tbody {
                commentCountByFilePath
                    .toList()
                    .sortedBy { (filePath, _) -> filePath.filePath }
                    .groupBy { (filePath, _) -> filePath.directory }
                    .forEach { (directory, directoryReviews) ->
                        tr {
                            td {
                                colSpan = "2"
                                h4 { +directory.ifBlank { "<root>" } }
                            }
                        }
                        directoryReviews.forEach { (filePath, reviewCount) ->
                            fileReviewRow(reviewCount = reviewCount, fileName = filePath.fileName)
                        }
                    }
            }
        }
    }

    private fun TBODY.fileReviewRow(
        reviewCount: Int,
        fileName: String,
    ) {
        tr {
            when (reviewCount) {
                0 -> {
                    td { +fileName }
                    td { +"No comments" }
                }

                else -> {
                    td { +fileName }
                    td { +pluralize(reviewCount, "comment") }
                }
            }
        }
    }

    private fun generateFooterComponent(): String {
        return createHTML(prettyPrint = false).h6 {
            +"Reviewed by Unblocked."
        }
    }

    private fun pluralize(count: Int, singular: String, plural: String = "${singular}s"): String {
        return when (count) {
            1 -> "$count $singular"
            else -> "$count $plural"
        }
    }

    private fun ReviewComment.getAsScmReviewComment(
        commit: Hash,
        relevantFileDiffs: List<String>,
    ): ScmReviewComment {
        val debug = generateDebugComment(this, commit, relevantFileDiffs)
        return ScmReviewComment(
            path = filePath,
            body = content.description + "\n\n$debug",
            side = when (lineRange?.side ?: ReviewComment.LineRange.Side.RIGHT) {
                ReviewComment.LineRange.Side.LEFT -> ScmReviewComment.Side.LEFT
                ReviewComment.LineRange.Side.RIGHT -> ScmReviewComment.Side.RIGHT
            },
            lineStart = lineRange?.startLine ?: 1,
            lineEnd = lineRange?.endLine ?: 1,
        )
    }
}
