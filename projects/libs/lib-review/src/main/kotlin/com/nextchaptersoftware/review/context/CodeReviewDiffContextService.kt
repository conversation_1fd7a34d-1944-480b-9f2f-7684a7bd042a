package com.nextchaptersoftware.review.context

import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.Stores.codeReviewExclusionStore
import com.nextchaptersoftware.git.GitAttributeKey
import com.nextchaptersoftware.git.GitAttributeValue
import com.nextchaptersoftware.git.GitAttributesParser
import com.nextchaptersoftware.git.GitPathGlob
import com.nextchaptersoftware.git.GitPathGlobAttributes
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.types.Hash
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CodeReviewDiffContextService(
    private val codeReviewSourceProvider: CodeReviewSourceProvider,
) {
    suspend fun getDiffContext(
        scmTeam: ScmTeam,
        repo: Repo,
        scm: Scm,
        pr: PullRequest,
        commit: Hash,
    ): Pair<Hash, List<ScmPullRequestFile>> {
        val exclusions = getPathExclusionPatterns(repo.id)

        val gitExclusions = getGitExclusions(scmTeam = scmTeam, repo = repo, scm = scm, commit = commit)

        val fileDiffs = codeReviewSourceProvider.getPrDiff(scmTeam, repo, pr, scm)
            .filterNot { it.diff.isNullOrEmpty() }
            .filterNot { file -> fileMatchesAnyPattern(exclusions, file) }
            .filterNot { file ->
                setOfNotNull(file.newFilePath, file.oldFilePath).any { filePath ->
                    findAllMatchingAttributes(gitExclusions, filePath).any { (_, value) ->
                        value == GitAttributeValue.Set
                    }
                }
            }

        val actualCommit = fileDiffs.mapNotNull { it.commitSha }.toSet().firstOrNull()?.let(Hash::parse) ?: commit
        if (actualCommit != commit) {
            // TODO fix by fetching the right diff context
            LOGGER.warnAsync("commit" to commit, "actualCommit" to actualCommit) { "Commit mismatch" }
        }

        return actualCommit to fileDiffs
    }

    private fun fileMatchesAnyPattern(patterns: List<GitPathGlob>, file: ScmPullRequestFile): Boolean {
        val paths = setOfNotNull(file.newFilePath, file.oldFilePath)
        return patterns.any { pattern -> paths.any { pattern.matches(it) } }
    }

    private suspend fun getPathExclusionPatterns(repoId: RepoId): List<GitPathGlob> {
        return codeReviewExclusionStore.getExclusionForRepo(repoId)?.excludedPaths.orEmpty()
            .map { GitPathGlob(it) }
    }

    private fun findAllMatchingAttributes(
        attributes: List<GitPathGlobAttributes>,
        filePath: String,
    ): Map<GitAttributeKey, GitAttributeValue> {
        return attributes
            .filter { (pattern, _) -> pattern.matches(filePath) }
            .map { (_, attributes) -> attributes }
            .flatten()
            // Take the last value for each key
            .associate { (key, value) -> key to value }
    }

    private suspend fun getGitExclusions(
        scmTeam: ScmTeam,
        repo: Repo,
        scm: Scm,
        commit: Hash,
    ): List<GitPathGlobAttributes> {
        return codeReviewSourceProvider.getFileContent(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
            commit = commit,
            filePath = ".gitattributes",
        )?.let {
            GitAttributesParser(targetAttributes = setOf(GitAttributeKey.LinguistGenerated, GitAttributeKey.LinguistVendored)).parse(it)
        }.orEmpty()
    }
}
