package com.nextchaptersoftware.review.lifecycle

import com.nextchaptersoftware.db.models.CodeReview
import com.nextchaptersoftware.db.models.types.CodeReviewEventType
import com.nextchaptersoftware.event.queue.dequeue.EventMessageProcessor
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.review.CodeReviewEvent
import com.nextchaptersoftware.review.services.CodeReviewControlService
import com.nextchaptersoftware.serialization.Serialization.decode
import jakarta.jms.Message
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CodeReviewEventProcessor(
    private val codeReviewControlService: CodeReviewControlService,
    private val codeReviewManager: CodeReviewManager = CodeReviewManager(),
    private val codeReviewService: CodeReviewService,
) : EventMessageProcessor {

    override suspend fun process(context: Message, body: String) {
        val event = body.decode<CodeReviewEvent>()

        withLoggingContextAsync(
            "event.class" to event.javaClass.canonicalName,
            "event.priority" to event.priority.name,
        ) {
            when (event) {
                is CodeReviewEvent.PullRequestChanged -> processPullRequestChangedEvent(event)
            }
        }
    }

    private suspend fun processPullRequestChangedEvent(event: CodeReviewEvent.PullRequestChanged) = withLoggingContextAsync(
        "orgId" to event.orgId,
        "scmTeamId" to event.scmTeamId,
        "repoId" to event.repoId,
        "prId" to event.prId,
        "eventType" to event.eventType.name,
    ) {
        if (!codeReviewControlService.shouldProcessCodeReview(
                orgId = event.orgId,
                scmTeamId = event.scmTeamId,
                repoId = event.repoId,
                prId = event.prId,
            )
        ) {
            return@withLoggingContextAsync
        }

        when (event.eventType) {
            CodeReviewEventType.PullRequestOpened,
            CodeReviewEventType.PullRequestReopened,
            CodeReviewEventType.PullRequestCodeChanged,
            CodeReviewEventType.PullRequestReadyForReview,
                -> {
                codeReviewManager.createReview(
                    orgId = event.orgId,
                    prId = event.prId,
                    commit = event.commit,
                    trigger = event.eventType,
                )?.also { codeReview ->
                    startReview(codeReview = codeReview, event = event)
                }
            }

            CodeReviewEventType.PullRequestClosed,
            CodeReviewEventType.PullRequestConvertedToDraft,
                -> codeReviewManager.cancelReviews(
                prId = event.prId,
                excludeCommit = null,
            )
        }
    }

    private suspend fun startReview(codeReview: CodeReview, event: CodeReviewEvent.PullRequestChanged) {
        LOGGER.debugAsync { "starting review" }
        runSuspendCatching {
            codeReviewService.reviewPullRequestCommit(
                codeReviewId = codeReview.id,
                scmTeamId = event.scmTeamId,
                repoId = event.repoId,
                prId = event.prId,
                commit = event.commit,
                trigger = event.eventType,
                scope = codeReview.scope,
            )
        }.onFailure { exception ->
            // Swallow completed exception
            if (exception is CodeReviewCompletedException) {
                LOGGER.debugAsync(exception) { "Code review has already completed" }
                return@onFailure
            }

            // Rethrow all other exceptions
            throw exception
        }
    }
}
