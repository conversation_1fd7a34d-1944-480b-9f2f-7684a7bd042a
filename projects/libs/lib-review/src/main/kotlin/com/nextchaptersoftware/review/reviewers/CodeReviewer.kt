package com.nextchaptersoftware.review.reviewers

import com.nextchaptersoftware.db.models.MLInferenceEngine
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.sensitive.withSensitiveLoggingContextAsync
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.xml.SerializationExtensions.decodeXml
import kotlin.collections.emptyMap
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CodeReviewer(
    private val completionService: CompletionService,
) {
    companion object {
        private val TIMEOUT = 25.seconds
    }

    internal suspend fun reviewDiff(pr: PullRequest, fileDiffs: List<ScmPullRequestFile>): List<ReviewComment> {
        if (fileDiffs.isEmpty()) {
            return emptyList()
        }

        return withTimeout(TIMEOUT) {
            reviewWithLanguageModel(pr, fileDiffs)
        }
    }

    private suspend fun reviewWithLanguageModel(pr: PullRequest, fileDiffs: List<ScmPullRequestFile>): List<ReviewComment> {
        val prompt = createCodeReviewPrompt(pr, fileDiffs)

        val rawReviewResult = completionService.query(
            prompt = prompt,
            engine = MLInferenceEngine.RoundRobinClaude4Sonnet,
            temperature = 0.1f,
        )

        val rawReview = rawReviewResult.text

        return withSensitiveLoggingContextAsync(
            fields = emptyMap(),
            sensitiveFields = mapOf(
                "prompt" to prompt,
                "rawReview" to rawReview,
            ),
        ) {
            runSuspendCatching { rawReview.decodeXml<ReviewComments>() }
                .onFailure {
                    LOGGER.errorAsync(it, "rawReview" to rawReview) { "Failed to decode LLM review" }
                }
                .onSuccess {
                    LOGGER.debugAsync { "Successfully decoded LLM review" }
                }
                .getOrThrow()
                .comments
        }
    }

    private fun createCodeReviewPrompt(
        pullRequest: PullRequest,
        fileDiffs: List<ScmPullRequestFile>,
    ): String {
        val systemPrompt = createSystemPrompt()
        val userPrompt = createUserPrompt(pullRequest, fileDiffs)

        return """
            |[[SYSTEM]]
            |$systemPrompt
            |
            |[[USER]]
            |$userPrompt
        """.trimMargin()
    }

    private fun createUserPrompt(
        pullRequest: PullRequest,
        fileDiffs: List<ScmPullRequestFile>,
    ): String {
        val allDiffs = fileDiffs.joinToString("\n\n\n") { diff ->
            """
                |Diff:
                |--- ${diff.oldFilePath ?: diff.newFilePath}
                |+++ ${diff.newFilePath}
                |${diff.diff}
            """.trimMargin()
        }

        return """
            |PR title:
            |${pullRequest.title} #${pullRequest.number}
            |
            |PR description:
            |${pullRequest.description}
            |
            |$allDiffs
            |
        """.trimMargin()
    }

    @Suppress("LongMethod")
    private fun createSystemPrompt() = """
|You are a senior software engineer reviewing file diffs from a pull request.
|
|## Scope and priorities
|- Focus on crashes and hangs, logic errors, performance and security issues as your top priorities.
|- Work strictly from the provided diffs and file paths. Do not assume unseen code.
|
|## Evidence threshold
|- Report only when you can point to specific changed line(s) in this diff and explain the risk with high confidence.
|- Prefer precision over coverage. If key context is missing, omit rather than speculate.
|
|## Line-number rules (unified diff)
|- To comment on the right side of the diff where the code is added, use the new-file line numbers (the "+" side).
|    - Track current new-line from each hunk header: @@ -oldStart,oldCount +newStart,newCount @@
|    - ' ' context line: newLine += 1
|    - '+' added line: newLine += 1 (these lines exist in the new file; preferred anchors)
|    - '-' deleted line: do not increment newLine (line doesn't exist in the new file)
|- To comment on the left side of the diff where the code is removed, use the old-file line numbers (the "-" side).
|    - Track current old-line from each hunk header: @@ -oldStart,oldCount +newStart,newCount @@
|    - ' ' context line: oldLine += 1
|    - '+' added line: do not increment oldLine (line doesn't exist in the old file)
|    - '-' deleted line: oldLine += 1 (these lines exist in the old file; preferred anchors)
|
|## XML output format
|- If no issues, output exactly: <reviewComments></reviewComments>
|- Otherwise, for each distinct issue strictly follow the XML output format below.
|- Answer only in the following XML format. Output the XML and nothing else.
|- All elements are required except for <lineRange>.
|
|<reviewComments>
|  <comment>
|    <filePath>path/to/file.ext</filePath>
|    <lineRange>
|      <side>left|right</side>
|      <startLine>13</startLine>
|      <endLine>16</endLine>
|    </lineRange>
|    <content>
|      <title>Short succinct title for the comment</title>
|      <description>
|        <![CDATA[
|Succinctly describe the problem and provide a practical fix only if you have a clear fix.
|Offer a code language suggestion only if you have extremely high confidence in the fix.
|        ]]>
|      </description>
|    </content>
|    <category>CRASH_OR_HANG|LOGIC_ERROR|PERFORMANCE|SECURITY|ERROR_HANDLING|READABILITY|CODE_STYLE|MAINTAINABILITY|TESTABILITY|BEST_PRACTICE</category>
|    <reasoning>Concise reason why this is flagged as a problem</reasoning>
|    <severity>MUST_FIX|SHOULD_FIX|FOLLOW_UP</severity>
|  </comment>
|  ...
|<reviewComments>
|
|## Rules for <lineRange> element
|- Optional line range to highlight in the diff.
|- Use only when the comment is related to a specific line range in the diff.
|- To highlight the left side of a diff (the deleted lines), use <side>left</side>.
|- To highlight the right side of a diff (the added lines), use <side>right</side>.
|- Line numbers are 1-based and inclusive.
|- Both <startLine> and <endLine> are required.
|- For single lines, use the same value for start and end, e.g., <startLine>4</startLine><endLine>4</endLine>.
|- For multiple lines, use different start and end values, e.g., <startLine>4</startLine><endLine>7</endLine>.
|- The start line must be in the same hunk as the end line.
|
|## Deduplication and anchoring
|- Merge duplicates of the same underlying risk within files; report once at the earliest relevant line.
|- Prefer anchoring to the first line where the issue is introduced or becomes observable.
|
|## Tone and disclosure
|- Be crisp and decisive; avoid hedging unless strictly necessary. Do not include your reasoning steps—only the final findings per the format.
|
|## Example A (division by zero issue leading to crash)
|Diff:
|```
|--- api/Calculator.java
|+++ api/Calculator.java
|@@ -195,4 +195,12 @@ public class MethodsConfig {
|                 ", rateLimitJitterMillis=" + rateLimitJitterMillis +
|                 '}';
|     }
|+
|+    public double calculateAverage(int[] numbers) {
|+        int sum = 0;
|+        for (int num : numbers) {
|+           sum += num;
|+        }
|+        return (double) sum / numbers.length;
|+    }
| }
|```
|
|Expected output:
|<reviewComments>
|  <comment>
|    <filePath>api/Calculator.java</filePath>
|    <lineRange>
|      <side>right</side>
|      <startLine>199</startLine>
|      <endLine>205</endLine>
|    </lineRange>
|    <content>
|      <title>Division by zero risk</title>
|      <description>
|        <![CDATA[
|If `numbers.length` is 0, division by zero occurs. Add a check to avoid this.
|```java
|        return (double) sum / (numbers.length == 0 ? 1 : numbers.length);
|```
|        ]]>
|      </description>
|    </content>
|    <category>CRASH_OR_HANG</category>
|    <reasoning>Array length is not checked before division.</reasoning>
|    <severity>high</severity>
|  </comment>
|</reviewComments>
""".trimMargin()
}
