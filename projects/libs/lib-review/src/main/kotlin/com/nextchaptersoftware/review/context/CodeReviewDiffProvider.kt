package com.nextchaptersoftware.review.context

import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import kotlinx.coroutines.flow.toList

class CodeReviewDiffProvider(
    private val scmRepoApiFactory: ScmRepoApiFactory,
) {
    suspend fun getScmPrDiff(
        scmTeam: ScmTeam,
        repo: Repo,
        pr: PullRequest,
        scm: Scm,
    ): List<ScmPullRequestFile> {
        return scmRepoApiFactory.withApiFromRepo(scmTeam = scmTeam, repo = repo, scm = scm) { repoApi ->
            repoApi.pullRequestFiles(pullRequestNumber = pr.number).toList()
        }
    }
}
