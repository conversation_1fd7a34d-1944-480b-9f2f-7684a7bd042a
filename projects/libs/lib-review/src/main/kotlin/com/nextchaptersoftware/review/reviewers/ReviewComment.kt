package com.nextchaptersoftware.review.reviewers

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import nl.adaptivity.xmlutil.serialization.XmlElement
import nl.adaptivity.xmlutil.serialization.XmlSerialName

@Serializable
@XmlSerialName("reviewComments")
internal data class ReviewComments(
    @XmlElement(true)
    @XmlSerialName("comment")
    val comments: List<ReviewComment>,
)

/**
 * Represents a review comment with structured data matching the XML format expected by the AI reviewer.
 */
@Serializable
data class ReviewComment(
    @XmlElement
    val filePath: String,
    @XmlElement
    @XmlSerialName("lineRange")
    val lineRange: LineRange? = null,
    @XmlElement
    val content: CommentContent,
    @XmlElement
    @XmlSerialName("category")
    val category: Category,
    @XmlElement
    val reasoning: String,
    @XmlElement
    @XmlSerialName("severity")
    val severity: Severity,
) {
    @Serializable
    @XmlSerialName("lineRange")
    data class LineRange(
        @XmlElement
        @XmlSerialName("side")
        val side: Side,
        @XmlElement
        @XmlSerialName("startLine")
        val startLine: Int,
        @XmlElement
        @XmlSerialName("endLine")
        val endLine: Int = startLine,
    ) {
        enum class Side {
            @SerialName("left")
            LEFT,

            @SerialName("right")
            RIGHT,
        }
    }

    @Serializable
    @XmlSerialName("content")
    data class CommentContent(
        @XmlElement
        @XmlSerialName("title")
        val title: String,
        @XmlElement
        @XmlSerialName("description")
        val description: String,
    )

    enum class Category {
        @SerialName("CRASH_OR_HANG")
        CRASH_OR_HANG,

        @SerialName("LOGIC_ERROR")
        LOGIC_ERROR,

        @SerialName("PERFORMANCE")
        PERFORMANCE,

        @SerialName("SECURITY")
        SECURITY,

        @SerialName("ERROR_HANDLING")
        ERROR_HANDLING,

        @SerialName("READABILITY")
        READABILITY,

        @SerialName("CODE_STYLE")
        CODE_STYLE,

        @SerialName("MAINTAINABILITY")
        MAINTAINABILITY,

        @SerialName("TESTABILITY")
        TESTABILITY,

        @SerialName("BEST_PRACTICE")
        BEST_PRACTICE,
    }

    enum class Severity {
        @SerialName("MUST_FIX")
        MUST_FIX,

        @SerialName("SHOULD_FIX")
        SHOULD_FIX,

        @SerialName("FOLLOW_UP")
        FOLLOW_UP,
    }
}
