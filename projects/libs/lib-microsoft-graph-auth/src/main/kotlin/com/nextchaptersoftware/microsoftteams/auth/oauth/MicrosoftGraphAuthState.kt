package com.nextchaptersoftware.microsoftteams.auth.oauth

import com.nextchaptersoftware.auth.oauth.OAuthStateProvider
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.serialization.Serialization.decode
import com.nextchaptersoftware.serialization.Serialization.encode
import com.nextchaptersoftware.utils.Base64.base64Decode
import com.nextchaptersoftware.utils.Base64.base64Encode
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class MicrosoftGraphAuthState(
    @Contextual val orgId: OrgId,
    @Contextual val personId: PersonId,
    @Contextual val redirectUrl: Url?,
) : OAuthStateProvider {
    override fun encodeAuthState(): String {
        return encodeAuthState(this)
    }

    companion object {
        fun encodeAuthState(microsoftGraphAuthState: MicrosoftGraphAuthState): String {
            return microsoftGraphAuthState.encode().base64Encode()
        }

        fun decodeAuthState(rawMicrosoftGraphAuthState: String): MicrosoftGraphAuthState {
            return rawMicrosoftGraphAuthState.base64Decode().decode()
        }
    }
}
