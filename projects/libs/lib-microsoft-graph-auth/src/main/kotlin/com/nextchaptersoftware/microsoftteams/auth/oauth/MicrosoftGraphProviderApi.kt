package com.nextchaptersoftware.microsoftteams.auth.oauth

import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchange
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.auth.oauth.OAuthTokenRefreshContext
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.auth.provider.utils.OAuthTokenExchangeExtensions.fromProvider
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.microsoftgraph.api.MicrosoftGraphApiProvider
import com.nextchaptersoftware.microsoftteams.ingestion.services.MicrosoftGraphMemberModelService
import com.nextchaptersoftware.serialization.Serialization.decode
import io.ktor.util.decodeBase64String
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

/**
 * This supports Microsoft Graph delegated OAuth where a single user signs in using the standard OAuth flow
 */
class MicrosoftGraphProviderApi(
    private val microsoftGraphApiProvider: MicrosoftGraphApiProvider,
    private val microsoftGraphMemberModelService: MicrosoftGraphMemberModelService,
) : OAuthApi {

    override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
        val oAuthState: OAuthState? = context.state?.decodeBase64String()?.decode()
        val microsoftGraphAuthState = oAuthState?.state?.let {
            MicrosoftGraphAuthState.decodeAuthState(it)
        } ?: run {
            LOGGER.infoAsync("state" to context.state) { "There was no valid Microsoft Teams auth state present" }
            error("Invalid oAuthState")
        }

        return withLoggingContextAsync(
            "orgId" to microsoftGraphAuthState.orgId,
            "personId" to microsoftGraphAuthState.personId,
            "redirectUrl" to microsoftGraphAuthState.redirectUrl,
        ) {
            // For Microsoft Teams, we use client credentials flow
            // The code from the OAuth callback contains the tenant ID
            // We'll use this to get access tokens using client credentials

            val oauthTokens = microsoftGraphApiProvider.authApi.exchangeForToken(code = context.code)
            val me = microsoftGraphApiProvider.usersApi.me(tokens = oauthTokens)

            microsoftGraphMemberModelService.upsertIdentity(
                oauthTokens = oauthTokens,
                personId = microsoftGraphAuthState.personId,
                externalId = me.id,
                displayName = me.displayName,
                userPrincipalName = me.userPrincipalName,
                name = me.givenName ?: me.displayName,
                email = me.mail ?: "",
            )

            OAuthTokenExchange(
                oAuthTokens = oauthTokens,
                redirectUrl = microsoftGraphAuthState.redirectUrl,
            ).fromProvider(
                provider = Provider.MicrosoftTeams,
            )
        }
    }

    override suspend fun refreshAccessTokens(context: OAuthTokenRefreshContext): OAuthTokens {
        TODO("Not yet implemented")
    }

    @Suppress("EmptyFunctionBlock")
    override fun close() {
    }
}
