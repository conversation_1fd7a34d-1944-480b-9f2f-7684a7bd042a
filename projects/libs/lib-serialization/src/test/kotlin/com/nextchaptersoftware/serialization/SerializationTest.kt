package com.nextchaptersoftware.serialization

import com.nextchaptersoftware.serialization.Serialization.decode
import kotlinx.serialization.Serializable
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SerializationTest {

    @Test
    fun `it should decode a map to a data class`() {
        @Serializable
        data class LotsOfFields(
            val string: String,
            val int: Int,
            val long: Long,
            val double: Double,
            val boolean: <PERSON><PERSON><PERSON>,
        )

        val map = mapOf(
            "string" to "value",
            "int" to "1",
            "long" to "1",
            "double" to "1.0",
            "boolean" to "true",
        )

        val decoded = map.decode<LotsOfFields>()

        assertThat(decoded).isEqualTo(
            LotsOfFields(
                string = "value",
                int = 1,
                long = 1,
                double = 1.0,
                boolean = true,
            ),
        )
    }
}
