package com.nextchaptersoftware.serialization.adapters

import java.time.Instant
import java.time.format.DateTimeFormatter
import java.util.Date
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

object DateAdapter : KSerializer<Date> {

    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("Date", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: Date) {
        encoder.encodeString(DateTimeFormatter.ISO_DATE_TIME.format(value.toInstant()))
    }

    override fun deserialize(decoder: Decoder): Date {
        val decoded = DateTimeFormatter.ISO_DATE_TIME.parse(decoder.decodeString())
        return Date.from(Instant.from(decoded))
    }
}
