package com.nextchaptersoftware.serialization

import com.nextchaptersoftware.serialization.adapters.DateAdapter
import com.nextchaptersoftware.serialization.adapters.LocalDateAdapter
import com.nextchaptersoftware.serialization.adapters.LocalDateTimeAdapter
import com.nextchaptersoftware.serialization.adapters.OffsetDateTimeAdapter
import com.nextchaptersoftware.serialization.adapters.StringBuilderAdapter
import com.nextchaptersoftware.serialization.adapters.URIAdapter
import com.nextchaptersoftware.serialization.adapters.URLAdapter
import com.nextchaptersoftware.serialization.adapters.UUIDAdapter
import io.ktor.http.Url
import io.ktor.http.UrlSerializer
import java.net.URI
import java.net.URL
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.util.Date
import java.util.UUID
import kotlin.time.Instant
import kotlinx.serialization.builtins.serializer
import kotlinx.serialization.modules.SerializersModule

object Modules {

    @JvmStatic
    val kotlin = SerializersModule {
        contextual(Date::class, DateAdapter)
        contextual(Instant::class, Instant.serializer())
        contextual(LocalDate::class, LocalDateAdapter)
        contextual(LocalDateTime::class, LocalDateTimeAdapter)
        contextual(OffsetDateTime::class, OffsetDateTimeAdapter)
        contextual(StringBuilder::class, StringBuilderAdapter)
        contextual(URI::class, URIAdapter)
        contextual(URL::class, URLAdapter)
        contextual(UUID::class, UUIDAdapter)
        contextual(Url::class, UrlSerializer)
    }
}
