package com.nextchaptersoftware.serialization

import com.nextchaptersoftware.serialization.Serializers.lenient
import com.nextchaptersoftware.serialization.Serializers.pretty
import com.nextchaptersoftware.serialization.Serializers.relaxed

object Serialization {

    inline fun <reified T> String.decode(): T = relaxed.decodeFromString(this)

    inline fun <reified T> String.lenientDecode(): T = lenient.decodeFromString(this)

    inline fun <reified T> T.encode(): String = relaxed.encodeToString(this)

    inline fun <reified T> T.encodePretty(): String = pretty.encodeToString(this)

    /**
     * Note: this function will only succeed if the decoded type is a data class with primitive properties
     */
    inline fun <reified T> Map<String, String>.decode() = this.encode().decode<T>()
}
