package com.nextchaptersoftware.serialization

import kotlinx.serialization.json.Json

object Serializers {

    @JvmStatic
    val relaxed by lazy {
        Json {
            serializersModule = Modules.kotlin
            ignoreUnknownKeys = true
        }
    }

    @JvmStatic
    val pretty: J<PERSON> by lazy {
        Json {
            serializersModule = Modules.kotlin
            ignoreUnknownKeys = true
            prettyPrint = true
        }
    }

    @JvmStatic
    val lenient by lazy {
        Json {
            serializersModule = Modules.kotlin
            ignoreUnknownKeys = true
            isLenient = true
            allowTrailingComma = true
        }
    }
}
