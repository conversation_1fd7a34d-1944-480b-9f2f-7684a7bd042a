package com.nextchaptersoftware.mongo

import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.MongoCredential
import com.mongodb.WriteConcern
import com.mongodb.kotlin.client.coroutine.MongoClient
import com.mongodb.kotlin.client.coroutine.MongoDatabase
import com.nextchaptersoftware.config.MongoWriteConcern
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.utils.KPropertyExtensions.isLazyInitialized
import com.sksamuel.hoplite.Secret
import io.ktor.http.Url
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

/**
 * Mongo client wrapper using the official Kotlin coroutine driver (5.x).
 *
 * Notes:
 *  - Works with both `mongodb://` and `mongodb+srv://` URIs.
 *  - Credentials are applied via MongoCredential (not via query params).
 *  - WriteConcern, retryWrites, journal, pool/timeouts are all set explicitly via settings.
 */
class MongoApiClient(
    private val baseUri: Url,
    private val userName: String,
    private val password: Secret,
    private val authDatabase: String = "admin",
    private val useTls: Boolean = true,
    private val maxPoolSize: Int = 100,
    private val appName: String? = null,

    /** Connect / read / pool wait timeouts (also used as default general timeout). */
    private val timeout: Duration = 10.seconds,

    /** How long to wait for server selection during initial/refresh topology resolution. */
    private val serverSelectionTimeout: Duration = 10.seconds,

    /** Maximum number of concurrent connections being established at once. */
    private val maxConnecting: Int = 2,

    /** Enable retryable writes. */
    private val retryWrites: Boolean = true,

    /** High-level write concern setting. */
    private val writeConcern: WriteConcern = WriteConcern.MAJORITY.withJournal(true),

    /** Optional customizer for low-level MongoClientSettings. */
    private val configureSettings: (MongoClientSettings.Builder.() -> Unit)? = null,
) : AutoCloseable {

    /** Lazily initialized coroutine Mongo client. */
    val instance: MongoClient by lazy { buildClient() }

    /** Convenience accessor for a database handle. */
    fun database(name: String): MongoDatabase = instance.getDatabase(name)

    private fun buildClient(): MongoClient {
        // Determine scheme. If caller provided non-mongo scheme, default to mongodb.
        val scheme = when (baseUri.protocol.name.lowercase()) {
            "mongodb", "mongodb+srv" -> baseUri.protocol.name
            else -> "mongodb"
        }

        // Build host[:port] (SRV records do not need a port)
        val hostPort = if (baseUri.port > 0 && scheme != "mongodb+srv") {
            "${baseUri.host}:${baseUri.port}"
        } else {
            baseUri.host
        }

        // URL-encode password for connection string safety
        val encodedPwd = URLEncoder.encode(password.value, StandardCharsets.UTF_8)

        // Optional path can designate the default db in the URI (not required)
        val defaultDbPath = if (baseUri.encodedPath.isNotBlank() && baseUri.encodedPath != "/") {
            baseUri.encodedPath
        } else {
            ""
        }

        // Preserve any existing query on the incoming URI (e.g. retryWrites, w, appName) but we *also*
        // set equivalent options explicitly in MongoClientSettings below so we don't rely on the URI.
        val rawQuery = baseUri.encodedQuery
        val querySuffix = if (rawQuery.isNullOrBlank()) "" else "?$rawQuery"

        val uri = "$scheme://$userName:$encodedPwd@$hostPort$defaultDbPath$querySuffix"
        val connectionString = ConnectionString(uri)

        val credential = MongoCredential.createCredential(
            userName,
            authDatabase, // Atlas users typically live in "admin"
            password.value.toCharArray(),
        )

        val settings = MongoClientSettings.builder()
            .applyConnectionString(connectionString)
            .credential(credential)
            .applicationName(appName)
            .retryWrites(retryWrites)
            .writeConcern(writeConcern)
            .applyToSslSettings { it.enabled(useTls) }
            .applyToSocketSettings {
                it.connectTimeout(timeout.inWholeSeconds, TimeUnit.SECONDS)
                it.readTimeout(timeout.inWholeSeconds, TimeUnit.SECONDS)
            }
            .applyToConnectionPoolSettings {
                it.maxSize(maxPoolSize)
                it.maxConnecting(maxConnecting)
                it.maxWaitTime(timeout.inWholeSeconds, TimeUnit.SECONDS)
            }
            .applyToClusterSettings {
                it.serverSelectionTimeout(serverSelectionTimeout.inWholeSeconds, TimeUnit.SECONDS)
            }
            .apply {
                configureSettings?.invoke(this)
            }
            .build()

        return MongoClient.create(settings)
    }

    /** Close the underlying client. Safe to call multiple times. */
    override fun close() {
        runSuspendCatching {
            if (::instance.isLazyInitialized) {
                instance.close()
            }
        }
    }

    companion object {
        /**
         * Convenience factory from explicit MongoConfig (explicit, typed options only).
         */
        fun fromConfig(
            config: com.nextchaptersoftware.config.MongoConfig,
            configureSettings: (MongoClientSettings.Builder.() -> Unit)? = null,
        ): MongoApiClient {
            return MongoApiClient(
                baseUri = Url(config.baseApiUri),
                userName = config.userName,
                password = config.password,
                authDatabase = config.authDatabase,
                useTls = config.useTls,
                maxPoolSize = config.maxPoolSize,
                appName = config.appName,
                timeout = config.timeout,
                serverSelectionTimeout = config.serverSelectionTimeout,
                maxConnecting = config.maxConnecting,
                retryWrites = config.retryWrites,
                writeConcern = config.writeConcern.toDriverWriteConcern(config.journal),
                configureSettings = configureSettings,
            )
        }
    }
}

// ---------- Helpers ----------
@Suppress("MagicNumber")
private fun MongoWriteConcern.toDriverWriteConcern(journal: Boolean): WriteConcern =
    when (this) {
        MongoWriteConcern.W1 -> WriteConcern.W1
        MongoWriteConcern.W2 -> WriteConcern(2)
        MongoWriteConcern.W3 -> WriteConcern(3)
        MongoWriteConcern.MAJORITY -> WriteConcern.MAJORITY
    }.withJournal(journal)
