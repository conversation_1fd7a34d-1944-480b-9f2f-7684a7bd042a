{
  "folders": [
    {
      "path": "web",
    },
    {
      "path": "web-landing",
    },
    {
      "path": "vscode",
    },
    {
      "path": "shared",
    },
    {
      "path": "jetbrains",
    },
    {
      "path": "desktop",
    },
    {
      "path": "api",
    },
    {
      "path": "common/src/main/resources/protos",
    },
    {
      "path": "mcp",
    },
    {
      "path": ".github/workflows",
    },
    {
      "path": ".cursor",
    },
  ],
  "settings": {
    "eslint.useFlatConfig": true
  },
}
