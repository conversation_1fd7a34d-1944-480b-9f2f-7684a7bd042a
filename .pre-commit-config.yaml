# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-json
      - id: check-yaml
        args: [ '--unsafe' ]
      - id: check-added-large-files
        args: [ --maxkb=5120 ]
      - id: check-merge-conflict
      - id: check-toml
      - id: check-xml
      - id: mixed-line-ending
      - id: requirements-txt-fixer
  - repo: https://github.com/gitguardian/ggshield
    rev: v1.27.0
    hooks:
      - id: ggshield
        language_version: python3
        stages: [ pre-commit ]
