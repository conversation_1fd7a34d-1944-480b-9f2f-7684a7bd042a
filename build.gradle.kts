import com.github.gradle.node.npm.task.NpmInstallTask
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import com.nextchaptersoftware.plugin.tasks.BuildMatrixTask
import io.gitlab.arturbosch.detekt.Detekt
import org.cyclonedx.gradle.CycloneDxTask
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.jmailen.gradle.kotlinter.tasks.FormatTask
import org.jmailen.gradle.kotlinter.tasks.LintTask

tasks.register<BuildMatrixTask>("buildMatrix") {
    buildTasks.set(
        provider {
            subprojects.mapNotNull { sub -> sub.tasks.findByName("build")?.path }
        },
    )
}

val ktlintVersion = libs.versions.ktlintVersion.get()


fun isGitHubActionsBuild(): Provider<Boolean> {
    return providers.environmentVariable("GITHUB_ACTIONS").map { true }.orElse(false)
}

fun isPullRequestBuild(): Provider<Boolean> {
    return providers.environmentVariable("GITHUB_HEAD_REF").map { it.isNotEmpty() }.orElse(false)
}

plugins {
    kotlin("jvm")
    id("io.gitlab.arturbosch.detekt")
    id("com.gradleup.shadow")
    id("org.gradle.test-retry")
    id("org.jmailen.kotlinter")
    id("org.cyclonedx.bom")
    id("com.github.node-gradle.node")
}

buildscript {
    dependencies {
        classpath(files("${rootProject.projectDir}/custom-ktlint-rules/libs/custom-ktlint-rules-1.0.0.jar"))
    }
}

repositories {
    mavenCentral()
    google()
    maven { url = uri("${rootProject.projectDir.absolutePath}/.maven") }
}

allprojects {
    group = "com.nextchaptersoftware"
    version = "1.0.0"

    apply(plugin = "com.nextchaptersoftware.plugin")
    apply(plugin = "org.cyclonedx.bom")
    apply(plugin = "org.gradle.test-retry")
    apply(plugin = "io.gitlab.arturbosch.detekt")

    if (!project.projectDir.absolutePath.contains("api-generated")) {
        apply(plugin = "org.jmailen.kotlinter")
        val lintExcludeSpec = Spec<FileTreeElement> { element ->
            element.file.path.contains("generated/") || element.file.path.contains("generated-")
        }

        tasks.withType<FormatTask> {
            exclude(lintExcludeSpec)
        }

        tasks.withType<LintTask> {
            exclude(lintExcludeSpec)
        }
    }

    val isServices = projectDir.absolutePath.contains("services")
    val isApps = projectDir.absolutePath.contains("apps")
    if (isServices || isApps) {
        apply(plugin = "com.gradleup.shadow")

        val copyDist by tasks.registering(Copy::class) {
            // Always include your resource dirs
            from(rootProject.layout.projectDirectory.dir("buildResources/docker"))
            from(rootProject.layout.projectDirectory.dir("buildResources/scripts"))

            // Copy jars built in this module
            from(layout.buildDirectory.dir("libs")) {
                include("*all.jar")
            }

            // Where to put them all
            into(rootProject.layout.buildDirectory.dir("libs"))
        }

        tasks.withType<ShadowJar>().configureEach {
            mergeServiceFiles()
            isZip64 = true
            if (isServices) {
                finalizedBy(copyDist)
            }
        }
    }

    tasks.withType<CycloneDxTask> {
        setIncludeConfigs(listOf("runtimeClasspath"))
        setSkipConfigs(listOf("compileClasspath", "testCompileClasspath"))
        setProjectType("application")
        setSchemaVersion("1.4")
        setDestination(project.file("build/reports"))
        setOutputName("bom")
        setOutputFormat("json")
        setIncludeBomSerialNumber(false)
        setComponentVersion("2.0.0")
    }

    tasks.withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
    }

    tasks.withType<KotlinCompile> {
        compilerOptions {
            freeCompilerArgs.add("-Xmulti-dollar-interpolation")
            freeCompilerArgs.add("-opt-in=kotlin.time.ExperimentalTime")
            jvmTarget.set(JvmTarget.JVM_17)
            allWarningsAsErrors = true
        }
    }

    tasks.withType<Detekt> {
        allRules = false
        buildUponDefaultConfig = true
        reports {
            xml.required.set(false)
            html.required.set(false)
            txt.required.set(false)
        }
        jvmTarget = "18"
        config.setFrom(file("$rootDir/detekt.yml"))
    }

    tasks.withType<Test> {
        maxParallelForks = 4
        failOnNoDiscoveredTests = false

        retry {
            if (isGitHubActionsBuild().get()) {
                maxRetries.set(5)
                maxFailures.set(40)
            }
            failOnPassedAfterRetry.set(false)
        }

        fun String.toBooleanRelaxed(): Boolean = when (trim().lowercase()) {
            "false", "0", "" -> false
            else -> true
        }

        val failFastProvider = providers.environmentVariable("FAIL_FAST").map { it.toBooleanRelaxed() }
        val failSlowProvider = providers.environmentVariable("FAIL_SLOW").map { it.toBooleanRelaxed() }
        failFast = failFastProvider.orElse(failSlowProvider.map { !it }).orElse(true).get()

        reports {
            junitXml.required.set(true)
        }

        testLogging {
            showCauses = true
            showExceptions = true
            showStackTraces = true
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
            if (isGitHubActionsBuild().get()) {
                events("PASSED", "SKIPPED", "FAILED")
            } else {
                events("FAILED")
            }
        }

        minHeapSize = "256m"
        // default maxHeapSize on gradle is 512m, lets increase to handle more demanding tests
        maxHeapSize = "6g"

        useJUnitPlatform()
        systemProperties(
            mapOf(
                Pair("junit.jupiter.execution.parallel.enabled", true),
                Pair("junit.jupiter.execution.parallel.mode.default", "concurrent"),
//                Pair("junit.jupiter.execution.parallel.mode.classes.default", "concurrent"),

                /*
                 * Supported timeout mode values:
                 * - enabled: enables timeouts
                 * - disabled: disables timeouts
                 * - disabled_on_debug: disables timeouts while debugging
                 */
                Pair("junit.jupiter.execution.timeout.mode", "disabled_on_debug"),

                /*
                 * Default test timeout.
                 * Use @Timeout() on test classes or tests to override.
                 */
                Pair("junit.jupiter.execution.timeout.default", "3m"),

                Pair("junit.jupiter.execution.parallel.config.strategy", "fixed"),

//                Pair("junit.jupiter.execution.parallel.config.dynamic.factor", ".6"),
//                Pair("junit.jupiter.execution.parallel.config.dynamic.max-pool-size-factor", "1"),
//                Pair("junit.jupiter.execution.parallel.config.fixed.saturate", "false"),
                Pair("junit.jupiter.execution.parallel.config.fixed.max-pool-size", "8"),
                Pair("junit.jupiter.execution.parallel.config.fixed.parallelism", "8"),
            ),
        )
    }

    tasks.whenTaskAdded {
        // https://github.com/johnrengelman/shadow/issues/153
        if (listOf(
                "shadowDistZip",
                "shadowDistTar",
                "distZip",
                "distTar",
                "startShadowScripts",
                "graphqlGenerateTestClient",
            ).contains(this.name)
        ) {
            this.enabled = false
        }
        // shadowJar with zip64 produces massive jars that affect caching performance
        if (listOf("shadowJar").contains(this.name)) {
            outputs.cacheIf { false }
            if (isPullRequestBuild().get()) {
                enabled = false
            }
        }
    }
}

subprojects {
    // TODO: Remove this block once KTOR has upgraded to 1.6.0
    // https://youtrack.jetbrains.com/issue/KTOR-5987/Regression-with-Json-ContentNegotation-using-list-of-types-that-we-have-contextual-adapters-for
    val kotlinxSerializationVersion = rootProject.libs.kotlinx.serialization.json.get().version ?: error("No kotlin serialization version found")
    configurations.all {
        resolutionStrategy.force("org.jetbrains.kotlinx:kotlinx-serialization-core:$kotlinxSerializationVersion")
        resolutionStrategy.force("org.jetbrains.kotlinx:kotlinx-serialization-json:$kotlinxSerializationVersion")
    }

    // TODO: Remove this block once pinecone-client has been upgraded
    val grpcVersion = rootProject.libs.grpc.netty.shaded.get().version ?: error("No grpc netty version found")
    configurations.all {
        // Force all gRPC artifacts to the same version to avoid conflicts
        resolutionStrategy.force("io.grpc:grpc-netty:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-netty-shaded:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-core:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-protobuf:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-stub:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-services:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-api:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-context:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-util:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-protobuf-lite:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-inprocess:$grpcVersion")
        resolutionStrategy.force("io.grpc:grpc-kotlin-stub:${rootProject.libs.grpc.kotlin.stub.get().version}")
    }

    // TODO: Hack, the ktor test libraries dependency on jupiter 5.12.2 is leading to runtime classpath issues.
    // Something is causing a mismatch between gradle's junit and some classes recently added to 5.12.2
    val jupiterVersion = rootProject.testLibs.junit.jupiter.get().version ?: error("No jupiter version found")
    configurations.all {
        resolutionStrategy.force(
            "org.junit.jupiter:junit-jupiter:$jupiterVersion",
            "org.junit.jupiter:junit-jupiter-api:$jupiterVersion",
            "org.junit.jupiter:junit-jupiter-engine:$jupiterVersion",
            "org.junit.jupiter:junit-jupiter-platform:$jupiterVersion",
            "org.junit.jupiter:junit-jupiter-params:$jupiterVersion",
            "org.junit.jupiter:junit-jupiter-engine:$jupiterVersion",
            "org.junit.jupiter:junit-jupiter-launcher:$jupiterVersion",
        )
    }

//    // TODO: Remove this block once krpc has been upgraded
//    val kotlinxCoroutinesVersion =
//        rootProject.libs.kotlinx.coroutines.debug.get().version ?: error("No kotlinx coroutines version found")
//    configurations.all {
//        resolutionStrategy.force("org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinxCoroutinesVersion")
//        resolutionStrategy.force("org.jetbrains.kotlinx:kotlinx-coroutines-debug:$kotlinxCoroutinesVersion")
//        resolutionStrategy.force("org.jetbrains.kotlinx:kotlinx-coroutines-reactive:$kotlinxCoroutinesVersion")
//    }

    repositories {
        mavenCentral()
        google()
        maven { url = uri("https://packages.atlassian.com/maven-public") }
        maven { url = uri("${rootProject.projectDir.absolutePath}/.maven") }
    }
}

node {
    download.set(false)
}

tasks.register("installNodeDependencies") {
    dependsOn(tasks.withType<NpmInstallTask>())
}
