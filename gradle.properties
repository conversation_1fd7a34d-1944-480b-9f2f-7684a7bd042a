kotlin.code.style=official
kotlin.stdlib.default.dependency=true
org.gradle.caching=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.configuration-cache=true

# https://github.com/gradle/gradle/issues/19750
# https://developer.android.com/build/optimize-your-build
org.gradle.jvmargs=-Xmx7g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseParallelGC -XX:MaxMetaspaceSize=2g

#### WARNING #####
# Do not add version properties to this file!
# Use the new canonical pattern via version catalogs.
# All version properties and dependencies are now stored in:
# ${rootProject.projectDir}/gradle/libs.versions.toml
# ${rootProject.projectDir}/gradle/testLibs.versions.toml
#
# For more reading:
# https://docs.gradle.org/current/userguide/platforms.html
##################
