flowchart TB

    Bitbucket[Bitbu<PERSON> Webhook]
    Bitbucket --> HooksApi

    Buildkite[Buildkite Webhook]
    Buildkite --> HooksApi

    CircleCi[CircleCi Webhook]
    CircleCi --> HooksApi

    GitHubWebhooks[GitHub Webhook]
    GitHubWebhooks --> HooksApi

    GitLabWebhooks[GitLab Webhook]
    GitLabWebhooks --> HooksApi

    subgraph queue: hooks_ci
        HooksApi --> CiWebhookHandler{{CiWebhookHandler}}

        CiWebhookHandler --> BitbucketWebhookHandler
        CiWebhookHandler --> BuildkiteWebhookHandler
        CiWebhookHandler --> CircleCiWebhookHandler
        CiWebhookHandler --> GitHubWebhookHandler
        CiWebhookHandler --> GitLabWebhookHandler

        BitbucketWebhookHandler --> BitbucketBuildEventHandler --> BuildIngestionService_onBuild
        BuildkiteWebhookHandler --> BuildkiteBuildEventHandler --> BuildIngestionService_onBuild
        CircleCiWebhookHandler --> CircleCiWorkflowEventHandler --> BuildIngestionService_onBuild
        GitHubWebhookHandler --> GitHubCheckSuiteEventHandler --> BuildIngestionService_onBuild
        GitLabWebhookHandler --> GitLabPipelineEventHandler --> BuildIngestionService_onBuild

        subgraph  BuildIngestionService
            BuildIngestionService_onBuild[[ingestBuild]]
            buildProcessService[[processBuild]]

            BuildIngestionService_onBuild ---> buildProcessService
        end
    end

    subgraph queue: ci_triages
        TriageRequest[[TriageRequest]]
        TriageExecute[[TriageExecute]]
        TriagePublish[[TriagePublish]]
        TriageBilling[[TriageBilling]]


        subgraph PROD
            BuildTriageService
        end

        subgraph DEV
            TriageExecute -->|on next step| TriageExecute
        end

        %% inputs
        TriageRequest --> TriageExecute
        TriageRequest --> BuildTriageService


        %% outputs
        BuildTriageService  -->|if Visible| TriagePublish
        buildProcessService -->|if Success| TriagePublish
        buildProcessService -->|if Failure| TriageRequest

        TriageExecute -->|on complete| TriagePublish
        TriagePublish -->|on publish| TriageBilling
    end

    TriagePublish ---->|on triage| SCM
