flowchart
    0{<i>MaybeBotQuestion?</i><br>AGENT} -- Yes --> A[QueryCompression<br>AGENT]
    A --> B[InitRepoContextualization]
    B --> C[MaliciousQueryDetection<br>AGENT]
    B --> D[RepoExtraction<br>AGENT]
    C --> E{<i>isMalicious?</i>}
    D --> E
    E -- No --> F[MLFunctionExecution<br>AGENT]
    E -- No --> documentStepChain
    F --> G[ConnectedRepoState]
    documentStepChain --> G
    G --> H[CompilePrompt]
    H --> I[FlowCompletion<br>AGENT]
    I --> postInferenceChain

    subgraph documentStepChain
        direction TB
        a[QueryEmbedding] --> b[ExtractLocalDocuments]
        b --> c[DocumentRerank]
        c --> DocumentRetrievalStep
        DocumentRetrievalStep --> e[DocumentListMerge]
        e --> f[DocumentAgeDecay]
        f --> g[DocumentSort]
    end

    subgraph postInferenceChain
        direction LR
        J[ReferencesResolver] --> K[ResponseSanitization]
        K --> L[InlineReferencesResolver]
        L --> M[ResultGeneration]
        M --> O[FollowupSuggestionsEnqueue]
    end

    subgraph DocumentRetrievalStep
        direction RL
        semanticDocumentRetriever --> agr1[documentRelevanceEvaluator<br>AGENT]
        agr1 -- Not Enough<br>Documents --> semanticDocumentRetriever
    end

    subgraph semanticDocumentRetriever
        direction TB
        sdr1[getRepoAccessForOrg] --> sdr2[findRepoFocus]
        sdr2 --> sdr3[buildDeepDocumentSets]
        sdr3 --> queryDocSet
        sdr3 --> queryDocSet
        sdr3 --> queryDocSet
        sdr3 --> queryDocSet
        sdr3 --> queryDocSet
        sdr3 --> queryDocSet
        queryDocSet --> sdr4[ageDecayDocumentScore]
    end

    subgraph queryDocSet
        direction TB
        ds1[semanticSearchDocumentService] --> ds2[pgSearchDocumentService]
        ds2 --> ds3[rerankDocuments]
    end

    style documentStepChain fill: #69D2
    style postInferenceChain fill: #69D2
    style DocumentRetrievalStep fill: #69D6
    style semanticDocumentRetriever fill: #69Da
    style queryDocSet fill: #69Df

    classDef agent fill: #f88
    class 0 agent
    class A agent
    class C agent
    class D agent
    class F agent
    class I agent
    class agr1 agent
