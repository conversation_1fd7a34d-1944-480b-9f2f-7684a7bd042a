@startuml
digraph GraphName {
    node [color = "pink", style="filled"]
    "lib-api" -> "lib-access"
    "lib-api" -> "lib-api-generated"
    "lib-api" -> "lib-api-model"
    "lib-api" -> "lib-aws"
    "lib-api" -> "lib-cache"
    "lib-api" -> "lib-common"
    "lib-api" -> "lib-expert-events"
    "lib-api" -> "lib-folder-data"
    "lib-api" -> "lib-intercom"
    "lib-api" -> "lib-log-kotlin"
    "lib-api" -> "lib-maintenance"
    "lib-api" -> "lib-notification"
    "lib-api" -> "lib-recommendation"
    "lib-api" -> "lib-scm"
    "lib-api" -> "lib-search"
    "lib-api" -> "lib-search-events"
    "lib-api" -> "lib-search-semantic"
    "lib-api" -> "lib-slack"
    "lib-api" -> "lib-topic-events"
    "lib-api" -> "lib-transcription"
    "lib-api" -> "lib-user-secret"
    "lib-api-generated" -> "lib-config"
    "lib-api-generated" -> "lib-ktor"
    "lib-api-generated" -> "lib-log-kotlin"
    "lib-api-integration" -> "lib-api-generated"
    "lib-api-integration" -> "lib-jira-auth"
    "lib-api-integration" -> "lib-slack-auth"
    "lib-api-model" -> "lib-common"
    "lib-api-model" -> "lib-notification"
    "lib-api-model" -> "lib-search"
    "lib-asset" -> "lib-aws"
    "lib-asset" -> "lib-common"
    "lib-auth" -> "lib-crypto"
    "lib-auth-provider" -> "lib-api-model"
    "lib-auth-provider" -> "lib-auth"
    "lib-auth-secret" -> "lib-auth"
    "lib-auth-secret" -> "lib-log-kotlin"
    "lib-auth-secret" -> "lib-user-secret"
    "lib-aws" -> "lib-cache"
    "lib-aws" -> "lib-log-kotlin"
    "lib-client-api-generated" -> "lib-config"
    "lib-client-api-generated" -> "lib-log-kotlin"
    "lib-common" -> "lib-config"
    "lib-common" -> "lib-kotlin"
    "lib-compress" -> "lib-common"
    "lib-config" -> "lib-network"
    "lib-crypto" -> "lib-common"
    "lib-embedding" -> "lib-log"
    "lib-environment" -> "lib-config"
    "lib-event-queue" -> "lib-api-generated"
    "lib-event-queue" -> "lib-aws"
    "lib-event-queue" -> "lib-compress"
    "lib-expert" -> "lib-log"
    "lib-expert-events" -> "lib-event-queue"
    "lib-expert-events" -> "lib-insight"
    "lib-expert-events" -> "lib-log"
    "lib-expert-events" -> "lib-ml"
    "lib-expert-events" -> "lib-search"
    "lib-expert-events" -> "lib-search-semantic"
    "lib-expert-events" -> "lib-topic"
    "lib-expert-events" -> "lib-topic-ml"
    "lib-folder-data" -> "lib-aws"
    "lib-folder-data" -> "lib-event-queue"
    "lib-ghissues-ingestion" -> "lib-ingestion"
    "lib-ghissues-ingestion" -> "lib-maintenance"
    "lib-ghissues-ingestion" -> "lib-scm"
    "lib-ghissues-ingestion" -> "lib-scm-data"
    "lib-git" -> "lib-common"
    "lib-git" -> "lib-log"
    "lib-git" -> "lib-log-kotlin"
    "lib-graphql" -> "lib-api-generated"
    "lib-ingestion" -> "lib-api"
    "lib-ingestion" -> "lib-common"
    "lib-ingestion" -> "lib-emoji"
    "lib-ingestion" -> "lib-environment"
    "lib-ingestion" -> "lib-log-kotlin"
    "lib-insight" -> "lib-common"
    "lib-insight" -> "lib-identity"
    "lib-intercom-service" -> "lib-common"
    "lib-intercom-service" -> "lib-insider"
    "lib-intercom-service" -> "lib-intercom"
    "lib-jira" -> "lib-common"
    "lib-jira" -> "lib-user-secret"
    "lib-jira-auth" -> "lib-auth"
    "lib-jira-auth" -> "lib-auth-provider"
    "lib-jira-auth" -> "lib-jira-events"
    "lib-jira-auth" -> "lib-jira-ingestion"
    "lib-jira-events" -> "lib-common"
    "lib-jira-events" -> "lib-event-queue"
    "lib-jira-events" -> "lib-jira"
    "lib-jira-events" -> "lib-jira-ingestion"
    "lib-jira-events" -> "lib-user-secret"
    "lib-jira-ingestion" -> "lib-ingestion"
    "lib-job" -> "lib-api-generated"
    "lib-job" -> "lib-common"
    "lib-job" -> "lib-event-queue"
    "lib-ktor" -> "lib-common"
    "lib-linear" -> "lib-cache"
    "lib-linear-data" -> "lib-aws"
    "lib-linear-data" -> "lib-event-queue"
    "lib-linear-data" -> "lib-linear"
    "lib-linear-data" -> "lib-log"
    "lib-linear-ingestion" -> "lib-ingestion"
    "lib-linear-ingestion" -> "lib-linear"
    "lib-linear-ingestion" -> "lib-linear-data"
    "lib-log" -> "lib-config"
    "lib-log-kotlin" -> "lib-log"
    "lib-lucene" -> "lib-slack-extractor"
    "lib-maintenance" -> "lib-access"
    "lib-maintenance" -> "lib-client-config"
    "lib-maintenance" -> "lib-common"
    "lib-maintenance" -> "lib-embedding"
    "lib-maintenance" -> "lib-identity"
    "lib-maintenance" -> "lib-slack"
    "lib-markdown" -> "lib-common"
    "lib-markdown" -> "lib-proto-generated"
    "lib-ml" -> "lib-search-events"
    "lib-notification" -> "lib-aws"
    "lib-notification" -> "lib-common"
    "lib-notification" -> "lib-environment"
    "lib-notification" -> "lib-event-queue"
    "lib-notification" -> "lib-log-kotlin"
    "lib-notification" -> "lib-markdown"
    "lib-notification" -> "lib-sendinblue"
    "lib-pr" -> "lib-common"
    "lib-pr" -> "lib-config"
    "lib-pr" -> "lib-log"
    "lib-pr-ingestion" -> "lib-event-queue"
    "lib-pr-ingestion" -> "lib-ingestion"
    "lib-pr-ingestion" -> "lib-maintenance"
    "lib-pr-ingestion" -> "lib-recommendation"
    "lib-pr-ingestion" -> "lib-scm"
    "lib-pr-ingestion" -> "lib-scm-data"
    "lib-pr-ingestion" -> "lib-search"
    "lib-pr-ingestion" -> "lib-slack-ingestion"
    "lib-pr-ingestion" -> "lib-topic-ingestion"
    "lib-pr-ingestion" -> "lib-user-secret"
    "lib-pr-summary-ingestion" -> "lib-event-queue"
    "lib-pr-summary-ingestion" -> "lib-ingestion"
    "lib-pr-summary-ingestion" -> "lib-pr"
    "lib-pr-summary-ingestion" -> "lib-search"
    "lib-qa-generation" -> "lib-common"
    "lib-qa-generation" -> "lib-event-queue"
    "lib-qa-generation" -> "lib-ml"
    "lib-recommendation" -> "lib-common"
    "lib-recommendation" -> "lib-insider"
    "lib-recommendation" -> "lib-log-kotlin"
    "lib-scm" -> "lib-common"
    "lib-scm" -> "lib-event-queue"
    "lib-scm" -> "lib-identity"
    "lib-scm" -> "lib-log-kotlin"
    "lib-scm" -> "lib-user-secret"
    "lib-scm-data" -> "lib-aws"
    "lib-scm-data" -> "lib-event-queue"
    "lib-scm-data" -> "lib-log"
    "lib-scm-data" -> "lib-scm"
    "lib-search" -> "lib-aws"
    "lib-search" -> "lib-common"
    "lib-search" -> "lib-embedding"
    "lib-search" -> "lib-event-queue"
    "lib-search" -> "lib-insight"
    "lib-search" -> "lib-markdown"
    "lib-search" -> "lib-ml"
    "lib-search" -> "lib-topic-ingestion"
    "lib-search" -> "lib-topic-insight"
    "lib-search-events" -> "lib-common"
    "lib-search-events" -> "lib-event-queue"
    "lib-search-semantic" -> "lib-event-queue"
    "lib-search-semantic" -> "lib-insight"
    "lib-search-semantic" -> "lib-log"
    "lib-search-semantic" -> "lib-ml"
    "lib-search-semantic" -> "lib-search"
    "lib-search-semantic" -> "lib-search-events"
    "lib-search-semantic" -> "lib-slack"
    "lib-search-semantic" -> "lib-topic-events"
    "lib-security" -> "lib-common"
    "lib-security" -> "lib-ktor"
    "lib-security" -> "lib-log"
    "lib-security" -> "lib-log-kotlin"
    "lib-sendinblue" -> "lib-log-kotlin"
    "lib-sendinblue-webhook" -> "lib-event-queue"
    "lib-sendinblue-webhook" -> "lib-log-kotlin"
    "lib-sendinblue-webhook" -> "lib-notification"
    "lib-service" -> "lib-common"
    "lib-service" -> "lib-insider"
    "lib-service" -> "lib-log-kotlin"
    "lib-service" -> "lib-trace-service"
    "lib-service-bootstrap" -> "lib-log-kotlin"
    "lib-service-bootstrap" -> "lib-service"
    "lib-service-bootstrap" -> "lib-service-grpc"
    "lib-service-bootstrap" -> "lib-trace-service"
    "lib-service-grpc" -> "lib-security"
    "lib-service-grpc" -> "lib-service"
    "lib-slack" -> "lib-cache"
    "lib-slack" -> "lib-insider"
    "lib-slack" -> "lib-user-secret"
    "lib-slack-auth" -> "lib-auth"
    "lib-slack-auth" -> "lib-auth-provider"
    "lib-slack-auth" -> "lib-slack"
    "lib-slack-auth" -> "lib-slack-ingestion"
    "lib-slack-bot" -> "lib-api"
    "lib-slack-bot" -> "lib-event-queue"
    "lib-slack-bot" -> "lib-search-semantic"
    "lib-slack-command" -> "lib-api"
    "lib-slack-command" -> "lib-event-queue"
    "lib-slack-command" -> "lib-search-semantic"
    "lib-slack-data" -> "lib-aws"
    "lib-slack-data" -> "lib-event-queue"
    "lib-slack-data" -> "lib-slack"
    "lib-slack-extractor" -> "lib-slack"
    "lib-slack-ingestion" -> "lib-auth"
    "lib-slack-ingestion" -> "lib-ingestion"
    "lib-slack-ingestion" -> "lib-slack"
    "lib-slack-ingestion" -> "lib-slack-data"
    "lib-slack-webhook" -> "lib-event-queue"
    "lib-slack-webhook" -> "lib-slack"
    "lib-slack-webhook" -> "lib-slack-bot"
    "lib-slack-webhook" -> "lib-slack-ingestion"
    "lib-sourcecode" -> "lib-aws"
    "lib-sourcecode" -> "lib-cache"
    "lib-sourcecode" -> "lib-log"
    "lib-sourcecode-ingestion" -> "lib-event-queue"
    "lib-sourcecode-ingestion" -> "lib-log"
    "lib-sourcecode-ingestion" -> "lib-sourcecode"
    "lib-sourcemark" -> "lib-aws"
    "lib-sourcemark" -> "lib-common"
    "lib-sourcemark" -> "lib-job"
    "lib-sourcemark" -> "lib-security"
    "lib-topic" -> "lib-aws"
    "lib-topic" -> "lib-cache"
    "lib-topic" -> "lib-expert"
    "lib-topic" -> "lib-log"
    "lib-topic-events" -> "lib-common"
    "lib-topic-events" -> "lib-event-queue"
    "lib-topic-ingestion" -> "lib-event-queue"
    "lib-topic-ingestion" -> "lib-log"
    "lib-topic-ingestion" -> "lib-topic"
    "lib-topic-ingestion" -> "lib-topic-events"
    "lib-topic-ingestion" -> "lib-topic-insight"
    "lib-topic-insight" -> "lib-insight"
    "lib-topic-insight" -> "lib-log"
    "lib-topic-insight" -> "lib-topic"
    "lib-topic-insight" -> "lib-topic-events"
    "lib-topic-insight" -> "lib-topic-ml"
    "lib-topic-metric" -> "lib-log"
    "lib-topic-metric" -> "lib-topic"
    "lib-topic-metric" -> "lib-topic-events"
    "lib-topic-ml" -> "lib-insight"
    "lib-topic-ml" -> "lib-log"
    "lib-topic-ml" -> "lib-ml"
    "lib-topic-ml" -> "lib-topic"
    "lib-topic-summary" -> "lib-insight"
    "lib-topic-summary" -> "lib-log"
    "lib-topic-summary" -> "lib-ml"
    "lib-topic-summary" -> "lib-search"
    "lib-topic-summary" -> "lib-search-semantic"
    "lib-topic-summary" -> "lib-topic"
    "lib-topic-summary" -> "lib-topic-ml"
    "lib-trace" -> "lib-log"
    "lib-trace-jdbc" -> "lib-trace"
    "lib-trace-ktor" -> "lib-common"
    "lib-trace-ktor" -> "lib-ktor"
    "lib-trace-ktor" -> "lib-trace"
    "lib-trace-redis" -> "lib-trace"
    "lib-trace-service" -> "lib-trace"
    "lib-transcription" -> "lib-asset"
    "lib-transcription" -> "lib-common"
    "lib-transcription" -> "lib-event-queue"
    "lib-transcription" -> "lib-log-kotlin"
    "lib-transcription" -> "lib-search"
    "lib-transcription" -> "lib-search-events"
    "lib-user-engagement" -> "lib-common"
    "lib-user-engagement" -> "lib-insider"
    "lib-user-secret" -> "lib-auth"
    "lib-user-secret" -> "lib-crypto"
    "lib-versions" -> "lib-aws"
    "lib-versions" -> "lib-common"
    "lib-video" -> "lib-common"
    "lib-video-transcoder" -> "lib-common"
    "lib-video-transcoder" -> "lib-event-queue"
}
@enduml
