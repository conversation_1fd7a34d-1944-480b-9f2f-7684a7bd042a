@startuml

autonumber

actor "Native Client" as client
actor <PERSON><PERSON><PERSON> as browser
participant "Web Service" as web
participant "API Service" as api
participant "Identity Service" as identity
participant "GitHub API" as github_api
participant "GitHub Web Auth" as github_web

group Native
    client -> api ++ : GET apiservice.unblocked.com/preauth
    api -> identity ++ : create and store secret, associate with token
    return OK
    return: OK {token: exchange_token, secret: client_secret}
    note over client
        begin polling apiservice for jwt
    end note
    client -> api ++ : OPEN apiservice.unblocked.com/login/options?client-secret=<secret>&client-type=<type>
    return OK {providers: [{provider:github, oauthUrl:apiservice.unblocked.com/login?provider=github&client-secret=<secret>&client-type=<type>}]}
    client -> browser : apiservice.unblocked.com/login?provider=github&client-secret=<secret>&client-type=<type>

    group Browser
        browser -> api ++ : GET apiservice.unblocked.com/login/github?provider=github&client-secret=<secret>&client-type=<type>
        api -> identity ++ : create and store temp nonce & associate with secret, client type, and repoUrl
        return OK
        return: REDIRECT github.com/login/oauth/authorize?state=<nonce>&client_id=<id>&redirectUri=webservice.unblocked.com/login/exchange\nSet-Cookie: signed-secret=<secret>; Secure; HttpOnly; SameSite=Strict

        browser -> github_web ++ : GET github.com/login/oauth/authorize?state=<nonce>&client_id=<id>&redirectUri=webservice.unblocked.com/exchange
        return: REDIRECT webservice.unblocked.com/login/exchange?state=<nonce>;code=<exchange_code>

        browser -> web ++ : GET webservice.unblocked.com/login/exchange?state=<nonce>;code=<exchange_code>
        return OK web-app loads, pulls params

        browser -> api ++ : GET apiservice.unblocked.com/login/exchange?state=nonce&code=exchange
        api -> identity ++: GET identity.unblocked.com/login/exchange {state=nonce; code=exchange}
        identity -> github_api ++ : POST github.com/login/oauth/access_token?state=<nonce>;code<exchange_code>
        return : OK { access_token:<token> }

        identity -> github_api ++ : GET github.com/user (Grab email, id, and avatar info)
        return : OK Return user info
        identity -> identity : validate or create user, update identity
        identity -> identity : match nonce to secret, store authentication status for user against secret\n discard all previous associations for user
        identity -> identity : Create signed jwt for user, discard github access token
        return : OK: {token: jwt}
        return : OK: {token: jwt}
    end

    note over client
        finish polling for token
    end note
    client -> api ++ : GET apiservice.unblocked.com/preauth/exchange Authorization: Bearer <token>
    api -> identity ++ : GET identity.unblocked.com/preauth/exchange Authorization: Bearer <token>
    identity -> identity : look up secret to user association, create jwt
    return: OK {token: jwt}
    return: OK {token: jwt}
    client -> api ++ : /sourceMark Authorization: bearer <jwt>
    api -> api : validate jwt signature
    return: OK
end


@enduml
