@startuml
digraph GraphName {
    node [color = "pink", style="filled"]
    "lib-api" -> "lib-search"
    "lib-api" -> "lib-search-events"
    "lib-api" -> "lib-search-semantic"
    "lib-api-model" -> "lib-search"
    "lib-expert-events" -> "lib-search"
    "lib-expert-events" -> "lib-search-semantic"
    "lib-ml" -> "lib-search-events"
    "lib-pr-ingestion" -> "lib-search"
    "lib-pr-summary-ingestion" -> "lib-search"
    "lib-search" -> "lib-aws"
    "lib-search" -> "lib-common"
    "lib-search" -> "lib-embedding"
    "lib-search" -> "lib-event-queue"
    "lib-search" -> "lib-insight"
    "lib-search" -> "lib-markdown"
    "lib-search" -> "lib-ml"
    "lib-search" -> "lib-topic-ingestion"
    "lib-search" -> "lib-topic-insight"
    "lib-search-events" -> "lib-common"
    "lib-search-events" -> "lib-event-queue"
    "lib-search-semantic" -> "lib-event-queue"
    "lib-search-semantic" -> "lib-insight"
    "lib-search-semantic" -> "lib-log"
    "lib-search-semantic" -> "lib-ml"
    "lib-search-semantic" -> "lib-search"
    "lib-search-semantic" -> "lib-search-events"
    "lib-search-semantic" -> "lib-slack"
    "lib-search-semantic" -> "lib-topic-events"
    "lib-slack-bot" -> "lib-search-semantic"
    "lib-slack-command" -> "lib-search-semantic"
    "lib-topic-summary" -> "lib-search"
    "lib-topic-summary" -> "lib-search-semantic"
    "lib-transcription" -> "lib-search"
    "lib-transcription" -> "lib-search-events"
}
@enduml
