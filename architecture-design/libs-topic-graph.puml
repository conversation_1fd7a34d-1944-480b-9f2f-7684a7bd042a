@startuml
digraph GraphName {
    node [color = "pink", style="filled"]
    "lib-api" -> "lib-topic-events"
    "lib-expert-events" -> "lib-topic"
    "lib-expert-events" -> "lib-topic-ml"
    "lib-pr-ingestion" -> "lib-topic-ingestion"
    "lib-search" -> "lib-topic-ingestion"
    "lib-search" -> "lib-topic-insight"
    "lib-search-semantic" -> "lib-topic-events"
    "lib-topic" -> "lib-aws"
    "lib-topic" -> "lib-cache"
    "lib-topic" -> "lib-expert"
    "lib-topic" -> "lib-log"
    "lib-topic-events" -> "lib-common"
    "lib-topic-events" -> "lib-event-queue"
    "lib-topic-ingestion" -> "lib-event-queue"
    "lib-topic-ingestion" -> "lib-log"
    "lib-topic-ingestion" -> "lib-topic"
    "lib-topic-ingestion" -> "lib-topic-events"
    "lib-topic-ingestion" -> "lib-topic-insight"
    "lib-topic-insight" -> "lib-insight"
    "lib-topic-insight" -> "lib-log"
    "lib-topic-insight" -> "lib-topic"
    "lib-topic-insight" -> "lib-topic-events"
    "lib-topic-insight" -> "lib-topic-ml"
    "lib-topic-metric" -> "lib-log"
    "lib-topic-metric" -> "lib-topic"
    "lib-topic-metric" -> "lib-topic-events"
    "lib-topic-ml" -> "lib-insight"
    "lib-topic-ml" -> "lib-log"
    "lib-topic-ml" -> "lib-ml"
    "lib-topic-ml" -> "lib-topic"
    "lib-topic-summary" -> "lib-insight"
    "lib-topic-summary" -> "lib-log"
    "lib-topic-summary" -> "lib-ml"
    "lib-topic-summary" -> "lib-search"
    "lib-topic-summary" -> "lib-search-semantic"
    "lib-topic-summary" -> "lib-topic"
    "lib-topic-summary" -> "lib-topic-ml"
}
@enduml
