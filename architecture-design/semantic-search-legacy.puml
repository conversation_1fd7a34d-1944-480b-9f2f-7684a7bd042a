@startuml
!theme aws-orange
hide footbox
skinparam backgroundColor #FFF

title Semantic Search Request\n[ Legacy Design ]

actor user
participant Client as client

box "Internal Services" #LightBlue
participant "<b>API</b>\nKotlin Service" as api
participant "<b>HuggingFace</b>\nAWS SageMaker\n[hf.api]" as hf
participant "<b>MachineLearning</b>\nAWS SageMaker\n[ml.api]" as ml
database "ChromaDB" as chroma
end box

box "External Services" #Pink
database "Pinecone" as pinecone
participant "<b>OpenAI</b>" as openai
participant "<b>Anthropic</b>" as anthropic
end box


'-------------------------------------------------------
user -> client ++ : question


'-------------------------------------------------------
client -> api ++: Semantic search request
note over client
    ""POST /search/semantic""
    ""POST /threadsV3/{threadId}""
    ""POST /slackbot/semanticSearchCommand""
end note


    '-------------------------------------------------------
    autonumber
    autonumber resume
    api -> hf ++ : ""question""
    autonumber stop
    note right hf
        Generate ""vector"" embedding
        for the ""question""
    end note
    return ""vector""


    '-------------------------------------------------------
    autonumber resume
    api -> pinecone++ : ""{vector}""
    autonumber stop
    note right pinecone
        Gather related thread
        and PR documents
    end note
    return ""documents""


    '-------------------------------------------------------
    autonumber resume
    api -> ml++ : ""{query, documents}""
    autonumber stop


        '-------------------------------------------------------
        autonumber resume
        ml -> chroma++ : ""{vector}""
        autonumber stop
        note right chroma
            Gather related
            source code files
        end note
        return ""files""


        '-------------------------------------------------------
        autonumber resume
        ml -> openai++ : query with documents and files embedded
        autonumber stop
        note over openai
            Generate
            prompt
        end note
        return ""prompt""


        '-------------------------------------------------------
        autonumber resume
        ml -> anthropic++ : prompt
        autonumber stop
        note right anthropic
            Generate
            completion
        end note
        return ""completion""


    return ""completion""


return OK ""{completion, documents}""

@enduml
