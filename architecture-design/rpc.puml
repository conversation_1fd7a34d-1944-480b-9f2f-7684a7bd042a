@startuml
digraph RPC {

    subgraph clusterPublic {
        label="Public"
        style=dotted

        ApiService
        AssetService
        'AuthService
        'PublicApiService
        'PusherService
        'TelemetryService
        'WebhookService

        subgraph clusterPublicVpn {
            label="(VPN)"
            style=dotted
            AdminWebService
        }
    }

    subgraph clusterInternalRealtime {
        label="Internal RT"
        style=dotted
        ProxyProvider
        'SearchService
        'SlackService
    }

    subgraph clusterInternalBatch {
        label="Internal Batch"
        style=dotted
        'DataService
        'EmbeddingService
        'IndexService
        'IngestConfluence
        'IngestGoogle
        'IngestJira
        'IngestLinear
        'IngestNotion
        'IngestSlack
        'IngestStackOverflow
        'IngestWeb
        'NotificationService
        'ScmService
        'SourceCodeService
        'TopicService
        'TranscriptionService
        'VideoService
    }

    'RPC
    rankdir=RL
    edge [ dir=back ]

    'ProxyProvider for AdminWebService
    ProxyProvider -> AdminWebService [label="echo"]

    'ProxyProvider for AssetService
    ProxyProvider -> AssetService [label="authorizeAssets"]

    'ProxyProvider for ApiService
    ProxyProvider -> ApiService [label="getTeamScmInstallation"]
    ProxyProvider -> ApiService [label="IsRepoValid"]
}
@enduml
