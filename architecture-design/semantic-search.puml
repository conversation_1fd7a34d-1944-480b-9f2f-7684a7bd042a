@startuml
!theme aws-orange
hide footbox
skinparam backgroundColor #FFF

title Semantic Search Request\n[ Updated Design ]

actor user
participant Client as client

box "Internal Services" #LightBlue
participant "<b>API</b>\nKotlin Service" as api
queue "ActiveMQ" as q
database "Postgres" as db
participant "<b>Search</b>\nKotlin Service" as search
participant "<b>HuggingFace</b>\nAWS SageMaker\n[hf.api]" as hf
participant "<b>MosaicML</b>\nAWS SageMaker\n[ml.api]" as mosaic
end box

box "External Services" #Pink
database "Pinecone" as pinecone
participant "<b>OpenAI</b>" as openai
participant "<b>Anthropic</b>" as anthropic
end box


'-------------------------------------------------------
user -> client ++ : question


'-------------------------------------------------------
client -> api ++: Create thread or message
note over client
    ""POST /threads/{threadId}""
    ""POST /messages/{messageId}""
end note


    '-------------------------------------------------------
    autonumber
    autonumber resume
    api -> db ++ : Persist Message/Thread models
    autonumber stop
    return


    '-------------------------------------------------------
    autonumber resume
    api -> q ++ : Conditionally, enqueue search request
    autonumber stop
    return
    return OK ""{thread}"" or ""{message}""


    '-------------------------------------------------------
    autonumber resume
    q -> search ++ : ""{question, threadId, messageId}""
    q++
    autonumber stop
    search -> hf ++ : ""question""
    note right hf
        Generate ""vector"" embedding
        for the ""question""
    end note
    return ""vector""


    '-------------------------------------------------------
    autonumber resume
    search -> pinecone ++ : ""{vector}""
    autonumber stop
    note right pinecone
        Gather related
        source code files,
        thread documents,
        PR documents
    end note
    return ""{files: [...], documents: [...]}""


    '-------------------------------------------------------
    autonumber resume
    search -> db ++ : Get prompt template
    autonumber stop
    return ""template""


    '-------------------------------------------------------
    autonumber resume
    search --> search ++: Construct prompt with\n""{files: [...], documents: [...]}""
    autonumber stop
    search --


    '-------------------------------------------------------
    note right search
        Generate AI completion using different inference engines
    end note
    opt MosaicML
        autonumber resume
        search -> mosaic ++ : prompt
        autonumber stop
        return ""completion""
    else OpenAI
        autonumber resume
        search -> openai ++ : prompt
        autonumber stop
        return ""completion""
    else Anthropic
        autonumber resume
        search -> anthropic ++ : prompt
        autonumber stop
        return ""completion""
    end


    '-------------------------------------------------------
    autonumber resume
    search -> db ++ : Persist message response
    autonumber stop
    return
    search--
    q--


client --> client--: Conditionally, poll\nmessage stream.


@enduml
