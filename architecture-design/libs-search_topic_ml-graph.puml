@startuml
digraph GraphName {
    node [color = "pink", style="filled"]
    "lib-api" -> "lib-search"
    "lib-api" -> "lib-search-events"
    "lib-api" -> "lib-search-semantic"
    "lib-api" -> "lib-topic-events"
    "lib-api-model" -> "lib-search"
    "lib-expert-events" -> "lib-ml"
    "lib-expert-events" -> "lib-search"
    "lib-expert-events" -> "lib-search-semantic"
    "lib-expert-events" -> "lib-topic"
    "lib-expert-events" -> "lib-topic-ml"
    "lib-ml" -> "lib-search-events"
    "lib-pr-ingestion" -> "lib-search"
    "lib-pr-ingestion" -> "lib-topic-ingestion"
    "lib-pr-summary-ingestion" -> "lib-search"
    "lib-qa-generation" -> "lib-ml"
    "lib-search" -> "lib-aws"
    "lib-search" -> "lib-common"
    "lib-search" -> "lib-embedding"
    "lib-search" -> "lib-event-queue"
    "lib-search" -> "lib-insight"
    "lib-search" -> "lib-markdown"
    "lib-search" -> "lib-ml"
    "lib-search" -> "lib-topic-ingestion"
    "lib-search" -> "lib-topic-insight"
    "lib-search-events" -> "lib-common"
    "lib-search-events" -> "lib-event-queue"
    "lib-search-semantic" -> "lib-event-queue"
    "lib-search-semantic" -> "lib-insight"
    "lib-search-semantic" -> "lib-log"
    "lib-search-semantic" -> "lib-ml"
    "lib-search-semantic" -> "lib-search"
    "lib-search-semantic" -> "lib-search-events"
    "lib-search-semantic" -> "lib-slack"
    "lib-search-semantic" -> "lib-topic-events"
    "lib-slack-bot" -> "lib-search-semantic"
    "lib-slack-command" -> "lib-search-semantic"
    "lib-topic" -> "lib-aws"
    "lib-topic" -> "lib-cache"
    "lib-topic" -> "lib-expert"
    "lib-topic" -> "lib-log"
    "lib-topic-events" -> "lib-common"
    "lib-topic-events" -> "lib-event-queue"
    "lib-topic-ingestion" -> "lib-event-queue"
    "lib-topic-ingestion" -> "lib-log"
    "lib-topic-ingestion" -> "lib-topic"
    "lib-topic-ingestion" -> "lib-topic-events"
    "lib-topic-ingestion" -> "lib-topic-insight"
    "lib-topic-insight" -> "lib-insight"
    "lib-topic-insight" -> "lib-log"
    "lib-topic-insight" -> "lib-topic"
    "lib-topic-insight" -> "lib-topic-events"
    "lib-topic-insight" -> "lib-topic-ml"
    "lib-topic-metric" -> "lib-log"
    "lib-topic-metric" -> "lib-topic"
    "lib-topic-metric" -> "lib-topic-events"
    "lib-topic-ml" -> "lib-insight"
    "lib-topic-ml" -> "lib-log"
    "lib-topic-ml" -> "lib-ml"
    "lib-topic-ml" -> "lib-topic"
    "lib-topic-summary" -> "lib-insight"
    "lib-topic-summary" -> "lib-log"
    "lib-topic-summary" -> "lib-ml"
    "lib-topic-summary" -> "lib-search"
    "lib-topic-summary" -> "lib-search-semantic"
    "lib-topic-summary" -> "lib-topic"
    "lib-topic-summary" -> "lib-topic-ml"
    "lib-transcription" -> "lib-search"
    "lib-transcription" -> "lib-search-events"
}
@enduml
