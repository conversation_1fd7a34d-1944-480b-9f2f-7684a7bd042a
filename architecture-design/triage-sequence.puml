@startuml

title "Triage Sequence"

participant WebhookService

queue HooksCi
participant CiWebhookHandler

queue CiEvent
participant BuildIngestionService
participant BuildProcessService
database PullRequestModel
database BuildModel
database BuildJobModel
database BuildTriageModel

queue CiTriages as CiPipeline
participant TriagePublishEventHandler

queue Temporal
participant CITriageWorkflow
participant CITriagePipelineWorkflow

participant GitHub
participant OrgBillingService

group webhooks
    --> WebhookService ++ : webhook
    WebhookService -> HooksCi : payload
    return accepted
end group

group on build event
    HooksCi --> CiWebhookHandler ++ : decode
        CiWebhookHandler -> CiEvent : BitbucketBuildEvent
        CiWebhookHandler -> CiEvent : BuildkiteBuildEvent
        CiWebhookHandler -> CiEvent : GitHubCheckSuiteEvent
    return
    CiEvent -> BuildIngestionService ++
        BuildIngestionService -> BuildIngestionService : is ciEnabled?
        BuildIngestionService -> BuildModel : upsert
        BuildIngestionService -> BuildJobModel : upsert(jobs)
        BuildIngestionService -> BuildProcessService ++ : processBuild
            alt onSuccess
                BuildProcessService -> BuildTriageModel : findTriageStateFor pullRequest
                BuildProcessService -> PullRequestModel : updateCiTriage
                BuildProcessService -> CiPipeline : TriagePublish
            end alt
            alt onFailure
                BuildProcessService -> Temporal : CITriageWorkflow
            end group
        return
    return
end group

group CITriageWorkflow
    Temporal -> CITriageWorkflow ++
        CITriageWorkflow -> CITriagePipelineWorkflow ++ : execute
        return
        alt isVisible
            CITriageWorkflow -> GitHub : pullRequestComment
            CITriageWorkflow -> CITriageWorkflow ++ : CITriageMeteringActivity
                alt when more than 5 comments
                    CITriageWorkflow -> OrgBillingService : assignSeat
                end alt
            return
        end alt
    return
end group

group TriagePublish
    CiPipeline -> TriagePublishEventHandler ++ : TriagePublish
        TriagePublishEventHandler -> GitHub : pullRequestComment
    return
end group

@enduml
