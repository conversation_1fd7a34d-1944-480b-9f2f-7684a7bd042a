services:
    activemq:
        build:
            context: ./activemq
            dockerfile: Dockerfile
        env_file:
            - ./docker/activemq/activemq.env
        volumes:
            - activemq-local-stack-data:/var/lib/activemq/data/
        hostname: activemq
        ports:
            - "61616:61616"
            - "61617:61617"
            - "8161:8161"

    aws:
        extends:
            file: docker-compose-local-aws.yml
            service: localstack

    mongo:
      image: mongo:8
      container_name: mongo
      restart: always
      ports:
        - "27017:27017"
      env_file:
        - ./docker/mongo/mongo.env
      volumes:
        - mongo-data:/data/db

    opensearch:
      profiles:
        - local
        - intellij
      image: opensearchproject/opensearch:latest
      env_file:
        - ./docker/opensearch/opensearch.env
      ports:
        - "9200:9200"  # OpenSearch REST API
        - "9600:9600"  # OpenSearch performance analyzer
      volumes:
        - opensearch-local-stack-data:/usr/share/opensearch/data
      restart: always

    opensearch-dashboards:
      profiles:
        - local
        - intellij
      image: opensearchproject/opensearch-dashboards:latest
      ports:
        - "5601:5601"
      env_file:
        - ./docker/opensearch/opensearch.dashboards.env
      depends_on:
        - opensearch

    postgres:
        image: "postgres:16.4" # use the same version as our AWS instance
        env_file:
            - ./docker/postgres/postgres.env
        command: postgres -c 'config_file=/etc/postgresql/postgresql.conf'
        volumes:
            - postgres-local-stack-data:/var/lib/postgresql/data/
            - ./docker/postgres/custom-postgresql.conf:/etc/postgresql/postgresql.conf
        hostname: postgres
        shm_size: '2gb'
        ports:
            - "5432:5432"

    postgres-ci:
      profiles:
        - local
        - intellij
      image: "postgres:16.4" # use the same version as our AWS instance
      env_file:
        - ./docker/postgres/postgres.env
      command: postgres -c 'config_file=/etc/postgresql/postgresql.conf'
      volumes:
        - ./docker/postgres/custom-postgresql.conf:/etc/postgresql/postgresql.conf
      hostname: postgres-ci
      shm_size: '2gb'
      ports:
        - "5433:5432"

    postgres-prefect:
      image: "postgres:16.4" # use the same version as our AWS instance
      restart: always
      env_file:
        - ./docker/postgres/postgres-prefect.env
      volumes:
        - postgres-prefect-data:/var/lib/postgresql/data
      ports:
        - "5434:5432"

    postgres-temporal:
      image: postgres:16.4
      env_file:
        - ./docker/postgres/postgres-temporal.env
      networks:
        - default
      ports:
        - "5435:5432"
      volumes:
        - postgres-temporal-data:/var/lib/postgresql/data

    prefect:
      restart: always
      image: prefecthq/prefect:3-python3.12
      env_file:
        - ./docker/prefect/prefect.env
      command: prefect server start --host 0.0.0.0
      ports:
        - "4200:4200"  # Prefect server UI and API
      volumes:
        - postgres-prefect-data:/data  # Persistent storage
        - .:/app  # Mount current directory
      depends_on:
        - postgres-prefect

    redis:
      image: redis:7.4-alpine
      volumes:
        # Mount the custom config read-only
        - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
        # Data directory for AOF / RDB files
        - redis-local-stack-data:/data
      command: >
        redis-server /usr/local/etc/redis/redis.conf
      hostname: redis
      ports:
        - "6379:6379"
      restart: always

    redoc:
        image: redocly/redoc
        env_file:
            - ./docker/redoc/redoc.env
        volumes:
            - ./api/:/usr/share/nginx/html/swagger/
        hostname: redoc
        ports:
            - "8088:80"
        platform: "linux/amd64"

    refinery:
        image: "honeycombio/refinery"
        env_file:
            - ~/.secrets/unblocked/local/refinery.env
        volumes:
            - ./docker/refinery/refinery-config.yaml:/etc/refinery/refinery.yaml:ro
            - ./docker/refinery/refinery-rules.yaml:/etc/refinery/rules.yaml:ro
        hostname: refinery
        ports:
            - "9091:8080"   # HTTP
            - "4317:4317"   # GRPC

    adminwebservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/adminwebservice/Dockerfile
        image: adminwebservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        environment:
          - SERVICE_PROXY_PROVIDER_HOST
          - SERVICE_PROXY_PROVIDER_PORT
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: adminwebservice
        ports:
            - "8086:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8086/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    apiservice:
      profiles:
        - local
      build:
        context: .
        dockerfile: projects/services/apiservice/Dockerfile
      image: apiservice
      platform: linux/amd64
      env_file:
        - ./docker/services.env
      volumes:
        - ~/.secrets:/root/.secrets:ro
        - ~/.secrets:/Users/<USER>
      hostname: apiservice
      ports:
        - "8081:8080"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:8081/api/__deepcheck" ]
        interval: 5s
        timeout: 5s
        retries: 5
        start_period: 30s
      depends_on:
        activemq:
          condition: service_started
        postgres:
          condition: service_started
        redis:
          condition: service_started
        aws:
          condition: service_healthy

    mcpservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/mcpservice/Dockerfile
        image: mcpservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: mcpservice
        ports:
            - "8094:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8094/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 5
            start_period: 30s
        depends_on:
            activemq:
                condition: service_started
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    assetservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/assetservice/Dockerfile
        image: assetservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: assetservice
        ports:
            - "8085:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8084/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    authservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/authservice/Dockerfile
        image: authservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: authservice
        ports:
            - "8093:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8093/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 5
            start_period: 30s
        depends_on:
            activemq:
                condition: service_started
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    billingservice:
      profiles:
        - local
      build:
        context: .
        dockerfile: projects/services/billingservice/Dockerfile
      image: billingservice
      platform: linux/amd64
      env_file:
        - ./docker/services.env
      volumes:
        - ~/.secrets:/root/.secrets:ro
        - ~/.secrets:/Users/<USER>
      hostname: billingservice
      ports:
        - "8120:8080"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:8120/api/__deepcheck" ]
        interval: 5s
        timeout: 5s
        retries: 3
        start_period: 30s
      depends_on:
        aws:
          condition: service_healthy

    dataservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/dataservice/Dockerfile
        image: dataservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: dataservice
        ports:
            - "8102:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8102/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            aws:
                condition: service_healthy

    embeddingservice:
      profiles:
        - local
      build:
        context: .
        dockerfile: projects/services/embeddingservice/Dockerfile
      image: embeddingservice
      platform: linux/amd64
      env_file:
        - ./docker/services.env
      volumes:
        - ~/.secrets:/root/.secrets:ro
        - ~/.secrets:/Users/<USER>
      hostname: embeddingservice
      ports:
        - "8107:8080"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:8107/api/__deepcheck" ]
        interval: 5s
        timeout: 5s
        retries: 3
        start_period: 30s
      depends_on:
        aws:
          condition: service_healthy

    indexservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/indexservice/Dockerfile
        image: indexservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: indexservice
        ports:
            - "8116:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8101/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            aws:
                condition: service_healthy

    maintenanceservice:
      profiles:
        - local
      build:
        context: .
        dockerfile: projects/services/maintenanceservice/Dockerfile
      image: maintenanceservice
      platform: linux/amd64
      env_file:
        - ./docker/services.env
      volumes:
        - ~/.secrets:/root/.secrets:ro
        - ~/.secrets:/Users/<USER>
      hostname: maintenanceservice
      ports:
        - "8119:8080"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:8096/api/__deepcheck" ]
        interval: 5s
        timeout: 5s
        retries: 3
        start_period: 30s
      depends_on:
        postgres:
          condition: service_started
        redis:
          condition: service_started

    notificationservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/notificationservice/Dockerfile
        image: notificationservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: notificationservice
        ports:
            - "8087:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8087/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            activemq:
                condition: service_started
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    proxy-provider:
      profiles:
        - local
      build:
        context: .
        dockerfile: projects/services/proxy-provider/Dockerfile
      image: proxy-provider
      platform: linux/amd64
      env_file:
        - ./docker/services.env
      volumes:
        - ~/.secrets:/root/.secrets:ro
        - ~/.secrets:/Users/<USER>
      hostname: proxy-provider
      ports:
        - "8096:8080"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:8096/api/__deepcheck" ]
        interval: 5s
        timeout: 5s
        retries: 3
        start_period: 30s
      depends_on:
        postgres:
          condition: service_started
        redis:
          condition: service_started

    publicapiservice:
      profiles:
        - local
      build:
        context: .
        dockerfile: projects/services/publicapiservice/Dockerfile
      image: publicapiservice
      platform: linux/amd64
      env_file:
        - ./docker/services.env
      volumes:
        - ~/.secrets:/root/.secrets:ro
        - ~/.secrets:/Users/<USER>
      hostname: publicapiservice
      ports:
        - "8106:8080"
      healthcheck:
        test: [ "CMD", "curl", "-f", "http://localhost:8081/api/__deepcheck" ]
        interval: 5s
        timeout: 5s
        retries: 5
        start_period: 30s
      depends_on:
        activemq:
          condition: service_started
        postgres:
          condition: service_started
        redis:
          condition: service_started
        aws:
          condition: service_healthy

    review:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/review/Dockerfile
        image: review
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: review
        ports:
            - "8105:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8105/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            activemq:
                condition: service_started
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    pusherservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/pusherservice/Dockerfile
        image: pusherservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        depends_on:
            - activemq
            - postgres
            - redis
        hostname: pusherservice
        ports:
            - "8082:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8082/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 5
            start_period: 30s

    scmservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/scmservice/Dockerfile
        image: scmservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
            - ./docker/certs/scmservice/:/certs/:ro
        hostname: scmservice
        ports:
            - "8083:8080"
            - "8483:8443"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8083/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            activemq:
                condition: service_started
            postgres:
                condition: service_started
            redis:
                condition: service_started
            aws:
                condition: service_healthy

    searchservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/searchservice/Dockerfile
        image: searchservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: searchservice
        ports:
            - "8089:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8089/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            activemq:
                condition: service_started
            postgres:
                condition: service_started
            aws:
                condition: service_healthy

    slackservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/slackservice/Dockerfile
        image: slackservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        ports:
            - "8091:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8091/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            aws:
                condition: service_healthy

    sourcecodeservice:
        profiles:
            - donotstart
        build:
            context: .
            dockerfile: projects/services/sourcecodeservice/Dockerfile
        image: sourcecodeservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: sourcecodeservice
        ports:
            - "8104:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8104/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            aws:
                condition: service_healthy

    telemetryservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/telemetryservice/Dockerfile
        image: telemetryservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: telemetryservice
        ports:
            - "8092:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8092/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            aws:
                condition: service_healthy

    topicservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/topicservice/Dockerfile
        image: topicservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: topicservice
        ports:
            - "8095:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8095/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s
        depends_on:
            aws:
                condition: service_healthy

    webhookservice:
        profiles:
            - local
        build:
            context: .
            dockerfile: projects/services/webhookservice/Dockerfile
        image: webhookservice
        platform: linux/amd64
        env_file:
            - ./docker/services.env
        volumes:
            - ~/.secrets:/root/.secrets:ro
            - ~/.secrets:/Users/<USER>
        hostname: webhookservice
        ports:
            - "8090:8080"
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8090/api/__deepcheck" ]
            interval: 5s
            timeout: 5s
            retries: 3
            start_period: 30s

    nginx:
        profiles:
            - donotstart
        image: nginx:latest
        platform: linux/amd64
        hostname: nginx
        ports:
            - "8080:8080"

    nginx-local:
        extends: nginx
        profiles:
            - local
        volumes:
            - ./docker/nginx/nginx.local.conf:/etc/nginx/nginx.conf:ro
            - ./projects/libs/lib-oauth/src/main/resources/public:/usr/share/nginx/html/public:ro
        depends_on:
            - apiservice
            - assetservice
            - authservice
            - pusherservice
            - telemetryservice
            - webhookservice

    nginx-intellij:
        extends: nginx
        profiles:
            - intellij
        volumes:
            - ./docker/nginx/nginx.local.intellij.conf:/etc/nginx/nginx.conf:ro
            - ./projects/libs/lib-oauth/src/main/resources/public:/usr/share/nginx/html/public:ro

    squid:
        image: ubuntu/squid:latest
        container_name: squid
        restart: unless-stopped
        profiles:
            - intellij
        ports:
            - "3128:3128"
        volumes:
            - ./docker/squid/squid.conf:/etc/squid/squid.conf:ro
            - squid-cache:/var/spool/squid
        # Optional: show logs on the host
        logging:
            driver: "json-file"
            options:
                max-size: "10m"

    unblocked-temporal:
      container_name: unblocked-temporal
      image: temporalio/auto-setup:********
      depends_on:
        - postgres-temporal
      env_file:
        - ./docker/temporal/temporal.env
      networks:
        - default
      ports:
        - "7233:7233"
      volumes:
        - ./docker/temporal/dynamicconfig:/etc/temporal/config/dynamicconfig
      healthcheck:
        test: ["CMD-SHELL", "tctl workflow list || exit 1"]
        interval: 10s
        timeout: 5s
        retries: 30
        start_period: 30s

    unblocked-temporal-admin-tools:
      container_name: unblocked-temporal-admin-tools
      image: temporalio/admin-tools:1.28
      depends_on:
        - unblocked-temporal
      env_file:
        - ./docker/temporal/temporal-admin-tools.env
      networks:
        - default
      stdin_open: true
      tty: true

    unblocked-temporal-ui:
      container_name: unblocked-temporal-ui
      image: temporalio/ui:2.39.0
      depends_on:
        - unblocked-temporal
      env_file:
        - ./docker/temporal/temporal-ui.env
      networks:
        - default
      ports:
        - "9080:8080"

    unblocked-temporal-test-setup:
      container_name: unblocked-temporal-test-setup
      image: temporalio/admin-tools:1.28
      depends_on:
        unblocked-temporal:
          condition: service_healthy
      env_file:
        - ./docker/temporal/temporal-admin-tools.env
      networks:
        - default
      volumes:
        - ./docker/temporal/local-test-setup.sh:/local-test-setup.sh:ro
      entrypoint: ["/bin/bash"]
      command: ["/local-test-setup.sh"]
      restart: "no"

    unblocked-temporal-default-setup:
      container_name: unblocked-temporal-default-setup
      image: temporalio/admin-tools:1.28
      depends_on:
        unblocked-temporal:
          condition: service_healthy
      env_file:
        - ./docker/temporal/temporal-admin-tools.env
      networks:
        - default
      volumes:
        - ./docker/temporal/local-default-setup.sh:/local-default-setup.sh:ro
      entrypoint: ["/bin/bash"]
      command: ["/local-default-setup.sh"]
      restart: "no"

volumes:
    activemq-local-stack-data:
    mongo-data:
    opensearch-local-stack-data:
    postgres-local-stack-data:
    postgres-prefect-data:
    postgres-temporal-data:
    redis-local-stack-data:
    squid-cache:
